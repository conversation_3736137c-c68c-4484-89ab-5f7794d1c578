var paginationManager = function (cssId,wuid, options){
    if(cssId === "" || cssId === undefined){
        this.$widget = $('.'+wuid).find(".pagination");
    }else{
        this.$widget = $('#'+cssId).find(".pagination-content").closest(".pagination");
    }
    this.options = options;
    this.options.cssId = cssId;
    this.options.wuid = wuid;

    if (this.$widget.length === 0){
        throw new Error('Unable to initialize widget : widget not found (cssId: "' + cssId + '").');
    }
    else{
        this.options.page = parseInt(options.currentPage);
        this.init();
    }
};

paginationManager.prototype.init = function(){

    this.options.$listContainer =  $("."+this.options.wuid).find(".panels-container").find(".scroll-container ul.hits");

    this.$widget.off(".pagination");
    this.$widget.on("click.pagination", ".first-page", $.proxy(function (e) {
        e.preventDefault();
        this.options.page = 1;
        this.loadPage(this.options.page, this.options.feedName, this.options.$listContainer);
    }, this));

    this.$widget.on("click.pagination", ".previous-page", $.proxy(function (e) {
        e.preventDefault();
        var currentPage = this.options.page;
        currentPage = this.options.page - 1;
        if(currentPage <= this.options.lastPage || !isNaN(currentPage) || currentPage >= 1){
            this.options.page--;
            this.loadPage(this.options.page, this.options.feedName, this.options.$listContainer);
        }
    }, this));

    this.$widget.on("click.pagination", ".other-page", $.proxy(function (e) {
        e.preventDefault();
        var $clickedItem = $(e.currentTarget).closest("li");
        var $currentSelected = this.$widget.find("li a.current").closest("li");
        var positionItem = this.$widget.find("li").index($clickedItem);
        var positionSelectedItem = this.$widget.find("li").index($currentSelected);
        var res = positionItem - positionSelectedItem;
        var currentPage = this.options.page;
        currentPage += res;
        if(currentPage <= this.options.lastPage || !isNaN(currentPage) || currentPage >= 1){
            this.options.page = currentPage;
            this.loadPage(this.options.page, this.options.feedName, this.options.$listContainer);
        }
    }, this));

    this.$widget.on("click.pagination", ".next-page", $.proxy(function (e) {
        e.preventDefault();
        var currentPage = this.options.page;
        currentPage = this.options.page + 1;
        if(currentPage <= this.options.lastPage || !isNaN(currentPage)){
            this.options.page++;
            this.loadPage(this.options.page, this.options.feedName, this.options.$listContainer);
        }
    }, this));

    this.$widget.on("click.pagination", ".last-page", $.proxy(function(e){
        e.preventDefault();
        this.options.page = this.options.lastPage;
        this.loadPage(this.options.lastPage, this.options.feedName, this.options.$listContainer);
    }, this));

};

paginationManager.prototype.loadPage = function(pageNum, feedName, $container) {
	$container.parent().showPLMASpinner({overlay: true});
    var pageUrl = new BuildUrl(this.options.url);
    pageUrl.addParameter(feedName + '.page', pageNum, true);
	pageUrl.addParameter('paginationLoad', this.options.wuid, true);
    var ajaxClient = new PlmaAjaxClient($("body"), {});
    ajaxClient.setQueryString(pageUrl.toString().substr(1));
    ajaxClient.addWidget(this.options.wuid);
    ajaxClient.getWidget($.proxy(function(widgets, pageAppendScript) {
        var $extraHits = $(widgets[0].html).filter('li.hit')
        if($extraHits.length > 0){
            $container.find("li.hit").remove();
            $extraHits.appendTo($container);
            //update Pagination widget
            var $paginationContent = $(widgets[0].html).find(".pagination-content");
            this.$widget.find(".pagination-content").remove();
            $paginationContent.appendTo(this.$widget);
        }
		$('#mainWrapper').append(pageAppendScript);
        $container.parent().hidePLMASpinner();
    }, this));
};