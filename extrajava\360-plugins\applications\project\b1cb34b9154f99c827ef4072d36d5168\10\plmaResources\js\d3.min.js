// https://d3js.org Version 4.2.2. Copyright 2016 <PERSON>.
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n(t.d3=t.d3||{})}(this,function(t){"use strict";function n(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function e(t){var e;return 1===t.length&&(e=t,t=function(t,r){return n(e(t),r)}),{left:function(n,e,r,i){for(null==r&&(r=0),null==i&&(i=n.length);r<i;){var o=r+i>>>1;t(n[o],e)<0?r=o+1:i=o}return r},right:function(n,e,r,i){for(null==r&&(r=0),null==i&&(i=n.length);r<i;){var o=r+i>>>1;t(n[o],e)>0?i=o:r=o+1}return r}}}var r=e(n),i=r.right,o=r.left;function u(t){return null===t?NaN:+t}function a(t,n){var e,r,i=t.length,o=0,a=0,c=-1,s=0;if(null==n)for(;++c<i;)isNaN(e=u(t[c]))||(a+=(r=e-o)*(e-(o+=r/++s)));else for(;++c<i;)isNaN(e=u(n(t[c],c,t)))||(a+=(r=e-o)*(e-(o+=r/++s)));if(s>1)return a/(s-1)}function c(t,n){var e=a(t,n);return e?Math.sqrt(e):e}function s(t,n){var e,r,i,o=-1,u=t.length;if(null==n){for(;++o<u;)if(null!=(r=t[o])&&r>=r){e=i=r;break}for(;++o<u;)null!=(r=t[o])&&(e>r&&(e=r),i<r&&(i=r))}else{for(;++o<u;)if(null!=(r=n(t[o],o,t))&&r>=r){e=i=r;break}for(;++o<u;)null!=(r=n(t[o],o,t))&&(e>r&&(e=r),i<r&&(i=r))}return[e,i]}var f=Array.prototype,l=f.slice,h=f.map;function p(t){return function(){return t}}function d(t){return t}function v(t,n,e){t=+t,n=+n,e=(i=arguments.length)<2?(n=t,t=0,1):i<3?1:+e;for(var r=-1,i=0|Math.max(0,Math.ceil((n-t)/e)),o=new Array(i);++r<i;)o[r]=t+r*e;return o}var _=Math.sqrt(50),y=Math.sqrt(10),g=Math.sqrt(2);function m(t,n,e){var r=x(t,n,e);return v(Math.ceil(t/r)*r,Math.floor(n/r)*r+r/2,r)}function x(t,n,e){var r=Math.abs(n-t)/Math.max(0,e),i=Math.pow(10,Math.floor(Math.log(r)/Math.LN10)),o=r/i;return o>=_?i*=10:o>=y?i*=5:o>=g&&(i*=2),n<t?-i:i}function b(t){return Math.ceil(Math.log(t.length)/Math.LN2)+1}function w(t,n,e){if(null==e&&(e=u),r=t.length){if((n=+n)<=0||r<2)return+e(t[0],0,t);if(n>=1)return+e(t[r-1],r-1,t);var r,i=(r-1)*n,o=Math.floor(i),a=+e(t[o],o,t);return a+(+e(t[o+1],o+1,t)-a)*(i-o)}}function M(t){for(var n,e,r,i=t.length,o=-1,u=0;++o<i;)u+=t[o].length;for(e=new Array(u);--i>=0;)for(n=(r=t[i]).length;--n>=0;)e[--u]=r[n];return e}function T(t,n){var e,r,i=-1,o=t.length;if(null==n){for(;++i<o;)if(null!=(r=t[i])&&r>=r){e=r;break}for(;++i<o;)null!=(r=t[i])&&e>r&&(e=r)}else{for(;++i<o;)if(null!=(r=n(t[i],i,t))&&r>=r){e=r;break}for(;++i<o;)null!=(r=n(t[i],i,t))&&e>r&&(e=r)}return e}function S(t){if(!(i=t.length))return[];for(var n=-1,e=T(t,k),r=new Array(e);++n<e;)for(var i,o=-1,u=r[n]=new Array(i);++o<i;)u[o]=t[o][n];return r}function k(t){return t.length}function N(){}function A(t,n){var e=new N;if(t instanceof N)t.each(function(t,n){e.set(n,t)});else if(Array.isArray(t)){var r,i=-1,o=t.length;if(null==n)for(;++i<o;)e.set(i,t[i]);else for(;++i<o;)e.set(n(r=t[i],i,t),r)}else if(t)for(var u in t)e.set(u,t[u]);return e}function E(){return{}}function C(t,n,e){t[n]=e}function z(){return A()}function P(t,n,e){t.set(n,e)}function q(){}N.prototype=A.prototype={constructor:N,has:function(t){return"$"+t in this},get:function(t){return this["$"+t]},set:function(t,n){return this["$"+t]=n,this},remove:function(t){var n="$"+t;return n in this&&delete this[n]},clear:function(){for(var t in this)"$"===t[0]&&delete this[t]},keys:function(){var t=[];for(var n in this)"$"===n[0]&&t.push(n.slice(1));return t},values:function(){var t=[];for(var n in this)"$"===n[0]&&t.push(this[n]);return t},entries:function(){var t=[];for(var n in this)"$"===n[0]&&t.push({key:n.slice(1),value:this[n]});return t},size:function(){var t=0;for(var n in this)"$"===n[0]&&++t;return t},empty:function(){for(var t in this)if("$"===t[0])return!1;return!0},each:function(t){for(var n in this)"$"===n[0]&&t(this[n],n.slice(1),this)}};var L=A.prototype;function R(t,n){var e=new q;if(t instanceof q)t.each(function(t){e.add(t)});else if(t){var r=-1,i=t.length;if(null==n)for(;++r<i;)e.add(t[r]);else for(;++r<i;)e.add(n(t[r],r,t))}return e}function U(t,n){var e,r;return t=null==t?0:+t,n=null==n?1:+n,function(){var i;if(null!=e)i=e,e=null;else do{e=2*Math.random()-1,i=2*Math.random()-1,r=e*e+i*i}while(!r||r>1);return t+n*i*Math.sqrt(-2*Math.log(r)/r)}}function D(t){return function(){for(var n=0,e=0;e<t;++e)n+=Math.random();return n}}function O(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}function F(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}q.prototype=R.prototype={constructor:q,has:L.has,add:function(t){return this["$"+(t+="")]=t,this},remove:L.remove,clear:L.clear,values:L.keys,size:L.size,empty:L.empty,each:L.each};var I=function t(n){function e(t){return Math.pow(t,n)}return n=+n,e.exponent=t,e}(3),Y=function t(n){function e(t){return 1-Math.pow(1-t,n)}return n=+n,e.exponent=t,e}(3),B=function t(n){function e(t){return((t*=2)<=1?Math.pow(t,n):2-Math.pow(2-t,n))/2}return n=+n,e.exponent=t,e}(3),j=Math.PI,H=j/2;function X(t){return(1-Math.cos(j*t))/2}function V(t){return((t*=2)<=1?Math.pow(2,10*t-10):2-Math.pow(2,10-10*t))/2}function $(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var W=4/11,Z=6/11,G=8/11,J=.75,Q=9/11,K=10/11,tt=.9375,nt=21/22,et=63/64,rt=1/W/W;function it(t){return(t=+t)<W?rt*t*t:t<G?rt*(t-=Z)*t+J:t<K?rt*(t-=Q)*t+tt:rt*(t-=nt)*t+et}var ot=function t(n){function e(t){return t*t*((n+1)*t-n)}return n=+n,e.overshoot=t,e}(1.70158),ut=function t(n){function e(t){return--t*t*((n+1)*t+n)+1}return n=+n,e.overshoot=t,e}(1.70158),at=function t(n){function e(t){return((t*=2)<1?t*t*((n+1)*t-n):(t-=2)*t*((n+1)*t+n)+2)/2}return n=+n,e.overshoot=t,e}(1.70158),ct=2*Math.PI,st=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=ct);function i(t){return n*Math.pow(2,10*--t)*Math.sin((r-t)/e)}return i.amplitude=function(n){return t(n,e*ct)},i.period=function(e){return t(n,e)},i}(1,.3),ft=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=ct);function i(t){return 1-n*Math.pow(2,-10*(t=+t))*Math.sin((t+r)/e)}return i.amplitude=function(n){return t(n,e*ct)},i.period=function(e){return t(n,e)},i}(1,.3),lt=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=ct);function i(t){return((t=2*t-1)<0?n*Math.pow(2,10*t)*Math.sin((r-t)/e):2-n*Math.pow(2,-10*t)*Math.sin((r+t)/e))/2}return i.amplitude=function(n){return t(n,e*ct)},i.period=function(e){return t(n,e)},i}(1,.3);function ht(t,n){return t[0]-n[0]||t[1]-n[1]}function pt(t){for(var n,e,r,i=t.length,o=[0,1],u=2,a=2;a<i;++a){for(;u>1&&(n=t[o[u-2]],e=t[o[u-1]],r=t[a],(e[0]-n[0])*(r[1]-n[1])-(e[1]-n[1])*(r[0]-n[0])<=0);)--u;o[u++]=a}return o.slice(0,u)}var dt=Math.PI,vt=2*dt,_t=vt-1e-6;function yt(){this._x0=this._y0=this._x1=this._y1=null,this._=[]}function gt(){return new yt}function mt(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o,u,a,c,s,f,l,h,p=t._root,d={data:r},v=t._x0,_=t._y0,y=t._x1,g=t._y1;if(!p)return t._root=d,t;for(;p.length;)if((s=n>=(o=(v+y)/2))?v=o:y=o,(f=e>=(u=(_+g)/2))?_=u:g=u,i=p,!(p=p[l=f<<1|s]))return i[l]=d,t;if(a=+t._x.call(null,p.data),c=+t._y.call(null,p.data),n===a&&e===c)return d.next=p,i?i[l]=d:t._root=d,t;do{i=i?i[l]=new Array(4):t._root=new Array(4),(s=n>=(o=(v+y)/2))?v=o:y=o,(f=e>=(u=(_+g)/2))?_=u:g=u}while((l=f<<1|s)==(h=(c>=u)<<1|a>=o));return i[h]=p,i[l]=d,t}function xt(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function bt(t){return t[0]}function wt(t){return t[1]}function Mt(t,n,e){var r=new Tt(null==n?bt:n,null==e?wt:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function Tt(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function St(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}yt.prototype=gt.prototype={constructor:yt,moveTo:function(t,n){this._.push("M",this._x0=this._x1=+t,",",this._y0=this._y1=+n)},closePath:function(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._.push("Z"))},lineTo:function(t,n){this._.push("L",this._x1=+t,",",this._y1=+n)},quadraticCurveTo:function(t,n,e,r){this._.push("Q",+t,",",+n,",",this._x1=+e,",",this._y1=+r)},bezierCurveTo:function(t,n,e,r,i,o){this._.push("C",+t,",",+n,",",+e,",",+r,",",this._x1=+i,",",this._y1=+o)},arcTo:function(t,n,e,r,i){t=+t,n=+n,e=+e,r=+r,i=+i;var o=this._x1,u=this._y1,a=e-t,c=r-n,s=o-t,f=u-n,l=s*s+f*f;if(i<0)throw new Error("negative radius: "+i);if(null===this._x1)this._.push("M",this._x1=t,",",this._y1=n);else if(l>1e-6)if(Math.abs(f*a-c*s)>1e-6&&i){var h=e-o,p=r-u,d=a*a+c*c,v=h*h+p*p,_=Math.sqrt(d),y=Math.sqrt(l),g=i*Math.tan((dt-Math.acos((d+l-v)/(2*_*y)))/2),m=g/y,x=g/_;Math.abs(m-1)>1e-6&&this._.push("L",t+m*s,",",n+m*f),this._.push("A",i,",",i,",0,0,",+(f*h>s*p),",",this._x1=t+x*a,",",this._y1=n+x*c)}else this._.push("L",this._x1=t,",",this._y1=n);else;},arc:function(t,n,e,r,i,o){t=+t,n=+n;var u=(e=+e)*Math.cos(r),a=e*Math.sin(r),c=t+u,s=n+a,f=1^o,l=o?r-i:i-r;if(e<0)throw new Error("negative radius: "+e);null===this._x1?this._.push("M",c,",",s):(Math.abs(this._x1-c)>1e-6||Math.abs(this._y1-s)>1e-6)&&this._.push("L",c,",",s),e&&(l>_t?this._.push("A",e,",",e,",0,1,",f,",",t-u,",",n-a,"A",e,",",e,",0,1,",f,",",this._x1=c,",",this._y1=s):(l<0&&(l=l%vt+vt),this._.push("A",e,",",e,",0,",+(l>=dt),",",f,",",this._x1=t+e*Math.cos(i),",",this._y1=n+e*Math.sin(i))))},rect:function(t,n,e,r){this._.push("M",this._x0=this._x1=+t,",",this._y0=this._y1=+n,"h",+e,"v",+r,"h",-e,"Z")},toString:function(){return this._.join("")}};var kt=Mt.prototype=Tt.prototype;kt.copy=function(){var t,n,e=new Tt(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=St(r),e;for(t=[{source:r,target:e._root=new Array(4)}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(4)}):r.target[i]=St(n));return e},kt.add=function(t){var n=+this._x.call(null,t),e=+this._y.call(null,t);return mt(this.cover(n,e),n,e,t)},kt.addAll=function(t){var n,e,r,i,o=t.length,u=new Array(o),a=new Array(o),c=1/0,s=1/0,f=-1/0,l=-1/0;for(e=0;e<o;++e)isNaN(r=+this._x.call(null,n=t[e]))||isNaN(i=+this._y.call(null,n))||(u[e]=r,a[e]=i,r<c&&(c=r),r>f&&(f=r),i<s&&(s=i),i>l&&(l=i));for(f<c&&(c=this._x0,f=this._x1),l<s&&(s=this._y0,l=this._y1),this.cover(c,s).cover(f,l),e=0;e<o;++e)mt(this,u[e],a[e],t[e]);return this},kt.cover=function(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{if(!(e>t||t>i||r>n||n>o))return this;var u,a,c=i-e,s=this._root;switch(a=(n<(r+o)/2)<<1|t<(e+i)/2){case 0:do{(u=new Array(4))[a]=s,s=u}while(o=r+(c*=2),t>(i=e+c)||n>o);break;case 1:do{(u=new Array(4))[a]=s,s=u}while(o=r+(c*=2),(e=i-c)>t||n>o);break;case 2:do{(u=new Array(4))[a]=s,s=u}while(r=o-(c*=2),t>(i=e+c)||r>n);break;case 3:do{(u=new Array(4))[a]=s,s=u}while(r=o-(c*=2),(e=i-c)>t||r>n)}this._root&&this._root.length&&(this._root=s)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this},kt.data=function(){var t=[];return this.visit(function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)}),t},kt.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},kt.find=function(t,n,e){var r,i,o,u,a,c,s,f=this._x0,l=this._y0,h=this._x1,p=this._y1,d=[],v=this._root;for(v&&d.push(new xt(v,f,l,h,p)),null==e?e=1/0:(f=t-e,l=n-e,h=t+e,p=n+e,e*=e);c=d.pop();)if(!(!(v=c.node)||(i=c.x0)>h||(o=c.y0)>p||(u=c.x1)<f||(a=c.y1)<l))if(v.length){var _=(i+u)/2,y=(o+a)/2;d.push(new xt(v[3],_,y,u,a),new xt(v[2],i,y,_,a),new xt(v[1],_,o,u,y),new xt(v[0],i,o,_,y)),(s=(n>=y)<<1|t>=_)&&(c=d[d.length-1],d[d.length-1]=d[d.length-1-s],d[d.length-1-s]=c)}else{var g=t-+this._x.call(null,v.data),m=n-+this._y.call(null,v.data),x=g*g+m*m;if(x<e){var b=Math.sqrt(e=x);f=t-b,l=n-b,h=t+b,p=n+b,r=v.data}}return r},kt.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(u=+this._y.call(null,t)))return this;var n,e,r,i,o,u,a,c,s,f,l,h,p=this._root,d=this._x0,v=this._y0,_=this._x1,y=this._y1;if(!p)return this;if(p.length)for(;;){if((s=o>=(a=(d+_)/2))?d=a:_=a,(f=u>=(c=(v+y)/2))?v=c:y=c,n=p,!(p=p[l=f<<1|s]))return this;if(!p.length)break;(n[l+1&3]||n[l+2&3]||n[l+3&3])&&(e=n,h=l)}for(;p.data!==t;)if(r=p,!(p=p.next))return this;return(i=p.next)&&delete p.next,r?(i?r.next=i:delete r.next,this):n?(i?n[l]=i:delete n[l],(p=n[0]||n[1]||n[2]||n[3])&&p===(n[3]||n[2]||n[1]||n[0])&&!p.length&&(e?e[h]=p:this._root=p),this):(this._root=i,this)},kt.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},kt.root=function(){return this._root},kt.size=function(){var t=0;return this.visit(function(n){if(!n.length)do{++t}while(n=n.next)}),t},kt.visit=function(t){var n,e,r,i,o,u,a=[],c=this._root;for(c&&a.push(new xt(c,this._x0,this._y0,this._x1,this._y1));n=a.pop();)if(!t(c=n.node,r=n.x0,i=n.y0,o=n.x1,u=n.y1)&&c.length){var s=(r+o)/2,f=(i+u)/2;(e=c[3])&&a.push(new xt(e,s,f,o,u)),(e=c[2])&&a.push(new xt(e,r,f,s,u)),(e=c[1])&&a.push(new xt(e,s,i,o,f)),(e=c[0])&&a.push(new xt(e,r,i,s,f))}return this},kt.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new xt(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var i=n.node;if(i.length){var o,u=n.x0,a=n.y0,c=n.x1,s=n.y1,f=(u+c)/2,l=(a+s)/2;(o=i[0])&&e.push(new xt(o,u,a,f,l)),(o=i[1])&&e.push(new xt(o,f,a,c,l)),(o=i[2])&&e.push(new xt(o,u,l,f,s)),(o=i[3])&&e.push(new xt(o,f,l,c,s))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},kt.x=function(t){return arguments.length?(this._x=t,this):this._x},kt.y=function(t){return arguments.length?(this._y=t,this):this._y};var Nt=[].slice,At={};function Et(t){if(!(t>=1))throw new Error;this._size=t,this._call=this._error=null,this._tasks=[],this._data=[],this._waiting=this._active=this._ended=this._start=0}function Ct(t){if(!t._start)try{!function(t){for(;t._start=t._waiting&&t._active<t._size;){var n=t._ended+t._active,e=t._tasks[n],r=e.length-1,i=e[r];e[r]=zt(t,n),--t._waiting,++t._active,e=i.apply(null,e),t._tasks[n]&&(t._tasks[n]=e||At)}}(t)}catch(n){t._tasks[t._ended+t._active-1]&&Pt(t,n)}}function zt(t,n){return function(e,r){t._tasks[n]&&(--t._active,++t._ended,t._tasks[n]=null,null==t._error&&(null!=e?Pt(t,e):(t._data[n]=r,t._waiting?Ct(t):qt(t))))}}function Pt(t,n){var e,r=t._tasks.length;for(t._error=n,t._data=void 0,t._waiting=NaN;--r>=0;)if((e=t._tasks[r])&&(t._tasks[r]=null,e.abort))try{e.abort()}catch(n){}t._active=NaN,qt(t)}function qt(t){!t._active&&t._call&&t._call(t._error,t._data)}function Lt(t){return new Et(arguments.length?+t:1/0)}function Rt(t){return function(){return t}}Et.prototype=Lt.prototype={constructor:Et,defer:function(t){if("function"!=typeof t||this._call)throw new Error;if(null!=this._error)return this;var n=Nt.call(arguments,1);return n.push(t),++this._waiting,this._tasks.push(n),Ct(this),this},abort:function(){return null==this._error&&Pt(this,new Error("abort")),this},await:function(t){if("function"!=typeof t||this._call)throw new Error;return this._call=function(n,e){t.apply(null,[n].concat(e))},qt(this),this},awaitAll:function(t){if("function"!=typeof t||this._call)throw new Error;return this._call=t,qt(this),this}};var Ut=1e-12,Dt=Math.PI,Ot=Dt/2,Ft=2*Dt;function It(t){return t.innerRadius}function Yt(t){return t.outerRadius}function Bt(t){return t.startAngle}function jt(t){return t.endAngle}function Ht(t){return t&&t.padAngle}function Xt(t){return t>=1?Ot:t<=-1?-Ot:Math.asin(t)}function Vt(t,n,e,r,i,o,u){var a=t-e,c=n-r,s=(u?o:-o)/Math.sqrt(a*a+c*c),f=s*c,l=-s*a,h=t+f,p=n+l,d=e+f,v=r+l,_=(h+d)/2,y=(p+v)/2,g=d-h,m=v-p,x=g*g+m*m,b=i-o,w=h*v-d*p,M=(m<0?-1:1)*Math.sqrt(Math.max(0,b*b*x-w*w)),T=(w*m-g*M)/x,S=(-w*g-m*M)/x,k=(w*m+g*M)/x,N=(-w*g+m*M)/x,A=T-_,E=S-y,C=k-_,z=N-y;return A*A+E*E>C*C+z*z&&(T=k,S=N),{cx:T,cy:S,x01:-f,y01:-l,x11:T*(i/b-1),y11:S*(i/b-1)}}function $t(t){this._context=t}function Wt(t){return new $t(t)}function Zt(t){return t[0]}function Gt(t){return t[1]}function Jt(){var t=Zt,n=Gt,e=Rt(!0),r=null,i=Wt,o=null;function u(u){var a,c,s,f=u.length,l=!1;for(null==r&&(o=i(s=gt())),a=0;a<=f;++a)!(a<f&&e(c=u[a],a,u))===l&&((l=!l)?o.lineStart():o.lineEnd()),l&&o.point(+t(c,a,u),+n(c,a,u));if(s)return o=null,s+""||null}return u.x=function(n){return arguments.length?(t="function"==typeof n?n:Rt(+n),u):t},u.y=function(t){return arguments.length?(n="function"==typeof t?t:Rt(+t),u):n},u.defined=function(t){return arguments.length?(e="function"==typeof t?t:Rt(!!t),u):e},u.curve=function(t){return arguments.length?(i=t,null!=r&&(o=i(r)),u):i},u.context=function(t){return arguments.length?(null==t?r=o=null:o=i(r=t),u):r},u}function Qt(){var t=Zt,n=null,e=Rt(0),r=Gt,i=Rt(!0),o=null,u=Wt,a=null;function c(c){var s,f,l,h,p,d=c.length,v=!1,_=new Array(d),y=new Array(d);for(null==o&&(a=u(p=gt())),s=0;s<=d;++s){if(!(s<d&&i(h=c[s],s,c))===v)if(v=!v)f=s,a.areaStart(),a.lineStart();else{for(a.lineEnd(),a.lineStart(),l=s-1;l>=f;--l)a.point(_[l],y[l]);a.lineEnd(),a.areaEnd()}v&&(_[s]=+t(h,s,c),y[s]=+e(h,s,c),a.point(n?+n(h,s,c):_[s],r?+r(h,s,c):y[s]))}if(p)return a=null,p+""||null}function s(){return Jt().defined(i).curve(u).context(o)}return c.x=function(e){return arguments.length?(t="function"==typeof e?e:Rt(+e),n=null,c):t},c.x0=function(n){return arguments.length?(t="function"==typeof n?n:Rt(+n),c):t},c.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:Rt(+t),c):n},c.y=function(t){return arguments.length?(e="function"==typeof t?t:Rt(+t),r=null,c):e},c.y0=function(t){return arguments.length?(e="function"==typeof t?t:Rt(+t),c):e},c.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:Rt(+t),c):r},c.lineX0=c.lineY0=function(){return s().x(t).y(e)},c.lineY1=function(){return s().x(t).y(r)},c.lineX1=function(){return s().x(n).y(e)},c.defined=function(t){return arguments.length?(i="function"==typeof t?t:Rt(!!t),c):i},c.curve=function(t){return arguments.length?(u=t,null!=o&&(a=u(o)),c):u},c.context=function(t){return arguments.length?(null==t?o=a=null:a=u(o=t),c):o},c}function Kt(t,n){return n<t?-1:n>t?1:n>=t?0:NaN}function tn(t){return t}$t.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n)}}};var nn=rn(Wt);function en(t){this._curve=t}function rn(t){function n(n){return new en(t(n))}return n._curve=t,n}function on(t){var n=t.curve;return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t.curve=function(t){return arguments.length?n(rn(t)):n()._curve},t}en.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,n){this._curve.point(n*Math.sin(t),n*-Math.cos(t))}};var un={draw:function(t,n){var e=Math.sqrt(n/Dt);t.moveTo(e,0),t.arc(0,0,e,0,Ft)}},an={draw:function(t,n){var e=Math.sqrt(n/5)/2;t.moveTo(-3*e,-e),t.lineTo(-e,-e),t.lineTo(-e,-3*e),t.lineTo(e,-3*e),t.lineTo(e,-e),t.lineTo(3*e,-e),t.lineTo(3*e,e),t.lineTo(e,e),t.lineTo(e,3*e),t.lineTo(-e,3*e),t.lineTo(-e,e),t.lineTo(-3*e,e),t.closePath()}},cn=Math.sqrt(1/3),sn=2*cn,fn={draw:function(t,n){var e=Math.sqrt(n/sn),r=e*cn;t.moveTo(0,-e),t.lineTo(r,0),t.lineTo(0,e),t.lineTo(-r,0),t.closePath()}},ln=Math.sin(Dt/10)/Math.sin(7*Dt/10),hn=Math.sin(Ft/10)*ln,pn=-Math.cos(Ft/10)*ln,dn={draw:function(t,n){var e=Math.sqrt(.8908130915292852*n),r=hn*e,i=pn*e;t.moveTo(0,-e),t.lineTo(r,i);for(var o=1;o<5;++o){var u=Ft*o/5,a=Math.cos(u),c=Math.sin(u);t.lineTo(c*e,-a*e),t.lineTo(a*r-c*i,c*r+a*i)}t.closePath()}},vn={draw:function(t,n){var e=Math.sqrt(n),r=-e/2;t.rect(r,r,e,e)}},_n=Math.sqrt(3),yn={draw:function(t,n){var e=-Math.sqrt(n/(3*_n));t.moveTo(0,2*e),t.lineTo(-_n*e,-e),t.lineTo(_n*e,-e),t.closePath()}},gn=-.5,mn=Math.sqrt(3)/2,xn=1/Math.sqrt(12),bn=3*(xn/2+1),wn={draw:function(t,n){var e=Math.sqrt(n/bn),r=e/2,i=e*xn,o=r,u=e*xn+e,a=-o,c=u;t.moveTo(r,i),t.lineTo(o,u),t.lineTo(a,c),t.lineTo(gn*r-mn*i,mn*r+gn*i),t.lineTo(gn*o-mn*u,mn*o+gn*u),t.lineTo(gn*a-mn*c,mn*a+gn*c),t.lineTo(gn*r+mn*i,gn*i-mn*r),t.lineTo(gn*o+mn*u,gn*u-mn*o),t.lineTo(gn*a+mn*c,gn*c-mn*a),t.closePath()}},Mn=[un,an,fn,vn,dn,yn,wn];function Tn(){}function Sn(t,n,e){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+e)/6)}function kn(t){this._context=t}function Nn(t){this._context=t}function An(t){this._context=t}function En(t,n){this._basis=new kn(t),this._beta=n}kn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Sn(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Sn(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},Nn.prototype={areaStart:Tn,areaEnd:Tn,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x2=t,this._y2=n;break;case 1:this._point=2,this._x3=t,this._y3=n;break;case 2:this._point=3,this._x4=t,this._y4=n,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:Sn(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},An.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var e=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(e,r):this._context.moveTo(e,r);break;case 3:this._point=4;default:Sn(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},En.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,e=t.length-1;if(e>0)for(var r,i=t[0],o=n[0],u=t[e]-i,a=n[e]-o,c=-1;++c<=e;)r=c/e,this._basis.point(this._beta*t[c]+(1-this._beta)*(i+r*u),this._beta*n[c]+(1-this._beta)*(o+r*a));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}};var Cn=function t(n){function e(t){return 1===n?new kn(t):new En(t,n)}return e.beta=function(n){return t(+n)},e}(.85);function zn(t,n,e){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-e),t._x2,t._y2)}function Pn(t,n){this._context=t,this._k=(1-n)/6}Pn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:zn(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:zn(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var qn=function t(n){function e(t){return new Pn(t,n)}return e.tension=function(n){return t(+n)},e}(0);function Ln(t,n){this._context=t,this._k=(1-n)/6}Ln.prototype={areaStart:Tn,areaEnd:Tn,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:zn(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Rn=function t(n){function e(t){return new Ln(t,n)}return e.tension=function(n){return t(+n)},e}(0);function Un(t,n){this._context=t,this._k=(1-n)/6}Un.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:zn(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Dn=function t(n){function e(t){return new Un(t,n)}return e.tension=function(n){return t(+n)},e}(0);function On(t,n,e){var r=t._x1,i=t._y1,o=t._x2,u=t._y2;if(t._l01_a>Ut){var a=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,c=3*t._l01_a*(t._l01_a+t._l12_a);r=(r*a-t._x0*t._l12_2a+t._x2*t._l01_2a)/c,i=(i*a-t._y0*t._l12_2a+t._y2*t._l01_2a)/c}if(t._l23_a>Ut){var s=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,f=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*s+t._x1*t._l23_2a-n*t._l12_2a)/f,u=(u*s+t._y1*t._l23_2a-e*t._l12_2a)/f}t._context.bezierCurveTo(r,i,o,u,t._x2,t._y2)}function Fn(t,n){this._context=t,this._alpha=n}Fn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this,this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:On(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var In=function t(n){function e(t){return n?new Fn(t,n):new Pn(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function Yn(t,n){this._context=t,this._alpha=n}Yn.prototype={areaStart:Tn,areaEnd:Tn,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:On(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Bn=function t(n){function e(t){return n?new Yn(t,n):new Ln(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function jn(t,n){this._context=t,this._alpha=n}jn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:On(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Hn=function t(n){function e(t){return n?new jn(t,n):new Un(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function Xn(t){this._context=t}function Vn(t){return t<0?-1:1}function $n(t,n,e){var r=t._x1-t._x0,i=n-t._x1,o=(t._y1-t._y0)/(r||i<0&&-0),u=(e-t._y1)/(i||r<0&&-0),a=(o*i+u*r)/(r+i);return(Vn(o)+Vn(u))*Math.min(Math.abs(o),Math.abs(u),.5*Math.abs(a))||0}function Wn(t,n){var e=t._x1-t._x0;return e?(3*(t._y1-t._y0)/e-n)/2:n}function Zn(t,n,e){var r=t._x0,i=t._y0,o=t._x1,u=t._y1,a=(o-r)/3;t._context.bezierCurveTo(r+a,i+a*n,o-a,u-a*e,o,u)}function Gn(t){this._context=t}function Jn(t){this._context=new Qn(t)}function Qn(t){this._context=t}function Kn(t){this._context=t}function te(t){var n,e,r=t.length-1,i=new Array(r),o=new Array(r),u=new Array(r);for(i[0]=0,o[0]=2,u[0]=t[0]+2*t[1],n=1;n<r-1;++n)i[n]=1,o[n]=4,u[n]=4*t[n]+2*t[n+1];for(i[r-1]=2,o[r-1]=7,u[r-1]=8*t[r-1]+t[r],n=1;n<r;++n)e=i[n]/o[n-1],o[n]-=e,u[n]-=e*u[n-1];for(i[r-1]=u[r-1]/o[r-1],n=r-2;n>=0;--n)i[n]=(u[n]-i[n+1])/o[n];for(o[r-1]=(t[r]+i[r-1])/2,n=0;n<r-1;++n)o[n]=2*t[n+1]-i[n+1];return[i,o]}function ne(t,n){this._context=t,this._t=n}Xn.prototype={areaStart:Tn,areaEnd:Tn,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,n){t=+t,n=+n,this._point?this._context.lineTo(t,n):(this._point=1,this._context.moveTo(t,n))}},Gn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Zn(this,this._t0,Wn(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var e=NaN;if(n=+n,(t=+t)!==this._x1||n!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,Zn(this,Wn(this,e=$n(this,t,n)),e);break;default:Zn(this,this._t0,e=$n(this,t,n))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n,this._t0=e}}},(Jn.prototype=Object.create(Gn.prototype)).point=function(t,n){Gn.prototype.point.call(this,n,t)},Qn.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,e,r,i,o){this._context.bezierCurveTo(n,t,r,e,o,i)}},Kn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,n=this._y,e=t.length;if(e)if(this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]),2===e)this._context.lineTo(t[1],n[1]);else for(var r=te(t),i=te(n),o=0,u=1;u<e;++o,++u)this._context.bezierCurveTo(r[0][o],i[0][o],r[1][o],i[1][o],t[u],n[u]);(this._line||0!==this._line&&1===e)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,n){this._x.push(+t),this._y.push(+n)}},ne.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,n),this._context.lineTo(t,n);else{var e=this._x*(1-this._t)+t*this._t;this._context.lineTo(e,this._y),this._context.lineTo(e,n)}}this._x=t,this._y=n}};var ee=Array.prototype.slice;function re(t,n){if((r=t.length)>1)for(var e,r,i=1,o=t[n[0]],u=o.length;i<r;++i){e=o,o=t[n[i]];for(var a=0;a<u;++a)o[a][1]+=o[a][0]=isNaN(e[a][1])?e[a][0]:e[a][1]}}function ie(t){for(var n=t.length,e=new Array(n);--n>=0;)e[n]=n;return e}function oe(t,n){return t[n]}function ue(t){var n=t.map(ae);return ie(t).sort(function(t,e){return n[t]-n[e]})}function ae(t){for(var n,e=0,r=-1,i=t.length;++r<i;)(n=+t[r][1])&&(e+=n);return e}function ce(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function se(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function fe(){}var le=/^#([0-9a-f]{3})$/,he=/^#([0-9a-f]{6})$/,pe=/^rgb\(\s*([-+]?\d+)\s*,\s*([-+]?\d+)\s*,\s*([-+]?\d+)\s*\)$/,de=/^rgb\(\s*([-+]?\d+(?:\.\d+)?)%\s*,\s*([-+]?\d+(?:\.\d+)?)%\s*,\s*([-+]?\d+(?:\.\d+)?)%\s*\)$/,ve=/^rgba\(\s*([-+]?\d+)\s*,\s*([-+]?\d+)\s*,\s*([-+]?\d+)\s*,\s*([-+]?\d+(?:\.\d+)?)\s*\)$/,_e=/^rgba\(\s*([-+]?\d+(?:\.\d+)?)%\s*,\s*([-+]?\d+(?:\.\d+)?)%\s*,\s*([-+]?\d+(?:\.\d+)?)%\s*,\s*([-+]?\d+(?:\.\d+)?)\s*\)$/,ye=/^hsl\(\s*([-+]?\d+(?:\.\d+)?)\s*,\s*([-+]?\d+(?:\.\d+)?)%\s*,\s*([-+]?\d+(?:\.\d+)?)%\s*\)$/,ge=/^hsla\(\s*([-+]?\d+(?:\.\d+)?)\s*,\s*([-+]?\d+(?:\.\d+)?)%\s*,\s*([-+]?\d+(?:\.\d+)?)%\s*,\s*([-+]?\d+(?:\.\d+)?)\s*\)$/,me={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function xe(t){var n;return t=(t+"").trim().toLowerCase(),(n=le.exec(t))?new Se((n=parseInt(n[1],16))>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):(n=he.exec(t))?be(parseInt(n[1],16)):(n=pe.exec(t))?new Se(n[1],n[2],n[3],1):(n=de.exec(t))?new Se(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=ve.exec(t))?we(n[1],n[2],n[3],n[4]):(n=_e.exec(t))?we(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=ye.exec(t))?ke(n[1],n[2]/100,n[3]/100,1):(n=ge.exec(t))?ke(n[1],n[2]/100,n[3]/100,n[4]):me.hasOwnProperty(t)?be(me[t]):"transparent"===t?new Se(NaN,NaN,NaN,0):null}function be(t){return new Se(t>>16&255,t>>8&255,255&t,1)}function we(t,n,e,r){return r<=0&&(t=n=e=NaN),new Se(t,n,e,r)}function Me(t){return t instanceof fe||(t=xe(t)),t?new Se((t=t.rgb()).r,t.g,t.b,t.opacity):new Se}function Te(t,n,e,r){return 1===arguments.length?Me(t):new Se(t,n,e,null==r?1:r)}function Se(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function ke(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new Ae(t,n,e,r)}function Ne(t,n,e,r){return 1===arguments.length?function(t){if(t instanceof Ae)return new Ae(t.h,t.s,t.l,t.opacity);if(t instanceof fe||(t=xe(t)),!t)return new Ae;if(t instanceof Ae)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),u=NaN,a=o-i,c=(o+i)/2;return a?(u=n===o?(e-r)/a+6*(e<r):e===o?(r-n)/a+2:(n-e)/a+4,a/=c<.5?o+i:2-o-i,u*=60):a=c>0&&c<1?0:u,new Ae(u,a,c,t.opacity)}(t):new Ae(t,n,e,null==r?1:r)}function Ae(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function Ee(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}ce(fe,xe,{displayable:function(){return this.rgb().displayable()},toString:function(){return this.rgb()+""}}),ce(Se,Te,se(fe,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new Se(this.r*t,this.g*t,this.b*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new Se(this.r*t,this.g*t,this.b*t,this.opacity)},rgb:function(){return this},displayable:function(){return 0<=this.r&&this.r<=255&&0<=this.g&&this.g<=255&&0<=this.b&&this.b<=255&&0<=this.opacity&&this.opacity<=1},toString:function(){var t=this.opacity;return(1===(t=isNaN(t)?1:Math.max(0,Math.min(1,t)))?"rgb(":"rgba(")+Math.max(0,Math.min(255,Math.round(this.r)||0))+", "+Math.max(0,Math.min(255,Math.round(this.g)||0))+", "+Math.max(0,Math.min(255,Math.round(this.b)||0))+(1===t?")":", "+t+")")}})),ce(Ae,Ne,se(fe,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new Ae(this.h,this.s,this.l*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new Ae(this.h,this.s,this.l*t,this.opacity)},rgb:function(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new Se(Ee(t>=240?t-240:t+120,i,r),Ee(t,i,r),Ee(t<120?t+240:t-120,i,r),this.opacity)},displayable:function(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1}}));var Ce=Math.PI/180,ze=180/Math.PI,Pe=.95047,qe=1,Le=1.08883,Re=4/29,Ue=6/29,De=3*Ue*Ue,Oe=Ue*Ue*Ue;function Fe(t){if(t instanceof Ye)return new Ye(t.l,t.a,t.b,t.opacity);if(t instanceof $e){var n=t.h*Ce;return new Ye(t.l,Math.cos(n)*t.c,Math.sin(n)*t.c,t.opacity)}t instanceof Se||(t=Me(t));var e=Xe(t.r),r=Xe(t.g),i=Xe(t.b),o=Be((.4124564*e+.3575761*r+.1804375*i)/Pe),u=Be((.2126729*e+.7151522*r+.072175*i)/qe);return new Ye(116*u-16,500*(o-u),200*(u-Be((.0193339*e+.119192*r+.9503041*i)/Le)),t.opacity)}function Ie(t,n,e,r){return 1===arguments.length?Fe(t):new Ye(t,n,e,null==r?1:r)}function Ye(t,n,e,r){this.l=+t,this.a=+n,this.b=+e,this.opacity=+r}function Be(t){return t>Oe?Math.pow(t,1/3):t/De+Re}function je(t){return t>Ue?t*t*t:De*(t-Re)}function He(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function Xe(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function Ve(t,n,e,r){return 1===arguments.length?function(t){if(t instanceof $e)return new $e(t.h,t.c,t.l,t.opacity);t instanceof Ye||(t=Fe(t));var n=Math.atan2(t.b,t.a)*ze;return new $e(n<0?n+360:n,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}(t):new $e(t,n,e,null==r?1:r)}function $e(t,n,e,r){this.h=+t,this.c=+n,this.l=+e,this.opacity=+r}ce(Ye,Ie,se(fe,{brighter:function(t){return new Ye(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker:function(t){return new Ye(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb:function(){var t=(this.l+16)/116,n=isNaN(this.a)?t:t+this.a/500,e=isNaN(this.b)?t:t-this.b/200;return t=qe*je(t),new Se(He(3.2404542*(n=Pe*je(n))-1.5371385*t-.4985314*(e=Le*je(e))),He(-.969266*n+1.8760108*t+.041556*e),He(.0556434*n-.2040259*t+1.0572252*e),this.opacity)}})),ce($e,Ve,se(fe,{brighter:function(t){return new $e(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker:function(t){return new $e(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb:function(){return Fe(this).rgb()}}));var We=-.14861,Ze=1.78277,Ge=-.29227,Je=-.90649,Qe=1.97294,Ke=Qe*Je,tr=Qe*Ze,nr=Ze*Ge-Je*We;function er(t,n,e,r){return 1===arguments.length?function(t){if(t instanceof rr)return new rr(t.h,t.s,t.l,t.opacity);t instanceof Se||(t=Me(t));var n=t.r/255,e=t.g/255,r=t.b/255,i=(nr*r+Ke*n-tr*e)/(nr+Ke-tr),o=r-i,u=(Qe*(e-i)-Ge*o)/Je,a=Math.sqrt(u*u+o*o)/(Qe*i*(1-i)),c=a?Math.atan2(u,o)*ze-120:NaN;return new rr(c<0?c+360:c,a,i,t.opacity)}(t):new rr(t,n,e,null==r?1:r)}function rr(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function ir(t,n,e,r,i){var o=t*t,u=o*t;return((1-3*t+3*o-u)*n+(4-6*o+3*u)*e+(1+3*t+3*o-3*u)*r+u*i)/6}function or(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[r],o=t[r+1],u=r>0?t[r-1]:2*i-o,a=r<n-1?t[r+2]:2*o-i;return ir((e-r/n)*n,u,i,o,a)}}function ur(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),i=t[(r+n-1)%n],o=t[r%n],u=t[(r+1)%n],a=t[(r+2)%n];return ir((e-r/n)*n,i,o,u,a)}}function ar(t){return function(){return t}}function cr(t,n){return function(e){return t+e*n}}function sr(t,n){var e=n-t;return e?cr(t,e>180||e<-180?e-360*Math.round(e/360):e):ar(isNaN(t)?n:t)}function fr(t){return 1==(t=+t)?lr:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):ar(isNaN(n)?e:n)}}function lr(t,n){var e=n-t;return e?cr(t,e):ar(isNaN(t)?n:t)}ce(rr,er,se(fe,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new rr(this.h,this.s,this.l*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new rr(this.h,this.s,this.l*t,this.opacity)},rgb:function(){var t=isNaN(this.h)?0:(this.h+120)*Ce,n=+this.l,e=isNaN(this.s)?0:this.s*n*(1-n),r=Math.cos(t),i=Math.sin(t);return new Se(255*(n+e*(We*r+Ze*i)),255*(n+e*(Ge*r+Je*i)),255*(n+e*(Qe*r)),this.opacity)}}));var hr=function t(n){var e=fr(n);function r(t,n){var r=e((t=Te(t)).r,(n=Te(n)).r),i=e(t.g,n.g),o=e(t.b,n.b),u=e(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=i(n),t.b=o(n),t.opacity=u(n),t+""}}return r.gamma=t,r}(1);function pr(t){return function(n){var e,r,i=n.length,o=new Array(i),u=new Array(i),a=new Array(i);for(e=0;e<i;++e)r=Te(n[e]),o[e]=r.r||0,u[e]=r.g||0,a[e]=r.b||0;return o=t(o),u=t(u),a=t(a),r.opacity=1,function(t){return r.r=o(t),r.g=u(t),r.b=a(t),r+""}}}var dr=pr(or),vr=pr(ur);function _r(t,n){var e,r=n?n.length:0,i=t?Math.min(r,t.length):0,o=new Array(r),u=new Array(r);for(e=0;e<i;++e)o[e]=Mr(t[e],n[e]);for(;e<r;++e)u[e]=n[e];return function(t){for(e=0;e<i;++e)u[e]=o[e](t);return u}}function yr(t,n){var e=new Date;return n-=t=+t,function(r){return e.setTime(t+n*r),e}}function gr(t,n){return n-=t=+t,function(e){return t+n*e}}function mr(t,n){var e,r={},i={};for(e in null!==t&&"object"==typeof t||(t={}),null!==n&&"object"==typeof n||(n={}),n)e in t?r[e]=Mr(t[e],n[e]):i[e]=n[e];return function(t){for(e in r)i[e]=r[e](t);return i}}var xr=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,br=new RegExp(xr.source,"g");function wr(t,n){var e,r,i,o=xr.lastIndex=br.lastIndex=0,u=-1,a=[],c=[];for(t+="",n+="";(e=xr.exec(t))&&(r=br.exec(n));)(i=r.index)>o&&(i=n.slice(o,i),a[u]?a[u]+=i:a[++u]=i),(e=e[0])===(r=r[0])?a[u]?a[u]+=r:a[++u]=r:(a[++u]=null,c.push({i:u,x:gr(e,r)})),o=br.lastIndex;return o<n.length&&(i=n.slice(o),a[u]?a[u]+=i:a[++u]=i),a.length<2?c[0]?function(t){return function(n){return t(n)+""}}(c[0].x):function(t){return function(){return t}}(n):(n=c.length,function(t){for(var e,r=0;r<n;++r)a[(e=c[r]).i]=e.x(t);return a.join("")})}function Mr(t,n){var e,r=typeof n;return null==n||"boolean"===r?ar(n):("number"===r?gr:"string"===r?(e=xe(n))?(n=e,hr):wr:n instanceof xe?hr:n instanceof Date?yr:Array.isArray(n)?_r:isNaN(n)?mr:gr)(t,n)}function Tr(t,n){return n-=t=+t,function(e){return Math.round(t+n*e)}}var Sr,kr,Nr,Ar,Er=180/Math.PI,Cr={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function zr(t,n,e,r,i,o){var u,a,c;return(u=Math.sqrt(t*t+n*n))&&(t/=u,n/=u),(c=t*e+n*r)&&(e-=t*c,r-=n*c),(a=Math.sqrt(e*e+r*r))&&(e/=a,r/=a,c/=a),t*r<n*e&&(t=-t,n=-n,c=-c,u=-u),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*Er,skewX:Math.atan(c)*Er,scaleX:u,scaleY:a}}function Pr(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,u){var a=[],c=[];return o=t(o),u=t(u),function(t,r,i,o,u,a){if(t!==i||r!==o){var c=u.push("translate(",null,n,null,e);a.push({i:c-4,x:gr(t,i)},{i:c-2,x:gr(r,o)})}else(i||o)&&u.push("translate("+i+n+o+e)}(o.translateX,o.translateY,u.translateX,u.translateY,a,c),function(t,n,e,o){t!==n?(t-n>180?n+=360:n-t>180&&(t+=360),o.push({i:e.push(i(e)+"rotate(",null,r)-2,x:gr(t,n)})):n&&e.push(i(e)+"rotate("+n+r)}(o.rotate,u.rotate,a,c),function(t,n,e,o){t!==n?o.push({i:e.push(i(e)+"skewX(",null,r)-2,x:gr(t,n)}):n&&e.push(i(e)+"skewX("+n+r)}(o.skewX,u.skewX,a,c),function(t,n,e,r,o,u){if(t!==e||n!==r){var a=o.push(i(o)+"scale(",null,",",null,")");u.push({i:a-4,x:gr(t,e)},{i:a-2,x:gr(n,r)})}else 1===e&&1===r||o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,u.scaleX,u.scaleY,a,c),o=u=null,function(t){for(var n,e=-1,r=c.length;++e<r;)a[(n=c[e]).i]=n.x(t);return a.join("")}}}var qr=Pr(function(t){return"none"===t?Cr:(Sr||(Sr=document.createElement("DIV"),kr=document.documentElement,Nr=document.defaultView),Sr.style.transform=t,t=Nr.getComputedStyle(kr.appendChild(Sr),null).getPropertyValue("transform"),kr.removeChild(Sr),zr(+(t=t.slice(7,-1).split(","))[0],+t[1],+t[2],+t[3],+t[4],+t[5]))},"px, ","px)","deg)"),Lr=Pr(function(t){return null==t?Cr:(Ar||(Ar=document.createElementNS("http://www.w3.org/2000/svg","g")),Ar.setAttribute("transform",t),(t=Ar.transform.baseVal.consolidate())?zr((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):Cr)},", ",")",")"),Rr=Math.SQRT2,Ur=2,Dr=4,Or=1e-12;function Fr(t){return((t=Math.exp(t))+1/t)/2}function Ir(t,n){var e,r,i=t[0],o=t[1],u=t[2],a=n[0],c=n[1],s=n[2],f=a-i,l=c-o,h=f*f+l*l;if(h<Or)r=Math.log(s/u)/Rr,e=function(t){return[i+t*f,o+t*l,u*Math.exp(Rr*t*r)]};else{var p=Math.sqrt(h),d=(s*s-u*u+Dr*h)/(2*u*Ur*p),v=(s*s-u*u-Dr*h)/(2*s*Ur*p),_=Math.log(Math.sqrt(d*d+1)-d),y=Math.log(Math.sqrt(v*v+1)-v);r=(y-_)/Rr,e=function(t){var n,e=t*r,a=Fr(_),c=u/(Ur*p)*(a*(n=Rr*e+_,((n=Math.exp(2*n))-1)/(n+1))-function(t){return((t=Math.exp(t))-1/t)/2}(_));return[i+c*f,o+c*l,u*a/Fr(Rr*e+_)]}}return e.duration=1e3*r,e}function Yr(t){return function(n,e){var r=t((n=Ne(n)).h,(e=Ne(e)).h),i=lr(n.s,e.s),o=lr(n.l,e.l),u=lr(n.opacity,e.opacity);return function(t){return n.h=r(t),n.s=i(t),n.l=o(t),n.opacity=u(t),n+""}}}var Br=Yr(sr),jr=Yr(lr);function Hr(t){return function(n,e){var r=t((n=Ve(n)).h,(e=Ve(e)).h),i=lr(n.c,e.c),o=lr(n.l,e.l),u=lr(n.opacity,e.opacity);return function(t){return n.h=r(t),n.c=i(t),n.l=o(t),n.opacity=u(t),n+""}}}var Xr=Hr(sr),Vr=Hr(lr);function $r(t){return function n(e){function r(n,r){var i=t((n=er(n)).h,(r=er(r)).h),o=lr(n.s,r.s),u=lr(n.l,r.l),a=lr(n.opacity,r.opacity);return function(t){return n.h=i(t),n.s=o(t),n.l=u(Math.pow(t,e)),n.opacity=a(t),n+""}}return e=+e,r.gamma=n,r}(1)}var Wr=$r(sr),Zr=$r(lr);var Gr={value:function(){}};function Jr(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r)throw new Error("illegal type: "+t);r[t]=[]}return new Qr(r)}function Qr(t){this._=t}function Kr(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}function ti(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=Gr,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}function ni(t){return new Function("d","return {"+t.map(function(t,n){return JSON.stringify(t)+": d["+n+"]"}).join(",")+"}")}function ei(t){var n=new RegExp('["'+t+"\n]"),e=t.charCodeAt(0);function r(t,n){var r,i,o={},u={},a=[],c=t.length,s=0,f=0;function l(){if(s>=c)return u;if(i)return i=!1,o;var n,r=s;if(34===t.charCodeAt(r)){for(var a=r;a++<c;)if(34===t.charCodeAt(a)){if(34!==t.charCodeAt(a+1))break;++a}return s=a+2,13===(n=t.charCodeAt(a+1))?(i=!0,10===t.charCodeAt(a+2)&&++s):10===n&&(i=!0),t.slice(r+1,a).replace(/""/g,'"')}for(;s<c;){var f=1;if(10===(n=t.charCodeAt(s++)))i=!0;else if(13===n)i=!0,10===t.charCodeAt(s)&&(++s,++f);else if(n!==e)continue;return t.slice(r,s-f)}return t.slice(r)}for(;(r=l())!==u;){for(var h=[];r!==o&&r!==u;)h.push(r),r=l();n&&null==(h=n(h,f++))||a.push(h)}return a}function i(n){return n.map(o).join(t)}function o(t){return null==t?"":n.test(t+="")?'"'+t.replace(/\"/g,'""')+'"':t}return{parse:function(t,n){var e,i,o=r(t,function(t,r){if(e)return e(t,r-1);i=t,e=n?function(t,n){var e=ni(t);return function(r,i){return n(e(r),i,t)}}(t,n):ni(t)});return o.columns=i,o},parseRows:r,format:function(n,e){return null==e&&(e=function(t){var n=Object.create(null),e=[];return t.forEach(function(t){for(var r in t)r in n||e.push(n[r]=r)}),e}(n)),[e.map(o).join(t)].concat(n.map(function(n){return e.map(function(t){return o(n[t])}).join(t)})).join("\n")},formatRows:function(t){return t.map(i).join("\n")}}}Qr.prototype=Jr.prototype={constructor:Qr,on:function(t,n){var e,r,i=this._,o=(r=i,(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:n}})),u=-1,a=o.length;if(!(arguments.length<2)){if(null!=n&&"function"!=typeof n)throw new Error("invalid callback: "+n);for(;++u<a;)if(e=(t=o[u]).type)i[e]=ti(i[e],t.name,n);else if(null==n)for(e in i)i[e]=ti(i[e],t.name,null);return this}for(;++u<a;)if((e=(t=o[u]).type)&&(e=Kr(i[e],t.name)))return e},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new Qr(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=new Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,e=(r=this._[t]).length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};var ri=ei(","),ii=ri.parse,oi=ri.parseRows,ui=ri.format,ai=ri.formatRows,ci=ei("\t"),si=ci.parse,fi=ci.parseRows,li=ci.format,hi=ci.formatRows;function pi(t,n){var e,r,i,o,u=Jr("beforesend","progress","load","error"),a=A(),c=new XMLHttpRequest,s=null,f=null,l=0;function h(t){var n,r=c.status;if(!r&&function(t){var n=t.responseType;return n&&"text"!==n?t.response:t.responseText}(c)||r>=200&&r<300||304===r){if(i)try{n=i.call(e,c)}catch(t){return void u.call("error",e,t)}else n=c;u.call("load",e,n)}else u.call("error",e,t)}if("undefined"==typeof XDomainRequest||"withCredentials"in c||!/^(http(s)?:)?\/\//.test(t)||(c=new XDomainRequest),"onload"in c?c.onload=c.onerror=c.ontimeout=h:c.onreadystatechange=function(t){c.readyState>3&&h(t)},c.onprogress=function(t){u.call("progress",e,t)},e={header:function(t,n){return t=(t+"").toLowerCase(),arguments.length<2?a.get(t):(null==n?a.remove(t):a.set(t,n+""),e)},mimeType:function(t){return arguments.length?(r=null==t?null:t+"",e):r},responseType:function(t){return arguments.length?(o=t,e):o},timeout:function(t){return arguments.length?(l=+t,e):l},user:function(t){return arguments.length<1?s:(s=null==t?null:t+"",e)},password:function(t){return arguments.length<1?f:(f=null==t?null:t+"",e)},response:function(t){return i=t,e},get:function(t,n){return e.send("GET",t,n)},post:function(t,n){return e.send("POST",t,n)},send:function(n,i,h){return c.open(n,t,!0,s,f),null==r||a.has("accept")||a.set("accept",r+",*/*"),c.setRequestHeader&&a.each(function(t,n){c.setRequestHeader(n,t)}),null!=r&&c.overrideMimeType&&c.overrideMimeType(r),null!=o&&(c.responseType=o),l>0&&(c.timeout=l),null==h&&"function"==typeof i&&(h=i,i=null),null!=h&&1===h.length&&(h=function(t){return function(n,e){t(null==n?e:null)}}(h)),null!=h&&e.on("error",h).on("load",function(t){h(null,t)}),u.call("beforesend",e,c),c.send(null==i?null:i),e},abort:function(){return c.abort(),e},on:function(){var t=u.on.apply(u,arguments);return t===u?e:t}},null!=n){if("function"!=typeof n)throw new Error("invalid callback: "+n);return e.get(n)}return e}function di(t,n){return function(e,r){var i=pi(e).mimeType(t).response(n);if(null!=r){if("function"!=typeof r)throw new Error("invalid callback: "+r);return i.get(r)}return i}}var vi=di("text/html",function(t){return document.createRange().createContextualFragment(t.responseText)}),_i=di("application/json",function(t){return JSON.parse(t.responseText)}),yi=di("text/plain",function(t){return t.responseText}),gi=di("application/xml",function(t){var n=t.responseXML;if(!n)throw new Error("parse error");return n});function mi(t,n){return function(e,r,i){arguments.length<3&&(i=r,r=null);var o=pi(e).mimeType(t);return o.row=function(t){return arguments.length?o.response(function(t,n){return function(e){return t(e.responseText,n)}}(n,r=t)):r},o.row(r),i?o.get(i):o}}var xi,bi,wi=mi("text/csv",ii),Mi=mi("text/tab-separated-values",si),Ti=0,Si=0,ki=0,Ni=1e3,Ai=0,Ei=0,Ci=0,zi="object"==typeof performance&&performance.now?performance:Date,Pi="function"==typeof requestAnimationFrame?zi===Date?function(t){requestAnimationFrame(function(){t(zi.now())})}:requestAnimationFrame:function(t){setTimeout(t,17)};function qi(){return Ei||(Pi(Li),Ei=zi.now()+Ci)}function Li(){Ei=0}function Ri(){this._call=this._time=this._next=null}function Ui(t,n,e){var r=new Ri;return r.restart(t,n,e),r}function Di(){qi(),++Ti;for(var t,n=xi;n;)(t=Ei-n._time)>=0&&n._call.call(null,t),n=n._next;--Ti}function Oi(t){Ei=(Ai=t||zi.now())+Ci,Ti=Si=0;try{Di()}finally{Ti=0,function(){var t,n,e=xi,r=1/0;for(;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:xi=n);bi=t,Ii(r)}(),Ei=0}}function Fi(){var t=zi.now(),n=t-Ai;n>Ni&&(Ci-=n,Ai=t)}function Ii(t){if(!Ti){Si&&(Si=clearTimeout(Si));var n=t-Ei;n>24?(t<1/0&&(Si=setTimeout(Oi,n)),ki&&(ki=clearInterval(ki))):(ki||(ki=setInterval(Fi,Ni)),Ti=1,Pi(Oi))}}function Yi(t,n,e){var r=new Ri;return n=null==n?0:+n,r.restart(function(e){r.stop(),t(e+n)},n,e),r}Ri.prototype=Ui.prototype={constructor:Ri,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?qi():+e)+(null==n?0:+n),this._next||bi===this||(bi?bi._next=this:xi=this,bi=this),this._call=t,this._time=e,Ii()},stop:function(){this._call&&(this._call=null,this._time=1/0,Ii())}};var Bi=new Date,ji=new Date;function Hi(t,n,e,r){function i(n){return t(n=new Date(+n)),n}return i.floor=i,i.ceil=function(e){return t(e=new Date(e-1)),n(e,1),t(e),e},i.round=function(t){var n=i(t),e=i.ceil(t);return t-n<e-t?n:e},i.offset=function(t,e){return n(t=new Date(+t),null==e?1:Math.floor(e)),t},i.range=function(e,r,o){var u=[];if(e=i.ceil(e),o=null==o?1:Math.floor(o),!(e<r&&o>0))return u;do{u.push(new Date(+e))}while(n(e,o),t(e),e<r);return u},i.filter=function(e){return Hi(function(n){for(;t(n),!e(n);)n.setTime(n-1)},function(t,r){for(;--r>=0;)for(;n(t,1),!e(t););})},e&&(i.count=function(n,r){return Bi.setTime(+n),ji.setTime(+r),t(Bi),t(ji),Math.floor(e(Bi,ji))},i.every=function(t){return t=Math.floor(t),isFinite(t)&&t>0?t>1?i.filter(r?function(n){return r(n)%t==0}:function(n){return i.count(0,n)%t==0}):i:null}),i}var Xi=Hi(function(){},function(t,n){t.setTime(+t+n)},function(t,n){return n-t});Xi.every=function(t){return t=Math.floor(t),isFinite(t)&&t>0?t>1?Hi(function(n){n.setTime(Math.floor(n/t)*t)},function(n,e){n.setTime(+n+e*t)},function(n,e){return(e-n)/t}):Xi:null};var Vi=Xi.range,$i=6e4,Wi=6048e5,Zi=Hi(function(t){t.setTime(1e3*Math.floor(t/1e3))},function(t,n){t.setTime(+t+1e3*n)},function(t,n){return(n-t)/1e3},function(t){return t.getUTCSeconds()}),Gi=Zi.range,Ji=Hi(function(t){t.setTime(Math.floor(t/$i)*$i)},function(t,n){t.setTime(+t+n*$i)},function(t,n){return(n-t)/$i},function(t){return t.getMinutes()}),Qi=Ji.range,Ki=Hi(function(t){var n=t.getTimezoneOffset()*$i%36e5;n<0&&(n+=36e5),t.setTime(36e5*Math.floor((+t-n)/36e5)+n)},function(t,n){t.setTime(+t+36e5*n)},function(t,n){return(n-t)/36e5},function(t){return t.getHours()}),to=Ki.range,no=Hi(function(t){t.setHours(0,0,0,0)},function(t,n){t.setDate(t.getDate()+n)},function(t,n){return(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*$i)/864e5},function(t){return t.getDate()-1}),eo=no.range;function ro(t){return Hi(function(n){n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)},function(t,n){t.setDate(t.getDate()+7*n)},function(t,n){return(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*$i)/Wi})}var io=ro(0),oo=ro(1),uo=ro(2),ao=ro(3),co=ro(4),so=ro(5),fo=ro(6),lo=io.range,ho=oo.range,po=uo.range,vo=ao.range,_o=co.range,yo=so.range,go=fo.range,mo=Hi(function(t){t.setDate(1),t.setHours(0,0,0,0)},function(t,n){t.setMonth(t.getMonth()+n)},function(t,n){return n.getMonth()-t.getMonth()+12*(n.getFullYear()-t.getFullYear())},function(t){return t.getMonth()}),xo=mo.range,bo=Hi(function(t){t.setMonth(0,1),t.setHours(0,0,0,0)},function(t,n){t.setFullYear(t.getFullYear()+n)},function(t,n){return n.getFullYear()-t.getFullYear()},function(t){return t.getFullYear()});bo.every=function(t){return isFinite(t=Math.floor(t))&&t>0?Hi(function(n){n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)},function(n,e){n.setFullYear(n.getFullYear()+e*t)}):null};var wo=bo.range,Mo=Hi(function(t){t.setUTCSeconds(0,0)},function(t,n){t.setTime(+t+n*$i)},function(t,n){return(n-t)/$i},function(t){return t.getUTCMinutes()}),To=Mo.range,So=Hi(function(t){t.setUTCMinutes(0,0,0)},function(t,n){t.setTime(+t+36e5*n)},function(t,n){return(n-t)/36e5},function(t){return t.getUTCHours()}),ko=So.range,No=Hi(function(t){t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCDate(t.getUTCDate()+n)},function(t,n){return(n-t)/864e5},function(t){return t.getUTCDate()-1}),Ao=No.range;function Eo(t){return Hi(function(n){n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)},function(t,n){t.setUTCDate(t.getUTCDate()+7*n)},function(t,n){return(n-t)/Wi})}var Co=Eo(0),zo=Eo(1),Po=Eo(2),qo=Eo(3),Lo=Eo(4),Ro=Eo(5),Uo=Eo(6),Do=Co.range,Oo=zo.range,Fo=Po.range,Io=qo.range,Yo=Lo.range,Bo=Ro.range,jo=Uo.range,Ho=Hi(function(t){t.setUTCDate(1),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCMonth(t.getUTCMonth()+n)},function(t,n){return n.getUTCMonth()-t.getUTCMonth()+12*(n.getUTCFullYear()-t.getUTCFullYear())},function(t){return t.getUTCMonth()}),Xo=Ho.range,Vo=Hi(function(t){t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCFullYear(t.getUTCFullYear()+n)},function(t,n){return n.getUTCFullYear()-t.getUTCFullYear()},function(t){return t.getUTCFullYear()});Vo.every=function(t){return isFinite(t=Math.floor(t))&&t>0?Hi(function(n){n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)},function(n,e){n.setUTCFullYear(n.getUTCFullYear()+e*t)}):null};var $o,Wo=Vo.range;function Zo(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,r=t.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+t.slice(e+1)]}function Go(t){return(t=Zo(Math.abs(t)))?t[1]:NaN}function Jo(t,n){var e=Zo(t,n);if(!e)return t+"";var r=e[0],i=e[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}var Qo={"":function(t,n){t:for(var e,r=(t=t.toPrecision(n)).length,i=1,o=-1;i<r;++i)switch(t[i]){case".":o=e=i;break;case"0":0===o&&(o=i),e=i;break;case"e":break t;default:o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t},"%":function(t,n){return(100*t).toFixed(n)},b:function(t){return Math.round(t).toString(2)},c:function(t){return t+""},d:function(t){return Math.round(t).toString(10)},e:function(t,n){return t.toExponential(n)},f:function(t,n){return t.toFixed(n)},g:function(t,n){return t.toPrecision(n)},o:function(t){return Math.round(t).toString(8)},p:function(t,n){return Jo(100*t,n)},r:Jo,s:function(t,n){var e=Zo(t,n);if(!e)return t+"";var r=e[0],i=e[1],o=i-($o=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,u=r.length;return o===u?r:o>u?r+new Array(o-u+1).join("0"):o>0?r.slice(0,o)+"."+r.slice(o):"0."+new Array(1-o).join("0")+Zo(t,Math.max(0,n+o-1))[0]},X:function(t){return Math.round(t).toString(16).toUpperCase()},x:function(t){return Math.round(t).toString(16)}},Ko=/^(?:(.)?([<>=^]))?([+\-\( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?([a-z%])?$/i;function tu(t){return new nu(t)}function nu(t){if(!(n=Ko.exec(t)))throw new Error("invalid format: "+t);var n,e=n[1]||" ",r=n[2]||">",i=n[3]||"-",o=n[4]||"",u=!!n[5],a=n[6]&&+n[6],c=!!n[7],s=n[8]&&+n[8].slice(1),f=n[9]||"";"n"===f?(c=!0,f="g"):Qo[f]||(f=""),(u||"0"===e&&"="===r)&&(u=!0,e="0",r="="),this.fill=e,this.align=r,this.sign=i,this.symbol=o,this.zero=u,this.width=a,this.comma=c,this.precision=s,this.type=f}nu.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(null==this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(null==this.precision?"":"."+Math.max(0,0|this.precision))+this.type};var eu,ru=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function iu(t){return t}function ou(t){var n,e,r=t.grouping&&t.thousands?(n=t.grouping,e=t.thousands,function(t,r){for(var i=t.length,o=[],u=0,a=n[0],c=0;i>0&&a>0&&(c+a+1>r&&(a=Math.max(1,r-c)),o.push(t.substring(i-=a,i+a)),!((c+=a+1)>r));)a=n[u=(u+1)%n.length];return o.reverse().join(e)}):iu,i=t.currency,o=t.decimal;function u(t){var n=(t=tu(t)).fill,e=t.align,u=t.sign,a=t.symbol,c=t.zero,s=t.width,f=t.comma,l=t.precision,h=t.type,p="$"===a?i[0]:"#"===a&&/[boxX]/.test(h)?"0"+h.toLowerCase():"",d="$"===a?i[1]:/[%p]/.test(h)?"%":"",v=Qo[h],_=!h||/[defgprs%]/.test(h);function y(t){var i,a,y,g=p,m=d;if("c"===h)m=v(t)+m,t="";else{var x=((t=+t)<0||1/t<0)&&(t*=-1,!0);if(t=v(t,l),x)for(i=-1,a=t.length,x=!1;++i<a;)if(48<(y=t.charCodeAt(i))&&y<58||"x"===h&&96<y&&y<103||"X"===h&&64<y&&y<71){x=!0;break}if(g=(x?"("===u?u:"-":"-"===u||"("===u?"":u)+g,m=m+("s"===h?ru[8+$o/3]:"")+(x&&"("===u?")":""),_)for(i=-1,a=t.length;++i<a;)if(48>(y=t.charCodeAt(i))||y>57){m=(46===y?o+t.slice(i+1):t.slice(i))+m,t=t.slice(0,i);break}}f&&!c&&(t=r(t,1/0));var b=g.length+t.length+m.length,w=b<s?new Array(s-b+1).join(n):"";switch(f&&c&&(t=r(w+t,w.length?s-m.length:1/0),w=""),e){case"<":return g+t+m+w;case"=":return g+w+t+m;case"^":return w.slice(0,b=w.length>>1)+g+t+m+w.slice(b)}return w+g+t+m}return l=null==l?h?6:12:/[gprs]/.test(h)?Math.max(1,Math.min(21,l)):Math.max(0,Math.min(20,l)),y.toString=function(){return t+""},y}return{format:u,formatPrefix:function(t,n){var e=u(((t=tu(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(Go(n)/3))),i=Math.pow(10,-r),o=ru[8+r/3];return function(t){return e(i*t)+o}}}}function uu(n){return eu=ou(n),t.format=eu.format,t.formatPrefix=eu.formatPrefix,eu}function au(t){return Math.max(0,-Go(Math.abs(t)))}function cu(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Go(n)/3)))-Go(Math.abs(t)))}function su(t,n){return t=Math.abs(t),n=Math.abs(n)-t,Math.max(0,Go(n)-Go(t))+1}function fu(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return n.setFullYear(t.y),n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function lu(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return n.setUTCFullYear(t.y),n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function hu(t){return{y:t,m:0,d:1,H:0,M:0,S:0,L:0}}function pu(t){var n=t.dateTime,e=t.date,r=t.time,i=t.periods,o=t.days,u=t.shortDays,a=t.months,c=t.shortMonths,s=bu(i),f=wu(i),l=bu(o),h=wu(o),p=bu(u),d=wu(u),v=bu(a),_=wu(a),y=bu(c),g=wu(c),m={a:function(t){return u[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return a[t.getMonth()]},c:null,d:Du,e:Du,H:Ou,I:Fu,j:Iu,L:Yu,m:Bu,M:ju,p:function(t){return i[+(t.getHours()>=12)]},S:Hu,U:Xu,w:Vu,W:$u,x:null,X:null,y:Wu,Y:Zu,Z:Gu,"%":la},x={a:function(t){return u[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return a[t.getUTCMonth()]},c:null,d:Ju,e:Ju,H:Qu,I:Ku,j:ta,L:na,m:ea,M:ra,p:function(t){return i[+(t.getUTCHours()>=12)]},S:ia,U:oa,w:ua,W:aa,x:null,X:null,y:ca,Y:sa,Z:fa,"%":la},b={a:function(t,n,e){var r=p.exec(n.slice(e));return r?(t.w=d[r[0].toLowerCase()],e+r[0].length):-1},A:function(t,n,e){var r=l.exec(n.slice(e));return r?(t.w=h[r[0].toLowerCase()],e+r[0].length):-1},b:function(t,n,e){var r=y.exec(n.slice(e));return r?(t.m=g[r[0].toLowerCase()],e+r[0].length):-1},B:function(t,n,e){var r=v.exec(n.slice(e));return r?(t.m=_[r[0].toLowerCase()],e+r[0].length):-1},c:function(t,e,r){return T(t,n,e,r)},d:Cu,e:Cu,H:Pu,I:Pu,j:zu,L:Ru,m:Eu,M:qu,p:function(t,n,e){var r=s.exec(n.slice(e));return r?(t.p=f[r[0].toLowerCase()],e+r[0].length):-1},S:Lu,U:Tu,w:Mu,W:Su,x:function(t,n,r){return T(t,e,n,r)},X:function(t,n,e){return T(t,r,n,e)},y:Nu,Y:ku,Z:Au,"%":Uu};function w(t,n){return function(e){var r,i,o,u=[],a=-1,c=0,s=t.length;for(e instanceof Date||(e=new Date(+e));++a<s;)37===t.charCodeAt(a)&&(u.push(t.slice(c,a)),null!=(i=vu[r=t.charAt(++a)])?r=t.charAt(++a):i="e"===r?" ":"0",(o=n[r])&&(r=o(e,i)),u.push(r),c=a+1);return u.push(t.slice(c,a)),u.join("")}}function M(t,n){return function(e){var r=hu(1900);if(T(r,t,e+="",0)!=e.length)return null;if("p"in r&&(r.H=r.H%12+12*r.p),"W"in r||"U"in r){"w"in r||(r.w="W"in r?1:0);var i="Z"in r?lu(hu(r.y)).getUTCDay():n(hu(r.y)).getDay();r.m=0,r.d="W"in r?(r.w+6)%7+7*r.W-(i+5)%7:r.w+7*r.U-(i+6)%7}return"Z"in r?(r.H+=r.Z/100|0,r.M+=r.Z%100,lu(r)):n(r)}}function T(t,n,e,r){for(var i,o,u=0,a=n.length,c=e.length;u<a;){if(r>=c)return-1;if(37===(i=n.charCodeAt(u++))){if(i=n.charAt(u++),!(o=b[i in vu?n.charAt(u++):i])||(r=o(t,e,r))<0)return-1}else if(i!=e.charCodeAt(r++))return-1}return r}return m.x=w(e,m),m.X=w(r,m),m.c=w(n,m),x.x=w(e,x),x.X=w(r,x),x.c=w(n,x),{format:function(t){var n=w(t+="",m);return n.toString=function(){return t},n},parse:function(t){var n=M(t+="",fu);return n.toString=function(){return t},n},utcFormat:function(t){var n=w(t+="",x);return n.toString=function(){return t},n},utcParse:function(t){var n=M(t,lu);return n.toString=function(){return t},n}}}uu({decimal:".",thousands:",",grouping:[3],currency:["$",""]});var du,vu={"-":"",_:" ",0:"0"},_u=/^\s*\d+/,yu=/^%/,gu=/[\\\^\$\*\+\?\|\[\]\(\)\.\{\}]/g;function mu(t,n,e){var r=t<0?"-":"",i=(r?-t:t)+"",o=i.length;return r+(o<e?new Array(e-o+1).join(n)+i:i)}function xu(t){return t.replace(gu,"\\$&")}function bu(t){return new RegExp("^(?:"+t.map(xu).join("|")+")","i")}function wu(t){for(var n={},e=-1,r=t.length;++e<r;)n[t[e].toLowerCase()]=e;return n}function Mu(t,n,e){var r=_u.exec(n.slice(e,e+1));return r?(t.w=+r[0],e+r[0].length):-1}function Tu(t,n,e){var r=_u.exec(n.slice(e));return r?(t.U=+r[0],e+r[0].length):-1}function Su(t,n,e){var r=_u.exec(n.slice(e));return r?(t.W=+r[0],e+r[0].length):-1}function ku(t,n,e){var r=_u.exec(n.slice(e,e+4));return r?(t.y=+r[0],e+r[0].length):-1}function Nu(t,n,e){var r=_u.exec(n.slice(e,e+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),e+r[0].length):-1}function Au(t,n,e){var r=/^(Z)|([+-]\d\d)(?:\:?(\d\d))?/.exec(n.slice(e,e+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),e+r[0].length):-1}function Eu(t,n,e){var r=_u.exec(n.slice(e,e+2));return r?(t.m=r[0]-1,e+r[0].length):-1}function Cu(t,n,e){var r=_u.exec(n.slice(e,e+2));return r?(t.d=+r[0],e+r[0].length):-1}function zu(t,n,e){var r=_u.exec(n.slice(e,e+3));return r?(t.m=0,t.d=+r[0],e+r[0].length):-1}function Pu(t,n,e){var r=_u.exec(n.slice(e,e+2));return r?(t.H=+r[0],e+r[0].length):-1}function qu(t,n,e){var r=_u.exec(n.slice(e,e+2));return r?(t.M=+r[0],e+r[0].length):-1}function Lu(t,n,e){var r=_u.exec(n.slice(e,e+2));return r?(t.S=+r[0],e+r[0].length):-1}function Ru(t,n,e){var r=_u.exec(n.slice(e,e+3));return r?(t.L=+r[0],e+r[0].length):-1}function Uu(t,n,e){var r=yu.exec(n.slice(e,e+1));return r?e+r[0].length:-1}function Du(t,n){return mu(t.getDate(),n,2)}function Ou(t,n){return mu(t.getHours(),n,2)}function Fu(t,n){return mu(t.getHours()%12||12,n,2)}function Iu(t,n){return mu(1+no.count(bo(t),t),n,3)}function Yu(t,n){return mu(t.getMilliseconds(),n,3)}function Bu(t,n){return mu(t.getMonth()+1,n,2)}function ju(t,n){return mu(t.getMinutes(),n,2)}function Hu(t,n){return mu(t.getSeconds(),n,2)}function Xu(t,n){return mu(io.count(bo(t),t),n,2)}function Vu(t){return t.getDay()}function $u(t,n){return mu(oo.count(bo(t),t),n,2)}function Wu(t,n){return mu(t.getFullYear()%100,n,2)}function Zu(t,n){return mu(t.getFullYear()%1e4,n,4)}function Gu(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+mu(n/60|0,"0",2)+mu(n%60,"0",2)}function Ju(t,n){return mu(t.getUTCDate(),n,2)}function Qu(t,n){return mu(t.getUTCHours(),n,2)}function Ku(t,n){return mu(t.getUTCHours()%12||12,n,2)}function ta(t,n){return mu(1+No.count(Vo(t),t),n,3)}function na(t,n){return mu(t.getUTCMilliseconds(),n,3)}function ea(t,n){return mu(t.getUTCMonth()+1,n,2)}function ra(t,n){return mu(t.getUTCMinutes(),n,2)}function ia(t,n){return mu(t.getUTCSeconds(),n,2)}function oa(t,n){return mu(Co.count(Vo(t),t),n,2)}function ua(t){return t.getUTCDay()}function aa(t,n){return mu(zo.count(Vo(t),t),n,2)}function ca(t,n){return mu(t.getUTCFullYear()%100,n,2)}function sa(t,n){return mu(t.getUTCFullYear()%1e4,n,4)}function fa(){return"+0000"}function la(){return"%"}function ha(n){return du=pu(n),t.timeFormat=du.format,t.timeParse=du.parse,t.utcFormat=du.utcFormat,t.utcParse=du.utcParse,du}ha({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var pa=Date.prototype.toISOString?function(t){return t.toISOString()}:t.utcFormat("%Y-%m-%dT%H:%M:%S.%LZ");var da=+new Date("2000-01-01T00:00:00.000Z")?function(t){var n=new Date(t);return isNaN(n)?null:n}:t.utcParse("%Y-%m-%dT%H:%M:%S.%LZ"),va=Array.prototype,_a=va.map,ya=va.slice,ga={name:"implicit"};function ma(t){var n=A(),e=[],r=ga;function i(i){var o=i+"",u=n.get(o);if(!u){if(r!==ga)return r;n.set(o,u=e.push(i))}return t[(u-1)%t.length]}return t=null==t?[]:ya.call(t),i.domain=function(t){if(!arguments.length)return e.slice();e=[],n=A();for(var r,o,u=-1,a=t.length;++u<a;)n.has(o=(r=t[u])+"")||n.set(o,e.push(r));return i},i.range=function(n){return arguments.length?(t=ya.call(n),i):t.slice()},i.unknown=function(t){return arguments.length?(r=t,i):r},i.copy=function(){return ma().domain(e).range(t).unknown(r)},i}function xa(){var t,n,e=ma().unknown(void 0),r=e.domain,i=e.range,o=[0,1],u=!1,a=0,c=0,s=.5;function f(){var e=r().length,f=o[1]<o[0],l=o[f-0],h=o[1-f];t=(h-l)/Math.max(1,e-a+2*c),u&&(t=Math.floor(t)),l+=(h-l-t*(e-a))*s,n=t*(1-a),u&&(l=Math.round(l),n=Math.round(n));var p=v(e).map(function(n){return l+t*n});return i(f?p.reverse():p)}return delete e.unknown,e.domain=function(t){return arguments.length?(r(t),f()):r()},e.range=function(t){return arguments.length?(o=[+t[0],+t[1]],f()):o.slice()},e.rangeRound=function(t){return o=[+t[0],+t[1]],u=!0,f()},e.bandwidth=function(){return n},e.step=function(){return t},e.round=function(t){return arguments.length?(u=!!t,f()):u},e.padding=function(t){return arguments.length?(a=c=Math.max(0,Math.min(1,t)),f()):a},e.paddingInner=function(t){return arguments.length?(a=Math.max(0,Math.min(1,t)),f()):a},e.paddingOuter=function(t){return arguments.length?(c=Math.max(0,Math.min(1,t)),f()):c},e.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},e.copy=function(){return xa().domain(r()).range(o).round(u).paddingInner(a).paddingOuter(c).align(s)},f()}function ba(t){return function(){return t}}function wa(t){return+t}var Ma=[0,1];function Ta(t,n){return(n-=t=+t)?function(e){return(e-t)/n}:ba(n)}function Sa(t,n,e,r){var i=t[0],o=t[1],u=n[0],a=n[1];return o<i?(i=e(o,i),u=r(a,u)):(i=e(i,o),u=r(u,a)),function(t){return u(i(t))}}function ka(t,n,e,r){var o=Math.min(t.length,n.length)-1,u=new Array(o),a=new Array(o),c=-1;for(t[o]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++c<o;)u[c]=e(t[c],t[c+1]),a[c]=r(n[c],n[c+1]);return function(n){var e=i(t,n,1,o)-1;return a[e](u[e](n))}}function Na(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp())}function Aa(t,n){var e,r,i,o=Ma,u=Ma,a=Mr,c=!1;function s(){return e=Math.min(o.length,u.length)>2?ka:Sa,r=i=null,f}function f(n){return(r||(r=e(o,u,c?(i=t,function(t,n){var e=i(t=+t,n=+n);return function(r){return r<=t?0:r>=n?1:e(r)}}):t,a)))(+n);var i}return f.invert=function(t){return(i||(i=e(u,o,Ta,c?function(t){return function(n,e){var r=t(n=+n,e=+e);return function(t){return t<=0?n:t>=1?e:r(t)}}}(n):n)))(+t)},f.domain=function(t){return arguments.length?(o=_a.call(t,wa),s()):o.slice()},f.range=function(t){return arguments.length?(u=ya.call(t),s()):u.slice()},f.rangeRound=function(t){return u=ya.call(t),a=Tr,s()},f.clamp=function(t){return arguments.length?(c=!!t,s()):c},f.interpolate=function(t){return arguments.length?(a=t,s()):a},s()}function Ea(n){var e=n.domain;return n.ticks=function(t){var n=e();return m(n[0],n[n.length-1],null==t?10:t)},n.tickFormat=function(n,r){return function(n,e,r){var i,o=n[0],u=n[n.length-1],a=x(o,u,null==e?10:e);switch((r=tu(null==r?",f":r)).type){case"s":var c=Math.max(Math.abs(o),Math.abs(u));return null!=r.precision||isNaN(i=cu(a,c))||(r.precision=i),t.formatPrefix(r,c);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(i=su(a,Math.max(Math.abs(o),Math.abs(u))))||(r.precision=i-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(i=au(a))||(r.precision=i-2*("%"===r.type))}return t.format(r)}(e(),n,r)},n.nice=function(t){var r=e(),i=r.length-1,o=null==t?10:t,u=r[0],a=r[i],c=x(u,a,o);return c&&(c=x(Math.floor(u/c)*c,Math.ceil(a/c)*c,o),r[0]=Math.floor(u/c)*c,r[i]=Math.ceil(a/c)*c,e(r)),n},n}function Ca(t,n){var e,r=0,i=(t=t.slice()).length-1,o=t[r],u=t[i];return u<o&&(e=r,r=i,i=e,e=o,o=u,u=e),t[r]=n.floor(o),t[i]=n.ceil(u),t}function za(t,n){return(n=Math.log(n/t))?function(e){return Math.log(e/t)/n}:ba(n)}function Pa(t,n){return t<0?function(e){return-Math.pow(-n,e)*Math.pow(-t,1-e)}:function(e){return Math.pow(n,e)*Math.pow(t,1-e)}}function qa(t){return isFinite(t)?+("1e"+t):t<0?0:t}function La(t){return 10===t?qa:t===Math.E?Math.exp:function(n){return Math.pow(t,n)}}function Ra(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),function(n){return Math.log(n)/t})}function Ua(t){return function(n){return-t(-n)}}function Da(t,n){return t<0?-Math.pow(-t,n):Math.pow(t,n)}function Oa(){var t=1,n=Aa(function(n,e){return(e=Da(e,t)-(n=Da(n,t)))?function(r){return(Da(r,t)-n)/e}:ba(e)},function(n,e){return e=Da(e,t)-(n=Da(n,t)),function(r){return Da(n+e*r,1/t)}}),e=n.domain;return n.exponent=function(n){return arguments.length?(t=+n,e(e())):t},n.copy=function(){return Na(n,Oa().exponent(t))},Ea(n)}var Fa=1e3,Ia=60*Fa,Ya=60*Ia,Ba=24*Ya,ja=7*Ba,Ha=30*Ba,Xa=365*Ba;function Va(t){return new Date(t)}function $a(t){return t instanceof Date?+t:+new Date(+t)}function Wa(t,n,r,i,o,u,a,c,s){var f=Aa(Ta,gr),l=f.invert,h=f.domain,p=s(".%L"),d=s(":%S"),v=s("%I:%M"),_=s("%I %p"),y=s("%a %d"),g=s("%b %d"),m=s("%B"),b=s("%Y"),w=[[a,1,Fa],[a,5,5*Fa],[a,15,15*Fa],[a,30,30*Fa],[u,1,Ia],[u,5,5*Ia],[u,15,15*Ia],[u,30,30*Ia],[o,1,Ya],[o,3,3*Ya],[o,6,6*Ya],[o,12,12*Ya],[i,1,Ba],[i,2,2*Ba],[r,1,ja],[n,1,Ha],[n,3,3*Ha],[t,1,Xa]];function M(e){return(a(e)<e?p:u(e)<e?d:o(e)<e?v:i(e)<e?_:n(e)<e?r(e)<e?y:g:t(e)<e?m:b)(e)}function T(n,r,i,o){if(null==n&&(n=10),"number"==typeof n){var u=Math.abs(i-r)/n,a=e(function(t){return t[2]}).right(w,u);a===w.length?(o=x(r/Xa,i/Xa,n),n=t):a?(o=(a=w[u/w[a-1][2]<w[a][2]/u?a-1:a])[1],n=a[0]):(o=x(r,i,n),n=c)}return null==o?n:n.every(o)}return f.invert=function(t){return new Date(l(t))},f.domain=function(t){return arguments.length?h(_a.call(t,$a)):h().map(Va)},f.ticks=function(t,n){var e,r=h(),i=r[0],o=r[r.length-1],u=o<i;return u&&(e=i,i=o,o=e),e=(e=T(t,i,o,n))?e.range(i,o+1):[],u?e.reverse():e},f.tickFormat=function(t,n){return null==n?M:s(n)},f.nice=function(t,n){var e=h();return(t=T(t,e[0],e[e.length-1],n))?h(Ca(e,t)):f},f.copy=function(){return Na(f,Wa(t,n,r,i,o,u,a,c,s))},f}function Za(t){return t.match(/.{6}/g).map(function(t){return"#"+t})}var Ga=Za("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf"),Ja=Za("393b795254a36b6ecf9c9ede6379398ca252b5cf6bcedb9c8c6d31bd9e39e7ba52e7cb94843c39ad494ad6616be7969c7b4173a55194ce6dbdde9ed6"),Qa=Za("3182bd6baed69ecae1c6dbefe6550dfd8d3cfdae6bfdd0a231a35474c476a1d99bc7e9c0756bb19e9ac8bcbddcdadaeb636363969696bdbdbdd9d9d9"),Ka=Za("1f77b4aec7e8ff7f0effbb782ca02c98df8ad62728ff98969467bdc5b0d58c564bc49c94e377c2f7b6d27f7f7fc7c7c7bcbd22dbdb8d17becf9edae5"),tc=Zr(er(300,.5,0),er(-240,.5,1)),nc=Zr(er(-100,.75,.35),er(80,1.5,.8)),ec=Zr(er(260,.75,.35),er(80,1.5,.8)),rc=er();function ic(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}var oc=ic(Za("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725")),uc=ic(Za("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf")),ac=ic(Za("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4")),cc=ic(Za("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921"));var sc="http://www.w3.org/1999/xhtml",fc={svg:"http://www.w3.org/2000/svg",xhtml:sc,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function lc(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),fc.hasOwnProperty(n)?{space:fc[n],local:t}:t}function hc(t){var n=lc(t);return(n.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===sc&&n.documentElement.namespaceURI===sc?n.createElement(t):n.createElementNS(e,t)}})(n)}var pc=0;function dc(){return new vc}function vc(){this._="@"+(++pc).toString(36)}vc.prototype=dc.prototype={constructor:vc,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}};var _c=function(t){return function(){return this.matches(t)}};if("undefined"!=typeof document){var yc=document.documentElement;if(!yc.matches){var gc=yc.webkitMatchesSelector||yc.msMatchesSelector||yc.mozMatchesSelector||yc.oMatchesSelector;_c=function(t){return function(){return gc.call(this,t)}}}}var mc=_c,xc={};(t.event=null,"undefined"!=typeof document)&&("onmouseenter"in document.documentElement||(xc={mouseenter:"mouseover",mouseleave:"mouseout"}));function bc(t,n,e){return t=wc(t,n,e),function(n){var e=n.relatedTarget;e&&(e===this||8&e.compareDocumentPosition(this))||t.call(this,n)}}function wc(n,e,r){return function(i){var o=t.event;t.event=i;try{n.call(this,this.__data__,e,r)}finally{t.event=o}}}function Mc(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)e=n[r],t.type&&e.type!==t.type||e.name!==t.name?n[++i]=e:this.removeEventListener(e.type,e.listener,e.capture);++i?n.length=i:delete this.__on}}}function Tc(t,n,e){var r=xc.hasOwnProperty(t.type)?bc:wc;return function(i,o,u){var a,c=this.__on,s=r(n,o,u);if(c)for(var f=0,l=c.length;f<l;++f)if((a=c[f]).type===t.type&&a.name===t.name)return this.removeEventListener(a.type,a.listener,a.capture),this.addEventListener(a.type,a.listener=s,a.capture=e),void(a.value=n);this.addEventListener(t.type,s,e),a={type:t.type,name:t.name,value:n,listener:s,capture:e},c?c.push(a):this.__on=[a]}}function Sc(n,e,r,i){var o=t.event;n.sourceEvent=t.event,t.event=n;try{return e.apply(r,i)}finally{t.event=o}}function kc(){for(var n,e=t.event;n=e.sourceEvent;)e=n;return e}function Nc(t,n){var e=t.ownerSVGElement||t;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=n.clientX,r.y=n.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}var i=t.getBoundingClientRect();return[n.clientX-i.left-t.clientLeft,n.clientY-i.top-t.clientTop]}function Ac(t){var n=kc();return n.changedTouches&&(n=n.changedTouches[0]),Nc(t,n)}function Ec(){}function Cc(t){return null==t?Ec:function(){return this.querySelector(t)}}function zc(){return[]}function Pc(t){return null==t?zc:function(){return this.querySelectorAll(t)}}function qc(t){return new Array(t.length)}function Lc(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}Lc.prototype={constructor:Lc,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var Rc="$";function Uc(t,n,e,r,i,o){for(var u,a=0,c=n.length,s=o.length;a<s;++a)(u=n[a])?(u.__data__=o[a],r[a]=u):e[a]=new Lc(t,o[a]);for(;a<c;++a)(u=n[a])&&(i[a]=u)}function Dc(t,n,e,r,i,o,u){var a,c,s,f={},l=n.length,h=o.length,p=new Array(l);for(a=0;a<l;++a)(c=n[a])&&(p[a]=s=Rc+u.call(c,c.__data__,a,n),s in f?i[a]=c:f[s]=c);for(a=0;a<h;++a)(c=f[s=Rc+u.call(t,o[a],a,o)])?(r[a]=c,c.__data__=o[a],f[s]=null):e[a]=new Lc(t,o[a]);for(a=0;a<l;++a)(c=n[a])&&f[p[a]]===c&&(i[a]=c)}function Oc(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function Fc(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function Ic(t){return t.trim().split(/^|\s+/)}function Yc(t){return t.classList||new Bc(t)}function Bc(t){this._node=t,this._names=Ic(t.getAttribute("class")||"")}function jc(t,n){for(var e=Yc(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function Hc(t,n){for(var e=Yc(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function Xc(){this.textContent=""}function Vc(){this.innerHTML=""}function $c(){this.nextSibling&&this.parentNode.appendChild(this)}function Wc(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Zc(){return null}function Gc(){var t=this.parentNode;t&&t.removeChild(this)}function Jc(t,n,e){var r=Fc(t),i=r.CustomEvent;i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}Bc.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var Qc=[null];function Kc(t,n){this._groups=t,this._parents=n}function ts(){return new Kc([[document.documentElement]],Qc)}function ns(t){return"string"==typeof t?new Kc([[document.querySelector(t)]],[document.documentElement]):new Kc([[t]],Qc)}function es(t,n,e){arguments.length<3&&(e=n,n=kc().changedTouches);for(var r,i=0,o=n?n.length:0;i<o;++i)if((r=n[i]).identifier===e)return Nc(t,r);return null}Kc.prototype=ts.prototype={constructor:Kc,select:function(t){"function"!=typeof t&&(t=Cc(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,u,a=n[i],c=a.length,s=r[i]=new Array(c),f=0;f<c;++f)(o=a[f])&&(u=t.call(o,o.__data__,f,a))&&("__data__"in o&&(u.__data__=o.__data__),s[f]=u);return new Kc(r,this._parents)},selectAll:function(t){"function"!=typeof t&&(t=Pc(t));for(var n=this._groups,e=n.length,r=[],i=[],o=0;o<e;++o)for(var u,a=n[o],c=a.length,s=0;s<c;++s)(u=a[s])&&(r.push(t.call(u,u.__data__,s,a)),i.push(u));return new Kc(r,i)},filter:function(t){"function"!=typeof t&&(t=mc(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,u=n[i],a=u.length,c=r[i]=[],s=0;s<a;++s)(o=u[s])&&t.call(o,o.__data__,s,u)&&c.push(o);return new Kc(r,this._parents)},data:function(t,n){if(!t)return d=new Array(this.size()),f=-1,this.each(function(t){d[++f]=t}),d;var e,r=n?Dc:Uc,i=this._parents,o=this._groups;"function"!=typeof t&&(e=t,t=function(){return e});for(var u=o.length,a=new Array(u),c=new Array(u),s=new Array(u),f=0;f<u;++f){var l=i[f],h=o[f],p=h.length,d=t.call(l,l&&l.__data__,f,i),v=d.length,_=c[f]=new Array(v),y=a[f]=new Array(v);r(l,h,_,y,s[f]=new Array(p),d,n);for(var g,m,x=0,b=0;x<v;++x)if(g=_[x]){for(x>=b&&(b=x+1);!(m=y[b])&&++b<v;);g._next=m||null}}return(a=new Kc(a,i))._enter=c,a._exit=s,a},enter:function(){return new Kc(this._enter||this._groups.map(qc),this._parents)},exit:function(){return new Kc(this._exit||this._groups.map(qc),this._parents)},merge:function(t){for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=new Array(r),a=0;a<o;++a)for(var c,s=n[a],f=e[a],l=s.length,h=u[a]=new Array(l),p=0;p<l;++p)(c=s[p]||f[p])&&(h[p]=c);for(;a<r;++a)u[a]=n[a];return new Kc(u,this._parents)},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,u=i[o];--o>=0;)(r=i[o])&&(u&&u!==r.nextSibling&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=Oc);for(var e=this._groups,r=e.length,i=new Array(r),o=0;o<r;++o){for(var u,a=e[o],c=a.length,s=i[o]=new Array(c),f=0;f<c;++f)(u=a[f])&&(s[f]=u);s.sort(n)}return new Kc(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){var t=new Array(this.size()),n=-1;return this.each(function(){t[++n]=this}),t},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}return null},size:function(){var t=0;return this.each(function(){++t}),t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],u=0,a=o.length;u<a;++u)(i=o[u])&&t.call(i,i.__data__,u,o);return this},attr:function(t,n){var e=lc(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?e.local?function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}:function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}:e.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(e,n))},style:function(t,n,e){var r;return arguments.length>1?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):Fc(r=this.node()).getComputedStyle(r,null).getPropertyValue(t)},property:function(t,n){return arguments.length>1?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=Ic(t+"");if(arguments.length<2){for(var r=Yc(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?jc:Hc)(this,t)}}:n?function(t){return function(){jc(this,t)}}:function(t){return function(){Hc(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?Xc:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?Vc:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each($c)},lower:function(){return this.each(Wc)},append:function(t){var n="function"==typeof t?t:hc(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:hc(t),r=null==n?Zc:"function"==typeof n?n:Cc(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(Gc)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=function(t){return t.trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}})}(t+""),u=o.length;if(!(arguments.length<2)){for(a=n?Tc:Mc,null==e&&(e=!1),r=0;r<u;++r)this.each(a(o[r],n,e));return this}var a=this.node().__on;if(a)for(var c,s=0,f=a.length;s<f;++s)for(r=0,c=a[s];r<u;++r)if((i=o[r]).type===c.type&&i.name===c.name)return c.value},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return Jc(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return Jc(this,t,n)}})(t,n))}};var rs=Jr("start","end","interrupt"),is=[],os=0,us=1,as=2,cs=3,ss=4,fs=5;function ls(t,n,e,r,i,o){var u=t.__transition;if(u){if(e in u)return}else t.__transition={};!function(t,n,e){var r,i=t.__transition;function o(o){var a,c,s,f;for(a in i)(f=i[a]).name===e.name&&(f.state===cs?(f.state=fs,f.timer.stop(),f.on.call("interrupt",t,t.__data__,f.index,f.group),delete i[a]):+a<n&&(f.state=fs,f.timer.stop(),delete i[a]));if(Yi(function(){e.state===cs&&(e.timer.restart(u,e.delay,e.time),u(o))}),e.state=as,e.on.call("start",t,t.__data__,e.index,e.group),e.state===as){for(e.state=cs,r=new Array(s=e.tween.length),a=0,c=-1;a<s;++a)(f=e.tween[a].value.call(t,t.__data__,e.index,e.group))&&(r[++c]=f);r.length=c+1}}function u(o){for(var u=o<e.duration?e.ease.call(null,o/e.duration):(e.state=ss,1),a=-1,c=r.length;++a<c;)r[a].call(null,u);if(e.state===ss){for(a in e.state=fs,e.timer.stop(),e.on.call("end",t,t.__data__,e.index,e.group),i)if(+a!==n)return void delete i[n];delete t.__transition}}i[n]=e,e.timer=Ui(function(t){e.state=us,e.delay<=t?o(t-e.delay):e.timer.restart(o,e.delay,e.time)},0,e.time)}(t,e,{name:n,index:r,group:i,on:rs,tween:is,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:os})}function hs(t,n){var e=t.__transition;if(!e||!(e=e[n])||e.state>os)throw new Error("too late");return e}function ps(t,n){var e=t.__transition;if(!e||!(e=e[n])||e.state>as)throw new Error("too late");return e}function ds(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw new Error("too late");return e}function vs(t,n){var e,r,i,o=t.__transition,u=!0;if(o){for(i in n=null==n?null:n+"",o)(e=o[i]).name===n?(r=e.state===cs,e.state=fs,e.timer.stop(),r&&e.on.call("interrupt",t,t.__data__,e.index,e.group),delete o[i]):u=!1;u&&delete t.__transition}}function _s(t,n,e){var r=t._id;return t.each(function(){var t=ps(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return ds(t,r).value[n]}}function ys(t,n){var e;return("number"==typeof n?gr:n instanceof xe?hr:(e=xe(n))?(n=e,hr):wr)(t,n)}var gs=ts.prototype.constructor;var ms=0;function xs(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function bs(t){return ts().transition(t)}function ws(){return++ms}var Ms=ts.prototype;xs.prototype=bs.prototype={constructor:xs,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=Cc(t));for(var r=this._groups,i=r.length,o=new Array(i),u=0;u<i;++u)for(var a,c,s=r[u],f=s.length,l=o[u]=new Array(f),h=0;h<f;++h)(a=s[h])&&(c=t.call(a,a.__data__,h,s))&&("__data__"in a&&(c.__data__=a.__data__),l[h]=c,ls(l[h],n,e,h,l,ds(a,e)));return new xs(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=Pc(t));for(var r=this._groups,i=r.length,o=[],u=[],a=0;a<i;++a)for(var c,s=r[a],f=s.length,l=0;l<f;++l)if(c=s[l]){for(var h,p=t.call(c,c.__data__,l,s),d=ds(c,e),v=0,_=p.length;v<_;++v)(h=p[v])&&ls(h,n,e,v,p,d);o.push(p),u.push(c)}return new xs(o,u,n,e)},filter:function(t){"function"!=typeof t&&(t=mc(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,u=n[i],a=u.length,c=r[i]=[],s=0;s<a;++s)(o=u[s])&&t.call(o,o.__data__,s,u)&&c.push(o);return new xs(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=new Array(r),a=0;a<o;++a)for(var c,s=n[a],f=e[a],l=s.length,h=u[a]=new Array(l),p=0;p<l;++p)(c=s[p]||f[p])&&(h[p]=c);for(;a<r;++a)u[a]=n[a];return new xs(u,this._parents,this._name,this._id)},selection:function(){return new gs(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=ws(),r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],c=a.length,s=0;s<c;++s)if(u=a[s]){var f=ds(u,n);ls(u,t,e,s,a,{time:f.time+f.delay+f.duration,delay:0,duration:f.duration,ease:f.ease})}return new xs(r,this._parents,t,e)},call:Ms.call,nodes:Ms.nodes,node:Ms.node,size:Ms.size,empty:Ms.empty,each:Ms.each,on:function(t,n){var e=this._id;return arguments.length<2?ds(this.node(),e).on.on(t):this.each(function(t,n,e){var r,i,o=function(t){return(t+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})}(n)?hs:ps;return function(){var u=o(this,t),a=u.on;a!==r&&(i=(r=a).copy()).on(n,e),u.on=i}}(e,t,n))},attr:function(t,n){var e=lc(t),r="transform"===e?Lr:ys;return this.attrTween(t,"function"==typeof n?(e.local?function(t,n,e){var r,i,o;return function(){var u,a=e(this);if(null!=a)return(u=this.getAttributeNS(t.space,t.local))===a?null:u===r&&a===i?o:o=n(r=u,i=a);this.removeAttributeNS(t.space,t.local)}}:function(t,n,e){var r,i,o;return function(){var u,a=e(this);if(null!=a)return(u=this.getAttribute(t))===a?null:u===r&&a===i?o:o=n(r=u,i=a);this.removeAttribute(t)}})(e,r,_s(this,"attr."+t,n)):null==n?(e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(e):(e.local?function(t,n,e){var r,i;return function(){var o=this.getAttributeNS(t.space,t.local);return o===e?null:o===r?i:i=n(r=o,e)}}:function(t,n,e){var r,i;return function(){var o=this.getAttribute(t);return o===e?null:o===r?i:i=n(r=o,e)}})(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw new Error;var r=lc(t);return this.tween(e,(r.local?function(t,n){function e(){var e=this,r=n.apply(e,arguments);return r&&function(n){e.setAttributeNS(t.space,t.local,r(n))}}return e._value=n,e}:function(t,n){function e(){var e=this,r=n.apply(e,arguments);return r&&function(n){e.setAttribute(t,r(n))}}return e._value=n,e})(r,n))},style:function(t,n,e){var r="transform"==(t+="")?qr:ys;return null==n?this.styleTween(t,function(t,n){var e,r,i;return function(){var o=Fc(this).getComputedStyle(this,null),u=o.getPropertyValue(t),a=(this.style.removeProperty(t),o.getPropertyValue(t));return u===a?null:u===e&&a===r?i:i=n(e=u,r=a)}}(t,r)).on("end.style."+t,function(t){return function(){this.style.removeProperty(t)}}(t)):this.styleTween(t,"function"==typeof n?function(t,n,e){var r,i,o;return function(){var u=Fc(this).getComputedStyle(this,null),a=u.getPropertyValue(t),c=e(this);return null==c&&(this.style.removeProperty(t),c=u.getPropertyValue(t)),a===c?null:a===r&&c===i?o:o=n(r=a,i=c)}}(t,r,_s(this,"style."+t,n)):function(t,n,e){var r,i;return function(){var o=Fc(this).getComputedStyle(this,null).getPropertyValue(t);return o===e?null:o===r?i:i=n(r=o,e)}}(t,r,n),e)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw new Error;return this.tween(r,function(t,n,e){function r(){var r=this,i=n.apply(r,arguments);return i&&function(n){r.style.setProperty(t,i(n),e)}}return r._value=n,r}(t,n,null==e?"":e))},text:function(t){return this.tween("text","function"==typeof t?function(t){return function(){var n=t(this);this.textContent=null==n?"":n}}(_s(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},remove:function(){return this.on("end.remove",(t=this._id,function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}));var t},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=ds(this.node(),e).tween,o=0,u=i.length;o<u;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?function(t,n){var e,r;return function(){var i=ps(this,t),o=i.tween;if(o!==e)for(var u=0,a=(r=e=o).length;u<a;++u)if(r[u].name===n){(r=r.slice()).splice(u,1);break}i.tween=r}}:function(t,n,e){var r,i;if("function"!=typeof e)throw new Error;return function(){var o=ps(this,t),u=o.tween;if(u!==r){i=(r=u).slice();for(var a={name:n,value:e},c=0,s=i.length;c<s;++c)if(i[c].name===n){i[c]=a;break}c===s&&i.push(a)}o.tween=i}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){hs(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){hs(this,t).delay=n}})(n,t)):ds(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){ps(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){ps(this,t).duration=n}})(n,t)):ds(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw new Error;return function(){ps(this,t).ease=n}}(n,t)):ds(this.node(),n).ease}};var Ts={time:null,delay:0,duration:250,ease:F};function Ss(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))return Ts.time=qi(),Ts;return e}ts.prototype.interrupt=function(t){return this.each(function(){vs(this,t)})},ts.prototype.transition=function(t){var n,e;t instanceof xs?(n=t._id,t=t._name):(n=ws(),(e=Ts).time=qi(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],c=a.length,s=0;s<c;++s)(u=a[s])&&ls(u,t,n,s,a,e||Ss(u,n));return new xs(r,this._parents,t,n)};var ks=[null];var Ns=Array.prototype.slice;function As(t){return t}var Es=1,Cs=2,zs=3,Ps=4,qs=1e-6;function Ls(t,n,e){var r=t(e);return"translate("+(isFinite(r)?r:n(e))+",0)"}function Rs(t,n,e){var r=t(e);return"translate(0,"+(isFinite(r)?r:n(e))+")"}function Us(){return!this.__axis}function Ds(t,n){var e=[],r=null,i=null,o=6,u=6,a=3;function c(c){var s,f=null==r?n.ticks?n.ticks.apply(n,e):n.domain():r,l=null==i?n.tickFormat?n.tickFormat.apply(n,e):As:i,h=Math.max(o,0)+a,p=t===Es||t===zs?Ls:Rs,d=n.range(),v=d[0]+.5,_=d[d.length-1]+.5,y=(n.bandwidth?function(t){var n=t.bandwidth()/2;return t.round()&&(n=Math.round(n)),function(e){return t(e)+n}}:As)(n.copy()),g=c.selection?c.selection():c,m=g.selectAll(".domain").data([null]),x=g.selectAll(".tick").data(f,n).order(),b=x.exit(),w=x.enter().append("g").attr("class","tick"),M=x.select("line"),T=x.select("text"),S=t===Es||t===Ps?-1:1,k=t===Ps||t===Cs?(s="x","y"):(s="y","x");m=m.merge(m.enter().insert("path",".tick").attr("class","domain").attr("stroke","#000")),x=x.merge(w),M=M.merge(w.append("line").attr("stroke","#000").attr(s+"2",S*o).attr(k+"1",.5).attr(k+"2",.5)),T=T.merge(w.append("text").attr("fill","#000").attr(s,S*h).attr(k,.5).attr("dy",t===Es?"0em":t===zs?"0.71em":"0.32em")),c!==g&&(m=m.transition(c),x=x.transition(c),M=M.transition(c),T=T.transition(c),b=b.transition(c).attr("opacity",qs).attr("transform",function(t){return p(y,this.parentNode.__axis||y,t)}),w.attr("opacity",qs).attr("transform",function(t){return p(this.parentNode.__axis||y,y,t)})),b.remove(),m.attr("d",t===Ps||t==Cs?"M"+S*u+","+v+"H0.5V"+_+"H"+S*u:"M"+v+","+S*u+"V0.5H"+_+"V"+S*u),x.attr("opacity",1).attr("transform",function(t){return p(y,y,t)}),M.attr(s+"2",S*o),T.attr(s,S*h).text(l),g.filter(Us).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===Cs?"start":t===Ps?"end":"middle"),g.each(function(){this.__axis=y})}return c.scale=function(t){return arguments.length?(n=t,c):n},c.ticks=function(){return e=Ns.call(arguments),c},c.tickArguments=function(t){return arguments.length?(e=null==t?[]:Ns.call(t),c):e.slice()},c.tickValues=function(t){return arguments.length?(r=null==t?null:Ns.call(t),c):r&&r.slice()},c.tickFormat=function(t){return arguments.length?(i=t,c):i},c.tickSize=function(t){return arguments.length?(o=u=+t,c):o},c.tickSizeInner=function(t){return arguments.length?(o=+t,c):o},c.tickSizeOuter=function(t){return arguments.length?(u=+t,c):u},c.tickPadding=function(t){return arguments.length?(a=+t,c):a},c}function Os(t,n){return t.parent===n.parent?1:2}function Fs(t,n){return t+n.x}function Is(t,n){return Math.max(t,n.y)}function Ys(t,n){var e,r,i,o,u,a=new Xs(t),c=+t.value&&(a.value=t.value),s=[a];for(null==n&&(n=Bs);e=s.pop();)if(c&&(e.value=+e.data.value),(i=n(e.data))&&(u=i.length))for(e.children=new Array(u),o=u-1;o>=0;--o)s.push(r=e.children[o]=new Xs(i[o])),r.parent=e,r.depth=e.depth+1;return a.eachBefore(Hs)}function Bs(t){return t.children}function js(t){t.data=t.data.data}function Hs(t){var n=0;do{t.height=n}while((t=t.parent)&&t.height<++n)}function Xs(t){this.data=t,this.depth=this.height=0,this.parent=null}function Vs(t){this._=t,this.next=null}function $s(t){return function t(n,e){var r,i,o,u=null,a=n.head;switch(e.length){case 1:c=e[0],r={x:c.x,y:c.y,r:c.r};break;case 2:r=function(t,n){var e=t.x,r=t.y,i=t.r,o=n.x,u=n.y,a=n.r,c=o-e,s=u-r,f=a-i,l=Math.sqrt(c*c+s*s);return{x:(e+o+c/l*f)/2,y:(r+u+s/l*f)/2,r:(l+i+a)/2}}(e[0],e[1]);break;case 3:r=function(t,n,e){var r=t.x,i=t.y,o=t.r,u=n.x,a=n.y,c=n.r,s=e.x,f=e.y,l=e.r,h=2*(r-u),p=2*(i-a),d=2*(c-o),v=r*r+i*i-o*o-u*u-a*a+c*c,_=2*(r-s),y=2*(i-f),g=2*(l-o),m=r*r+i*i-o*o-s*s-f*f+l*l,x=_*p-h*y,b=(p*m-y*v)/x-r,w=(y*d-p*g)/x,M=(_*v-h*m)/x-i,T=(h*g-_*d)/x,S=w*w+T*T-1,k=2*(b*w+M*T+o),N=b*b+M*M-o*o,A=(-k-Math.sqrt(k*k-4*S*N))/(2*S);return{x:b+w*A+r,y:M+T*A+i,r:A}}(e[0],e[1],e[2])}var c;for(;a;)o=a._,i=a.next,r&&Ws(r,o)?u=a:(u?(n.tail=u,u.next=null):n.head=n.tail=null,e.push(o),r=t(n,e),e.pop(),n.head?(a.next=n.head,n.head=a):(a.next=null,n.head=n.tail=a),(u=n.tail).next=i),a=i;n.tail=u;return r}(function(t){for(var n=(t=t.slice()).length,e=null,r=e;n;){var i=new Vs(t[n-1]);r=r?r.next=i:e=i,t[void 0]=t[--n]}return{head:e,tail:r}}(t),[])}function Ws(t,n){var e=n.x-t.x,r=n.y-t.y,i=t.r-n.r;return i*i+1e-6>e*e+r*r}function Zs(t,n,e){var r=t.x,i=t.y,o=n.r+e.r,u=t.r+e.r,a=n.x-r,c=n.y-i,s=a*a+c*c;if(s){var f=.5+((u*=u)-(o*=o))/(2*s),l=Math.sqrt(Math.max(0,2*o*(u+s)-(u-=s)*u-o*o))/(2*s);e.x=r+f*a+l*c,e.y=i+f*c-l*a}else e.x=r+u,e.y=i}function Gs(t,n){var e=n.x-t.x,r=n.y-t.y,i=t.r+n.r;return i*i>e*e+r*r}function Js(t,n,e){var r=t.x-n,i=t.y-e;return r*r+i*i}function Qs(t){this._=t,this.next=null,this.previous=null}function Ks(t){if(!(i=t.length))return 0;var n,e,r,i;if((n=t[0]).x=0,n.y=0,!(i>1))return n.r;if(e=t[1],n.x=-e.r,e.x=n.r,e.y=0,!(i>2))return n.r+e.r;Zs(e,n,r=t[2]);var o,u,a,c,s,f,l,h=n.r*n.r,p=e.r*e.r,d=r.r*r.r,v=h+p+d,_=h*n.x+p*e.x+d*r.x,y=h*n.y+p*e.y+d*r.y;n=new Qs(n),e=new Qs(e),r=new Qs(r),n.next=r.previous=e,e.next=n.previous=r,r.next=e.previous=n;t:for(a=3;a<i;++a){if(Zs(n._,e._,r=t[a]),r=new Qs(r),(s=n.previous)===(c=e.next)){if(Gs(c._,r._)){n=e,e=c,--a;continue t}}else{f=c._.r,l=s._.r;do{if(f<=l){if(Gs(c._,r._)){e=c,n.next=e,e.previous=n,--a;continue t}f+=(c=c.next)._.r}else{if(Gs(s._,r._)){(n=s).next=e,e.previous=n,--a;continue t}l+=(s=s.previous)._.r}}while(c!==s.next)}for(r.previous=n,r.next=e,n.next=e.previous=e=r,v+=d=r._.r*r._.r,_+=d*r._.x,y+=d*r._.y,h=Js(n._,o=_/v,u=y/v);(r=r.next)!==e;)(d=Js(r._,o,u))<h&&(n=r,h=d);e=n.next}for(n=[e._],r=e;(r=r.next)!==e;)n.push(r._);for(r=$s(n),a=0;a<i;++a)(n=t[a]).x-=r.x,n.y-=r.y;return r.r}function tf(t){if("function"!=typeof t)throw new Error;return t}function nf(){return 0}function ef(t){return function(){return t}}function rf(t){return Math.sqrt(t.value)}function of(t){return function(n){n.children||(n.r=Math.max(0,+t(n)||0))}}function uf(t,n){return function(e){if(r=e.children){var r,i,o,u=r.length,a=t(e)*n||0;if(a)for(i=0;i<u;++i)r[i].r+=a;if(o=Ks(r),a)for(i=0;i<u;++i)r[i].r-=a;e.r=o+a}}}function af(t){return function(n){var e=n.parent;n.r*=t,e&&(n.x=e.x+t*n.x,n.y=e.y+t*n.y)}}function cf(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)}function sf(t,n,e,r,i){for(var o,u=t.children,a=-1,c=u.length,s=t.value&&(r-n)/t.value;++a<c;)(o=u[a]).y0=e,o.y1=i,o.x0=n,o.x1=n+=o.value*s}Xs.prototype=Ys.prototype={constructor:Xs,each:function(t){var n,e,r,i,o=this,u=[o];do{for(n=u.reverse(),u=[];o=n.pop();)if(t(o),e=o.children)for(r=0,i=e.length;r<i;++r)u.push(e[r])}while(u.length);return this},eachAfter:function(t){for(var n,e,r,i=this,o=[i],u=[];i=o.pop();)if(u.push(i),n=i.children)for(e=0,r=n.length;e<r;++e)o.push(n[e]);for(;i=u.pop();)t(i);return this},eachBefore:function(t){for(var n,e,r=this,i=[r];r=i.pop();)if(t(r),n=r.children)for(e=n.length-1;e>=0;--e)i.push(n[e]);return this},sum:function(t){return this.eachAfter(function(n){for(var e=+t(n.data)||0,r=n.children,i=r&&r.length;--i>=0;)e+=r[i].value;n.value=e})},sort:function(t){return this.eachBefore(function(n){n.children&&n.children.sort(t)})},path:function(t){for(var n=this,e=function(t,n){if(t===n)return t;var e=t.ancestors(),r=n.ancestors(),i=null;for(t=e.pop(),n=r.pop();t===n;)i=t,t=e.pop(),n=r.pop();return i}(n,t),r=[n];n!==e;)n=n.parent,r.push(n);for(var i=r.length;t!==e;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,n=[t];t=t.parent;)n.push(t);return n},descendants:function(){var t=[];return this.each(function(n){t.push(n)}),t},leaves:function(){var t=[];return this.eachBefore(function(n){n.children||t.push(n)}),t},links:function(){var t=this,n=[];return t.each(function(e){e!==t&&n.push({source:e.parent,target:e})}),n},copy:function(){return Ys(this).eachBefore(js)}};var ff="$",lf={depth:-1},hf={};function pf(t){return t.id}function df(t){return t.parentId}function vf(t,n){return t.parent===n.parent?1:2}function _f(t){var n=t.children;return n?n[0]:t.t}function yf(t){var n=t.children;return n?n[n.length-1]:t.t}function gf(t,n,e){var r=e/(n.i-t.i);n.c-=r,n.s+=e,t.c+=r,n.z+=e,n.m+=e}function mf(t,n,e){return t.a.parent===n.parent?t.a:e}function xf(t,n){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=n}function bf(t,n,e,r,i){for(var o,u=t.children,a=-1,c=u.length,s=t.value&&(i-e)/t.value;++a<c;)(o=u[a]).x0=n,o.x1=r,o.y0=e,o.y1=e+=o.value*s}xf.prototype=Object.create(Xs.prototype);var wf=(1+Math.sqrt(5))/2;function Mf(t,n,e,r,i,o){for(var u,a,c,s,f,l,h,p,d,v,_,y,g=[],m=n.children,x=0,b=m.length,w=n.value;x<b;){for(s=i-e,f=o-r,h=p=l=m[x].value,y=l*l*(_=Math.max(f/s,s/f)/(w*t)),v=Math.max(p/y,y/h),c=x+1;c<b;++c){if(l+=a=m[c].value,a<h&&(h=a),a>p&&(p=a),y=l*l*_,(d=Math.max(p/y,y/h))>v){l-=a;break}v=d}g.push(u={value:l,dice:s<f,children:m.slice(x,c)}),u.dice?sf(u,e,r,i,w?r+=f*l/w:o):bf(u,e,r,w?e+=s*l/w:i,o),w-=l,x=c}return g}var Tf=function t(n){function e(t,e,r,i,o){Mf(n,t,e,r,i,o)}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(wf);var Sf=function t(n){function e(t,e,r,i,o){if((u=t._squarify)&&u.ratio===n)for(var u,a,c,s,f,l=-1,h=u.length,p=t.value;++l<h;){for(c=(a=u[l]).children,s=a.value=0,f=c.length;s<f;++s)a.value+=c[s].value;a.dice?sf(a,e,r,i,r+=(o-r)*a.value/p):bf(a,e,r,e+=(i-e)*a.value/p,o),p-=a.value}else t._squarify=u=Mf(n,t,e,r,i,o),u.ratio=n}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(wf);function kf(t){return function(){return t}}function Nf(){return 1e-6*(Math.random()-.5)}function Af(t){return t.x+t.vx}function Ef(t){return t.y+t.vy}function Cf(t,n){return n}function zf(t){return t.x}function Pf(t){return t.y}var qf=10,Lf=Math.PI*(3-Math.sqrt(5));function Rf(){t.event.stopImmediatePropagation()}function Uf(){t.event.preventDefault(),t.event.stopImmediatePropagation()}function Df(t){var n=t.document.documentElement,e=ns(t).on("dragstart.drag",Uf,!0);"onselectstart"in n?e.on("selectstart.drag",Uf,!0):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function Of(t,n){var e=t.document.documentElement,r=ns(t).on("dragstart.drag",null);n&&(r.on("click.drag",Uf,!0),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}function Ff(t){return function(){return t}}function If(t,n,e,r,i,o,u,a,c,s){this.target=t,this.type=n,this.subject=e,this.identifier=r,this.active=i,this.x=o,this.y=u,this.dx=a,this.dy=c,this._=s}function Yf(){return!t.event.button}function Bf(){return this.parentNode}function jf(n){return null==n?{x:t.event.x,y:t.event.y}:n}function Hf(t){return function(){return t}}function Xf(t){return t[0]}function Vf(t){return t[1]}function $f(){this._=null}function Wf(t){t.U=t.C=t.L=t.R=t.P=t.N=null}function Zf(t,n){var e=n,r=n.R,i=e.U;i?i.L===e?i.L=r:i.R=r:t._=r,r.U=i,e.U=r,e.R=r.L,e.R&&(e.R.U=e),r.L=e}function Gf(t,n){var e=n,r=n.L,i=e.U;i?i.L===e?i.L=r:i.R=r:t._=r,r.U=i,e.U=r,e.L=r.R,e.L&&(e.L.U=e),r.R=e}function Jf(t){for(;t.L;)t=t.L;return t}function Qf(t,n,e,r){var i=[null,null],o=wl.push(i)-1;return i.left=t,i.right=n,e&&tl(i,t,n,e),r&&tl(i,n,t,r),xl[t.index].halfedges.push(o),xl[n.index].halfedges.push(o),i}function Kf(t,n,e){var r=[n,e];return r.left=t,r}function tl(t,n,e,r){t[0]||t[1]?t.left===e?t[1]=r:t[0]=r:(t[0]=r,t.left=n,t.right=e)}function nl(t,n,e,r,i){var o,u=t[0],a=t[1],c=u[0],s=u[1],f=0,l=1,h=a[0]-c,p=a[1]-s;if(o=n-c,h||!(o>0)){if(o/=h,h<0){if(o<f)return;o<l&&(l=o)}else if(h>0){if(o>l)return;o>f&&(f=o)}if(o=r-c,h||!(o<0)){if(o/=h,h<0){if(o>l)return;o>f&&(f=o)}else if(h>0){if(o<f)return;o<l&&(l=o)}if(o=e-s,p||!(o>0)){if(o/=p,p<0){if(o<f)return;o<l&&(l=o)}else if(p>0){if(o>l)return;o>f&&(f=o)}if(o=i-s,p||!(o<0)){if(o/=p,p<0){if(o>l)return;o>f&&(f=o)}else if(p>0){if(o<f)return;o<l&&(l=o)}return!(f>0||l<1)||(f>0&&(t[0]=[c+f*h,s+f*p]),l<1&&(t[1]=[c+l*h,s+l*p]),!0)}}}}}function el(t,n,e,r,i){var o=t[1];if(o)return!0;var u,a,c=t[0],s=t.left,f=t.right,l=s[0],h=s[1],p=f[0],d=f[1],v=(l+p)/2,_=(h+d)/2;if(d===h){if(v<n||v>=r)return;if(l>p){if(c){if(c[1]>=i)return}else c=[v,e];o=[v,i]}else{if(c){if(c[1]<e)return}else c=[v,i];o=[v,e]}}else if(a=_-(u=(l-p)/(d-h))*v,u<-1||u>1)if(l>p){if(c){if(c[1]>=i)return}else c=[(e-a)/u,e];o=[(i-a)/u,i]}else{if(c){if(c[1]<e)return}else c=[(i-a)/u,i];o=[(e-a)/u,e]}else if(h<d){if(c){if(c[0]>=r)return}else c=[n,u*n+a];o=[r,u*r+a]}else{if(c){if(c[0]<n)return}else c=[r,u*r+a];o=[n,u*n+a]}return t[0]=c,t[1]=o,!0}function rl(t,n){var e=t.site,r=n.left,i=n.right;return e===i&&(i=r,r=e),i?Math.atan2(i[1]-r[1],i[0]-r[0]):(e===r?(r=n[1],i=n[0]):(r=n[0],i=n[1]),Math.atan2(r[0]-i[0],i[1]-r[1]))}function il(t,n){return n[+(n.left!==t.site)]}function ol(t,n){return n[+(n.left===t.site)]}If.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t},$f.prototype={constructor:$f,insert:function(t,n){var e,r,i;if(t){if(n.P=t,n.N=t.N,t.N&&(t.N.P=n),t.N=n,t.R){for(t=t.R;t.L;)t=t.L;t.L=n}else t.R=n;e=t}else this._?(t=Jf(this._),n.P=null,n.N=t,t.P=t.L=n,e=t):(n.P=n.N=null,this._=n,e=null);for(n.L=n.R=null,n.U=e,n.C=!0,t=n;e&&e.C;)e===(r=e.U).L?(i=r.R)&&i.C?(e.C=i.C=!1,r.C=!0,t=r):(t===e.R&&(Zf(this,e),e=(t=e).U),e.C=!1,r.C=!0,Gf(this,r)):(i=r.L)&&i.C?(e.C=i.C=!1,r.C=!0,t=r):(t===e.L&&(Gf(this,e),e=(t=e).U),e.C=!1,r.C=!0,Zf(this,r)),e=t.U;this._.C=!1},remove:function(t){t.N&&(t.N.P=t.P),t.P&&(t.P.N=t.N),t.N=t.P=null;var n,e,r,i=t.U,o=t.L,u=t.R;if(e=o?u?Jf(u):o:u,i?i.L===t?i.L=e:i.R=e:this._=e,o&&u?(r=e.C,e.C=t.C,e.L=o,o.U=e,e!==u?(i=e.U,e.U=t.U,t=e.R,i.L=t,e.R=u,u.U=e):(e.U=i,i=e,t=e.R)):(r=t.C,t=e),t&&(t.U=i),!r)if(t&&t.C)t.C=!1;else{do{if(t===this._)break;if(t===i.L){if((n=i.R).C&&(n.C=!1,i.C=!0,Zf(this,i),n=i.R),n.L&&n.L.C||n.R&&n.R.C){n.R&&n.R.C||(n.L.C=!1,n.C=!0,Gf(this,n),n=i.R),n.C=i.C,i.C=n.R.C=!1,Zf(this,i),t=this._;break}}else if((n=i.L).C&&(n.C=!1,i.C=!0,Gf(this,i),n=i.L),n.L&&n.L.C||n.R&&n.R.C){n.L&&n.L.C||(n.R.C=!1,n.C=!0,Zf(this,n),n=i.L),n.C=i.C,i.C=n.L.C=!1,Gf(this,i),t=this._;break}n.C=!0,t=i,i=i.U}while(!t.C);t&&(t.C=!1)}}};var ul,al=[];function cl(){Wf(this),this.x=this.y=this.arc=this.site=this.cy=null}function sl(t){var n=t.P,e=t.N;if(n&&e){var r=n.site,i=t.site,o=e.site;if(r!==o){var u=i[0],a=i[1],c=r[0]-u,s=r[1]-a,f=o[0]-u,l=o[1]-a,h=2*(c*l-s*f);if(!(h>=-Tl)){var p=c*c+s*s,d=f*f+l*l,v=(l*p-s*d)/h,_=(c*d-f*p)/h,y=al.pop()||new cl;y.arc=t,y.site=i,y.x=v+u,y.y=(y.cy=_+a)+Math.sqrt(v*v+_*_),t.circle=y;for(var g=null,m=bl._;m;)if(y.y<m.y||y.y===m.y&&y.x<=m.x){if(!m.L){g=m.P;break}m=m.L}else{if(!m.R){g=m;break}m=m.R}bl.insert(g,y),g||(ul=y)}}}}function fl(t){var n=t.circle;n&&(n.P||(ul=n.N),bl.remove(n),al.push(n),Wf(n),t.circle=null)}var ll=[];function hl(){Wf(this),this.edge=this.site=this.circle=null}function pl(t){var n=ll.pop()||new hl;return n.site=t,n}function dl(t){fl(t),ml.remove(t),ll.push(t),Wf(t)}function vl(t){var n=t.circle,e=n.x,r=n.cy,i=[e,r],o=t.P,u=t.N,a=[t];dl(t);for(var c=o;c.circle&&Math.abs(e-c.circle.x)<Ml&&Math.abs(r-c.circle.cy)<Ml;)o=c.P,a.unshift(c),dl(c),c=o;a.unshift(c),fl(c);for(var s=u;s.circle&&Math.abs(e-s.circle.x)<Ml&&Math.abs(r-s.circle.cy)<Ml;)u=s.N,a.push(s),dl(s),s=u;a.push(s),fl(s);var f,l=a.length;for(f=1;f<l;++f)s=a[f],c=a[f-1],tl(s.edge,c.site,s.site,i);c=a[0],(s=a[l-1]).edge=Qf(c.site,s.site,null,i),sl(c),sl(s)}function _l(t){for(var n,e,r,i,o=t[0],u=t[1],a=ml._;a;)if((r=yl(a,u)-o)>Ml)a=a.L;else{if(!((i=o-gl(a,u))>Ml)){r>-Ml?(n=a.P,e=a):i>-Ml?(n=a,e=a.N):n=e=a;break}if(!a.R){n=a;break}a=a.R}!function(t){xl[t.index]={site:t,halfedges:[]}}(t);var c=pl(t);if(ml.insert(n,c),n||e){if(n===e)return fl(n),e=pl(n.site),ml.insert(c,e),c.edge=e.edge=Qf(n.site,c.site),sl(n),void sl(e);if(e){fl(n),fl(e);var s=n.site,f=s[0],l=s[1],h=t[0]-f,p=t[1]-l,d=e.site,v=d[0]-f,_=d[1]-l,y=2*(h*_-p*v),g=h*h+p*p,m=v*v+_*_,x=[(_*g-p*m)/y+f,(h*m-v*g)/y+l];tl(e.edge,s,d,x),c.edge=Qf(s,t,null,x),e.edge=Qf(t,d,null,x),sl(n),sl(e)}else c.edge=Qf(n.site,c.site)}}function yl(t,n){var e=t.site,r=e[0],i=e[1],o=i-n;if(!o)return r;var u=t.P;if(!u)return-1/0;var a=(e=u.site)[0],c=e[1],s=c-n;if(!s)return a;var f=a-r,l=1/o-1/s,h=f/s;return l?(-h+Math.sqrt(h*h-2*l*(f*f/(-2*s)-c+s/2+i-o/2)))/l+r:(r+a)/2}function gl(t,n){var e=t.N;if(e)return yl(e,n);var r=t.site;return r[1]===n?r[0]:1/0}var ml,xl,bl,wl,Ml=1e-6,Tl=1e-12;function Sl(t,n){return n[1]-t[1]||n[0]-t[0]}function kl(t,n){var e,r,i,o=t.sort(Sl).pop();for(wl=[],xl=new Array(t.length),ml=new $f,bl=new $f;;)if(i=ul,o&&(!i||o[1]<i.y||o[1]===i.y&&o[0]<i.x))o[0]===e&&o[1]===r||(_l(o),e=o[0],r=o[1]),o=t.pop();else{if(!i)break;vl(i.arc)}if(function(){for(var t,n,e,r,i=0,o=xl.length;i<o;++i)if((t=xl[i])&&(r=(n=t.halfedges).length)){var u=new Array(r),a=new Array(r);for(e=0;e<r;++e)u[e]=e,a[e]=rl(t,wl[n[e]]);for(u.sort(function(t,n){return a[n]-a[t]}),e=0;e<r;++e)a[e]=n[u[e]];for(e=0;e<r;++e)n[e]=a[e]}}(),n){var u=+n[0][0],a=+n[0][1],c=+n[1][0],s=+n[1][1];!function(t,n,e,r){for(var i,o=wl.length;o--;)el(i=wl[o],t,n,e,r)&&nl(i,t,n,e,r)&&(Math.abs(i[0][0]-i[1][0])>Ml||Math.abs(i[0][1]-i[1][1])>Ml)||delete wl[o]}(u,a,c,s),function(t,n,e,r){var i,o,u,a,c,s,f,l,h,p,d,v,_=xl.length,y=!0;for(i=0;i<_;++i)if(o=xl[i]){for(u=o.site,a=(c=o.halfedges).length;a--;)wl[c[a]]||c.splice(a,1);for(a=0,s=c.length;a<s;)d=(p=ol(o,wl[c[a]]))[0],v=p[1],l=(f=il(o,wl[c[++a%s]]))[0],h=f[1],(Math.abs(d-l)>Ml||Math.abs(v-h)>Ml)&&(c.splice(a,0,wl.push(Kf(u,p,Math.abs(d-t)<Ml&&r-v>Ml?[t,Math.abs(l-t)<Ml?h:r]:Math.abs(v-r)<Ml&&e-d>Ml?[Math.abs(h-r)<Ml?l:e,r]:Math.abs(d-e)<Ml&&v-n>Ml?[e,Math.abs(l-e)<Ml?h:n]:Math.abs(v-n)<Ml&&d-t>Ml?[Math.abs(h-n)<Ml?l:t,n]:null))-1),++s);s&&(y=!1)}if(y){var g,m,x,b=1/0;for(i=0,y=null;i<_;++i)(o=xl[i])&&(x=(g=(u=o.site)[0]-t)*g+(m=u[1]-n)*m)<b&&(b=x,y=o);if(y){var w=[t,n],M=[t,r],T=[e,r],S=[e,n];y.halfedges.push(wl.push(Kf(u=y.site,w,M))-1,wl.push(Kf(u,M,T))-1,wl.push(Kf(u,T,S))-1,wl.push(Kf(u,S,w))-1)}}for(i=0;i<_;++i)(o=xl[i])&&(o.halfedges.length||delete xl[i])}(u,a,c,s)}this.edges=wl,this.cells=xl,ml=bl=wl=xl=null}function Nl(t){return function(){return t}}function Al(t,n,e){this.target=t,this.type=n,this.transform=e}function El(t,n,e){this.k=t,this.x=n,this.y=e}kl.prototype={constructor:kl,polygons:function(){var t=this.edges;return this.cells.map(function(n){var e=n.halfedges.map(function(e){return il(n,t[e])});return e.data=n.site.data,e})},triangles:function(){var t=[],n=this.edges;return this.cells.forEach(function(e,r){for(var i,o,u,a,c=e.site,s=e.halfedges,f=-1,l=s.length,h=n[s[l-1]],p=h.left===c?h.right:h.left;++f<l;)i=p,p=(h=n[s[f]]).left===c?h.right:h.left,r<i.index&&r<p.index&&(u=i,a=p,((o=c)[0]-a[0])*(u[1]-o[1])-(o[0]-u[0])*(a[1]-o[1])<0)&&t.push([c.data,i.data,p.data])}),t},links:function(){return this.edges.filter(function(t){return t.right}).map(function(t){return{source:t.left.data,target:t.right.data}})}},El.prototype={constructor:El,scale:function(t){return 1===t?this:new El(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new El(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Cl=new El(1,0,0);function zl(t){return t.__zoom||Cl}function Pl(){t.event.stopImmediatePropagation()}function ql(){t.event.preventDefault(),t.event.stopImmediatePropagation()}function Ll(){return!t.event.button}function Rl(){var t,n,e=this;return e instanceof SVGElement?(t=(e=e.ownerSVGElement||e).width.baseVal.value,n=e.height.baseVal.value):(t=e.clientWidth,n=e.clientHeight),[[0,0],[t,n]]}function Ul(){return this.__zoom||Cl}function Dl(t){return function(){return t}}function Ol(t,n,e){this.target=t,this.type=n,this.selection=e}function Fl(){t.event.stopImmediatePropagation()}function Il(){t.event.preventDefault(),t.event.stopImmediatePropagation()}zl.prototype=El.prototype;var Yl={name:"drag"},Bl={name:"space"},jl={name:"handle"},Hl={name:"center"},Xl={name:"x",handles:["e","w"].map(Kl),input:function(t,n){return t&&[[t[0],n[0][1]],[t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},Vl={name:"y",handles:["n","s"].map(Kl),input:function(t,n){return t&&[[n[0][0],t[0]],[n[1][0],t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},$l={name:"xy",handles:["n","e","s","w","nw","ne","se","sw"].map(Kl),input:function(t){return t},output:function(t){return t}},Wl={overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},Zl={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},Gl={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},Jl={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},Ql={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function Kl(t){return{type:t}}function th(){return!t.event.button}function nh(){var t=this.ownerSVGElement||this;return[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function eh(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function rh(t){return t[0][0]===t[1][0]||t[0][1]===t[1][1]}function ih(n){var e,r=nh,i=th,o=Jr(a,"start","brush","end"),u=6;function a(t){var e=t.property("__brush",h).selectAll(".overlay").data([Kl("overlay")]);e.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",Wl.overlay).merge(e).each(function(){var t=eh(this).extent;ns(this).attr("x",t[0][0]).attr("y",t[0][1]).attr("width",t[1][0]-t[0][0]).attr("height",t[1][1]-t[0][1])}),t.selectAll(".selection").data([Kl("selection")]).enter().append("rect").attr("class","selection").attr("cursor",Wl.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var r=t.selectAll(".handle").data(n.handles,function(t){return t.type});r.exit().remove(),r.enter().append("rect").attr("class",function(t){return"handle handle--"+t.type}).attr("cursor",function(t){return Wl[t.type]}),t.each(c).attr("fill","none").attr("pointer-events","all").style("-webkit-tap-highlight-color","rgba(0,0,0,0)").on("mousedown.brush touchstart.brush",l)}function c(){var t=ns(this),n=eh(this).selection;n?(t.selectAll(".selection").style("display",null).attr("x",n[0][0]).attr("y",n[0][1]).attr("width",n[1][0]-n[0][0]).attr("height",n[1][1]-n[0][1]),t.selectAll(".handle").style("display",null).attr("x",function(t){return"e"===t.type[t.type.length-1]?n[1][0]-u/2:n[0][0]-u/2}).attr("y",function(t){return"s"===t.type[0]?n[1][1]-u/2:n[0][1]-u/2}).attr("width",function(t){return"n"===t.type||"s"===t.type?n[1][0]-n[0][0]+u:u}).attr("height",function(t){return"e"===t.type||"w"===t.type?n[1][1]-n[0][1]+u:u})):t.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function s(t,n){return t.__brush.emitter||new f(t,n)}function f(t,n){this.that=t,this.args=n,this.state=t.__brush,this.active=0}function l(){if(t.event.touches){if(t.event.changedTouches.length<t.event.touches.length)return Il()}else if(e)return;if(i.apply(this,arguments)){var r,o,u,a,f,l,h,p,d,v,_,y,g,m=this,x=t.event.target.__data__.type,b="selection"===(t.event.metaKey?x="overlay":x)?Yl:t.event.altKey?Hl:jl,w=n===Vl?null:Jl[x],M=n===Xl?null:Ql[x],T=eh(m),S=T.extent,k=T.selection,N=S[0][0],A=S[0][1],E=S[1][0],C=S[1][1],z=w&&M&&t.event.shiftKey,P=Ac(m),q=P,L=s(m,arguments).beforestart();"overlay"===x?T.selection=k=[[r=n===Vl?N:P[0],u=n===Xl?A:P[1]],[f=n===Vl?E:r,h=n===Xl?C:u]]:(r=k[0][0],u=k[0][1],f=k[1][0],h=k[1][1]),o=r,a=u,l=f,p=h;var R=ns(m).attr("pointer-events","none"),U=R.selectAll(".overlay").attr("cursor",Wl[x]);if(t.event.touches)R.on("touchmove.brush",O,!0).on("touchend.brush touchcancel.brush",I,!0);else{var D=ns(t.event.view).on("keydown.brush",function(){switch(t.event.keyCode){case 16:z=w&&M;break;case 18:b===jl&&(w&&(f=l-d*w,r=o+d*w),M&&(h=p-v*M,u=a+v*M),b=Hl,F());break;case 32:b!==jl&&b!==Hl||(w<0?f=l-d:w>0&&(r=o-d),M<0?h=p-v:M>0&&(u=a-v),b=Bl,U.attr("cursor",Wl.selection),F());break;default:return}Il()},!0).on("keyup.brush",function(){switch(t.event.keyCode){case 16:z&&(y=g=z=!1,F());break;case 18:b===Hl&&(w<0?f=l:w>0&&(r=o),M<0?h=p:M>0&&(u=a),b=jl,F());break;case 32:b===Bl&&(t.event.altKey?(w&&(f=l-d*w,r=o+d*w),M&&(h=p-v*M,u=a+v*M),b=Hl):(w<0?f=l:w>0&&(r=o),M<0?h=p:M>0&&(u=a),b=jl),U.attr("cursor",Wl[x]),F());break;default:return}Il()},!0).on("mousemove.brush",O,!0).on("mouseup.brush",I,!0);Df(t.event.view)}Fl(),vs(m),c.call(m),L.start()}function O(){var t=Ac(m);!z||y||g||(Math.abs(t[0]-q[0])>Math.abs(t[1]-q[1])?g=!0:y=!0),q=t,_=!0,Il(),F()}function F(){var t;switch(d=q[0]-P[0],v=q[1]-P[1],b){case Bl:case Yl:w&&(d=Math.max(N-r,Math.min(E-f,d)),o=r+d,l=f+d),M&&(v=Math.max(A-u,Math.min(C-h,v)),a=u+v,p=h+v);break;case jl:w<0?(d=Math.max(N-r,Math.min(E-r,d)),o=r+d,l=f):w>0&&(d=Math.max(N-f,Math.min(E-f,d)),o=r,l=f+d),M<0?(v=Math.max(A-u,Math.min(C-u,v)),a=u+v,p=h):M>0&&(v=Math.max(A-h,Math.min(C-h,v)),a=u,p=h+v);break;case Hl:w&&(o=Math.max(N,Math.min(E,r-d*w)),l=Math.max(N,Math.min(E,f+d*w))),M&&(a=Math.max(A,Math.min(C,u-v*M)),p=Math.max(A,Math.min(C,h+v*M)))}l<o&&(w*=-1,t=r,r=f,f=t,t=o,o=l,l=t,x in Zl&&U.attr("cursor",Wl[x=Zl[x]])),p<a&&(M*=-1,t=u,u=h,h=t,t=a,a=p,p=t,x in Gl&&U.attr("cursor",Wl[x=Gl[x]])),k=T.selection,y&&(o=k[0][0],l=k[1][0]),g&&(a=k[0][1],p=k[1][1]),k[0][0]===o&&k[0][1]===a&&k[1][0]===l&&k[1][1]===p||(T.selection=[[o,a],[l,p]],c.call(m),L.brush())}function I(){if(Fl(),t.event.touches){if(t.event.touches.length)return;e&&clearTimeout(e),e=setTimeout(function(){e=null},500),R.on("touchmove.brush touchend.brush touchcancel.brush",null)}else Of(t.event.view,_),D.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);R.attr("pointer-events","all"),U.attr("cursor",Wl.overlay),rh(k)&&(T.selection=null,c.call(m)),L.end()}}function h(){var t=this.__brush||{selection:null};return t.extent=r.apply(this,arguments),t.dim=n,t}return a.move=function(t,e){t.selection?t.on("start.brush",function(){s(this,arguments).beforestart().start()}).on("interrupt.brush end.brush",function(){s(this,arguments).end()}).tween("brush",function(){var t=this,r=t.__brush,i=s(t,arguments),o=r.selection,u=n.input("function"==typeof e?e.apply(this,arguments):e,r.extent),a=Mr(o,u);function f(n){r.selection=1===n&&rh(u)?null:a(n),c.call(t),i.brush()}return o&&u?f:f(1)}):t.each(function(){var t=arguments,r=this.__brush,i=n.input("function"==typeof e?e.apply(this,t):e,r.extent),o=s(this,t).beforestart();vs(this),r.selection=null==i||rh(i)?null:i,c.call(this),o.start().brush().end()})},f.prototype={beforestart:function(){return 1==++this.active&&(this.state.emitter=this,this.starting=!0),this},start:function(){return this.starting&&(this.starting=!1,this.emit("start")),this},brush:function(){return this.emit("brush"),this},end:function(){return 0==--this.active&&(delete this.state.emitter,this.emit("end")),this},emit:function(t){Sc(new Ol(a,t,n.output(this.state.selection)),o.apply,o,[t,this.that,this.args])}},a.extent=function(t){return arguments.length?(r="function"==typeof t?t:Dl([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),a):r},a.filter=function(t){return arguments.length?(i="function"==typeof t?t:Dl(!!t),a):i},a.handleSize=function(t){return arguments.length?(u=+t,a):u},a.on=function(){var t=o.on.apply(o,arguments);return t===o?a:t},a}var oh=Math.cos,uh=Math.sin,ah=Math.PI,ch=ah/2,sh=2*ah,fh=Math.max;var lh=Array.prototype.slice;function hh(t){return function(){return t}}function ph(t){return t.source}function dh(t){return t.target}function vh(t){return t.radius}function _h(t){return t.startAngle}function yh(t){return t.endAngle}function gh(){return new mh}function mh(){this.reset()}mh.prototype={constructor:mh,reset:function(){this.s=this.t=0},add:function(t){bh(xh,t,this.t),bh(this,xh.s,this.s),this.s?this.t+=xh.t:this.s=xh.t},valueOf:function(){return this.s}};var xh=new mh;function bh(t,n,e){var r=t.s=n+e,i=r-n,o=r-i;t.t=n-o+(e-i)}var wh=1e-6,Mh=1e-12,Th=Math.PI,Sh=Th/2,kh=Th/4,Nh=2*Th,Ah=180/Th,Eh=Th/180,Ch=Math.abs,zh=Math.atan,Ph=Math.atan2,qh=Math.cos,Lh=Math.ceil,Rh=Math.exp,Uh=Math.log,Dh=Math.pow,Oh=Math.sin,Fh=Math.sign||function(t){return t>0?1:t<0?-1:0},Ih=Math.sqrt,Yh=Math.tan;function Bh(t){return t>1?0:t<-1?Th:Math.acos(t)}function jh(t){return t>1?Sh:t<-1?-Sh:Math.asin(t)}function Hh(t){return(t=Oh(t/2))*t}function Xh(){}function Vh(t,n){t&&Wh.hasOwnProperty(t.type)&&Wh[t.type](t,n)}var $h={Feature:function(t,n){Vh(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)Vh(e[r].geometry,n)}},Wh={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){Zh(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)Zh(e[r],n,0)},Polygon:function(t,n){Gh(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)Gh(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)Vh(e[r],n)}};function Zh(t,n,e){var r,i=-1,o=t.length-e;for(n.lineStart();++i<o;)r=t[i],n.point(r[0],r[1],r[2]);n.lineEnd()}function Gh(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)Zh(t[e],n,1);n.polygonEnd()}function Jh(t,n){t&&$h.hasOwnProperty(t.type)?$h[t.type](t,n):Vh(t,n)}var Qh,Kh,tp,np,ep,rp,ip,op,up,ap,cp,sp,fp,lp=gh(),hp=gh(),pp={point:Xh,lineStart:Xh,lineEnd:Xh,polygonStart:function(){lp.reset(),pp.lineStart=dp,pp.lineEnd=vp},polygonEnd:function(){var t=+lp;hp.add(t<0?Nh+t:t),this.lineStart=this.lineEnd=this.point=Xh},sphere:function(){hp.add(Nh)}};function dp(){pp.point=_p}function vp(){yp(Qh,Kh)}function _p(t,n){pp.point=yp,Qh=t,Kh=n,tp=t*=Eh,np=qh(n=(n*=Eh)/2+kh),ep=Oh(n)}function yp(t,n){var e=(t*=Eh)-tp,r=e>=0?1:-1,i=r*e,o=qh(n=(n*=Eh)/2+kh),u=Oh(n),a=ep*u,c=np*o+a*qh(i),s=a*r*Oh(i);lp.add(Ph(s,c)),tp=t,np=o,ep=u}function gp(t){return[Ph(t[1],t[0]),jh(t[2])]}function mp(t){var n=t[0],e=t[1],r=qh(e);return[r*qh(n),r*Oh(n),Oh(e)]}function xp(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function bp(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function wp(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function Mp(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function Tp(t){var n=Ih(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}var Sp,kp,Np,Ap,Ep,Cp,zp,Pp,qp,Lp,Rp,Up,Dp,Op,Fp,Ip,Yp,Bp,jp=gh(),Hp={point:Xp,lineStart:$p,lineEnd:Wp,polygonStart:function(){Hp.point=Zp,Hp.lineStart=Gp,Hp.lineEnd=Jp,jp.reset(),pp.polygonStart()},polygonEnd:function(){pp.polygonEnd(),Hp.point=Xp,Hp.lineStart=$p,Hp.lineEnd=Wp,lp<0?(rp=-(op=180),ip=-(up=90)):jp>wh?up=90:jp<-wh&&(ip=-90),kp[0]=rp,kp[1]=op}};function Xp(t,n){Sp.push(kp=[rp=t,op=t]),n<ip&&(ip=n),n>up&&(up=n)}function Vp(t,n){var e=mp([t*Eh,n*Eh]);if(fp){var r=bp(fp,e),i=bp([r[1],-r[0],0],r);Tp(i),i=gp(i);var o,u=t-ap,a=u>0?1:-1,c=i[0]*Ah*a,s=Ch(u)>180;s^(a*ap<c&&c<a*t)?(o=i[1]*Ah)>up&&(up=o):s^(a*ap<(c=(c+360)%360-180)&&c<a*t)?(o=-i[1]*Ah)<ip&&(ip=o):(n<ip&&(ip=n),n>up&&(up=n)),s?t<ap?Qp(rp,t)>Qp(rp,op)&&(op=t):Qp(t,op)>Qp(rp,op)&&(rp=t):op>=rp?(t<rp&&(rp=t),t>op&&(op=t)):t>ap?Qp(rp,t)>Qp(rp,op)&&(op=t):Qp(t,op)>Qp(rp,op)&&(rp=t)}else Xp(t,n);fp=e,ap=t}function $p(){Hp.point=Vp}function Wp(){kp[0]=rp,kp[1]=op,Hp.point=Xp,fp=null}function Zp(t,n){if(fp){var e=t-ap;jp.add(Ch(e)>180?e+(e>0?360:-360):e)}else cp=t,sp=n;pp.point(t,n),Vp(t,n)}function Gp(){pp.lineStart()}function Jp(){Zp(cp,sp),pp.lineEnd(),Ch(jp)>wh&&(rp=-(op=180)),kp[0]=rp,kp[1]=op,fp=null}function Qp(t,n){return(n-=t)<0?n+360:n}function Kp(t,n){return t[0]-n[0]}function td(t,n){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}var nd={sphere:Xh,point:ed,lineStart:id,lineEnd:ad,polygonStart:function(){nd.lineStart=cd,nd.lineEnd=sd},polygonEnd:function(){nd.lineStart=id,nd.lineEnd=ad}};function ed(t,n){t*=Eh;var e=qh(n*=Eh);rd(e*qh(t),e*Oh(t),Oh(n))}function rd(t,n,e){Ep+=(t-Ep)/++Np,Cp+=(n-Cp)/Np,zp+=(e-zp)/Np}function id(){nd.point=od}function od(t,n){t*=Eh;var e=qh(n*=Eh);Ip=e*qh(t),Yp=e*Oh(t),Bp=Oh(n),nd.point=ud,rd(Ip,Yp,Bp)}function ud(t,n){t*=Eh;var e=qh(n*=Eh),r=e*qh(t),i=e*Oh(t),o=Oh(n),u=Ph(Ih((u=Yp*o-Bp*i)*u+(u=Bp*r-Ip*o)*u+(u=Ip*i-Yp*r)*u),Ip*r+Yp*i+Bp*o);Ap+=u,Pp+=u*(Ip+(Ip=r)),qp+=u*(Yp+(Yp=i)),Lp+=u*(Bp+(Bp=o)),rd(Ip,Yp,Bp)}function ad(){nd.point=ed}function cd(){nd.point=fd}function sd(){ld(Op,Fp),nd.point=ed}function fd(t,n){Op=t,Fp=n,t*=Eh,n*=Eh,nd.point=ld;var e=qh(n);Ip=e*qh(t),Yp=e*Oh(t),Bp=Oh(n),rd(Ip,Yp,Bp)}function ld(t,n){t*=Eh;var e=qh(n*=Eh),r=e*qh(t),i=e*Oh(t),o=Oh(n),u=Yp*o-Bp*i,a=Bp*r-Ip*o,c=Ip*i-Yp*r,s=Ih(u*u+a*a+c*c),f=Ip*r+Yp*i+Bp*o,l=s&&-Bh(f)/s,h=Ph(s,f);Rp+=l*u,Up+=l*a,Dp+=l*c,Ap+=h,Pp+=h*(Ip+(Ip=r)),qp+=h*(Yp+(Yp=i)),Lp+=h*(Bp+(Bp=o)),rd(Ip,Yp,Bp)}function hd(t){return function(){return t}}function pd(t,n){function e(e,r){return e=t(e,r),n(e[0],e[1])}return t.invert&&n.invert&&(e.invert=function(e,r){return(e=n.invert(e,r))&&t.invert(e[0],e[1])}),e}function dd(t,n){return[t>Th?t-Nh:t<-Th?t+Nh:t,n]}function vd(t,n,e){return(t%=Nh)?n||e?pd(yd(t),gd(n,e)):yd(t):n||e?gd(n,e):dd}function _d(t){return function(n,e){return[(n+=t)>Th?n-Nh:n<-Th?n+Nh:n,e]}}function yd(t){var n=_d(t);return n.invert=_d(-t),n}function gd(t,n){var e=qh(t),r=Oh(t),i=qh(n),o=Oh(n);function u(t,n){var u=qh(n),a=qh(t)*u,c=Oh(t)*u,s=Oh(n),f=s*e+a*r;return[Ph(c*i-f*o,a*e-s*r),jh(f*i+c*o)]}return u.invert=function(t,n){var u=qh(n),a=qh(t)*u,c=Oh(t)*u,s=Oh(n),f=s*i-c*o;return[Ph(c*i+s*o,a*e+f*r),jh(f*e-a*r)]},u}function md(t,n,e,r,i,o){if(e){var u=qh(n),a=Oh(n),c=r*e;null==i?(i=n+r*Nh,o=n-c/2):(i=xd(u,i),o=xd(u,o),(r>0?i<o:i>o)&&(i+=r*Nh));for(var s,f=i;r>0?f>o:f<o;f-=c)s=gp([u,-a*qh(f),-a*Oh(f)]),t.point(s[0],s[1])}}function xd(t,n){(n=mp(n))[0]-=t,Tp(n);var e=Bh(-n[1]);return((-n[2]<0?-e:e)+Nh-wh)%Nh}function bd(){var t,n=[];return{point:function(n,e){t.push([n,e])},lineStart:function(){n.push(t=[])},lineEnd:Xh,rejoin:function(){n.length>1&&n.push(n.pop().concat(n.shift()))},result:function(){var e=n;return n=[],t=null,e}}}function wd(t,n){return Ch(t[0]-n[0])<wh&&Ch(t[1]-n[1])<wh}function Md(t,n,e,r){this.x=t,this.z=n,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}function Td(t,n,e,r,i){var o,u,a=[],c=[];if(t.forEach(function(t){if(!((n=t.length-1)<=0)){var n,e,r=t[0],u=t[n];if(wd(r,u)){for(i.lineStart(),o=0;o<n;++o)i.point((r=t[o])[0],r[1]);i.lineEnd()}else a.push(e=new Md(r,t,null,!0)),c.push(e.o=new Md(r,null,e,!1)),a.push(e=new Md(u,t,null,!1)),c.push(e.o=new Md(u,null,e,!0))}}),a.length){for(c.sort(n),Sd(a),Sd(c),o=0,u=c.length;o<u;++o)c[o].e=e=!e;for(var s,f,l=a[0];;){for(var h=l,p=!0;h.v;)if((h=h.n)===l)return;s=h.z,i.lineStart();do{if(h.v=h.o.v=!0,h.e){if(p)for(o=0,u=s.length;o<u;++o)i.point((f=s[o])[0],f[1]);else r(h.x,h.n.x,1,i);h=h.n}else{if(p)for(s=h.p.z,o=s.length-1;o>=0;--o)i.point((f=s[o])[0],f[1]);else r(h.x,h.p.x,-1,i);h=h.p}s=(h=h.o).z,p=!p}while(!h.v);i.lineEnd()}}}function Sd(t){if(n=t.length){for(var n,e,r=0,i=t[0];++r<n;)i.n=e=t[r],e.p=i,i=e;i.n=e=t[0],e.p=i}}dd.invert=dd;var kd=1e9,Nd=-kd;function Ad(t,n,e,r){function i(i,o){return t<=i&&i<=e&&n<=o&&o<=r}function o(i,o,a,s){var f=0,l=0;if(null==i||(f=u(i,a))!==(l=u(o,a))||c(i,o)<0^a>0)do{s.point(0===f||3===f?t:e,f>1?r:n)}while((f=(f+a+4)%4)!==l);else s.point(o[0],o[1])}function u(r,i){return Ch(r[0]-t)<wh?i>0?0:3:Ch(r[0]-e)<wh?i>0?2:1:Ch(r[1]-n)<wh?i>0?1:0:i>0?3:2}function a(t,n){return c(t.x,n.x)}function c(t,n){var e=u(t,1),r=u(n,1);return e!==r?e-r:0===e?n[1]-t[1]:1===e?t[0]-n[0]:2===e?t[1]-n[1]:n[0]-t[0]}return function(u){var c,s,f,l,h,p,d,v,_,y,g,m=u,x=bd(),b={point:w,lineStart:function(){b.point=T,s&&s.push(f=[]);y=!0,_=!1,d=v=NaN},lineEnd:function(){c&&(T(l,h),p&&_&&x.rejoin(),c.push(x.result()));b.point=w,_&&m.lineEnd()},polygonStart:function(){m=x,c=[],s=[],g=!0},polygonEnd:function(){var n=function(){for(var n=0,e=0,i=s.length;e<i;++e)for(var o,u,a=s[e],c=1,f=a.length,l=a[0],h=l[0],p=l[1];c<f;++c)o=h,u=p,l=a[c],h=l[0],p=l[1],u<=r?p>r&&(h-o)*(r-u)>(p-u)*(t-o)&&++n:p<=r&&(h-o)*(r-u)<(p-u)*(t-o)&&--n;return n}(),e=g&&n,i=(c=M(c)).length;(e||i)&&(u.polygonStart(),e&&(u.lineStart(),o(null,null,1,u),u.lineEnd()),i&&Td(c,a,n,o,u),u.polygonEnd());m=u,c=s=f=null}};function w(t,n){i(t,n)&&m.point(t,n)}function T(o,u){var a=i(o,u);if(s&&f.push([o,u]),y)l=o,h=u,p=a,y=!1,a&&(m.lineStart(),m.point(o,u));else if(a&&_)m.point(o,u);else{var c=[d=Math.max(Nd,Math.min(kd,d)),v=Math.max(Nd,Math.min(kd,v))],x=[o=Math.max(Nd,Math.min(kd,o)),u=Math.max(Nd,Math.min(kd,u))];!function(t,n,e,r,i,o){var u,a=t[0],c=t[1],s=0,f=1,l=n[0]-a,h=n[1]-c;if(u=e-a,l||!(u>0)){if(u/=l,l<0){if(u<s)return;u<f&&(f=u)}else if(l>0){if(u>f)return;u>s&&(s=u)}if(u=i-a,l||!(u<0)){if(u/=l,l<0){if(u>f)return;u>s&&(s=u)}else if(l>0){if(u<s)return;u<f&&(f=u)}if(u=r-c,h||!(u>0)){if(u/=h,h<0){if(u<s)return;u<f&&(f=u)}else if(h>0){if(u>f)return;u>s&&(s=u)}if(u=o-c,h||!(u<0)){if(u/=h,h<0){if(u>f)return;u>s&&(s=u)}else if(h>0){if(u<s)return;u<f&&(f=u)}return s>0&&(t[0]=a+s*l,t[1]=c+s*h),f<1&&(n[0]=a+f*l,n[1]=c+f*h),!0}}}}}(c,x,t,n,e,r)?a&&(m.lineStart(),m.point(o,u),g=!1):(_||(m.lineStart(),m.point(c[0],c[1])),m.point(x[0],x[1]),a||m.lineEnd(),g=!1)}d=o,v=u,_=a}return b}}var Ed,Cd,zd,Pd=gh(),qd={sphere:Xh,point:Xh,lineStart:function(){qd.point=Rd,qd.lineEnd=Ld},lineEnd:Xh,polygonStart:Xh,polygonEnd:Xh};function Ld(){qd.point=qd.lineEnd=Xh}function Rd(t,n){Ed=t*=Eh,Cd=Oh(n*=Eh),zd=qh(n),qd.point=Ud}function Ud(t,n){t*=Eh;var e=Oh(n*=Eh),r=qh(n),i=Ch(t-Ed),o=qh(i),u=r*Oh(i),a=zd*e-Cd*r*o,c=Cd*e+zd*r*o;Pd.add(Ph(Ih(u*u+a*a),c)),Ed=t,Cd=e,zd=r}function Dd(t){return Pd.reset(),Jh(t,qd),+Pd}var Od=[null,null],Fd={type:"LineString",coordinates:Od};function Id(t,n,e){var r=v(t,n-wh,e).concat(n);return function(t){return r.map(function(n){return[t,n]})}}function Yd(t,n,e){var r=v(t,n-wh,e).concat(n);return function(t){return r.map(function(n){return[n,t]})}}function Bd(t){return t}var jd,Hd,Xd,Vd,$d=gh(),Wd=gh(),Zd={point:Xh,lineStart:Xh,lineEnd:Xh,polygonStart:function(){Zd.lineStart=Gd,Zd.lineEnd=Kd},polygonEnd:function(){Zd.lineStart=Zd.lineEnd=Zd.point=Xh,$d.add(Ch(Wd)),Wd.reset()},result:function(){var t=$d/2;return $d.reset(),t}};function Gd(){Zd.point=Jd}function Jd(t,n){Zd.point=Qd,jd=Xd=t,Hd=Vd=n}function Qd(t,n){Wd.add(Vd*t-Xd*n),Xd=t,Vd=n}function Kd(){Qd(jd,Hd)}var tv=1/0,nv=tv,ev=-tv,rv=ev,iv={point:function(t,n){t<tv&&(tv=t);t>ev&&(ev=t);n<nv&&(nv=n);n>rv&&(rv=n)},lineStart:Xh,lineEnd:Xh,polygonStart:Xh,polygonEnd:Xh,result:function(){var t=[[tv,nv],[ev,rv]];return ev=rv=-(nv=tv=1/0),t}};var ov,uv,av,cv,sv=0,fv=0,lv=0,hv=0,pv=0,dv=0,vv=0,_v=0,yv=0,gv={point:mv,lineStart:xv,lineEnd:Mv,polygonStart:function(){gv.lineStart=Tv,gv.lineEnd=Sv},polygonEnd:function(){gv.point=mv,gv.lineStart=xv,gv.lineEnd=Mv},result:function(){var t=yv?[vv/yv,_v/yv]:dv?[hv/dv,pv/dv]:lv?[sv/lv,fv/lv]:[NaN,NaN];return sv=fv=lv=hv=pv=dv=vv=_v=yv=0,t}};function mv(t,n){sv+=t,fv+=n,++lv}function xv(){gv.point=bv}function bv(t,n){gv.point=wv,mv(av=t,cv=n)}function wv(t,n){var e=t-av,r=n-cv,i=Ih(e*e+r*r);hv+=i*(av+t)/2,pv+=i*(cv+n)/2,dv+=i,mv(av=t,cv=n)}function Mv(){gv.point=mv}function Tv(){gv.point=kv}function Sv(){Nv(ov,uv)}function kv(t,n){gv.point=Nv,mv(ov=av=t,uv=cv=n)}function Nv(t,n){var e=t-av,r=n-cv,i=Ih(e*e+r*r);hv+=i*(av+t)/2,pv+=i*(cv+n)/2,dv+=i,vv+=(i=cv*t-av*n)*(av+t),_v+=i*(cv+n),yv+=3*i,mv(av=t,cv=n)}function Av(t){var n=4.5,e={point:r,lineStart:function(){e.point=i},lineEnd:u,polygonStart:function(){e.lineEnd=a},polygonEnd:function(){e.lineEnd=u,e.point=r},pointRadius:function(t){return n=t,e},result:Xh};function r(e,r){t.moveTo(e+n,r),t.arc(e,r,n,0,Nh)}function i(n,r){t.moveTo(n,r),e.point=o}function o(n,e){t.lineTo(n,e)}function u(){e.point=r}function a(){t.closePath()}return e}function Ev(){var t=Cv(4.5),n=[],e={point:r,lineStart:function(){e.point=i},lineEnd:u,polygonStart:function(){e.lineEnd=a},polygonEnd:function(){e.lineEnd=u,e.point=r},pointRadius:function(n){return t=Cv(n),e},result:function(){if(n.length){var t=n.join("");return n=[],t}}};function r(e,r){n.push("M",e,",",r,t)}function i(t,r){n.push("M",t,",",r),e.point=o}function o(t,e){n.push("L",t,",",e)}function u(){e.point=r}function a(){n.push("Z")}return e}function Cv(t){return"m0,"+t+"a"+t+","+t+" 0 1,1 0,"+-2*t+"a"+t+","+t+" 0 1,1 0,"+2*t+"z"}var zv=gh();function Pv(t,n,e,r){return function(i,o){var u,a,c,s=n(o),f=i.invert(r[0],r[1]),l=bd(),h=n(l),p=!1,d={point:v,lineStart:y,lineEnd:g,polygonStart:function(){d.point=m,d.lineStart=x,d.lineEnd=b,a=[],u=[]},polygonEnd:function(){d.point=v,d.lineStart=y,d.lineEnd=g,a=M(a);var t=function(t,n){var e=n[0],r=n[1],i=[Oh(e),-qh(e),0],o=0,u=0;zv.reset();for(var a=0,c=t.length;a<c;++a)if(f=(s=t[a]).length)for(var s,f,l=s[f-1],h=l[0],p=l[1]/2+kh,d=Oh(p),v=qh(p),_=0;_<f;++_,h=g,d=x,v=b,l=y){var y=s[_],g=y[0],m=y[1]/2+kh,x=Oh(m),b=qh(m),w=g-h,M=w>=0?1:-1,T=M*w,S=T>Th,k=d*x;if(zv.add(Ph(k*M*Oh(T),v*b+k*qh(T))),o+=S?w+M*Nh:w,S^h>=e^g>=e){var N=bp(mp(l),mp(y));Tp(N);var A=bp(i,N);Tp(A);var E=(S^w>=0?-1:1)*jh(A[2]);(r>E||r===E&&(N[0]||N[1]))&&(u+=S^w>=0?1:-1)}}return(o<-wh||o<wh&&zv<-wh)^1&u}(u,f);a.length?(p||(o.polygonStart(),p=!0),Td(a,Lv,t,e,o)):t&&(p||(o.polygonStart(),p=!0),o.lineStart(),e(null,null,1,o),o.lineEnd()),p&&(o.polygonEnd(),p=!1),a=u=null},sphere:function(){o.polygonStart(),o.lineStart(),e(null,null,1,o),o.lineEnd(),o.polygonEnd()}};function v(n,e){var r=i(n,e);t(n=r[0],e=r[1])&&o.point(n,e)}function _(t,n){var e=i(t,n);s.point(e[0],e[1])}function y(){d.point=_,s.lineStart()}function g(){d.point=v,s.lineEnd()}function m(t,n){c.push([t,n]);var e=i(t,n);h.point(e[0],e[1])}function x(){h.lineStart(),c=[]}function b(){m(c[0][0],c[0][1]),h.lineEnd();var t,n,e,r,i=h.clean(),s=l.result(),f=s.length;if(c.pop(),u.push(c),c=null,f)if(1&i){if((n=(e=s[0]).length-1)>0){for(p||(o.polygonStart(),p=!0),o.lineStart(),t=0;t<n;++t)o.point((r=e[t])[0],r[1]);o.lineEnd()}}else f>1&&2&i&&s.push(s.pop().concat(s.shift())),a.push(s.filter(qv))}return d}}function qv(t){return t.length>1}function Lv(t,n){return((t=t.x)[0]<0?t[1]-Sh-wh:Sh-t[1])-((n=n.x)[0]<0?n[1]-Sh-wh:Sh-n[1])}var Rv=Pv(function(){return!0},function(t){var n,e=NaN,r=NaN,i=NaN;return{lineStart:function(){t.lineStart(),n=1},point:function(o,u){var a=o>0?Th:-Th,c=Ch(o-e);Ch(c-Th)<wh?(t.point(e,r=(r+u)/2>0?Sh:-Sh),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(a,r),t.point(o,r),n=0):i!==a&&c>=Th&&(Ch(e-i)<wh&&(e-=i*wh),Ch(o-a)<wh&&(o-=a*wh),r=function(t,n,e,r){var i,o,u=Oh(t-e);return Ch(u)>wh?zh((Oh(n)*(o=qh(r))*Oh(e)-Oh(r)*(i=qh(n))*Oh(t))/(i*o*u)):(n+r)/2}(e,r,o,u),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(a,r),n=0),t.point(e=o,r=u),i=a},lineEnd:function(){t.lineEnd(),e=r=NaN},clean:function(){return 2-n}}},function(t,n,e,r){var i;if(null==t)i=e*Sh,r.point(-Th,i),r.point(0,i),r.point(Th,i),r.point(Th,0),r.point(Th,-i),r.point(0,-i),r.point(-Th,-i),r.point(-Th,0),r.point(-Th,i);else if(Ch(t[0]-n[0])>wh){var o=t[0]<n[0]?Th:-Th;i=e*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(n[0],n[1])},[-Th,-Sh]);function Uv(t){function n(){}var e=n.prototype=Object.create(Dv.prototype);for(var r in t)e[r]=t[r];return function(t){var e=new n;return e.stream=t,e}}function Dv(){}function Ov(t,n,e){var r=n[1][0]-n[0][0],i=n[1][1]-n[0][1],o=t.clipExtent&&t.clipExtent();t.scale(150).translate([0,0]),null!=o&&t.clipExtent(null),Jh(e,t.stream(iv));var u=iv.result(),a=Math.min(r/(u[1][0]-u[0][0]),i/(u[1][1]-u[0][1])),c=+n[0][0]+(r-a*(u[1][0]+u[0][0]))/2,s=+n[0][1]+(i-a*(u[1][1]+u[0][1]))/2;return null!=o&&t.clipExtent(o),t.scale(150*a).translate([c,s])}function Fv(t){return function(n,e){return Ov(t,[[0,0],n],e)}}function Iv(t){return function(n,e){return Ov(t,n,e)}}Dv.prototype={point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var Yv=16,Bv=qh(30*Eh);function jv(t,n){return+n?function(t,n){function e(r,i,o,u,a,c,s,f,l,h,p,d,v,_){var y=s-r,g=f-i,m=y*y+g*g;if(m>4*n&&v--){var x=u+h,b=a+p,w=c+d,M=Ih(x*x+b*b+w*w),T=jh(w/=M),S=Ch(Ch(w)-1)<wh||Ch(o-l)<wh?(o+l)/2:Ph(b,x),k=t(S,T),N=k[0],A=k[1],E=N-r,C=A-i,z=g*E-y*C;(z*z/m>n||Ch((y*E+g*C)/m-.5)>.3||u*h+a*p+c*d<Bv)&&(e(r,i,o,u,a,c,N,A,S,x/=M,b/=M,w,v,_),_.point(N,A),e(N,A,S,x,b,w,s,f,l,h,p,d,v,_))}}return function(n){var r,i,o,u,a,c,s,f,l,h,p,d,v={point:_,lineStart:y,lineEnd:m,polygonStart:function(){n.polygonStart(),v.lineStart=x},polygonEnd:function(){n.polygonEnd(),v.lineStart=y}};function _(e,r){e=t(e,r),n.point(e[0],e[1])}function y(){f=NaN,v.point=g,n.lineStart()}function g(r,i){var o=mp([r,i]),u=t(r,i);e(f,l,s,h,p,d,f=u[0],l=u[1],s=r,h=o[0],p=o[1],d=o[2],Yv,n),n.point(f,l)}function m(){v.point=_,n.lineEnd()}function x(){y(),v.point=b,v.lineEnd=w}function b(t,n){g(r=t,n),i=f,o=l,u=h,a=p,c=d,v.point=g}function w(){e(f,l,s,h,p,d,i,o,r,u,a,c,Yv,n),v.lineEnd=m,m()}return v}}(t,n):function(t){return Uv({point:function(n,e){n=t(n,e),this.stream.point(n[0],n[1])}})}(t)}var Hv=Uv({point:function(t,n){this.stream.point(t*Eh,n*Eh)}});function Xv(t){return Vv(function(){return t})()}function Vv(t){var n,e,r,i,o,u,a,c,s,f,l=150,h=480,p=250,d=0,v=0,_=0,y=0,g=0,m=null,x=Rv,b=null,w=Bd,M=.5,T=jv(N,M);function S(t){return[(t=o(t[0]*Eh,t[1]*Eh))[0]*l+e,r-t[1]*l]}function k(t){return(t=o.invert((t[0]-e)/l,(r-t[1])/l))&&[t[0]*Ah,t[1]*Ah]}function N(t,i){return[(t=n(t,i))[0]*l+e,r-t[1]*l]}function A(){o=pd(i=vd(_,y,g),n);var t=n(d,v);return e=h-t[0]*l,r=p+t[1]*l,E()}function E(){return s=f=null,S}return S.stream=function(t){return s&&f===t?s:s=Hv(x(i,T(w(f=t))))},S.clipAngle=function(t){return arguments.length?(x=+t?function(t,n){var e=qh(t),r=e>0,i=Ch(e)>wh;function o(t,n){return qh(t)*qh(n)>e}function u(t,n,r){var i=[1,0,0],o=bp(mp(t),mp(n)),u=xp(o,o),a=o[0],c=u-a*a;if(!c)return!r&&t;var s=e*u/c,f=-e*a/c,l=bp(i,o),h=Mp(i,s);wp(h,Mp(o,f));var p=l,d=xp(h,p),v=xp(p,p),_=d*d-v*(xp(h,h)-1);if(!(_<0)){var y=Ih(_),g=Mp(p,(-d-y)/v);if(wp(g,h),g=gp(g),!r)return g;var m,x=t[0],b=n[0],w=t[1],M=n[1];b<x&&(m=x,x=b,b=m);var T=b-x,S=Ch(T-Th)<wh;if(!S&&M<w&&(m=w,w=M,M=m),S||T<wh?S?w+M>0^g[1]<(Ch(g[0]-x)<wh?w:M):w<=g[1]&&g[1]<=M:T>Th^(x<=g[0]&&g[0]<=b)){var k=Mp(p,(-d+y)/v);return wp(k,h),[g,gp(k)]}}}function a(n,e){var i=r?t:Th-t,o=0;return n<-i?o|=1:n>i&&(o|=2),e<-i?o|=4:e>i&&(o|=8),o}return Pv(o,function(t){var n,e,c,s,f;return{lineStart:function(){s=c=!1,f=1},point:function(l,h){var p,d=[l,h],v=o(l,h),_=r?v?0:a(l,h):v?a(l+(l<0?Th:-Th),h):0;if(!n&&(s=c=v)&&t.lineStart(),v!==c&&(p=u(n,d),(wd(n,p)||wd(d,p))&&(d[0]+=wh,d[1]+=wh,v=o(d[0],d[1]))),v!==c)f=0,v?(t.lineStart(),p=u(d,n),t.point(p[0],p[1])):(p=u(n,d),t.point(p[0],p[1]),t.lineEnd()),n=p;else if(i&&n&&r^v){var y;_&e||!(y=u(d,n,!0))||(f=0,r?(t.lineStart(),t.point(y[0][0],y[0][1]),t.point(y[1][0],y[1][1]),t.lineEnd()):(t.point(y[1][0],y[1][1]),t.lineEnd(),t.lineStart(),t.point(y[0][0],y[0][1])))}!v||n&&wd(n,d)||t.point(d[0],d[1]),n=d,c=v,e=_},lineEnd:function(){c&&t.lineEnd(),n=null},clean:function(){return f|(s&&c)<<1}}},function(e,r,i,o){md(o,t,n,i,e,r)},r?[0,-t]:[-Th,t-Th])}(m=t*Eh,6*Eh):(m=null,Rv),E()):m*Ah},S.clipExtent=function(t){return arguments.length?(w=null==t?(b=u=a=c=null,Bd):Ad(b=+t[0][0],u=+t[0][1],a=+t[1][0],c=+t[1][1]),E()):null==b?null:[[b,u],[a,c]]},S.scale=function(t){return arguments.length?(l=+t,A()):l},S.translate=function(t){return arguments.length?(h=+t[0],p=+t[1],A()):[h,p]},S.center=function(t){return arguments.length?(d=t[0]%360*Eh,v=t[1]%360*Eh,A()):[d*Ah,v*Ah]},S.rotate=function(t){return arguments.length?(_=t[0]%360*Eh,y=t[1]%360*Eh,g=t.length>2?t[2]%360*Eh:0,A()):[_*Ah,y*Ah,g*Ah]},S.precision=function(t){return arguments.length?(T=jv(N,M=t*t),E()):Ih(M)},S.fitExtent=Iv(S),S.fitSize=Fv(S),function(){return n=t.apply(this,arguments),S.invert=n.invert&&k,A()}}function $v(t){var n=0,e=Th/3,r=Vv(t),i=r(n,e);return i.parallels=function(t){return arguments.length?r(n=t[0]*Eh,e=t[1]*Eh):[n*Ah,e*Ah]},i}function Wv(t,n){var e=Oh(t),r=(e+Oh(n))/2,i=1+e*(2*r-e),o=Ih(i)/r;function u(t,n){var e=Ih(i-2*r*Oh(n))/r;return[e*Oh(t*=r),o-e*qh(t)]}return u.invert=function(t,n){var e=o-n;return[Ph(t,e)/r,jh((i-(t*t+e*e)*r*r)/(2*r))]},u}function Zv(){return $v(Wv).scale(155.424).center([0,33.6442])}function Gv(){return Zv().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function Jv(t){return function(n,e){var r=qh(n),i=qh(e),o=t(r*i);return[o*i*Oh(n),o*Oh(e)]}}function Qv(t){return function(n,e){var r=Ih(n*n+e*e),i=t(r),o=Oh(i),u=qh(i);return[Ph(n*o,r*u),jh(r&&e*o/r)]}}var Kv=Jv(function(t){return Ih(2/(1+t))});Kv.invert=Qv(function(t){return 2*jh(t/2)});var t_=Jv(function(t){return(t=Bh(t))&&t/Oh(t)});function n_(t,n){return[t,Uh(Yh((Sh+n)/2))]}function e_(t){var n,e=Xv(t),r=e.scale,i=e.translate,o=e.clipExtent;return e.scale=function(t){return arguments.length?(r(t),n&&e.clipExtent(null),e):r()},e.translate=function(t){return arguments.length?(i(t),n&&e.clipExtent(null),e):i()},e.clipExtent=function(t){if(!arguments.length)return n?null:o();if(n=null==t){var u=Th*r(),a=i();t=[[a[0]-u,a[1]-u],[a[0]+u,a[1]+u]]}return o(t),e},e.clipExtent(null)}function r_(t){return Yh((Sh+t)/2)}function i_(t,n){var e=qh(t),r=t===n?Oh(t):Uh(e/qh(n))/Uh(r_(n)/r_(t)),i=e*Dh(r_(t),r)/r;if(!r)return n_;function o(t,n){i>0?n<-Sh+wh&&(n=-Sh+wh):n>Sh-wh&&(n=Sh-wh);var e=i/Dh(r_(n),r);return[e*Oh(r*t),i-e*qh(r*t)]}return o.invert=function(t,n){var e=i-n,o=Fh(r)*Ih(t*t+e*e);return[Ph(t,e)/r,2*zh(Dh(i/o,1/r))-Sh]},o}function o_(t,n){return[t,n]}function u_(t,n){var e=qh(t),r=t===n?Oh(t):(e-qh(n))/(n-t),i=e/r+t;if(Ch(r)<wh)return o_;function o(t,n){var e=i-n,o=r*t;return[e*Oh(o),i-e*qh(o)]}return o.invert=function(t,n){var e=i-n;return[Ph(t,e)/r,i-Fh(r)*Ih(t*t+e*e)]},o}function a_(t,n){var e=qh(n),r=qh(t)*e;return[e*Oh(t)/r,Oh(n)/r]}function c_(t,n){return[qh(n)*Oh(t),Oh(n)]}function s_(t,n){var e=qh(n),r=1+qh(t)*e;return[e*Oh(t)/r,Oh(n)/r]}function f_(t,n){return[Uh(Yh((Sh+n)/2)),-t]}t_.invert=Qv(function(t){return t}),n_.invert=function(t,n){return[t,2*zh(Rh(n))-Sh]},o_.invert=o_,a_.invert=Qv(zh),c_.invert=Qv(jh),s_.invert=Qv(function(t){return 2*zh(t)}),f_.invert=function(t,n){return[-n,2*zh(Rh(t))-Sh]},t.version="4.2.2",t.bisect=i,t.bisectRight=i,t.bisectLeft=o,t.ascending=n,t.bisector=e,t.descending=function(t,n){return n<t?-1:n>t?1:n>=t?0:NaN},t.deviation=c,t.extent=s,t.histogram=function(){var t=d,n=s,e=b;function r(r){var o,u,a=r.length,c=new Array(a);for(o=0;o<a;++o)c[o]=t(r[o],o,r);var s=n(c),f=s[0],l=s[1],h=e(c,f,l);Array.isArray(h)||(h=m(f,l,h));for(var p=h.length;h[0]<=f;)h.shift(),--p;for(;h[p-1]>=l;)h.pop(),--p;var d,v=new Array(p+1);for(o=0;o<=p;++o)(d=v[o]=[]).x0=o>0?h[o-1]:f,d.x1=o<p?h[o]:l;for(o=0;o<a;++o)f<=(u=c[o])&&u<=l&&v[i(h,u,0,p)].push(r[o]);return v}return r.value=function(n){return arguments.length?(t="function"==typeof n?n:p(n),r):t},r.domain=function(t){return arguments.length?(n="function"==typeof t?t:p([t[0],t[1]]),r):n},r.thresholds=function(t){return arguments.length?(e="function"==typeof t?t:Array.isArray(t)?p(l.call(t)):p(t),r):e},r},t.thresholdFreedmanDiaconis=function(t,e,r){return t=h.call(t,u).sort(n),Math.ceil((r-e)/(2*(w(t,.75)-w(t,.25))*Math.pow(t.length,-1/3)))},t.thresholdScott=function(t,n,e){return Math.ceil((e-n)/(3.5*c(t)*Math.pow(t.length,-1/3)))},t.thresholdSturges=b,t.max=function(t,n){var e,r,i=-1,o=t.length;if(null==n){for(;++i<o;)if(null!=(r=t[i])&&r>=r){e=r;break}for(;++i<o;)null!=(r=t[i])&&r>e&&(e=r)}else{for(;++i<o;)if(null!=(r=n(t[i],i,t))&&r>=r){e=r;break}for(;++i<o;)null!=(r=n(t[i],i,t))&&r>e&&(e=r)}return e},t.mean=function(t,n){var e,r=0,i=t.length,o=-1,a=i;if(null==n)for(;++o<i;)isNaN(e=u(t[o]))?--a:r+=e;else for(;++o<i;)isNaN(e=u(n(t[o],o,t)))?--a:r+=e;if(a)return r/a},t.median=function(t,e){var r,i=[],o=t.length,a=-1;if(null==e)for(;++a<o;)isNaN(r=u(t[a]))||i.push(r);else for(;++a<o;)isNaN(r=u(e(t[a],a,t)))||i.push(r);return w(i.sort(n),.5)},t.merge=M,t.min=T,t.pairs=function(t){for(var n=0,e=t.length-1,r=t[0],i=new Array(e<0?0:e);n<e;)i[n]=[r,r=t[++n]];return i},t.permute=function(t,n){for(var e=n.length,r=new Array(e);e--;)r[e]=t[n[e]];return r},t.quantile=w,t.range=v,t.scan=function(t,e){if(r=t.length){var r,i,o=0,u=0,a=t[u];for(e||(e=n);++o<r;)(e(i=t[o],a)<0||0!==e(a,a))&&(a=i,u=o);return 0===e(a,a)?u:void 0}},t.shuffle=function(t,n,e){for(var r,i,o=(null==e?t.length:e)-(n=null==n?0:+n);o;)i=Math.random()*o--|0,r=t[o+n],t[o+n]=t[i+n],t[i+n]=r;return t},t.sum=function(t,n){var e,r=0,i=t.length,o=-1;if(null==n)for(;++o<i;)(e=+t[o])&&(r+=e);else for(;++o<i;)(e=+n(t[o],o,t))&&(r+=e);return r},t.ticks=m,t.tickStep=x,t.transpose=S,t.variance=a,t.zip=function(){return S(arguments)},t.entries=function(t){var n=[];for(var e in t)n.push({key:e,value:t[e]});return n},t.keys=function(t){var n=[];for(var e in t)n.push(e);return n},t.values=function(t){var n=[];for(var e in t)n.push(t[e]);return n},t.map=A,t.set=R,t.nest=function(){var t,n,e,r=[],i=[];function o(e,i,u,a){if(i>=r.length)return null!=n?n(e):null!=t?e.sort(t):e;for(var c,s,f,l=-1,h=e.length,p=r[i++],d=A(),v=u();++l<h;)(f=d.get(c=p(s=e[l])+""))?f.push(s):d.set(c,[s]);return d.each(function(t,n){a(v,n,o(t,i,u,a))}),v}return e={object:function(t){return o(t,0,E,C)},map:function(t){return o(t,0,z,P)},entries:function(t){return function t(e,o){if(++o>r.length)return e;var u,a=i[o-1];return null!=n&&o>=r.length?u=e.entries():(u=[],e.each(function(n,e){u.push({key:e,values:t(n,o)})})),null!=a?u.sort(function(t,n){return a(t.key,n.key)}):u}(o(t,0,z,P),0)},key:function(t){return r.push(t),e},sortKeys:function(t){return i[r.length-1]=t,e},sortValues:function(n){return t=n,e},rollup:function(t){return n=t,e}}},t.randomUniform=function(t,n){return t=null==t?0:+t,n=null==n?1:+n,1===arguments.length?(n=t,t=0):n-=t,function(){return Math.random()*n+t}},t.randomNormal=U,t.randomLogNormal=function(){var t=U.apply(this,arguments);return function(){return Math.exp(t())}},t.randomBates=function(t){var n=D(t);return function(){return n()/t}},t.randomIrwinHall=D,t.randomExponential=function(t){return function(){return-Math.log(1-Math.random())/t}},t.easeLinear=function(t){return+t},t.easeQuad=O,t.easeQuadIn=function(t){return t*t},t.easeQuadOut=function(t){return t*(2-t)},t.easeQuadInOut=O,t.easeCubic=F,t.easeCubicIn=function(t){return t*t*t},t.easeCubicOut=function(t){return--t*t*t+1},t.easeCubicInOut=F,t.easePoly=B,t.easePolyIn=I,t.easePolyOut=Y,t.easePolyInOut=B,t.easeSin=X,t.easeSinIn=function(t){return 1-Math.cos(t*H)},t.easeSinOut=function(t){return Math.sin(t*H)},t.easeSinInOut=X,t.easeExp=V,t.easeExpIn=function(t){return Math.pow(2,10*t-10)},t.easeExpOut=function(t){return 1-Math.pow(2,-10*t)},t.easeExpInOut=V,t.easeCircle=$,t.easeCircleIn=function(t){return 1-Math.sqrt(1-t*t)},t.easeCircleOut=function(t){return Math.sqrt(1- --t*t)},t.easeCircleInOut=$,t.easeBounce=it,t.easeBounceIn=function(t){return 1-it(1-t)},t.easeBounceOut=it,t.easeBounceInOut=function(t){return((t*=2)<=1?1-it(1-t):it(t-1)+1)/2},t.easeBack=at,t.easeBackIn=ot,t.easeBackOut=ut,t.easeBackInOut=at,t.easeElastic=ft,t.easeElasticIn=st,t.easeElasticOut=ft,t.easeElasticInOut=lt,t.polygonArea=function(t){for(var n,e=-1,r=t.length,i=t[r-1],o=0;++e<r;)n=i,i=t[e],o+=n[1]*i[0]-n[0]*i[1];return o/2},t.polygonCentroid=function(t){for(var n,e,r=-1,i=t.length,o=0,u=0,a=t[i-1],c=0;++r<i;)n=a,a=t[r],c+=e=n[0]*a[1]-a[0]*n[1],o+=(n[0]+a[0])*e,u+=(n[1]+a[1])*e;return[o/(c*=3),u/c]},t.polygonHull=function(t){if((e=t.length)<3)return null;var n,e,r=new Array(e),i=new Array(e);for(n=0;n<e;++n)r[n]=[+t[n][0],+t[n][1],n];for(r.sort(ht),n=0;n<e;++n)i[n]=[r[n][0],-r[n][1]];var o=pt(r),u=pt(i),a=u[0]===o[0],c=u[u.length-1]===o[o.length-1],s=[];for(n=o.length-1;n>=0;--n)s.push(t[r[o[n]][2]]);for(n=+a;n<u.length-c;++n)s.push(t[r[u[n]][2]]);return s},t.polygonContains=function(t,n){for(var e,r,i=t.length,o=t[i-1],u=n[0],a=n[1],c=o[0],s=o[1],f=!1,l=0;l<i;++l)e=(o=t[l])[0],(r=o[1])>a!=s>a&&u<(c-e)*(a-r)/(s-r)+e&&(f=!f),c=e,s=r;return f},t.polygonLength=function(t){for(var n,e,r=-1,i=t.length,o=t[i-1],u=o[0],a=o[1],c=0;++r<i;)n=u,e=a,n-=u=(o=t[r])[0],e-=a=o[1],c+=Math.sqrt(n*n+e*e);return c},t.path=gt,t.quadtree=Mt,t.queue=Lt,t.arc=function(){var t=It,n=Yt,e=Rt(0),r=null,i=Bt,o=jt,u=Ht,a=null;function c(){var c,s,f=+t.apply(this,arguments),l=+n.apply(this,arguments),h=i.apply(this,arguments)-Ot,p=o.apply(this,arguments)-Ot,d=Math.abs(p-h),v=p>h;if(a||(a=c=gt()),l<f&&(s=l,l=f,f=s),l>Ut)if(d>Ft-Ut)a.moveTo(l*Math.cos(h),l*Math.sin(h)),a.arc(0,0,l,h,p,!v),f>Ut&&(a.moveTo(f*Math.cos(p),f*Math.sin(p)),a.arc(0,0,f,p,h,v));else{var _,y,g=h,m=p,x=h,b=p,w=d,M=d,T=u.apply(this,arguments)/2,S=T>Ut&&(r?+r.apply(this,arguments):Math.sqrt(f*f+l*l)),k=Math.min(Math.abs(l-f)/2,+e.apply(this,arguments)),N=k,A=k;if(S>Ut){var E=Xt(S/f*Math.sin(T)),C=Xt(S/l*Math.sin(T));(w-=2*E)>Ut?(x+=E*=v?1:-1,b-=E):(w=0,x=b=(h+p)/2),(M-=2*C)>Ut?(g+=C*=v?1:-1,m-=C):(M=0,g=m=(h+p)/2)}var z=l*Math.cos(g),P=l*Math.sin(g),q=f*Math.cos(b),L=f*Math.sin(b);if(k>Ut){var R=l*Math.cos(m),U=l*Math.sin(m),D=f*Math.cos(x),O=f*Math.sin(x);if(d<Dt){var F=w>Ut?function(t,n,e,r,i,o,u,a){var c=e-t,s=r-n,f=u-i,l=a-o,h=(f*(n-o)-l*(t-i))/(l*c-f*s);return[t+h*c,n+h*s]}(z,P,D,O,R,U,q,L):[q,L],I=z-F[0],Y=P-F[1],B=R-F[0],j=U-F[1],H=1/Math.sin(Math.acos((I*B+Y*j)/(Math.sqrt(I*I+Y*Y)*Math.sqrt(B*B+j*j)))/2),X=Math.sqrt(F[0]*F[0]+F[1]*F[1]);N=Math.min(k,(f-X)/(H-1)),A=Math.min(k,(l-X)/(H+1))}}M>Ut?A>Ut?(_=Vt(D,O,z,P,l,A,v),y=Vt(R,U,q,L,l,A,v),a.moveTo(_.cx+_.x01,_.cy+_.y01),A<k?a.arc(_.cx,_.cy,A,Math.atan2(_.y01,_.x01),Math.atan2(y.y01,y.x01),!v):(a.arc(_.cx,_.cy,A,Math.atan2(_.y01,_.x01),Math.atan2(_.y11,_.x11),!v),a.arc(0,0,l,Math.atan2(_.cy+_.y11,_.cx+_.x11),Math.atan2(y.cy+y.y11,y.cx+y.x11),!v),a.arc(y.cx,y.cy,A,Math.atan2(y.y11,y.x11),Math.atan2(y.y01,y.x01),!v))):(a.moveTo(z,P),a.arc(0,0,l,g,m,!v)):a.moveTo(z,P),f>Ut&&w>Ut?N>Ut?(_=Vt(q,L,R,U,f,-N,v),y=Vt(z,P,D,O,f,-N,v),a.lineTo(_.cx+_.x01,_.cy+_.y01),N<k?a.arc(_.cx,_.cy,N,Math.atan2(_.y01,_.x01),Math.atan2(y.y01,y.x01),!v):(a.arc(_.cx,_.cy,N,Math.atan2(_.y01,_.x01),Math.atan2(_.y11,_.x11),!v),a.arc(0,0,f,Math.atan2(_.cy+_.y11,_.cx+_.x11),Math.atan2(y.cy+y.y11,y.cx+y.x11),v),a.arc(y.cx,y.cy,N,Math.atan2(y.y11,y.x11),Math.atan2(y.y01,y.x01),!v))):a.arc(0,0,f,b,x,v):a.lineTo(q,L)}else a.moveTo(0,0);if(a.closePath(),c)return a=null,c+""||null}return c.centroid=function(){var e=(+t.apply(this,arguments)+ +n.apply(this,arguments))/2,r=(+i.apply(this,arguments)+ +o.apply(this,arguments))/2-Dt/2;return[Math.cos(r)*e,Math.sin(r)*e]},c.innerRadius=function(n){return arguments.length?(t="function"==typeof n?n:Rt(+n),c):t},c.outerRadius=function(t){return arguments.length?(n="function"==typeof t?t:Rt(+t),c):n},c.cornerRadius=function(t){return arguments.length?(e="function"==typeof t?t:Rt(+t),c):e},c.padRadius=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:Rt(+t),c):r},c.startAngle=function(t){return arguments.length?(i="function"==typeof t?t:Rt(+t),c):i},c.endAngle=function(t){return arguments.length?(o="function"==typeof t?t:Rt(+t),c):o},c.padAngle=function(t){return arguments.length?(u="function"==typeof t?t:Rt(+t),c):u},c.context=function(t){return arguments.length?(a=null==t?null:t,c):a},c},t.area=Qt,t.line=Jt,t.pie=function(){var t=tn,n=Kt,e=null,r=Rt(0),i=Rt(Ft),o=Rt(0);function u(u){var a,c,s,f,l,h=u.length,p=0,d=new Array(h),v=new Array(h),_=+r.apply(this,arguments),y=Math.min(Ft,Math.max(-Ft,i.apply(this,arguments)-_)),g=Math.min(Math.abs(y)/h,o.apply(this,arguments)),m=g*(y<0?-1:1);for(a=0;a<h;++a)(l=v[d[a]=a]=+t(u[a],a,u))>0&&(p+=l);for(null!=n?d.sort(function(t,e){return n(v[t],v[e])}):null!=e&&d.sort(function(t,n){return e(u[t],u[n])}),a=0,s=p?(y-h*m)/p:0;a<h;++a,_=f)c=d[a],f=_+((l=v[c])>0?l*s:0)+m,v[c]={data:u[c],index:a,value:l,startAngle:_,endAngle:f,padAngle:g};return v}return u.value=function(n){return arguments.length?(t="function"==typeof n?n:Rt(+n),u):t},u.sortValues=function(t){return arguments.length?(n=t,e=null,u):n},u.sort=function(t){return arguments.length?(e=t,n=null,u):e},u.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:Rt(+t),u):r},u.endAngle=function(t){return arguments.length?(i="function"==typeof t?t:Rt(+t),u):i},u.padAngle=function(t){return arguments.length?(o="function"==typeof t?t:Rt(+t),u):o},u},t.radialArea=function(){var t=Qt().curve(nn),n=t.curve,e=t.lineX0,r=t.lineX1,i=t.lineY0,o=t.lineY1;return t.angle=t.x,delete t.x,t.startAngle=t.x0,delete t.x0,t.endAngle=t.x1,delete t.x1,t.radius=t.y,delete t.y,t.innerRadius=t.y0,delete t.y0,t.outerRadius=t.y1,delete t.y1,t.lineStartAngle=function(){return on(e())},delete t.lineX0,t.lineEndAngle=function(){return on(r())},delete t.lineX1,t.lineInnerRadius=function(){return on(i())},delete t.lineY0,t.lineOuterRadius=function(){return on(o())},delete t.lineY1,t.curve=function(t){return arguments.length?n(rn(t)):n()._curve},t},t.radialLine=function(){return on(Jt().curve(nn))},t.symbol=function(){var t=Rt(un),n=Rt(64),e=null;function r(){var r;if(e||(e=r=gt()),t.apply(this,arguments).draw(e,+n.apply(this,arguments)),r)return e=null,r+""||null}return r.type=function(n){return arguments.length?(t="function"==typeof n?n:Rt(n),r):t},r.size=function(t){return arguments.length?(n="function"==typeof t?t:Rt(+t),r):n},r.context=function(t){return arguments.length?(e=null==t?null:t,r):e},r},t.symbols=Mn,t.symbolCircle=un,t.symbolCross=an,t.symbolDiamond=fn,t.symbolSquare=vn,t.symbolStar=dn,t.symbolTriangle=yn,t.symbolWye=wn,t.curveBasisClosed=function(t){return new Nn(t)},t.curveBasisOpen=function(t){return new An(t)},t.curveBasis=function(t){return new kn(t)},t.curveBundle=Cn,t.curveCardinalClosed=Rn,t.curveCardinalOpen=Dn,t.curveCardinal=qn,t.curveCatmullRomClosed=Bn,t.curveCatmullRomOpen=Hn,t.curveCatmullRom=In,t.curveLinearClosed=function(t){return new Xn(t)},t.curveLinear=Wt,t.curveMonotoneX=function(t){return new Gn(t)},t.curveMonotoneY=function(t){return new Jn(t)},t.curveNatural=function(t){return new Kn(t)},t.curveStep=function(t){return new ne(t,.5)},t.curveStepAfter=function(t){return new ne(t,1)},t.curveStepBefore=function(t){return new ne(t,0)},t.stack=function(){var t=Rt([]),n=ie,e=re,r=oe;function i(i){var o,u,a=t.apply(this,arguments),c=i.length,s=a.length,f=new Array(s);for(o=0;o<s;++o){for(var l,h=a[o],p=f[o]=new Array(c),d=0;d<c;++d)p[d]=l=[0,+r(i[d],h,d,i)],l.data=i[d];p.key=h}for(o=0,u=n(f);o<s;++o)f[u[o]].index=o;return e(f,u),f}return i.keys=function(n){return arguments.length?(t="function"==typeof n?n:Rt(ee.call(n)),i):t},i.value=function(t){return arguments.length?(r="function"==typeof t?t:Rt(+t),i):r},i.order=function(t){return arguments.length?(n=null==t?ie:"function"==typeof t?t:Rt(ee.call(t)),i):n},i.offset=function(t){return arguments.length?(e=null==t?re:t,i):e},i},t.stackOffsetExpand=function(t,n){if((r=t.length)>0){for(var e,r,i,o=0,u=t[0].length;o<u;++o){for(i=e=0;e<r;++e)i+=t[e][o][1]||0;if(i)for(e=0;e<r;++e)t[e][o][1]/=i}re(t,n)}},t.stackOffsetNone=re,t.stackOffsetSilhouette=function(t,n){if((e=t.length)>0){for(var e,r=0,i=t[n[0]],o=i.length;r<o;++r){for(var u=0,a=0;u<e;++u)a+=t[u][r][1]||0;i[r][1]+=i[r][0]=-a/2}re(t,n)}},t.stackOffsetWiggle=function(t,n){if((i=t.length)>0&&(r=(e=t[n[0]]).length)>0){for(var e,r,i,o=0,u=1;u<r;++u){for(var a=0,c=0,s=0;a<i;++a){for(var f=t[n[a]],l=f[u][1]||0,h=(l-(f[u-1][1]||0))/2,p=0;p<a;++p){var d=t[n[p]];h+=(d[u][1]||0)-(d[u-1][1]||0)}c+=l,s+=h*l}e[u-1][1]+=e[u-1][0]=o,c&&(o-=s/c)}e[u-1][1]+=e[u-1][0]=o,re(t,n)}},t.stackOrderAscending=ue,t.stackOrderDescending=function(t){return ue(t).reverse()},t.stackOrderInsideOut=function(t){var n,e,r=t.length,i=t.map(ae),o=ie(t).sort(function(t,n){return i[n]-i[t]}),u=0,a=0,c=[],s=[];for(n=0;n<r;++n)e=o[n],u<a?(u+=i[e],c.push(e)):(a+=i[e],s.push(e));return s.reverse().concat(c)},t.stackOrderNone=ie,t.stackOrderReverse=function(t){return ie(t).reverse()},t.color=xe,t.rgb=Te,t.hsl=Ne,t.lab=Ie,t.hcl=Ve,t.cubehelix=er,t.interpolate=Mr,t.interpolateArray=_r,t.interpolateDate=yr,t.interpolateNumber=gr,t.interpolateObject=mr,t.interpolateRound=Tr,t.interpolateString=wr,t.interpolateTransformCss=qr,t.interpolateTransformSvg=Lr,t.interpolateZoom=Ir,t.interpolateRgb=hr,t.interpolateRgbBasis=dr,t.interpolateRgbBasisClosed=vr,t.interpolateHsl=Br,t.interpolateHslLong=jr,t.interpolateLab=function(t,n){var e=lr((t=Ie(t)).l,(n=Ie(n)).l),r=lr(t.a,n.a),i=lr(t.b,n.b),o=lr(t.opacity,n.opacity);return function(n){return t.l=e(n),t.a=r(n),t.b=i(n),t.opacity=o(n),t+""}},t.interpolateHcl=Xr,t.interpolateHclLong=Vr,t.interpolateCubehelix=Wr,t.interpolateCubehelixLong=Zr,t.interpolateBasis=or,t.interpolateBasisClosed=ur,t.quantize=function(t,n){for(var e=new Array(n),r=0;r<n;++r)e[r]=t(r/(n-1));return e},t.dispatch=Jr,t.dsvFormat=ei,t.csvParse=ii,t.csvParseRows=oi,t.csvFormat=ui,t.csvFormatRows=ai,t.tsvParse=si,t.tsvParseRows=fi,t.tsvFormat=li,t.tsvFormatRows=hi,t.request=pi,t.html=vi,t.json=_i,t.text=yi,t.xml=gi,t.csv=wi,t.tsv=Mi,t.now=qi,t.timer=Ui,t.timerFlush=Di,t.timeout=Yi,t.interval=function(t,n,e){var r=new Ri,i=n;return null==n?(r.restart(t,n,e),r):(n=+n,e=null==e?qi():+e,r.restart(function o(u){u+=i,r.restart(o,i+=n,e),t(u)},n,e),r)},t.timeInterval=Hi,t.timeMillisecond=Xi,t.timeMilliseconds=Vi,t.timeSecond=Zi,t.timeSeconds=Gi,t.timeMinute=Ji,t.timeMinutes=Qi,t.timeHour=Ki,t.timeHours=to,t.timeDay=no,t.timeDays=eo,t.timeWeek=io,t.timeWeeks=lo,t.timeSunday=io,t.timeSundays=lo,t.timeMonday=oo,t.timeMondays=ho,t.timeTuesday=uo,t.timeTuesdays=po,t.timeWednesday=ao,t.timeWednesdays=vo,t.timeThursday=co,t.timeThursdays=_o,t.timeFriday=so,t.timeFridays=yo,t.timeSaturday=fo,t.timeSaturdays=go,t.timeMonth=mo,t.timeMonths=xo,t.timeYear=bo,t.timeYears=wo,t.utcMillisecond=Xi,t.utcMilliseconds=Vi,t.utcSecond=Zi,t.utcSeconds=Gi,t.utcMinute=Mo,t.utcMinutes=To,t.utcHour=So,t.utcHours=ko,t.utcDay=No,t.utcDays=Ao,t.utcWeek=Co,t.utcWeeks=Do,t.utcSunday=Co,t.utcSundays=Do,t.utcMonday=zo,t.utcMondays=Oo,t.utcTuesday=Po,t.utcTuesdays=Fo,t.utcWednesday=qo,t.utcWednesdays=Io,t.utcThursday=Lo,t.utcThursdays=Yo,t.utcFriday=Ro,t.utcFridays=Bo,t.utcSaturday=Uo,t.utcSaturdays=jo,t.utcMonth=Ho,t.utcMonths=Xo,t.utcYear=Vo,t.utcYears=Wo,t.formatLocale=ou,t.formatDefaultLocale=uu,t.formatSpecifier=tu,t.precisionFixed=au,t.precisionPrefix=cu,t.precisionRound=su,t.isoFormat=pa,t.isoParse=da,t.timeFormatLocale=pu,t.timeFormatDefaultLocale=ha,t.scaleBand=xa,t.scalePoint=function(){return function t(n){var e=n.copy;return n.padding=n.paddingOuter,delete n.paddingInner,delete n.paddingOuter,n.copy=function(){return t(e())},n}(xa().paddingInner(1))},t.scaleIdentity=function t(){var n=[0,1];function e(t){return+t}return e.invert=e,e.domain=e.range=function(t){return arguments.length?(n=_a.call(t,wa),e):n.slice()},e.copy=function(){return t().domain(n)},Ea(e)},t.scaleLinear=function t(){var n=Aa(Ta,gr);return n.copy=function(){return Na(n,t())},Ea(n)},t.scaleLog=function n(){var e=Aa(za,Pa).domain([1,10]),r=e.domain,i=10,o=Ra(10),u=La(10);function a(){return o=Ra(i),u=La(i),r()[0]<0&&(o=Ua(o),u=Ua(u)),e}return e.base=function(t){return arguments.length?(i=+t,a()):i},e.domain=function(t){return arguments.length?(r(t),a()):r()},e.ticks=function(t){var n,e=r(),a=e[0],c=e[e.length-1];(n=c<a)&&(h=a,a=c,c=h);var s,f,l,h=o(a),p=o(c),d=null==t?10:+t,v=[];if(!(i%1)&&p-h<d){if(h=Math.round(h)-1,p=Math.round(p)+1,a>0){for(;h<p;++h)for(f=1,s=u(h);f<i;++f)if(!((l=s*f)<a)){if(l>c)break;v.push(l)}}else for(;h<p;++h)for(f=i-1,s=u(h);f>=1;--f)if(!((l=s*f)<a)){if(l>c)break;v.push(l)}}else v=m(h,p,Math.min(p-h,d)).map(u);return n?v.reverse():v},e.tickFormat=function(n,r){if(null==r&&(r=10===i?".0e":","),"function"!=typeof r&&(r=t.format(r)),n===1/0)return r;null==n&&(n=10);var a=Math.max(1,i*n/e.ticks().length);return function(t){var n=t/u(Math.round(o(t)));return n*i<i-.5&&(n*=i),n<=a?r(t):""}},e.nice=function(){return r(Ca(r(),{floor:function(t){return u(Math.floor(o(t)))},ceil:function(t){return u(Math.ceil(o(t)))}}))},e.copy=function(){return Na(e,n().base(i))},e},t.scaleOrdinal=ma,t.scaleImplicit=ga,t.scalePow=Oa,t.scaleSqrt=function(){return Oa().exponent(.5)},t.scaleQuantile=function t(){var e=[],r=[],o=[];function u(){var t=0,n=Math.max(1,r.length);for(o=new Array(n-1);++t<n;)o[t-1]=w(e,t/n);return a}function a(t){if(!isNaN(t=+t))return r[i(o,t)]}return a.invertExtent=function(t){var n=r.indexOf(t);return n<0?[NaN,NaN]:[n>0?o[n-1]:e[0],n<o.length?o[n]:e[e.length-1]]},a.domain=function(t){if(!arguments.length)return e.slice();e=[];for(var r,i=0,o=t.length;i<o;++i)null==(r=t[i])||isNaN(r=+r)||e.push(r);return e.sort(n),u()},a.range=function(t){return arguments.length?(r=ya.call(t),u()):r.slice()},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(e).range(r)},a},t.scaleQuantize=function t(){var n=0,e=1,r=1,o=[.5],u=[0,1];function a(t){if(t<=t)return u[i(o,t,0,r)]}function c(){var t=-1;for(o=new Array(r);++t<r;)o[t]=((t+1)*e-(t-r)*n)/(r+1);return a}return a.domain=function(t){return arguments.length?(n=+t[0],e=+t[1],c()):[n,e]},a.range=function(t){return arguments.length?(r=(u=ya.call(t)).length-1,c()):u.slice()},a.invertExtent=function(t){var i=u.indexOf(t);return i<0?[NaN,NaN]:i<1?[n,o[0]]:i>=r?[o[r-1],e]:[o[i-1],o[i]]},a.copy=function(){return t().domain([n,e]).range(u)},Ea(a)},t.scaleThreshold=function t(){var n=[.5],e=[0,1],r=1;function o(t){if(t<=t)return e[i(n,t,0,r)]}return o.domain=function(t){return arguments.length?(n=ya.call(t),r=Math.min(n.length,e.length-1),o):n.slice()},o.range=function(t){return arguments.length?(e=ya.call(t),r=Math.min(n.length,e.length-1),o):e.slice()},o.invertExtent=function(t){var r=e.indexOf(t);return[n[r-1],n[r]]},o.copy=function(){return t().domain(n).range(e)},o},t.scaleTime=function(){return Wa(bo,mo,io,no,Ki,Ji,Zi,Xi,t.timeFormat).domain([new Date(2e3,0,1),new Date(2e3,0,2)])},t.scaleUtc=function(){return Wa(Vo,Ho,Co,No,So,Mo,Zi,Xi,t.utcFormat).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)])},t.schemeCategory10=Ga,t.schemeCategory20b=Ja,t.schemeCategory20c=Qa,t.schemeCategory20=Ka,t.scaleSequential=function t(n){var e=0,r=1,i=!1;function o(t){var o=(t-e)/(r-e);return n(i?Math.max(0,Math.min(1,o)):o)}return o.domain=function(t){return arguments.length?(e=+t[0],r=+t[1],o):[e,r]},o.clamp=function(t){return arguments.length?(i=!!t,o):i},o.interpolator=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return t(n).domain([e,r]).clamp(i)},Ea(o)},t.interpolateCubehelixDefault=tc,t.interpolateRainbow=function(t){(t<0||t>1)&&(t-=Math.floor(t));var n=Math.abs(t-.5);return rc.h=360*t-100,rc.s=1.5-1.5*n,rc.l=.8-.9*n,rc+""},t.interpolateWarm=nc,t.interpolateCool=ec,t.interpolateViridis=oc,t.interpolateMagma=uc,t.interpolateInferno=ac,t.interpolatePlasma=cc,t.creator=hc,t.customEvent=Sc,t.local=dc,t.matcher=mc,t.mouse=Ac,t.namespace=lc,t.namespaces=fc,t.select=ns,t.selectAll=function(t){return"string"==typeof t?new Kc([document.querySelectorAll(t)],[document.documentElement]):new Kc([null==t?[]:t],Qc)},t.selection=ts,t.selector=Cc,t.selectorAll=Pc,t.touch=es,t.touches=function(t,n){null==n&&(n=kc().touches);for(var e=0,r=n?n.length:0,i=new Array(r);e<r;++e)i[e]=Nc(t,n[e]);return i},t.window=Fc,t.active=function(t,n){var e,r,i=t.__transition;if(i)for(r in n=null==n?null:n+"",i)if((e=i[r]).state>us&&e.name===n)return new xs([[t]],ks,n,+r);return null},t.interrupt=vs,t.transition=bs,t.axisTop=function(t){return Ds(Es,t)},t.axisRight=function(t){return Ds(Cs,t)},t.axisBottom=function(t){return Ds(zs,t)},t.axisLeft=function(t){return Ds(Ps,t)},t.cluster=function(){var t=Os,n=1,e=1,r=!1;function i(i){var o,u=0;i.eachAfter(function(n){var e=n.children;e?(n.x=function(t){return t.reduce(Fs,0)/t.length}(e),n.y=function(t){return 1+t.reduce(Is,0)}(e)):(n.x=o?u+=t(n,o):0,n.y=0,o=n)});var a=function(t){for(var n;n=t.children;)t=n[0];return t}(i),c=function(t){for(var n;n=t.children;)t=n[n.length-1];return t}(i),s=a.x-t(a,c)/2,f=c.x+t(c,a)/2;return i.eachAfter(r?function(t){t.x=(t.x-i.x)*n,t.y=(i.y-t.y)*e}:function(t){t.x=(t.x-s)/(f-s)*n,t.y=(1-(i.y?t.y/i.y:1))*e})}return i.separation=function(n){return arguments.length?(t=n,i):t},i.size=function(t){return arguments.length?(r=!1,n=+t[0],e=+t[1],i):r?null:[n,e]},i.nodeSize=function(t){return arguments.length?(r=!0,n=+t[0],e=+t[1],i):r?[n,e]:null},i},t.hierarchy=Ys,t.pack=function(){var t=null,n=1,e=1,r=nf;function i(i){return i.x=n/2,i.y=e/2,t?i.eachBefore(of(t)).eachAfter(uf(r,.5)).eachBefore(af(1)):i.eachBefore(of(rf)).eachAfter(uf(nf,1)).eachAfter(uf(r,i.r/Math.min(n,e))).eachBefore(af(Math.min(n,e)/(2*i.r))),i}return i.radius=function(n){return arguments.length?(t=null==(e=n)?null:tf(e),i):t;var e},i.size=function(t){return arguments.length?(n=+t[0],e=+t[1],i):[n,e]},i.padding=function(t){return arguments.length?(r="function"==typeof t?t:ef(+t),i):r},i},t.packSiblings=function(t){return Ks(t),t},t.packEnclose=$s,t.partition=function(){var t=1,n=1,e=0,r=!1;function i(i){var o=i.height+1;return i.x0=i.y0=e,i.x1=t,i.y1=n/o,i.eachBefore(function(t,n){return function(r){r.children&&sf(r,r.x0,t*(r.depth+1)/n,r.x1,t*(r.depth+2)/n);var i=r.x0,o=r.y0,u=r.x1-e,a=r.y1-e;u<i&&(i=u=(i+u)/2),a<o&&(o=a=(o+a)/2),r.x0=i,r.y0=o,r.x1=u,r.y1=a}}(n,o)),r&&i.eachBefore(cf),i}return i.round=function(t){return arguments.length?(r=!!t,i):r},i.size=function(e){return arguments.length?(t=+e[0],n=+e[1],i):[t,n]},i.padding=function(t){return arguments.length?(e=+t,i):e},i},t.stratify=function(){var t=pf,n=df;function e(e){var r,i,o,u,a,c,s,f=e.length,l=new Array(f),h={};for(i=0;i<f;++i)r=e[i],a=l[i]=new Xs(r),null!=(c=t(r,i,e))&&(c+="")&&(h[s=ff+(a.id=c)]=s in h?hf:a);for(i=0;i<f;++i)if(a=l[i],null!=(c=n(e[i],i,e))&&(c+="")){if(!(u=h[ff+c]))throw new Error("missing: "+c);if(u===hf)throw new Error("ambiguous: "+c);u.children?u.children.push(a):u.children=[a],a.parent=u}else{if(o)throw new Error("multiple roots");o=a}if(!o)throw new Error("no root");if(o.parent=lf,o.eachBefore(function(t){t.depth=t.parent.depth+1,--f}).eachBefore(Hs),o.parent=null,f>0)throw new Error("cycle");return o}return e.id=function(n){return arguments.length?(t=tf(n),e):t},e.parentId=function(t){return arguments.length?(n=tf(t),e):n},e},t.tree=function(){var t=vf,n=1,e=1,r=null;function i(i){var c=function(t){for(var n,e,r,i,o,u=new xf(t,0),a=[u];n=a.pop();)if(r=n._.children)for(n.children=new Array(o=r.length),i=o-1;i>=0;--i)a.push(e=n.children[i]=new xf(r[i],i)),e.parent=n;return(u.parent=new xf(null,0)).children=[u],u}(i);if(c.eachAfter(o),c.parent.m=-c.z,c.eachBefore(u),r)i.eachBefore(a);else{var s=i,f=i,l=i;i.eachBefore(function(t){t.x<s.x&&(s=t),t.x>f.x&&(f=t),t.depth>l.depth&&(l=t)});var h=s===f?1:t(s,f)/2,p=h-s.x,d=n/(f.x+h+p),v=e/(l.depth||1);i.eachBefore(function(t){t.x=(t.x+p)*d,t.y=t.depth*v})}return i}function o(n){var e=n.children,r=n.parent.children,i=n.i?r[n.i-1]:null;if(e){!function(t){for(var n,e=0,r=0,i=t.children,o=i.length;--o>=0;)(n=i[o]).z+=e,n.m+=e,e+=n.s+(r+=n.c)}(n);var o=(e[0].z+e[e.length-1].z)/2;i?(n.z=i.z+t(n._,i._),n.m=n.z-o):n.z=o}else i&&(n.z=i.z+t(n._,i._));n.parent.A=function(n,e,r){if(e){for(var i,o=n,u=n,a=e,c=o.parent.children[0],s=o.m,f=u.m,l=a.m,h=c.m;a=yf(a),o=_f(o),a&&o;)c=_f(c),(u=yf(u)).a=n,(i=a.z+l-o.z-s+t(a._,o._))>0&&(gf(mf(a,n,r),n,i),s+=i,f+=i),l+=a.m,s+=o.m,h+=c.m,f+=u.m;a&&!yf(u)&&(u.t=a,u.m+=l-f),o&&!_f(c)&&(c.t=o,c.m+=s-h,r=n)}return r}(n,i,n.parent.A||r[0])}function u(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function a(t){t.x*=n,t.y=t.depth*e}return i.separation=function(n){return arguments.length?(t=n,i):t},i.size=function(t){return arguments.length?(r=!1,n=+t[0],e=+t[1],i):r?null:[n,e]},i.nodeSize=function(t){return arguments.length?(r=!0,n=+t[0],e=+t[1],i):r?[n,e]:null},i},t.treemap=function(){var t=Tf,n=!1,e=1,r=1,i=[0],o=nf,u=nf,a=nf,c=nf,s=nf;function f(t){return t.x0=t.y0=0,t.x1=e,t.y1=r,t.eachBefore(l),i=[0],n&&t.eachBefore(cf),t}function l(n){var e=i[n.depth],r=n.x0+e,f=n.y0+e,l=n.x1-e,h=n.y1-e;l<r&&(r=l=(r+l)/2),h<f&&(f=h=(f+h)/2),n.x0=r,n.y0=f,n.x1=l,n.y1=h,n.children&&(e=i[n.depth+1]=o(n)/2,r+=s(n)-e,f+=u(n)-e,(l-=a(n)-e)<r&&(r=l=(r+l)/2),(h-=c(n)-e)<f&&(f=h=(f+h)/2),t(n,r,f,l,h))}return f.round=function(t){return arguments.length?(n=!!t,f):n},f.size=function(t){return arguments.length?(e=+t[0],r=+t[1],f):[e,r]},f.tile=function(n){return arguments.length?(t=tf(n),f):t},f.padding=function(t){return arguments.length?f.paddingInner(t).paddingOuter(t):f.paddingInner()},f.paddingInner=function(t){return arguments.length?(o="function"==typeof t?t:ef(+t),f):o},f.paddingOuter=function(t){return arguments.length?f.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):f.paddingTop()},f.paddingTop=function(t){return arguments.length?(u="function"==typeof t?t:ef(+t),f):u},f.paddingRight=function(t){return arguments.length?(a="function"==typeof t?t:ef(+t),f):a},f.paddingBottom=function(t){return arguments.length?(c="function"==typeof t?t:ef(+t),f):c},f.paddingLeft=function(t){return arguments.length?(s="function"==typeof t?t:ef(+t),f):s},f},t.treemapBinary=function(t,n,e,r,i){var o,u,a=t.children,c=a.length,s=new Array(c+1);for(s[0]=u=o=0;o<c;++o)s[o+1]=u+=a[o].value;!function t(n,e,r,i,o,u,c){if(n>=e-1){var f=a[n];return f.x0=i,f.y0=o,f.x1=u,void(f.y1=c)}for(var l=s[n],h=r/2+l,p=n+1,d=e-1;p<d;){var v=p+d>>>1;s[v]<h?p=v+1:d=v}var _=s[p]-l,y=r-_;if(c-o>u-i){var g=(o*y+c*_)/r;t(n,p,_,i,o,u,g),t(p,e,y,i,g,u,c)}else{var m=(i*y+u*_)/r;t(n,p,_,i,o,m,c),t(p,e,y,m,o,u,c)}}(0,c,t.value,n,e,r,i)},t.treemapDice=sf,t.treemapSlice=bf,t.treemapSliceDice=function(t,n,e,r,i){(1&t.depth?bf:sf)(t,n,e,r,i)},t.treemapSquarify=Tf,t.treemapResquarify=Sf,t.forceCenter=function(t,n){var e;function r(){var r,i,o=e.length,u=0,a=0;for(r=0;r<o;++r)u+=(i=e[r]).x,a+=i.y;for(u=u/o-t,a=a/o-n,r=0;r<o;++r)(i=e[r]).x-=u,i.y-=a}return null==t&&(t=0),null==n&&(n=0),r.initialize=function(t){e=t},r.x=function(n){return arguments.length?(t=+n,r):t},r.y=function(t){return arguments.length?(n=+t,r):n},r},t.forceCollide=function(t){var n,e,r=1,i=1;function o(){for(var t,o,a,c,s,f,l,h=n.length,p=0;p<i;++p)for(o=Mt(n,Af,Ef).visitAfter(u),t=0;t<h;++t)a=n[t],f=e[t],l=f*f,c=a.x+a.vx,s=a.y+a.vy,o.visit(d);function d(n,e,i,o,u){var h=n.data,p=n.r,d=f+p;if(!h)return e>c+d||o<c-d||i>s+d||u<s-d;if(h.index>t){var v=c-h.x-h.vx,_=s-h.y-h.vy,y=v*v+_*_;y<d*d&&(0===v&&(y+=(v=Nf())*v),0===_&&(y+=(_=Nf())*_),y=(d-(y=Math.sqrt(y)))/y*r,a.vx+=(v*=y)*(d=(p*=p)/(l+p)),a.vy+=(_*=y)*d,h.vx-=v*(d=1-d),h.vy-=_*d)}}}function u(t){if(t.data)return t.r=e[t.data.index];for(var n=t.r=0;n<4;++n)t[n]&&t[n].r>t.r&&(t.r=t[n].r)}return"function"!=typeof t&&(t=kf(null==t?1:+t)),o.initialize=function(r){var i,o=(n=r).length;for(e=new Array(o),i=0;i<o;++i)e[i]=+t(n[i],i,n)},o.iterations=function(t){return arguments.length?(i=+t,o):i},o.strength=function(t){return arguments.length?(r=+t,o):r},o.radius=function(n){return arguments.length?(t="function"==typeof n?n:kf(+n),o):t},o},t.forceLink=function(t){var n,e,r,i,o,u=Cf,a=function(t){return 1/Math.min(i[t.source.index],i[t.target.index])},c=kf(30),s=1;function f(r){for(var i=0,u=t.length;i<s;++i)for(var a,c,f,l,h,p,d,v=0;v<u;++v)c=(a=t[v]).source,l=(f=a.target).x+f.vx-c.x-c.vx||Nf(),h=f.y+f.vy-c.y-c.vy||Nf(),l*=p=((p=Math.sqrt(l*l+h*h))-e[v])/p*r*n[v],h*=p,f.vx-=l*(d=o[v]),f.vy-=h*d,c.vx+=l*(d=1-d),c.vy+=h*d}function l(){if(r){var a,c,s=r.length,f=t.length,l=A(r,u);for(a=0,i=new Array(s);a<s;++a)i[a]=0;for(a=0;a<f;++a)(c=t[a]).index=a,"object"!=typeof c.source&&(c.source=l.get(c.source)),"object"!=typeof c.target&&(c.target=l.get(c.target)),++i[c.source.index],++i[c.target.index];for(a=0,o=new Array(f);a<f;++a)c=t[a],o[a]=i[c.source.index]/(i[c.source.index]+i[c.target.index]);n=new Array(f),h(),e=new Array(f),p()}}function h(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+a(t[e],e,t)}function p(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+c(t[n],n,t)}return null==t&&(t=[]),f.initialize=function(t){r=t,l()},f.links=function(n){return arguments.length?(t=n,l(),f):t},f.id=function(t){return arguments.length?(u=t,f):u},f.iterations=function(t){return arguments.length?(s=+t,f):s},f.strength=function(t){return arguments.length?(a="function"==typeof t?t:kf(+t),h(),f):a},f.distance=function(t){return arguments.length?(c="function"==typeof t?t:kf(+t),p(),f):c},f},t.forceManyBody=function(){var t,n,e,r,i=kf(-30),o=1,u=1/0,a=.81;function c(r){var i,o=t.length,u=Mt(t,zf,Pf).visitAfter(f);for(e=r,i=0;i<o;++i)n=t[i],u.visit(l)}function s(){if(t){var n,e=t.length;for(r=new Array(e),n=0;n<e;++n)r[n]=+i(t[n],n,t)}}function f(t){var n,e,i,o,u,a=0;if(t.length){for(i=o=u=0;u<4;++u)(n=t[u])&&(e=n.value)&&(a+=e,i+=e*n.x,o+=e*n.y);t.x=i/a,t.y=o/a}else{(n=t).x=n.data.x,n.y=n.data.y;do{a+=r[n.data.index]}while(n=n.next)}t.value=a}function l(t,i,c,s){if(!t.value)return!0;var f=t.x-n.x,l=t.y-n.y,h=s-i,p=f*f+l*l;if(h*h/a<p)return p<u&&(0===f&&(p+=(f=Nf())*f),0===l&&(p+=(l=Nf())*l),p<o&&(p=Math.sqrt(o*p)),n.vx+=f*t.value*e/p,n.vy+=l*t.value*e/p),!0;if(!(t.length||p>=u)){(t.data!==n||t.next)&&(0===f&&(p+=(f=Nf())*f),0===l&&(p+=(l=Nf())*l),p<o&&(p=Math.sqrt(o*p)));do{t.data!==n&&(h=r[t.data.index]*e/p,n.vx+=f*h,n.vy+=l*h)}while(t=t.next)}}return c.initialize=function(n){t=n,s()},c.strength=function(t){return arguments.length?(i="function"==typeof t?t:kf(+t),s(),c):i},c.distanceMin=function(t){return arguments.length?(o=t*t,c):Math.sqrt(o)},c.distanceMax=function(t){return arguments.length?(u=t*t,c):Math.sqrt(u)},c.theta=function(t){return arguments.length?(a=t*t,c):Math.sqrt(a)},c},t.forceSimulation=function(t){var n,e=1,r=.001,i=1-Math.pow(r,1/300),o=0,u=.6,a=A(),c=Ui(f),s=Jr("tick","end");function f(){l(),s.call("tick",n),e<r&&(c.stop(),s.call("end",n))}function l(){var n,r,c=t.length;for(e+=(o-e)*i,a.each(function(t){t(e)}),n=0;n<c;++n)null==(r=t[n]).fx?r.x+=r.vx*=u:(r.x=r.fx,r.vx=0),null==r.fy?r.y+=r.vy*=u:(r.y=r.fy,r.vy=0)}function h(){for(var n,e=0,r=t.length;e<r;++e){if((n=t[e]).index=e,isNaN(n.x)||isNaN(n.y)){var i=qf*Math.sqrt(e),o=e*Lf;n.x=i*Math.cos(o),n.y=i*Math.sin(o)}(isNaN(n.vx)||isNaN(n.vy))&&(n.vx=n.vy=0)}}function p(n){return n.initialize&&n.initialize(t),n}return null==t&&(t=[]),h(),n={tick:l,restart:function(){return c.restart(f),n},stop:function(){return c.stop(),n},nodes:function(e){return arguments.length?(t=e,h(),a.each(p),n):t},alpha:function(t){return arguments.length?(e=+t,n):e},alphaMin:function(t){return arguments.length?(r=+t,n):r},alphaDecay:function(t){return arguments.length?(i=+t,n):+i},alphaTarget:function(t){return arguments.length?(o=+t,n):o},velocityDecay:function(t){return arguments.length?(u=1-t,n):1-u},force:function(t,e){return arguments.length>1?(null==e?a.remove(t):a.set(t,p(e)),n):a.get(t)},find:function(n,e,r){var i,o,u,a,c,s=0,f=t.length;for(null==r?r=1/0:r*=r,s=0;s<f;++s)(u=(i=n-(a=t[s]).x)*i+(o=e-a.y)*o)<r&&(c=a,r=u);return c},on:function(t,e){return arguments.length>1?(s.on(t,e),n):s.on(t)}}},t.forceX=function(t){var n,e,r,i=kf(.1);function o(t){for(var i,o=0,u=n.length;o<u;++o)(i=n[o]).vx+=(r[o]-i.x)*e[o]*t}function u(){if(n){var o,u=n.length;for(e=new Array(u),r=new Array(u),o=0;o<u;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=kf(null==t?0:+t)),o.initialize=function(t){n=t,u()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:kf(+t),u(),o):i},o.x=function(n){return arguments.length?(t="function"==typeof n?n:kf(+n),u(),o):t},o},t.forceY=function(t){var n,e,r,i=kf(.1);function o(t){for(var i,o=0,u=n.length;o<u;++o)(i=n[o]).vy+=(r[o]-i.y)*e[o]*t}function u(){if(n){var o,u=n.length;for(e=new Array(u),r=new Array(u),o=0;o<u;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=kf(null==t?0:+t)),o.initialize=function(t){n=t,u()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:kf(+t),u(),o):i},o.y=function(n){return arguments.length?(t="function"==typeof n?n:kf(+n),u(),o):t},o},t.drag=function(){var n,e,r=Yf,i=Bf,o=jf,u={},a=Jr("start","drag","end"),c=0;function s(t){t.on("mousedown.drag",f).on("touchstart.drag",p).on("touchmove.drag",d).on("touchend.drag touchcancel.drag",v).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function f(){if(!e&&r.apply(this,arguments)){var o=_("mouse",i.apply(this,arguments),Ac,this,arguments);o&&(ns(t.event.view).on("mousemove.drag",l,!0).on("mouseup.drag",h,!0),Df(t.event.view),Rf(),n=!1,o("start"))}}function l(){Uf(),n=!0,u.mouse("drag")}function h(){ns(t.event.view).on("mousemove.drag mouseup.drag",null),Of(t.event.view,n),Uf(),u.mouse("end")}function p(){if(r.apply(this,arguments)){var n,e,o=t.event.changedTouches,u=i.apply(this,arguments),a=o.length;for(n=0;n<a;++n)(e=_(o[n].identifier,u,es,this,arguments))&&(Rf(),e("start"))}}function d(){var n,e,r=t.event.changedTouches,i=r.length;for(n=0;n<i;++n)(e=u[r[n].identifier])&&(Uf(),e("drag"))}function v(){var n,r,i=t.event.changedTouches,o=i.length;for(e&&clearTimeout(e),e=setTimeout(function(){e=null},500),n=0;n<o;++n)(r=u[i[n].identifier])&&(Rf(),r("end"))}function _(n,e,r,i,f){var l,h,p,d=r(e,n),v=a.copy();if(Sc(new If(s,"beforestart",l,n,c,d[0],d[1],0,0,v),function(){return null!=(t.event.subject=l=o.apply(i,f))&&(h=l.x-d[0]||0,p=l.y-d[1]||0,!0)}))return function t(o){var a,_=d;switch(o){case"start":u[n]=t,a=c++;break;case"end":delete u[n],--c;case"drag":d=r(e,n),a=c}Sc(new If(s,o,l,n,a,d[0]+h,d[1]+p,d[0]-_[0],d[1]-_[1],v),v.apply,v,[o,i,f])}}return s.filter=function(t){return arguments.length?(r="function"==typeof t?t:Ff(!!t),s):r},s.container=function(t){return arguments.length?(i="function"==typeof t?t:Ff(t),s):i},s.subject=function(t){return arguments.length?(o="function"==typeof t?t:Ff(t),s):o},s.on=function(){var t=a.on.apply(a,arguments);return t===a?s:t},s},t.dragDisable=Df,t.dragEnable=Of,t.voronoi=function(){var t=Xf,n=Vf,e=null;function r(r){return new kl(r.map(function(e,i){var o=[Math.round(t(e,i,r)/Ml)*Ml,Math.round(n(e,i,r)/Ml)*Ml];return o.index=i,o.data=e,o}),e)}return r.polygons=function(t){return r(t).polygons()},r.links=function(t){return r(t).links()},r.triangles=function(t){return r(t).triangles()},r.x=function(n){return arguments.length?(t="function"==typeof n?n:Hf(+n),r):t},r.y=function(t){return arguments.length?(n="function"==typeof t?t:Hf(+t),r):n},r.extent=function(t){return arguments.length?(e=null==t?null:[[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]],r):e&&[[e[0][0],e[0][1]],[e[1][0],e[1][1]]]},r.size=function(t){return arguments.length?(e=null==t?null:[[0,0],[+t[0],+t[1]]],r):e&&[e[1][0]-e[0][0],e[1][1]-e[0][1]]},r},t.zoom=function(){var n,e,r=Ll,i=Rl,o=0,u=1/0,a=-u,c=u,s=a,f=c,l=250,h=[],p=Jr("start","zoom","end"),d=500,v=150;function _(t){t.on("wheel.zoom",T).on("mousedown.zoom",S).on("dblclick.zoom",k).on("touchstart.zoom",N).on("touchmove.zoom",A).on("touchend.zoom touchcancel.zoom",E).style("-webkit-tap-highlight-color","rgba(0,0,0,0)").property("__zoom",Ul)}function y(t,n){return(n=Math.max(o,Math.min(u,n)))===t.k?t:new El(n,t.x,t.y)}function g(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new El(t.k,r,i)}function m(t,n){var e=Math.min(0,t.invertX(n[0][0])-a)||Math.max(0,t.invertX(n[1][0])-c),r=Math.min(0,t.invertY(n[0][1])-s)||Math.max(0,t.invertY(n[1][1])-f);return e||r?t.translate(e,r):t}function x(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function b(t,n,e){t.on("start.zoom",function(){w(this,arguments).start()}).on("interrupt.zoom end.zoom",function(){w(this,arguments).end()}).tween("zoom",function(){var t=arguments,r=w(this,t),o=i.apply(this,t),u=e||x(o),a=Math.max(o[1][0]-o[0][0],o[1][1]-o[0][1]),c=this.__zoom,s="function"==typeof n?n.apply(this,t):n,f=Ir(c.invert(u).concat(a/c.k),s.invert(u).concat(a/s.k));return function(t){if(1===t)t=s;else{var n=f(t),e=a/n[2];t=new El(e,u[0]-n[0]*e,u[1]-n[1]*e)}r.zoom(null,t)}})}function w(t,n){for(var e,r=0,i=h.length;r<i;++r)if((e=h[r]).that===t)return e;return new M(t,n)}function M(t,n){this.that=t,this.args=n,this.index=-1,this.active=0,this.extent=i.apply(t,n)}function T(){if(r.apply(this,arguments)){var n=w(this,arguments),e=this.__zoom,i=Math.max(o,Math.min(u,e.k*Math.pow(2,-t.event.deltaY*(t.event.deltaMode?120:1)/500))),a=Ac(this);if(n.wheel)n.mouse[0][0]===a[0]&&n.mouse[0][1]===a[1]||(n.mouse[1]=e.invert(n.mouse[0]=a)),clearTimeout(n.wheel);else{if(e.k===i)return;n.mouse=[a,e.invert(a)],vs(this),n.start()}ql(),n.wheel=setTimeout(function(){n.wheel=null,n.end()},v),n.zoom("mouse",m(g(y(e,i),n.mouse[0],n.mouse[1]),n.extent))}}function S(){if(!e&&r.apply(this,arguments)){var n=w(this,arguments),i=ns(t.event.view).on("mousemove.zoom",function(){ql(),n.moved=!0,n.zoom("mouse",m(g(n.that.__zoom,n.mouse[0]=Ac(n.that),n.mouse[1]),n.extent))},!0).on("mouseup.zoom",function(){i.on("mousemove.zoom mouseup.zoom",null),Of(t.event.view,n.moved),ql(),n.end()},!0),o=Ac(this);Df(t.event.view),Pl(),n.mouse=[o,this.__zoom.invert(o)],vs(this),n.start()}}function k(){if(r.apply(this,arguments)){var n=this.__zoom,e=Ac(this),o=n.invert(e),u=m(g(y(n,n.k*(t.event.shiftKey?.5:2)),e,o),i.apply(this,arguments));ql(),l>0?ns(this).transition().duration(l).call(b,u,e):ns(this).call(_.transform,u)}}function N(){if(r.apply(this,arguments)){var e,i,o,u=w(this,arguments),a=t.event.changedTouches,c=a.length;for(Pl(),e=0;e<c;++e)o=[o=es(this,a,(i=a[e]).identifier),this.__zoom.invert(o),i.identifier],u.touch0?u.touch1||(u.touch1=o):u.touch0=o;if(n&&(n=clearTimeout(n),!u.touch1))return u.end(),k.apply(this,arguments);t.event.touches.length===c&&(n=setTimeout(function(){n=null},d),vs(this),u.start())}}function A(){var e,r,i,o,u=w(this,arguments),a=t.event.changedTouches,c=a.length;for(ql(),n&&(n=clearTimeout(n)),e=0;e<c;++e)i=es(this,a,(r=a[e]).identifier),u.touch0&&u.touch0[2]===r.identifier?u.touch0[0]=i:u.touch1&&u.touch1[2]===r.identifier&&(u.touch1[0]=i);if(r=u.that.__zoom,u.touch1){var s=u.touch0[0],f=u.touch0[1],l=u.touch1[0],h=u.touch1[1],p=(p=l[0]-s[0])*p+(p=l[1]-s[1])*p,d=(d=h[0]-f[0])*d+(d=h[1]-f[1])*d;r=y(r,Math.sqrt(p/d)),i=[(s[0]+l[0])/2,(s[1]+l[1])/2],o=[(f[0]+h[0])/2,(f[1]+h[1])/2]}else{if(!u.touch0)return;i=u.touch0[0],o=u.touch0[1]}u.zoom("touch",m(g(r,i,o),u.extent))}function E(){var n,r,i=w(this,arguments),o=t.event.changedTouches,u=o.length;for(Pl(),e&&clearTimeout(e),e=setTimeout(function(){e=null},d),n=0;n<u;++n)r=o[n],i.touch0&&i.touch0[2]===r.identifier?delete i.touch0:i.touch1&&i.touch1[2]===r.identifier&&delete i.touch1;i.touch1&&!i.touch0&&(i.touch0=i.touch1,delete i.touch1),i.touch0||i.end()}return _.transform=function(t,n){var e=t.selection?t.selection():t;e.property("__zoom",Ul),t!==e?b(t,n):e.interrupt().each(function(){w(this,arguments).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},_.scaleBy=function(t,n){_.scaleTo(t,function(){return this.__zoom.k*("function"==typeof n?n.apply(this,arguments):n)})},_.scaleTo=function(t,n){_.transform(t,function(){var t=i.apply(this,arguments),e=this.__zoom,r=x(t),o=e.invert(r);return m(g(y(e,"function"==typeof n?n.apply(this,arguments):n),r,o),t)})},_.translateBy=function(t,n,e){_.transform(t,function(){return m(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),i.apply(this,arguments))})},M.prototype={start:function(){return 1==++this.active&&(this.index=h.push(this)-1,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(h.splice(this.index,1),this.index=-1,this.emit("end")),this},emit:function(t){Sc(new Al(_,t,this.that.__zoom),p.apply,p,[t,this.that,this.args])}},_.filter=function(t){return arguments.length?(r="function"==typeof t?t:Nl(!!t),_):r},_.extent=function(t){return arguments.length?(i="function"==typeof t?t:Nl([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),_):i},_.scaleExtent=function(t){return arguments.length?(o=+t[0],u=+t[1],_):[o,u]},_.translateExtent=function(t){return arguments.length?(a=+t[0][0],c=+t[1][0],s=+t[0][1],f=+t[1][1],_):[[a,s],[c,f]]},_.duration=function(t){return arguments.length?(l=+t,_):l},_.on=function(){var t=p.on.apply(p,arguments);return t===p?_:t},_},t.zoomIdentity=Cl,t.zoomTransform=zl,t.brush=function(){return ih($l)},t.brushX=function(){return ih(Xl)},t.brushY=function(){return ih(Vl)},t.brushSelection=function(t){var n=t.__brush;return n?n.dim.output(n.selection):null},t.chord=function(){var t=0,n=null,e=null,r=null;function i(i){var o,u,a,c,s,f,l=i.length,h=[],p=v(l),d=[],_=[],y=_.groups=new Array(l),g=new Array(l*l);for(o=0,s=-1;++s<l;){for(u=0,f=-1;++f<l;)u+=i[s][f];h.push(u),d.push(v(l)),o+=u}for(n&&p.sort(function(t,e){return n(h[t],h[e])}),e&&d.forEach(function(t,n){t.sort(function(t,r){return e(i[n][t],i[n][r])})}),c=(o=fh(0,sh-t*l)/o)?t:sh/l,u=0,s=-1;++s<l;){for(a=u,f=-1;++f<l;){var m=p[s],x=d[m][f],b=i[m][x],w=u,M=u+=b*o;g[x*l+m]={index:m,subindex:x,startAngle:w,endAngle:M,value:b}}y[m]={index:m,startAngle:a,endAngle:u,value:h[m]},u+=c}for(s=-1;++s<l;)for(f=s-1;++f<l;){var T=g[f*l+s],S=g[s*l+f];(T.value||S.value)&&_.push(T.value<S.value?{source:S,target:T}:{source:T,target:S})}return r?_.sort(r):_}return i.padAngle=function(n){return arguments.length?(t=fh(0,n),i):t},i.sortGroups=function(t){return arguments.length?(n=t,i):n},i.sortSubgroups=function(t){return arguments.length?(e=t,i):e},i.sortChords=function(t){return arguments.length?(null==t?r=null:(n=t,r=function(t,e){return n(t.source.value+t.target.value,e.source.value+e.target.value)})._=t,i):r&&r._;var n},i},t.ribbon=function(){var t=ph,n=dh,e=vh,r=_h,i=yh,o=null;function u(){var u,a=lh.call(arguments),c=t.apply(this,a),s=n.apply(this,a),f=+e.apply(this,(a[0]=c,a)),l=r.apply(this,a)-ch,h=i.apply(this,a)-ch,p=f*oh(l),d=f*uh(l),v=+e.apply(this,(a[0]=s,a)),_=r.apply(this,a)-ch,y=i.apply(this,a)-ch;if(o||(o=u=gt()),o.moveTo(p,d),o.arc(0,0,f,l,h),l===_&&h===y||(o.quadraticCurveTo(0,0,v*oh(_),v*uh(_)),o.arc(0,0,v,_,y)),o.quadraticCurveTo(0,0,p,d),o.closePath(),u)return o=null,u+""||null}return u.radius=function(t){return arguments.length?(e="function"==typeof t?t:hh(+t),u):e},u.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:hh(+t),u):r},u.endAngle=function(t){return arguments.length?(i="function"==typeof t?t:hh(+t),u):i},u.source=function(n){return arguments.length?(t=n,u):t},u.target=function(t){return arguments.length?(n=t,u):n},u.context=function(t){return arguments.length?(o=null==t?null:t,u):o},u},t.geoAlbers=Gv,t.geoAlbersUsa=function(){var t,n,e,r,i,o,u=Gv(),a=Zv().rotate([154,0]).center([-2,58.5]).parallels([55,65]),c=Zv().rotate([157,0]).center([-3,19.9]).parallels([8,18]),s={point:function(t,n){o=[t,n]}};function f(t){var n=t[0],u=t[1];return o=null,e.point(n,u),o||(r.point(n,u),o)||(i.point(n,u),o)}return f.invert=function(t){var n=u.scale(),e=u.translate(),r=(t[0]-e[0])/n,i=(t[1]-e[1])/n;return(i>=.12&&i<.234&&r>=-.425&&r<-.214?a:i>=.166&&i<.234&&r>=-.214&&r<-.115?c:u).invert(t)},f.stream=function(e){return t&&n===e?t:(r=[u.stream(n=e),a.stream(e),c.stream(e)],i=r.length,t={point:function(t,n){for(var e=-1;++e<i;)r[e].point(t,n)},sphere:function(){for(var t=-1;++t<i;)r[t].sphere()},lineStart:function(){for(var t=-1;++t<i;)r[t].lineStart()},lineEnd:function(){for(var t=-1;++t<i;)r[t].lineEnd()},polygonStart:function(){for(var t=-1;++t<i;)r[t].polygonStart()},polygonEnd:function(){for(var t=-1;++t<i;)r[t].polygonEnd()}});var r,i},f.precision=function(t){return arguments.length?(u.precision(t),a.precision(t),c.precision(t),f):u.precision()},f.scale=function(t){return arguments.length?(u.scale(t),a.scale(.35*t),c.scale(t),f.translate(u.translate())):u.scale()},f.translate=function(t){if(!arguments.length)return u.translate();var n=u.scale(),o=+t[0],l=+t[1];return e=u.translate(t).clipExtent([[o-.455*n,l-.238*n],[o+.455*n,l+.238*n]]).stream(s),r=a.translate([o-.307*n,l+.201*n]).clipExtent([[o-.425*n+wh,l+.12*n+wh],[o-.214*n-wh,l+.234*n-wh]]).stream(s),i=c.translate([o-.205*n,l+.212*n]).clipExtent([[o-.214*n+wh,l+.166*n+wh],[o-.115*n-wh,l+.234*n-wh]]).stream(s),f},f.fitExtent=Iv(f),f.fitSize=Fv(f),f.scale(1070)},t.geoArea=function(t){return hp.reset(),Jh(t,pp),2*hp},t.geoAzimuthalEqualArea=function(){return Xv(Kv).scale(124.75).clipAngle(179.999)},t.geoAzimuthalEqualAreaRaw=Kv,t.geoAzimuthalEquidistant=function(){return Xv(t_).scale(79.4188).clipAngle(179.999)},t.geoAzimuthalEquidistantRaw=t_,t.geoBounds=function(t){var n,e,r,i,o,u,a;if(up=op=-(rp=ip=1/0),Sp=[],Jh(t,Hp),e=Sp.length){for(Sp.sort(Kp),n=1,o=[r=Sp[0]];n<e;++n)td(r,(i=Sp[n])[0])||td(r,i[1])?(Qp(r[0],i[1])>Qp(r[0],r[1])&&(r[1]=i[1]),Qp(i[0],r[1])>Qp(r[0],r[1])&&(r[0]=i[0])):o.push(r=i);for(u=-1/0,n=0,r=o[e=o.length-1];n<=e;r=i,++n)i=o[n],(a=Qp(r[1],i[0]))>u&&(u=a,rp=i[0],op=r[1])}return Sp=kp=null,rp===1/0||ip===1/0?[[NaN,NaN],[NaN,NaN]]:[[rp,ip],[op,up]]},t.geoCentroid=function(t){Np=Ap=Ep=Cp=zp=Pp=qp=Lp=Rp=Up=Dp=0,Jh(t,nd);var n=Rp,e=Up,r=Dp,i=n*n+e*e+r*r;return i<Mh&&(n=Pp,e=qp,r=Lp,Ap<wh&&(n=Ep,e=Cp,r=zp),(i=n*n+e*e+r*r)<Mh)?[NaN,NaN]:[Ph(e,n)*Ah,jh(r/Ih(i))*Ah]},t.geoCircle=function(){var t,n,e=hd([0,0]),r=hd(90),i=hd(6),o={point:function(e,r){t.push(e=n(e,r)),e[0]*=Ah,e[1]*=Ah}};function u(){var u=e.apply(this,arguments),a=r.apply(this,arguments)*Eh,c=i.apply(this,arguments)*Eh;return t=[],n=vd(-u[0]*Eh,-u[1]*Eh,0).invert,md(o,a,c,1),u={type:"Polygon",coordinates:[t]},t=n=null,u}return u.center=function(t){return arguments.length?(e="function"==typeof t?t:hd([+t[0],+t[1]]),u):e},u.radius=function(t){return arguments.length?(r="function"==typeof t?t:hd(+t),u):r},u.precision=function(t){return arguments.length?(i="function"==typeof t?t:hd(+t),u):i},u},t.geoClipExtent=function(){var t,n,e,r=0,i=0,o=960,u=500;return e={stream:function(e){return t&&n===e?t:t=Ad(r,i,o,u)(n=e)},extent:function(a){return arguments.length?(r=+a[0][0],i=+a[0][1],o=+a[1][0],u=+a[1][1],t=n=null,e):[[r,i],[o,u]]}}},t.geoConicConformal=function(){return $v(i_).scale(109.5).parallels([30,30])},t.geoConicConformalRaw=i_,t.geoConicEqualArea=Zv,t.geoConicEqualAreaRaw=Wv,t.geoConicEquidistant=function(){return $v(u_).scale(131.154).center([0,13.9389])},t.geoConicEquidistantRaw=u_,t.geoDistance=function(t,n){return Od[0]=t,Od[1]=n,Dd(Fd)},t.geoEquirectangular=function(){return Xv(o_).scale(152.63)},t.geoEquirectangularRaw=o_,t.geoGnomonic=function(){return Xv(a_).scale(144.049).clipAngle(60)},t.geoGnomonicRaw=a_,t.geoGraticule=function(){var t,n,e,r,i,o,u,a,c,s,f,l,h=10,p=h,d=90,_=360,y=2.5;function g(){return{type:"MultiLineString",coordinates:m()}}function m(){return v(Lh(r/d)*d,e,d).map(f).concat(v(Lh(a/_)*_,u,_).map(l)).concat(v(Lh(n/h)*h,t,h).filter(function(t){return Ch(t%d)>wh}).map(c)).concat(v(Lh(o/p)*p,i,p).filter(function(t){return Ch(t%_)>wh}).map(s))}return g.lines=function(){return m().map(function(t){return{type:"LineString",coordinates:t}})},g.outline=function(){return{type:"Polygon",coordinates:[f(r).concat(l(u).slice(1),f(e).reverse().slice(1),l(a).reverse().slice(1))]}},g.extent=function(t){return arguments.length?g.extentMajor(t).extentMinor(t):g.extentMinor()},g.extentMajor=function(t){return arguments.length?(r=+t[0][0],e=+t[1][0],a=+t[0][1],u=+t[1][1],r>e&&(t=r,r=e,e=t),a>u&&(t=a,a=u,u=t),g.precision(y)):[[r,a],[e,u]]},g.extentMinor=function(e){return arguments.length?(n=+e[0][0],t=+e[1][0],o=+e[0][1],i=+e[1][1],n>t&&(e=n,n=t,t=e),o>i&&(e=o,o=i,i=e),g.precision(y)):[[n,o],[t,i]]},g.step=function(t){return arguments.length?g.stepMajor(t).stepMinor(t):g.stepMinor()},g.stepMajor=function(t){return arguments.length?(d=+t[0],_=+t[1],g):[d,_]},g.stepMinor=function(t){return arguments.length?(h=+t[0],p=+t[1],g):[h,p]},g.precision=function(h){return arguments.length?(y=+h,c=Id(o,i,90),s=Yd(n,t,y),f=Id(a,u,90),l=Yd(r,e,y),g):y},g.extentMajor([[-180,-90+wh],[180,90-wh]]).extentMinor([[-180,-80-wh],[180,80+wh]])},t.geoInterpolate=function(t,n){var e=t[0]*Eh,r=t[1]*Eh,i=n[0]*Eh,o=n[1]*Eh,u=qh(r),a=Oh(r),c=qh(o),s=Oh(o),f=u*qh(e),l=u*Oh(e),h=c*qh(i),p=c*Oh(i),d=2*jh(Ih(Hh(o-r)+u*c*Hh(i-e))),v=Oh(d),_=d?function(t){var n=Oh(t*=d)/v,e=Oh(d-t)/v,r=e*f+n*h,i=e*l+n*p,o=e*a+n*s;return[Ph(i,r)*Ah,Ph(o,Ih(r*r+i*i))*Ah]}:function(){return[e*Ah,r*Ah]};return _.distance=d,_},t.geoLength=Dd,t.geoMercator=function(){return e_(n_).scale(961/Nh)},t.geoMercatorRaw=n_,t.geoOrthographic=function(){return Xv(c_).scale(249.5).clipAngle(90+wh)},t.geoOrthographicRaw=c_,t.geoPath=function(){var t,n,e,r,i=4.5;function o(t){return t&&("function"==typeof i&&r.pointRadius(+i.apply(this,arguments)),Jh(t,n(r))),r.result()}return o.area=function(t){return Jh(t,n(Zd)),Zd.result()},o.bounds=function(t){return Jh(t,n(iv)),iv.result()},o.centroid=function(t){return Jh(t,n(gv)),gv.result()},o.projection=function(e){return arguments.length?(n=null==(t=e)?Bd:e.stream,o):t},o.context=function(t){return arguments.length?(r=null==(e=t)?new Ev:new Av(t),"function"!=typeof i&&r.pointRadius(i),o):e},o.pointRadius=function(t){return arguments.length?(i="function"==typeof t?t:(r.pointRadius(+t),+t),o):i},o.projection(null).context(null)},t.geoProjection=Xv,t.geoProjectionMutator=Vv,t.geoRotation=function(t){function n(n){return(n=t(n[0]*Eh,n[1]*Eh))[0]*=Ah,n[1]*=Ah,n}return t=vd(t[0]*Eh,t[1]*Eh,t.length>2?t[2]*Eh:0),n.invert=function(n){return(n=t.invert(n[0]*Eh,n[1]*Eh))[0]*=Ah,n[1]*=Ah,n},n},t.geoStereographic=function(){return Xv(s_).scale(250).clipAngle(142)},t.geoStereographicRaw=s_,t.geoStream=Jh,t.geoTransform=function(t){return{stream:Uv(t)}},t.geoTransverseMercator=function(){var t=e_(f_),n=t.center,e=t.rotate;return t.center=function(t){return arguments.length?n([-t[1],t[0]]):[(t=n())[1],-t[0]]},t.rotate=function(t){return arguments.length?e([t[0],t[1],t.length>2?t[2]+90:90]):[(t=e())[0],t[1],t[2]-90]},e([0,0,90]).scale(159.155)},t.geoTransverseMercatorRaw=f_,Object.defineProperty(t,"__esModule",{value:!0})});