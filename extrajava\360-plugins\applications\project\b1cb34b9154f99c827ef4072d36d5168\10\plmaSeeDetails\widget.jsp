<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>

<render:import varWidget="widget" varFeeds="feeds"/>

<config:getOption name="iconCss" var="iconCss"/>
<config:getOption name="label" var="label"/>
<config:getOption name="showLabel" var="showLabel"/>
<config:getOption name="pageName" var="pageName" defaultValue="search"/>

<search:getPageName var="currentPage"/>
<search:getFeed var="feed" feeds="${feeds}"/>

<url:url var="url" keepQueryString="true" value="${pageName}"/>

<plma:correctUrl var="refinedUrl" url="${url}" pageUrl="" skip="${plma:getParameterValues(widget,'blacklistParams')}" delete2DFacet=""
                 keepExtraRefinements=""/>

<widget:widget varUcssId="uCssId" extraCss="plmaButton see-details" varCssId="cssId" disableStyles="true">
    <c:if test="${not empty iconCss}">
        <i class="${iconCss}" title="${label}" onclick="window.location.replace('${refinedUrl}')"></i>
    </c:if>
    <c:if test="${showLabel}">
        <span class="button-label">${label}</span>
    </c:if>
</widget:widget>