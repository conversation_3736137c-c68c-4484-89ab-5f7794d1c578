(function (window) {
    'use strict';

    /* Constants */
    var WIDGET_NAME = 'chartboard';
    var URL_CHARTBOARD_ID_PARAM = 'chartboardId';
    var URL_CHARTBOARD_PAGE_ID_PARAM = 'pageId';
    var EDIT_MODE_CSS_CLASS = 'edit-active';
    var Chartboard = function (uCssId, options) {
        var defaultOptions = {};
        defaultOptions.editButtonLocationSelector = '.' + uCssId + ' .chartboard-header';
        defaultOptions.baseY = 60;
        defaultOptions.marginY = 10;
        defaultOptions.originalConfig = [];

        this.uCssId = uCssId;
        // this.id = options.id;
        // this.originId = options.id;
        this.pageId = options.pageId;
        this.chartboardConfig = options.chartboardConfig;
        this.originPageId = options.pageId;
        this.templatePage = options.templatePage;
        this.label = options.label;
        this.description = options.description;
        this.icon = options.icon;
        this.widget = $('.' + uCssId);
        this.widget.data('widget', this);

        this.allowEditPages = options.allowEditPages === 'true';
        this.lockMashupPages = options.lockMashupPages === 'true';

        this.gridstackContainer = this.widget.find('.chartboard-layout.grid-stack');
        this.gridstack = null;

        this.options = $.extend({}, defaultOptions, options);
        this.buttonContainer = $("<div class='chartboard-edit-buttons'></div>");

        this.recentlyAddedOrDeletedWidgets = false;
        this.originalConfig = this.options.originalConfig;

        this.customWidgets = {};
        this.widget.find('.add-custom-widgets .custom-widget').each($.proxy(function (i, e) {
            var $e = $(e);
            this.customWidgets[$e.data('customwidget')] = $e.clone();
        }, this));

        this.init();
    };

    /*
     * INIT FUNCTIONS
     * */

    Chartboard.prototype.init = function () {
        this.gridstack = this.gridstackContainer.gridstack({
            cellHeight: this.options.baseY,
            verticalMargin: this.options.marginY,
            float: false,
            acceptWidgets: '.available-widget, .grid-stack-item'
        }).data('gridstack');

        /* By default, disable the moving/resizing widgets */
        this.gridstack.disable();

        this.chartboardStorageManager = new ChartboardStorageManager();
        //this.filters = new ChartboardFilters(this);
        //this.share = new SharePage(this);

        this.isPageAndButtonLoaded = false;
        /* Create the edit icons */
        this.initEditButtons();

        this.initMenuButton();

        /* Manage focus on custom widgets */
        this.initCustomWidgets();

        this.initFullScreen();

        this.initDragDrop();

        /* Init input search on available widgets */
        this.initAvailableSearch();

        this.initRichTextEditor();

        this.initPageInfo();

        /* Trigger a resize event so inner charts can update */
        $(window).trigger('plma:resize', [500]);
    };

    Chartboard.prototype.initEditButtons = function () {
        // var toolbarIconsSize = this.options.editButtonIconSize != '' ? `--icons-size:${this.options.editButtonIconSize}px` : '';
        var button = $('<span class="buttons-container fonticon fonticon-pencil" title="' + Chartboard.getMessage('chartboard.dots.tooltip') + '"></span>');
        var dropdown = $('<div class="chartboard-dropdown-button"></div>');

        var editButton = $('<div class="button-container button-edit-container" title="' + Chartboard.getMessage('chartboard.edit.tooltip') + '"><span class="button button-edit fonticon fonticon-pencil"></span><span class="label">' + Chartboard.getMessage('chartboard.edit.label') + '</span></div>');
        var duplicateButton = $('<div class="button-container button-duplicate-container" title="' + Chartboard.getMessage('chartboard.duplicate.tooltip') + '"><span class="button button-duplicate fonticon fonticon-docs"></span><span class="label">' + Chartboard.getMessage('chartboard.duplicate.label') + '</span></div>');
        var deleteButton = $('<div class="button-container button-delete-container" title="' + Chartboard.getMessage('chartboard.delete.tooltip') + '"><span class="button button-delete fonticon fonticon-trash"></span><span class="label">' + Chartboard.getMessage('chartboard.delete.label') + '</span></div>');
        var resetButton = $('<div class="button-container button-reset-container" title="' + Chartboard.getMessage('chartboard.reset.tooltip') + '"><span class="button button-reset fonticon fonticon-reset"></span><span class="label">' + Chartboard.getMessage('chartboard.reset.label') + '</span></div>');

        var isMashupPage = true;
        if (this.options.pageId && this.options.pageId !== this.options.templatePage) {
            isMashupPage = false;
        }

        if ((!this.lockMashupPages && isMashupPage) || this.options.pageEditable || this.options.pageOwner) {
            dropdown.append(editButton);
        }
        if (this.allowEditPages) {
            dropdown.append(duplicateButton);
        }
        if ((this.allowEditPages && !isMashupPage) && this.options.pageOwner) {
            dropdown.append(deleteButton);
        }
        if (this.allowEditPages && isMashupPage && this.chartboardConfig.fromSavedPage) {
            dropdown.append(resetButton);
        }

        button.append(dropdown);
        this.buttonContainer.append(button);

        var buttonLocation = $(this.options.editButtonLocationSelector);
        if (this.options.hasUser === 'true' && buttonLocation.find('.button-edit').length < 1) {
            buttonLocation.append(this.buttonContainer);
        }

        button.on('click', $.proxy(function () {
            dropdown.toggleClass('active');
        }, this));
        $('body').on('click', $.proxy(function (e) {
            var $e = $(e.target).closest('.buttons-container.fonticon-pencil');
            if (!$(e.target).hasClass('buttons-container') && $e.length === 0 && dropdown.hasClass('active')) {
                dropdown.toggleClass('active');
            }
        }, this));

        /* Bind the click actions */
        editButton.on('click', $.proxy(function () {
            this.enableEdit(false);
        }, this));

        if (this.allowEditPages) {
            duplicateButton.on('click', $.proxy(function () {
                this.enableEdit(true);
            }, this));
            // this.validateDeletePage(deleteButton);
            this.createConfirmDeletePopup($.proxy(function () {
                this.deletePage();
                this.widget.find('.confirm-delete-page-popup-container').removeClass('active');
            }, this));
            deleteButton.on('click', $.proxy(function () {
                this.widget.find('.confirm-delete-page-popup-container').addClass('active');
            }, this));
            resetButton.on('click', $.proxy(function () {
                this.deletePage();
            }, this));
        }

        if (this.isPageAndButtonLoaded) {
            this.updateButtons();
        } else {
            this.isPageAndButtonLoaded = true;
        }
    };

    Chartboard.prototype.initCustomWidgets = function () {
        /* Bind the click action on editable widgets pencil to focus them */
        this.widget.on('click', '.editable-custom-widget-icon', $.proxy(this.focusEditableContent, this));

        /* Remove the focused click when we loose focus */
        $(window).on('click', $.proxy(function (e) {
            var closestCustomWidget = $(e.target).closest('.custom-widget');
            if (closestCustomWidget.length === 0) {
                this.widget.find('.focused').removeClass('focused');
            }
        }, this));

        /* Remember original value of custom elements */
        this.widget.find('.chartboard-layout .custom-widget .content').each($.proxy(function (i, e) {
            var $e = $(e);
            $e.data('original-content', $e.text());
        }, this));

        this.gridstackCustomWidget = this.widget.find('.add-custom-widgets').gridstack({
            cellHeight: 90,
            verticalMargin: 10,
            disableResize: true,
            height: 1,
            width: 2,
            disableOneColumnMode: true
        }).data('gridstack');
    };

    Chartboard.prototype.initFullScreen = function () {
        /* Intercept the click on a full-size button to add a class on the gridstack item (z-index) */
        this.widget.find('.fonticon-resize-full').on('click', $.proxy(this.toggleZIndexOnItem, this));
        this.widget.find('.wuid').on('click', $.proxy(this.removeZIndexOnItem, this));
    };

    Chartboard.prototype.initDragDrop = function () {
        /* Moving widgets */
        this.gridstackContainer.on('dragstart', $.proxy(this.onDragStart, this));
        this.gridstackContainer.on('dragstop', $.proxy(this.onDragStop, this));

        /* Delete */
        this.widget.find('.trash-area').droppable({
            accept: '.grid-stack-item',
            tolerance: 'pointer',
            drop: $.proxy(function (event, ui) {
                this.onTrashDrop(event, ui);
            }, this)
        });

        /* Widget instantiation */
        this.widget.find('.available-widget').draggable({revert: 'invalid', helper: 'clone'});
        this.widget.find('.available-widget').each($.proxy(function (i, e) {
            var $e = $(e);
            this.insertPreview($e, $e.data('preview'));
        }, this));
        this.gridstack.container.on('added', $.proxy(this.onAdded, this));
    };

    Chartboard.prototype.initAvailableSearch = function () {
        var timer = 0;
        this.widget.find('.chartboard-sidebar .search-bar-container input').on('keyup', $.proxy(function (e) {
            var inputValue = e.target.value;
            clearTimeout(timer);
            timer = setTimeout($.proxy(function () {
                this.searchAvailable(inputValue);
            }, this), 1000);
        }, this));
    };

    Chartboard.prototype.initRichTextEditor = function () {
        this.richTextEditor = new RichTextEditor(this.widget, {
            sideArrow: this.options.sideArrow,
            user: this.options.user,
            tinymceUrl: this.options.tinymceUrl
        });
    };

    Chartboard.prototype.initPageInfo = function () {
        this.toolbar = new ChartboardToolbar(this);
        this.page = {};
        this.page.id = this.options.pageId;
        this.page.templatePage = this.options.templatePage;
        this.page.label = this.options.label;
        this.page.description = this.options.description;
        this.page.icon = this.options.icon;

        this.toolbar.initEditToolbar();

        if (this.isPageAndButtonLoaded) {
            this.updateButtons();
        } else {
            this.isPageAndButtonLoaded = true;
        }
    };

    Chartboard.prototype.initMenuButton = function () {
        var buttonLocation = $(this.options.editButtonLocationSelector);
        if (this.options.hasUser === 'true' && buttonLocation.find('.button-edit').length < 1) {
            buttonLocation.append(this.buttonContainer);
        }
    };

    /*
     * OTHER FUNCTIONS
     * */

    Chartboard.prototype.updateButtons = function () {
        var currentUrl = new BuildUrl(document.location.href);
        var hasNewFilters = false;
        for (var param in currentUrl.params) {
            this.exceptionList = ['pageId', '_context', '_', 'lang', ''];
            if (!this.exceptionList.includes(param) && (param.indexOf('.r') !== -1 || param.indexOf('.zr') !== -1 || param.indexOf('q') !== -1 || param.indexOf(this.options.dateParamName) !== -1)) {
                hasNewFilters = true;
                break;
            }
        }
        if (!hasNewFilters) {
            /* Desactivate save filters button */
            this.buttonContainer.find('.button-save-filters-container').addClass('disabled').off();
        }

        if (!this.page || !this.page.filters) {
            /* Desactivate remove filters button */
            this.buttonContainer.find('.button-remove-filters-container').addClass('disabled').off();
        }

        if ((this.pageId === this.templatePage) || (this.page && this.page.id === this.page.templatePage)) {
            /* Desactivate delete page */
            this.buttonContainer.find('.button-delete-container').addClass('disabled').off();
        }
    };

    Chartboard.prototype.toggleZIndexOnItem = function (e) {
        $(e.target).closest('.grid-stack-item').toggleClass('fullsize-item');
    };

    Chartboard.prototype.removeZIndexOnItem = function (e) {
        if (e.target.getClassList().contains('searchWidget')) {
            $(e.target).closest('.grid-stack-item').removeClass('fullsize-item');
        }
    };

    Chartboard.prototype.searchAvailable = function (inputValue) {
        this.widget.find('.chartboard-sidebar .available-widgets .available-widget').each($.proxy(function (i, e) {
            var $e = $(e);
            if ($e.find('.gridstack-elem-title-label').text() !== undefined
                && $e.find('.gridstack-elem-title-label').text() !== ''
                && $e.find('.gridstack-elem-title-label').text().toLowerCase().indexOf(inputValue.toLowerCase()) === -1) {
                $e.addClass('hidden');
            } else {
                $e.removeClass('hidden');
            }
        }, this));
    };

    Chartboard.prototype.focusEditableContent = function (e) {
        var elt = $(e.target).siblings('.content');
        this.richTextEditor.openRichtextPopup(elt);
    };

    Chartboard.prototype.focusOnElement = function (elt) {
        elt.focus();
        if (document.getSelection && document.createRange) {
            var range = document.createRange();
            range.selectNodeContents(elt.get(0));
            range.collapse(false);
            var select = document.getSelection();
            select.removeAllRanges();
            select.addRange(range);
        } else if (document.selection) {
            /* IE < 11 */
            elt.get(0).focus();
            var currentRange = document.selection.createRange();
            currentRange.moveEnd('character', 0);
            currentRange.select();
        }
        this.widget.find('.focused').removeClass('focused');
        elt.closest('.grid-stack-item').addClass('focused');
    };


    Chartboard.prototype.enableEdit = function (isDuplicate) {
        //We enable the moving/resizing widgets
        this.gridstack.enable();
        this.gridstackCustomWidget.enableMove(true, true);
        this.widget.find('.add-custom-widgets').find('.ui-resizable-handle').remove();

        //Enable the editable from custom widgets
        this.widget.find('.chartboard-layout .custom-widget .tile .content').attr('contenteditable', true);

        //We add an edit class on the chartBoard widget and on the button container
        this.widget.addClass(EDIT_MODE_CSS_CLASS);
        this.buttonContainer.addClass(EDIT_MODE_CSS_CLASS);

        //Disable interactions on sub-widgets (create a div above the widget)
        this.widget.find('.grid-stack-item .grid-stack-hide-item').removeClass('hidden');

        //upload tinymce if it isn't
        this.richTextEditor.loadScript();

        this.addEditToolbar(isDuplicate);

        this.widget.trigger('plma:resize');
    };

    Chartboard.prototype.deletePage = function () {
        this.chartboardStorageManager.deletePage(this.pageId, this.options.url);
    };

    Chartboard.prototype.saveEdit = function (isDuplicate) {
        if (!this.page) {
            // If no page is defined, then we are on a default page
            this.page = {};
            this.page.id = this.options.pageId;
            this.page.label = this.getPageLabel();
            this.page.description = this.getPageDescription();
            this.page.icon = this.getPageIcon();
            this.page.templatePage = this.getTemplatePage();
        }

        var config = this.getConfig();
        if(this.chartboardConfig.fromSavedPage || isDuplicate || this.hasChange(this.page, config, this.chartboardConfig)){
            this.chartboardStorageManager.savePage(this.page, this.options.url, config, isDuplicate);
        }
        else{
            this.cancelEdit();
        }
    };

    Chartboard.prototype.hasChange = function(page, newConfig, oldConfig) {
        const filterTiles = ({ cssClass, cssId, preview, previewHTML, previewId, title, backgroundColor, arrowSide,y, ...rest }) => rest;
        const displayedTilesNew = newConfig.tiles.map(filterTiles).filter(tile => tile.displayed);
        const displayedTilesOld = oldConfig.tiles.map(filterTiles).filter(tile => tile.displayed);

        if (page.label !== oldConfig.label || page.description !== oldConfig.description || page.icon !== oldConfig.icon) {
            return true;
        }

        if (displayedTilesNew.length !== displayedTilesOld.length) {
            return true;
        }

        for (let i = 0; i < displayedTilesNew.length; i++) {
            const obj1 = displayedTilesNew[i];
            const obj2 = displayedTilesOld[i];

            const keys1 = Object.keys(obj1);
            const keys2 = Object.keys(obj2);

            if (keys1.length !== keys2.length) {
                return true;
            }

            for (let key of keys1) {
                if (obj1[key] !== obj2[key]) {
                return true;
                }
            }
        }

        return false;
    }

    Chartboard.prototype.cancelEdit = function () {
        this.removeEditToolbar();
        // this.id = this.originId;
        if (this.originPageId && this.page) {
            this.page.id = this.originPageId;
        } else {
            this.page = undefined;
        }

        this.update();
    };

    Chartboard.prototype.hasPageChanges = function (trueCallback, falseCallback) {
        if (this.options.pageId && this.options.pageId !== this.options.templatePage) {
            trueCallback.call();
        } else {
            falseCallback.call();
        }
    };

    Chartboard.prototype.hasLayoutChanges = function (trueCallback, falseCallback) {
        if (this.options.pageId && this.options.pageId !== this.options.templatePage) {
            trueCallback.call();
        } else {
            falseCallback.call();
        }
    };

    Chartboard.prototype.hasFiltersChanges = function (trueCallback, falseCallback) {
        if (this.options.pageId && this.options.pageId !== this.options.templatePage) {
            trueCallback.call();
        } else {
            falseCallback.call();
        }
    };

    Chartboard.prototype.disableEdit = function () {
        //We disable the moving/resizing widgets
        this.gridstack.disable();
        this.gridstackCustomWidget.disable();

        //Remove the editable from custom widgets
        this.widget.find('.custom-widget .tile .content').removeAttr('contenteditable');

        //We remove the edit class on the chartBoard widget and on the button container
        this.widget.removeClass(EDIT_MODE_CSS_CLASS);
        this.buttonContainer.removeClass(EDIT_MODE_CSS_CLASS);

        //Enable interactions on sub-widgets
        this.widget.find('.grid-stack-item .grid-stack-hide-item').addClass('hidden');
        this.widget.trigger('plma:resize');
    };

    Chartboard.prototype.resetLayout = function () {
        //Destroy all new custom widgets (not saved yet)
        this.widget.find('.chartboard-layout .grid-stack-item[data-is-not-saved="true"]').remove();

        var originalLayout = {};
        var customWidgetCount = 0;
        for (var i = 0; i < this.originalConfig.length; i++) {
            var tileConfig = this.originalConfig[i];
            if (tileConfig.cellId !== "") {
                originalLayout[tileConfig.cellId] = tileConfig;
            } else {
                var customWidgetCellId = this.uCssId + '-custom-widget-' + customWidgetCount;
                customWidgetCount++;
                originalLayout[customWidgetCellId] = tileConfig;
            }
        }

        this.widget.find('.chartboard-layout .grid-stack-item').each($.proxy(function (i, e) {
            var $e = $(e);
            var tileConfig = originalLayout[$e.data('cell-id')];
            if (tileConfig) {
                $e.attr('data-gs-x', tileConfig.x)
                    .attr('data-gs-y', tileConfig.y)
                    .attr('data-gs-width', tileConfig.width)
                    .attr('data-gs-height', tileConfig.height);
            } else {
                var customTileConfig = originalLayout[$e.data('custom-cell-id')];
                if (customTileConfig) {
                    $e.attr('data-gs-x', customTileConfig.x)
                        .attr('data-gs-y', customTileConfig.y)
                        .attr('data-gs-width', customTileConfig.width)
                        .attr('data-gs-height', customTileConfig.height);
                    var $content = $e.find('.content');
                    $content.text($content.data('original-content'));
                }
            }
        }, this));


    };

    Chartboard.prototype.onDragStart = function (event, ui) {
        this.widget.addClass('dragging');
    };

    Chartboard.prototype.onDragStop = function (event, ui) {
        this.widget.removeClass('dragging');
    };

    Chartboard.prototype.onTrashDrop = function (event, ui) {
        var $tile = $(ui.draggable).closest('.grid-stack-item');
        if (!$tile.hasClass('custom-widget')) {
            var $div = this.createAvailbleWidgetElement($tile);
            this.widget.find('.available-widgets').append($div);
        }
        this.gridstack.removeWidget($tile, true);
        this.widget.removeClass('dragging');
        this.layoutModified();
    };

    Chartboard.prototype.layoutModified = function () {
        this.recentlyAddedOrDeletedWidgets = true;
        this.toolbar.toolbar.addClass('layout-modified');
    };

    /**
     * Creates the div to display in the Available Widgets list, based
     * on the tile from the grid.
     */
    Chartboard.prototype.createAvailbleWidgetElement = function ($tile) {
        var $div = $('<div/>');
        $div.addClass('available-widget preview with-preview');
        if ($tile.attr('id')) {
            $div.attr('id', $tile.attr('id'));
        }
        $div.attr('data-cell-id', $tile.data('cell-id'));
        $div.attr('data-cell-cssclass', $tile.data('cell-cssclass'));
        $div.attr('data-displayed', false);

        var previewString = this.getPreview($tile);
        if ((previewString === undefined || previewString === "") && $tile.find('.gridstack-elem-title-label').length > 0) {
            //Get title inside gridstack elem
            $div.append($tile.find('.gridstack-elem-title-label')[0]);
        }
        var $title = $('<span />');
        $title.addClass('gridstack-elem-title-label');
        $title.text($tile.find('.widgetTitle').text());
        $div.append($div, $title);
        this.insertPreview($div, previewString);

        $div.draggable({revert: 'invalid', helper: 'clone'});
        $div.append($('<div class="tile grid-stack-item-content"/>'));
        return $div;
    };

    Chartboard.prototype.insertPreview = function ($div, previewString) {
        if (previewString) {
            $div.attr('data-preview', previewString);
            var imageBlob = new Blob([previewString], {type: 'image/svg+xml;charset=utf-8'});
            var domURL = self.URL || self.webkitURL || self;
            $div.append($('<img class="img-thumbnail" src=' + domURL.createObjectURL(imageBlob) + ' />'));

        } else {
            $div.append($('<span class="icon-thumbnail fonticon fonticon-chart-line"/>'));
        }
    };

    Chartboard.prototype.onAdded = function (event, items) {
        for (var i = items ? items.length : 0; i > 0; i--) {
            var $e = $(items[i - 1].el);
            if ($e.hasClass('custom-widget')) {
                this.transformCustomWidget($e);
                this.layoutModified();
            } else {
                if (!$($e.context).hasClass('grid-stack-item')) {
                    $e.attr('data-displayed', true);
                    $e.removeClass('available-widget');
                    this.layoutModified();
                } else {
                    this.onDragStop();
                }
            }
        }
    };

    /**
     * This function fakes some gridstack black magic on elements dropped from outside the grid.
     * It seems to limit the occurrence of http://ng/trac/apps/6956.
     */
    Chartboard.prototype.fakeGridStuff = function (element) {
        /* To prevent a gridstack error from happening when its event handler is triggered before ours. */
        var node = this.gridstack.grid._prepareNode({width: 1, height: 1, _added: false, _temporary: true});
        node._grid = this.gridstack;

        element.data('_gridstack_node_orig', node);
    };

    Chartboard.prototype.transformCustomWidget = function ($element) {
        /* Clean the $element from useless resize handles: only keep the first handle element,
           The other ones don't work */
        $element.find('.ui-resizable-handle').detach().first().appendTo($element);

        /* Clone the widget and add it inside the custom gridstack */
        var clone = $element.clone();
        var x = clone.data('cell-cssclass').indexOf('title-widget') > -1 ? 1 : 0;
        this.gridstackCustomWidget.addWidget(clone, x, 0, 2, 1, true);
        this.gridstackCustomWidget.enableMove(true, true);
        clone.find('.ui-resizable-handle').remove();

        var $content = $element.find('.content');
        $content.attr('contenteditable', true);
        $content.children().remove();
        this.focusOnElement($content);
    };

    Chartboard.prototype.addEditToolbar = function (isDuplicate) {
        this.toolbar.toolbar.addClass('active');
        this.toolbar.isDuplicate = isDuplicate;

        /* Fix page label */
        this.toolbar.toolbar.find('.chartboard-create-page-popup .icon-container .input')[0].innerHTML = '';
        this.toolbar.toolbar.find('.chartboard-create-page-popup .icon-container .input').addClass(this.getPageIcon());
    };

    Chartboard.prototype.removeEditToolbar = function () {
        this.toolbar.toolbar.removeClass('active');
    };

    Chartboard.prototype.getConfig = function () {
        this.widget.find('.chartboard-layout .grid-stack-item.grid-stack-placeholder').remove();
        var serializedCells = _.map(this.widget.find('.chartboard-layout .grid-stack-item, .available-widget'), $.proxy(function (el) {
            el = $(el);
            var node = el.data('_gridstack_node') || {x: 0, y: 0, width: 1, height: 1};
            var preview = "";
            var backgroundColor = "";
            var arrowSide = "";
            if (el.hasClass('custom-widget')) {
                preview = el.find('.content')[0].innerHTML;
                if (el.find('.content').data('background-color')) {
                    backgroundColor = el.find('.content').data('background-color');
                }
                if (el.find('.content').data('arrow-side')) {
                    arrowSide = el.find('.content').data('arrow-side');
                }
            } else {
                preview = this.getPreview(el);
            }

            var title = "";
            if (el.hasClass('available-widget')) {
                title = el.find('.gridstack-elem-title-label').text();
            }

            return {
                cellId: el.data('cell-id') || "",
                x: node.x,
                y: node.y,
                width: node.width,
                height: node.height,
                cssClass: el.data('cell-cssclass') || "",
                cssId: el.attr('id') || "",
                displayed: el.data('displayed'),
                preview: this.options.savePreview ? preview : '',
                previewHTML: this.options.savePreview && el.data('preview') ? el.data('preview') : '',
                previewId: this.options.savePreview && el.data('preview') ? 'chartboard_widget_preview_' + this.id + '_' + el.data('cell-id') : '',
                title: title,
                backgroundColor: backgroundColor,
                arrowSide: arrowSide
            };


        }, this));

        var config = {
            id: this.getId(),
            label: this.getLabel(),
            templatePage: this.getTemplatePage(),
            description: this.getDescription(),
            tiles: serializedCells
        };

        return config;
    };

    Chartboard.prototype.getPageLabel = function () {
        return this.chartboardConfig.label;
    };

    Chartboard.prototype.getInputPageLabel = function () {
        return this.toolbar.toolbar.find('.chartboard-create-page-popup .label-container input').val();
    };

    Chartboard.prototype.getPageDescription = function () {
        return this.chartboardConfig.description;
    };

    Chartboard.prototype.getInputPageDescription = function () {
        return this.toolbar.toolbar.find('.chartboard-create-page-popup .description-container textarea').val();
    };

    Chartboard.prototype.getPageIcon = function () {
        return this.chartboardConfig.icon;
    };

    Chartboard.prototype.getPageId = function () {
        return this.pageId;
    };

    Chartboard.prototype.getLabel = function () {
        return this.label;
    };

    Chartboard.prototype.getId = function () {
        return this.id;
    };

    Chartboard.prototype.getTemplatePage = function () {
        return this.chartboardConfig.templatePage;
    };

    Chartboard.prototype.getDescription = function () {
        return this.description;
    };

    Chartboard.prototype.getPreview = function ($element) {
        var xmlSerializer = new XMLSerializer();
        var svg = $element.find('svg').get(0);
        if (typeof svg === "undefined") {
            //We search if we have already a preview and not a svg
            var content = $element.data('preview');
            if (typeof content !== "undefined" && content.indexOf('<svg') !== -1) {
                return content;
            } else {
                var htmlContent = $element.find('.content').get(0);
                if (typeof htmlContent !== "undefined") {
                    return $(htmlContent).html();
                } else {
                    return "";
                }
            }
        } else {
            return xmlSerializer.serializeToString($element.find('svg').get(0));
        }
    };

    Chartboard.prototype.displayErrorMessage = function (message) {
        var errorContainer = this.widget.find('.notification-template');
        errorContainer.find('.message-text').text(message);
        errorContainer.find('.message-plma-container').removeClass('hidden');
    };

    Chartboard.prototype.displayNotification = function (message, notificationType) {
        $.notify.defaults({autoHide: false});
        $.notify(Chartboard.getMessage(message), notificationType);
        $.notify.defaults({autoHide: true});
    };

    Chartboard.getMessage = function (code) {
        return mashupI18N.get(WIDGET_NAME, code);
    };

    Chartboard.prototype.createConfirmDeletePopup = function (successCallback) {
        var container = $('<div class="confirm-delete-page-popup-container"></div>');
        container.append($('<div class="overlay visible"></div>'));

        var wrapper = $('<div class="confirm-delete-page-popup-wrapper"></div>');
        wrapper.append($('<div class="title">' + Chartboard.getMessage('chartboard.delete.page.title') + '</div>'));

        var buttonContainer = $('<div class="button-container"></div>');
        var confirmButton = $('<div class="btn btn-default confirm-button delete-page-button">' + Chartboard.getMessage('chartboard.delete.page.confirm') + '</div>');
        var cancelButton = $('<div class="btn btn-default cancel-button delete-page-button">' + Chartboard.getMessage('chartboard.delete.page.cancel') + '</div>');
        buttonContainer.append(cancelButton).append(confirmButton);

        wrapper.append(buttonContainer);

        container.append(wrapper);

        this.widget.find('> .widgetContent').append(container);

        this.widget.find('.widgetContent .confirm-delete-page-popup-container .overlay').on('click', $.proxy(function () {
            this.widget.find('.confirm-delete-page-popup-container').removeClass('active');
        }, this));

        cancelButton.on('click', $.proxy(function () {
            this.widget.find('.confirm-delete-page-popup-container').removeClass('active');
        }, this));

        confirmButton.on('click', $.proxy(function () {
            successCallback.call();
        }, this));
    };

    /**
     * Uses the ajax client to reload the widget.
     */
    Chartboard.prototype.update = function (id) {
        if (id === 'undefined') {
            this.id = undefined;
        } else if (id) {
            this.id = id;
        }
        if (!(typeof history.pushState === 'undefined')) {
            var url = new BuildUrl(document.location.href);
            if (this.page && this.page.id && this.page.id !== this.getTemplatePage()) {
                url.addParameter(URL_CHARTBOARD_PAGE_ID_PARAM, this.page.id, true);
            } else {
                url.removeParameter(URL_CHARTBOARD_PAGE_ID_PARAM);
            }

            var stateObj = {};
            window.history.pushState(stateObj, url.getPage(), url);
        }

        var client = new PlmaAjaxClient(this.widget);
        this.buttonContainer.remove();
        if (this.page && this.page.id && this.page.id !== this.getTemplatePage()) {
            client.getAjaxUrl().addParameter(URL_CHARTBOARD_PAGE_ID_PARAM, this.page.id, true);
        } else {
            client.getAjaxUrl().removeParameter(URL_CHARTBOARD_PAGE_ID_PARAM);
        }

        client.addWidget(this.uCssId);

        /* Remove toolbar */
        this.toolbar.toolbar.remove();

        /* Add trigger to reload other widgets */
        $(document).trigger('plma:chartboard-save');

        client.update();
    };

    window.Chartboard = Chartboard;
})(window);
