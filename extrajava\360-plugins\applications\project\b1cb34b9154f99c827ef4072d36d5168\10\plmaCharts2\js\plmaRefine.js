var PLMARefine = function() {
	this.feedName = '';
	this.zapRefine = false;
	this.exclude = false;
	this.facetId = '';
	this.categories = {0: []};
	this.type = PLMARefine.TYPE_NORMAL;
};

PLMARefine.TYPE_NORMAL = 'normal';
PLMARefine.TYPE_MULTIDIM = 'multidim';
PLMARefine.TYPE_2D = '2d';
PLMARefine.DIMENSION_SWITCH = '__dimension_switch__';


/**
 * If the refine is on a multidim or 2d facet, this method
 * returns a new refine applied on one of its dimension.
 * Otherwise, it just changes the facet id.
 * dimensionId: int, the dimension to keep, starting at 1. Ignored for multidim.
 * facetId: string, the id of the facet on wich the new refine applies
 *   */
PLMARefine.prototype.getProjection = function(dimensionId, facetId) {
	var projection = PLMARefine.factory.clone(this);
	projection.setType(PLMARefine.TYPE_NORMAL);
	projection.setFacetId(facetId);
	
	if (this.getType() == PLMARefine.TYPE_2D) {
		var categories = this.getCategories(dimensionId -1);
		projection.clearCategories();
		projection.setCategories(categories);
	}
	if (this.getType() == PLMARefine.TYPE_MULTIDIM) {
		/* The path would be 1/Top/classproperties/myclass/myattribute/mycategory 
		 * As hierarchical facets are not used in multidim (or only at the first level),
		 * we can assume that the last part of the path is enough */
		var categories = this.getCategories(facetId);
		projection.clearCategories();
		projection.setCategories(categories.slice(categories.length -1));
	}
	
	return projection;
};

/**
 * Returns the name of the associated URL parameter, 
 * e.g. 'myfeed.r'
 */
PLMARefine.prototype.getParamName = function() {
	return this.feedName + (this.isZapRefine() ? '.zr' : '.r'); 

};
/**
 * Returns the value of the associated URL parameter,
 * e.g. 'f/current/complete'. For multidim facets, returns
 * an array containing all the values for the refine parameter.
 */
PLMARefine.prototype.getParamValue = function() {
	var paramValue = (this.isExclude() ? '-f/' : 'f/') + this.facetId + '/';
	if (this.getType === PLMARefine.TYPE_MULTIDIM) {
		var paramValues = [];
		for (dimension in this.getCategories()) {
			paramValues.push(paramValue + this.getCategories(dimension).join('/'));
		}
		return paramValues;
	}
	
	if (this.getType === PLMARefine.TYPE_2D) {
		paramValue += this.getCategories(0) + '/' 
			+ PLMARefine.DIMENSION_SWITCH + '/' + this.getCategories(1);
		return paramValue;
	}

	paramValue += this.getCategories(0).join('/');
	return paramValue;
};


PLMARefine.prototype.withFeedName = function(feedName){
	this.feedName = feedName;
	return this;
};
PLMARefine.prototype.getFeedName = function(){
	return this.feedName;
};

PLMARefine.prototype.withZapRefine = function(zapRefine){
	this.zapRefine = zapRefine;
	return this;
};
PLMARefine.prototype.isZapRefine = function(){
	return this.zapRefine;
};

PLMARefine.prototype.withExclude = function(exclude){
	this.exclude = exclude;
	return this;
};
PLMARefine.prototype.isExclude = function(){
	return this.exclude;
};

PLMARefine.prototype.setFacetId = function(facetId){
	this.facetId = facetId;
};
PLMARefine.prototype.withFacetId = function(facetId){
	this.setFacetId(facetId);
	return this;
};
PLMARefine.prototype.getFacetId = function(){
	return this.facetId;
};

/**
 * Sets the given category for this refine under the given dimension.
 * If dimension is omitted, the category is set on the default dimension, 0.
 */
PLMARefine.prototype.setCategory = function(categoryName, dimension) {
	var dim = dimension === undefined ? 0 : dimension; 
	this.categories = {
		dim : [categoryName]
	};
};
PLMARefine.prototype.clearCategories = function(){
	this.categories = {0: []};
};
PLMARefine.prototype.setCategories = function(categories, dimension) {
	if (Array.isArray(categories)) {
		this.categories[dimension === undefined ? 0 : dimension] = categories;
	} else if (typeof categories === 'string') {
		this.categories[dimension === undefined ? 0 : dimension] = categories.split('/');
	} else {
		this.categories = categories;	
	}
};
PLMARefine.prototype.addCategories = function(categoryNames, dimension) {
	var categories;
	if (Array.isArray(categoryNames)) {
		categories = categoryNames;
	}
	if (typeof categoryNames === 'string') {
		categories = categoryNames.split('/');
	}
	this.categories [dimension === undefined ? 0 : dimension] = categories;
	
};
PLMARefine.prototype.withCategories = function(categoryNames, dimension) {
	this.setCategories(categoryNames, dimension);
	return this;
};
PLMARefine.prototype.addCategory = function(categoryName, dimension) {
	var dim = dimension === undefined ? 0 : dimension;
	if (!this.categories.hasOwnProperty(dim)) {
		this.categories[dim] = [];
	} 
	this.categories[dim].push(categoryName);
};
/**
 * Returns an array containing all the categories for this refine on the specified
 * dimension. If dimension is omitted, returns an object containing all categories 
 * for all dimension
 */
PLMARefine.prototype.getCategories = function(dimension) {
	if (dimension === undefined) {
		return this.categories;
	} else {
		return this.categories.hasOwnProperty(dimension)?
				this.categories[dimension] : [];
	}
};

PLMARefine.prototype.setType = function(type) {
	this.type = type;
};
PLMARefine.prototype.withType = function(type) {
	this.setType(type);
	return this;
};
PLMARefine.prototype.getType = function() {
	return this.type;
};

(function(){
	/**
	 * Internal methods
	 */
	var handleMultidimParam = function(refine, refinePath){
		refine.setType(PLMARefine.TYPE_MULTIDIM);
		refine.setFacetId(refine.getFacetId().split('<')[0]);
		var dimensionName;
		var categories = [];
		
		var classPropertiesIndex = Math.max(
				refinePath.indexOf('classproperties'), 
				refinePath.indexOf('ClassProperties'));
		/* If the path has 'classproperties' in it, the facet is generated by the
		 * datamodel, so its name will be className_facetId except if the class is the
		 * default one. Let's try to get the right dimension name. */
		if (classPropertiesIndex > -1) {
			var afterClassProperties = refinePath.slice(classPropertiesIndex +1);
			/* We must assume that the refinement is on only 1 level. On multidim
			 * it would not work otherwise. */
			if (afterClassProperties.length === 2){
				/* e.g. 'current/complete' ==> default class */
				dimensionName = afterClassProperties[0];
				categories = refinePath.slice(1);
			} else {
				/* e.g. 'change/severity/high' ==> 'change' class */
				dimensionName = afterClassProperties.slice(0, 2).join('_');
				categories = refinePath.slice(2);
			}
		} else {
			dimensionName = refinePath[refinePath.length -2];
			categories = refinePath.slice(refinePath.length -1);
		}
		
		refine.setCategories(categories, dimensionName);
		
	};
	
	var handle2dParam = function(refine, refinePath){
		refine.setType(PLMARefine.TYPE_2D);
		var dimensionCategories = refinePath.join('/').split('/' + PLMARefine.DIMENSION_SWITCH + '/');
		for (var j=0; j<dimensionCategories.length; j++) {
			var categories = dimensionCategories[j];
			if (j == 0) {
				categories = categories.split('/').slice(2); /* Removing f/facetId/ */ 
			}
			refine.setCategories(categories, j);
		}
	};
	
	/**
	 * Constructor helpers
	 */
	PLMARefine.factory = {
		/** To create a PLMARefine from a URL refine parameter, e.g.:
		 * myfeed.r=%2Bf%2Fcurrent%2Fcomplete 
		 * */
		fromRefineParam: function(refineParam) {
			var refine = new PLMARefine();
			
			var splitParam = refineParam.split('=');
			var paramName = splitParam[0];	/* e.g. 'myfeed.r' */
			var paramValue = splitParam.length >= 2 ? decodeURIComponent(splitParam[1]) : '';	/* e.g. '+f/current/complete' */
			var refinePath = paramValue.split('/');
			var facetId = refinePath.length >= 2 ? refinePath[1] : '';
			
			refine.withFeedName(paramName.split('.')[0])
				.withZapRefine(paramName.endsWith('.zr'))
				.withExclude(paramValue.startsWith('-f'))
				.withFacetId(facetId);
			
			if (refine.facetId.indexOf('<') != -1) {
				/* Multidim facets have '<' and '>' in their generated ID */
				handleMultidimParam(refine, refinePath);
			} else if (refinePath.indexOf(PLMARefine.DIMENSION_SWITCH) != -1) {
				/* 2D facets have a dimension switch in their path */
				handle2dParam(refine, refinePath);
			} else {
				refine.withCategories(refinePath.slice(2));	
			}
			
			return refine;
		},
		
		/** To create a PLMARefine from a string of URL refine parameters, 
		 * usually describing a multidim facet, e.g.:
		 * myfeed.r=%2Bf%2Fcurrent_severity<QHJKDUI=>%2F0%2FTop%2Fclassproperties%2Fcomplete&
		 * myfeed.r=%2Bf%2Fcurrent_severity<QHJKDUI=>%2F0%2FTop%2Fclassproperties%2Fseverity%2Fhigh
		 * Works if there is only 1 refine parameter too.
		 * */
		fromRefineParams: function(refineParams) {
			var refines = [];
			var refineParamList = refineParams.split('&');
			for (var i=0; i<refineParamList.length; i++){
				var refineParam = refineParamList[i];
				refines.push(PLMARefine.factory.fromRefineParam(refineParam));
				
			}
			var refine = PLMARefine.factory.merge(refines);
			return refine;
		},
		
		/**
		 * To create a PLMARefine from another PLMARefine.
		 */
		clone: function(original) {
			var refine = new PLMARefine();
			if (original instanceof PLMARefine) {
				refine.withFeedName(original.getFeedName())
					.withZapRefine(original.isZapRefine())
					.withExclude(original.isExclude())
					.withFacetId(original.getFacetId())
					.withType(original.getType());
				for (dimension in original.getCategories()) {
					refine.setCategories(original.getCategories(dimension).slice(), dimension);
				}
			}
			return refine;
		},
		
		/**
		 * To merge several PLMARefines into one.
		 * The categories are merged, other attributes are
		 * ignored if already valued, or overriden if
		 * override is true.
		 */
		merge: function(plmaRefines, override) {
			if (plmaRefines.length == 0) {
				return new PLMARefine();
			}
			if (plmaRefines.length == 1) {
				return plmaRefines[0];
			}
			
			var refine = PLMARefine.factory.clone(plmaRefines[0]);
			for (var i=1; i<plmaRefines.length; i++) {
				var toMerge = plmaRefines[i];
				if (override) {
					refine.setZapRefine(toMerge.isZapRefine());
					refine.setExclude(toMerge.isExclude());
					refine.setType(toMerge.getType());
				}
				if (override || !refine.getFeedName()) {
					refine.setFeedName(toMerge.getFeedName());
				}
				if (override || !refine.getFacetId()) {
					refine.setFacetId(toMerge.getFacetId());
				}
				refine.setCategories($.extend({}, refine.getCategories(), toMerge.getCategories()));
			}
			return refine;
		}
			
	};
	
})();
