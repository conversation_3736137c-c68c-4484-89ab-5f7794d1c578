<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA result list commons (DO NOT USE)" group="PLM Analytics/Results Rendering" premium="true">

	<Description>This widget contains result list common resources for different implementations (common JS, JSP templates, css...).</Description>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="css" path="css/resultlist-commons.less" />
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="../3dxpSearchAndRefine/js/message-handler.js"/>
		<Include type="js" path="../plmaResources/js/experienceAPIs/Session3DContent.js"/>
		<Include type="js" path="../plmaResources/js/select.js" />
		<Include type="js" path="../plmaResources/js/SelectionWidget.js" />
		<Include type="js" path="js/plmaResultList.js" />
		<Include type="js" path="js/gridTableSetup.js" />
		<Include type="js" path="/resources/javascript/exa/io/io.js" />
		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js"/>
		<Include type="js" path="../plmaResources/js/PlmaInfiniteScroll.js"/>
		<Include type="js" path="../plmaResources/js/detailHitHelper.js"/>
		<Include type="js" path="../plmaResources/js/historyState.js"/>
		<Include type="js" path="../plmaResources/js/lightbox.js"/>
		<Include type="js" path="../plmaResources/js/plmaUrlBuilder.js"/>
		<Include type="css" path="css/resultlist-pref.less" />
		<Include type="js" path="js/plmaResultListPref.js"/>
		<Include type="js" path="js/resultListCollectionManager.js"/>
		<Include type="js" path="js/collectionEventHandlers.js"/>
		<Include type="js" path="js/hitDetailsEventHandlers.js"/>
		<Include type="js" path="js/urlHitSelector.js"/>
		<Include type="js" path="js/3dxpShareItems.js"/>
	</Includes>
	
	<Dependencies>
		<Widget name="thumbnail" includeCondition="showThumbnail=true" />
		<Widget name="plmaResources" />
		<Widget name="plmaPagination" />
		<Widget name="plmaExport" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO" />

	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>hitcustom.sort_asc</JsKey>
			<JsKey>hitcustom.sort_dsc</JsKey>
			<JsKey>plma.resultlist.view.default</JsKey>
			<JsKey>plma.resultlist.view.gridtable</JsKey>
			<JsKey>plma.resultlist.view.gridtable.rows</JsKey>
			<JsKey>plma.resultlist.view.gridtable.columns</JsKey>
			<JsKey>plma.resultlist.view.gridtable.pin</JsKey>
			<JsKey>plma.resultlist.view.gridtable.pincol</JsKey>
			<JsKey>plma.resultlist.view.gridtable.unpincol</JsKey>
			<JsKey>plma.resultlist.view.gridtable.unpin</JsKey>
			<JsKey>plma.resultlist.compare.hidesimilarcols.hide</JsKey>
			<JsKey>plma.resultlist.compare.hidesimilarcols.show</JsKey>

			<JsKey>plma.resultlist.tab.label</JsKey>
			<JsKey>plma.resultlist.tab.title</JsKey>
			<JsKey>plma.resultlist.tab.description</JsKey>
			<JsKey>plma.resultlist.button.selectall</JsKey>
			<JsKey>plma.resultlist.button.deselectall</JsKey>
			<JsKey>plma.resultlist.column.state.false</JsKey>
			<JsKey>plma.resultlist.column.state.ifnotempty</JsKey>
			<JsKey>plma.resultlist.column.state.true</JsKey>
			<JsKey>plma.resultlist.save</JsKey>
			<JsKey>plma.resultlist.reset</JsKey>
		</JsKeys>
	</SupportI18N>
</Widget>
