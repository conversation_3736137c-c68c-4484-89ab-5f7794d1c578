var HideShowTabs = function(uCssId, options){
	'use strict';
	
	var defaults = {
			showSelectors: [],
			defaultTab: -1
	};
	
	var $window = $(window);
	
	var hide = function($elements){
		if($elements!=undefined){
			$elements.addClass('hideShowTabs-hidden');
		}
	};
	
	var show = function($elements,$elementsToHide, widgetOptions, fromClick){
		if($elements!=undefined){
			var hasToLoad = false;
			for(var i=0; i<$elements.length; i++){
				if($elements[i].getClassList().contains('subWidgetHidden')){
					hasToLoad = true;
					
					var url = window.location.href;
					var options = {};
					options.success = $.proxy(function(){
						if($elementsToHide!=undefined){
							$elementsToHide.addClass('hideShowTabs-hidden');
						}
						$elements.removeClass('hideShowTabs-hidden');
						$(window).trigger('plma:resize');
						(widgetOptions.secondWidgetCallback).call(null, $elements);
					},this);
					
					var decoded = decodeURIComponent(url);
					var client = new PlmaAjaxClient($('.'+$elements[i].getClassList()[2]),options);
					var params = decoded.split('?');
					var parameters = getUrlParameters(params[1]);
					
					//Delete the base params
					client.getAjaxUrl().params = {};

					for (var j = 0; j < parameters.length; j++) {
						var key = parameters[j].key;
						var value = parameters[j].value;
						if(key != "" && key != "_"){
							//remove anchor if contained in a parameter value
							client.getAjaxUrl().addParameter(key, value.split("#")[0], false);
						}
					}
					
					// set wuids to update
					client.addWidget($elements[i].getClassList()[2]);

					client.update();
				}
			}
			if(!hasToLoad){
				if($elementsToHide!=undefined){
					$elementsToHide.addClass('hideShowTabs-hidden');
				}
				$elements.removeClass('hideShowTabs-hidden');
				$(document).find('.widgetTitle').trigger('plma:resize');
				if(fromClick){
                    (widgetOptions.tabClickCallback).call(null, $elements);
				}
			}
		}
	};
	
	var getUrlParameters = function(url) {
		var f = [];
		if( url == undefined ) {
			return f;
		}

		var t = url.split('&');
		var j = 0;
		for (var i = 0; i < t.length; i++) {
			var x = t[i].split('=');
			if (x.length == 1 && t[i] != "" && j > 0) {
				f[j-1].value = f[j-1].value + '&' + t[i]; 
			} else if(x.length == 2) {
				f[j] = {
					key : x[0],
					value : x[1]
				};
				j++;
			} else if(x.length == 3) { // CASE refine=test+tokenizer=<EMAIL> => t=['refine', 'test+tokenizer', '<EMAIL>']
				f[j] = {
					key : x[0],
					value : x[1] + '=' + x[2]
				};
				j++;
			}

		}
		return f;
	};
	
	this.init = function(){

		this.options = $.extend({}, defaults, options);
		this.widget = $('.wuid.' + uCssId);
		if (this.widget.length === 0){
			throw new Error('HideShowTabs : no widget found with uCssid ' + uCssId);
		}
		this.updateLists();
		
		this.onClick(this.options.defaultTab, false).apply(this);

		this.initShowHiddenTabEvent();

		this.initActivateTabEvent();
	};

	/*  some tabs are hidden from user. Here is the method that hide/show these tabs based on
	    'plma:show-tab' & 'plma:hide-tab' event trigger. */
	this.initShowHiddenTabEvent = function(){

        this.widget.find(".tab").on("plma:show-tab", function(e){
			var $e = $(e.target);
			// $e.removeClass("hidden");
			$e.css("display","inline");
		});

        this.widget.find(".tab").on("plma:hide-tab", function(e){
            var $e = $(e.target);
            // $e.addClass("hidden");
			$e.css("display","none");
        });
	};

	/* set a tab active using 'plma:activate-tab' event */
	this.initActivateTabEvent = function(){
        this.widget.find(".tab").on("plma:activate-tab", $.proxy(function(e){
            var $e = $(e.target);
            var tabClassCss = $e.attr("class").split(" ")[2];
			this.widget.find(".content-wrapper > div").addClass("hideShowTabs-hidden");
            this.widget.find("."+tabClassCss).removeClass("hideShowTabs-hidden");
            this.widget.find(".tab.active").removeClass("active");
            $e.addClass("active");
        },this));
	};
	
	
	this.activate = function(i){
		this.tabs.removeClass('active');
		this.getTab(i).addClass('active');
	};
	
	this.isActive = function(i){
		return this.getTab(i).hasClass('active');
	};
	
	this.getTab = ($.proxy(function(){
		var tabs = {};
		return $.proxy(function(i){
			if (!tabs[i]){
				tabs[i] = $(this.tabs.get(i));
			}
			return tabs[i];
		}, this);
	}, this))();
	
	this.onClick = function(tabIndex, fromClick){
		var toShow = this.showElements[tabIndex];
		var all = this.allElements;
		var index = tabIndex;
		return $.proxy(function(){
			if (!this.isActive(index)){
				this.updateLists();
				toShow = this.showElements[tabIndex];
				all = this.allElements;
				this.activate(index);
//				hide(all);
				show(toShow,all,this.options, fromClick);
			} 
		}, this);
	};
	
	this.updateLists = function(){
		this.content = this.widget.find('.content');
		
		this.tabs = this.widget.find('.tab');
		this.showElements = [];
		this.allElements = this.content.find(this.options.showSelectors.join(', '));
		this.allElements.addClass('hideShowTabs-element');
		
		for (var i=0; i<this.options.showSelectors.length; i++){
			this.showElements.push(this.content.find(this.options.showSelectors[i]));
		}
		
		for (var i=0; i<this.tabs.length; i++){
			if (this.showElements.length > i){
				this.tabs.filter('.tab_' + i).on('click', this.onClick(i, true));
			}
		}
	};
	
	this.init();
	
};