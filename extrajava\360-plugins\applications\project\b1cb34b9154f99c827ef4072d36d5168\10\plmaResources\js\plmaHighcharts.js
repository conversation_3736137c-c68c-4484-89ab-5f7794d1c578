(function(window) {
	'use strict';
	
	/**
	 * Resizes a single chart.
	 */
	function updateChart(chart, animation) {
		if(chart){
			var height = chart.renderTo.clientHeight; 
			var width = chart.renderTo.clientWidth; 
			if(height != 0 && width !=0 && (height != chart.chartHeight || width != chart.chartWidth)){
				chart.setSize(width, height, animation);
			}
			if(chart.series.length>1){
				chart.xAxis[0].crosshair = true;
			}
		}
	}
	
	/**
	 * Resizes all charts in the page.
	 */
	function updateAllCharts(timeoutDuration, chartIndex) {
		if(timeoutDuration == undefined){
			timeoutDuration = 200;
		}
		if(timeoutDuration == 0){ // Immediate update
			if(chartIndex != undefined && Highcharts.charts.length > chartIndex){
				updateChart(Highcharts.charts[chartIndex], false);
			}else{
				$(Highcharts.charts).each(function(i,chart){
					updateChart(chart, false);
				});
			}
		}else{
			setTimeout(function(){
				if(chartIndex != undefined && Highcharts.charts.length > chartIndex){
					updateChart(Highcharts.charts[chartIndex], true);
				}else{
					$(Highcharts.charts).each(function(i,chart){
						updateChart(chart, true);
					});
				}
			}, timeoutDuration);
		}
	}

	/**
	 * Resizes all charts in the given element. To use cautiously,
	 * not very efficient.
	 */
	function updateChartsIn($element) {
		$element.find('[data-highcharts-chart]').each(function(i,chartContainer){
			updateChart($(chartContainer).highcharts(), true);
		});
	}


	/** Echoes window resize events to let highCharts resize
	 * charts after animation */
//	if (typeof highCharts !== 'undefined'){
//		var echo = true;
//		var $window = $(window);
//		$window.on('resize', function(){
//			if (echo){
//				setTimeout(function(){
//					echo = false;
//					$window.resize();
//					setTimeout(function(){
//						echo = true;
//					}, 400);
//				}, 400);
//			}
//		});
//	}

	$(window)
		.on('resize', updateAllCharts)
		.on('plma:resize', function(e,timeoutDuration, chartIndex){
			updateAllCharts(timeoutDuration, chartIndex);
		});
	
})(window);