truncate-tag-show = [Read more]
truncate-tag-hide = [Hide]
more-like-this = More like this
empty-list = This list does not contain any data to display.

hitcustom.metalist=Meta list
hitcustom.facetList=Facet list
hitcustom.idList=Id
hitcustom.similarList=Similar queries

hitcustom.title={0} results
hitcustom.no_result=No result
hitcustom.sort=Sort by
hitcustom.sort_asc=Ascending sort
hitcustom.sort_dsc=Descending sort

hitcustom.tiles_view=Tiles view
hitcustom.collapsed_view=Collapsed view
hitcustom.expanded_view=Expanded view

hitcustom.meta.day=J
hitcustom.meta.hour=H
hitcustom.meta.minute=m
hitcustom.meta.second=s

hitcustom.column.label=Title
hitcustom.column.content=Content
hitcustom.column.type=Type
hitcustom.column.description.display=Display Description
hitcustom.column.description.hide=Hide Description

plma.resultlist.view.default=Show List View
plma.resultlist.view.gridtable=Show Table View
plma.resultlist.view.gridtable.rows=Show Hits as table rows
plma.resultlist.view.gridtable.columns=Show Hits as table columns
plma.resultlist.view.gridtable.pin=Pin Row
plma.resultlist.view.gridtable.pincol=Pin Column
plma.resultlist.view.gridtable.unpin=Unpin Row
plma.resultlist.view.gridtable.unpincol=Unpin Column
plma.resultlist.tab.title=Configure ResultList Display
plma.resultlist.compare.hidesimilarcols.hide=Hide Fully matched
plma.resultlist.compare.hidesimilarcols.show=Show Fully matched

plma.resultlist.tab.label=ResultLists
plma.resultlist.tab.description=Configure metas to show/hide and order of the display for each result list configuration.
plma.resultlist.button.selectall=Select All
plma.resultlist.button.deselectall=Deselect All
plma.resultlist.column.state.false=Hide
plma.resultlist.column.state.ifnotempty=Show(if Not Empty)
plma.resultlist.column.state.true=Show
plma.resultlist.save=Save
plma.resultlist.reset=Reset
plma.resultList.export=Export to CSV
plma.resultList.exportNotification=The CSV file will contain first {limit} items.
plma.resultList.exportNotification.allResults=The CSV file will contain {limit} items.