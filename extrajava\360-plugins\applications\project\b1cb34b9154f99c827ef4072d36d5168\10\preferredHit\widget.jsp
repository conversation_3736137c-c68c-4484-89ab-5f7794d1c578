<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import varWidget="widget" varFeeds="feeds"/>
<search:getEntry var="entry" feeds="${feeds}"/>

<config:getOption name="label" var="label" defaultValue="Add to favorites"/>
<config:getOption name="showLabel" var="showLabel"/>
<config:getOption name="collection" var="collection"/>
<config:getOption name="counterSelector" var="counterSelector" defaultValue=".preferred-hits"/>
<config:getOption name="hitID" var="hitID" defaultValue="${entry.metas['id']}"/>

<string:eval string="${hitID}" feeds="${feeds}" entry="${entry}" var="idValue"/>

<widget:widget varUcssId="uCssId" varCssId="cssId" disableStyles="true">
   <render:template template="templates/preferredHit.jsp" widget="preferredHit">
       <render:parameter name="feeds" value="${feeds}"/>
       <render:parameter name="entry" value="${entry}"/>
       <render:parameter name="label" value="${label}"/>
       <render:parameter name="showLabel" value="${showLabel}"/>
       <render:parameter name="collection" value="${collection}"/>
       <render:parameter name="counterSelector" value="${counterSelector}"/>
       <render:parameter name="hitID" value="${idValue}"/>
       <render:parameter name="buttonPrefix" value="favorite"/>
   </render:template>
</widget:widget>