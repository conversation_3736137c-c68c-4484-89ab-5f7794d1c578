<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Page footer" group="PLM Analytics/Layout/Structure" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget displays the logo and copyright of the application.</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/plmaFooter/images/preview.PNG" alt="PLMA Footer" />
        ]]>
	</Preview>
	<Dependencies>
		<Widget name="plmaResources"></Widget>
	</Dependencies>
	
	<Includes>
		<Include type="css" path="css/style.less" />
	</Includes>
	
	<SupportWidgetsId arity="ZERO"/>
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportI18N supported="true"/>
	
	<OptionsGroup name="General">
		<Option id="copyright" name="Copyright" isEvaluated="true"></Option>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue id="copyright">Dassault Systèmes</DefaultValue>
	</DefaultValues>

</Widget>
