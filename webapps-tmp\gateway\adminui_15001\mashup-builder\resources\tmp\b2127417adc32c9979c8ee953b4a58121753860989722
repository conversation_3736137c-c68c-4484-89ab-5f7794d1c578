

/**
 * File: /resources/mashupBuilder/js/Page.js
 */
/*
 * Mashup_Page.js
 */

/*
 * Constructor
 */
function Mashup_Page(jsonPageFeed, jsonMashupPage) {
	this.pageName = jsonMashupPage.id || jsonPageFeed.id || '';
	this.pageAPI = new Mashup_Page_API(this, jsonPageFeed);
	this.pageUI = new Mashup_Page_UI(this, jsonMashupPage);
	this.pageSettings = new Mashup_Page_Settings(this);
	this.pageCode = new Mashup_Page_Code(this);
	this.pagePreview = new Mashup_Page_Preview(this);

	this.errorsCount = 0;

	this.messages = new Mashup_Control_Messages({
		onUpdateCallbackData: { _this : this },
		onUpdateCallback: function(context) {
			if (context.data._this.isOpen()) {
				context.data._this.onResize();
			}
		}
	});
}

/**
 * Returns the name of the page
 *
 * @returns {string}
 */
Mashup_Page.prototype.getName = function() {
	return this.pageName;
};

/**
 * Returns the order of the page
 */
Mashup_Page.prototype.getOrder = function() {
	var order = window.mashupBuilder.state.get('m', 'o', []);
	if ((pos = order.indexOf(this.getName())) != -1) {
		return pos;
	}
	return this.getName();
};

/**
 * Returns the messages handler for this page
 *
 * @returns {Mashup_Control_Messages}
 */
Mashup_Page.prototype.getMessages = function() {
	return this.messages;
};

/**
 * Returns the errors count of the page
 *
 * @returns
 */
Mashup_Page.prototype.getErrorsCount = function() {
	return this.errorsCount;
};

/**
 * Sets the new page ID
 *
 * @param newId
 */
Mashup_Page.prototype.setId = function(newId) {
	this.id = newId;
	this.getAPI().setId(newId);
	this.getUI().setId(newId);
};

/**
 * Sets whether the page should be secured or not
 *
 * @param isSecurityEnabled
 */
Mashup_Page.prototype.setIsSecurityEnable = function(isSecurityEnabled) {
	getJsonParameter(this.getUI().json.parameters, 'enableSecurity', true).value = isSecurityEnabled ? 'true' : 'false';
	this.showSecurityIcon(isSecurityEnabled);
};

/**
 * Returns whether the security is enabled on this page
 *
 * @returns {Boolean}
 */
Mashup_Page.prototype.isSecurityEnable = function() {
	return getJsonParameter(this.getUI().json.parameters, 'enableSecurity', true).value == 'true';
};

/**
 * Returns whether the page is up to date or not
 * 
 * @returns {Boolean}
 */
Mashup_Page.prototype.isUpToDate = function() {
	return this.getUI().isUpToDate() && this.getAPI().isUpToDate();
};

/**
 * Returns whether the page is currently opened or not
 *
 * @returns {Boolean}
 */
Mashup_Page.prototype.isOpen = function() {
	return ((openedPage = window.mashupBuilder.getOpenedPage()) != null && (openedPage.getName() == this.getName()));
};

/**
 * Returns whether the page is visible or not
 *
 * @returns {Boolean}
 */
Mashup_Page.prototype.isVisible = function() {
	if (this.isPageVisible == null) {
		this.isPageVisible = window.mashupBuilder.permission.canViewPage(this.getName());
	}
	return this.isPageVisible;
};

/**
 * Returns whether the page is readonly or not
 *
 * @returns {Boolean}
 */
Mashup_Page.prototype.isReadOnly = function() {
	if (this.isPageReadOnly == null) {
		this.isPageReadOnly = !window.mashupBuilder.permission.canUpdatePage(this.getName());
	}
	return this.isPageReadOnly;
};

/**
 * Returns whether the page is updated
 *
 * @returns {Boolean}
 */
Mashup_Page.prototype.isUpdated = function() {
	return this.getAPI().isUpdated() || this.getUI().isUpdated();
};

/**
 * Returns whether this page is the Home Page
 *
 * @returns {Boolean}
 */
Mashup_Page.prototype.isHomePage = function() {
	if (window.mashupBuilder != null) {
		return (window.mashupBuilder.getDefaultHomePage() == this.getName());
	}
	return false;
};

/**
 * Returns whether this page is the login page
 */
Mashup_Page.prototype.isLoginPage = function() {
	return this.getName() == window.defaultLoginPage && window.mashupBuilder.security.hasSecurityProvider();
};

/**
 * Returns the Mashup API for this page
 *
 * @returns {Mashup_Page_API}
 */
Mashup_Page.prototype.getAPI = function() {
	return this.pageAPI;
};

/**
 * Returns the Mashup UI for this page
 *
 * @returns {Mashup_Page_UI}
 */
Mashup_Page.prototype.getUI = function() {
	return this.pageUI;
};

/**
 * Returns the Mashup Settings for this page
 *
 * @returns {Mashup_Page_Settings}
 */
Mashup_Page.prototype.getSettings = function() {
	return this.pageSettings;
};

/**
 * Returns the Mashup Code for this page
 *
 * @returns {Mashup_Page_Code}
 */
Mashup_Page.prototype.getCode = function() {
	return this.pageCode;
};

/**
 * Returns the Mashup Preview for this page
 *
 * @returns {Mashup_Page_Preview}
 */
Mashup_Page.prototype.getPreview = function() {
	return this.pagePreview;
};

/**
 * Returns a Mashup Tab class
 *
 * @param tabName
 * @returns
 */
Mashup_Page.prototype.getTab = function(tabName) {
	switch (tabName) {
	case 'ui':
		return this.pageUI;
	case 'api':
		return this.pageAPI;
	case 'code':
		return this.pageCode;
	case 'settings':
		return this.pageSettings;
	case 'preview':
		return this.pagePreview;
	default:
		return null;
	}
};

/**
 * Returns the name of the opened tab
 *
 * @returns
 */
Mashup_Page.prototype.getOpenedTabName = function() {
	return this.openedTabName;
};

/**
 * Returns the default tab name for this page
 *
 * @returns
 */
Mashup_Page.prototype.getDefaultTabName = function() {
	if (this.getUI().isVisible()) {
		return 'ui';
	} else if (this.getAPI().isVisible()) {
		return 'api';
	}
	return null;
};

/**
 * Called when the window is resized
 */
Mashup_Page.prototype.onResize = function() {
	var msgHeight = this.getMessages().getHeight();

	this.width = window.mashupBuilder.width - 270; // left(270)
	this.height = window.mashupBuilder.height - msgHeight;

	this.getEl().css({
		width: this.width,
		height: this.height,
		paddingTop: msgHeight
	});

	this.getMessages().getEl().css('width', window.mashupBuilder.width - 290); // left(270) scrollbar(15) padding-right(5)

	this.getAPI().onResize();
	this.getUI().onResize();
	this.getSettings().onResize();
	this.getCode().onResize();
	this.getPreview().onResize();
};

/**
 * Updates the error count for the Page
 *
 * @param {number} errorsCount Can be positive (more errors) or negative (less errors)
 */
Mashup_Page.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;
	if (this.errorsCount == 0) {
		this.getElTopTabsErrors().css('display', 'none');
	} else {
		this.getElTopTabsErrors().css('display', 'inline-block').html(this.errorsCount);
	}
};

/**
 * Updates the UI
 *
 * @param jsonMashupPage
 */
Mashup_Page.prototype.updateUI = function(jsonMashupPage) {
	this.getUI().json = jsonMashupPage;
	this.getUI().redraw();
	this.getSettings().redraw();
	this.getCode().redraw();
};

/**
 * Updates the API
 *
 * @param jsonPageFeed
 */
Mashup_Page.prototype.updateAPI = function(jsonPageFeed) {
	this.getAPI().json = jsonPageFeed;
	this.getAPI().redraw();
	this.getUI().redrawElUseFeeds();
	window.mashupBuilder.getToolboxContainer().redrawToolboxes(['pageParameters']);
};

/**
 * Updates the displayed messages to the user
 */
Mashup_Page.prototype.updateUserMessages = function() {
	if (!window.mashupBuilder.concurrency.isAppUIUpToDate() || !window.mashupBuilder.concurrency.isAppAPIUpToDate()) {
		this.getMessages().setMessage('disabled-app', {
			level : 'error',
			canClose : false,
			message : _.CONCURRENCY_APPLICATION_NOT_UPTODATE()
		});
	} else {
		this.getMessages().removeMessage('disabled-app');
	}
};

Mashup_Page.prototype.copyTo = function(pageName, resetUIDS) {
	/* MashupAPI */
	var strMashupAPI = $.toJSON(this.getAPI().getJson());
	if (resetUIDS) {
		strMashupAPI = removeFuids(strMashupAPI);
	}

	var jsonMashupAPI = $.evalJSON(strMashupAPI);
	jsonMashupAPI.id = pageName;
	jsonMashupAPI.lastModifiedDate = -1;
	jsonMashupAPI.appLastModifiedDate = -1;

	/* MashupUI */
	var strMashupUI = $.toJSON(this.getUI().getJson());
	if (resetUIDS) {
		strMashupUI = removeWuids(strMashupUI);
	}

	var jsonMashupUI = $.evalJSON(strMashupUI);
	jsonMashupUI.id = pageName;
	jsonMashupUI.lastModifiedDate = -1;
	jsonMashupUI.appLastModifiedDate = -1;

	var newPageClass = window.mashupBuilder.registerPageClass(jsonMashupAPI, jsonMashupUI);
	newPageClass.getUI().onUpdate();
	newPageClass.getAPI().onUpdate();

	window.mashupBuilder.state.setById(newPageClass.getName(), window.mashupBuilder.state.getById(this.getName()));

	return newPageClass;
};

/*
* Dom Manipulation
*/
Mashup_Page.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
			'<div id="page-wrapper">' +
			'</div>'
		);
		this.el.data('_this', this);

		this.el.append(this.getMessages().getEl());

		if (this.getAPI().isVisible()) {
			this.getAPI().getEl().appendTo(this.el);
			this.getAPI().getEl().addClass('moveToMoon');
		}

		if (this.getUI().isVisible()) {
			this.getUI().getEl().appendTo(this.el);
			this.getUI().getEl().addClass('moveToMoon');
		}

		if (this.getCode().isVisible()) {
			this.getCode().getEl().appendTo(this.el);
			this.getCode().getEl().addClass('moveToMoon');
		}

		if (this.getSettings().isVisible()) {
			this.getSettings().getEl().appendTo(this.el);
			this.getSettings().getEl().addClass('moveToMoon');
		}

		this.getPreview().getEl().appendTo(this.el);
		this.getPreview().getEl().addClass('moveToMoon');

		this.onResize();
	}
	return this.el;
};

/**
 * Called when the page is opened
 */
Mashup_Page.prototype.onOpen = function() {
	// appends tabs to the DOM and flag page as selected
	this.appendBottomTabs(window.mashup.navigation.getElTabs());
	this.getElTopTabs().addClass('selected');

	// update user messages
	window.mashupBuilder.updateUserMessages();
	this.updateUserMessages();

	// reload page-dependent components
	window.mashupBuilder.getToolboxContainer().redrawToolboxes(['pageParameters', 'previewParameters', 'pageLayout']);

	// opens the default tab according to the current anchor
	var anchor = window.mashupBuilder.parseAnchor(),
		pageName = anchor[0], tabName = anchor[1];

	if (pageName == 'APPLICATION') {
		this.openTab(this.getDefaultTabName());
	} else if (tabName == 'ui' || tabName == 'api' || tabName == 'code' || tabName == 'preview') {
		this.openTab(tabName);
	} else if (tabName == 'styles' || tabName == 'javascript') {
		this.openTab('code');
	} else {
		this.openTab('settings');
	}

	// update the table markers position
	if (window.mashupBuilder.getViewMode() == 'edit') {
		this.getUI().getLayout().getEditLayout().onUpdatePageLayout();
	}

	// reloads the current context for this page
	window.mashupBuilder.context.reload({ forceNow: true });
};

/**
 * Called when this page is closed
 */
Mashup_Page.prototype.onClose = function() {
	// re-create the page as we do not want to keep any cache
	// in case the page state were updated while closed
	var jsonMashupAPI = this.getAPI().getJson();
	var jsonMashupUI = this.getUI().getJson();
	window.mashupBuilder.removePageClass(this);
	window.mashupBuilder.registerPageClass(jsonMashupAPI, jsonMashupUI);
};

/**
 * Returns whether this page is linked to a Mashup UI or not
 *
 * @returns {Boolean}
 */
Mashup_Page.prototype.hasUI = function() {
	return true;
};

/**
 * Returns whether this page is linked to a Mashup API or not
 *
 * @returns {Boolean}
 */
Mashup_Page.prototype.hasAPI = function() {
	return true;
};

/**
 *
 * @param tabName
 * @returns the focused tabName. null otherwise
 */
Mashup_Page.prototype.openTab = function(tabName) {
	var tab = this.getTab(tabName);

	// fallback to default tab if none given or unable to access it
	if (tab == null || (this.openedTabName == null && tab.isVisible() == false)) {
		tabName = this.getDefaultTabName(),
		tab = this.getTab(tabName);
	}

	// if invalid given tab then skip
	if (tab.isVisible() == false || tab.canOpen() == false) {
		return null;
	}

	// if already opened then trigger onOpen and skip
	if (this.openedTabName == tabName) {
		tab.onOpen();
		return tabName;
	}

	// close the current bottomtoolbar
	window.mashupBuilder.removeOpenedBottomToolbar();

	// switch tab state
	if ((openedTab = this.getTab(this.openedTabName)) != null) {
		openedTab.getElBottomTabs().removeClass('selected');
		openedTab.getEl().addClass('moveToMoon');
		openedTab.onClose();
	}
	tab.onOpen();
	tab.getElBottomTabs().addClass('selected');
	tab.getEl().removeClass('moveToMoon');

	// refresh toolbox
	window.mashupBuilder.getToolboxContainer().onOpenPageTab(tabName);

	this.openedTabName = tabName;
	return tabName;
};

/**
 * Returns the DOM element for the top tab of this page
 *
 * @returns
 */
Mashup_Page.prototype.getElTopTabs = function() {
	if (this.elTopTabs == null) {
		this.elTopTabs = $('' +
			'<li class="list-items-page" name="doOpenTab">' +
				'<span class="icon icon-user" title="' + _.PAGE_TAB_SOMEONE_VIEWING() + '" name="doOpenWorkingUsers" data-pageName="' + this.getName() + '"></span>' +
				'<span class="icon icon-users" title="' + _.PAGE_TAB_SOMEONES_VIEWING() + '" name="doOpenWorkingUsers" data-pageName="' + this.getName() + '"></span>' +
				'<span class="icon icon-user-edit" title="' + _.PAGE_TAB_SOMEONE_EDITING() + '" name="doOpenWorkingUsers" data-pageName="' + this.getName() + '"></span>' +
				'<span class="icon icon-lock" title="' + _.PAGE_TAB_SECURITY_ENABLED() + '" name="doOpenSecurity"></span>' +
				'<span class="icon icon-homepage" title="' + _.PAGE_TAB_IS_HOMEPAGE() + '" name="doOpenApplication"></span>' +
				'<span class="page-name-wrapper" name="doOpenTab">/<span class="page-name" name="doOpenTab">' + this.getName() + '</span></span> ' +
				'<span class="icon icon-outdated" title="' + _.PAGE_TAB_NOT_UPTODATE() + '"></span>' +
				'<span class="icon icon-error" name="doOpenError" title="' + _.MB_ERROR_CLICK_TO_SEE() + '">' + this.errorsCount + '</span>' +
			'</li>'
		);
		this.showSecurityIcon(this.isSecurityEnable());
		this.showHomePageIcon(this.isHomePage());
	}
	return this.elTopTabs;
};

Mashup_Page.prototype.showSecurityIcon = function(show) {
	this.getElTopTabs().find('.icon-lock').css('display', show ? 'inline-block' : 'none');
};

Mashup_Page.prototype.showHomePageIcon = function(show) {
	this.getElTopTabs().find('.icon-homepage').css('display', show ? 'inline-block' : 'none');
};

Mashup_Page.prototype.showWorkingUserIcon = function(nbUsersViewing, nbUsersEditing) {
	this.getElTopTabs().find('.icon-user').css('display', nbUsersViewing == 1 ? 'inline-block' : 'none');
	this.getElTopTabs().find('.icon-users').css('display', nbUsersViewing > 1 ? 'inline-block' : 'none');
	this.getElTopTabs().find('.icon-user-edit').css('display', nbUsersEditing > 0 ? 'inline-block' : 'none');
};

Mashup_Page.prototype.showNotUpToDate = function(show) {
	this.getElTopTabs().find('.icon-outdated').css('display', show ? 'inline-block' : 'none');
};

Mashup_Page.prototype.getElTopTabsErrors = function() {
	if (this.elErrors == null) {
		this.elErrors = this.getElTopTabs().find('.icon-error');
	}
	return this.elErrors;
};

Mashup_Page.prototype.appendBottomTabs = function(container) {
	if (this.getAPI().isVisible()) {
		this.getAPI().getElBottomTabs().appendTo(container);
	}
	if (this.getUI().isVisible()) {
		this.getUI().getElBottomTabs().appendTo(container);
	}
	if (this.getCode().isVisible()) {
		this.getCode().getElBottomTabs().appendTo(container);
	}
	this.getPreview().getElBottomTabs().appendTo(container);
	if (this.getPreview().isVisible() == false) {
		this.getPreview().getElBottomTabs().hide();
	}
};

/**
 * Returns a key's value for this page
 *
 * @param key
 * @param defaultValue
 * @returns
 */
Mashup_Page.prototype.getState = function(key, defaultValue) {
	return window.mashupBuilder.state.get(this.getName(), key, defaultValue);
};

/**
 * Saves a key/values for this page
 *
 * @param key
 * @param value
 */
Mashup_Page.prototype.saveState = function(key, value) {
	window.mashupBuilder.state.save(this.getName(), key, value);
};

/**
 * Removes this page from the DOM
 */
Mashup_Page.prototype.remove = function() {
	this.getAPI().remove();
	this.getUI().remove();
	this.getSettings().remove();
	this.getPreview().remove();
	this.getCode().remove();
	this.getMessages().remove();

	if (this.el != null) {
		this.el.remove();
		this.el = undefined;
	}
	if (this.elTopTabs != null) {
		this.elTopTabs.remove();
		this.elTopTabs = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Page/Layout/Trigger.js
 */
/**
 * Enum for the different type of triggers
 */
var TRIGGER_TYPES = {
	// Page triggers
	MashupPageTrigger: 'com.exalead.cv360.searchui.triggers.MashupPageTrigger',
	PreRequestTrigger: 'com.exalead.cv360.searchui.triggers.PreRequestTrigger',

	// Widget triggers
	MashupWidgetTrigger: 'com.exalead.cv360.searchui.triggers.MashupWidgetTrigger',
	ParameterAdapterWidgetTrigger: 'com.exalead.cv360.searchui.triggers.ParameterAdapterWidgetTrigger',

	// Feed triggers
	FeedTrigger: 'com.exalead.access.feedapi.FeedTrigger',
	PageFeedTrigger: 'com.exalead.access.feedapi.PageFeedTrigger', 

	// Generic design triggers (should not be used as we cannot filter properly)
	MashupTrigger: 'com.exalead.cv360.searchui.triggers.MashupTrigger'
};

/**
 * Implementation of a trigger
 *
 * @param type
 * @param parentTriggerContainer
 * @param jsonTrigger
 * @returns
 */
function Mashup_Page_Layout_Trigger(type, parentTriggerContainer, jsonTrigger) {
	this.type = type;
	this.parentTriggerContainer = parentTriggerContainer;
	this.json = jsonTrigger;
	this.errorsCount = 0;
	this.triggerDefinition = null;
}

/**
 * Returns the ID (className)
 *
 * @returns
 */
Mashup_Page_Layout_Trigger.prototype.getId = function() {
	return this.getClassName();
};


/**
 * Returns the class name
 *
 * @returns
 */
Mashup_Page_Layout_Trigger.prototype.getClassName = function() {
	return this.json.className;
};

/**
 * Sets the trigger annotation
 * 
 * @param annotation
 */
Mashup_Page_Layout_Trigger.prototype.setAnnotation = function(annotation) {
	if (annotation == '') {
		annotation = null;
	}
	this.json.annotation = annotation;
	this.getElHeader().find('.trigger-name').html(this.getDisplayName());
};

/**
 * Returns the display name
 *
 * @returns
 */
Mashup_Page_Layout_Trigger.prototype.getDisplayName = function() {
	if (this.json.annotation != null && this.json.annotation != '') {
		return this.json.annotation.escapeHTML() + ' - ' + this.getTriggerDefinition().name;
	} else {
		return this.getTriggerDefinition().name;
	}
};

/**
 * Returns the description
 */
Mashup_Page_Layout_Trigger.prototype.getDescription = function() {
	return this.getTriggerDefinition().description;
};

/**
 * Returns the type of the trigger (feedTrigger/designTrigger)
 *
 * @returns
 */
Mashup_Page_Layout_Trigger.prototype.getType = function() {
	return this.type;
};

/**
 * Returns the interface of the trigger
 */
Mashup_Page_Layout_Trigger.prototype.getInterface = function() {
	return this.getTriggerDefinition().type;
};

/**
 * Returns whether the trigger has a valid definition or not
 */
Mashup_Page_Layout_Trigger.prototype.hasDefinition = function() {
	return this.getTriggerDefinition().isValid == undefined;
};

/**
 * Returns whether this trigger is supported by its parent
 */
Mashup_Page_Layout_Trigger.prototype.isSupported = function() {
	if (this.getParent().constructor != MashupBuilder) {
		var parent = this.getParent().constructor;
		if (getComponentType(this.getParent()) == 'ui') {
			if (isWidget(parent)) {
				return this.getInterface() == TRIGGER_TYPES.MashupWidgetTrigger || this.getInterface() == TRIGGER_TYPES.ParameterAdapterWidgetTrigger;
			} else if (parent == Mashup_Page_UI || parent == MashupBuilder) {
				return this.getInterface() == TRIGGER_TYPES.PreRequestTrigger || this.getInterface() == TRIGGER_TYPES.MashupPageTrigger || this.getInterface() == TRIGGER_TYPES.MashupTrigger;
			}
			return false;
		} else {
			if (parent == Mashup_Page_API_Layout_Feed) {
				return this.getInterface() == TRIGGER_TYPES.FeedTrigger;
			} else if (parent == Mashup_Page_API || parent == MashupBuilder) {
				return true; // both feedTrigger & pageFeedTrigger are OK
			}
		}
	}
	return true;
};

/**
 * Returns whether this trigger is valid or not
 *
 * @returns {Boolean}
 */
Mashup_Page_Layout_Trigger.prototype.isValid = function() {
	return this.hasDefinition() && this.isSupported();
};

/**
 * Returns whether this trigger is a dependency or not
 */
Mashup_Page_Layout_Trigger.prototype.isDependency = function() {
	return this.getDependentComponents().length > 0;
};

/**
 * Returns the components dependant for this trigger
 */
Mashup_Page_Layout_Trigger.prototype.getDependentComponents = function() {
	return getWidgetDependenciesForTrigger(this.getClassName());
};

/**
 * Returns whether the widget is enabled or not
 *
 * @returns
 */
Mashup_Page_Layout_Trigger.prototype.isEnabled = function() {
	return this.json.enable;
};

/**
 * Returns whether this trigger is readonly or not
 *
 * @returns {Boolean}
 */
Mashup_Page_Layout_Trigger.prototype.isReadOnly = function() {
	return this.getParent().isReadOnly();
};

/**
 * Returns the trigger definition
 */
Mashup_Page_Layout_Trigger.prototype.getTriggerDefinition = function() {
	if (this.triggerDefinition == undefined) {
		this.triggerDefinition = getTriggerDefinition(this.getClassName());
		if (this.triggerDefinition == null) {
			this.triggerDefinition = {
				className: this.getClassName(),
				name: this.getClassName().substr(this.getClassName().lastIndexOf('.') + 1),
				description: '',
				isValid: false
			};
		}
	}
	return this.triggerDefinition;
};

/**
 * Returns the parent of this trigger
 *
 * @returns
 */
Mashup_Page_Layout_Trigger.prototype.getParent = function() {
	return this.getParentTriggerContainer().getParent();
};

/**
 * Sets the trigger container that contains this trigger
 *
 * @returns
 */
Mashup_Page_Layout_Trigger.prototype.setParentTriggerContainer = function(parentTriggerContainer) {
	this.parentTriggerContainer = parentTriggerContainer;
};

/**
 * Returns the trigger container that contains this trigger
 *
 * @returns {Mashup_Page_Layout_TriggerContainer}
 */
Mashup_Page_Layout_Trigger.prototype.getParentTriggerContainer = function() {
	return this.parentTriggerContainer;
};

/**
 * Called when the trigger has been updated
 */
Mashup_Page_Layout_Trigger.prototype.onUpdate = function() {
	this.getParent().onUpdateTrigger(this);
};

/**
 * jQuery onClick event handler
 *
 * @param e
 * @returns {Boolean}
 */
Mashup_Page_Layout_Trigger.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'doEditTrigger':
			this.doEditTrigger();
			return false;

		case 'doCopyTrigger':
			this.doCopyTrigger();
			return false;

		case 'doAnnotateTrigger':
			this.doAnnotateTrigger();
			return false;

		case 'doRemoveTrigger':
			this.doRemoveTrigger();
			return false;

		case 'doToggleStatus':
			if (this.isEnabled()) {
				this.disable();
			} else {
				this.enable();
			}
			this.getParentTriggerContainer().getParent().onUpdateTrigger(this);
			return false;
		}
	}
	return true;
};

/**
 * User process to edit this trigger
 */
Mashup_Page_Layout_Trigger.prototype.doEditTrigger = function() {
	if (this.bottomToolbar == undefined) {
		// create our content
		var editProperties = new Mashup_Window_TabContent_EditProperties(this, {
			isReadOnly: this.isReadOnly(),
			$errorClassEl: this.getEl().find('.icon-properties'),
			errorClassName: 'error',
			$errorClassElCount: this.getEl().find('.doEditTrigger .icon-error')
		}, Mashup_Parameter_Factory.createTriggerParameters(this.getClassName()));

		// open it as a bottomToolbar
		this.bottomToolbar = new Mashup_BottomToolbar();
		this.bottomToolbar.setContent(editProperties);
	}

	window.mashupBuilder.openBottomToolbar(this.bottomToolbar);
};

/**
 * User process to annotate the trigger
 */
Mashup_Page_Layout_Trigger.prototype.doAnnotateTrigger = function() {
	window.mashupBuilder.removeOpenedBottomToolbar();
	var _this = this;
	var promptBox = new Mashup_Popup_Prompt({
		title: _.TRIGGER_TOOLBAR_ANNOTATE_TITLE(),
		value: (this.json.annotation == null ? '' : this.json.annotation),
		label: _.TRIGGER_TOOLBAR_ANNOTATE_LABEL(),
		onClickOkCallback: function(context, newTriggerName) {
			_this.setAnnotation(newTriggerName);
			_this.getParentTriggerContainer().getParent().onUpdateTrigger(_this);
			this.remove();
		}
	});
	promptBox.show();
};

/**
 * User process to copy this plugin
 */
Mashup_Page_Layout_Trigger.prototype.doCopyTrigger = function() {
	if (this.errorsCount > 0) {
		new Mashup_Popup_Error(this.errorsCount).show();
		return;
	}

	// force saving of opened toolbar
	window.mashupBuilder.removeOpenedBottomToolbar();

	// adds after the copied trigger
	this.getParentTriggerContainer().addNewTrigger(clone(this.getJson()), this.getParentTriggerContainer().indexOf(this) + 1);
};

/**
 * User process to delete this trigger
 */
Mashup_Page_Layout_Trigger.prototype.doRemoveTrigger = function() {
	var _this = this;
	new Mashup_Popup_Confirm({
		title: _.TRIGGER_DELETE_TITLE(),
		text: _.TRIGGER_DELETE(),
		buttonOkLabel: _.YES(),
		buttonCancelLabel: _.NO(),
		onClickOkCallback: function(context) {
			_this.getParentTriggerContainer().removeTrigger(_this);
			this.remove();
			return false;
		}
	}).show();
};

/**
 * Moves the current trigger to the given container
 *
 * @param triggerContainer
 * @param position
 */
Mashup_Page_Layout_Trigger.prototype.moveTo = function(triggerContainer, position) {
	if (this.getParentTriggerContainer() != triggerContainer || position != triggerContainer.indexOf(this)) {
		var previousTriggerContainer = this.getParentTriggerContainer();

		this.getParentTriggerContainer().unRegisterTrigger(this);
		triggerContainer.addTrigger(this, position);

		// validate previous parent trigger container
		if (previousTriggerContainer.getParent() != null) {
			previousTriggerContainer.getParent().onUpdateTrigger(this);
		}
	}
};

/**
 * Updates the error count
 *
 * @param {number}
 *            errorsCount Can be positive (more errors) or negative (less errors)
 */
Mashup_Page_Layout_Trigger.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;
	if (this.errorsCount == 0) {
		this.getEl().removeClass('has-error');
	} else {
		this.getEl().addClass('has-error');
	}

	if (this.getType() == 'designTrigger') {
		if (isWidget(this.getParent())) {
			this.getParent().getPageUI().updateError(errorsCount);
		} else {
			this.getParent().updateError(errorsCount);
		}
	} else if (this.getType() == 'feedTrigger') {
		if (this.getParent().constructor == Mashup_Page_API_Layout_Feed) {
			this.getParent().getPageAPI().updateError(errorsCount);
		} else {
			this.getParent().updateError(errorsCount);
		}
	}
};

/**
 * Returns the DOM element for the trigger
 * @returns
 */
Mashup_Page_Layout_Trigger.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
			'<div class="trigger-wrapper ' + this.type + ' ' + this.type + '-draggable ' + this.type + '-droppable">' +
				'<div class="trigger">' +
				'</div>' +
				'<div class="drop-zone hidden-drop-zone ' + this.type + '-drop-zone ' + this.type + '-droppable">' +
				'</div>' +
			'</div>'
		);
		this.el.data('_this', this);
		this.el.find('> .trigger').append(this.getElHeader());

		// is the trigger valid?
		if (!this.hasDefinition()) {
			this.disable(_.TRIGGER_ERROR_UNKNOWN_CLASSNAME(this.getClassName()));
		} else if (!this.isSupported()) {
			this.disable(_.TRIGGER_NOT_COMPATIBLE());
		} else if (!this.getParentTriggerContainer().isUpToDate()) {
			this.disable(_.TRIGGER_NOT_UPTODATE());
		} else if (!this.isEnabled()) {
			this.disable();
		}

		this.updateMenuHTML();

		// attach events
		this.el.on('click', $.proxy(this.handleEventOnClick, this));
		this.attachDraggable();
	}
	return this.el;
};

/**
 * Returns the DOM node for this trigger header
 *
 * @returns
 */
Mashup_Page_Layout_Trigger.prototype.getElHeader = function() {
	if (this.elHeader == undefined) {
		this.elHeader = $('' +
		'<div class="trigger-header" name="doEditTrigger">' +
			'<table class="trigger-header-table">' +
				'<tbody>' +
					'<tr>' +
						'<td class="trigger-icon-wrapper"><span class="icon icon-trigger"></span></td>' +
						'<td><div class="trigger-name" name="doEditTrigger" title="' + this.getDisplayName() + '">' + this.getDisplayName() + '</div></td>' +
						'<td class="trigger-header-icons">' +
							(!this.isReadOnly() ?
								'<span name="doAnnotateTrigger" class="icon icon-annotate" title="' + _.TRIGGER_ANNOTATE() + '"></span>' : ''
							) +
							'<span class="has-popup-menu icon icon-properties">' +
								'<ul class="menu-trigger popup-menu popup-menu-right has-popup-icons"></ul>' +
							'</span>' +
							'<span class="icon trigger-move-handle" title="' + _.TRIGGER_MOVE() + '"></span>' +
						'</td>' +
					'</tr>' +
				'</tbody>' +
			'</table>' +
		'</div>');
	}
	return this.elHeader;
};

/**
 * Updates the trigger menu HTML according to its actual state
 *
 * @returns
 */
Mashup_Page_Layout_Trigger.prototype.updateMenuHTML = function() {
	var sb = new StringBuilder();
	sb.append('<li name="doEditTrigger" class="doEditTrigger popup-menu-item icon-properties"><span class="icon icon-menu"></span>' + _.TRIGGER_MENU_EDIT_PROPERTIES() + ' <span class="icon icon-error" name="doEditTrigger"></span></li>');	if (!this.isReadOnly()) {
		sb.append('<li name="doAnnotateTrigger" class="doAnnotateTrigger popup-menu-item icon-annotate"><span class="icon icon-menu"></span>' + _.TRIGGER_MENU_ANNOTATE_TITLE() + '</li>');
		if (this.isEnabled()) {
			sb.append('<li name="doToggleStatus" class="popup-menu-item icon-visibility"><span class="icon icon-menu"></span>' + _.TRIGGER_MENU_DISABLE() +  '</li>');
		} else {
			sb.append('<li name="doToggleStatus" class="popup-menu-item icon-visibility"><span class="icon icon-menu"></span>' + _.TRIGGER_MENU_ENABLE() +  '</li>');
		}
		sb.append('<li name="doCopyTrigger" class="doCopyTrigger popup-menu-item icon-copy"><span class="icon icon-menu"></span>' + _.TRIGGER_MENU_COPY() + '</li>');

		if (this.getInterface() != TRIGGER_TYPES.ParameterAdapterWidgetTrigger) {
			sb.append('<li name="doRemoveTrigger" class="popup-menu-item icon-delete"><span class="icon icon-menu"></span>' + _.TRIGGER_MENU_DELETE() +  '</li>');
		} else {
		    // ParameterAdapterWidgetTrigger are handled by dependencies and cannot be drag&dropped
		    // thus we do not allow their removal
		    var dependencies = this.getDependentComponents();
		    var def = getWidgetDefinition(dependencies[0]);
		    if (def != undefined) {
		        sb.append('<li class="popup-menu-item icon-delete disabled" title="' + _.TRIGGER_MENU_DELETE_DISABLED(def.name) + '"><span class="icon icon-menu"></span>' + _.TRIGGER_MENU_DELETE() +  '</li>');
		    }
		}
	}

	this.getElHeader().find('.menu-trigger').html(sb.toString());
};

/**
 * Disabled the current trigger
 *
 * @param message
 */
Mashup_Page_Layout_Trigger.prototype.disable = function(message) {
	if (message == undefined) {
		this.json.enable = false;
	}
	if (!this.getEl().hasClass('trigger-disabled')) {
		this.getEl().addClass('trigger-disabled');
		if (message == undefined) {
			this.getElHeader().find('.trigger-header-icons').prepend('<span class="icon icon-trigger-disabled" name="doToggleStatus" title="' + _.TRIGGER_DISABLED() + '"></span>');
		} else {
			this.getElHeader().find('.trigger-header-icons').prepend('<span class="icon icon-trigger-invalid" warningMessage="' + message + '"></span>');
		}
		this.updateMenuHTML();
	}
};

/**
 * Enables the current trigger
 */
Mashup_Page_Layout_Trigger.prototype.enable = function() {
	this.json.enable = true;
	if (this.getEl().hasClass('trigger-disabled')) {
		this.getEl().removeClass('trigger-disabled');
		this.getElHeader().find('.icon-trigger-invalid,.icon-trigger-disabled').remove();
		this.updateMenuHTML();
	}
};

/**
 * Attaches the draggable event
 */
Mashup_Page_Layout_Trigger.prototype.attachDraggable = function() {
	var _this = this;
	this.getEl().draggable({
		revert: 'invalid',
		zIndex: 4200,
		distance: 10,
		helper: function() {
			return $('' +
				'<form class="trigger-wrapper">' +
					'<div class="trigger">' +
						'<div class="trigger-header" style="cursor:move;">' +
							'<table class="trigger-header-table">' +
								'<tbody>' +
									'<tr>' +
										'<td class="trigger-icon-wrapper"><span class="icon icon-trigger"></span></td>' +
										'<td><div class="trigger-name" title="' + _this.getDisplayName() + '">' + _this.getDisplayName() + '</div></td>' +
										'<td class="trigger-header-icons">' +
											'<span class="icon trigger-move-handle" title="' + _.TRIGGER_MOVE() + '"></span>' +
										'</td>' +
									'</tr>' +
								'</tbody>' +
							'</table>' +
						'</div>' +
					'</div>' +
				'</form>'
			);
		},
		appendTo: '#page-wrapper',
		start: function(e, obj) {
			if (!window.mashupBuilder.getEl().hasClass('hide-' + _this.type + '-drop-zones')) {
				return onDragComponentStart.call(this, _this.type, e, obj);
			}
			return false;
		},
		stop: function(e, obj) { onDragComponentStop.call(this, _this.type, e, obj); },
	});
};

/**
 * Clears all the errors of this trigger
 */
Mashup_Page_Layout_Trigger.prototype.clearError = function() {
	if (this.bottomToolbar != undefined) {
		window.mashupBuilder.removeBottomToolbar(this.bottomToolbar, true /* force remove */);
		this.bottomToolbar = undefined;
	}
};

/**
 * Removes the trigger from the DOM
 */
Mashup_Page_Layout_Trigger.prototype.remove = function() {
	// clears any error of the triggers
	this.clearError();

	// remove the DOM element
	if (this.el != null) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Removes the trigger as JSON
 */
Mashup_Page_Layout_Trigger.prototype.getJson = function() {
	return this.json;
};

/**
 * File: /resources/mashupBuilder/js/Page/Layout/TriggerContainer.js
 */
/**
 * Handles a set of triggers
 * 
 * @constructor
 * @this {Mashup_Page_Layout_TriggerContainer}
 * @param type
 * @param parent
 * @param jsonTriggers
 * @returns
 */
function Mashup_Page_Layout_TriggerContainer(type, parent, jsonTriggers) {
	this.type = type;
	this.parent = parent;

	this.triggers = [];
	for (var i = 0; i < jsonTriggers.length; i++) {
		this.registerNewTrigger(jsonTriggers[i], i);
	}
}

/**
 * Returns whether this container has triggers or not
 * 
 * @param className if undefined then any trigger
 * @returns {Boolean}
 */
Mashup_Page_Layout_TriggerContainer.prototype.hasTrigger = function(className) {
	if (className == undefined) {
		return this.triggers.length > 0;
	}
	for (var i = 0; i < this.triggers.length; i++) {
		if (this.triggers[i].getClassName() == className) {
			return true;
		}
	}
	return false;
};

/**
 * Returns the parent, could be either a widget, a feed, ...
 * 
 * @returns
 */
Mashup_Page_Layout_TriggerContainer.prototype.getParent = function() {
	return this.parent;
};

/**
 * Returns all the triggers
 * 
 * @returns {Array}
 */
Mashup_Page_Layout_TriggerContainer.prototype.getTriggers = function() {
	return this.triggers;
};

/**
 * Returns all the triggers of the given class names
 * 
 * @returns {Array}
 */
Mashup_Page_Layout_TriggerContainer.prototype.getTriggersByClassName = function(className) {
	var triggers = [];
	for (var i = 0; i < this.triggers.length; i++) {
		if (this.triggers[i].getClassName() == className) {
			triggers.push(this.triggers[i]);
		}
	}
	return triggers;
};

/**
 * Returns whether or not the trigger container is up to date (for Application only)
 */
Mashup_Page_Layout_TriggerContainer.prototype.isUpToDate = function() {
	var parent = this.getParent().constructor;
	if (parent == MashupBuilder) {
		return window.mashupBuilder.getApplication().isUpToDate();
	}
	return true;
};

/**
 * Returns the DOM element for this container
 * 
 * @returns
 */
Mashup_Page_Layout_TriggerContainer.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
			'<div class="trigger-container">' +
				'<div class="drop-zone ' + this.type + '-drop-zone ' + this.type + '-droppable">' +
					this.getDropZoneLabel() +
				'</div>' +
			'</div>'
		);
		this.el.data('_this', this);

		this.elDropZone = this.el.find('.drop-zone');

		if (this.triggers.length > 0) {
			for (var i = 0; i < this.triggers.length; i++) {
				this.el.append(this.triggers[i].getEl());
			}
		} else {
			this.el.addClass('no-trigger');
		}

		if (this.getParent().isReadOnly()) {
			this.elDropZone.remove();
		}
	}
	return this.el;
};

/**
 * Returns the drop zone label
 * 
 * @returns
 */
Mashup_Page_Layout_TriggerContainer.prototype.getDropZoneLabel = function() {
	var parent = this.getParent().constructor;
	if (parent == Mashup_Page_UI || parent == Mashup_Page_API) {
		return _.TRIGGER_DROP_PAGE_TRIGGER_HERE();
	} else if (isWidget(parent)) {
		return _.TRIGGER_DROP_WIDGET_TRIGGER_HERE();
	} else if (parent == Mashup_Page_API_Layout_Feed) {
		return _.TRIGGER_DROP_FEED_TRIGGER_HERE();
	} else if (parent == MashupBuilder) {
		return _.TRIGGER_DROP_APPLICATION_TRIGGER_HERE();
	}
	return '';
};

/**
 * Returns the index of the given trigger in this container
 * 
 * @param trigger
 * @returns
 */
Mashup_Page_Layout_TriggerContainer.prototype.indexOf = function(trigger) {
	return this.triggers.indexOf(trigger);
};

/**
 * Register a new trigger in this container
 * 
 * @private
 * @param jsonTrigger
 * @param insertAtPosition
 * @returns {Mashup_Page_UI_Layout_Trigger}
 */
Mashup_Page_Layout_TriggerContainer.prototype.registerNewTrigger = function(jsonTrigger, insertAtPosition) {
	var trigger = new Mashup_Page_Layout_Trigger(this.type, this, jsonTrigger);
	this.triggers.splice(insertAtPosition, 0, trigger);

	if (this.el != undefined) {
		if (this.triggers.length > 0) {
			this.getEl().removeClass('no-trigger');
		}
	}

	return trigger;
};

/**
 * Register a trigger from the container
 * 
 * @private
 * @param trigger
 * @param insertAtPosition
 */
Mashup_Page_Layout_TriggerContainer.prototype.registerTrigger = function(trigger, insertAtPosition) {
	trigger.setParentTriggerContainer(this);
	this.triggers.splice(insertAtPosition, 0, trigger);

	if (this.el != undefined) {
		if (this.triggers.length > 0) {
			this.getEl().removeClass('no-trigger');
		}
	}
};

/**
 * Unregister a trigger from the container
 * 
 * @param trigger
 */
Mashup_Page_Layout_TriggerContainer.prototype.unRegisterTrigger = function(trigger) {
	this.triggers.splice(this.triggers.indexOf(trigger), 1);
	trigger.getEl().detach();

	if (this.el != undefined) {
		if (this.triggers.length == 0) {
			this.getEl().addClass('no-trigger');
		}
	}
};

/**
 * Register and adds a new trigger in this container
 * 
 * @param jsonTrigger
 * @param insertAtPosition
 * @returns
 */
Mashup_Page_Layout_TriggerContainer.prototype.addNewTrigger = function(jsonTrigger, insertAtPosition) {
	var trigger = this.registerNewTrigger(jsonTrigger, insertAtPosition);

	if (this.el != undefined) {
		if (insertAtPosition == 0) {
			trigger.getEl().insertAfter(this.elDropZone);
		} else {
			trigger.getEl().insertAfter(this.triggers[insertAtPosition - 1].getEl());
		}
	}

	this.getParent().onCreateTrigger(trigger);

	return trigger;
};

/**
 * Register and adds the given trigger in this container
 * 
 * @param trigger
 * @param insertAtPosition
 */
Mashup_Page_Layout_TriggerContainer.prototype.addTrigger = function(trigger, insertAtPosition) {
	this.registerTrigger(trigger, insertAtPosition);

	if (this.el != undefined) {
		if (insertAtPosition == 0) {
			trigger.getEl().insertAfter(this.elDropZone);
		} else {
			trigger.getEl().insertAfter(this.triggers[insertAtPosition - 1].getEl());
		}
	}

	this.getParent().onMoveTrigger(trigger);
};

/**
 * Remove a trigger from the container
 * 
 * @param trigger
 */
Mashup_Page_Layout_TriggerContainer.prototype.removeTrigger = function(trigger) {
	this.unRegisterTrigger(trigger);
	trigger.remove();

	this.getParent().onRemoveTrigger(trigger);
};

/**
 * Redraw this trigger container
 */
Mashup_Page_Layout_TriggerContainer.prototype.redraw = function() {
	if (this.el != undefined) {
		for (var i = 0; i < this.triggers.length; i++) {
			this.triggers[i].remove();
		}

		var $backup = this.el;
		this.el = undefined;
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();
	}
};

/**
 * Removes this container from the DOM
 */
Mashup_Page_Layout_TriggerContainer.prototype.remove = function() {
	for (var i = 0; i < this.triggers.length; i++) {
		this.triggers[i].remove();
	}
	this.triggers = [];

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Returns the JSON for all the contained triggers
 * 
 * @returns {Array}
 */
Mashup_Page_Layout_TriggerContainer.prototype.getJson = function() {
	var triggers = [];
	for (var i = 0 ; i < this.triggers.length; i++) {
		triggers.push(this.triggers[i].getJson());
	}
	return triggers;
};


/**
 * File: /resources/mashupBuilder/js/Page/Settings.js
 */
/**
 * Implementation of a Mashup Settings
 * 
 * @constructor
 * @this {Mashup_Page_Settings}
 * @param {Mashup_Page} page The current page
 */
function Mashup_Page_Settings(page) {
	this.errorsCount = 0;
	this.page = page;
}

/**
 * Returns the page name
 * 
 * @returns
 */
Mashup_Page_Settings.prototype.getId = function() {
	return this.page.getName();
};

/**
 * Returns the page name
 * 
 * Used by the navigation toolbox (behave like a page)
 * 
 * @returns
 */
Mashup_Page_Settings.prototype.getName = function() {
	return this.page.getName();
};

/**
 * Returns the page name
 * 
 * @returns
 */
Mashup_Page_Settings.prototype.getPageName = function() {
	return this.page.getName();
};

/**
 * Returns the messages handler
 * 
 * @returns {Mashup_Control_Messages}
 */
Mashup_Page_Settings.prototype.getMessages = function() {
	return this.page.getMessages();
};

/**
 * Returns the page class
 * 
 * @returns {Mashup_Page}
 */
Mashup_Page_Settings.prototype.getPage = function() {
	return this.page;
};

/**
 * Returns the context menu
 * 
 * @returns {Mashup_Window_TabMenu_Context}
 */
Mashup_Page_Settings.prototype.getContext = function() {
	if (this.context == undefined) {
		this.context = new Mashup_Window_TabMenu_Context({ showClose: true });
		this.context.setParent(this);
		this.context.getEl().hide().appendTo(this.getEl());
		this.context.resize(undefined, 450);
	}
	return this.context;
};

/**
 * Returns whether the page is visible or not
 * 
 * @returns
 */
Mashup_Page_Settings.prototype.isVisible = function() {
	return this.page.isVisible();
};

/**
 * Returns whether the page is read only or not
 * 
 * @returns
 */
Mashup_Page_Settings.prototype.isReadOnly = function() {
	return this.page.getUI().isReadOnly();
};

/**
 * Returns whether the page has been updated or not
 * 
 * @returns {Boolean}
 */
Mashup_Page_Settings.prototype.isUpdated = function() {
	return this.page.isUpdated();
};

/**
 * Returns whether the page is up to date or not
 */
Mashup_Page_Settings.prototype.isUpToDate = function() {
	return this.page.getUI().isUpToDate();
};

/**
 * Called when something has been updated within the Mashup UI

 * @param e can be null in some case
 * @returns {Boolean}
 */
Mashup_Page_Settings.prototype.onUpdate = function() {
};

/**
 * Called by the onResize method in the MashupBuilder
 */
Mashup_Page_Settings.prototype.onResize = function() {
	if (this.context != undefined) {
		this.getContext().resize(undefined, 450);
	}
	var tabs = this.getTabs();
	for (var i = 0; i < tabs.length; i++) {
		tabs[i].onResize();
	}
};

/**
 * Returns whether this tab can be opened or not
 */
Mashup_Page_Settings.prototype.canOpen = function() {
	return true;
};

/**
 * Called when the page settings is open
 */
Mashup_Page_Settings.prototype.onOpen = function() {
	this.onResize();
	window.mashupBuilder.getToolboxContainer().hideToolboxes();
	window.mashupBuilder.getToolboxContainer().getToolbox('navigation').setActivePage(this);
	this.openTab(window.mashupBuilder.parseAnchor()[1]);
	$('body').addClass('workspace-fullscreen');
	this.getElBottomTabs().addClass('selected');
};

/**
 * Called when the page settings is closed
 */
Mashup_Page_Settings.prototype.onClose = function() {
	this.getElBottomTabs().removeClass('selected');
	$('body').removeClass('workspace-fullscreen');
	window.mashupBuilder.getToolboxContainer().getToolbox('navigation').setActivePage(null);
	this.getTab(this.getOpenedTabName()).onClose();
};

/**
 * Called if the config has been updated
 */
Mashup_Page_Settings.prototype.redraw = function() {
	for (var i = 0; i < this.tabs.length; i++) {
		this.tabs[i].redraw();
		if (this.getOpenedTabName() == this.tabs[i].getId()) {
			this.tabs[i].onOpen();
		}
	}
};

/**
 * Updates the error count for the Page Settings
 * 
 * @param {number} errorsCount Can be positive (more errors) or negative (less errors)
 */
Mashup_Page_Settings.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;
	if (this.errorsCount == 0) {
		this.getElBottomTabsErrors().css('display', 'none');
	} else {
		this.getElBottomTabsErrors().css('display', 'inline-block').html(this.errorsCount);
	}
	this.getPage().updateError(errorsCount);
};

/**
 * Returns the DOM element for the Mashup API
 * 
 * @returns
 */
Mashup_Page_Settings.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="settings-workspace" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);

		var tabs = this.getTabs();
		for (var i = 0; i < tabs.length; i++) {
			tabs[i].getEl().appendTo(this.el);
			tabs[i].getEl().addClass('moveToMoon');
		}

		this.el.on('click', $.proxy(this.handleEventOnClick, this));
	}
	return this.el;
};

/**
 * jQuery onClick event handler
 * 
 * @param e
 */
Mashup_Page_Settings.prototype.handleEventOnClick = function(e) {
	this.getContext().onInputFocus(undefined);
	return true;
};

/**
 * Returns the default tab name for this page
 * 
 * @returns
 */
Mashup_Page_Settings.prototype.getDefaultTabName = function() {
	if (this.openedTabName != undefined) {
		return this.openedTabName;
	}
	var tabs = this.getTabs();
	for (var i = 0; i < tabs.length; i++) {
		if (tabs[i].isVisible()) {
			return tabs[i].getId();
		}
	}
	return null;
};

/**
 * Returns the name of the opened tab
 * 
 * @returns
 */
Mashup_Page_Settings.prototype.getOpenedTabName = function() {
	return this.openedTabName;
};

/**
 * Returns the tabs (lazy-loaded)
 * 
 * @param tabName
 * @returns
 */
Mashup_Page_Settings.prototype.getTabs = function() {
	if (this.tabs == undefined) {
		this.tabs = [];
		this.tabs.push(new Mashup_Page_Settings_Properties(this));
	}
	return this.tabs;
};

/**
 * Returns a Mashup Tab class
 * 
 * @param tabName
 * @returns
 */
Mashup_Page_Settings.prototype.getTab = function(tabName) {
	var tabs = this.getTabs();
	for (var i = 0; i < tabs.length; i++) {
		if (tabs[i].getId() == tabName) {
			return tabs[i];
		}
	}
	return null;
};

/**
*
* @param tabName
* @returns the focused tabName. null otherwise
*/
Mashup_Page_Settings.prototype.openTab = function(tabName) {
	var tab = this.getTab(tabName);

	// fallback to default tab if none given or unable to access it
	if (tab == null || (this.openedTabName == null && tab.isVisible() == false)) {
		tabName = this.getDefaultTabName();
		tab = this.getTab(tabName);
	}

	// if invalid given tab then skip
	if (tab.isVisible() == false) {
		return null;
	}

	// if already opened then trigger onOpen and skip
	if (this.openedTabName == tabName) {
		tab.onOpen();
		return tabName;
	}

	// close the opened tab
	if (this.getTab(this.openedTabName) != null) {
		this.getTab(this.openedTabName).getEl().addClass('moveToMoon');
		this.getTab(this.openedTabName).onClose();
	}

	// opens the new tab
	tab.onOpen();
	tab.getEl().removeClass('moveToMoon');
	this.openedTabName = tabName;

	return tabName;
};

/**
 * Returns the DOM element for the Tab
 * 
 * @returns
 */
Mashup_Page_Settings.prototype.getElBottomTabs = function() {
	return window.mashupBuilder.getElPageMenu();
};

/**
 * Returns the tab error Node
 * 
 * @returns
 */
Mashup_Page_Settings.prototype.getElBottomTabsErrors = function() {
	if (this.elErrors == undefined) {
		this.elErrors = this.getElBottomTabs().find('.icon-error');
	}
	return this.elErrors;
};

/**
 * Remove the tab class and all its dependencies
 */
Mashup_Page_Settings.prototype.remove = function() {
	this.openedTabName = undefined;

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}

	if (this.tabs != undefined) {
		for (var i = 0; i < this.tabs.length; i++) {
			this.tabs[i].remove();
		}
	}
};


/**
 * File: /resources/mashupBuilder/js/Page/Settings/Properties.js
 */
/**
 * Implementation of a page for the application properties
 * 
 * @constructor
 * @param page
 * @returns
 */
Inherit(Mashup_Page_Settings_Properties, Mashup_Application_Abstract);
function Mashup_Page_Settings_Properties(page) {
	Mashup_Page_Settings_Properties.superclass.constructor.call(this, page, {
		id: 'properties',
		label: _.PAGE_MENU_ITEM_PROPERTIES(),
		group: _.PAGE_MENU_GROUP_GENERAL()
	});
}

/**
 * Returns the parameters for the properties
 */
Mashup_Page_Settings_Properties.prototype.getContainers = function() {
	if (this.containers == undefined) {
		this.containers = [];

		var config = Mashup_Parameter_Factory.createInvertedConfigParameters(this.getPage().getPage().getUI().json.parameters);
		var properties = Mashup_Parameter_Factory.createPageParameters();
		for (var i = 0; i < properties.length; i++) {
			var containerConfig = properties[i].container,
				parameters = properties[i].parameters;

			// configured by the layout toolbox or different page
			if (containerConfig.id == 'layout' || containerConfig.id == 'styles' || containerConfig.id == 'javascript') {
				continue;
			}

			var parameterContainer = new Mashup_Parameter_Container($.extend({
				showToggle: false,
				showLabel: true,
				parent: this,
				onChangeCallback: $.proxy(this.getPage().getPage().getUI().onUpdateParameters, this.getPage().getPage().getUI())
			}, containerConfig));

			for (var j = 0; j < parameters.length; j++) {
				parameterContainer.addParameter(new Mashup_Parameter(window.defaultJsonCustomComponentParameter['class'], $.extend(parameters[j], {
					width: this.getParameterWidth(),
					values: config[parameters[j].name] ? config[parameters[j].name] : []
				})));
			}
	
			this.containers.push(parameterContainer);
		}
	}
	return this.containers;
};

/**
 * Sets the page as read only
 */
Mashup_Page_Settings_Properties.prototype.isReadOnly = function() {
	return this.getPage().isReadOnly();
};

/**
 * Returns the DOM element for this page
 * 
 * @returns
 */
Mashup_Page_Settings_Properties.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="page-properties" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);
	}
	return this.el;
};

/**
 * Called when the tab is opened
 */
Mashup_Page_Settings_Properties.prototype.onOpen = function() {
	if (this.containers == undefined) {
		var containers = this.getContainers();
		for (var i = 0; i < containers.length; i++) {
			if (containers[i].getId() != 'security') {
				this.getEl().append(containers[i].getEl());
			}
		}
	}
	Mashup_Page_Settings_Properties.superclass.onOpen.call(this);
};

/**
 * Called when the config has been updated
 */
Mashup_Page_Settings_Properties.prototype.redraw = function() {
	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}

	if (this.el != undefined) {
		var $backup = this.el;
		this.el = undefined;
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();
	}

	// retrieve the real page
	var page = this.getPage().getPage(); // Settings > Page

	// displays the security icon if needed
	page.showSecurityIcon(page.isSecurityEnable());

	// displays the preview tab if not secured
	page.getPreview().getElBottomTabs().css('display', page.isSecurityEnable() ? 'none' : 'inline-block');
};

/**
 * Remove page from the DOM
 */
Mashup_Page_Settings_Properties.prototype.remove = function() {
	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Page/API.js
 */
/**
 * Implementation of a Mashup API
 * 
 * @constructor
 * @this {Mashup_Page_API}
 * @param {Mashup_Page} page The current page
 * @param {hash} jsonPageFeed The feeds of the page as JSON
 */
function Mashup_Page_API(page, jsonPageFeed) {
	this.page = page;
	this.json = jsonPageFeed;

	this.errorsCount = 0;
}

/**
 * Returns the page ID
 * 
 * @returns
 */
Mashup_Page_API.prototype.getId = function() {
	return this.json.id;
};

/**
 * Returns the page class
 * 
 * @returns {Mashup_Page}
 */
Mashup_Page_API.prototype.getPage = function() {
	return this.page;
};

/**
 * Returns the page name
 * 
 * @returns
 */
Mashup_Page_API.prototype.getPageName = function() {
	return this.page.getName();
};

/**
 * Returns whether the Mashup API is visible or not
 * 
 * @returns
 */
Mashup_Page_API.prototype.isVisible = function() {
	if (this.isAPIVisible == undefined) {
		this.isAPIVisible = mashupBuilder.permission.canViewPageTab(this.page.getName(), 'api');
	}
	return this.isAPIVisible;
};

/**
 * Returns whether the Mashup API is read only or not
 * 
 * @returns
 */
Mashup_Page_API.prototype.isReadOnly = function() {
	return !window.mashupBuilder.permission.canUpdatePageTab(this.page.getName(), 'api') || !this.isUpToDate();
};

/**
 * Returns whether the Mashup API has been updated or not
 * 
 * @returns {Boolean}
 */
Mashup_Page_API.prototype.isUpdated = function() {
	return this.json.somethingChanged == true;
};

/**
 * Returns whether the Mashup API is up-to-date or not
 */
Mashup_Page_API.prototype.isUpToDate = function() {
	return window.mashupBuilder.concurrency.isPageAPIUpToDate(this.getPageName());
};

/**
 * Returns the Feed Container for this Mashup API
 * 
 * @returns {Mashup_Page_API_Layout_FeedContainer}
 */
Mashup_Page_API.prototype.getFeedContainer = function() {
	if (this.feedContainer == undefined) {
		this.feedContainer = new Mashup_Page_API_Layout_FeedContainer(this, this.json.subfeeds);
	}
	return this.feedContainer;
};

/**
 * Returns the trigger container of the page triggers
 * 
 * @returns {Mashup_Page_Layout_TriggerContainer}
 */
Mashup_Page_API.prototype.getTriggerContainer = function() {
	if (this.triggerContainer == null) {
		this.triggerContainer = new Mashup_Page_Layout_TriggerContainer('feedTrigger', this, this.json.triggers);
	}
	return this.triggerContainer;
};

/**
 * Returns the context menu for the Mashup API
 * 
 * @returns {Mashup_Window_TabMenu_Context}
 */
Mashup_Page_API.prototype.getContext = function() {
	if (this.context == undefined) {
		// initialize the context menu
		this.context = new Mashup_Window_TabMenu_Context({ showClose: true });
		this.context.setParent(this);

		// attach the scroll event
		this.getPage().getEl().on('scroll', $.proxy(this.onPageScroll, this));
	}
	return this.context;
};

/**
 * Returns whether or not the Mashup API has feeds
 * 
 * @returns {array}
 */
Mashup_Page_API.prototype.hasFeeds = function() {
	return this.getFeedContainer().getFeeds(false).length > 0;
};

/**
 * Returns all the feeds for this Mashup API
 * 
 * @returns {array}
 */
Mashup_Page_API.prototype.getFeeds = function() {
	return this.getFeedContainer().getFeeds(true);
};

/**
 * Returns a single feed contained in this Mashup API
 * 
 * @param feedId
 * @returns {Mashup_Page_API_Layout_Feed}
 */
Mashup_Page_API.prototype.getFeed = function(feedId) {
	return this.getFeedContainer().getFeed(feedId, true);
};

/**
 * Sets the view mode of the API
 */
Mashup_Page_API.prototype.setViewMode = function(viewMode) {
	if ((openedFeed = this.getOpenedFeed()) != null) {
		openedFeed.setViewMode(viewMode);
	}
};

/**
 * Updates the color of the feeds depending on their position
 */
Mashup_Page_API.prototype.updateFeedsColor = function() {
	this.getFeedContainer().updateFeedsColor(0, -1);
};

/**
 * Updates the "actAsFeed" inputs of the feeds
 */
Mashup_Page_API.prototype.updateActAsFeed = function() {
	if (this.getOpenedFeed() != null) {
		var feeds = this.getFeeds(true);
		this.getOpenedFeed().updateActAsFeed(feeds);
	}
};

/**
 * Returns whether this tab can be opened or not
 */
Mashup_Page_API.prototype.canOpen = function() {
	return true;
};

/**
 * Returns whether this trigger is supported or not
 */
Mashup_Page_API.prototype.canSupportTrigger = function(className) {
	var definition = getTriggerDefinition(className);
	return definition && (
		definition.type == TRIGGER_TYPES.PageFeedTrigger ||
		definition.type == TRIGGER_TYPES.FeedTrigger
	);
};

/**
 * jQuery onClick event handler
 * 
 * @param e
 */
Mashup_Page_API.prototype.handleEventOnClick = function(e) {
	this.getContext().onInputFocus(undefined);
	return true;
};

/**
 * Called when something has been updated within the Mashup API
 * 
 * @returns {Boolean}
 */
Mashup_Page_API.prototype.onUpdate = function() {
	if (this.json.somethingChanged != true) {
		this.json.somethingChanged = true;
		if (this.json.id != null) {
			window.mashupBuilder.concurrency.postEvent(ConcurrencyEventType.EDITPAGE, this.json.id);
		}
		window.mashupBuilder.somethingChanged();
	}
};

/**
 * Called when a feed has been created within the Mashup API
 * 
 * @param {Mashup_Page_API_Layout_Feed} feed
 */
Mashup_Page_API.prototype.onCreateFeed = function(feed) {
	if (!this.getPage().isOpen()) {
		this.onUpdate();
	} else {
		this.openFeed(feed);

		Mashup_Config_Validator.checkFeed(feed);

		// update feed's context for its new parent feed
		if ((parentFeed = feed.getParentFeed()) != null) {
			parentFeed.onUpdateSubFeeds();
		}

		this.updateFeedsColor();
		this.getPage().getUI().redrawElUseFeeds();
		this.updateActAsFeed();
		this.onUpdate();

		if ((parameter = feed.getParameter('feedId')) != null) {
			if (parameter.hasErrors()) {
				parameter.focus();
			}
		}

		if (!feed.hasErrors()) {
			window.mashupBuilder.context.reload();
		}
	}
};

/**
 * Called when a feed has been removed from the Mashup API
 * 
 * @param {Mashup_Page_API_Layout_Feed} feed
 */
Mashup_Page_API.prototype.onRemoveFeed = function(feed) {
	var state = mashupBuilder.state;
	(function(feed) {
		state.remove(feed.getFuid());
		var subfeeds = feed.getFeeds(true);
		for (var i = 0; i < subfeeds.length; i++) {
			arguments.callee(subfeeds[i]);
		}
	})(feed);

	if (this.getPage().isOpen()) {

		// opens a new feed if it was the opened one
		if (this.getOpenedFeed() == feed) {
			this.openedFeed = null;
			this.openFirstFeed();
		}

		// update feed's context for its new parent feed
		if ((parentFeed = feed.getParentFeed()) != null) {
			parentFeed.onUpdateSubFeeds();
		}

		this.updateFeedsColor();
		this.getPage().getUI().redrawElUseFeeds();
		this.getPage().getUI().onUpdate();
		this.updateActAsFeed();
	}
	this.onUpdate();
};

/**
 * Called when a feed ID has been updated
 * 
 * @param {Mashup_Page_API_Layout_Feed} feed
 */
Mashup_Page_API.prototype.onUpdateFeedId = function(feed) {
	if (!this.getPage().isOpen()) {
		this.onUpdate();
	} else {
		Mashup_Config_Validator.checkFeed(feed);
		this.getPage().getUI().redrawElUseFeeds();
		this.getPage().getUI().onUpdate();
		this.updateActAsFeed();
		this.onUpdate();
		window.mashupBuilder.context.reload();
	}
};

/**
 * Called when a feed has been moved
 * 
 * @param {Mashup_Page_API_Layout_Feed} feed
 */
Mashup_Page_API.prototype.onMoveFeed = function(feed) {
	if (this.getPage().isOpen()) {

		// if the moved feed it the opened one remove its
		// flag as opened as it was detached from the DOM
		if (this.getOpenedFeed() == feed) {
			this.openedFeed = null;
		}
		this.openFeed(feed);

		Mashup_Config_Validator.checkFeed(feed);

		// update feed's context for its new parent feed
		if ((parentFeed = feed.getParentFeed()) != null) {
			parentFeed.onUpdateSubFeeds();
		}

		this.updateFeedsColor();
		this.getPage().getUI().redrawElUseFeeds();
		this.getPage().getUI().onUpdate();
		this.updateActAsFeed();
	}
	this.onUpdate();
};

/**
 * Called when a trigger has been created
 */
Mashup_Page_API.prototype.onCreateTrigger = function(trigger) {
	if (this.getPage().isOpen()) {
		Mashup_Config_Validator.checkTrigger(trigger);
		this.onUpdateTriggerDisplay();
	}
	this.onUpdate();
};

/**
 * Called when a trigger has been moved
 */
Mashup_Page_API.prototype.onMoveTrigger = function(trigger) {
	this.onUpdateTriggerDisplay();
	this.onUpdate();
};

/**
 * Called when a trigger has been removed
 */
Mashup_Page_API.prototype.onRemoveTrigger = function(trigger) {
	this.onUpdateTriggerDisplay();
	this.onUpdate();
};

/**
 * Called when a trigger has been updated
 */
Mashup_Page_API.prototype.onUpdateTrigger = function(trigger) {
	this.onUpdate();
};

/**
 * Called when a trigger has been updated
 */
Mashup_Page_API.prototype.onUpdateTriggerDisplay = function() {
};

/**
 * Called by the onResize method in the MashupBuilder or when creating the el
 */
Mashup_Page_API.prototype.onResize = function() {
	// resize the feed navigation wrapper
	this.getElNavigationWrapper().css({
		height: this.getPage().height - 25 // padding(25)
	});

	// resize the feed wrapper
	this.getElFeedWrapper().css({
		height: this.getPage().height - 25, // padding(25)
		width: this.getPage().width - 295 // timeline(270) padding(25)
	});

	// resize the opened feed
	if ((openedFeed = this.getOpenedFeed()) != null) {
		openedFeed.onResize();
	}

	// resize the context menu
	this.getContext().resize(undefined, this.getPage().height - 40);
};

/**
 * Called when the tab is opened
 */
Mashup_Page_API.prototype.onOpen = function() {
	if (this.getOpenedFeed() == null) {
		this.openFirstFeed();
	}

	if (this.isReadOnly()) {
		window.mashupBuilder.getEl().addClass('feeds-readonly');
	} else {
		window.mashupBuilder.getEl().removeClass('feeds-readonly');
	}

	this.updateUserMessages();
};

/**
 * Updates the displayed message on this page
 */
Mashup_Page_API.prototype.updateUserMessages = function() {
	if (!this.isUpToDate()) {
		this.getPage().getMessages().setMessage('disabled', {
			level : 'error',
			canClose : false,
			message : _.CONCURRENCY_MASHUP_API_NOT_UPTODATE()
		});
	} else {
		this.getPage().getMessages().removeMessage('disabled');
	}
};

/**
 * Called when the tab is closed
 */
Mashup_Page_API.prototype.onClose = function() {
	this.getPage().getMessages().removeMessage('disabled');
};

/**
 * Called when an input of the feed gained the focus
 * 
 * @param input
 */
Mashup_Page_API.prototype.onInputFocus = function(input) {
	var offset = input.getParameterContainer().getEl().offset();
	this.getContext().onInputFocus(input);
	this.getContext().getEl().css({ top: this.getPage().getEl().scrollTop() + 10 });
	this.getContext().show();
};

/**
 * Called when the user call the page while the context menu is opened
 * 
 * @param input
 */
Mashup_Page_API.prototype.onPageScroll = function(e) {
	if (this.scrollTimer != null) {
		clearTimeout(this.scrollTimer);
	}

	var _this = this;
	this.scrollTimer = setTimeout(function() {
		_this.getContext().getEl().animate({ 'top':  _this.getPage().getEl().scrollTop() + 10 });
	}, 200);
};

/**
 * Opens the given feed into the Mashup API for edition
 * 
 * @param feed
 */
Mashup_Page_API.prototype.openFeed = function(feed) {
	if (typeof(feed) == 'string') {
		feed = this.getFeed(feed);
	}

	if (feed == null || feed == this.openedFeed) {
		return;
	}

	closeCodeMirrorFullScreen();

	if (this.openedFeed != undefined) {
		this.openedFeed.onClose();
		this.openedFeed.getEl().detach();
		this.openedFeed.getElNavigationHeader().removeClass('selected');
	}

	feed.getElNavigationHeader().addClass('selected');
	this.getElFeedWrapper().find('.feed-overflow-content').html(feed.getEl());
	feed.onResize();
	feed.onOpen();
	feed.setViewMode(window.mashupBuilder.getViewMode(), false /* do not animate */);
	this.openedFeed = feed;

	// refresh the state of the ActAsFeed parameter
	this.updateActAsFeed();

	// remove any opened bottom toolbar
	window.mashupBuilder.removeOpenedBottomToolbar();
};

/**
 * Opens the first available feed into the Mashup API for edition
 */
Mashup_Page_API.prototype.openFirstFeed = function() {
	var feeds = this.getFeeds();
	if (feeds.length > 0) {
		this.openFeed(feeds[0]);
	}
};

/**
 * Returns the currently opened feed
 * 
 * @returns {Mashup_Page_API_Layout_Feed}
 */
Mashup_Page_API.prototype.getOpenedFeed = function() {
	return this.openedFeed;
};

/**
 * Updates the error count for the Page API
 * 
 * @param {number} errorsCount Can be positive (more errors) or negative (less errors)
 */
Mashup_Page_API.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;
	if (this.errorsCount == 0) {
		this.getElBottomTabsErrors().css('display', 'none');
	} else {
		this.getElBottomTabsErrors().css('display', 'inline-block').html(this.errorsCount);
	}
	this.getPage().updateError(errorsCount);
};

/**
 * Returns the DOM element for the Mashup API
 * 
 * @returns
 */
Mashup_Page_API.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="feeds-workspace" class="workspace">' +
				'<div id="feed-navigation-wrapper">' +
					'<div class="feed-navigation-overflow-content">' +
					'</div>' +
				'</div>' +
				'<div id="feed-wrapper">' +
					'<div class="feed-overflow-content">' +
					'</div>' +
				'</div>' +
			'</div>'
		);
		this.el.data('_this', this);

		this.getElNavigationWrapper()
			.find('.feed-navigation-overflow-content')
			.append(window.mashupBuilder.getFeedTriggerContainer().getEl())
			.append(this.getTriggerContainer().getEl())
			.append(this.getFeedContainer().getEl());

		this.getContext().getEl().hide();
		this.getEl().append(this.getContext().getEl());
		this.getEl().on('click', $.proxy(this.handleEventOnClick, this));

		this.onResize();

		this.updateFeedsColor();
		this.updateActAsFeed();
	}
	return this.el;
};

/**
 * Returns the DOM node for the navigation wrapper
 * 
 * @returns
 */
Mashup_Page_API.prototype.getElNavigationWrapper = function() {
	if (this.elNavigationWrapper == undefined) {
		this.elNavigationWrapper = this.getEl().find('#feed-navigation-wrapper');
	}
	return this.elNavigationWrapper;
};

/**
 * Returns the DOM node for the feed wrapper
 * 
 * @returns
 */
Mashup_Page_API.prototype.getElFeedWrapper = function() {
	if (this.elFeedWrapper == undefined) {
		this.elFeedWrapper = this.getEl().find('#feed-wrapper');
	}
	return this.elFeedWrapper;
};

/**
 * Returns the DOM element for the Tab
 * 
 * @returns
 */
Mashup_Page_API.prototype.getElBottomTabs = function() {
	if (this.elTopTabs == undefined) {
		this.elTopTabs = $('' +
			'<li class="page-bottom-tab mashupAPI" name="doOpenMashupAPI">' +
				'<span class="typeId" name="doOpenMashupAPI">' + _.PAGEAPI_TAB_TITLE() + '</span> ' +
				'<span class="icon icon-error" name="doOpenError" title="' + _.MB_ERROR_CLICK_TO_SEE() + '">' + this.errorsCount + '</span>' +
			'</li>'
		);
	}
	return this.elTopTabs;
};

/**
 * Returns the tab error Node
 * 
 * @returns
 */
Mashup_Page_API.prototype.getElBottomTabsErrors = function() {
	if (this.elErrors == undefined) {
		this.elErrors = this.getElBottomTabs().find('.icon-error');
	}
	return this.elErrors;
};

/**
 * Redraws the API
 */
Mashup_Page_API.prototype.redraw = function() {
	// destroy the feed container
	if (this.feedContainer != undefined) {
		this.feedContainer.remove();
		this.feedContainer = undefined;
	}

	// destroy cache node
	this.elNavigationWrapper = undefined;
	this.elFeedWrapper = undefined;

	// refresh the body
	if (this.el != undefined) {
		var $backup = this.el;
		this.el = undefined;
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();
	}

	// re-trigger the open callback
	if (this.getPage().getOpenedTabName() == 'api') {
		this.onOpen();
	}

	// re-validate the Mashup API
	Mashup_Config_Validator.checkAPI(this);

	// redraw the page parameters
	window.mashupBuilder.getToolboxContainer().getToolbox('pageParameters').redraw();
};

/**
 * Remove the API class and all its dependencies
 */
Mashup_Page_API.prototype.remove = function() {
	window.mashupBuilder.getFeedTriggerContainer().getEl().detach();

	this.getContext().remove();
	this.getFeedContainer().remove();
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
	if (this.elTopTabs != undefined) {
		this.elTopTabs.remove();
		this.elTopTabs = undefined;
	}
};

/**
 * Return the JSON
 * 
 * @returns
 */
Mashup_Page_API.prototype.getJson = function() {
	this.json.subfeeds = this.getFeedContainer().getJson();
	this.json.triggers = this.getTriggerContainer().getJson();
	return this.json;
};


/**
 * File: /resources/mashupBuilder/js/Page/API/Interactions/DragDrop.js
 */
/*
 * 
 * Feed
 * 
 */

/**
 * Called when the user start to drag either an existing feed or a new feed
 */
function dragFeedStart(e, obj) {
	if (!window.mashupBuilder.getEl().hasClass('hide-feed-drop-zones') && !window.mashupBuilder.getEl().hasClass('feeds-readonly')) {
		window.mashupBuilder.context.closeContextMenus();
		return onDragComponentStart.call(this, 'feed', e, obj);
	}
	return false;
};

/**
 * Called when the user drop a draggable feed
 */
function dragFeedStop(e, obj) {
	onDragComponentStop.call(this, 'feed', e, obj);
};

/**
 * Function called when the feed is dropped
 */
function doDragStartFeed(draggable, droppable) {
	doDragComponentStart.call(this, 'feed', draggable, droppable);
}

/**
 * Function called when the widget is dropped
 */
function doDragStopFeed(draggable, addHistory) {
	doDragComponentStop.call(this, 'feed', draggable, addHistory);
}

/*
 * 
 * Feed Triggers
 * 
 */

/**
 * Called when the user start to drag either an existing trigger or a new trigger
 */
function dragFeedTriggerStart(e, obj) {
	if (!window.mashupBuilder.getEl().hasClass('hide-feedTrigger-drop-zones') && !window.mashupBuilder.getEl().hasClass('feeds-readonly')) {
		window.mashupBuilder.context.closeContextMenus();
		return onDragComponentStart.call(this, 'feedTrigger', e, obj);
	}
	return false;
};

/**
 * Called when the user drop a draggable trigger
 */
function dragFeedTriggerStop(e, obj) {
	onDragComponentStop.call(this, 'feedTrigger', e, obj);
};

/**
 * Function called when the trigger is dropped
 */
function doDragStartFeedTrigger(draggable, droppable) {
	doDragComponentStart.call(this, 'feedTrigger', draggable, droppable);
}

/**
 * Function called when the trigger is dropped
 */
function doDragStopFeedTrigger(draggable, addHistory) {
	doDragComponentStop.call(this, 'feedTrigger', draggable, addHistory);
}


/**
 * File: /resources/mashupBuilder/js/Page/API/Layout/Feed.js
 */
/**
 * Implementation of a Mashup Feed
 * 
 * @constructor
 * @this {Mashup_Page_API_Layout_Feed}
 * @param {Mashup_Page_API_Layout_FeedContainer} parentFeedContainer
 * @param {hash} jsonFeed The JSON of the feed
 */
function Mashup_Page_API_Layout_Feed(parentFeedContainer, jsonFeed) {
	this.supportSubFeeds = window.mashupBuilder.permission.canHaveManyFeeds();
	this.parentFeedContainer = parentFeedContainer;

	this.errorsCount = 0;

	this.color = null;

	this.json = jsonFeed;
	if (this.json.fuid == null || this.json.fuid == '') {
		this.json.fuid = generateID(8);
	}

	this.onResizeCallbacks = [];

	this.feedContainer = new Mashup_Page_API_Layout_FeedContainer(this, this.json.subfeeds);
	this.triggerContainer = new Mashup_Page_Layout_TriggerContainer('feedTrigger', this, this.json.triggers);

	this.initState();
}

/**
 * Returns the feed ID
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getId = function() {
	return this.json.id;
};

/**
 * Returns the feed display name
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getDisplayName = function() {
	return '<span class="feed-id">' + this.getId() + '</span> (' + this.getFeedDefinition().name + ')';
};

/**
 * Returns the feed class name
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getClassName = function() {
	return this.json.className;
};

/**
 * Returns the feed FUID
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getFuid = function() {
	return this.json.fuid;
};

/**
 * Sets the feed ID
 * 
 * @param newId
 */
Mashup_Page_API_Layout_Feed.prototype.setId = function(newId) {
	this.json.id = newId;
};

/**
 * Enables the feed
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.enable = function() {
	this.json.enable = true;
	this.updateMenuHTML();
};

/**
 * Disables the feed
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.disable = function() {
	this.json.enable = false;
	this.updateMenuHTML();
};

/**
 * Returns whether this feed is read only or not
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.isReadOnly = function() {
	return this.getPageAPI().isReadOnly();
};

/**
 * Returns whether or not the feed is secured
 */
Mashup_Page_API_Layout_Feed.prototype.isSecured = function() {
	if (this.parameterContainers == undefined) {
		for (var i = 0; i < this.json.parameters.length; i++) {
			if (this.json.parameters[i].name == 'enableSecurity') {
				return this.json.parameters[i].value == "true";
			}
		}
	} else {
		var parameter = this.getParameter("enableSecurity");
		return parameter != null && parameter.getValue() == "true";
	}
};

/**
 * Returns whether or not the feed is enabled
 */
Mashup_Page_API_Layout_Feed.prototype.isEnabled = function() {
	return this.json.enable;
};

/**
 * Returns whether or not the feed is synchronized
 */
Mashup_Page_API_Layout_Feed.prototype.isSynchronized = function() {
	if (this.parameterContainers == undefined) {
		return this.json['synchronized'] === true;
	} else {
		var parameter = this.getParameter("synchronized");
		return parameter != null && parameter.getValue() == "true";
	}
};

/**
 * Returns whether the feed has errors or not
 * 
 * @returns {Boolean}
 */
Mashup_Page_API_Layout_Feed.prototype.hasErrors = function() {
	return this.errorsCount > 0;
};

/**
 * Returns whether the feed has subfeeds or not
 * 
 * @returns {Boolean}
 */
Mashup_Page_API_Layout_Feed.prototype.hasFeeds = function() {
	return this.getFeedContainer().getFeeds(false).length > 0;
};

/**
 * Returns whether the feed has triggers or not
 * 
 * @returns {Boolean}
 */
Mashup_Page_API_Layout_Feed.prototype.hasTriggers = function() {
	return this.getTriggerContainer().getTriggers().length > 0;
};

/**
 * Returns whether the feed supports subfeeds or not (license restriction)
 */
Mashup_Page_API_Layout_Feed.prototype.isSubFeedSupported = function() {
	return this.supportSubFeeds;
};

/**
 * Returns whether this feed is valid or not
 * 
 * @returns {Boolean}
 */
Mashup_Page_API_Layout_Feed.prototype.isValid = function() {
	return this.getFeedDefinition().isValid === undefined || this.getFeedDefinition().isValid === true;
};

/**
 * Returns whether this trigger is supported or not
 */
Mashup_Page_API_Layout_Feed.prototype.canSupportTrigger = function(className) {
	var definition = getTriggerDefinition(className);
	return definition && definition.type == TRIGGER_TYPES.FeedTrigger;
};

/**
 * Returns the definition of this Feed
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getFeedDefinition = function() {
	if (this.feedDefinition == null) {
		this.feedDefinition = getFeedDefinition(this.getClassName());
		if (this.feedDefinition == null) {
			this.feedDefinition = clone(defaultJsonFeedDefinition);
			this.feedDefinition.className = this.getClassName();
			if (this.feedDefinition.className == 'com.exalead.access.widgetbuilder._InternalDummyFeed') {
				this.feedDefinition.name = _.WB_INTERNAL_FEED_NAME();
				this.feedDefinition.parameters = [];
				this.feedDefinition.isValid = true;
			} else {
				this.feedDefinition.name = _.FEED_UNKNOWN_LABEL(this.getClassName().substr(this.getClassName().lastIndexOf('.') + 1));
				this.feedDefinition.parameters = this.feedDefinition.parameters || [];
				this.feedDefinition.isValid = false;
			}
		}
	}
	return this.feedDefinition;
};

/**
 * Returns the parameter containers (lazy load)
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getParameterContainers = function() {
	if (this.parameterContainers == undefined) {
		this.parameterContainers = [];

		var parameterClassName = window.defaultJsonFeedParameter['class'];

		// retrieve the parameters for this feed
		var containers = Mashup_Parameter_Factory.createFeedParameters(this.getClassName());

		// compute the configuration of the feed
		var config = $.extend({
			whiteListPatterns: this.json.whiteListPatterns
		}, Mashup_Parameter_Factory.createInvertedConfigParameters(this.json.parameters));

		// adds the feed config container
		for (var i = 0; i < containers.length; i++) {
			if (containers[i].container.id == 'feed-options') {
				this.parameterContainers.push(new Mashup_Parameter_Container_FeedConfig(this, containers[i].parameters, {
					synchronized: [this.json.synchronized + ''],
					enable: [this.json.enable + ''],
					embed: [this.json.embed + ''],
					actAsFeed: this.json.actAsFeed != null ? [this.json.actAsFeed] : [],
					enableSecurity: config.enableSecurity
				}));
			}
		}

		// for each container of the feed definition
		var _this = this;
		for (var i = 0; i < containers.length; i++) {
			var containerId = containers[i].container.id;

			// already handled
			if (containerId == 'feed-options') {
				continue;
			}

			var parameterContainer = new Mashup_Parameter_Container($.extend(containers[i].container, {
				parent: this,
				showLabel: true,
				showToggle: true,
				onToggleCallback: function(context) {
					_this.state(this.getId(), !context.visible ? 1 : 0);
				},
				onChangeCallback: function(context) {
					_this.getPageAPI().onUpdate();
				}
			}));

			for (var j = 0; j < containers[i].parameters.length; j++) {
				var parameter = containers[i].parameters[j];
				parameterContainer.addParameter(new Mashup_Parameter(parameterClassName, $.extend(parameter, {
					width: 190,
					isValueOrdered: false, // otherwise too cluttered
					values: config[parameter.name] ? config[parameter.name] : []
				})));
			}

			this.parameterContainers.push(parameterContainer);
		}

		// create the properties mapping container
		this.parameterContainers.push(new Mashup_Parameter_Container_PropertiesMapping(this, this.json.properties));

		// sets default hidden value for parameters containers
		for (var i = 0; i < this.parameterContainers.length; i++) {
			this.parameterContainers[i].options.isHidden = this.state(this.parameterContainers[i].getId());
		}
	}
	return this.parameterContainers;
};

/**
 * Returns a Parameter of this feed by its ID
 * 
 * @param parameterId
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getParameter = function(parameterId) {
	var containers = this.getParameterContainers();
	for (var i = 0; i < containers.length; i++) {
		if ((param = containers[i].getParameter(parameterId)) != null) {
			return param;
		}
	}
	return null;
};

/**
 * Returns the trigger container of the feed
 * 
 * @returns {Mashup_Page_Layout_TriggerContainer}
 */
Mashup_Page_API_Layout_Feed.prototype.getTriggerContainer = function() {
	if (this.triggerContainer == null) {
		this.triggerContainer = new Mashup_Page_Layout_TriggerContainer('feedTrigger', this, this.json.triggers);
	}
	return this.triggerContainer;
};

/**
 * Returns the FeedContainer
 * 
 * @returns {Mashup_Page_API_Layout_FeedContainer}
 */
Mashup_Page_API_Layout_Feed.prototype.getFeedContainer = function() {
	return this.feedContainer;
};

/**
 * Sets the parent FeedContainer
 */
Mashup_Page_API_Layout_Feed.prototype.setParentFeedContainer = function(parentFeedContainer) {
	this.parentFeedContainer = parentFeedContainer;
};

/**
 * Returns the parent FeedContainer
 * 
 * @returns {Mashup_Page_API_Layout_FeedContainer}
 */
Mashup_Page_API_Layout_Feed.prototype.getParentFeedContainer = function() {
	return this.parentFeedContainer;
};

/**
 * Returns the parent Feed
 * 
 * @returns {Mashup_Page_API_Layout_Feed}
 */
Mashup_Page_API_Layout_Feed.prototype.getParentFeed = function() {
	return this.getParentFeedContainer().getParentFeed();
};

/**
 * Returns the Mashup API
 * 
 * @returns {Mashup_Page_API}
 */
Mashup_Page_API_Layout_Feed.prototype.getPageAPI = function() {
	return this.getParentFeedContainer().getPageAPI();
};

/**
 * Returns the properties container of this feed
 * 
 * @returns {Mashup_Parameter_Container_PropertiesMapping}
 */
Mashup_Page_API_Layout_Feed.prototype.getPropertiesContainer = function() {
	var containers = this.getParameterContainers();
	return containers[containers.length - 1];
};

/**
 * Returns all the sub feeds
 * 
 * @param recursive
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getFeeds = function(recursive) {
	if (recursive == true) {
		return this.getFeedContainer().getFeeds(recursive);
	} else {
		return [];
	}
};

/**
 * Returns whether this feed has configuration sets or not
 * 
 * @returns {Boolean}
 */
Mashup_Page_API_Layout_Feed.prototype.hasConfigurationSets = function() {
	return this.getFeedDefinition().defaultConfigurationSets.length > 1;
};

/**
 * Sets the feed in the given view mode
 */
Mashup_Page_API_Layout_Feed.prototype.setViewMode = function(viewMode, animate) {
	var containers = this.getParameterContainers();

	switch (viewMode) {
	case 'compact':
		for (var i = 0; i < containers.length; i++) {
			// do not hide containers in errors
			if (containers[i].getErrorsCount() > 0) {
				continue;
			}
			containers[i].slideUp(animate, false);
		}
		break;

	case 'extended':
		for (var i = 0; i < containers.length; i++) {
			containers[i].slideDown(animate, false);
		}
		break;

	default:
		for (var i = 0; i < containers.length; i++) {
			// do not hide containers in errors
			if (containers[i].getErrorsCount() > 0) {
				containers[i].slideDown(animate, false);
			} else {
				var hiddenConfig = this.state(containers[i].getId());
				if (hiddenConfig) {
					containers[i].slideUp(animate, false);
				} else {
					containers[i].slideDown(animate, false);
				}
			}
		}
		break;
	}
};

/**
 * jQuery 'onClick' event handler
 * 
 * @param e
 * @returns {Boolean}
 */
Mashup_Page_API_Layout_Feed.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);

	if (target != null) {
		switch (target.attr('name')) {

		case 'doOpenDefaultConfigurationSets':
			this.doOpenDefaultConfigurationSets();
			return false;

		case 'doHideDefaultConfigurationSets':
			this.doHideDefaultConfigurationSets();
			return false;

		case 'doApplyDefaultConfigurationSets':
			this.doApplyDefaultConfigurationSets(target.attr('data-idx'));
			return false;

		case 'doToggleStatus':
			this.doToggleStatus();
			return false;

		case 'doCopyFeed':
			this.doCopyFeed();
			return false;

		case 'doRemoveFeed':
			this.doRemoveFeed();
			return false;

		case 'doSlideFeed':
			this.doSlideFeed(null, function(isClosed) {
				this.state('f', isClosed ? 1 : 0);
			});
			return false;
		}
	}
	return true;
};

/**
 * User process to apply a configuration set
 */
Mashup_Page_API_Layout_Feed.prototype.doApplyDefaultConfigurationSets = function(idx) {
	this.doHideDefaultConfigurationSets();

	var configurationSet = getConfigurationSets(this.getFeedDefinition())[idx];
	if (configurationSet == undefined) {
		return false;
	}

	// compute default values
	var configurationValues = {};
	for (var i = 0; i < configurationSet.defaultValues.length; i++) {
		var defaultValue = configurationSet.defaultValues[i];
		if (configurationValues[defaultValue.name] == undefined) {
			configurationValues[defaultValue.name] = [];
		}
		configurationValues[defaultValue.name].push(defaultValue.value);
	}

	var containers = this.getParameterContainers();
	for (var i = 0; i < containers.length; i++) {
		var container = containers[i];

		// do not touch those containers using default value
		if (container.getId() == 'feed-configuration' || container.getId() == 'parameters-filtering' || container.getId() == 'properties-mapping') {
			continue;
		}

		// sets the new parameter's value
		var parameters = container.getParameters();
		for (var j = 0; j < parameters.length; j++) {
			if (configurationValues[parameters[j].getName()] != undefined) {
				parameters[j].setValue(configurationValues[parameters[j].getName()]);
			}
		}
	}

	// sets focus back to the first input
	if ((parameter = this.getParameter('feedId')) != null) {
		parameter.focus();
	}
};

/**
 * User process to opens the configuration set for this feed
 */
Mashup_Page_API_Layout_Feed.prototype.doOpenDefaultConfigurationSets = function() {
	if (this.dcsEl == undefined) {
		var dcs = getConfigurationSets(this.getFeedDefinition()), dcsLength = dcs.length;
		if (dcsLength > 1) {
			var html = '';
			for (var i = 0; i < dcsLength; i++) {
				html += '<li class="configuration-sets-item" data-idx="' + i + '" style="' +  ((dcs[i].previewUrl != null && dcs[i].previewUrl != '') ? 'background-image:url(' + dcs[i].previewUrl + ')' : '') + '" name="doApplyDefaultConfigurationSets">' +
							(dcs[i].displayName || 'Default') +
						'</li>';
			}
			this.dcsEl = $('' +
				'<div class="configuration-sets-wrapper">' +
					'<span class="icon icon-close" name="doHideDefaultConfigurationSets"></span>' +
					'<ul class="configuration-sets-list">' +
						html +
					'</ul>' +
				'</div>');
		}
	}
	if (this.dcsEl != undefined) {
		this.dcsEl.appendTo(this.getEl().find('.feed-content'));
	}
};

/**
 * User process to hides the configuration set
 */
Mashup_Page_API_Layout_Feed.prototype.doHideDefaultConfigurationSets = function() {
	if (this.dcsEl != undefined) {
		this.dcsEl.remove();
		this.dcsEl = undefined;
	}
};

/**
 * User process to show/hide the feed
 *
 * @param shouldBeClosed
 * @param onEndCallback
 */
Mashup_Page_API_Layout_Feed.prototype.doSlideFeed = function(shouldBeClosed, onEndCallback) {
	var isClosed = this.getElNavigation().hasClass('feed-closed');
	if (shouldBeClosed == null || isClosed != shouldBeClosed) {
		if (isClosed == false) {
			this.getElNavigation().addClass('feed-closed');
		} else {
			this.getElNavigation().removeClass('feed-closed');
		}

		var $content = this.getElNavigation().find('> .feed > .feed-navigation-item-content');

		if (shouldBeClosed == null) {
			// toggle it
			var _this = this;
			if (isClosed) {
				$content.slideDown('fast', function() {
					$content.find('.feed-navigation-item-wrapper').animate({ opacity: 1 }, 'fast');
					if (onEndCallback != null) {
						onEndCallback.call(_this, !isClosed);
					}
				});
			} else {
				$content.find('.feed-navigation-item-wrapper').animate({ opacity: 0 }, 'fast');
				$content.slideUp('fast', function() {
					if (onEndCallback != null) {
						onEndCallback.call(_this, !isClosed);
					}
				});
			}
		} else {
			// force show/hide
			if (shouldBeClosed) {
				$content.hide();
			} else {
				$content.show();
			}
			if (onEndCallback != null) {
				onEndCallback.call(this, !isClosed);
			}
		}
	}
};

/**
 * User process to toggles the status of the feed
 */
Mashup_Page_API_Layout_Feed.prototype.doToggleStatus = function() {
	if (this.isEnabled()) {
		this.disable();
	} else {
		this.enable();
	}

	this.updateFeedDisabled();
	this.getPageAPI().onUpdate();
};

/**
 * User process to remove a feed
 */
Mashup_Page_API_Layout_Feed.prototype.doRemoveFeed = function() {
	var _this = this;
	new Mashup_Popup_Confirm({
		title: _.FEED_DELETE_TITLE(),
		text: _.FEED_DELETE_TEXT(),
		buttonOkLabel: _.YES(),
		buttonCancelLabel: _.NO(),
		onClickOkCallback: function(context) {
			_this.getParentFeedContainer().removeFeed(_this);
			this.remove();
		}
	}).show();
};

/**
 * User process to copy a feed
 */
Mashup_Page_API_Layout_Feed.prototype.doCopyFeed = function() {
	if (this.hasErrors()) {
		var alertBox = new Mashup_Popup_Alert({
			text: _.CORRECT_FEED_ERRORS_BEFORE(this.errorsCount),
			level: BoxLevel.ERROR
		});
		alertBox.show();
		return;
	}

	var confirmBox = new Mashup_Popup_Confirm({
		title: _.FEED_CONFIRM_COPY_TITLE(),
		text: '',
		onClickOkCallbackData: { _this: this },
		onClickOkCallback: function(context) {
			context.data._this.copyTo(this.getElContent().find('select[name=pageName]').val());
			this.remove();
		}
	});

	confirmBox.focus = function() {
		this.getElContent().find('input:first').focus();
	};

	var sb = new StringBuilder();
	sb.append('<option value="' + this.getPageAPI().getPageName() + '">' + _.FEED_CONFIRM_COPY_THIS_PAGE() + '</option>');
	var pages = mashupBuilder.getPages();
	for (var i = 0; i < pages.length; i++) {
		if (pages[i].isReadOnly() == false) {
			sb.append('<option value="' + pages[i].getName() + '">' + pages[i].getName() + '</option>');
		}
	}

	confirmBox.getElContent().prepend('' +
		'<div class="popup-form-wrapper">' +
			'<p class="popup-form-input-wrapper">' +
				'<label class="popup-form-label" for="pageName">' + _.FEED_CONFIRM_COPY() + '</label>' +
				'<select name="pageName">' +
					sb.toString() +
				'</select>' +
			'</p>' +
		'</div>'
	);
	confirmBox.show();
};

/**
 * Updates the error count for this toolbar
 * 
 * @param {number} errorsCount Can be positive (more errors) or negative (less errors)
 */
Mashup_Page_API_Layout_Feed.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;
	if (this.errorsCount == 0) {
		this.getEl().removeClass('has-error');
		this.getElNavigationHeader().find('.icon-feed-error').css('display', 'none');
		this.resizeFeedIdentifier();
	} else {
		this.getEl().addClass('has-error');
		this.getElNavigationHeader().find('.icon-feed-error').css('display', 'inline-block');
		this.resizeFeedIdentifier();
	}
	this.getPageAPI().updateError(errorsCount);
};

/**
 * Returns the DOM element for the navigation
 */
Mashup_Page_API_Layout_Feed.prototype.getElNavigation = function() {
	if (this.elNavigation == undefined) {
		this.elNavigation = $('' +
			'<div class="feed-navigation-item-wrapper feed-draggable feed-droppable">' +
				'<div class="feed">' +
					'<div class="feed-navigation-item-header hover-bgcolor" name="doOpenFeed">' +
						'<span class="feed-color bgcolor" name="doSlideFeed">' +
							'<span class="icon icon-feed-color" name="doSlideFeed"></span>' +
						'</span>' +
						'<span name="doOpenFeed" class="feed-identifier" title="' + this.getId() + '">' +
							this.getId() +
						'</span>' +
						'<span class="feed-header-icons">' +
							'<span name="doOpenFeed" class="icon icon-feed-disabled" title="' + _.FEED_DISABLED() + '"></span>' +
							'<span name="doOpenFeed" class="icon icon-feed-security" title="' + _.FEED_SECURED() + '"></span>' +
							'<span name="doOpenFeed" class="icon icon-feed-synchronized" title="' + _.FEED_SYNCHRONIZED() + '"></span>' +
							'<span name="doOpenFeed" class="icon icon-feed-error"></span>' +
						'</span>' +
						'<span class="icon feed-move-handle" title="' + _.FEED_MOVE() + '"></span>' +
					'</div>' +
					'<div class="feed-navigation-item-content">' +
					'</div>' +
				'</div>' +
				'<div class="drop-zone hidden-drop-zone feed-drop-zone feed-droppable">' +
					_.FEED_DROP_FEEDS_HERE(this.getParentFeed()) +
				'</div>' +
			'</div>'
		);
		this.elNavigation.data('_this', this);

		// appends our feed container
		this.elNavigation.find('> .feed > .feed-navigation-item-content').append(this.getFeedContainer().getEl());

		// appends our trigger container
		this.elNavigation.find('> .feed > .feed-navigation-item-content').append(this.getTriggerContainer().getEl());

		// if no subfeeds then add state class
		if (!this.hasFeeds()) {
			this.elNavigation.addClass('feed-no-subfeeds');
		}

		// if no triggers then add state class
		if (!this.hasTriggers()) {
			this.elNavigation.addClass('feed-no-triggers');
		}

		// if not valid then add state class
		if (!this.isValid()) {
			this.elNavigation.addClass('feed-invalid');
		}

		// close if should be closed
		if (this.state('f')) {
			this.doSlideFeed(true);
		}

		// update feed's icon state
		this.updateFeedDisabled();
		this.updateFeedSecurity();
		this.updateFeedSynchronized();

		// attach event
		this.elNavigation.on('click', $.proxy(this.handleEventOnClick, this));

		// attach draggable
		this.attachDraggable();
	}
	return this.elNavigation;
};

/**
 * Returns the DOM node for the navigation header
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getElNavigationHeader = function() {
	if (this.elNavigationHeader == undefined) {
		this.elNavigationHeader = this.getElNavigation().find('> .feed > .feed-navigation-item-header');
	}
	return this.elNavigationHeader;
};

/**
 * Returns the DOM element for this feed
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
			'<div class="feed">' +
				'<div class="feed-content">' +
				'</div>' +
			'</div>');

		this.el.data('_this', this);
		this.el.prepend(this.getElHeader());

		// bind events
		this.el.on('click', $.proxy(this.handleEventOnClick, this));

		var $feedContent = this.el.find('.feed-content');
		if (this.isValid()) {
			// appends the parameter containers
			var containers = this.getParameterContainers();
			for (var i = 0; i < containers.length; i++) {
				$feedContent.append(containers[i].getEl());
			}
		} else {
			$feedContent.append('<div class="feed-invalid-content">' + _.FEED_UNKNOWN_DEFINITION_TEXT(this.getClassName()) + '</div>');
		}

		this.updateMenuHTML();
		this.onResize();
	}
	return this.el;
};

/**
 * Returns the DOM node for the feed header
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getElHeader = function() {
	if (this.elHeader == undefined) {
		this.elHeader = $('' +
			'<div class="feed-header bgcolor ' + (this.color ? ' ' + this.color : '') + '">' +
				'<span class="feed-name">' + this.getDisplayName() + '</span>' +
				'<span class="has-popup-menu icon icon-properties">' +
					'<ul class="popup-menu popup-menu-right has-popup-icons menu-feed">' +
					'</ul>' +
				'</span>' +
			'</div>'
		);
	}
	return this.elHeader;
};

/**
 * Updates the feed menu HTML according to its actual state
 *
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.updateMenuHTML = function() {
	var sb = new StringBuilder();

	if (!this.isReadOnly()) {
		// adds the configuration sets if several available
		if (this.hasConfigurationSets()) {
			sb.append('<li name="doOpenDefaultConfigurationSets" class="popup-menu-item icon-confsets"><span class="icon icon-menu"></span>' + _.FEED_MENU_DCS() + '</li>');
		}

		if (this.isEnabled()) {
			sb.append('<li name="doToggleStatus" class="popup-menu-item icon-visibility"><span class="icon icon-menu"></span>' + _.FEED_MENU_DISABLE() +  '</li>');
		} else {
			sb.append('<li name="doToggleStatus" class="popup-menu-item icon-visibility"><span class="icon icon-menu"></span>' + _.FEED_MENU_ENABLE() +  '</li>');
		}
		sb.append('<li name="doCopyFeed" class="popup-menu-item icon-copy"><span class="icon icon-menu"></span>' + _.FEED_MENU_COPYTO() +  '</li>');
		sb.append('<li name="doRemoveFeed" class="popup-menu-item icon-delete"><span class="icon icon-menu"></span>' + _.FEED_MENU_DELETE() +  '</li>');
	}

	this.getElHeader().find('.menu-feed').html(sb.toString());
};

/**
 * Moves the current feed to the given container
 * 
 * @param feedContainer
 * @param position
 */
Mashup_Page_API_Layout_Feed.prototype.moveTo = function(feedContainer, position) {
	if (this.getParentFeedContainer() != feedContainer || position != feedContainer.indexOf(this)) {
		var previousFeedContainer = this.getParentFeedContainer();

		this.getParentFeedContainer().unRegisterFeed(this);
		feedContainer.addFeed(this, position);

		// validate previous parent feed container
		if (previousFeedContainer.getParentFeed() != null) {
			previousFeedContainer.getParentFeed().onUpdateSubFeeds();
		}
	}
};

/**
 * Copy this feed to the given page name
 * 
 * @param pageName
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.copyTo = function(pageName) {
	var parentPageName = this.getPageAPI().getPageName();
	var newFeedClass = null;

	/* inverted index of used feed ids */
	var namesTaken = {};
	var targetFeedClasses = mashupBuilder.getPageClass(pageName).getAPI().getFeeds();
	for (var i = 0; i < targetFeedClasses.length; i++) {
		namesTaken[targetFeedClasses[i].getId()] = true;
	}

	/* retrieve the feed names to copy */
	var namesToCopy = [this.getId()];
	var copiedFeedClasses = this.getFeeds(true);
	for (var i = 0; i < copiedFeedClasses.length; i++) {
		namesToCopy.push(copiedFeedClasses[i].getId());
	}

	/* check if we need to rename some feeds */
	var toRename = [];
	for (var i = 0; i < namesToCopy.length; i++) {
		var toName = namesToCopy[i];
		while (namesTaken[toName] == true) {
			toName = 'copy_' + toName;
		}
		if (toName != namesToCopy[i]) {
			toRename.push([namesToCopy[i], toName]);
		}
	}

	/* copy the feed JSON */
	var jsonFeedAsString = $.toJSON(clone(this.getJson()));

	/* strip all the fuids */
	jsonFeedAsString = removeFuids(jsonFeedAsString);

	/* rename the feeds if needed */
	for (var i = 0; i < toRename.length; i++) {
		jsonFeedAsString = replaceFeedId(jsonFeedAsString, toRename[i][0], toRename[i][1]);
		jsonFeedAsString = MELReplace(jsonFeedAsString, toRename[i][0], toRename[i][1]);
	}

	var jsonFeed = $.evalJSON(jsonFeedAsString);

	/* copy the feed to the target page */
	if (parentPageName == pageName) {
		this.getParentFeedContainer().addNewFeed(jsonFeed, this.getParentFeedContainer().indexOf(this) + 1);
	} else {
		window.mashupBuilder.getPageClass(pageName).getAPI().getFeedContainer().addNewFeed(jsonFeed, 0);
	}

	return newFeedClass;
};

/**
 * Called when the window has been resized
 */
Mashup_Page_API_Layout_Feed.prototype.onResize = function() {
	var feedWidth =  window.mashupBuilder.width - 585,  // toolbox(275) timeline(275) padding(35)
		parameterWidth = feedWidth - 240; // label(150) icons(80) padding(10)

	if (feedWidth != this.width) {
		// resize the parameter containers except filtering and properties mapping
		var containers = this.getParameterContainers(),
			containersLength = containers.length;
		for (var i = 0; i < containersLength - 2; i++) {
			var parameters = containers[i].getParameters();
			var parametersLength = parameters.length;
			for (var j = 0; j < parametersLength; j++) {
				parameters[j].setWidth(parameterWidth);
			}
		}

		// resize the properties mapping
		var parameterWidth = feedWidth - 90; // icons(80) padding(10)
		var parameters = this.getPropertiesContainer().getParameters();
		var parametersLength = parameters.length;
		for (var j = 0; j < parametersLength; j++) {
			parameters[j].setWidth(parameterWidth);
		}

		this.width = feedWidth;
	}

	// calls the onResize callbacks
	for (var i = 0; i < this.onResizeCallbacks.length; i++) {
		this.onResizeCallbacks[i].call(this);
	}
};

/**
 * Called when the feed's subfeeds context has been updated
 */
Mashup_Page_API_Layout_Feed.prototype.onUpdateSubFeeds = function() {
	if (!this.hasFeeds() || !this.isSubFeedSupported()) {
		this.getElNavigation().addClass('feed-no-subfeeds');
	} else {
		this.getElNavigation().removeClass('feed-no-subfeeds');
	}
};

/**
 * Called when the feed's trigger context has been updated
 */
Mashup_Page_API_Layout_Feed.prototype.onUpdateTriggerDisplay = function() {
	if (!this.hasTriggers()) {
		this.getElNavigation().addClass('feed-no-triggers');
	} else {
		this.getElNavigation().removeClass('feed-no-triggers');
	}
};

/**
 * Called when an input of the feed gained the focus
 * 
 * @param input
 */
Mashup_Page_API_Layout_Feed.prototype.onInputFocus = function(input) {
	this.getPageAPI().getContext().setParent(this);
	this.getPageAPI().onInputFocus(input);
};

/**
 * Called when the feed is opened
 * 
 * @param input
 */
Mashup_Page_API_Layout_Feed.prototype.onOpen = function() {
	var containers = this.getParameterContainers();
	for (var i = 0; i < containers.length; i++) {
		containers[i].onDisplay();
	}
};

/**
 * Called when the feed is closed
 * 
 * @param input
 */
Mashup_Page_API_Layout_Feed.prototype.onClose = function() {
};

/**
 * Called when a trigger has been created
 */
Mashup_Page_API_Layout_Feed.prototype.onCreateTrigger = function(trigger) {
	if (this.getPageAPI().getPage().isOpen()) {
		Mashup_Config_Validator.checkTrigger(trigger);
		this.onUpdateTriggerDisplay();
	}
	this.onUpdate();
};

/**
 * Called when a trigger has been moved
 */
Mashup_Page_API_Layout_Feed.prototype.onMoveTrigger = function(trigger) {
	if (this.getPageAPI().getPage().isOpen()) {
		this.onUpdateTriggerDisplay();
	}
	this.onUpdate();
};

/**
 * Called when a trigger has been removed
 */
Mashup_Page_API_Layout_Feed.prototype.onRemoveTrigger = function(trigger) {
	if (this.getPageAPI().getPage().isOpen()) {
		this.onUpdateTriggerDisplay();
	}
	this.onUpdate();
};

/**
 * Called when a trigger config has been updated
 */
Mashup_Page_API_Layout_Feed.prototype.onUpdateTrigger = function(trigger) {
	this.onUpdate();
};

/**
 * Called when the feed has been updated
 */
Mashup_Page_API_Layout_Feed.prototype.onUpdate = function() {
	return this.getPageAPI().onUpdate();
};

/**
 * Called when the JSON has been updated
 */
Mashup_Page_API_Layout_Feed.prototype.onUpdateJson = function() {
	if (this.parameterContainers != undefined) {
		var containers = this.parameterContainers;

		var config = $.extend({
			whiteListPatterns: this.json.whiteListPatterns
		}, Mashup_Parameter_Factory.createInvertedConfigParameters(this.json.parameters));

		for (var i = 0; i < containers.length; i++) {
			if (containers[i].getId() == 'properties-mapping') {
				containers[i].properties = this.json.properties;
				containers[i].onUpdateJson();
			} else if (containers[i].getId() != 'feed-configuration') {
				var parameters = containers[i].getParameters();
				for (var j = 0; j < parameters.length; j++) {
					var parameter = parameters[j];
					parameter.setValue(config[parameter.name] ? config[parameter.name] : []);
				}
			}
		}
	}
};

/**
 * Updates the color of this feed
 * 
 * @param position
 * @param level
 */
Mashup_Page_API_Layout_Feed.prototype.updateFeedsColor = function(position, level) {
	// remove old auto color
	if (this.color != null) {
		this.getElHeader().removeClass(this.color);
		this.getElNavigationHeader().removeClass(this.color);
	}
	this.color = 'color' + (position % 8) + (level % 5);
	this.getElHeader().addClass(this.color);
	this.getElNavigationHeader().addClass(this.color);
	this.getFeedContainer().updateFeedsColor(position, level++);
};

/**
 * Updates the disabled display of the feed
 */
Mashup_Page_API_Layout_Feed.prototype.updateFeedDisabled = function() {
	this.getElNavigationHeader().find('.icon-feed-disabled').css('display', !this.isEnabled() ? 'inline-block' : 'none');
	this.resizeFeedIdentifier();
};

/**
 * Updates the security display of the feed
 */
Mashup_Page_API_Layout_Feed.prototype.updateFeedSecurity = function() {
	this.getElNavigationHeader().find('.icon-feed-security').css('display', this.isSecured() ? 'inline-block' : 'none');
	this.resizeFeedIdentifier();
};

/**
 * Updates the synchronize display of the feed
 */
Mashup_Page_API_Layout_Feed.prototype.updateFeedSynchronized = function() {
	this.getElNavigationHeader().find('.icon-feed-synchronized').css('display', this.isSynchronized() ? 'inline-block' : 'none');
	this.resizeFeedIdentifier();
};

/**
 *  Called to resize the feed identifier according to the visible icons
 */
Mashup_Page_API_Layout_Feed.prototype.resizeFeedIdentifier = function() {
	var width = 180;
	if (this.hasErrors()) width -= 20;
	if (this.isSecured()) width -= 20;
	if (this.isSynchronized()) width -= 20;
	if (!this.isEnabled()) width -= 20;
	this.getElNavigationHeader().find('.feed-identifier').css('width', width);
};

/**
 * Updates the ActAsFeed parameter of the feed
 * 
 * @param feeds
 */
Mashup_Page_API_Layout_Feed.prototype.updateActAsFeed = function(feeds) {
	if (this.parameterContainers == undefined) {
		return;
	}

	var parameter = this.getParameter('actAsFeed');
	if (parameter != null) {
		if (feeds.length == 1) {
			parameter.setValue('', false /* do not focus */);
			parameter.getEl().hide();
		} else {
			var input = parameter.getValuesClass()[0].getInput(0);
			if (typeof(input.setOptions) == 'function') {
				var options = [''];
				for (var i = 0; i < feeds.length; i++) {
					if ((feedId = feeds[i].getId()) != this.getId()) {
						options.push(feedId);
					}
				}
				input.setOptions(options);
			}
			parameter.getEl().show().blur();
		}
	}
};

/**
 * Attaches the draggable for this feed
 */
Mashup_Page_API_Layout_Feed.prototype.attachDraggable = function() {
	var _this = this;
	this.getElNavigation().draggable({
		revert: 'invalid',
		zIndex: 4200,
		distance: 10,
		handle: '.feed-navigation-item-header',
		helper: function() {
			return $('' +
				'<div class="feed-navigation-item-header ' + _this.color + '" style="cursor:move;">' +
					'<span class="feed-color bgcolor">' +
						'<span class="icon icon-feed-color"></span>' +
					'</span>' +
					'<span class="feed-identifier">' + _this.getId() + '</span>' +
					'<span class="icon feed-move-handle"></span>' +
				'</div>'
			);
		},
		appendTo: '#page-wrapper',
		start: dragFeedStart,
		stop: dragFeedStop
	});
};

/**
 * Initialize the state of the feed
 */
Mashup_Page_API_Layout_Feed.prototype.initState = function() {
	// compute the default value & index array
	this.stateIdxArray = ['f'];
	var tmp = [];
	tmp.push(0); // feed opened/closed
	// retrieve the parameters for this feed
	var containers = Mashup_Parameter_Factory.createFeedParameters(this.getClassName());
	for (var i = 0; i < containers.length; i++) {
		this.stateIdxArray.push(containers[i].container.id);
		tmp.push(i > 0 ? 1 : 0);
	}
	this.stateDefaultValue = tmp.join('!');
};

/**
 * Gets or sets a state for this feed
 * 
 * @param key
 * @param defaultValue
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.state = function(key, value) {
	var idx = this.stateIdxArray.indexOf(key);
	var state = window.mashupBuilder.state.get('f', this.getFuid(), this.stateDefaultValue).split('!');
	if (value == undefined) {
		// cast 1 as true and 0 as false
		return state[idx] == 1;
	} else {
		state[idx] = value;
		var newState = state.join('!');
		window.mashupBuilder.state.save('f', this.getFuid(), newState == this.stateDefaultValue ? null : newState);
	}
};

/**
 * Registers a custom onResize callback
 * 
 * @param func
 */
Mashup_Page_API_Layout_Feed.prototype.registerOnResize = function(func) {
	this.onResizeCallbacks.push(func);
};

/**
 * Unregisters a custom onResize callback
 * 
 * @param func
 */
Mashup_Page_API_Layout_Feed.prototype.unregisterOnResize = function(func) {
	if ((idx = this.onResizeCallbacks.indexOf(func)) != -1) {
		this.onResizeCallbacks.splice(idx, 1);
	}
};

/**
 * Removes this feed from the DOM
 */
Mashup_Page_API_Layout_Feed.prototype.remove = function() {
	this.getFeedContainer().remove();
	this.getTriggerContainer().remove();

	if (this.elNavigation != undefined) {
		this.elNavigation.remove();
		this.elNavigation = undefined;
		this.elNavigationHeader = undefined;
	}

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}

	if (this.parameterContainers != undefined) {
		for (var i = 0; i < this.parameterContainers.length; i++) {
			this.parameterContainers[i].remove();
		}
	}
};

/**
 * Returns this feed as JSON
 * 
 * @returns
 */
Mashup_Page_API_Layout_Feed.prototype.getJson = function() {
	if (this.parameterContainers != undefined) {
		var containers = this.parameterContainers,
			containersLength = containers.length;

		// something is wrong here
		if (containersLength < 3) {
			return this.json;
		}

		// compute the properties (must be call first)
		this.json.properties = containers[containersLength - 1].getJson();

		// compute the parameters
		this.json.parameters = [];
		for (var i = 1; i < containersLength - 2; i++) {
			this.json.parameters = $.merge(this.json.parameters, containers[i].getJson());
		}

		// compute the feed options
		this.json.actAsFeed = null; //  must init actAsFeed as null (empty) because getJson won't return any value if not set
		var feedOptions = containers[0].getJson();
		for (var i = 0; i < feedOptions.length; i++) {
			if (feedOptions[i].name == 'embed' || feedOptions[i].name == 'synchronized') {
				this.json[feedOptions[i].name] = (feedOptions[i].value === 'true');
			} else if (feedOptions[i].name == 'actAsFeed') {
				this.json[feedOptions[i].name] = feedOptions[i].value;
			} else if (feedOptions[i].name != 'feedId') {
				this.json.parameters.push(feedOptions[i]);
			}
		}

		// compute the whitelist parameters
		this.json.whiteListPatterns = [];
		var parametersFiltering = containers[containersLength - 2].getJson();
		for (var i = 0; i < parametersFiltering.length; i++) {
			this.json.whiteListPatterns.push(parametersFiltering[i].value);
		}
	}

	// compute the JSON of the subfeeds
	this.json.subfeeds = this.getFeedContainer().getJson();

	// compute the JSON of triggers
	this.json.triggers = this.getTriggerContainer().getJson();

	return this.json;
};

/**
 * File: /resources/mashupBuilder/js/Page/API/Layout/FeedContainer.js
 */
/**
 * Contains a set of Feeds
 *
 * @constructor
 * @this {Mashup_Page_API_Layout_FeedContainer}
 */
function Mashup_Page_API_Layout_FeedContainer(parentFeed, jsonFeeds, level, position) {
	this.supportManyFeeds = mashupBuilder.permission.canHaveManyFeeds();
	this.parentFeed = parentFeed;
	this.level = level;
	this.position = position;

	this.feeds = [];
	for (var i = 0; i < jsonFeeds.length; i++) {
		this.registerNewFeed(jsonFeeds[i], i);
	}
}

/**
 * Returns whether or not the feed container has feeds
 */
Mashup_Page_API_Layout_FeedContainer.prototype.hasFeeds = function() {
	return this.feeds.length > 0;
};

/**
 * Returns the index of the given widget in this container
 *
 * @param widget
 * @returns
 */
Mashup_Page_API_Layout_FeedContainer.prototype.indexOf = function(feed) {
	return this.feeds.indexOf(feed);
};

/**
 * jQuery onClick event handler
 *
 * @param e
 * @returns {Boolean}
 */
Mashup_Page_API_Layout_FeedContainer.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'doOpenFeed':
			this.getPageAPI().openFeed(target.closest('.feed-navigation-item-wrapper').data('_this'));
			return false;
		}
	}
	return true;
};

/**
 * Returns the DOM element for this container
 *
 * @returns
 */
Mashup_Page_API_Layout_FeedContainer.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
			'<div class="feed-container">' +
				'<div class="drop-zone feed-drop-zone feed-droppable">' +
					_.FEED_DROP_FEEDS_HERE(this.getParentFeed()) +
				'</div>' +
			'</div>'
		);
		this.el.data('_this', this);
		this.elDropZone = this.el.find('.drop-zone');

		for (var i = 0; i < this.feeds.length; i++) {
			this.el.append(this.feeds[i].getElNavigation());
		}

		this.el.on('click', $.proxy(this.handleEventOnClick, this));
	}
	return this.el;
};

/**
 * Register a new feed in this container
 *
 * @private
 * @param jsonFeed
 * @param insertAtPosition
 * @returns {Mashup_Page_API_Layout_Feed}
 */
Mashup_Page_API_Layout_FeedContainer.prototype.registerNewFeed = function(jsonFeed, insertAtPosition) {
	var feed = new Mashup_Page_API_Layout_Feed(this, jsonFeed);
	this.feeds.splice(insertAtPosition, 0, feed);
	return feed;
};

/**
 * Register a feed in the container
 *
 * @private
 * @param feed
 * @param insertAtPosition
 */
Mashup_Page_API_Layout_FeedContainer.prototype.registerFeed = function(feed, insertAtPosition) {
	feed.setParentFeedContainer(this);
	this.feeds.splice(insertAtPosition, 0, feed);
};

/**
 * Unregister a feed from the container
 *
 * @param feed
 */
Mashup_Page_API_Layout_FeedContainer.prototype.unRegisterFeed = function(feed) {
	this.feeds.splice(this.feeds.indexOf(feed), 1);
	feed.getEl().detach();
};

/**
 * Register and adds a new feed in this container
 *
 * @param jsonFeed
 * @param insertAtPosition
 * @returns
 */
Mashup_Page_API_Layout_FeedContainer.prototype.addNewFeed = function(jsonFeed, insertAtPosition) {
	// validate against the license
	if (this.supportManyFeeds == false) {
		if (this.feeds.length > 0 || this.getParentFeed() != null) {
			new Mashup_Popup_Alert({
				title: _.LICENSE_RESTRICTION_TITLE(),
				text: _.LICENSE_RESTRICTION_FEED_DESCRIPTION(),
				level: BoxLevel.ERROR
			}).show();
			return null;
		}
	}

	var feed = this.registerNewFeed(jsonFeed, insertAtPosition);

	if (this.el != undefined) {
		if (insertAtPosition == undefined || insertAtPosition == 0) {
			feed.getElNavigation().insertAfter(this.elDropZone);
		} else {
			feed.getElNavigation().insertAfter(this.feeds[insertAtPosition - 1].getElNavigation());
		}
	}

	this.getPageAPI().onCreateFeed(feed);

	return feed;
};

/**
 * Register and adds the given feed in this container
 *
 * @param feed
 * @param insertAtPosition
 */
Mashup_Page_API_Layout_FeedContainer.prototype.addFeed = function(feed, insertAtPosition) {
	this.registerFeed(feed, insertAtPosition);

	if (this.el != undefined) {
		if (insertAtPosition == 0) {
			feed.getElNavigation().insertAfter(this.elDropZone);
		} else {
			feed.getElNavigation().insertAfter(this.feeds[insertAtPosition - 1].getElNavigation());
		}

		if ((parentFeed = this.getParentFeed()) != null) {
			parentFeed.getElNavigation().removeClass('feed-no-subfeeds');
		}
	}

	this.getPageAPI().onMoveFeed(feed);
};

/**
 * Removes a feed from this container
 *
 * @param feed
 */
Mashup_Page_API_Layout_FeedContainer.prototype.removeFeed = function(feed) {
	feed.remove();
	this.feeds.splice(this.feeds.indexOf(feed), 1);

	if (this.el != undefined) {
		if ((parentFeed = this.getParentFeed()) != null) {
			parentFeed.getElNavigation().addClass('feed-no-subfeeds');
		}
	}

	this.getPageAPI().onRemoveFeed(feed);
};
/**
 * Returns whether or not the feed container has feeds
 */
Mashup_Page_API_Layout_FeedContainer.prototype.hasFeeds = function() {
	return this.feeds.length > 0;
};

/**
 * Returns all the feeds
 *
 * @param recursive
 * @returns {Array}
 */
Mashup_Page_API_Layout_FeedContainer.prototype.getFeeds = function(recursive) {
	var feeds = [];
	for (var i = 0; i < this.feeds.length; i++) {
		feeds.push(this.feeds[i]);
		$.merge(feeds, this.feeds[i].getFeeds(recursive));
	}
	return feeds;
};

/**
 * Returns a feed by its ID
 *
 * @param feedId
 * @param recursive
 * @returns {Mashup_Page_API_Layout_Feed}
 */
Mashup_Page_API_Layout_FeedContainer.prototype.getFeed = function(feedId, recursive) {
	for (var i = 0 ; i < this.feeds.length; i++) {
		if (this.feeds[i].getId() == feedId) {
			return this.feeds[i];
		} else if (recursive == true) {
			if ((feed = this.feeds[i].getFeedContainer().getFeed(feedId, recursive)) != null) {
				return feed;
			}
		}
	}
	return null;
};

/**
 * Returns the parent Feed
 *
 * @returns {Mashup_Page_API_Layout_Feed}
 */
Mashup_Page_API_Layout_FeedContainer.prototype.getParentFeed = function() {
	if (this.parentFeed.constructor == Mashup_Page_API_Layout_Feed) {
		return this.parentFeed;
	}
	return null;
};

/**
 * Returns the Mashup API
 *
 * @returns {Mashup_Page_API}
 */
Mashup_Page_API_Layout_FeedContainer.prototype.getPageAPI = function() {
	if (this.parentFeed.constructor == Mashup_Page_API_Layout_Feed) {
		return this.parentFeed.getPageAPI();
	}
	return this.parentFeed;
};

/**
 * Updates the color of the feeds in this container
 *
 * @param position
 * @param level
 */
Mashup_Page_API_Layout_FeedContainer.prototype.updateFeedsColor = function(position, level) {
	for (var i = 0; i < this.feeds.length; i++) {
		if (level == -1) {
			this.feeds[i].updateFeedsColor(position++, level + 1);
		} else {
			this.feeds[i].updateFeedsColor(position, level + 1);
		}
	}
};

/**
 * Removes this container from the DOM
 */
Mashup_Page_API_Layout_FeedContainer.prototype.remove = function() {
	for (var i = 0; i < this.feeds.length; i++) {
		this.feeds[i].remove();
	}
	this.feeds = [];

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Returns this container as JSON
 *
 * @returns {Array}
 */
Mashup_Page_API_Layout_FeedContainer.prototype.getJson = function() {
	var json = [];
	for (var i = 0 ; i < this.feeds.length; i++) {
		json.push(this.feeds[i].getJson());
	}
	return json;
};


/**
 * File: /resources/mashupBuilder/js/Page/Code.js
 */
/*
 * Mashup_Page_Code.js
 */

/*
 * Constructor
 */
function Mashup_Page_Code(page) {
	this.errorsCount = 0;
	this.page = page;
}

/**
 * Returns whether or not the DOM is built
 */
Mashup_Page_Code.prototype.isDOMReady = function() {
	return this.el != undefined;
};

/**
 * Returns the page for this UI
 * 
 * @returns {Mashup_Page}
 */
Mashup_Page_Code.prototype.getId = function() {
	return this.page.getName();
};

/**
 * Returns the page name
 * 
 * Used by the navigation toolbox (behave like a page)
 * 
 * @returns
 */
Mashup_Page_Code.prototype.getName = function() {
	return this.page.getName();
};

/**
 * Returns the page name
 * 
 * @returns
 */
Mashup_Page_Code.prototype.getPageName = function() {
	return this.page.getName();
};

/**
 * Returns the page for this UI
 * 
 * @returns {Mashup_Page}
 */
Mashup_Page_Code.prototype.getPage = function() {
	return this.page;
};

/**
 * Returns the context menu
 * 
 * @returns {Mashup_Window_TabMenu_Context}
 */
Mashup_Page_Code.prototype.getContext = function() {
	if (this.context == undefined) {
		this.context = new Mashup_Window_TabMenu_Context({ showClose: true });
		this.context.setParent(this);
		this.context.getEl().hide().appendTo(this.getEl());
		this.context.resize(undefined, 450);
	}
	return this.context;
};

/**
 * Returns the messages handler
 * 
 * @returns {Mashup_Control_Messages}
 */
Mashup_Page_Code.prototype.getMessages = function() {
	return this.page.getMessages();
};

/**
 * Returns whether the UI is visible or not
 * 
 * @returns
 */
Mashup_Page_Code.prototype.isVisible = function() {
	return this.page.isVisible();
};

/**
 * Returns whether the UI is readonly or not
 * 
 * @returns
 */
Mashup_Page_Code.prototype.isReadOnly = function() {
	return this.page.getUI().isReadOnly();
};

/**
 * Returns whether the UI is updated or not
 * 
 * @returns {Boolean}
 */
Mashup_Page_Code.prototype.isUpdated = function() {
	return this.page.isUpdated();
};

/**
 * Returns whether the Mashup UI is up-to-date or not
 */
Mashup_Page_Code.prototype.isUpToDate = function() {
	return this.page.getUI().isUpToDate();
};

/**
 * Called when something has been updated within the Mashup UI
 * 
 * @returns {Boolean}
 */
Mashup_Page_Code.prototype.onUpdate = function() {
};

/**
 * Called when the window is resized
 */
Mashup_Page_Code.prototype.onResize = function() {
	if (this.context != undefined) {
		this.getContext().resize(undefined, 450);
	}
	var tabs = this.getTabs();
	for (var i = 0; i < tabs.length; i++) {
		tabs[i].onResize();
	}
};

/**
 * Returns whether this tab can be opened or not
 */
Mashup_Page_Code.prototype.canOpen = function() {
	return true;
};

/**
 * Called when this tab is open
 */
Mashup_Page_Code.prototype.onOpen = function() {
	this.onResize();
	window.mashupBuilder.getToolboxContainer().hideToolboxes();
	window.mashupBuilder.getToolboxContainer().getToolbox('navigation').setActivePage(this);
	this.openTab(window.mashupBuilder.parseAnchor()[1]);
	$('body').addClass('workspace-fullscreen');
	this.getElBottomTabs().addClass('selected');
};

/**
 * Called when this tab is closed
 */
Mashup_Page_Code.prototype.onClose = function() {
	this.getElBottomTabs().removeClass('selected');
	$('body').removeClass('workspace-fullscreen');
	window.mashupBuilder.getToolboxContainer().getToolbox('navigation').setActivePage(null);
	this.getTab(this.getOpenedTabName()).onClose();
};

/**
 * Called if the config has been updated
 */
Mashup_Page_Code.prototype.redraw = function() {
	for (var i = 0; i < this.tabs.length; i++) {
		this.tabs[i].redraw();
		if (this.getOpenedTabName() == this.tabs[i].getId()) {
			this.tabs[i].onOpen();
		}
	}
};

/**
 * Updates the error count for the Page UI
 * 
 * @param {number}
 *            errorsCount Can be positive (more errors) or negative (less
 *            errors)
 */
Mashup_Page_Code.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;
	if (this.errorsCount == 0) {
		this.getElBottomTabsErrors().css('display', 'none');
	} else {
		this.getElBottomTabsErrors().css('display', 'inline-block').html(
				this.errorsCount);
	}
	this.getPage().updateError(errorsCount);
};

/*
 * Dom Manipulation
 */
Mashup_Page_Code.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('<div id="code-workspace" class="workspace"></div>');
		this.el.data('_this', this);

		var tabs = this.getTabs();
		for (var i = 0; i < tabs.length; i++) {
			tabs[i].getEl().appendTo(this.el);
			tabs[i].getEl().addClass('moveToMoon');
		}

		this.el.on('click', $.proxy(this.handleEventOnClick, this));
	}
	return this.el;
};

/**
 * jQuery onClick event handler
 * 
 * @param e
 */
Mashup_Page_Code.prototype.handleEventOnClick = function(e) {
	this.getContext().onInputFocus(undefined);
	return true;
};

/**
 * Returns the default tab name for this page
 * 
 * @returns
 */
Mashup_Page_Code.prototype.getDefaultTabName = function() {
	if (this.openedTabName != undefined) {
		return this.openedTabName;
	}
	var tabs = this.getTabs();
	for (var i = 0; i < tabs.length; i++) {
		if (tabs[i].isVisible()) {
			return tabs[i].getId();
		}
	}
	return null;
};

/**
 * Returns the name of the opened tab
 * 
 * @returns
 */
Mashup_Page_Code.prototype.getOpenedTabName = function() {
	return this.openedTabName;
};

/**
 * Returns the tabs (lazy-loaded)
 * 
 * @param tabName
 * @returns
 */
Mashup_Page_Code.prototype.getTabs = function() {
	if (this.tabs == undefined) {
		this.tabs = [];
		this.tabs.push(new Mashup_Page_Code_Styles(this));
		this.tabs.push(new Mashup_Page_Code_Javascript(this));
	}
	return this.tabs;
};

/**
 * Returns a Mashup Tab class
 * 
 * @param tabName
 * @returns
 */
Mashup_Page_Code.prototype.getTab = function(tabName) {
	var tabs = this.getTabs();
	for (var i = 0; i < tabs.length; i++) {
		if (tabs[i].getId() == tabName) {
			return tabs[i];
		}
	}
	return null;
};

/**
*
* @param tabName
* @returns the focused tabName. null otherwise
*/
Mashup_Page_Code.prototype.openTab = function(tabName) {
	var tab = this.getTab(tabName);

	// fallback to default tab if none given or unable to access it
	if (tab == null || (this.openedTabName == null && tab.isVisible() == false)) {
		tabName = this.getDefaultTabName();
		tab = this.getTab(tabName);
	}

	// if invalid given tab then skip
	if (tab.isVisible() == false) {
		return null;
	}

	// if already opened then trigger onOpen and skip
	if (this.openedTabName == tabName) {
		tab.onOpen();
		return tabName;
	}

	// close the opened tab
	if (this.getTab(this.openedTabName) != null) {
		this.getTab(this.openedTabName).getEl().addClass('moveToMoon');
		this.getTab(this.openedTabName).onClose();
	}

	// opens the new tab
	tab.onOpen();
	tab.getEl().removeClass('moveToMoon');
	this.openedTabName = tabName;

	return tabName;
};

/* ElBottomTabs */
Mashup_Page_Code.prototype.getElBottomTabs = function() {
	if (this.elTopTabs == null) {
		this.elTopTabs = $(''
				+ '<li class="page-bottom-tab mashupCode" name="doOpenMashupCode">'
				+ '<span class="typeId" name="doOpenMashupCode">'
				+ _.PAGECODE_TAB_TITLE() + '</span> '
				+ '<span class="icon icon-error" name="doOpenError" title="'
				+ _.MB_ERROR_CLICK_TO_SEE() + '">' + this.errorsCount
				+ '</span>' + '</li>');
	}
	return this.elTopTabs;
};

Mashup_Page_Code.prototype.getElBottomTabsErrors = function() {
	if (this.elErrors == null) {
		this.elErrors = this.getElBottomTabs().find('.icon-error');
	}
	return this.elErrors;
};

/**
 * Removes from the DOM
 */
Mashup_Page_Code.prototype.remove = function() {
	this.openedTabName = undefined;

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}

	if (this.elTopTabs != undefined) {
		this.elTopTabs.remove();
		this.elTopTabs = undefined;
	}

	if (this.tabs != undefined) {
		for (var i = 0; i < this.tabs.length; i++) {
			this.tabs[i].remove();
		}
	}
};


/**
 * File: /resources/mashupBuilder/js/Page/Code/Styles.js
 */
/**
 * Implementation of a page for the page styles
 * 
 * @constructor
 * @param page
 * @returns
 */
Inherit(Mashup_Page_Code_Styles, Mashup_Application_Abstract);
function Mashup_Page_Code_Styles(page) {
	Mashup_Page_Code_Styles.superclass.constructor.call(this, page, {
		id: 'styles',
		label: _.PAGE_MENU_ITEM_STYLES(),
		group: _.PAGE_MENU_GROUP_GENERAL()
	});
}

/**
 * Initialize the parameters for the styles
 */
Mashup_Page_Code_Styles.prototype.getContainers = function() {
	if (this.containers == undefined) {
		this.containers = [];

		var config = Mashup_Parameter_Factory.createInvertedConfigParameters(this.getPage().getPage().getUI().json.parameters);
		var properties = Mashup_Parameter_Factory.createPageParameters();
		for (var i = 0; i < properties.length; i++) {
			if (properties[i].container.id != 'styles') {
				continue;
			}

			var parameters = properties[i].parameters;
			for (var j = 0; j < parameters.length; j++) {
				var parameter = parameters[j];

				var parameterContainer = new Mashup_Parameter_Container({
					label: parameter.label,
					parent: this,
					showLabel: false,
					showToggle: false
				});

				parameterContainer.addParameter(new Mashup_Parameter(window.defaultJsonCustomComponentParameter['class'], $.extend(parameters[j], {
					width: 600,
					values: config[parameter.name] ? config[parameter.name] : [],
					showLabels: false,
					onChangeCallback: $.proxy(this.getPage().getPage().getUI().onUpdateParameters, this.getPage().getPage().getUI())
				})));

				this.containers.push(parameterContainer);
			}
		}
	}
	return this.containers;
};

/**
 * Sets the page as read only
 */
Mashup_Page_Code_Styles.prototype.isReadOnly = function() {
	return this.getPage().isReadOnly();
};

/**
 * Returns the DOM element for this page
 * 
 * @returns
 */
Mashup_Page_Code_Styles.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="page-styles" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);
	}
	return this.el;
};

/**
 * Called when the tab is opened
 */
Mashup_Page_Code_Styles.prototype.onOpen = function() {
	if (this.getEl().html().length == 0) {
		var containers = this.getContainers();
		for (var i = 0; i < containers.length; i++) {
			this.getEl().append(containers[i].getEl());
		}
	} else {
		var containers = this.getContainers();
		for (var i = 0; i < containers.length; i++) {
			containers[i].onDisplay();
		}
	}
	Mashup_Page_Code_Styles.superclass.onOpen.call(this);
};

/**
 * Called when the config has been updated
 */
Mashup_Page_Code_Styles.prototype.redraw = function() {
	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}

	if (this.el != undefined) {
		var $backup = this.el;
		this.el = undefined;
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();
	}
};

/**
 * Remove the page from the DOM
 */
Mashup_Page_Code_Styles.prototype.remove = function() {
	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Page/Code/Javascript.js
 */
/**
 * Implementation of a page for the page javascript
 * 
 * @constructor
 * @param page
 * @returns
 */
Inherit(Mashup_Page_Code_Javascript, Mashup_Application_Abstract);
function Mashup_Page_Code_Javascript(page) {
	Mashup_Page_Code_Javascript.superclass.constructor.call(this, page, {
		id: 'javascript',
		label: _.PAGE_MENU_ITEM_JAVASCRIPT(),
		group: _.PAGE_MENU_GROUP_GENERAL()
	});
}

/**
 * Initialize the parameters for the javascript
 */
Mashup_Page_Code_Javascript.prototype.getContainers = function() {
	if (this.containers == undefined) {
		this.containers = [];

		var config = Mashup_Parameter_Factory.createInvertedConfigParameters(this.getPage().getPage().getUI().json.parameters);
		var properties = Mashup_Parameter_Factory.createPageParameters();
		for (var i = 0; i < properties.length; i++) {
			if (properties[i].container.id != 'javascript') {
				continue;
			}

			var parameters = properties[i].parameters;
			for (var j = 0; j < parameters.length; j++) {
				var parameter = parameters[j];

				var parameterContainer = new Mashup_Parameter_Container({
					label: parameter.label,
					parent: this,
					showLabel: false,
					showToggle: false
				});

				parameterContainer.addParameter(new Mashup_Parameter(window.defaultJsonCustomComponentParameter['class'], $.extend(parameters[j], {
					width: 600,
					values: config[parameter.name] ? config[parameter.name] : [],
					showLabels: false,
					onChangeCallback: $.proxy(this.getPage().getPage().getUI().onUpdateParameters, this.getPage().getPage().getUI())
				})));

				this.containers.push(parameterContainer);
			}
		}
	}
	return this.containers;
};

/**
 * Sets the page as read only
 */
Mashup_Page_Code_Javascript.prototype.isReadOnly = function() {
	return this.getPage().isReadOnly();
};

/**
 * Returns the DOM element for this page
 * 
 * @returns
 */
Mashup_Page_Code_Javascript.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="page-javascript" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);
	}
	return this.el;
};

/**
 * Called when the tab is opened
 */
Mashup_Page_Code_Javascript.prototype.onOpen = function() {
	if (this.getEl().html().length == 0) {
		var containers = this.getContainers();
		for (var i = 0; i < containers.length; i++) {
			this.getEl().append(containers[i].getEl());
		}
	} else {
		var containers = this.getContainers();
		for (var i = 0; i < containers.length; i++) {
			containers[i].onDisplay();
		}
	}
	Mashup_Page_Code_Javascript.superclass.onOpen.call(this);
};

/**
 * Called when the config has been updated
 */
Mashup_Page_Code_Javascript.prototype.redraw = function() {
	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}

	if (this.el != undefined) {
		var $backup = this.el;
		this.el = undefined;
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();
	}
};

/**
 * Remove the page from the DOM
 */
Mashup_Page_Code_Javascript.prototype.remove = function() {
	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Page/UI.js
 */
/*
 * Mashup_Page_UI.js
 */

/*
 * Constructor
 */
function Mashup_Page_UI(page, jsonMashupPage) {
	this.page = page;
	this.json = jsonMashupPage;

	this.errorsCount = 0;
	this.bottomToolbar = null;
}

/**
 * Returns whether or not the DOM node has been created
 */
Mashup_Page_UI.prototype.isDOMReady = function() {
	return this.el != undefined;
};

/**
 * Returns the page name
 * 
 * @returns
 */
Mashup_Page_UI.prototype.getPageName = function() {
	return this.page.getName();
};

/**
 * Returns the page for this UI
 * 
 * @returns {Mashup_Page}
 */
Mashup_Page_UI.prototype.getPage = function() {
	return this.page;
};

/**
 * Returns the trigger container of the page triggers
 * 
 * @returns {Mashup_Page_Layout_TriggerContainer}
 */
Mashup_Page_UI.prototype.getTriggerContainer = function() {
	if (this.triggerContainer == undefined) {
		this.triggerContainer = new Mashup_Page_Layout_TriggerContainer('designTrigger', this, this.json.triggers);
	}
	return this.triggerContainer;
};

/**
 * Returns the table layout of the page
 * 
 * @returns {array}
 */
Mashup_Page_UI.prototype.getLayout = function() {
	if (this.layout == undefined) {
		this.layout = new Mashup_Page_UI_Layout_TableLayout(this, this.json.layout);
	}
	return this.layout;
};

/**
 * Returns all the widgets of the page
 * 
 * @param recursive
 * @returns
 */
Mashup_Page_UI.prototype.getWidgets = function(recursive) {
	return this.getLayout().getWidgets(recursive);
};

/**
 * Returns all the rows of the page
 * 
 * @returns
 */
Mashup_Page_UI.prototype.getRows = function() {
	return this.getLayout().getRows();
};

/**
 * Returns all the cells of the page
 * 
 * @returns
 */
Mashup_Page_UI.prototype.getCells = function() {
	return this.getLayout().getCells();
};

/**
 * Returns the page width format ('px' or '%')
 * 
 * @returns
 */
Mashup_Page_UI.prototype.getWidthFormat = function() {
	return this.getLayout().getWidthFormat();
};

/**
 * Returns the page width
 * 
 * @returns
 */
Mashup_Page_UI.prototype.getWidth = function() {
	return this.getLayout().getWidth();
};

/**
 * Returns whether the UI is visible or not
 * 
 * @returns
 */
Mashup_Page_UI.prototype.isVisible = function() {
	if (this.isUIVisible == null) {
		this.isUIVisible = window.mashupBuilder.hasMashupUI() && window.mashupBuilder.permission.canViewPageTab(this.page.getName(), 'ui');
	}
	return this.isUIVisible;
};

/**
 * Returns whether the UI is readonly or not
 * 
 * @returns
 */
Mashup_Page_UI.prototype.isReadOnly = function() {
	if (window.widgetBuilder != null) {
		return !window.mashupBuilder.permission.canUpdateWidgetBuilder();
	}
	return !window.mashupBuilder.permission.canUpdatePageTab(this.page.getName(), 'ui') || !this.isUpToDate();
};

/**
 * Returns whether the UI is updated or not
 * 
 * @returns {Boolean}
 */
Mashup_Page_UI.prototype.isUpdated = function() {
	return this.json.somethingChanged == true;
};

/**
 * Returns whether the Mashup UI is up-to-date or not
 */
Mashup_Page_UI.prototype.isUpToDate = function() {
	return window.mashupBuilder.concurrency.isPageUIUpToDate(this.getPageName());
};

/**
 * Called when a parameter has been updated within the Mashup UI
 */
Mashup_Page_UI.prototype.onUpdateParameters = function() {
	// save new page parameters
	this.json.parameters = this.getJsonParameters();

	// displays the security icon if needed
	this.getPage().showSecurityIcon(this.getPage().isSecurityEnable());

	// displays the preview tab if not secured
	this.getPage().getPreview().getElBottomTabs().css('display', this.getPage().isSecurityEnable() ? 'none' : 'inline-block');

	// trigger an update on the UI
	this.onUpdate();
};

/**
 * Called when something has been updated within the Mashup UI
 * 
 * @returns {Boolean}
 */
Mashup_Page_UI.prototype.onUpdate = function() {
	if (this.json.somethingChanged != true) {
		this.json.somethingChanged = true;
		if (this.json.id != null) {
			window.mashupBuilder.concurrency.postEvent(ConcurrencyEventType.EDITPAGE, this.json.id);
		}
		window.mashupBuilder.somethingChanged();
	}
};

/**
 * Called when a widget has been moved in the page
 * 
 * @param widget
 */
Mashup_Page_UI.prototype.onMoveWidget = function(widget) {
	if (this.getPage().isOpen()) {
		// update feed's context of the moved widget
		widget.redrawElUseFeeds();
		widget.checkUseFeeds();

		// update widget's context for its new parent widget
		if ((parentWidget = widget.getParentWidget()) != null) {
			parentWidget.checkUseWidgets();
			parentWidget.onUpdateSubWidgets();
		}

		// update feed's context for all its subwidgets
		var subwidgets = widget.getWidgets(true);
		for ( var i = 0; i < subwidgets.length; i++) {
			subwidgets[i].redrawElUseFeeds();
			subwidgets[i].checkUseFeeds();
		}
	}

	// propagate update event
	this.onUpdate();
};

/**
 * Called when a widget has been created
 * 
 * @param {Mashup_Page_UI_Layout_Widget}
 *            widget
 */
Mashup_Page_UI.prototype.onCreateWidget = function(widget) {
	if (this.getPage().isOpen()) {
		// if widget must have feeds but have none
		if (!widget.canHaveNoFeeds() && !widget.hasFeeds() && !widget.isParentEntryUsed()) {
			var availableFeeds = widget.getAvailableFeeds();
			if (availableFeeds.feeds.length == 1) {
				// By default, use the first feed
				widget.setFeedIds([availableFeeds.feeds[0].getId()]);
				widget.redrawElUseFeeds();
			} else if (availableFeeds.parentFeed != null) {
				// otherwise, use the parent entry
				widget.setIsParentEntryUsed(true);
				widget.redrawElUseFeeds();
			}
		}

		if ((parentWidget = widget.getParentWidget()) != null) {
			parentWidget.checkUseWidgets();
			parentWidget.onUpdateSubWidgets();
		}

		Mashup_Config_Validator.checkWidget(widget);
		widget.setViewMode(window.mashupBuilder.getViewMode());
	}
	this.onUpdate();
};

/**
 * Called when a widget is removed from the page
 * 
 * @param widget
 */
Mashup_Page_UI.prototype.onRemoveWidget = function(widget) {
	var state = window.mashupBuilder.state;
	(function(widget) {
		state.remove('w', widget.getWuid());
		var subwidgets = widget.getWidgets(true);
		for ( var i = 0; i < subwidgets.length; i++) {
			arguments.callee(subwidgets[i]);
		}
	})(widget);

	if (this.getPage().isOpen()) {
		if ((parentWidget = widget.getParentWidget()) != null) {
			parentWidget.checkUseWidgets();
			parentWidget.onUpdateSubWidgets();
		}
	}

	this.onUpdate();
};

/**
 * Returns whether this trigger is supported or not
 */
Mashup_Page_UI.prototype.canSupportTrigger = function(className) {
	var definition = getTriggerDefinition(className);
	return definition && (definition.type == TRIGGER_TYPES.PreRequestTrigger || definition.type == TRIGGER_TYPES.MashupPageTrigger || definition.type == TRIGGER_TYPES.MashupTrigger);
};

/**
 * Returns whether this tab can be opened or not
 */
Mashup_Page_UI.prototype.canOpen = function() {
	return true;
};

/**
 * Called when this tab is open
 */
Mashup_Page_UI.prototype.onOpen = function() {
	if (this.isReadOnly()) {
		window.mashupBuilder.getEl().addClass('design-readonly');
	} else {
		window.mashupBuilder.getEl().removeClass('design-readonly');
	}
	this.updateUserMessages();
};

/**
 * Updates the displayed message on this page
 */
Mashup_Page_UI.prototype.updateUserMessages = function() {
	if (!this.isUpToDate()) {
		this.getPage().getMessages().setMessage('disabled', {
			level : 'error',
			canClose : false,
			message : _.CONCURRENCY_MASHUP_UI_NOT_UPTODATE()
		});
	} else {
		this.getPage().getMessages().removeMessage('disabled');
	}
};

/**
 * Called when this tab is closed
 */
Mashup_Page_UI.prototype.onClose = function() {
	this.getPage().getMessages().removeMessage('disabled');
};

/**
 * Updates the error count for the Page UI
 * 
 * @param {number}
 *            errorsCount Can be positive (more errors) or negative (less
 *            errors)
 */
Mashup_Page_UI.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;
	if (this.errorsCount == 0) {
		this.getElBottomTabsErrors().css('display', 'none');
	} else {
		this.getElBottomTabsErrors().css('display', 'inline-block').html(
				this.errorsCount);
	}
	this.getPage().updateError(errorsCount);
};

/*
 * Events
 */
Mashup_Page_UI.prototype.handleEventOnClick = function(e) {
	if (this.getLayout().isEditModeEnabled()) {
		return this.getLayout().getEditLayout().handleEventOnClick(e);
	}
	return true;
};

/*
 * Dom Manipulation
 */
Mashup_Page_UI.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('<div id="design-workspace" class="workspace"></div>');
		this.el.data('_this', this);

		// do not append application or page triggers for widget builder
		if (window.widgetBuilder == null) {
			window.mashupBuilder.getDesignTriggerContainer().getEl().appendTo(this.el);
			this.getTriggerContainer().getEl().appendTo(this.el);
		}

		this.getLayout().getEl().appendTo(this.el);

		this.setViewMode(window.mashupBuilder.getViewMode());

		this.el.on('click', $.proxy(this.handleEventOnClick, this));
	}
	return this.el;
};

/**
 * Refresh the list of available feeds for all the widgets
 */
Mashup_Page_UI.prototype.redrawElUseFeeds = function() {
	if (this.isDOMReady()) {
		var widgets = this.getWidgets(true);
		for ( var i = 0; i < widgets.length; i++) {
			widgets[i].redrawElUseFeeds();
		}
	}
};

Mashup_Page_UI.prototype.getState = function(key, defaultValue) {
	return window.mashupBuilder.state.get('ui', key, defaultValue);
};

Mashup_Page_UI.prototype.saveState = function(key, value) {
	window.mashupBuilder.state.save('ui', key, value);
};

/**
 * Sets the UI in the given view mode
 * 
 * @param viewMode
 */
Mashup_Page_UI.prototype.setViewMode = function(viewMode) {
	// propagate the view mode to all the widgets
	var widgets = this.getWidgets(true);
	var widgetsLength = widgets.length;
	for ( var i = 0; i < widgetsLength; i++) {
		widgets[i].setViewMode(viewMode);
	}

	// disables/enables the edit mode unless readonly
	if (!this.isReadOnly()) {
		if (viewMode == 'edit') {
			this.getLayout().enableEditMode();
		} else {
			this.getLayout().disableEditMode();
		}
	}
};

/* ElBottomTabs */
Mashup_Page_UI.prototype.getElBottomTabs = function() {
	if (this.elTopTabs == null) {
		this.elTopTabs = $(''
				+ '<li class="page-bottom-tab mashupUI" name="doOpenMashupUI">'
				+ '<span class="typeId" name="doOpenMashupUI">'
				+ _.PAGEUI_TAB_TITLE() + '</span> '
				+ '<span class="icon icon-error" name="doOpenError" title="'
				+ _.MB_ERROR_CLICK_TO_SEE() + '">' + this.errorsCount
				+ '</span>' + '</li>');
	}
	return this.elTopTabs;
};

Mashup_Page_UI.prototype.getElBottomTabsErrors = function() {
	if (this.elErrors == null) {
		this.elErrors = this.getElBottomTabs().find('.icon-error');
	}
	return this.elErrors;
};

/**
 * Called when the window is resized
 */
Mashup_Page_UI.prototype.onResize = function() {
	// nothing to do
};

/**
 * Called when a trigger has been created
 */
Mashup_Page_UI.prototype.onCreateTrigger = function(trigger) {
	if (this.getPage().isOpen()) {
		Mashup_Config_Validator.checkTrigger(trigger);
		this.onUpdateTriggerDisplay();
	}

	// propagate call
	this.onUpdate();
};

/**
 * Called when a trigger has been moved
 */
Mashup_Page_UI.prototype.onMoveTrigger = function(trigger) {
	this.onUpdateTriggerDisplay();
	this.onUpdate();
};

/**
 * Called when a trigger has been removed
 */
Mashup_Page_UI.prototype.onRemoveTrigger = function(trigger) {
	this.onUpdateTriggerDisplay();
	this.onUpdate();
};

/**
 * Called when a trigger has been updated
 */
Mashup_Page_UI.prototype.onUpdateTrigger = function(trigger) {
	this.onUpdate();
};

/**
 * Called when a trigger has been updated
 */
Mashup_Page_UI.prototype.onUpdateTriggerDisplay = function() {
};

/**
 * Redraw the UI
 */
Mashup_Page_UI.prototype.redraw = function() {
	// destroy the trigger container
	if (this.triggerContainer != undefined) {
		this.triggerContainer.remove();
		this.triggerContainer = undefined;
	}

	// destroy the layout
	if (this.layout != undefined) {
		this.layout.remove();
		this.layout = undefined;
	}

	// refresh the body
	if (this.el != undefined) {
		var $backup = this.el;
		this.el = undefined;
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();
	}

	// re-trigger the open callback if opened
	if (this.getPage().getOpenedTabName() == 'ui') {
		this.onOpen();
	}

	// update the layout markers position
	if (this.getLayout().isEditModeEnabled()) {
		this.getLayout().getEditLayout().onUpdatePageLayout();
	}

	// re-validate the Mashup UI
	Mashup_Config_Validator.checkUI(this);
};

/**
 * Removes from the DOM
 */
Mashup_Page_UI.prototype.remove = function() {
	window.mashupBuilder.getDesignTriggerContainer().getEl().detach();

	if (this.layout != undefined) {
		this.layout.remove();
		this.layout = undefined;
	}

	if (this.triggerContainer != undefined) {
		this.triggerContainer.remove();
		this.triggerContainer = undefined;
	}

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}

	if (this.elTopTabs != undefined) {
		this.elTopTabs.remove();
		this.elTopTabs = undefined;
	}
};

/**
 * Returns the parameters as JSON
 */
Mashup_Page_UI.prototype.getJsonParameters = function() {
	var getParameters = function(json, page) {
		var tabs = page.getTabs();
		for (var i = 0; i < tabs.length; i++) {
			var containers = tabs[i].getContainers();
			for (var j = 0; j < containers.length; j++) {
				$.merge(json, containers[j].getJson());
			}
		}
	};

	var json = [];
	getParameters(json, this.getPage().getSettings());
	getParameters(json, this.getPage().getCode());
	$.merge(json, window.mashupBuilder.getToolboxContainer().getToolbox('pageLayout').getJson());
	return json;
};

/**
 * Returns the JSON
 * 
 * @returns
 */
Mashup_Page_UI.prototype.getJson = function() {
	if (this.isDOMReady()) {
		this.json.layout = this.getLayout().getJson();
		this.json.triggers = this.getTriggerContainer().getJson();
	}
	return this.json;
};


/**
 * File: /resources/mashupBuilder/js/Page/UI/Interactions/DragDrop.js
 */
/*
 * 
 * Widget
 * 
 */

/**
 * Called when the user start to drag either an existing widget or a new widget
 */
function dragWidgetStart(e, obj) {
	if (!window.mashupBuilder.getEl().hasClass('hide-widget-drop-zones') && !window.mashupBuilder.getEl().hasClass('design-readonly')) {
		return onDragComponentStart.call(this, 'widget', e, obj);
	}
	return false;
};

/**
 * Called when the user drop a draggable widget
 */
function dragWidgetStop(e, obj) {
	onDragComponentStop.call(this, 'widget', e, obj);
};

/**
 * Function called when the widget is dropped
 */
function doDragStartWidget(draggable, droppable) {
	doDragComponentStart.call(this, 'widget', draggable, droppable);
}

/**
 * Function called when the widget is dropped
 */
function doDragStopWidget(draggable, addHistory) {
	doDragComponentStop.call(this, 'widget', draggable, addHistory);
}

/*
 * 
 * Design Triggers
 * 
 */

/**
 * Called when the user start to drag either an existing trigger or a new trigger
 */
function dragDesignTriggerStart(e, obj) {
	if (!window.mashupBuilder.getEl().hasClass('hide-designTrigger-drop-zones') && !window.mashupBuilder.getEl().hasClass('design-readonly')) {
		return onDragComponentStart.call(this, 'designTrigger', e, obj);
	}
	return false;
};

/**
 * Called when the user drop a draggable trigger
 */
function dragDesignTriggerStop(e, obj) {
	onDragComponentStop.call(this, 'designTrigger', e, obj);
};

/**
 * Function called when the trigger is dropped
 */
function doDragStartDesignTrigger(draggable, droppable) {
	doDragComponentStart.call(this, 'designTrigger', draggable, droppable);
}

/**
 * Function called when the trigger is dropped
 */
function doDragStopDesignTrigger(draggable, addHistory) {
	doDragComponentStop.call(this, 'designTrigger', draggable, addHistory);
}


/**
 * File: /resources/mashupBuilder/js/Page/UI/Layout/EditLayout.js
 */
/**
 * A wrapper to edit a Table layout
 *
 * @constructor
 * @this {Mashup_Page_UI_Layout_EditLayout}
 * 
 * @param {Mashup_Page_UI_Layout_TableLayout} layout
 */
function Mashup_Page_UI_Layout_EditLayout(layout) {
	this.layout = layout;
	this.enabled = false;
}

/**
 * Returns the page UI for this table
 * 
 * @returns {Mashup_Page_UI}
 */
Mashup_Page_UI_Layout_EditLayout.prototype.getPageUI = function() {
	return this.getLayout().getPageUI();
};

/**
 * Returns the wrapped table
 * 
 * @returns {Mashup_Page_UI_Layout_TableLayout}
 */
Mashup_Page_UI_Layout_EditLayout.prototype.getLayout = function() {
	return this.layout;
};

/**
 * Returns the pad to control cell layout
 * 
 * @returns {Mashup_Page_UI_Layout_Cell_Pad}
 */
Mashup_Page_UI_Layout_EditLayout.prototype.getPad = function() {
	if (this.pad == undefined) {
		this.pad = new Mashup_Page_UI_Layout_Cell_Pad();
	}
	return this.pad;
};

/**
 * Returns whether or not the table edition is enabled
 */
Mashup_Page_UI_Layout_EditLayout.prototype.isEnabled = function() {
	return this.enabled;
};

/**
 * Initialize the edit mode
 */
Mashup_Page_UI_Layout_EditLayout.prototype.enableEditMode = function() {
	if (this.enabled) {
		return;
	}

	// flag as enabled
	this.enabled = true;

	// initialize EditTable DOM
	this.initializeRuler();
	var tables = this.getLayout().getTables();
	for (var i = 0; i < tables.length; i++) {
		this.initializeTable(tables[i]);
	}

	// initialize events
	this.getLayout().getEl().on('mouseenter', '.cell', $.proxy(this.handleEventOnMouseEnterTD, this));
	this.getLayout().getEl().on('mouseleave', '.cell', $.proxy(this.handleEventOnMouseLeaveTD, this));

	this.getLayout().getEl().on('mouseenter', '.vmarker-handle', $.proxy(this.handleEventOnMouseEnterResizeHandle, this));
	this.getLayout().getEl().on('mouseleave', '.vmarker-handle', $.proxy(this.handleEventOnMouseLeaveResizeHandle, this));

	// update the markers position
	this.onUpdatePageLayout();
};

/**
 * Disables the edit mode of the table
 */
Mashup_Page_UI_Layout_EditLayout.prototype.disableEditMode = function() {
	if (!this.enabled) {
		return;
	}

	// flag as disabled
	this.enabled = false;

	// remove any opened bottomtoolbar (cell/row)
	window.mashupBuilder.removeOpenedBottomToolbar();

	// unitialize EditTable DOM
	this.unitializeRuler();
	var tables = this.getLayout().getTables();
	for (var i = 0; i < tables.length; i++) {
		this.unitializeTable(tables[i]);
	}

	// unitialize events
	this.getLayout().getEl().off('mouseenter', '.vmarker-handle');
	this.getLayout().getEl().off('mouseleave', '.vmarker-handle');

	this.getLayout().getEl().off('mouseenter', '.cell');
	this.getLayout().getEl().off('mouseleave', '.cell');

	this.getPad().disable();
};

/**
 * jQuery onClick event
 */
Mashup_Page_UI_Layout_EditLayout.prototype.handleEventOnClick = function(e) {
	if ((target = getEventTarget(e)) != null) {
		switch (target.attr('name')) {
		case 'doInsertNewRow':
			var table = target.closest('.table-wrapper').data('_this');
			table.getRow(parseFloat(target.attr('idx'))).doInsertNewRow();
			return false;

		case 'doRemoveRow':
			var table = target.closest('.table-wrapper').data('_this');
			table.getRow(parseFloat(target.attr('idx'))).doRemoveRow();
			return false;

		case 'doEditRow':
			var table = target.closest('.table-wrapper').data('_this');
			table.getRow(parseFloat(target.attr('idx'))).doEditRow();
			return false;
		}
	}
};

/**
 * Called when the user hover a cell
 */
Mashup_Page_UI_Layout_EditLayout.prototype.handleEventOnMouseEnterTD = function(e) {
	if (this.disableEvent == undefined) {
		this.getPad().setCell(getEventTarget(e).closest('.cell').data('_this'));
	}
	return false;
};

/**
 * Called when the user leave a cell
 */
Mashup_Page_UI_Layout_EditLayout.prototype.handleEventOnMouseLeaveTD = function(e) {
	if (this.disableEvent == undefined) {
		this.getPad().disable();
	}
	return false;
};

/**
 * Called when the user hover the column resize handle
 */
Mashup_Page_UI_Layout_EditLayout.prototype.handleEventOnMouseEnterResizeHandle = function(e) {
	if (!(target = getEventTarget(e)).hasClass('vmarker-handle')) {
		target = target.parent();
	}
	var col = target.closest('.table-wrapper').data('_this').getCol(target.attr('data-col-idx'));
	col.$markerV.addClass('hover');
	return false;
};

/**
 * Called when the user leave the column resize handle
 */
Mashup_Page_UI_Layout_EditLayout.prototype.handleEventOnMouseLeaveResizeHandle = function(e) {
	if (!(target = getEventTarget(e)).hasClass('vmarker-handle')) {
		target = target.parent();
	}
	var col = target.closest('.table-wrapper').data('_this').getCol(target.attr('data-col-idx'));
	col.$markerV.removeClass('hover');
	return false;
};

/**
 * Called when the page format (width or widthFormat) has been updated
 */
Mashup_Page_UI_Layout_EditLayout.prototype.onUpdatePageFormat = function() {
	var cols = this.getLayout().getCols();
	for (var i = 0; i < cols.length; i++) {
		this.updateCol(cols[i]);
	}
	this.initializeRuler();
};

/**
 * Called when the page layout has been updated (resize, new cells, etc.)
 * 
 * @param table if undefined will apply to all tables
 */
Mashup_Page_UI_Layout_EditLayout.prototype.onUpdatePageLayout = function(table) {
	// update position of the cols markers
	var cols = (table || this.getLayout()).getCols();
	for (var i = 0; i < cols.length; i++) {
		this.updateCol(cols[i]);
	}

	// update the position of the rows markers
	var rows = (table || this.getLayout()).getRows();
	for (var i = 0; i < rows.length; i++) {
		this.updateRow(rows[i]);
	}
};

/**
 * Initializes the ruler for the table edition
 */
Mashup_Page_UI_Layout_EditLayout.prototype.initializeRuler = function() {
	if (this.$ruler == undefined) {
		this.$ruler = $('<div class="table-resize-wrapper"></div>');
	} else {
		this.$ruler.find('.mark').remove();
	}

	var multi = this.getLayout().getWidth() / 100;

	var ratio = 0;
	while (ratio <= 100) {
		this.$ruler.append('' +
			'<div class="mark' + (ratio == 0 ? ' first-child' : (ratio == 100 ? ' last-child' : '')) + '" style="left: ' + ratio + '%;">' +
				'<span>' + parseInt(ratio * multi) +'</span>' +
			'</div>'
		);
		ratio += 25;
	}

	this.$ruler.prependTo(this.getLayout().getEl());
};

/**
 * Initialize the given table
 * 
 * @param table
 */
Mashup_Page_UI_Layout_EditLayout.prototype.initializeTable = function(table) {
	this.initializeCols(table);
	this.initializeRows(table);
};

/**
 * Initialize the columns
 */
Mashup_Page_UI_Layout_EditLayout.prototype.initializeCols = function(table) {
	var cols = table.getCols();
	for (var j = 1; j < cols.length - 1; j++) {
		this.initializeCol(cols[j]);
	}
};

/**
 * Initialize the col
 * 
 * @param col
 */
Mashup_Page_UI_Layout_EditLayout.prototype.initializeCol = function(col) {
	// create resize marker
	col.$markerV = $('' +
		'<div class="vmarker">' +
			'<span class="vmarker-label">?</span>' +
		'</div>'
	);
	col.$label = col.$markerV.find('.vmarker-label');
	col.getTable().getEl().append(col.$markerV);

	// create resize bar
	col.$resizeBarV = $('' +
		'<div class="vmarker-handle" data-col-idx="' + col.getIdx() +'">' +
			'<span class="vmarker-snap"></span>' +
		'</div>'
	);
	col.getTable().getEl().append(col.$resizeBarV);

	// attach the draggable events
	this.attachDraggableEvents(col);

	// update maker position
	this.updateCol(col);
};

/**
 * Initialize the rows
 */
Mashup_Page_UI_Layout_EditLayout.prototype.initializeRows = function(table) {
	var rows = table.getRows();
	for (var i = 0; i < rows.length; i++) {
		this.initializeRow(rows[i]);
	}
};

/**
 * Initialize the row
 * 
 * @param row
 */
Mashup_Page_UI_Layout_EditLayout.prototype.initializeRow = function(row) {
	row.$newRow = $('<span class="hmarker icon icon-new-row" idx="'+ row.getIdx() + '" name="doInsertNewRow"></span>');
	row.$newRow.appendTo(row.getTable().getEl());

	row.$removeRow = $('<span class="hmarker icon icon-remove-row" idx="'+ row.getIdx() + '" name="doRemoveRow"></span>');
	row.$removeRow.appendTo(row.getTable().getEl());

	row.$editRow = $('<span class="hmarker icon icon-edit-row" idx="'+ row.getIdx() + '" name="doEditRow"></span>');
	row.$editRow.appendTo(row.getTable().getEl());
};

/**
 * Updates the markers for the given column
 * 
 * @param col
 */
Mashup_Page_UI_Layout_EditLayout.prototype.updateCol = function(col) {
	// retrieve the offset of the given column
	var offset = getComputedColOffset(col);

	// update label of the resize marker
	if (col.$label != undefined) {
		col.$label.html(col.getWidthDisplay());
	}

	// update offset of the resize marker
	if (col.$markerV != undefined) {
		col.$markerV.css('left', offset + '%');
	}

	// update resize bar
	if (col.$resizeBarV != undefined) {
		col.$resizeBarV.css({
			position: 'absolute',
			left: offset + '%',
			height: col.getTable().getElTable().height() - 10 // border-spacing(5)
		});
		col.$resizeBarV.find('.vmarker-snap').css('height', col.$resizeBarV.css('height'));
	}
};

/**
 * Update markers of the given row
 * 
 * @param {Mashup_Page_UI_Layout_Row} row
 */
Mashup_Page_UI_Layout_EditLayout.prototype.updateRow = function(row) {
	if (row.canEdit()) {
		row.$editRow.css({
			display: 'inline-block',
			top: row.getEl().position().top + parseInt(row.getEl().height() / 2) - 14
		});
	} else {
		row.$editRow.remove();
	}

	if (row.canInsertBefore()) {
		if ((topRow = getTopRow(row)) != null) {
			row.$newRow.css({
				display: 'inline-block',
				top: topRow.getEl().position().top + parseInt(topRow.getEl().height()) - 14
			});
		} else {
			row.$newRow.css({
				display: 'inline-block',
				top: row.getEl().position().top - 17
			});
		}
	} else {
		row.$newRow.hide();
	}

	if (row.canRemove()) {
		row.$removeRow.css({
			display: 'inline-block',
			top: row.getEl().position().top + parseInt(row.getEl().height() / 2) - 14
		});
	} else {
		row.$removeRow.hide();
	}
};

/**
 * Cleanup the ruler
 */
Mashup_Page_UI_Layout_EditLayout.prototype.unitializeRuler = function() {
	if (this.$ruler != undefined) {
		this.$ruler.detach();
	}
};

/**
 * Cleanup table
 * 
 * @param table
 */
Mashup_Page_UI_Layout_EditLayout.prototype.unitializeTable = function(table) {
	this.unitializeCols(table);
	this.unitializeRows(table);
};

/**
 * Cleanup cols
 */
Mashup_Page_UI_Layout_EditLayout.prototype.unitializeCols = function(table) {
	var cols = table.getCols();
	for (var j = 1; j < cols.length - 1; j++) {
		this.unitializeCol(cols[j]);
	}
};

/**
 * Cleanup col
 * 
 * @param col
 */
Mashup_Page_UI_Layout_EditLayout.prototype.unitializeCol = function(col) {
	// remove resize marker
	col.$markerV.remove();
	col.$markerV = undefined;
	col.$label = undefined;

	// remove resize bar
	col.$resizeBarV.remove();
	col.$resizeBarV = undefined;
};

/**
 * Cleanup rows
 */
Mashup_Page_UI_Layout_EditLayout.prototype.unitializeRows = function(table) {
	var rows = table.getRows();
	for (var i = 0; i < rows.length; i++) {
		this.unitializeRow(rows[i]);
	}
};

/**
 * Cleanup row
 * 
 * @param row
 */
Mashup_Page_UI_Layout_EditLayout.prototype.unitializeRow = function(row) {
	row.$newRow.remove();
	row.$newRow = undefined;

	row.$removeRow.remove();
	row.$removeRow = undefined;

	row.$editRow.remove();
	row.$editRow = undefined;
};

/**
 * Attaches the draggable events of the given column
 * @param col
 */
Mashup_Page_UI_Layout_EditLayout.prototype.attachDraggableEvents = function(col) {
	var _this = this;
	col.$resizeBarV.draggable({
		axis: 'x',
		addClasses: false,
		containment: 'parent',
		start: function(event, ui) {
			window.dragElement = {};

			// disable other events
			_this.disableEvent = true;

			// sets dragged column as hovered
			col.$markerV.addClass('selected');

			// find our layout width format
			window.dragElement.widthFormat = col.getTable().getWidthFormat();

			// find width for ratio calculation
			window.dragElement.maxLeft = _this.$ruler.width();

			// save current col
			window.dragElement.col = col;
			window.dragElement.width = col.getWidth();
			window.dragElement.offset = getComputedColOffset(col) - window.dragElement.width;

			// find minimal offset
			var prevCol = getPreviousCol(col);
			window.dragElement.minOffsetCol = Math.max(getComputedColOffset(prevCol) + 1, 1); // cannot be less than 1%
			window.dragElement.minOffsetLeft = Math.round((window.dragElement.minOffsetCol * window.dragElement.maxLeft) / 100);

			// find max offset
			var nextCol = getNextCol(col);
			window.dragElement.nextCol = nextCol;
			window.dragElement.nextWidth = nextCol.getWidth();
			window.dragElement.maxOffsetCol = getComputedColOffset(nextCol) - 1; // cannot be less than 1%
			window.dragElement.maxOffsetLeft = Math.round((window.dragElement.maxOffsetCol * window.dragElement.maxLeft) / 100);

			// save all offsets
			var snapsCol = getComputedColOffsets(_this.getLayout());
			window.dragElement.snapsLeft = [];
			for (var i = 0; i < snapsCol.length; i++) {
				window.dragElement.snapsLeft.push(Math.round((snapsCol[i] * window.dragElement.maxLeft) / 100));
			}

			// clears the pad
			_this.getPad().disable();
		},

		drag: function(event, ui) {
			// check that we do not go outside our limits
			if (ui.position.left < window.dragElement.minOffsetLeft) {
				ui.position.left = window.dragElement.minOffsetLeft;
			} else if (ui.position.left > window.dragElement.maxOffsetLeft) {
				ui.position.left = window.dragElement.maxOffsetLeft;
			} else {
				var snapsLeft = window.dragElement.snapsLeft;
				var x = ui.offset.left - 270;
				for (var i = 0; i < snapsLeft.length; i++) {
					if (x > (snapsLeft[i] - 10) && x < (snapsLeft[i] + 10)) {
						ui.position.left = snapsLeft[i];
						break;
					}
				}
			}

			var position, newWidth, newNextWidth;

			if (window.dragElement.widthFormat == '%') {
				// get current position as '%'
				position = parseInt((ui.position.left * 100) / window.dragElement.maxLeft);

				// compute the new width
				newWidth = position - window.dragElement.offset;
				newNextWidth = parseInt(window.dragElement.width - newWidth + window.dragElement.nextWidth);
			} else {
				// get current position as '%'
				position = parseFloat(((ui.position.left * 100) / window.dragElement.maxLeft).toFixed(1));

				// compute the new width and check that the resulting pixel width is an integer
				newWidth = parseFloat((position - window.dragElement.offset).toFixed(1));
				if (!isInteger((tmp = window.dragElement.col.getTable().toWidth(newWidth)))) {
					newWidth = window.dragElement.col.getTable().toRatio(parseInt(tmp));
				}

				newNextWidth = parseFloat((window.dragElement.width - newWidth + window.dragElement.nextWidth).toFixed(1));
			}

			// update width of current col and next col
			window.dragElement.col.setWidth(newWidth);
			window.dragElement.nextCol.setWidth(newNextWidth);

			// update position of markers
			_this.updateCol(window.dragElement.col);

			return true;
		},

		stop: function(event, ui) {
			// enables events
			_this.disableEvent = undefined;

			// clears dragged column hovered status
			col.$markerV.removeClass('selected');

			// clears global element
			window.dragElement = undefined;

			// triggers layout update
			_this.onUpdatePageLayout();
			_this.getPageUI().onUpdate();
		}
	});
};

/**
 * Removes the EditTable components from the DOM
 */
Mashup_Page_UI_Layout_EditLayout.prototype.remove = function() {
	if (this.isEnabled()) {
		this.disableEditMode();
	}

	if (this.pad != undefined) {
		this.pad.remove();
		this.pad = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Page/UI/Layout/Table.js
 */
/**
 * A class that represent a Table
 *
 * @constructor
 * @this {Mashup_Page_UI_Layout_Table}
 * 
 * @param parent
 * @param {object} json
 */
function Mashup_Page_UI_Layout_Table(layout, json) {
	this.layout = layout;
	this.json = json;

	// register cols
	this.cols = [];
	for (var i = 0; i < json.colsConfig.length; i++) {
		json.colsConfig[i].width = this.toRatio(json.colsConfig[i].width); // normalize width
		this.registerCol(json.colsConfig[i]);
	}
	this.registerCol(createCol(0, 0));

	// register rows
	this.rows = [];
	for (var i = 0; i < json.rows.length; i++) {
		this.registerRow(json.rows[i]);
	}
	this.registerRow(createRow(json.rows[json.rows.length - 1].cells[0].rowEnd));
}

/**
 * Returns layout of this table
 * 
 * @returns {Mashup_Page_UI_Layout_TableLayout}
 */
Mashup_Page_UI_Layout_Table.prototype.getLayout = function() {
	return this.layout;
};

/**
 * Returns the width of this table
 * 
 * @returns
 */
Mashup_Page_UI_Layout_Table.prototype.getWidth = function() {
	return this.getLayout().getWidth();
};

/**
 * Returns the width format of this table
 */
Mashup_Page_UI_Layout_Table.prototype.getWidthFormat = function() {
	return this.getLayout().getWidthFormat();
};

/**
 * Returns the Page UI
 * 
 * @returns {Mashup_Page_UI}
 */
Mashup_Page_UI_Layout_Table.prototype.getPageUI = function() {
	return this.getLayout().getPageUI();
};

/**
 * Returns the position of this table within the layout
 */
Mashup_Page_UI_Layout_Table.prototype.getPosition = function() {
	return this.getLayout().getTables().indexOf(this);
};

/**
 * Returns the columns of the table
 * 
 * @returns {Array}
 */
Mashup_Page_UI_Layout_Table.prototype.getCols = function() {
	return this.cols;
};

/**
 * Returns a column by its idx
 * 
 * @param idx
 * @returns {Mashup_Page_UI_Layout_Col}
 */
Mashup_Page_UI_Layout_Table.prototype.getCol = function(idx) {
	for (var i = 0; i < this.cols.length; i++) {
		if (this.cols[i].idx == idx) {
			return this.cols[i];
		}
	}
	return null;
};

/**
 * Returns the rows of the table
 * 
 * @returns {Array}
 */
Mashup_Page_UI_Layout_Table.prototype.getRows = function() {
	return this.rows;
};

/**
 * Returns a row by its idx
 * 
 * @param idx
 * @returns
 */
Mashup_Page_UI_Layout_Table.prototype.getRow = function(idx) {
	for (var i = 0; i < this.rows.length; i++) {
		if (this.rows[i].idx == idx) {
			return this.rows[i];
		}
	}
	return null;
};

/**
 * Returns all the cells of the table
 * 
 * @returns {Array}
 */
Mashup_Page_UI_Layout_Table.prototype.getCells = function() {
	var cells = [];
	for (var i = 0; i < this.rows.length; i++) {
		$.merge(cells, this.rows[i].getCells());
	}
	return cells;
};

/**
 * Returns whether or not the table contains widgets
 * 
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Table.prototype.hasWidgets = function() {
	for (var i = 0; i < this.rows.length; i++) {
		if (this.rows[i].hasWidgets()) {
			return true;
		}
	}
	return false;
};

/**
 * Returns all the widgets contained by the table
 * 
 * @param recursive
 * @returns {Array}
 */
Mashup_Page_UI_Layout_Table.prototype.getWidgets = function(recursive) {
	var widgets = [];
	for (var i = 0; i < this.rows.length; i++) {
		$.merge(widgets, this.rows[i].getWidgets(recursive));
	}
	return widgets;
};

/**
 * Returns the DOM node for the table wrapper
 * 
 * @returns
 */
Mashup_Page_UI_Layout_Table.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
			'<div class="table-wrapper">' +
				'<table class="table-layout" cellspacing="0" cellpadding="0" border="0">' +
					'<colgroup></colgroup>' +
				'</table>' +
			'</div>');
		this.el.data('_this', this);

		this.elTable = this.el.find('> table');

		var $colgroup = this.elTable.find('> colgroup');
		for (var i = 1; i < this.cols.length; i++) {
			this.cols[i].getEl().appendTo($colgroup);
		}

		for (var i = 0; i < this.rows.length - 1; i++) {
			this.rows[i].getEl().appendTo(this.elTable);
		}
	}
	return this.el;
};

/**
 * Returns the DOM node for the table
 * 
 * @returns
 */
Mashup_Page_UI_Layout_Table.prototype.getElTable = function() {
	return this.elTable;
};

/**
 * Called when a cell has been created
 * 
 * @param cell
 */
Mashup_Page_UI_Layout_Table.prototype.onCreateCell = function(cell) {
	if (this.getLayout().isEditModeEnabled()) {
		this.getLayout().getEditLayout().onUpdatePageLayout();
	}
	this.getPageUI().onUpdate();
};

/**
 * Called when a cell has been removed
 * 
 * @param cell
 */
Mashup_Page_UI_Layout_Table.prototype.onRemoveCell = function(cell) {
	optimizeTable(cell.getTable());
	if (this.getLayout().isEditModeEnabled()) {
		if (this.getLayout().getEditLayout().getPad().getCell() == cell) {
			this.getLayout().getEditLayout().getPad().disable();
		}
		this.getLayout().getEditLayout().onUpdatePageLayout();
	}
	this.getPageUI().onUpdate();
};

/**
 * Called when a row has been created
 * 
 * @param row
 */
Mashup_Page_UI_Layout_Table.prototype.onCreateRow = function(row) {
	if (this.getLayout().isEditModeEnabled()) {
		this.getLayout().getEditLayout().initializeRow(row);
		this.getLayout().getEditLayout().onUpdatePageLayout();
	}
	this.getPageUI().onUpdate();
};

/**
 * Called when a row has been removed
 * 
 * @param row
 */
Mashup_Page_UI_Layout_Table.prototype.onRemoveRow = function(row) {
	optimizeTable(row.getTable());
	if (this.getLayout().isEditModeEnabled()) {
		this.getLayout().getEditLayout().unitializeRow(row);
		this.getLayout().getEditLayout().onUpdatePageLayout();
	}
	this.getPageUI().onUpdate();
};

/**
 * Called when a col has been removed
 * 
 * @param col
 */
Mashup_Page_UI_Layout_Table.prototype.onRemoveCol = function(col) {
	if (this.getLayout().isEditModeEnabled()) {
		this.getLayout().getEditLayout().unitializeCol(col);
	}
	this.getPageUI().onUpdate();
};

/**
 * Registers a new row
 * 
 * @param jsonRow
 * @returns {Mashup_Page_UI_Layout_Row}
 */
Mashup_Page_UI_Layout_Table.prototype.registerRow = function(jsonRow) {
	var row = new Mashup_Page_UI_Layout_Row(this, jsonRow);
	this.rows.push(row);
	this.rows.sort(function(a, b) { return a.idx - b.idx; });
	return row;
};

/**
 * Removes the given row
 * 
 * @param row
 */
Mashup_Page_UI_Layout_Table.prototype.removeRow = function(row) {
	row.remove();
	this.rows.splice(this.rows.indexOf(row), 1);
};

/**
 * Registers a new column
 * 
 * @param jsonCol
 * @returns {Mashup_Page_UI_Layout_Col}
 */
Mashup_Page_UI_Layout_Table.prototype.registerCol = function(jsonCol) {
	var col = new Mashup_Page_UI_Layout_Col(this, jsonCol);
	this.cols.push(col);
	this.cols.sort(function(a, b) { return a.idx - b.idx; });
	return col;
};

/**
 * Removes the given col
 * 
 * @param col
 */
Mashup_Page_UI_Layout_Table.prototype.removeCol = function(col) {
	var nextCol = getNextCol(col);

	col.remove();
	this.cols.splice(this.cols.indexOf(col), 1);

	if (nextCol != null) {
		nextCol.setWidth(col.getWidth() + nextCol.getWidth());
	}
};

/**
 * Returns the ratio for the given width
 * 
 * @param width
 */
Mashup_Page_UI_Layout_Table.prototype.toRatio = function(width) {
	if (width == null) {
		return null;
	}

	if (this.getWidthFormat() == '%') {
		return parseInt(width);
	} else {
		return parseFloat(((width * 100) / this.getWidth()).toFixed(1));
	}
};

/**
 * Returns the width for the given ratio
 * 
 * @param ratio
 */
Mashup_Page_UI_Layout_Table.prototype.toWidth = function(ratio) {
	if (ratio == null) {
		return null;
	}

	if (this.getWidthFormat() == '%') {
		return parseInt(ratio);
	} else {
		return parseFloat(((ratio * this.getWidth()) / 100).toFixed(1));
	}
};

/**
 * Removes this table from the DOM
 */
Mashup_Page_UI_Layout_Table.prototype.remove = function() {
	for (var i = 0; this.rows.length > 0; i++) {
		this.removeRow(this.rows[0]);
	}

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Returns this table as JSON
 */
Mashup_Page_UI_Layout_Table.prototype.getJson = function() {
	this.json.rows = [];
	for (var i = 0; i < this.rows.length - 1; i++) { // skip last special Row
		this.json.rows.push(this.rows[i].getJson());
	}
	this.json.colsConfig = [];
	for (var i = 1; i < this.cols.length; i++) { // skip first special Col
		this.json.colsConfig.push(this.cols[i].getJson());
	}
	return this.json;
};


/**
 * File: /resources/mashupBuilder/js/Page/UI/Layout/TableLayout.js
 */
function Mashup_Page_UI_Layout_TableLayout(parent, json) {
	this.parent = parent;
	this.json = json;

	this.setWidth(json.width || this.getDefaultWidth());
	this.setWidthFormat(json.widthFormat || this.getDefaultWidthFormat());
}

/**
 * Returns the parent of the layout. Either the Page UI or a widget
 * 
 * @returns
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getParent = function() {
	return this.parent;
};

/**
 * Returns the Page UI
 * 
 * @returns {Mashup_Page_UI}
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getPageUI = function() {
	if (!isWidget(this.getParent())) {
		return this.getParent();
	}
	return this.getParent().getPageUI();
};

/**
 * Returns the parent widget if exists or NULL
 * 
 * @returns {Mashup_Page_UI_Layout_Widget}
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getParentWidget = function() {
	if (isWidget(this.getParent())) {
		return this.getParent();
	}
	return null;
};

/**
 * Returns whether or not the edit mode is enabled
 */
Mashup_Page_UI_Layout_TableLayout.prototype.isEditModeEnabled = function() {
	return (this.editLayout != undefined && this.editLayout.isEnabled());
};

/**
 * Returns the width of the layout
 * 
 * @returns
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getWidth = function() {
	return this.width;
};

/**
 * Sets the width of the layout
 * 
 * @param width
 */
Mashup_Page_UI_Layout_TableLayout.prototype.setWidth = function(width) {
	this.width = parseInt(width);
};

/**
 * Returns the default width for this layout
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getDefaultWidth = function() {
	return isWidget(this.getParent()) ? '100' : '1000';
};

/**
 * Returns the width format of the layout
 * 
 * @returns
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getWidthFormat = function() {
	return this.widthFormat;
};

/**
 * Sets the width format of the layout
 * 
 * @param widthFormat
 */
Mashup_Page_UI_Layout_TableLayout.prototype.setWidthFormat = function(widthFormat) {
	this.widthFormat = widthFormat;
};

/**
 * Returns the default width format for this layout
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getDefaultWidthFormat = function() {
	return isWidget(this.getParent()) ? '%' : 'px';
};

/**
 * Returns the tables of the layout
 * 
 * @returns {Array}
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getTables = function() {
	if (this.tables == undefined) {
		this.tables = [];
		for (var i = 0; i < this.json.tables.length; i++) {
			this.tables.push(new Mashup_Page_UI_Layout_Table(this, this.json.tables[i]));
		}
	}
	return this.tables;
};

/**
 * Returns the table for the given idx
 * 
 * @param idx
 * @returns {Mashup_Page_UI_Layout_Table}
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getTable = function(idx) {
	return this.tables[idx];
};

/**
 * Returns all the cols of the layout
 * 
 * @returns {Array}
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getCols = function() {
	var cols = [];
	var tables = this.getTables();
	for (var i = 0; i < tables.length; i++) {
		$.merge(cols, tables[i].getCols());
	}
	return cols;
};

/**
 * Returns all the rows of the layout
 * 
 * @returns {Array}
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getRows = function() {
	var rows = [];
	var tables = this.getTables();
	for (var i = 0; i < tables.length; i++) {
		$.merge(rows, tables[i].getRows());
	}
	return rows;
};

/**
 * Returns all the cells of the layout
 * 
 * @returns {Array}
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getCells = function() {
	var cells = [];
	var tables = this.getTables();
	for (var i = 0; i < tables.length; i++) {
		$.merge(cells, tables[i].getCells());
	}
	return cells;
};

/**
 * Returns the widgets of the layout
 * 
 * @param recursive
 * @returns {Array}
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getWidgets = function(recursive) {
	var widgets = [];
	var tables = this.getTables();
	for (var i = 0; i < tables.length; i++) {
		$.merge(widgets, tables[i].getWidgets(recursive));
	}
	return widgets;
};

/**
 * Returns the edition wrapper of the layout
 * 
 * @returns {Mashup_Page_UI_Layout_EditLayout}
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getEditLayout = function() {
	return this.editLayout;
};

/**
 * Enables the edition mode
 */
Mashup_Page_UI_Layout_TableLayout.prototype.enableEditMode = function() {
	if (this.editLayout == undefined) {
		this.editLayout = new Mashup_Page_UI_Layout_EditLayout(this);
	}
	this.editLayout.enableEditMode();
};

/**
 * Disables the edition mode
 */
Mashup_Page_UI_Layout_TableLayout.prototype.disableEditMode = function() {
	if (this.editLayout != undefined) {
		this.editLayout.disableEditMode();
	}
};

/**
 * Registers the given table
 * 
 * @param jsonTable
 * @param position
 */
Mashup_Page_UI_Layout_TableLayout.prototype.registerTable = function(jsonTable, position) {
	var table = new Mashup_Page_UI_Layout_Table(this, jsonTable);
	this.tables.splice(position, 0, table);
	return table;
};

/**
 * Unregisters the given table
 * 
 * @param table
 */
Mashup_Page_UI_Layout_TableLayout.prototype.unregisterTable = function(table) {
	this.tables.splice(this.tables.indexOf(table), 1);
};

/**
 * Updates the current layout width the given template
 * 
 * @param template
 */
Mashup_Page_UI_Layout_TableLayout.prototype.adaptTo = function(jsonTemplate) {
	// update width & widthFormat
	this.setWidth(jsonTemplate.width);
	this.setWidthFormat(jsonTemplate.widthFormat);

	// compute the new JSON
	var json = clone(jsonTemplate);
	for (var i = 0; i < this.tables.length; i++) {
		var rows = this.tables[i].rows;
		var jsonRows = json.tables[i < (json.tables.length - 1) ? i : json.tables.length - 1].rows;
		for (var j = 0; j < rows.length; j++) {
			var cells = rows[j].getCells();
			var jsonCells = jsonRows[j < (jsonRows.length - 1) ? j : jsonRows.length - 1].cells;
			for (var k = 0; k < cells.length; k++) {
				$.merge(jsonCells[k < jsonCells.length ? k : jsonCells.length - 1].widgets, cells[k].getSubWidgetContainer().getJson());
			}
		}
	}

	// create a new layout and appends it to the DOM
	var layout = new Mashup_Page_UI_Layout_TableLayout(this.parent, json);
	this.getEl().replaceWith(layout.getEl());
	this.parent.layout = layout;

	// re-set the 'edit' mode
	window.mashupBuilder.setViewMode('edit');

	// remove the old table data
	this.remove();

	// trigger onUpdate event
	this.getPageUI().onUpdate();
};

/**
 * Returns the DOM node for the layout
 * 
 * @returns
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('<div class="table-layout-wrapper"></div>');
		var tables = this.getTables();
		for (var i = 0; i < tables.length; i++) {
			this.el.append(tables[i].getEl());
		}
	}
	return this.el;
};

/**
 * Called when the page format has been updated (width and or format)
 */
Mashup_Page_UI_Layout_TableLayout.prototype.onUpdatePageFormat = function() {
	if (this.isEditModeEnabled()) {
		this.getEditLayout().onUpdatePageFormat();
	}
};

/**
 * Called when a table is created
 * 
 * @param table
 */
Mashup_Page_UI_Layout_TableLayout.prototype.onCreateTable = function(table) {
	if (this.isEditModeEnabled()) {
		var widgets = table.getWidgets(true);
		for (var i = 0; i < widgets.length; i++) {
			widgets[i].setViewMode('edit');
		}
		this.getEditLayout().initializeTable(table);
		this.getEditLayout().onUpdatePageLayout();
	}
	this.getPageUI().onUpdate();
};

/**
 * Called when a table is removed
 * 
 * @param table
 */
Mashup_Page_UI_Layout_TableLayout.prototype.onRemoveTable = function(table) {
	if (this.isEditModeEnabled()) {
		this.getEditLayout().unitializeTable(table);
		this.getEditLayout().onUpdatePageLayout();
	}
	this.getPageUI().onUpdate();
};

/**
 * Removes a table
 * 
 * @param table
 */
Mashup_Page_UI_Layout_TableLayout.prototype.removeTable = function(table) {
	this.unregisterTable(table);
	table.remove();
	this.onRemoveTable(table);
};

/**
 * Removes the layout from the DOM
 */
Mashup_Page_UI_Layout_TableLayout.prototype.remove = function() {
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}

	if (this.tables != undefined) {
		for (var i = 0; i < this.tables.length; i++) {
			this.tables[i].remove();
		}
		this.tables = [];
	}

	if (this.editLayout != undefined) {
		this.editLayout.remove();
		this.editLayout = undefined;
	}
};

/**
 * Returns the JSON of the layout
 */
Mashup_Page_UI_Layout_TableLayout.prototype.getJson = function() {
	this.json.width = this.getWidth() + ''; // cast to string
	this.json.widthFormat = this.getWidthFormat();
	if (this.tables != undefined) {
		this.json.tables = [];
		for (var i = 0; i < this.tables.length; i++) {
			this.json.tables.push(this.tables[i].getJson());
		}
	}
	return this.json;
};


/**
 * File: /resources/mashupBuilder/js/Page/UI/Layout/Col.js
 */
/**
 * A class that represent a Column in a Table
 *
 * @constructor
 * @this {Mashup_Page_UI_Layout_Col}
 * 
 * @param {Mashup_Page_UI_Layout_Table} table
 * @param {object} jsonCol
 */
function Mashup_Page_UI_Layout_Col(table, jsonCol) {
	this.table = table;
	this.json = jsonCol;

	this.setIdx(jsonCol.idx);
	this.setWidth(jsonCol.width);
}

/**
 * Returns this col idx
 */
Mashup_Page_UI_Layout_Col.prototype.getIdx = function() {
	return this.idx;
};

/**
 * Sets this col idx
 * 
 * @param idx
 */
Mashup_Page_UI_Layout_Col.prototype.setIdx = function(idx) {
	this.idx = parseFloat(idx);
};

/**
 * Returns a display for this column real width
 */
Mashup_Page_UI_Layout_Col.prototype.getWidthDisplay = function() {
	return this.getTable().toWidth(getComputedColOffset(this)) + ' ' + this.getTable().getWidthFormat();
};

/**
 * Returns the width of this column in %
 *
 * @returns {Integer}
 */
Mashup_Page_UI_Layout_Col.prototype.getWidth = function() {
	return this.width;
};

/**
 * Sets the width of this column
 * 
 * @param width
 */
Mashup_Page_UI_Layout_Col.prototype.setWidth = function(width) {
	this.width = width;
	if (this.el != undefined) {
		this.el.attr('width', width + '%');
	}
};

/**
 * Returns the Mashup UI Table
 *
 * @returns {Mashup_Page_UI_Layout_Table}
 */
Mashup_Page_UI_Layout_Col.prototype.getTable = function() {
	return this.table;
};

/**
 * Returns the DOM node for the column
 *
 * @returns
 */
Mashup_Page_UI_Layout_Col.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('<col idx="' + this.getIdx() + '" width="' + this.getWidth() + '%" />');
	}
	return this.el;
};

/**
 * Removes this column from the DOM
 */
Mashup_Page_UI_Layout_Col.prototype.remove = function() {
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Returns the JSON of this object
 */
Mashup_Page_UI_Layout_Col.prototype.getJson = function() {
	this.json.idx = this.getIdx();
	this.json.width = this.getTable().toWidth(this.getWidth());
	return this.json;
};


/**
 * File: /resources/mashupBuilder/js/Page/UI/Layout/Row.js
 */
/**
 * Implementation of a MashupPage row
 *
 * @param table
 * @param jsonRow
 * @param options
 * @returns
 */
function Mashup_Page_UI_Layout_Row(table, jsonRow) {
	this.table = table;
	this.json = jsonRow;

	this.cells = [];
	for (var j = 0; j < jsonRow.cells.length; j++) {
		this.registerCell(jsonRow.cells[j]);
	}

	this.setIdx(jsonRow.rowIdx);
}

/**
 * Returns the idx of the row
 */
Mashup_Page_UI_Layout_Row.prototype.getIdx = function() {
	return this.idx;
};

/**
 * Sets the idx of the row
 */
Mashup_Page_UI_Layout_Row.prototype.setIdx = function(idx) {
	this.idx = parseFloat(idx);
};

/**
 * Returns the Table that contains this row
 *
 * @returns {Mashup_Page_UI_Layout_Table}
 */
Mashup_Page_UI_Layout_Row.prototype.getTable = function() {
	return this.table;
};

/**
 * Returns the Page UI
 *
 * @returns {Mashup_Page_UI}
 */
Mashup_Page_UI_Layout_Row.prototype.getPageUI = function() {
	return this.getTable().getPageUI();
};

/**
 * Returns all the cells of this row
 *
 * @returns {Array}
 */
Mashup_Page_UI_Layout_Row.prototype.getCells = function() {
	return this.cells;
};

/**
 * Returns the cell at the given index
 *
 * @param idx
 * @returns
 */
Mashup_Page_UI_Layout_Row.prototype.getCell = function(idx) {
	for (var i = 0; i < this.cells.length; i++) {
		if (this.cells[i].getColStart() == idx) {
			return this.cells[i];
		}
	}
	return null;
};

/**
 * Returns all the widgets from this row
 *
 * @param recursive
 * @returns {boolean}
 */
Mashup_Page_UI_Layout_Row.prototype.hasWidgets = function() {
	for (var i = 0; i < this.cells.length; i++) {
		if (this.cells[i].hasWidgets()) {
			return true;
		}
	}
	return false;
};

/**
 * Returns all the widgets from this row
 *
 * @param recursive
 * @returns {Array}
 */
Mashup_Page_UI_Layout_Row.prototype.getWidgets = function(recursive) {
	var widgets = [];
	for (var i = 0; i < this.cells.length; i++) {
		$.merge(widgets, this.cells[i].getWidgets(recursive));
	}
	return widgets;
};

/**
 * Returns whether or not the row is the "special" hidden one
 */
Mashup_Page_UI_Layout_Row.prototype.isHiddenRow = function() {
	return this.table.rows.indexOf(this) == this.table.rows.length - 1;
};

/**
 * Returns whether or not we can edit this row
 */
Mashup_Page_UI_Layout_Row.prototype.canEdit = function() {
	return !this.isHiddenRow();
};

/**
 * Returns whether or not we can remove this row
 */
Mashup_Page_UI_Layout_Row.prototype.canRemove = function() {
	// must have at least one row (+ hidden row)
	if (this.table.rows.length <= 2) {
		// unless we have several tables
		if (this.getTable().getLayout().getTables().length == 1) {
			return false;
		}
	}

	// cannot remove special hidden row
	if (this.isHiddenRow()) {
		return false;
	}

	// check the cells if we can remove it
	var nbCols = 0;
	var selfIdx = this.idx, bottomIdx = getBottomRow(this).idx;
	var cells = this.getCells();
	for (var i = 0, cellsLength = cells.length; i < cellsLength; i++) {
		if (cells[i].rowStart != selfIdx || cells[i].rowEnd != bottomIdx) {
			return false;
		}
		nbCols += getComputedColspan(this.getTable(), cells[i].getColStart(), cells[i].getColEnd());
	}

	// not the same columns number ( == rowspan )
	if (nbCols != (this.table.cols.length - 1)) {
		return false;
	}

	return true;
};

/**
 * Returns whether or not we can insert a row before this row
 */
Mashup_Page_UI_Layout_Row.prototype.canInsertBefore = function() {
	if (!this.isHiddenRow()) {
		// cannot insert a row before if not the same column number ( == rowspan )
		var nbCols = 0;
		var cells = this.getCells();
		for (var i = 0; i < cells.length; i++) {
			nbCols += getComputedColspan(this.getTable(), cells[i].getColStart(), cells[i].getColEnd());
		}
		if (nbCols != (this.table.cols.length - 1)) {
			return false;
		}
	} else {
		// cannot insert before hidden unless it's the last table
		if (this.getTable().getPosition() != (this.getTable().getLayout().getTables().length - 1)) {
			return false;
		}
	}
	return true;
};

/**
 * Register a new cell in this row
 *
 * @param jsonCell
 * @returns {Mashup_Page_UI_Layout_Cell}
 */
Mashup_Page_UI_Layout_Row.prototype.registerCell = function(jsonCell) {
	var cell = new Mashup_Page_UI_Layout_Cell(this, jsonCell);
	this.cells.push(cell);
	this.sortCells();
	return cell;
};

/**
 * Unregisters the given cell from the row
 *
 * @param cell
 */
Mashup_Page_UI_Layout_Row.prototype.unRegisterCell = function(cell) {
	this.cells.splice(this.cells.indexOf(cell), 1);
};

/**
 * Removes the given cell from the row
 *
 * @param cell
 */
Mashup_Page_UI_Layout_Row.prototype.removeCell = function(cell) {
	cell.remove();
	this.cells.splice(this.cells.indexOf(cell), 1);
};

/**
 * Sorts the cell inside the row
 */
Mashup_Page_UI_Layout_Row.prototype.sortCells = function() {
	this.cells.sort(function(a, b) { return a.getColStart() - b.getColStart(); });
};

/**
 * Returns the available layout for the rows
 *
 * @static
 * @return {array}
 */
Mashup_Page_UI_Layout_Row.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('<tr class="cell-container"></tr>');
		this.el.data('_this', this);
		for (var j = 0; j < this.cells.length; j++) {
			this.cells[j].getEl().appendTo(this.el);
		}
	}
	return this.el;
};

/**
 * Inserts a new row before self
 *
 * @param colStart
 * @param colEnd
 * @param rowStart
 * @param rowEnd
 * @returns
 */
Mashup_Page_UI_Layout_Row.prototype.insertRowBefore = function(colStart, colEnd, rowStart, rowEnd) {
	// insert new row with a cell
	var newRow = this.getTable().registerRow(createRow(rowStart));
	var newCell = newRow.registerCell(createCellForTable(this.getTable(), colStart, colEnd, rowStart, rowEnd));
	newCell.getEl().appendTo(newRow.getEl());

	// update the cells rowspan
	var cells = this.getTable().getCells();
	for (var i = 0; i < cells.length; i++) {
		if (rowStart > cells[i].getRowStart() && rowStart < cells[i].getRowEnd()) {
			cells[i].update();
		}
	}

	// append the row in the DOM
	if (this.isHiddenRow()) {
		newRow.getEl().appendTo(this.getTable().getElTable());
	} else {
		newRow.getEl().insertBefore(this.getEl());
	}

	return newRow;
};

/**
 * Inserts a new row after self
 */
Mashup_Page_UI_Layout_Row.prototype.insertRowAfter = function(colStart, colEnd, rowStart, rowEnd) {
	var newRow = this.insertRowBefore(colStart, colEnd, rowStart, rowEnd);
	newRow.getEl().insertAfter(this.getEl()); // move the row :p
	return newRow;
};

/**
 * User process to insert a new row before the given one
 * 
 * @param row {Mashup_Page_UI_Layout_Row}
 */
Mashup_Page_UI_Layout_Row.prototype.doInsertNewRow = function() {
	var table = this.getTable(), layout = table.getLayout();
	var jsonTableRow = createTableRow(layout);

	// if first row or hidden row then simply add a new table before
	if (this.isHiddenRow()) {
		var newTable = layout.registerTable(jsonTableRow, table.getPosition() + 1);
		newTable.getEl().appendTo(layout.getEl());
		layout.onCreateTable(newTable);
		return;
	} else if (getTopRow(this) == null) {
		var newTable = layout.registerTable(jsonTableRow, table.getPosition());
		newTable.getEl().insertBefore(table.getEl());
		layout.onCreateTable(newTable);
		return;
	}

	// split the current table and insert the new one between them

	var splitIdx = table.getRows().indexOf(this);
	var jsonTop = clone(table.getJson());
	var jsonBottom = clone(jsonTop);

	jsonTop.rows = jsonTop.rows.slice(0, splitIdx);
	jsonBottom.rows = jsonBottom.rows.slice(splitIdx);

	// inject bottom table
	var bottomTable = layout.registerTable(jsonBottom, table.getPosition() + 1);
	bottomTable.getEl().insertAfter(table.getEl());
	layout.onCreateTable(bottomTable);
	optimizeTable(bottomTable);

	// inject top table
	var topTable = layout.registerTable(jsonTop, table.getPosition());
	table.getEl().replaceWith(topTable.getEl());
	layout.onCreateTable(topTable);
	optimizeTable(topTable);

	// inject new table
	var newTable = layout.registerTable(jsonTableRow, table.getPosition());
	newTable.getEl().insertAfter(topTable.getEl());
	layout.onCreateTable(newTable);

	// remove current table
	layout.unregisterTable(table);
	table.remove();
};

/**
 * User process to remove this row
 */
Mashup_Page_UI_Layout_Row.prototype.doRemoveRow = function() {
	var cells = this.getCells();

	var $toRemove = $();
	for (var i = 0; i < cells.length; i++) {
		$toRemove.push(cells[i].getEl().get(0));
	}
	$toRemove.addClass('to-remove');

	var _this = this;
	new Mashup_Popup_Confirm({
		title: _.LAYOUT_REMOVE_ROW_TITLE(),
		text: _.LAYOUT_REMOVE_ROW_CONFIRM(),
		buttonOkLabel: _.YES(),
		buttonCancelLabel: _.NO(),
		onClickOkCallback: function(context) {
			if (_this.getTable().getRows().length == 2) {
				_this.getTable().getLayout().removeTable(_this.getTable());
			} else {
				// retrieve the row beneath self
				var bottomRow = getBottomRow(_this);

				// remove self
				_this.getTable().removeRow(_this);

				// update the idx of the cells
				var cells = _this.getTable().getCells();
				for (var i = 0; i < cells.length; i++) {
					if (cells[i].getRowEnd() == _this.getIdx()) {
						cells[i].update(null, null, null, bottomRow.getIdx());
					} else if (cells[i].getRowStart() == _this.getIdx()) {
						cells[i].update(null, null, bottomRow.getIdx());
					}
				}

				// trigger onRemove callback
				_this.getTable().onRemoveRow(_this);
			}
			this.remove();
		},
		onClickCancelCallback: function() {
			$toRemove.removeClass('to-remove');
			this.remove();
		}
	}).show();
};

/**
 * User process to edit this row properties
 */
Mashup_Page_UI_Layout_Row.prototype.doEditRow = function() {
	if (this.bottomToolbar == undefined) {
		// create our content
		var editProperties = new Mashup_Window_TabContent_EditProperties(this, {
			isReadOnly: this.getPageUI().isReadOnly()
		}, Mashup_Parameter_Factory.createRowParameters());

		// open it as a bottomToolbar
		this.bottomToolbar = new Mashup_BottomToolbar();
		this.bottomToolbar.setContent(editProperties);
	}
	window.mashupBuilder.openBottomToolbar(this.bottomToolbar);
};

/**
 * Called when the row has been updated
 */
Mashup_Page_UI_Layout_Row.prototype.onUpdate = function() {
	return this.getPageUI().onUpdate();
};

/**
 * Removes the row from the DOM
 */
Mashup_Page_UI_Layout_Row.prototype.remove = function() {
	for (var i = 0; this.cells.length > 0; i++) {
		this.removeCell(this.cells[0]);
	}
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Returns the row as JSON
 *
 * @returns
 */
Mashup_Page_UI_Layout_Row.prototype.getJson = function() {
	this.json.rowIdx = this.getIdx();
	this.json.cells = [];
	for (var i = 0 ; i < this.cells.length; i++) {
		this.json.cells.push(this.cells[i].getJson());
	}
	return this.json;
};


/**
 * File: /resources/mashupBuilder/js/Page/UI/Layout/Cell.js
 */
/**
 * A class that represent a Cell in a Table
 *
 * @constructor
 * @this {Mashup_Page_UI_Layout_Cell}
 * 
 * @param {Mashup_Page_UI_Layout_Row} row
 * @param {object} jsonCell
 */
function Mashup_Page_UI_Layout_Cell(row, jsonCell) {
	this.row = row;
	this.json = jsonCell;
	this.errorsCount = 0;

	this.setColStart(jsonCell.colStart);
	this.setColEnd(jsonCell.colEnd);
	this.setRowStart(jsonCell.rowStart);
	this.setRowEnd(jsonCell.rowEnd);
}

/**
 * Returns the Mashup UI
 *
 * @returns {Mashup_Page_UI}
 */
Mashup_Page_UI_Layout_Cell.prototype.getPageUI = function() {
	return this.row.getPageUI();
};

/**
 * Returns the Mashup UI Table
 *
 * @returns {Mashup_Page_UI_Layout_Table}
 */
Mashup_Page_UI_Layout_Cell.prototype.getTable = function() {
	return this.row.getTable();
};

/**
 * Returns the parent row
 *
 * @returns {Mashup_Page_UI_Layout_Row}
 */
Mashup_Page_UI_Layout_Cell.prototype.getRow = function() {
	return this.row;
};

/**
 * Returns the parent col
 * 
 * @returns {Mashup_Page_UI_Layout_Col}
 */
Mashup_Page_UI_Layout_Cell.prototype.getCol = function() {
	return this.getTable().getCol(this.colEnd);
};

/**
 * Returns the colstart of the cell
 * 
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.getColStart = function() {
	return this.colStart;
};

/**
 * Sets the colstart of the cell
 * 
 * @param colStart
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.setColStart = function(colStart) {
	if (colStart != null) {
		this.colStart = parseFloat(colStart);
	}
};

/**
 * Returns the colend of the cell
 * 
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.getColEnd = function() {
	return this.colEnd;
};

/**
 * Sets the colEnd of the cell
 * 
 * @param colEnd
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.setColEnd = function(colEnd) {
	if (colEnd != null) {
		this.colEnd = parseFloat(colEnd);
	}
};

/**
 * Returns the rowStart of the cell
 * 
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.getRowStart = function() {
	return this.rowStart;
};

/**
 * Sets the rowStart of the cell
 * 
 * @param rowStart
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.setRowStart = function(rowStart) {
	if (rowStart != null) {
		this.rowStart = parseFloat(rowStart);
	}
};

/**
 * Returns the rowend of the cell
 * 
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.getRowEnd = function() {
	return this.rowEnd;
};

/**
 * Sets the rowend of the cell
 * 
 * @param rowEnd
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.setRowEnd = function(rowEnd) {
	if (rowEnd != null) {
		this.rowEnd = parseFloat(rowEnd);
	}
};

/**
 * Returns the widget container
 *
 * @returns {Mashup_Page_UI_Layout_WidgetContainer}
 */
Mashup_Page_UI_Layout_Cell.prototype.getSubWidgetContainer = function() {
	if (this.subWidgetContainer == undefined) {
		this.subWidgetContainer = new Mashup_Page_UI_Layout_WidgetContainer(this, this.json.widgets);
	}
	return this.subWidgetContainer;
};

/**
 * Returns whether the cell has widgets or not
 */
Mashup_Page_UI_Layout_Cell.prototype.hasWidgets = function() {
	return this.getSubWidgetContainer().getWidgets(false).length > 0;
};

/**
 * Returns all the widgets of the cell
 *
 * @param recursive
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.getWidgets = function(recursive) {
	return this.getSubWidgetContainer().getWidgets(recursive);
};

/**
 * Returns whether the cell can support this container or not
 *
 * @param widgetId
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Cell.prototype.canSupportWidget = function(widgetId) {
	return true;
};

/**
 * Updates the error count
 *
 * @param {number}
 *            errorsCount Can be positive (more errors) or negative (less
 *            errors)
 */
Mashup_Page_UI_Layout_Cell.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;
	if (this.errorsCount == 0) {
		this.getEl().removeClass('has-error');
	} else {
		this.getEl().addClass('has-error');
	}
	this.getPageUI().updateError(errorsCount);
};

/**
 * Returns the DOM element for this cell
 *
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
		'<td class="cell" name="cell" valign="top">' +
			'<div class="cell-wrapper">' +
			'</div>' +
		'</td>');
		this.el.data('_this', this);
		this.getSubWidgetContainer().getEl().appendTo(this.getElWrapper());
		this.update();
	}
	return this.el;
};

/**
 * Returns the DOM node for the cell wrapper
 *
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.getElWrapper = function() {
	if (this.elWrapper == undefined) {
		this.elWrapper = this.getEl().find('.cell-wrapper');
	}
	return this.elWrapper;
};

/**
 * Updates the start/end index of the cell
 * 
 * @param colStart
 * @param colEnd
 * @param rowStart
 * @param rowEnd
 */
Mashup_Page_UI_Layout_Cell.prototype.update = function(colStart, colEnd, rowStart, rowEnd) {
	// update start/end indexes
	this.setColStart(colStart);
	this.setColEnd(colEnd);
	this.setRowStart(rowStart);
	this.setRowEnd(rowEnd);

	// update the new colspan/rowspan
	this.getEl().attr({
		colspan: getComputedColspan(this.getTable(), this.getColStart(), this.getColEnd()),
		rowspan: getComputedRowspan(this.getTable(), this.getRowStart(), this.getRowEnd())
	});

	// sorts the cell of this row
	this.getRow().sortCells();
};

/**
 * Called when the cell has been updated
 */
Mashup_Page_UI_Layout_Cell.prototype.onUpdate = function() {
	return this.getPageUI().onUpdate();
};

/**
 * User process to edit cell properties
 */
Mashup_Page_UI_Layout_Cell.prototype.doEditCell = function() {
	if (this.bottomToolbar == undefined) {
		// create our content
		var editProperties = new Mashup_Window_TabContent_EditProperties(this, {
			isReadOnly: this.getPageUI().isReadOnly()
		}, Mashup_Parameter_Factory.createCellParameters());

		// open it as a bottomToolbar
		this.bottomToolbar = new Mashup_BottomToolbar();
		this.bottomToolbar.setContent(editProperties);
	}

	window.mashupBuilder.openBottomToolbar(this.bottomToolbar);
};

/**
 * Joins the cell horizontally (LEFT)
 */
Mashup_Page_UI_Layout_Cell.prototype.doJoinHL = function() {
	if ((leftCell = getLeftCell(this)) != null) {
		// copy widgets from the left cell to the current and remove it
		leftCell.getSubWidgetContainer().moveTo(this.getSubWidgetContainer());
		leftCell.getRow().removeCell(leftCell);

		// update index of this cell
		this.update(leftCell.getColStart());

		// trigger remove callback
		this.getTable().onRemoveCell(leftCell);
	}
};

/**
 * Joins the cell horizontally (RIGHT)
 */
Mashup_Page_UI_Layout_Cell.prototype.doJoinHR = function() {
	if ((rightCell = getRightCell(this)) != null) {
		// copy widgets from the right cell to the current and remove it
		rightCell.getSubWidgetContainer().moveTo(this.getSubWidgetContainer());
		rightCell.getRow().removeCell(rightCell);

		// update index of this cell
		this.update(null, rightCell.getColEnd());

		// trigger remove callback
		this.getTable().onRemoveCell(rightCell);
	}
};

/**
 * Splits the cell horizontally (LEFT)
 */
Mashup_Page_UI_Layout_Cell.prototype.doSplitHL = function() {
	if ((splitHRColConf = getComputedSplitHRColConf(this)) == null) {
		return;
	}

	// col's configuration exists
	if (splitHRColConf.splitCol != null) {
		var newCell = this.getRow().registerCell(createCellForTable(this.getTable(), this.getColStart(), splitHRColConf.splitColIdx, this.getRowStart(), this.getRowEnd()));
		newCell.getEl().insertBefore(this.getEl());
		this.update(splitHRColConf.splitColIdx);
		this.getTable().onCreateCell(newCell);
		return;
	}

	// retrieve col and width of both columns
	var col = this.getCol();
	var widthLeft, widthRight;
	if (this.getTable().getWidthFormat() == '%') {
		widthLeft = parseInt(col.getWidth() / 2);
		widthRight = parseInt(col.getWidth() - widthLeft);
	} else {
		widthLeft = parseFloat((col.getWidth() / 2).toFixed(1));
		if (!isInteger((tmp = this.getTable().toWidth(widthLeft)))) {
			widthLeft = this.getTable().toRatio(parseInt(tmp));
		}
		widthRight = parseFloat((col.getWidth() - widthLeft).toFixed(1));
	}

	// create a new column
	var newCol = this.getTable().registerCol(createCol(splitHRColConf.splitColIdx, widthLeft));
	newCol.getEl().insertBefore(col.getEl());
	this.getTable().getLayout().getEditLayout().initializeCol(newCol);

	// create a new cell
	var newCell = this.getRow().registerCell(createCellForTable(this.getTable(), this.getColStart(), splitHRColConf.splitColIdx, this.getRowStart(), this.getRowEnd()));
	newCell.getEl().insertBefore(this.getEl());

	// update old column
	this.update(splitHRColConf.splitColIdx);
	col.setWidth(widthRight);

	// update impacted cells
	var cells = this.getTable().getCells();
	for (var i = 0; i < cells.length; i++) {
		if (splitHRColConf.splitColIdx > cells[i].getColStart() && splitHRColConf.splitColIdx < cells[i].getColEnd()) {
			cells[i].update();
		}
	}

	// trigger onCreate callback
	this.getTable().onCreateCell(newCell);
};

/**
 * Splits the cell horizontally (RIGHT)
 */
Mashup_Page_UI_Layout_Cell.prototype.doSplitHR = function() {
	if ((splitHLColConf = getComputedSplitHLColConf(this)) == null) {
		return;
	}

	// col's configuration exists
	if (splitHLColConf.splitCol != null) {
		newCell = this.getRow().registerCell(createCellForTable(this.getTable(), splitHLColConf.splitColIdx, this.getColEnd(), this.getRowStart(), this.getRowEnd()));
		newCell.getEl().insertAfter(this.getEl());
		this.update(this.getColStart(), splitHLColConf.splitColIdx);
		this.getTable().onCreateCell(newCell);
		return;
	}

	// retrieve col and width of both columns
	var col = this.getCol();
	var widthLeft, widthRight;
	if (this.getTable().getWidthFormat() == '%') {
		widthLeft = parseInt(col.getWidth() / 2);
		widthRight = parseInt(col.getWidth() - widthLeft);
	} else {
		widthLeft = parseFloat((col.getWidth() / 2).toFixed(1));
		if (!isInteger((tmp = this.getTable().toWidth(widthLeft)))) {
			widthLeft = this.getTable().toRatio(parseInt(tmp));
		}
		widthRight = parseFloat((col.getWidth() - widthLeft).toFixed(1));
	}

	// create a new column
	var newCol = this.getTable().registerCol(createCol(splitHLColConf.splitColIdx, widthRight));
	newCol.getEl().insertBefore(col.getEl());
	this.getTable().getLayout().getEditLayout().initializeCol(newCol);

	// create a new cell
	var newCell = this.getRow().registerCell(createCellForTable(this.getTable(), splitHLColConf.splitColIdx, this.getColEnd(), this.getRowStart(), this.getRowEnd()));
	newCell.getEl().insertAfter(this.getEl());

	// update old column
	this.update(null, splitHLColConf.splitColIdx);
	col.setWidth(widthLeft);

	// update impacted cells
	var cells = this.getTable().getCells();
	for (var i = 0; i < cells.length; i++) {
		if (splitHLColConf.splitColIdx > cells[i].getColStart() && splitHLColConf.splitColIdx < cells[i].getColEnd()) {
			cells[i].update();
		}
	}

	// trigger onCreate callback
	this.getTable().onCreateCell(newCell);
};

/**
 * Joins the cell vertically (TOP)
 */
Mashup_Page_UI_Layout_Cell.prototype.doJoinVT = function() {
	if ((topCell = getTopCell(this)) != null) {
		// copy widgets from current cell to the top cell and remove it
		this.getSubWidgetContainer().moveTo(topCell.getSubWidgetContainer());
		this.getRow().removeCell(this);

		// update index of top cell
		topCell.update(null, null, null, this.getRowEnd());

		// trigger remove callback
		this.getTable().onRemoveCell(this);
	}
};

/**
 * Joins the cell vertically (BOTTOM)
 */
Mashup_Page_UI_Layout_Cell.prototype.doJoinVB = function() {
	if ((bottomCell = getBottomCell(this)) != null) {
		// copy widgets from bottom cell to the current cell and remove it
		bottomCell.getSubWidgetContainer().moveTo(this.getSubWidgetContainer());
		bottomCell.getRow().removeCell(bottomCell);

		// update index of current cell
		this.update(null, null, null, bottomCell.getRowEnd());

		// trigger remove callback
		this.getTable().onRemoveCell(bottomCell);
	}
};

/**
 * Splits the cell vertically (TOP)
 */
Mashup_Page_UI_Layout_Cell.prototype.doSplitVT = function() {
	var splitVBConf = getComputedSplitVBConf(this);
	if (splitVBConf.splitRow != null) {
		// row's configuration exists
		var cell = splitVBConf.splitRow.registerCell(createCellForTable(this.getTable(), this.colStart, this.colEnd, splitVBConf.splitRowIdx, this.rowEnd));

		var leftCell = getLeftCell(cell, true);
		if (leftCell != null) {
			// Insert after left cell
			cell.getEl().insertAfter(leftCell.getEl());
		} else {
			// Insert before right cell
			cell.getEl().insertBefore(getRightCell(cell, true).getEl());
		}
		this.getSubWidgetContainer().moveTo(cell.getSubWidgetContainer());
		this.update(null, null, null, splitVBConf.splitRowIdx);
		this.getTable().onCreateCell(cell);

	} else {
		// row's configuration doesn't exist
		var nbRowspans = getComputedRowspan(this.getTable(), this.rowStart, splitVBConf.splitRowIdx);
		var row = getBottomRow(this.getRow(), nbRowspans - 1);
		var newRow = row.insertRowAfter(this.colStart, this.colEnd, splitVBConf.splitRowIdx, this.rowEnd);
		this.getSubWidgetContainer().moveTo(newRow.getCells()[0].getSubWidgetContainer());
		this.update(null, null, null, splitVBConf.splitRowIdx);
		this.getTable().onCreateRow(newRow);
	}
};

/**
 * Splits the cell vertically (BOTTOM)
 */
Mashup_Page_UI_Layout_Cell.prototype.doSplitVB = function() {
	var splitVTConf = getComputedSplitVTConf(this);
	if (splitVTConf.splitRow != null) {
		// row's configuration exists
		var cell = splitVTConf.splitRow.registerCell(createCellForTable(this.getTable(), this.colStart, this.colEnd, splitVTConf.splitRowIdx, this.rowEnd));

		var leftCell = getLeftCell(cell, true);
		if (leftCell != null) {
			// Insert after left cell
			cell.getEl().insertAfter(leftCell.getEl());
		} else {
			// Insert before right cell
			cell.getEl().insertBefore(getRightCell(cell, true).getEl());
		}
		this.update(null, null, null, splitVTConf.splitRowIdx);
		this.getTable().onCreateCell(cell);

	} else {
		// row's configuration doesn't exist
		var nbRowspans = getComputedRowspan(this.getTable(), this.rowStart, splitVTConf.splitRowIdx);
		var row = getBottomRow(this.getRow(), nbRowspans - 1);
		var newRow = row.insertRowAfter(this.colStart, this.colEnd, splitVTConf.splitRowIdx, this.rowEnd);
		this.update(null, null, null, splitVTConf.splitRowIdx);
		this.getTable().onCreateRow(newRow);
	}
};

/**
 * Removes this cell from the DOM
 */
Mashup_Page_UI_Layout_Cell.prototype.remove = function() {
	this.getSubWidgetContainer().remove();
	if (this.el != null) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Returns the JSON of the cell
 * 
 * @returns
 */
Mashup_Page_UI_Layout_Cell.prototype.getJson = function() {
	this.json.colStart = this.getColStart();
	this.json.colEnd = this.getColEnd();
	this.json.colSpan = getComputedColspan(this.getTable(), this.getColStart(), this.getColEnd());
	this.json.rowStart = this.getRowStart();
	this.json.rowEnd = this.getRowEnd();
	this.json.rowSpan = getComputedRowspan(this.getTable(), this.getRowStart(), this.getRowEnd());
	this.json.widgets = this.getSubWidgetContainer().getJson();
	return this.json;
};


/**
 * File: /resources/mashupBuilder/js/Page/UI/Layout/Cell/Pad.js
 */
/**
 * A class that represent the navigation pad of a cell
 *
 * @constructor
 * @this {Mashup_Page_UI_Layout_Cell_Pad}
 */
function Mashup_Page_UI_Layout_Cell_Pad() {
}

/**
 * Returns the currently active cell
 * 
 * @returns {Mashup_Page_UI_Layout_Cell}
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.getCell = function() {
	return this.cell;
};

/**
 * Sets the currently active cell
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.setCell = function(cell) {
	this.cell = cell;
	this.onCellUpdate();
	this.reset();
	this.getEl().prependTo(cell.getEl());
};

/**
 * Disables the pad
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.disable = function() {
	if (this.cell != undefined) {
		this.cleanup();
		this.cell = undefined;
	}
	this.getEl().detach();
};

/**
 * Returns the DOM node for the pad
 * 
 * @returns
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.getEl = function() {
	if (this.el == undefined) {
		// relative position on TD does not work
		// so we use a DIV wrapper for the cellpad
		this.el = $('' +
			'<div class="cellpad-wrapper">' +
			'	<div class="cellpad navigation">' +
			'		<span class="icon icon-properties" name="doEditProperties"></span>' +
			'		<div class="arrow arrow-left">' +
			'			<div class="actions">' +
			'				<span class="join joinH joinHL" name="doJoinHL"></span><span class="split splitH splitHL" name="doSplitHL"></span>' +
			'			</div>' +
			'		</div>' +
			'		<div class="arrow arrow-top">' +
			'			<div class="actions">' +
			'				<span class="join joinV joinVT" name="doJoinVT"></span><span class="split splitV splitVT" name="doSplitVT"></span>' +
			'			</div>' +
			'		</div>' +
			'		<div class="arrow arrow-right">' +
			'			<div class="actions">' +
			'				<span class="split splitH splitHR" name="doSplitHR"></span><span class="join joinH joinHR" name="doJoinHR"></span>' +
			'			</div>' +
			'		</div>' +
			'		<div class="arrow arrow-bottom">' +
			'			<div class="actions">' +
			'				<span class="split splitV splitVB" name="doSplitVB"></span><span class="join joinV joinVB" name="doJoinVB"></span>' +
			'			</div>' +
			'		</div>' +
			'	</div>' +
			'</div>');

		this.el.on('click', $.proxy(this.handleEventOnClick, this));

		this.el.on('mouseenter', '.arrow-left', $.proxy(function() { this.reset(); this.getElPad().addClass('arrow-left-hover'); }, this));
		this.el.on('mouseenter', '.arrow-top', $.proxy(function() { this.reset(); this.getElPad().addClass('arrow-top-hover'); }, this));
		this.el.on('mouseenter', '.arrow-right', $.proxy(function() { this.reset(); this.getElPad().addClass('arrow-right-hover'); }, this));
		this.el.on('mouseenter', '.arrow-bottom', $.proxy(function() { this.reset(); this.getElPad().addClass('arrow-bottom-hover'); }, this));
		this.el.on('mouseleave', '.arrow-left, .arrow-top, .arrow-right, .arrow-bottom,', $.proxy(function() { this.reset(); }, this));

		this.el.on('mouseenter', '.joinHR', $.proxy(this.handleEventOnMouseEnterJoinHR, this));
		this.el.on('mouseleave', '.joinHR', $.proxy(this.handleEventOnMouseLeaveJoinHR, this));

		this.el.on('mouseenter', '.joinHL', $.proxy(this.handleEventOnMouseEnterJoinHL, this));
		this.el.on('mouseleave', '.joinHL', $.proxy(this.handleEventOnMouseLeaveJoinHL, this));

		this.el.on('mouseenter', '.joinVT', $.proxy(this.handleEventOnMouseEnterJoinVT, this));
		this.el.on('mouseleave', '.joinVT', $.proxy(this.handleEventOnMouseLeaveJoinVT, this));

		this.el.on('mouseenter', '.joinVB', $.proxy(this.handleEventOnMouseEnterJoinVB, this));
		this.el.on('mouseleave', '.joinVB', $.proxy(this.handleEventOnMouseLeaveJoinVB, this));

		this.el.on('mouseenter', '.splitHR', $.proxy(this.handleEventOnMouseEnterSplitHR, this));
		this.el.on('mouseleave', '.splitHR', $.proxy(this.handleEventOnMouseLeaveSplitHR, this));

		this.el.on('mouseenter', '.splitHL', $.proxy(this.handleEventOnMouseEnterSplitHL, this));
		this.el.on('mouseleave', '.splitHL', $.proxy(this.handleEventOnMouseLeaveSplitHL, this));

		this.el.on('mouseenter', '.splitVT', $.proxy(this.handleEventOnMouseEnterSplitVT, this));
		this.el.on('mouseleave', '.splitVT', $.proxy(this.handleEventOnMouseLeaveSplitVT, this));

		this.el.on('mouseenter', '.splitVB', $.proxy(this.handleEventOnMouseEnterSplitVB, this));
		this.el.on('mouseleave', '.splitVB', $.proxy(this.handleEventOnMouseLeaveSplitVB, this));
	}

	return this.el;
};

/**
 * Returns the DOM node for the cellpad
 * 
 * @returns
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.getElPad = function() {
	if (this.elPad == undefined) {
		this.elPad = this.getEl().find('> .cellpad');
	}
	return this.elPad;
};

/**
 * Updates the state of the pad for the currently active cell
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.onCellUpdate = function() {
	this.getEl().css({
		top: this.cell.getEl().height() / 2,
		left: this.cell.getEl().width() / 2
	});

	if ((splitHRColConf = getComputedSplitHRColConf(this.cell)) == null) {
		this.getEl().find('.splitHR').attr('title', _.LAYOUT_CELLPAD_CELL_TOO_SMALL()).addClass('disabled');
	} else {
		this.getEl().find('.splitHR').attr('title', '').removeClass('disabled');
	}

	if ((splitHRColConf = getComputedSplitHRColConf(this.cell)) == null) {
		this.getEl().find('.splitHL').attr('title', _.LAYOUT_CELLPAD_CELL_TOO_SMALL()).addClass('disabled');
	} else {
		this.getEl().find('.splitHL').attr('title', '').removeClass('disabled');
	}

	if ((leftCell = getLeftCell(this.cell)) == null || leftCell.getRowStart() != this.cell.getRowStart() || leftCell.getRowEnd() != this.cell.getRowEnd()) {
		this.getEl().find('.joinHL').attr('title', _.LAYOUT_CELLPAD_CELL_NOT_SAME_LINE()).addClass('disabled');
	} else {
		this.getEl().find('.joinHL').attr('title', '').removeClass('disabled');
	}

	if ((rightCell = getRightCell(this.cell)) == null || rightCell.getRowStart() != this.cell.getRowStart() || rightCell.getRowEnd() != this.cell.getRowEnd()) {
		this.getEl().find('.joinHR').attr('title', _.LAYOUT_CELLPAD_CELL_NOT_SAME_LINE()).addClass('disabled');
	} else {
		this.getEl().find('.joinHR').attr('title', '').removeClass('disabled');
	}

	if (getTopCell(this.cell) == null) {
		this.getEl().find('.joinVT').attr('title', _.LAYOUT_CELLPAD_CELL_NOT_SAME_COLUMN()).addClass('disabled');
	} else {
		this.getEl().find('.joinVT').attr('title', '').removeClass('disabled');
	}

	if (getBottomCell(this.cell) == null) {
		this.getEl().find('.joinVB').attr('title', _.LAYOUT_CELLPAD_CELL_NOT_SAME_COLUMN()).addClass('disabled');
	} else {
		this.getEl().find('.joinVB').attr('title', '').removeClass('disabled');
	}
};

/**
 * Resets the state of the pad
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.reset = function() {
	this.getElPad().removeClass('arrow-top-hover arrow-bottom-hover arrow-left-hover arrow-right-hover');
};

/**
 * jQuery onClick event handler
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'doEditProperties':
			this.cell.doEditCell();
			return false;
		default:
			if (!target.hasClass('disabled')) {
				var cell = this.cell;
				this.disable();
				if ((func = cell[target.attr('name')]) != undefined) {
					func.call(cell);
				}
			}
		}
	}
};

/**
 * Called when the user hover the RIGHT join
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseEnterJoinHR = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		if ((rightCell = getRightCell(this.cell)) != null) {
			this.cell.getEl().addClass('to-expand');
			rightCell.getEl().addClass('to-remove');
		}
	}
	return false;
};

/**
 * Called when the user leave the RIGHT join
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseLeaveJoinHR = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		if ((rightCell = getRightCell(this.cell)) != null) {
			this.cell.getEl().removeClass('to-expand');
			rightCell.getEl().removeClass('to-remove');
		}
	}
};

/**
 * Called when the user hover the LEFT join
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseEnterJoinHL = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		if ((leftCell = getLeftCell(this.cell)) != null) {
			this.cell.getEl().addClass('to-expand');
			leftCell.getEl().addClass('to-remove');
		}
	}
	return false;
};

/**
 * Called when the user leave the LEFT join
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseLeaveJoinHL = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		if ((leftCell = getLeftCell(this.cell)) != null) {
			this.cell.getEl().removeClass('to-expand');
			leftCell.getEl().removeClass('to-remove');
		}
	}
};

/**
 * Called when the user hover the LEFT split
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseEnterSplitHL = function(e) {
	if (getEventTarget(e).hasClass('disabled') || (splitHRColConf = getComputedSplitHRColConf(this.cell)) == null) {
		return;
	}

	var width = this.cell.getEl().width();

	var cellOffset = null;
	if (splitHRColConf.splitCol == null) {
		cellOffset = parseInt(width / 2);
	} else {
		cellOffset = splitHRColConf.splitCol.$resizeBarV.position().left - this.cell.getEl().position().left;
	}

	this.cell.getElWrapper()
		.append('<div class="willSplitHLHere" style="left:' + cellOffset + 'px; height:' + (this.cell.getEl().height() - 8) + 'px;"></div>')
		.css({ width: width - cellOffset - 5, paddingLeft: cellOffset + 5 });

	this.cell.getSubWidgetContainer().getEl().css('width', width - cellOffset - 5);

	return false;
};

/**
 * Called when the user leave the LEFT split
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseLeaveSplitHL = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		this.cell.getSubWidgetContainer().getEl().css('width', '');
		this.cell.getElWrapper()
			.find('> .willSplitHLHere').remove()
			.end()
			.css({ width: '', paddingLeft: '' });
	}
};

/**
 * Called when the user hover the RIGHT split
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseEnterSplitHR = function(e) {
	if (getEventTarget(e).hasClass('disabled') || (splitHLColConf = getComputedSplitHLColConf(this.cell)) == null) {
		return;
	}

	var cellOffset = null;
	if (splitHLColConf.splitCol == null) {
		cellOffset = parseInt(this.cell.getEl().width() / 2);
	} else {
		cellOffset = splitHLColConf.splitCol.$resizeBarV.position().left - this.cell.getEl().position().left;
	}

	this.cell.getElWrapper().append('<div class="willSplitHRHere" style="left:' + cellOffset + 'px; height:' + (this.cell.getEl().height() - 8) + 'px;"></div>');
	this.cell.getSubWidgetContainer().getEl().css('width', cellOffset - 5);

	return false;
};

/**
 * Called when the user leave the RIGHT split
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseLeaveSplitHR = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		this.cell.getSubWidgetContainer().getEl().css('width', '');
		this.cell.getElWrapper()
			.find('> .willSplitHRHere').remove()
			.end()
			.css({ width: '' });
	}
};

/**
 * Called when the user hover the TOP join
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseEnterJoinVT = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		if ((topCell = getTopCell(this.cell)) != null) {
			this.cell.getEl().addClass('to-expand');
			topCell.getEl().addClass('to-remove');
		}
	}
	return false;
};

/**
 * Called when the user leave the TOP join
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseLeaveJoinVT = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		if ((topCell = getTopCell(this.cell)) != null) {
			this.cell.getEl().removeClass('to-expand');
			topCell.getEl().removeClass('to-remove');
		}
	}
};

/**
 * Called when the user hover the BOTTOM join
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseEnterJoinVB = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		if ((bottomCell = getBottomCell(this.cell)) != null) {
			this.cell.getEl().addClass('to-expand');
			bottomCell.getEl().addClass('to-remove');
		}
	}
	return false;
};

/**
 * Called when the user leave the BOTTOM join
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseLeaveJoinVB = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		if ((bottomCell = getBottomCell(this.cell)) != null) {
			this.cell.getEl().removeClass('to-expand');
			bottomCell.getEl().removeClass('to-remove');
		}
	}
};

/**
 * Called when the user hover the BOTTOM split
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseEnterSplitVB = function(e) {
	if (getEventTarget(e).hasClass('disabled') || (splitVTConf = getComputedSplitVTConf(this.cell)) == null) {
		return;
	}

	var offset;
	if (splitVTConf.splitRow == null) {
		offset = parseInt(this.cell.getEl().height() / 2);
	} else {
		offset = splitVTConf.splitRow.getEl().position().top - this.cell.getEl().position().top - 3;
	}

	this.cell.getElWrapper()
		.append('<div class="willSplitVBHere" style="bottom:' + offset + 'px; width:' + this.cell.getEl().width() + 'px;"></div>')
		.css('padding-bottom', offset + 5);

	return false;
};

/**
 * Called when the user leave the BOTTOM split
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseLeaveSplitVB = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		this.cell.getElWrapper()
			.find('> .willSplitVBHere').remove()
			.end()
			.css('padding-bottom', '');
	}
};

/**
 * Called when the user hover the TOP split
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseEnterSplitVT = function(e) {
	if (getEventTarget(e).hasClass('disabled') || (splitVBConf = getComputedSplitVBConf(this.cell)) == null) {
		return;
	}

	var offset;
	if (splitVBConf.splitRow == null) {
		offset = parseInt(this.cell.getEl().height() / 2);
	} else {
		offset = splitVBConf.splitRow.getEl().position().top - this.cell.getEl().position().top - 3;
	}

	this.cell.getElWrapper()
		.append('<div class="willSplitVTHere" style="top:' + offset + 'px; width:' + this.cell.getEl().css('width') + '"></div>')
		.css('padding-top', offset + 5);

	return false;
};

/**
 * Called when the user leave the TOP split
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.handleEventOnMouseLeaveSplitVT = function(e) {
	if (!getEventTarget(e).hasClass('disabled')) {
		this.cell.getElWrapper()
			.find('> .willSplitVTHere').remove()
			.end()
			.css('padding-top', '');
	}
};

/**
 * Cleanup the cell from all layout-related states
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.cleanup = function() {
	// cleanup cell
	this.cell.getEl().removeClass('to-remove to-expand');

	// cleanup cell wrapper
	this.cell.getElWrapper()
		.find('.willSplitHRHere,.willSplitHLHere,.willSplitVTHere,.willSplitVBHere').remove().end()
		.css({
			paddingLeft: '',
			paddingTop: '',
			paddingBottom: '',
			width: ''
		});

	// cleanup widget container state
	this.cell.getSubWidgetContainer().getEl().css('width', '');

	// cleanup top-cell
	if ((topCell = getTopCell(this.cell)) != null) {
		topCell.getEl().removeClass('to-remove');
	}

	// cleanup bottom-cell
	if ((bottomCell = getBottomCell(this.cell)) != null) {
		bottomCell.getEl().removeClass('to-remove');
	}

	// cleanup left-cell
	if ((leftCell = getLeftCell(this.cell)) != null) {
		leftCell.getEl().removeClass('to-remove');
	}

	// cleanup right-cell
	if ((rightCell = getRightCell(this.cell)) != null) {
		rightCell.getEl().removeClass('to-remove');
	}
};

/**
 * Removes the pad
 */
Mashup_Page_UI_Layout_Cell_Pad.prototype.remove = function() {
	this.disable();

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Page/UI/Layout/WidgetContainer.js
 */
/**
 * Handles a set of widget
 *
 * @constructor
 * @param parent
 * @param jsonWidgets
 * @returns
 */
function Mashup_Page_UI_Layout_WidgetContainer(parent, jsonWidgets) {
	this.parent = parent;
	this.widgets = [];
	for (var i = 0; i < jsonWidgets.length; i++) {
		this.registerNewWidget(jsonWidgets[i], i);
	}
}

/**
 * Returns whether this container has widgets or not
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.hasWidget = function() {
	return this.widgets.length > 0;
};

/**
 * Returns the parent, could be either a widget or a cell
 *
 * @returns
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.getParent = function() {
	return this.parent;
};

/**
 * Returns the parent widget or NULL if none
 *
 * @returns {Mashup_Page_UI_Layout_Widget}
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.getParentWidget = function() {
	var parent = this.getParent();
	if (isWidget(parent)) {
		return parent;
	} else if (isCell(parent)) {
		return parent.getRow().getTable().getLayout().getParentWidget();
	}
	return null;
};

/**
 * Returns the parent cell
 *
 * @returns {Mashup_Page_UI_Layout_Cell}
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.getParentCell = function() {
	var parent = this.getParent();
	if (parent.constructor == Mashup_Page_UI_Layout_Cell) {
		return parent;
	}
	return parent.getParentCell();
};

/**
 * Returns the Mashup UI
 *
 * @returns {Mashup_Page_UI}
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.getPageUI = function() {
	return this.getParent().getPageUI();
};

/**
 * Returns the DOM element for this container
 *
 * @returns
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
			'<div class="widget-container">' +
				'<div class="drop-zone widget-drop-zone widget-droppable">' +
					_.WIDGET_DROP_WIDGETS_HERE(this.getParentWidget()) +
				'</div>' +
			'</div>'
		);
		this.el.data('_this', this);

		this.elDropZone = this.el.find('.drop-zone');

		for (var i = 0; i < this.widgets.length; i++) {
			this.el.append(this.widgets[i].getEl());
		}

		if (this.getPageUI().isReadOnly()) {
			this.el.find('.drop-zone').remove();
		}
	}
	return this.el;
};

/**
 * Returns the index of the given widget in this container
 *
 * @param widget
 * @returns
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.indexOf = function(widget) {
	return this.widgets.indexOf(widget);
};

/**
 * Register a new widget in this container
 *
 * @private
 * @param jsonWidget
 * @param insertAtPosition
 * @returns {Mashup_Page_UI_Layout_Widget}
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.registerNewWidget = function(jsonWidget, insertAtPosition) {
	var widget;
	if ((definition = getWidgetDefinition(jsonWidget.useWidgetId)) != null && definition.supportWidgetsId.displayType == 'LAYOUT') {
		widget = new Mashup_Page_UI_Layout_Widget_Table(this, jsonWidget);
	} else {
		widget = new Mashup_Page_UI_Layout_Widget(this, jsonWidget);
	}
	this.widgets.splice(insertAtPosition, 0, widget);
	return widget;
};

/**
 * Register a widget from the container
 *
 * @private
 * @param widget
 * @param insertAtPosition
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.registerWidget = function(widget, insertAtPosition) {
	widget.setParentWidgetContainer(this);
	this.widgets.splice(insertAtPosition, 0, widget);
};

/**
 * Unregister a widget from the container
 * @param widget
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.unRegisterWidget = function(widget) {
	this.widgets.splice(this.widgets.indexOf(widget), 1);
	widget.getEl().detach();
};

/**
 * Register and adds a new widget in this container
 *
 * @param jsonWidget
 * @param insertAtPosition
 * @returns
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.addNewWidget = function(jsonWidget, insertAtPosition) {
	var widget = this.registerNewWidget(jsonWidget, insertAtPosition);

	if (this.el != undefined) {
		if (insertAtPosition == 0) {
			widget.getEl().insertAfter(this.elDropZone);
		} else {
			widget.getEl().insertAfter(this.widgets[insertAtPosition - 1].getEl());
		}
	}

	this.getPageUI().onCreateWidget(widget);

	return widget;
};

/**
 * Register and adds the given widget in this container
 *
 * @param widget
 * @param insertAtPosition
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.addWidget = function(widget, insertAtPosition) {
	this.registerWidget(widget, insertAtPosition);

	if (this.el != undefined) {
		if (insertAtPosition == 0) {
			widget.getEl().insertAfter(this.elDropZone);
		} else {
			widget.getEl().insertAfter(this.widgets[insertAtPosition - 1].getEl());
		}
	}

	this.getPageUI().onMoveWidget(widget);
};

/**
 * Remove a widget from the container
 *
 * @param widget
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.removeWidget = function(widget) {
	this.unRegisterWidget(widget);
	widget.remove();

	this.getPageUI().onRemoveWidget(widget);
};

/**
 * Returns all the widget of this container
 *
 * @param recursive
 * @returns {Array}
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.getWidgets = function(recursive) {
	var widgets = [];
	var widgetsLength = this.widgets.length;
	for (var i = 0; i < widgetsLength; i++) {
		widgets.push(this.widgets[i]);
		if (recursive) {
			$.merge(widgets, this.widgets[i].getWidgets(recursive));
		}
	}
	return widgets;
};

/**
 * Moves the widgets of this container to the given container
 *
 * @param widgetContainer
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.moveTo = function(widgetContainer) {
	while (this.widgets.length > 0) {
		this.widgets[0].moveTo(widgetContainer, widgetContainer.widgets.length);
	}
};

/**
 * Removes this container from the DOM
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.remove = function() {
	for (var i = 0; i < this.widgets.length; i++) {
		this.widgets[i].remove();
	}
	this.widgets = [];

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Returns the JSON for all the contained widgets
 *
 * @returns {Array}
 */
Mashup_Page_UI_Layout_WidgetContainer.prototype.getJson = function() {
	var widgets = [];
	for (var i = 0 ; i < this.widgets.length; i++) {
		widgets.push(this.widgets[i].getJson());
	}
	return widgets;
};


/**
 * File: /resources/mashupBuilder/js/Page/UI/Layout/Widget.js
 */
/*
 * Mashup_Page_UI_Layout_Widget.js
 */

/*
 * Constructor
 */
function Mashup_Page_UI_Layout_Widget(parentWidgetContainer, jsonWidget) {
	this.setParentWidgetContainer(parentWidgetContainer);
	this.json = jsonWidget;

	if (this.json.wuid == null || this.json.wuid == '') {
		/* Generate a unique ID of 8 characters */
		this.json.wuid = generateID(8);
	}
	this.widgetDefinition = null; // Will be lazy load in the getWidgetDefinition()

	this.errorsCount = 0;
	this.errorsDOM = [];
}

/**
 * Returns the ID of the widget
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getId = function() {
	return this.json.useWidgetId;
};

/**
 * Returns the WUID of the widget
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getWuid = function() {
	return this.json.wuid;
};

/**
 * Returns whether the widget use or not the parent entry
 */
Mashup_Page_UI_Layout_Widget.prototype.isParentEntryUsed = function() {
	return this.json.useFeeds.useParentEntry;
};

/**
 * Sets whether the widget use or not the parent entry
 */
Mashup_Page_UI_Layout_Widget.prototype.setIsParentEntryUsed = function(isParentEntryUsed) {
	this.json.useFeeds.useParentEntry = isParentEntryUsed;
};

/**
 * Returns whether the widget is enabled or not
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.isEnabled = function() {
	return this.json.enable;
};

/**
 * Sets a list of used feeds and validate them
 */
Mashup_Page_UI_Layout_Widget.prototype.setFeedIds = function(feedIds) {
	if (!$.compareArray(this.json.useFeeds.feedsId, feedIds)) {
		this.json.useFeeds.feedsId = feedIds;
		this.checkUseFeeds();
	}
};

/**
 * Returns a list of used feeds
 */
Mashup_Page_UI_Layout_Widget.prototype.getFeedIds = function() {
	return this.json.useFeeds.feedsId;
};

/**
 * Returns whether the widget has feeds configured or not
 */
Mashup_Page_UI_Layout_Widget.prototype.hasFeeds = function() {
	return this.json.useFeeds.feedsId.length > 0;
};

/**
 * Returns whether the widget has a valid definition or not
 */
Mashup_Page_UI_Layout_Widget.prototype.hasDefinition = function() {
	return this.getWidgetDefinition().id != undefined;
};

/**
 * Returns whether this widget is supported by its parent widget
 */
Mashup_Page_UI_Layout_Widget.prototype.isSupported = function() {
	if ((parentWidget = this.getParentWidget()) != null) {
		return parentWidget.canSupportWidget(this.getId());
	}
	return true;
};

/**
 * Returns whether this widget has configuration sets or not
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.hasConfigurationSets = function() {
	return this.getWidgetDefinition().defaultConfigurationSets.length > 1;
};

/**
 * Returns whether the widget has subwidgets or not
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.hasWidgets = function() {
	return this.getSubWidgetContainer().getWidgets(false).length > 0;
};

/**
 * Sets whether the widget use or not the parent entry
 */
Mashup_Page_UI_Layout_Widget.prototype.setIsParentEntryUsed = function(isParentEntryUsed) {
	this.json.useFeeds.useParentEntry = isParentEntryUsed;
};

/**
 * Returns whether the widget can have subwidgets
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.canHaveWidgets = function() {
	return this.getWidgetDefinition().supportWidgetsId.arity != 'ZERO';
};

/**
 * Returns whether the widget can not have subwidgets
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.canHaveNoWidgets = function() {
	var arity = this.getWidgetDefinition().supportWidgetsId.arity;
	return arity != 'ONE' && arity != 'MANY';
};

/**
 * Returns whether the widget can many subwidgets
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.canHaveManyWidgets = function() {
	var arity = this.getWidgetDefinition().supportWidgetsId.arity;
	return arity == 'ZERO_OR_MANY' || arity == 'MANY';
};

/**
 * Returns whether the widget can have subfeeds
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.canHaveFeeds = function() {
	return (this.getWidgetDefinition().supportFeedsId.arity != 'ZERO');
};

/**
 * Returns whether the widget can not have subfeeds
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.canHaveNoFeeds = function() {
	var arity = this.getWidgetDefinition().supportFeedsId.arity;
	return arity != 'ONE' && arity != 'MANY';
};

/**
 * Returns whether this trigger is supported or not
 */
Mashup_Page_UI_Layout_Widget.prototype.canSupportTrigger = function(className) {
	var definition = getTriggerDefinition(className);
	return definition && (definition.type == TRIGGER_TYPES.MashupWidgetTrigger || definition.type == TRIGGER_TYPES.ParameterAdapterWidgetTrigger);
};

/**
 * Returns the supported widgets or NULL if supports all
 *
 * @returns {Array}
 */
Mashup_Page_UI_Layout_Widget.prototype.getSupportedWidgets = function() {
	var supportedWidgets = this.getWidgetDefinition().supportWidgetsId.widgetsId;
	if (supportedWidgets.length > 0) {
		return supportedWidgets;
	}
	return null;
};

/**
 * Returns whether the widget can support the given widget ID
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.canSupportWidget = function(widgetId) {
	if (this.canHaveWidgets()) {
		var supportedWidgets = this.getWidgetDefinition().supportWidgetsId.widgetsId;
		return supportedWidgets.length == 0 || supportedWidgets.indexOf(widgetId) != -1;
	}
	return false;
};

/**
 * Returns whether the widget consume its feeds or not
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.isFeedConsumed = function() {
	return (this.getWidgetDefinition().supportFeedsId.consumeFeed == true);
};

/**
 * Returns whether this widget is valid or not
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.isValid = function() {
	return this.hasDefinition() && this.isSupported();
};

/**
 * Returns whether this widget is of type composite or not
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.isComposite = function() {
	return this.getWidgetDefinition().compositeWidget;
};

/**
 * Returns whether this widget is of type layout or not
 */
Mashup_Page_UI_Layout_Widget.prototype.isLayout = function() {
	return this.getWidgetDefinition().supportWidgetsId.displayType == 'LAYOUT';
};

/**
 * Returns whether this widget is readonly or not
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget.prototype.isReadOnly = function() {
	return this.getPageUI().isReadOnly();
};

/**
 * Returns the widget definition or a dummy one if unknown
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getWidgetDefinition = function() {
	if (this.widgetDefinition == null) {
		this.widgetDefinition = getWidgetDefinition(this.json.useWidgetId);
		if (this.widgetDefinition == null) {
			this.widgetDefinition = clone(defaultJsonWidgetDefinition);
			this.widgetDefinition.name = this.json.useWidgetId;
			if (this.hasWidgets()) {
				this.widgetDefinition.supportWidgetsId.arity = PARAMETER_ARITY.ZERO_OR_MANY;
			}
			if (this.hasFeeds()) {
				this.widgetDefinition.supportFeedsId.arity = PARAMETER_ARITY.ZERO_OR_MANY;
			}
		}
	}
	return this.widgetDefinition;
};

/**
 * Returns the widget container of this widget
 *
 * @returns {Mashup_Page_UI_Layout_WidgetContainer}
 */
Mashup_Page_UI_Layout_Widget.prototype.getSubWidgetContainer = function() {
	if (this.subWidgetContainer == undefined) {
		var json = this.json.widgets;
		if (json == null) {
			json = [];
		}
		this.subWidgetContainer = new Mashup_Page_UI_Layout_WidgetContainer(this, json);
	}
	return this.subWidgetContainer;
};

/**
 * Returns the trigger container of this widget
 *
 * @returns {Mashup_Page_Layout_TriggerContainer}
 */
Mashup_Page_UI_Layout_Widget.prototype.getTriggerContainer = function() {
	if (this.triggerContainer == undefined) {
		this.triggerContainer = new Mashup_Page_Layout_TriggerContainer('designTrigger', this, this.json.triggers);
	}
	return this.triggerContainer;
};

/**
 * Returns the parent widget or null if top widget
 *
 * @returns {Mashup_Page_UI_Layout_Widget}
 */
Mashup_Page_UI_Layout_Widget.prototype.getParentWidget = function() {
	return this.getParentWidgetContainer().getParentWidget();
};

/**
 * Returns the first parent widget class which have feeds and consume them
 *
 * If none found, it will return null
 */
Mashup_Page_UI_Layout_Widget.prototype.getParentWidgetWithFeeds = function() {
	var parentWidget = this.getParentWidget();
	while (parentWidget != null) {
		if (parentWidget.canHaveFeeds() && parentWidget.isFeedConsumed()) {
			return parentWidget;
		}
		parentWidget = parentWidget.getParentWidget();
	}
	return null;
};

/**
 * Returns the widget container that contains this widget
 *
 * @returns {Mashup_Page_UI_Layout_WidgetContainer}
 */
Mashup_Page_UI_Layout_Widget.prototype.getParentWidgetContainer = function() {
	return this.parentWidgetContainer;
};

/**
 * Set the parent widget
 * @param the widget container that contains this widget
 */
Mashup_Page_UI_Layout_Widget.prototype.setParentWidgetContainer = function(widgetContainer) {
	this.parentWidgetContainer = widgetContainer;
};

/**
 * Returns the cell that contains this widget
 *
 * @returns {Mashup_Page_UI_Layout_Cell}
 */
Mashup_Page_UI_Layout_Widget.prototype.getParentCell = function() {
	return this.getParentWidgetContainer().getParentCell();
};

/**
 * Returns the Mashup UI for this widget
 *
 * @returns {Mashup_Page_UI}
 */
Mashup_Page_UI_Layout_Widget.prototype.getPageUI = function() {
	return this.getParentWidgetContainer().getPageUI();
};

/**
 * Returns all the widgets contained by this widget
 *
 * @param recursive
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getWidgets = function(recursive) {
	return this.getSubWidgetContainer().getWidgets(recursive);
};

/**
 * Returns all the triggers contained by this widget
 * 
 * @param recursive
 */
Mashup_Page_UI_Layout_Widget.prototype.getTriggers = function(recursive) {
	if (!recursive) {
		return this.getTriggerContainer().getTriggers();
	} else {
		var widgets = this.getWidgets(true);
		var triggers = [];
		$.merge(triggers, this.getTriggers(false));
		for (var i = 0; i < widgets.length; i++) {
			$.merge(triggers, widgets[i].getTriggers(false));
		}
		return triggers;
	}
};

/**
 * Returns the name of the widget
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getWidgetName = function() {
	if (this.json.widgetTitle != null && this.json.widgetTitle != '') {
		return this.json.widgetTitle.escapeHTML() + ' - ' + this.getWidgetDefinition().name;
	} else {
		return this.getWidgetDefinition().name;
	}
};

/**
 * Sets the widget in the given view mode
 *
 * @param viewMode
 */
Mashup_Page_UI_Layout_Widget.prototype.setViewMode = function(viewMode) {
	switch (viewMode) {
	case 'compact':
		this.doSlideWidget(false);
		this.doSlideUseFeeds(true);
		this.doSlideTriggers(true);
		this.doSlideSubWidgets(false);
		this.getElPreview().hide();
		break;

	case 'extended':
		this.doSlideWidget(false);
		this.doSlideUseFeeds(false);
		this.doSlideTriggers(false);
		this.doSlideSubWidgets(false);
		this.getElPreview().show();
		break;

	case 'custom':
		this.doSlideWidget(this.state('w'));
		this.doSlideUseFeeds(this.state('f'));
		this.doSlideTriggers(this.state('t'));
		this.doSlideSubWidgets(this.state('sw'));
		this.getElPreview().show();
		break;

	case 'edit':
		this.doSlideWidget(true);
		this.getElPreview().show();
		break;
	}

	this.viewMode = viewMode;
};

/**
 * Called when a trigger has been created
 */
Mashup_Page_UI_Layout_Widget.prototype.onCreateTrigger = function(trigger) {
	if (this.getPageUI().getPage().isOpen()) {
		// update the trigger count
		this.onUpdateTriggerDisplay();

		// validate trigger
		Mashup_Config_Validator.checkTrigger(trigger);
	}

	// propagate call
	this.onUpdate();
};

/**
 * Called when a trigger has been moved
 */
Mashup_Page_UI_Layout_Widget.prototype.onMoveTrigger = function(trigger) {
	if (this.getPageUI().getPage().isOpen()) {
		// update the trigger count
		this.onUpdateTriggerDisplay(trigger);
	}

	// propagate call
	this.onUpdate();
};

/**
 * Called when a trigger has been removed
 */
Mashup_Page_UI_Layout_Widget.prototype.onRemoveTrigger = function(trigger) {
	if (this.getPageUI().getPage().isOpen()) {
		// update the trigger count
		this.onUpdateTriggerDisplay(trigger);
	}

	// propagate call
	this.onUpdate();
};

/**
 * Called when a trigger configuration has been updated
 */
Mashup_Page_UI_Layout_Widget.prototype.onUpdateTrigger = function(trigger) {
	this.onUpdate();
};

/**
 * Called when the widget is initialized
 */
Mashup_Page_UI_Layout_Widget.prototype.onWidgetInit = function() {
};

/**
 * Called when the widget has been updated
 */
Mashup_Page_UI_Layout_Widget.prototype.onUpdate = function(c) {
	return this.getPageUI().onUpdate();
};

/*
 * Events
 */
Mashup_Page_UI_Layout_Widget.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'editWidget':
			// user cannot manually trigger doEditWidget although we need
			// to do it pragmatically for validation
			if (this.viewMode != 'edit') {
				this.doEditWidget();
			}
			return false;

		case 'doEditLayout':
			this.doEditLayout();
			return false;

		case 'doAnnotateWidget':
			this.doAnnotateWidget();
			return false;

		case 'editCompositeWidget':
			loadWidgetComposite(this.getId());
			return false;

		case 'doCreateComposite':
			window.mashupBuilder.doNewCompositeWidget(this);
			return false;

		case 'doToggleStatus':
			if (this.isEnabled()) {
				this.disable();
			} else {
				this.enable();
			}
			this.getPageUI().onUpdate();
			return false;

		case 'doSlideWidget':
			this.doSlideWidget(null, function(isClosed) {
				this.state('w', isClosed ? 1 : 0);
			});
			return false;

		case 'doSlideUseFeeds':
			this.doSlideUseFeeds(null, function(isClosed) {
				this.state('f', isClosed ? 1 : 0);
			});
			return false;

		case 'doSlideTriggers':
			this.doSlideTriggers(null, function(isClosed) {
				this.state('t', isClosed ? 1 : 0);
			});
			return false;

		case 'doSlideSubWidgets':
			this.doSlideSubWidgets(null, function(isClosed) {
				this.state('sw', isClosed ? 1 : 0);
			});
			return false;

		case 'doOpenDefaultConfigurationSets':
			this.doOpenDefaultConfigurationSets();
			return false;

		case 'doHideDefaultConfigurationSets':
			this.doHideDefaultConfigurationSets();
			return false;

		case 'doApplyDefaultConfigurationSets':
			this.doApplyDefaultConfigurationSets(target.attr('data-idx'));
			return false;

		case 'doCopyWidget':
			this.doCopyWidget();
			return false;

		case 'doRemoveWidget':
			this.doRemoveWidget();
			return false;

		case 'feedName':
			var feedIds = (target.attr('type') == 'radio') ? [target.val()] : this.getElUseFeeds().find('input:checked').map(function() { return $(this).val(); });
			this.doEditUseFeeds(feedIds);
			return false;
		}
	}

	return true;
};

/**
 * User process to edit the used feeds
 */
Mashup_Page_UI_Layout_Widget.prototype.doEditUseFeeds = function(feedIds) {
	// update the usefeeds JSON
	this.setIsParentEntryUsed(false);
	var newFeedIds = [];
	for (var i = 0; i < feedIds.length; i++) {
		// FIXME: should not use startsWith because of i18n
		if (feedIds[i].startsWith('entry of ')) {
			this.setIsParentEntryUsed(true);
		} else if (feedIds[i] != '') {
			newFeedIds.push(feedIds[i]);
		}
	}
	this.setFeedIds(newFeedIds);

	// redraws the available feeds of subwidgets
	var widgets = this.getPageUI().getWidgets(true);
	for (var i = 0; i < widgets.length; i++) {
		widgets[i].redrawElUseFeeds();
	}

	// revalidate my selected feed
	this.checkUseFeeds();

	// propagate the update event
	this.getPageUI().onUpdate();
};

/**
 * User process to delete this widget
 */
Mashup_Page_UI_Layout_Widget.prototype.doRemoveWidget = function() {
	var _this = this;
	new Mashup_Popup_Confirm({
		title: _.WIDGET_DELETE_TITLE(),
		text: _.WIDGET_DELETE(),
		buttonOkLabel: _.YES(),
		buttonCancelLabel: _.NO(),
		onClickOkCallback: function(context) {
			_this.getParentWidgetContainer().removeWidget(_this);
			this.remove();
			return false;
		}
	}).show();
};

/**
 * User process to copy this widget
 */
Mashup_Page_UI_Layout_Widget.prototype.doCopyWidget = function() {
	if (this.errorsCount > 0) {
		new Mashup_Popup_Error(this.errorsCount).show();
		return;
	}

	var confirmBox = new Mashup_Popup_Confirm({
		title: _.WIDGET_CONFIRM_COPY_TITLE(),
		text: '',
		onClickOkCallbackData: { _this: this },
		onClickOkCallback: function(context) {
			context.data._this.copyTo(this.getElContent().find('select[name=pageName]').val());
			this.remove();
		}
	});

	confirmBox.focus = function() {
		this.getElContent().find('input:first').focus();
	};

	var sb = new StringBuilder();
	if (window.widgetBuilder == null) {
		sb.append('<option value="' + this.getPageUI().getPageName() + '">' + _.WIDGET_CONFIRM_COPY_THIS_PAGE() + '</option>');
		var pages = mashupBuilder.getPages();
		for (var i = 0; i < pages.length; i++) {
			if (pages[i].isReadOnly() == false) {
				sb.append('<option value="' + pages[i].getName() + '">' + pages[i].getName() + '</option>');
			}
		}
	} else {
		sb.append('<option value="' + this.getPageUI().getPageName() +'">' + _.WIDGET_CONFIRM_COPY_THIS_COMPONENT() + '</option>');
	}

	confirmBox.getElContent().prepend('' +
		'<div class="popup-form-wrapper">' +
			'<p class="popup-form-input-wrapper">' +
				'<label class="popup-form-label" for="pageName">' + _.WIDGET_CONFIRM_COPY() + '</label>' +
				'<select name="pageName">' +
					sb.toString() +
				'</select>' +
			'</p>' +
		'</div>'
	);
	confirmBox.show();
};

/**
 * Handles the show/hide of the triggers
 *
 * @param shouldBeClosed
 * @param onEndCallback
 */
Mashup_Page_UI_Layout_Widget.prototype.doSlideTriggers = function(shouldBeClosed, onEndCallback) {
	var isClosed = this.getElTriggers().hasClass('widget-triggers-closed');
	if (shouldBeClosed == null || isClosed != shouldBeClosed) {

		if (shouldBeClosed == null) {
			// toggle it
			if (isClosed) {
				this.getElTriggers().find('.widget-triggers-list').hide();
			}

			var _this = this;
			this.getElTriggers().find('.widget-triggers-edit').slideToggle('fast', function() {
				if (isClosed) {
					_this.getElTriggers().removeClass('widget-triggers-closed');
				} else {
					_this.getElTriggers().find('.widget-triggers-list').show();
					_this.getElTriggers().addClass('widget-triggers-closed');
				}

				if (onEndCallback != null) {
					onEndCallback.call(_this, !isClosed);
				}
			});

		} else {
			// force show/hide
			if (shouldBeClosed) {
				this.getElTriggers().find('.widget-triggers-edit').hide();
				this.getElTriggers().find('.widget-triggers-list').show();
				this.getElTriggers().addClass('widget-triggers-closed');
			} else {
				this.getElTriggers().find('.widget-triggers-edit').show();
				this.getElTriggers().find('.widget-triggers-list').hide();
				this.getElTriggers().removeClass('widget-triggers-closed');
			}
			if (onEndCallback != null) {
				onEndCallback.call(this, !isClosed);
			}
		}
	}
};

/**
 * Handles the show/hide of the widget
 *
 * @param shouldBeClosed
 * @param onEndCallback
 */
Mashup_Page_UI_Layout_Widget.prototype.doSlideWidget = function(shouldBeClosed, onEndCallback) {
	var isClosed = this.getEl().hasClass('widget-closed');
	if (shouldBeClosed == null || isClosed != shouldBeClosed) {
		if (isClosed == false) {
			this.getEl().addClass('widget-closed');
		} else {
			this.getEl().removeClass('widget-closed');
		}

		if (shouldBeClosed == null) {
			// toggle it
			var _this = this;
			this.getElContent().slideToggle('fast', function() {
				if (onEndCallback != null) {
					onEndCallback.call(_this, !isClosed);
				}
			});
		} else {
			// force show/hide
			if (shouldBeClosed) {
				this.getElContent().hide();
			} else {
				this.getElContent().show();
			}
			if (onEndCallback != null) {
				onEndCallback.call(this, !isClosed);
			}
		}
	}
};

/**
 * Handles the show/hide of the subwidgets
 *
 * @param shouldBeClosed
 * @param onEndCallback
 */
Mashup_Page_UI_Layout_Widget.prototype.doSlideSubWidgets = function(shouldBeClosed, onEndCallback) {
	var isClosed = this.getElSubWidgets().hasClass('widget-subwidgets-closed');
	if (shouldBeClosed == null || isClosed != shouldBeClosed) {

		if (shouldBeClosed == null) {
			// toggle it
			if (isClosed) {
				this.getElSubWidgets().find('> .widget-subwidgets-list').hide();
			}

			var _this = this;
			this.getElSubWidgets().find('> .widget-subwidgets-edit').slideToggle('fast', function() {
				if (isClosed) {
					_this.getElSubWidgets().removeClass('widget-subwidgets-closed');
				} else {
					_this.getElSubWidgets().find('> .widget-subwidgets-list').show();
					_this.getElSubWidgets().addClass('widget-subwidgets-closed');
				}

				if (onEndCallback != null) {
					onEndCallback.call(_this, !isClosed);
				}
			});

		} else {
			// force show/hide
			if (shouldBeClosed) {
				this.getElSubWidgets().find('> .widget-subwidgets-edit').hide();
				this.getElSubWidgets().find('> .widget-subwidgets-list').show();
				this.getElSubWidgets().addClass('widget-subwidgets-closed');
			} else {
				this.getElSubWidgets().find('> .widget-subwidgets-edit').show();
				this.getElSubWidgets().find('> .widget-subwidgets-list').hide();
				this.getElSubWidgets().removeClass('widget-subwidgets-closed');
			}
			if (onEndCallback != null) {
				onEndCallback.call(this, !isClosed);
			}
		}
	}
};

/**
 * Handles the show/hide of the feeds
 *
 * @param shouldBeClosed
 * @param onEndCallback
 */
Mashup_Page_UI_Layout_Widget.prototype.doSlideUseFeeds = function(shouldBeClosed, onEndCallback) {
	var isClosed = this.getElUseFeeds().hasClass('widget-feeds-closed');
	if (shouldBeClosed == null || isClosed != shouldBeClosed) {

		if (shouldBeClosed == null) {
			// toggle it
			if (isClosed) {
				this.getElUseFeeds().find('.widget-feeds-list').hide();
			}

			var _this = this;
			this.getElUseFeeds().find('.widget-feeds-edit').slideToggle('fast', function() {
				if (isClosed) {
					_this.getElUseFeeds().removeClass('widget-feeds-closed');
				} else {
					_this.getElUseFeeds().find('.widget-feeds-list').show();
					_this.getElUseFeeds().addClass('widget-feeds-closed');
				}

				if (onEndCallback != null) {
					onEndCallback.call(_this, !isClosed);
				}
			});

		} else {
			// force show/hide
			if (shouldBeClosed) {
				this.getElUseFeeds().find('.widget-feeds-edit').hide();
				this.getElUseFeeds().find('.widget-feeds-list').show();
				this.getElUseFeeds().addClass('widget-feeds-closed');
			} else {
				this.getElUseFeeds().find('.widget-feeds-edit').show();
				this.getElUseFeeds().find('.widget-feeds-list').hide();
				this.getElUseFeeds().removeClass('widget-feeds-closed');
			}
			if (onEndCallback != null) {
				onEndCallback.call(this, !isClosed);
			}
		}
	}
};

/**
 * Returns whether the widget as errors in its DOM (missing feeds, widgets, etc..)
 */
Mashup_Page_UI_Layout_Widget.prototype.hasErrorsDOM = function() {
	return this.errorsDOM.length > 0;
};

/**
 * Updates the error count
 *
 * @param {number}
 *			errorsCount Can be positive (more errors) or negative (less errors)
 */
Mashup_Page_UI_Layout_Widget.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;
	if (this.errorsCount == 0) {
		this.getEl().removeClass('has-error');
	} else {
		this.getEl().addClass('has-error');
	}
	this.getPageUI().updateError(errorsCount);
};

Mashup_Page_UI_Layout_Widget.prototype.addError = function($element) {
	if (this.errorsDOM.indexOf($element[0]) == -1) {
		$element.addClass('error');
		this.errorsDOM.push($element[0]);
		this.updateError(1);
	}
};

Mashup_Page_UI_Layout_Widget.prototype.removeError = function($element) {
	if ((index = this.errorsDOM.indexOf($element[0])) != -1) {
		$element.removeClass('error');
		this.errorsDOM.splice(index, 1);
		this.updateError(-1);
	}
};

/**
 * Clears all the errors of this widget
 */
Mashup_Page_UI_Layout_Widget.prototype.clearError = function() {
	// clear subwidgets errors
	var subwidgets = this.getWidgets(false);
	for (var i = 0; i < subwidgets.length; i++) {
		subwidgets[i].clearError();
	}

	// clear DOM errors
	while (this.errorsDOM.length > 0) {
		this.removeError($(this.errorsDOM[0]));
	}

	// clear errors from properties
	if (this.bottomToolbar != undefined) {
		window.mashupBuilder.removeBottomToolbar(this.bottomToolbar, true /* force remove */);
		this.bottomToolbar = undefined;
	}
};

/**
 * Updates the widget menu HTML according to its actual state
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.updateMenuHTML = function() {
	var sb = new StringBuilder();

	sb.append('<li name="editWidget" class="editWidget popup-menu-item icon-properties"><span class="icon icon-menu"></span>' + _.WIDGET_MENU_EDIT_PROPERTIES() + ' <span class="icon icon-error" name="editWidget"></span></li>');

	// adds the "edit layout" if available
	if (this.isLayout()) {
		if (this.getLayout().isEditModeEnabled()) {
			sb.append('<li name="doEditLayout" class="popup-menu-item icon-edit"><span class="icon icon-menu"></span>' + _.WIDGET_MENU_EDIT_LAYOUT_CLOSE() +  '</li>');
		} else {
			sb.append('<li name="doEditLayout" class="popup-menu-item icon-edit"><span class="icon icon-menu"></span>' + _.WIDGET_MENU_EDIT_LAYOUT() +  '</li>');
		}
	}

	if (!this.isReadOnly()) {
		sb.append('<li name="doAnnotateWidget" class="doAnnotateWidget popup-menu-item icon-annotate"><span class="icon icon-menu"></span>' + _.WIDGET_MENU_ANNOTATE_TITLE() + '</li>');
	}

	// adds the "edit composite" if available
	if (this.isComposite() && window.mashupBuilder.permission.canViewWidgetBuilder()) {
		sb.append('<li name="editCompositeWidget" class="popup-menu-item icon-edit"><span class="icon icon-menu"></span>' + _.WIDGET_MENU_EDIT_COMPOSITE() +  '</li>');
	}

	// adds the configuration sets if several available
	if (this.hasConfigurationSets()) {
		sb.append('<li name="doOpenDefaultConfigurationSets" class="popup-menu-item icon-confsets"><span class="icon icon-menu"></span>' + _.FEED_MENU_DCS() + '</li>');
	}

	if (!this.isReadOnly()) {
		if (this.isEnabled()) {
			sb.append('<li name="doToggleStatus" class="popup-menu-item icon-visibility"><span class="icon icon-menu"></span>' + _.WIDGET_MENU_DISABLE() +  '</li>');
		} else {
			sb.append('<li name="doToggleStatus" class="popup-menu-item icon-visibility"><span class="icon icon-menu"></span>' + _.WIDGET_MENU_ENABLE() +  '</li>');
		}
		sb.append('<li name="doCopyWidget" class="popup-menu-item icon-copy"><span class="icon icon-menu"></span>' + _.WIDGET_MENU_COPYTO() +  '</li>');
		if (window.mashupBuilder.permission.canViewWidgetBuilder() && window.widgetBuilder == null) {
			sb.append('<li name="doCreateComposite" class="popup-menu-item icon-create"><span class="icon icon-menu"></span>' + _.WIDGET_MENU_CREATE_COMPOSITE() + '</li>');
		}
		sb.append('<li name="doRemoveWidget" class="popup-menu-item icon-delete"><span class="icon icon-menu"></span>' + _.WIDGET_MENU_DELETE() +  '</li>');
	}

	this.getElHeader().find('.menu-widget').html(sb.toString());
};

/**
 * Moves the current widget to the given container
 *
 * @param widgetContainer
 * @param position
 */
Mashup_Page_UI_Layout_Widget.prototype.moveTo = function(widgetContainer, position) {
	if (this.getParentWidgetContainer() != widgetContainer || position != widgetContainer.indexOf(this)) {
		var previousWidgetContainer = this.getParentWidgetContainer();
		this.getParentWidgetContainer().unRegisterWidget(this);
		widgetContainer.addWidget(this, position);

		// validate previous parent widget container
		if (previousWidgetContainer.getParentWidget() != null) {
			previousWidgetContainer.getParentWidget().checkUseWidgets();
			previousWidgetContainer.getParentWidget().onUpdateSubWidgets();
		}
	}
};

/**
 * Copy the current widget to the given page
 *
 * @param pageName
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.copyTo = function(pageName) {
	var parentPageName = this.getPageUI().getPageName();
	var newWidgetClass = null;

	// force saving of opened toolbar
	window.mashupBuilder.removeOpenedBottomToolbar();

	// copy the widget JSON
	var jsonWidgetAsString = $.toJSON(clone(this.getJson()));
	jsonWidgetAsString = removeWuids(jsonWidgetAsString);
	var jsonWidget = $.evalJSON(jsonWidgetAsString);

	// adds the widget to the target page
	if (parentPageName == pageName) {
		// adds after the copied widget
		this.getParentWidgetContainer().addNewWidget(jsonWidget, this.getParentWidgetContainer().indexOf(this) + 1);
	} else {
		// adds in the first cell
		window.mashupBuilder.getPageClass(pageName).getUI().getRows()[0].getCells()[0].getSubWidgetContainer().addNewWidget(jsonWidget, 0);
	}

	return newWidgetClass;
};

/*
 * Dom Manipulation
 */
Mashup_Page_UI_Layout_Widget.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
		'<form class="widget-wrapper widget-droppable widget-draggable">' +
			'<div class="drop-zone hidden-drop-zone widget-drop-zone">' +
				_.WIDGET_DROP_WIDGETS_HERE(this.getParentWidget()) +
			'</div>' +
		'</form>');
		this.el.data('_this', this);

		// appends the widget to the wrapper
		this.el.prepend(this.getElWidget());

		// init folded widget
		if (this.state('w') == true) {
			this.getEl().addClass('widget-closed');
			this.getElContent().hide();
			this.getElHeader().find('.widget-fold-handle').switchClass('icon-bottom', 'icon-up');
		}

		// is the widget valid?
		if (!this.hasDefinition()) {
			this.disable(_.WIDGET_ERROR_UNKNOWN_ID(this.getId()));
		} else if (!this.isSupported()) {
			this.disable(_.WIDGET_SUBWIDGET_NOT_COMPATIBLE());
		} else if (!this.isEnabled()) {
			this.disable();
		}

		// attach events
		this.el.on('click', $.proxy(this.handleEventOnClick, this));
		this.el.on('submit', false);

		this.attachDraggable();
		this.updateMenuHTML();

		this.onWidgetInit();
	}
	return this.el;
};

/**
 * User process to opens the default configuration sets
 */
Mashup_Page_UI_Layout_Widget.prototype.doOpenDefaultConfigurationSets = function() {
	if (this.dcsEl == null) {
		var dcs = getConfigurationSets(this.getWidgetDefinition()), dcsLength = dcs.length;
		if (dcsLength > 1) {
			var html = '';
			for (var i = 0; i < dcsLength; i++) {
				html += '<li class="configuration-sets-item" data-idx="' + i + '" style="' +  ((dcs[i].previewUrl != null && dcs[i].previewUrl != '') ? 'background-image:url(' + dcs[i].previewUrl + ')' : '') + '" name="doApplyDefaultConfigurationSets">' +
							(dcs[i].displayName || 'Default') +
						'</li>';
			}
			this.dcsEl = $('' +
				'<div class="configuration-sets-wrapper">' +
					'<span class="icon icon-close" name="doHideDefaultConfigurationSets"></span>' +
					'<ul class="configuration-sets-list">' +
						html +
					'</ul>' +
				'</div>');
		}
	}
	if (this.dcsEl != null) {
		this.dcsEl.appendTo(this.getElContent());
	}
};

/**
 * User process to a pply a new configuration set
 *
 * @param idx
 */
Mashup_Page_UI_Layout_Widget.prototype.doApplyDefaultConfigurationSets = function(idx) {
	this.doHideDefaultConfigurationSets();
	var configurationSet = getConfigurationSets(this.getWidgetDefinition())[idx];
	if (configurationSet != undefined) {
		window.mashupBuilder.removeOpenedBottomToolbar(true);
		this.json.parameters = getMergedParameters(this.json.parameters, getConfigurationSetAsParameters(configurationSet));
		this.doEditWidget(true);
	}
};

/**
 * User process to hide the default configuration sets
 */
Mashup_Page_UI_Layout_Widget.prototype.doHideDefaultConfigurationSets = function() {
	if (this.dcsEl != null) {
		this.dcsEl.remove();
		this.dcsEl = null;
	}
};

/**
 * User process to edit widget layout
 */
Mashup_Page_UI_Layout_Widget.prototype.doEditLayout = function() {
	// unimplemented
};

/**
 * User process to annotate the widget
 */
Mashup_Page_UI_Layout_Widget.prototype.doAnnotateWidget = function() {
	window.mashupBuilder.removeOpenedBottomToolbar();
	var _this = this;
	var promptBox = new Mashup_Popup_Prompt({
		title: _.WIDGET_TOOLBAR_ANNOTATE_TITLE(),
		value: (this.json.widgetTitle == null ? '' : this.json.widgetTitle),
		label: _.WIDGET_TOOLBAR_ANNOTATE_LABEL(),
		onClickOkCallback: function(context, newWidgetName) {
			_this.setWidgetName(newWidgetName);
			_this.getPageUI().onUpdate();
			this.remove();
		}
	});
	promptBox.show();
};

/**
 * User process to edit the widget properties
 */
Mashup_Page_UI_Layout_Widget.prototype.doEditWidget = function(clearCache) {
	if (clearCache === true && this.bottomToolbar != undefined) {
		this.bottomToolbar.remove();
		this.bottomToolbar = undefined;
	}

	if (this.bottomToolbar == undefined) {
		// create our content
		var editProperties = new Mashup_Window_TabContent_EditProperties(this, {
			isReadOnly: this.getPageUI().isReadOnly(),
			$errorClassEl: this.getElHeader().find('.icon-properties'),
			errorClassName: 'errorOnEditWidget',
			$errorClassElCount: this.getElHeader().find('.editWidget .icon-error')
		}, Mashup_Parameter_Factory.createWidgetParameters(this.getId()));

		// inject preview & information tab
		if (window.widgetBuilder == null) {
			editProperties.addTab(new Mashup_Window_TabContent_Tab_WidgetPreview(this));
		}
		editProperties.addTab(new Mashup_Window_TabContent_Tab_WidgetInfos(this));

		// open it as a bottomToolbar
		this.bottomToolbar = new Mashup_BottomToolbar();
		this.bottomToolbar.setContent(editProperties);
	}

	window.mashupBuilder.openBottomToolbar(this.bottomToolbar);
};

Mashup_Page_UI_Layout_Widget.prototype.setWidgetName = function(newWidgetName) {
	if (newWidgetName == '') {
		this.json.widgetTitle = null;
	} else {
		this.json.widgetTitle = newWidgetName;
	}
	this.getElHeader().find('.widget-name').html(this.getWidgetName());
};

/**
 * Returns the DOM node for this widget component
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getElWidget = function() {
	if (this.elWidget == undefined) {
		this.elWidget = $('<div class="widget"></div>');
		this.elWidget.append(this.getElHeader());
		this.elWidget.append(this.getElContent());
	}
	return this.elWidget;
};

/**
 * Returns the DOM node for this widget header
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getElHeader = function() {
	if (this.elHeader == undefined) {
		this.elHeader = $('' +
		'<div class="widget-header" name="editWidget">' +
			'<table class="widget-header-table">' +
				'<tbody>' +
					'<tr>' +
						'<td class="widget-fold-handle"><span class="icon" name="doSlideWidget"></span></td>' +
						'<td><div class="widget-name" name="editWidget" title="' + this.getWidgetName() + '">' + this.getWidgetName() + '</div></td>' +
						'<td class="widget-header-icons">' +
							(!this.isReadOnly() ?
								'<span name="doAnnotateWidget" class="icon icon-annotate" title="' + _.WIDGET_ANNOTATE() + '"></span>' : ''
							) +
							'<span class="has-popup-menu icon icon-properties" title="' + _.WIDGET_PROPERTIES() + '"><ul class="menu-widget popup-menu popup-menu-right has-popup-icons"></ul></span>' +
							'<span class="icon widget-move-handle" title="' + _.WIDGET_MOVE() + '"></span>' +
						'</td>' +
					'</tr>' +
				'</tbody>' +
			'</table>' +
		'</div>');
	}
	return this.elHeader;
};

/**
 * Returns the DOM node for this widget content
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getElContent = function() {
	if (this.elContent == undefined) {
		this.elContent = $('<div class="widget-content"></div>');
		this.elContent.append(this.getElPreview());
		this.elContent.append(this.getElUseFeeds());
		this.elContent.append(this.getElTriggers());
		this.elContent.append(this.getElSubWidgets());
	}
	return this.elContent;
};

/**
 * Returns the DOM node for the preview
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getElPreview = function() {
	if (this.elPreview == undefined) {
		if (((preview = this.getWidgetDefinition().preview) != null) && preview.length > 0) {
			this.elPreview = $('' +
				'<div class="widget-preview">' +
					'<div class="widget-preview-overflow">' +
						preview +
					'</div>' +
				'</div>'
			);
		} else {
			this.elPreview = $();
		}
	}
	return this.elPreview;
};

/**
 * Returns the DOM node for the feeds container
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getElUseFeeds = function() {
	if (this.elUseFeeds == undefined) {
		if (this.canHaveFeeds()) {
			var feedsLabel = this.getWidgetDefinition().supportFeedsId.label;

			this.elUseFeeds = $('' +
				'<div class="widget-feeds-wrapper">' +
					'<span name="doSlideUseFeeds" class="icon slide-widget-feeds"></span>' +
					'<div class="widget-feeds-list" name="doSlideUseFeeds">' +
						'<ul class="widget-feeds"></ul>' +
					'</div>' +
					'<div class="widget-feeds-edit">' +
						'<span name="doSlideUseFeeds" class="widget-feeds-label"> ' +
							(feedsLabel != null ? feedsLabel : _.WIDGET_AVAILABLE_FEEDS()) + ':' +
						'</span>' +
						'<ul class="widget-feeds"></ul>' +
					'</div>' +
				'</div>');

			this.redrawElUseFeeds();

			if (this.state('f') == true) {
				this.getElUseFeeds().addClass('widget-feeds-closed');
			}

		} else {
			this.elUseFeeds = $();
		}
	}
	return this.elUseFeeds;
};

/**
 * Returns the DOM node for the sub-widgets container
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getElSubWidgets = function() {
	if (this.elSubWidgets == undefined) {
		if (this.canHaveWidgets()) {
			var widgetLabel = this.getWidgetDefinition().supportWidgetsId.label;
			this.elSubWidgets = $('' +
				'<div class="widget-subwidgets-wrapper">' +
					'<span name="doSlideSubWidgets" class="icon slide-widget-subwidgets"></span>' +
					'<div class="widget-subwidgets-list" name="doSlideSubWidgets">' +
					'</div>' +
					'<div class="widget-subwidgets-edit">' +
						'<span name="doSlideSubWidgets" class="widget-subwidgets-label">' +
						(widgetLabel != null ? widgetLabel : _.WIDGET_SUBWIDGETS_LABEL()) + ':' +
						'</span>' +
					'</div>' +
				'</div>');

			this.getSubWidgetContainer().getEl().appendTo(this.getElSubWidgets().find('> .widget-subwidgets-edit'));
			this.onUpdateSubWidgets();

			if (this.state('sw') == true) {
				this.getElSubWidgets().addClass('widget-subwidgets-closed');
			}

		} else {
			this.elSubWidgets = $();
		}
	}
	return this.elSubWidgets;
};

/**
 * Returns the DOM node for the triggers cotnainer
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getElTriggers = function() {
	if (this.elTriggers == undefined) {
		this.elTriggers = $('' +
			'<div class="widget-triggers-wrapper">' +
				'<span name="doSlideTriggers" class="icon slide-widget-triggers"></span>' +
				'<div class="widget-triggers-list" name="doSlideTriggers">' +
				'</div>' +
				'<div class="widget-triggers-edit">' +
					'<span name="doSlideTriggers" class="widget-triggers-label">' +
						_.WIDGET_TRIGGERS_LABEL() + ':' +
					'</span>' +
				'</div>' +
			'</div>'
		);

		this.getTriggerContainer().getEl().appendTo(this.getElTriggers().find('.widget-triggers-edit'));
		this.onUpdateTriggerDisplay();

		if (this.state('t') == true) {
			this.getElTriggers().addClass('widget-triggers-closed');
		}

	}
	return this.elTriggers;
};

/**
 * Sets or returns a state for this widget
 *
 * @param key
 * @param value
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.state = function(key, value) {
	var idx = ['w', 'f', 't', 'sw'].indexOf(key);
	var defaultValue = '0!0!0!0';
	var state = window.mashupBuilder.state.get('w', this.json.wuid, defaultValue).split('!');
	if (value == undefined) {
		// cast 1 as true and 0 as false
		return state[idx] == 1;
	} else {
		state[idx] = value;
		var newState = state.join('!');
		window.mashupBuilder.state.save('w', this.json.wuid, newState == defaultValue ? null : newState);
	}
};

/**
 * Called when subwidget context has changed (created/deleted/moved)
 */
Mashup_Page_UI_Layout_Widget.prototype.onUpdateSubWidgets = function() {
	var nbWidgets = this.getSubWidgetContainer().getWidgets(false).length;
	if (nbWidgets > 0) {
		this.getElSubWidgets().find('> .widget-subwidgets-list').html(_.WIDGET_SUBWIDGETS_LIST(nbWidgets));
		this.getElSubWidgets().removeClass('widget-no-subwidgets');
	} else {
		this.getElSubWidgets().find('> .widget-subwidgets-list').html(_.WIDGET_SUBWIDGETS_LIST_EMPTY());
		this.getElSubWidgets().addClass('widget-no-subwidgets');
	}
};

/**
 * Called when trigger context has changed (created/deleted/moved)
 */
Mashup_Page_UI_Layout_Widget.prototype.onUpdateTriggerDisplay = function() {
	var nbTriggers = this.getTriggerContainer().getTriggers().length;
	if (nbTriggers > 0) {
		this.getElTriggers().find('> .widget-triggers-list').html(_.WIDGET_TRIGGERS_LIST(nbTriggers));
		this.getElTriggers().removeClass('widget-no-triggers');
	} else {
		this.getElTriggers().find('> .widget-triggers-list').html(_.WIDGET_TRIGGERS_LIST_EMPTY());
		this.getElTriggers().addClass('widget-no-triggers');
	}
};

/**
 * Refresh the list of feeds for the widget
 */
Mashup_Page_UI_Layout_Widget.prototype.redrawElUseFeeds = function() {
	if (!this.canHaveFeeds()) {
		return;
	}

	// not drawn yet
	if (this.el == undefined) {
		return;
	}

	var editHtml = new StringBuilder();
	var listHtml = new StringBuilder();

	// retrieve available feeds for the widget
	var availableFeeds = this.getAvailableFeeds();

	// The widget cannot have parent entry as source
	if (availableFeeds.parentFeed == null) {
		this.setIsParentEntryUsed(false);
	}

	if (availableFeeds.feeds.length == 0 && availableFeeds.parentFeed == null) {
		editHtml.append('<li class="widget-feed no-feed">' + _.WIDGET_NO_FEED_AVAILABLE() + '</li>');
		listHtml.append('<li class="widget-feed" name="doSlideUseFeeds">' + _.WIDGET_NO_FEED_AVAILABLE() + '</li>');

		// no feeds, empty the use feeds of the widget
		this.setFeedIds([]);

	} else {
		var feedArity = this.getWidgetDefinition().supportFeedsId.arity;
		var isDisabled = this.isReadOnly();

		// in case of ZERO_OR_ONE (radio button). Adds a 'NONE' feed.
		if (feedArity == 'ZERO_OR_ONE') {
			this.appendUseFeed(editHtml, {
				isChecked: !this.hasFeeds(),
				displayFeedName: _.WIDGET_FEED_NONE(),
				arity: feedArity,
				disabled: isDisabled
			});
		}

		// if a parent entry is available exists. Adds it.
		if (availableFeeds.parentFeed != null) {
			this.appendUseFeed(editHtml, {
				isChecked: this.isParentEntryUsed(),
				displayFeedName: _.WIDGET_FEED_ENTRY_CURRENT(),
				feedId: _.WIDGET_FEED_ENTRY_ID(availableFeeds.parentFeed.length > 1 ? null : availableFeeds.parentFeed[0].getId()),
				color: availableFeeds.parentFeed.length > 1 ? undefined : availableFeeds.parentFeed[0].color,
				arity: feedArity,
				disabled: isDisabled
			});

			if (this.isParentEntryUsed()) {
				this.appendUseFeed(listHtml, {
					readOnly: true,
					color: availableFeeds.parentFeed.length > 1 ? undefined : availableFeeds.parentFeed[0].color,
					feedId: _.WIDGET_FEED_ENTRY_ID(availableFeeds.parentFeed.length > 1 ? null : availableFeeds.parentFeed[0].getId())
				});
			}
		}

		// adds the available feeds
		var newFeedIds = [];
		var feedIds = this.getFeedIds();
		for (var i = 0; i < availableFeeds.feeds.length; i++) {
			var availableFeed = availableFeeds.feeds[i],
			availableFeedId = availableFeed.json.id;

			// check if this feed is selected
			var isChecked = false;
			if (feedIds.indexOf(availableFeedId) != -1) {
				isChecked = true;
				newFeedIds.push(availableFeedId);
			}

			// adds the <li> for this feed
			this.appendUseFeed(editHtml, {
				isChecked: isChecked,
				displayFeedName: availableFeed.feedDefinition.name,
				feedId: availableFeedId,
				arity: feedArity,
				color: availableFeed.color,
				disabled: isDisabled
			});

			if (isChecked) {
				this.appendUseFeed(listHtml, {
					readOnly: true,
					displayFeedName: availableFeed.feedDefinition.name,
					feedId: availableFeedId,
					color: availableFeed.color
				});
			}
		}

		if (listHtml.getLength() == 0) {
			listHtml.append('<li class="widget-feed" name="doSlideUseFeeds">' + _.WIDGET_NO_FEED_SELECTED() + '</li>');
		}

		// update feeds used by the widget after the redraw because the context of the widget
		// may have changed and the feeds currently sets may not be up to date anymore
		this.setFeedIds(newFeedIds);
	}

	// update the list of available feeds
	this.getElUseFeeds().find('.widget-feeds-edit .widget-feeds').html(editHtml.toString());
	this.getElUseFeeds().find('.widget-feeds-list .widget-feeds').html(listHtml.toString());
};

/**
 * Appends the used feed to the given string builder
 */
Mashup_Page_UI_Layout_Widget.prototype.appendUseFeed = function(sb, options) {
	var title = undefined;
	if (options.feedId != undefined) {
		title = options.feedId;
		if (options.displayFeedName != undefined) {
			if (options.feedId.length > 0) {
				title += ' - ';
			}
			title += options.displayFeedName;
		}
	}

	sb.append('<li class="widget-feed"' + (title != undefined ? (' title="' + title + '"') : '') + '>');
	sb.append(	'<label class="widget-feed-label">');
	if (options.readOnly != true) {
		sb.append('<input type="' + (options.arity == 'ZERO_OR_ONE' || options.arity == 'ONE' ? 'radio' : 'checkbox') + '" class="widget-feed-checkbox" name="feedName" value="' + (options.feedId || '') + '" ' + (options.isChecked ? 'checked="checked"' : '') + (options.disabled ? 'disabled="disabled"' : '')  + ' /> ');
	}
	sb.append(		'<span class="widget-feed-color bgcolor ' + (options.color || 'no-color') + '"></span> ');
	if (options.feedId != undefined) {
		sb.append('<span class="widget-feed-id"' + (options.readOnly == true ? ' name="doSlideUseFeeds"' : '') + '>' + options.feedId + '</span>');
	}
	if (options.readOnly != true && options.displayFeedName != undefined) {
		if (options.feedId != undefined && options.feedId.length > 0) {
			sb.append(' - ');
		}
		sb.append('<span class="widget-feed-type">' + options.displayFeedName + '</span> ');
	}
	sb.append(	'</label>');
	sb.append('</li>');
};

/**
 * Returns the feed available for this widget
 *
 * @returns {hash}
 */
Mashup_Page_UI_Layout_Widget.prototype.getAvailableFeeds = function() {
	var ret = { feeds: [], parentFeed: null };

	// Retrieve the closest parent that supports and consume feeds or top level if not found
	var feedsCanBeUseTmp = [];
	var parentWidgetWithFeeds = this.getParentWidgetWithFeeds();

	if (parentWidgetWithFeeds != null) {
		var parentFeeds = parentWidgetWithFeeds.getFeedClasses(true, false);
		if (parentFeeds.length > 0) {
			for (var i = 0; i < parentFeeds.length; i++) {
				$.merge(feedsCanBeUseTmp, parentFeeds[i].getFeedContainer().getFeeds(false));
			}
			ret.parentFeed = parentFeeds;
		}
	} else {
		feedsCanBeUseTmp = this.getPageUI().getPage().getAPI().getFeedContainer().getFeeds(false);
	}

	// Filter supported feeds
	var supportedFeeds = this.getWidgetDefinition().supportFeedsId.feedsId;
	if (supportedFeeds.length > 0) {
		var feedsLength = feedsCanBeUseTmp.length;
		for ( var i = 0; i < feedsLength; i++) {
			if (supportedFeeds.indexOf(feedsCanBeUseTmp[i].getClassName()) != -1) {
				ret.feeds.push(feedsCanBeUseTmp[i]);
			}
		}
	} else {
		ret.feeds = feedsCanBeUseTmp;
	}

	return ret;
};

/**
 * Returns all feeds classes used in this widget (including parent ones)
 * @param boolean includeCurrentWidget
 * @param boolean includeParentWidgets
 */
Mashup_Page_UI_Layout_Widget.prototype.getFeedClasses = function(includeCurrentWidget, includeParentWidgets) {
	if (includeCurrentWidget == null) {
		throw 'Missing parameter includeCurrentWidget';
	}
	if (includeParentWidgets == null) {
		throw 'Missing parameter includeParentWidgets';
	}

	var feedClasses = [];

	// feeds used by parent widgets
	if (includeParentWidgets == true) {
		if ((parentWidget = this.getParentWidgetWithFeeds()) != null) {
			feedClasses = parentWidget.getFeedClasses(true, true);
		}
	}

	// feeds used by self
	if (includeCurrentWidget == true) {

		// gets the parent entry
		if (this.isParentEntryUsed()) {
			if (((parentWidget = this.getParentWidgetWithFeeds()) != null) && (parentWidget.hasFeeds() || parentWidget.isParentEntryUsed())) {
				var parentFeedClasses = parentWidget.getFeedClasses(true, false);
				for (var i = 0; i < parentFeedClasses.length; i++) {
					var feedClass = parentFeedClasses[i];
					if (feedClasses.indexOf(feedClass) == -1) {
						feedClasses.push(feedClass);
					}
				}
			}
		}

		// gets the feed used by self
		var feedIds = this.getFeedIds();
		if (feedIds.length > 0) {
			var pageAPI = this.getPageUI().getPage().getAPI();
			for (var i = 0; i < feedIds.length; i++) {
				if ((feed = pageAPI.getFeed(feedIds[i])) != null) {
					if (feed.getId().length > 0 && feedClasses.indexOf(feed) == -1) {
						feedClasses.push(feed);
					}
				}
			}
		}
	}

	return feedClasses;
};

/**
 * Validate the widget subwidgets
 */
Mashup_Page_UI_Layout_Widget.prototype.checkUseWidgets = function() {
	if (!this.canHaveNoWidgets() && !this.hasWidgets()) {
		this.addError(this.getElSubWidgets());
	} else if (!this.canHaveManyWidgets() && this.getWidgets(false).length > 1) {
		this.addError(this.getElSubWidgets());
	} else {
		this.removeError(this.getElSubWidgets());
	}
};

/**
 * Validates the widget feeds
 */
Mashup_Page_UI_Layout_Widget.prototype.checkUseFeeds = function() {
	if (!this.canHaveNoFeeds() && !this.hasFeeds() && !this.isParentEntryUsed()) {
		this.addError(this.getElUseFeeds());
	} else {
		this.removeError(this.getElUseFeeds());
	}
};

/**
 * Disabled the current widget
 *
 * @param message
 */
Mashup_Page_UI_Layout_Widget.prototype.disable = function(message) {
	if (message == undefined) {
		this.json.enable = false;
	}
	if (!this.getEl().hasClass('widget-disabled')) {
		this.getEl().addClass('widget-disabled');
		if (message == undefined) {
			this.getElHeader().find('.widget-header-icons').prepend('<span class="icon icon-widget-disabled" name="doToggleStatus" title="' + _.WIDGET_DISABLED() + '"></span>');
			this.doSlideWidget(true, function() {
				this.state('w', 1);
			});
		} else {
			this.getElHeader().find('.widget-header-icons').prepend('<span class="icon icon-widget-invalid" warningMessage="' + message + '"></span>');
		}

		var triggers = this.getTriggers(true);
		for (var i = 0; i < triggers.length; i++) {
			triggers[i].disable();
		}

		this.updateMenuHTML();
	}
};

/**
 * Enables the current widget
 */
Mashup_Page_UI_Layout_Widget.prototype.enable = function() {
	this.json.enable = true;
	if (this.getEl().hasClass('widget-disabled')) {
		this.getEl().removeClass('widget-disabled');
		this.getElHeader().find('.icon-widget-disabled,.icon-widget-invalid').remove();
		this.doSlideWidget(false, function() {
			this.state('w', 0);
		});
		
		var triggers = this.getTriggers(true);
		for (var i = 0; i < triggers.length; i++) {
			triggers[i].enable();
		}
		
		this.updateMenuHTML();
	}
};

/**
 * Attaches the draggable event
 */
Mashup_Page_UI_Layout_Widget.prototype.attachDraggable = function() {
	var _this = this;
	this.getEl().draggable({
		revert: 'invalid',
		zIndex: 4200,
		handle: '.widget-header',
		distance: 10,
		helper: function() {
			return $('' +
				'<form class="widget-wrapper">' +
					'<div class="widget widget-closed">' +
						'<div class="widget-header" style="cursor:move;">' +
							'<table class="widget-header-table">' +
								'<tbody>' +
									'<tr>' +
										'<td class="widget-fold-handle"><span class="icon"></span></td>' +
										'<td><div class="widget-name" name="editWidget">' + _this.getWidgetName() + '</div></td>' +
										'<td class="widget-header-icons">' +
											'<span class="icon widget-move-handle" title="' + _.WIDGET_MOVE() + '"></span>' +
										'</td>' +
									'</tr>' +
								'</tbody>' +
							'</table>' +
						'</div>' +
					'</div>' +
				'</form>'
			);
		},
		appendTo: '#page-wrapper',
		start: dragWidgetStart,
		stop: dragWidgetStop
	});
};

/**
 * Removes the widget from the DOM
 */
Mashup_Page_UI_Layout_Widget.prototype.remove = function() {
	// clears any error of the widget/subwidgets
	this.clearError();

	if (this.triggerContainer != undefined) {
		this.triggerContainer.remove();
		this.triggerContainer = undefined;
	}

	if (this.subWidgetContainer != undefined) {
		this.subWidgetContainer.remove();
		this.subWidgetContainer = undefined;
	}

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Returns this widget as JSON
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget.prototype.getJson = function() {
	this.json.compositeWidget = this.getWidgetDefinition().compositeWidget;
	if (!this.isLayout()) {
		this.json.table = null;
		if (this.subWidgetContainer != undefined) {
			this.json.widgets = this.subWidgetContainer.getJson();
		}
	} else {
		this.json.widgets = null;
	}
	if (this.triggerContainer != undefined) {
		this.json.triggers = this.triggerContainer.getJson();
	}
	return this.json;
};

/**
 * File: /resources/mashupBuilder/js/Page/UI/Layout/Widget/Table.js
 */
Inherit(Mashup_Page_UI_Layout_Widget_Table, Mashup_Page_UI_Layout_Widget);
function Mashup_Page_UI_Layout_Widget_Table(container, json) {
	Mashup_Page_UI_Layout_Widget_Table.superclass.constructor.call(this, container, json);
}

/**
 * Returns the layout of the widget
 * 
 * @returns {Mashup_Page_UI_Layout_TableLayout}
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.getLayout = function() {
	if (this.layout == undefined) {
		this.layout = new Mashup_Page_UI_Layout_TableLayout(this, this.json.layout);
	}
	return this.layout;
};

/**
 * Returns whether the widget has subwidgets or not
 *
 * @returns {Boolean}
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.hasWidgets = function() {
	return this.getLayout().hasWidgets();
};

/**
 * Returns all the widgets contained by this widget
 *
 * @param recursive
 * @returns
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.getWidgets = function(recursive) {
	return this.getLayout().getWidgets(recursive);
};

/**
 * User process to edit the widget properties
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.doEditWidget = function(clearCache) {
	// close edit mode if opened
	if (this.getLayout().isEditModeEnabled()) {
		this.doEditLayout();
	}
	Mashup_Page_UI_Layout_Widget_Table.superclass.doEditWidget.call(this, clearCache);
};

/**
 * User process to edit this widget layout
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.doEditLayout = function() {
	var viewMode = this.getLayout().isEditModeEnabled() ? 'custom' : 'edit';

	var widgets = this.getWidgets(true);
	var widgetsLength = widgets.length;
	for (var i = 0; i < widgetsLength; i++) {
		widgets[i].setViewMode(viewMode);
	}

	if (viewMode == 'edit') {
		this.getLayout().enableEditMode();
		this.getElSubWidgets().addClass('view-edit');

		if (this.updateLayout != undefined) {
			this.getLayout().onUpdatePageFormat();
			this.updateLayout = undefined;
		}

	} else {
		this.getLayout().disableEditMode();
		this.getElSubWidgets().removeClass('view-edit');
	}

	this.updateMenuHTML();
};

/**
 * Called when the widget has been updated
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.onUpdate = function(context) {
	if (context != undefined && context.input != undefined) {
		var name = context.input.getName();
		if (name == 'width' || name == 'widthFormat') {
			this.getLayout().setWidth(getJsonParameterValue(this.json.parameters, 'width'));
			this.getLayout().setWidthFormat(getJsonParameterValue(this.json.parameters, 'widthFormat'));
			this.updateLayout = true;
		}
	}
	return Mashup_Page_UI_Layout_Widget_Table.superclass.onUpdate.call(this, context);
};

/**
 * jQuery onClick event handler
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.handleEventOnClick = function(e) {
	var ret = true;
	if (this.getLayout().isEditModeEnabled()) {
		ret = this.getLayout().getEditLayout().handleEventOnClick(e);
	}
	if (ret === false) {
		return false;
	}
	return Mashup_Page_UI_Layout_Widget_Table.superclass.handleEventOnClick.call(this, e);
};

/**
 * Returns the DOM node for the sub-widgets
 * @returns
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.getElSubWidgets = function() {
	if (this.elSubWidgets == undefined) {
		if (this.canHaveWidgets()) {
			this.elSubWidgets = $('<div class="widget-subwidgets-wrapper widget-subwidgets-layout"></div>');
			this.elSubWidgets.append(this.getLayout().getEl());
		} else {
			this.elSubWidgets = $();
		}
	}
	return this.elSubWidgets;
};

/**
 * Called when subwidget context has changed (created/deleted/moved)
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.onUpdateSubWidgets = function() {
	// nothing to update
};

/**
 * Removes the widget from the DOM
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.remove = function() {
	if (this.layout != undefined) {
		this.layout.remove();
		this.layout = undefined;
	}
	Mashup_Page_UI_Layout_Widget_Table.superclass.remove.call(this);
};

/**
 * Returns this widget as JSON
 *
 * @returns
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.getJson = function() {
	Mashup_Page_UI_Layout_Widget_Table.superclass.getJson.call(this);
	this.json.layout = this.getLayout().getJson();
	return this.json;
};

/**
 * unimplemented
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.getSubWidgetContainer = function() {
	return null;
};

/**
 * unimplemented
 */
Mashup_Page_UI_Layout_Widget_Table.prototype.doSlideSubWidgets = function(shouldBeClosed, onEndCallback) {
};


/**
 * File: /resources/mashupBuilder/js/Page/Preview.js
 */
/**
 * Implementation of a Mashup Preview
 *
 * @constructor
 * @this {Mashup_Page_Preview}
 * @param {Mashup_Page} page The current page
 */
function Mashup_Page_Preview(page) {
	this.page = page;
}

/**
 * Returns the page class
 *
 * @returns {Mashup_Page}
 */
Mashup_Page_Preview.prototype.getPage = function() {
	return this.page;
};

/**
 * Returns the page name
 *
 * @returns
 */
Mashup_Page_Preview.prototype.getPageName = function() {
	return this.page.getName();
};

/**
 * Returns whether the Mashup Preview is visible or not
 *
 * @returns
 */
Mashup_Page_Preview.prototype.isVisible = function() {
	return window.mashupBuilder.hasMashupUI();
};

/**
 * Returns whether the Mashup Preview is read only or not
 *
 * @returns
 */
Mashup_Page_Preview.prototype.isReadOnly = function() {
	return false;
};

/**
 * Returns whether the Mashup Preview is updated or not
 *
 * @returns
 */
Mashup_Page_Preview.prototype.isUpdated = function() {
	return false;
};

/**
 * Called by the onResize method in the MashupBuilder or when creating the el
 */
Mashup_Page_Preview.prototype.onResize = function() {
	var msgHeight = this.getPage().getMessages().getHeight();
	var height = window.mashupBuilder.height - msgHeight - 4;

	this.getEl().css({ height: height });
	this.getElIframe().css({ height: height });
};

/**
 * Returns whether this tab can be opened or not
 */
Mashup_Page_Preview.prototype.canOpen = function() {
	if ((nbErrors = window.mashupBuilder.getErrorsCount()) > 0) {
		new Mashup_Popup_Error(nbErrors).show();
		return false;
	}
	return true;
};

/**
 * Called when the preview is opened
 */
Mashup_Page_Preview.prototype.onOpen = function() {
	if (this.getPage().isSecurityEnable()) {
		this.getPage().getMessages().setMessage('preview', { level: 'warning', canClose: false, message: _.PAGEPREVIEW_UNAVAILABLE_SECURITY() });
		this.getElIframe().detach();
	} else if (!this.getPage().isUpToDate()) {
		this.getPage().getMessages().setMessage('preview', { level: 'warning', canClose: false, message: _.PAGEPREVIEW_UNAVAILABLE_NOTUPTODATE() });
		this.getElIframe().detach();
	} else {
		if (window.mashupBuilder.isUpdated()) {
			window.mashupBuilder.config.save({
				doApply: false,
				doWait: true
			});
		}
		this.getElIframe().appendTo(this.getEl());
		this.reloadIframe();
	}
};

/**
 * Called when the preview is closed
 */
Mashup_Page_Preview.prototype.onClose = function() {
	this.getPage().getMessages().removeMessage('preview');
	this.getElIframe().detach();
};

/**
 * Called when we need to redraw the preview
 */
Mashup_Page_Preview.prototype.redraw = function() {
	if (this.getPage().getOpenedTabName() == 'preview') {
		this.onOpen();
	}
};

/**
 * Reloads the iframe
 */
Mashup_Page_Preview.prototype.reloadIframe = function() {
	// compute the iframe URL
	var urlBuilder = new BuildUrl(getMashupUIStaging() + '/' + this.getPage().getName());
	var parameters = window.mashupBuilder.getToolboxContainer().getToolbox('previewParameters').getJson();
	for (var i = 0; i < parameters.length; i++) {
		if (parameters[i].name != 'debugMode' && parameters[i].name != 'maxResults') {
			var rawValue = parameters[i].value.split('##'),
				name = rawValue[0], value = rawValue[1];
			if (name.length > 0) {
				urlBuilder.addParameter(name, value);
			}
		}
	}

	var maxResults = window.mashupBuilder.getOpenedPage().getState('mr', '');
	if (maxResults != undefined && maxResults != '') {
		urlBuilder.addParameter('__maxResults', parseInt(maxResults));
	}

	// adds a random number to prevent cache issue
	urlBuilder.addParameter('__time', (new Date()).getTime());

	var iframe = this.getElIframe()[0];

	// update the iframe src
	iframe.src = urlBuilder.toString();

	// handle loading spinner
	var _this = this;
	this.getElSpinner().appendTo(this.getEl());
	bindIframeOnLoad(iframe, function() {
		_this.getElSpinner().detach();
	});
};

/**
 * Returns the DOM element for the Mashup Preview
 *
 * @returns
 */
Mashup_Page_Preview.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
		'<div id="preview-workspace" class="page-livepreview-wrapper workspace">' +
		'</div>');
	}
	return this.el;
};

/**
 * Returns the DOM element for the iframe
 */
Mashup_Page_Preview.prototype.getElIframe = function() {
	if (this.elIframe == undefined) {
		this.elIframe = $('<iframe class="page-livepreview-iframe"></iframe>');
	}
	return this.elIframe;
};

/**
 * Returns the DOM element for the Tab
 *
 * @returns
 */
Mashup_Page_Preview.prototype.getElBottomTabs = function() {
	if (this.elTopTabs == undefined) {
		this.elTopTabs = $('' +
			'<li class="page-bottom-tab mashupPreview" name="doOpenMashupPreview">' +
				'<span class="typeId" name="doOpenMashupPreview">' + _.PAGEPREVIEW_TITLE() + '</span> ' +
			'</li>'
		);
	}
	return this.elTopTabs;
};

/**
 * Returns the DOM element for the spinner
 *
 * @returns
 */
Mashup_Page_Preview.prototype.getElSpinner = function() {
	if (this.elSpinner == undefined) {
		this.elSpinner = $('' +
			'<div class="page-livepreview-spinner">' +
				'<img src="' + window.mashup.baseUrl + 'resources/commons/images/spinner.gif" />' +
				'<span class="page-livepreview-spinner-text">' + _.PAGEPREVIEW_LOADING_TEXT() + '</span>' +
			'</div>');
	}
	return this.elSpinner;
};

/**
 * Returns the tab error Node
 *
 * @returns
 */
Mashup_Page_Preview.prototype.getElBottomTabsErrors = function() {
	return $();
};

/**
 * Remove this page from the DOM
 */
Mashup_Page_Preview.prototype.remove = function() {
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
	if (this.elTopTabs != undefined) {
		this.elTopTabs.remove();
		this.elTopTabs = undefined;
	}
	this.elIframe = undefined;
};

/**
 * Return the JSON
 *
 * @returns
 */
Mashup_Page_Preview.prototype.getJson = function() {
	return [];
};


/**
 * File: /resources/mashupBuilder/js/Application.js
 */
/**
 * A page dedicated to the application configuration
 *
 * @constructor
 * @this {Mashup_Application}
 */
function Mashup_Application() {
	this.errorsCount = 0;
	this.messages = new Mashup_Control_Messages({
		onUpdateCallbackData: { _this : this },
		onUpdateCallback: function(context) {
			context.data._this.onResize();
		}
	});
}

/**
 * Returns the name of the page
 *
 * @returns {string}
 */
Mashup_Application.prototype.getName = function() {
	return 'APPLICATION';
};

/**
 * Returns the messages handler
 *
 * @returns {Mashup_Control_Messages}
 */
Mashup_Application.prototype.getMessages = function() {
	return this.messages;
};

/**
 * Returns the number of errors
 *
 * @returns {Number}
 */
Mashup_Application.prototype.getErrorsCount = function() {
	return this.errorsCount;
};

/**
 * Returns whether this page is linked to a Mashup UI or not
 *
 * @returns {Boolean}
 */
Mashup_Application.prototype.hasUI = function() {
	return false;
};

/**
 * Returns whether this page is linked to a Mashup API or not
 *
 * @returns {Boolean}
 */
Mashup_Application.prototype.hasAPI = function() {
	return false;
};

/**
 * Returns whether the page is currently opened or not
 *
 * @returns {Boolean}
 */
Mashup_Application.prototype.isOpen = function() {
	return ((openedPage = window.mashupBuilder.getOpenedPage()) != null && (openedPage.getName() == this.getName()));
};

/**
 * Returns whether or not the page is up to date
 */
Mashup_Application.prototype.isUpToDate = function() {
	return this.upToDate == undefined || this.upToDate == true;
};

/**
 * Sets whether or not this page is up to date
 */
Mashup_Application.prototype.setUpToDate = function(upToDate) {
	this.upToDate = upToDate;
};

/**
 * Returns the context menu
 *
 * @returns {Mashup_Window_TabMenu_Context}
 */
Mashup_Application.prototype.getContext = function() {
	if (this.context == undefined) {
		this.context = new Mashup_Window_TabMenu_Context({ showClose: true });
		this.context.setParent(this);
		this.context.getEl().hide().appendTo(this.getEl());
		this.context.resize(undefined, 450);
	}
	return this.context;
};

/**
 * Update the error count for this page
 *
 * @param errorsCount
 */
Mashup_Application.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;

	if (this.errorsCount == 0) {
		this.getElTopTabs().removeClass('has-error');
	} else {
		this.getElTopTabs().addClass('has-error');
	}
};

/**
 * Returns the DOM element for this page
 *
 * @returns
 */
Mashup_Application.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
			'<div id="page-wrapper">' +
			'</div>'
		);
		this.el.data('_this', this);
		this.el.append(this.getMessages().getEl());

		var tabs = this.getTabs();
		for (var i = 0; i < tabs.length; i++) {
			tabs[i].getEl().appendTo(this.el);
			tabs[i].getEl().addClass('moveToMoon');
		}

		this.el.on('click', $.proxy(this.handleEventOnClick, this));
	}
	return this.el;
};

/**
 * jQuery onClick event handler
 * 
 * @param e
 */
Mashup_Application.prototype.handleEventOnClick = function(e) {
	this.getContext().onInputFocus(undefined);
	return true;
};

/**
 * Returns the DOM node for the top tab
 *
 * @returns
 */
Mashup_Application.prototype.getElTopTabs = function() {
	if (this.elTopTabs == undefined) {
		this.elTopTabs = $('.menu-application');
	}
	return this.elTopTabs;
};

/**
 * Returns the name of the opened tab
 *
 * @returns
 */
Mashup_Application.prototype.getOpenedTabName = function() {
	return this.openedTabName;
};

/**
 * Returns the default tab name for this page
 *
 * @returns
 */
Mashup_Application.prototype.getDefaultTabName = function() {
	if (this.openedTabName != undefined) {
		return this.openedTabName;
	}
	var tabs = this.getTabs();
	for (var i = 0; i < tabs.length; i++) {
		if (tabs[i].isVisible()) {
			return tabs[i].getId();
		}
	}
	return null;
};

/**
 * Returns the tabs (lazy load)
 *
 * @param tabName
 * @returns
 */
Mashup_Application.prototype.getTabs = function() {
	if (this.tabs == undefined) {
		this.tabs = [];

		// General
		if (window.mashupBuilder.hasMashupUI()) {
			this.tabs.push(new Mashup_Application_Properties(this));
		}
		this.tabs.push(new Mashup_Application_API(this));
		if (window.mashupBuilder.hasMashupUI()) {
			this.tabs.push(new Mashup_Application_Styles(this));
			this.tabs.push(new Mashup_Application_Javascript(this));
			this.tabs.push(new Mashup_Application_Security(this));
		}

		// Manage components
		this.tabs.push(new Mashup_Application_Plugins(this));
		this.tabs.push(new Mashup_Application_CustomComponents(this));
		if (window.mashupBuilder.hasMashupUI()) {
			this.tabs.push(new Mashup_Application_Export(this));
			this.tabs.push(new Mashup_Application_Controllers(this));
		}

		// Developer area
		this.tabs.push(new Mashup_Application_DeveloperArea(this));
	}
	return this.tabs;
};

/**
 * Returns a Mashup Tab class
 *
 * @param tabName
 * @returns
 */
Mashup_Application.prototype.getTab = function(tabName) {
	var tabs = this.getTabs();
	for (var i = 0; i < tabs.length; i++) {
		if (tabs[i].getId() == tabName) {
			return tabs[i];
		}
	}
	return null;
};

/**
 *
 * @param tabName
 * @returns the focused tabName. null otherwise
 */
Mashup_Application.prototype.openTab = function(tabName) {
	var tab = this.getTab(tabName);

	// fallback to default tab if none given or unable to access it
	if (tab == null || (this.openedTabName == null && tab.isVisible() == false)) {
		tabName = this.getDefaultTabName();
		tab = this.getTab(tabName);
	}

	// if invalid given tab then skip
	if (tab.isVisible() == false) {
		return null;
	}

	// if already opened then trigger onOpen and skip
	if (this.openedTabName == tabName) {
		tab.onOpen();
		return tabName;
	}

	// close the opened tab
	if (this.getTab(this.openedTabName) != null) {
		this.getTab(this.openedTabName).getEl().addClass('moveToMoon');
		this.getTab(this.openedTabName).onClose();
	}

	// opens the new tab
	tab.onOpen();
	tab.getEl().removeClass('moveToMoon');
	this.openedTabName = tabName;

	return tabName;
};

/**
 * Called when the window is resized
 */
Mashup_Application.prototype.onResize = function() {
	var msgHeight = this.getMessages().getHeight();

	this.width = window.mashupBuilder.width - 270; // left(270)
	this.height = window.mashupBuilder.height - msgHeight;

	this.getEl().css({
		width: this.width,
		height: this.height,
		paddingTop: msgHeight
	});

	this.getContext().resize(undefined, 450);

	this.getMessages().getEl().css('width', window.mashupBuilder.width - 290); // left(270) scrollbar(15) padding-right(5)

	var tabs = this.getTabs();
	for (var i = 0; i < tabs.length; i++) {
		tabs[i].onResize();
	}
};

/**
 * Called when the page is opened by the MashupBuilder
 */
Mashup_Application.prototype.onOpen = function() {
	this.onResize();

	window.mashupBuilder.getToolboxContainer().hideToolboxes();
	window.mashupBuilder.getToolboxContainer().getToolbox('messages').show();
	window.mashupBuilder.getToolboxContainer().getToolbox('navigation').setActivePage(this);

	window.mashupBuilder.getElPageMenu().hide();
	window.mashupBuilder.getElViewModeMenu().hide();
	window.mashupBuilder.getElAppRightMenu().css('display', '');

	this.openTab(window.mashupBuilder.parseAnchor()[1]);

	$('body').addClass('workspace-fullscreen');
	this.getElTopTabs().addClass('selected');
};

/**
 * Called when the page is closed by the Mashup Builder
 */
Mashup_Application.prototype.onClose = function() {
	window.mashupBuilder.getElPageMenu().css('display', ''); // do not use show() otherwise it will have priority over CSS
	window.mashupBuilder.getElViewModeMenu().show();
	window.mashupBuilder.getElAppRightMenu().hide();
	this.getElTopTabs().removeClass('selected');
	this.remove();
};

/**
 * Redraws the application page
 */
Mashup_Application.prototype.redraw = function() {
	window.mashupBuilder.doOpenPage(this.getName(), this.getOpenedTabName(), true);
};

/**
 * Remove this page from the DOM
 */
Mashup_Application.prototype.remove = function() {
	$('body').removeClass('workspace-fullscreen');

	window.mashupBuilder.getToolboxContainer().getToolbox('navigation').setActivePage(null);

	this.openedTabName = undefined;

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}

	if (this.context != undefined) {
		this.context.remove();
		this.context = undefined;
	}

	this.getMessages().remove();

	if (this.tabs != undefined) {
		for (var i = 0; i < this.tabs.length; i++) {
			this.tabs[i].remove();
		}
	}
};


/**
 * File: /resources/mashupBuilder/js/Application/Abstract.js
 */
/**
 * Abstract class of a Mashup application page
 * 
 * @constructor
 * @this {Mashup_Application_Abstract}
 * @param page
 * @param options
 * @returns
 */
function Mashup_Application_Abstract(page, options) {
	this.page = page;
	this.id = options.id || '';
	this.label = options.label || this.id;
	this.group = options.group || _.APP_MENU_GROUP_GENERAL();
	this.description = options.description || '';

	this.parameterWidth = 350;
	this.errorsCount = 0;

	this.onResizeCallbacks = [];

	this.options = options;
}

/**
 * Returns the page ID
 * 
 * @returns
 */
Mashup_Application_Abstract.prototype.getId = function() {
	return this.id;
};

/**
 * Returns the page label
 * 
 * @returns
 */
Mashup_Application_Abstract.prototype.getLabel = function() {
	return this.label;
};

/**
 * Returns the page group for the navigation menu
 * 
 * @returns
 */
Mashup_Application_Abstract.prototype.getGroup = function() {
	return this.group;
};

/**
 * Returns the page class
 * 
 * @returns {Mashup_Page}
 */
Mashup_Application_Abstract.prototype.getPage = function() {
	return this.page;
};

/**
 * Returns the page name
 * 
 * @returns
 */
Mashup_Application_Abstract.prototype.getPageName = function() {
	return this.page.getName();
};

/**
 * Returns the parameter width
 * 
 * @returns
 */
Mashup_Application_Abstract.prototype.getParameterWidth = function() {
	return this.parameterWidth;
};

/**
 * Returns the errors count
 * 
 * @returns
 */
Mashup_Application_Abstract.prototype.getErrorsCount = function() {
	return this.errorsCount;
};

/**
 * Returns whether the page is visible or not
 * 
 * @returns
 */
Mashup_Application_Abstract.prototype.isVisible = function() {
	return window.mashupBuilder.permission.canViewAppSettings() == true;
};

/**
 * Returns whether the page is read only or not
 * 
 * @returns
 */
Mashup_Application_Abstract.prototype.isReadOnly = function() {
	return !window.mashupBuilder.permission.canUpdateAppSettings() || !this.getPage().isUpToDate();
};

/**
 * Called when something has been updated within the page
 * 
 * @returns {Boolean}
 */
Mashup_Application_Abstract.prototype.onUpdate = function() {
};

/**
 * Called before the page is displayed
 * 
 * @returns {Boolean}
 */
Mashup_Application_Abstract.prototype.onOpen = function() {
	if (!this.getPage().isUpToDate()) {
		if (this.getPage().getName() == 'APPLICATION') {
			this.getPage().getMessages().setMessage('disabled-app', { level : 'error', canClose : false, message : _.CONCURRENCY_APPLICATION_NOT_UPTODATE() });
		} else {
			this.getPage().getMessages().setMessage('disabled', { level: 'error', canClose: false, message: _.CONCURRENCY_MASHUP_UI_NOT_UPTODATE() });
		}
	}

	if (this.description.length > 0) {
		this.getPage().getMessages().setMessage('description', { message: this.description, level: 'info', canClose: false });
	}

	window.mashupBuilder.getToolboxContainer().getToolbox('navigation').setActiveTab(this.getId());
};

/**
 * Called before the page is displayed
 * 
 * @returns {Boolean}
 */
Mashup_Application_Abstract.prototype.onClose = function() {
	if (!this.getPage().isUpToDate()) {
		if (this.getPage().getName() == 'APPLICATION') {
			this.getPage().getMessages().removeMessage('disabled-app');
		} else {
			this.getPage().getMessages().removeMessage('disabled');
		}
	}
	if (this.description.length > 0) {
		this.getPage().getMessages().removeMessage('description');
	}
	closeCodeMirrorFullScreen();
};

/**
 * Called by the onResize method in the MashupBuilder or when creating the el
 */
Mashup_Application_Abstract.prototype.onResize = function() {
	for (var i = 0; i < this.onResizeCallbacks.length; i++) {
		this.onResizeCallbacks[i].call(this);
	}
};

/**
 * Called when an input inside this page has gained focus
 * 
 * @param input
 */
Mashup_Application_Abstract.prototype.onInputFocus = function(input) {
	var offset = input.getParameterContainer().getEl().offset();
	this.getPage().getContext().onInputFocus(input);
	this.getPage().getContext().getEl().css({ top: offset.top - 66 });
	this.getPage().getContext().show();
};

/**
 * Updates the error count for this application page
 * 
 * @param {number} errorsCount Can be positive (more errors) or negative (less errors)
 */
Mashup_Application_Abstract.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;

	if (this.errorsCount == 0) {
		this.getElMenuError().hide();
	} else {
		this.getElMenuError().css('display', 'inline-block').html(this.errorsCount);
	}

	this.getPage().updateError(errorsCount);
};

/**
 * Returns the DOM element for this page
 * 
 * @returns
 */
Mashup_Application_Abstract.prototype.getEl = function() {
	return $();
};

/**
 * Returns the DOM node for the menu
 */
Mashup_Application_Abstract.prototype.getElMenuError = function() {
	return window.mashupBuilder.getToolboxContainer().getToolbox('navigation').getElInnerContent().find('.' + this.getId() + ' .icon-error');
};

/**
 * Registers a custom onResize callback
 * 
 * @param func
 */
Mashup_Application_Abstract.prototype.registerOnResize = function(func) {
	this.onResizeCallbacks.push(func);
};

/**
 * Unregisters a custom onResize callback
 * 
 * @param func
 */
Mashup_Application_Abstract.prototype.unregisterOnResize = function(func) {
	if ((idx = this.onResizeCallbacks.indexOf(func)) != -1) {
		this.onResizeCallbacks.splice(idx, 1);
	}
};

/**
 * Remove this page from the DOM
 */
Mashup_Application_Abstract.prototype.remove = function() {
};

/**
 * Return the JSON
 * 
 * @returns
 */
Mashup_Application_Abstract.prototype.getJson = function() {
	return [];
};


/**
 * File: /resources/mashupBuilder/js/Application/API.js
 */
/**
 * Implementation of a page for the API properties
 *
 * @constructor
 * @param page
 * @returns
 */
Inherit(Mashup_Application_API, Mashup_Application_Abstract);
function Mashup_Application_API(page) {
	Mashup_Application_API.superclass.constructor.call(this, page, {
		id: 'api',
		label: _.APP_MENU_ITEM_API(),
		group: _.APP_MENU_GROUP_GENERAL()
	});
}

/**
 * Initialize the parameters for the properties
 */
Mashup_Application_API.prototype.getContainers = function() {
	if (this.containers == undefined) {
		this.containers = [];

		var config = Mashup_Parameter_Factory.createInvertedConfigParameters(window.jsonMashupAPI.parameters);
		for (var name in window.jsonMashupAPI.concurrencyPolicies) {
			if (name != 'class') {
				if (config[name] == undefined) {
					config[name] = [];
				}
				config[name].push(window.jsonMashupAPI.concurrencyPolicies[name] + '');
			}
		}

		var properties = Mashup_Parameter_Factory.createAPIParameters();
		for (var i = 0; i < properties.length; i++) {
			var containerConfig = properties[i].container,
				parameters = properties[i].parameters;

			var parameterContainer = new Mashup_Parameter_Container($.extend({
				showToggle: false,
				showLabel: true,
				parent: this,
				onChangeCallback: $.proxy(this.onUpdate, this),
			}, containerConfig));

			for (var j = 0; j < parameters.length; j++) {
				parameterContainer.addParameter(new Mashup_Parameter(window.defaultJsonCustomComponentParameter['class'], $.extend(parameters[j], {
					width: this.getParameterWidth(),
					values: config[parameters[j].name] ? config[parameters[j].name] : []
				})));
			}

			this.containers.push(parameterContainer);
		}
	}
	return this.containers;
};

/**
 * Called when an input has been updated
 */
Mashup_Application_API.prototype.onUpdate = function() {
	window.mashupBuilder.jsonMashupAPI.parameters = this.getJson();
	window.mashupBuilder.jsonMashupAPI.concurrencyPolicies = $.extend(window.mashupBuilder.jsonMashupAPI.concurrencyPolicies, this.getConcurrencyPolicies());
	window.mashupBuilder.onUpdateAPIConfiguration();
};

/**
 * Returns the DOM element for this page
 *
 * @returns
 */
Mashup_Application_API.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="api-properties" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);

		// adds container
		var containers = this.getContainers();
		for (var i = 0; i < containers.length; i++) {
			this.el.append(containers[i].getEl());
		}

		// inject a document icon for the reporting
		this.el.find('.parameter_reportingEnabled').find('.parameter-value-icons').append('' +
			'<span class="icon icon-documentation" title="' + _.PARAM_ACTIONS_TITLE_DOC() + '" name="doOpenDoc" data-chapter="REPORTING"></span>'
		);
	}
	return this.el;
};

/**
 * Remove page from the DOM
 */
Mashup_Application_API.prototype.remove = function() {
	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Returns the values for the concurrency policies
 */
Mashup_Application_API.prototype.getConcurrencyPolicies = function() {
	var containers = this.getContainers();
	for (var i = 0; i < containers.length; i++) {
		if (containers[i].id == 'concurrency-policies') {
			var ret = {};
			var values = containers[i].getJson();
			for (var j = 0; j < values.length; j++) {
				ret[values[j].name] = values[j].value;
			}
			return ret;
		}
	}
	return {};
};

/**
 * Return the JSON for the API properties
 *
 * @returns
 */
Mashup_Application_API.prototype.getJson = function() {
	var json = [];
	var containers = this.getContainers();
	for (var i = 0 ; i < containers.length; i++) {
		if (containers[i].id != 'concurrency-policies') {
			$.merge(json, containers[i].getJson());
		}
	}
	return json;
};


/**
 * File: /resources/mashupBuilder/js/Application/Controllers.js
 */
/**
 * Implementation of a page for the application controllers
 *
 * @constructor
 * @param page
 * @returns
 */
Inherit(Mashup_Application_Controllers, Mashup_Application_Abstract);
function Mashup_Application_Controllers(page) {
	Mashup_Application_Controllers.superclass.constructor.call(this, page, {
		id: 'controllers',
		label: _.APP_MENU_ITEM_CONTROLLERS(),
		group: _.APP_MENU_GROUP_COMPONENTS(),
		description: _.APP_CONTROLLERS_DESCRIPTION()
	});
}

/**
 * Initialize the parameters for the javascript
 */
Mashup_Application_Controllers.prototype.getContainers = function() {
	if (this.containers == undefined) {
		this.containers = [];

		var hasConfigurableController = false;
		var controllers = getAvailableControllers();

		for (var i = 0; i < controllers.length; i++) {
			var configuration = getCustomComponent(window.jsonMashupUI.springControllers, controllers[i]);
			var config = Mashup_Parameter_Factory.createInvertedConfigParameters(configuration != null ? configuration.parameters : {});
			var properties = Mashup_Parameter_Factory.createControllerParameters(controllers[i])[0];

			// create the container for this controller
			var parameterContainer = new Mashup_Parameter_Container($.extend({
				parent: this,
				showLabel: true,
				showToggle: false,
				onChangeCallback: $.proxy(this.onUpdate, this)
			}, properties.container));

			// adds the parameters
			if (properties.parameters.length > 0) {
				hasConfigurableController = true;
				for (var j = 0; j < properties.parameters.length; j++) {
					var parameter = properties.parameters[j];
					parameterContainer.addParameter(new Mashup_Parameter(window.defaultJsonCustomComponentParameter['class'], $.extend(parameter, {
						width: 600,
						values: config[parameter.name] ? config[parameter.name] : []
					})));
				}
			}

			// register the container
			this.containers.push(parameterContainer);
		}

		if (hasConfigurableController && !this.isReadOnly()) {
			container = new Mashup_Parameter_Container({
				id: 'controllers-actions',
				label: _.APP_DEV_TITLE_ACTIONS(),
				showToggle: false,
				showLabel: true,
				showEmpty: false,
				parent: this
			});

			container.addButton({
				name: 'doReloadComponents',
				label: _.APP_DEV_RLD_BUTTON()
			});

			this.containers.push(container);
		}
	}
	return this.containers;
};

/**
 * jQuery onClick event handler
 * 
 * @param e
 * @returns {Boolean}
 */
Mashup_Application_Controllers.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'doReloadComponents':
			window.mashupBuilder.doReloadAvailableComponents();
			return false;
		}
	}
};

/**
 * Returns the DOM element for this page
 *
 * @returns
 */
Mashup_Application_Controllers.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="application-controllers" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);

		var containers = this.getContainers();
		if (containers.length == 0) {
			this.el.append('<div class="no-controllers">' + _.APP_CONTROLLERS_EMPTY() + '</div>');
		} else {
			for (var i = 0; i < containers.length; i++) {
				this.el.append(containers[i].getEl());
			}

			this.el.on('click', $.proxy(this.handleEventOnClick, this));
		}
	}
	return this.el;
};

/**
 * Called when an input has been updated
 */
Mashup_Application_Controllers.prototype.onUpdate = function() {
	window.mashupBuilder.jsonMashupUI.springControllers = this.getJson();
	window.mashupBuilder.onUpdateControllers();
};

/**
 * Redraw the controllers list
 */
Mashup_Application_Controllers.prototype.redraw = function() {
	if (this.el != undefined) {
		var $backup = this.el;
		this.el = undefined;
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();
	}
};

/**
 * Remove the page from the DOM
 */
Mashup_Application_Controllers.prototype.remove = function() {
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}

	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}
};

/**
 * Return the JSON
 *
 * @returns
 */
Mashup_Application_Controllers.prototype.getJson = function() {
	var json = [];
	var containers = this.getContainers();
	for (var i = 0 ; i < containers.length; i++) {
		if (containers[i].getId() != 'controllers-actions') {
			var customComponent = createCustomComponent(containers[i].options.className);
			customComponent.parameters = containers[i].getJson();
			json.push(customComponent);
		}
	}
	return json;
};

/**
 * File: /resources/mashupBuilder/js/Application/CustomComponents.js
 */
/**
 * Implementation of an application page to register Custom Components
 */
Inherit(Mashup_Application_CustomComponents, Mashup_Application_Abstract);
function Mashup_Application_CustomComponents(page) {
	Mashup_Application_CustomComponents.superclass.constructor.call(this, page, {
		id: 'customcomponents',
		label: _.APP_MENU_ITEM_CC(),
		group: _.APP_MENU_GROUP_COMPONENTS(),
		description: _.APP_CC_DESCRIPTION()
	});
	this.checkComponent = new Mashup_Config_CheckComponent();
}

/**
 * Initialize the parameter containers for the custom components
 */
Mashup_Application_CustomComponents.prototype.getContainers = function() {
	if (this.containers == undefined) {
		this.containers = [];

		// Mashup API components

		var container = new Mashup_Parameter_Container({
			id: 'customComponentsAPI',
			label: _.APP_CC_FEED_TITLE(),
			showToggle: false,
			showLabel: true,
			parent: this,
			onChangeCallback: $.proxy(this.onUpdateAPI, this)
		});

		var mashupAPIValues = [];
		for (var i = 0; i < mashupBuilder.jsonMashupAPI.customComponentList.components.length; i++) {
			mashupAPIValues.push(mashupBuilder.jsonMashupAPI.customComponentList.components[i].className);
		}
		container.addParameter(new Mashup_Parameter('com.exalead.cv360.config.elements.CustomComponentList$CustomComponentClass', {
			width: 450,
			name: 'customComponentsMashupAPI',
			isValueOrdered: false,
			label: _.APP_CC_FEED_LABEL(),
			values: mashupAPIValues,
			arity: PARAMETER_ARITY.ZERO_OR_MANY,
			description: _.APP_CC_FEED_DESCRIPTION(),
			textAddIconOnRight: _.APP_CC_FEED_BUTTON(),
			showAddIconOnRight: false,
			inputs: [{
				type: 'Input',
				name: 'customComponentsMashupAPI',
				label: _.APP_CC_FEED_LABEL()
			}]
		}));

		this.containers.push(container);

		// Mashup UI components

		if (window.mashupBuilder.hasMashupUI()) {

			container = new Mashup_Parameter_Container({
				id: 'customComponentsUI',
				label: _.APP_CC_DESIGN_TITLE(),
				parent: this,
				showToggle: false,
				showLabel: true,
				onChangeCallback: $.proxy(this.onUpdateUI, this)
			});

			var mashupUIValues = [];
			for (var i = 0; i < mashupBuilder.jsonMashupUI.customComponentList.components.length; i++) {
				mashupUIValues.push(mashupBuilder.jsonMashupUI.customComponentList.components[i].className);
			}
			container.addParameter(new Mashup_Parameter('com.exalead.cv360.config.elements.CustomComponentList$CustomComponentClass', {
				width: 450,
				name: 'customComponentsMashupUI',
				label: _.APP_CC_DESIGN_LABEL(),
				isValueOrdered: false,
				values: mashupUIValues,
				arity: PARAMETER_ARITY.ZERO_OR_MANY,
				description: _.APP_CC_DESIGN_DESCRIPTION(),
				textAddIconOnRight: _.APP_CC_DESIGN_BUTTON(),
				showAddIconOnRight: false,
				inputs: [{
					type: 'Input',
					name: 'customComponentsMashupAPI',
					label: _.APP_CC_DESIGN_LABEL()
				}]
			}));

			this.containers.push(container);
		}

		// Actions container
		if (!this.isReadOnly()) {
			container = new Mashup_Parameter_Container({
				id: 'custom-components-actions',
				label: _.APP_DEV_TITLE_ACTIONS(),
				showToggle: false,
				showLabel: true,
				showEmpty: false,
				parent: this
			});

			container.addButton({
				name: 'doReloadComponents',
				label: _.APP_DEV_RLD_BUTTON()
			});
		}

		this.containers.push(container);
	}
	return this.containers;
};

/**
 * Called when the UI custom components have been updated
 */
Mashup_Application_CustomComponents.prototype.onUpdateUI = function() {
	if (!window.mashupBuilder.hasMashupUI()) {
		return;
	}

	var componentUI = [];

	// save custom Components for Mashup UI
	var newCustomComponentsUI = [];
	var jsonCustomComponentsMashupUI = this.containers[1].getParameter('customComponentsMashupUI').getJson();
	for (var i = 0; i < jsonCustomComponentsMashupUI.length; i++) {
		jsonCustomComponentsMashupUI[i].className = jsonCustomComponentsMashupUI[i].value;
		newCustomComponentsUI.push(jsonCustomComponentsMashupUI[i]);
	}
	for (var a = 0; a < window.mashupBuilder.jsonMashupUI.customComponentList.components.length; a++) {
		var className = window.mashupBuilder.jsonMashupUI.customComponentList.components[a].className;
		if (getCustomComponent(newCustomComponentsUI, className) == null) {
			componentUI.push(className);
		}
	}

	// Retrieve usage of the removed custom components
	var pages = window.mashupBuilder.getPages(); 

	var usage = [];
	if (componentUI.length > 0) {
		for (var i = 0; i < pages.length; i++) {
			if (this.checkComponent.hasUIComponent(pages[i], componentUI)) {
				usage.push(pages[i].getName());
			}
		}
	}

	// If were used then ask the user whether to remove the components or not
	if (usage.length > 0) {
		var _this = this;
		new Mashup_Popup_Prompt({
			title: _.APP_CC_TITLE(),
			text: _.APP_CC_UPDATE_USED_TEXT(usage),
			label: _.APP_CC_UPDATE_CONFIRM(),
			type: 'checkbox',
			buttonOkLabel: _.YES(),
			buttonCancelLabel: _.NO(),
			onClickOkCallback: function(context) {
				if (this.getElPromptInput().is(':checked')) {
					for (var i = 0; i < pages.length; i++) {
						_this.checkComponent.removeUIComponent(pages[i], componentUI);
					}
				}

				for (var i = 0; i < jsonAvailableMashupUITriggers.components.length; i++) {
					if (componentUI.indexOf(jsonAvailableMashupUITriggers.components[i].className) != -1) {
						jsonAvailableMashupUITriggers.components.splice(i--, 1);
					}
				}
				window.mashupBuilder.jsonMashupUI.customComponentList.components = newCustomComponentsUI;

				this.remove();
			}
		}).show();
	} else {
		for (var i = 0; i < jsonAvailableMashupUITriggers.components.length; i++) {
			if (componentUI.indexOf(jsonAvailableMashupUITriggers.components[i].className) != -1) {
				jsonAvailableMashupUITriggers.components.splice(i--, 1);
			}
		}
		window.mashupBuilder.jsonMashupUI.customComponentList.components = newCustomComponentsUI;
	}

	window.mashupBuilder.onUpdateCustomComponentUI();
};

/**
 * Called when the API custom components have been updated
 */
Mashup_Application_CustomComponents.prototype.onUpdateAPI = function() {
	var componentAPI = [];

	// save custom Components for Mashup API
	var jsonCustomComponentsMashupAPI = this.containers[0].getParameter('customComponentsMashupAPI').getJson();
	var newCustomComponentsAPI = [];
	for (var i = 0; i < jsonCustomComponentsMashupAPI.length; i++) {
		jsonCustomComponentsMashupAPI[i].className = jsonCustomComponentsMashupAPI[i].value;
		newCustomComponentsAPI.push(jsonCustomComponentsMashupAPI[i]);
	}
	for (var a = 0; a < window.mashupBuilder.jsonMashupAPI.customComponentList.components.length; a++) {
		var className = window.mashupBuilder.jsonMashupAPI.customComponentList.components[a].className;
		if (getCustomComponent(newCustomComponentsAPI, className) == null) {
			componentAPI.push(className);
		}
	}

	var pages = window.mashupBuilder.getPages();

	// retrieve usage of the removed custom components
	var usage = [];
	if (componentAPI.length > 0) {
		for (var i = 0; i < pages.length; i++) {
			if (this.checkComponent.hasAPIComponent(pages[i], componentAPI)) {
				usage.push(pages[i].getName());
			}
		}
	}

	// If were used then ask the user whether to remove the components or not
	if (usage.length > 0) {
		var _this = this;
		new Mashup_Popup_Prompt({
			title: _.APP_CC_TITLE(),
			text: _.APP_CC_UPDATE_USED_TEXT(usage),
			label: _.APP_CC_UPDATE_CONFIRM(),
			type: 'checkbox',
			buttonOkLabel: _.YES(),
			buttonCancelLabel: _.NO(),
			onClickOkCallback: function(context) {
				if (this.getElPromptInput().is(':checked')) {
					for (var i = 0; i < pages.length; i++) {
						_this.checkComponent.removeAPIComponent(pages[i], componentAPI);
					}
				}
				for (var i = 0; i < jsonAvailableMashupAPITriggers.components.length; i++) {
					if (componentAPI.indexOf(jsonAvailableMashupAPITriggers.components[i].className) != -1) {
						jsonAvailableMashupAPITriggers.components.splice(i--, 1);
					}
				}
				mashupBuilder.jsonMashupAPI.customComponentList.components = newCustomComponentsAPI;

				this.remove();
			}
		}).show();
	} else {
		for (var i = 0; i < jsonAvailableMashupAPITriggers.components.length; i++) {
			if (componentAPI.indexOf(jsonAvailableMashupAPITriggers.components[i].className) != -1) {
				jsonAvailableMashupAPITriggers.components.splice(i--, 1);
			}
		}
		window.mashupBuilder.jsonMashupAPI.customComponentList.components = newCustomComponentsAPI;
	}

	window.mashupBuilder.onUpdateCustomComponentAPI();
};

/**
 * Returns the DOM element for this page
 * 
 * @returns
 */
Mashup_Application_CustomComponents.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="custom-components" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);

		var containers = this.getContainers();
		for (var i = 0; i < containers.length; i++) {
			this.el.append(containers[i].getEl());
		}

		this.el.on('click', $.proxy(this.handleEventOnClick, this));
	}
	return this.el;
};

/**
 * jQuery onClick event handler
 * 
 * @param e
 * @returns {Boolean}
 */
Mashup_Application_CustomComponents.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'doReloadComponents':
			window.mashupBuilder.doReloadAvailableComponents();
			return false;
		}
	}
};

/**
 * Remove this page from the DOM
 */
Mashup_Application_CustomComponents.prototype.remove = function() {
	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Application/DeveloperArea.js
 */
/**
 * Implementation of an application page for developper
 */
Inherit(Mashup_Application_DeveloperArea, Mashup_Application_Abstract);
function Mashup_Application_DeveloperArea(page) {
	Mashup_Application_DeveloperArea.superclass.constructor.call(this, page, {
		id: 'developer',
		label: _.APP_MENU_ITEM_DEVELOPER(),
		group: _.APP_MENU_GROUP_DEVELOPER(),
		description: _.APP_DEV_DESCRIPTION()
	});
}

/**
 * Returns the parameter containers
 */
Mashup_Application_DeveloperArea.prototype.getContainers = function() {
	if (this.containers == undefined) {
		this.containers = [];
		var container;

		if (!this.isReadOnly()) {
			container = new Mashup_Parameter_Container({
					id: 'developer-actions',
					label: _.APP_DEV_TITLE_ACTIONS(),
					showToggle: false,
					showLabel: true,
					showEmpty: false,
					parent: this
				});

			container.addButton({
				name: 'doReloadComponents',
				label: _.APP_DEV_RLD_BUTTON()
			});

			container.addButton({
				name: 'doClearStorage',
				label: _.APP_DEV_CLEAR_STORAGE()
			});

			container.addButton({
				name: 'doCheckConfiguration',
				label: _.APP_DEV_CHECKCONF_BUTTON()
			});

			this.containers.push(container);
		}

		if (!window.mashupBuilder.hasMashupUI()) {
			return this.containers;
		}

		// create the context menu functions
		var contextMenuFunctions = [];
		if ((devUris = window.mashupBuilder.state.get('m', 'u', [])).length > 0) {
			contextMenuFunctions = [{ name: 'emptyOnChange', parameters: [] }, { name: 'addContext', parameters: ['History', devUris] }];
		}

		// create the container
		container = new Mashup_Parameter_Container({
			id: 'customUrl',
			label: _.APP_DEV_CUSTOM_URL_TITLE(),
			description: _.APP_DEV_CUSTOM_URL_DESCRIPTION(),
			showToggle: false,
			showLabel: true,
			parent: this
		});

		// adds the debug mode
		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			width: this.getParameterWidth(),
			name: 'debugMode',
			label: _.APP_DEV_DEVMODE_LABEL(),
			description: _.APP_DEV_DEVMODE_DESCRIPTION(),
			values: [window.mashupBuilder.isDebugMode() + ''],
			type: 'Checkbox',
			possibleValues: ['false', 'true'],
			onChangeCallback: function() {
				window.mashupBuilder.services.application.setDebugMode(this.getValue() == 'true');
			}
		})));

		// adds the MashupUI URL parameter
		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			width: this.getParameterWidth(),
			name: 'customMashupUI',
			label: _.APP_DEV_CUSTOM_MASHUP_LABEL(),
			arity: PARAMETER_ARITY.ZERO_OR_ONE,
			description: _.APP_DEV_CUSTOM_MASHUP_DESCRIPTION(),
			context: contextMenuFunctions,
			values: [developmentMode],
			onBlur: [{
				handler: function(ctx) {
					var _this = this,
						devUri = this.val();

					$.ajax({
						method: 'GET',
						url: getCurrentPath() + '/setDevelopmentMode?uri=' + encodeURIComponent(devUri),
						dataType: 'json',
						context: this,
						success: function(data) {
							// clear errors
							_this.clearError();

							// retrieve used saved URI
							var devUri = data.uri;

							// save new URL
							if (devUri.length > 0 && devUris.indexOf(devUri) == -1) {
								devUris.push(devUri);
								window.mashupBuilder.state.save('m', 'u', devUris);
							}

							// update current devURI and reload components
							window.mashupBuilder.setDevelopmentMode(devUri);
						},
						error: function() {
							_this.setError([_.APP_DEV_CUSTOM_MASHUP_ERROR_INVALID()]);
						}
					});
				}
			}]
		})));

		this.containers.push(container);

		container = new Mashup_Parameter_Container({
			id: 'overview',
			label: _.APP_DEV_OVERVIEW_TITLE(),
			showToggle: false,
			showLabel: true,
			showEmpty: false,
			parent: this,
			onCreateCallback: $.proxy(this.doRefreshOverview, this)
		});

		container.addButton({
			name: 'doRefreshOverview',
			label: _.APP_DEV_OVERVIEW_BUTTON_REFRESH()
		});

		this.containers.push(container);
	}
	return this.containers;
};

/**
 * jQuery onClick event handler
 * 
 * @param e
 * @returns {Boolean}
 */
Mashup_Application_DeveloperArea.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'doRefreshOverview':
			this.doRefreshOverview();
			return false;

		case 'doReloadComponents':
			window.mashupBuilder.doReloadAvailableComponents();
			return false;

		case 'doClearStorage':
			if (window.mashupBuilder.permission.canUpdateAppSettings()) {
				window.mashupBuilder.clearStorage();
			}
			return false;

		case 'doCheckConfiguration':
			this.doCheckConfiguration();
			return false;
		}
	}
};

/**
 * User process to check the entire configuration
 */
Mashup_Application_DeveloperArea.prototype.doCheckConfiguration = function() {
	var nbErrors = window.mashupBuilder.getErrorsCount();
	if (nbErrors > 0) {
		new Mashup_Popup_Error(nbErrors).show();
		return;
	}

	new Mashup_Popup_Confirm({
		title: _.APP_DEV_CHECKCONF_CONFIRM_TITLE(),
		text: _.APP_DEV_CHECKCONF_CONFIRM_TEXT(),
		buttonOkLabel: _.YES(),
		buttonCancelLabel: _.NO(),
		level: BoxLevel.WARNING,
		onClickOkCallbackData: { _this: this },
		onClickOkCallback: function(context) {
			var _this = context.data._this;

			if (_this.configValidator == undefined) {
				_this.configValidator = new Mashup_Config_Validator();
			}

			this.remove();

			if (_this.configValidator.isStarted()) {
				new Mashup_Popup_Confirm({
					title: _.APP_DEV_CHECKCONF_CONFIRM_TITLE(),
					text: _.APP_DEV_CHECKCONF_CONFIRM_RESUME(),
					buttonOkLabel: _.YES(),
					buttonCancelLabel: _.NO(),
					level: BoxLevel.WARNING,
					onClickOkCallback: function(context) {
						this.remove();
						_this.configValidator.checkApplication();
					},
					onClickCancelCallback: function(context) {
						this.remove();
						_this.configValidator.reset();
						_this.configValidator.checkApplication();
					}
				}).show();
			} else {
				_this.configValidator.checkApplication();
			}

		}
	}).show();
};

/**
 * Refreshes the overview parameter container
 */
Mashup_Application_DeveloperArea.prototype.doRefreshOverview = function() {
	var _this = this;

	if (this.elLoading == undefined) {
		this.elLoading = $('' +
			'<div class="is-loading">' +
				'<span class="is-loading-text">' + _.APP_DEV_OVERVIEW_LOADING() + '</span>' +
			'</div>'
		);
		this.elLoading.css({ left: 350, top: 80 });
	}

	var overviewContainer = this.containers[this.containers.length - 1];
	overviewContainer.getEl().append(this.elLoading);

	$.ajax({
		type: 'GET',
		url: getCurrentPath() + '/testProduction',
		dataType: 'json',
		success: function(data, textStatus) {
			_this.elLoading.remove();
			for (var i = 0; i < data.length; i++) {
				var flag = data[i];
				var parameter = overviewContainer.getParameter(flag.key);
				if (parameter == null) {
					parameter = new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
						type: 'Literal',
						width: _this.getParameterWidth(),
						name: flag.key,
						label: _this.getFlagLabel(flag.key),
						description: _this.getFlagDescription(flag.key)
					}));
					overviewContainer.addParameter(parameter);
				}
				var input = parameter.getValuesClass()[0].getInputs()[0];
				if (flag.productionReady == 'true') {
					input.getEl().removeClass('warning').removeAttr('warningMessage');
				} else {
					input.getEl().addClass('warning').attr('warningMessage', _.APP_DEV_OVERVIEW_NOT_READY());
				}
				parameter.setValue(flag.value + '');
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
			_this.elLoading.remove();
			if (overviewContainer.getParameters().length == 0) {
				overviewContainer.getEl().find('.parameters-wrapper').html('' +
					'<div class="messages-wrapper">' +
						'<div class="error-message">' + _.APP_DEV_OVERVIEW_SERVER_ERROR() + '</div>' +
					'</div>'
				);
			}
		}
	});
};

/**
 * Returns the label for the flag
 * 
 * @param str
 * @returns
 */
Mashup_Application_DeveloperArea.prototype.getFlagLabel = function(str) {
	switch (str) {
	case 'alwaysReload': return _.APP_DEV_LABEL_RELOAD();
	case 'enableAuthentication': return _.APP_DEV_LABEL_AUTHENTICATION();
	case 'packResources': return _.APP_DEV_LABEL_PACKRESOURCES();
	case 'displayStackTrace': return _.APP_DEV_LABEL_STACKTRACE();
	case 'trimFilter': return _.APP_DEV_LABEL_TRIM();
	case 'jspDevelopmentMode': return _.APP_DEV_LABEL_JSPDEVMODE();
	default: return str;
	}
};

/**
 * Returns the description for the flag
 * 
 * @param str
 * @returns
 */
Mashup_Application_DeveloperArea.prototype.getFlagDescription = function(str) {
	switch (str) {
	case 'alwaysReload': return _.APP_DEV_DESCRIPTION_RELOAD();
	case 'enableAuthentication': return _.APP_DEV_DESCRIPTION_AUTHENTICATION();
	case 'packResources': return _.APP_DEV_DESCRIPTION_PACKRESOURCES();
	case 'displayStackTrace': return _.APP_DEV_DESCRIPTION_STACKTRACE();
	case 'trimFilter': return _.APP_DEV_DESCRIPTION_TRIM();
	case 'jspDevelopmentMode': return _.APP_DEV_DESCRIPTION_JSPDEVMODE();
	default: return str;
	}
};

/**
 * Returns the DOM element for this page
 * 
 * @returns
 */
Mashup_Application_DeveloperArea.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="application-developer" class="workspace">' +
			'</div>'
		);

		this.el.data('_this', this);
		this.el.on('click', $.proxy(this.handleEventOnClick, this));

		var containers = this.getContainers();
		for (var i = 0; i < containers.length; i++) {
			this.el.append(containers[i].getEl());
		}
	}
	return this.el;
};

/**
 * Remove this page from the DOM
 */
Mashup_Application_DeveloperArea.prototype.remove = function() {
	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Application/Export.js
 */
/**
 * Implementation of a page for the plugin export
 * 
 * @constructor
 * @param page
 * @returns
 */
Inherit(Mashup_Application_Export, Mashup_Application_Abstract);
function Mashup_Application_Export(page) {
	Mashup_Application_Export.superclass.constructor.call(this, page, {
		id: 'export',
		label: _.APP_MENU_ITEM_EXPORT(),
		group: _.APP_MENU_GROUP_COMPONENTS(),
		description: _.PLUGIN_EXPORT_TAB_DESCRIPTION()
	});
}

/**
 * Returns the export parameter container
 */
Mashup_Application_Export.prototype.getParameterContainer = function() {
	if (this.parameterContainer == undefined) {
		var opts = {'': '--'};
		for (var i = 0; i < window.jsonAvailableWidgets.widget.length; i++) {
			var w = window.jsonAvailableWidgets.widget[i];
			var group = (w.group == null || w.group.length == 0) ? _.PLUGIN_EXPORT_TAB_WIDGET_NO_GROUP() : w.group;
			if (opts[group] == null) {
				opts[group] = {};
			}
			opts[group][w.id] = w.name;
		}

		this.parameterContainer = new Mashup_Parameter_Container({
			id: 'exportWidget',
			label: _.PLUGIN_EXPORT_TAB_TITLE(),
			showLabel: true,
			onClickCallback: $.proxy(this.handleEventOnClick, this)
		});

		this.parameterContainer.addParameter(
			new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
				width: this.getParameterWidth(),
				name: 'widgetId',
				label: _.PLUGIN_EXPORT_TAB_WIDGET_LABEL(),
				possibleValues: opts,
				validateOnDisplay: false,
				isValueOrdered: false,
				arity: PARAMETER_ARITY.MANY,
				description: _.PLUGIN_EXPORT_TAB_WIDGET_DESCRIPTION(),
				onChangeCallback: function(context) {
					var widgetId = this.getValue();
					if (widgetId != '') {
						// set plugin name
						var name = this.getParameterContainer().getParameter('name');
						name.setValue(widgetId);

						// set plugin desc
						var desc = this.getParameterContainer().getParameter('description');
						var def = getWidgetDefinition(widgetId);
						var defDesc = $.trim(def.description);
						if (defDesc != '') {
							desc.setValue(defDesc);
						} else {
							desc.setValue('');
						}

						// set plugin author
						var author = this.getParameterContainer().getParameter('author');
						var defAuthor = $.trim(def.author);
						if (defAuthor != '') {
							author.setValue(defAuthor);
						} else {
							author.setValue('');
						}
					}
				}
			}))
		);

		this.parameterContainer.addParameter(
			new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
				width: this.getParameterWidth(),
				name: 'name',
				label: _.PLUGIN_EXPORT_TAB_NAME_LABEL(),
				arity: PARAMETER_ARITY.ONE,
				description: _.PLUGIN_EXPORT_TAB_NAME_DESCRIPTION(),
				validateOnDisplay: false
			}))
		);

		this.parameterContainer.addParameter(
			new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
				type: 'Textarea',
				width: this.getParameterWidth(),
				name: 'description',
				label: _.PLUGIN_EXPORT_TAB_DESC_LABEL(),
				arity: PARAMETER_ARITY.ONE,
				description: _.PLUGIN_EXPORT_TAB_DESC_DESCRIPTION(),
				validateOnDisplay: false
			}))
		);

		this.parameterContainer.addParameter(
			new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
				width: this.getParameterWidth(),
				name: 'author',
				label: _.PLUGIN_EXPORT_TAB_AUTHOR_LABEL(),
				arity: PARAMETER_ARITY.ONE,
				description: _.PLUGIN_EXPORT_TAB_AUTHOR_DESCRIPTION(),
				validateOnDisplay: false
			}))
		);

		this.parameterContainer.addButton({
			name: 'doExportWidget',
			label: _.PLUGIN_EXPORT_TAB_BUTTON()
		});
	}
	return this.parameterContainer;
};

/**
 * jQuery onClick event handler
 * 
 * @param e
 */
Mashup_Application_Export.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'doExportWidget':
			this.doExportWidget();
			return false;
		}
	}
	return true;
};

/**
 * Trigger the export for the given configuration
 */
Mashup_Application_Export.prototype.doExportWidget = function() {
	var container = this.getParameterContainer();

	var data = {widgets: []};
	var json = container.getJson();
	for (var i = 0; i < json.length; i++) {
		if (json[i].name == 'widgetId') {
			data.widgets.push(json[i].value);
		} else {
			data[json[i].name] = json[i].value;
		}
	}

	container.validate();

	if (data.name != null && data.description != null && data.author != null && data.widgets.length > 0) {
		// empty the form and clear errors
		container.getParameter('name').setValue('');
		container.getParameter('description').setValue('');
		container.getParameter('author').setValue('');
		container.getParameter('widgetId').setValue('');
		container.clearError();

		// bypass the onbeforeunload execution for the export
		var unloadCallback = window.onbeforeunload;
		window.onbeforeunload = function() {
			window.onbeforeunload = unloadCallback;
		};

		// redirect the user to the export url
		window.location = getCurrentPath() + '/createPlugin' +
			'?widgetId=' + encodeURIComponent(data.widgets.join(',')) +
			'&name=' + encodeURIComponent(data.name) +
			'&description=' + encodeURIComponent(data.description) +
			'&author=' + encodeURIComponent(data.author);
	}
};

/**
 * Returns the DOM element for export page
 * 
 * @returns
 */
Mashup_Application_Export.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="export-plugins" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);
		this.el.append(this.getParameterContainer().getEl());
	}
	return this.el;
};

/**
 * Remove the export page from the DOM
 */
Mashup_Application_Export.prototype.remove = function() {
	if (this.parameterContainer != undefined) {
		this.parameterContainer.remove();
		this.parameterContainer = undefined;
	}

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Application/Javascript.js
 */
/**
 * Implementation of a page for the application javascript
 * 
 * @constructor
 * @param page
 * @returns
 */
Inherit(Mashup_Application_Javascript, Mashup_Application_Abstract);
function Mashup_Application_Javascript(page) {
	Mashup_Application_Javascript.superclass.constructor.call(this, page, {
		id: 'javascript',
		label: _.APP_MENU_ITEM_JAVASCRIPT(),
		group: _.APP_MENU_GROUP_GENERAL(),
		description: _.APP_JAVASCRIPT_DESCRIPTION()
	});
}

/**
 * Initialize the parameters for the javascript
 */
Mashup_Application_Javascript.prototype.getContainers = function() {
	if (this.containers == undefined) {
		this.containers = [];

		var config = Mashup_Parameter_Factory.createInvertedConfigParameters(window.jsonMashupUI.parameters);
		var properties = Mashup_Parameter_Factory.createApplicationParameters();
		for (var i = 0; i < properties.length; i++) {
			if (properties[i].container.id != 'javascript') {
				continue;
			}

			var parameters = properties[i].parameters;
			for (var j = 0; j < parameters.length; j++) {
				var parameter = parameters[j];

				var parameterContainer = new Mashup_Parameter_Container({
					label: parameter.label,
					parent: this,
					showLabel: true,
					showToggle: false
				});

				parameterContainer.addParameter(new Mashup_Parameter(window.defaultJsonCustomComponentParameter['class'], $.extend(parameters[j], {
					width: 600,
					values: config[parameter.name] ? config[parameter.name] : [],
					showLabels: false,
					onChangeCallback: $.proxy(this.getPage().getTab('properties').onUpdate, this.getPage().getTab('properties'))
				})));

				this.containers.push(parameterContainer);
			}
		}
	}
	return this.containers;
};

/**
 * Returns the DOM element for this page
 * 
 * @returns
 */
Mashup_Application_Javascript.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="application-javascript" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);

		var containers = this.getContainers();
		for (var i = 0; i < containers.length; i++) {
			this.el.append(containers[i].getEl());
		}
	}
	return this.el;
};
/**
 * Remove the page from the DOM
 */
Mashup_Application_Javascript.prototype.remove = function() {
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}

	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}
};

/**
 * Return the JSON
 * 
 * @returns
 */
Mashup_Application_Javascript.prototype.getJson = function() {
	var json = [];
	var containers = this.getContainers();
	for (var i = 0 ; i < containers.length; i++) {
		$.merge(json, containers[i].getJson());
	}
	return json;
};

/**
 * File: /resources/mashupBuilder/js/Application/Plugins.js
 */
/**
 * Implementation of a page for the application plugins
 *
 * @constructor
 * @param page
 * @returns
 */
Inherit(Mashup_Application_Plugins, Mashup_Application_Abstract);
function Mashup_Application_Plugins(page) {
	Mashup_Application_Plugins.superclass.constructor.call(this, page, {
		id: 'plugins',
		label: _.APP_MENU_ITEM_PLUGINS(),
		group: _.APP_MENU_GROUP_COMPONENTS(),
		description: _.APP_PLUGINS_DESCRIPTION()
	});
}

/**
 * jQuery onClick event handler
 *
 * @param e
 * @returns {Boolean}
 */
Mashup_Application_Plugins.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'doUploadPlugin':
			this.doUploadPlugin();
			return false;

		case 'doRefreshPlugin':
			this.doRefreshPlugin();
			return false;

		case 'doDeletePlugin':
			this.doDeletePlugin(target.closest('.plugin-wrapper').attr('data-pluginId'));
			return false;

		case 'doDownloadPlugin':
			window.location = target.attr('data-href');
			return false;

		case 'doSlidePlugin':
			target.closest('.plugin-wrapper').find('.plugin-content').slideToggle('fast');
			return false;

		case 'doSynchronizePlugins':
			this.doSynchronizePlugins();
			return false;
		}
	}
};

/**
 * Closes the opened upload box
 */
Mashup_Application_Plugins.prototype.closeUploadBox = function() {
	if (this.uploadBox != undefined) {
		this.uploadBox.remove();
		this.uploadbox = undefined;
	}
};

/**
 * User process to upload a plugin
 */
Mashup_Application_Plugins.prototype.doUploadPlugin = function() {
	this.closeUploadBox();
	this.uploadBox = new Mashup_Popup_Alert({
		title: _.PLUGIN_INSTALL_TITLE(),
		width: 400,
		text: '' +
		'<div class="popup-upload-plugin">' +
			'<iframe style="border: 0; display: block; height: 30px; width:400px;" frameBorder="0"></iframe>' +
		'</div>',
		buttonOkLabel: _.CANCEL()
	});

	var _this = this;
	var iframe = this.uploadBox.getElContent().find('iframe')[0];
	iframe.onload = iframe.onreadystatechange = function() {
		if (iframe.readyState == undefined || iframe.readyState == 'complete') {
			iframe.onload = iframe.onreadystatechange = undefined;

			var $body = $(iframe.contentDocument.body);

			$body.css({ 'margin': 0})
				 .html('' +
					'<form id="uploadForm" style="margin:0;" action="' + getCurrentPath() + '/uploadPlugin" method="POST" enctype="multipart/form-data">' +
						'<table style="width: 100%;font-family:arial,sans-serif;font-size:11px;">' +
							'<tbody>' +
								'<tr>' +
									'<td><strong>' + _.PLUGIN_INSTALL_LABEL() + '</strong></td>' +
									'<td><input type="file" name="file" style="display: inline-block;font-size:11px;" /></td>' +
								'</tr>' +
							'</tbody>' +
						'</table>' +
					'</form>');

			var $input = $body.find('input'),
				$form = $body.find('form');

			var onChange = function() {
				var filePath = $input.val();
				if (filePath.length > 0) {
					$form.submit();

					if (((idx = filePath.lastIndexOf('/')) != -1) || ((idx = filePath.lastIndexOf('\\')) != -1)) {
						filePath = filePath.substr(idx + 1);
					}

					$(iframe).hide();

					_this.uploadBox.getEl().css({ width: '', marginLeft: '' });
					_this.uploadBox.getElContent().find('.popup-message').append(_.PLUGIN_INSTALL_UPLOADING(filePath));
					_this.uploadBox.showLoading();
					_this.uploadBox.hideButtons();

					return false;
				}
			};

			if ($.browser.msie) {
				$input.click(function() {
					setTimeout(function() { onChange(); }, 0);
				});
			} else {
				$input.bind('change', onChange);
			}

			$(iframe).show();
		}
	};

	this.uploadBox.show();
};

/**
 * User process to refresh the listed plugins
 */
Mashup_Application_Plugins.prototype.doRefreshPlugin = function() {
	window.mashupBuilder.doReloadAvailableComponents({
		showConfirmBox: false,
		onReloadCallback: $.proxy(function() {
			this.refreshPlugins();
			this.checkUpdates();
		}, this)
	});
};

/**
 * User process to synchronize the plugins
 */
Mashup_Application_Plugins.prototype.doSynchronizePlugins = function() {
	var _this = this;
	var confirmBox = new Mashup_Popup_Confirm({
		text: '',
		title: _.APP_PLUGINS_COPY_TITLE(),
		onOpenDoc: 'SYNCHRONIZE_PLUGINS',
		buttonOkLabel: _.APP_PLUGINS_COPY_BUTTONS_CONTINUE(),
		onClickOkCallback: function(context) {
			this.remove();

			var waitbox = new Mashup_Popup_Wait({ title: _.APP_PLUGINS_COPY_TITLE() }).show();

			var fromApp = this.getEl().find('select[name=fromApp]').val();

			$.ajax({
				type: 'POST',
				dataType: 'json',
				url: getCurrentPath() + '/checkPluginsSynchronization',
				data: 'fromApp=' + encodeURIComponent(fromApp),
				success: function(data, textStatus) {
					if (data.toAdd.length == 0 && data.toDelete.length == 0) {
						waitbox.remove();
						new Mashup_Popup_Alert({
							title: _.APP_PLUGINS_COPY_TITLE(),
							text: _.APP_PLUGINS_COPY_ALREADY_SYNCHRONIZED(mashup.applicationId, fromApp)
						}).show();
					} else {
						waitbox.remove();
						var syncBox = new Mashup_Popup_Confirm({
							text: '',
							buttonOkLabel: _.APP_PLUGINS_COPY_BUTTONS_SYNCHRONIZE(),
							onClickOkCallback: function(context) {
								this.remove();

								waitbox.show();

								$.ajax({
									type: 'POST',
									dataType: 'json',
									url: getCurrentPath() + '/synchronizePlugins',
									data: 'fromApp=' + encodeURIComponent(fromApp),
									success: function(data, textStatus) {
										waitbox.remove();
										window.mashupBuilder.doReloadAvailableComponents({
											showConfirmBox: data.isRestartNeeded,
											onReloadCallback: $.proxy(function() {
												_this.refreshPlugins();
												_this.checkUpdates();
											}, this)
										});
									},
									error: function(XMLHttpRequest, textStatus, errorThrown) {
										waitbox.remove();
										new Mashup_Popup_Alert({
											title: _.APP_PLUGINS_COPY_TITLE(),
											text: _.APP_PLUGINS_COPY_FAILED(),
											level: BoxLevel.ERROR
										}).show();
									}
								});

							}
						});

						var syncBoxContent = syncBox.getElContent();
						syncBoxContent.prepend('' +
							'<div class="popup-form-wrapper synchronize">' +
								'<div class="toInstall">' +
									'<strong>' + _.APP_PLUGINS_COPY_LABEL_DEPLOY_ON() + '</strong>' +
									'<ul style="margin-top:5px;">' +
									'</ul>' +
								'</div>' +
								'<div class="toDelete">' +
								'<strong>' + _.APP_PLUGINS_COPY_LABEL_DELETE_FROM() + '</strong>' +
									'<ul style="margin-top:5px;">' +
									'</ul>' +
								'</div>' +
							'</div>'
						);

						if (data.toAdd.length == 0) {
							syncBoxContent.find('.toInstall').remove();
						} else {
							for (var i = 0; i < data.toAdd.length; i++) {
								syncBoxContent.find('.toInstall ul').append('<li> - ' + data.toAdd[i] + '</li>');
							}
						}

						if (data.toDelete.length == 0) {
							syncBoxContent.find('.toDelete').remove();
						} else {
							for (var i = 0; i < data.toDelete.length; i++) {
								syncBoxContent.find('.toDelete ul').append('<li> - ' + data.toDelete[i] + '</li>');
							}
						}

						syncBox.show();
					}
				},
				error: function(XMLHttpRequest, textStatus, errorThrown) {
					waitbox.remove();
					var okBox = new Mashup_Popup_Alert({
						title: _.APP_PLUGINS_COPY_TITLE(),
						text: _.APP_PLUGINS_COPY_FAILED(),
						level: BoxLevel.ERROR
					});
					okBox.show();
				}
			});
		}
	});

	var selectHTML = '';
	for (var i = 0; i < window.otherApps.length; i++) {
		selectHTML += '<option value="' + window.otherApps[i] + '">' + window.otherApps[i] + '</option>';
	}

	confirmBox.getElContent().prepend('' +
		'<div class="popup-form-wrapper synchronize">' +
			'<p class="popup-form-input-wrapper">' +
				'<label class="popup-form-label">' + _.APP_PLUGINS_COPY_LABEL_SOURCE() + '</label>' +
				'<select name="fromApp">' + selectHTML + '</select>' +
			'</p>' +
		'</div>'
	);
	confirmBox.show();

	return false;
};

/**
 * User process to delete the given plugin
 *
 * @param pluginId
 * @returns {Boolean}
 */
Mashup_Application_Plugins.prototype.doDeletePlugin = function(pluginId) {
	if (this.isReadOnly() == true) {
		return false;
	}

	// application must be saved
	if (window.mashupBuilder.isUpdated() == true) {
		new Mashup_Popup_Alert({
			title: _.PLUGIN_DELETE_TITLE(),
			text: _.PLUGIN_DELETE_ERROR_UNSAVED(),
			level: BoxLevel.WARNING
		}).show();
		return;
	}

	// search for pages that use this plugin
	var plugin = window.mashupBuilder.plugins.getPlugin(pluginId),
		usage = window.mashupBuilder.plugins.getPluginUsage(plugin);

	// ask for user validation on plugin deletion
	if (usage.length > 0) {
		new Mashup_Popup_Prompt({
			title: _.PLUGIN_DELETE_TITLE(),
			text: _.PLUGIN_DELETE_USED_TEXT(plugin.name, usage),
			type: 'checkbox',
			label: _.PLUGIN_DELETE_CONFIRM(),
			buttonOkLabel: _.YES(),
			buttonCancelLabel: _.NO(),
			onClickOkCallback: function(context) {
				window.mashupBuilder.plugins.deletePlugin(plugin, this.getElPromptInput().is(':checked'));
				this.remove();
			}
		}).show();
	} else {
		new Mashup_Popup_Confirm({
			title: _.PLUGIN_DELETE_TITLE(),
			text: _.PLUGIN_DELETE_TEXT(plugin.name),
			buttonOkLabel: _.YES(),
			buttonCancelLabel: _.NO(),
			onClickOkCallback: function(context) {
				window.mashupBuilder.plugins.deletePlugin(plugin, false);
				this.remove();
			}
		}).show();
	}
};

/**
 * Returns the DOM element for the Mashup API
 *
 * @returns
 */
Mashup_Application_Plugins.prototype.getEl = function() {
	if (this.el == undefined) {
		var isReadOnly = this.isReadOnly() || window.mashupBuilder.isDevelopmentMode();
		this.el = $('' +
			'<div id="installed-plugins" class="workspace">' +
				'<div class="workspace-buttons-wrapper">' +
					'<span class="text-button refresh-plugin" name="doRefreshPlugin">' + _.APP_PLUGINS_REFRESH_BUTTON() +  '</span>' +
					(!isReadOnly ?
						('<span class="text-button synchronize-plugin" name="doSynchronizePlugins">' + _.APP_PLUGINS_COPY_BUTTON() +  '</span>' +
						'<span class="text-button upload-plugin" name="doUploadPlugin">' + _.APP_PLUGINS_UPLOAD_BUTTON() +  '</span>') : ''
					) +
				'</div>' +
				'<div class="list-plugins">' +
					'<table class="list-plugins-header">' +
						'<thead>' +
							'<tr>' +
								'<th class="plugin-name">' + _.APP_PLUGINS_HEADER_NAME() + '</th>' +
								'<th class="plugin-version">' + _.APP_PLUGINS_HEADER_VERSION() + '</th>' +
								'<th class="plugin-actions">' + _.APP_PLUGINS_HEADER_ACTIONS() + '</th>' +
							'</tr>' +
						'</thead>' +
					'</table>' +
					'<table class="list-plugins-content">' +
						'<tbody class="list-plugins-body">' +
						'</tbody>' +
				'</table>' +
				'</div>' +
			'</div>'
		);

		this.el.data('_this', this);
		this.el.on('click', $.proxy(this.handleEventOnClick, this));

		this.elList = this.el.find('.list-plugins-body');

		this.refreshPlugins();
		this.checkUpdates();
	}
	return this.el;
};

/**
 * Returns the DOM node that contains the plugin list
 *
 * @returns
 */
Mashup_Application_Plugins.prototype.getElList = function() {
	return this.elList;
};

/**
 * Returns the total number of components of the given plugin
 *
 * @param plugin
 * @returns
 */
Mashup_Application_Plugins.prototype.getPluginsComponentsCount = function(plugin) {
	return (
			plugin.widgets.length +
			plugin.feeds.length +
			plugin.securityProviders.length +
			plugin.mashupTriggers.length +
			plugin.feedTriggers.length +
			plugin.melFunctions.length +
			plugin.MUIMelFunctions.length +
			plugin.springControllers.length +
			plugin.jspTags.length +
			plugin.themes.length
		);
};

/**
 * Called when the page is opened
 */
Mashup_Application_Plugins.prototype.onOpen = function() {
	Mashup_Application_Plugins.superclass.onOpen.call(this);
	if (window.mashupBuilder.isDevelopmentMode()) {
		this.getPage().getMessages().setMessage('devmode', { message: _.APP_PLUGINS_DEVMODE_WARNING(), level: 'info', canClose: false });
	}
};

/**
 * Called when the page is closed
 */
Mashup_Application_Plugins.prototype.onClose = function() {
	Mashup_Application_Plugins.superclass.onClose.call(this);
	if (window.mashupBuilder.isDevelopmentMode()) {
		this.getPage().getMessages().removeMessage('devmode');
	}
};

/**
 * Refreshes the list of plugins in the DOM
 */
Mashup_Application_Plugins.prototype.refreshPlugins = function() {
	if (this.el == undefined) {
		return;
	}

	var plugins = window.mashupBuilder.plugins.getPlugins();
	if (plugins.length > 0) {

		var isReadOnly = this.isReadOnly() || window.mashupBuilder.isDevelopmentMode();

		plugins.sort(function(a, b) { return (a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1); });

		var tmp = 0;
		var listComponents = function(label, components) {
			components.sort(function(a, b) { return (a.toLowerCase() < b.toLowerCase() ? -1 : 1); });
			var html = '';
			for (var i = 0; i < components.length; i++) {
				html += '' +
				'<tr class="plugin-component ' + ((++tmp % 2) ? 'odd' : 'even') + '">' +
					'<td class="plugin-component-name">' + components[i]  + '</td>' +
					'<td class="plugin-component-description">' + label + '</td>' +
				'</tr>';
			}
			return html;
		};

		var pluginsHTML = '';
		for (var i = 0; i < plugins.length; i++) {
			var plugin = plugins[i];
			pluginsHTML += '' +
			'<tr><td>' +
				'<table class="plugin-wrapper plugin-' + hashCode($.sanitize(plugin.name)).toString(32) + '" data-pluginId="' + plugin.id + '"><tbody>' +
					'<tr class="plugin-header" name="doSlidePlugin">' +
						'<td class="plugin-name" name="doSlidePlugin">' +
							'<span class="plugin-name-value" name="doSlidePlugin">' + plugin.name + '</span>' +
							' - <span class="plugin-name-components" name="doSlidePlugin">' + _.APP_PLUGINS_NAME_LABEL_COMPONENTS(this.getPluginsComponentsCount(plugin)) + '</span>' +
						'</td>' +
						'<td class="plugin-version" name="doSlidePlugin">' + (plugin.version || '-') + ' </td>' +
						'<td class="plugin-actions">' +
							(isReadOnly ? '' :
							'<span class="icon icon-download-plugin" title="' + _.APP_PLUGINS_ACTIONS_DOWNLOAD_TITLE() + '" data-href="' + getCurrentPath() + '/downloadPlugin?pluginId=' + plugin.id + '&pluginName=' + plugin.name + '" name="doDownloadPlugin"></span>' +
							'<span class="icon icon-delete-plugin" title="' + _.APP_PLUGINS_ACTIONS_DELETE_TITLE() + '" name="doDeletePlugin"></span> '
							) +
						'</td>' +
					'</tr>' +
					'<tr>' +
						'<td colspan="3">' +
							'<div class="plugin-content">' +
								'<ul class="plugin-infos">' +
									'<li><span class="plugin-info-label">' + _.APP_PLUGINS_LABEL_ID() + '</span> ' + plugin.id + '</li>' +
									'<li><span class="plugin-info-label">' + _.APP_PLUGINS_LABEL_VERSION() + '</span> ' + (plugin.pluginVersion || '-') + '</li>' +
									'<li><span class="plugin-info-label">' + _.APP_PLUGINS_LABEL_DESCRIPTION() + '</span> ' + (plugin.description || '-') + '</li>' +
									'<li><span class="plugin-info-label">' + _.APP_PLUGINS_LABEL_AUTHOR() + '</span> ' + (plugin.author || '-') + '</li>' +
									'<li><span class="plugin-info-label">' + _.APP_PLUGINS_LABEL_COPYRIGHT() + '</span> ' + (plugin.copyright || '-') + '</li>' +
								'</ul>' +
								'<table class="plugin-components">' +
									'<thead class="plugin-components-header">' +
										'<tr>' +
											'<th class="plugin-component-name">' + _.APP_PLUGINS_HEADER_COMPONENT() + '</th>' +
											'<th class="plugin-component-description">' + _.APP_PLUGINS_HEADER_DESCRIPTION() + '</th>' +
										'</tr>' +
									'</thead>' +
									'<tbody>' +
										listComponents(_.APP_PLUGINS_LIST_WIDGET(), plugin.widgets) +
										listComponents(_.APP_PLUGINS_LIST_THEME(), plugin.themes) +
										listComponents(_.APP_PLUGINS_LIST_FEED(), plugin.feeds) +
										listComponents(_.APP_PLUGINS_LIST_SECURITY(), plugin.securityProviders) +
										listComponents(_.APP_PLUGINS_LIST_TRIGGERUI(), plugin.mashupTriggers) +
										listComponents(_.APP_PLUGINS_LIST_TRIGGERAPI(), plugin.feedTriggers) +
										// clone because $.merge is destructive
										listComponents(_.APP_PLUGINS_LIST_MEL(), $.merge(clone(plugin.melFunctions), plugin.MUIMelFunctions)) +
										listComponents(_.APP_PLUGINS_LIST_CONTROLLER(), plugin.springControllers) +
										listComponents(_.APP_PLUGINS_LIST_JSPTAG(), plugin.jspTags) +
									'</tbody>' +
								'</table>' +
							'</div>' +
						'</td>' +
					'</tr>' +
				'</tbody></table>' +
			'</td></tr>';
		}
		this.getElList().html(pluginsHTML);
	} else {
		this.getElList().html('<tr><td class="no-plugins" colspan="3">' + _.APP_PLUGINS_NONE_INSTALLED() + '</td></tr>');
	}
};

/**
 * Checks for updates on EXALEAD plugins
 */
Mashup_Application_Plugins.prototype.checkUpdates = function() {
	var plugins = window.mashupBuilder.plugins.getPlugins();
	var isReadOnly = this.isReadOnly() || window.mashupBuilder.isDevelopmentMode();

	if (plugins.length > 0 && !isReadOnly && window.mashupBuilder.services.plugins.isAlive()) {
		var params = [];
		for (var i = 0; i < plugins.length; i++) {
			if (plugins[i].name && plugins[i].pluginVersion) {
				params.push({
					id: plugins[i].name,
					version: plugins[i].pluginVersion
				});
			}
		}
		if (params.length > 0) {
			var _this = this;
			window.mashupBuilder.services.plugins.updates(params, {
				onSuccess: function(data) {
					if (data != undefined) {
						for (var i = 0; i < data.length; i++) {
							_this.getElList().find('.plugin-' + hashCode($.sanitize(data[i].title)).toString(32)).find('.plugin-actions').prepend('' +
								'<a href="' + data[i].detailsUrl + '" class="link plugin-update" target="_blank" title="' + _.APP_PLUGINS_UPDATE_AVAILABLE_TITLE() + '">' +
									_.APP_PLUGINS_UPDATE_AVAILABLE(data[i].plugin_version) +
								'</a>'
							);
						}
					}
				}
			});
		}
	}
};

/**
 * Redraws the whole page
 */
Mashup_Application_Plugins.prototype.redraw = function() {
	if (this.el != undefined) {
		var $backup = this.el;
		this.el = undefined;
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();
	}
};

/**
 * Remove this page from the DOM
 */
Mashup_Application_Plugins.prototype.remove = function() {
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};



/**
 * File: /resources/mashupBuilder/js/Application/Properties.js
 */
/**
 * Implementation of a page for the application properties
 *
 * @constructor
 * @param page
 * @returns
 */
Inherit(Mashup_Application_Properties, Mashup_Application_Abstract);
function Mashup_Application_Properties(page) {
	Mashup_Application_Properties.superclass.constructor.call(this, page, {
		id: 'properties',
		label: _.APP_MENU_ITEM_PROPERTIES(),
		group: _.APP_MENU_GROUP_GENERAL()
	});
}

/**
 * Initialize the parameters for the properties
 */
Mashup_Application_Properties.prototype.getContainers = function() {
	if (this.containers == undefined) {
		this.containers = [];

		var config = Mashup_Parameter_Factory.createInvertedConfigParameters(window.jsonMashupUI.parameters);
		var properties = Mashup_Parameter_Factory.createApplicationParameters();
		for (var i = 0; i < properties.length; i++) {
			var containerConfig = properties[i].container,
				parameters = properties[i].parameters;

			// displayed in a different page
			if (containerConfig.id == 'styles' || containerConfig.id == 'javascript' || containerConfig.id == 'theme') {
				continue;
			}

			var parameterContainer = new Mashup_Parameter_Container($.extend({
				showToggle: false,
				showLabel: true,
				parent: this,
				onChangeCallback: $.proxy(this.onUpdate, this),
			}, containerConfig));

			for (var j = 0; j < parameters.length; j++) {

				if (parameters[j].name == 'defaultHomePage') {
					parameters[j].inputs[0].type = 'Select';
					var pages = window.mashupBuilder.getPages();
					var options = new Array('');
					for (var k = 0; k < pages.length; k++) {
						options.push(pages[k].getName());
					}
					parameters[j].inputs[0].options.possibleValues = options;
				}

				parameterContainer.addParameter(new Mashup_Parameter(window.defaultJsonCustomComponentParameter['class'], $.extend(parameters[j], {
					width: this.getParameterWidth(),
					values: config[parameters[j].name] ? config[parameters[j].name] : []
				})));
			}

			this.containers.push(parameterContainer);
		}
	}
	return this.containers;
};

/**
 * Called when an input has been updated
 */
Mashup_Application_Properties.prototype.onUpdate = function() {
	// retrieve the new JSON
	var jsonParameters = this.getJson();

	// Update the homepage if needed
	var newDefaultHomePage = getJsonParameter(jsonParameters, 'defaultHomePage');
	window.mashupBuilder.setDefaultHomePage(newDefaultHomePage != null ? newDefaultHomePage.value : null);

	// Save new application settings
	window.mashupBuilder.jsonMashupUI.parameters = jsonParameters;
	window.mashupBuilder.onUpdateUIConfiguration();
};

/**
 * Returns the DOM element for this page
 *
 * @returns
 */
Mashup_Application_Properties.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="application-properties" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);

		// adds container
		var containers = this.getContainers();
		for (var i = 0; i < containers.length; i++) {
			this.el.append(containers[i].getEl());
		}

		// inject a document icon for the theme
		this.el.find('.parameter_cssTheme').find('.parameter-value-icons').append('' +
			'<span class="icon icon-documentation" title="' + _.PARAM_ACTIONS_TITLE_DOC() + '" name="doOpenDoc" data-chapter="SWITCH_THEME"></span>'
		);

		// inject a document icon for the reporting
		this.el.find('.parameter_reportingEnabled').find('.parameter-value-icons').append('' +
			'<span class="icon icon-documentation" title="' + _.PARAM_ACTIONS_TITLE_DOC() + '" name="doOpenDoc" data-chapter="REPORTING"></span>'
		);
	}
	return this.el;
};

/**
 * Remove page from the DOM
 */
Mashup_Application_Properties.prototype.remove = function() {
	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Return the JSON for the application properties
 *
 * @returns
 */
Mashup_Application_Properties.prototype.getJson = function() {
	var json = [];

	// retrieve this page JSON
	var containers = this.getContainers();
	for (var i = 0 ; i < containers.length; i++) {
		$.merge(json, containers[i].getJson());
	}

	// and merge it with js/css
	$.merge(json, this.getPage().getTab('styles').getJson());
	$.merge(json, this.getPage().getTab('javascript').getJson());

	return json;
};


/**
 * File: /resources/mashupBuilder/js/Application/Security.js
 */
/**
 * Implementation of a page for the application security
 *
 * @constructor
 * @param page
 * @returns
 */
Inherit(Mashup_Application_Security, Mashup_Application_Abstract);
function Mashup_Application_Security(page) {
	Mashup_Application_Security.superclass.constructor.call(this, page, {
		id: 'security',
		label: _.APP_MENU_ITEM_SECURITY(),
		group: _.APP_MENU_GROUP_GENERAL(),
		description: _.APP_SECURITY_PROVIDER_DESCRIPTION()
	});
}

/**
 * Returns the parameter container for the provider config
 */
Mashup_Application_Security.prototype.getProviderContainer = function() {
	if (this.providerContainer == undefined) {
		var securityProvider = window.mashupBuilder.security.getSecurityProvider();

		this.providerContainer = new Mashup_Parameter_Container({
			id: 'securityProvider',
			label: _.APP_SECURITY_PROVIDER_TITLE(),
			parent: this,
			showLabel: true,
			showToggle: false,
			showEmpty: false,
			onChangeCallback: function() {
				if (securityProvider != null) {
					securityProvider.parameters = this.getJson();
					window.mashupBuilder.security.setSecurityProvider(securityProvider);
					window.mashupBuilder.onUpdateSecurityProvider();
				}
			}
		});

		if (securityProvider == null) {

			this.providerContainer.addButton({
				name: 'doAddSecurityProvider',
				label: _.APP_SECURITY_BUTTON_ADD(),
				hideIfReadOnly: true
			});

		} else {

			var properties = Mashup_Parameter_Factory.createSecurityProviderParameters(securityProvider.className)[0];
			var config = Mashup_Parameter_Factory.createInvertedConfigParameters(securityProvider.parameters);

			this.providerContainer.setLabel(properties.container.label);
			this.providerContainer.setDescription(properties.container.description);

			for (var i = 0; i < properties.parameters.length; i++) {
				var parameter = properties.parameters[i];
				this.providerContainer.addParameter(new Mashup_Parameter(window.defaultJsonCustomComponentParameter['class'], $.extend(parameter, {
					width: this.getParameterWidth(),
					values: config[parameter.name] ? config[parameter.name] : []
				})));
			}

			this.providerContainer.addButton({
				name: 'doRemoveSecurityProvider',
				label: _.APP_SECURITY_BUTTON_REMOVE(),
				hideIfReadOnly: true
			});

		}
	}
	return this.providerContainer;
};

/**
 * Returns the parameter container for the pages selection
 */
Mashup_Application_Security.prototype.getPagesContainer = function() {
	if (window.mashupBuilder.security.hasSecurityProvider()) {
		if (this.pagesContainer == undefined) {
			this.pagesContainer = new Mashup_Parameter_Container({
				id: 'security-pages',
				label: _.APP_SECURITY_PAGES_TITLE(),
				description: _.APP_SECURITY_PAGES_DESCRIPTION(),
				parent: this,
				showLabel: true,
				showToggle: false
			});

			var pages = window.mashupBuilder.getPages();
			for (var i = 0; i < pages.length; i++) {
				var page = pages[i],
					pageName = page.getName();

				if (page.isVisible() == false || page.isReadOnly() || page.getName() == window.defaultLoginPage) {
					continue;
				}

				this.pagesContainer.addParameter(new Mashup_Parameter('', {
					name: pageName,
					label: pageName,
					width: this.getParameterWidth(),
					values: [page.isSecurityEnable() ? 'true' : 'false'],
					inputs: [{
						type: 'Checkbox',
						name: pageName,
						label: pageName,
						options: {
							possibleValues: ['false', 'true']
						}
					}],
					onChangeCallback: function() {
						if ((page = window.mashupBuilder.getPageClass(this.getName())) != null) {
							page.setIsSecurityEnable(this.getValue() == 'true');
							page.getUI().onUpdate();
						}
					}
				}));
			}
		}
		return this.pagesContainer;
	}
};

/**
 * jQuery onClick event callback
 *
 * @param e
 */
Mashup_Application_Security.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'doAddSecurityProvider':
			this.doAddSecurityProvider();
			e.stopPropagation();
			break;
		case 'doRemoveSecurityProvider':
			this.doRemoveSecurityProvider();
			e.stopPropagation();
			break;
		}
	}
};

/**
 * Returns the DOM element for the security configuration
 *
 * @returns
 */
Mashup_Application_Security.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="application-security" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);
		this.el.on('click', $.proxy(this.handleEventOnClick, this));

		if (!window.mashupBuilder.security.hasAvailableProvider()) {
			this.el.append('<p>' + _.APP_SECURITY_PROVIDER_NONE_AVAILABLE() + '</p>');
		} else {
			this.el.append(this.getProviderContainer().getEl());
			if ((pagesContainer = this.getPagesContainer()) != undefined) {
				this.el.append(pagesContainer.getEl());
			}
		}
	}
	return this.el;
};

/**
 * Refreshes the display of the tab
 */
Mashup_Application_Security.prototype.refresh = function() {
	// re-initialize the containers
	if (this.providerContainer != undefined) {
		this.providerContainer.remove();
		this.providerContainer = undefined;
	}
	if (this.pagesContainer != null) {
		this.pagesContainer.remove();
		this.pagesContainer = undefined;
	}

	// update the DOM display
	if (this.el != undefined) {
		var $backup = this.el;
		this.el = undefined;
		$backup.replaceWith(this.getEl());
	}
};

/**
 * User process to adds a new security provider
 */
Mashup_Application_Security.prototype.doAddSecurityProvider = function() {
	var confirmBox = new Mashup_Popup_Confirm({
		title:  _.APP_SECURITY_ADD_CONFIRM_TITLE(),
		text: _.APP_SECURITY_ADD_CONFIRM_TEXT(),
		width: 370,
		onOpenCallback: function(context) {
			this.getElContent().find('select').css({ width: 225, maxWidth: 225 });
			this.getElContent().find('.popup-form-label').css('width', 100);
		},
		onClickOkCallbackData: { _this: this },
		onClickOkCallback: function(context) {
			var securityProviderClass = this.getElContent().find('select').val();
			if (securityProviderClass.length > 0) {
				var securityProvider = createSecurityProviderComponent(securityProviderClass);
				window.mashupBuilder.security.setSecurityProvider(securityProvider);
				window.mashupBuilder.security.checkSecurity();
				window.mashupBuilder.onUpdateSecurityProvider();
				context.data._this.refresh();
			}
			this.remove();
		}
	});

	confirmBox.focus = function() {
		this.getElContent().find('select').focus();
	};

	confirmBox.getElContent().prepend('' +
		'<div class="popup-form-wrapper">' +
			'<p class="popup-form-input-wrapper">' +
				'<label class="popup-form-label" for="securityProviderClass">' + _.APP_SECURITY_ADD_CONFIRM_LABEL() + '</label>' +
				'<select name="securityProviderClass">' +
					'<option value=""></option>' +
					(function(providers) {
						var options = '';
						for (var i = 0; i < providers.length; i++) {
							options += '<option value="' + providers[i].className + '">' + providers[i].name + '</option>';
						}
						return options;
					}(window.mashupBuilder.security.getAvailableProviders())) +
				'</select>' +
			'</p>' +
		'</div>'
	);

	confirmBox.show();
};

/**
 * User process to remove a security provider
 */
Mashup_Application_Security.prototype.doRemoveSecurityProvider = function() {
	new Mashup_Popup_Confirm({
		title: _.APP_SECURITY_REMOVE_CONFIRM_TITLE(),
		text: _.APP_SECURITY_REMOVE_CONFIRM_TEXT(),
		onClickOkCallbackData: { _this: this },
		onClickOkCallback: function(context) {
			window.mashupBuilder.security.removeSecurityProvider();
			window.mashupBuilder.security.removePagesSecurity();
			window.mashupBuilder.security.checkSecurity();
			window.mashupBuilder.onUpdateSecurityProvider();
			context.data._this.refresh();
			this.remove();
		}
	}).show();
};

/**
 * Remove this page from the DOM
 */
Mashup_Application_Security.prototype.remove = function() {
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
	if (this.providerContainer != undefined) {
		this.providerContainer.remove();
		this.providerContainer = undefined;
	}
	if (this.pagesContainer != null) {
		this.pagesContainer.remove();
		this.pagesContainer = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Application/Styles.js
 */
/**
 * Implementation of a page for the application styles
 * 
 * @constructor
 * @param page
 * @returns
 */
Inherit(Mashup_Application_Styles, Mashup_Application_Abstract);
function Mashup_Application_Styles(page) {
	Mashup_Application_Styles.superclass.constructor.call(this, page, {
		id: 'styles',
		label: _.APP_MENU_ITEM_STYLES(),
		group: _.APP_MENU_GROUP_GENERAL(),
		description: _.APP_STYLES_DESCRIPTION()
	});
}

/**
 * Initialize the parameters for the styles
 */
Mashup_Application_Styles.prototype.getContainers = function() {
	if (this.containers == undefined) {
		this.containers = [];

		var config = Mashup_Parameter_Factory.createInvertedConfigParameters(window.jsonMashupUI.parameters);
		var properties = Mashup_Parameter_Factory.createApplicationParameters();
		for (var i = 0; i < properties.length; i++) {
			if (properties[i].container.id != 'styles') {
				continue;
			}

			var parameters = properties[i].parameters;
			for (var j = 0; j < parameters.length; j++) {
				var parameter = parameters[j];

				var parameterContainer = new Mashup_Parameter_Container({
					label: parameter.label,
					parent: this,
					showLabel: true,
					showToggle: false
				});

				parameterContainer.addParameter(new Mashup_Parameter(window.defaultJsonCustomComponentParameter['class'], $.extend(parameters[j], {
					width: 600,
					values: config[parameter.name] ? config[parameter.name] : [],
					showLabels: false,
					onChangeCallback: $.proxy(this.getPage().getTab('properties').onUpdate, this.getPage().getTab('properties'))
				})));

				this.containers.push(parameterContainer);
			}
		}
	}
	return this.containers;
};

/**
 * Returns the DOM element for this page
 * 
 * @returns
 */
Mashup_Application_Styles.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div id="application-styles" class="workspace">' +
			'</div>'
		);
		this.el.data('_this', this);

		var containers = this.getContainers();
		for (var i = 0; i < containers.length; i++) {
			this.el.append(containers[i].getEl());
		}
	}
	return this.el;
};

/**
 * Remove the page from the DOM
 */
Mashup_Application_Styles.prototype.remove = function() {
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}

	if (this.containers != undefined) {
		for (var i = 0; i < this.containers.length; i++) {
			this.containers[i].remove();
		}
		this.containers = undefined;
	}
};

/**
 * Return the JSON
 * 
 * @returns
 */
Mashup_Application_Styles.prototype.getJson = function() {
	var json = [];
	var containers = this.getContainers();
	for (var i = 0 ; i < containers.length; i++) {
		$.merge(json, containers[i].getJson());
	}
	return json;
};