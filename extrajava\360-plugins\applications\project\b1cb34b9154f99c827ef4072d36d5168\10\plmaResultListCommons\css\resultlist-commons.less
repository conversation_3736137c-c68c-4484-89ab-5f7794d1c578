@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/styles/responsive.less";
@import "../../plmaResources/css/styles/variables.less";

.draggable {
  cursor: grab;
}

.dragging {
  cursor: move;
}

.mashup .resultListHeaderWithToolbar {
  //border-left: 1px solid @cblock-border;
  border-bottom: 1px solid @cblock-border;
  //border: 1px solid @cblock-border;
  //box-shadow: -3px 6px 10px -6px @ctext-weak;
  z-index: 50;

  .headerTitle {
    font-size: var(--icons-size, 14px);
  }

  .headerToolbar {
    .display-description-button {
      cursor: pointer;

      span.icon.fonticon:after {
        content: '\e238';
        font-size: 12px;
      }

      .label {
        display: none;
      }

      .hidden {
        display: none;
      }
    }

    .sortContainer {
      position: relative;
      display: inline-flex;
      flex-direction: row-reverse;

      .sort-dropdown {
        display: none;
        position: absolute;
        width: 230px;
        background: white;
        border: 1px solid #e2e4e3;
        top: 28px;
        z-index: 100;

        .sortLinkOptions {
          display: flex;
          flex-direction: column;

          .sortOption {
            display: flex;
            padding: 8px 2px;
            font-size: 16px;
            border-bottom: 1px solid #e2e4e3;
            border-left: 5px solid #ffffff;

            &:hover {
              background: #f4f5f6;
              border-left: 5px solid #3d3d3d;

              &:not(.activeSort) {
                .sortLinkOrder {
                  .fonticon:first-child {
                    color: #3d3d3d;
                  }
                }
              }
            }

            &.activeSort {
              border-left: 5px solid #368EC4;

              .label {
                color: #368ec4;
                font-style: unset;
              }

              &:hover {
                .fonticon {
                  &:not(.selected) {
                    color: #3d3d3d;
                  }
                }
              }
            }

            .label {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              display: flow;
              line-height: 16px;
              flex-grow: 1;
              cursor: pointer;
              margin-right: 10px;
            }

            .sortText {
              flex-grow: 1;
              margin-left: 5px;


            }

            .sortLinkOrder {
              display: inline-flex;

              .fonticon {
                font-size: 14px;
                margin-right: 10px;
              }

              .fonticon:hover {
                cursor: pointer;
              }

              .selected {
                color: @clink;
              }
            }
          }

          .sortOption:last-child {
            border-bottom: none;
          }
        }
      }

      .action-sorting.active {
        color: @clink;
      }

      .action-sorting.active + .sort-dropdown {
        display: block;
      }

      .action-sorting:not(.active) + .sort-dropdown.hasSort {
        display: block;
        position: initial;
        padding: 2px 0px 0px 0px;
        width: max-content;
        border: unset;
        margin-right: 1px;
        margin-top: -5px;

        .sortLinkOptions {
          .sortOption {
            display: none;
          }

          .sortOption:not(.activeSort) {
            display: none;
          }
        }
      }
    }
  }
}