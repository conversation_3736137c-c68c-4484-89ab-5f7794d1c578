/**
 * Mashup Ajax Client for plma
 * @param _this is the DOM element where the event occured
 * @param options {
 * 		spinner: true/false
 * }
 * @returns {PlmaAjaxClient}
 */
function PlmaAjaxClient(_this, options) {
    this.options = $.extend({
        spinner: true
    }, options);

    this.$container = $(_this);
    if (this.$container.hasClass('wuid') == false) {
        this.$container = this._getParentWidget(this.$container);
    }
    if (this.$container.length == 0) {
        this.$container = $('body');
    }

    this.ajaxRequest = null;
    this.paths = {};
    this.eraseWidgets = [];
    this.headerValues = new BuildUrl();
    this.url = new BuildUrl(this.options.baseUrl ? this.options.baseUrl : window.location.search); // ?q=exalead
}

PlmaAjaxClient.prototype.addWidget = function (wuid, erase, recurse) {
    if (typeof (erase) == 'undefined') {
        erase = true;
    }

    /**
     * It happens that we don't want to recurse to construct full widget path,
     * sometimes, widget UID is sufficient, consider true as default value
     */
    if (typeof (recurse) == 'undefined') {
        recurse = true;
    }

    var cssIds = this._findWidget(wuid, this.$container, erase);

    if (recurse) {
        for (var i = 0; i < cssIds.length; i++) {
            var cssId = cssIds[i];
            if (this.paths[cssId] != undefined) {
                continue; // already registered
            }

            var path = this._findPath($('.' + cssId));
            if (path == null) {
                continue; // could not compute the widget path
            }
            this.paths[cssId] = path;

            if (erase == true) {
                this.eraseWidgets.push(cssId);
            }
        }
    } else if (cssIds.length > 0) {
        // Only consider widget ID as path but not full path....
        this.paths[cssIds[0]] = cssIds[0];
        if (erase == true) {
            this.eraseWidgets.push(cssIds[0]);
        }
    }

    return this;
};

/**
 * Get widgets from a page
 *
 * @param success A success callback function: function(theWidgets, theAppendScript)
 * @param fail A fail callback function: function()
 * @returns a JQuery ajax instance
 */
PlmaAjaxClient.prototype.getWidget = function (success, fail) {
    var _this = this;
    var s = success || $.noop;
    var f = fail || $.noop;

    this.ajaxRequest = $.ajax({
        type: this.options.method ? this.options.method : 'GET',
        url: this.getAjaxUrl().toString(),
        dataType: 'json',
        cache: false,
        success: function (data, textStatus) {
            if (data != null && data.widgets != null && data.widgets.length > 0) {
                s(data.widgets, data.appendScript);
            } else
                f();
        },
        error: f
    });
    return this.ajaxRequest;
};

/**
 * Existing key will be replaced
 * @param key
 * @param values
 * @returns {PlmaAjaxClient}
 */
PlmaAjaxClient.prototype.addParameters = function (key, values) {
    this.url.addParameters(key, values, true);
    return this;
};

/**
 * Existing key will be replaced
 * @param key
 * @param value
 * @returns {PlmaAjaxClient}
 */
PlmaAjaxClient.prototype.addParameter = function (key, value) {
    this.url.addParameter(key, value, true);
    return this;
};
/**
 * Add Technical Parameter in Header instead of URL.All the parameter should be added in "Query-Parameters".
 * @param key
 * @param value
 * @returns {PlmaAjaxClient}
 */
PlmaAjaxClient.prototype.addParameterInHeader = function (key, value) {
    this.headerValues.addParameter(key, value, false);
    return this;
};

PlmaAjaxClient.prototype._getHeader = function () {
    if (this.headerValues.params == null || $.isEmptyObject(this.headerValues.params)) {
        return {};
    } else {
        return {'Query-Parameters': this.headerValues.toString().replace('?', '').split('&')};
    }
};

PlmaAjaxClient.prototype.updateParameter = function (key, value) {
    this.url.addParameter(key, value, false);
    return this;
};

PlmaAjaxClient.prototype.setQueryString = function (queryString) {
    this.url.setQueryString(queryString, true); // setQueryString must override window.location.search, otherwise you can have unexpected duplicate parameters such as feedName.page when paginating
    return this;
};

PlmaAjaxClient.prototype.getAjaxUrl = function () {
    if (this.options.baseUrl) {
        // Base URL is set so only concat widget path or ID
        this.url.setUrl(this.options.baseUrl + "/" + this._getAllPath().join(','));
    } else {
        // Default behavior, replace path name by OOTB ajax query (for example '/app_name/page/page_name' -> '/app_name/ajax/page_name')
        this.url.setUrl(mashup.baseUrl + window.location.pathname.replace(/.*\/([^/]+)$/, "/ajax/$1") + "/" + this._getAllPath().join(','));
    }
    return this.url;
};

PlmaAjaxClient.prototype.update = function () {
    var _this = this;

    if ($.isEmptyObject(this.paths)) {
        throw 'No widget to update. The addWidget method must be call at least once.';
    }

//	if(this.getAjaxUrl().params['hide-spinner'] == undefined || this.getAjaxUrl().params['hide-spinner'][0] != 'true'){
    this.showSpinner();
//	}

    var buttonToCreateList = [];
    for (var i = 0; i < this.$container.length; i++) {
        if (this.$container[i].parentElement && this.$container[i].parentElement.parentElement && this.$container[i].parentElement.parentElement.parentElement && this.$container[i].parentElement.parentElement.parentElement.getClassList().contains('flipContainer')) {
//			var event = new Event('buildFlipButton');
            buttonToCreateList.push(this.$container[i].parentElement.parentElement.parentElement);
//			this.$container[i].parentElement.parentElement.parentElement.dispatchEvent(event);
        }
    }

    this.ajaxRequest = $.ajax({
        type: 'GET',
        url: this.getAjaxUrl().toString(),
        dataType: 'json',
        headers: this._getHeader(),
        cache: false,
        success: function (data, textStatus) {
            if (data != null) { // request was aborted
                for (var i = 0; i < data.widgets.length; i++) {
                    if (data.widgets[i].html && data.widgets[i].cssId) {
                        if (_this.eraseWidgets.indexOf(data.widgets[i].cssId) == -1) {
                            $('.' + data.widgets[i].cssId).append(data.widgets[i].html);
                        } else {
                            var $widget = $('.' + data.widgets[i].cssId);
                            // before replacing the widget, destroy it to prevent memory leaks
                            if ($widget.data('widget') && typeof $widget.data('widget').destroy === 'function') {
                                $widget.data('widget').destroy();
                            }
                            $widget.replaceWith(data.widgets[i].html);
                        }
                    }
                }
                //			$('#mainWrapper').append(data.executeLater);
                $('#mainWrapper').append(data.appendScript);
                if (_this.options.success && typeof (_this.options.success) == "function") {
                    _this.options.success.call(this, data, textStatus);
                }
                for (var j = 0; j < buttonToCreateList.length; j++) {
                    var event = new Event('buildFlipButton');
                    buttonToCreateList[j].dispatchEvent(event);
                }
            }
            _this.remove();
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            if (_this.options.error && typeof (_this.options.error) == "function") {
                _this.options.error.call(this, textStatus, errorThrown);
            }
            _this.remove();
        }
    });

    return this.ajaxRequest;
};

PlmaAjaxClient.prototype.updateInterval = function (refreshInterval) {
    var _this = this;
    return setInterval(function () {
        _this.update();
    }, refreshInterval);
};

PlmaAjaxClient.prototype.remove = function () {
    this.ajaxRequest.abort();
    if (this.options.spinner) {
        this.hideSpinner();
    }
};

PlmaAjaxClient.prototype.showSpinner = function () {
    for (var cssId in this.paths) {
        if (this.paths.hasOwnProperty(cssId)) {
            $('.' + cssId).showPLMASpinner({overlay: true});
        }
    }
};

PlmaAjaxClient.prototype.hideSpinner = function () {
    for (var cssId in this.paths) {
        if (this.paths.hasOwnProperty(cssId)) {
            $('.' + cssId).hidePLMASpinner();
        }
    }
};

/**
 * Returns a clean path that remove useless pathes
 * @returns
 */
PlmaAjaxClient.prototype._getAllPath = function () {
    var paths = [];
    for (var cssId in this.paths) {
        if (this.paths.hasOwnProperty(cssId)) {
            paths.push(this.paths[cssId]);
        }
    }
    return paths;
};

/**
 * Returns the parent widget
 * @param $node
 * @returns
 */
PlmaAjaxClient.prototype._getParentWidget = function ($node) {
    if ($node.hasClass('wuid') == true) {
        return $node.parent().closest('.wuid');
    } else {
        return $node.closest('.wuid');
    }
};

/**
 * Returns the unique css id
 * @param cssClass
 * @returns
 */
PlmaAjaxClient._getUCssId = function (cssClass) {
    if (cssClass != undefined) {
        var classes = cssClass.split(' ');
        for (var i = 0; i < classes.length; i++) {
            // dependency with UCssId.java
            var match = /[^_\ ]{8}_(?:[0-9]+_[^\ ]+)?/gi.exec(classes[i]);
            if (match != null) {
                return match[0];
            }
        }
    }
    return null;
};

/**
 * Recursive function
 * @param wuid
 * @param $node
 * @returns
 */
PlmaAjaxClient.prototype._findWidget = function (wuid, $node, erase) {
    var cssIds = [];

    var $widgets = $node.find('.wuid.' + wuid);
    if ($widgets.length > 0) {
        for (var i = 0; i < $widgets.length; i++) {
            cssIds.push(PlmaAjaxClient._getUCssId($($widgets[i]).attr('class')));
        }
    } else {
        // Legacy: $node is supposed to be a container, don't know why we try to lookup
        // within the parents....
        var $parentNode = $node.parent();
        if ($parentNode.length > 0) {
            return this._findWidget(wuid, $parentNode, erase);
        } else if ($node !== $(document)) {
            return this._findWidget(wuid, $(document), erase);
        }
    }

    return cssIds;
};

/**
 * Recursive function
 * @param $widget
 * @returns
 */
PlmaAjaxClient.prototype._findPath = function ($widget) {
    var $upperWidget = this._getParentWidget($widget);
    if ($upperWidget.length > 0) {
        var p = this._findPath($upperWidget);
        p += ':' + PlmaAjaxClient._getUCssId($widget.attr('class'));
        return p;
    } else {
        return PlmaAjaxClient._getUCssId($widget.attr('class'));
    }
};