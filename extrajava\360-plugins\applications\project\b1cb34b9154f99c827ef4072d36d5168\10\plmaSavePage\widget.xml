<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA Save Page" group="PLM Analytics/Favorites" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>Store page.</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/plmaButton/images/preview.png" alt="Preferred hit widget" />
        ]]>
	</Preview>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/savePage.js" />
	</Includes>

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO" />
	<SupportI18N supported="true">
		<JsKeys>
		</JsKeys>
	</SupportI18N>

	<OptionsGroup name="General">
		<Option id="label" name="Label" isEvaluated="true" arity="ONE">
			<Description>Specifies the label of the button. Briefly explains the purpose of the button (3 words max).</Description>
		</Option>
		<Option id="showLabel" name="Show label" arity="ONE">
			<Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>

		<Option id="paramsListMode" name="Parameter root list mode" arity="ONE">
			<Description>
				<![CDATA[
					Specifies the parameter list filtering mode.<br />
					For the 'Exclude' and 'Include' modes, the 'Parameter list' field allows to specify the params to exclude or include.<br />
					Leaving 'Parameter list' empty forces iteration over all params whatever the chosen mode.
				]]>
			</Description>
			<Values>
				<Value>No filtering</Value>
				<Value>Include</Value>
				<Value>Exclude</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('No filtering', ['paramsList'], [], true, false)</Display>
			</Functions>
		</Option>
		<Option id="paramsList" name="Parameter list by default" isEvaluated="true" arity="ZERO_OR_MANY">
			<Description>
				<![CDATA[
				  The specified params will be either the only ones included ('Include' mode) or the only ones excluded ('Exclude' mode).<br />
				  Leaving the param list empty iterates over all params.
				]]>
			</Description>
		</Option>

	</OptionsGroup>

	<DefaultValues displayName="Normal" previewUrl="">
		<DefaultValue name="label">Save Page</DefaultValue>
		<DefaultValue name="showLabel">false</DefaultValue>
		<DefaultValue name="cssDisableStyle">true</DefaultValue>
	</DefaultValues>
</Widget>
