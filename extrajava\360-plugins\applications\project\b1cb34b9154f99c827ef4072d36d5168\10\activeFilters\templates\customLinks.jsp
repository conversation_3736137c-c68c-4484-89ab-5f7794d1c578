<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>

<render:import parameters="customLinks"/>
<render:import parameters="extraCss" ignore="true"/>
<render:import parameters="uCssId"/>

<c:if test="${fn:length(customLinks) > 0}">
	<div class="linkContainer ${extraCss}">
		<c:forEach var="customLink" items="${customLinks}">
			<url:url var="url" value="${customLink.url}" keepQueryString="${customLink.keepQueryParams}"></url:url>
			<a class="customLink" data-url="${customLink.url}" data-keep="${customLink.keepQueryParams}">
				<c:if test="${not empty customLink.iconCss}">
					<span class="icon ${customLink.iconCss}" title="${customLink.label}"></span>
				</c:if>
				<c:if test="${not empty customLink.label}">
					<span class="label">${customLink.label}</span>
				</c:if>
			</a>
		</c:forEach>
		<render:renderScript position="READY">
			var activeFiltersCustomLink = new ActiveFiltersCustomLink('${uCssId}');
			activeFiltersCustomLink.init();
		</render:renderScript>
	</div>
</c:if>