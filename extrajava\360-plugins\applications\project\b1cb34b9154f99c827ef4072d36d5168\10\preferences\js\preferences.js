(function (window) {
	'use strict';

	var WIDGET_NAME = 'preferences';

	/**
	 * Callbacks to call when the widget is initialized.
	 * @type {jQuery.Callbacks}
	 */
	var initCallbacks = $.Callbacks('memory');

	/**
	 * Object defining a tab
	 * @typedef {Object} Tab
	 * @property {string} id - Tab identifier. Prevents from having identical tabs.
	 * @property {function} [onInit] - Function to be called when the tab is clicked for the first time.
	 * @property {string|jQuery} tab - CSS selector or jQuery object representing the tab.
	 * @property {string|jQuery} container - CSS selector or jQuery object representing the container.
	 * @property {boolean} [isInit=false] - Whether onInit has been called. Used internally.
	 */

	/**
	 * @type {Tab}
	 */
	var DEFAULT_TAB_OPTIONS = {
		onInit: $.noop,
		tab: $(),
		container: $()
	};

	var Preferences = function(uCssId, options) {
		this.uCssId = uCssId;
		this.widget = $('.' + this.uCssId);
		this.widget.data({
			widget: this
		});
		this.appConfig = options.config;
		var defaultOptions = {};
		this.options = $.extend({}, defaultOptions, options);
		this.tabs = [];

		this.init();
		// Initialization is complete, fire the callbacks.
		initCallbacks.fire(this);
	};
	
	Preferences.prototype.hasTab = function (tabId) {
		return this.tabs.some(function (currentTab) {
			return currentTab.id === tabId;
		});
	}
	/**
	 * Add a tab if it doesn't exist yet.
	 * @param {Tab} tab - Tab to be added
	 */
	Preferences.prototype.addTab = function (tab) {
		tab = $.extend({}, DEFAULT_TAB_OPTIONS, tab, {isInit: false});
		var isTabPresent = this.hasTab(tab.id);
		if (!isTabPresent) {
			this.tabs.push(tab);
			var $tab = $(tab.tab);
			var $container = $(tab.container);
			// If the jQuery element doesn't exist in the DOM, then add it. This happens when 
			// Tab is added externally
			if (!$.contains(this.widget[0], $tab[0])) {
				this.widget.find('.preference-tabs').append($tab);
				if(this.tabs.length == 1){ $tab.addClass('active'); } // In case No Default tab displayed
			}else{
				$tab.removeClass('hidden'); // Default tab created by widget, display it.
			}
			if (!$.contains(this.widget[0], $container[0])) {
				this.widget.find('.preference-config-flex-container').append($container);
				if(this.tabs.length == 1){ $container.addClass('active'); } // In case No Default tab displayed
			}else{
				$container.removeClass('hidden'); // Default tab created by widget, display it.
			}
			$tab.on('click', function () {
				this.closeAllTabs();
				if (!tab.isInit) {
					tab.onInit.call(this);
					tab.isInit = true;
				}
				$tab.add($container).addClass('active');
			}.bind(this));
		}
	};
	/**
	 * Provide the app and config name for external tab addition.
	 */
	Preferences.prototype.appName = function() {
		return this.options.appName;
	}
	Preferences.prototype.configName = function() {
		return this.options.configName;
	}

	Preferences.prototype.init = function () {
		// If the default configs present then only add the tabs.
		var showAll = !this.options.configureTabs;
		if(this.appConfig.facetDisplays && (showAll || this.options.tabList.includes('facetDisplay'))){
			this.addTab({
				id: 'facetDisplay',
				onInit: this.initFacetDisplay,
				tab: '.preference-facet-display-tab',
				container: '.preference-facet-display-container'
			});
		}
		if(this.appConfig.facetRanges && (showAll || this.options.tabList.includes('facetRange'))){
			this.addTab({
				id: 'facetRange',
				onInit: this.initFacetRange,
				tab: '.preference-facet-range-tab',
				container: '.preference-facet-range-container'
			});
		}
		if(this.appConfig.refineRanges && (showAll || this.options.tabList.includes('dateRange'))){
			this.addTab({
				id: 'dateRange',
				onInit: this.initDateRange,
				tab: '.preference-date-range-tab',
				container: '.preference-date-range-container'
			});
		}
		if(this.appConfig.menu && (showAll || this.options.tabList.includes('menu'))){
			this.addTab({
				id: 'menu',
				onInit: this.initMenu,
				tab: '.preference-menu-tab',
				container: '.preference-menu-container'
			});
		}

		// Lets call this functions from widgets only. Not globally available.
		//this.initResultListTab();
		//this.initHitDetailsTab();

		// the first tab is active
		if (this.tabs.length > 0) {
			$(this.tabs[0].tab).trigger('click');
		}
		
		// Remove 'close' button
		setTimeout($.proxy(function() {
			this.widget.closest('.plmalightbox-box.plma-light-box-pref').find('.lightbox-hide-button.fonticon.fonticon-cancel').remove();
		},this),0);
	};

	Preferences.prototype.reload = function () {
		var client = new PlmaAjaxClient(this.widget);
		client.addWidget(this.uCssId);
		client.update();
	};

	Preferences.prototype.initFacetDisplay = function(){
		var facetDisplay = new FacetDisplay(this.appConfig.facetDisplays.facetDisplay,this.uCssId, this.options);
		facetDisplay.init();
	};

	Preferences.prototype.initFacetRange = function(){
		var facetRange = new FacetRange(this.appConfig.facetRanges.setFacetRange,this.uCssId, this.options);
		facetRange.init();
	};

	Preferences.prototype.initDateRange = function(){
		var dateRange = new DateRange(this.appConfig.refineRanges.refineRange,this.uCssId, this.options);
		dateRange.init();
	};

	Preferences.prototype.initMenu = function(){
		var page = new PreferencePage(this.options.mainItems,this.uCssId,this.options);
		page.init();
	};

	Preferences.prototype.initLayout = function(){
		var layout = new PreferenceLayout(this.uCssId);
		layout.init();
	};

	Preferences.prototype.initResultListTab = function(){
		new PlmaResultListPrefTab({ prefObject: this });
	};

    Preferences.prototype.initHitDetailsTab = function(){
        new PlmaHitDetailsPrefTab({ prefObject: this });
    };

	Preferences.prototype.closeAllTabs = function () {
		this.tabs.forEach(function (tab) {
			$(tab.tab).add(tab.container).removeClass('active');
		});
	};

	Preferences.getMessage = function (code) {
		return mashupI18N.get(WIDGET_NAME, code);
	};

	Preferences.getInitCallbacks = function () {
		return initCallbacks;
	};

	window.Preferences = Preferences;
})(window);
