/*For Standalone popup*/
.plmalightbox .plmalightbox-box.hitdetails-preference {
  width: 100%;

  .plmalightbox-header {
    padding: 13px 10px;
    border-bottom: 1px solid #e2e4e3;
    width: 95%;
    align-self: center;
  }

  .plmalightbox-contentwrapper {
    overflow-y: auto;

    .hitdetails-preference-form {
      width: 100%;
    }
  }
}

/*Common(standalone/global) form styles */
.hitdetails-preference-form {
  position: relative;
  padding: 5px 10px;

  .header {
    padding: 10px 0px;

    .label {
      font-size: 2em;
      font-weight: bold;
      padding: 10px;
    }

    .description {
      font-size: 1.3em;
      font-style: italic;
    }
  }

  .groups-config {
    display: flex;
    row-gap: 5px;
    flex-direction: column;
    margin-bottom: 10px;

    .group {
      border: 1px solid;
      padding: 5px;

      .group-header {
        padding: 0px;
        height: 30px;
        cursor: move;

        &:hover {
          background-color: #f1f1f1;
        }

        span {
          line-height: 100%;
          vertical-align: middle;
        }

        span.icon {
          font-size: 18px;
          margin: 0px;
        }

        span.icon.move {
          font-size: 30px;
          margin-left: 0px;
        }

        & > .label {
          font-size: 2em;
          font-weight: bold;
          padding: 0px 10px;
        }

        & > .description {
          font-size: 1.3em;
          font-style: italic;
        }
      }

      &.ui-state-highlight {
        height: 50px;
        line-height: 50px;
      }

      &.ui-sortable-helper {
        background-color: white;
      }
    }
  }

  .fields-config {
    display: flex;
    flex-wrap: wrap;
    row-gap: 10px;
    margin-top: 5px;
    min-height: 28px;

    .field {
      display: flex;
      flex-basis: 100px;
      cursor: move;
      font-size: 1.4em;
      padding: 6px 5px;
      border: 1px solid #e2e4e3;
      margin: 0px 10px;

      &:hover {
        background-color: #f1f1f1;
      }

      &.ui-sortable-helper {
        background-color: white;
      }

      &.ui-state-highlight {
        width: 100px;
        line-height: 50px;
      }
    }
  }

  span.condition {
    float: right;
    flex-grow: 1;
    text-align: end;
    color: #00B8DE;
    position: relative;
    font-size: 12px;

    .condition-tooltip {
      font-family: entypo;
      display: none;
      position: absolute;
      top: 14px;
      right: 0px;
      border: 1px solid black;
      padding: 5px 10px;
      background: #EDF6EB;
      color: black;
      text-align: left;
      z-index: 1;
      flex-direction: column;
      row-gap: 5px;

      .expr {
        color: #0087A3;
      }
    }

    &.show-right .condition-tooltip {
      right: auto;
    }

    &:hover .condition-tooltip {
      display: inline-flex;
    }
  }

  .button-container {
    border: 1px solid #e2e4e3;
    height: 45px;

    .button {
      margin: 7px;
      cursor: pointer;
      color: #3d3d3d;
      background-color: #F1F1F1;
      font-size: 14px;
      border: 1px solid #b4b6ba;
      border-radius: 4px;
      line-height: 1.42857;
      padding: 5px 10px;
      float: right;
    }

    .save-button.active {
      background-color: #EDF6EB;
      border-color: #57B847;
      color: #57B847;
    }
  }
}

/*Styles for HitDetail Preferences Inside Global Preference Widget*/
.mashup.mashup-style .plma-preferences .preference-widget .preference-config .preference-config-flex-container {
  .preference-container.preference-hitdetails-container .button-container {
    margin: 0px;
    border: 1px solid #e2e4e3;

    .save-button.active {
      background-color: #EDF6EB;
      border-color: #57B847;
      color: #57B847;
    }
  }
}

.preference-hitdetails-container {
  .hitdetails-preference-form .header {
    display: none;
  }

  .hitdetails_list {
    overflow: auto;
    width: 100%;

    &.wrapped {
      width: 35%;
    }

    li {
      padding: 10px;
      background-color: #F1F1F1;
      border: 1px solid #D8D8F8;

      &.selected {
        border: 2px solid #42a2da;
        border-right: 0;
      }

      .hitHeader {
        display: flex;
        min-height: 23px;

        .hit-name {
          font-family: '3ds';
          font-weight: normal;
          color: #77797c;
          font-size: 16px;
          cursor: pointer;
          width: 30%;
          line-height: 23px;
        }
      }
    }
  }

  .hitdetails_details {
    width: 65%;
    overflow: auto;

    &.active {
      border: 2px solid #42a2da;
    }
  }
}

.plmalightbox-overlay.visible {
  pointer-events: none;
}