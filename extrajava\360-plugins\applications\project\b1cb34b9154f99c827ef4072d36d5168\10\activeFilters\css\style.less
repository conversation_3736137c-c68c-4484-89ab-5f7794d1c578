@import "../../plmaResources/css/styles/responsive.less";
@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/polyfills.less";

.mashup {
	@padding-small: (@line-height /4);
	@padding-med: (@line-height /2);

	.activeFilters {
		border-bottom: 1px solid @cblock-border;
		//border-top: 1px solid @cblock-border;
		width: 100%;

		.filterType {
			font-size: @m-font;
			line-height: @line-height;
			font-family: "3ds";
			color: @ctext-weak;
			padding: (@line-height /4) 0 0 (@line-height /2);
		}

		.feedFilters {
			border-top: 1px solid @cblock-border;

			&:first-child {
				border-top: none;
			}
		}

		.reset-btn {
			float: right;
			color: @ctext-weak;
			font-size: @m-font;

			&:hover {
				text-decoration: none;
				color: @ctext-bold;
			}
		}

		.no-filters {
			color: @ctext-weak;
			font-size: @xs-font;
			padding: (@line-height /2);
		}

		.linkContainer {
			font-size: @m-font;
		}

		.customLink {
			display: block;
			margin: @padding-med;
			white-space: nowrap;
			cursor: pointer;

			&:hover {
				color: @clink-hover;
				background-color: @clink-bg-hover;
			}

		}

		.custom-button {
			font-size: 25px;
			position: relative;
			top: 12px;
			cursor: pointer;

			&:hover {
				color: @clink-active;
			}

			&.active {
				color: @clink-active;
			}
		}

		&.compactMode {
			background-color: @cblock-bg;
			padding-top: 0;
			padding-bottom: 0;
			min-height: 40px;
			.display-flex();
			.flex-flow(row nowrap);

			.savedFilterContainer {
				border-left: 1px solid @cblock-border;
				border-right: 1px solid @cblock-border;
				border-top: 1px solid @cblock-border;
				background-color: #f1f1f1;
				position: relative;
				padding-left: 7px;
				display: flex;
				align-items: center;
				.flex(1 1 auto);

				&.locked {
					cursor: pointer;

					.saved-filters-label {
						font-size: 14px;
						height: 100%;
						display: flex;
						align-items: center;

						.saved-filters-save-label {
							display: none;
						}
					}

					.saved-filters-unlock-label {
						display: none;
					}

					.saved-filters-drop-container {
						display: none;
					}

					.saved-filter-lock-open {
						display: none;
					}

					&.right-lock {
						cursor: default;

						.activeFilter {
							cursor: default;
						}
					}

					&:not(.right-lock) {
						&:hover {
							background-color: #d5e8f2;
							align-items: flex-start;

							.saved-filters-label {
								opacity: 0;
							}

							.saved-filters-unlock-label {
								display: flex;
								align-items: center;
								position: absolute;
								width: 100%;
								height: 100%;
								font-size: 14px;

							}

							.saved-filter-elem-container {
								opacity: 0;
							}

							.saved-filter-lock-container {
								opacity: 0;

								.saved-filter-lock {
									color: @ctext;
								}
							}

						}
					}
				}

				&.unlocked {
					background-color: @clink-active;

					.saved-filters-label {
						font-size: 14px;
						color: white;
						height: 100%;
						display: flex;
						align-items: center;
						cursor: pointer;

						.saved-filters-main-label {
							display: none;
						}

						.saved-filters-save-label {
							flex: 1;
							min-width: 80px;
						}

						&:hover {
							color: @clink-bg-active;
						}
					}

					.saved-filters-unlock-label, .saved-filters-drop-container {
						display: none;
					}

					.saved-filter-lock {
						display: none;
					}

					.saved-filter-lock-open {
						color: white;
					}

					.saved-filter-trash {
						color: white;
						margin-right: 15px;
						cursor: pointer;

						&:hover {
							color: @clink-bg-active;
						}
					}

					&.drag-active {
						background-color: @clink;
						text-align: center;

						.saved-filters-save-label {
							opacity: 0;
						}

						.saved-filter-lock-open {
							opacity: 0;
						}

						.saved-filter-elem-container {
							opacity: 0;
						}

						.saved-filter-trash {
							opacity: 0;
						}

						.saved-filters-drop-container {
							display: block;
							position: absolute;
							width: 100%;

							.fonticon {
								color: white;
								font-size: 26px;
								position: relative;
								top: 3px;
								right: 5px;
							}

							.saved-filters-drop-label {
								font-size: 14px;
								color: white;

								&:before {
									color: white;
									font-size: 26px;
								}
							}
						}

						&.drop-zone-active {
							.saved-filters-drop-container {
								.fonticon {
									&:before {
										color: @clink-bg-active;
									}
								}
							}
						}
					}
				}

				.saved-filter-elem-container {
					display: flex;
					flex-wrap: wrap;
				}

				.saved-filter-trash {
					font-size: 13px;
					margin-left: 12px;
					color: #d1d4d4;
				}

				.saved-filter-lock {
					font-size: 17px;
					margin-left: 7px;
					color: #b1b4ba;
				}

				.saved-filter-lock-open {
					font-size: 17px;
					margin-left: 7px;
					color: #b1b4ba;
				}

				.activeFilter {
					&.inactive {
						position: relative;

						.fonticon-cancel {
							display: none;
						}
					}
				}
			}

			.widgetTitle {
				.flex(0 0 auto);
				align-self: center;
				margin-right: @line-height;
				font-size: @m-font;
				padding: @padding-med @padding-med @padding-med 0;
			}

			.activeFiltersContainer, .quickFiltersContainer {
				.flex(0 1 auto);
				align-self: center;
				margin-left: 7px;
				padding: 0;

				.activeFilter {
					&.ui-draggable-dragging {
						box-shadow: 0.5px 0.5px 1px 0.5px;
					}

					&.dragged {
						background-color: @cblock-border-alt;

						.facetName {
							opacity: 0;
						}

						.categoryName {
							opacity: 0;
						}

						.fonticon {
							opacity: 0;
						}
					}
				}
			}

			.feedFilters {
				.display-flex();
				.flex-flow(column nowrap);
				margin-bottom: (@line-height /2);
				border-top: none;
				border-left: 1px solid @cblock-border;
				padding: 0 @line-height 0 0;

				&:first-child {
					border: none;
				}

				&:hover {
					.feedName {
						color: @clink;
						.transition(@transition-quick, ~"color");
					}
				}
			}

			.feedName {
				.flex(0 0 auto);
				padding: 0 (@line-height /2);
				color: @ctext-weak;
				.transition(@transition-quick, ~"color");
			}

			.filtersWrapper {
				.display-flex();
				.flex-flow(row wrap);
				.align-items(center);
			}

			.reset-btn {
				float: none;
				.flex(0 0 auto);
				align-self: center;
				padding: @padding-med;
				margin: 0 @line-height;
			}

			.linkContainer {
				.flex(1 1 auto);
				text-align: right;
				.display-flex();
				justify-content: flex-end;

				&.align-start {
					.justify-content(flex-start);
					.flex-grow(0);
				}
			}

			.customLink {
				.flex(0 0 auto);

				&:hover {
					color: @clink;
					background-color: transparent;
				}
			}

			.quickFilters {
				display: flex;

				.ui-autocomplete {
					border-radius: 0px 0px 5px 5px;
					max-height: 200px;
					overflow-y: auto;
					/* prevent horizontal scrollbar */
					overflow-x: hidden;
					border: 1px solid #ddd;
					background: #fff;
					color: #333;

					.ui-autocomplete-not-item {
						padding: .2em .4em;
						margin: .8em 0 .2em;
						line-height: 1.5;
						color: #77797c;

						&.facet {
							font-weight: bold;
						}

						&.no-results {
							font-style: italic;
						}

						&.most-used-filters {
							font-style: italic;
							padding-top: 0;
							padding-bottom: 0;
						}

						.clear {
							float: right;
						}
					}

					.ui-menu-item {
						display: flex;
						justify-content: space-between;
						flex-direction: row-reverse;
						padding: inherit;
						color: #77797c;

						.categoryName, .exclude {
							padding: 3px 1em 3px .4em;
						}

						.categoryName {
							width: 100%;
						}

						.exclude {
							color: @cblock-border-alt;
							font-weight: bold;
							padding-right: 0.5em;
						}

						&.ui-state-focus {
							background: none;
							border: 1px solid white;

							.categoryName {
								background-color: @clink-active;
								color: white;
							}

							.exclude:hover + .categoryName {
								background-color: white;
								color: inherit;
							}
						}
					}
				}

				.quickFiltersContainer {
					display: flex;
					flex-flow: row wrap;

					.quickFilter {
						opacity: 0.5;

						.facetName {
							margin-right: 4px;
						}
					}
				}

				.quickFiltersContainer-droppable {
					&.highlight {
						width: 100px;
						margin: 3.5px;
						background-color: #d5e8f2;
						border: 1px dashed #78befa;
					}

					&.hover {
						background-color: #78befa;
						border: 1px dashed #42a2da;
					}
				}

				.quickFiltersButtons {
					height: 30px;
					margin-top: 4px;

					.quickFilters-suggest {
						border: none;
						background-color: #f4f5f6;
						padding: 7px;
						border-radius: 3px;

						&:focus {
							background-color: #f1f1f1;
						}
					}

					button {
						border: none;
						padding: 0;
						background: none;
						font-size: medium;

						&:hover {
							font-size: large;
							cursor: pointer;
						}

						&.quickFilters-cancel {
							color: #EA4F37;
						}

						&.quickFilters-apply {
							color: #57B847;
						}
					}
				}
			}
		}
	}

	/* Not including this in .activeFilters so the style is available to other widgets */

	.activeFiltersContainer {
		.display-flex();
		.flex-flow(row wrap);
		.align-items(center);
		padding: @padding-small @padding-small @padding-med @padding-small;
	}

	.activeFilter {
		border-radius: 3px;
		color: @ctext;
		.flex(0 0 auto);
		margin: @padding-small;
		padding: 9px 0 9px @padding-med;
		cursor: pointer;

		max-width: 500px;
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;

		.fonticon-cancel {
			&.hidden {
				display: none;
			}
		}

		.fonticon-drag-grip {
			font-size: 18px;
			width: 5px;
			position: relative;
			left: -8px;
			top: 2px;
		}

		&:hover {
			color: @ctext-inverted;
			text-decoration: none;

			.facetName {
				color: @ctext-inverted;
			}
		}

		&.selectedFilter {
			background-color: #d5e8f2;

			&:hover {
				background-color: #005686;
			}
		}

		&.zapFilter {
			background-color: #e2e4e3;

			.categoryName {
				text-decoration: line-through;
			}

			&:hover {
				background-color: @ctext;
				color: @ctext-inverted;
			}
		}

		&.excludedFilter {
			background-color: #fad4ce;
			padding-right: 5px;

			.categoryName {
				text-decoration: line-through;
			}

			&:hover {
				background-color: #CC092F;
				color: @ctext-inverted;
			}
		}

		&.inactiveFilter {
			background: none;
			border: 1px solid @cblock-border-alt;
			color: @cblock-border-alt;

			.facetName {
				color: @cblock-border-alt;
			}

			&:hover {
				background: none;
				border: 1px solid @ctext;
				color: @ctext;

				.facetName {
					color: @ctext;
				}
			}
		}

        &.catgroup{
            &.catgroup-first,&.catgroup-mid{
                &.selectedFilter{
                    &:after{ color: #d5e8f2; }
                    &:hover{
                        &:after{ color: #005686; }
                    }
                }
                &.zapFilter{
                    &:after{ color: #e2e4e3; }
                    &:hover{
                        &:after{ color: @ctext; }
                    }
                }
                &.excludedFilter {
                    &:after{ color: #fad4ce; }
                    &:hover{
                        &:after{ color: #CC092F; }
                    }
                }
                &.inactiveFilter {
                    &:after{ color: #f1f1f1; }
                    &:hover{
                        &:after{ color: @ctext; }
                    }
                }

            }
            &.catgroup-last,&.catgroup-mid{
                &.inactiveFilter{
                    margin-left:-2px;
                    border-left: none;
                }
            }

            &.catgroup-first,&.catgroup-mid{
                padding-right: 2px;
                padding-left: 3.5px;
                border-top-right-radius: 0px;
                border-bottom-right-radius: 0px;
                position: relative;
                overflow: visible;
                &:after{
                    //border-left: 15px solid;
                    border-top: 16px solid transparent;
                    border-bottom: 15px solid transparent;
                    display: inline-block;
                    content: '';
                    position: absolute;
                    right: -15px;
                    top: 0;
                    z-index: 5;
                }
            }
            &.catgroup-last,&.catgroup-mid{
                padding-left: 18px;
                border-top-left-radius: 0px;
                border-bottom-left-radius: 0px;
                .facetName{ display: none; }
                position: relative;
                overflow: visible;
                &:before{
                    color: white;
//                     border-left: 15px solid;
//                     border-top: 16px solid transparent;
//                     border-bottom: 15px solid transparent;
                    display: inline-block;
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                }
            }
            &.catgroup-first{
                margin-right:2px;
                .facetName{ margin-right:10px;}
            }
            &.catgroup-mid{
                margin-left:-2px;
                margin-right:2px;
            }
            &.catgroup-last{
                margin-left:-2px;
            }
        }
	}

	.facetName {
		color: @ctext-bold;
		font-weight: bold;
	}

	div.quickFilters-suggest {
		display: none !important;
	}

	/* FIXME: buttons disappear in IE when there are too much filters */

	&.ie {
		.quickFiltersButtons {
			flex-flow: row wrap;
		}
	}
}

.activeFilter.selectedFilter.similarRefinement {
	background-color: #005686;
	color: @ctext-inverted;

	span.facetName {
		color: @ctext-inverted;
	}
}


/* Medium devices  */
@media (max-width: @screen-sm-max) {
	.mashup {
		.activeFilters {
			.customLink {
				.icon {
					font-size: @l-font;
				}

				.label {
					display: none;
				}
			}
		}
	}
}

/* Small devices */
@media (max-width: @screen-xs-max) {
	.mashup {
		.activeFilters {
			.facetName {
				display: none;
			}

			&.compactMode {
				.widgetTitle {
					display: none;
				}

				.quickFilters {
					display: none;
				}
			}
		}
	}
}
