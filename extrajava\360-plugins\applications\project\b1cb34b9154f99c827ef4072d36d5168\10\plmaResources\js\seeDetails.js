/**
 * Used to go to the search page.
 * 
 * @param {String} currentPage - Name of the current page
 * @param {String} searchPage - Name of the search page
 * @param {String} feed - Name of the feed
 * @param {String} refine - Comma-separated list of facet IDs applied in the search page
 */
var SeeDetails = function (currentPage, searchPage, feed, refine, parameters) {
	this.currentPage = currentPage;
	this.searchPage = searchPage;
	this.feed = feed;
	this.refine = refine || '';
	this.parameters = parameters;
	this.url = new BuildUrl(window.location.href);
};

SeeDetails.prototype.createUrl = function () {
	this.url.path = this.url.path.replace(this.currentPage, this.searchPage);
	if (this.feed && this.refine) {
		var refines = this.refine.split(',');
		for (var i = 0; i < refines.length; i++) {
			this.url.addParameter(this.feed + '.r', refines[i], false);
		}
	}
	if (this.parameters && this.parameters.constructor === Object) {
		for (var i = 0; i < Object.keys(this.parameters).length; i++) {
			var key = Object.keys(this.parameters)[i];
			var value = this.parameters[key];
			this.url.addParameter(key, value, true);
		}
	}
	this.goToUrl();
};


SeeDetails.prototype.goToUrl = function () {
	this.url.removeParameter('pageId');
	// Manage history (non blocker on issue)
	$.ajax(mashup.baseUrl + '/pages/get', {
		data: {
			url: window.location.href
		},
		async: false,
		success: function(page) {
			BackButton.addHistoryEntry({
			 		url: window.location.href,
			 		label: page.label,
			 		icon: page.icon
			});
		}
	});

	window.location.assign(this.url.toString());
};


/**
 * TODO: remove this function later
 * For backwards compatibility purposes.
 */
SeeDetails.removeReferer = function() {
	if (typeof BackButton !== 'undefined') {
		BackButton.clearHistory();
	}
};
