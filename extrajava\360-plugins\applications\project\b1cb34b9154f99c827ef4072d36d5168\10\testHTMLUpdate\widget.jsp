<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import varWidget="widget" />

<config:getOption name="selector" var="selector" defaultValue=".test-html-div"/>


<widget:widget varUcssId="uCssId" varCssId="cssId">
    <div>
        <input type="text" id="htmlUpdateURL" value="/change/savedPages/admin/defect_details"/> <input type="button" id="htmlUpdateButton" value="SEND"/>
    </div>
    <div class="test-html-div"/>


    <render:renderScript position="READY">
        var options = {};
        options.selector = '${selector}';
        new TestHTMLUpdate('${uCssId}',options);
    </render:renderScript>
</widget:widget>