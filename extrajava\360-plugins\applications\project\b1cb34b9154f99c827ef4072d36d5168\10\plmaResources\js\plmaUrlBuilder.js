var PlmaUrlBuilder = function (url, options) {
    if (url != null) {
        this.urlBuilder = new BuildUrl(url);
    } else {
        this.urlBuilder = new BuildUrl(window.location.href);
    }
    this.options = options;
    this.init();
    return this;
};

PlmaUrlBuilder.prototype.init = function () {
    if (this.options.clearRefines) {
        this.options.feeds.forEach((feed) => {
            this.urlBuilder.removeParameter(feed + BuildUrl.REFINE_CATEGORY_PARAMETER);
            this.urlBuilder.removeParameter(feed + BuildUrl.CANCEL_CATEGORY_PARAMETER);
            this.urlBuilder.removeParameter(feed + BuildUrl.ZAP_CATEGORY_PARAMETER);
        })
    }
    var paramsToRemove = [];
    for (var key in this.urlBuilder.getParameters()) {
        if (!this.toKeep(key)) {
            paramsToRemove.push(key);
        }
    }
    paramsToRemove.forEach((p) => this.urlBuilder.removeParameter(p));
    for (var key in this.options.addParams) {
        this.urlBuilder.addParameter(key, this.options.addParams[key], false);
    }
}

PlmaUrlBuilder.prototype.addParameter = function (key, value, override) {
    this.urlBuilder.addParameter(key, value, override);
}

PlmaUrlBuilder.prototype.toKeep = function (param) {
    if (this.options.removeAllParams && this.options.keepParams.length == 0 && this.options.removeParams.length == 0) {
        return false;
    }
    return (this.options.keepParams.length == 0 || this.options.keepParams.includes(param)) && !this.options.removeParams.includes(param);
}

PlmaUrlBuilder.prototype.toString = function () {
    return this.urlBuilder.toString();
}