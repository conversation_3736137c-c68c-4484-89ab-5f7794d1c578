<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Collapsible block" group="PLM Analytics/Layout/Hide and show" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>A block you can collapse. The content is only loaded on demand, through Ajax refresh.</Description>

	
	<Includes>
        <Include type="css" path="css/style.less" />
        <Include type="js" path="js/collapsibleBlock.js" />
	</Includes>
	
	<SupportWidgetsId arity="ZERO_OR_MANY"/>
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportI18N supported="true"/>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<OptionsGroup name="General">
		<Option id="title" name="Title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>The widget title. If empty, no name is displayed.</Description>
		</Option>
        <Option id="collapseByDefault" name="Collapse by default" arity="ONE">
            <Description>Displays the widget as collapsed when the page is loaded.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>
		<Option id="saveState" name="Save State" arity="ONE">
            <Description>Saves the collapse state. Default false</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>
	</OptionsGroup>
	<OptionsGroup name="Display">
        <Option id="iconCss" name="Icon CSS" isEvaluated="true" arity="ONE">
            <Description>Specifies the CSS class name of the icon added to the button that is displayed when the widget is expanded.</Description>
        </Option>
        <Option id="iconCssCollapsed" name="Collapsed icon CSS" isEvaluated="true" arity="ZERO_OR_ONE">
            <Description>Specifies the CSS class name of the icon added to the button that is displayed when the widget is collapsed.</Description>
        </Option>
        <Option id="duration" name="Animation duration">
            <Description>The duration of the collapse animation, in milliseconds (ms).</Description>
            <Functions>
                <Check>isInteger()</Check>
            </Functions>
        </Option>
	</OptionsGroup>
    <OptionsGroup name="Ajax">
        <OptionComposite id="additionalParams" name="Additional parameters" arity="ZERO_OR_MANY">
            <Description>Additionnal URL parameters to be sent when the inner widgets are called through Ajax.</Description>
            <Option id="paramName" name="Name" arity="ONE">
                <Description>The name of the parameter.</Description>
                <Functions>
                    <ContextMenu>PageParameters()</ContextMenu>
                </Functions>
            </Option>
            <Option id="paramValue" name="Value" arity="ZERO_OR_ONE" isEvaluated="true">
                <Description>The value to send for this parameter. Can be a MEL expression.</Description>
            </Option>
        </OptionComposite>
        <Option id="addUsedFeeds" name="Add used feeds parameter" arity="ONE">
            <Description>Add 'use_page_feeds' parameter to query to filter executed feeds, it is taken into account if access trigger
                'Filter executed feeds' is present in page feeds configuration, it is a good and recommended approach to avoid configuring complex
                ignore feed triggers everywhere since feeds filtering trigger is on page level and manages correctly executed feeds.
                If depth is greater than 2 (ex: F1.F2.F3), it is recommended to de-activate this option and forge parameter manually because we only
                support parent feed ID and selected feeds for widget (generates for example 'F1.F2, F1.F3' with 'F1' parent feed ID and 'F2, 'F3'
                collapsible block selected subfeeds)
            </Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>
    </OptionsGroup>
	
	<DefaultValues>
        <DefaultValue name="displayMode">Single line</DefaultValue>
        <DefaultValue name="collapseByDefault">false</DefaultValue>
        <DefaultValue name="saveState">false</DefaultValue>
        <DefaultValue name="iconCss">fonticon fonticon-down-open</DefaultValue>
        <DefaultValue name="iconCssCollapsed">fonticon fonticon-left-open</DefaultValue>
        <DefaultValue name="duration">400</DefaultValue>
        <DefaultValue name="addUsedFeeds">true</DefaultValue>
	</DefaultValues>

</Widget>
