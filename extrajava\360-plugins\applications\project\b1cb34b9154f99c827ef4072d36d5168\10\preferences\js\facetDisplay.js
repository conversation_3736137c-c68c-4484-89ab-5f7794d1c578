var FacetDisplay = function (facetDisplays, uCssId, options) {
	this.facetDisplays = facetDisplays;
	this.uCssId = uCssId;
	var defaultOptions = {};
	this.options = $.extend({}, defaultOptions, options);
	this.widget = $('.' + this.uCssId);
	this.container = $('.' + this.uCssId + ' .preference-facet-display-container');
};

FacetDisplay.prototype.init = function () {
	this.initTitle();
	this.initTree();
	this.initButtons();
};

FacetDisplay.prototype.initTitle = function () {
	var titleBlock = $('<div class="preference-title"></div>');
	titleBlock.append($('<div class="main">' + Preferences.getMessage('plma.preference.facet.display.title') + '</div>'));
	titleBlock.append($('<div class="description">' + Preferences.getMessage('plma.preference.facet.display.description') + '</div>'));

	this.container.append(titleBlock);
};

FacetDisplay.prototype.initTree = function () {
	var blockContainer = $('<div class="preference-block-container"></div>');
	var facetDisplayContainer = $('<div class="preference-block"></div>');
	var facetsContainer = $('<div class="preference-elem-block preference-facets-block"></div>');
	for (var i = 0; i < this.facetDisplays.length; i++) {
		var facetDisplay = this.facetDisplays[i];
		var facetBlock = $('<div class="elem-block facet-block collapsed"></div>');
		var facetDescription = $('<div class="elem-description facet-description" data-id="' + _.escape(facetDisplay.id) + '"></div>');
		facetDescription.append($('<span class="elem-icon facet-icon fonticon ' + _.escape(facetDisplay.icon) + '"></span>'));
		facetDescription.append($('<span class="elem-label facet-label">' + _.escape(facetDisplay.id) + '</span>'));
		if (facetDisplay.categoryDisplay.length > 0) {
			var dropdownButton = $('<span class="elem-dropdown-icon facet-dropdown-icon fonticon fonticon-down-open"></span>');
			facetDescription.append(dropdownButton);
		}
		var trashIcon = $('<span class="trash-icon fonticon fonticon-trash"></span>');
		facetDescription.append(trashIcon);
		trashIcon.on('click', $.proxy(function (e) {
			$e = $(e.target).closest('.facet-block');
			this.deleteFacet($e);
		}, this));
		facetDescription.on('click', $.proxy(function (e) {
			$e = $(e.target);
			this.container.find('.facet-block').addClass('collapsed');
			$e.closest('.facet-block').removeClass('collapsed');
			this.hideFacetDetail();
			this.hideCategoryDetail();
			this.container.find('.selected').removeClass('selected');
			$e.closest('.facet-block').addClass('selected');
			this.displayFacetDetail();
		}, this));

		facetBlock.append(facetDescription);
		var facetCategories = $('<div class="elem-categories facet-categories"></div>');
		for (var j = 0; j < facetDisplay.categoryDisplay.length; j++) {
			var category = facetDisplay.categoryDisplay[j];
			var categoryDescription = $('<div class="category-description" data-id="' + _.escape(category.id) + '"></div>');
			if (category.icon && category.icon.indexOf('no-icon') === -1) {
				categoryDescription.append($('<span class="category-icon fonticon ' + _.escape(category.icon) + '" style="color: ' + _.escape(category.color) + '"></span>'));
			} else {
				categoryDescription.append($('<span class="category-icon no-icon" style="background-color: ' + _.escape(category.color) + '"></span>'));
			}
			categoryDescription.append($('<span class="category-label">' + _.escape(category.id) + '</span>'));
			var categoryTrash = $('<span class="category-trash-icon fonticon fonticon-trash"></span>');
			categoryTrash.on('click', $.proxy(function (e) {
				$e = $(e.target).closest('.category-description');
				this.deleteCategory($e);
			}, this));
			categoryDescription.append(categoryTrash);
			categoryDescription.on('click', $.proxy(function (e) {
				$e = $(e.target);
				this.hideFacetDetail();
				this.hideCategoryDetail();
				this.container.find('.selected').removeClass('selected');
				$e.closest('.category-description').addClass('selected');
				$e.closest('.facet-block').addClass('selected');
				this.displayCategoryDetail();
			}, this));
			facetCategories.append(categoryDescription);
		}
		var plusDescription = $('<div class="category-description plus-category"><span class="category-icon plus-icon fonticon fonticon-plus"></span></div>');
		plusDescription.on('click', $.proxy(function () {
			this.addCategory();
		}, this));
		facetCategories.append(plusDescription);
		facetBlock.append(facetCategories);
		facetsContainer.append(facetBlock);
	}
	var plusContainer = $('<div class="elem-block elem-block-plus facet-block collapsed"></div>');
	plusContainer.append($('<div class="elem-description facet-description"><span class="elem-icon facet-icon icon-plus fonticon fonticon-plus"></span></div>'));
	plusContainer.on('click', $.proxy(function () {
		this.addFacet();
	}, this));
	facetsContainer.append(plusContainer);

	facetDisplayContainer.append(facetsContainer);

	blockContainer.append(facetDisplayContainer);
	this.container.append(blockContainer);
};

FacetDisplay.prototype.initButtons = function () {
	/* Add Save / cancel buttons */
	var buttonsContainer = $('<div class="button-container"></div>');
	var closeButton = $('<span class="close-button">' + Preferences.getMessage('plma.preference.close') + '</span>');
	var resetButton = $('<span class="reset-button">' + Preferences.getMessage('plma.preference.reset') + '</span>');
	buttonsContainer.append(closeButton);
	buttonsContainer.append(resetButton);
	this.container.append(buttonsContainer);

	resetButton.on('click', $.proxy(function () {
		$.ajax({
			type: 'DELETE',
			url: this.options.url + 'config/user/facet/display/reset',
			data: {
				confName: this.options.configName,
				path: ''
			},
			success: $.proxy(function () {
				$.notify(Preferences.getMessage('plma.preference.save.success'), 'success');
				setTimeout($.proxy(function() {
					this.reload();
				},this),1000);
			}, this)
		});
	}, this));
	
	closeButton.on('click', $.proxy(function () {
		window.location.reload();
	}, this));
};

FacetDisplay.prototype.displayFacetDetail = function () {
	var facet = this.findFacet();
	if (!this.facetDetail) {
		this.initFacetDetail(facet);
	} else {
		this.updateFacetDetail(facet);
	}
	this.facetDetail.removeClass('hidden');
};

FacetDisplay.prototype.hideFacetDetail = function () {
	if (this.facetDetail) {
		this.facetDetail.addClass('hidden');
	}
};

FacetDisplay.prototype.initFacetDetail = function (facet) {
	if (!facet) {
		facet = {};
		facet.id = 'newFacet';
	}
	this.facetDetail = $('<div class="preference-detail preference-facet-detail hidden"></div>');
	this.facetDetail.append($('<div class="title-container">' + Preferences.getMessage('plma.preference.detail.facet.title') + '</div>'));
	this.facetDetail.append($('<div class="label-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.id') + ': </span><input class="label-input" type="text" value="' + facet.id + '"/></div>'));
	if (facet.icon) {
		this.facetDetail.append($('<div class="icon-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.icon') + ': </span><span class="icon-input fonticon ' + _.escape(facet.icon) + '"></span></div>'));
	} else {
		this.facetDetail.append($('<div class="icon-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.icon') + ': </span><span class="icon-input no-icon">' + Preferences.getMessage('plma.preference.detail.noicon') + '</span></div>'));
	}
	this.initIconContainer(this.facetDetail);
	this.facetDetail.find('.icon-container .icon-input').on('click', $.proxy(function (e) {
		this.toggleIconContainer(this.facetDetail);
	}, this));
	this.container.find('.preference-block-container').append(this.facetDetail);

	/* Listen to modifications */
	var timer = 0;
	this.facetDetail.find('.label-input').on('keyup', $.proxy(function (e) {
		this.successTemp();
		var inputValue = e.target.value;
		clearTimeout(timer);
		timer = setTimeout($.proxy(function () {
			this.editFacetTemp();
			this.container.find('.facet-block.selected .facet-label').text(inputValue);
		}, this), 2000);
	}, this));
	this.facetDetail.find('.fonticon-elem').on('click', $.proxy(function (e) {
		this.successTemp();
		$e = $(e.target);
		if (this.facetDetail.find('.icon-input.no-icon').length > 0) {
			this.facetDetail.find('.icon-input.no-icon').text('');
		}
		this.facetDetail.find('.icon-input').removeClass().addClass('icon-input').addClass($e.attr('class')).removeClass('fonticon-elem');
		this.container.find('.facet-block.selected .elem-icon').removeClass().addClass('elem-icon facet-icon').addClass($e.attr('class')).removeClass('fonticon-elem');
		this.editFacetTemp();
	}, this));
};

FacetDisplay.prototype.updateFacetDetail = function (facet) {
	if (!facet) {
		facet = {};
		facet.id = 'newCat';
	}
	this.facetDetail.find('.label-container .label-input')[0].value = facet.id;
	if (facet.icon) {
		this.facetDetail.find('.icon-container .icon-input').removeClass().addClass('icon-input fonticon ' + _.escape(facet.icon)).text('');
	} else {
		this.facetDetail.find('.icon-container .icon-input').removeClass().addClass('icon-input no-icon').text(Preferences.getMessage('plma.preference.detail.noicon'));
	}
};

FacetDisplay.prototype.displayCategoryDetail = function () {
	var category = this.findCategory();
	if (!this.categoryDetail) {
		this.initCategoryDetail(category);
	} else {
		this.updateCategoryDetail(category);
	}
	this.categoryDetail.removeClass('hidden');
};

FacetDisplay.prototype.hideCategoryDetail = function () {
	if (this.categoryDetail) {
		this.categoryDetail.addClass('hidden');
	}
};

FacetDisplay.prototype.initCategoryDetail = function (category) {
	if (!category) {
		category = {};
		category.id = 'newCat';
	}
	this.categoryDetail = $('<div class="preference-detail preference-facet-detail hidden"></div>');
	this.categoryDetail.append($('<div class="title-container">' + Preferences.getMessage('plma.preference.detail.category.title') + '</div>'));
	this.categoryDetail.append($('<div class="label-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.id') + ': </span><input class="label-input" type="text" value="' + category.id + '"/></div>'));
	if (category.icon) {
		this.categoryDetail.append($('<div class="icon-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.icon') + ': </span><span class="icon-input fonticon ' + _.escape(category.icon) + '"></span></div>'));
	} else {
		this.categoryDetail.append($('<div class="icon-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.icon') + ': </span><span class="icon-input no-icon">' + Preferences.getMessage('plma.preference.detail.noicon') + '</span></div>'));
	}
	this.initIconContainer(this.categoryDetail);
	this.categoryDetail.find('.icon-container .icon-input').on('click', $.proxy(function (e) {
		this.toggleIconContainer(this.categoryDetail);
	}, this));
	if (category.color) {
		this.categoryDetail.append($('<div class="color-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.color') + ': </span><span class="color-input" style="background-color: ' + _.escape(category.color) + '"></span></div>'));
	} else {
		this.categoryDetail.append($('<div class="color-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.color') + ': </span><span class="color-input no-color">' + Preferences.getMessage('plma.preference.detail.nocolor') + '</span></div>'));
	}
	this.initColorContainer(this.categoryDetail, category.color);
	this.categoryDetail.find('.color-container .color-input').on('click', $.proxy(function (e) {
		this.updateColorPicker($(e.target).css('background-color'));
		this.toggleColorContainer(this.categoryDetail);
	}, this));
	this.container.find('.preference-block-container').append(this.categoryDetail);

	/* Listen to modifications */
	var timer = 0;
	this.categoryDetail.find('.label-input').on('keyup', $.proxy(function (e) {
		this.successTemp();
		var inputValue = e.target.value;
		clearTimeout(timer);
		timer = setTimeout($.proxy(function () {
			this.editCategoryTemp();
			this.container.find('.category-description.selected .category-label').text(inputValue);
		}, this), 2000);
	}, this));
	this.categoryDetail.find('.fonticon-elem').on('click', $.proxy(function (e) {
		$e = $(e.target);
		if (this.categoryDetail.find('.icon-input.no-icon').length > 0) {
			this.categoryDetail.find('.icon-input.no-icon').text('');
			var color = this.container.find('.category-description.selected .category-icon').css('background-color');
			this.container.find('.category-description.selected .category-icon').css('color', color);
			this.container.find('.category-description.selected .category-icon').css('background-color', 'inherit');
		}
		this.categoryDetail.find('.icon-input').removeClass().addClass('icon-input').addClass($e.attr('class')).removeClass('fonticon-elem');
		this.container.find('.category-description.selected .category-icon').removeClass().addClass('category-icon').addClass($e.attr('class')).removeClass('fonticon-elem');
		this.editCategoryTemp();
	}, this));
	this.categoryDetail.find('.preference-color-tooltip .color').on('click', $.proxy(function (e) {
		$e = $(e.target);
		if (this.categoryDetail.find('.color-input.no-color').length > 0) {
			this.categoryDetail.find('.color-input.no-color').text('');
		}
		this.categoryDetail.find('.color-input').css('background-color', $e.css('background-color'));
		if (this.categoryDetail.find('.icon-input.no-icon').length > 0) {
			this.container.find('.category-description.selected .category-icon').css('background-color', $e.css('background-color'));
		} else {
			this.container.find('.category-description.selected .category-icon').css('color', $e.css('background-color'));
		}
		this.editCategoryTemp();
	}, this));
};

FacetDisplay.prototype.updateCategoryDetail = function (category) {
	if (!category) {
		category = {};
		category.id = 'newCat';
	}
	this.categoryDetail.find('.label-container .label-input')[0].value = category.id;
	if (category.icon) {
		this.categoryDetail.find('.icon-container .icon-input').removeClass().addClass('icon-input fonticon ' + category.icon).text('');
	} else {
		this.categoryDetail.find('.icon-container .icon-input').removeClass().addClass('icon-input no-icon').text(Preferences.getMessage('plma.preference.detail.noicon'));
	}
	if (category.color) {
		this.categoryDetail.find('.color-container .color-input').css('background-color', category.color).text('');
	} else {
		this.categoryDetail.find('.color-container .color-input').addClass('no-color').css('background-color', '').text(Preferences.getMessage('plma.preference.detail.nocolor'));
	}
};

FacetDisplay.prototype.findFacet = function () {
	var facet = {};
	if (this.container.find('.facet-block.selected').length > 0) {
		facet.id = this.container.find('.facet-block.selected .facet-description .facet-label').text();
		var classes = this.container.find('.facet-block.selected .facet-description .facet-icon').attr('class').split(' ');
		if (classes[classes.length - 1] !== 'null') {
			facet.icon = classes[classes.length - 2] + ' ' + classes[classes.length - 1];
		}
		return facet;
	}
	return undefined;
};

FacetDisplay.prototype.findCategory = function () {
	var category = {};
	if (this.container.find('.category-description.selected').length > 0) {
		category.id = this.container.find('.category-description.selected .category-label').text();

		if (!this.container.find('.category-description.selected .category-icon').hasClass('no-icon')) {
			var iconClasses = this.container.find('.category-description.selected .category-icon').attr('class').split(' ');
			category.icon = iconClasses[iconClasses.length - 2] + ' ' + iconClasses[iconClasses.length - 1];
			category.color = this.container.find('.category-description.selected .category-icon').css('color');
		} else {
			category.color = this.container.find('.category-description.selected .category-icon').css('background-color');
		}
		return category;
	}
	return undefined;
};

FacetDisplay.prototype.initIconContainer = function (container) {
	var icons = $(this.widget.find('.preference-icon-tooltip')[0]).clone();
	icons.addClass('hidden');
	icons.css('display', '');
	icons.find('.preference-icon-tooltip-input').on('keyup', function () {
		var searchValue = this.value;
		var iconList = this.parentElement.children;
		for(var i=1 ; i<iconList.length ; i++){
			var iconText = $(iconList[i]).data('icon');
			if(iconText.indexOf(searchValue.replace('fonticon ','')) !== -1){
				$(iconList[i]).removeClass('hidden');
			}else{
				$(iconList[i]).addClass('hidden');
			}
		}
	});
	container.append(icons);
};

FacetDisplay.prototype.toggleIconContainer = function (container) {
	container.find('.preference-icon-tooltip').toggleClass('hidden');
};

FacetDisplay.prototype.initColorContainer = function (container, color) {
	var colors = $(this.widget.find('.preference-color-tooltip')[0]).clone();
	colors.addClass('hidden');
	colors.css('display', '');
	this.createColorPicker(colors, color);
	container.append(colors);
};

FacetDisplay.prototype.toggleColorContainer = function (container) {
	container.find('.preference-color-tooltip').toggleClass('hidden');
};

FacetDisplay.prototype.updateColorPicker = function (color) {
	var rgb = {};
	rgb.r = color.substring(4, 7);
	rgb.g = color.substring(8, 11);
	rgb.b = color.substring(12, 15);
	this.colorPicker.setRgb(color);
};

FacetDisplay.prototype.createColorPicker = function (container, colorValue) {
	var divPickerWrapper = $('<div class="picker-wrapper"></div>');
	var divPicker = $('<div class="picker"></div>');
	var divPickerIndicator = $('<div class="picker-indicator"></div>');
	divPickerWrapper.append(divPicker);
	divPickerWrapper.append(divPickerIndicator);

	var divSliderWrapper = $('<div class="slider-wrapper"></div>');
	var divSlider = $('<div class="slider"></div>');
	var divSliderIndicator = $('<div class="slider-indicator"></div>');
	divSliderWrapper.append(divSlider);
	divSliderWrapper.append(divSliderIndicator);

	var colorPicker = $('<div class="color-picker-container"></div>');
	colorPicker.append(divSliderWrapper);
	colorPicker.append(divPickerWrapper);

	container.prepend(colorPicker);

	ColorPicker.fixIndicators(
		divSliderIndicator[0],
		divPickerIndicator[0]);

	this.colorPicker = ColorPicker(
		divSlider[0],
		divPicker[0],

		$.proxy(function (hex, hsv, rgb, pickerCoordinate, sliderCoordinate) {
			ColorPicker.positionIndicators(
				divSliderIndicator[0],
				divPickerIndicator[0],
				sliderCoordinate, pickerCoordinate
			);

			if (!isNaN(pickerCoordinate.x)) {
				this.categoryDetail.find('.color-input').css('background-color', hex);
				this.container.find('.category-description.selected .category-icon').css('color', hex);

				this.editCategoryTemp();
			}

		}, this));

	if (colorValue) {
		this.colorPicker.setHex(colorValue);
	}
};

FacetDisplay.prototype.editFacetTemp = function () {
	var realFacetId = this.container.find('.facet-block.selected .facet-description .facet-label').text();
	var newFacetId = this.facetDetail.find('.label-container .label-input')[0].value;
	var classes = this.facetDetail.find('.icon-container .icon-input').attr('class').split(' ');
	var facetIcon = 'fonticon ' + classes[classes.length - 1];
	$.ajax({
		type: 'POST',
		url: this.options.url + 'config/user/facet/display/edit',
		data: {
			appName: this.options.appName,
			confName: this.options.configName,
			id: realFacetId,
			facetId: newFacetId,
			facetIcon: facetIcon
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetDisplay.prototype.addFacetTemp = function () {
	var newFacetId = this.facetDetail.find('.label-container .label-input')[0].value;
	var classes = this.facetDetail.find('.icon-container .icon-input').attr('class').split(' ');
	var facetIcon = 'fonticon ' + classes[classes.length - 1];
	$.ajax({
		type: 'GET',
		url: this.options.url + 'config/user/facet/display/add',
		data: {
			appName: this.options.appName,
			confName: this.options.configName,
			facetId: newFacetId,
			facetIcon: facetIcon
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetDisplay.prototype.removeFacetTemp = function (facetId) {
	$.ajax({
		type: 'DELETE',
		url: this.options.url + 'config/user/facet/display/remove',
		data: {
			facetId: facetId
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetDisplay.prototype.editCategoryTemp = function () {
	var realFacetId = this.container.find('.facet-block.selected .facet-description .facet-label').text();
	var realCategoryId = this.container.find('.facet-block.selected .facet-categories .category-description.selected .category-label').text();
	var newCategoryId = this.categoryDetail.find('.label-container .label-input')[0].value;
	var classes = this.categoryDetail.find('.icon-container .icon-input').attr('class').split(' ');
	var categoryIcon = 'fonticon ' + classes[classes.length - 1];
	var categoryColor = this.categoryDetail.find('.color-container .color-input').css('background-color');
	$.ajax({
		type: 'PUT',
		url: this.options.url + 'config/user/category/display/edit',
		data: {
			confName: this.options.configName,
			id: realCategoryId,
			facetId: realFacetId,
			categoryId: newCategoryId,
			categoryIcon: categoryIcon,
			categoryColor: categoryColor
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetDisplay.prototype.addCategoryTemp = function () {
	var realFacetId = this.container.find('.facet-block.selected .facet-description .facet-label').text();
	var newCategoryId = this.categoryDetail.find('.label-container .label-input')[0].value;
	var classes = this.categoryDetail.find('.icon-container .icon-input').attr('class').split(' ');
	var categoryIcon = 'fonticon ' + classes[classes.length - 1];
	var categoryColor = this.categoryDetail.find('.color-container .color-input').css('background-color');
	$.ajax({
		type: 'POST',
		url: this.options.url + 'config/user/category/display/add',
		data: {
			confName: this.options.configName,
			facetId: realFacetId,
			categoryId: newCategoryId,
			categoryIcon: categoryIcon,
			categoryColor: categoryColor
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetDisplay.prototype.removeCategoryTemp = function (catId) {
	var realFacetId = this.container.find('.facet-block.selected .facet-description .facet-label').text();
	$.ajax({
		type: 'DELETE',
		url: this.options.url + 'config/user/category/display/remove',
		data: {
			confName: this.options.configName,
			facetId: realFacetId,
			categoryId: catId
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetDisplay.prototype.addFacet = function () {
	/* Add new facet */
	var newFacet = $('<div class="elem-block facet-block"></div>');
	var facetDescription = $('<div class="elem-description facet-description"></div>');
	facetDescription.append($('<span class="elem-icon facet-icon fonticon null"></span>'));
	facetDescription.append($('<span class="elem-label facet-label">' + (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) + '</span>'));
	var trashIcon = $('<span class="trash-icon fonticon fonticon-trash"></span>');
	facetDescription.append(trashIcon);
	trashIcon.on('click', $.proxy(function (e) {
		$e = $(e.target).closest('.facet-block');
		this.deleteFacet($e);
	}, this));
	newFacet.append(facetDescription);
	var facetCategories = $('<div class="elem-categories facet-categories"></div>');
	var plusDescription = $('<div class="category-description plus-category"><span class="category-icon plus-icon fonticon fonticon-plus"></span></div>');
	plusDescription.on('click', $.proxy(function () {
		this.addCategory();
	}, this));
	facetCategories.append(plusDescription);
	newFacet.append(facetCategories);

	var blockContainer = this.container.find('.preference-facets-block');
	blockContainer.find('.elem-block:last').before(newFacet);
	newFacet.addClass('selected');

	this.displayFacetDetail();

	this.addFacetTemp();

	facetDescription.on('click', $.proxy(function (e) {
		$e = $(e.target);
		$e.closest('.facet-block').toggleClass('collapsed');
		this.hideFacetDetail();
		this.hideCategoryDetail();
		this.container.find('.selected').removeClass('selected');
		$e.closest('.facet-block').addClass('selected');
		this.displayFacetDetail();
	}, this));
};

FacetDisplay.prototype.addCategory = function () {
	/* Add new category in facet */
	var newCategory = $('<div class="category-description"></div>');
	newCategory.append($('<span class="category-icon no-icon" style="color: null"></span>'));
	newCategory.append($('<span class="category-label">' + (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) + '</span>'));
	var categoryTrash = $('<span class="category-trash-icon fonticon fonticon-trash"></span>');
	categoryTrash.on('click', $.proxy(function (e) {
		$e = $(e.target).closest('.category-description');
		this.deleteCategory($e);
	}, this));
	newCategory.append(categoryTrash);

	var facetContainer = this.container.find('.facet-block.selected');
	facetContainer.find('.elem-categories .category-description:last').before(newCategory);
	this.hideFacetDetail();
	this.container.find('.selected').removeClass('selected');
	newCategory.addClass('selected');
	newCategory.closest('.facet-block').addClass('selected');
	this.displayCategoryDetail();

	if (facetContainer.find('.elem-dropdown-icon').length === 0) {
		facetContainer.find('.facet-description').append($('<span class="elem-dropdown-icon facet-dropdown-icon fonticon fonticon-down-open"></span>'));
	}

	this.addCategoryTemp();

	newCategory.on('click', $.proxy(function (e) {
		$e = $(e.target);
		this.hideFacetDetail();
		this.hideCategoryDetail();
		this.container.find('.selected').removeClass('selected');
		$e.closest('.category-description').addClass('selected');
		$e.closest('.facet-block').addClass('selected');
		this.displayCategoryDetail();
	}, this));
};

FacetDisplay.prototype.deleteFacet = function (elem) {
	elem.remove();
	this.container.find('.selected').addClass('collapsed');
	this.container.find('.selected').removeClass('selected');
	this.hideCategoryDetail();
	this.hideFacetDetail();

	this.removeFacetTemp(elem.find('.facet-label').text());
};

FacetDisplay.prototype.deleteCategory = function (elem) {
	elem.remove();
	this.container.find('.category-description.selected').removeClass('selected');
	this.hideCategoryDetail();

	this.removeCategoryTemp(elem.find('.category-label').text());
};

FacetDisplay.prototype.successTemp = function () {
	this.widget.find('.button-container .save-button').addClass('active');
	this.widget.find('.button-container .cancel-button').addClass('active');
};

FacetDisplay.prototype.failureTemp = function () {
	$.notify(Preferences.getMessage('plma.preference.save.error'), 'error');
};

FacetDisplay.prototype.reload = function () {
	// var client = new PlmaAjaxClient(this.widget);
	// client.addWidget(this.uCssId);
	// client.update();
	location.reload();
};
