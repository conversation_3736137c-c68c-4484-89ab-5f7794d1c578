<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds" />

<config:getOption name="buttonSelector" var="buttonSelector"/>
<config:getOption name="event" var="event"/>
<config:getOption name="showOverlay" var="showOverlay"/>
<config:getOption name="title" var="title" isJsEscaped="true"/>
<config:getOption name="enableDragging" var="enableDragging" defaultValue="false" />

<config:getOption name="onShow" var="onShow"/>
<config:getOption name="onHide" var="onHide"/>
<config:getOption name="onInit" var="onInit"/>

<config:getOption name="cssClass" var="cssClass"/>


<widget:widget extraCss="plmaLightboxWidget" varCssId="cssId" varUcssId="uCssId">
	<div class="plmaLightboxWidgetWrapper">
		<render:subWidgets/>
	</div>

</widget:widget>


<render:renderScript position="READY">
	(function(){

		var button = $('${buttonSelector}');
		if ($ && $.fn.plmalightbox){
			button.plmalightbox({
					container: $('#${cssId}').parent(),
					uCssId: '${uCssId}',
					showOverlay: ${showOverlay},
					content: $('#${cssId} .plmaLightboxWidgetWrapper'),
					title: <c:choose><c:when test="${not empty title}">"${title}"</c:when><c:otherwise>null</c:otherwise></c:choose>,
					extraCss: '${cssClass}',
					triggerEvent: '${event}',
					onInit: function(){
						   ${onInit}
						},
					onShow: function(){
						   ${onShow}
						},
					onHide: function(){
						   ${onHide}
						},
					draggable: ${enableDragging}
			});
		}
	})();
</render:renderScript>