/**
 * Alternative PDF generation using PDF-lib for better Chinese support
 * PDF-lib has native support for Unicode and custom fonts
 */

class PDFLibChineseGenerator {
    constructor() {
        this.pdfLib = null;
        this.customFont = null;
        this.loadPDFLib();
    }

    /**
     * Load PDF-lib library
     */
    async loadPDFLib() {
        try {
            // Load PDF-lib from CDN
            if (!window.PDFLib) {
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/pdf-lib@1.17.1/dist/pdf-lib.min.js';
                document.head.appendChild(script);
                
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                });
            }
            
            this.pdfLib = window.PDFLib;
            console.log('PDF-lib loaded successfully');
        } catch (error) {
            console.error('Failed to load PDF-lib:', error);
        }
    }

    /**
     * Create PDF with Chinese text support
     */
    async createPDFWithChineseText(content) {
        if (!this.pdfLib) {
            throw new Error('PDF-lib not loaded');
        }

        const { PDFDocument, rgb } = this.pdfLib;
        
        // Create a new PDF document
        const pdfDoc = await PDFDocument.create();
        
        // Embed a font that supports Chinese characters
        let font;
        try {
            // Try to embed custom font if available
            if (typeof font !== 'undefined') {
                // Convert base64 to Uint8Array
                const fontBytes = Uint8Array.from(atob(font), c => c.charCodeAt(0));
                font = await pdfDoc.embedFont(fontBytes);
            } else {
                // Fallback to Helvetica (won't show Chinese properly, but won't crash)
                font = await pdfDoc.embedFont('Helvetica');
            }
        } catch (error) {
            console.warn('Failed to embed custom font, using Helvetica:', error);
            font = await pdfDoc.embedFont('Helvetica');
        }

        // Add a page
        const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
        const { width, height } = page.getSize();

        // Draw text
        page.drawText(content.title || '标题', {
            x: 50,
            y: height - 50,
            size: 18,
            font: font,
            color: rgb(0, 0, 0),
        });

        page.drawText(content.body || '这是中文内容示例', {
            x: 50,
            y: height - 100,
            size: 12,
            font: font,
            color: rgb(0, 0, 0),
        });

        // Serialize the PDF
        const pdfBytes = await pdfDoc.save();
        return pdfBytes;
    }

    /**
     * Download PDF
     */
    downloadPDF(pdfBytes, filename = 'chinese-document.pdf') {
        const blob = new Blob([pdfBytes], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
    }

    /**
     * Example usage
     */
    async generateChinesePDF() {
        try {
            const content = {
                title: '中文PDF测试',
                body: '这是一个包含中文字符的PDF文档示例。PDF-lib库提供了更好的Unicode支持。'
            };

            const pdfBytes = await this.createPDFWithChineseText(content);
            this.downloadPDF(pdfBytes, 'chinese-test.pdf');
            
            console.log('Chinese PDF generated successfully');
        } catch (error) {
            console.error('Failed to generate Chinese PDF:', error);
        }
    }
}

// Usage example:
// const generator = new PDFLibChineseGenerator();
// generator.generateChinesePDF();

window.PDFLibChineseGenerator = PDFLibChineseGenerator;
