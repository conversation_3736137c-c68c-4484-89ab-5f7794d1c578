@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/responsive.less";
@import "../../plmaResources/css/styles/colorPreferences.less";

.mashup .chartboard {
	width: 100%;
	position: relative;
	> .widgetContent {
		height: 100%;
		.display-flex();
		align-items: stretch;
		.unauthorized-page {
			.empty-icon {
				font-size: 30px;
				margin-top: 25px;
				display: block;
			}
			.text {
				display: block;
				text-align: center;
				margin-top: 25px;
				font-size: 18px;
			}
		}
		.chartboard-popup-tinymce {
			display: none;
			&.visible {
				.display-flex();
			}
			position: absolute;
			top: 0;
			left: 0;
			background-color: rgba(0, 0, 0, 0.3);
			width: 100%;
			height: 100%;
			z-index: 10;
			align-items: center;
			.chartboard-popup-tinymce-popup {
				margin-left: 13%;
				width: 75%;
				height: 60%;
				.display-flex();
				background-color: white;
				min-height: 400px;
				max-height: 600px;
				max-width: 1200px;
				.mce-i-add-signature {
					font-family: entypo;
					font-size: 22px;
				}
				.mce-i-change-arrow {
					font-family: entypo;
					font-size: 22px;
					margin-right: 5px;
				}
				.chartboard-tinymce-template {
					.flex(1 0 0);
					flex-grow: 1;
					flex-basis: 0;
					border: 1px solid @cblock-border;
					overflow: auto;
					.chartboard-tinymce-title {
						height: 30px;
						font-size: 24px;
						margin-left: 15px;
						margin-top: 15px;
					}
					.chartboard-tinymce-template-elem {
						margin: 10px 20px 20px 20px;
						border: 1px solid @cblock-border;
						height: 100px;
						cursor: pointer;
						.title {
							display: block;
						}
						.text {
							display: block;
						}
						&.active {
							border: 1px solid @blue-3;
							-moz-box-shadow: 0 0 10px 1px @blue-3;
							-webkit-box-shadow: 0 0 10px 1px @blue-3;
							box-shadow: 0 0 10px 1px @blue-3;
							outline: 0;
							&.chartboard-tinymce-template-arrow {
								&:before {
									border-right-color: @blue-3;
								}
							}
						}
					}
					.chartboard-tinymce-template-title {
						.title {
							margin: 10px;
							padding-bottom: 12px;
							border-bottom: 1px solid @cblock-border;
							font-size: 22px;
						}
						.text {
							margin: 15px;
							padding-top: 10px;
							font-size: 13px;
						}
					}
					.chartboard-tinymce-template-note {
						background-color: @blue-0;
						font-size: 16px;
						line-height: 18px;
						.title {
							margin: 10px;
							font-size: 20px;
						}
						.text {
							margin: 15px;
							padding-top: 10px;
							font-size: 13px;
						}
					}
					.chartboard-tinymce-template-widget {
						border-top-left-radius: 3px;
						border-top-right-radius: 3px;
						.title {
							color: @grey-6;
							font-size: 13px;
							font-weight: bold;
							padding: 10px;
							border-bottom: 1px solid @cblock-border;
						}
						.text {
							margin: 10px;
							padding-top: 15px;
						}
					}
					.chartboard-tinymce-template-arrow {
						position: relative;
						&:after {
							right: 100%;
							top: 50%;
							border: solid transparent;
							content: " ";
							height: 0;
							width: 0;
							position: absolute;
							pointer-events: none;
							border-color: rgba(136, 183, 213, 0);
							border-right-color: white;
							border-width: 15px;
							margin-top: -15px;
						}
						&:before {
							right: 100%;
							top: 50%;
							border: solid transparent;
							content: " ";
							height: 0;
							width: 0;
							position: absolute;
							pointer-events: none;
							border-color: rgba(194, 225, 245, 0);
							border-right-color: @cblock-border;
							border-width: 16px;
							margin-top: -16px;
						}
						.title {
							font-size: 16px;
							margin: 10px;
						}
						.text {
							margin: 10px;
							padding-top: 15px;
						}
					}
					.chartboard-tinymce-template-material {
						.title {
							background-color: @blue-3;
							color: white;
							text-align: center;
							font-size: 16px;
							font-weight: bold;
							margin-top: 0px;
							padding-top: 10px;
							padding-bottom: 10px;
						}
						.text {
							margin: 10px;
							padding-top: 15px;
							color: @blue-3;
						}
					}
				}
				.chartboard-tinymce-container {
					.flex(4 0 0);
					flex-grow: 4;
					flex-basis: 0;
					border: 1px solid @cblock-border;
					.display-flex();
					flex-direction: column;
					.chartboard-tinymce-title {
						height: 20px;
						font-size: 24px;
						margin-left: 15px;
						margin-top: 15px;
						.mce-save-button {
							font-family: entypo;
							float: right;
							cursor: pointer;
							font-size: 28px;
						}
						.mce-close-button {
							font-family: entypo;
							float: right;
							cursor: pointer;
							font-size: 28px;
						}
					}
					.chartboard-tinymce-title-container {
						margin: 20px 15px 15px 15px;
						span {
							font-size: 18px;
						}
						input {
							font-size: 16px;
							width: 70%;
						}
					}
					.chartboard-tinymce-subtitle {
						margin: 30px;
						font-size: 20px;
						margin-top: 5px;
					}
					.chartboard-tinymce-editor-container {
						width: 90%;
						position: relative;
						.flex(1 0 0);
						flex-grow: 1;
						margin: 0px 15px 18px 15px;
					}
				}
			}
		}
		.mce-tinymce {
			z-index: 10;
			height: 100%;
			.mce-container-body.mce-stack-layout {
				height: 100%;
				display: flex;
				flex-direction: column;
				.mce-edit-area {
					flex-grow: 1;
				}
				.mce-toolbar-grp {
					.mce-stack-layout {
						width: 100%;
						left: 0;
					}
				}
			}
			.mce-edit-area {
				.display-flex();
				flex-direction: column;
				iframe {
					.flex(1 0 0);
					flex-grow: 1;
				}
			}
		}
		.chartboard-layout-container {
			.flex(1 0 auto);
		}
		.chartboard-layout {
			min-height: 150px;
			margin-top: @line-height;
			margin-bottom: @line-height;
		}

		.chartboard-sidebar {
			.flex(0 0 300px);
			border-left: 1px solid @cblock-border;
			position: relative;
			display: none;
		}

		.confirm-delete-page-popup-container {
			display: none;
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 20000;
			align-items: center;
			justify-content: center;
			.overlay {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 1000;
				background-color: rgba(0, 0, 0, 0.3);
			}
			.confirm-delete-page-popup-wrapper {
				background-color: #ffffff;
				box-shadow: 0px 1px 10px 2px rgba(0, 0, 0, 0.33);
				z-index: 1001;
				position: relative;
				border-radius: 5px;
				margin-bottom: 0;
				width: 500px;
				.title {
					line-height: 25px;
					font-size: 16px;
					text-align: center;
					margin-top: 10px;
				}
				.button-container {
					padding: 15px;
					text-align: center;
					.delete-page-button {
						margin: 10px;
					}
				}
			}
			&.active {
				display: flex;
			}
		}
		.share-popup-container {
			display: none;
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 20000;
			align-items: center;
			justify-content: center;
			.overlay {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 1000;
				background-color: rgba(0, 0, 0, 0.3);
			}
			.share-popup-wrapper {
				background-color: #ffffff;
				box-shadow: 0px 1px 10px 2px rgba(0, 0, 0, 0.33);
				z-index: 1001;
				position: relative;
				border-radius: 5px;
				margin-bottom: 0;
				width: 500px;
				.title {
					line-height: 40px;
					font-size: 16px;
					margin-left: 10px;
				}
				.page-owner {
					line-height: 40px;
					font-size: 13px;
					margin-left: 10px;
				}
				.share-save-button {
					position: absolute;
					right: 15px;
					bottom: 15px;
				}
				.share-popup-options {
					margin: 10px;
					.share-line {
						margin-bottom: 15px;
						display: flex;
						align-items: center;
						.label {
							flex: 1 0 0px;
							align-self: flex-start;
							line-height: 22px;
							font-weight: bold;
						}
						.users-input-container {
							position: relative;
							.add-button {
								background-color: #368ec4;
								color: white;
								height: 22px;
								width: 22px;
								position: relative;
								top: -3px;
								border-radius: 4px;
								margin-left: 5px;
								font-size: 15px;
								cursor: pointer;
								&:before {
									top: 4px;
									left: 0.5px;
									position: relative;
								}
							}
							.share-suggest-container {
								border: 1px solid #b4b6ba;
								background-color: white;
								z-index: 1000;
								position: absolute;
								top: 22px;
								padding: 4px;
								max-height: 200px;
								overflow-y: auto;
								.share-suggest-user {
									padding: 4px 0;
									cursor: pointer;
									.img-user-share {
										border-radius: 20px;
										position: relative;
										top: 6px;
										margin-right: 5px;
									}
                                    .icon-user{
                                        margin-right: 5px;
                                    }
                                    .icon-group{
                                        margin-right: 5px;
                                        color: #42a2da;
                                    }
									&:hover {
										color: #368EC4;
									}
								}
							}
						}
						.user-list {
							margin-top: 5px;
							margin-left: 5px;
							.user {
								display: flex;
								padding-right: 6px;
								cursor: pointer;
								.share-icon {
									margin-right: 7px;
									&:hover {
										color: #368EC4;
									}
								}
								.user-name {
									flex: 1;
									font-size: 13px;
								}
								&:hover {
									color: #368EC4;
								}
							}
							&.read-user-list {
								margin-bottom: 45px;
							}
						}
						.share-input {
							flex: 1 0 0px;
							color: #3d3d3d;
							line-height: 18px;
							padding-right: 14px;
							padding-left: 14px;
							border: 1px solid #b4b6ba;
							border-radius: 4px;
							padding-top: 1px;
							padding-bottom: 1px;
						}
						.share-select {
							align-self: flex-start;
							flex: 1 0 0px;
							display: flex;
							justify-content: center;
							align-items: center;
							.share-checkbox-input {
								display: none;
								&:checked {
									+.share-checkbox-label:before {
										border: 1px solid #368ec4;
										background: #368ec4;
									}
								}
							}
							.share-checkbox-label {
								position: relative;
								display: block;
								padding-left: 30px;
								cursor: pointer;
								vertical-align: middle;
								line-height: 22px;
								font-size: 15px;
								&:before {
									position: absolute;
									top: 0;
									left: 0;
									display: inline-block;
									width: 20px;
									height: 20px;
									content: '';
									border: 1px solid #c0c0c0;
									border-radius: 4px;
								}
								&:after {
									top: 3px;
									left: 8px;
									box-sizing: border-box;
									width: 6px;
									height: 12px;
									transform: rotate(45deg);
									border: 2px solid #fff;
									border-top: 0;
									border-left: 0;
									position: absolute;
									content: '';
								}
							}
						}
					}
				}

				.close-share-popup-button {
					position: absolute;
					top: 15px;
					right: 7px;
					font-size: 18px;
					cursor: pointer;
				}
			}
			&.active {
				display: flex;
			}
		}
	}

	.editable-custom-widget-icon {
		display: none;
	}

	&.searchWidget {
		.grid-stack {
			background-color: @cblock-bg;
		}
	}

	.grid-stack.chartboard-layout {

		.grid-stack-item {
			&.ui-state-disabled {
				opacity: 1;
				cursor: default;
				.ui-resizable-handle {
					opacity: 0;
					cursor: default;
				}
			}
			&.fullsize-item {
				z-index: 20000;
			}

			&.note-widget {
				.tile {
					background-color: #D5E8F2;
					font-size: 16px;
					line-height: 18px;
				}
			}

			&.title-widget {
				.tile {
					font-size: @xl-font;
					border-bottom: 1px solid @cblock-border;
					.content {
						line-height: @xl-font;
					}
				}
			}

			&.custom-widget {
				.tile {
					&:focus {
						color: @ctext;
					}
					.content {
						padding: @line-height (@line-height / 2) (@line-height / 2) (@line-height / 2);
					}
					.display-flex();
					flex-direction: column;
					overflow: visible;
					.chartboard-tinymce-template-title {
						padding: 0;
						.flex(1 0 0);
						height: 100%;
						overflow: auto;
						.editor-title {
							margin: 10px;
							padding-bottom: 12px;
							border-bottom: 1px solid @cblock-border;
							font-size: 30px;
						}
						.editor-text {
							margin: 15px;
							padding-top: 10px;
							font-size: 13px;
						}
					}
					.chartboard-tinymce-template-note {
						padding: 0;
						.flex(1 0 0);
						border: 1px solid @cblock-border;
						background-color: @blue-0;
						font-size: 16px;
						line-height: 18px;
						height: 100%;
						overflow: auto;
						.editor-title {
							margin: 10px;
							font-size: 20px;
							display: block;
						}
						.editor-text {
							margin: 15px;
							padding-top: 10px;
							font-size: 13px;
							display: block;
						}
					}
					.chartboard-tinymce-template-widget {
						padding: 0;
						.flex(1 0 0);
						border: 1px solid @cblock-border;
						border-top-left-radius: 3px;
						border-top-right-radius: 3px;
						height: 100%;
						overflow: auto;
						.editor-title {
							color: @grey-6;
							font-size: 13px;
							font-weight: bold;
							padding: 10px;
							border-bottom: 1px solid @cblock-border;
							display: block;
						}
						.editor-text {
							margin: 10px;
							padding-top: 15px;
							display: block;
						}
					}
					.chartboard-tinymce-template-arrow {
						padding: 0;
						.flex(1 0 0);
						border: 1px solid @cblock-border;
						overflow: auto;
						position: relative;
						height: 100%;
						&.no-color {
							.arrow-container {
								&.left {
									.arrow-relative-container {
										&:after {
											border-right-color: @grey-0;
										}
									}
								}
								&.top {
									.arrow-relative-container {
										&:after {
											border-bottom-color: @grey-0;
										}
									}
								}
								&.right {
									.arrow-relative-container {
										&:after {
											border-left-color: @grey-0;
										}
									}
								}
								&.bottom {
									.arrow-relative-container {
										&:after {
											border-top-color: @grey-0;
										}
									}
								}
							}
							&:after {
								border-right-color: @grey-0;
							}
						}
						.arrow-container {
							position: absolute;
							height: 100%;
							width: 100%;
							top: 0;
							.arrow-relative-container {
								position: relative;
								height: 100%;
								width: 100%;
								&:after {
									border: solid transparent;
									content: " ";
									height: 0;
									width: 0;
									position: absolute;
									pointer-events: none;
									border-color: rgba(136, 183, 213, 0);
									border-width: 15px;
								}
								&:before {
									border: solid transparent;
									content: " ";
									height: 0;
									top: 0;
									width: 0;
									position: absolute;
									pointer-events: none;
									border-color: rgba(194, 225, 245, 0);
									border-width: 16px;
								}
							}
							&.left {
								.arrow-relative-container {
									&:before {
										right: 100%;
										top: 50%;
										border-right-color: @grey-3;
										margin-top: -16px;
									}
									&:after {
										right: 100%;
										top: 50%;
										border-right-color: inherit;
										margin-top: -15px;
									}
								}
							}
							&.top {
								.arrow-relative-container {
									&:before {
										bottom: 100%;
										left: 50%;
										border-bottom-color: @grey-3;
										margin-left: -16px;
									}
									&:after {
										bottom: 100%;
										left: 50%;
										border-bottom-color: inherit;
										margin-left: -15px;
									}
								}
							}
							&.right {
								.arrow-relative-container {
									&:before {
										left: 100%;
										top: 50%;
										border-left-color: @grey-3;
										margin-top: -16px;
									}
									&:after {
										left: 100%;
										top: 50%;
										border-left-color: inherit;
										margin-top: -15px;
									}
								}
							}
							&.bottom {
								.arrow-relative-container {
									&:before {
										top: 100%;
										left: 50%;
										border-top-color: @grey-3;
										margin-left: -16px;
									}
									&:after {
										top: 100%;
										left: 50%;
										border-top-color: inherit;
										margin-left: -15px;
									}
								}
							}
						}
						.editor-title {
							font-size: 16px;
							margin: 10px;
							display: block;
						}
						.editor-text {
							margin: 10px;
							padding-top: 15px;
							display: block;
						}
					}
					.chartboard-tinymce-template-material {
						padding: 0;
						.flex(1 0 0);
						border: 1px solid @blue-3;
						overflow: auto;
						.editor-title {
							background-color: @blue-3;
							color: white;
							text-align: center;
							font-size: 16px;
							font-weight: bold;
							margin-top: 0px;
							padding-top: 10px;
							padding-bottom: 10px;
							display: block;
						}
						.editor-text {
							margin: 10px;
							padding-top: 15px;
							color: @blue-3;
							display: block;
						}
					}
				}
			}

			.grid-stack-hide-item {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				&.hidden {
					display: none;
				}
				/* IE fix to prevent clicks from triggering on underlying elements */
				background: url('/resources/widgets/chartboard/image/chartboard.PNG');
				opacity: 0;
			}

			&.custom-widget {
				//				height: 100%;
				.tile {
					height: 100%;
					> .wuid {
						padding: 0;
						margin: 0;
						&.full-size-active {
							padding: 50px;
						}
					}
				}
			}

			&.hidden {
				display: none;
			}
		}
		&.chartboard-layout {
			.grid-stack-item {
				.tile {
					&.grid-stack-item-content {
						.wuid {
							height: 100%;
							.display-flex();
							.flex-direction(column);
							.widgetContent {
								.flex(1);
							}
							&.plmaMetrics {
								display: block;
							}
						}
					}
					> .searchWidget {
						padding: 0;
						&.full-size-active {
							padding: 50px;
						}
					}
					.flipContainer {
						&.full-size-active {
							.searchWidget {
								padding: 50px;
							}
						}
					}
				}
			}
		}
	}

	.available-widgets {
		background: @cblock-bg-alt;
		width: 100%;
		padding: (@line-height/2) 0;
		.box-shadow(inset 3px 0 6px -2px @cblock-border-alt);
		.flex(1 0 0);
		overflow: auto;
		.available-widget {
			height: 180px;
			width: 260px;
			margin: @line-height auto;
			background: @cblock-bg;
			.box-shadow(0 0 6px 0px @cblock-border-alt);
			cursor: move;
			background-color: @blue-2;
			color: white;
			.display-flex();
			flex-direction: column;
			border: 1px solid #42a2da;
			&.with-preview {
				margin-bottom: 34px;
				.gridstack-elem-title-label {
					padding-top: 30px;
				}
			}
			&.hidden {
				display: none;
			}
			.gridstack-elem-title-label {
				position: relative;
				.flex(1 0 0px);
				font-weight: bold;
				font-size: 20px;
				padding-left: 5px;
				border-bottom: 1px solid white;
				line-height: 20px;
				max-height: 40px;
				width: 100%;
				text-align: center;
				padding-top: 5px;
				padding-bottom: 5px;
			}
			.icon-thumbnail {
				.flex(4 0 0px);
				position: relative;
				top: 40px;
			}
			.img-thumbnail {
				border: 1px solid #42a2da;
			}
		}
	}
	.gridstack-elem-title-label {
		font-size: 20px;
		font-weight: bold;
		padding-left: 20px;
		line-height: 20px;
	}
	.chartboard-sidebar {
		.search-bar-container {
			border-bottom: 1px solid @cblock-border;
			.search-input {
				border: 1px solid #e2e4e3;
				border-radius: 3px;
				padding: 0 0 0 5px;
				line-height: 26px;
				height: 26px;
				text-align: left;
				margin: 7px;
				background-color: white;
				input {
					border: none;
					width: 255px;
					height: 24px;
					line-height: 24px;
				}
				.fonticon {
					display: inline-block;
					line-height: 26px;
					height: 26px;
					vertical-align: top;
					position: absolute;
					right: 12px;
					font-size: 16px;
				}
			}
		}
		.avalaible-widgets {
			.flex(1 0 0);
		}
		.sidebar-block-title {
			.flex(0 0 20px);
			background-color: @cblock-bg;
			font-size: 18px;
			line-height: 18px;
			padding: 10px;
			border-top: 1px solid @cblock-border;
			border-bottom: 1px solid @cblock-border;
		}
		.trash-area {
			position: absolute;
			top: 0;
			height: 100%;
			width: 100%;
			background-color: @cblock-bg-alt;
			.justify-content(center);
			.align-items(flex-start);

			opacity: 0;
			display: none;
			.transition(@transition-normal, opacity);

			.trash-icon {
				height: 64px;
				width: 64px;
				line-height: 64px;
				font-size: 36px;
				color: @ctext;
				border: 1px solid @ctext;
				border-radius: 32px;
				margin-top: 50%;
			}
		}
		.add-custom-widgets {
			.flex(0 0 104px);
			.box-shadow(inset 3px 0 6px -2px @cblock-border-alt);
			background: @cblock-bg-alt;
			.custom-widget {
				opacity: 1;

				.tile {
					height: 100%;
					cursor: move;
					border: 1px solid @cblock-border;
					.box-shadow(0 0 6px 0px @cblock-border-alt);
					background-color: @cblock-bg;
					margin: (@line-height /2);

					.content {
						.display-flex();
						.flex-direction(column);
						align-items: center;
						justify-content: center;
						.custom-widget-title {
							font-size: 25px;
							.flex(1 0 0);
							line-height: 30px;
						}
						.custom-widget-icon {
							font-size: 30px;
							.flex(2 0 0);
							line-height: 60px;
						}

					}
				}
				.editable-custom-widget-icon {
					display: none;
				}
				.ui-resizable-handle {
					display: none;
				}
			}

			.grid-stack-item {
				@gridstack-columns: 2;

				&[data-gs-width='1'] {
					width: (100% / @gridstack-columns) * 1;
				}
				&[data-gs-x='1'] {
					left: (100% / @gridstack-columns) * 1;
				}
				&[data-gs-min-width='1'] {
					min-width: (100% / @gridstack-columns) * 1;
				}
				&[data-gs-max-width='1'] {
					max-width: (100% / @gridstack-columns) * 1;
				}

				&[data-gs-width='2'] {
					width: (100% / @gridstack-columns) * 2;
				}
				&[data-gs-x='2'] {
					left: (100% / @gridstack-columns) * 2;
				}
				&[data-gs-min-width='2'] {
					min-width: (100% / @gridstack-columns) * 2;
				}
				&[data-gs-max-width='2'] {
					max-width: (100% / @gridstack-columns) * 2;
				}
			}

		}
	}

	.preview {
		.display-flex();
		.align-items(center);
		.justify-content(center);

		.icon-thumbnail {
			font-size: 36px;
			margin-right: 20px;
		}
		.img-thumbnail {
			max-width: 100%;
			max-height: 100%;
		}
	}

	.chartboard-edit-toolbar {
		left: 0;
		position: absolute;
	}

	&.edit-active {
		background-color: @cblock-bg-alt;
		.flex(1 0 0);
		flex-grow: 1;
		flex-shrink: 0;
		flex-basis: 0;
		width: auto;
		overflow: hidden;
		.chartboard-layout-container {
			overflow: auto;
		}
		.chartboard-layout .tile {
			border: 1px solid @clink;
			.box-shadow(0 0 10px 1px @clink);
			outline: 0;
			cursor: move;
		}
		.chartboard-sidebar {
			.display-flex();
			.flex-direction(column);
		}

		.grid-stack-item {
			opacity: 0.5;
			&.focused {
				opacity: 1;
			}
			&.chartboard-tinymce-template-material {
				.editable-custom-widget-icon {
					color: white;
				}
			}
			.editable-custom-widget-icon {
				display: block;
				position: absolute;
				top: (@line-height / 2);
				right: (@line-height / 4);
				cursor: pointer;
				font-size: 15px;
			}
			&.custom-widget {
				.grid-stack-item-content.tile {
					.chartboard-tinymce-template-arrow.content {
						&.left {
							&:before {
								border-right-color: @blue-3;
							}
						}
						&.top {
							&:before {
								border-bottom-color: @blue-3;
							}
						}
						&.right {
							&:before {
								border-left-color: @blue-3;
							}
						}
						&.bottom {
							&:before {
								border-top-color: @blue-3;
							}
						}
					}
				}
			}
		}

	}

	&.dragging {
		.chartboard-sidebar {
			.trash-area {
				.display-flex();
				opacity: 0.5;

				.transition(@transition-normal, opacity);
			}
		}
	}
}

/* Control buttons */
.mashup .chartboard-edit-buttons {
	.display-flex();
	//width: 40px;
	.buttons-container {
		width: 100%;
		margin: 0;
		position: relative;
		cursor: pointer;
		font-size: var(--icons-size, @l-font);
		.chartboard-dropdown-button {
			position: absolute;
			right: 0;
			top: 30px;
			background-color: white;
			z-index: 100;
			border: none;
			-moz-box-shadow: 0 1px 6px 0px @ctext-weak;
			-webkit-box-shadow: 0 1px 6px 0px @ctext-weak;
			box-shadow: 0 1px 6px 0px @ctext-weak;
			min-width: 170px;
			max-height: 0;
			overflow: hidden;
			transition: 0.5s ease-in-out;
			&.active {
				max-height: 300px;
			}
			.button-container {
				width: 100%;
				font-size: 15px;
				cursor: pointer;
				font-family: Arial;
				color: #77797c;
				font-weight: normal;
				line-height: 40px;
				border-bottom: 1px solid @cblock-border;
				white-space: nowrap;
				text-align: left;
				&.disabled {
					cursor: not-allowed;
				}
				.button {
					color: #b4b6ba;
					font-size: 1.35em;
					width: 44px;
				}
				.label {
					color: @ctext;
				}
				&:hover {
					background-color: #f4f5f6;
					.button {
						color: @clink-active;
					}
					.label {
						color: @clink-active;
					}
				}
			}
		}
	}
}

/* Dialogs */
.mashup .chartboard-lightbox {
	padding: @line-height;
	font-size: @m-font;
	line-height: @line-height;
	width: 350px;

	.buttons {
		.display-flex();
		.justify-content(space-around);
		margin-top: @line-height;
	}

	.box-button {
		padding: (@line-height /2) @line-height;
		.flex(0 0 auto);
		text-align: center;
		border: 1px solid @cblock-border;
		border-radius: 3px;
		cursor: pointer;
	}
}

.grid-stack-item {
	&.full-size-active {
		z-index: 501;
	}
}

.pageTitleContainer {
	position: relative;
}

.chartboard-edit-toolbar {
	height: 50px;
	width: 100%;
	border-bottom: 1px solid @cblock-border;
	background: #f4f5f6;
	z-index: 600;
	position: absolute;
	top: 0;
	transform: rotate3d(1, 0, 0, -90deg);
	transition: 0.5s ease-in-out;
	top: -50px;
	transform: rotate3d(1, 0, 0, -90deg) translateY(50px);
	&.active {
		transform: translateY(50px);
	}
	.modified {
		display: none;
	}
	&.layout-modified {
		.toolbar-layout-container {
			.value {
				color: @clink;
			}
		}
		.modified {
			display: inline-block;
			color: @clink;
			font-weight: bold;
			vertical-align: top;
			padding-right: 8px;
			position: relative;
			top: -10px;
		}
		.save-as-button {
			display: inline-block;
			color: @clink;
		}
	}
	.chartboard-popup {
		position: absolute;
		left: 0;
		top: 50px;
		background-color: @cblock-bg-alt;
		padding: 0;
		overflow: hidden;
		max-height: 0;
		transition: 0.5s ease-in-out;
		border: none;
		&.active {
			border: 1px solid @cblock-border;
			&.chartboard-create-page-popup {
				padding: 10px;
				max-height: 500px;
				min-width: 300px;
				border-left: 2px solid @clink-active;
			}
			&.chartboard-create-layout-popup {
				padding: 10px;
				max-height: 500px;
				min-width: 300px;
				border-left: 2px solid #fd872bad;
			}
			&.chartboard-choose-layout-popup {
				max-height: 250px;
				min-width: 300px;
				border-left: 2px solid @clink-active;
			}
		}
		&.chartboard-choose-layout-popup {
			padding: 0;
			overflow: auto;
		}
		&.chartboard-create-page-popup {
			overflow: auto;
		}
		.input-container {
			.display-flex();
			&.description-container, &.icon-container {
				margin-top: 10px;
			}
			.label {
				border: 1px solid @clink-active;
				border-radius: 3px;
				border-top-right-radius: 0;
				border-bottom-right-radius: 0;
				background-color: @clink-active;
				color: white;
				margin: 0;
				font-size: 14px;
				.display-flex();
				justify-content: center;
				align-items: center;
				height: 26px;
				width: 25px;
				&.fonticon-info {
					height: 54px;
				}
			}
			.input {
				flex: 4;
				margin: 0;
				border: 1px solid @clink-active;
				border-radius: 3px;
				border-top-left-radius: 0;
				border-bottom-left-radius: 0;
				padding: 0 0 0 5px;
				line-height: 26px;
				height: 26px;
				text-align: left;
				&.input-description {
					height: 54px;
				}
				&.empty {
					border-color: #EA4F37;
				}
				&.input-icon {
					height: 40px;
					flex: 0 0 40px;
					font-size: 25px;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 0;
					background-color: white;
					cursor: pointer;
					&:hover {
						color: @clink-active;
					}
				}
			}
			&.icon-container {
				.label {
					height: 40px;
				}
				.chartboard-icon-list {
					border: 1px solid @clink-active;
					padding: 5px;
					margin-left: 10px;
					position: relative;
					max-width: 300px;
					&:after, &:before {
						right: 100%;
						top: 20px;
						border: solid transparent;
						content: " ";
						height: 0;
						width: 0;
						position: absolute;
						pointer-events: none;
					}
					&:before {
						border-color: rgba(136, 183, 213, 0);
						border-right-color: @cblock-bg-alt;
						border-width: 10px;
						margin-top: -10px;
						z-index: 10;
					}
					&:after {
						border-color: rgba(194, 225, 245, 0);
						border-right-color: @clink-active;
						border-width: 11px;
						margin-top: -11px;
					}
					.chartboard-icon-list-input {
						display: block;
						margin-bottom: 5px;
						width: 94%;
						border-radius: 3px;
						padding-left: 10px;
						border: 1px solid @cblock-border;
						line-height: 26px;
					}
					.fonticon-elem {
						font-size: 14px;
						margin: 0.2em;
					}
				}
			}
		}
		.chartboard-create-page-save-container, .chartboard-create-layout-save-container {
			margin-top: 10px;
			.save-container {
				float: right;
				border: 1px solid @clink-active;
				padding: 5px;
				border-radius: 3px;
				background-color: white;
				cursor: pointer;
			}
		}
		.chartboard-choose-layout {
			.display-flex();
			align-items: center;
			padding: 10px;
			border-bottom: 1px solid @cblock-border;
			cursor: pointer;
			min-width: 200px;
			.info-layout-container {
				flex: 1;
				.label {
					font-size: 16px;
					line-height: 16px;
					display: block;
					max-width: 300px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
				.description {
					margin-left: 20px;
					color: @ctext-weak;
					display: block;
					max-width: 300px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
			.layout-page-container {
				flex: 1;
				.display-flex();
				flex-wrap: wrap;
				.layout-page {
					background-color: #D5E8F2;
					padding: 4px;
					border-radius: 3px;
					margin: 2px;
				}
			}
			.fonticon-right {
				float: right;
				border: 1px solid @ctext;
				border-radius: 50px;
				width: 20px;
				height: 20px;
				font-size: 14px;
				line-height: 20px;
				transition: 0.5s ease-in-out;
			}
			&:hover {
				background-color: @clink-active;
				color: white;
				z-index: 51;
				.info-layout-container {
					.description {
						color: #f1f1f1;
					}
				}
				.fonticon-right {
					border-color: white;
					transform: rotate(360deg);
				}
				.layout-page {
					background-color: white;
					color: @clink-active;
				}
			}
		}
	}
	.toolbar-elem-container {
		height: 100%;
		display: inline-flex;
		position: relative;
		.toolbar-main-icon {
			height: 100%;
			width: 50px;
			background-color: @clink-active;
			margin: 0;
			font-size: 27px;
			color: white;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.toolbar-dropdown-container {
			height: 100%;
			margin: 0;
			cursor: pointer;
			display: inline-flex;
			align-items: center;
			background-color: white;
			max-width: 600px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			&.toolbar-page-dropdown {
				&.no-page-modification {
					cursor: default;
					padding-right: 10px;
				}
			}
			&:hover {
				color: @clink-active;
				.toolbar-dropdown-icon {
					color: @clink-active;
				}
			}
			&.active {
				border-bottom: 2px solid @cblock-bg-alt;
				z-index: 50;
				background-color: @cblock-bg-alt;
				color: @clink-active;
				.toolbar-dropdown-icon {
					color: @clink-active;
				}
			}
			.value {
				margin-left: 13px;
				font-size: 20px;
				line-height: 20px;
				@media screen and (max-width: @screen-md) {
					display: none;
				}
			}
			.toolbar-dropdown-icon {
				width: 30px;
				padding-left: 10px;
				margin: 0;
				font-size: 25px;
				display: flex;
				align-items: center;
				height: 100%;
				background-color: #fd872b;
				background-color: #fd872bad;
				color: white;
				margin-left: 20px;
				padding-right: 5px;
			}
		}
		.toolbar-create-button {
			height: 100%;
			width: 40px;
			margin: 0;
			font-size: 21px;
			border-left: 1px solid @cblock-border;
			padding-right: 5px;
			padding-left: 5px;
			background-color: #fd8720;
			background-color: #fd872bad;
			color: white;
			cursor: pointer;
			display: flex;
			justify-content: center;
			align-items: center;
			&:hover {
				color: @clink-active;
			}
			&.active {
				color: @clink-active;
			}
		}
	}
	.toolbar-button-container {
		width: 88px;
		display: inline-block;
		float: right;
		height: 100%;
		&.with-three-buttons {
			width: 132px;
		}
		.toolbar-save-button, .toolbar-cancel-button, .toolbar-reset-button {
			color: white;
			font-size: 22px;
			position: relative;
			padding-left: 11px;
			padding-right: 11px;
			height: 100%;
			background-color: #fd872b;
			background-color: #fd872bad;
			display: inline-block;
			margin: 0;
			cursor: pointer;
			display: inline-flex;
			justify-content: center;
			align-items: center;
			&:hover {
				color: @clink-active;
			}
		}
		.toolbar-save-button {
			background-color: #57B847;
		}
		.toolbar-cancel-button {
			background-color: #ea4f37;
		}
	}

	.grid-stack-item {
		&.full-size-active {
			background-color: inherit;
		}
	}
}
