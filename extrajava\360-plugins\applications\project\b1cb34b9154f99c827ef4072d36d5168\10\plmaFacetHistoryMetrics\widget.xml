<?xml version="1.0" encoding="UTF-8" ?>
<Widget name="PLMA Facet History Metrics" group="PLM Analytics/Visualizations" premium="true">

	<Description>This widget displays metrics based on a MEL expression.</Description>
	
	<Preview>
		<![CDATA[
			<img src="/resources/widgets/plmaFacetHistoryMetrics/images/preview.PNG" alt="Metrics" />
		]]>
	</Preview>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/historyMetrics.js" />
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>

	<SupportFeedsId arity="ZERO_OR_MANY" />
	<SupportWidgetsId arity="ZERO" />
	<SupportI18N supported="true" />

	<OptionsGroup name="General">
		<Option arity="ZERO_OR_ONE" name="Title" id="title" isEvaluated="true">
			<Description>Widget Title. It can be used to describe the metric, for example, 'Most Popular Camera'.</Description>
		</Option>
		<Option arity="ZERO_OR_ONE" name="Facet" id="facet">
			<Description>Specifies the facet used by the widget.</Description>
				<Functions>
					<ContextMenu>Facets()</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
		</Option>
		<Option id="aggregation" name="Aggregation" arity="ZERO_OR_ONE">
			<Description>The aggregation (calculated on the specified facet) to display in this series. Defaults to 'count'.</Description>
			<Functions>
				<ContextMenu>Aggregations('facetId')</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option arity="ZERO_OR_ONE" name="Limit" id="limit">
			<Description>Specifies the number of points displayed on the chart (0 means no limit).</Description>
				<Functions>
					<Display>SetType('number')</Display>
					<Check>isInteger</Check>
				</Functions>
		</Option>
		<Option arity="ZERO_OR_ONE" name="Link value" id="linkValue">
			<Description>JavaScript code that is executed when clicking the link's button.</Description>
		</Option>
		<Option arity="ZERO_OR_ONE" name="Link icon" id="linkIcon">
			<Description>Specifies the class attribute of the link's button icon.</Description>
		</Option>
		<Option arity="ZERO_OR_ONE" name="Link title" id="linkTitle" isEvaluated="true">
			<Description>Specifies the title of the link. It is displayed as a tooltip when hovering over the link's button.</Description>
		</Option>
		<Option arity="ZERO_OR_ONE" name="Chart Type" id="chartType">
			<Description>Specifies the chart type that is used to display the values of the facet.</Description>
			<Values>
				<Value>spline</Value>
				<Value>column</Value>
			</Values>
		</Option>
		<Option id="tooltip" name="Tooltip" arity="ONE" isEvaluated="true">
			<Description>JavaScript code that specifies the style of the tooltips which are displayed when hovering over the chart values.</Description>		
			<Values>
				<Value><![CDATA[
{
	backgroundColor: '#222',
	borderWidth: 0,
	style: {
		color: '#fff'
	},
	useHTML: true,
	headerFormat: '<div style="display: inline-block; margin-right: 4px;margin-bottom: 2px;width:6px;height:6px;border-radius:3px;background-color:{series.color};"></div><b>{series.name}</b><br />{point.key}',
	pointFormat: ':{point.y}<br />',
	footerFormat:'',
	valueDecimals: 0
}
]]>
				</Value>
			</Values>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<OptionComposite name="Colors ranges" id="colors" arity="ZERO_OR_MANY">
			<Option name="Color" id="color">
				<Description>Hex code applied to the widget when the aggregation value is within this range.</Description>
			</Option>
			<Option name="Start Range Factor" id="startRange">
				<Description>The aggregation value must be higher than the specified number multiplied by the average value.</Description>
			</Option>
			<Option name="End Range Factor" id="endRange">
				<Description>The aggregation value must be lower than the specified number multiplied by the average value.</Description>
			</Option>
			<Values>
				<Value></Value>
			</Values>
		</OptionComposite>
		<Option arity="ONE" name="Hide chart" id="hideChart">
			<Description>Hides the chart. The widget title (if defined), last facet value, and link and documentation buttons (if defined) remain displayed.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Documentation">
		<Option id="displayDoc" name="Enable Documentation">
			<Description>If true, adds a button to display a HTML documentation to give the user information about the chart. (Default value is false)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['doc']})</Display>
			</Functions>
		</Option>
		<Option id="doc" name="User Reference" isEvaluated="true">
			<Description>This is an HTML code to describe the chart.</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
		<Option id="techDoc" name="Technical Reference" isEvaluated="true">
			<Description>This is the chart's technical reference (it won't be displayed in the application).</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="false" />
	</Platforms>
</Widget>
