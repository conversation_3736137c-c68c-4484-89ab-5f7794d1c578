var refineCountBar = {
		init : function(uCssId,widgets,position,color) {
			var facetBlocks = [];
			/* First iterate on all facets and their categories to read values */
			widgets.find(".table-facets").each(function(iFacet, eFacet){
				var categories = $(eFacet).find(".category");
				var facetBlock = {
					$categories: categories,
					categoryBlocks: []
				};

				var aggregationValues = [];
				facetBlock.$categories.each(function(iCat, eCat){
					var value = parseInt($(eCat).find(".count").text(), 10);
					if (!isNaN(value)){
						aggregationValues.push(value);
					}
				});

				var maxAggregationValue = Math.max.apply(null, aggregationValues);

				facetBlock.$categories.each(function(iCat, eCat){
					var $eCat = $(eCat);
					var aggregationValue = parseInt($eCat.find(".count").text());
					var categoryBlock = {
						$category: $eCat
					};
					if (!isNaN(aggregationValue)){
						if(position === "left"){
							categoryBlock.barCss = {
									"width": aggregationValue * 100 / maxAggregationValue + "px",
									"margin-left": 100 - (aggregationValue * 100 / maxAggregationValue + "px"),
									"background-color": color
								};
						}else if(position === "behind"){
							categoryBlock.barCss = {
								"width": aggregationValue * 100 / maxAggregationValue  + "%",
								"background-color": color
							};
						}else if(position === "right"){
							categoryBlock.barCss = {
								"width": aggregationValue * 100 / maxAggregationValue + "%",
								"background-color": color
							};
						}else{
							categoryBlock.barCss = {
								"width": aggregationValue * 100 / maxAggregationValue  + "%",
								"border-color": color
							};
						}

						facetBlock.categoryBlocks.push(categoryBlock);
					}
				});

				facetBlocks.push(facetBlock);
			});

			/* Then create bars and append them wherever needed */
			var bar = $('<div/>');
			bar.addClass('aggregation-bar ' + uCssId + ' position-' + position);
			var barContainerSelector;
			if(position === "right"){
				barContainerSelector = '.count';
			} else {
				barContainerSelector = '.refineName';
			}
			$.each(facetBlocks, function(i, facetBlock) {
				facetBlock.$categories.find('.count .aggregation-bar').remove();
				$.each(facetBlock.categoryBlocks, function(j, categoryBlock) {
					var countBar = bar.clone().css(categoryBlock.barCss);
					categoryBlock.$category.find(barContainerSelector).css('position', 'relative').prepend(countBar);
				});
			});
		}
};
