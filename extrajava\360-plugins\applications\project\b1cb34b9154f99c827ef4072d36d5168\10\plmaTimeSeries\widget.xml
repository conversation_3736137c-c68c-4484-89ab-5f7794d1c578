<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Time series chart" group="PLM Analytics/Visualization/Charts" premium="true"
		xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget is dedicated for Highcharts time series generation</Description>

	<Preview>
		<![CDATA[
		<img src="/resources/highcharts/images/preview_linechart.png" alt="Line Chart" />
	]]>
	</Preview>

	<Platforms>
		<Platform type="web" supported="true"/>
		<Platform type="mobile" supported="true"/>
	</Platforms>

	<Includes>
		<Include type="css" path="/resources/highcharts/css/style.css"/>
		<Include type="css" path="../plmaResources/css/lightbox.less"/>
		<Include type="css" path="../plmaCharts2/css/style.less"/>
		<Include type="js" path="../plmaResources/js/lodash.min.js"/>
		<Include type="js" path="../plmaCharts2/js/plmaCharts.js"/>
		<Include type="js" path="../plmaCharts2/js/plmaChartButtons.js"/>
		<Include type="js" path="/resources/highcharts/js/highcharts.src.js"/>
		<Include type="js" path="/resources/highcharts/js/highcharts.js"/>
		<Include type="js" path="/resources/highcharts/js/lineChart.js"/>
		<Include type="js" path="/resources/highcharts/js/highcharts-more.js"/>
		<Include type="js" path="../plmaResources/js/plmaHighcharts.js"/>
		<Include type="js" path="../plmaResources/js/lodash.min.js"/>
		<Include type="js" path="../plmaResources/js/popupLib.js"/>
		<Include type="js" path="../plmaResources/js/lightbox.js"/>
		<Include type="js" path="../plmaResources/js/moment-with-locales.min.js" />
		<Include type="js" path="../plmaResources/js/widgetButtonManager.js"/>
		<Include type="js" path="../plmaResources/js/fullScreenWidget.js"/>
		<Include type="js" path="../plmaResources/lib/dom-to-image/dom-to-image.js"/>
	</Includes>

	<Dependencies>
		<Widget name="formInput"/>
		<Widget name="plmaResources"/>
		<Widget name="plmaCharts2"/>
	</Dependencies>

	<SupportWidgetsId arity="ZERO"/>
	<SupportFeedsId arity="MANY"/>
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>charts.nodata</JsKey>
			<JsKey>widget.plmacharts.menu.fullsize</JsKey>
			<JsKey>widget.plmacharts.legend.hide</JsKey>
			<JsKey>widget.plmacharts.legend.display</JsKey>
			<JsKey>widget.plmacharts.exportchart.err.nodata</JsKey>
			<JsKey>widget.plmacharts.exportchart.err.convert</JsKey>
		</JsKeys>
	</SupportI18N>

	<DefaultValues displayName="Normal" previewUrl="">
		<DefaultValue name="basedOn">####count######column##0##Disabled####${category.description}##${category.description}</DefaultValue>
		<DefaultValue name="dataProcessor"><![CDATA[
function(data) {
	return data;
}
]]></DefaultValue>
		<DefaultValue name="exportChart">false</DefaultValue>
		<DefaultValue name="imageType">svg</DefaultValue>
		<DefaultValue name="enableNewDoc">false</DefaultValue>
	</DefaultValues>

	<OptionsGroup name="General">
		<Description>Series generation implementation is automatically detected from facet type (2D, multidim, dyndate ...) and can be overridden,
			default implementations are:&lt;br/&gt;
			- multi --&gt; default implementation for multi-dimensions facet (uses flea representation)&lt;br/&gt;
			- multi-tree --&gt; uses access answer tree representation instead of flea representation&lt;br/&gt;
			- dyndate --&gt; process dynamic date facet&lt;br/&gt;
			&lt;br/&gt;
			Other implementation can be added through java serviceloader mechanism extending
			&lt;b&gt;com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.timeseries.implem.SeriesGenerator&lt;/b&gt; class&lt;br/&gt;
			Series generators can also be configured through JSON configuration (depending on implementation), dyndate and multidim series generators
			are using same configuration where you can set default value, activate data accumulation or override answer date format, for axample:&lt;br/&gt;
			&lt;b&gt;{defaultValue:0, cumulative:false, yearFormat:"yyyy", monthFormat:"MMM yyyy", weekFormat:"week 'w Y", dayFormat:"yyyy/MM/dd",
			hourFormat:"yyyy/MM/dd HH", minuteFormat="yyyy/MM/dd HH:mm", secondFormat="yyyy/MM/dd HH:mm:ss"}&lt;/b&gt;&lt;br/&gt;
			It is not recommended to override date formats in search logic because it must also be configured in widget since default configuration is
			compliant with default search logic values.
		</Description>
		<Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
		</Option>
		<OptionComposite id="basedOn" name="Series" arity="ZERO_OR_MANY" glue="##">
			<Option id="feedId" name="Feed" arity="ZERO_OR_ONE">
				<Description>Specifies the feed to use for this series. Can be empty if the widget is based on only one
					feed.
				</Description>
				<Functions>
					<ContextMenu>Feeds()</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="facetId" name="Facet" arity="ONE">
				<Description>
					Specifies the facet to use for this series. &lt;br/&gt;
					&lt;b&gt;Warning:&lt;/b&gt; using a facet with a large number of categories
					(several thousands), without limiting the number of returned categories, can
					cause performance issues. Use the Search Logic configuration or the
					&lt;b&gt;Limit facet categories&lt;/b&gt; feed trigger to reduce the number of
					returned categories.
				</Description>
				<Functions>
					<ContextMenu>Facets()</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="aggregation" name="Aggregation" arity="ZERO_OR_ONE">
				<Description>The aggregation (calculated on the specified facet) to display for this series. Defaults to
					'count'.
				</Description>
				<Functions>
					<ContextMenu>Aggregations('facetId')</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="implem" name="Time series implementation" arity="ZERO_OR_ONE" isEvaluated="false">
				<Description>Java time series implementation ID used for series generation.</Description>
			</Option>
			<Option id="jsonConfig" name="Time series generator JSON config" arity="ZERO_OR_ONE" isEvaluated="false">
				<Description>Time series generator class JSON configuration</Description>
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>
			<Option id="representation" name="Representation" arity="ONE">
				<Description>The chart type used to display the series.</Description>
				<Values>
					<Value>column</Value>
					<Value>area</Value>
					<Value>areaspline</Value>
					<Value>bar</Value>
					<Value>line</Value>
					<Value>spline</Value>
					<Value>pie</Value>
					<Value>waterfall</Value>
				</Values>
			</Option>
			<Option id="axis" name="Axis" arity="ZERO_OR_ONE">
				<Description>The y-axis to use for this series.</Description>
				<Functions>
					<Display>SetType('number')</Display>
					<Check>isInteger</Check>
				</Functions>
			</Option>
			<Option id="stacking" name="Stacking" arity="ONE">
				<Description>
					Allows to add up series between them instead of displaying them independently. &lt;br/&gt;
					'Disabled' displays series independently, 'Normal' stacks them by adding their values, 'Percent'
					stacks them with equal sizes to show relative values.&lt;br/&gt;
					If set to 'Normal' or 'Percent', the user can switch to the other mode.
				</Description>
				<Values>
					<Value>Disabled</Value>
					<Value>Normal</Value>
					<Value>Percent</Value>
				</Values>
			</Option>
			<Option id="stack" name="Stack index" arity="ZERO_OR_ONE">
				<Description>The stack on which this serie should be stacked. This allows stacking several series
					together or on different stacks. Defaults to 0.
				</Description>
				<Functions>
					<Display>SetType('number')</Display>
					<Check>isInteger</Check>
				</Functions>
			</Option>
			<Option id="legend" name="Legend" arity="ZERO_OR_ONE" isEvaluated="false">
				<Description>A short description of what the series represents.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<ContextMenu>WIDGET_EvalFacet()</ContextMenu>
					<ContextMenu>WIDGET_EvalCategory()</ContextMenu>
				</Functions>
			</Option>
			<Option id="pointLegend" name="Point legend" arity="ZERO_OR_ONE" isEvaluated="false">
				<Description>A short description of what each point represents.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
				</Functions>
			</Option>
			<Option id="colorConfig" name="Color config" arity="ZERO_OR_ONE">
				<Description>You can specify the name of a facet to use its color configuration (from the applicative
					configuration) for this chart. By default, the color configuration is inferred from the charted
					facet.
				</Description>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>
	<OptionsGroup name="Time units">
		<Description>
			These options allow you to add widget menu buttons to change displayed dynamic date facet time unit option.
			This options reload widget adding parameters {'enable_year', 'enable_month', 'enable_week','enable_day','enable_hour','enable_min',
			'enable_sec'}, this parameters must be taken into account in feed configuration, feed parameters are not automatically overridden since
			search logic can vary.
		</Description>
		<Option id="tuYear" name="Year" arity="ONE">
			<Description>Add year time unit menu button.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="tuMonth" name="Month" arity="ONE">
			<Description>Add month time unit menu button.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="tuWeek" name="Week" arity="ONE">
			<Description>Add week time unit menu button.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="tuDay" name="Day" arity="ONE">
			<Description>Add day time unit menu button.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="tuHour" name="Hour" arity="ONE">
			<Description>Add hour time unit menu button.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="tuMinute" name="Minute" arity="ONE">
			<Description>Add minute time unit menu button.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="tuSecond" name="Second" arity="ONE">
			<Description>Add second time unit menu button.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Axes">
		<Option id="xAxisLabel" name="X axis label" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>The legend displayed below the x-axis to indicate what is displayed and/or the unit.
			</Description>
		</Option>
		<Option id="yAxisLabels" name="Y axis label" arity="ZERO_OR_MANY" isEvaluated="true">
			<Description>The legend displayed next to the corresponding y-axis to indicate what is displayed and/or the
				unit.
			</Description>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Filters">
		<Description>Enable or disable refine URL generation, in case of multidim facets, refines can be generated on one or two dimensions.&lt;br/&gt;
			By default only first dimension is used for refine URL generation, to use only second dimension or both dimensions, configure 1d facets
			IDs to be taken into account (for example 2D or multidim facet 'A/B', configure 'A' and 'B').&lt;br/&gt;
			/!\ in multidim implementation, referenced 1D facets are used to generate X Axis and some description so be sure to configure
			'max_per_level=0' parameter for each facet to generate consistent time series&lt;br/&gt;
			If you configured 'multi-tree' implementation in general options tab --&gt; use dimension indices instead of names to filter refines
			(in tree representation, dimensions IDs are not present...), so for multidim 'A/B'; to generate refine URLs based on facet 'B',
			configure '1' in filtered facets (0 based indices).
		</Description>
		<Option id="enableRefines" name="Enable refine" arity="ONE">
			<Description>Allows the user to refine on a category by clicking the corresponding point or label on the
				chart.
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['target', 'forceRefineOnFeeds',
					'forceRefineOnFacets']})
				</Display>
			</Functions>
		</Option>
		<Option id="target" name="Destination page on click" isUrlEncoded="true" isEvaluated="true">
			<Description>Specifies the URL that opens when clicking on a point or label of the chart. If empty, the user
				will stay on the current page.
			</Description>
			<Functions>
				<ContextMenu>Pages()</ContextMenu>
			</Functions>
		</Option>
		<Option id="forceRefineOnFacets" name="Force refinement for facets (names or dimension (0-N))" arity="ZERO_OR_MANY">
			<Description>Applies refinements on the specified facets instead of the original one. Only works for 1D
				facets. Configure facet name(s) or dimension index in multidim facet.
			</Description>
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Advanced">
		<Option id="activateReloadChart" name="Activate Reload Chart" arity="ONE">
			<Description>Activates a button to reload chart with additional parameters.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'],
					showOptions:['labelReload','iconReload','additionalParams','reloadCondition']})
				</Display>
			</Functions>
		</Option>
		<Option id="labelReload" name="Button label" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Specifies the label of the button. If empty, no label is displayed.</Description>
		</Option>
		<Option id="iconReload" name="Button icon" arity="ZERO_OR_ONE">
			<Description>Specifies the CSS class name of the icon added to the button. If empty, no icon is displayed.
			</Description>
		</Option>
		<OptionComposite id="additionalParams" name="Additional parameters" arity="ZERO_OR_MANY">
			<Description>Additionnal URL parameters to be sent when the hidden widget is called through Ajax.
			</Description>
			<Option id="paramName" name="Name" arity="ONE">
				<Description>The name of the parameter.</Description>
				<Functions>
					<ContextMenu>PageParameters()</ContextMenu>
				</Functions>
			</Option>
			<Option id="paramValue" name="Value" arity="ZERO_OR_ONE" isEvaluated="true">
				<Description>The value to send for this parameter. Can be a MEL expression.</Description>
			</Option>
		</OptionComposite>
		<Option arity="ZERO_OR_ONE" name="Reload Button Condition" id="reloadCondition" isEvaluated="true">
			<Description>Condition to display the reload button (default is true).</Description>
			<Functions>
				<Display>SetType('code', 'mel')</Display>
			</Functions>
		</Option>
		<Option id="stackingButton" name="Display a button to modify stacking">
			<Description>Display a button to modify stacking. In the chart menu, you will be able to click to pass to stacking percent and stacking normal modes.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="activateFilter" name="Activate filter on categories">
			<Description>Activates filters on category names.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('false',['widgetsId'],[], true, false)</Display>
			</Functions>
		</Option>
		<OptionComposite id="widgetsId" name="Wuid from widget to update them in ajax at each search"
						 arity="ZERO_OR_MANY" glue="##">
			<Description>The list of widget wuids to update with a search.</Description>
			<Option id="idWidget" name="Widget Id" arity="ONE">
			</Option>
		</OptionComposite>
		<Option arity="ONE" name="Show options for Waterfall chart" id="optionsWaterfall">
			<Description>Displays the options for a waterfall chart.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'],
					showOptions:['upColor','downColor','finalSumWaterfall','finalSumDisplayName','finalSumDisplayColor','intermediateSumWaterfall','intermediateSum']})
				</Display>
			</Functions>
		</Option>
		<!--		 <Option id="upColor" name="Color for positive values in Waterfall chart" isEvaluated="true"> -->
		<!--		 </Option> -->
		<!--		 <Option id="downColor" name="Color for negative values in Waterfall chart" isEvaluated="true"> -->
		<!--		 </Option> -->
		<Option arity="ONE" name="Display final sum for Waterfall chart" id="finalSumWaterfall">
			<Description>On a waterfall chart, displays a bar with the final sum.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'],
					showOptions:['finalSumDisplayName','finalSumDisplayColor']})
				</Display>
			</Functions>
		</Option>
		<Option id="finalSumDisplayName" name="Final sum display name" isEvaluated="true">
		</Option>
		<Option id="finalSumDisplayColor" name="Final sum color">
		</Option>
		<Option arity="ONE" name="Display intermediate sum for Waterfall chart" id="intermediateSumWaterfall">
			<Description>On a waterfall chart, displays the intermediate sum.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['intermediateSum']})</Display>
			</Functions>
		</Option>
		<OptionComposite arity="ZERO_OR_ONE" id="intermediateSum" name="Intermediate sum" glue="##">
			<Option id="intermediateSumType" name="Type">
				<Values>
					<Value>Fixed number of sums</Value>
					<Value>Sum to fixed number of values</Value>
					<Value>List of positions (2,4,6 etc...)</Value>
				</Values>
			</Option>
			<Option id="intermediateSumNb" name="Number"></Option>
			<Option id="isIntermediate" name="Intermediate or total">
				<Values>
					<Value>Total</Value>
					<Value>Intermediate</Value>
				</Values>
			</Option>
			<Option id="intermediateSumName" name="Display name" isEvaluated="true"></Option>
			<Option id="intermediateSumColor" name="Color"></Option>
		</OptionComposite>
	</OptionsGroup>

	<OptionsGroup name="Buttons">
		<Option id="exportChart" name="Export Chart to 3DSpace">
			<Description>Enables Chart Share to platform. This functionality captures the Image of the chart
				This image will be uploaded to 3DSpace. To upload image you can create new document by enabling option
				here, or drop the 3DSpace Object to modify. For Existing Object, you can either choose to add new file or
				revise existing file. if you enable this option, make sure you have required JS files at page or app level.
				All JS files inside 'plmaResources/js/exportTo3DSpace' will be required.
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['spaceUrl', 'imageType','enableNewDoc']})</Display>
			</Functions>
		</Option>
		<Option id="spaceUrl" name="Url of 3DSpace" arity="ZERO_OR_ONE">
			<Description>Used to call 3DSpace API's for uploading the image to 3DSpace Object. If not provided internally queried to PlatformServices.</Description>
			<Functions>
				<Display></Display>
			</Functions>
		</Option>
		<Option id="imageType" name="Type of Image" arity="ONE">
			<Description>Image type to export.</Description>
			<Values>
				<Value>svg</Value>
				<Value>png</Value>
			</Values>
		</Option>
		<Option id="enableNewDoc" name="Enable New Document Creation">
			<Description><![CDATA[ If true, New Document creation option will be enabled for Share to platform Functionality.
				(Default value is false).
				<span style="color:red;"><b>CAUTION</b>: Use this option carefully as it can create many object in 3DSpace/FCS.</span>
			]]></Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>

		<OptionComposite name="Buttons" id="buttons" arity="ZERO_OR_MANY">
			<Option name="Icon CSS" id="css">
				<Description>Specifies the CSS class name of the icon added to the button, which is displayed on the
					widget header.
				</Description>
			</Option>
			<Option name="Label" id="label" isEvaluated="true">
				<Description>A short text displayed in the button.</Description>
			</Option>
			<Option name="On click" id="onClick" isEvaluated="true">
				<Description>Javascript code that is executed when clicking the button.</Description>
				<Placeholder>function(button, widget, data){ }</Placeholder>
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>

			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>

	<OptionsGroup name="JavaScript">
		<Option id="opts" name="JavaScript options" arity="ONE" isEvaluated="true">
			<Description>Lets you customize some javascript options used by the Highcharts library. Your javascript
				options will overwrite the default ones. For example, it can be useful to redirect clicks on pages.
			</Description>
			<Values>
				<Value><![CDATA[

{
	/*colors: ['#4572A7', '#AA4643', '#89A54E', '#80699B', '#3D96AE', '#DB843D', '#92A8CD', '#A47D7C', '#B5CA92'],*/
	/*colors: ['#058DC7', '#50B432', '#ED561B', '#DDDF00', '#24CBE5'],*/
	chart: {
		defaultSeriesType: 'column',
		zoomType: 'x'
	},
  	title:{
   		text:null
  	},
 	/*xAxis: [{
      type: "datetime",
      labels: {
        formatter: function() {
          return Highcharts.dateFormat('%Y-%m-%d', this.value);
        }
      }
    }],*/
  	credits: {
 		enabled: false
	},
	tooltip: {
      shared: true
    },
	plotOptions: {
		series: {
			// Configure color threshold
			//threshold:0,
			//negativeColor:"#FF0000",
			turboThreshold: 0,
			cursor: 'pointer',
			followPointer: true,
			stickyTracking: true,
			shadow: false,
			marker: {
				enabled: false
			}
		}
	}
}
]]></Value>
			</Values>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Data processor">
		<Description>The Data Processor allows you to manipulate the json before its execution. Be careful, some
			properties of the data object may be overridden by the ones set in the Javascript tab.
		</Description>
		<Option id="dataProcessor" name="Javascript function">
			<Description>Lets you define the Javascript function that will be executed on data before the Highcharts
				processing.
			</Description>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Documentation">
		<Option id="displayDoc" name="Enable Documentation">
			<Description>If true, adds a button to display a HTML documentation to give the user information about the chart. (Default value is false)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['doc']})</Display>
			</Functions>
		</Option>
		<Option id="doc" name="User Reference" isEvaluated="true">
			<Description>This is an HTML code to describe the chart.</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
		<Option id="techDoc" name="Technical Reference" isEvaluated="true">
			<Description>This is the chart's technical reference (it won't be displayed in the application).</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Style">
		<Option id="width" name="Width">
			<Description>Specifies the widget width (pixels). You must enter an integer.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="height" name="Height">
			<Description>Specifies the widget height (pixels). If no integer is given, the chart will take 100% of the
				available height.
                &lt;b&gt;WARNING&lt;/b&gt;:
                If widget is used as a sub-widget of a Chart board cell widget, height must not be set to take all available height.
			</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="minwidth" name="Min Width">
			<Description>Specifies the minimum width of the widget (pixels). You must enter an integer.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="enableFullScreen" name="Enable full screen" arity="ONE">
			<Description>Allows the user to resize the chart to full screen.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<!-- <Option id="enableFullScreen" name="Enable full screen" arity="ONE">
			<Description>

			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>-->
	</OptionsGroup>
</Widget>
