<?xml version="1.0" encoding='UTF-8'?>
<Widget name="iiHeatmap" group="Results Rendering" premium="false">

	<Description>Displays the categories of a given facet in a table that provides conditional formatting options to the end user.</Description>

	<StorageKeys>
		<StorageKey key="AdvancedTableConfigurations[]" resource="global" />
		<StorageKey optionId="storageKey" resource="user" />
	</StorageKeys>

	<Dependencies>
		<Widget name="advancedTable" />
	</Dependencies>
	
	<SupportI18N supported="true" />
	
	<Includes>
        <Include type="js" path="js/d3-js-polyfill.min.js" />
		<Include type="js" path="../plmaResources/js/react.production.min.js" />
		<Include type="js" path="../plmaResources/js/react-dom.production.min.js" />
		<Include type="js" path="../plmaResources/js/d3.min.js" />
		<Include type="js" path="../plmaResources/js/iiCharts.js" />
		<Include type="js" path="js/heatmap.js" />
        <Include type="css" path="css/style.less" />
	</Includes>

	<Preview>
		<![CDATA[
			<img src="/resources/widgets/iiHeatmap/images/preview.png" alt="Heatmap" />
		]]>
	</Preview>

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="MANY" />
	

	<DefaultValues>
		<DefaultValue name="height">350</DefaultValue>
		<DefaultValue name="maxLevelFacet">100</DefaultValue>
		<DefaultValue name="format_x"><![CDATA[
function(data) {
	return data;
}
]]></DefaultValue>
		<DefaultValue name="format_y"><![CDATA[
function(data) {
	return data;
}
]]></DefaultValue>
	</DefaultValues>

	<OptionsGroup name="General">
		<Option id="title" name="Widget title" isEvaluated="true">
			<Description>Widget title. If blank, no name is displayed.</Description>
		</Option>
		<Option id="width" name="Width">
			<Description>Specifies the width of the widget (pixels). You must enter an integer.</Description>		
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="height" name="Height" arity="ONE">
			<Description>Specifies the height of the widget (pixels). You must enter an integer.</Description>			
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
        <Option id="onClick" name="On click" isEvaluated="true">
            <Description>The JavaScript code that is executed when a rectangle is clicked. Event is passed as a parameter, second argument is data about the rectangle.</Description>
            <Placeholder>function(e, node) {}</Placeholder>
            <Functions>
                <Display>SetType('code', 'js')</Display>
            </Functions>
        </Option>
	</OptionsGroup>

	<OptionsGroup name="Axis">
		<OptionComposite id="facetSeries" name="Series" arity="ZERO_OR_MANY" glue="#@#">
			<Option id="facetId" name="Facet" arity="ONE">
				<Description>The facet to use for this serie.</Description>
				<Functions>
					<ContextMenu>Facets()</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="aggregation" name="Aggregation" arity="ZERO_OR_ONE">
				<Description>The aggregation (calculated on the specified facet) to display in this serie. Defaults to 'count'.</Description>
				<Functions>
					<ContextMenu>Aggregations('facetId')</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="aggregationLabel" name="Label" arity="ZERO_OR_ONE" isEvaluated="true">
				<Description>The label to display for this aggregation (only displayed for 1D facets). If empty, the aggregation name is used.</Description>
			</Option>
			<Option id="aggregationFormat" name="Num. format">
				<Description>You can specify a Java number format to format aggregation values. Use '0' to display each digit, '#' to only display digits that are not heading 0s.</Description>
				<Functions>
					<ContextMenu>addContext("format", ["#", "#.##", "###,###.##", "000000.00", "$ ###.###,##", "€ ### ###,##"])</ContextMenu>
				</Functions>
			</Option>
		</OptionComposite>
	</OptionsGroup>

	<OptionsGroup name="Color">	
		<Option id="colors" name="Colors" arity="ZERO_OR_MANY">
			<Description>Color hexadecimal</Description>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Label">	
		<Option id="format_x" name="Formatting function that returns the X axis labels">
			<Description>Formats the labels on the X axis</Description>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="format_y" name="Formatting function that returns the Y axis labels">
			<Description>Formats the labels on the Y axis</Description>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

</Widget>

