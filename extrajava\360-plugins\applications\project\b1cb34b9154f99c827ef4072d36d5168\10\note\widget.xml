<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Note" group="PLM Analytics/Miscellaneous" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <Description>This widget is a sticky notes container. Every user can add some notes and they will be saved.</Description>
    <Preview>
        <![CDATA[
            <img src="/resources/widgets/note/images/preview.PNG" alt="Note" />
        ]]>
    </Preview>
    <Includes>
        <Include type="js" path="js/note.js" />
        <Include type="js" path="js/noteEditor.js" />
        <Include type="js" path="../plmaResources/js/lodash.min.js"/>
        <Include type="js" path="/resources/jquery-ui-1.11.4.custom/jquery-ui.min.js"/>
        <Include type="js" path="../plmaResources/lib/gridstack/gridstack.js"/>
        <Include type="js" path="../plmaResources/lib/gridstack/gridstack.jQueryUI.js"/>
        <Include type="js" path="../plmaResources/js/lightbox.js"/>
        <Include type="css" path="../plmaResources/lib/gridstack/gridstack.less"/>
        <Include type="css" path="../plmaResources/css/lightbox.less"/>
        <Include type="css" path="css/style.less" />
    </Includes>

    <SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
    <SupportWidgetsId arity="ZERO_OR_MANY" />
    <SupportI18N supported="true">
        <JsKeys>
            <JsKey>note.editor.save</JsKey>
            <JsKey>note.editor.cancel</JsKey>
            <JsKey>note.editor.delete</JsKey>
        </JsKeys>
    </SupportI18N>

    <Dependencies>
        <Widget name="plmaResources" />
    </Dependencies>

    <OptionsGroup name="General">
        <Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
            <Description>Widget title. If empty, no name is displayed.</Description>
        </Option>
        <Option id="cellHeight" name="Height of widgets">
            <Description>Height in pixel of a layout box. It will be rounded to the closest grid row.</Description>
        </Option>
    </OptionsGroup>

    <DefaultValues>
    </DefaultValues>

</Widget>
