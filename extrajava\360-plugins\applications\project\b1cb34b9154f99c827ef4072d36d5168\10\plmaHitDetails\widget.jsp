<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds"/>

<plma:getUsedFeeds var="usedFeeds"/>

<request:getParameterValues var="hitParamValue" name="hit"/>

<config:getOption var="useExternalisedConfig" name="useExternalisedConfig" defaultValue="false"/>
<config:getOption var="disableHitDetailsPrefTab" name="disableHitDetailsPrefTab" defaultValue="false"/>

<%-- Close Button Config Start--%>
<config:getOption name="showCloseButton" var="showCloseButton"/>
<config:getOption name="showOpenButton" var="showOpenButton" defaultValue="true"/>
<config:getOption name="openButtonTooltip" var="openButtonTooltip" defaultValue="Open in Enovia 3DSpace"/>
<config:getOption name="closeButtonLabel" var="closeButtonLabel"/>
<config:getOption name="showCloseButtonLabel" var="showCloseButtonLabel"/>
<%-- Close Button Config End--%>

<%-- Icons font size option --%>
<config:getOption var="fontSizeOption" name="iconsSize"/>
<c:set var="fontSize" value=""/>
<c:if test="${fontSizeOption != null}"><c:set var="fontSize" value="--icons-size:${fontSizeOption}px;"/></c:if>
<%-- Icons font size option --%>

<widget:widget varCssId="cssId" varUcssId="uCssId" extraCss="plmaHitDetails" disableStyles="true">
    <c:choose>

        <%-- If widget has no Feed --%>
        <c:when test="${search:hasFeeds(feeds) == false}">
            <widget:header>
                <config:getOption name="title" defaultValue=""/>
            </widget:header>
            <widget:content>
                <render:definition name="noFeeds">
                    <render:parameter name="widget" value="${widget}"/>
                    <render:parameter name="showSuggestion" value="true"/>
                </render:definition>
            </widget:content>
        </c:when>

        <%-- If all feeds have no results --%>
        <c:when test="${!search:hasEntries(feeds)}">
            <widget:header>
                <config:getOption name="title" defaultValue=""/>
            </widget:header>
            <widget:content>
                <config:getOption var="noResultsJspPath" name="noResultsJspPath" defaultValue="template/noHitSelected.jsp"/>
                <config:getOption var="noResultJspPathWidget" name="noResultJspPathWidget" defaultValue="plmaResources"/>
                <render:template template="${noResultsJspPath}" widget="${noResultJspPathWidget}">
                    <render:parameter name="accessFeeds" value="${feeds}"/>
                </render:template>
            </widget:content>
            <%-- /If all feeds have no results --%>
        </c:when>

        <c:otherwise>
            <span id="hitParamValue" class="hit-param" style="display: none;">${hitParamValue[0]}</span>

            <span id="timelineLabel" style="display: none;"><i18n:message code="hitdetail.timeline"/></span>

            <%-- Results --%>
            <search:forEachFeed feeds="${feeds}" var="feed" varStatus="accessFeedsStatus">
                <search:forEachEntry var="entry" feed="${feed}" varStatus="entryStatus">
                    <%-- Retrieve hit properties  --%>
                    <config:getOption var="hitTitle" name="hitTitle" defaultValue="${entry.title}" entry="${entry}" feed="${feed}"/>
                    <config:getOption var="hitUrl" name="hitUrl" entry="${entry}" feed="${feed}"/>

                    <div class="hitDetailsHeaderWithToolbar widgetHeaderWithToolbar">
                        <div class="hitDetailsHeaderTitle">
                            <c:if test="${plma:hasParam(widget, 'title')}">
                                <span class="widgetTitle">
                                   <string:eval string="${plma:getStringParam(widget, 'title','' )}" feeds="${feeds}"/>
                                </span>
                            </c:if>

                            <c:if test="${not empty hitTitle && plma:getBooleanParam(widget, 'titleInHeader', false)}">
                                <div class="hitHeader">
                                    <render:template template="templates/hit-details-title.jsp">
                                        <render:parameter name="hitTitle" value="${hitTitle}"/>
                                        <render:parameter name="entry" value="${entry}"/>
                                        <render:parameter name="widget" value="${widget}"/>
                                        <render:parameter name="iconContainer" value="${false}"/>
                                    </render:template>
                                </div>
                            </c:if>
                        </div>

                        <div class="headerToolbar" style="${plma:format('--icons-size:%spx;', buttonsIconSize, '')}">
                                <%-- Show toolbar  --%>
                            <c:if test="${showCloseButton == 'true' || showOpenButton == 'true' || plma:hasSubWidget(widget, 'plmaButtonsContainer')}">
                                <div class="hitDetailsButtons iconsPlaceholder" style="${fontSize}">
                                        <%--Code for Hit Details Close Button Start--%>
                                    <c:if test="${showCloseButton == 'true'}">
                                        <div class="closeHitDetailsButton">
										<span class="plmaButton iconDiv">
											<i class="fonticon fonticon-cancel" title="${closeButtonLabel}"></i>
											<c:if test="${showCloseButtonLabel}">
                                                <span class="button-label">${closeButtonLabel}</span>
                                            </c:if>
										</span>
                                        </div>
                                    </c:if>

                                        <%-- Actions --%>
                                    <c:if test="${not empty hitUrl && showOpenButton == 'true'}">
                                        <div class="openHitBtn iconDiv">
                                            <a href="${hitUrl}" target="_blank" class="action-btn" title="${openButtonTooltip}">
                                                <span class="fonticon fonticon-export"></span>
                                            </a>
                                        </div>
                                    </c:if>

                                        <%-- Displays sub-widgets (except plma buttons) --%>
                                    <c:if test="${plma:hasSubWidget(widget, 'plmaButtonsContainer')}">
                                        <plma:forEachSubWidget includes="plmaButtonsContainer">
                                            <div class="iconDiv">
                                                <render:widget/>
                                            </div>
                                        </plma:forEachSubWidget>
                                    </c:if>
                                    <span class="iconDiv">
                                        <c:if test="${plma:getBooleanParam(widget, 'enableCompare', false)}">
                                            <c:set var="compareIdMetaName" value="${plma:getStringParam(widget, 'compareIdMetaName', '')}"/>
                                            <i18n:message  var="compareLabelI18N" code="widgets.plma.hitdetails.compare.add" widget="${widget}"/>
                                            <render:template template="templates/compareHit.jsp" widget="compareHit">
                                                <render:parameter name="compareCollection"
                                                                  value="${plma:getStringParam(widget, 'compareCollection', 'compare' )}"/>
                                                <render:parameter name="hitID" value="${plma:getEntryID(entry, compareIdMetaName)}"/>
                                                <render:parameter name="compareLabel" value="${plma:getStringParam(widget, 'compareLabel', 'Add to Compare')}"/>
                                                <render:parameter name="showCompareLabel" value="${plma:getBooleanParam(widget, 'showCompareLabel', 'true' )}"/>
                                            </render:template>
                                        </c:if>
                                        <c:if test="${plma:getBooleanParam(widget, 'enableFavorite', false)}">
                                            <c:set var="favoriteIdMetaName" value="${plma:getStringParam(widget, 'favoriteIdMetaName', '')}"/>
                                            <i18n:message  var="favoriteLabelI18N" code="widgets.plma.hitdetails.favorite.add" widget="${widget}"/>
                                            <render:template template="templates/preferredHit.jsp" widget="preferredHit">
                                                <render:parameter name="feeds" value="${feeds}"/>
                                                <render:parameter name="entry" value="${entry}"/>
                                                <render:parameter name="label" value="${plma:getStringParam(widget, 'favoriteLabel', 'Add to Favorites' )}"/>
                                                <render:parameter name="showLabel" value="${plma:getBooleanParam(widget, 'showFavoriteLabel', 'false' )}"/>
                                                <render:parameter name="collection"
                                                                  value="${plma:getStringParam(widget, 'favoriteCollection', 'favoriteHits' )}"/>
                                                <render:parameter name="counterSelector" value=".preferred-hits"/>
                                                <render:parameter name="hitID" value="${plma:getEntryID(entry, favoriteIdMetaName)}"/>
                                                <render:parameter name="buttonPrefix" value="favorite"/>
                                            </render:template>
                                        </c:if>
                                    </span>
                                </div>
                            </c:if>
                        </div>
                    </div>

                    <ul class="hits">
                            <%-- Renders a custom view for this hit --%>
                        <config:getOption var="customJspPathHit" name="customJspPathHit" entry="${entry}" feed="${feed}" defaultValue=""/>
                        <render:template template="${customJspPathHit}" defaultTemplate="templates/hit-detail.jsp">
                            <render:parameter name="accessFeeds" value="${feeds}"/>
                            <render:parameter name="feed" value="${feed}"/>
                            <render:parameter name="showCloseButton" value="${showCloseButton}"/>
                            <render:parameter name="entry" value="${entry}"/>
                            <render:parameter name="widget" value="${widget}"/>
                            <c:if test="${hitUrl != null}">
                                <render:parameter name="hitUrl" value="${hitUrl}"/>
                            </c:if>
                            <c:if test="${hitTitle != null}">
                                <render:parameter name="hitTitle" value="${hitTitle}"/>
                            </c:if>
                            <render:parameter name="accessFeedsStatus" value="${accessFeedsStatus}"/>
                            <render:parameter name="entryStatus" value="${entryStatus}"/>
                        </render:template>
                    </ul>

                </search:forEachEntry>
            </search:forEachFeed>
            <%--  Handle HitDecorationInterface --%>
            <config:getOptionsComposite var="decorators" name="exa.io.HitDecorationInterface" separator="##"/>
            <c:if test="${fn:length(decorators)>0}">
                <render:renderScript position="READY">
                    <c:forEach var="decorator" items="${decorators}">
                        <c:if test="${fn:length(decorator[1])>0}">
                            <c:set var="insertMethod" value="${decorator[2]}"/>
                            <c:if test="${fn:length(insertMethod)==0}">
                                <c:set var="insertMethod" value="prepend"/>
                            </c:if>
                            exa.io.register('exa.io.HitDecorationInterface', '${decorator[0]}', function (hitDecorationInterface) {
                            <search:forEachFeed feeds="${feeds}" var="feed" varStatus="accessFeedsStatus">
                                <search:forEachEntry var="entry" feed="${feed}">
                                    $('#${cssId}').find('<string:eval string="${decorator[1]}" entry="${entry}" feed="${feed}"/>')
                                    .each(function() {
                                    var decorationHtml = hitDecorationInterface.getDecoration('${search:cleanEntryId(entry)}');
                                    if (decorationHtml) {
                                    $(decorationHtml).${insertMethod}To(this);
                                    hitDecorationInterface.afterDecorating('${search:cleanEntryId(entry)}');
                                    }
                                    });
                                </search:forEachEntry>
                            </search:forEachFeed>
                            });
                        </c:if>
                    </c:forEach>
                </render:renderScript>
            </c:if>
        </c:otherwise>
    </c:choose>
</widget:widget>

<config:getOption name="onInit" var="onInit" defaultValue="function(){}"/>
<config:getOption name="scrollTop" var="scrollTop" defaultValue="false"/>
<render:renderScript position="READY">
    $('.hit-detail-link-refine').each(function(iLink, eLink){
    var url = new BuildUrl(eLink.href);
    url.removeParameter('hit');
    eLink.href = url.toString() + '#${hitParamValue[0]}';
    });

    var hitDetail = new HitDetail('${uCssId}', {'feeds':${plma:toJSArray(usedFeeds)}, 'scrollTop': '${scrollTop}'});
    <c:if test="${useExternalisedConfig == 'true' && disableHitDetailsPrefTab == 'false'}">
        new PlmaHitDetailsPrefTab();
    </c:if>
    (${onInit}).call(null, $('.${uCssId}'));

    <c:if test="${showCloseButton == 'true'}">
        new PLMAHitDetailsClose('${uCssId}');
    </c:if>

    // Trigger an event on any result list view
    $('.resultlistView').trigger('hit-details:opened');

</render:renderScript>
