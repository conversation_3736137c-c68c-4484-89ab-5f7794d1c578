(function (window) {

	var $templates = $('.alertingTemplates');
	var $form = $templates.find('.alert_form').clone();
	var form = $form.prop('outerHTML');
	var create_buttons = $templates.find('.buttons_div.create').prop('outerHTML');
	var edit_buttons = $templates.find('.buttons_div.edit').prop('outerHTML');

	function addBadge($parent_div, value) {
		var recipient;
		var subscriptionId;
		var isOwner = false;
		if (typeof value === 'object') {
			recipient = value.subscribedUserID;
			subscriptionId = value.id;
			isOwner = value.alertOwnerID === recipient;
		} else {
			recipient = value;
		}
		var $badge_span = $(
			'<span class="badge-primary badge">' +
				'<span class="badge-content">' + _.escape(recipient) +'</span>' +
				(isOwner ? '' : '<span class="btn-remove fonticon fonticon-cancel" title="' + getMessage('alerting.form.actions.remove') + '"></span>') +
			'</span>'
		);
		// useful when we want to retrieve all input values
		var $recipientInput = $('<input/>', {
			name: AlertingForm.ALERTING_RECIPIENT_PARAM,
			type: 'hidden',
			value: recipient
		});
		var $notifIdInput = subscriptionId
			? $('<input/>', {
				name: AlertingForm.ALERTING_NOTIFICATION_ID_PARAM,
				type: 'hidden',
				value: subscriptionId
			})
			: $();
		$badge_span.append($recipientInput, $notifIdInput);
		$parent_div.append($badge_span);
	}

	function removeBadge($badge) {
		$badge.remove();
	}

	function addBadgeForInput($input) {
		addBadge($input.parents('.line').next('.mails-badges'), $input.val());
		$input.val('');
	}

	function validateEmail(email) {
		var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
		return re.test(email);
	}

	function markAsWrong($selection) {
		$selection.addClass("wrong-field").delay(2000).queue(function(){
			$(this).removeClass("wrong-field").dequeue();
		});
	}

	function createSubscriptionRow(subscriptions, type) {
		if (subscriptions.length === 0) {
			return $();
		}
		var $template = $form.find('.recipients .fields_wrapper').find('.fields_row').clone();
		$template.find('select[name="' + AlertingForm.ALERTING_NOTIFICATION_TYPE_PARAM + '"]').val(type);
		subscriptions.forEach(function (subscription) {
			addBadge($template.find('.mails-badges'), subscription);
		});
		return $template;
	}

	/**
	 * @param timestamp - Timestamp in ms
	 * @returns {string} date formatted in YYYY-MM-DD
	 */
	function parseTimestampToDate(timestamp) {
		return moment(timestamp).format(moment.HTML5_FMT.DATE);
	}

	/**
	 * @param {string} date - Date formatted in YYYY-MM-DD
	 * @returns {number} timestamp in ms
	 */
	function parseDateToTimestamp(date) {
		return moment(date).valueOf();
	}

	/**
	 * Returns a timestamp in ms based on the provided date.
	 *
	 * @param {string} date - Date formatted in YYYY-MM-DD
	 * @param {boolean} isEndDate - Whether the provided date is an end date
	 */
	function toTimestamp(date, isEndDate) {
		if (!date) {
			return 0;
		}
		var mDate = moment(date);
		if (isEndDate) {
			mDate.add({
				hours: 23,
				minutes: 59,
				seconds: 59
			});
		}
		return mDate.valueOf();
	}

	function containsDuplicateLabels($select) {
		var labels = $select.find('option')
			.map(function () {
				return $(this).text();
			})
			.get();
		var uniqLabels = _.uniq(labels);
		return labels.length !== uniqLabels.length;
	}

	function getMessage(code) {
		return mashupI18N.get('alerting', code);
	}

	function AlertingForm(options) {
		this.options = options;
		this.$form = $(form);
		this.errors = [];
		this.init();
	}

	AlertingForm.TYPE_CREATE = 'create';
	AlertingForm.TYPE_EDIT = 'edit';
	AlertingForm.ALERTING_THRESHOLD_ID_PARAM = 'thr_id';
	AlertingForm.ALERTING_NOTIFICATION_ID_PARAM = 'notif_id';

	AlertingForm.prototype.init = function () {
		this.addButtons();
		this.bind();
		this.fill();
		this.initCalendars();
	};

	AlertingForm.prototype.initCalendars = function () {
		var $startDateInput = this.$form.find('input[name="' + AlertingForm.ALERTING_START_DATE_PARAM + '"]');
		var $endDateInput = this.$form.find('input[name="' + AlertingForm.ALERTING_END_DATE_PARAM + '"]');
		var $dateInputs = $startDateInput.add($endDateInput);
		$dateInputs.daterangepicker({
			autoUpdateInput: false,
			singleDatePicker: true,
			showDropdowns: true,
			locale: {
				format: moment.HTML5_FMT.DATE
			},
			drops: 'up'
		});
		$dateInputs.each(function (i, el) {
			$(el).data('daterangepicker').container
				.addClass('alerting-daterangepicker')
				// Hide calendars when initializing
				.css('display', 'none');
		});
		$dateInputs.on('apply.daterangepicker', function (ev, picker) {
			$(this).val(picker.startDate.format(moment.HTML5_FMT.DATE));
		});
	};

	AlertingForm.prototype.addButtons = function () {
		var $buttons;
		switch (this.options.data.type) {
			case AlertingForm.TYPE_CREATE:
				$buttons = $(create_buttons);
				break;
			case AlertingForm.TYPE_EDIT:
				$buttons = $(edit_buttons);
				// do not allow the subscriber to edit the alert
				if (this.options.data.owner.mail !== this.options.user) {
					$buttons.find('.edit_btn').remove();
				}
				break;
		}
		if ($buttons) {
			this.$form.append($buttons);
		}
	};

	AlertingForm.prototype.bind = function () {
		this.$form
			.on('change', 'select[name="' + AlertingForm.ALERTING_SERIES_PARAM + '"]', function (e) {
				// Fill categories from facet
				var $select = $(e.currentTarget);
				var $categories = $select.next('select[name="' + AlertingForm.ALERTING_CATEGORY_PARAM + '"]');
				var serie = $select.find("option:selected").data('serie');
				var categories = null;
				if(serie.isMultiDimensionFacet){
					categories = serie.categories.dimension1;
				}else{
					categories = serie.categories;
					/* handle specific use case with one category */
					if(categories && categories.length === 1 && serie.pointLegend != ""){
						categories = [{name : serie.categories[0].name, i18n: serie.pointLegend}]
					}
				}
				var options = categories.map(function (category) {
					return $('<option/>', {
						text: category.i18n || category.name,
						value: category.path || category.name
					});
				});
				options.unshift($('<option/>', {text: getMessage('alerting.form.anyCategory'), value: AlertingForm.ALERTING_CATEGORY_ANY}));
				$categories.empty().append(options);
			}.bind(this))
			.on('change', 'select[name="' + AlertingForm.ALERTING_THRESHOLD_TYPE_PARAM + '"]', function (e) {
				var $select = $(e.currentTarget);
				var $info = $select.next('.thr_type_info');
				if ($select.val() === 'percent') {
					$info.removeClass('hidden');
				} else {
					$info.addClass('hidden');
				}
			}.bind(this))
			.on('click', '.section_2 .btn-remove', function (e) {
				var $btn = $(e.currentTarget);
				// do not delete the first row
				if ($btn.closest('.fields_wrapper').find('.fields_row').length > 1) {
					$btn.closest('.fields_row').remove();
				}
			})
			.on('click', '.section_2 .btn-add', function (e) {
				var $fieldsWrapper = $(e.currentTarget).closest('.fields_wrapper');
				var $row = $fieldsWrapper.find('.fields_row').first().clone(true);
				// select the first option
				$row.find('select')
					.val(function () {
						return $(this).find('option:not(:disabled)').val() || $(this).val();
					})
					.trigger('change');
				// reset value
				$row.find('input').val('').trigger('change');
				// remove badges
				$row.find('.mails-badges').empty();
				$fieldsWrapper.append($row);
			})
			.on('keypress', 'input[name="' + AlertingForm.ALERTING_RECIPIENT_PARAM + '"]', function (e) {
				var $input = $(e.currentTarget);
				var keycode = (e.keyCode ? e.keyCode : e.which);
				// On Enter button click
				if (keycode === 13) {
					// prevent submit
					e.preventDefault();
					addBadgeForInput($input);
				}
			})
			.on('click', '.add-recipient', function (e) {
				var $button = $(e.currentTarget);
				var $input = $button.closest('.input-group').find('input[name="' + AlertingForm.ALERTING_RECIPIENT_PARAM + '"]');
				addBadgeForInput($input);
				$button.blur();
			})
			.on('click', '.mails-badges .btn-remove', function (e) {
				removeBadge($(e.currentTarget).closest('.badge'));
			})
			.on('click', '.cancel_btn', function (e) {
				this.options.onCancel.call(null, this);
			}.bind(this))
			.on('submit', function (e) {
				e.preventDefault();
				if (this.check()) {
					this.submit();
				} else {
					$.notify('Creation failed, please check the following fields: \n' + this.errors.join('\n'), 'error');
				}
			}.bind(this))
	};

	AlertingForm.prototype.fillSelectOption = function($select, serie){
		var $option = $('<option/>', {
			text: serie.label || serie.facet,
			value: serie.facet
		});
		$select.append($option);
		$option.data("serie", serie);
	};

	AlertingForm.prototype.fillFacetCategory = function($select, category, serie){
		var $option = $('<option/>', {
			text: category.i18n || category.name,
			value: category.path
		});
		$select.append($option);
		$option.data("serie", serie);
	};

	AlertingForm.prototype.fill = function () {
		var $series = this.$form.find('select[name="' + AlertingForm.ALERTING_SERIES_PARAM + '"]');
		var $categories = $series.next('select[name="category"]');
		this.options.data.seriesAndFacets.forEach(function (serie) {
			if(serie.isMultiDimensionFacet) {
				var series = serie.categories.dimension2;
				var categories = serie.categories.dimension1;
				if (series && categories) {
					// Fill series from multi dimension facet
					series.forEach(function (serieItem) {
						this.fillFacetCategory($series, serieItem, serie);
					}, this);
					$series.trigger('change');
				}
			}else{
				// Fill series from normal facet
				this.fillSelectOption($series, serie);
				$series.trigger('change');
			}
		}, this);
		// In case of duplicated names in the series: add a legend to differentiate
		if (containsDuplicateLabels($series)) {
			$series.find('option').each(function () {
				var $this = $(this);
				var series = $this.data('serie');
				$this.text($this.text() + " (" + series.legend + ")")
			});
		}
		if (this.options.data.type === AlertingForm.TYPE_EDIT) {
			this.fillByName(AlertingForm.ALERTING_TITLE_PARAM, this.options.data.title);
			this.fillByName(AlertingForm.ALERTING_CONDITION_TYPE_PARAM, this.options.data.condition);
			this.fillDate(AlertingForm.ALERTING_START_DATE_PARAM, this.options.data.startDate);
			this.fillDate(AlertingForm.ALERTING_END_DATE_PARAM, this.options.data.endDate);
			this.fillThresholds(this.options.data.thresholds);
			this.fillSubscriptions(this.options.data.subscriptions);
		}
		if (this.options.data.type === AlertingForm.TYPE_CREATE) {
			if (this.options.user) {
				var $input = this.$form
					.find('input[name="' + AlertingForm.ALERTING_RECIPIENT_PARAM + '"]').first();
				var $badgeContainer = $input.parents('.line').next('.mails-badges');
				var $select = this.$form.find('select[name="' + AlertingForm.ALERTING_NOTIFICATION_TYPE_PARAM + '"]').first();
				$select.val(AlertingForm.ALERTING_NOTIFICATION_TYPE_NOTIFICATION).trigger('change');
				addBadge($badgeContainer, {
					alertOwnerID: this.options.user,
					subscribedUserID: this.options.user
				});
			}
		}
	};

	AlertingForm.prototype.fillByName = function (name, value) {
		if (value) {
			this.$form.find('[name="' + name + '"]').val(value);
		}
	};

	AlertingForm.prototype.fillDate = function (name, value) {
		this.fillByName(name, value ? parseTimestampToDate(value) : '');
	};

	AlertingForm.prototype.fillThresholds = function (thresholds) {
		var selector = '.thresholds .fields_wrapper';
		this.$form.find(selector)
			.empty()
			.append(thresholds.map(function (threshold) {
				var $template = $form.find(selector).find('.fields_row').clone();
				$template.find('select[name="' + AlertingForm.ALERTING_SERIES_PARAM + '"]').append($('<option/>', {
					value: threshold.category2ID || threshold.series.facet,
					text: threshold.category2Label || threshold.series.label || threshold.series.facet
				}));
				$template.find('select[name="' + AlertingForm.ALERTING_CATEGORY_PARAM + '"]').append($('<option/>', {
					value: threshold.category1ID,
					text: threshold.category1Label
				}));
				$template.find('select[name="' + AlertingForm.ALERTING_THRESHOLD_CONDITION_PARAM + '"]').val(threshold.condition);
				$template.find('input[name="' + AlertingForm.ALERTING_THRESHOLD_VALUE_PARAM + '"]').val(threshold.value);
				$template.find('select[name="' + AlertingForm.ALERTING_THRESHOLD_TYPE_PARAM + '"]').val(threshold.type);
				$template.append($('<input/>', {
					name: AlertingForm.ALERTING_THRESHOLD_ID_PARAM,
					type: 'hidden',
					value: threshold.id
				}));
				return $template;
			}));
	};

	AlertingForm.prototype.fillSubscriptions = function (subscriptions) {
		this.$form.find('.recipients .fields_wrapper')
			.empty()
			.append(
				createSubscriptionRow(subscriptions.filter(function (subscription) {
					return subscription.type === AlertingForm.ALERTING_NOTIFICATION_TYPE_EMAIL;
				}), AlertingForm.ALERTING_NOTIFICATION_TYPE_EMAIL),
				createSubscriptionRow(subscriptions.filter(function (subscription) {
					return subscription.type === AlertingForm.ALERTING_NOTIFICATION_TYPE_NOTIFICATION;
				}), AlertingForm.ALERTING_NOTIFICATION_TYPE_NOTIFICATION)
			);
	};

	AlertingForm.prototype.check = function () {
		this.errors = [];
		// show all invalid fields independently
		[
			this.checkTitle,
			this.checkCondition,
			this.checkThresholds,
			this.checkRecipients,
			this.checkDates
		].forEach(function (validator) {
			validator.call(this);
		}, this);

		return this.errors.length === 0;
	};

	AlertingForm.prototype.checkCondition = function () {
		this.options.data.condition = this.$form.find('select[name="' + AlertingForm.ALERTING_CONDITION_TYPE_PARAM + '"]').val();
	};

	AlertingForm.prototype.checkDates = function () {
		var $startDateInput = this.$form.find('input[name="' + AlertingForm.ALERTING_START_DATE_PARAM + '"]');
		var $endDateInput = this.$form.find('input[name="' + AlertingForm.ALERTING_END_DATE_PARAM + '"]');
		var actualStartDate = $startDateInput.val();
		var actualEndDate = $endDateInput.val();
		var startDate = toTimestamp(actualStartDate, false);
		var endDate = toTimestamp(actualEndDate, true);
		if (startDate && endDate && startDate > endDate) {
			markAsWrong($startDateInput.add($endDateInput));
			this.errors.push('Period');
		} else {
			this.options.data.startDate = startDate;
			this.options.data.endDate = endDate;
		}
	};

	AlertingForm.prototype.checkThresholds = function () {
		var $invalidInputs = this.$form
			.find('input[name="' + AlertingForm.ALERTING_THRESHOLD_VALUE_PARAM + '"],' +
				'input[name="' + AlertingForm.ALERTING_SERIES_PARAM+ '"],' +
				'input[name="' + AlertingForm.ALERTING_CATEGORY_PARAM + '"]')
			.filter(function () {
				return $(this).val() === '';
			});

		if ($invalidInputs.length > 0) {
			markAsWrong($invalidInputs);
			this.errors.push('Thresholds');
		} else {
			var thresholds = this.options.data.thresholds;
			this.options.data.thresholds = [];
			this.$form.find('.thresholds .fields_row').each(function (thresholds, index, row) {
				var $row = $(row);
				var $category = $row.find('select[name="' + AlertingForm.ALERTING_CATEGORY_PARAM + '"]');
				var $series = $row.find('select[name="' + AlertingForm.ALERTING_SERIES_PARAM + '"]');
				var threshold = _.find(thresholds, {id: $row.find('input[name="' + AlertingForm.ALERTING_THRESHOLD_ID_PARAM + '"]').val()}) || {};
				var series = threshold.series || $series.find('option:selected').data('serie');
				var isMulti = series && series.isMultiDimensionFacet;
				this.options.data.thresholds.push($.extend({}, threshold, {
					id: threshold.id,
					series: series,
					category1ID: $category.val(),
					category2ID: isMulti ? $series.val() : null,
					category1Label: $category.find('option:selected').text(),
					category2Label: isMulti ? $series.find('option:selected').text() : null,
					condition: $row.find('select[name="' + AlertingForm.ALERTING_THRESHOLD_CONDITION_PARAM + '"]').val(),
					value: $row.find('input[name="' + AlertingForm.ALERTING_THRESHOLD_VALUE_PARAM + '"]').val(),
					type: $row.find('select[name="' + AlertingForm.ALERTING_THRESHOLD_TYPE_PARAM + '"]').val()
				}));
			}.bind(this, thresholds));
		}
	};

	AlertingForm.prototype.checkRecipients = function () {
		// case: invalid info
		var $invalidInputs = this.$form
			.find('input[name="' + AlertingForm.ALERTING_RECIPIENT_PARAM + '"]')
			.filter(function () {
				var notifType = $(this).closest('.fields_row').find('.recipient_info').prev('select[name="' + AlertingForm.ALERTING_NOTIFICATION_TYPE_PARAM + '"]').val();
				var value = $(this).val();
				switch (notifType) {
					case AlertingForm.ALERTING_NOTIFICATION_TYPE_EMAIL:
						return value && !validateEmail(value);
					default:
						return false;
				}
			});
		var $associatedBadges = $invalidInputs.prev('.badge');

		// case: empty input + no badge
		var $emptyInputs = this.$form
			.find('input[name="' + AlertingForm.ALERTING_RECIPIENT_PARAM + '"]')
			.filter(function () {
				return !$(this).val() && $(this).closest('.fields_row').find('.mails-badges').children().length === 0;
			});

		// case: none selected
		var $invalidRow = this.$form
			.find('select[name="' + AlertingForm.ALERTING_NOTIFICATION_TYPE_PARAM + '"]')
			.filter(function () {
				return !$(this).val();
			})
			.closest('.fields_row');

		if ($invalidInputs.add($emptyInputs).length > 0) {
			markAsWrong($invalidInputs.add($associatedBadges));
			markAsWrong($emptyInputs);
			markAsWrong($invalidRow);
			this.errors.push('Recipients');
		} else {
			var subscriptions = this.options.data.subscriptions;
			this.options.data.subscriptions = [];
			this.$form.find('.recipients .fields_row').each(function (index, element) {
				var $row = $(element);
				var notifType = $row.find('select[name="' + AlertingForm.ALERTING_NOTIFICATION_TYPE_PARAM + '"]').val();
				$row.find('input[name="' + AlertingForm.ALERTING_RECIPIENT_PARAM + '"]').each(function (i, input) {
					var $input = $(input);
					var subscription = _.find(subscriptions, {id: $input.next('input[name="' + AlertingForm.ALERTING_NOTIFICATION_ID_PARAM + '"]').val()}) || {};
					var value = $input.val();
					if (value) {
						this.options.data.subscriptions.push($.extend({}, subscription, {
							subscribedUserID: value,
							type: notifType
						}));
					}
				}.bind(this))
			}.bind(this));
		}
	};

	AlertingForm.prototype.checkTitle = function () {
		var $title = this.$form.find('input[name="' + AlertingForm.ALERTING_TITLE_PARAM + '"]');
		var title = $title.val();
		if (!title) {
			markAsWrong($title);
			this.errors.push('Title');
		} else {
			this.options.data.title = title;
		}
	};

	AlertingForm.prototype.to$ = function () {
		return this.$form;
	};

	AlertingForm.prototype.submit = function () {
		switch (this.options.data.type) {
			case AlertingForm.TYPE_CREATE:
					AlertingClient.createAlert({
						title: this.options.data.title,
						pageName: this.options.data.pageName,
						pageId: this.options.data.pageId,
						chartID: this.options.data.chartID,
						condition: this.options.data.condition,
						thresholds: JSON.stringify(this.options.data.thresholds),
						startDate: this.options.data.startDate,
						endDate: this.options.data.endDate,
						subscriptions: JSON.stringify(this.options.data.subscriptions)
					})
					.then(function () {
						this.options.onSubmitSuccess.call(null, this);
					}.bind(this), function () {
						this.options.onSubmitFail.call(null, this);
					}.bind(this));
				break;
			case AlertingForm.TYPE_EDIT:
					AlertingClient.editAlert({
						id: this.options.data.id,
						title: this.options.data.title,
						condition: this.options.data.condition,
						thresholds: JSON.stringify(this.options.data.thresholds),
						startDate: this.options.data.startDate,
						endDate: this.options.data.endDate,
						subscriptions: JSON.stringify(this.options.data.subscriptions)
					})
					// the alert got edited: new notifications should show up
					.then(function () {
						return AlertingClient.updateNotificationInfo({
							alert_id: this.options.data.id,
							user_id: this.options.user,
							is_seen: true,
							last_seen: moment().unix()
						});
					}.bind(this))
					.then(function () {
						this.options.onSubmitSuccess.call(null, this);
					}.bind(this), function () {
						this.options.onSubmitFail.call(null, this);
					}.bind(this));
				break;
		}
	};

	window.AlertingForm = AlertingForm;
})(window);