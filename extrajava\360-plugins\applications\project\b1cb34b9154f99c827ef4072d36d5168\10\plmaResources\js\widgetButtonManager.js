var WidgetButtonManager = function (widgetId, addingMethod, containerSelector) {
	this.wigetId = widgetId;
	this.widget = $('.' + this.wigetId);
	/* If addingMethod is 'menu', then we add a menu and button will be displayed in this menu */
	/* This menu will be displayed on click on a 3-dots button which will be in the selector */
	/*  Else, button will be added without label in the selector */
	this.addingMethod = addingMethod;
	this.containerSelector = containerSelector;
	this.container = this.widget.find(this.containerSelector);
};

WidgetButtonManager.prototype.reInit = function () {
	this.widget = $('.' + this.wigetId);
	this.container = this.widget.find(this.containerSelector);
};

WidgetButtonManager.prototype.addButton = function (icon, label, onclick, extraParameter, cssClass, title) {
	/* Add a new button */
	if (this.addingMethod === 'menu' && !this.hasMenu()) {
		this.addMenu();
	}
    
	// Enable toolbar mode as the height of the menu is getting increased. Hard to
	// see all menues and click.
    this.menu.addClass('toolbar-mode');

	if (this.addingMethod === 'menu') {
		var buttonWithLabel = $('<div class="widget-menu-item with-label" title="' + label + '"><i class="' + icon + '"></i><span class="label">' + label + '</span></div>');
		if (title) {
			 buttonWithLabel = $('<div class="widget-menu-item with-label" title="' + title + '"><i class="' + icon + '"></i><span class="label">' + label + '</span></div>');
		}

		if (cssClass !== undefined) {
			buttonWithLabel.addClass(cssClass);
		}
		buttonWithLabel.off();
		buttonWithLabel.on('click', $.proxy(function (e) {
			this.closeMenu();
			if ($.isFunction(onclick)) {
				onclick.call(null, e, extraParameter);
			}
		}, this));
		this.menu.append(buttonWithLabel);
		return buttonWithLabel;
	} else {
		var buttonWithTitle = $('<div class="widget-menu-item with-title" title="' + label + '"><i class="' + icon + '"></i></div>');
		if (cssClass !== undefined) {
			buttonWithTitle.addClass(cssClass);
		}
		buttonWithTitle.off();
		buttonWithTitle.on('click', $.proxy(function (e) {
			this.closeMenu();
			if ($.isFunction(onclick)) {
				onclick.call(null, e, extraParameter);
			}
		}, this));
		if (this.menu) {
			this.menu.append(buttonWithTitle);
		}

		return buttonWithTitle;
	}
};

WidgetButtonManager.prototype.addMenu = function () {
	/* If the addingMethod is 'menu', add a menu to contain all buttons */
	var menuButton = $('<div class="widget-menu-button widgetHeaderButton fonticon fonticon-menu-dot" title="Menu"></div>');
	var menu = $('<div class="widget-menu-container hidden"></div>');
	this.menu = menu;

	menuButton.on('click', $.proxy(function () {
		this.menu.toggleClass('hidden');
	}, this));

	$('body').on('click', $.proxy(function (e) {
		var $e = $(e.target).closest('.widget-menu-button.fonticon-menu-dot');
		if ($e.length === 0) {
			this.closeMenu();
		}
	}, this));

	this.container.append(menuButton);
	this.container.append(menu);
};

WidgetButtonManager.prototype.hasMenu = function () {
	if (this.container.find('.widget-menu-container').length > 0) {
		this.menu = this.container.find('.widget-menu-container');
		return true;
	} else if (this.container.closest('.wuid').find('.widgetHeader .fonticon-menu-dot').length > 0) {
		this.menu = this.container;
		this.menuCreatedOutside = true;
		return true;
	}
	return false;
};

WidgetButtonManager.prototype.closeMenu = function () {
	if (this.menu) {
		if (!this.menuCreatedOutside) {
			this.menu.addClass('hidden');
		} else {
			this.menu.addClass('hidden');
			this.menu.closest('.menu-popup').removeClass('active').hide();
			this.widget.find('.fonticon-menu-dot.appspopup-button').removeClass('active').removeClass('appspopup-active');
		}
	}
};

WidgetButtonManager.prototype.hasButton = function (icon, label) {
	if (!this.hasMenu()) {
		return false;
	}
	var iconSelector = icon ? 'i.' + icon.replace(' ', '.') : 'i';
	return this.addingMethod === 'menu'
		? this.menu.find('.widget-menu-item.with-label:contains("' + label + '")').has(iconSelector).length > 0
		: this.menu.find('.widget-menu-item.with-title[title="' + label + '"]').has(iconSelector).length > 0;
};