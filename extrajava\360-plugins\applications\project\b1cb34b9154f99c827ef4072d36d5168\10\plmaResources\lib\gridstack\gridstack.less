/**
 * Manually converted from scss to less.
 */

@gridstack-columns: 12 ;
@horizontal_padding: 20px ;
@vertical_padding: 20px ;
@animation_speed: .3s ;

.transform(@value){
    -webkit-transform:@value;
       -moz-transform:@value;
        -ms-transform:@value;
         -o-transform:@value;
            transform:@value;
}
.transition(...){
    -webkit-transition:@arguments;
       -moz-transition:@arguments;
        -ms-transition:@arguments;
         -o-transition:@arguments;
            transition:@arguments;
}

:root .grid-stack-item > .ui-resizable-handle { filter: none; }

.grid-stack {
    position: relative;

    &.grid-stack-rtl {
        direction: ltr;

        > .grid-stack-item {
            direction: rtl;
        }
    }

    .grid-stack-placeholder > .placeholder-content {
        border: 1px dashed lightgray;
        margin: 0;
        position: absolute;
        top: 0;
        left: @horizontal_padding / 2;
        right: @horizontal_padding / 2;
        bottom: 0;
        width: auto;
        z-index: 0 !important;
        text-align: center;
    }

    > .grid-stack-item {
        min-width: 100% / @gridstack-columns;
        position: absolute;
        padding: 0;

        > .grid-stack-item-content {
            margin: 0;
            position: absolute;
            top: 0;
            left: @horizontal_padding / 2;
            right: @horizontal_padding / 2;
            bottom: 0;
            width: auto;
            z-index: 0 !important;
            overflow-x: hidden;
            overflow-y: auto;
        }

        > .ui-resizable-handle {
            position: absolute;
            font-size: 0.1px;
            display: block;
            -ms-touch-action: none;
            touch-action: none;
        }

        &.ui-resizable-disabled > .ui-resizable-handle,
        &.ui-resizable-autohide > .ui-resizable-handle { display: none; }

        &.ui-draggable-dragging,
        &.ui-resizable-resizing {
            z-index: 100;

            > .grid-stack-item-content,
            > .grid-stack-item-content {
                box-shadow: 1px 4px 6px rgba(0, 0, 0, 0.2);
                opacity: 0.8;
            }
        }

        > .ui-resizable-se,
        > .ui-resizable-sw {
            background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTYuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iQ2FwYV8xIiB4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjE2cHgiIGhlaWdodD0iMTZweCIgdmlld0JveD0iMCAwIDUxMS42MjYgNTExLjYyNyIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTExLjYyNiA1MTEuNjI3OyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxnPgoJPHBhdGggZD0iTTMyOC45MDYsNDAxLjk5NGgtMzYuNTUzVjEwOS42MzZoMzYuNTUzYzQuOTQ4LDAsOS4yMzYtMS44MDksMTIuODQ3LTUuNDI2YzMuNjEzLTMuNjE1LDUuNDIxLTcuODk4LDUuNDIxLTEyLjg0NSAgIGMwLTQuOTQ5LTEuODAxLTkuMjMxLTUuNDI4LTEyLjg1MWwtNzMuMDg3LTczLjA5QzI2NS4wNDQsMS44MDksMjYwLjc2LDAsMjU1LjgxMywwYy00Ljk0OCwwLTkuMjI5LDEuODA5LTEyLjg0Nyw1LjQyNCAgIGwtNzMuMDg4LDczLjA5Yy0zLjYxOCwzLjYxOS01LjQyNCw3LjkwMi01LjQyNCwxMi44NTFjMCw0Ljk0NiwxLjgwNyw5LjIyOSw1LjQyNCwxMi44NDVjMy42MTksMy42MTcsNy45MDEsNS40MjYsMTIuODUsNS40MjYgICBoMzYuNTQ1djI5Mi4zNThoLTM2LjU0MmMtNC45NTIsMC05LjIzNSwxLjgwOC0xMi44NSw1LjQyMWMtMy42MTcsMy42MjEtNS40MjQsNy45MDUtNS40MjQsMTIuODU0ICAgYzAsNC45NDUsMS44MDcsOS4yMjcsNS40MjQsMTIuODQ3bDczLjA4OSw3My4wODhjMy42MTcsMy42MTcsNy44OTgsNS40MjQsMTIuODQ3LDUuNDI0YzQuOTUsMCw5LjIzNC0xLjgwNywxMi44NDktNS40MjQgICBsNzMuMDg3LTczLjA4OGMzLjYxMy0zLjYyLDUuNDIxLTcuOTAxLDUuNDIxLTEyLjg0N2MwLTQuOTQ4LTEuODA4LTkuMjMyLTUuNDIxLTEyLjg1NCAgIEMzMzguMTQyLDQwMy44MDIsMzMzLjg1Nyw0MDEuOTk0LDMyOC45MDYsNDAxLjk5NHoiIGZpbGw9IiM2NjY2NjYiLz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8L3N2Zz4K);
            background-repeat: no-repeat;
            background-position: center;
            .transform(rotate(45deg));
        }

        > .ui-resizable-se {
            .transform(rotate(-45deg));
        }


        > .ui-resizable-nw { cursor: nw-resize; width: 20px; height: 20px; left: 10px; top: 0; }
        > .ui-resizable-n  { cursor: n-resize;  height: 10px; top: 0; left: 25px; right: 25px; }
        > .ui-resizable-ne { cursor: ne-resize; width: 20px; height: 20px; right: 10px; top: 0; }
        > .ui-resizable-e  { cursor: e-resize;  width: 10px; right: @horizontal_padding / 2; top: 15px; bottom: 15px; }
        > .ui-resizable-se { cursor: se-resize; width: 20px; height: 20px; right: 10px; bottom: 0; }
        > .ui-resizable-s  { cursor: s-resize;  height: 10px; left: 25px; bottom: 0; right: 25px; }
        > .ui-resizable-sw { cursor: sw-resize; width: 20px; height: 20px; left: 10px; bottom: 0; }
        > .ui-resizable-w  { cursor: w-resize;  width: 10px; left: @horizontal_padding / 2; top: 15px; bottom: 15px; }

        &.ui-draggable-dragging {
            &> .ui-resizable-handle {
                display: none !important;
            }
        }

        &[data-gs-width='1'] { width: (100% / @gridstack-columns) * 1; }
        &[data-gs-x='1'] { left: (100% / @gridstack-columns) * 1; }
        &[data-gs-min-width='1'] { min-width: (100% / @gridstack-columns) * 1; }
        &[data-gs-max-width='1'] { max-width: (100% / @gridstack-columns) * 1; }

        &[data-gs-width='2'] { width: (100% / @gridstack-columns) * 2; }
        &[data-gs-x='2'] { left: (100% / @gridstack-columns) * 2; }
        &[data-gs-min-width='2'] { min-width: (100% / @gridstack-columns) * 2; }
        &[data-gs-max-width='2'] { max-width: (100% / @gridstack-columns) * 2; }

        &[data-gs-width='3'] { width: (100% / @gridstack-columns) * 3; }
        &[data-gs-x='3'] { left: (100% / @gridstack-columns) * 3; }
        &[data-gs-min-width='3'] { min-width: (100% / @gridstack-columns) * 3; }
        &[data-gs-max-width='3'] { max-width: (100% / @gridstack-columns) * 3; }

        &[data-gs-width='4'] { width: (100% / @gridstack-columns) * 4; }
        &[data-gs-x='4'] { left: (100% / @gridstack-columns) * 4; }
        &[data-gs-min-width='4'] { min-width: (100% / @gridstack-columns) * 4; }
        &[data-gs-max-width='4'] { max-width: (100% / @gridstack-columns) * 4; }

        &[data-gs-width='5'] { width: (100% / @gridstack-columns) * 5; }
        &[data-gs-x='5'] { left: (100% / @gridstack-columns) * 5; }
        &[data-gs-min-width='5'] { min-width: (100% / @gridstack-columns) * 5; }
        &[data-gs-max-width='5'] { max-width: (100% / @gridstack-columns) * 5; }

        &[data-gs-width='6'] { width: (100% / @gridstack-columns) * 6; }
        &[data-gs-x='6'] { left: (100% / @gridstack-columns) * 6; }
        &[data-gs-min-width='6'] { min-width: (100% / @gridstack-columns) * 6; }
        &[data-gs-max-width='6'] { max-width: (100% / @gridstack-columns) * 6; }

        &[data-gs-width='7'] { width: (100% / @gridstack-columns) * 7; }
        &[data-gs-x='7'] { left: (100% / @gridstack-columns) * 7; }
        &[data-gs-min-width='7'] { min-width: (100% / @gridstack-columns) * 7; }
        &[data-gs-max-width='7'] { max-width: (100% / @gridstack-columns) * 7; }

        &[data-gs-width='8'] { width: (100% / @gridstack-columns) * 8; }
        &[data-gs-x='8'] { left: (100% / @gridstack-columns) * 8; }
        &[data-gs-min-width='8'] { min-width: (100% / @gridstack-columns) * 8; }
        &[data-gs-max-width='8'] { max-width: (100% / @gridstack-columns) * 8; }

        &[data-gs-width='9'] { width: (100% / @gridstack-columns) * 9; }
        &[data-gs-x='9'] { left: (100% / @gridstack-columns) * 9; }
        &[data-gs-min-width='9'] { min-width: (100% / @gridstack-columns) * 9; }
        &[data-gs-max-width='9'] { max-width: (100% / @gridstack-columns) * 9; }

        &[data-gs-width='10'] { width: (100% / @gridstack-columns) * 10; }
        &[data-gs-x='10'] { left: (100% / @gridstack-columns) * 10; }
        &[data-gs-min-width='10'] { min-width: (100% / @gridstack-columns) * 10; }
        &[data-gs-max-width='10'] { max-width: (100% / @gridstack-columns) * 10; }

        &[data-gs-width='11'] { width: (100% / @gridstack-columns) * 11; }
        &[data-gs-x='11'] { left: (100% / @gridstack-columns) * 11; }
        &[data-gs-min-width='11'] { min-width: (100% / @gridstack-columns) * 11; }
        &[data-gs-max-width='11'] { max-width: (100% / @gridstack-columns) * 11; }

        &[data-gs-width='12'] { width: (100% / @gridstack-columns) * 12; }
        &[data-gs-x='12'] { left: (100% / @gridstack-columns) * 12; }
        &[data-gs-min-width='12'] { min-width: (100% / @gridstack-columns) * 12; }
        &[data-gs-max-width='12'] { max-width: (100% / @gridstack-columns) * 12; }
    }

    &.grid-stack-animate,
    &.grid-stack-animate .grid-stack-item {
        .transition(left @animation_speed, top @animation_speed, height @animation_speed, width @animation_speed);
    }

    &.grid-stack-animate .grid-stack-item.ui-draggable-dragging,
    &.grid-stack-animate .grid-stack-item.ui-resizable-resizing,
    &.grid-stack-animate .grid-stack-item.grid-stack-placeholder{
        .transition(left .0s, top .0s, height .0s, width .0s);
    }
    
	&.grid-stack-one-column-mode {
		height: auto !important;
		&> .grid-stack-item {
	        position: relative !important;
	        width: auto !important;
	        left: 0 !important;
	        top: auto !important;
	        margin-bottom: @vertical_padding;
	        max-width: none !important;

	        &> .ui-resizable-handle { display: none; }
		}
	}
}
