<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>

<render:import parameters="customButtons"/>
<render:import parameters="extraCss" ignore="true"/>
<render:import parameters="uCssId"/>
<render:import parameters="positioning" ignore="true"/>

<%--check if there is at least one btn that should be displayed based on 'positioning' variable & not empty icon --%>
<c:set var="displayContainer" value="${false}" />
<c:forEach var="customButton" items="${customButtons}" varStatus="loop">
	<%--default position of btn is right if no position has been set in config--%>
	<c:set var="btnPosition" value="right" />
	<c:if test="${not empty customButton.positioning}">
		<c:set var="btnPosition" value="${customButton.positioning}" />
	</c:if>
	<c:if test="${not empty customButton.iconCss && positioning.equals(btnPosition)}">
		<c:set var="displayContainer" value="${true}" />
	</c:if>
</c:forEach>

<c:if test="${fn:length(customButtons) > 0 && displayContainer}">
	<div class="linkContainer ${extraCss}">
		<c:forEach var="customButton" items="${customButtons}" varStatus="loop">
			<%--default position of btn is right if no position has been set in config--%>
			<c:set var="btnPosition" value="right" />
			<c:if test="${not empty customButton.positioning}">
				<c:set var="btnPosition" value="${customButton.positioning}" />
			</c:if>
			<c:if test="${not empty customButton.iconCss && positioning.equals(btnPosition)}">
				<i class="custom-button custom-button-${loop.index} icon ${customButton.iconCss}"
				   title="${customButton.title}"></i>

				<render:renderScript position="READY">
					(function() {
					var button = $('.${uCssId} .custom-button.custom-button-${loop.index}');
					(${customButton.onInit}).call(button);

					button.on('click', function(e) {
					(${customButton.onClick}).call(button, e);
					});
					})();
				</render:renderScript>
			</c:if>
		</c:forEach>
	</div>
</c:if>