/**
 * Custom jQuery UI autocomplete widget.
 * Provides:
 * 	- categorization of facets
 * 	- exclude button
 * 	- display of the most used filters at the beginning
 * 	- filtering by label and by value
 * 
 * @param {Object} options - Widget options
 * @param {Function} options.exclude - Function to be called when a category is excluded. The item to exclude is passed.
 * @param {Function} options.removeMostUsedFilters - Function to be called when the most used filters must be removed.
 * 
 * @example
 * $('#myInput').quickFiltersAutocomplete({
 * 		exclude: myExcludeFunction,
 * 		removeMostUsedFilters: myRemoveMostUsedFiltersFunction
 * });
 */
$.widget('plma.quickFiltersAutocomplete', $.ui.autocomplete, {

	NOT_ITEM_CLASS: 'ui-autocomplete-not-item',

	/* Default options */
	options: {
		exclude: $.noop,
		removeMostUsedFilters: $.noop
	},

	/**
	 * @override
	 */
	_create: function() {
		this._super();
		this.widget().menu('option', 'items', '> :not(.' + this.NOT_ITEM_CLASS + ')');
	},

	/**
	 * @override
	 */
	_renderMenu: function($ul, items) {
		var currentFacet = '';
		/* Display "Most used filters" message */
		if (items.length > 0 && items[0].mostUsedFilter) {
			var $li = $('<li>').addClass(this.NOT_ITEM_CLASS + ' most-used-filters');
			$li.append($('<span>', {
				text: this._getMessage('widgets.activeFilters.quickFilters.mostUsedFilters')
			}));
			if (items[0].mostUsedFilter) {
				$li.append($('<span>', {
					class: 'fonticon fonticon-trash clear',
					title: this._getMessage('widgets.activeFilters.quickFilters.mostUsedFilters.clear')
				}).click(function() {
					this.options.removeMostUsedFilters.call();
				}.bind(this)))
			}
			$ul.append($li);
		}
		items.forEach(function(item) {
			/* Display "no results" message */
			if (!item.value) {
				$ul.append($('<li>', {
					class: this.NOT_ITEM_CLASS + ' no-results',
					text: item.label
				}));
			} else {
				var $li;
				if (item.facet !== currentFacet) {
					$ul.append($('<li>', {
						class: this.NOT_ITEM_CLASS + ' facet',
						text: item.facet
					}));
					currentFacet = item.facet;
				}
				$li = this._renderItemData($ul, item);
				if (item.facet) {
					$li.attr('aria-label', item.facet + ' : ' + item.label);
				}
			}
		}, this);
	},

	/**
	 * @override
	 */
	_renderItem: function($ul, item) {
		var $addButton = $('<span>', {
			class: 'categoryName',
			text: item.label,
			title: item.isZapFilter ? this._getMessage('widgets.activeFilters.tooltip.cancel') : ''
		});
		var $excludeButton = item.isZapFilter
			? $()
			: $('<span>')
				.addClass('fonticon fonticon-block exclude')
				.click(function(e) {
					this.options.exclude.call(null, item);
					this.element.val('').keyup(); /* HACK: refresh suggests */
				}.bind(this));
		return $('<li>')
			.append($excludeButton, $addButton)
			.appendTo($ul);
	},

	/**
	 * Returns a translation.
	 * 
	 * @param {String} code - Message code
	 * @returns {String} Translation for the given code
	 */
	_getMessage: function(code) {
		return mashupI18N.get('activeFilters', code);
	},

	/**
	 * @override
	 */
	_initSource: function() {
		this._super();
		if ($.isArray(this.options.source)) {
			var array = this.options.source;
			this.source = function(request, response) {
				response(this.filter(array, request.term));
			}
		}
	},

	/**
	 * Filter by label AND by value.
	 * 
	 * @override
	 */
	filter: function(array, term) {
		var matcher = new RegExp($.ui.autocomplete.escapeRegex(term), 'i');
		return $.grep(array, function(value) {
			return matcher.test(value.label) || matcher.test(value.value);
		});
	}
});

/**
 * QuickFilters widget.
 * 
 * @param {Object} options - Widget options
 * @param {String} options.suggestUrl - Suggest URL
 * @param {String} options.pageId - Page id
 * @param {String} options.pageName - Page name
 * @param {String} options.wuid - Widget id
 * @param {String} options.feedName - Feed name
 * @param {Object} options.facetsList - Allowed facets: Object with (facet id, facet label) pairs
 * @param {String} options.droppableItemsSelector - Selector of the droppable items. They must have a "data-categoryid" attribute.
 * @param {Object} options.currentFilters - Current filters applied to the page
 * @param {String[]} options.currentFilters.refined - Refined categories
 * @param {String[]} options.currentFilters.excluded - Excluded categories
 * 
 * @example
 * $('#myDiv').quickFilters({
 *	// options
 * });
 */
$.widget('plma.quickFilters', {

	INVALID_DROPPABLE_ITEM_CLASS: 'invalid-droppable-item',

	/* Default options */
	options: {
		facetsList: [],
		droppableItemsSelector: '.refine-link', /* Selector used in the refine panel */
		currentFilters: {
			refined: [],
			excluded: []
		}
	},

	/**
	 * Constructor.
	 */
	_create: function() {
		this.url = new BuildUrl(window.location.href);
		this.filters = [];
		this.$filtersContainer = this.element.find('.quickFiltersContainer');
		this.$input = this.element.find('input.quickFilters-suggest');

		this._initAutocompletion();
		this._initSuggest();
		this._initDropping();
		this._bindEvents();
	},

	/**
	 * Binds all the events used by this widget.
	 */
	_bindEvents: function() {
		this._on(this.element, {
			'click .quickFilters-apply': function(e) {
				this._onApply();
			},
			'click .quickFilters-cancel': function(e) {
				this._onCancel();
			},
			'quickfiltersaddfilter': function(e, filter) {
				this._handleButtons();
				/* Disable dropping */
				this._getDraggableItems(filter).addClass(this.INVALID_DROPPABLE_ITEM_CLASS);
			},
			'quickfiltersremovefilter': function(e, filter) {
				this._handleButtons();
				/* Enable dropping */
				this._getDraggableItems(filter).removeClass(this.INVALID_DROPPABLE_ITEM_CLASS);
			}
		});

		this.$input.click(function() {
			if (!this.$input.val()) {
				this.$input.quickFiltersAutocomplete('option', 'source', this._getMostUsedFilters());
				this.$input.quickFiltersAutocomplete('search');
			}
		}.bind(this));
	},

	/**
	 * Shows buttons if there is at least one filter, hides otherwise.
	 */
	_handleButtons: function() {
		var $buttons = this.element.find('.quickFilters-cancel, .quickFilters-apply');
		if (this.filters.length === 0) {
			this._hide($buttons, 'fadeOut');
		} else {
			this._show($buttons, 'fadeIn');
		}
	},

	/**
	 * Returns the most used filters, defined by the refineCounter.
	 * @returns {Object[]} Most used filters
	 */
	_getMostUsedFilters: function() {
		var filters = [];
		if (window.refineCounter) {
			var state = window.refineCounter.getState();
			var tmp = [];
			for (var categoryId in state) {
				var properties = state[categoryId];
				properties.categoryId = categoryId;
				tmp.push(properties);
			}
			filters = tmp
				.map(function(property) {
					return this._buildItemFromCategoryId(property.categoryId, property.label, { mostUsedFilter: true, count: property.count });
				}, this)
				.filter(this._isValidEntry.bind(this))
				/* Sort by facet (asc), then by count (desc), and if same, by category name (asc) */
				.sort(function(filter, otherFilter) {
					var compare = filter.facet.localeCompare(otherFilter.facet);
					if (compare !== 0) {
						return compare;
					} else {
						var countDiff = otherFilter.count - filter.count;
						return countDiff !== 0
							? countDiff
							: filter.label.localeCompare(otherFilter.label);
					}
				});
		}
		return filters;
	},

	/**
	 * Returns draggable elements related to a filter.
	 * 
	 * @param {Object} filter - Filter related to the draggable elements
	 * @returns {jQuery} Draggable jQuery elements that are related to a filter
	 */
	_getDraggableItems: function(filter) {
		return $(this.options.droppableItemsSelector)
			.filter(function(i, e) {
				var categoryId = $(e).data('categoryid');
				return this._isSameFilter(filter.value, categoryId);
			}.bind(this));
	},

	/**
	 * Initializes custom autocomplete.
	 */
	_initAutocompletion: function() {
		this.$input.quickFiltersAutocomplete({
			appendTo: '.' + this.element.prop('class'),
			minLength: 0,
			source: this._getMostUsedFilters(),
			exclude: this._onFilterExclude.bind(this),
			removeMostUsedFilters: this._onRemoveMostUsedFilters.bind(this),
			select: function(e, ui) {
				var filter = ui.item;
				e.preventDefault();
				if (!this._hasFilter(filter)) {
					this._onFilterAdd(filter);
				}
				$(e.target).val('').keyup(); /* HACK: refresh suggests */
			}.bind(this),
			focus: function(e, ui) {
				/* Prevent input to have the item's value */
				e.preventDefault();
			},
			response: function(e, ui) {
				if (!ui.content.length) {
					ui.content.push({
						value: '',
						label: this._getMessage('widgets.activeFilters.quickFilters.noResults'),
						mostUsedFilter: !$(e.target).val()
					});
				}
			}.bind(this)
		});
	},

	/**
	 * Initializes suggest.
	 */
	_initSuggest: function() {
		this.$input.enablePlmaSuggest(
			this.options.suggestUrl,
			this.options.wuid,
			{
				autoSubmit: false,
				suggestBoxClass: 'quickFilters-suggest',
				successCallback: function(suggestions) {
					var source = $.isPlainObject(suggestions) && suggestions.entries
						? this._getSource(suggestions)
						: this._getMostUsedFilters();
					this.$input.quickFiltersAutocomplete('option', 'source', source);
				}.bind(this),
				errorCallback: function (xhr, textStatus, suggestContext) {
					$.notify(this._getMessage('widgets.activeFilters.quickFilters.suggests.error'), 'error');
					if (console && console.warn) {
						console.warn('An error occurred while contacting the server. Make sure that suggests are already built.');
					}
				}.bind(this)
			}
		);
	},

	/**
	 * Initializes dropping. Accepts filters from the refine panel.
	 */
	_initDropping: function() {
		this.element.find('.quickFiltersContainer-droppable').droppable({
			accept: this.options.droppableItemsSelector + ':not(.' + this.INVALID_DROPPABLE_ITEM_CLASS + ')',
			activeClass: 'highlight',
			hoverClass: 'hover',
			drop: this._onFilterDrop.bind(this),
			tolerance: 'touch'
		});
	},

	/**
	 * Handles a filter that has been dropped.
	 * 
	 * @param {Event} e
	 * @param {Object} ui
	 */
	_onFilterDrop: function(e, ui) {
		var categoryId = ui.draggable.data('categoryid'),
			categoryLabel = ui.draggable.text().trim() || ui.draggable.parents('.category').find('.refineName a').text().trim(),
			entry = this._buildItemFromCategoryId(categoryId, categoryLabel);
		if (ui.draggable.hasClass('exclude')) {
			this._onFilterExclude(entry);
		} else {
			this._onFilterAdd(entry);
		}
	},

	/**
	 * Removes all the most used filters and refresh the autocomplete menu.
	 */
	_onRemoveMostUsedFilters: function() {
		if (window.refineCounter) {
			window.refineCounter.removeAll();
			this.$input.quickFiltersAutocomplete('option', 'source', []);
			this.$input.quickFiltersAutocomplete('search', '');
		}
	},

	/**
	 * Returns an sorted and filtered array of suggestions.
	 * 
	 * @param {Object[]} suggestions - Array of suggestions from the suggest
	 * @returns {Object[]} Sanitized array of suggestions
	 */
	_getSource: function(suggestions) {
		return suggestions.entries
			.map(function(entry) {
				/**
				 * Should match either
				 * facet__<facet>: <title>
				 * facet__<facet>: "<title>"
				 * facet__<facet>: <id>#<title>
				 * facet__<facet>: "<id>#<title>"
				 */
				var matches = entry.entry.match(/facet__(.*): (?:"?)([^#"]*)(?:#?)([^"]*)(?:"?)/),
					facetId = this._getMessage(matches[1]).split(':')[0],
					categoryId = this._getMessage(matches[2]),
					categoryName = matches[3].length > 0 ? this._getMessage(matches[3]) : categoryId;
				return this._buildItem(entry.entry, facetId, categoryId, {label: categoryName});
			}, this)
			/* Reject invalid entries */
			.filter(function(entry, i, entries) {
				return this._isValidEntry(entry);
			}, this)
			/* Sort by facet and then by category */
			.sort(function(entryA, entryB) {
				var compare = entryA.facet.localeCompare(entryB.facet);
				return compare !== 0
					? compare
					: entryA.label.localeCompare(entryB.label);
			});
	},

	/**
	 * A duplicated entry is an entry that has the same label as an other one
	 * and with invalid value (normalized label should be different from the id).
	 * 
	 * @param {Object[]} entries - Array of entries
	 * @param {Object} entry - Entry to check
	 * @returns {Boolean} true if it is a duplicate, false otherwise
	 */
	_isDuplicatedEntry: function(entries, entry) {
		var entriesWithSameLabel = entries.filter(function(e) {
			return e.label === entry.label;
		});
		/* No other entry with same label */
		if (entriesWithSameLabel.length < 2) {
			return false;
		} else {
			/* Keep the entry with id#title */
			var shortCategoryId = entry.value.match(/f\/(.*)\/(.*)/)[2];
			return shortCategoryId === this._normalizeString(entry.label);
		}
	},

	/**
	 * Returns true if entry has an allowed facet and category.
	 * 
	 * @param {Object} entry - Entry to check
	 * @returns {Boolean} true if entry is valid, false otherwise
	 */
	_isValidEntry: function(entry) {
		var isAllowedFacet = this.options.facetsList.hasOwnProperty(entry.facetId.split('/')[0]);
		return isAllowedFacet && !this._hasFilter(entry);
	},

	/**
	 * Returns an autocomplete-compliant item.
	 * 
	 * @param {String} itemId - Item id
	 * @param {String} facetId - Facet id
	 * @param {String} shortCategoryId - Short category id (can be a category label)
	 * @param {Object} additionalData - Data to add
	 * @returns {Object} A filter
	 */
	_buildItem: function(itemId, facetId, shortCategoryId, additionalData) {
		var categoryId = 'f/' + facetId + '/' + this._normalizeString(shortCategoryId);
		return $.extend(
			{},
			{
				id: itemId,
				facetId: facetId,
				facet: this.options.facetsList[facetId.split('/')[0]] || facetId,
				isZapFilter: this._isZapFilter(categoryId),
				/* label and value are both mandatory for the autocomplete widget */
				label: shortCategoryId,
				value: categoryId
			},
			additionalData
		);
	},

	/**
	 * Returns an autocomplete-compliant item with category-related properties.
	 * 
	 * @param {String} categoryId - Category id
	 * @param {String} categoryLabel - Category label
	 * @param {Object} additionalData - Data to add
	 * @returns {Object} A filter
	 */
	_buildItemFromCategoryId: function(categoryId, categoryLabel, additionalData) {
		var matches = categoryId.match(/f\/(.*)\/(.*)/),
			facetId = matches[1],
			shortCategoryId = matches[2];
		return this._buildItem(
			'facet__' + facetId + ': "' + categoryLabel + '"',
			facetId,
			shortCategoryId,
			$.extend({}, additionalData, {label: categoryLabel})
		);
	},

	/**
	 * Returns true if the filter is a zap filter
	 * 
	 * @param {Object} filter - Filter to check
	 * @returns {Boolean} true if it is a zap filter, false otherwise
	 */
	_isZapFilter: function(filter) {
		return this.options.currentFilters.refined
			.concat(this.options.currentFilters.excluded)
			.some(function(otherFilter) {
				return this._isSameFilter(otherFilter, filter);
			}, this);
	},

	/**
	 * Adds a filter (can be a zap refine).
	 * 
	 * @param {Object} filter - Filter to add
	 */
	_onFilterAdd: function(filter) {
		/* The filter can be a zap refine: check if the filter is already applied at the beginning */
		if (filter.isZapFilter) {
			filter.parameter = BuildUrl.ZAP_CATEGORY_PARAMETER;
			filter.class = 'zapFilter';
		} else {
			filter.parameter = BuildUrl.REFINE_CATEGORY_PARAMETER;
			filter.class = 'selectedFilter';
			filter.value = '+' + filter.value;
		}
		if (!this._hasFilter(filter)) {
			this._addFilter(filter);
		}
	},

	/**
	 * Excludes a filter.
	 * 
	 * @param {Object} filter - Filter to exclude
	 */
	_onFilterExclude: function(filter) {
		filter.value = '-' + filter.value;
		filter.parameter = BuildUrl.REFINE_CATEGORY_PARAMETER;
		filter.class = 'excludedFilter';
		if (!this._hasFilter(filter)) {
			this._addFilter(filter);
		}
	},

	/**
	 * Adds a filter in the UI.
	 * 
	 * @param {Object} filter - Filter to add
	 */
	_addFilter: function(filter) {
		var $filter = $('<a>', {
			class: 'activeFilter quickFilter ' + filter.class,
			'data-id': filter.id,
			title: this._getMessage('widgets.activeFilters.tooltip.cancel')
		}).append(
			$('<span>').addClass('facetName').text(filter.facet),
			$('<span>').addClass('categoryName').text(filter.label),
			$('<span>').addClass('fonticon fonticon-cancel')
		).click(function(e) {
			this._onClickFilter($(e.currentTarget))
		}.bind(this));
		this.filters.push(filter);
		this.$filtersContainer.append($filter);
		this._trigger('addFilter', null, filter);
	},

	/**
	 * Handler to call when a filter is clicked. Removes it.
	 * 
	 * @param {jQuery} $filter - Filter as jQuery element to remove
	 */
	_onClickFilter: function($filter) {
		var filter = this.filters.find(function(filter) {
			return filter.id === $filter.data('id');
		});
		if (filter) {
			this._removeFilter(filter);
		}
	},

	/**
	 * Removes a filter in the UI.
	 * 
	 * @param {Object} filter 
	 */
	_removeFilter: function(filter) {
		var index = this.filters.findIndex(function(otherFilter) {
			return filter.id === otherFilter.id;
		});
		if (index !== -1) {
			this.filters.splice(index, 1);
			this.$filtersContainer
				.find('.quickFilter')
				.filter(function(i, e) {
					return $(e).data('id') === filter.id;
				})
				.remove();
			this._trigger('removeFilter', null, filter);
		}
	},

	/**
	 * Returns true if a filter is already present (doesn't include page and saved filters).
	 * 
	 * @param {Object} filter - Filter to test
	 * @returns {Boolean} true if the filter is present, false otherwise
	 */
	_hasFilter: function(filter) {
		return this.filters.some(function(otherFilter) {
			return otherFilter.id === filter.id;
		});
	},

	/**
	 * Applies all the filters.
	 */
	_onApply: function() {
		this.filters.forEach(function(filter) {
			if (filter.parameter === BuildUrl.REFINE_CATEGORY_PARAMETER && this._isFilterCanceled(filter)) {
				if (this._isSameFilter(this.url.getParameter(this.options.feedName + BuildUrl.REFINE_CATEGORY_PARAMETER), filter.value)) {
					this.url.removeParameter(this.options.feedName + BuildUrl.ZAP_CATEGORY_PARAMETER);
				} else {
					this.url.removeParameter(this.options.feedName + BuildUrl.ZAP_CATEGORY_PARAMETER);
					this.url.addParameter(this.options.feedName + BuildUrl.REFINE_CATEGORY_PARAMETER, filter.value);
				}
			} else {
				this.url.addParameter(this.options.feedName + filter.parameter, filter.value);
			}

			/* Increment counter */
			if (window.refineCounter) {
				var normalizedCategory = this._normalizeFilter(filter.value);
				/* Do not increment if it is a zap filter */
				if (normalizedCategory && !filter.isZapFilter) {
					var newState = window.refineCounter.incrementCategory(normalizedCategory);
					if (!newState[normalizedCategory].label) {
						window.refineCounter.setCategoryLabel(normalizedCategory, filter.label);
					}
				}
			}
		}, this);
		window.location.assign(this.url.toString());
	},

	/**
	 * Returns true if a filter is refined and zap refined at the same time.
	 * 
	 * @param {Object} filter - Filter to check
	 * @returns {Boolean} true if the filter is canceled, false otherwise
	 */
	_isFilterCanceled: function(filter) {
		var refines = this.url.getParameter(this.options.feedName + BuildUrl.REFINE_CATEGORY_PARAMETER) || [];
		var zapRefines = this.url.getParameter(this.options.feedName + BuildUrl.ZAP_CATEGORY_PARAMETER) || [];
		var isRefined = refines.some(function(refine) {
			return this._isSameFilter(filter.value, refine);
		}, this);
		var isZapRefined = zapRefines.some(function(zapRefine) {
			return this._isSameFilter(filter.value, zapRefine);
		}, this);
		return isRefined && isZapRefined;
	},

	/**
	 * Removes all the filters.
	 */
	_onCancel: function() {
		var length = this.filters.length;
		for (var i = 0; i < length; i++) {
			if (this.filters.length) {
				this._removeFilter(this.filters[0]);
			}
		}
	},

	/**
	 * Returns a translation.
	 * 
	 * @param {String} code - Message code
	 * @returns {String} Translation for the given code
	 */
	_getMessage: function(code) {
		return mashupI18N.get('activeFilters', code);
	},

	/**
	 * Checks if two filters are the same.
	 * 
	 * @param {String} filter - First filter to compare
	 * @param {String} otherFilter - Second filter to compare
	 * @returns {Boolean} true if they are both the same, false otherwise
	 */
	_isSameFilter: function(filter, otherFilter) {
		return this._normalizeFilter(filter) === this._normalizeFilter(otherFilter);
	},

	/**
	 * Returns a normalized filter.
	 * 
	 * @param {String} filter - Filter to normalize
	 * @returns {String} Normalized filter
	 * @example
	 * this._normalizeFilter('+f/facet/category') // returns 'f/facet/category'
	 * this._normalizeFilter('f/facet/category') // returns 'f/facet/category'
	 */
	_normalizeFilter: function(filter) {
		if (typeof filter === 'string' && !filter.startsWith('f')) {
			return filter.substring(1);
		}
		return filter;
	},

	/**
	 * Returns a normalized (i.e. lowercase and without accents) string.
	 * 
	 * @param {String} str - String to normalize
	 * @returns {String} the normalized string
	 */
	_normalizeString: function(str) {
		return _.deburr(str.toLowerCase());
	}
});
