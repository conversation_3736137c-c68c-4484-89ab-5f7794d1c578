.mashup {
	.searchWidget {
		&.plmaFacetHistoryMetrics {
			color: white;
			.metrics {
				flex-direction: column;
				border: none;
				padding: 10px;
				padding-bottom: 0;
				padding-left: 0;
				height: 160px;
				.top-bar {
					flex: 1;
					padding-left: 10px;
					.metric {
						font-size: 36px;
						font-weight: bold;
						position: relative;
						top: 10px;
					}
					.link {
						float: right;
						cursor: pointer;
						font-size: 20px;
						margin-top: 4px;
					}
				}
				.middle-lane {
					font-size: 18px;
					flex: 1;
					padding-left: 10px;
				}
				.chart-container {
					flex: 3;
					width: 100%;
					margin: 0;
					position: relative;
					.highcharts-point {
						opacity: 0.5;
					}
					.highcharts-container {
						width: 100% !important;
						position: absolute !important;
						.highcharts-root {
							width: 100% !important;
							.highcharts-plot-background {
								height: 102% !important;
								y: -1 !important;
							}

						}
					}
				}
			}
		}
		.metrics {
			height: 125px;
			font-variant: small-caps;
			.wordbreak();

			.metric-wrapper {
				float: left;
				height: 100%;
			}

			.metric-subwrapper {
				border-left: 1px solid @cblock-border;
				height: 100%;
			}

			.metric-wrapper:first-child .metric-subwrapper {
				border-left-width: 0;
			}

			.main {
				font-size: 3vw;
				text-align: center;
				font-weight: bold;
				line-height: 70px;
				text-shadow: 0 1px 2px #ccc;
				min-height: 80px;
				padding-top: 5px;
			}

			.main span {
				vertical-align: middle;
				font-size: 60%;
			}

			a {
				cursor: pointer;
			}

			.positive {
				color: #58b017;
			}

			.negative {
				color: #B23600;
			}

			.description {
				font-weight: bold;
				font-size: 16px;
				text-align: center;
			}

			position: relative;
			.doc-container {
				font-variant: normal;
				color: @ctext;
				display: flex;
				.container {
					border-radius: 4px;
					border: 1px solid @cblock-border;
					flex: 1 0 0px;
				}
			}
		}
	}
}