/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:27 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.web;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class mashup_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(8);
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/reporting.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fcomponent_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005fname_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetEntry_0026_005fvar_005ffeed_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005fentry_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fevalTriggers_0026_005ffeeds_005fcomponent;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fdoEval_005fdefaultValue_005fcomponent_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fcomponent_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fresolveLayout_0026_005fvar_005flayout_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderLater_0026_005fflush_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fflush_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005freporting_005fpageExecutionEnd_005fnobody;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fcomponent_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005fname_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetEntry_0026_005fvar_005ffeed_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005fentry_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fevalTriggers_0026_005ffeeds_005fcomponent = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fdoEval_005fdefaultValue_005fcomponent_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fcomponent_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fresolveLayout_0026_005fvar_005flayout_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderLater_0026_005fflush_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fflush_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005freporting_005fpageExecutionEnd_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fcomponent_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005fname_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetEntry_0026_005fvar_005ffeed_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005fentry_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fevalTriggers_0026_005ffeeds_005fcomponent.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fdoEval_005fdefaultValue_005fcomponent_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fcomponent_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.release();
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fconfig_005fresolveLayout_0026_005fvar_005flayout_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
    _005fjspx_005ftagPool_005frender_005frenderLater_0026_005fflush_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fflush_005fnobody.release();
    _005fjspx_005ftagPool_005freporting_005fpageExecutionEnd_005fnobody.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=utf-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f0(_jspx_page_context))
        return;
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f1(_jspx_page_context))
        return;
      out.write('\n');
      out.write('\n');
      out.write('\n');
      if (_jspx_meth_search_005fgetFeed_005f0(_jspx_page_context))
        return;
      out.write('\n');
      if (_jspx_meth_search_005fgetEntry_005f0(_jspx_page_context))
        return;
      out.write('\n');
      if (_jspx_meth_search_005fgetFeed_005f1(_jspx_page_context))
        return;
      out.write('\n');
      out.write('\n');
      if (_jspx_meth_config_005fevalTriggers_005f0(_jspx_page_context))
        return;
      out.write('\n');
      if (_jspx_meth_reporting_005fpageExecutionEnd_005f0(_jspx_page_context))
        return;
      out.write('\n');
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_config_005fgetOption_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f0.setParent(null);
      // /WEB-INF/jsp/web/mashup.jsp(11,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setVar("favicon");
      // /WEB-INF/jsp/web/mashup.jsp(11,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setName("favicon");
      // /WEB-INF/jsp/web/mashup.jsp(11,0) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${v10MashupUI}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(11,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setDefaultValue("/resources/images/favicon.ico");
      int[] _jspx_push_body_count_config_005fgetOption_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f0 = _jspx_th_config_005fgetOption_005f0.doStartTag();
        if (_jspx_th_config_005fgetOption_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fcomponent_005fnobody.reuse(_jspx_th_config_005fgetOption_005f0);
      _jspx_th_config_005fgetOption_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f1.setParent(null);
      // /WEB-INF/jsp/web/mashup.jsp(12,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setVar("cssTheme");
      // /WEB-INF/jsp/web/mashup.jsp(12,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setName("cssTheme");
      // /WEB-INF/jsp/web/mashup.jsp(12,0) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${v10MashupUI}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(12,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f1 = _jspx_th_config_005fgetOption_005f1.doStartTag();
        if (_jspx_th_config_005fgetOption_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fcomponent_005fnobody.reuse(_jspx_th_config_005fgetOption_005f1);
      _jspx_th_config_005fgetOption_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFeed_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFeed
    com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag _jspx_th_search_005fgetFeed_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag) _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005fname_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag.class);
    boolean _jspx_th_search_005fgetFeed_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFeed_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFeed_005f0.setParent(null);
      // /WEB-INF/jsp/web/mashup.jsp(15,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeed_005f0.setVar("pageFeed");
      // /WEB-INF/jsp/web/mashup.jsp(15,0) name = name type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeed_005f0.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(15,0) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeed_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupAPI}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFeed_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFeed_005f0 = _jspx_th_search_005fgetFeed_005f0.doStartTag();
        if (_jspx_th_search_005fgetFeed_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFeed_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFeed_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFeed_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005fname_005ffeeds_005fnobody.reuse(_jspx_th_search_005fgetFeed_005f0);
      _jspx_th_search_005fgetFeed_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFeed_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFeed_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetEntry_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getEntry
    com.exalead.cv360.searchui.view.jspapi.search.GetEntryTag _jspx_th_search_005fgetEntry_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetEntryTag) _005fjspx_005ftagPool_005fsearch_005fgetEntry_0026_005fvar_005ffeed_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetEntryTag.class);
    boolean _jspx_th_search_005fgetEntry_005f0_reused = false;
    try {
      _jspx_th_search_005fgetEntry_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetEntry_005f0.setParent(null);
      // /WEB-INF/jsp/web/mashup.jsp(16,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetEntry_005f0.setVar("pageEntry");
      // /WEB-INF/jsp/web/mashup.jsp(16,0) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetEntry_005f0.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageFeed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetEntry_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetEntry_005f0 = _jspx_th_search_005fgetEntry_005f0.doStartTag();
        if (_jspx_th_search_005fgetEntry_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetEntry_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetEntry_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetEntry_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetEntry_0026_005fvar_005ffeed_005fnobody.reuse(_jspx_th_search_005fgetEntry_005f0);
      _jspx_th_search_005fgetEntry_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetEntry_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetEntry_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFeed_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFeed
    com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag _jspx_th_search_005fgetFeed_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag) _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005fentry_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag.class);
    boolean _jspx_th_search_005fgetFeed_005f1_reused = false;
    try {
      _jspx_th_search_005fgetFeed_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFeed_005f1.setParent(null);
      // /WEB-INF/jsp/web/mashup.jsp(17,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeed_005f1.setVar("firstFeed");
      // /WEB-INF/jsp/web/mashup.jsp(17,0) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeed_005f1.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageEntry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFeed_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFeed_005f1 = _jspx_th_search_005fgetFeed_005f1.doStartTag();
        if (_jspx_th_search_005fgetFeed_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFeed_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFeed_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFeed_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005fentry_005fnobody.reuse(_jspx_th_search_005fgetFeed_005f1);
      _jspx_th_search_005fgetFeed_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFeed_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fgetFeed_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fevalTriggers_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:evalTriggers
    com.exalead.cv360.searchui.view.jspapi.config.EvalTriggersTag _jspx_th_config_005fevalTriggers_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.EvalTriggersTag) _005fjspx_005ftagPool_005fconfig_005fevalTriggers_0026_005ffeeds_005fcomponent.get(com.exalead.cv360.searchui.view.jspapi.config.EvalTriggersTag.class);
    boolean _jspx_th_config_005fevalTriggers_005f0_reused = false;
    try {
      _jspx_th_config_005fevalTriggers_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fevalTriggers_005f0.setParent(null);
      // /WEB-INF/jsp/web/mashup.jsp(19,0) name = component type = com.exalead.cv360.searchui.configuration.v10.TriggerContainer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fevalTriggers_005f0.setComponent((com.exalead.cv360.searchui.configuration.v10.TriggerContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI}", com.exalead.cv360.searchui.configuration.v10.TriggerContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(19,0) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fevalTriggers_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupAPI}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_config_005fevalTriggers_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fevalTriggers_005f0 = _jspx_th_config_005fevalTriggers_005f0.doStartTag();
        if (_jspx_eval_config_005fevalTriggers_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_config_005fevalTriggers_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_config_005fevalTriggers_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_config_005fevalTriggers_005f0);
          }
          do {
            out.write('\n');
            out.write('\n');
            out.write('	');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_config_005fgetOption_005f2(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write('\n');
            out.write('	');
            if (_jspx_meth_config_005fgetOption_005f3(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write('\n');
            out.write('	');
            if (_jspx_meth_config_005fgetOption_005f4(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write('\n');
            out.write('	');
            if (_jspx_meth_config_005fgetOption_005f5(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write('\n');
            out.write('	');
            if (_jspx_meth_config_005fgetOptionsComposite_005f0(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write('\n');
            out.write('	');
            if (_jspx_meth_config_005fgetOptionsComposite_005f1(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write('	');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_config_005fgetOption_005f6(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\n");
            out.write("\n");
            out.write("	<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${i18nLang}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" xml:lang=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${i18nLang}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\">\n");
            out.write("		<head>\n");
            out.write("			<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n");
            out.write("			<meta name=\"description\" content=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${description}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" />\n");
            out.write("			<meta name=\"baseurl\" content=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("/\" />\n");
            out.write("			");
            if (_jspx_meth_c_005fif_005f0(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\n");
            out.write("\n");
            out.write("			");
            out.write("\n");
            out.write("			<title>");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${title}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("</title>\n");
            out.write("\n");
            out.write("			");
            out.write("\n");
            out.write("			");
            if (_jspx_meth_c_005fforEach_005f0(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\n");
            out.write("			\n");
            out.write("			");
            out.write("\n");
            out.write("			");
            if (_jspx_meth_c_005fforEach_005f1(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\n");
            out.write("\n");
            out.write("			<link rel=\"canonical\" href=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${canonicalURL}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" />\n");
            out.write("			<link rel=\"shortcut icon\" type=\"image/ico\" href=\"");
            if (_jspx_meth_c_005furl_005f0(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\" />\n");
            out.write("\n");
            out.write("			");
            out.write("\n");
            out.write("			");
            if (_jspx_meth_render_005fdefinition_005f0(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\n");
            out.write("\n");
            out.write("			");
            out.write("\n");
            out.write("			");
            if (_jspx_meth_c_005fif_005f1(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\n");
            out.write("		</head>\n");
            out.write("\n");
            out.write("		<body id=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" class=\"mashup mashup-style web ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${bodyClass}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(' ');
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssClass}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(' ');
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssTheme}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\">\n");
            out.write("			<div id=\"mainWrapper\" style=\"width:");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI.layout.width}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI.layout.widthFormat}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\">\n");
            out.write("				");
            if (_jspx_meth_c_005fchoose_005f0(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\n");
            out.write("			</div>\n");
            out.write("\n");
            out.write("			");
            if (_jspx_meth_render_005fdefinition_005f3(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\n");
            out.write("\n");
            out.write("			");
            if (_jspx_meth_render_005frenderLater_005f0(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\n");
            out.write("			");
            if (_jspx_meth_render_005frenderScript_005f0(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\n");
            out.write("			");
            out.write("\n");
            out.write("			");
            if (_jspx_meth_c_005fif_005f2(_jspx_th_config_005fevalTriggers_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
              return true;
            out.write("\n");
            out.write("		</body>\n");
            out.write("	</html>\n");
            int evalDoAfterBody = _jspx_th_config_005fevalTriggers_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_config_005fevalTriggers_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_config_005fevalTriggers_005f0[0]--;
          }
        }
        if (_jspx_th_config_005fevalTriggers_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fevalTriggers_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fevalTriggers_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fevalTriggers_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fevalTriggers_0026_005ffeeds_005fcomponent.reuse(_jspx_th_config_005fevalTriggers_005f0);
      _jspx_th_config_005fevalTriggers_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fevalTriggers_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fevalTriggers_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fdoEval_005fdefaultValue_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(22,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setVar("title");
      // /WEB-INF/jsp/web/mashup.jsp(22,1) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setName("title");
      // /WEB-INF/jsp/web/mashup.jsp(22,1) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(22,1) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${firstFeed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(22,1) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setDefaultValue("${i18n['html.title']}");
      // /WEB-INF/jsp/web/mashup.jsp(22,1) name = doEval type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setDoEval(true);
      int[] _jspx_push_body_count_config_005fgetOption_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f2 = _jspx_th_config_005fgetOption_005f2.doStartTag();
        if (_jspx_th_config_005fgetOption_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fdoEval_005fdefaultValue_005fcomponent_005fnobody.reuse(_jspx_th_config_005fgetOption_005f2);
      _jspx_th_config_005fgetOption_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f3_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(23,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setVar("description");
      // /WEB-INF/jsp/web/mashup.jsp(23,1) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setName("description");
      // /WEB-INF/jsp/web/mashup.jsp(23,1) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(23,1) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${firstFeed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(23,1) name = doEval type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setDoEval(true);
      int[] _jspx_push_body_count_config_005fgetOption_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f3 = _jspx_th_config_005fgetOption_005f3.doStartTag();
        if (_jspx_th_config_005fgetOption_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody.reuse(_jspx_th_config_005fgetOption_005f3);
      _jspx_th_config_005fgetOption_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f4 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f4_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f4.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(24,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setVar("cssId");
      // /WEB-INF/jsp/web/mashup.jsp(24,1) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setName("cssId");
      // /WEB-INF/jsp/web/mashup.jsp(24,1) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(24,1) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setDefaultValue("body");
      int[] _jspx_push_body_count_config_005fgetOption_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f4 = _jspx_th_config_005fgetOption_005f4.doStartTag();
        if (_jspx_th_config_005fgetOption_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fcomponent_005fnobody.reuse(_jspx_th_config_005fgetOption_005f4);
      _jspx_th_config_005fgetOption_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f4, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f5 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f5_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f5.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(25,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setVar("cssClass");
      // /WEB-INF/jsp/web/mashup.jsp(25,1) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setName("cssClass");
      // /WEB-INF/jsp/web/mashup.jsp(25,1) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(25,1) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f5 = _jspx_th_config_005fgetOption_005f5.doStartTag();
        if (_jspx_th_config_005fgetOption_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fcomponent_005fnobody.reuse(_jspx_th_config_005fgetOption_005f5);
      _jspx_th_config_005fgetOption_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f5, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(26,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setVar("metasHeader");
      // /WEB-INF/jsp/web/mashup.jsp(26,1) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setName("metasHeader");
      // /WEB-INF/jsp/web/mashup.jsp(26,1) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(26,1) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${firstFeed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(26,1) name = separator type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setSeparator("=");
      // /WEB-INF/jsp/web/mashup.jsp(26,1) name = doEval type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setDoEval(true);
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f0 = _jspx_th_config_005fgetOptionsComposite_005f0.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f0);
      _jspx_th_config_005fgetOptionsComposite_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(27,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setVar("metasHttpEquiv");
      // /WEB-INF/jsp/web/mashup.jsp(27,1) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setName("metasHttpEquiv");
      // /WEB-INF/jsp/web/mashup.jsp(27,1) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(27,1) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${firstFeed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/web/mashup.jsp(27,1) name = doEval type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setDoEval(true);
      // /WEB-INF/jsp/web/mashup.jsp(27,1) name = separator type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setSeparator("##");
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f1 = _jspx_th_config_005fgetOptionsComposite_005f1.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005ffeed_005fdoEval_005fcomponent_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f1);
      _jspx_th_config_005fgetOptionsComposite_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f6 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f6_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f6.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(28,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setVar("customLayout");
      // /WEB-INF/jsp/web/mashup.jsp(28,1) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setName("customLayout");
      // /WEB-INF/jsp/web/mashup.jsp(28,1) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_config_005fgetOption_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f6 = _jspx_th_config_005fgetOption_005f6.doStartTag();
        if (_jspx_th_config_005fgetOption_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fcomponent_005fnobody.reuse(_jspx_th_config_005fgetOption_005f6);
      _jspx_th_config_005fgetOption_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f6, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(35,3) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${csrf != null}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\n");
          out.write("				<meta name=\"CSRF-Token\" content=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${csrf}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" />\n");
          out.write("			");
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f0_reused = false;
    try {
      _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(43,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setVar("meta");
      // /WEB-INF/jsp/web/mashup.jsp(43,3) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/web/mashup.jsp(43,3) '${metasHeader}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${metasHeader}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
        if (_jspx_eval_c_005fforEach_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\n");
            out.write("				<meta name=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${meta[0]}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" content=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${meta[1]}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" />\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
      _jspx_th_c_005fforEach_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f1 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f1_reused = false;
    try {
      _jspx_th_c_005fforEach_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(48,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f1.setVar("meta");
      // /WEB-INF/jsp/web/mashup.jsp(48,3) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f1.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/web/mashup.jsp(48,3) '${metasHttpEquiv}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${metasHttpEquiv}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int[] _jspx_push_body_count_c_005fforEach_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f1 = _jspx_th_c_005fforEach_005f1.doStartTag();
        if (_jspx_eval_c_005fforEach_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\n");
            out.write("				<meta http-equiv=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${meta[0]}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" content=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${meta[1]}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" />\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f1);
      _jspx_th_c_005fforEach_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f0 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f0_reused = false;
    try {
      _jspx_th_c_005furl_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(53,52) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f0.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${favicon}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int _jspx_eval_c_005furl_005f0 = _jspx_th_c_005furl_005f0.doStartTag();
      if (_jspx_th_c_005furl_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f0);
      _jspx_th_c_005furl_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f0, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fdefinition_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:definition
    com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag _jspx_th_render_005fdefinition_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fdefinition_005f0);
    try {
      _jspx_th_render_005fdefinition_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005fdefinition_005f0.setParent(_jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(56,3) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fdefinition_005f0.setName("resources");
      _jspx_th_render_005fdefinition_005f0.setJspBody(new Helper( 0, _jspx_page_context, _jspx_th_render_005fdefinition_005f0, _jspx_push_body_count_config_005fevalTriggers_005f0));
      _jspx_th_render_005fdefinition_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fdefinition_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f0);
    try {
      _jspx_th_render_005fparameter_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f0.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(57,4) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setName("position");
      // /WEB-INF/jsp/web/mashup.jsp(57,4) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setValue("HEADER");
      _jspx_th_render_005fparameter_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(61,3) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${debugMode.debugModeEnable == true}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\n");
          out.write("				");
          if (_jspx_meth_render_005fdefinition_005f1(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
            return true;
          out.write("\n");
          out.write("			");
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fdefinition_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:definition
    com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag _jspx_th_render_005fdefinition_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fdefinition_005f1);
    try {
      _jspx_th_render_005fdefinition_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005fdefinition_005f1.setParent(_jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/web/mashup.jsp(62,4) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fdefinition_005f1.setName("debug");
      _jspx_th_render_005fdefinition_005f1.setJspBody(new Helper( 1, _jspx_page_context, _jspx_th_render_005fdefinition_005f1, _jspx_push_body_count_config_005fevalTriggers_005f0));
      _jspx_th_render_005fdefinition_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fdefinition_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f1);
    try {
      _jspx_th_render_005fparameter_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f1.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(63,5) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setName("position");
      // /WEB-INF/jsp/web/mashup.jsp(63,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setValue("HEADER");
      _jspx_th_render_005fparameter_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\n");
          out.write("					");
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
            return true;
          out.write("\n");
          out.write("					");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
            return true;
          out.write("\n");
          out.write("				");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(71,5) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customLayout != null}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\n");
          out.write("						");
          if (_jspx_meth_config_005fresolveLayout_005f0(_jspx_th_c_005fwhen_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
            return true;
          out.write("\n");
          out.write("						");
          if (_jspx_meth_render_005ftemplate_005f0(_jspx_th_c_005fwhen_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
            return true;
          out.write("	\n");
          out.write("					");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fresolveLayout_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:resolveLayout
    com.exalead.cv360.searchui.view.jspapi.config.ResolveLayoutTag _jspx_th_config_005fresolveLayout_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.ResolveLayoutTag) _005fjspx_005ftagPool_005fconfig_005fresolveLayout_0026_005fvar_005flayout_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.ResolveLayoutTag.class);
    boolean _jspx_th_config_005fresolveLayout_005f0_reused = false;
    try {
      _jspx_th_config_005fresolveLayout_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fresolveLayout_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(72,6) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fresolveLayout_005f0.setVar("layoutPath");
      // /WEB-INF/jsp/web/mashup.jsp(72,6) name = layout type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fresolveLayout_005f0.setLayout((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customLayout}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_config_005fresolveLayout_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fresolveLayout_005f0 = _jspx_th_config_005fresolveLayout_005f0.doStartTag();
        if (_jspx_th_config_005fresolveLayout_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fresolveLayout_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fresolveLayout_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fresolveLayout_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fresolveLayout_0026_005fvar_005flayout_005fnobody.reuse(_jspx_th_config_005fresolveLayout_005f0);
      _jspx_th_config_005fresolveLayout_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fresolveLayout_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fresolveLayout_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f0);
    try {
      _jspx_th_render_005ftemplate_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f0.setParent(_jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(73,6) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${layoutPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005ftemplate_005f0.setJspBody(new Helper( 2, _jspx_page_context, _jspx_th_render_005ftemplate_005f0, _jspx_push_body_count_config_005fevalTriggers_005f0));
      _jspx_th_render_005ftemplate_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f2);
    try {
      _jspx_th_render_005fparameter_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f2.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(74,7) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setName("layout");
      // /WEB-INF/jsp/web/mashup.jsp(74,7) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI.layout}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f3 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f3);
    try {
      _jspx_th_render_005fparameter_005f3.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f3.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(75,7) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setName("parentFeed");
      // /WEB-INF/jsp/web/mashup.jsp(75,7) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageFeed}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f3.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f3);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f4 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f4);
    try {
      _jspx_th_render_005fparameter_005f4.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f4.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(76,7) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setName("parentEntry");
      // /WEB-INF/jsp/web/mashup.jsp(76,7) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageEntry}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f4.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f4);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f5 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f5);
    try {
      _jspx_th_render_005fparameter_005f5.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f5.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(77,7) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setName("wrapperCssClass");
      // /WEB-INF/jsp/web/mashup.jsp(77,7) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setValue("pageLayout");
      _jspx_th_render_005fparameter_005f5.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f5);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("						\n");
          out.write("						");
          if (_jspx_meth_render_005fdefinition_005f2(_jspx_th_c_005fotherwise_005f0, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
            return true;
          out.write("\n");
          out.write("					");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fdefinition_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:definition
    com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag _jspx_th_render_005fdefinition_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fdefinition_005f2);
    try {
      _jspx_th_render_005fdefinition_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005fdefinition_005f2.setParent(_jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(81,6) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fdefinition_005f2.setName("tableLayout");
      _jspx_th_render_005fdefinition_005f2.setJspBody(new Helper( 3, _jspx_page_context, _jspx_th_render_005fdefinition_005f2, _jspx_push_body_count_config_005fevalTriggers_005f0));
      _jspx_th_render_005fdefinition_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fdefinition_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f6 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f6);
    try {
      _jspx_th_render_005fparameter_005f6.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f6.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(82,7) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setName("layout");
      // /WEB-INF/jsp/web/mashup.jsp(82,7) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mashupUI.layout}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f6.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f6);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f7 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f7);
    try {
      _jspx_th_render_005fparameter_005f7.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f7.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(83,7) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setName("parentFeed");
      // /WEB-INF/jsp/web/mashup.jsp(83,7) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageFeed}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f7.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f7);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f8 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f8);
    try {
      _jspx_th_render_005fparameter_005f8.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f8.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(84,7) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f8.setName("parentEntry");
      // /WEB-INF/jsp/web/mashup.jsp(84,7) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f8.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageEntry}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f8.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f8);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f9 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f9);
    try {
      _jspx_th_render_005fparameter_005f9.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f9.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(85,7) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f9.setName("wrapperCssClass");
      // /WEB-INF/jsp/web/mashup.jsp(85,7) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f9.setValue("pageLayout");
      _jspx_th_render_005fparameter_005f9.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f9);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fdefinition_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:definition
    com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag _jspx_th_render_005fdefinition_005f3 = new com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fdefinition_005f3);
    try {
      _jspx_th_render_005fdefinition_005f3.setJspContext(_jspx_page_context);
      _jspx_th_render_005fdefinition_005f3.setParent(_jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(91,3) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fdefinition_005f3.setName("resources");
      _jspx_th_render_005fdefinition_005f3.setJspBody(new Helper( 4, _jspx_page_context, _jspx_th_render_005fdefinition_005f3, _jspx_push_body_count_config_005fevalTriggers_005f0));
      _jspx_th_render_005fdefinition_005f3.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fdefinition_005f3);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f10 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f10);
    try {
      _jspx_th_render_005fparameter_005f10.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f10.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(92,4) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f10.setName("position");
      // /WEB-INF/jsp/web/mashup.jsp(92,4) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f10.setValue("BODY");
      _jspx_th_render_005fparameter_005f10.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f10);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderLater_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderLater
    com.exalead.cv360.searchui.view.jspapi.render.RenderLaterTag _jspx_th_render_005frenderLater_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderLaterTag) _005fjspx_005ftagPool_005frender_005frenderLater_0026_005fflush_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.render.RenderLaterTag.class);
    boolean _jspx_th_render_005frenderLater_005f0_reused = false;
    try {
      _jspx_th_render_005frenderLater_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderLater_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(95,3) name = flush type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderLater_005f0.setFlush(java.lang.Boolean.valueOf("true"));
      int[] _jspx_push_body_count_render_005frenderLater_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderLater_005f0 = _jspx_th_render_005frenderLater_005f0.doStartTag();
        if (_jspx_th_render_005frenderLater_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderLater_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderLater_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderLater_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderLater_0026_005fflush_005fnobody.reuse(_jspx_th_render_005frenderLater_005f0);
      _jspx_th_render_005frenderLater_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderLater_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderLater_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fflush_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f0_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(96,3) name = flush type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f0.setFlush(java.lang.Boolean.valueOf("true"));
      int[] _jspx_push_body_count_render_005frenderScript_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f0 = _jspx_th_render_005frenderScript_005f0.doStartTag();
        if (_jspx_th_render_005frenderScript_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fflush_005fnobody.reuse(_jspx_th_render_005frenderScript_005f0);
      _jspx_th_render_005frenderScript_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_config_005fevalTriggers_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_config_005fevalTriggers_005f0);
      // /WEB-INF/jsp/web/mashup.jsp(98,3) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${debugMode.debugModeEnable == true}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\n");
          out.write("				");
          if (_jspx_meth_render_005fdefinition_005f4(_jspx_th_c_005fif_005f2, _jspx_page_context, _jspx_push_body_count_config_005fevalTriggers_005f0))
            return true;
          out.write("\n");
          out.write("			");
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fdefinition_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_config_005fevalTriggers_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:definition
    com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag _jspx_th_render_005fdefinition_005f4 = new com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fdefinition_005f4);
    try {
      _jspx_th_render_005fdefinition_005f4.setJspContext(_jspx_page_context);
      _jspx_th_render_005fdefinition_005f4.setParent(_jspx_th_c_005fif_005f2);
      // /WEB-INF/jsp/web/mashup.jsp(99,4) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fdefinition_005f4.setName("debug");
      _jspx_th_render_005fdefinition_005f4.setJspBody(new Helper( 5, _jspx_page_context, _jspx_th_render_005fdefinition_005f4, _jspx_push_body_count_config_005fevalTriggers_005f0));
      _jspx_th_render_005fdefinition_005f4.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fdefinition_005f4);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f11 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f11);
    try {
      _jspx_th_render_005fparameter_005f11.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f11.setParent(_jspx_parent);
      // /WEB-INF/jsp/web/mashup.jsp(100,5) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f11.setName("position");
      // /WEB-INF/jsp/web/mashup.jsp(100,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f11.setValue("FOOTER");
      _jspx_th_render_005fparameter_005f11.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f11);
    }
    return false;
  }

  private boolean _jspx_meth_reporting_005fpageExecutionEnd_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  reporting:pageExecutionEnd
    com.exalead.cv360.searchui.view.jspapi.reporting.PageExecutionEndTag _jspx_th_reporting_005fpageExecutionEnd_005f0 = (com.exalead.cv360.searchui.view.jspapi.reporting.PageExecutionEndTag) _005fjspx_005ftagPool_005freporting_005fpageExecutionEnd_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.reporting.PageExecutionEndTag.class);
    boolean _jspx_th_reporting_005fpageExecutionEnd_005f0_reused = false;
    try {
      _jspx_th_reporting_005fpageExecutionEnd_005f0.setPageContext(_jspx_page_context);
      _jspx_th_reporting_005fpageExecutionEnd_005f0.setParent(null);
      int[] _jspx_push_body_count_reporting_005fpageExecutionEnd_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_reporting_005fpageExecutionEnd_005f0 = _jspx_th_reporting_005fpageExecutionEnd_005f0.doStartTag();
        if (_jspx_th_reporting_005fpageExecutionEnd_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_reporting_005fpageExecutionEnd_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_reporting_005fpageExecutionEnd_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_reporting_005fpageExecutionEnd_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005freporting_005fpageExecutionEnd_005fnobody.reuse(_jspx_th_reporting_005fpageExecutionEnd_005f0);
      _jspx_th_reporting_005fpageExecutionEnd_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_reporting_005fpageExecutionEnd_005f0, _jsp_getInstanceManager(), _jspx_th_reporting_005fpageExecutionEnd_005f0_reused);
    }
    return false;
  }

  private class Helper
      extends org.apache.jasper.runtime.JspFragmentHelper
  {
    private jakarta.servlet.jsp.tagext.JspTag _jspx_parent;
    private int[] _jspx_push_body_count;

    public Helper( int discriminator, jakarta.servlet.jsp.JspContext jspContext, jakarta.servlet.jsp.tagext.JspTag _jspx_parent, int[] _jspx_push_body_count ) {
      super( discriminator, jspContext, _jspx_parent );
      this._jspx_parent = _jspx_parent;
      this._jspx_push_body_count = _jspx_push_body_count;
    }
    public boolean invoke0( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\n");
      out.write("				");
      if (_jspx_meth_render_005fparameter_005f0(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\n");
      out.write("			");
      return false;
    }
    public boolean invoke1( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\n");
      out.write("					");
      if (_jspx_meth_render_005fparameter_005f1(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\n");
      out.write("				");
      return false;
    }
    public boolean invoke2( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\n");
      out.write("							");
      if (_jspx_meth_render_005fparameter_005f2(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\n");
      out.write("							");
      if (_jspx_meth_render_005fparameter_005f3(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\n");
      out.write("							");
      if (_jspx_meth_render_005fparameter_005f4(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\n");
      out.write("							");
      if (_jspx_meth_render_005fparameter_005f5(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("					\n");
      out.write("						");
      return false;
    }
    public boolean invoke3( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\n");
      out.write("							");
      if (_jspx_meth_render_005fparameter_005f6(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\n");
      out.write("							");
      if (_jspx_meth_render_005fparameter_005f7(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\n");
      out.write("							");
      if (_jspx_meth_render_005fparameter_005f8(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\n");
      out.write("							");
      if (_jspx_meth_render_005fparameter_005f9(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\n");
      out.write("						");
      return false;
    }
    public boolean invoke4( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\n");
      out.write("				");
      if (_jspx_meth_render_005fparameter_005f10(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\n");
      out.write("			");
      return false;
    }
    public boolean invoke5( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\n");
      out.write("					");
      if (_jspx_meth_render_005fparameter_005f11(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\n");
      out.write("				");
      return false;
    }
    public void invoke( java.io.Writer writer )
      throws jakarta.servlet.jsp.JspException
    {
      jakarta.servlet.jsp.JspWriter out = null;
      if( writer != null ) {
        out = this.jspContext.pushBody(writer);
      } else {
        out = this.jspContext.getOut();
      }
      try {
        Object _jspx_saved_JspContext = this.jspContext.getELContext().getContext(jakarta.servlet.jsp.JspContext.class);
        this.jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,this.jspContext);
        switch( this.discriminator ) {
          case 0:
            invoke0( out );
            break;
          case 1:
            invoke1( out );
            break;
          case 2:
            invoke2( out );
            break;
          case 3:
            invoke3( out );
            break;
          case 4:
            invoke4( out );
            break;
          case 5:
            invoke5( out );
            break;
        }
        jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,_jspx_saved_JspContext);
      }
      catch( java.lang.Throwable e ) {
        if (e instanceof jakarta.servlet.jsp.SkipPageException)
            throw (jakarta.servlet.jsp.SkipPageException) e;
        throw new jakarta.servlet.jsp.JspException( e );
      }
      finally {
        if( writer != null ) {
          this.jspContext.popBody();
        }
      }
    }
  }
}
