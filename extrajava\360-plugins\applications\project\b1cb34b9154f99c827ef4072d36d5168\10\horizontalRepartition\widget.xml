<?xml version="1.0" encoding="UTF-8" ?>
<Widget name="Horizontal repartition" group="PLM Analytics/Visualizations" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	
	<Description>Displays facet category sizes stacked on an horizontal bar.</Description>
	
	<Preview>
		<![CDATA[
			
		]]>
	</Preview>

	<Includes>
		<Include type="css" path="css/style.less" />
	</Includes>

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
    <SupportFeedsId arity="ONE"/>
    <SupportWidgetsId arity="ZERO"/>
    <SupportI18N supported="true"/>
    
    <OptionsGroup name="Display">
    	<Option id="displayHeader" name="Display header">
    		<Description>Whether to display the widget header or not.</Description>
    		<Values>
    			<Value>true</Value>
    			<Value>false</Value>
    		</Values>
    	</Option>
    	<Option id="title" name="Title" isEvaluated="true">
    		<Description>A title for your KPI.</Description>
    	</Option>
        <Option id="colors" name="Colors">
        	<Description>The set of colors to use to render the bar.</Description>
        </Option>
        <Option id="thickness" name="Thickness">
        	<Description>The thickness of the bar, in px.</Description>
        </Option>
    </OptionsGroup>
    <OptionsGroup name="Data">
        <Option arity="ONE" name="Facet" id="facetName">
            <Description>The facet to display. </Description>
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
        </Option>
        <Option arity="ONE" name="Aggregation" id="aggregation">
            <Description>Aggregation function used for calculating sizes.</Description>
			<Functions>
				<ContextMenu>Aggregations('facetName')</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
            <Values>
                <Value>count</Value>
            </Values>
        </Option>
        <Option id="filter" name="Filter" arity="ONE">
        	<Description>Whether to display only the specified categories (Include), or to display all categories except the specified ones (Exclude).</Description>
        	<Values>
        		<Value>Include</Value>
        		<Value>Exclude</Value>
        	</Values>
        </Option>
        <Option id="filterList" name="Categories" arity="ZERO_OR_ONE">
        	<Description>A comma-separated list of categories to include/exclude.</Description>
        	<Functions>
        		<ContextMenu>EvalMeta()</ContextMenu>
        	</Functions>
        </Option>
    
    </OptionsGroup>
    <OptionsGroup name="Advanced">
    	<OptionComposite id="categoryStyle" name="Custom styles" arity="ZERO_OR_MANY">
    		<Description>You can specify a color for each category.</Description>
    		<Option id="categoryName" name="Name" arity="ONE">
    			<Description>The name of the category.</Description>
    			<Functions>
	        		<ContextMenu>EvalCategory()</ContextMenu>
	        	</Functions>
    		</Option> 
    		<Option id="color" name="Color">
    			<Description>The color to display this category in.</Description>
    		</Option>
    	</OptionComposite>
    </OptionsGroup>
    
    <Platforms>
        <Platform type="web" supported="true"/>
        <Platform type="mobile" supported="false"/>
    </Platforms>

	<DefaultValues>
		<DefaultValue name="aggregation">count</DefaultValue>
		<DefaultValue name="numberFormat">0</DefaultValue>
		<DefaultValue name="filter">Exclude</DefaultValue>
		<DefaultValue name="colors">#5E61A3, #597DAB, #5296B5, #4AB2C2, #42CFCC, #7557BD, #8052B0, #874FA3, #944A99, #8F3869, #BD4D6E, #B04A42, #A34F36, #995E38, #8F7038, #BD8538, #B08A2E, #A38C24, #999426, #668F29</DefaultValue>
		<DefaultValue name="thickness">32</DefaultValue>
	</DefaultValues>
    
</Widget>
