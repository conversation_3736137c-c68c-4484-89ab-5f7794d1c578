/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:32 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.plmaCharts2;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class javascript_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_0;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_1;

static {
  _jspx_fnmap_0= org.apache.jasper.runtime.ProtectedFunctionMapper.getInstance();
  _jspx_fnmap_0.mapFunction("fn:length", org.apache.taglibs.standard.functions.Functions.class, "length", new Class[] {java.lang.Object.class});
  _jspx_fnmap_0.mapFunction("fn:split", org.apache.taglibs.standard.functions.Functions.class, "split", new Class[] {java.lang.String.class, java.lang.String.class});
  _jspx_fnmap_1= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:trim", org.apache.taglibs.standard.functions.Functions.class, "trim", new Class[] {java.lang.String.class});
}

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(16);
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/widget.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/i18n.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/list.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/url.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/data.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/highcharts.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/map.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdoEval_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fhighchartsJSON_0026_005fvar_005fmaxCategories_005fforceRefineOnMulti_005fforceRefineOnFeeds_005fforceRefineOnFacets_005ffeeds_005fcategoriesOrder_005fbaseUrl_005faxisLabelsY_005faxisLabelX_005fadditionalCategories;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fhighchartsSerie_0026_005fserieConfig_005ffeeds_005fcustomSeriesClass_005fcustomSeries_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFeedUri_0026_005fvar_005ffeed_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005ffacet_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fhighchartsSerieName_0026_005fvar_005fserieConfig_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFacetType_0026_005fvar_005ffacet_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetFacetDimensions_0026_005fid2_005fid1_005ffeeds_005ffacetId_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdoEval_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fhighchartsJSON_0026_005fvar_005fmaxCategories_005fforceRefineOnMulti_005fforceRefineOnFeeds_005fforceRefineOnFacets_005ffeeds_005fcategoriesOrder_005fbaseUrl_005faxisLabelsY_005faxisLabelX_005fadditionalCategories = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fhighchartsSerie_0026_005fserieConfig_005ffeeds_005fcustomSeriesClass_005fcustomSeries_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFeedUri_0026_005fvar_005ffeed_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005ffacet_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fhighchartsSerieName_0026_005fvar_005fserieConfig_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFacetType_0026_005fvar_005ffacet_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetFacetDimensions_0026_005fid2_005fid1_005ffeeds_005ffacetId_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdoEval_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.release();
    _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fhighchartsJSON_0026_005fvar_005fmaxCategories_005fforceRefineOnMulti_005fforceRefineOnFeeds_005fforceRefineOnFacets_005ffeeds_005fcategoriesOrder_005fbaseUrl_005faxisLabelsY_005faxisLabelX_005fadditionalCategories.release();
    _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fhighchartsSerie_0026_005fserieConfig_005ffeeds_005fcustomSeriesClass_005fcustomSeries_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFeedUri_0026_005fvar_005ffeed_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005ffacet_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fhighchartsSerieName_0026_005fvar_005fserieConfig_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFacetType_0026_005fvar_005ffacet_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetFacetDimensions_0026_005fid2_005fid1_005ffeeds_005ffacetId_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType.release();
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptionsComposite_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptions_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptions_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f4(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f5(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f6(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f7(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptionsComposite_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f8(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f9(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptions_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f10(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f11(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f12(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f13(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f14(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f15(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f16(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f17(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f18(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptionComposite_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f19(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptionsComposite_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fset_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("var waterFallOptions = {};\r\n");
      if (_jspx_meth_c_005fforEach_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fset_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_list_005fnew_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fforEach_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_list_005fnew_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fforEach_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_plma_005fhighchartsJSON_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("var data = ");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${highchartsJSON}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write(";\r\n");
      out.write("\r\n");
      out.write("var reloadParams = [];\r\n");
      if (_jspx_meth_c_005fforEach_005f4(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("var selectedFeeds = [];\r\n");
      if (_jspx_meth_c_005fforEach_005f5(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("var feedsDefinition = [];\r\n");
      if (_jspx_meth_search_005fforEachFeed_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("var dataSeriesConfiguration = [];\r\n");
      out.write("var highchartsSeriesName = [];\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_search_005fgetFeed_005f0(_jspx_page_context))
        return;
      out.write(' ');
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fforEach_005f6(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("var plmaChart2Options = {\r\n");
      out.write("	data: data,\r\n");
      out.write("	dataProcessor: ");
      if (_jspx_meth_config_005fgetOption_005f20(_jspx_page_context))
        return;
      out.write(",\r\n");
      out.write("	userOptions: ");
      if (_jspx_meth_config_005fgetOption_005f21(_jspx_page_context))
        return;
      out.write(",\r\n");
      out.write("	defaultOptions: {\r\n");
      out.write("	chartImage: '");
      if (_jspx_meth_c_005furl_005f0(_jspx_page_context))
        return;
      out.write("',\r\n");
      out.write("	chartName: 'plmaCharts2',\r\n");
      out.write("	chartDisplayName: '");
      if (_jspx_meth_i18n_005fmessage_005f1(_jspx_page_context))
        return;
      out.write("',\r\n");
      out.write("	chartTitle: ");
      if (_jspx_meth_string_005fescape_005f2(_jspx_page_context))
        return;
      out.write(" /* Used for multipleCharts */\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("	},\r\n");
      out.write("	baseUrl: '");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${target}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("',\r\n");
      out.write("	enableRefine: ");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${enableRefines == 'true'}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write(",\r\n");
      out.write("	reload: {\r\n");
      out.write("	enable: ");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${activateReloadChart == 'true'}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write(",\r\n");
      out.write("	params: reloadParams\r\n");
      out.write("	},\r\n");
      out.write("	hideEmpty: '");
      if (_jspx_meth_config_005fgetOption_005f23(_jspx_page_context))
        return;
      out.write("',\r\n");
      out.write("	isWaterFall: ");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isWaterFall == 'true'}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write(",\r\n");
      out.write("	waterFallOptions: waterFallOptions,\r\n");
      out.write("    selectedFeeds: selectedFeeds,\r\n");
      out.write("    dataSeriesConfiguration: dataSeriesConfiguration,\r\n");
      out.write("    feedsDefinition: feedsDefinition,\r\n");
      out.write("	fullScreen: ");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fullScreen}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\r\n");
      out.write("};\r\n");
      out.write("var plmaChart = new PLMAChart2('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write('\'');
      out.write(',');
      out.write('\'');
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("', plmaChart2Options);\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("var buttonOptions = {};\r\n");
      out.write("buttonOptions.buttons = [];\r\n");
      if (_jspx_meth_c_005fif_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f24(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fif_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fif_005f4(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fif_005f5(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fforEach_005f7(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("var plmaChartButtons = new PlmaChartButton('");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("', buttonOptions, plmaChart2Options);\r\n");
      out.write("plmaChartButtons.init();\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(16,0) name = varFeeds type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarFeeds("feeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(16,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setParameters("chartContainerId,cssId,uCssId,fullScreen,buttons,series");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(18,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setVar("enableRefines");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(18,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setName("enableRefines");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(18,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f0 = _jspx_th_config_005fgetOption_005f0.doStartTag();
        if (_jspx_th_config_005fgetOption_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f0);
      _jspx_th_config_005fgetOption_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(19,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setVar("target");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(19,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setName("target");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(19,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f1 = _jspx_th_config_005fgetOption_005f1.doStartTag();
        if (_jspx_th_config_005fgetOption_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f1);
      _jspx_th_config_005fgetOption_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(20,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setVar("facetType");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(20,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setName("facetType");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(20,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setDefaultValue("normal");
      int[] _jspx_push_body_count_config_005fgetOption_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f2 = _jspx_th_config_005fgetOption_005f2.doStartTag();
        if (_jspx_th_config_005fgetOption_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f2);
      _jspx_th_config_005fgetOption_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdoEval_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f3_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(21,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setVar("facetsList");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(21,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setName("facetsList");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(21,0) name = doEval type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setDoEval(true);
      int[] _jspx_push_body_count_config_005fgetOption_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f3 = _jspx_th_config_005fgetOption_005f3.doStartTag();
        if (_jspx_th_config_005fgetOption_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdoEval_005fnobody.reuse(_jspx_th_config_005fgetOption_005f3);
      _jspx_th_config_005fgetOption_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(22,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setVar("serieConfiguration");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(22,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setName("basedOn");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(22,0) name = mapIndex type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setMapIndex(true);
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f0 = _jspx_th_config_005fgetOptionsComposite_005f0.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f0);
      _jspx_th_config_005fgetOptionsComposite_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptions_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptions
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag _jspx_th_config_005fgetOptions_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag) _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag.class);
    boolean _jspx_th_config_005fgetOptions_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOptions_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptions_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(23,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f0.setVar("forceRefineOnFeeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(23,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f0.setName("forceRefineOnFeeds");
      int[] _jspx_push_body_count_config_005fgetOptions_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptions_005f0 = _jspx_th_config_005fgetOptions_005f0.doStartTag();
        if (_jspx_th_config_005fgetOptions_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptions_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptions_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptions_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOptions_005f0);
      _jspx_th_config_005fgetOptions_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptions_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptions_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptions_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptions
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag _jspx_th_config_005fgetOptions_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag) _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag.class);
    boolean _jspx_th_config_005fgetOptions_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOptions_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptions_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(24,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f1.setVar("forceRefineOnFacets");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(24,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f1.setName("forceRefineOnFacets");
      int[] _jspx_push_body_count_config_005fgetOptions_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptions_005f1 = _jspx_th_config_005fgetOptions_005f1.doStartTag();
        if (_jspx_th_config_005fgetOptions_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptions_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptions_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptions_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOptions_005f1);
      _jspx_th_config_005fgetOptions_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptions_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptions_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f4 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f4_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f4.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(25,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setVar("forceRefineOnMulti");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(25,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setName("forceRefineOnMulti");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(25,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f4 = _jspx_th_config_005fgetOption_005f4.doStartTag();
        if (_jspx_th_config_005fgetOption_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f4);
      _jspx_th_config_005fgetOption_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f4, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f5(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f5 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f5_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f5.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f5.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(26,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setVar("maxCategories");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(26,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setName("maxCategories");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(26,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setDefaultValue("0");
      int[] _jspx_push_body_count_config_005fgetOption_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f5 = _jspx_th_config_005fgetOption_005f5.doStartTag();
        if (_jspx_th_config_005fgetOption_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f5);
      _jspx_th_config_005fgetOption_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f5, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f6(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f6 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f6_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f6.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f6.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(27,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setVar("additionalCategories");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(27,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setName("additionalCategories");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(27,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setDefaultValue("Hide");
      int[] _jspx_push_body_count_config_005fgetOption_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f6 = _jspx_th_config_005fgetOption_005f6.doStartTag();
        if (_jspx_th_config_005fgetOption_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f6);
      _jspx_th_config_005fgetOption_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f6, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f7(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f7 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f7_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f7.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f7.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(29,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setVar("isGrouping");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(29,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setName("grouping");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(29,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f7 = _jspx_th_config_005fgetOption_005f7.doStartTag();
        if (_jspx_th_config_005fgetOption_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f7);
      _jspx_th_config_005fgetOption_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f7, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(30,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setVar("dataGroups");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(30,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setName("dataGroups");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(30,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f1 = _jspx_th_config_005fgetOptionsComposite_005f1.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f1);
      _jspx_th_config_005fgetOptionsComposite_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f8(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f8 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f8_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f8.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f8.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(31,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setVar("disableRefine");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(31,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setName("disableRefine");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(31,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f8 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f8 = _jspx_th_config_005fgetOption_005f8.doStartTag();
        if (_jspx_th_config_005fgetOption_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f8[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f8.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f8.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f8);
      _jspx_th_config_005fgetOption_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f8, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f9(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f9 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f9_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f9.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f9.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(32,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setVar("xAxisLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(32,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setName("xAxisLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(32,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f9 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f9 = _jspx_th_config_005fgetOption_005f9.doStartTag();
        if (_jspx_th_config_005fgetOption_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f9[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f9.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f9.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f9);
      _jspx_th_config_005fgetOption_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f9, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptions_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptions
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag _jspx_th_config_005fgetOptions_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag) _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag.class);
    boolean _jspx_th_config_005fgetOptions_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOptions_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptions_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(33,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f2.setVar("yAxisLabels");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(33,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f2.setName("yAxisLabels");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(33,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f2.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOptions_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptions_005f2 = _jspx_th_config_005fgetOptions_005f2.doStartTag();
        if (_jspx_th_config_005fgetOptions_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptions_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptions_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptions_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOptions_005f2);
      _jspx_th_config_005fgetOptions_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptions_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptions_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f10(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f10 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f10_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f10.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f10.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(34,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setVar("categoriesOrder");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(34,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setName("categoriesOrder");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(34,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f10 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f10 = _jspx_th_config_005fgetOption_005f10.doStartTag();
        if (_jspx_th_config_005fgetOption_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f10[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f10.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f10.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f10);
      _jspx_th_config_005fgetOption_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f10, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f11(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f11 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f11_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f11.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f11.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(36,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setVar("customSeries");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(36,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setName("customSeries");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(36,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f11 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f11 = _jspx_th_config_005fgetOption_005f11.doStartTag();
        if (_jspx_th_config_005fgetOption_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f11[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f11.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f11.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f11);
      _jspx_th_config_005fgetOption_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f11, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f12(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f12 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f12_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f12.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f12.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(37,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setVar("customSeriesClass");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(37,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setName("customSeriesClass");
      int[] _jspx_push_body_count_config_005fgetOption_005f12 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f12 = _jspx_th_config_005fgetOption_005f12.doStartTag();
        if (_jspx_th_config_005fgetOption_005f12.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f12[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f12.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f12.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f12);
      _jspx_th_config_005fgetOption_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f12, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f13(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f13 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f13_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f13.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f13.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(39,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setVar("displayDoc");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(39,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setName("displayDoc");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(39,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f13 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f13 = _jspx_th_config_005fgetOption_005f13.doStartTag();
        if (_jspx_th_config_005fgetOption_005f13.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f13[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f13.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f13.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f13);
      _jspx_th_config_005fgetOption_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f13, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f14(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f14 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f14_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f14.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f14.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(40,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setVar("exportChart");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(40,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setName("exportChart");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(40,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f14 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f14 = _jspx_th_config_005fgetOption_005f14.doStartTag();
        if (_jspx_th_config_005fgetOption_005f14.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f14[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f14.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f14.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f14);
      _jspx_th_config_005fgetOption_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f14, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f15(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f15 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f15_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f15.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f15.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(42,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f15.setVar("finalSumWaterfall");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(42,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f15.setName("finalSumWaterfall");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(42,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f15.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f15 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f15 = _jspx_th_config_005fgetOption_005f15.doStartTag();
        if (_jspx_th_config_005fgetOption_005f15.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f15[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f15.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f15.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f15);
      _jspx_th_config_005fgetOption_005f15_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f15, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f15_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f16(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f16 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f16_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f16.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f16.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(43,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f16.setVar("finalSumDisplayName");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(43,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f16.setName("finalSumDisplayName");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(43,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f16.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f16 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f16 = _jspx_th_config_005fgetOption_005f16.doStartTag();
        if (_jspx_th_config_005fgetOption_005f16.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f16[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f16.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f16.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f16);
      _jspx_th_config_005fgetOption_005f16_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f16, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f16_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f17(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f17 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f17_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f17.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f17.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(44,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f17.setVar("finalSumDisplayColor");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(44,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f17.setName("finalSumDisplayColor");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(44,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f17.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f17 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f17 = _jspx_th_config_005fgetOption_005f17.doStartTag();
        if (_jspx_th_config_005fgetOption_005f17.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f17[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f17.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f17.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f17);
      _jspx_th_config_005fgetOption_005f17_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f17, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f17_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f18(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f18 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f18_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f18.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f18.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(45,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f18.setVar("intermediateSumWaterfall");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(45,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f18.setName("intermediateSumWaterfall");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(45,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f18.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f18 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f18 = _jspx_th_config_005fgetOption_005f18.doStartTag();
        if (_jspx_th_config_005fgetOption_005f18.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f18[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f18.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f18.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f18);
      _jspx_th_config_005fgetOption_005f18_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f18, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f18_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionComposite_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionCompositeTag _jspx_th_config_005fgetOptionComposite_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionComposite_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOptionComposite_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionComposite_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(46,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f0.setVar("intermediateSum");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(46,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f0.setName("intermediateSum");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(46,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f0.setDefaultValue("");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(46,0) name = mapIndex type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f0.setMapIndex(true);
      int[] _jspx_push_body_count_config_005fgetOptionComposite_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionComposite_005f0 = _jspx_th_config_005fgetOptionComposite_005f0.doStartTag();
        if (_jspx_th_config_005fgetOptionComposite_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionComposite_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionComposite_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionComposite_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOptionComposite_005f0);
      _jspx_th_config_005fgetOptionComposite_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionComposite_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionComposite_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f19(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f19 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f19_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f19.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f19.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(47,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f19.setVar("activateReloadChart");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(47,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f19.setName("activateReloadChart");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(47,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f19.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f19 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f19 = _jspx_th_config_005fgetOption_005f19.doStartTag();
        if (_jspx_th_config_005fgetOption_005f19.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f19[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f19.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f19.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f19);
      _jspx_th_config_005fgetOption_005f19_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f19, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f19_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(48,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f2.setVar("additionalParams");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(48,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f2.setName("additionalParams");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(48,0) name = mapIndex type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f2.setMapIndex(true);
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f2 = _jspx_th_config_005fgetOptionsComposite_005f2.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f2);
      _jspx_th_config_005fgetOptionsComposite_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f0_reused = false;
    try {
      _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(51,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setVar("isWaterFall");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(51,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(51,0) 'false'",_jsp_getExpressionFactory().createValueExpression("false",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
      if (_jspx_th_c_005fset_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
      _jspx_th_c_005fset_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f0_reused = false;
    try {
      _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(53,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setVar("config");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(53,0) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(53,0) '${serieConfiguration}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${serieConfiguration}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
        if (_jspx_eval_c_005fforEach_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write('\r');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_c_005fif_005f0(_jspx_th_c_005fforEach_005f0, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
      _jspx_th_c_005fforEach_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(54,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${config.representation=='waterfall'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_c_005fset_005f1(_jspx_th_c_005fif_005f0, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("		waterFallOptions.finalSumWaterfall = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${finalSumWaterfall}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("		waterFallOptions.finalSumDisplayName = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${finalSumDisplayName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("		waterFallOptions.finalSumDisplayColor = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${finalSumDisplayColor}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("		waterFallOptions.intermediateSumWaterfall = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${intermediateSumWaterfall}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("		waterFallOptions.intermediateSum = {};\r\n");
          out.write("		waterFallOptions.intermediateSum.type = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${intermediateSum.intermediateSumType}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("		waterFallOptions.intermediateSum.number = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${intermediateSum.intermediateSumNb}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("		waterFallOptions.intermediateSum.total = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${intermediateSum.isIntermediate}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("		waterFallOptions.intermediateSum.name = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${intermediateSum.intermediateSumName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("		waterFallOptions.intermediateSum.color = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${intermediateSum.intermediateSumColor}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("	");
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f1_reused = false;
    try {
      _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(55,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setVar("isWaterFall");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(55,2) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(55,2) 'true'",_jsp_getExpressionFactory().createValueExpression("true",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
      if (_jspx_th_c_005fset_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
      _jspx_th_c_005fset_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f2_reused = false;
    try {
      _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(69,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setVar("nbFacet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(69,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(69,0) '${fn:length(fn:split(facetsList,','))}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_0),"${fn:length(fn:split(facetsList,','))}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
      if (_jspx_th_c_005fset_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
      _jspx_th_c_005fset_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fnew_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:new
    com.exalead.cv360.searchui.view.jspapi.list.NewTag _jspx_th_list_005fnew_005f0 = (com.exalead.cv360.searchui.view.jspapi.list.NewTag) _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.NewTag.class);
    boolean _jspx_th_list_005fnew_005f0_reused = false;
    try {
      _jspx_th_list_005fnew_005f0.setPageContext(_jspx_page_context);
      _jspx_th_list_005fnew_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(71,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fnew_005f0.setVar("yAxisLabelsAsList");
      int[] _jspx_push_body_count_list_005fnew_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fnew_005f0 = _jspx_th_list_005fnew_005f0.doStartTag();
        if (_jspx_th_list_005fnew_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fnew_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fnew_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fnew_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_list_005fnew_005f0);
      _jspx_th_list_005fnew_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fnew_005f0, _jsp_getInstanceManager(), _jspx_th_list_005fnew_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f1 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f1_reused = false;
    try {
      _jspx_th_c_005fforEach_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(72,0) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f1.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(72,0) '${yAxisLabels}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${yAxisLabels}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(72,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f1.setVar("yAxisLabel");
      int[] _jspx_push_body_count_c_005fforEach_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f1 = _jspx_th_c_005fforEach_005f1.doStartTag();
        if (_jspx_eval_c_005fforEach_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write('\r');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_list_005fadd_005f0(_jspx_th_c_005fforEach_005f1, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f1);
      _jspx_th_c_005fforEach_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fadd_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:add
    com.exalead.cv360.searchui.view.jspapi.list.AddTag _jspx_th_list_005fadd_005f0 = (com.exalead.cv360.searchui.view.jspapi.list.AddTag) _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.AddTag.class);
    boolean _jspx_th_list_005fadd_005f0_reused = false;
    try {
      _jspx_th_list_005fadd_005f0.setPageContext(_jspx_page_context);
      _jspx_th_list_005fadd_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(73,1) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fn:trim(yAxisLabel)}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_1));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(73,1) name = list type = java.util.Collection reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f0.setList((java.util.Collection) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${yAxisLabelsAsList}", java.util.Collection.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_list_005fadd_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fadd_005f0 = _jspx_th_list_005fadd_005f0.doStartTag();
        if (_jspx_th_list_005fadd_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fadd_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fadd_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fadd_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.reuse(_jspx_th_list_005fadd_005f0);
      _jspx_th_list_005fadd_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fadd_005f0, _jsp_getInstanceManager(), _jspx_th_list_005fadd_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fnew_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:new
    com.exalead.cv360.searchui.view.jspapi.list.NewTag _jspx_th_list_005fnew_005f1 = (com.exalead.cv360.searchui.view.jspapi.list.NewTag) _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.NewTag.class);
    boolean _jspx_th_list_005fnew_005f1_reused = false;
    try {
      _jspx_th_list_005fnew_005f1.setPageContext(_jspx_page_context);
      _jspx_th_list_005fnew_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(75,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fnew_005f1.setVar("categoriesOrderAsList");
      int[] _jspx_push_body_count_list_005fnew_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fnew_005f1 = _jspx_th_list_005fnew_005f1.doStartTag();
        if (_jspx_th_list_005fnew_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fnew_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fnew_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fnew_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_list_005fnew_005f1);
      _jspx_th_list_005fnew_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fnew_005f1, _jsp_getInstanceManager(), _jspx_th_list_005fnew_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f2 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f2_reused = false;
    try {
      _jspx_th_c_005fforEach_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(76,0) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f2.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(76,0) '${categoriesOrder}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${categoriesOrder}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(76,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f2.setVar("category");
      int[] _jspx_push_body_count_c_005fforEach_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f2 = _jspx_th_c_005fforEach_005f2.doStartTag();
        if (_jspx_eval_c_005fforEach_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write('\r');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_list_005fadd_005f1(_jspx_th_c_005fforEach_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f2))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f2.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f2);
      _jspx_th_c_005fforEach_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fadd_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:add
    com.exalead.cv360.searchui.view.jspapi.list.AddTag _jspx_th_list_005fadd_005f1 = (com.exalead.cv360.searchui.view.jspapi.list.AddTag) _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.AddTag.class);
    boolean _jspx_th_list_005fadd_005f1_reused = false;
    try {
      _jspx_th_list_005fadd_005f1.setPageContext(_jspx_page_context);
      _jspx_th_list_005fadd_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(77,1) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f1.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fn:trim(category)}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_1));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(77,1) name = list type = java.util.Collection reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f1.setList((java.util.Collection) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoriesOrderAsList}", java.util.Collection.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_list_005fadd_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fadd_005f1 = _jspx_th_list_005fadd_005f1.doStartTag();
        if (_jspx_th_list_005fadd_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fadd_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fadd_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fadd_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.reuse(_jspx_th_list_005fadd_005f1);
      _jspx_th_list_005fadd_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fadd_005f1, _jsp_getInstanceManager(), _jspx_th_list_005fadd_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fhighchartsJSON_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:highchartsJSON
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsJsonTag _jspx_th_plma_005fhighchartsJSON_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsJsonTag) _005fjspx_005ftagPool_005fplma_005fhighchartsJSON_0026_005fvar_005fmaxCategories_005fforceRefineOnMulti_005fforceRefineOnFeeds_005fforceRefineOnFacets_005ffeeds_005fcategoriesOrder_005fbaseUrl_005faxisLabelsY_005faxisLabelX_005fadditionalCategories.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsJsonTag.class);
    boolean _jspx_th_plma_005fhighchartsJSON_005f0_reused = false;
    try {
      _jspx_th_plma_005fhighchartsJSON_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fhighchartsJSON_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(80,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setVar("highchartsJSON");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(80,0) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(80,0) name = categoriesOrder type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setCategoriesOrder((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoriesOrderAsList}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(80,0) name = axisLabelX type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setAxisLabelX((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${xAxisLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(80,0) name = axisLabelsY type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setAxisLabelsY((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${yAxisLabelsAsList}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(80,0) name = maxCategories type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setMaxCategories(((java.lang.Integer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${maxCategories}", int.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).intValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(80,0) name = additionalCategories type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setAdditionalCategories((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${additionalCategories}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(80,0) name = baseUrl type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setBaseUrl((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${target}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(80,0) name = forceRefineOnFeeds type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setForceRefineOnFeeds((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${forceRefineOnFeeds}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(80,0) name = forceRefineOnFacets type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setForceRefineOnFacets((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${forceRefineOnFacets}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(80,0) name = forceRefineOnMulti type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setForceRefineOnMulti(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${forceRefineOnMulti == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int[] _jspx_push_body_count_plma_005fhighchartsJSON_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fhighchartsJSON_005f0 = _jspx_th_plma_005fhighchartsJSON_005f0.doStartTag();
        if (_jspx_eval_plma_005fhighchartsJSON_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_plma_005fhighchartsJSON_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_plma_005fhighchartsJSON_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_plma_005fhighchartsJSON_005f0);
          }
          do {
            out.write("\r\n");
            out.write("\r\n");
            out.write("	");
            if (_jspx_meth_c_005fforEach_005f3(_jspx_th_plma_005fhighchartsJSON_005f0, _jspx_page_context, _jspx_push_body_count_plma_005fhighchartsJSON_005f0))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_plma_005fhighchartsJSON_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_plma_005fhighchartsJSON_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_plma_005fhighchartsJSON_005f0[0]--;
          }
        }
        if (_jspx_th_plma_005fhighchartsJSON_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fhighchartsJSON_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fhighchartsJSON_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fhighchartsJSON_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fhighchartsJSON_0026_005fvar_005fmaxCategories_005fforceRefineOnMulti_005fforceRefineOnFeeds_005fforceRefineOnFacets_005ffeeds_005fcategoriesOrder_005fbaseUrl_005faxisLabelsY_005faxisLabelX_005fadditionalCategories.reuse(_jspx_th_plma_005fhighchartsJSON_005f0);
      _jspx_th_plma_005fhighchartsJSON_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fhighchartsJSON_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fhighchartsJSON_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_plma_005fhighchartsJSON_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_plma_005fhighchartsJSON_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f3 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f3_reused = false;
    try {
      _jspx_th_c_005fforEach_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_plma_005fhighchartsJSON_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(92,1) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f3.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(92,1) '${serieConfiguration}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${serieConfiguration}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(92,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f3.setVar("serieConfig");
      int[] _jspx_push_body_count_c_005fforEach_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f3 = _jspx_th_c_005fforEach_005f3.doStartTag();
        if (_jspx_eval_c_005fforEach_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_map_005fput_005f0(_jspx_th_c_005fforEach_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f3))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_plma_005fhighchartsSerie_005f0(_jspx_th_c_005fforEach_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f3))
              return true;
            out.write('\r');
            out.write('\n');
            out.write('	');
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f3.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f3);
      _jspx_th_c_005fforEach_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f3)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f0 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f0_reused = false;
    try {
      _jspx_th_map_005fput_005f0.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(93,2) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f0.setKey("maxCategories");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(93,2) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${maxCategories}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(93,2) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f0.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f0 = _jspx_th_map_005fput_005f0.doStartTag();
        if (_jspx_th_map_005fput_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f0);
      _jspx_th_map_005fput_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f0, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fhighchartsSerie_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f3)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:highchartsSerie
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsSerieTag _jspx_th_plma_005fhighchartsSerie_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsSerieTag) _005fjspx_005ftagPool_005fplma_005fhighchartsSerie_0026_005fserieConfig_005ffeeds_005fcustomSeriesClass_005fcustomSeries_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsSerieTag.class);
    boolean _jspx_th_plma_005fhighchartsSerie_005f0_reused = false;
    try {
      _jspx_th_plma_005fhighchartsSerie_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fhighchartsSerie_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(94,2) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsSerie_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(94,2) name = serieConfig type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsSerie_005f0.setSerieConfig((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(94,2) name = customSeries type = java.lang.Boolean reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsSerie_005f0.setCustomSeries(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customSeries}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(94,2) name = customSeriesClass type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsSerie_005f0.setCustomSeriesClass((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customSeriesClass}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fhighchartsSerie_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fhighchartsSerie_005f0 = _jspx_th_plma_005fhighchartsSerie_005f0.doStartTag();
        if (_jspx_th_plma_005fhighchartsSerie_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fhighchartsSerie_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fhighchartsSerie_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fhighchartsSerie_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fhighchartsSerie_0026_005fserieConfig_005ffeeds_005fcustomSeriesClass_005fcustomSeries_005fnobody.reuse(_jspx_th_plma_005fhighchartsSerie_005f0);
      _jspx_th_plma_005fhighchartsSerie_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fhighchartsSerie_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fhighchartsSerie_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f4 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f4_reused = false;
    try {
      _jspx_th_c_005fforEach_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(101,0) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f4.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(101,0) '${additionalParams}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${additionalParams}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(101,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f4.setVar("additionalParam");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(101,0) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f4.setVarStatus("loop");
      int[] _jspx_push_body_count_c_005fforEach_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f4 = _jspx_th_c_005fforEach_005f4.doStartTag();
        if (_jspx_eval_c_005fforEach_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("	reloadParams.push('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${additionalParam.paramName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("' + '__' + '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${additionalParam.paramValue}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("');\r\n");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f4.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f4);
      _jspx_th_c_005fforEach_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f5(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f5 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f5_reused = false;
    try {
      _jspx_th_c_005fforEach_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f5.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(107,0) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f5.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(107,0) '${feeds}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${feeds}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(107,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f5.setVar("feed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(107,0) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f5.setVarStatus("loop");
      int[] _jspx_push_body_count_c_005fforEach_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f5 = _jspx_th_c_005fforEach_005f5.doStartTag();
        if (_jspx_eval_c_005fforEach_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("    var key = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed.key}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("    selectedFeeds.push(key);\r\n");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f5.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f5);
      _jspx_th_c_005fforEach_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachFeed_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachFeed
    com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag _jspx_th_search_005fforEachFeed_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag) _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag.class);
    boolean _jspx_th_search_005fforEachFeed_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachFeed_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachFeed_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(113,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f0.setVar("feed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(113,0) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fforEachFeed_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachFeed_005f0 = _jspx_th_search_005fforEachFeed_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachFeed_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_search_005fgetFeedUri_005f0(_jspx_th_search_005fforEachFeed_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFeed_005f0))
              return true;
            out.write("\r\n");
            out.write("    var feedId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("    var definition = {};\r\n");
            out.write("    feedsDefinition[feedId] = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uri}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("    feedsDefinition.push(definition);\r\n");
            int evalDoAfterBody = _jspx_th_search_005fforEachFeed_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachFeed_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachFeed_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachFeed_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachFeed_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.reuse(_jspx_th_search_005fforEachFeed_005f0);
      _jspx_th_search_005fforEachFeed_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachFeed_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachFeed_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFeedUri_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFeed_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFeed_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFeedUri
    com.exalead.cv360.searchui.view.jspapi.search.GetFeedUriTag _jspx_th_search_005fgetFeedUri_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFeedUriTag) _005fjspx_005ftagPool_005fsearch_005fgetFeedUri_0026_005fvar_005ffeed_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFeedUriTag.class);
    boolean _jspx_th_search_005fgetFeedUri_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFeedUri_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFeedUri_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFeed_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(114,4) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeedUri_005f0.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(114,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeedUri_005f0.setVar("uri");
      int[] _jspx_push_body_count_search_005fgetFeedUri_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFeedUri_005f0 = _jspx_th_search_005fgetFeedUri_005f0.doStartTag();
        if (_jspx_th_search_005fgetFeedUri_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFeedUri_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFeedUri_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFeedUri_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFeedUri_0026_005fvar_005ffeed_005fnobody.reuse(_jspx_th_search_005fgetFeedUri_005f0);
      _jspx_th_search_005fgetFeedUri_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFeedUri_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFeedUri_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFeed_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFeed
    com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag _jspx_th_search_005fgetFeed_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag) _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag.class);
    boolean _jspx_th_search_005fgetFeed_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFeed_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFeed_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(125,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeed_005f0.setVar("feed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(125,0) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeed_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFeed_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFeed_005f0 = _jspx_th_search_005fgetFeed_005f0.doStartTag();
        if (_jspx_th_search_005fgetFeed_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFeed_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFeed_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFeed_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody.reuse(_jspx_th_search_005fgetFeed_005f0);
      _jspx_th_search_005fgetFeed_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFeed_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFeed_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f6(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f6 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f6_reused = false;
    try {
      _jspx_th_c_005fforEach_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f6.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(126,0) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f6.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(126,0) '${serieConfiguration}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${serieConfiguration}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(126,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f6.setVar("serie");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(126,0) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f6.setVarStatus("loop");
      int[] _jspx_push_body_count_c_005fforEach_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f6 = _jspx_th_c_005fforEach_005f6.doStartTag();
        if (_jspx_eval_c_005fforEach_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_search_005fgetFacet_005f0(_jspx_th_c_005fforEach_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_string_005feval_005f0(_jspx_th_c_005fforEach_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_string_005feval_005f1(_jspx_th_c_005fforEach_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_plma_005fhighchartsSerieName_005f0(_jspx_th_c_005fforEach_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
              return true;
            out.write('\r');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_string_005fescape_005f0(_jspx_th_c_005fforEach_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_i18n_005fmessage_005f0(_jspx_th_c_005fforEach_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
              return true;
            out.write('\r');
            out.write('\n');
            out.write('	');
            out.write('\r');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_string_005fescape_005f1(_jspx_th_c_005fforEach_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
              return true;
            out.write('\r');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_search_005fgetFacetType_005f0(_jspx_th_c_005fforEach_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
              return true;
            out.write("\r\n");
            out.write("    highchartsSeriesName.push('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("');\r\n");
            out.write("\r\n");
            out.write("    var dataSerieConfiguration = {\r\n");
            out.write("        label 			: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("        feed  			: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty serie.feedId ? serie.feedId : feed.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("        facet 			: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serie.facetId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("        facetI18n       : '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetI18n}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("		facetType		: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetType}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("        isMultiDimensionFacet : false,\r\n");
            out.write("        aggregation 	: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serie.aggregation}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("        pointLegend     : '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pointLegend}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("'\r\n");
            out.write("    };\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_plma_005fgetFacetDimensions_005f0(_jspx_th_c_005fforEach_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fif_005f1(_jspx_th_c_005fforEach_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("    dataSeriesConfiguration.push(dataSerieConfiguration);\r\n");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f6.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f6);
      _jspx_th_c_005fforEach_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacet_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacet
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag _jspx_th_search_005fgetFacet_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag) _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag.class);
    boolean _jspx_th_search_005fgetFacet_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFacet_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacet_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(127,4) name = facetId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f0.setFacetId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serie.facetId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(127,4) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f0.setVar("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(127,4) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacet_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacet_005f0 = _jspx_th_search_005fgetFacet_005f0.doStartTag();
        if (_jspx_th_search_005fgetFacet_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacet_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacet_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacet_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody.reuse(_jspx_th_search_005fgetFacet_005f0);
      _jspx_th_search_005fgetFacet_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacet_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacet_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f0_reused = false;
    try {
      _jspx_th_string_005feval_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(128,4) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serie.pointLegend}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(128,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setVar("pointLegend");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(128,4) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(128,4) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(128,4) name = isJsEscape type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setIsJsEscape(true);
      int[] _jspx_push_body_count_string_005feval_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f0 = _jspx_th_string_005feval_005f0.doStartTag();
        if (_jspx_th_string_005feval_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005ffacet_005fnobody.reuse(_jspx_th_string_005feval_005f0);
      _jspx_th_string_005feval_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f0, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f1 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f1_reused = false;
    try {
      _jspx_th_string_005feval_005f1.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(129,4) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serie.legend}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(129,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setVar("legend");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(129,4) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(129,4) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(129,4) name = isJsEscape type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setIsJsEscape(true);
      int[] _jspx_push_body_count_string_005feval_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f1 = _jspx_th_string_005feval_005f1.doStartTag();
        if (_jspx_th_string_005feval_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005ffacet_005fnobody.reuse(_jspx_th_string_005feval_005f1);
      _jspx_th_string_005feval_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f1, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fhighchartsSerieName_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:highchartsSerieName
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsSerieNameTag _jspx_th_plma_005fhighchartsSerieName_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsSerieNameTag) _005fjspx_005ftagPool_005fplma_005fhighchartsSerieName_0026_005fvar_005fserieConfig_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsSerieNameTag.class);
    boolean _jspx_th_plma_005fhighchartsSerieName_005f0_reused = false;
    try {
      _jspx_th_plma_005fhighchartsSerieName_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fhighchartsSerieName_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(130,4) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsSerieName_005f0.setVar("serieName");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(130,4) name = serieConfig type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsSerieName_005f0.setSerieConfig((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serie}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(130,4) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsSerieName_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fhighchartsSerieName_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fhighchartsSerieName_005f0 = _jspx_th_plma_005fhighchartsSerieName_005f0.doStartTag();
        if (_jspx_th_plma_005fhighchartsSerieName_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fhighchartsSerieName_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fhighchartsSerieName_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fhighchartsSerieName_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fhighchartsSerieName_0026_005fvar_005fserieConfig_005ffeeds_005fnobody.reuse(_jspx_th_plma_005fhighchartsSerieName_005f0);
      _jspx_th_plma_005fhighchartsSerieName_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fhighchartsSerieName_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fhighchartsSerieName_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f0_reused = false;
    try {
      _jspx_th_string_005fescape_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(131,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setVar("serieName");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(131,1) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(131,1) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setEscapeType("JAVASCRIPT");
      int[] _jspx_push_body_count_string_005fescape_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f0 = _jspx_th_string_005fescape_005f0.doStartTag();
        if (_jspx_th_string_005fescape_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f0);
      _jspx_th_string_005fescape_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f0, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f0 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f0_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f0.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(132,4) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("facet_${facet.path}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(132,4) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setVar("facetI18n");
      int[] _jspx_push_body_count_i18n_005fmessage_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f0 = _jspx_th_i18n_005fmessage_005f0.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f0);
      _jspx_th_i18n_005fmessage_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f0, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f1 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f1_reused = false;
    try {
      _jspx_th_string_005fescape_005f1.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(134,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setVar("facetI18n");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(134,1) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetI18n}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(134,1) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setEscapeType("JAVASCRIPT");
      int[] _jspx_push_body_count_string_005fescape_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f1 = _jspx_th_string_005fescape_005f1.doStartTag();
        if (_jspx_th_string_005fescape_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f1);
      _jspx_th_string_005fescape_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f1, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacetType_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacetType
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetTypeTag _jspx_th_search_005fgetFacetType_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetTypeTag) _005fjspx_005ftagPool_005fsearch_005fgetFacetType_0026_005fvar_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetTypeTag.class);
    boolean _jspx_th_search_005fgetFacetType_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFacetType_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacetType_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(135,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetType_005f0.setVar("facetType");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(135,1) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetType_005f0.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacetType_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacetType_005f0 = _jspx_th_search_005fgetFacetType_005f0.doStartTag();
        if (_jspx_th_search_005fgetFacetType_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacetType_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacetType_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacetType_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacetType_0026_005fvar_005ffacet_005fnobody.reuse(_jspx_th_search_005fgetFacetType_005f0);
      _jspx_th_search_005fgetFacetType_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacetType_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacetType_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetFacetDimensions_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getFacetDimensions
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetDimensionsFromFacetTag _jspx_th_plma_005fgetFacetDimensions_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetDimensionsFromFacetTag) _005fjspx_005ftagPool_005fplma_005fgetFacetDimensions_0026_005fid2_005fid1_005ffeeds_005ffacetId_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetDimensionsFromFacetTag.class);
    boolean _jspx_th_plma_005fgetFacetDimensions_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetFacetDimensions_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetFacetDimensions_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(149,4) name = id1 type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetFacetDimensions_005f0.setId1("facetId1");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(149,4) name = id2 type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetFacetDimensions_005f0.setId2("facetId2");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(149,4) name = facetId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetFacetDimensions_005f0.setFacetId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serie.facetId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(149,4) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetFacetDimensions_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetFacetDimensions_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetFacetDimensions_005f0 = _jspx_th_plma_005fgetFacetDimensions_005f0.doStartTag();
        if (_jspx_th_plma_005fgetFacetDimensions_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetFacetDimensions_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetFacetDimensions_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetFacetDimensions_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetFacetDimensions_0026_005fid2_005fid1_005ffeeds_005ffacetId_005fnobody.reuse(_jspx_th_plma_005fgetFacetDimensions_005f0);
      _jspx_th_plma_005fgetFacetDimensions_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetFacetDimensions_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetFacetDimensions_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(150,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetId1 != null && facetId2 != null}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_string_005feval_005f2(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_search_005fgetFacet_005f1(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_search_005fgetFacetType_005f1(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_search_005fgetFacet_005f2(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_search_005fgetFacetType_005f2(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
            return true;
          out.write("\r\n");
          out.write("		dataSerieConfiguration = $.extend({}, dataSerieConfiguration, {\r\n");
          out.write("			multiDimensionFacetIds : ['");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetId1}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("', '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetId2}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("'],\r\n");
          out.write("			multiDimensionFacetTypes : ['");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetType1}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("', '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetType2}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("'],\r\n");
          out.write("			isMultiDimensionFacet : true, \r\n");
          out.write("			label: '', \r\n");
          out.write("			legend: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${legend}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("'\r\n");
          out.write("		});\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f2 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f2_reused = false;
    try {
      _jspx_th_string_005feval_005f2.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(151,8) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f2.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serie.legend}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(151,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f2.setVar("legend");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(151,8) name = isJsEscape type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f2.setIsJsEscape(true);
      int[] _jspx_push_body_count_string_005feval_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f2 = _jspx_th_string_005feval_005f2.doStartTag();
        if (_jspx_th_string_005feval_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005fnobody.reuse(_jspx_th_string_005feval_005f2);
      _jspx_th_string_005feval_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f2, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacet_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacet
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag _jspx_th_search_005fgetFacet_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag) _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag.class);
    boolean _jspx_th_search_005fgetFacet_005f1_reused = false;
    try {
      _jspx_th_search_005fgetFacet_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacet_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(152,2) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f1.setVar("facet1");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(152,2) name = facetId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f1.setFacetId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetId1}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(152,2) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f1.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacet_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacet_005f1 = _jspx_th_search_005fgetFacet_005f1.doStartTag();
        if (_jspx_th_search_005fgetFacet_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacet_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacet_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacet_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody.reuse(_jspx_th_search_005fgetFacet_005f1);
      _jspx_th_search_005fgetFacet_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacet_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacet_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacetType_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacetType
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetTypeTag _jspx_th_search_005fgetFacetType_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetTypeTag) _005fjspx_005ftagPool_005fsearch_005fgetFacetType_0026_005fvar_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetTypeTag.class);
    boolean _jspx_th_search_005fgetFacetType_005f1_reused = false;
    try {
      _jspx_th_search_005fgetFacetType_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacetType_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(153,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetType_005f1.setVar("facetType1");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(153,2) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetType_005f1.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet1}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacetType_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacetType_005f1 = _jspx_th_search_005fgetFacetType_005f1.doStartTag();
        if (_jspx_th_search_005fgetFacetType_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacetType_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacetType_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacetType_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacetType_0026_005fvar_005ffacet_005fnobody.reuse(_jspx_th_search_005fgetFacetType_005f1);
      _jspx_th_search_005fgetFacetType_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacetType_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacetType_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacet_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacet
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag _jspx_th_search_005fgetFacet_005f2 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag) _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag.class);
    boolean _jspx_th_search_005fgetFacet_005f2_reused = false;
    try {
      _jspx_th_search_005fgetFacet_005f2.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacet_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(154,2) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f2.setVar("facet2");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(154,2) name = facetId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f2.setFacetId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetId2}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(154,2) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f2.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacet_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacet_005f2 = _jspx_th_search_005fgetFacet_005f2.doStartTag();
        if (_jspx_th_search_005fgetFacet_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacet_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacet_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacet_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody.reuse(_jspx_th_search_005fgetFacet_005f2);
      _jspx_th_search_005fgetFacet_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacet_005f2, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacet_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacetType_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacetType
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetTypeTag _jspx_th_search_005fgetFacetType_005f2 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetTypeTag) _005fjspx_005ftagPool_005fsearch_005fgetFacetType_0026_005fvar_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetTypeTag.class);
    boolean _jspx_th_search_005fgetFacetType_005f2_reused = false;
    try {
      _jspx_th_search_005fgetFacetType_005f2.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacetType_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(155,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetType_005f2.setVar("facetType2");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(155,2) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetType_005f2.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet2}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacetType_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacetType_005f2 = _jspx_th_search_005fgetFacetType_005f2.doStartTag();
        if (_jspx_th_search_005fgetFacetType_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacetType_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacetType_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacetType_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacetType_0026_005fvar_005ffacet_005fnobody.reuse(_jspx_th_search_005fgetFacetType_005f2);
      _jspx_th_search_005fgetFacetType_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacetType_005f2, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacetType_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f20(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f20 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f20_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f20.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f20.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(170,16) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f20.setName("dataProcessor");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(170,16) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f20.setDefaultValue("function(data) { return data; }");
      int[] _jspx_push_body_count_config_005fgetOption_005f20 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f20 = _jspx_th_config_005fgetOption_005f20.doStartTag();
        if (_jspx_th_config_005fgetOption_005f20.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f20[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f20.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f20.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f20);
      _jspx_th_config_005fgetOption_005f20_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f20, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f20_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f21(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f21 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f21_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f21.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f21.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(171,14) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f21.setName("opts");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(171,14) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f21.setDefaultValue("{}");
      int[] _jspx_push_body_count_config_005fgetOption_005f21 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f21 = _jspx_th_config_005fgetOption_005f21.doStartTag();
        if (_jspx_th_config_005fgetOption_005f21.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f21[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f21.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f21.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f21);
      _jspx_th_config_005fgetOption_005f21_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f21, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f21_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f0 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f0_reused = false;
    try {
      _jspx_th_c_005furl_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(173,14) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f0.setValue("/resources/highcharts/images/chart_line.png");
      int _jspx_eval_c_005furl_005f0 = _jspx_th_c_005furl_005f0.doStartTag();
      if (_jspx_th_c_005furl_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f0);
      _jspx_th_c_005furl_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f0, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f1 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f1_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f1.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(175,20) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setCode("widget.plmacharts.name");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(175,20) name = javaScriptEscape type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setJavaScriptEscape("true");
      int[] _jspx_push_body_count_i18n_005fmessage_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f1 = _jspx_th_i18n_005fmessage_005f1.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f1);
      _jspx_th_i18n_005fmessage_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f1, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f2 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f2_reused = false;
    try {
      _jspx_th_string_005fescape_005f2.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(176,13) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f2.setEscapeType("jsonValue");
      int[] _jspx_push_body_count_string_005fescape_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f2 = _jspx_th_string_005fescape_005f2.doStartTag();
        if (_jspx_eval_string_005fescape_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_string_005fescape_005f2 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_string_005fescape_005f2[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_string_005fescape_005f2);
          }
          do {
            if (_jspx_meth_config_005fgetOption_005f22(_jspx_th_string_005fescape_005f2, _jspx_page_context, _jspx_push_body_count_string_005fescape_005f2))
              return true;
            int evalDoAfterBody = _jspx_th_string_005fescape_005f2.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_string_005fescape_005f2 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_string_005fescape_005f2[0]--;
          }
        }
        if (_jspx_th_string_005fescape_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType.reuse(_jspx_th_string_005fescape_005f2);
      _jspx_th_string_005fescape_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f2, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f22(jakarta.servlet.jsp.tagext.JspTag _jspx_th_string_005fescape_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_string_005fescape_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f22 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f22_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f22.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f22.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_string_005fescape_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(176,51) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f22.setName("title");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(176,51) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f22.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f22 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f22 = _jspx_th_config_005fgetOption_005f22.doStartTag();
        if (_jspx_th_config_005fgetOption_005f22.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f22[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f22.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f22.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f22);
      _jspx_th_config_005fgetOption_005f22_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f22, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f22_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f23(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f23 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f23_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f23.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f23.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(186,13) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f23.setName("hideEmpty");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(186,13) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f23.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f23 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f23 = _jspx_th_config_005fgetOption_005f23.doStartTag();
        if (_jspx_th_config_005fgetOption_005f23.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f23[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f23.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f23.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f23);
      _jspx_th_config_005fgetOption_005f23_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f23, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f23_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(199,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fullScreen == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("	var elem = {};\r\n");
          out.write("	var isFullScreen = $('.");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("').hasClass('full-size-active');\r\n");
          out.write("	elem.icon = FullScreenWidget.BASE_ICON_CSS_CLASS + ' ' + (isFullScreen\r\n");
          out.write("		? FullScreenWidget.EXIT_FULL_SCREEN_ICON_CSS_CLASS\r\n");
          out.write("		: FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS\r\n");
          out.write("	);\r\n");
          out.write("	elem.label = isFullScreen\r\n");
          out.write("		? FullScreenWidget.getMessage('widget.action.exitFullScreen')\r\n");
          out.write("		: FullScreenWidget.getMessage('widget.action.fullScreen');\r\n");
          out.write("	buttonOptions.buttons.push(elem);\r\n");
          out.write("	");
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f24(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f24 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f24_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f24.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f24.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(220,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f24.setName("stackingButton");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(220,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f24.setVar("stacking");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(220,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f24.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f24 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f24 = _jspx_th_config_005fgetOption_005f24.doStartTag();
        if (_jspx_th_config_005fgetOption_005f24.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f24[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f24.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f24.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f24);
      _jspx_th_config_005fgetOption_005f24_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f24, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f24_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(222,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${stacking}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("	var elem = {};\r\n");
          out.write("	elem.icon = 'fonticon fonticon-chart-bar-stacked';\r\n");
          out.write("	elem.label = '");
          if (_jspx_meth_i18n_005fmessage_005f2(_jspx_th_c_005fif_005f3, _jspx_page_context))
            return true;
          out.write("';\r\n");
          out.write("	elem.action = function(e) {\r\n");
          out.write("		$(e.target).closest('.plmaCharts').data('widget').switchStacking();\r\n");
          out.write("	};\r\n");
          out.write("	buttonOptions.buttons.push(elem);\r\n");
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f2 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f2_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f2.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(225,15) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setCode("widget.plmacharts.menu.stacking");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(225,15) name = javaScriptEscape type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setJavaScriptEscape((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${true}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f2 = _jspx_th_i18n_005fmessage_005f2.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f2);
      _jspx_th_i18n_005fmessage_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f2, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f4_reused = false;
    try {
      _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(231,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${displayDoc}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
      if (_jspx_eval_c_005fif_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("	var docButton = {};\r\n");
          out.write("	docButton.icon = 'fonticon fonticon-info';\r\n");
          out.write("	docButton.label = '");
          if (_jspx_meth_i18n_005fmessage_005f3(_jspx_th_c_005fif_005f4, _jspx_page_context))
            return true;
          out.write("';\r\n");
          out.write("	docButton.action = function(e) {\r\n");
          out.write("		var chart = $(e.target).closest('.plmaCharts');\r\n");
          out.write("		var docContainer = chart.find('.doc-container');\r\n");
          out.write("		docContainer.removeClass('hidden');\r\n");
          out.write("	};\r\n");
          out.write("	buttonOptions.buttons.push(docButton);\r\n");
          out.write("	$('.");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("').find('.doc-container .close-doc').on('click', function(e) {\r\n");
          out.write("		var chart = $(e.target).closest('.plmaCharts');\r\n");
          out.write("		chart.find('.doc-container').addClass('hidden');\r\n");
          out.write("	});\r\n");
          int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      _jspx_th_c_005fif_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f3 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f3_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f3.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(234,20) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f3.setCode("widget.plmacharts.menu.documentation");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(234,20) name = javaScriptEscape type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f3.setJavaScriptEscape((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${true}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f3 = _jspx_th_i18n_005fmessage_005f3.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f3);
      _jspx_th_i18n_005fmessage_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f3, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f5(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f5 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f5_reused = false;
    try {
      _jspx_th_c_005fif_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f5.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(246,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${exportChart}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f5 = _jspx_th_c_005fif_005f5.doStartTag();
      if (_jspx_eval_c_005fif_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_config_005fgetOption_005f25(_jspx_th_c_005fif_005f5, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_config_005fgetOption_005f26(_jspx_th_c_005fif_005f5, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_config_005fgetOption_005f27(_jspx_th_c_005fif_005f5, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_config_005fgetOption_005f28(_jspx_th_c_005fif_005f5, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("	var exportButton = {};\r\n");
          out.write("	exportButton.icon = 'fonticon fonticon-picture';\r\n");
          out.write("	exportButton.label = '");
          if (_jspx_meth_i18n_005fmessage_005f4(_jspx_th_c_005fif_005f5, _jspx_page_context))
            return true;
          out.write("';\r\n");
          out.write("	exportButton.action = function(e) {\r\n");
          out.write("	    var plmaChart = $(e.target).closest('.plmaCharts').data('widget');\r\n");
          out.write("\r\n");
          out.write("	    /* Call showPopup only when the data is available. if no data available then notify error.*/\r\n");
          out.write("        if (!plmaChart.getChart()) {\r\n");
          out.write("            $.notify(plmaChart.getMessage('widget.plmacharts.exportchart.err.nodata'), 'error');\r\n");
          out.write("        }else{\r\n");
          out.write("            /* Turn off the Pointer events to avoid unwanted fade/blur/click event.\r\n");
          out.write("            Events will be put back once the export is done. */\r\n");
          out.write("            let $chartWrapper = plmaChart.widget.find('.chart-wrapper');\r\n");
          out.write("            $chartWrapper.css('pointer-events','none');\r\n");
          out.write("\r\n");
          out.write("            var previewHelper = window.exportTo3DSpace.startDocumentExport({\r\n");
          out.write("                id:\"DocumentExport\",\r\n");
          out.write("				title: '");
          if (_jspx_meth_i18n_005fmessage_005f5(_jspx_th_c_005fif_005f5, _jspx_page_context))
            return true;
          out.write("',\r\n");
          out.write("				fileMode: true,\r\n");
          out.write("				fileExtension: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${imageType}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("				spaceUrl: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${spaceUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("                enableAddNewDocument: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${enableNewDoc}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(",\r\n");
          out.write("                isBookmarkEnable: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${enableBookmarkMode}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\r\n");
          out.write("            });\r\n");
          out.write("            plmaChart.exportChart({\r\n");
          out.write("                imageType: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${imageType}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("                container: previewHelper.container,\r\n");
          out.write("                doneCallback: previewHelper.doneCallback\r\n");
          out.write("            });\r\n");
          out.write("\r\n");
          out.write("            $chartWrapper.css('pointer-events','auto');\r\n");
          out.write("        }\r\n");
          out.write("	};\r\n");
          out.write("	buttonOptions.buttons.push(exportButton);\r\n");
          int evalDoAfterBody = _jspx_th_c_005fif_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f5);
      _jspx_th_c_005fif_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f25(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f25 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f25_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f25.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f25.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(247,4) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f25.setName("spaceUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(247,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f25.setVar("spaceUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(247,4) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f25.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f25 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f25 = _jspx_th_config_005fgetOption_005f25.doStartTag();
        if (_jspx_th_config_005fgetOption_005f25.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f25[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f25.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f25.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f25);
      _jspx_th_config_005fgetOption_005f25_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f25, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f25_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f26(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f26 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f26_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f26.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f26.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(248,4) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f26.setName("imageType");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(248,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f26.setVar("imageType");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(248,4) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f26.setDefaultValue("svg");
      int[] _jspx_push_body_count_config_005fgetOption_005f26 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f26 = _jspx_th_config_005fgetOption_005f26.doStartTag();
        if (_jspx_th_config_005fgetOption_005f26.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f26[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f26.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f26.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f26);
      _jspx_th_config_005fgetOption_005f26_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f26, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f26_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f27(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f27 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f27_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f27.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f27.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(249,4) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f27.setName("enableNewDoc");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(249,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f27.setVar("enableNewDoc");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(249,4) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f27.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f27 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f27 = _jspx_th_config_005fgetOption_005f27.doStartTag();
        if (_jspx_th_config_005fgetOption_005f27.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f27[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f27.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f27.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f27);
      _jspx_th_config_005fgetOption_005f27_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f27, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f27_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f28(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f28 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f28_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f28.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f28.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(250,4) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f28.setName("enableBookmarkMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(250,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f28.setVar("enableBookmarkMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(250,4) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f28.setDefaultValue("true");
      int[] _jspx_push_body_count_config_005fgetOption_005f28 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f28 = _jspx_th_config_005fgetOption_005f28.doStartTag();
        if (_jspx_th_config_005fgetOption_005f28.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f28[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f28.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f28.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f28);
      _jspx_th_config_005fgetOption_005f28_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f28, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f28_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f4 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f4_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f4.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(254,23) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f4.setCode("widget.plmacharts.menu.exportchart");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(254,23) name = javaScriptEscape type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f4.setJavaScriptEscape((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${true}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f4 = _jspx_th_i18n_005fmessage_005f4.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f4);
      _jspx_th_i18n_005fmessage_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f4, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f5 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f5_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f5.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(269,12) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f5.setCode("plma.exportto3dspace.title");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(269,12) name = javaScriptEscape type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f5.setJavaScriptEscape((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${true}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f5 = _jspx_th_i18n_005fmessage_005f5.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fjavaScriptEscape_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f5);
      _jspx_th_i18n_005fmessage_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f5, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f7(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f7 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f7_reused = false;
    try {
      _jspx_th_c_005fforEach_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f7.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(288,0) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f7.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(288,0) '${buttons}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${buttons}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(288,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f7.setVar("button");
      int[] _jspx_push_body_count_c_005fforEach_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f7 = _jspx_th_c_005fforEach_005f7.doStartTag();
        if (_jspx_eval_c_005fforEach_005f7 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("	var elem = {};\r\n");
            out.write("	elem.icon = '");
            if (_jspx_meth_string_005fescape_005f3(_jspx_th_c_005fforEach_005f7, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f7))
              return true;
            out.write("';\r\n");
            out.write("	elem.label = '");
            if (_jspx_meth_string_005fescape_005f4(_jspx_th_c_005fforEach_005f7, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f7))
              return true;
            out.write("';\r\n");
            out.write("	elem.action = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${button.onClick}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(";\r\n");
            out.write("	");
            if (_jspx_meth_c_005fif_005f6(_jspx_th_c_005fforEach_005f7, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f7))
              return true;
            out.write("\r\n");
            out.write("	buttonOptions.buttons.push(elem);\r\n");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f7.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f7);
      _jspx_th_c_005fforEach_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f7)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f3 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f3_reused = false;
    try {
      _jspx_th_string_005fescape_005f3.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f7);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(290,14) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f3.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${button.css}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(290,14) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f3.setEscapeType("JAVASCRIPT");
      int[] _jspx_push_body_count_string_005fescape_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f3 = _jspx_th_string_005fescape_005f3.doStartTag();
        if (_jspx_th_string_005fescape_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f3);
      _jspx_th_string_005fescape_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f3, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f7)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f4 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f4_reused = false;
    try {
      _jspx_th_string_005fescape_005f4.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f7);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(291,15) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f4.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${button.label}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(291,15) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f4.setEscapeType("JAVASCRIPT");
      int[] _jspx_push_body_count_string_005fescape_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f4 = _jspx_th_string_005fescape_005f4.doStartTag();
        if (_jspx_th_string_005fescape_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f4);
      _jspx_th_string_005fescape_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f4, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f7)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f6 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f6_reused = false;
    try {
      _jspx_th_c_005fif_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f7);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaCharts2/javascript.jsp(293,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f6.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty button.onInit}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f6 = _jspx_th_c_005fif_005f6.doStartTag();
      if (_jspx_eval_c_005fif_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("		\r\n");
          out.write("		elem.onInit = ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${button.onInit}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(";\r\n");
          out.write("	");
          int evalDoAfterBody = _jspx_th_c_005fif_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f6);
      _jspx_th_c_005fif_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f6_reused);
    }
    return false;
  }
}
