@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/colorPreferences.less";

.plma-light-box-pref {
	min-width: 700px;
	width: 90%;
	max-width: none;
	height: 90%;
	overflow: hidden;
	.plmalightbox-contentwrapper {
		height: 100%;
		flex-grow: 1;
		.plmaLightboxWidgetWrapper {
			height: 100%;
			flex-grow: 1;
			.plma-preferences {
				height: 100%;
				flex-grow: 1;
				.preference-config-flex-container {
					.flex(1);
					overflow: hidden;
					.preference-block-container {
						overflow: auto;
					}
				}
			}
		}
	}
}
.mashup.mashup-style .plma-preferences {
	padding: 0;
	height: 500px;
	position: relative;
	.spinner-container {
		position: absolute;
		width: 100%;
		height: 100%;
		justify-content: center;
		display: none;
		&.active {
			display: flex;
		}
		.spinner {
			border: 16px solid #f3f3f3;
			border-top: 16px solid @clink-active;
			border-radius: 50%;
			width: 60px;
			height: 60px;
			animation: spin 2s linear infinite;
			z-index: 20;
			align-self: center;
		}
	}
	.overlay-preference {
		display: none;
		&.active {
			position: absolute;
			display: block;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 100;
			background-color: rgba(0, 0, 0, 0.3);
		}
	}
	.widgetContent {
		border: none;
		height: 100%;
	}
	.preference-widget {
		//.display-flex();
		//flex-wrap: wrap;
		height: 100%;
		.preference-config {
			//.flex(2);
			height: 100%;
			.display-flex();
			flex-direction: column;
			.preference-tabs {
				.display-flex();
				flex-basis: 40px;
				flex-grow: 0;
				flex-shrink: 0;
				border-bottom: 1px solid @cblock-border;
				margin: 0;
				padding: 0 5px;
				.preference-tab {
					padding: 11px 14px;
					color: @ctext;
					border-bottom: 3px solid transparent;
					cursor: pointer;
					margin-bottom: -1px;
					text-align: center;
					.tab-icon {
						margin-right: 7px;
						line-height: 1rem;
						font-size: 1.14286rem;
						margin-left: 0;
					}
					.label {
						font-size: 13px;
					}
					&:hover {
						color: @ctext-bold;
						border-bottom-color: @ctext;
						.tab-icon {
							color: @ctext-bold;
						}
					}
					&.active {
						color: @ctext-bold;
						border-bottom-color: @clink;
						.label {
							color: @clink;
						}
						.tab-icon {
							color: @clink;
						}
					}
				}
			}
			.preference-config-flex-container {
				margin-top: 2px;
				flex-grow: 1;
				.preference-container {
					display: none;
					height: 100%;
					&.active {
						.display-flex();
						flex-direction: column;
					}
					.preference-page-tab-container {
						overflow: auto;
						flex: 1 0 0px;
					}
					.preference-title {
						margin: 7px;
						margin-top: 20px;
						.main {
							font-size: 16px;
							.collapse-button {
								cursor: pointer;
							}
						}
						.description {
							margin-top: 10px;
							color: @ctext-weak;
						}
					}
					&.preference-selection-container {
						.preference-block-container {
							padding-left: 7px;
						}
					}
					.preference-block-container {
						.flex(1 0 0px);
						overflow: hidden;
						.display-flex();
						&.hidden {
							display: none;
						}
						.preference-block {
							.display-flex();
							.flex(2 0 0);
							flex-grow: 2;
							flex-basis: 0;
							flex-direction: column;
							.elem-button {
								font-size: 18px;
								align-self: flex-end;
								margin-right: 25px;
								.add-button {
									cursor: pointer;
									margin-right: 10px;
								}
								.delete-button {
									cursor: pointer;
								}
							}
							.preference-elem-block {
								border: 1px solid #e2e4e3;
								margin: 15px;
								.flex(1 0 0px);
								overflow: auto;
								&.pages-block {
									flex: none;
								}
								.elem-block {
									margin: 0;
									border: 1px solid @cblock-border;
									height: 40px;
									background-color: #f1f1f1;
									overflow: hidden;
									.elem-description {
										font-size: 14px;
										position: relative;
										top: 12px;
										left: 5px;
										cursor: pointer;
										.elem-icon {
											font-weight: bold;
											margin-right: 5px;
											font-size: 18px;
										}
										.trash-icon {
											float: right;
											margin-right: 10px;
											font-size: 20px;
										}
									}
									&.elem-block-plus {
										border-style: dashed;
										background-color: #f9f9f9;
										cursor: pointer;
										.elem-description {
											text-align: center;
											.icon-plus {
												font-size: 25px;
											}
										}
									}
									.elem-categories {
										margin-left: 10px;
										margin-top: 5px;
										padding-left: 15px;
										.display-flex();
										flex-wrap: wrap;
										.category-description {
											margin: 4px;
											padding: 5px;
											width: 200px;
											border: 1px solid @cblock-border;
											background-color: @cblock-bg-alt;
											border-radius: 50px;
											background-color: white;
											cursor: pointer;
											.category-icon {
												font-size: 14px;
												&.no-icon {
													width: 10px;
													height: 10px;
													border: 1px solid transparent;
													border-radius: 50px;
													display: inline-block;
												}
											}
											.category-label {
												margin-left: 5px;
											}
											.category-trash-icon {
												float: right;
												font-size: 14px;
											}
											&:hover {
												border-color: @clink-active;
												.category-label {
													color: @clink-active;
												}
											}
											&.selected {
												border-color: @clink-active;
												border-width: 2px;
												.category-label {
													color: @clink-active;
												}
											}
										}
										.facet-range-description {
											margin: 15px;
											width: 200px;
											&.active{
												flex-basis: 100%;
												.facet-range {
													height: inherit;
													.range-start {
														width: 58px;
														height: 58px;
														left: 5px;
													}
													.range-end {
														width: 58px;
														height: 58px;
														right: 5px;
													}
												}
												.facet-range-values {
													.start {
														width: 100px;
													}
													.end {
														width: 100px;
													}
												}
												.facet-range {
													.sub-ranges-container {
														padding-left: 60px;
														padding-right: 60px;
														flex-wrap: wrap;
														.sub-range-container {
															min-width: 80px;
															&.sub-range-plus {
																min-width: unset;
															}
														}
													}
												}
											}
											&.selected {
												.facet-range {
													border-color: @clink-active;
												}
											}
											&.plus-facet-range {
												display: flex;
												align-items: center;
												width: 22px;
												.facet-range {
													flex: 1;
													border: 1px dashed #e2e4e3;
													.plus-icon {
														padding-top: 3px;
														font-size: 14px;
													}
												}
											}
											.facet-range-label {
												text-align: center;
												font-size: 13px;
												font-weight: bold;
												margin-bottom: 3px;
												.trash-icon {
													cursor: pointer;
													&:hover {
														color: @clink-active;
													}
												}
											}
											.facet-range {
												height: 20px;
												border: 1px solid #D5E8F2;
												border-radius: 35px;
												background-color: #D5E8F2;
												position: relative;
												cursor: pointer;
												&:hover {
													border-color: @clink-active;
												}
												.range-start {
													position: absolute;
													width: 18px;
													height: 18px;
													background-color: @clink-active;
													border-radius: 30px;
													top: 1px;
													left: 20px;
												}
												.range-end {
													position: absolute;
													width: 18px;
													height: 18px;
													background-color: @clink-active;
													border-radius: 30px;
													bottom: 1px;
													right: 20px;
												}
												.sub-ranges-container {
													.display-flex();
													padding-left: 38px;
													padding-right: 38px;
													margin: 5px;
													.sub-range-container {
														&.collapsed {
															.flex(1 0 0);
															.sub-range-label {
																display: none;
															}
															.sub-range-range {
																display: none;
															}
															.sub-range-values {
																display: none;
															}
															&:before {
																content: " ";
																position: absolute;
																top: 7px;
																background-color: white;
																width: 6px;
																height: 6px;
																border-radius: 50px;
															}
															&.sub-range-plus {
																display: none;
															}
														}
														&.sub-range-plus {
															display: flex;
															align-items: center;
															flex-grow: 0;
															flex-basis: 25px;
															.sub-range-range {
																.plus-icon {
																	font-size: 14px;
																	padding-left: 3px;
																	padding-top: 5px;
																}
																&:hover {
																	color: @clink-active;
																	border-color: @clink-active;
																}
															}
														}
														.flex(1 0 0);
														margin-right: 7px;
														margin-left: 7px;
														.sub-range-label {
															font-weight: bold;
															text-align: center;
															.trash-icon {
																cursor: pointer;
																&:hover {
																	color: @clink-active;
																}
															}
														}
														.sub-range-range {
															width: 100%;
															background-color: white;
															height: 25px;
															border-radius: 50px;
															position: relative;
															.start {
																position: absolute;
																width: 23px;
																height: 23px;
																background-color: @clink;
																border-radius: 50px;
																top: 1px;
																left: 10px;
															}
															.end{
																position: absolute;
																width: 23px;
																height: 23px;
																background-color: @clink;
																border-radius: 50px;
																top: 1px;
																right: 10px;
															}
														}
														.sub-range-values {
															height: 15px;
															font-size: 14px;
															margin-top: 2px;
															.start {
																width: 40px;
																display: inline-block;
																text-align: center;
															}
															.end {
																width: 40px;
																display: inline-block;
																text-align: center;
																float: right;
															}
														}
													}
												}
											}
											.facet-range-values {
												height: 15px;
												font-size: 16px;
												margin-top: 2px;
												.start {
													width: 60px;
													display: inline-block;
													text-align: center;
												}
												.end {
													width: 60px;
													display: inline-block;
													text-align: center;
													float: right;
												}
											}
										}
										&.date-categories {
											width: 300px;
											position: relative;
											border-bottom: 3px solid @clink-active;
											height: 50px;
											margin-bottom: 30px;
											margin-left: 20px;
											padding-left: 0;
											.date-label {
												display: block;
												font-size: 14px;
											}
											.start-date {
												position: absolute;
												left: -11px;
												bottom: 9px;
											}
											.from-date {
												width: 145px;
												margin-left: 3px;
												border-top: 2px solid @clink-active;
												text-align: center;
												position: absolute;
												bottom: -31px;
												left: 0px;
												&:before {
													content: ' ';
													width: 0;
													height: 0;
													border-top: 5px solid transparent;
													border-bottom: 5px solid transparent;
													border-right: 5px solid #42a2da;
													left: -1px;
													position: absolute;
													bottom: 10px;
												}
												&:after {
													content: ' ';
													width: 0;
													height: 0;
													border-top: 5px solid transparent;
													border-bottom: 5px solid transparent;
													border-left: 5px solid #42a2da;
													right: -1px;
													position: absolute;
													bottom: 10px;
												}
											}
											.to-date {
												width: 145px;
												margin-right: 3px;
												border-top: 2px solid @clink-active;
												text-align: center;
												position: absolute;
												bottom: -31px;
												right: 0px;
												&:before {
													content: ' ';
													width: 0;
													height: 0;
													border-top: 5px solid transparent;
													border-bottom: 5px solid transparent;
													border-right: 5px solid #42a2da;
													left: -1px;
													position: absolute;
													bottom: 10px;
												}
												&:after {
													content: ' ';
													width: 0;
													height: 0;
													border-top: 5px solid transparent;
													border-bottom: 5px solid transparent;
													border-left: 5px solid #42a2da;
													right: -1px;
													position: absolute;
													bottom: 10px;
												}
											}
											.end-date {
												position: absolute;
												bottom: 8px;
												right: -8px;
											}
											.current-date {
												position: absolute;
												bottom: 8px;
												left: 121px;
												&:before {
													content: ' ';
													height: 20px;
													width: 2px;
													position: absolute;
													background-color: #42a2da;
													bottom: -20px;
													left: 28px;
												}
											}
											&:before {
												content: " ";
												position: absolute;
												width: 2px;
												height: 20px;
												background-color: @clink-active;
												bottom: -11px;
												left: 0;
											}
											&:after {
												content: " ";
												position: absolute;
												width: 2px;
												height: 20px;
												background-color: @clink-active;
												bottom: -11px;
												right: 0;
											}
										}
										&.layout-categories {
											.layout-description {
												height: 45px;
												border: 1px solid @cblock-border;
												margin: 10px;
												padding: 10px;
												background-color: white;
												display: flex;
												width: 450px;
												cursor: pointer;
												&:hover {
													background-color: @clink-active;
													color: white;
													.info-layout {
														.description {
															color: white;
														}
													}
													.layout-pages {
														.layout-page {
															background-color: white;
															color: @ctext;
														}
													}
													.layout-icon {
														border-color: white;
														transform: rotate(360deg);
													}
												}
												&.selected {
													background-color: @clink-active;
													color: white;
													.info-layout {
														.description {
															color: white;
														}
													}
													.layout-pages {
														.layout-page {
															background-color: white;
															color: @ctext;
														}
													}
													.layout-icon {
														border-color: white;
														transform: rotate(360deg);
													}
												}
												.info-layout {
													.flex(1 0 0);
													.label {
														font-size: 16px;
														line-height: 16px;
														display: block;
													}
													.description {
														margin-left: 20px;
														color: #b4b6ba;
														display: block;
													}
												}
												.layout-pages {
													.flex(1 0 0);
													.layout-page {
														background-color: @clink-active;
														color: white;
														font-size: 14px;
														padding: 4px;
														border-radius: 3px;
														margin: 2px;
													}
												}
												.layout-icon {
													float: right;
													border: 1px solid @ctext;
													border-radius: 50px;
													width: 30px;
													height: 30px;
													font-size: 14px;
													line-height: 30px;
													transition: 0.5s ease-in-out;
													align-self: center;
													margin-right: 10px;
												}
											}
										}
									}
									&:hover {
										border-color: @clink-active;
										.elem-description {
											color: @clink-active;
										}
									}
									&.selected {
										border-color: @clink-active;
										border-width: 2px;
										height: auto;
										padding: 10px;
										padding-top: 0;
										padding-left: 0;
										.elem-description {
											color: @clink-active;
											.elem-icon {
												position: relative;
												left: 30px;
												z-index: 10;
												top: 2px;
												background-color: #f1f1f1;
											}
											.elem-label {
												position: relative;
												padding-left: 30px;
												padding-right: 30px;
												background-color: #f1f1f1;
											}
											.elem-dropdown-icon {
												position: relative;
												left: -29px;
											}
											.trash-icon {
												background-color: #f1f1f1;
											}
										}
										.elem-categories {
											border: 2px solid #e2e4e3;
											padding-top: 10px;
											&.date-categories {
												border: none;
												padding-top: 25px;
											}
										}
										.category-description {
											&.plus-category {
												width: 25px;
												padding: 0;
												border-style: dashed;
												.category-icon {
													position: relative;
													top: 5px;
													left: 3px;
												}
											}
										}
									}
									&.collapsed {
										.elem-categories {
											display: none;
										}
									}
								}
								&.pages-block {
									.display-flex();
									flex-wrap: wrap;
									.page-section {
										width: 100%;
										.title {
											font-size: 20px;
											margin-top: 10px;
											margin-left: 10px;
										}
										.container {
											display: flex;
											flex-wrap: wrap;
										}
									}
									.page-elem {
										width: 150px;
										height: 200px;
										margin: 10px;
										border: 1px solid @cblock-border;
										cursor: pointer;
										position: relative;
										.page-description {
											height: 100%;
											width: 100%;
											.display-flex();
											flex-direction: column;
											.page-section {
												margin: 5px;
												font-size: 13px;
												text-decoration: underline;
											}
											.page-icon {
												margin: 10px;
												margin-top: 20px;
												font-size: 25px;
											}
											.page-label {
												width: 100%;
												text-align: center;
												font-size: 18px;
												align-self: center;
												margin: 15px;
												line-height: 20px;
											}
											.trash-icon, .unsubscribe-page {
												position: absolute;
												bottom: 5px;
												right: 0;
												font-size: 16px;
												cursor: pointer;
												&:hover {
													color: @clink;
												}
											}
										}
										&:before {
											content: ' ';
											right: -1px;
											top: -1px;
											position: absolute;
											background-color: white;
											z-index: 11;
											border-top: 20px solid white;
											border-left: 20px solid #d1d4d4;
										}
										&:after {
											content: ' ';
											right: 0px;
											top: 0px;
											position: absolute;
											background-color: white;
											z-index: 10;
											width: 20px;
											border-bottom: 1px solid @cblock-border;
											border-left: 1px solid @cblock-border;
											height: 20px;
										}
										&:hover {
											border-color: @clink-active;
											color: @clink-active;
											&:before {
												border-left-color: @clink-active;
											}
											&:after {
												border-color: @clink-active;
											}
										}
										&.selected {
											border-color: @clink-active;
											color: @clink-active;
											&:before {
												border-left-color: @clink-active;
											}
											&:after {
												border-color: @clink-active;
											}
										}
									}
								}
								&.landing-page-block {
									padding-left: 10px;
									overflow-y: hidden;
									margin-top: 0px;
									margin-bottom: 0px;
									flex: 0 0 80px;
 									form {
										select {
											display: inline-block;
											width: 250px;
											height: 32px;
											margin-left: 6px;
											.section {
												font-weight: bold;
												color: black;
											}
										}
										.control-label {
											padding-top: 2px;
										}
									}
								}
							}
						}
						.preference-detail {
							.flex(1 0 0);
							flex-grow: 1;
							flex-basis: 0;
							padding: 30px;
							font-size: 15px;
							border: 2px solid @clink-active;
							margin: 15px;
							overflow: auto;
							.title-container {
								font-weight: bold;
							}
							.label-container {
								margin: 15px;
								.label {
									margin-right: 15px;
								}
								.label-input {
									border: 1px solid #e2e4e3;
									border-radius: 3px;
									padding: 0 0 0 5px;
									line-height: 26px;
									height: 26px;
									text-align: left;
									&.empty {
										border-color: #EA4F37;
									}
								}
							}
							.icon-container {
								margin: 15px;
								.label {
									margin-right: 15px;
								}
								.icon-input {
									cursor: pointer;
								}
							}
							.color-container {
								margin: 15px;
								.label {
									margin-right: 15px;
								}
								.color-input {
									width: 15px;
									height: 15px;
									display: inline-block;
									border-radius: 50px;
									cursor: pointer;
									border: 1px solid #e2e3e4;
								}
							}
							.description-container {
								textarea {
									border-radius: 3px;
									padding: 0 0 0 5px;
								}
							}
							.value-container {
								margin: 15px;
								.label {
									margin-right: 15px;
								}
								input {
									border: 1px solid #e2e4e3;
									border-radius: 3px;
									padding: 0 0 0 5px;
									line-height: 26px;
									height: 26px;
									text-align: left;
								}
							}
							.preference-icon-tooltip {
								border: 1px solid @cblock-border;
								position: relative;
								&.hidden {
									display: none;
								}
								&:after, &:before {
									bottom: 100%;
									left: 77px;
									border: solid transparent;
									content: " ";
									height: 0;
									width: 0;
									position: absolute;
									pointer-events: none;
								}
								&:before {
									border-color: rgba(136, 183, 213, 0);
									border-bottom-color: white;
									border-width: 10px;
									margin-left: -10px;
									z-index: 10;
								}
								&:after {
									border-color: rgba(194, 225, 245, 0);
									border-bottom-color: @cblock-border;
									border-width: 11px;
									margin-left: -11px;
								}
							}
							.preference-color-tooltip {
								border: 1px solid @cblock-border;
								position: relative;
								&.hidden {
									display: none;
								}
								&:after, &:before {
									bottom: 100%;
									left: 77px;
									border: solid transparent;
									content: " ";
									height: 0;
									width: 0;
									position: absolute;
									pointer-events: none;
								}
								&:before {
									border-color: rgba(136, 183, 213, 0);
									border-bottom-color: white;
									border-width: 10px;
									margin-left: -5px;
									z-index: 10;
								}
								&:after {
									border-color: rgba(194, 225, 245, 0);
									border-bottom-color: @cblock-border;
									border-width: 11px;
									margin-left: -6px;
								}
							}
							&.hidden {
								display: none;
							}
							&.preference-facet-range-detail {
								.id-container {
									margin-top: 10px;
									.label {
										margin-right: 15px;
									}
									.id-input {
										border: 1px solid #e2e4e3;
										border-radius: 3px;
										padding: 0 0 0 5px;
										line-height: 26px;
										height: 26px;
										text-align: left;
									}
								}
								.facet-range-detail-container {
									.display-flex();
									flex-wrap: wrap;
									.facet-range-detail-object {
										border: 1px solid @cblock-border;
										margin: 5px;
										.label-container {
											margin: 5px;
											.label-input {
												border: 1px solid #e2e4e3;
												border-radius: 3px;
												padding: 0 0 0 5px;
												line-height: 26px;
												height: 26px;
												text-align: left;
											}
										}
										.start-container {
											margin: 5px;
											.start-input {
												border: 1px solid #e2e4e3;
												border-radius: 3px;
												padding: 0 0 0 5px;
												line-height: 26px;
												height: 26px;
												text-align: left;
											}
										}
										.end-container {
											margin: 5px;
											.end-input {
												border: 1px solid #e2e4e3;
												border-radius: 3px;
												padding: 0 0 0 5px;
												line-height: 26px;
												height: 26px;
												text-align: left;
											}
										}
									}
								}
							}
							&.preference-sub-range-detail {
								.sub-range-detail-container {
									.display-flex();
									flex-wrap: wrap;
									.sub-range-detail-object {
										border: 1px solid @cblock-border;
										margin: 5px;
										.sub-label-container {
											margin: 5px;
											.sub-label-input {
												border: 1px solid #e2e4e3;
												border-radius: 3px;
												padding: 0 0 0 5px;
												line-height: 26px;
												height: 26px;
												text-align: left;
											}
										}
										.start-container {
											margin: 5px;
											.start-input {
												border: 1px solid #e2e4e3;
												border-radius: 3px;
												padding: 0 0 0 5px;
												line-height: 26px;
												height: 26px;
												text-align: left;
											}
										}
										.end-container {
											margin: 5px;
											.end-input {
												border: 1px solid #e2e4e3;
												border-radius: 3px;
												padding: 0 0 0 5px;
												line-height: 26px;
												height: 26px;
												text-align: left;
											}
										}
									}
								}
							}
							&.preference-page-detail {
                                .label {
                                    flex: 1 0 0px;
                                    align-self: flex-start;
                                    line-height: 22px;
                                    font-weight: bold;
                                }
								.share-popup-wrapper {
									.title {
										line-height: 40px;
										font-size: 16px;
										margin-left: 10px;
									}
									.share-save-button {
										position: absolute;
										right: 15px;
										bottom: 15px;
									}
									.share-popup-options {
										margin: 10px;
										.share-line {
											margin-bottom: 15px;
											display: flex;
											align-items: center;
											.users-input-container {
												position: relative;
												.add-button {
													background-color: #368ec4;
													color: white;
													height: 22px;
													width: 22px;
													position: relative;
													top: -3px;
													border-radius: 4px;
													margin-left: 5px;
													font-size: 15px;
													cursor: pointer;
													&:before {
														top: 4px;
														left: 0.5px;
														position: relative;
													}
												}
												.share-suggest-container {
													border: 1px solid #b4b6ba;
													background-color: white;
													z-index: 1000;
													position: absolute;
													top: 22px;
													padding: 4px;
													.share-suggest-user {
														padding: 4px 0;
														cursor: pointer;
														.img-user-share {
															border-radius: 20px;
															position: relative;
															top: 6px;
															margin-right: 5px;
														}
														&:hover {
															color: #368EC4;
														}
													}
												}
											}
											.user-list {
												margin-top: 5px;
												margin-left: 5px;
												.user {
													display: flex;
													padding-right: 3px;
													line-height: 20px;
													cursor: pointer;
													.user-name {
														flex: 1;
														font-size: 13px;
													}
													&:hover {
														color: #368EC4;
													}
													.share-icon {
														margin-left: 7px;
													}
												}
												&.read-user-list {
													margin-bottom: 45px;
												}
											}
											.share-input {
												flex: 1 0 0px;
												color: #3d3d3d;
												line-height: 18px;
												padding-right: 14px;
												padding-left: 14px;
												border: 1px solid #b4b6ba;
												border-radius: 4px;
												padding-top: 1px;
												padding-bottom: 1px;
											}
											.share-select {
												align-self: flex-start;
												flex: 1 0 0px;
												display: flex;
												justify-content: center;
												align-items: center;
												.share-checkbox-input {
													display: none;
													&:checked {
														+.share-checkbox-label:before {
															border: 1px solid #368ec4;
															background: #368ec4;
														}
													}
												}
												.share-checkbox-label {
													position: relative;
													display: block;
													padding-left: 30px;
													cursor: pointer;
													vertical-align: middle;
													line-height: 22px;
													font-size: 15px;
													&:before {
														position: absolute;
														top: 0;
														left: 0;
														display: inline-block;
														width: 20px;
														height: 20px;
														content: '';
														border: 1px solid #c0c0c0;
														border-radius: 4px;
													}
													&:after {
														top: 3px;
														left: 8px;
														box-sizing: border-box;
														width: 6px;
														height: 12px;
														transform: rotate(45deg);
														border: 2px solid #fff;
														border-top: 0;
														border-left: 0;
														position: absolute;
														content: '';
													}
												}
											}
										}
									}
									&.hidden {
										display: none;
									}
								}
							}
						}
					}
					.button-container {
						position: sticky;
						bottom: 0;
						left: 0;
						background-color: #fff;
						z-index: 1000;
						padding: 10px;
						margin: 15px;
						border: 1px solid @cblock-border;
						height: 45px;
						.save-button, .cancel-button, .reset-button, .close-button {
							margin: 7px;
							cursor: pointer;
							color: @ctext-bold;
							background-color: #F1F1F1;
							font-size: 14px;
							border: 1px solid @ctext-weak;
							border-radius: 4px;
							line-height: 1.42857;
							padding: 5px 10px;
							float: right;
						}
						.save-button {
							&.active {
								border-color: #57B847;
								color: #57B847;
							}
						}
						.cancel-button, .close-button {
							&.active {
								border-color: #EA4F37;
								color: #EA4F37;
							}
						}
					}
				}
			}
			//.preference-config-flex-container{
			//	height: 100%;
			//	.display-flex();
			//	flex-direction: column;
			//	.preference-flex-block-top{
			//		.display-flex();
			//		flex-wrap: wrap;
			//		border-bottom: 1px solid @cblock-border;
			//		min-height: 45px;.preference-flex-block-top-ariane{
			//			.display-flex();
			//			.flex(1 0 0);
			//			-ms-flex: 1;
			//		}
			//		.icon-back-home{
			//			font-size: 24px;
			//			position: relative;
			//			top: 15px;
			//			cursor: pointer;
			//			&:hover{
			//				color: @clink-bg-active;
			//			}
			//		}
			//		.preference-flex-block-top-ariane{
			//			.top-config-elem{
			//				position: relative;
			//			    margin: 5px 11px;
			//			    .display-flex();
			//			    align-items: center;
			//			    padding-left: 10px;
			//			    border: 1px solid @cblock-border;
			//			    border-left: 4px solid @clink-bg-active;
			//			    cursor: pointer;
			//			    font-size: 15px;
			//			    &:after{
			//					color: white;
			//					border-left: 16px solid;
			//					border-top: 17px solid transparent;
			//					border-bottom: 16px solid transparent;
			//					display: inline-block;
			//					content: '';
			//					position: absolute;
			//					right: -16px;
			//					top: 0;
			//				}
			//				&:before{
			//					color: @cblock-border;
			//					border-left: 17px solid;
			//					border-top: 18px solid transparent;
			//					border-bottom: 17px solid transparent;
			//					display: inline-block;
			//					content: '';
			//					position: absolute;
			//					right: -18px;
			//					top:-1px;
			//				}
			//				&:hover{
			//					color: white;
			//					background-color: @clink-bg-active;
			//					&:after{
			//						color: @clink-bg-active;
			//					}
			//				}
			//			}
			//		}
			//		.preference-flex-block-top-buttons{
			//			font-size: 22px;
			//			.icon-preference-header{
			//				margin-top: 15px;
			//				float: right;
			//				margin-right: 20px;
			//				cursor: pointer;
			//				&.icon-preference-header-save{
			//					color: #57B847;
			//				}
			//				&.icon-preference-header-init{
			//					color: @ctext;
			//					&:hover{
			//						color: @clink-bg-active;
			//					}
			//				}
			//				&.icon-preference-header-cancel{
			//					color: @ctext;
			//					&:hover{
			//						color: @clink-bg-active;
			//					}
			//				}
			//				&.disabled{
			//					cursor: not-allowed;
			//					color: @ctext;
			//				}
			//			}
			//		}
			//	}
			//	.preference-flex-block-bottom{
			//		.display-flex();
			//		flex-wrap: wrap;
			//		height: 100%;
			//		background-color: @cbody-bg;
			//		@media screen and (max-width: 900px) {
			//			flex-direction: column;
			//		}
			//		.preference-flex-block-side{
			//			.flex(1 0 0);
			//			-ms-flex: 1;
			//			border: 1px solid @cblock-border;
			//			background-color: white;
			//			.side-config-elem{
			//				height: 50px;
			//				.display-flex();
			//				align-items: center;
			//				padding-left: 14px;
			//				border-bottom: 1px solid @cblock-border;
			//				cursor: pointer;
			//				font-size: 15px;
			//				&.selected{
			//					border-left: 4px solid @clink-bg-active;
			//					padding-left: 10px;
			//				}
			//				&:hover{
			//					background-color: @clink-bg-active;
			//					color: white;
			//				}
			//			}
			//		}
			//		.preference-flex-block-main{
			//			.flex(3 0 0);
			//			-ms-flex: 3;
			//			.display-flex();
			//			flex-wrap: wrap;
			//			overflow: hidden;
			//			border: 1px solid @cblock-border;
			//			background-color: white;
			//			.application-config{
			//				width: 100%;
			//				overflow: auto;
			//				overflow-x: hidden;
			//				.display-flex();
			//				flex-direction: column;
			//				.flex(1 0 0);
			//				.config-elem{
			//					font-weight: normal;
			//					.flex(1 0 0);
			//					.transition(@transition-simple);
			//					height: 100%;
			//					width: 100%;
			//					&.hidden{
			//						display: none;
			//					}
			//					.config-elem-title{
			//						display: none;
			//					}
			//					.config-elem-value{
			//						display: none;
			//					}
			//					.folder-icon-block{
			//						display: none;
			//					}
			//					&.parent{
			//						border-bottom: 1px solid @cblock-border;
			//						width: auto;
			//					height: auto;
			//					-ms-flex: none;
			//						&.config-elem-value-parent{
			//							width: auto;
			//						height: auto;
			//						}
			//						.config-elem-title{
			//							font-weight: bold;
			//							display: block;
			//							&.clickable-title{
			//								cursor: pointer;
			//								padding: 20px;
			//								border-bottom: 1px dashed @cblock-border;
			//								z-index: 0;
			//								position: relative;
			//								&:after{
			//									.transition-scale(1.3);
			//								    opacity: 0;
			//								    top: -2px;
			//								    left: -2px;
			//								    padding: 2px;
			//								    z-index: -1;
			//								    background: @clink-bg-active;
			//								    .transition(@transition-simple);
			//								    pointer-events: none;
			//								    position: absolute;
			//								    width: 100%;
			//								    height: 100%;
			//								    content: '';
			//								    -webkit-box-sizing: content-box;
			//								    -moz-box-sizing: content-box;
			//								    box-sizing: content-box;
			//								}
			//								&:hover{
			//									color: white;
			//									&:after{
			//										.transition-scale(1);
			//									    opacity: 1;
			//									}
			//								}
			//							}
			//						}
			//						.config-elem-value{
			//							display: block;
			//							&.hidden{
			//								display: none;
			//							}
			//						}
			//						&.config-elem-value{
			//							display: block;
			//							width: initial;
			//						height: initial;
			//						}
			//					}
			//					&.clickable{
			//						cursor: pointer;
			//						height: 100px;
			//					    width: 200px;
			//					    display: inline-block;
			//					    border: 1px solid @cblock-border;
			//					    margin: 10px;
			//					    align-self: center;
			//					    position: relative;
			//					    text-align: center;
			//					    vertical-align: top;
			//					    display: inline-flex;
			//					    flex-direction: column-reverse;
			//					    .folder-icon-block{
			//					    	display: block;
			//					    	font-size: 23px;
			//					    	margin-top: 10px;
			//					    	margin-bottom: 25px;
			//					    	.flex(1 0 0);
			//					    }
			//					    .line{
			//					    	position: absolute;
			//							display: block;
			//						    background: none;
			//						    .transition(@transition-simple);
			//						    &.line-top{
			//						    	width: 0;
			//							    height: 2px;
			//							    top: 0;
			//							    left: -110%;
			//						    }
			//						    &.line-right{
			//						   	 width: 2px;
			//							    height: 0;
			//							    top: -110%;
			//							    right: 0;
			//						    }
			//						    &.line-bottom{
			//						    	width: 0;
			//							    height: 2px;
			//							    bottom: 0;
			//							    right: -110%;
			//						    }
			//						    &.line-left{
			//						    	width: 2px;
			//							    height: 0;
			//							    bottom: -110%;
			//							    left: 0;
			//						    }
			//					    }
			//					    &:hover{
			//					    	background-color: @clink-bg-active;
			//					    	color: white;
			//
			//					    	.line{
			//						    	background: @clink-bg-active;
			//							    &.line-top{
			//							    	width: 100%;
			//								    left: 0;
			//							    }
			//							    &.line-right{
			//								    height: 100%;
			//								    top: 0;
			//							    }
			//							    &.line-bottom{
			//							    	width: 100%;
			//								    right: 0;
			//							    }
			//							    &.line-left{
			//								    height: 100%;
			//								    bottom: 0;
			//							    }
			//						    }
			//					    }
			//					    .config-elem-title{
			//					    	font-weight: normal;
			//							display: block;
			//					    }
			//					    .config-elem-value{
			//							display: block;
			//							&.hidden{
			//								display: none;
			//							}
			//						}
			//						.list-empty-elem-title{
			//							display: none;
			//						}
			//					}
			//				}
			//				.config-elem-title{
			//					font-size: 16px;
			//					margin-bottom: 15px;
			//					&.hidden{
			//						display: none;
			//					}
			//				}
			//				.config-elem-value{
			//					margin: 15px;
			//					font-size: 13px;
			//					&.hidden{
			//						display: none;
			//					}
			//					.config-elem-value-name{
			//						text-transform: capitalize;
			//					}
			//					.config-elem-value-value{
			//						margin-right: 10px;
			//					}
			//					.config-elem-color{
			//						width: 20px;
			//					    height: 20px;
			//					    border-radius: 10px;
			//					    display: inline-block;
			//					    cursor: pointer;
			//					    margin-left: 20px;
			//					}
			//					.config-elem-icon{
			//						font-size: 20px;
			//						cursor: pointer;
			//					    margin-left: 20px;
			//					    margin-right: 0;
			//					}
			//					.config-elem-input{
			//						white-space: nowrap;
			//					    background: #fff;
			//					    border: 1px solid @cblock-border;
			//					    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
			//					    border-radius: 4px;
			//					    font-size: 14px;
			//					    padding: 5px;
			//					}
			//					.init-elem{
			//						cursor: pointer;
			//						position: relative;
			//						left: -50px;
			//						top: -7px;
			//						font-size: 12px;
			//						&.init-elem-after-input{
			//							left: 0;
			//							top: -15px;
			//						}
			//					}
			//					&.parent{
			//						padding-bottom: 15px;
			//					}
			//				}
			//				.list-empty-elem-title{
			//					display: inline-block;
			//				    margin-left: 35px;
			//				    text-transform: capitalize;
			//				    font-size: 13px;
			//				}
			//				.config-box-action{
			//					.flex(1 0 0);
			//					cursor: pointer;
			//					height: 100px;
			//					width: 100px;
			//					display: inline-block;
			//					border: 1px dashed @cblock-border;
			//					margin: 10px;
			//					text-align: center;
			//					.action-icon{
			//						font-size: 35px;
			//						margin-top: 45px;
			//						.transition(@transition-simple);
			//					}
			//					&:hover{
			//						border-color: @clink-bg-active;
			//						color: @clink-bg-active;
			//						.action-icon{
			//							transform: scale(1.6);
			//						}
			//					}
			//					&.hidden{
			//						display: none;
			//					}
			//				}
			//				.list-empty-elem-title{
			//					&.hidden{
			//						display: none;
			//					}
			//				}
			//			}
			//		}
			//	}
			//}
		}
	}
	.add-box-action-container {
		&.hidden {
			display: none;
		}
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		.display-flex();
		align-items: center;
		justify-content: center;
		.add-box-action-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 10;
			background-color: rgba(0, 0, 0, 0.3);
		}
		.add-box-action {
			max-height: none;
			background-color: white;
			box-shadow: 0 1px 10px 2px rgba(0, 0, 0, 0.33);
			border-radius: 5px;
			padding: 10px;
			z-index: 901;
			position: relative;
			width: 400px;
			font-size: 17px;
			.title {
				display: block;
				margin-bottom: 25px;
				font-size: 19px;
			}
			.value {
				display: block;
				margin-bottom: 15px;
			}
			.button {
				display: block;
				float: right;
				background: #fff;
				cursor: pointer;
				padding: 9px 10px;
				border: 1px solid #D1D4D4;
				box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
				border-radius: 4px;
			}
			input {
				padding: 9px 10px;
				border: 1px solid #D1D4D4;
				box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
				border-radius: 4px;
			}
		}
	}
	.delete-box-action-container {
		&.hidden {
			display: none;
		}
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		.display-flex();
		align-items: center;
		justify-content: center;
		.delete-box-action-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 10;
			background-color: rgba(0, 0, 0, 0.3);
		}
	}
	.confirm-box-action-container {
		&.hidden {
			display: none;
		}
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		.display-flex();
		align-items: center;
		justify-content: center;
		.confirm-box-action-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 10;
			background-color: rgba(0, 0, 0, 0.3);
		}
		.confirm-box-action {
			max-height: none;
			background-color: white;
			box-shadow: 0 1px 10px 2px rgba(0, 0, 0, 0.33);
			border-radius: 5px;
			padding: 10px;
			z-index: 901;
			position: relative;
			width: 400px;
			font-size: 17px;
			.title {
				display: block;
				margin-bottom: 25px;
				font-size: 19px;
				line-height: 22px;
				margin-bottom: 30px;
			}
			.button {
				background: #fff;
				cursor: pointer;
				padding: 9px 10px;
				border: 1px solid #D1D4D4;
				box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
				border-radius: 4px;
				width: 100px;
				display: inline-block;
				margin-left: 50px;
				text-align: center;
			}
		}
	}
	.cancel-box-action-container {
		&.hidden {
			display: none;
		}
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		.display-flex();
		align-items: center;
		justify-content: center;
		.cancel-box-action-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 10;
			background-color: rgba(0, 0, 0, 0.3);
		}
		.cancel-box-action {
			max-height: none;
			background-color: white;
			box-shadow: 0 1px 10px 2px rgba(0, 0, 0, 0.33);
			border-radius: 5px;
			padding: 10px;
			z-index: 901;
			position: relative;
			width: 400px;
			font-size: 17px;
			.title {
				display: block;
				margin-bottom: 25px;
				font-size: 19px;
				line-height: 22px;
				margin-bottom: 30px;
			}
			.button {
				background: #fff;
				cursor: pointer;
				padding: 9px 10px;
				border: 1px solid #D1D4D4;
				box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
				border-radius: 4px;
				width: 100px;
				display: inline-block;
				margin-left: 50px;
				text-align: center;
			}
		}
	}
	.box-to-delete {
		z-index: 910;
		background-color: white;
		&:hover {
			background-color: @clink-bg-active;
		}
	}
	.preference-icon-tooltip {
		width: 325px;
		height: 300px;
		overflow: scroll;
		overflow-x: hidden;
		.preference-icon-tooltip-input {
			display: block;
			margin: 10px;
			width: 250px;
			border: 1px solid @cblock-border;
			border-radius: 4px;
			color: @ctext-bold;
			height: 25px;
			line-height: 30px;
			padding: 0 6px 0 28px;
		}
		.fonticon-elem {
			font-size: 17px;
			margin: 4px;
			cursor: pointer;
			&.hidden {
				display: none;
			}
		}
	}
	.preference-color-tooltip {
		width: 540px;
		.color-picker-container {
			.picker-wrapper {
				width: 153px;
				display: inline-block;
				position: relative;
				margin: 20px 20px 10px 0;
				.picker {
					border-radius: 75px;
					height: 150px;
					width: 153px;
					svg {
						border-radius: 75px;
					}
				}
				.picker-indicator {
					width: 5px;
					height: 5px;
					border: 2px solid lightblue;
					border-radius: 4px;
					opacity: .5;
					background-color: white;
				}
			}
			.slider-wrapper {
				width: 20px;
				display: inline-block;
				float: left;
				position: relative;
				margin: 20px 10px 10px 20px;
				.slider-indicator {
					width: 100%;
					height: 10px;
					left: -4px;
					opacity: 0.6;
					border: 4px solid lightblue;
					border-radius: 4px;
					background-color: white;
					position: absolute;
				}
			}
		}
		span {
			width: 15px;
			height: 15px;
			margin: 10px;
			cursor: pointer;
			display: inline-block;
			border-radius: 10px;
		}
		.preference-color-column-main-grey {
			span:nth-child(1) {
				background-color: @grey-0;
			}
			span:nth-child(2) {
				background-color: @grey-1;
			}
			span:nth-child(3) {
				background-color: @grey-2;
			}
			span:nth-child(4) {
				background-color: @grey-3;
			}
			span:nth-child(5) {
				background-color: @grey-4;
			}
			span:nth-child(6) {
				background-color: @grey-5;
			}
			span:nth-child(7) {
				background-color: @grey-6;
			}
			span:nth-child(8) {
				background-color: @grey-7;
			}
		}
		.preference-color-column-main-blue {
			span:nth-child(1) {
				background-color: @blue-0;
			}
			span:nth-child(2) {
				background-color: @blue-1;
			}
			span:nth-child(3) {
				background-color: @blue-2;
			}
			span:nth-child(4) {
				background-color: @blue-3;
			}
			span:nth-child(5) {
				background-color: @blue-4;
			}
			span:nth-child(6) {
				background-color: @blue-5;
			}
		}
		.line {
			&.line-1 {
				span:nth-child(1) {
					background-color: @green-0;
				}
				span:nth-child(2) {
					background-color: @green-1;
				}
				span:nth-child(3) {
					background-color: @green-2;
				}
			}
			&.line-2 {
				span:nth-child(1) {
					background-color: @red-0;
				}
				span:nth-child(2) {
					background-color: @red-1;
				}
				span:nth-child(3) {
					background-color: @red-2;
				}
			}
			&.line-3 {
				span:nth-child(1) {
					background-color: @orange-0;
				}
				span:nth-child(2) {
					background-color: @orange-1;
				}
				span:nth-child(3) {
					background-color: @orange-2;
				}
			}
			&.line-4 {
				span:nth-child(1) {
					background-color: @cyan-0;
				}
				span:nth-child(2) {
					background-color: @cyan-1;
				}
				span:nth-child(3) {
					background-color: @cyan-2;
				}
			}
		}
		.preference-color-other-column {
			span:nth-child(1) {
				background-color: @yellow-dv-0;
			}
			span:nth-child(2) {
				background-color: @blue-dv-0;
			}
			span:nth-child(3) {
				background-color: @orange-dv-0;
			}
			span:nth-child(4) {
				background-color: @violet-dv-0;
			}
			span:nth-child(5) {
				background-color: @turquoise-dv-0;
			}
			span:nth-child(6) {
				background-color: @pink-dv-0;
			}
			span:nth-child(7) {
				background-color: @blue-dv-1;
			}
			span:nth-child(8) {
				background-color: @red-dv-0;
			}
			span:nth-child(9) {
				background-color: @gray-dv-0;
			}
			span:nth-child(10) {
				background-color: @green-dv-0;
			}
			span:nth-child(11) {
				background-color: @violet-dv-1;
			}
			span:nth-child(12) {
				background-color: @blue-dv-2;
			}
			span:nth-child(13) {
				background-color: @khaki-dv-0;
			}
			span:nth-child(14) {
				background-color: @gray-dv-1;
			}
			span:nth-child(15) {
				background-color: @blue-dv-3;
			}
			span:nth-child(16) {
				background-color: @green-dv-1;
			}
			span:nth-child(17) {
				background-color: @gray-dv-2;
			}
			span:nth-child(18) {
				background-color: @turquoise-dv-1;
			}
			span:nth-child(19) {
				background-color: @brown-dv-0;
			}
			span:nth-child(20) {
				background-color: @black-dv-0;
			}
			span:nth-child(21) {
				background-color: @blue-dv-4;
			}
		}
	}
}