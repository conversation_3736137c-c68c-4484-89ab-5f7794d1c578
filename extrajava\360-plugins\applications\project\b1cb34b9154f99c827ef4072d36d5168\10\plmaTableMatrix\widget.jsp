<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="displayHits" uri="http://www.exalead.com/displayHits-widget-helpers" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>

<render:import varWidget="widget" varFeeds="feeds" />

<config:getOption var="withAjax" name="withAjax" defaultValue="false" />
<config:getOption var="baseUrl" name="facetPageName" defaultValue="" />

<config:getOptionsComposite var="axisXConfigFacetList" separator="##" name="axisX" mapIndex="true" />
<request:getParameterValue var="currentAxeX" name="w\$${widget.wuid}.x" defaultValue="0" />
<config:getOption var="axisXMaxItems" name="axisXMaxItems" defaultValue="0"/>

<config:getOptionsComposite var="axisYConfigFacetList" separator="##" name="axisY" mapIndex="true" />
<request:getParameterValue var="currentAxeY" name="w\$${widget.wuid}.y" defaultValue="0" />
<config:getOption var="axisYMaxItems" name="axisYMaxItems" defaultValue="0"/>

<config:getOptionsComposite var="aggregationList" separator="##" name="aggregationList" mapIndex="true" />
<request:getParameterValue var="currentAggregation" name="w\$${widget.wuid}.a" defaultValue="0" />
<c:set var="currentAggregationId">${aggregationList[currentAggregation].id}</c:set>

<config:getOption var="pagination" name="pagination" defaultValue="false" />
<config:getOption var="maxElemByPage" name="maxElemByPage" />
<request:getParameterValue var="pageNb" name="pageNb" defaultValue="1" />

<config:getOption var="facetName" name="facetName" />

<request:getParameterValue var="isFromAjax" name="isFromAjax" defaultValue="false" />

<config:getOption var="nbDim" name="nbDim" defaultValue="1" />
<config:getOption var="facet1DName" name="facet1DName" defaultValue="" />
<config:getOption var="displayDoc" name="displayDoc" />

<c:choose>
	<c:when test="${nbDim=='2'}">
		<search:getFacet2D
			var="table2D"
			varVerticalCategories="verticalCategories"
			varHorizontalCategories="horizontalCategories"
			varVerticalRefinedCategories="verticalRefinedCategories"
			varHorizontalRefinedCategories="horizontalRefinedCategories"
			facetId="${facetName}"
			feeds="${feeds}" />
			
		<plma:getRefinements2DFacet var="refinements" feeds="${feeds}" facetId="${facetName}" />
		<c:set var="refinementsString" value="" />
		<c:forEach var="refinement" items="${refinements}">
			<c:set var="refinementsString" value="${refinementsString}//${refinement.description}" />
		</c:forEach>
			
		<widget:widget varUcssId="uCssId" extraCss="plmaTable plmaTableMatrix ${isFromAjax ? '' : ''}">
			<widget:header extraCss="headerChartContainer">
				<div class="widgetChartHeader"><config:getOption name="title" defaultValue="" /></div>
			</widget:header>
		
			<c:choose>
			
				<%-- If widget has no Feed --%>
				<c:when test="${search:hasFeeds(feeds) == false}">
					<widget:content>
						<render:definition name="noFeeds">
							<render:parameter name="widget" value="${widget}" />
							<render:parameter name="showSuggestion" value="false" />
						</render:definition>
					</widget:content>.
				</c:when>
		
				<%-- If facet is not present in response --%>
				<c:when test="${fn:length(table2D) == 0}">
					<config:getOption var="noResultsJspPathHit" name="noResultsJspPathHit" defaultValue="/WEB-INF/jsp/commons/noFacets.jsp" />
					<widget:content>
						<render:template template="${noResultsJspPathHit}">
							<render:parameter name="accessFeeds" value="${feeds}" />
							<render:parameter name="showSuggestion" value="true" />
						</render:template>
					</widget:content>
				</c:when>
				
				<%-- Display data table --%>
				<c:otherwise>
					<widget:content extraCss="transpose-normal">
						<render:template template="templates/content.jsp">
							<render:parameter name="feeds" value="${feeds}" />
							<render:parameter name="widget" value="${widget}" />
							<render:parameter name="baseUrl" value="${baseUrl}" />
							<render:parameter name="table2D" value="${table2D}" />
							<render:parameter name="currentAxeX" value="${currentAxeX}" />
							<render:parameter name="axisXMaxItems" value="${axisXMaxItems}" />
							<render:parameter name="axisXConfigFacetList" value="${axisXConfigFacetList}" />
							<render:parameter name="axisXCategories" value="${horizontalCategories}" />
							<render:parameter name="axisXRefinedCategories" value="${horizontalRefinedCategories}" />
							<render:parameter name="currentAxeY" value="${currentAxeY}" />
							<render:parameter name="axisYMaxItems" value="${axisYMaxItems}" />
							<render:parameter name="axisYConfigFacetList" value="${axisYConfigFacetList}" />
							<render:parameter name="axisYCategories" value="${verticalCategories}" />
							<render:parameter name="currentAggregation" value="${currentAggregation}" />
							<render:parameter name="currentAggregationId" value="${currentAggregationId}" />
							<render:parameter name="withAjax" value="${withAjax}" />
						</render:template>
					</widget:content>
                    
                    <widget:content extraCss="transpose-transposed">
                        <render:template template="templates/content.jsp">
                            <render:parameter name="feeds" value="${feeds}" />
                            <render:parameter name="widget" value="${widget}" />
                            <render:parameter name="baseUrl" value="${baseUrl}" />
                            <render:parameter name="table2D" value="${table2D}" />
                            <render:parameter name="currentAxeX" value="${currentAxeX}" />
                            <render:parameter name="axisXMaxItems" value="${axisXMaxItems}" />
                            <render:parameter name="axisXConfigFacetList" value="${axisXConfigFacetList}" />
                            <render:parameter name="axisXCategories" value="${horizontalCategories}" />
                            <render:parameter name="axisXRefinedCategories" value="${horizontalRefinedCategories}" />
                            <render:parameter name="currentAxeY" value="${currentAxeY}" />
                            <render:parameter name="axisYMaxItems" value="${axisYMaxItems}" />
                            <render:parameter name="axisYConfigFacetList" value="${axisYConfigFacetList}" />
                            <render:parameter name="axisYCategories" value="${verticalCategories}" />
                            <render:parameter name="currentAggregation" value="${currentAggregation}" />
                            <render:parameter name="currentAggregationId" value="${currentAggregationId}" />
                            <render:parameter name="withAjax" value="${withAjax}" />
                            <render:parameter name="transpose">${true}</render:parameter>
                        </render:template>
                    </widget:content>
					
					<c:if test="${displayDoc}">
						<widget:content extraCss="doc-container hidden">
							<div class="container"><config:getOption name="doc" /></div>
							<span class="close-doc fonticon fonticon-cancel" title="<i18n:message code='generic.close' />"></span>
						</widget:content>
					</c:if>

					<c:if test="${pagination == 'true'}">
						<widget:getUcssId var="uCssId" />
						<div class="table-pagination"> 
							<c:if test="${pageNb>1}">
								<span class="fonticon fonticon-left-open" onclick="PLMATable.utils.previousPage('${uCssId}','${pageNb}')"></span>
							</c:if> 
							<span>
								${pageNb} 
								<i18n:message code="plma.table.pagination.of" /> 
								<fmt:formatNumber value="${( (fn:length(verticalCategories)-fn:length(verticalCategories) % maxElemByPage) / maxElemByPage)+1}" maxFractionDigits="0" /> 
							</span>
							<c:if test="${pageNb < (fn:length(verticalCategories)/maxElemByPage)}">
								<span class="fonticon fonticon-right-open" onclick="PLMATable.utils.nextPage('${uCssId}','${pageNb}')"></span>
							</c:if>
						</div>
					</c:if>
				</c:otherwise>
			</c:choose>
			<render:renderScript position="READY">
				var options = {};
				options.refinements = '${refinementsString}';
				options.isFromAjax = '${isFromAjax}';
				var table = new PLMATable('${widget.wuid}',options);
				<c:if test="${displayDoc}">
					table.initDoc();
				</c:if>

				var buttons = [
					{
						icon: 'button-transpose fonticon fonticon-refresh',
						label: '<i18n:message code="plma.table.transpose" text="Transpose"/>',
						onClick: function() {
							table.transpose();
						}
					}
					<config:getOptionsComposite var="buttons" name="buttons" mapIndex="true"/>
					<c:forEach items="${buttons}" var="button">
						, {
							icon: '<string:escape value="${button.css}" escapeType="JAVASCRIPT"/>',
							label: '<string:escape value="${button.tooltip}" escapeType="JAVASCRIPT"/>',
							onClick: ${button.onClick}
						}
					</c:forEach>
					<c:if test="${displayDoc}">
						, {
							icon: 'fonticon fonticon-info',
							label: '<i18n:message code="plma.navigation.doc" />',
							onClick: function(e) {
								$(e.target).closest('.plmaTableMatrix').find('.doc-container').toggleClass('hidden');
							}
						}
					</c:if>
				];
				var buttonManager = new WidgetButtonManager('${uCssId}', 'menu', '.widgetHeader');
				buttons.forEach(function(button) {
					buttonManager.addButton(button.icon, button.label, button.onClick);
				});
			</render:renderScript>
		</widget:widget>

		
	</c:when>
	<c:otherwise>
		<search:getFacet var="facet" feeds="${feeds}" facetId="${facet1DName}" />
		
		<widget:widget extraCss="plmaTable plmaTableMatrix plmatable1d">
			<widget:header>
				<config:getOption name="title" defaultValue="" />
			</widget:header>
		
			<c:choose>
				<%-- If widget has no Feed --%>
				<c:when test="${search:hasFeeds(feeds) == false}">
					<widget:content>
						<render:definition name="noFeeds">
							<render:parameter name="widget" value="${widget}" />
							<render:parameter name="showSuggestion" value="false" />
						</render:definition>
					</widget:content>
				</c:when>
		
				<%-- If facet is not present in response --%>
				<c:when test="${facet == null}">
					<widget:content>
						<config:getOption var="noResultsJspPathHit" name="noResultsJspPathHit" defaultValue="/WEB-INF/jsp/commons/noFacets.jsp" />
						<render:template template="${noResultsJspPathHit}">
							<render:parameter name="accessFeeds" value="${feeds}" />
							<render:parameter name="showSuggestion" value="true" />
						</render:template>
					</widget:content>
				</c:when>
		
				<%-- Display data table --%>
				<c:otherwise>
		
					<config:getOption var="displayTotal" name="showSummaries" defaultValue="true" />
					<config:getOption var="sortMode" name="sortMode" defaultValue="default" />
					<config:getOption var="iterMode" name="iterMode" />
					<config:getOptions var="forceRefineOn" name="forceRefineOnFeeds" />
					<config:getOptionsComposite var="columns" name="aggregationList" />
		
					<%-- calculate total values for each category --%>
					<map:new var="total" />
					<c:forEach var="column" items="${columns}">
						<c:if test="${map:containsKey(total, column[0]) == false}">
							<search:getCategoryValue var="value" name="${column[0]}" category="${facet}" iterationMode="${iterMode}" defaultValue="0" />
							<map:put map="${total}" key="${column[0]}" value="${value}" />
						</c:if>
					</c:forEach>
		
					<%-- Render Table HTML --%>
					<widget:content>
						<table class="data-table">
							<tbody>
								<%-- Display titles --%>
								<tr class="top">
									<th class="description">
									</th>
									<c:forEach var="column" items="${columns}">
										<th class="${column[0]}">
											<string:eval string="${column[2]}" />
										</th>
									</c:forEach>
								</tr>
			
								<%-- Display rows --%>
								<search:forEachCategory root="${facet}" var="subCategory" sortMode="${sortMode}" drillDown="true" iterationMode="${iterMode}" varStatus="varStatus">
									<tr class="hover hit ${varStatus.index % 2 == 0 ? 'even' : 'odd'} ${className}">
										<td class="description">
											<render:categoryLink category="${subCategory}" baseUrl="" forceRefineOn="${forceRefineOn}" feeds="${feeds}" />
										</td>
										<c:forEach var="column" items="${columns}">
											<search:getCategoryValue var="categoryValue" category="${subCategory}" name="${column[0]}" defaultValue="0" />
											<td class="${column[0]}">
												<search:getCategoryValueType var="type" category="${subCategory}" name="${column[0]}" />
												<c:choose>
													<c:when test="${type == 'STRING'}">
														${categoryValue}
													</c:when>
													<c:otherwise>
														<string:formatNumber>${categoryValue}</string:formatNumber>
													</c:otherwise>
												</c:choose>
											</td>
										</c:forEach>
									</tr>
								</search:forEachCategory>
			
								<%-- Display totals --%>
								<c:if test="${displayTotal == 'true'}">
									<tr class="bottom">
										<th class="description">
											<config:getOption name="totalDescription" defaultValue="Total" />
										</th>
										<c:forEach var="column" items="${columns}">
											<td class="${column[0]}">
												<string:formatNumber>${total[column[0]]}</string:formatNumber>
											</td>
										</c:forEach>
									</tr>
								</c:if>
							</tbody>
						</table>
					</widget:content>
				</c:otherwise>
			</c:choose>
		</widget:widget>
	</c:otherwise>
</c:choose>

