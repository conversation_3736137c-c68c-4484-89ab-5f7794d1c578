PlmaDataTable.prototype = new SelectionWidget();
PlmaDataTable.prototype.constructor = PlmaDataTable;

function PlmaDataTable(uCssId, options) {
	var defaults = {
		isNullSort :false,
		datatableConfig: {},  /* contains all datatable-related config */
		enableFilters: false,
		columnRefines: {},
		columnSortFields: {},
		paramNames: {},
		defaultTranspose: false
	};

	/* By default, Datatables error are thrown to the user's face with an alert().
	 * Let's avoid that */
	$.fn.dataTable.ext.errMode = 'throw';

	this.options = $.extend({}, defaults, options);
	this.uCssId = uCssId;
	this.widget = $('.' + uCssId);
    /* Just initialize Menu when no results (needed in case of flipcontainers)*/
	if(options.isEmpty){
		this.initMenu();
		return;
	}

	this.widget.data({
		'plmaDataTable': this,
		'widget': this
	});

	this.table = null;
	this.datatable = null;
	this.columnIndexes = {};
	this.columnRefines = this.options.columnRefines;

	this.isStateLoaded = false;
	this.isInit = false;
	this.enableSaveState = true;

	/* Transposed values are persisted to avoid
	 * recomputation when the user toggles between
	 * transposed and normal */
	this.transposeInfo = {
		isTransposed: false
	};

	if (this.options.selectionConfig) {
		this.setSelectionConfig(this.options.selectionConfig);
	}

	/**
	 * Hooks for modifying the datatable behavior.
	 */
	this.preAjaxCallbacks = $.Callbacks();
	this.postAjaxCallbacks = $.Callbacks();

	if (this.options.saveScrollPositions) {
		var scrollManager = new PlmaDataTable.utils.ScrollManager(this.getWidgetContent());
		this.preAjaxCallbacks.add(scrollManager.saveScrollPositions.bind(scrollManager));
		this.postAjaxCallbacks.add(scrollManager.restoreScrollPositions.bind(scrollManager));
	}

	/* Using a queue to manage data table initializations, as collisions occur
	 * otherwise. */
	QueueManager.queue('plmaDataTable', this.init, this, []);
	QueueManager.run('plmaDataTable');

};

(function (PlmaDataTable) {

	/* Constants -------------------------------------------------- */
	var WIDGET_NAME = 'plmaDataTable';
	var REFINE_COLUMN_NAME = '_refine';
	var HIT_ID_COLUMN_NAME = '_hit_id';

	var FIXED_HEADER = 'fixedHeader';
	var FIXED_COLUMNS = 'fixedColumns';
	var FIXED_COLUMNS_HEADER = 'fixedColumnsHeader';
	var FIXED_ROWS = 'fixedRows';
	PlmaDataTable.EXTRA_COLUMN_CLASS = 'extra-column';
	PlmaDataTable.EXTRA_COLUMN_NAME = '_extra_column';
	var SELECTED_COLUMNS = [];

	/* Utils -------------------------------------------------- */

	/** Inspired from dataTables.fixedHeader.
	 * Sets column widths in an table based on column widths from another table.
	 * Useful for fixedHeaders. */
	var matchWidths = function (from, to) {
		matchDims(from, to, 'width');
	};

	var matchHeights = function (from, to) {
		matchDims(from, to, 'height');
	};

	var matchDims = function (from, to, dimension) {
		var get = function (name) {
			return $(name, from)
				.map(function () {
					return dimension === 'height' ? $(this).height() : $(this).width();
				}).toArray();
		};

		var set = function (name, toDims) {
			$(name, to).each(function (i) {
				var cssOpts = {};
				cssOpts[dimension] = toDims[i];
				cssOpts['min' + dimension[0].toUpperCase + dimension.slice(1)] = toDims[i];
				$(this).css(cssOpts);
			});
		};

		var thDims, tdDims, trDims;
		if (dimension === 'height') {
			trDims = get('tr');
			set('tr', trDims);
		} else {
			thDims = get('th');
			tdDims = get('td');

			set('th', thDims);
			set('td', tdDims);
		}

		if (dimension === 'width' && thDims) {
			var nbColumns = $('th', to).length;
			if (nbColumns > 0) {
				to.css({
					width: thDims.slice(0, nbColumns).reduce(function (a, b) {
						return a + b;
					})
				});
			}
		}
	};

	/**
	 * Computes the height of sticky clone elements in a container, optionnally excluding elements.
	 * Useful for placing a new sticky element.
	 * All arguments are jQuery objects.
	 * */
	var getExistingStickyClonesHeight = function (container, excludedItems) {
		if (excludedItems) {
			for (var i = 0; i < excludedItems.length; i++) {
				excludedItems[i].addClass('hidden');
			}
		}
		var existingStickyClonesOffset = 0;
		container.find('.sticky:visible')
			.map(function (i, e) {
				existingStickyClonesOffset += $(e).outerHeight(true)
			});
		if (excludedItems) {
			for (var j = 0; j < excludedItems.length; j++) {
				excludedItems[j].removeClass('hidden');
			}
		}
		return existingStickyClonesOffset;
	};

	/**
	 * Returns an array of b integers whose sum is a. If possible, all the integers are equal.
	 * a and b must be positive integers.
	 */
	var split = function (a, b) {
		var s = [];
		var r = a % b;
		for (var i = 0; i < b; i++) {
			s.push(Math.floor(a / b) + (i < r ? 1 : 0));
		}
		return s;
	};

	/**
	 * Inserts the integer n in a sorted array a.
	 */
	var insertSorted = function (n, a) {
		var insertAt = 0;
		for (var i = 0; i < a.length; i++) {
			if (n > a[i]) {
				insertAt++;
			}
		}
		a.splice(insertAt, 0, n);
	};

	/**
	 * Converts the 'start' and 'length' datatables params
	 * to 'page' and 'per_page' mashup params.
	 * Normally, 'per_page' === 'length' but 'start' may not be a multiple of 'per_page', the latter can be higher.
	 */
	var convertToPageParams = function (start, length) {
		var pageNumber = Math.floor(start / length);
		var offset = pageNumber === 0 ? start : start % (length * pageNumber);
		var perPage = offset + (pageNumber === 0 ? length : (start - offset) / pageNumber);
		return {
			pageNumber: pageNumber,
			perPage: perPage
		};
	};

	var escapeCssSelector = function (selector) {
		return selector.replace(/([:.\[\],=@/])/g, '\\$1');
	};

	var sanitizeData = function (data) {
		data.forEach(function (rowData) {
			if (rowData._row_id) {
				rowData._row_id = escapeCssSelector(rowData._row_id);
			}
		});
	};

	var containColumn = function (columns, columnName) {
		return columns.some(function (column) {
			return column.name === columnName;
		});
	};

	var removeColumn = function (columns, columnName) {
		return columns.filter(function (column) {
			return column.name !== columnName;
		});
	};

	/**
	 * Utility class for managing the scroll position
	 * @param {jQuery} $scrollableElement
	 * @constructor
	 */
	function ScrollManager($scrollableElement) {
		this.$scrollableElement = $scrollableElement;
		this.scroll = {
			top: 0,
			left: 0
		};
	}
	ScrollManager.prototype.saveScrollPositions = function () {
		this.scroll = {
			top: this.$scrollableElement.scrollTop(),
			left: this.$scrollableElement.scrollLeft()
		}
	};
	ScrollManager.prototype.restoreScrollPositions = function () {
		this.$scrollableElement
			.scrollTop(this.scroll.top)
			.scrollLeft(this.scroll.left);
	};

	PlmaDataTable.utils = {
		ScrollManager: ScrollManager
	};

	/* Static variables ----------------------------------------------------*/

	/**
	 * Array containing references of PlmaDataTable instances whose DataTables instance must be destroyed.
	 * @type {Array.<PlmaDataTable>}
	 * @private
	 */
	PlmaDataTable._destroy = [];

	/* Prototype methods -------------------------------------------------- */

	/**
	 * Gathers all the init methods
	 */
	PlmaDataTable.prototype.init = function () {
		this.table = this.widget.find('.widgetContent > table');

		/* Independant from the datatables API */
		this.initMenu();
		this.initFullScreen();
		this.initStateSave();
		this.initStateReset();
		this.initDisplayDoc();
		this.initTranspose();
		this.initClones();
		this.initExplicitSort();
		this.initDrag();

		/* Creating the actual datatables object */
		this.initDataTable();

		this.isInit = true;


	};

	/**
	 * These features use either the datatables API or DOM elements, and
	 * need the state to be loaded.
	 */
	PlmaDataTable.prototype.postInit = function () {
		if (this.isInit && (this.isStateLoaded || !this.options.datatableConfig.stateSave)) {

			if (!this.isInitFilter) {
				this.initFilter();
				this.isInitFilter = true;
			}
			if (!this.isInitSeeMore) {
				this.initSeeMore();
				this.isInitSeeMore = true;
			}
			if (!this.isInitUserActions) {
				this.initUserActions();
				this.isInitUserActions = true;
			}
			if (!this.isInitFixedHeader) {
				this.initFixedHeader();
				this.isInitFixedHeader = true;
			}
			if (!this.isInitFixedColumns) {
				this.initFixedColumns();
				this.isInitFixedColumns = true;
			}
			if (!this.isInitFixedRows) {
				this.initFixedRows();
				this.isInitFixedRows = true;
			}

			if (!this.isInitSettings) {
				this.initSettings();
				this.isInitSettings = true;
			}
			if (!this.isInitExport) {
				this.initExportData();
			}

			if (!this.isCheckIsEmpty) {
				this.checkIsEmpty();
			}

			if (this.enableSaveState && this.datatable.state()) {
				this.setNbFixedColumns(this.datatable.state().nbFixedColumns);
			}

			if (this.options.onInitAction) {
				var helper = {};
				helper.selectRow = function (id) {
					this.datatable.row('#' + escapeCssSelector(id)).select();
				}.bind(this);
				this.options.onInitAction.call(null, helper);
			}

			if (this.options.elements.length > 0) {
				this.options.elements.forEach(function (element, i) {
					// for nth-child selector, counting starts at 1
					var selector = '.' + PlmaDataTable.EXTRA_COLUMN_CLASS + '> :nth-child(' + (i + 1) + ')';
					this.widget.off('click', selector).on('click', selector, function (e) {
						var row = this.datatable.row($(e.currentTarget).parents('tr'));
						element.onClick.call(null, e, row.data(), row.index());
					}.bind(this));
				}, this);
			}

			if (!this.isInitKeyboardEvents) {
				this.initKeyboardEvents();
				this.isInitKeyboardEvents = true;
			}
		}
		this.widget.trigger('plma:datatable_loaded');
	};

	/**
	 * If the table is empty, the displayed text will still be
	 * "Loading". This method fakes a search in the table
	 * to make it display the "no data" message.
	 */
	PlmaDataTable.prototype.checkIsEmpty = function () {
		if (this.datatable.data().length === 0 && !this.datatable.search()) {
			this.isCheckIsEmpty = true;
			this.datatable.search('').draw();
		}
	};

  /**
   * HideEmpty function checks if the HideEmptySeries option is true.
   * Iterates over column's definition (colDefList) and makes columns'
   * visible property false for rows with empty values.
   */
	PlmaDataTable.prototype.hideEmpty = function () {
		const originalState = this.options.datatableConfig.columns.map((element) => element.visible);
		const saveState = this.options.datatableConfig.stateSave;
		this.datatable.columns().every(function(columnIndex) {
		  const selectedColumnVisible = SELECTED_COLUMNS.length > 0 ?
		        SELECTED_COLUMNS[columnIndex] :
		        saveState ?
		            (this.state().columns[columnIndex].visible || originalState[columnIndex]) :
		            originalState[columnIndex];

		  if (selectedColumnVisible) {
		    const isEmpty = this.data().toArray().some((value) => {
                  // If the value is a string, trim it and check if it's non-empty
                  if (typeof value === 'string') {
                    return value.trim() !== "";
                  }

                  // If the value is an object, check if it's an empty object
                  if (typeof value === 'object' && value !== null) {
                    return Object.keys(value).length > 0; // Checks if object has properties
                  }

                  // If value is neither a string nor an object (like numbers, booleans, etc.), treat it as "non-empty"
                  return value !== null && value !== undefined;
            });

		    this.column(columnIndex).visible(isEmpty);
		  }
		});
	};

	PlmaDataTable.prototype.initMenu = function () {
		this.menuContainer = this.widget.find('.plmadatatable-menu-container');
		this.menuButton = this.widget.find('.plmadatatable-menu-btn');
		if(this.menuContainer.children().length == 0) {
		    this.menuButton.addClass('hidden');
		    return;
		 }
		this.menuPopup = new AppsPopup({
		    button: this.menuButton,
			content: this.menuContainer,
			mode: 'click',
			delay: 0,
			direction: 'down',
			closeOnClickOutside: true,
			extraCss: 'menu-popup',
			onInit: function (popup) {
				/* Hack to prevent positionning */
				this.positionSet = true;
			},
			onClose: $.proxy(function () {
				if (this.isMenuDisplayed()) {
					this.hideMenu();
				}
			}, this)
		});
		this.widget.find('.widgetHeader').append(this.menuPopup.popup);
		/* This prevents the popup from closing when clicked inside */
		this.menuPopup.wrapper.on('click', function (e) {
			e.stopPropagation();
			return false;
		});

		this.menuButton.on('click', $.proxy(function () {
			if (this.isMenuDisplayed()) {
				this.hideMenu();
			} else {
				this.showMenu();
			}
		}, this));

	};

	PlmaDataTable.prototype.showMenu = function () {
		this.menuContainer.removeClass('hidden');
		this.menuButton.addClass('active');
		this.menuPopup.open();
	};

	PlmaDataTable.prototype.hideMenu = function () {
		this.menuPopup.close();
		this.menuContainer.addClass('hidden');
		this.menuButton.removeClass('active');
	};

	PlmaDataTable.prototype.isMenuDisplayed = function () {
		return !this.menuContainer.hasClass('hidden');
	};

	PlmaDataTable.prototype.initFullScreen = function () {
		this.fullScreenButton = this.widget.find('.plmadatatable-fullscreen');
		if (this.fullScreenButton.length > 0) {
			this.widget.fullScreenWidget({
				button: this.fullScreenButton,
				onShow: this.onShowFullScreen.bind(this),
				onHide: this.onHideFullScreen.bind(this)
			});
		}
	};

	PlmaDataTable.prototype.onShowFullScreen = function () {
		this.hideMenu();
		this.placeFixedHeader(true);
	};

	PlmaDataTable.prototype.onHideFullScreen = function () {
		this.hideMenu();
		this.placeFixedHeader(true);
	};

	/**
	 * Allow user to select row to fix them
	 */
	PlmaDataTable.prototype.activateFixedRowSelection = function () {
		if (this.options.datatableConfig.serverSide) {
			this.fixedRowIdName = HIT_ID_COLUMN_NAME;
		} else {
			this.fixedRowIdName = this.options.datatableConfig.columns[1].name;
		}
		this.widget.find('.fixed-rows-desactivate').removeClass('hidden');
		this.widget.find('.fixed-rows-activate').addClass('hidden');
		this.widget.find('.section-fixedRows input').each(function (i, e) {
			$(e).prop('disabled', false);
		}.bind(this));
		/* Remove former action on row click */
		this.datatable.off('user-select').off('select').off('deselect').off('mouseover');
		this.widget.on('click', 'tr', function (e) {
			if ($(e.currentTarget).closest('.rowsClone').length > 0) {
				var rowId = $(e.currentTarget).data('id').replace(/\s/g, '');
				var row = this.fixedRows[rowId];
				var checkbox = this.widget.find('.fixed-row input[data-id="' + rowId + '"]');
				checkbox.prop('checked', !this.fixedRows[rowId].displayed);
				this.toggleFixedRow(checkbox, row);
				$(e.currentTarget).remove();
			} else {
				/* On row click, add row as fixed row in the menu, and in the table or remove it */
				var clickedHit = this.datatable.row($(e.currentTarget));
				this.updateFixedRows(clickedHit);
			}
		}.bind(this));
		this.datatable.on('column-reorder', '', function (e, settings, details) {
			/* Re-order fixed rows */
			this.reorderColumn(details);
		}.bind(this));
		$(window).on('resize', function () {
			this.fixRowsCellWidth();
		}.bind(this));
	};

	PlmaDataTable.prototype.reorderColumn = function (details) {
		for (var i = 0; i < this.getClone(FIXED_ROWS).find('tr').length; i++) {
			var row = $(this.getClone(FIXED_ROWS).find('tr')[i]);
			var toFromMove = $(row.find('td')[details.to]);
			var tdToMove = $(row.find('td')[details.from]).detach();
			if (details.from > details.to) {
				toFromMove.before(tdToMove);
			} else if (details.from < details.to) {
				toFromMove.after(tdToMove);
			}
		}
	};

	/**
	 * See if the row needs to be fixed or unfixed
	 * @param row
	 */
	PlmaDataTable.prototype.updateFixedRows = function (row) {
		var rowName = row.data()[this.fixedRowIdName].replace(/\s/g, '');
		/* See if a row has been added or removed */
		if (this.fixedRows[rowName]) {
			var checkbox = this.widget.find('.fixed-row input[data-id="' + rowName + '"]');
			checkbox.prop('checked', !this.fixedRows[rowName].displayed);
			this.toggleFixedRow(checkbox, row.data());
		} else {
			this.addFixedRow(row);
		}
	};

	/**
	 * Display rows than have already been fixed
	 */
	PlmaDataTable.prototype.displayFixedRoxs = function () {
		var clone = this.widget.find('.dataTable.tableClone.rowsClone');
		if (clone.length > 0) {
			var fixedRowsFormContainer = this.widget.find('.fixed-rows');
			for (var key in this.fixedRows) {
				var rowName = this.fixedRows[key][this.fixedRowIdName].replace(/\s/g, '');
				var fixRowId = this.uCssId + '-fixedRow-' + rowName.replace(/\s/g, '');
				var selectRow = $('<p class="fixed-row">' +
					'<input type="checkbox" name="fixedRows" id="' + fixRowId + '" data-id="' + rowName.replace(/\s/g, '') + '" value="1" checked/>' +
					'<label for="' + fixRowId + '">' + rowName + '</label>' +
					'</p>');
				if (!this.fixedRows[key].displayed) {
					selectRow.find('input').attr('checked', false);
				}
				fixedRowsFormContainer.append(selectRow);
			}
			fixedRowsFormContainer.find('input').off().on('change', function (e) {
				var row = this.fixedRows[$(e.currentTarget).data('id')];
				// $(e.currentTarget).remove();
				this.toggleFixedRow($(e.currentTarget), row);
			}.bind(this));
		}
	};

	/**
	 * Fix the row
	 * @param row
	 */
	PlmaDataTable.prototype.addFixedRow = function (row) {
		var fixedRowsFormContainer = this.widget.find('.fixed-rows');
		/* We take the value of the first displayed column */
		var rowName = row.data()[this.fixedRowIdName].replace(/\s/g, '');
		var fixRowId = this.uCssId + '-fixedRow-' + rowName.replace(/\s/g, '');
		fixedRowsFormContainer.append($('<p class="fixed-row">' +
			'<input type="checkbox" name="fixedRows" id="' + fixRowId + '" data-id="' + rowName.replace(/\s/g, '') + '" value="1" checked/>' +
			'<label for="' + fixRowId + '">' + rowName + '</label>' +
			'</p>'));
		this.fixedRows[rowName] = row.data();
		this.fixedRows[rowName].displayed = true;
		/* Fix the row */
		this.fixRow(row);
		/* Add action for checkbox */
		fixedRowsFormContainer.find('input').off().on('change', function (e) {
			var row = this.fixedRows[$(e.currentTarget).data('id')];
			// $(e.currentTarget).remove();
			this.toggleFixedRow($(e.currentTarget), row);
		}.bind(this));
	};

	PlmaDataTable.prototype.toggleFixedRow = function (checkbox, row) {
		if (checkbox.prop('checked')) {
			/* We want to fix a specific row, let's get it */
			if (this.options.datatableConfig.serverSide) {
				for (var key in this.fixedRows) {
					if (!this.fixedRows[key].displayed) {
						var row = undefined;
						this.datatable.rows().data().filter(function (value, index) {
							if (value[HIT_ID_COLUMN_NAME] === this.fixedRows[key][HIT_ID_COLUMN_NAME]) {
								row = this.datatable.row(index);
							}
						}.bind(this));
						if (row) {
							this.fixedRows[key].displayed = true;
							this.fixRow(row);
						}
					}
				}
			} else {
				var firstColumnRealOrder = this.getRightColumnOrder();
				this.datatable.column(firstColumnRealOrder + 1).data().filter(function (value, index) {
					if (value === row[this.fixedRowIdName]) {
						this.fixedRows[row[this.fixedRowIdName].replace(/\s/g, '')].displayed = true;
						this.fixRow(this.datatable.row(index));
					}
					return false;
				}.bind(this));
			}
		} else {
			this.fixedRows[row[this.fixedRowIdName].replace(/\s/g, '')].displayed = false;
			this.unFixRow(row);
		}
	};

	PlmaDataTable.prototype.fixRow = function (row) {
		/* Add fix row */
		var rowHtml = $(row.node()).attr('data-id', row.data()[this.fixedRowIdName]).addClass('selected');
		var clone = this.widget.find('.dataTable.tableClone.rowsClone');
		if (clone.length === 0) {
			this.initFixedRows();
			clone = this.widget.find('.dataTable.tableClone.rowsClone');
		}
		clone.find('tbody').append(rowHtml);
		/* Removed fixed row inside table */
		row.remove();
		if (!this.options.datatableConfig.serverSide) {
			this.datatable.draw();
		}

		this.fixRowsCellWidth();
		this.placeFixedRows();
	};

	PlmaDataTable.prototype.unFixRow = function (row) {
		/* Add row inside the table */
		this.datatable.row.add(row).draw();
		var firstColumnRealOrder = this.getRightColumnOrder();
		/* Remove fixed row */
		this.widget.find('.dataTable.tableClone.rowsClone tbody tr').each(function (i, e) {
			if (this.options.datatableConfig.serverSide) {
				if ($(e).data('id') === row[this.fixedRowIdName]) {
					$(e).remove();
				}
			} else {
				if ($(e).find('td')[firstColumnRealOrder].innerHTML === row[this.fixedRowIdName]) {
					$(e).remove();
				}
			}

		}.bind(this));

		this.placeFixedRows();
	};

	PlmaDataTable.prototype.removeAllFixedRowsAfterDraw = function () {
		/* Remove fixed rows */
		for (var key in this.fixedRows) {
			if (this.fixedRows[key].displayed) {
				var row = undefined;
				this.datatable.rows().data().filter(function (value, index) {
					if (value[HIT_ID_COLUMN_NAME] === this.fixedRows[key][HIT_ID_COLUMN_NAME]) {
						row = this.datatable.row(index);
					}
				}.bind(this));
				if (row) {
					row.remove().draw();
				}
			}
		}
	};

	PlmaDataTable.prototype.getRightColumnOrder = function () {
		var firstColumnRealOrder = 1;
		for (var i = 0; i < this.datatable.colReorder.order().length; i++) {
			var order = this.datatable.colReorder.order()[i];
			if (order === 1) {
				firstColumnRealOrder = i;
				if (firstColumnRealOrder > 0) {
					firstColumnRealOrder--;
				}
				break;
			}
		}

		return firstColumnRealOrder;
	};

	PlmaDataTable.prototype.getNbFixedRows = function () {
		var count = 0;
		for (var fixRow in this.fixedRows) {
			if (this.fixedRows[fixRow].displayed) {
				count++;
			}
		}
		return count;
	};

	PlmaDataTable.prototype.fixRowsCellWidth = function () {
		this.getClone(FIXED_ROWS).find('tbody tr').each(function (i, e) {
			var columns = $(e).find('td');
			for (var i = 0; i < columns.length; i++) {
				var column = $(columns[i]);
				var headColumn = $(this.widget.find('.dataTable:not(.tableClone) thead tr th')[i]);
				/* We remove the padding */
				column.css('width', headColumn.outerWidth() - 6);
			}
		}.bind(this));
	};

	/**
	 * Remove the row selection mode
	 */
	PlmaDataTable.prototype.desactivateFixedRowSelection = function () {
		this.widget.find('.fixed-rows-desactivate').addClass('hidden');
		this.widget.find('.fixed-rows-activate').removeClass('hidden');
		this.widget.find('.section-fixedRows input').each(function (i, e) {
			$(e).prop('disabled', true);
		}.bind(this));
		this.datatable.off('select').off('deselect');
		this.widget.off('click', 'tr');
		/* Reactivate former actions */
		this.initUserActions();
	};

	/**
	 * Initializes callbacks to integrate a StorageService persistence
	 * into the standard datatables saveState mechanism.
	 */
	PlmaDataTable.prototype.initStateSave = function () {
		var managed_state_key = this.getManagedStateKey();
		var uCssId = this.uCssId;
		var errorCallback = $.proxy(this.stateErrorCallback, this);

		this.options.datatableConfig.stateSaveParams = $.proxy(function (settings, data) {
			data.transposed = this.transposeInfo.isTransposed;
			data.nbFixedColumns = this.getNbFixedColumns();
			data.exportAll = this.getExportAll();
			delete data.start;
		}, this);

		this.options.datatableConfig.stateLoadParams = $.proxy(function (settings, data) {
			this.setNbFixedColumns(data.nbFixedColumns);
			this.setExportAll(data.exportAll);
		}, this);

		this.options.datatableConfig.stateSaveCallback = $.proxy(function (settings, data) {
			if (this.enableSaveState) {
				if (this.options.saveStateInStorageService) {
					var storage = new StorageClient('user');
					/* Keep a list of all PlmaDataTable saved states */
					storage.get('plmaDataTable_managedStates[]', function (items) {
						var found = false;
						for (var i = 0; i < items.length; i++) {
							if (items[i].value === uCssId) {
								found = true;
								break;
							}
						}
						if (!found) {
							storage.put('plmaDataTable_managedStates[]', uCssId, function () {
								storage.set(managed_state_key, JSON.stringify(data), function () {
								}, errorCallback);
							}, errorCallback);
						} else {
							storage.set(managed_state_key, JSON.stringify(data), function () {
							}, errorCallback);
						}
					}, errorCallback);
				}

				/* Store the data in the local storage anyway, in case the Storage Service is down later on */
				localStorage.setItem(managed_state_key, JSON.stringify(data));
			}
		}, this);

		this.options.datatableConfig.stateLoadCallback = $.proxy(function (settings, callback) {
			var c = $.proxy(function (data) {
				callback(data);
				/* Resume execution of other plmaDataTable initializations, now that the state is loaded */
				this.resumeQueueExecution(true);
				if (!this.isStateLoaded) {
					this.isStateLoaded = true;
				}
			}, this);

			var localSavedState = JSON.parse(localStorage.getItem(managed_state_key)) || {};


			/* If the user is logged in, the state is saved in the Storage Service */
			if (this.options.saveStateInStorageService) {
				var storage = new StorageClient('user');
				storage.get(this.getManagedStateKey(), function (data) {
					var storageServiceSavedState = data && data.length > 0 ? JSON.parse(data[0].value) : {};
					/* If the local saved state is more recent than the one in the Storage Service, use it instead */
					c(localSavedState.time > storageServiceSavedState.time ? localSavedState : storageServiceSavedState);
				}, function () {
					/* If the Storage Service is not available, the local data is used. */
					errorCallback();
					c(localSavedState);
				});
			} else {
				/* Otherwise, using the browser's local storage */
				c(localSavedState);
			}
		}, this);

	};

	PlmaDataTable.prototype.initStateReset = function () {
		this.resetButton = this.widget.find('.plmadatatable-reset');
		this.resetButton.on('click', $.proxy(function () {
			this.spinner('on');
			/* Remove fixedRows */
			this.fixedRows = {};
			this.placeFixedRows();
			localStorage.removeItem(this.getManagedStateKey());
			this.datatable.state.clear();
			/* Ugly but otherwise the state is not reset yet when initiating the table */
			setTimeout($.proxy(function () {
				/* If the table is transposed, it must be transposed again */
				this.resetTable(this.transposeInfo.isTransposed);
				this.notify('success', 'widget.plmaDataTable.reset.success');
				this.hideMenu();
				this.spinner('off');
			}, this), 400);
		}, this));
	};

	PlmaDataTable.prototype.initDisplayDoc = function () {
		this.docButton = this.widget.find('.plmadatatable-doc');

		this.docButton.on('click', $.proxy(function (e) {
			this.widget.find('.doc-container, .dataTables_wrapper').toggleClass('hidden');
			this.hideMenu();
		}, this));

		this.widget.find('.doc-container .close-doc').on('click', function (e) {
			this.widget.find('.doc-container, .dataTables_wrapper').toggleClass('hidden');
		}.bind(this));
	};

	/**
	 * Returns the key used in the StorageService and/or in the
	 * local storage to store this table's state.
	 */
	PlmaDataTable.prototype.getManagedStateKey = function () {
		return 'plmaDataTable_managedState_' + this.uCssId + (this.options.userName ? '_' : '') + this.options.userName;
	};


	/**
	 * Called when an error occurs trying to save or load the state.
	 */
	PlmaDataTable.prototype.stateErrorCallback = function (data) {
		this.notify('warn', 'widget.plmaDataTable.error.saveState');
	};


	/**
	 * Resume initialization of other plmaDataTable objects, but only if
	 * the state has been loaded already. Otherwise collisions may occur
	 * in DataTable static objects
	 */
	PlmaDataTable.prototype.resumeQueueExecution = function (force) {
		if (force || this.isStateLoaded || !this.options.datatableConfig.stateSave) {
			QueueManager.unfreeze('plmaDataTable');
			QueueManager.run('plmaDataTable');
		}
	};

	/**
	 * Prepares the config object and calls the DataTable constructor.
	 * Optionnally transposes the data before doing so.
	 */
	PlmaDataTable.prototype.initDataTable = function (transposeData) {
		var tableConfig = $.extend(this.options.datatableConfig, {
			autoWidth: false,
			language: {url: this.options.i18nFile},
			/* ajax: pass function if really needed, otherwise initComplete might not be fired */
			ajax: this.options.datatableConfig.serverSide ? this.ajax.bind(this) : undefined,
			lengthMenu: this.getLengthMenu(),
			initComplete: function () {
				/* The DataTables instance can be destroyed as soon as its initialization finishes */
				this.handleDestroy();
				if (!this.options.datatableConfig.stateSave) {
					this.resumeQueueExecution();
				}
				this.postInit();
			}.bind(this)
		});

		if (transposeData) {
			// do not display the extra column when the table is transposed
			tableConfig.columns = removeColumn(tableConfig.columns, PlmaDataTable.EXTRA_COLUMN_NAME);
			this.transposeData(tableConfig);
		}

		for (var i = 0; i < tableConfig.columns.length; i++) {
			this.columnIndexes[tableConfig.columns[i].name] = i;
		}

		if (this.options.enableDataTablesExport) {
			tableConfig.buttons = [{
				extend: 'csv',
				text: mashupI18N.get(WIDGET_NAME, 'widget.plmaDataTable.export'),
				filename: this.options.exportFileName,
				fieldSeparator:this.options.exportSeparator,
				extension: '.csv',
				bom: this.options.datatableConfig.bom,
				exportOptions: {
					columns: $.proxy(function (index, data, node) {
						var column = this.datatable.column(node);
						var reorderedIndex = column.colReorder.order()[index];
						if(this.getExportAll())
						    return !tableConfig.columns[reorderedIndex].name.startsWith('_');
						return column.visible() && !tableConfig.columns[reorderedIndex].name.startsWith('_');
					}, this)
				}
			}];
		}

		tableConfig.createdRow = function (row, data, dataIndex) {
			if (this.options.enableDrag) {
				$(row).prop('draggable', true);
			}
		}.bind(this);

		tableConfig.drawCallback = function (settings) {
			if (this.options.datatableConfig.serverSide) {
				this.removeAllFixedRowsAfterDraw();
			}
			this.setNbFixedColumns(this.getNbFixedColumns());
		}.bind(this);

		/* Prevent initialization of other plmaDataTable objects before starting creating
		 * the datatables object */
		QueueManager.freeze('plmaDataTable');

		/* Set the order of datatable based on json data natural order AND NOT the initial asc order */
		tableConfig = $.extend({order: []}, tableConfig);
		if (this.options.elements.length > 0 && !containColumn(tableConfig.columns, PlmaDataTable.EXTRA_COLUMN_NAME)) {
			tableConfig.columns.push($.extend(this.options.extraColumnOptions, {
				data: null,
				name: PlmaDataTable.EXTRA_COLUMN_NAME,
				className: PlmaDataTable.EXTRA_COLUMN_CLASS,
				render: function (data, type, row, meta) {
					if (type === 'display') {
						var renderedArray = this.options.elements.map(function (element) {
							return element.renderer.call(null, row, meta);
						});
						return renderedArray.join('');
					}
					return data;
				}.bind(this)
			}));
		}

		if (this.options.fixPaginationToolbar) {
		    /* Check DataTable are in sequance of "fix-header" t "fix-footer"*/
			if(!(tableConfig.dom.includes('fix-page-toolbar') || tableConfig.dom.includes('fix-page-toolbar fix-footer') || tableConfig.dom.includes('fix-page-toolbar fix-header')))
                console.warn('DataTable dom: "fix-header" and "fix-footer" classes are not in sequance with respect to t(table).')

            /* Set the pagination-Toolbar css:margin-left on horizontal scroll */
            this.getWidgetContent().scroll(function() {
                this.widget.find('.fix-page-toolbar').css('margin-left', this.getWidgetContent().scrollLeft().toString()+"px");
            }.bind(this));
        }

		this.datatable = this.getTable().DataTable(tableConfig);
	};

	/**
	 *
	 */
	PlmaDataTable.prototype.transposeData = function (tableConfig) {
		this.transposeButton.toggleClass('active');
		this.transposeInfo.isTransposed = !this.transposeInfo.isTransposed;
		/* Limitation: the state of the table cannot be saved if the table
		 * is transposed. */
		this.enableSaveState = !this.transposeInfo.isTransposed;

		/* If data has been persisted, the table has already been transposed,
		 * so just reverse the process */
		if (this.transposeInfo.initialData && this.transposeInfo.initialHeaders) {
			tableConfig.data = this.transposeInfo.initialData;
			tableConfig.aoData = this.transposeInfo.initialData;
			delete tableConfig.aaData;
			tableConfig.columns = this.transposeInfo.initialHeaders;
			tableConfig.aoColumns = this.transposeInfo.initialHeaders;
			this.transposeInfo.initialData = this.transposeInfo.transposedData;
			this.transposeInfo.initialHeaders = this.transposeInfo.transposedHeaders;
			this.transposeInfo.transposedData = tableConfig.data;
			this.transposeInfo.transposedHeaders = tableConfig.columns;
		} else {
			/* Otherwise EVERYBODY TRANSPOSE NOW */
			this.transposeInfo.initialData = tableConfig.data;
			this.transposeInfo.initialHeaders = tableConfig.columns;
			this.transposeInfo.initialColumnRefines = this.columnRefines;
			this.transposeInfo.transposedData = [];
			this.transposeInfo.transposedHeaders = [];
			this.transposeInfo.transposedColumnRefines = {};

			/* Adding a hidden column to hold the refine for each row */
			this.transposeInfo.transposedHeaders.push({
				name: REFINE_COLUMN_NAME,
				data: REFINE_COLUMN_NAME,
				title: REFINE_COLUMN_NAME,
				visible: false,
				searchable: false
			});
			/* First column */
			this.transposeInfo.transposedHeaders.push(this.transposedHeader);


			/* First iterate on headers to create rows with a name and a refine url if any */
			var rowOrder = [];
			for (var j = 0; j < this.transposeInfo.initialHeaders.length; j++) {
				var initialHeader = this.transposeInfo.initialHeaders[j];
				var row = {};
				row[REFINE_COLUMN_NAME] = this.transposeInfo.initialColumnRefines[initialHeader.name] || '';
				row[this.transposedHeader.name] = initialHeader.title;
				this.transposeInfo.transposedData.push(row);
				rowOrder.push(initialHeader.name);
			}

			/* Then iterate on data to fill in the rows */
			for (var i = 0; i < this.transposeInfo.initialData.length; i++) {
				var initialRow = this.transposeInfo.initialData[i];
				var newHeader = {};
				var currentRefine;
				var refineIndex;

				for (var j = 0; j < rowOrder.length; j++) {
					var prop = rowOrder[j];
					var value = initialRow[prop];
					var initialHeader = this.transposeInfo.initialHeaders[j];
					/* The first non technical value (technical column names start with "_") is used as new header */
					if ($.isEmptyObject(newHeader) && initialHeader.name && !initialHeader.name.startsWith('_')) {
						newHeader = {
							name: this.sanitize(value),
							title: value,
							data: this.sanitize(value),
							orderable: true,
							className: 'value numerical-value'
						};
						this.transposeInfo.transposedHeaders.push(newHeader);
					} else if (initialHeader.name == REFINE_COLUMN_NAME) {
						/* Add the refine value for the current row to the refine associated to the new column */
						currentRefine = value;
						refineIndex = j
					} else {
						this.transposeInfo.transposedData[j][newHeader.data] = value;
					}

				}
				if (newHeader.name) {
					this.transposeInfo.transposedColumnRefines[newHeader.name] = currentRefine;
				}
			}
			/* Then remove the first line, duplicate of the header, and remove the refine column. */
			this.transposeInfo.transposedData.splice(1, 1);
			this.transposeInfo.transposedData.splice(refineIndex, 1);

			tableConfig.data = this.transposeInfo.transposedData;
			tableConfig.aoData = this.transposeInfo.transposedData;
			delete tableConfig.aaData;
			tableConfig.columns = this.transposeInfo.transposedHeaders;
			tableConfig.aoColumns = this.transposeInfo.transposedHeaders;
			this.columnRefines = this.transposeInfo.transposedColumnRefines;
		}
	};

	/**
	 * Removes dots, brackets and parenthesis from a string.
	 * Dots are considered to be separators between keys for nested values,
	 * we don't want that.
	 * */
	PlmaDataTable.prototype.sanitize = function (stringToSanitize) {
		return stringToSanitize.replace(/[.\[\]()]/g, '_');
	};

	PlmaDataTable.prototype.initUserActions = function () {
		if (this.options.enableFilters) {
			this.datatable.on('user-select', function (e, dt, type, cell, originalEvent) {
				var dataRow = this.datatable.row(cell.index().row);
				var dataCol = this.datatable.column(cell.index().column);

				var url = this.getRefineUrl(dataRow, dataCol);

				if (url.length > 0) {
				    var opened = window.open(url.toString(), this.options.hitTargetFrame);
                    if (!opened) {
                        // Apple does not allow window.open, see https://developer.apple.com/library/safari/documentation/Tools/Conceptual/SafariExtensionGuide/WorkingwithWindowsandTabs/WorkingwithWindowsandTabs.html
                        exa.redirect(url.toString());
					}
				} else {
					var selectedHits = this.datatable.row(cell.index().row).data().toArray();

					this.publishHits(selectedHits);
				}
			}.bind(this));
		} else {
			this.datatable.on('select', function (e, dt, type, indexes) {
				var selectedHits = this.datatable.rows({selected: true}).data().toArray();
				this.options.onRowClickAction.call(null, selectedHits);
				this.publishHits(selectedHits);
			}.bind(this));
		}

		this.datatable.on('deselect', function (e, dt, type, indexes) {
			var selectedHits = this.datatable.rows({selected: true}).data().toArray();
			this.publishHits(selectedHits);
		}.bind(this));

		if (this.options.onHoverAction) {
			this.getTable().on('mouseover', 'tr', function (e) {
				this.options.onHoverAction.call(null, e);
			}.bind(this));
		}
	};

	PlmaDataTable.prototype.publishHits = function (selectedHits) {
		if (!this.selectionFromSubscription) {
			var hits = [];
			if (selectedHits) {
				hits = selectedHits.map(function (hit) {
					return hit[HIT_ID_COLUMN_NAME]
				});
			}
			this.setSelectedHits(hits);
		} else {
			this.selectionFromSubscription = false;
		}
	};

	PlmaDataTable.prototype.initFilter = function () {
		var widgetHeader = this.widget.find('.widgetHeader');
		if (widgetHeader.length > 0) {
			var searchButton = this.widget.find('.plmadatatable-search');
			var datatablesFilter = this.widget.find('.dataTables_filter');
			var filterInput = datatablesFilter.find('input');
			datatablesFilter.addClass('hidden');
			searchButton.off('click.plmaDataTable').on('click.plmaDataTable', $.proxy(function (e) {
				this.menuPopup.close();
				if (datatablesFilter.hasClass('hidden')) {
					datatablesFilter.removeClass('hidden');
					searchButton.addClass('active');
					filterInput.focus();
				} else {
					searchButton.removeClass('active');
					datatablesFilter.addClass('hidden');
				}
			}, this));
			filterInput
				.on('blur', $.proxy(function () {
					setTimeout(function () {
						searchButton.removeClass('active');
						datatablesFilter.addClass('hidden');
					}, 100);
				}, this))
				.on('keyup keydown', $.proxy(function (e) {
					if (e.which === 27) /* ESC */{
						searchButton.removeClass('active');
						datatablesFilter.addClass('hidden');
					}
				}, this));
		}
	};

	PlmaDataTable.prototype.getLengthMenu = function () {
		var lengthMenu = this.options.datatableConfig.lengthMenu || [10, 25, 50, 100];
		var displayLength = this.options.datatableConfig.displayLength;
		if (displayLength && lengthMenu.indexOf(displayLength) === -1) {
			insertSorted(displayLength, lengthMenu);
		}
		return lengthMenu;
	};

	PlmaDataTable.prototype.initSeeMore = function () {
		var seeMore = this.widget.find('.see-more').clone();

		if (seeMore.length > 0) {
			seeMore.insertAfter(this.getTable().find('.datatables_paginate').addClass('hidden'));
			seeMore.on('click', $.proxy(function (e) {
				this.reloadWithParam(e);
			}, this));
		}
	};


	PlmaDataTable.prototype.initFixedHeader = function () {
		if (this.options.fixedHeader) {

			var container = this.getFixedHeaderContainer();
			this.initFixedHeaderContainer(container);

			/* In fullscreen mode, widget content is scrollable. */
			var widgetContent = this.getWidgetContent();
			if (!container.is(widgetContent)) {
				this.initFixedHeaderContainer(widgetContent);
			}

			$(window).on('resize', function () {
				if (this.isCloneInDom[FIXED_HEADER]) {
					this.placeFixedHeader(true);
				}
			}.bind(this));

			this.datatable.on('column-reorder.dt column-visibility.dt draw.dt', '', function (e, settings, details) {
				/* Re-order fixed header */
				if (this.isCloneInDom[FIXED_HEADER]) {
					this.placeFixedHeader(true);
				}
				/* set correct column style (e.g fixed column style if any) after reordering column */
                this.setFixedColumnsStyle();
			}.bind(this));
		}
	};

	PlmaDataTable.prototype.initFixedHeaderContainer = function (container) {
		container.addClass('relative');
		container.on('scroll', $.proxy(function () {
			this.placeFixedHeader();
		}, this));
	};

	PlmaDataTable.prototype.initFixedColumns = function () {
		var container = this.getFixedHeaderContainer();
		container.addClass('relative');
		var scrollLeft = container.scrollLeft();
		container.on('scroll', $.proxy(function () {
			if (container.scrollLeft() !== scrollLeft && this.getNbFixedColumns() > 0) {
				scrollLeft = container.scrollLeft();
				this.placeFixedColumns();
			}
		}, this));
	};

	PlmaDataTable.prototype.initFixedRows = function () {
		if (this.options.activateFixedRow) {
			if (!this.fixedRows) {
				this.fixedRows = {};
			}

			var container = this.getFixedHeaderContainer();
			this.initFixedRowsContainer(container);

			/* In fullscreen mode, widget content is scrollable. */
			var widgetContent = this.getWidgetContent();
			if (!container.is(widgetContent)) {
				this.initFixedRowsContainer(widgetContent);
			}
		}
	};

	PlmaDataTable.prototype.initFixedRowsContainer = function (container) {
		var widgetContent = this.getWidgetContent();
		this.addClone(FIXED_ROWS, widgetContent);
		container.on('scroll', $.proxy(function () {
			this.placeFixedRows();
		}, this));
	};

	PlmaDataTable.prototype.placeFixedRows = function () {
		var container = this.getFixedHeaderContainer();
		var widgetContent = this.getWidgetContent();
		var widgetTable = widgetContent.find('.dataTable:not(.tableClone)');

		var lineHeight = 18; /* TODO */
		var headerHeight = widgetContent.find('.dataTable thead').height();

		var containerOffset = container.children(':visible').offset() ? container.children(':visible').offset().top : 0;
		var scroll = container.scrollTop() - (container.get(0) === widgetContent.get(0) ? 0 : (this.getTable().offset().top - containerOffset));
		var tableEnd = this.getTable().offset().top + this.getTable().outerHeight(false);


		var nbFixedRows = this.getNbFixedRows();
		var rowsOffset = nbFixedRows * lineHeight + 1;

        var $fixHeader = widgetContent.find('.fix-page-toolbar.fix-header');

        if ($fixHeader.length == 1)
            headerHeight += $fixHeader.height() + 5 ; // +5 or +2 css alignment issue
        else if(!this.options.fixPaginationToolbar) {
        	domPagination=['l','i','p'];
        	if(domPagination.some(i => this.options.datatableConfig.dom.substring(0,this.options.datatableConfig.dom.indexOf('t')).includes(i))) {
        		headerHeight += 29.5;
			}
		}
		if (nbFixedRows > 0) {
			if (scroll > 0 && tableEnd > 0) {
				/* scroll > 0: place the rows under the header (add margin before the real header) */
				var widgetOffset = widgetContent.offset() !== undefined ? widgetContent.offset().top - containerOffset : 0;
				var offset = container.scrollTop() - (container.get(0) === widgetContent.get(0) ? 0 : widgetOffset) + getExistingStickyClonesHeight(container, [this.getClone(FIXED_ROWS)]);
				var $fixHeaderSticky = widgetContent.find('.dataTable.no-footer.tableClone.headerClone.sticky');
                if ($fixHeaderSticky.length == 1)
                    offset = parseFloat($fixHeaderSticky.css("top")) + $fixHeaderSticky.height();

                else if ($fixHeader.length == 1)
                    offset += $fixHeader.height();
				this.getClone(FIXED_ROWS).css('top', offset).css('position', 'absolute');
				this.getClone(FIXED_HEADER).css('position', 'absolute');

				widgetTable.css('position', 'unset').css('top', nbFixedRows * lineHeight + 1 + 'px');
				widgetTable.find('tbody:before').css('line-height', '0');
				document.styleSheets[0].addRule('.mashup.mashup-style [class*="' + this.uCssId + '"] .table-wrapper .dataTable:not(.tableClone) tbody:before', 'line-height: 0;');
			}
			if (scroll <= 0 || tableEnd <= 0) {
				this.getClone(FIXED_ROWS).css('top', headerHeight + 'px').css('position', 'absolute').css('z-index','3');
				widgetTable.css('top', 'inherit');

				widgetTable.find('tbody:before').css('line-height', rowsOffset + 'px');
				document.styleSheets[0].addRule('.mashup.mashup-style [class*="' + this.uCssId + '"] .table-wrapper .dataTable:not(.tableClone) tbody:before', 'line-height: ' + rowsOffset + 'px;');
			}
			if (container.scrollLeft() > 0 & tableEnd > 0 & widgetContent.find('.dataTable.no-footer.tableClone.columnClone').length ==1) {
				this.getClone(FIXED_ROWS).find('.fixedColumn').css('left', container.scrollLeft() +"px").css('position', 'relative');
				widgetContent.find('.dataTable.no-footer.tableClone.columnClone tbody').css('top',rowsOffset + 'px').css('position', 'relative');
				widgetContent.find('.dataTable.no-footer.tableClone.columnClone tr').css('position', 'relative').css('background', 'white');
			}
			else{
				this.getClone(FIXED_ROWS).find('.fixedColumn').css('left', 0).css('position', 'relative');
			}
			if(container.scrollTop() > 5) {
				widgetContent.find('.dataTable.no-footer.tableClone.columnClone tbody').css('top', 0).css('position', '');
				widgetContent.find('.dataTable.no-footer.tableClone.columnClone tr').css('position', '').css('background', '');
			}

		} else {
			document.styleSheets[0].addRule('.mashup.mashup-style [class*="' + this.uCssId + '"] .table-wrapper .dataTable:not(.tableClone) tbody:before', 'line-height: 0;');
		}
	};

	PlmaDataTable.prototype.getFixedHeaderContainer = function () {
		var container;
		if (this.options.scrollContainerSelector === '' || this.widget.hasClass(FullScreenWidget.CSS_CLASS)) {
			container = this.getWidgetContent();
		} else if (this.options.scrollContainerSelector === 'window') {
			container = $(window);
		} else {
			container = this.getTable().closest(this.options.scrollContainerSelector);
		}
		return container;
	};


	PlmaDataTable.prototype.addClone = function (cloneName, container) {
		var clone = this.getClone(cloneName);
		/* Cannot append directly to the window */
		if (container.length > 0 && container.get(0) === window) {
			$('body').append(clone);
		} else {
			container.append(clone);
		}
		/* Copy column widths */
		matchWidths(this.getTable(), clone);
		matchHeights(this.getTable(), clone);
		this.isCloneInDom[cloneName] = true;
	};

	PlmaDataTable.prototype.removeClone = function (cloneName) {
		if (this.clones[cloneName]) {
			this.clones[cloneName].remove();
			delete this.clones[cloneName];
		}
		this.isCloneInDom[cloneName] = false;
	};

	PlmaDataTable.prototype.placeFixedHeader = function (forceUpdate) {
		var container = this.getFixedHeaderContainer();
		var widgetContent = this.getWidgetContent();

		var containerOffset = container.children(':visible').offset() ? container.children(':visible').offset().top : 0;
		var scroll = container.scrollTop() - (container.get(0) === widgetContent.get(0) ? 0 : (this.getTable().offset().top - containerOffset));
		var tableEnd = this.getTable().offset().top + this.getTable().outerHeight(false);

		/* Force to create a new clone if needed */
		if (forceUpdate) {
			this.removeClone(FIXED_HEADER);
			this.removeClone(FIXED_COLUMNS_HEADER);
		}

		/* Add or remove the clone depending on the scroll position */
		if (scroll > 0 && tableEnd > 0 && !this.isCloneInDom[FIXED_HEADER]) {
			this.addClone(FIXED_HEADER, widgetContent);
			this.getClone(FIXED_HEADER).css('position', 'absolute');
			if (this.getNbFixedColumns() > 0 && this.isCloneInDom[FIXED_COLUMNS]) {
				this.addClone(FIXED_COLUMNS_HEADER, widgetContent);
			}
		}
		if ((scroll <= 0 || tableEnd <= 0) && this.isCloneInDom[FIXED_HEADER]) {
			// this.addClone(FIXED_HEADER, widgetContent);
			this.removeClone(FIXED_HEADER);
			if (this.getNbFixedColumns() > 0) {
				this.removeClone(FIXED_COLUMNS_HEADER);
			}
		}

		/* Position the clone if needed */
		var widgetOffset = widgetContent.offset() !== undefined ? widgetContent.offset().top - containerOffset : 0;

		if (this.isCloneInDom[FIXED_HEADER]) {
            var offset = container.scrollTop() - (container.get(0) === widgetContent.get(0) ? 0 : widgetOffset) + getExistingStickyClonesHeight(container, [this.getClone(FIXED_HEADER), this.getClone(FIXED_ROWS)]);
            var $fixHeader = this.widget.find('.fix-page-toolbar.fix-header');

            if ($fixHeader.length == 1)
                offset += $fixHeader.height() ;

            this.getClone(FIXED_HEADER).css('top', offset);
            if (this.getNbFixedColumns() > 0 && this.isCloneInDom[FIXED_COLUMNS]) {
                this.getClone(FIXED_COLUMNS_HEADER).css({
                    'top': offset,
					'position': '',
                    'left': this.getClone(FIXED_COLUMNS).css('left') || 0
                });
            }
		}
	};

	PlmaDataTable.prototype.placeFixedColumns = function (forceUpdate) {
		var container = this.getFixedHeaderContainer();
		var widgetContent = this.getWidgetContent();

		var containerOffset = container.children(':visible').offset() != null ? container.children(':visible').offset().left : 0;
		var scroll = container.scrollLeft() - (container.get(0) === widgetContent.get(0) ? 0 : (widgetContent.offset().left - containerOffset));
		var tableEnd = this.getTable().offset().left + this.getTable().outerWidth(false);

		/* Force to create a new clone if needed */
		if (forceUpdate) {
			this.removeClone(FIXED_COLUMNS);
		}

		/* Add or remove the clone depending on the scroll position */
		if (scroll > 0 && tableEnd > 0 && !this.isCloneInDom[FIXED_COLUMNS]) {
			this.addClone(FIXED_COLUMNS, widgetContent);
			if (this.options.fixedHeader && this.isCloneInDom[FIXED_HEADER]) {
				this.addClone(FIXED_COLUMNS_HEADER, widgetContent);
			}
		}
		if ((scroll <= 0 || tableEnd <= 0) && this.isCloneInDom[FIXED_COLUMNS]) {
			this.removeClone(FIXED_COLUMNS);
			if (this.options.fixedHeader) {
				this.removeClone(FIXED_COLUMNS_HEADER);
			}
		}

		/* Position the clone if needed */
		if (this.isCloneInDom[FIXED_COLUMNS]) {
			var offset = container.scrollLeft() - (container.get(0) === widgetContent.get(0) ? 0 : widgetContent.offset().left - containerOffset);
			this.getClone(FIXED_COLUMNS).css({
				'left': offset,
				'position': '',
				'top': this.getTable().position().top,
				'z-index': 2,
			});
			if (this.options.fixedHeader && this.isCloneInDom[FIXED_HEADER]) {
				this.getClone(FIXED_COLUMNS_HEADER).css({
					'left': offset,
					'top': this.getClone(FIXED_HEADER).css('top') || 0
				});
			}
		}
	};

	PlmaDataTable.prototype.getNbFixedColumns = function () {
		return this.nbFixedColumns || 0;
	};

    PlmaDataTable.prototype.getExportAll = function () {
		if (this.exportAll === undefined){
			return this.options.enableExportAll;
		}
        return this.exportAll;
    };

	PlmaDataTable.prototype.setFixedColumnsStyle = function(nbFixedColumns){
        nbFixedColumns = nbFixedColumns === undefined ? this.getNbFixedColumns():nbFixedColumns;
        /* Adding a specific style to the fixed cells */
        this.datatable.cells().nodes().to$().removeClass('fixedColumn');
        for (var i = 0; i < nbFixedColumns; i++) {
            this.datatable.column(i + ':visible').nodes().to$().addClass('fixedColumn');
        }
    };

	PlmaDataTable.prototype.setNbFixedColumns = function (nbFixedColumns, saveState) {
		nbFixedColumns = parseInt(nbFixedColumns);
		if (!isNaN(nbFixedColumns)) {
			this.nbFixedColumns = nbFixedColumns;

			this.setFixedColumnsStyle(nbFixedColumns);

			/* Resetting column clones */
			this.removeClone(FIXED_COLUMNS_HEADER);
			this.removeClone(FIXED_COLUMNS);
			this.getWidgetContent().scrollLeft(0);

			/* Saving state */
			if (saveState) {
				this.datatable.state.save();
			}
		}
	};

    PlmaDataTable.prototype.setExportAll = function (exportAll, saveState) {
        this.exportAll = exportAll;
        if (saveState) {
            this.datatable.state.save();
        }
    };

	PlmaDataTable.prototype.initClones = function () {
		this.clones = {};
		this.isCloneInDom = {};

		var _this = this;

		function createClone(classes) {
			return _this.getTable().clone(true)
				.addClass(classes)
				.removeAttr('id');
		}

		this.cloneConstructors = {
			'fixedHeader': function () {
				var headerClone = createClone('tableClone headerClone sticky');
				headerClone.find('tbody').remove();
				this.isCloneInDom[FIXED_HEADER] = false;
				return headerClone;
			},
			'fixedColumns': function () {
				var columnClone = createClone('tableClone columnClone');
				columnClone.find('tr').each($.proxy(function (i, e) {
					while (e.childNodes.length > this.getNbFixedColumns()) {
						e.removeChild(e.lastChild);
					}
				}, this));
				this.isCloneInDom[FIXED_COLUMNS] = false;
				return columnClone;
			},
			'fixedColumnsHeader': function () {
				var columnsHeaderClone = createClone('tableClone columnsHeaderClone');
				columnsHeaderClone.find('tbody').remove();
				columnsHeaderClone.find('th').slice(this.getNbFixedColumns()).remove();
				this.isCloneInDom[FIXED_COLUMNS_HEADER] = false;
				return columnsHeaderClone;
			},
			'fixedRows': function () {
				var rowsClone = createClone('tableClone rowsClone sticky');
				rowsClone.find('thead').empty();
				rowsClone.find('tbody').empty();
				this.isCloneInDom[FIXED_ROWS] = false;
				return rowsClone;
			}
		};
	};

	PlmaDataTable.prototype.getClone = function (cloneName) {
		if (!this.clones.hasOwnProperty(cloneName)) {
			if (this.cloneConstructors.hasOwnProperty(cloneName)) {
				this.clones[cloneName] = this.cloneConstructors[cloneName].call(this);
			} else {
				this.clones[cloneName] = $();
			}
		}
		return this.clones[cloneName];
	};

	PlmaDataTable.prototype.createHeader = function (name) {
		return {
			name: name,
			data: name,
			title: name,
			orderable: true
		};
	};

	PlmaDataTable.prototype.initTranspose = function () {
		this.transposeButton = this.widget.find('.plmadatatable-transpose');
		if (this.options.transposedHeader === undefined || this.options.transposedHeader === '') {
			if (this.options.datatableConfig.transposedHeader) {
				this.transposedHeader = this.options.datatableConfig.transposedHeader;
			} else {
				this.transposedHeader = this.createHeader(mashupI18N.get(WIDGET_NAME, 'widget.plmaDataTable.transpose.columnTitle'));
			}
		} else {
			this.transposedHeader = this.createHeader(this.options.transposedHeader);
		}

		this.transposeButton.on('click', $.proxy(function () {
			this.transposeTable();
		}, this));
	};

	PlmaDataTable.prototype.transposeTable = function () {
		this.resetTable(true);
	};

	PlmaDataTable.prototype.resetTable = function (transpose) {
		this.clearTable();

		/* Resetting the table content and some interactions */
		this.isInit = false;
		this.isInitFilter = false;
		this.isInitSeeMore = false;
		this.isInitExport = false;
		this.isStateLoaded = false;
		this.isInitKeyboardEvents = false;
		this.setNbFixedColumns(0, false);
		this.setExportAll(true, false);

		this.initDataTable(transpose);
		this.isInit = true;

	};

	PlmaDataTable.prototype.initExportData = function () {
		var headerExportButton = this.widget.find('.plmadatatable-export');
		if (this.options.enableDataTablesExport) {
			var standardExportButton = this.widget.find('.buttons-csv');
			headerExportButton.off('click').on('click', function () {
				standardExportButton.click();
				this.notify('success','widget.plmaDataTable.export.success'," "+this.options.datatableConfig.data.length);
			}.bind(this));
			this.isInitExport = true;
		} else if (this.options.enableControllerExport) {
			var controllerExportForm = this.widget.find('.export-data-form');
			headerExportButton.off('click').on('click', function () {
			    //Only export columns that are visible
			    var ExportAllColumn = this.getExportAll();
					this.datatable.columns().every(function(){
						// check if not technical column(identified by text starts with '_' or do not have class 'value'
						// Also the columnId is set as 'column_'+orderInConfig([0-9]+)
						let headerClassList = $(this.header()).attr('class');
						if (headerClassList.search('value') != -1){
							let columnIdClass = headerClassList.split(' ').filter(function(s){
								return s.match(/column_[0-9]+/) != null;
							})
							if(columnIdClass.length > 0) {
								var columnData = controllerExportForm.find(".column-data." + columnIdClass[0]);
								columnData.attr('name', (ExportAllColumn || this.visible() ? 'columns': 'no-export') )
							}else{
								console.error("Unable to find the column input inside export form.");
								console.error("Found column Header Class '" + headerClassList + "' but pattern 'column_[0-9]+' is missing");
							}
						}
					});
                controllerExportForm.submit();
                this.notify('success','widget.plmaDataTable.export.success'," "+controllerExportForm.find("[name='hf']").attr('value'));
			}.bind(this));
		}
	};

	PlmaDataTable.prototype.initExplicitSort = function () {
		var ranks = {};

		for (var columnName in this.options.columnExplicitSorts) {
			ranks[columnName] = {};
			var columnOrder = this.options.columnExplicitSorts[columnName];
			for (var i = 0; i < columnOrder.length; i++) {
				var value = columnOrder[i]
				ranks[columnName][value] = i;
			}
		}

		for (var colName in this.options.columnExplicitSorts) {
			(function (columnName) {
				$.fn.dataTableExt.oSort[columnName + '-explicit-desc'] = function (x, y) {
					var
						rankX = ranks[columnName][x],
						rankY = ranks[columnName][y];
					return rankX < rankY ? 1 : rankX > rankY ? -1 : 0;
				}
				$.fn.dataTableExt.oSort[columnName + '-explicit-asc'] = function (x, y) {
					var
						rankX = ranks[columnName][x],
						rankY = ranks[columnName][y];
					return rankX < rankY ? -1 : rankX > rankY ? 1 : 0;
				}
			})(colName);
		}
	};

	PlmaDataTable.prototype.initDrag = function () {
		if (this.options.enableDrag) {
			this.table
				.on('dragstart', 'tr', function (e) {
					var selectedRows = this.datatable.rows({ selected: true });
					var draggedRow = this.datatable.rows(e.currentTarget);
					// the dragged row is selected: use the whole selection for the message
					// otherwise: use only the dragged row
					var applicableRows = selectedRows.nodes().to$().is(e.currentTarget) ? selectedRows : draggedRow;
					var data = Array.from(applicableRows.data());
					var indexes = Array.from(applicableRows.indexes());
					e.originalEvent.dataTransfer.setData('Text', JSON.stringify(this.options.messageBuilder.call(this, data, indexes)));
				}.bind(this))
				.on('dragover', 'tr', function (e) {
					e.preventDefault();
				});
		}
	};

	PlmaDataTable.prototype.initKeyboardEvents = function () {
		this.widget
			// make the widget focusable
			.prop('tabindex', 0)
			.keydown(function (e) {
				// the widget is focused when doing ctrl + (A or a)
				if (e.ctrlKey && (e.keyCode === 65 || e.keyCode === 97)
					&& (this.widget.is(document.activeElement) || $(document.activeElement).parent(this.widget).length)
				) {
					e.preventDefault();
					this.datatable.rows().select();
				}
			}.bind(this))
			// make the tbody scrollable with the arrow keys
			.find('tbody')
				.prop('tabindex', 0);
	};

	PlmaDataTable.prototype.clearTable = function () {
		for (var cloneName in this.clones) {
			this.removeClone(cloneName);
		}
		this.hideSettings();
		this.datatable.destroy();
		this.getTable().empty();
		this.widget.find('.dataTables_filter').remove();
	};


	PlmaDataTable.prototype.getWidgetContent = function () {
		return this.widget.find('.widgetContent.table-wrapper');
	};

	PlmaDataTable.prototype.onHitsSelect = function (selectedHits) {
		if (this.options.datatableConfig.serverSide && selectedHits) {
			this.selectionFromSubscription = true;
			this.datatable
				.rows()
				.deselect();
			this.selectionFromSubscription = true;
			this.datatable
				.rows(function (idx, data, node) {
					return selectedHits.indexOf(data[HIT_ID_COLUMN_NAME]) !== -1;
				})
				.select();
		}
	};

	/**
     * selectedColumns function sets the SELECTED_COLUMNS array based on the visibility of DataTable columns.
     * If the hideEmpty option is true, it populates SELECTED_COLUMNS with the visibility status of DataTable columns.
     */
    PlmaDataTable.prototype.selectedColumns = function () {
        if (this.options.hideEmpty === "true") {
            SELECTED_COLUMNS = [];
            this.datatable.columns()[0].forEach(index => {
                SELECTED_COLUMNS.push(this.datatable.column(index).visible());
            });
        }
    };

	PlmaDataTable.prototype.initSettings = (function () {
		var settingsFormHtml = '<div class="widgetContent not-top settings-form hidden">' +
			'<div class="tabs">' +
			'</div>' +
			'<div class="options">' +
			'</div>' +
			'<i class="close fonticon fonticon-cancel"></i>' +
			'</div>';

		return function () {
			this.settingsForm = $(settingsFormHtml);
			this.settingsButton = this.widget.find('.plmadatatable-config');

			this.settingsForm.insertAfter(this.widget.find('.widgetHeader'));

			this.settingsForm.find('.close').on('click', $.proxy(function () {
				this.settingsButton.removeClass('active');
				this.settingsForm.addClass('hidden');
			}, this));

			this.settingsButton.on('click', $.proxy(function () {
				if (this.settingsForm.hasClass('hidden')) {
					this.showSettings();
				} else {
					this.hideSettings()
				}
			}, this));

			this.settingsForm
				.on('change', 'input[name="displayColumns"]', function (e) {
					this.datatable.column(e.target.value).visible(e.target.checked);
                    this.selectedColumns();
					/* update fixed column if new displayed column is in the range of fixed columns */
                    var currentColumnIdx = parseInt(e.target.value);
                    var fixedColumnIdx = parseInt(this.settingsForm.find('input[name="fixedColumns"]').val());
                    if(!isNaN(currentColumnIdx) && !isNaN(fixedColumnIdx) && currentColumnIdx <= fixedColumnIdx){
                        this.setNbFixedColumns(fixedColumnIdx, true);
                    }
				}.bind(this))
				.on('change', 'input[name="exportAll"]', function (e) {
					var checked = e.target.checked;
					if (!isNaN(checked)) {
						this.setExportAll(checked, true);
					}
				}.bind(this))
				.on('change', 'input[name="fixedColumns"]', function (e) {
					var value = e.target.value;
					if (value >= 0 && value <= this.getVisibleColumns()) {
						this.setNbFixedColumns(value, true);
					}
				}.bind(this))
				.on('change', 'input[name="fixedRows"]', function (e) {
					/* TODO: add or remove row */
				}.bind(this))
				.on('click', '.fixed-rows-activate', function (e) {
					this.activateFixedRowSelection();
				}.bind(this))
				.on('click', '.fixed-rows-desactivate', function (e) {
					this.desactivateFixedRowSelection();
				}.bind(this));

			/* Update the column-visibility menu if it is visible */
			this.datatable.on('column-reorder', $.proxy(function (e, settings, details) {
				if (details.drop && !this.settingsForm.hasClass('hidden')) {
					this.generateSettingsMenu();
				}
			}, this));
		};
	})();

	/**
	 * Displays the settings menu.
	 */
	PlmaDataTable.prototype.showSettings = function () {
		this.settingsButton.addClass('active');
		this.settingsForm.removeClass('hidden');
		this.generateSettingsMenu();
		this.displayFixedRoxs();
		this.hideMenu();
	};

	/**
	 * Hides the settings menu.
	 */
	PlmaDataTable.prototype.hideSettings = function () {
		this.settingsButton.removeClass('active');
		this.settingsForm.addClass('hidden');
	};
	/**
	 * Generates the settings menu.
	 */
	PlmaDataTable.prototype.generateSettingsMenu = (function () {
		function selectSection(sectionTab, sectionOptions, allTabs, allOptions) {
			allTabs.removeClass('active');
			allOptions.addClass('hidden');
			sectionTab.addClass('active');
			sectionOptions.removeClass('hidden');
		}

		return function () {
			var datatable = this.datatable;
			var uCssId = this.uCssId;
			var getNbFixedColumns = $.proxy(this.getNbFixedColumns, this);
			var getVisibleColumns = $.proxy(this.getVisibleColumns, this);

			/* This object will generates all sections in the menu. Each key represents a section,
			 * and the corresponding function must return the HTML to display in this section
			 * Section names can be i18n-ized with the i18n key 'widget.plmaDataTable.config.section.SECTION_NAME'
			 * where SECTION_NAME is the section name.
			 *  */
			var settingSections = {
				'displayedColumns': function () {
					var html = '<div class="table"><div class="column-1">';
					datatable.columns().every(function () {
						var column = this;
						if (!column.header().textContent.startsWith('_')) {
							var index = column.index();
							var id = uCssId + '_' + 'displayedColumn_' + index;
							html += '<p class="displayedColumn">' +
								'<input type="checkbox" name="displayColumns" id ="' + id + '" value="' + index + '" ' + (column.visible() ? 'checked' : '') + ' />' +
								'<label for="' + id + '">' + column.header().textContent + '</label>' +
								'</p>';
						}
					});
					html += "</div>";
					if(JSON.parse(this.options.enableExportAll))
                        html += '<div class="column-2"><p class="column-config-list"><input type="checkbox" name="exportAll" id ="exportAll" value="true" ' + (this.getExportAll() ? 'checked' : '') + ' />'+ '<label for="exportAll">' + mashupI18N.get(WIDGET_NAME, 'widget.plmaDataTable.config.section.exportAllColumns') + '</label><span class="fonticon fonticon-info split-help " title="' + mashupI18N.get(WIDGET_NAME, 'widget.plmaDataTable.config.section.exportAllColumns.info') + '"></span></p></div>';
					html+="</div>";
					return html;
				}.bind(this),
				'fixedColumns': function () {
					var visibleColumns = 0;

					var html = '<label for="fixedColumns">' + mashupI18N.get(WIDGET_NAME, 'widget.plmaDataTable.config.fixedColumns.number') + '</label>' +
						'<input type="number" name="fixedColumns" id="fixedColumns" value="' + getNbFixedColumns() + '" min="0" max="' + getVisibleColumns() + '">';
					return html;
				},
				'fixedRows': function () {
						var html = '<div class="fixed-rows"></div><div class="fixed-rows-action"><span class="fixed-rows-activate">' + mashupI18N.get(WIDGET_NAME, 'widget.plmaDataTable.config.section.fixedRows.select') + '</span><span class="fixed-rows-desactivate hidden">' + mashupI18N.get(WIDGET_NAME, 'widget.plmaDataTable.config.section.fixedRows.ok') + '</span></div>';
						if (this.enableSaveState) {
							html = '<div class="fixed-rows-info">' + mashupI18N.get(WIDGET_NAME, 'widget.plmaDataTable.config.section.fixedRows.info') + '</div>' + html;
						}
						return html;
				}.bind(this)
			};

			var sectionsTabs = this.settingsForm.find('.tabs');
			var sectionsOptions = this.settingsForm.find('.options');
			sectionsTabs.empty();
			sectionsOptions.empty();
			var first = true;
			var settingsForm = this.settingsForm;
			for (var section in settingSections) {
				var sectionTab = $('<div class="section-tab section-' + section + '" tabindex="0" data-section="' + section + '">' + mashupI18N.get(WIDGET_NAME, 'widget.plmaDataTable.config.section.' + section) + '</div>');
				var sectionOptions = $('<div class="hidden section-options section-' + section + '">' + settingSections[section].call() + '</div>')
				sectionTab.on('click', function (e) {
					var section = $(e.target).data('section');
					selectSection(settingsForm.find('.tabs .section-tab.section-' + section),
						settingsForm.find('.options .section-options.section-' + section),
						settingsForm.find('.tabs .section-tab'),
						settingsForm.find('.options .section-options'));
				});

				if (first) {
					selectSection(sectionTab, sectionOptions, this.settingsForm.find('.tabs .section-tab'), this.settingsForm.find('.options .section-options'));
					first = false;
				}
				if (section !== 'fixedRows' || this.options.activateFixedRow) {
					sectionsTabs.append(sectionTab)
					sectionsOptions.append(sectionOptions);
				}
			}

		};
	})();

	PlmaDataTable.prototype.getVisibleColumns = function () {
		var visibleColumns = 0;
		this.datatable.columns().visible().each(function (e) {
			if (e === true) {
				visibleColumns++;
			}
		});
		return visibleColumns;
	};

    /* Custom Sort function to get null values at top for ascending and at bottom for descending sort */
	PlmaDataTable.prototype.sortTable = function(objects, columnIndex, sortOrder) {
		const columnName = `column_${columnIndex-1}`;
		const sortedObjects = objects.sort((a, b) => {
			const aValue = a[columnName];
			const bValue = b[columnName];

			// Handle null values at the top for ascending order
			if (sortOrder === 'asc' && (aValue == null || bValue == null)) {
				if (aValue == null && bValue == null) {
					return 0;
				}
				return aValue == null ? -1 : 1;
			}

			// Handle null values at the bottom for descending order
			if (sortOrder === 'desc' && (aValue == null || bValue == null)) {
				if (aValue == null && bValue == null) {
					return 0;
				}
				return aValue == null ? 1 : -1;
			}

			// Handle non-null values
			if (aValue < bValue) {
				return sortOrder === 'asc' ? -1 : 1;
			} else if (aValue > bValue) {
				return sortOrder === 'asc' ? 1 : -1;
			}
			return 0;
		});
		return sortedObjects;
	}

	PlmaDataTable.prototype.columnContainsEmpty = function(data, columnIndex) {
		const columnName = `column_${columnIndex - 1}`;
		for (const item of data) {
			if (item[columnName] === "") {
				return true; // If empty value found, return true
			}
		}
		return false; // If no empty empty found, return false
	};


	PlmaDataTable.prototype.ajax = function (data, callback, settings) {
		if (this.options.datatableConfig.serverSide) {
			this.spinner('on');
			var client = new PlmaAjaxClient(this.widget);
			/* If an AJAX url is provided, forward its parameters */
			if (this.options.ajaxUrl) {
				var ajaxUrl = new BuildUrl(this.options.ajaxUrl);
				client.setQueryString(ajaxUrl.toString(), true);
			}
			client.addWidget(PlmaAjaxClient._getUCssId(this.widget.attr('class')));
			client.addParameter('plmaDataTable_onlyData', 'true');
			/* Draw counter to make sure calls are rendered in the correct order */
			client.addParameter('plmaDataTable_draw', data.draw);

			for (var i = 0; i < this.options.paramNames.page.length; i++) {
				var params = convertToPageParams(data.start, data.length);
				/* Page number */
				client.addParameter(this.options.paramNames.page[i], params.pageNumber + 1);
				/* Page length: splitting the desired number of hits across all feeds */
				var perPageValues = split(params.perPage, this.options.paramNames.perPage.length);
				client.addParameter(this.options.paramNames.perPage[i], perPageValues[i]);
			}
			/* Sort */
			var sortParamValue = data.order
				.map(function (order) {
					var columnName = this.datatable.column(order.column).dataSrc();
					var sortField = this.options.columnSortFields[columnName];
					return order.dir + '(' + sortField + ')';
				}, this)
				.join(',');
			if (sortParamValue) {
				this.options.paramNames.sort.forEach(function (param) {
					client.addParameter(param, sortParamValue);
				});
			}
			this.preAjaxCallbacks.fire();
			client.getWidget(
				/* Success */
				$.proxy(function (widgets, appendScript) {
					var newData = JSON.parse(widgets[0].html);
					if (newData.error) {
						this.notify('error', newData.error);
						if (typeof console != 'undefined' && console.error) {
							console.error('plmaDataTable: ' + newData.details);
						}
					} else {
						if (sortParamValue && this.options.datatableConfig.isNullSort && this.columnContainsEmpty(newData.data,data.order[0].column))
						    newData.data=this.sortTable(newData.data,data.order[0].column,data.order[0].dir);
						sanitizeData(newData.data);
						if (newData.data.length > data.length) {
							/* Too much data, remove the useless ones */
							newData.data.splice(0, newData.data.length - data.length);
						}
						settings.json = newData;
						callback(newData);
					}
					if (this.options.hideEmpty === "true" && newData.data.length != 0) {
                        this.hideEmpty();
                    }
					this.postAjaxCallbacks.fire();
					this.placeFixedColumns();
					this.spinner('off');
				}, this),
				/* Fail */
				$.proxy(function (xhr, textStatus, errorThrown) {
					this.spinner('off');
					this.notify('error', 'widget.plmaDataTable.error.server');
					if (typeof console != 'undefined' && console.error) {
						console.error('plmaDataTable: ' + textStatus, errorThrown);
					}
				}, this)
			);
		}
	}

	PlmaDataTable.prototype.getTable = function () {
		return this.table;
	};

	PlmaDataTable.prototype.notify = function (notificationType, i18nKey) {
		var additionalText = [];
		for (var i = 2; i < arguments.length; i++) {
			additionalText.push(arguments[i]);
		}

		$.notify(mashupI18N.get(WIDGET_NAME, i18nKey) + additionalText.join(' '), {
			className: notificationType,
			autoHideDelay: 5000
		});
	};

	PlmaDataTable.prototype.spinner = (function () {
		var overlay = $('<div class="overlay"></div>');
		var DATA_OVERLAY = 'overlay;'

		return function (onOff) {
			if (onOff === undefined || onOff === 'on') {
				if (!this.widget.data(DATA_OVERLAY)) {
					var overlayClone = overlay.clone();
					this.widget.data(DATA_OVERLAY, overlayClone);
					overlayClone.insertAfter(this.getTable().parent());
					this.widget.showSpinner();
				}
			} else if (onOff === 'off') {
				this.widget.hideSpinner();
				if (this.widget.data(DATA_OVERLAY)) {
					this.widget.data(DATA_OVERLAY).remove();
					this.widget.data(DATA_OVERLAY, null);
				}
			} else {
				/* Invalid command */
				if (console && console.log) {
					console.log('PlmaDataTable.prototype.spinner only understands "on" and "off" as arguments');
				}
			}
		};
	})();

	PlmaDataTable.prototype.getRefineUrl = function (dataRow, dataCol) {
		/* Refine link from the row */
		var buildUrl1 = new BuildUrl();
		var anchor = '';

		var rowData = dataRow.data();
		if (rowData && rowData.hasOwnProperty(REFINE_COLUMN_NAME)) {
			var refine1 = rowData[REFINE_COLUMN_NAME];
			/* Workaround for the fact that BuildUrl does not handle anchors */
			var anchorSplit = refine1.split('#');
			anchor = anchorSplit[1];
			buildUrl1 = new BuildUrl(anchorSplit[0]);
		}

		/* Refine link from the column */
		var buildUrl2;
		var reorderedIndex = dataCol.colReorder.order()[dataCol.index()];
		var columnHeader = this.options.datatableConfig.columns[reorderedIndex];
		if (columnHeader) {
			var refine2 = this.columnRefines[columnHeader.name];
			buildUrl2 = new BuildUrl(refine2);
		}

		if (buildUrl2) {
			for (var paramName in buildUrl2.params) {
				buildUrl1.addParameters(paramName, buildUrl2.params[paramName]);
			}
		}

		for (var i = 0; i < this.options.removeParameters.length; i++) {
			buildUrl1.removeParameter(this.options.removeParameters[i]);
		}

		return buildUrl1.toString() + (anchor ? '#' + anchor : '');
	};

	/**
	 * Reload the plma chart in ajax with all data (add parameter to set max_per_level param to 0)
	 */
	PlmaDataTable.prototype.reloadWithParam = function (e) {
		var widgetHeader = this.widget.find('.widgetHeader').clone(true);

		var url = new BuildUrl(window.location.href);
		var options = {};
		options.success = $.proxy(function () {
			/* Get the old header back */
			$('.' + this.uCssId + ' .widgetHeader').replaceWith(widgetHeader);
		}, this);

		var client = new PlmaAjaxClient(this.widget, options);
		var parameters = url.getParameters();

		/* Delete the base params */
		client.getAjaxUrl().params = {};

		$.each(parameters, function (index, value) {
			if (index != '' && index != '_') {
				for (var i = 0; i < value.length; i++) {
					client.getAjaxUrl().addParameter(index, value[i], false);
				}
			}
		});

		/* Add parameter */
		client.getAjaxUrl().addParameter(e.target.getAttribute('data-name'), e.target.getAttribute('data-value'));

		/* set wuids to update */
		client.addWidget(this.uCssId);

		client.update();
	};

	/**
	 * Destroy the DataTables instance to prevent memory leaks
	 */
	PlmaDataTable.prototype.destroy = function () {
		if (this.datatable && this.initComplete) {
			/* The DataTable has been fully initialized, we can destroy it*/
			this.datatable.destroy();
		} else {
			/* Otherwise, wait for its initialization to destroy it */
			PlmaDataTable._destroy.push(this);
		}
	};

	/**
	 * Must be called when the DataTables instance has been fully initialized
	 */
	PlmaDataTable.prototype.handleDestroy = function () {
		this.initComplete = true;
		var index = PlmaDataTable._destroy.indexOf(this);
		if (index !== -1) {
			this.destroy();
			PlmaDataTable._destroy.splice(index, 1);
		}
	};

})(PlmaDataTable);
