EXPUtils = (function(){
    function getAPI(name, req) {
        return new Promise(function (resolve, reject) {
            (req || require)([name], function (module) {
                resolve(module);
            }, function () {
                reject();
            });
        });
    };
	function getBlob(getDataUrl, successCallback){
		fetch(getDataUrl())
			.then(res => res.blob())
			.then(successCallback);
	};
		
    return {
		getWafData: function() {
            return getAPI('DS/WAFData/WAFData', window.top.require);
        },
		generateTempId: function() {
        	return "temp_" + (new Date).getTime() + Math.floor(1e4 * Math.random())
        },
        fetch3DSpaceUrl: function(data){
            if(data.platformUrl != undefined && data.platformUrl.length > 5){
                data.successCallback(data);
                return;
            }
            window.dashboardController.getPlatformServices('getServiceUrl', { 
				serviceName: '3DSpace' 
				}, function(url) {
                    if(url == undefined){ // If the URL is not found note error.
                        data.stage = 'Fetch3DSpaceUrl';
                        data.error = 'Unable to get 3DSpace url';
                        data.failureCallback(data)
                    }else{
                        data.platformUrl = url;
                        data.successCallback(data);
                    }
				});
        },
        fetchCSRFToken: function(data){
            var url = data.platformUrl + '/resources/v1/application/CSRF';
            data.API.authenticatedRequest(url, {
                method: 'GET',
                type: 'json',
                onComplete: function(response) {
                    data.csrf = response.csrf;
                    data.successCallback(data);
                },
                onFailure: function(error) {
                    data.stage = 'FetchCSRFToken';
                    data.error = 'Unable to retrieve CSRF Token [' + error + ']';
                    data.failureCallback(data);
                }
            });
        },
        fetchDocumentInfo: function(data){
            var url = data.platformUrl + '/resources/v1/modeler/documents/'+ data.documentInfo.id +
                '?$include=files&$fields=all';
            data.API.authenticatedRequest(url, {
                method: "GET",
                type: 'json',
                headers: {
                    'ENO_CSRF_TOKEN': data.csrf.value,
                    'SecurityContext': data.securityContext,
                    'Content-Type': 'application/json',
                },
                onComplete: function (response) {
                    data.result = response.data[0];
                    data.successCallback(data);
                },
                onFailure: function (error) {
                    data.stage = 'FetchDocumentInfo';
                    data.error = 'Unable to retrieve document info from 3DSpace [' + error + ']';
                    data.failureCallback(data);
                }
            });
        },
        fetchCheckoutTicket: function(data){
            if(data.fileRef == undefined || data.documentInfo.id == undefined || data.fileRef.id == undefined){
                // No Need to checkout just call the success function.
                data.successCallback(data);
                return;
            }
            let fileId = data.fileRef.id;
            let url = data.platformUrl + '/resources/v1/modeler/documents/'+
                data.documentInfo.id + '/files/'+ fileId +
                '/CheckoutTicket?$include=all&$fields=all';

            data.API.authenticatedRequest(url, {
                method: 'PUT',
                type: 'json',
                headers: {
                    'ENO_CSRF_TOKEN': data.csrf.value,
                    'SecurityContext': data.securityContext,
                    'Content-Type': 'application/json',
                },
                onComplete: function(response) {
                    data.checkOutTicket = response;
                    data.successCallback(data);
                },
                onFailure: function(error) {
                    data.stage = 'FetchCheckoutTicket';
                    data.error = 'Error retrieving Checkout Ticket(may be Already CheckedOut file) [' + error + ']';
                    data.failureCallback(data);
                }
            });
        },
        fetchCheckInTicket: function(data){
			if(data.fileRef == undefined){
				// Non file mode, hence just invoke successCallback
				data.successCallback(data);
				return;
			}
            let url = data.platformUrl + '/resources/v1/modeler/documents'+
                (data.documentInfo.id != undefined ? ('/' + data.documentInfo.id) : '' ) +
                '/files/CheckinTicket?$include=all&$fields=all';

            data.API.authenticatedRequest(url, {
                method: 'PUT',
                type: 'json',
                headers: {
                    'ENO_CSRF_TOKEN': data.csrf.value,
                    'SecurityContext': data.securityContext,
                    'Content-Type': 'application/json',
                },
                onComplete: function(response) {
                    data.checkInTicket = response.data[0].dataelements;
                    data.successCallback(data);
                },
                onFailure: function(error) {
                    data.stage = 'FetchCheckInTicket';
                    data.error = 'Error retrieving Check-in Ticket [' + error + ']';
                    data.failureCallback(data);
                }
            });
        },
		uploadFile: function(data){
			if(data.fileRef == undefined){
				// Non file mode, hence just invoke successCallback
				data.successCallback(data);
				return;
			}
            let title = data.fileRef.dataelements.title;
            getBlob(data.getDataUrl, function(blob){
				$.ajax({
                    url: data.checkInTicket.ticketURL,
                    method: "POST",
                    processData: false, // Stop converting data to query string
                    contentType: false,
                    data: (function(){
                        let file = new File([blob], title, { type: blob.type });
						
						let formData = new FormData();
                        formData.append(data.checkInTicket.ticketparamname, data.checkInTicket.ticket);
                        formData.append('file_0', file, file.name);
                        return formData;
                    })()
                }).done(function(receipt){
                    data.receipt = receipt;
                    data.successCallback(data);
                }).fail(function(error){
                    data.stage = 'UploadFile';
                    data.error ='Error Uploading file to FCS [' + error + ']';
                    data.failureCallback(data);
                });
            });
        },
        modifyCreateDocument: function(data){
            let url = data.platformUrl + '/resources/v1/modeler/documents/?$fields=all';
            if(data.fileRef)
                data.fileRef.dataelements.receipt = data.receipt;
            data.API.authenticatedRequest(url, {
                method: 'PUT',
                type: 'json',
                headers:{
                    'ENO_CSRF_TOKEN': data.csrf.value,
                    'SecurityContext': data.securityContext,
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    data: [ data.documentInfo ]
                }),
                onComplete: function(response) {
                    data.result = response.data[0];
                    data.successCallback(data);
                },
                onFailure: function(error) {
                    data.stage = 'ModifyCreateDocument';
                    data.error = 'Error Creating/Updating File [' + error + ']';
                    data.failureCallback(data);
                }
            });
        },
		fetchBookMarksList: function(data){
            var url = data.platformUrl + '/resources/v1/modeler/dsbks/dsbks:Bookmark/search?' +
                '$mask=dsbks:BksMask.Details'; /*Need id and name of bookmarks only*/
            data.API.authenticatedRequest(url, {
                method: "GET",
                type: 'json',
                headers: {
                    'ENO_CSRF_TOKEN': data.csrf.value,
                    'SecurityContext': data.securityContext,
                    'Content-Type': 'application/json',
                },
                onComplete: function (response) {
                    data.result = response;
                    data.successCallback(data);
                },
                onFailure: function (error) {
                    data.stage = 'FetchBookMarksList';
                    data.error = 'Unable to retrieve Bookmarks List from 3DSpace [' + error + ']';
                    data.failureCallback(data);
                }
            });
        },
        fetchBookMarkItems: function(data){
            var url = data.platformUrl + '/resources/v1/modeler/dsbks/dsbks:Bookmark/' + data.bookmarkId +
                '?$mask=dsbks:BksMask.Items';
            data.API.authenticatedRequest(url, {
                method: "GET",
                type: 'json',
                headers: {
                    'ENO_CSRF_TOKEN': data.csrf.value,
                    'SecurityContext': data.securityContext,
                    'Content-Type': 'application/json',
                },
                onComplete: function (response) {
                    data.result = response.member[0].items.member;
                    data.successCallback(data);
                },
                onFailure: function (error) {
                    data.stage = 'FetchBookMarkInfo';
                    data.error = 'Unable to retrieve Bookmark Info from 3DSpace [' + error + ']';
                    data.failureCallback(data);
                }
            });
        },
        attachToBookmark : function(data){
			if(data.bookmark == undefined){
				data.successCallback(data);
                return;
			}
            var url = data.platformUrl + '/resources/v1/modeler/dsbks/dsbks:Bookmark/'+ data.bookmark.id +'/attach';
            data.API.authenticatedRequest(url, {
                method: "POST",
                type: 'json',
                headers: {
                    'ENO_CSRF_TOKEN': data.csrf.value,
                    'SecurityContext': data.securityContext,
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify(data.bookmark.documentData),
                onComplete: function (response) {
                    data.result = response;
                    data.successCallback(data);
                },
                onFailure: function (error) {
                    data.stage = 'AttachToBookmark';
                    data.error = 'Unable to Attach Document to Bookmark in 3DSpace [' + error + ']';
                    data.failureCallback(data);
                }
            });
        },

		/* SWYM Utilities */
		getSwymPublishForm: function() {
			return getAPI('DS/SwymPublishForm/script/publish-form-view', window.top.require);
		},
		fetch3DSwymUrl: function(data){
            if(data.swymUrl != undefined && data.swymUrl.length > 5){
                data.successCallback(data);
                return;
            }
            window.dashboardController.getPlatformServices('getServiceUrl', { 
				serviceName: '3DSwym' 
				}, function(url) {
					if(url == undefined){ // If the URL is not found note error.
						data.stage = 'Fetch3DwymUrl';
						data.error = 'Unable to get 3DSwym url';
						data.failureCallback(data)
					}else{
						data.swymUrl = url;
						data.successCallback(data);
					}
				});
        },
        fetchSwymCSRFToken: function(data){
            var url = data.swymUrl + '/api/index/tk';
            data.API.authenticatedRequest(url, {
                method: 'GET',
                type: 'json',
                onComplete: function(response) {
                    data.csrf = response.result.ServerToken;
                    data.successCallback(data);
                },
                onFailure: function(error) {
                    data.stage = 'FetchSWYMCSRFToken';
                    data.error = 'Unable to retrieve CSRF Token for SWYM [' + error + ']';
                    data.failureCallback(data);
                }
            });
        },
        uploadMediaToSwym: function(data){
			getBlob(data.mediaData.getDataUrl, function(blob){
				var url = data.swymUrl + '/api/media/add';
				$.ajax({
					url: url,
					method: "POST",
					processData: false, // Stop converting data to query string
					contentType: false,
					data: (function(){
						//Create Form Data that hold BLOB file and community Id where the media needs to be posted
						let file = new Blob([ blob ], { type : blob.type });
						
						let formData = new FormData();
						formData.append('userFile', file, data.mediaData.fileName);
						formData.append('published', data.mediaData.published);
						formData.append('community_id', data.mediaData.community_id);
						return formData;
					})(),
					headers: {
						'X-DS-SWYM-CSRFTOKEN': data.csrf
					},
				}).done(function(response){
					data.result = response.result;
					data.successCallback(data);
				}).fail(function(error){
					data.stage = 'UploadMediaToSwym';
					data.error ='Error Uploading file to SWYM [' + error + ']';
					data.failureCallback(data);
				});
			});
        }
    }
})()