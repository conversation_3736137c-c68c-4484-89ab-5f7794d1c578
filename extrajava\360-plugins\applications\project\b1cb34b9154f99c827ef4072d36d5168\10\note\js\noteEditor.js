var NoteEditor = function (widget, options) {
	this.widget = widget;
	this.options = $.extend({}, {
		user: null,
		tinymceUrl: null
	}, options);

	this.currentElt = null;
	this.currentIframeContents = null;
};

var WIDGET_NAME = 'note';

NoteEditor.prototype.openRichtextPopup = function (elt) {
	this.currentElt = elt;
	this.widget.find('.elem-active-editor').removeClass('elem-active-editor');
	this.currentElt.addClass('elem-active-editor');

	if (!tinymce.activeEditor) {
		this.initTinyMCE();
		this.initClickEvents();
		this.currentIframeContents = this.widget.find('.chartboard-tinymce-container .chartboard-tinymce-editor-container iframe').contents();
	}

	/* get value from div and put it in the editor (watch for template class, title and text) */
	if (this.currentElt.find('.editor-text').length > 0) {
		tinymce.activeEditor.setContent(this.currentElt.find('.editor-text')[0].innerHTML);
	} else if (this.currentElt[0]) {
		tinymce.activeEditor.setContent(this.currentElt[0].innerHTML);
	} else {
		tinymce.activeEditor.setContent('');
	}

	//display the editor
	this.showRichtextPopup();
};

NoteEditor.prototype.loadScript = function () {
	$.getScript(this.options.tinymceUrl);
};

NoteEditor.prototype.initTinyMCE = function () {
	tinyMCE.baseURL = this.options.tinymceUrl.split('/tinymce.min.js')[0];
	tinymce.init({
		selector: '.chartboard-popup-tinymce .chartboard-tinymce-editor',
		theme: 'modern',
		plugins: 'textcolor',
		branding: false,
		menubar: false,
		resize: false,
		toolbar1: 'formatselect | bold italic underline strikethrough forecolor backcolor | link | alignleft aligncenter alignright alignjustify  | numlist bullist outdent indent  | removeformat'
	});
};

NoteEditor.prototype.initClickEvents = function () {

	/* Remove already existing buttons - a better solution would be to prevent them from remaining after the popup is closed */
	var existingSaveButton = this.widget.find('.chartboard-tinymce-container .chartboard-tinymce-title .mce-save-button');
	if (existingSaveButton.length > 0) {
		existingSaveButton.siblings('.mce-close-button').remove();
		existingSaveButton.remove();
	}

	var cancelButton = $('<span class="mce-close-button fonticon fonticon-cancel" title="' + this.getMessage("note.editor.cancel") + '"></span>');
	var saveButton = $('<span class="mce-save-button fonticon fonticon-check" title="' + this.getMessage("note.editor.save") + '"></span>');
	var deleteButton = $('<span class="mce-save-button fonticon fonticon-trash" title="' + this.getMessage("note.editor.delete") + '"></span>');

	this.widget.find('.chartboard-tinymce-container .chartboard-tinymce-title')
		.append(cancelButton)
		.append(deleteButton)
		.append(saveButton);

	saveButton.on('click', $.proxy(function (e) {
		this.currentElt.closest('.custom-note').removeClass('isNew');
		this.currentElt.empty();
		var divText = $('<div class="editor-text">' + tinymce.activeEditor.getContent() + '</div>');
		this.currentElt.append(divText);

		/* close the editor */
		this.hideRichtextPopup();
	}, this));

	cancelButton.on('click', $.proxy(function (e) {
		this.hideRichtextPopup();
		/* If the note is a new one, delete it */
		var note = this.currentElt.closest('.custom-note');
		if (note.hasClass('isNew')) {
			// note.remove();
			this.options.noteWidget.gridstack.removeWidget(note, true);
		}
	}, this));

	deleteButton.on('click', $.proxy(function (e) {
		this.hideRichtextPopup();
		/* If the note is a new one, delete it */
		var note = this.currentElt.closest('.custom-note');
		this.options.noteWidget.gridstack.removeWidget(note, true);
	}, this));

	this.widget.find('.chartboard-popup-tinymce').on('click', $.proxy(function (e) {
		if (e.target.getClassList().contains('chartboard-popup-tinymce')) {
			this.hideRichtextPopup();
			/* If the note is a new one, delete it */
			var note = this.currentElt.closest('.custom-note');
			if (note.hasClass('isNew')) {
				// note.remove();
				this.options.noteWidget.gridstack.removeWidget(note, true);
			}
		}
	}, this));

};

NoteEditor.prototype.hideRichtextPopup = function () {
	this.widget.find('.chartboard-popup-tinymce').removeClass('visible');
};

NoteEditor.prototype.showRichtextPopup = function () {
	this.widget.find('.chartboard-popup-tinymce').addClass('visible');
};

NoteEditor.prototype.getMessage = function (code) {
	return mashupI18N.get(WIDGET_NAME, code);
};
