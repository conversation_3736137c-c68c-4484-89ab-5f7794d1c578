<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import parameters="feeds"/>
<render:import parameters="columnsConfig" ignore="true"/>

<search:getFeed var="feed" feeds="${feeds}"/>
<url:url var="currentUrl" keepQueryString="true"/>
<url:url var="baseUrl" keepQueryString="true">
    <url:parameter name="${feed.id}.s" value=""/>
</url:url>

<%--${plma:logObject('sortColumnsConfig', columnsConfig)}--%>

<span class="sortContainer">
	<span class="action-sorting fonticon fonticon-sorting" title="<i18n:message code='hitcustom.sort'/>"></span>
	<span class="sort-dropdown">
		<div class="sortLinkOptions">
			<c:forEach var="group" items="${columnsConfig.columns}">
				<c:forEach var="column" items="${group.column}">
					<c:if test="${not empty column.sortField}">
						<search:getSortUrl var="ascSortUrl" varState="sortState" feeds="${feeds}" expr="${column.sortField}" defaultOrder="ASC"/>
						<search:getSortUrl var="descSortUrl" varState="sortState" feeds="${feeds}" expr="${column.sortField}" defaultOrder="DESC"/>
						<c:choose>
							<c:when test="${column.sortField == 'text_relevance'}">
								<span class="sortOption ${fn:contains(currentUrl, '.s') ? '' : 'activeSort'}"
									  onclick="window.location.href = '${baseUrl}'">
							</c:when>
							<c:otherwise>
								<span class="sortOption ${sortState} ${sortState != ''? 'activeSort' : ''}"
									  title="${column.sortLabel}"
									  data-sorturl="${ascSortUrl}"
									  data-descsorturl="${descSortUrl}"
									  data-sort-active="${sortState}"
									  data-column-name="${column.meta}"
								>
							</c:otherwise>
						</c:choose>
							<%-- type-icon,asc-icon,desc-icon --%>
							<string:split var="icons" string="${column.sortIcon}"/>
							<c:set var="typeIcon" value="${icons[0]}"/>
							<c:set var="ascIcon" value="${fn:length(icons)==3? icons[1]: 'fonticon-expand-up'}"/>
							<c:set var="descIcon" value="${fn:length(icons)==3? icons[2]: 'fonticon-expand-down'}"/>
							<span class="label">
								<span class="fonticon ${typeIcon}"></span>
								<c:choose>
									<c:when test="${not empty column.sortLabel}">
										<span class="sortText"><string:eval string="${column.sortLabel}"/></span>
									</c:when>
									<c:otherwise>
										<span class="sortText"><string:eval string="${column.label}"/></span>
									</c:otherwise>
								</c:choose>
							</span>
							<div class="sortLinkOrder">
								<span class="fonticon ${ascIcon} icon-sort-asc  ${sortState == 'ordered_asc' ? 'selected' : ''}"
									  title="<i18n:message code='hitcustom.sort_asc'/>"></span>
								<span class="fonticon ${descIcon} icon-sort-desc ${sortState == 'ordered_desc' ? 'selected' : ''}"
									  title="<i18n:message code='hitcustom.sort_dsc'/>"></span>
							</div>
						</span>
					</c:if>
				</c:forEach>
			</c:forEach>
		</div>
	</span>
</span>