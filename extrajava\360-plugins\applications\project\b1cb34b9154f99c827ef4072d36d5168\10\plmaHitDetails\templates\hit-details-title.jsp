<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<render:import parameters="entry,widget"/>
<render:import parameters="hitTitle,iconContainer" ignore="true"/>

<%-- retrieve the widget options --%>
<config:getOption name="typeFacetId" var="typeFacetId"/>
<config:getOption var="facetIcon" name="facetIcon" defaultValue="fonticon-legend"/>
<config:getOption var="hitSubTitleDescription" name="hitSubTitleDescription" entry="${entry}"/>

<%-- Title --%>
<c:if test="${not empty hitTitle}">
    <c:if test="${not empty typeFacetId}">
        <search:getFacet var="typeFacet" facetId="${typeFacetId}" entry="${entry}"/>
        <search:getFacetLabel var="facetLabel" facet="${typeFacet}"/>
        <search:forEachCategory root="${typeFacet}" var="typeCategory">
            <plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
            <string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
            <plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
            <string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
            <search:getCategoryLabel var="categoryLabel" category="${typeCategory}"/>
            <c:choose>
                <c:when test="${iconContainer}">
                    <div class="icon-container" style="background-color:${plma:toRGB(categoryColor, '.3')};">
                        <span class="categoryIcon fonticon ${not empty categoryIcon ? categoryIcon : facetIcon}"
                                style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"
                                title="${facetLabel}: ${categoryLabel}">
                        </span>
                    </div>
                </c:when>
                <c:otherwise>
                    <span class="categoryIcon fonticon ${not empty categoryIcon ? categoryIcon : facetIcon}"
                            style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"
                            title="${facetLabel}: ${categoryLabel}">
                    </span>
                </c:otherwise>
            </c:choose>
        </search:forEachCategory>
    </c:if>
    <div class="hitTitle-container">
        <i18n:message var="titleLabel" code="plma.hitdetail.hit.title.label"/>
        <span class="hitTitle" title="${titleLabel}: ${fn:escapeXml(hitTitle)}">${hitTitle}</span>
        <c:if test="${not empty hitSubTitleDescription}">
            <span class="hitSubTitleDescription">${hitSubTitleDescription}</span>
        </c:if>
    </div>
</c:if>