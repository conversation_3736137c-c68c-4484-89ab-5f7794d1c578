@import "../../plmaResources/css/polyfills.less";

.mashup .plmaCharts {

	&.center-title{
		.headerChartContainer{
			align-self:center;
		}
	}

    &.no-border{
        .widgetHeader{
            border:none;
        }

        .widgetContent{
            border:none;
        }
    }

	.headerChartContainer{
		.form-filter-chart{
			display: inline-block;
		}
		.input-filter-chart{
			border: 1px solid #D1D4D4;
			border-radius: 4px;
			background-color: #fff;
			background-image: none;
			box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
			color: #3D3D3D;
			font-size: 14px;
			margin-right: 20px;
			margin-top: 5px;
			margin-bottom: 5px;
			padding-left: 5px;
		}
		.fonticon-search{
			margin-right: 10px;
			cursor: pointer;
			&:hover{
				color: #3d3d3d;
			}
		}
		.form-filter-chart{
		}
		.fonticon-filter{
			display: none;
			float: right;
			.align-self(center);
			margin-right: 10px;
			.filter-popup{
				min-height: initial;
				min-width: initial;
				.appspopup-wrapper{
					padding: 5px;
					font-size: 11px;
					line-height: 14px;
					.refine{
						background-color: #d5e8f2;
						border-radius: 3px;
						color: #77797c;
						margin-bottom: 5px;
						margin-top: 5px;
						padding: 3.5px;
					}
				}
			}
		}
	}

	.widgetContent {
		position: relative;
	}
	
	&.selectedChartFromHome{
		.widgetHeader{
			border-color: @clink;
			border-bottom: 0;
			outline: 0;
			box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
			-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
			-moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
		}
		.widgetContent{
			border-color: @clink;
			outline: 0;
			box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
			-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
			-moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
		}
		
	}
	
	.widgetHeaderButton.fonticon-resize-full, .widgetHeaderButton.fonticon-filter{
		font-size: 100%;
		margin-right: 0;
	}
	
	.chart-paginate {
			font-size: @m-font;
			z-index: 10;
			position: absolute;
			margin: @line-height /2 0;
			width: 100%;
			.display-flex();
			.justify-content(flex-end);
			
			.chart-btn {
				cursor: pointer;
				
				&:hover {
					color: @ctext-bold;
				}
			}
			.current-page, .max-page {
				font-weight: bold;
			}
			
			&.hidden{
				display: none;
			}
			.see-more {
				cursor: pointer;
				&.fonticon {
					font-size: 25px;
					font-weight: bold;
				}
				&:hover {
					color: @ctext-bold;
				}
			}
	}
	
}



.plmaChart-suggest-container{
	width: 175px !important;
}

.mashup.mashup-style .searchWidget {
	&.full-size-active {
		position: fixed;
	    width: 100%;
	    height: 100%;
	    top: 0;
	    left: 0;
	    z-index: 20000;
	    .display-flex();
	    flex-direction: column;
	    padding: 50px;
	    background-color: rgba(0, 0, 0, 0.3);
	    .widgetHeader {
	    	flex-basis: 20px;
	    }
	    .widgetContent {
	    	flex: 1;
	    	padding: 0;
	    	.chart-wrapper{
	    		height: 100% !important;
	    		.highChartsSVGWrapper {
	    			height: 100% !important;
	    		}
	    	}
	    }
	}
}

.mashup .fullScreenChart{
	height:90%;
	width:90%;
	max-height:none;
	max-width:none;
	overflow-y: hidden;
	
	.chart-inner{
		position:relative;
	}
	.searchWidget{
		padding:0;
		height:100%;
		width:100%
	}
	.plmalightbox-contentwrapper{
		height:100%;
		width:100%
	}
	.chart-wrapper{
		height:100% !important;
	}
	.highChartsSVGWrapper{
		height:100% !important;
	}
	.highcharts-container{
		height:100% !important;
		width: 100% !important;
	}
	svg{
		height:100%;
		width: 100% !important;
	}
}


