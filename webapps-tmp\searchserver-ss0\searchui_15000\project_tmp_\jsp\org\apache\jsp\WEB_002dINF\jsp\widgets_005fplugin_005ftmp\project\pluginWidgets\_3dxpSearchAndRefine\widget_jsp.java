/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:30 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets._3dxpSearchAndRefine;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class widget_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_0;

static {
  _jspx_fnmap_0= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:split", org.apache.taglibs.standard.functions.Functions.class, "split", new Class[] {java.lang.String.class, java.lang.String.class});
}

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(11);
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/tld/experience.tld", Long.valueOf(1751968403638L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/widget.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/list.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fexperience_005fis3DXP_0026_005fvar_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fextraCss_005fdisableStyles;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fexperience_005fgetFeeds_0026_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fexperience_005fgetActiveRefines_0026_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fexperience_005fgetFacetsForTagger_0026_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fexperience_005fget6WTags_0026_005fwhy_005fwho_005fwhere_005fwhen_005fwhat_005fmappingFile_005fhow_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fexperience_005fis3DXP_0026_005fvar_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fextraCss_005fdisableStyles = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fexperience_005fgetFeeds_0026_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fexperience_005fgetActiveRefines_0026_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fexperience_005fgetFacetsForTagger_0026_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fexperience_005fget6WTags_0026_005fwhy_005fwho_005fwhere_005fwhen_005fwhat_005fmappingFile_005fhow_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fexperience_005fis3DXP_0026_005fvar_005fnobody.release();
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fextraCss_005fdisableStyles.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
    _005fjspx_005ftagPool_005fexperience_005fgetFeeds_0026_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fexperience_005fgetActiveRefines_0026_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fexperience_005fgetFacetsForTagger_0026_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fexperience_005fget6WTags_0026_005fwhy_005fwho_005fwhere_005fwhen_005fwhat_005fmappingFile_005fhow_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f4(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f5(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f6(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f7(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f8(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f9(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f10(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f11(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptions_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptions_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptions_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptions_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptions_005f4(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptions_005f5(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptions_005f6(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f12(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f13(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_experience_005fis3DXP_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_widget_005fwidget_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(11,0) name = varWidget type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarWidget("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(11,0) name = varFeeds type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarFeeds("feeds");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(13,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setName("searchBehavior");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(13,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setVar("searchBehavior");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(13,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setDefaultValue("open page");
      int[] _jspx_push_body_count_config_005fgetOption_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f0 = _jspx_th_config_005fgetOption_005f0.doStartTag();
        if (_jspx_th_config_005fgetOption_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f0);
      _jspx_th_config_005fgetOption_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(14,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setName("keepParameters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(14,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setVar("keepParameters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(14,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f1 = _jspx_th_config_005fgetOption_005f1.doStartTag();
        if (_jspx_th_config_005fgetOption_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f1);
      _jspx_th_config_005fgetOption_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(15,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setName("searchPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(15,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setVar("searchPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(15,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f2 = _jspx_th_config_005fgetOption_005f2.doStartTag();
        if (_jspx_th_config_005fgetOption_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f2);
      _jspx_th_config_005fgetOption_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f3_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(16,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setName("refinePage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(16,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setVar("refinePage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(16,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f3 = _jspx_th_config_005fgetOption_005f3.doStartTag();
        if (_jspx_th_config_005fgetOption_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f3);
      _jspx_th_config_005fgetOption_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f4 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f4_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f4.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(17,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setName("searchParameter");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(17,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setVar("searchParameter");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(17,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setDefaultValue("q");
      int[] _jspx_push_body_count_config_005fgetOption_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f4 = _jspx_th_config_005fgetOption_005f4.doStartTag();
        if (_jspx_th_config_005fgetOption_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f4);
      _jspx_th_config_005fgetOption_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f4, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f5(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f5 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f5_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f5.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f5.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(18,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setName("customSearchBehavior");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(18,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setVar("customSearchBehavior");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(18,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setDefaultValue("function() {}");
      int[] _jspx_push_body_count_config_005fgetOption_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f5 = _jspx_th_config_005fgetOption_005f5.doStartTag();
        if (_jspx_th_config_005fgetOption_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f5);
      _jspx_th_config_005fgetOption_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f5, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f6(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f6 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f6_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f6.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f6.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(19,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setName("ignoreFactes");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(19,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setVar("ignoreFactes");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(19,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f6 = _jspx_th_config_005fgetOption_005f6.doStartTag();
        if (_jspx_th_config_005fgetOption_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f6);
      _jspx_th_config_005fgetOption_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f6, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f7(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f7 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f7_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f7.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f7.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(21,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setName("resetBehavior");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(21,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setVar("resetBehavior");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(21,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setDefaultValue("open page");
      int[] _jspx_push_body_count_config_005fgetOption_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f7 = _jspx_th_config_005fgetOption_005f7.doStartTag();
        if (_jspx_th_config_005fgetOption_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f7);
      _jspx_th_config_005fgetOption_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f7, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f8(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f8 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f8_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f8.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f8.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(22,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setName("resetSearchPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(22,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setVar("resetSearchPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(22,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f8 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f8 = _jspx_th_config_005fgetOption_005f8.doStartTag();
        if (_jspx_th_config_005fgetOption_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f8[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f8.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f8.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f8);
      _jspx_th_config_005fgetOption_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f8, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f9(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f9 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f9_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f9.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f9.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(23,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setName("resetPageParameters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(23,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setVar("resetPageParameters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(23,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setDefaultValue("q");
      int[] _jspx_push_body_count_config_005fgetOption_005f9 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f9 = _jspx_th_config_005fgetOption_005f9.doStartTag();
        if (_jspx_th_config_005fgetOption_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f9[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f9.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f9.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f9);
      _jspx_th_config_005fgetOption_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f9, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f10(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f10 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f10_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f10.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f10.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(24,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setName("customResetBehavior");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(24,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setVar("customResetBehavior");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(24,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setDefaultValue("function() {}");
      int[] _jspx_push_body_count_config_005fgetOption_005f10 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f10 = _jspx_th_config_005fgetOption_005f10.doStartTag();
        if (_jspx_th_config_005fgetOption_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f10[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f10.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f10.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f10);
      _jspx_th_config_005fgetOption_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f10, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f11(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f11 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f11_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f11.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f11.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(26,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setVar("keepAllRefines");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(26,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setName("keepAllRefines");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(26,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f11 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f11 = _jspx_th_config_005fgetOption_005f11.doStartTag();
        if (_jspx_th_config_005fgetOption_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f11[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f11.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f11.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f11);
      _jspx_th_config_005fgetOption_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f11, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptions_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptions
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag _jspx_th_config_005fgetOptions_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag) _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag.class);
    boolean _jspx_th_config_005fgetOptions_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOptions_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptions_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(27,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f0.setVar("mappingFile");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(27,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f0.setName("mappingFile");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(27,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f0.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOptions_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptions_005f0 = _jspx_th_config_005fgetOptions_005f0.doStartTag();
        if (_jspx_th_config_005fgetOptions_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptions_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptions_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptions_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOptions_005f0);
      _jspx_th_config_005fgetOptions_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptions_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptions_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptions_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptions
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag _jspx_th_config_005fgetOptions_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag) _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag.class);
    boolean _jspx_th_config_005fgetOptions_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOptions_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptions_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(28,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f1.setVar("who");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(28,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f1.setName("who");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(28,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f1.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOptions_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptions_005f1 = _jspx_th_config_005fgetOptions_005f1.doStartTag();
        if (_jspx_th_config_005fgetOptions_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptions_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptions_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptions_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOptions_005f1);
      _jspx_th_config_005fgetOptions_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptions_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptions_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptions_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptions
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag _jspx_th_config_005fgetOptions_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag) _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag.class);
    boolean _jspx_th_config_005fgetOptions_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOptions_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptions_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(29,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f2.setVar("when");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(29,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f2.setName("when");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(29,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f2.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOptions_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptions_005f2 = _jspx_th_config_005fgetOptions_005f2.doStartTag();
        if (_jspx_th_config_005fgetOptions_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptions_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptions_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptions_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOptions_005f2);
      _jspx_th_config_005fgetOptions_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptions_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptions_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptions_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptions
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag _jspx_th_config_005fgetOptions_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag) _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag.class);
    boolean _jspx_th_config_005fgetOptions_005f3_reused = false;
    try {
      _jspx_th_config_005fgetOptions_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptions_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(30,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f3.setVar("what");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(30,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f3.setName("what");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(30,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f3.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOptions_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptions_005f3 = _jspx_th_config_005fgetOptions_005f3.doStartTag();
        if (_jspx_th_config_005fgetOptions_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptions_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptions_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptions_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOptions_005f3);
      _jspx_th_config_005fgetOptions_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptions_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptions_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptions_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptions
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag _jspx_th_config_005fgetOptions_005f4 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag) _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag.class);
    boolean _jspx_th_config_005fgetOptions_005f4_reused = false;
    try {
      _jspx_th_config_005fgetOptions_005f4.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptions_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(31,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f4.setVar("where");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(31,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f4.setName("where");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(31,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f4.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOptions_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptions_005f4 = _jspx_th_config_005fgetOptions_005f4.doStartTag();
        if (_jspx_th_config_005fgetOptions_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptions_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptions_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptions_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOptions_005f4);
      _jspx_th_config_005fgetOptions_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptions_005f4, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptions_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptions_005f5(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptions
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag _jspx_th_config_005fgetOptions_005f5 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag) _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag.class);
    boolean _jspx_th_config_005fgetOptions_005f5_reused = false;
    try {
      _jspx_th_config_005fgetOptions_005f5.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptions_005f5.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(32,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f5.setVar("why");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(32,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f5.setName("why");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(32,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f5.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOptions_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptions_005f5 = _jspx_th_config_005fgetOptions_005f5.doStartTag();
        if (_jspx_th_config_005fgetOptions_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptions_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptions_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptions_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOptions_005f5);
      _jspx_th_config_005fgetOptions_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptions_005f5, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptions_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptions_005f6(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptions
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag _jspx_th_config_005fgetOptions_005f6 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag) _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsTag.class);
    boolean _jspx_th_config_005fgetOptions_005f6_reused = false;
    try {
      _jspx_th_config_005fgetOptions_005f6.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptions_005f6.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(33,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f6.setVar("how");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(33,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f6.setName("how");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(33,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptions_005f6.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOptions_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptions_005f6 = _jspx_th_config_005fgetOptions_005f6.doStartTag();
        if (_jspx_th_config_005fgetOptions_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptions_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptions_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptions_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptions_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOptions_005f6);
      _jspx_th_config_005fgetOptions_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptions_005f6, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptions_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f12(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f12 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f12_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f12.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f12.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(35,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setVar("mappingPolicy");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(35,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setName("mappingPolicy");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(35,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setDefaultValue("widget");
      int[] _jspx_push_body_count_config_005fgetOption_005f12 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f12 = _jspx_th_config_005fgetOption_005f12.doStartTag();
        if (_jspx_th_config_005fgetOption_005f12.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f12[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f12.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f12.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f12);
      _jspx_th_config_005fgetOption_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f12, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f13(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f13 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f13_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f13.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f13.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(36,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setName("addExtraMappings");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(36,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setVar("addExtraMappings");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(36,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setDefaultValue("function() {}");
      int[] _jspx_push_body_count_config_005fgetOption_005f13 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f13 = _jspx_th_config_005fgetOption_005f13.doStartTag();
        if (_jspx_th_config_005fgetOption_005f13.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f13[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f13.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f13.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f13);
      _jspx_th_config_005fgetOption_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f13, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_experience_005fis3DXP_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  experience:is3DXP
    com.exalead.cv360.searchui.view.jspapi.experience.IsIn3DXPTag _jspx_th_experience_005fis3DXP_005f0 = (com.exalead.cv360.searchui.view.jspapi.experience.IsIn3DXPTag) _005fjspx_005ftagPool_005fexperience_005fis3DXP_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.experience.IsIn3DXPTag.class);
    boolean _jspx_th_experience_005fis3DXP_005f0_reused = false;
    try {
      _jspx_th_experience_005fis3DXP_005f0.setPageContext(_jspx_page_context);
      _jspx_th_experience_005fis3DXP_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(38,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fis3DXP_005f0.setVar("isIn3DXP");
      int[] _jspx_push_body_count_experience_005fis3DXP_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_experience_005fis3DXP_005f0 = _jspx_th_experience_005fis3DXP_005f0.doStartTag();
        if (_jspx_th_experience_005fis3DXP_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_experience_005fis3DXP_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_experience_005fis3DXP_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_experience_005fis3DXP_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fexperience_005fis3DXP_0026_005fvar_005fnobody.reuse(_jspx_th_experience_005fis3DXP_005f0);
      _jspx_th_experience_005fis3DXP_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_experience_005fis3DXP_005f0, _jsp_getInstanceManager(), _jspx_th_experience_005fis3DXP_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fwidget_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:widget
    com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag _jspx_th_widget_005fwidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag) _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fextraCss_005fdisableStyles.get(com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag.class);
    boolean _jspx_th_widget_005fwidget_005f0_reused = false;
    try {
      _jspx_th_widget_005fwidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fwidget_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(40,0) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setExtraCss("experience experience-search-and-refine");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(40,0) name = disableStyles type = boolean reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setDisableStyles(true);
      int[] _jspx_push_body_count_widget_005fwidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fwidget_005f0 = _jspx_th_widget_005fwidget_005f0.doStartTag();
        if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fwidget_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fwidget_005f0);
          }
          do {
            out.write('\r');
            out.write('\n');
            if (_jspx_meth_c_005fif_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_widget_005fwidget_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fwidget_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fwidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fwidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fwidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fwidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fextraCss_005fdisableStyles.reuse(_jspx_th_widget_005fwidget_005f0);
      _jspx_th_widget_005fwidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fwidget_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fwidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(41,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isIn3DXP}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_render_005frenderScript_005f0(_jspx_th_c_005fif_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_render_005frenderScript_005f1(_jspx_th_c_005fif_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f0_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(42,4) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f0.setPosition("BEFORE");
      int[] _jspx_push_body_count_render_005frenderScript_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f0 = _jspx_th_render_005frenderScript_005f0.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f0);
          }
          do {
            out.write("\r\n");
            out.write("        $('body').addClass('mashup-experience');\r\n");
            out.write("    ");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f0);
      _jspx_th_render_005frenderScript_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f1 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f1_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f1.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(46,4) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f1.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f1 = _jspx_th_render_005frenderScript_005f1.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f1[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f1);
          }
          do {
            out.write("\r\n");
            out.write("        var options = {\r\n");
            out.write("            context: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("            icon: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("/resources/widgets/3dxpSearchAndRefine/img/exalead.png',\r\n");
            out.write("            search: {\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fchoose_005f0(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write("\r\n");
            out.write("            },\r\n");
            out.write("            reset: {\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fchoose_005f1(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write("\r\n");
            out.write("            },\r\n");
            out.write("            refines: {\r\n");
            out.write("                refinepage: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePage}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("'\r\n");
            out.write("            },\r\n");
            out.write("            feeds: ");
            if (_jspx_meth_experience_005fgetFeeds_005f0(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write(",\r\n");
            out.write("            activeRefines: ");
            if (_jspx_meth_experience_005fgetActiveRefines_005f0(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write(",\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fchoose_005f2(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write("\r\n");
            out.write("        	hooks: ");
            if (_jspx_meth_config_005fgetOption_005f14(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write(",\r\n");
            out.write("        	ignoreFactes: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ignoreFactes}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("'\r\n");
            out.write("        };\r\n");
            out.write("        options.ignoreFactesList = [];\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fif_005f1(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write("\r\n");
            out.write("        options.tags.keepAllRefines = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${keepAllRefines}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(";\r\n");
            out.write("        if (window.dashboardController) {\r\n");
            out.write("            MessageHandler.afterStop.done(function (stopped){\r\n");
            out.write("                delete window.dashboardController;\r\n");
            out.write("                window.dashboardController = new MessageHandler(options);\r\n");
            out.write("                window.dashboardController.start();\r\n");
            out.write("            });\r\n");
            out.write("            window.dashboardController.stop();\r\n");
            out.write("        }else{\r\n");
            out.write("                window.dashboardController = new MessageHandler(options);\r\n");
            out.write("                window.dashboardController.start();\r\n");
            out.write("        }\r\n");
            out.write("\r\n");
            out.write("    ");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f1[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f1);
      _jspx_th_render_005frenderScript_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f1, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
            return true;
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(52,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${searchBehavior == 'open page'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                page: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${searchPage}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("                parameter: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${searchParameter}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("                keepParameters: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${keepParameters}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("                resetParameters: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${resetPageParameters}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("'\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                callback: function(options) { \r\n");
          out.write("                    (");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customSearchBehavior}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(").call(null, options);\r\n");
          out.write("                }\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f1 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f1_reused = false;
    try {
      _jspx_th_c_005fchoose_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      int _jspx_eval_c_005fchoose_005f1 = _jspx_th_c_005fchoose_005f1.doStartTag();
      if (_jspx_eval_c_005fchoose_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fwhen_005f1(_jspx_th_c_005fchoose_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
            return true;
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fotherwise_005f1(_jspx_th_c_005fchoose_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f1);
      _jspx_th_c_005fchoose_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f1 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f1_reused = false;
    try {
      _jspx_th_c_005fwhen_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(67,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${resetBehavior == 'open page'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f1 = _jspx_th_c_005fwhen_005f1.doStartTag();
      if (_jspx_eval_c_005fwhen_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                page: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${resetSearchPage}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("                resetParameters: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${resetPageParameters}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("'\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f1);
      _jspx_th_c_005fwhen_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f1 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f1_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      int _jspx_eval_c_005fotherwise_005f1 = _jspx_th_c_005fotherwise_005f1.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                callback: function(options) { \r\n");
          out.write("                    (");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customResetBehavior}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(").call(null, options);\r\n");
          out.write("                }\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f1);
      _jspx_th_c_005fotherwise_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_experience_005fgetFeeds_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  experience:getFeeds
    com.exalead.cv360.searchui.view.jspapi.experience.GetFeedsTag _jspx_th_experience_005fgetFeeds_005f0 = (com.exalead.cv360.searchui.view.jspapi.experience.GetFeedsTag) _005fjspx_005ftagPool_005fexperience_005fgetFeeds_0026_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.experience.GetFeedsTag.class);
    boolean _jspx_th_experience_005fgetFeeds_005f0_reused = false;
    try {
      _jspx_th_experience_005fgetFeeds_005f0.setPageContext(_jspx_page_context);
      _jspx_th_experience_005fgetFeeds_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(81,19) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fgetFeeds_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_experience_005fgetFeeds_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_experience_005fgetFeeds_005f0 = _jspx_th_experience_005fgetFeeds_005f0.doStartTag();
        if (_jspx_th_experience_005fgetFeeds_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_experience_005fgetFeeds_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_experience_005fgetFeeds_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_experience_005fgetFeeds_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fexperience_005fgetFeeds_0026_005ffeeds_005fnobody.reuse(_jspx_th_experience_005fgetFeeds_005f0);
      _jspx_th_experience_005fgetFeeds_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_experience_005fgetFeeds_005f0, _jsp_getInstanceManager(), _jspx_th_experience_005fgetFeeds_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_experience_005fgetActiveRefines_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  experience:getActiveRefines
    com.exalead.cv360.searchui.view.jspapi.experience.GetActiveRefinesTag _jspx_th_experience_005fgetActiveRefines_005f0 = (com.exalead.cv360.searchui.view.jspapi.experience.GetActiveRefinesTag) _005fjspx_005ftagPool_005fexperience_005fgetActiveRefines_0026_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.experience.GetActiveRefinesTag.class);
    boolean _jspx_th_experience_005fgetActiveRefines_005f0_reused = false;
    try {
      _jspx_th_experience_005fgetActiveRefines_005f0.setPageContext(_jspx_page_context);
      _jspx_th_experience_005fgetActiveRefines_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(82,27) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fgetActiveRefines_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_experience_005fgetActiveRefines_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_experience_005fgetActiveRefines_005f0 = _jspx_th_experience_005fgetActiveRefines_005f0.doStartTag();
        if (_jspx_th_experience_005fgetActiveRefines_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_experience_005fgetActiveRefines_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_experience_005fgetActiveRefines_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_experience_005fgetActiveRefines_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fexperience_005fgetActiveRefines_0026_005ffeeds_005fnobody.reuse(_jspx_th_experience_005fgetActiveRefines_005f0);
      _jspx_th_experience_005fgetActiveRefines_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_experience_005fgetActiveRefines_005f0, _jsp_getInstanceManager(), _jspx_th_experience_005fgetActiveRefines_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f2 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f2_reused = false;
    try {
      _jspx_th_c_005fchoose_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      int _jspx_eval_c_005fchoose_005f2 = _jspx_th_c_005fchoose_005f2.doStartTag();
      if (_jspx_eval_c_005fchoose_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fwhen_005f2(_jspx_th_c_005fchoose_005f2, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
            return true;
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fotherwise_005f2(_jspx_th_c_005fchoose_005f2, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f2);
      _jspx_th_c_005fchoose_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f2 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f2_reused = false;
    try {
      _jspx_th_c_005fwhen_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(84,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mappingPolicy == 'tag controller'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f2 = _jspx_th_c_005fwhen_005f2.doStartTag();
      if (_jspx_eval_c_005fwhen_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                facets: ");
          if (_jspx_meth_experience_005fgetFacetsForTagger_005f0(_jspx_th_c_005fwhen_005f2, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
            return true;
          out.write(",\r\n");
          out.write("	            addExtraMappings: function(options) {\r\n");
          out.write("	                return (");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${addExtraMappings}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(").call(null, options);\r\n");
          out.write("	            },\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f2);
      _jspx_th_c_005fwhen_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_experience_005fgetFacetsForTagger_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  experience:getFacetsForTagger
    com.exalead.cv360.searchui.view.jspapi.experience.GetFacetsForTaggerTag _jspx_th_experience_005fgetFacetsForTagger_005f0 = (com.exalead.cv360.searchui.view.jspapi.experience.GetFacetsForTaggerTag) _005fjspx_005ftagPool_005fexperience_005fgetFacetsForTagger_0026_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.experience.GetFacetsForTaggerTag.class);
    boolean _jspx_th_experience_005fgetFacetsForTagger_005f0_reused = false;
    try {
      _jspx_th_experience_005fgetFacetsForTagger_005f0.setPageContext(_jspx_page_context);
      _jspx_th_experience_005fgetFacetsForTagger_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(85,24) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fgetFacetsForTagger_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_experience_005fgetFacetsForTagger_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_experience_005fgetFacetsForTagger_005f0 = _jspx_th_experience_005fgetFacetsForTagger_005f0.doStartTag();
        if (_jspx_th_experience_005fgetFacetsForTagger_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_experience_005fgetFacetsForTagger_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_experience_005fgetFacetsForTagger_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_experience_005fgetFacetsForTagger_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fexperience_005fgetFacetsForTagger_0026_005ffeeds_005fnobody.reuse(_jspx_th_experience_005fgetFacetsForTagger_005f0);
      _jspx_th_experience_005fgetFacetsForTagger_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_experience_005fgetFacetsForTagger_005f0, _jsp_getInstanceManager(), _jspx_th_experience_005fgetFacetsForTagger_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f2 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f2_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f2);
      int _jspx_eval_c_005fotherwise_005f2 = _jspx_th_c_005fotherwise_005f2.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                tags: ");
          if (_jspx_meth_experience_005fget6WTags_005f0(_jspx_th_c_005fotherwise_005f2, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
            return true;
          out.write(",\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f2);
      _jspx_th_c_005fotherwise_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_experience_005fget6WTags_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  experience:get6WTags
    com.exalead.cv360.searchui.view.jspapi.experience.JsonTag _jspx_th_experience_005fget6WTags_005f0 = (com.exalead.cv360.searchui.view.jspapi.experience.JsonTag) _005fjspx_005ftagPool_005fexperience_005fget6WTags_0026_005fwhy_005fwho_005fwhere_005fwhen_005fwhat_005fmappingFile_005fhow_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.experience.JsonTag.class);
    boolean _jspx_th_experience_005fget6WTags_005f0_reused = false;
    try {
      _jspx_th_experience_005fget6WTags_005f0.setPageContext(_jspx_page_context);
      _jspx_th_experience_005fget6WTags_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(91,22) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fget6WTags_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(91,22) name = mappingFile type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fget6WTags_005f0.setMappingFile((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mappingFile}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(91,22) name = who type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fget6WTags_005f0.setWho((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${who}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(91,22) name = when type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fget6WTags_005f0.setWhen((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${when}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(91,22) name = what type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fget6WTags_005f0.setWhat((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${what}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(91,22) name = where type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fget6WTags_005f0.setWhere((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${where}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(91,22) name = why type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fget6WTags_005f0.setWhy((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${why}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(91,22) name = how type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_experience_005fget6WTags_005f0.setHow((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${how}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_experience_005fget6WTags_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_experience_005fget6WTags_005f0 = _jspx_th_experience_005fget6WTags_005f0.doStartTag();
        if (_jspx_th_experience_005fget6WTags_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_experience_005fget6WTags_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_experience_005fget6WTags_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_experience_005fget6WTags_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fexperience_005fget6WTags_0026_005fwhy_005fwho_005fwhere_005fwhen_005fwhat_005fmappingFile_005fhow_005ffeeds_005fnobody.reuse(_jspx_th_experience_005fget6WTags_005f0);
      _jspx_th_experience_005fget6WTags_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_experience_005fget6WTags_005f0, _jsp_getInstanceManager(), _jspx_th_experience_005fget6WTags_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f14 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f14_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f14.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f14.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(95,16) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setName("hooks");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(95,16) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setDefaultValue("{}");
      int[] _jspx_push_body_count_config_005fgetOption_005f14 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f14 = _jspx_th_config_005fgetOption_005f14.doStartTag();
        if (_jspx_th_config_005fgetOption_005f14.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f14[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f14.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f14.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f14);
      _jspx_th_config_005fgetOption_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f14, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(99,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ignoreFactes != 'false'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fforEach_005f0(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f0_reused = false;
    try {
      _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(100,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setVar("ignoreFacte");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(100,12) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/widget.jsp(100,12) '${fn:split(ignoreFactes, ',')}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_0),"${fn:split(ignoreFactes, ',')}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
        if (_jspx_eval_c_005fforEach_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                    options.ignoreFactesList.push('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${ignoreFacte}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("');\r\n");
            out.write("            ");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
      _jspx_th_c_005fforEach_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f0_reused);
    }
    return false;
  }
}
