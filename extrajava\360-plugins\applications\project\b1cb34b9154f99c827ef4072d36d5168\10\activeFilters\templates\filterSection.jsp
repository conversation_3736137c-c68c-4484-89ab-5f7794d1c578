<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import parameters="filterType"/>
<render:import parameters="feed,feeds,filters" ignore="true"/>

<%-- If only one feed is provided, fake a 'feeds' variable --%>
<c:if test="${empty feeds}">
	<map:new var="feeds"/>
	<map:put key="${feed.id}" value="${feed}" map="${feeds}"/>
</c:if>

<config:getOption var="display2D" name="display2D" defaultValue="false"/>
<config:getOption var="hideInactive" name="hideInactive" defaultValue="false"/>
<config:getOption var="searchParameter" name="searchParameter" defaultValue=""/>
<config:getOption var="separateFeeds" name="separateFeeds"/>
<config:getOption name="displaySavedFilter" var="displaySavedFilter" defaultValue="false"/>
<config:getOptionsComposite var="customParams" name="customParams" mapIndex="true"/>


<request:getParameterValue name="pageId" var="pageId" defaultValue=""/>
<search:getPageName var="pageName"/>

<c:if test="${pageId == undefined || pageId == ''}">
	<search:getPageName var="pageId"/>
</c:if>

<i18n:message var="cancelTooltip" code="widgets.activeFilters.tooltip.cancel" text="Remove this filter"/>
<i18n:message var="inactiveTooltip" code="widgets.activeFilters.tooltip.inactive"
			  text="This filter has no effect on the current page"/>

<c:choose>
	<c:when test="${filterType == 'REFINED' || filterType == 'EXCLUDED'}">
		<%-- List of facet refines, or of facet exclusions --%>
		<search:forEachFacet var="facet" feeds="${feeds}">
			<search:getFacetType var="facetType" facet="${facet}"/>
			<c:if test="${display2D == 'true' || (facetType != 'H2D' && facetType != 'MULTI')}">
				<search:forEachCategory root="${facet}" var="category" varStatus="catStatus" showState="${filterType}" iterationMode="ALL">
					<search:getCategoryUrl var="categoryUrl" category="${category}" feeds="${feeds}"/>
					<string:escape var="catIdJS" value="${category.id}" escapeType="JAVASCRIPT"/>
					<string:escape var="catIdJS" value="${catIdJS}" escapeType="HTML"/>
					<string:escape var="catIdHTML" value="${category.id}" escapeType="HTML"/>
					<c:set var="catPosition" value="mid"/>
					<c:if test="${catStatus.isFirst()}"><c:set var="catPosition" value="first"/></c:if>
					<c:if test="${catStatus.isLast()}"><c:set var="catPosition" value="last"/></c:if>
					<c:if test="${catStatus.isFirst() && catStatus.isLast()}"><c:set var="catPosition" value="none"/></c:if>
					<a class="activeFilter ${filterType == 'REFINED'?'selected':'excluded'}Filter catgroup catgroup-${catPosition}"
					   data-facet="${catIdHTML}"
					   data-type="${filterType == 'REFINED'?'r':'cr'}"
					    <c:choose>
							<c:when test="${separateFeeds == 'true'}">
								href="${categoryUrl}"
							</c:when>
							<c:otherwise>
								onclick="ActiveFilters.utils.deleteRefine('${catIdJS}')"
							</c:otherwise>
						</c:choose>
                       title="${cancelTooltip}">
						<span class="facetName">
							<plma:getFacetLabel facet="${facet}"/>&nbsp;
						</span>
						<span class="categoryName">
							<plma:getCategoryLabel category="${category}" feeds="${feeds}"/>&nbsp;
						</span>
						<span class="fonticon fonticon-cancel">
						</span>
					</a>
					<c:if test="${filters != null}">
						<list:add list="${filters}" value="${category.id}"/>
					</c:if>
				</search:forEachCategory>
			</c:if>
		</search:forEachFacet>
	</c:when>

	<c:when test="${filterType == 'ZAPPED'}">
		<c:if test="${displaySavedFilter == 'true'}">
			<url:url var="currentUrl" keepQueryString="true"/>
			<c:if test="${fn:length(fn:split(currentUrl, '?')) > 1}">
				<plma:getZapRefineFromSavedFilters var="zapped" pageId="${pageId}" pageName="${pageName}"
												   query="${fn:split(currentUrl, '?')[1]}"/>
				<c:forEach var="zap" items="${zapped}">
					<search:getFacet var="facet" facetId="${zap.facetId}" feeds="${feeds}"/>
					<search:getCategory var="category" categoryId="${zap.categoryId}" facet="${facet}"/>
					<string:escape var="catIdJS" value="${category.id}" escapeType="JAVASCRIPT"/>
					<string:escape var="catIdJS" value="${catIdJS}" escapeType="HTML"/>
                    <string:escape var="catIdHTML" value="${category.id}" escapeType="HTML"/>
					<a class="activeFilter selectedFilter"
					   data-facet="${catIdHTML}"
					   data-type="r"
					   onclick="ActiveFilters.utils.deleteRefine('${catIdJS}')"
					   title="${cancelTooltip}">
						<span class="facetName">
							<c:choose>
								<c:when test="${not empty facet}">
									<plma:getFacetLabel facet="${facet}"/>&nbsp;
								</c:when>
								<c:otherwise>
									<plma:getFacetLabel facetId="${zap.facetId}" feeds="${feeds}"/>&nbsp;
								</c:otherwise>
							</c:choose>
						</span>
						<span class="categoryName">
							<c:choose>
								<c:when test="${not empty category}">
									<plma:getCategoryLabel category="${category}" feeds="${feeds}"/>&nbsp;
								</c:when>
								<c:otherwise>
									<plma:getCategoryLabel categoryId="${zap.categoryId}"/>&nbsp;
								</c:otherwise>
							</c:choose>
						</span>
						<span class="fonticon fonticon-cancel">
						</span>
					</a>
					<a class="activeFilter zapFilter"
					   data-facet="${catIdHTML}"
					   data-type="zr"
					   data-rtype="r"
					   onclick="ActiveFilters.utils.deleteZapRefine('${catIdJS}')"
					   title="${cancelTooltip}">
						<span class="facetName">
							<c:choose>
								<c:when test="${not empty facet}">
									<plma:getFacetLabel facet="${facet}"/>&nbsp;
								</c:when>
								<c:otherwise>
									<plma:getFacetLabel facetId="${zap.facetId}" feeds="${feeds}"/>&nbsp;
								</c:otherwise>
							</c:choose>
						</span>
						<span class="categoryName">
							<c:choose>
								<c:when test="${not empty category}">
									<plma:getCategoryLabel category="${category}" feeds="${feeds}"/>&nbsp;
								</c:when>
								<c:otherwise>
									<plma:getCategoryLabel categoryId="${zap.categoryId}"/>&nbsp;
								</c:otherwise>
							</c:choose>
						</span>
						<span class="fonticon fonticon-cancel">
						</span>
					</a>
				</c:forEach>
			</c:if>
		</c:if>
	</c:when>

	<c:when test="${filterType == 'FROM_URL'}">
		<%-- To display all applied filters, even on an empty feed, we must look at the refine parameters in the URL --%>
		<search:forEachFeed feeds="${feeds}" var="feed">
			<search:getFeedInfo var="nhits" name="nhits" feed="${feed}"/>
			<c:if test="${nhits == '0' || !hideInactive}">
				<plma:getRefines var="refines" varInactive="inactiveRefines" feed="${feed}" only1d="${not display2D}"/>
				<c:forEach items="${refines}" var="refine">
					<%-- Removing the '+' at the beginning of the refine value to make sure we detect already displayed filters --%>
					<c:set var="refineValue"
						   value="${fn:startsWith(refine.value, '+') ? fn:substring(refine.value, 1, fn:length(refine.value)) : refine.value}"/>
					<c:if test="${filters != null && !list:contains(filters, refineValue)}">
						<a class="activeFilter ${refine.exclude ? 'excluded' : 'selected'}Filter"
						   data-facet="${refine.value}" data-type="${refine.exclude ?'cr':'r'}"
						   onclick="ActiveFilters.utils.deleteRefine('${refine.value}')"
						   title="${isInactive ? inactiveTooltip : cancelTooltip}">
							<span class="facetName">
								<plma:getFacetLabel facetId="${refine.facetId}" feed="${feed}"/>&nbsp;
							</span>
							<span class="categoryName">
								<plma:getCategoryLabel categoryId="${refineValue}"/>&nbsp;
							</span>
							<span class="fonticon fonticon-cancel">
							</span>
						</a>
						<c:if test="${filters != null}">
							<list:add list="${filters}" value="${refineValue}"/>
						</c:if>
					</c:if>
				</c:forEach>
				<c:forEach items="${inactiveRefines}" var="refine">
					<%-- Removing the '+' at the beginning of the refine value to make sure we detect already displayed filters --%>
					<c:set var="refineValue"
						   value="${fn:startsWith(refine.value, '+') ? fn:substring(refine.value, 1, fn:length(refine.value)) : refine.value}"/>
					<c:if test="${filters != null && !list:contains(filters, refineValue)}">
						<a class="activeFilter ${refine.exclude ? 'excluded' : 'selected'}Filter inactiveFilter"
						   data-facet="${refine.value}" data-type="${refine.exclude ?'cr':'r'}"
						   onclick="ActiveFilters.utils.deleteRefine('${refine.value}')"
						   title="${inactiveTooltip}">
							<span class="facetName">
								<plma:getFacetLabel facetId="${refine.facetId}" feed="${feed}"/>&nbsp;
							</span>
							<span class="categoryName">
								<plma:getCategoryLabel categoryId="${refineValue}"/>&nbsp;
							</span>
							<span class="fonticon fonticon-cancel">
							</span>
						</a>
						<c:if test="${filters != null}">
							<list:add list="${filters}" value="${refineValue}"/>
						</c:if>
					</c:if>
				</c:forEach>
			</c:if>
		</search:forEachFeed>

	</c:when>

	<c:when test="${filterType == 'SEARCH'}">
		<%-- Current user search --%>
		<c:if test="${not empty searchParameter}">
			<i18n:message var="cancelSearchTooltip" code="widgets.activeFilters.tooltip.cancelSearch"
						  text="Remove this search query"/>
			<search:forEachFeed feeds="${feeds}" var="feed">
				<request:getParameterValue var="query" name="${searchParameter}"/>
				<c:if test="${not empty query}">
					<list:add list="${filters}" value="${query}"/>

					<url:url var="cancelSearchUrl" keepQueryString="true">
						<url:parameter name="${searchParameter}" value=""/>
					</url:url>
					<c:if test="${separateFeeds == 'true' }">
						<a class="activeFilter selectedFilter searchRefine_${searchParameter}"
						   onmouseover="ActiveFilters.utils.highlightRefinements('searchRefine_${searchParameter}', true)"
						   onmouseout="ActiveFilters.utils.highlightRefinements('searchRefine_${searchParameter}', false)"
						   href="${cancelSearchUrl}"
						   title="${cancelSearchTooltip}"
						   data-type="q"
						   data-query="unsaved"
						   data-facet="${query}">
							<span class="facetName">
								<i18n:message code="widgets.activeFilters.label.search" text="Search"/>&nbsp;
							</span>
							<span class="categoryName">
									${query}
							</span>
							<span class="fonticon fonticon-cancel">
							</span>
						</a>
					</c:if>
				</c:if>
			</search:forEachFeed>
			<c:if test="${not empty query}">
				<c:if test="${separateFeeds == 'false' }">
					<a class="activeFilter selectedFilter" href="${cancelSearchUrl}" title="${cancelSearchTooltip}"
					   data-type="q"
					   data-query="unsaved"
					   data-facet="${query}">
						<span class="facetName">
							<i18n:message code="widgets.activeFilters.label.search" text="Search"/>&nbsp;
						</span>
						<span class="categoryName">
								${query}
						</span>
						<span class="fonticon fonticon-cancel">
						</span>
					</a>
				</c:if>
			</c:if>
		</c:if>
		<c:if test="${displaySavedFilter == 'true'}">
			<plma:getParamFromSavedFilters var="savedQuery" pageId="${pageId}" pageName="${pageName}" param="q"/>
			<c:if test="${not empty savedQuery}">
				<c:forEach var="query" items="${savedQuery}">
					<a class="activeFilter selectedFilter" href="${cancelSearchUrl}" title="${cancelSearchTooltip}"
					   data-facet="${query}"
					   data-type="q">
						<span class="facetName">
							<i18n:message code="widgets.activeFilters.label.search" text="Search"/>&nbsp;
						</span>
						<span class="categoryName">
								${query}
						</span>
						<span class="fonticon fonticon-cancel">
						</span>
					</a>
				</c:forEach>
			</c:if>
		</c:if>
	</c:when>

	<c:when test="${filterType == 'DATE'}">
		<config:getOptionsComposite var="customDateRefine" name="customDateRefine"/>
		<c:forEach items="${customDateRefine}" var="refine">
			<c:if test="${param[refine[1]] != undefined}">
				<url:url keepQueryString="true" var="cancelDateUrl">
					<url:parameter name="${refine[1]}" value=""/>
				</url:url>
				<a class="activeFilter selectedFilter" title="${cancelSearchTooltip}" href="${cancelDateUrl}">
					<span class="facetName">${refine[0]} </span>
					<span class="categoryName">
						<i18n:message code="widgets.activeFilters.label.between"/>
						<fmt:parseDate value="${fn:substring(param[refine[1]],1,11)}" var="parseStartDate"
									   pattern="yyyy-MM-dd"/>
						<fmt:formatDate type="date" dateStyle="long" timeStyle="long" value="${parseStartDate}"/>
						<i18n:message code="widgets.activeFilters.label.and"/>
						<fmt:parseDate value="${fn:substring(param[refine[1]],12,22)}" var="parseEndDate"
									   pattern="yyyy-MM-dd"/>
						<fmt:formatDate type="date" dateStyle="long" timeStyle="long" value="${parseEndDate}"/>
					</span>
					<span class="fonticon fonticon-cancel"></span>
				</a>
			</c:if>
		</c:forEach>
		<plma:getConstantValueFromNameTag var="simpleDateRefine" constantName="SIMPLE_DATE_PARAM" />

		<c:if test="${simpleDateRefine != ''}">
			<request:getParameterValues var="simpleDateRefineParams" name="${simpleDateRefine}" defaultValue=""/>
			<c:forEach items="${simpleDateRefineParams}" var="refine">
				<c:if test="${refine != undefined && refine != ''}">
					<url:url keepQueryString="true" var="cancelDateUrl">
					</url:url>
					<url:encode var="stringToReplace" value="${refine}"/>
					<c:set var="stringToReplace" value="${simpleDateRefine}=${stringToReplace}"/>
					<c:set var="cancelDateUrl" value="${fn:replace(cancelDateUrl,stringToReplace,'')}"/>
					<c:set var="field_name" value="${fn:replace(fn:split(refine,',')[0],'[','')}"/>
					<c:set var="start_date" value="${fn:split(refine,',')[1]}"/>
					<c:set var="end_date" value="${fn:replace(fn:split(refine,',')[2],']','')}"/>
					<a class="activeFilter selectedFilter" title="${cancelSearchTooltip}" href="${cancelDateUrl}"
					   data-facet="${refine}"
					   data-type="date">
						<span class="facetName">
							<i18n:message var="facetLabel" code="field_${field_name}"/>
							<c:choose>
								<c:when test="${facetLabel != undefined && facetLabel != ''}">${facetLabel}</c:when>
								<c:otherwise>${field_name}</c:otherwise>
							</c:choose>
							<c:if test="${fn:length(fn:split(refine,',')) > 2}">
								<c:choose>
									<c:when test="${start_date == 'null' && end_date != 'null'}">
										<i18n:message code="widgets.activeFilters.label.to"/>
									</c:when>
									<c:when test="${start_date != 'null' && end_date == 'null'}">
										<i18n:message code="widgets.activeFilters.label.from"/>
									</c:when>
									<c:otherwise>
										<i18n:message code="widgets.activeFilters.label.between"/>
									</c:otherwise>
								</c:choose>
							</c:if>
						</span>
						<span class="categoryName">
							<c:choose>
								<c:when test="${fn:length(fn:split(refine,',')) > 2}">
									<c:if test="${start_date != 'null'}">
										<fmt:parseDate value="${fn:substring(start_date,0,10)}" var="parseStartDate"
													   pattern="yyyy-MM-dd"/>
										<fmt:formatDate type="date" dateStyle="long" timeStyle="long"
														value="${parseStartDate}"/>
									</c:if>
									<c:if test="${start_date != 'null' && end_date != 'null'}">
										<i18n:message code="widgets.activeFilters.label.and"/>
									</c:if>
									<c:if test="${end_date != 'null'}">
										<fmt:parseDate value="${fn:substring(end_date,0,10)}" var="parseEndDate"
													   pattern="yyyy-MM-dd"/>
										<fmt:formatDate type="date" dateStyle="long" timeStyle="long"
														value="${parseEndDate}"/>
									</c:if>
								</c:when>
								<c:otherwise>
									<plma:getDateRangeLabelByIdTag rangeLabel="dateLabel"
																   rangeId="${fn:replace(fn:split(refine,',')[1],']','')}"/>
									<string:escape escapeType="HTML">
										<i18n:message code="${dateLabel}"/>
									</string:escape>
								</c:otherwise>
							</c:choose>
						</span>
						<span class="fonticon fonticon-cancel"></span>
					</a>
				</c:if>
			</c:forEach>
			<c:if test="${displaySavedFilter == 'true'}">
				<plma:getParamFromSavedFilters var="savedQuery" pageId="${pageId}" pageName="${pageName}"
											   param="${simpleDateRefine}"/>
				<c:if test="${not empty savedQuery}">
					<c:forEach items="${savedQuery}" var="refine">
						<c:if test="${refine != undefined && refine != ''}">
							<url:url keepQueryString="true" var="cancelDateUrl">
							</url:url>
							<url:encode var="stringToReplace" value="${refine}"/>
							<c:set var="stringToReplace" value="${simpleDateRefine}=${stringToReplace}"/>
							<c:set var="cancelDateUrl" value="${fn:replace(cancelDateUrl,stringToReplace,'')}"/>
							<c:set var="field_name" value="${fn:replace(fn:split(refine,',')[0],'[','')}"/>
							<c:set var="start_date" value="${fn:split(refine,',')[1]}"/>
							<c:set var="end_date" value="${fn:replace(fn:split(refine,',')[2],']','')}"/>
							<a class="activeFilter selectedFilter" title="${cancelSearchTooltip}"
							   href="${cancelDateUrl}" about=""
							   data-facet="${refine}"
							   data-type="date">
								<span class="facetName">
									<i18n:message var="facetLabel" code="field_${field_name}"/>
									<c:choose>
										<c:when test="${facetLabel != undefined && facetLabel != ''}">${facetLabel}</c:when>
										<c:otherwise>${field_name}</c:otherwise>
									</c:choose>
									<c:if test="${fn:length(fn:split(refine,',')) > 2}">
										<c:choose>
											<c:when test="${start_date == 'null' && end_date != 'null'}">
												<i18n:message code="widgets.activeFilters.label.to"/>
											</c:when>
											<c:when test="${start_date != 'null' && end_date == 'null'}">
												<i18n:message code="widgets.activeFilters.label.from"/>
											</c:when>
											<c:otherwise>
												<i18n:message code="widgets.activeFilters.label.between"/>
											</c:otherwise>
										</c:choose>
									</c:if>
								</span>
								<span class="categoryName">
									<c:choose>
										<c:when test="${fn:length(fn:split(refine,',')) > 2}">
											<c:if test="${start_date != 'null'}">
												<fmt:parseDate value="${fn:substring(start_date,0,10)}"
															   var="parseStartDate"
															   pattern="yyyy-MM-dd"/>
												<fmt:formatDate type="date" dateStyle="long" timeStyle="long"
																value="${parseStartDate}"/>
											</c:if>
											<c:if test="${start_date != 'null' && end_date != 'null'}">
												<i18n:message code="widgets.activeFilters.label.and"/>
											</c:if>
											<c:if test="${end_date != 'null'}">
												<fmt:parseDate value="${fn:substring(end_date,0,10)}" var="parseEndDate"
															   pattern="yyyy-MM-dd"/>
												<fmt:formatDate type="date" dateStyle="long" timeStyle="long"
																value="${parseEndDate}"/>
											</c:if>
										</c:when>
										<c:otherwise>
											<plma:getDateRangeLabelByIdTag rangeLabel="dateLabel"
																		   rangeId="${fn:replace(fn:split(refine,',')[1],']','')}"/>
											<string:escape escapeType="HTML">
												<i18n:message code="${dateLabel}"/>
											</string:escape>
										</c:otherwise>
									</c:choose>

								</span>
								<span class="fonticon fonticon-cancel"></span>
							</a>
						</c:if>
					</c:forEach>
				</c:if>
			</c:if>
		</c:if>
	</c:when>
	<c:when test="${filterType == 'CUSTOM'}">
		<c:if test="${not empty customParams}">
			<c:forEach var="customParam" items="${customParams}">
				<request:getParameterValue var="paramValue" name="${customParam.paramName}"/>
				<c:if test="${not empty paramValue}">
					<url:url keepQueryString="true" var="cancelParamUrl">
						<url:parameter name="${customParam.paramName}" value=""/>
					</url:url>
					<a class="activeFilter selectedFilter" href="${cancelParamUrl}" title="${customParam.paramName}"
					   data-type="custom"
					   data-query="${customParam.paramName}"
					   data-facet="${paramValue}">
						<span class="facetName">
								${customParam.label}
						</span>
						<span class="categoryName">
								${paramValue}
						</span>
						<span class="fonticon fonticon-cancel">
						</span>
					</a>
				</c:if>
			</c:forEach>
		</c:if>
		<c:if test="${displaySavedFilter == 'true'}">
			<c:if test="${not empty customParams}">
				<c:forEach var="customParam" items="${customParams}">
					<plma:getParamFromSavedFilters var="savedQuery" pageId="${pageId}" pageName="${pageName}"
												   param="${customParam.paramName}"/>
					<c:if test="${not empty savedQuery}">
						<url:url keepQueryString="true" var="cancelParamUrl">
							<url:parameter name="${customParam.paramName}" value=""/>
						</url:url>
						<a class="activeFilter selectedFilter" href="${cancelParamUrl}" title="${customParam.paramName}"
						   data-type="custom"
						   data-query="${customParam.paramName}"
						   data-facet="${savedQuery[0]}">
							<span class="facetName">
									${customParam.label}
							</span>
							<span class="categoryName">
									${savedQuery[0]}
							</span>
							<span class="fonticon fonticon-cancel">
						</span>
						</a>
					</c:if>
				</c:forEach>
			</c:if>
		</c:if>
	</c:when>
</c:choose>

