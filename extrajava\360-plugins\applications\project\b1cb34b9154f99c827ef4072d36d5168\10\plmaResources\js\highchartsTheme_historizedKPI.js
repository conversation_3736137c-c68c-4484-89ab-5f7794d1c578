/* global Highcharts, exa, buildUrl, data */
(function(){
	'use strict';
	
	if (typeof Highcharts !== 'undefined'){
		
		var plmaTheme = {
			chart: {
				zoomType: 'xy',
				spacing: 20,			
				className: 'card',
				backgroundColor: '#368ec4',
				marginLeft: 0,
				marginRight: 0,
				marginBottom: 50, 
				marginTop: 100,
				border: 1,
				borderRadius: 5,
				style: {
					fontFamily: '3ds',
					fontSize: '14pt'
				}
			},
			legend: {
				enabled: false
			},
			title: {
				align: 'left',
				verticalAlign: 'top',
				style:{
					color: '#fff',
					fontFamily: '\"3ds\"',
					fontSize: '14pt'
				}
					
			},	
			credits: {
				enabled: false
			},
			tooltip: {
					positioner: function () {
					  return {
						x: this.chart.chartWidth - this.label.width - 5, // right aligned
						y: -1// align to title
					  };
					},
					borderWidth: 0,
					backgroundColor: 'none',
					pointFormat: '{point.y}',
					headerFormat: '',
					hideDelay: 1000,
					shadow: false,
					style: {
					  fontFamily: 'Arial',
					  fontWeight: 'bold',
					  fontSize: '20pt',
					  color: '#11B9DF'
					}
				},
			plotOptions: {
				series: {
					turboThreshold: 0,
					cursor: 'pointer',
					shadow: false,
					marker: {
						enabled: false
					},
					lineColor: '#fff',
					fillColor: 'rgba(255, 255, 255, 0.25)',			
					events: {
						click: function(e) {
							if (e.point.r) {
								exa.redirect(buildUrl(this.options.baseUrl, e.point.r));
							}
						}
					}
				}
			},
			xAxis: {
				labels: {
					style: {
						fontFamily: 'Arial',
						fontSize: '9pt',
						color: '#fff'
					},
					y: 35
				},
				lineColor: '#11B9DF',
				tickColor: 'transparent',
				tickInterval: (typeof data !== 'undefined' && 
						data.xAxis !== undefined && 
						data.xAxis.categories !== undefined) ? Math.floor(data.xAxis.categories.length / 15, 0) + 1 : null,
				min:0
			},
			yAxis: [{
				title: {
					text: null
				},
				min:0,
				minRange: 1
			}]
				
		};
		
		Highcharts.setOptions(plmaTheme);

	} else {
		if (console && console.warn){
			console.warn('Highcharts has been loaded too late: PLMA theme not applied.');	
		}
	}
	
})();
