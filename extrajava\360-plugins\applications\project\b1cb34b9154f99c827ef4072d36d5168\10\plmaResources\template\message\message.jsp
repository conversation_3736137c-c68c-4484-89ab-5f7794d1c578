<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>

<script src="../resources/widgets/plmaResources/js/message.js"></script>

<render:import parameters="result,message,container" />

<div class="message-plma-container hidden">
	<div class="message-plma-padding">
		<div class="message-plma ${result=='alert' ? 'message-alert' : ''} ${result=='success' ? 'message-success' : ''} ${result=='info' ? 'message-info' : ''}">
			<c:choose>
				<c:when test="${result=='alert'}">
					<span class="fonticon-message fonticon fonticon-attention"></span>
				</c:when>
				<c:when test="${result=='success'}">
					<span class="fonticon-message fonticon fonticon-check"></span>
				</c:when>
				<c:when test="${result=='info'}">
					<span class="fonticon-message fonticon fonticon-info-circled"></span>
				</c:when>
			</c:choose>
			<span class="message-text">
				<i18n:message code="${message}" text="${message}"/>
			</span>
			<span class="message-cancel fonticon fonticon-cancel" onclick="hideMessage('${container}')"></span>
		</div>
	</div>
</div>