/**
 * Refine panel toggle button library
 *
 * @param options Library options
 * @constructor Constructor
 */
var RefinePanelToggle = function (options) {
    this.options = options;
    this.init();
};

/**
 * Init button event handler
 */
RefinePanelToggle.prototype.init = function () {
    var selectorId = '.preferred-hit-' + $.escapeSelector(this.options.docId);
    $('.refineButtonToggle').find('.fonticon-filter').off('click').on('click', $.proxy(function (e) {
        this.expandContainer();
    }, this));
    $('.refineButtonToggle').find('.fonticon-filter-delete').off('click').on('click', $.proxy(function (e) {
        this.collapseContainer();
    }, this));
}

RefinePanelToggle.prototype.expandContainer = function () {
    $('.refinesContainer,.refineButtonToggle').addClass('state-expanded').removeClass('state-collapsed');
    $.cookie(PLMACollapsibleContainer.REFINES_CONTAINER_COOKIE_NAME, 'expanded');
    $(window).trigger('plma:resize', [0]);
};

RefinePanelToggle.prototype.collapseContainer = function () {
    $('.refinesContainer,.refineButtonToggle').addClass('state-collapsed').removeClass('state-expanded');
    $.cookie(PLMACollapsibleContainer.REFINES_CONTAINER_COOKIE_NAME, 'collapsed');
    $(window).trigger('plma:resize', [0]);
};