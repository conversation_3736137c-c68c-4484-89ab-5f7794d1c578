@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/utils.less";

.wuid.searchWidget.alert-notifications-container {
    height: 100%;
	width: 800px;
	padding: 27px;
	margin: 0;

	.widget-title {
		font-size: 18px;
		color: @clink-active;
		margin-bottom: 15px;
	}

	.widget-container {
        height: 100%;
        overflow: auto;

		.list-container {
            padding-right: 5px;
            padding-left: 5px;

			.notification-wrapper {
				display: flex;
				justify-content: space-between;
				padding: 10px;
				border: 1px solid #e2e4e3;
				font-size: 16px;
				flex-wrap: wrap;
				margin-bottom: 10px;

                &:hover {
                  opacity: 0.8;
                  cursor: pointer;
                }

				&.seen {
					background-color: white;
				}

				&.unseen {
					background-color: #d5e8f2;
					font-weight: 700;
					.icon {
						color: #368ec4;
					}
				}

				.icon {
					padding: 10px;
					&.delete-notification {
						&:hover {
							opacity: 0.5;
						}
					}
				}

				.content {
					align-items: center;
					display: inline-flex;
				}
			}
		}
	}
}