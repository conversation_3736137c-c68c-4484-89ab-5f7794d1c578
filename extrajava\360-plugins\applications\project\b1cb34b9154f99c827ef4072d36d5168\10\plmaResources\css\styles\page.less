@import "../polyfills.less";

@-moz-document url-prefix() {
	.mashup.mashup-style .sideBarMenu.collapsed {
		min-width: 40px;
	}
	@media  (max-width: @screen-sm-max) {
		.mashup.mashup-style .sideBarMenu {
			min-width: 40px;
		}
	}
}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
	.mashup.mashup-style .sideBarMenu.collapsed {
		min-width: 40px;
	}
	@media  (max-width: @screen-sm-max) {
		.mashup.mashup-style .sideBarMenu {
			min-width: 40px;
		}
	}
}

@media screen and (max-width: @screen-md) {
	.flexPanelsContainer-row .flexPanelsContainer-item.result-panel.no-hit-selected {
		flex-grow: 0;
		flex-shrink: 0;
		overflow: hidden;
		display: none;
	}
}

.mashup.mashup-style{
	.hidden {
		display: none;
	}

	::-webkit-scrollbar {
		width: 6px;
		height: 6px;
	}
	::-webkit-scrollbar-track {
		background: #D1D4D4;
	}
	::-webkit-scrollbar-thumb {
		border-radius: 1px;
	}
	:hover::-webkit-scrollbar-thumb {
		background: #42a2da;
	}
	::-webkit-scrollbar-thumb:window-inactive {
		background: #D1D4D4;
	}

	.pageTitleContainer{
		> .rangeSelector{
			position: absolute;
			right: 0;
		}
		> .flexPanelsContainer-row{
			height : 100%;
		}
	}


	.pageTitleWrapper{
		/*
		position: absolute;
		left: 0;*/
	}

	.pageTitleWidget{
		/*
		position: relative;
		height: 100%; */
		border-bottom: 1px solid @cblock-border;
	}

	.pageTitle{
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		//line-height: 36px;
		/*margin-bottom: @line-height;*/
		padding: @line-height 10px;
		font-size: 23px;
	}

	.pageTitleContainer ~ .activeFilters{
		border-bottom: 1px solid @cblock-border;
		width: 100%;
		box-sizing: border-box;
	}

	.iconsPlaceholder {
		.display-flex();
		.align-content(flex-end);
		.flex-flow(row-reverse);
		.justify-content(center);
		.align-items(center);
		margin-right: 10px;

		.iconDiv {
			.display-flex();
			align-items: center;
			font-size: var(--icons-size, @l-font);
			margin-left: 5px;
		}
	}

	.icons-div{
		//position: absolute;
		//right: 10px;
		//z-index: 100;

		> div{
			/*
			text-align: left;
			float: right;
			margin: 24px 0;
			height: 36px;
			*/
			font-size: @l-font;

			&.active {
				color: @clink;
			}
		}

		.back-button {
			display: flex;
			/*
			line-height: 36px;
			margin-top: 12px;
			*/
			&.with-dropdown {
				.back, .dropdown {
					border: 1px solid transparent; /* Prevent elements from moving while hovering over */
				}
				&:hover {
					.back, .dropdown {
						border: 1px solid @ctext-weak;
						&:hover {
							background-color: @ctext-weaker;
						}
					}
					.back {
						border-radius: 3px 0px 0px 3px;
					}
					.dropdown {
						border-radius: 0px 3px 3px 0px;
					}
				}
			}
			.dropdown {
				position: relative;
				font-size: 12px;
				margin-left: -1px;
				.history-dropdown {
					position: absolute;
					top: 40px;
					right: 0px;
					background-color: white;
					font-family: Arial;
					font-weight: normal;
					white-space: nowrap;
					z-index: 1;
					cursor: default;
					line-height: normal;
					max-height: 200px;
                    overflow-y: auto;
                    overflow-x: hidden;
                    border-bottom: 1px solid #e2e4e3;
                    padding-bottom: 1px;

					.history-entry {
						border: 1px solid @cblock-border;
						margin-bottom: -1px; /* Prevent border overlapping */
						&.dashboardPage {
							border-top: 2px solid @cblock-border;
						}
						a {
							display: inline-block;
							width: 100%;
							padding: 5px;
							color: @ctext;
							i {
								margin-right: 6px;
							}
						}
						&:hover {
							background-color: @clink-active;
							a {
								color: white;
							}
						}
					}
				}
			}
		}
	}



	.activeFilters, .cleverFilter{
		.flex(0 0 auto);
	}

	.exa-table {
		table-layout: fixed;
	}

	.selectedChartFromHome{
		.widgetHeader{
			border-color: @clink;
			border-bottom: 0;
			outline: 0;
			box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
			-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
			-moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
		}
		.widgetContent{
			border-color: @clink;
			outline: 0;
			box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
			-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
			-moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px @clink;
		}

	}

	#panelsContainer{
		height: 100vh;
		> .flexPanelsContainer-row {
			height: 100%;


			>.flexPanelsContainer-item{
				height: 100%;
				overflow: auto;

				&:first-child{ /* Menu sidebar */
					.flex(0 0 auto);

					/* Overriding the sidebar style to avoid weird scrolling behaviour */
					background: @cblock-border;
					border-right: 1px solid @cblock-border-alt;
					box-sizing: border-box;

				}

				&.collapsiblePanel{
					.transition(@transition-quick, "~flex-grow, flex-basis");
					.flex-shrink(0);
					.flex-basis(150px);
					border-right: 1px solid @cblock-border;
					border-left: 1px solid @cblock-border;
					padding: 0 (@line-height /2);
					white-space: nowrap;

					&.hidden{
						.transition(@transition-quick, "~flex-grow, flex-basis");
						.flex-grow(0);
						.flex-basis(0);
						border: none;
						padding: 0;
						/* Fix for IE */
						overflow: hidden;
					}
				}

				&.refinesPanel{
				}
				&.bookmarksPanel{
					padding: 0;
					.widgetHeader {
						margin: @line-height (@line-height /2);
					}
					.savedbookmarks{
						max-width: 60em;  /* To avoid weird behaviour when bookmark title is too long */
					}
				}

				&.contentPanel{
					.display-flex();
					.flex-flow(column nowrap);
					.justify-content(flex-start);

					.pageTitleContainer{
						.flex(0 0 auto);
					}

					.flex-panel-container-hits{
						.flex(1 1 auto);
						position: relative;
					}
				}

				&.main-flex-container-home{
					height: inherit;
				}
			}

		}

	}

	.sideBarMenu{
		height: 100%;
		/* Border and background are set on the container for better scrolling behaviour */
		background: none;
		border: none;
	}

	.refinesPanel, .bookmarksPanel{
		background-color: @cblock-bg;
		border-right: 1px solid @cblock-border;
		height: 100%;

		.widgetHeader {
			margin: @line-height 0
		}
	}

	/* */
	.refinesPanel.refines, .refinesPanel .refines{
		//		background: @cblock-bg;
		//		margin: 0;
		//		min-width: 150px;
		//		padding: 0 (@line-height / 2);
		//		border: none;
		//		border-radius: 0;
		//
		//		.widgetHeader{
		//			padding: 10px 4px 0;
		//			font-size: @l-font;
		//			line-height: @l-font;
		//			font-family: "3ds-light";
		//			font-weight: normal;
		//		}
		//
		//		.widgetHeader, .widgetContent{
		//			background: none;
		//			border: 0 none;
		//			border-radius: 0;
		//		}
		.widgetContent.facets{
			h3{
				background: none;
				border: none;
				font-weight: bold;
				margin-top: @line-height;

				&.sub-header-collapsed{
					border-bottom: 1px solid @ctext-inverted;
				}

				&:hover{
					color: @ctext;
				}
			}

		}
	}

	.result-detail-container {
		flex: 1 0 0px;
		position: relative;
		.flexPanelsContainer-row {
			position: absolute;
			height: 100%;
			width: 100%;
			.result-panel {
				.plmaResultList {
					padding-bottom: 0;
					padding-right: 0;
					.panels-container {
						background-color: @cbody-bg;
						.resultsTitle {
							font-size: 15px;
						}
						.hit {
							background-color: white;
							cursor: pointer;
							&:hover {
								background-color: @cblock-bg-alt;
							}
							&.hit-table {
								.hit-checkbox-container {
									top: 0;
									bottom: 0;
									margin-bottom: auto;
									margin-top: auto;
								}
							}
						}
					}
				}
			}
			.detail-panel {
			    border-left: 1px solid @cblock-border-alt;
				overflow: auto;
				.hit {
					position: relative;
					&.hit-more {
						height: 30px;
						align-self: center;
					}
					.socialTimeline {
						.referenceDate {
							background: white;
							margin-left: 40px;
							margin-right: 40px;
						}
					}
					.resultCarousel{
						.hit {
							height: 100%;
							background: white;
						}
					}
					.action-btn {
						margin-right: 45px;
					}
					.action-btn-mixin() {
						position: absolute;
						top: 17px;
						font-size: 24px;
						&:hover {
							color: @clink-active;
							cursor: pointer;
						}
					}
					.close-panel-detail-button {
						.action-btn-mixin();
						right: 10px;
					}
					.open-with-button {
						.action-btn-mixin();
						right: 48px
					}
					.recentSearches.pin {
						.action-btn-mixin();
						right: 95px
					}
					.edit-object-properties-button {
						.action-btn-mixin();
						right: 160px;
					}
					.collapsibleBlock {
						.collapse-content {
							position: relative;
							.edit-object-properties-button {
								top: -10px;
								right: 50px;
								font-size: 20px;
							}
						}
					}
				}
				&.hidden {
					display: none;
				}
				.hitTitle {
					margin-left: 0;
				}

				.select-all-container {
					position: relative;
					width: 280px;
					margin-left: 8px;
					font-size: 14px;

					.select-all-label {
						margin-left: 5px;
					}
					.select-all-button {
						right: 8px;
					}
				}
			}
			.selection-panel {
				border-left: 1px solid @cblock-border;
				//padding: 20px;
				padding-bottom: 0;
				overflow: auto;
				&.hidden {
					display: none;
				}
				.reload-container {
					width: 100%;
					height: 100%;
					padding: 0;
					.title-selection-panel {
						height: 22px;
						font-size: 20px;
						margin-bottom: 20px;
						padding-left: 7px;
						color: @clink-active;
						position: relative;
                        top: 19px;
						.title-selection-nb-hit-container {
							background-color: #fd872b;
							background-color: #fd872bad;
							color: white;
							width: 25px;
							height: 25px;
							border-radius: 25px;
							display: inline-block;
							position: relative;
							top: -5px;
							.title-selection-nb-hit {
								position: relative;
								font-size: 21px;
								top: 6px;
								left: 8px;
								&.two-digit-number {
									font-size: 18px;
									left: 3px;
								}
								&.three-digit-number {
									font-size: 13px;
									left: 2px;
									top: 4px;
								}
								&.four-digit-number {
									font-size: 11px;
									left: 1px;
									top: 3px;
								}
							}
						}
					}
					.edit-object-properties-button {
						top: 22px;
						right: 30px;
					}
				}
			}
			.edit-object-properties-button {
				position: relative;
				font-size: 24px;
				display: inline-block;
				&:hover {
					color: @clink-active;
					cursor: pointer;
				}
			}
		}
	}
	// */

	.linkButton{
		display: block;
		font-size: @m-font;
		padding: (@line-height /2) @line-height;

		&:hover{
			text-decoration: none;
			color: @clink-hover;
			background-color: @clink-bg-hover;
		}
	}

	.main-flex-container-home{
		.display-flex();
		.flex-direction(column);
		/*
		.pageTitleContainer{
			.flex-basis(75px);
			flex-shrink: 0;
			height: 75px;
		}
		 */
		.pageBlockMenu{
			.flex(1);
			.align-items(center);
			.justify-content(center);
			.display-flex();
			margin-bottom: 200px;
			@media (max-height: 600px) {
				margin-bottom: 100px;
			}
			@media (max-height: 400px) {
				margin-bottom: 0px;
			}
		}
	}

	.aboutLightbox{
		background: transparent;
		margin-top: 300px;
		.align-self(flex-start);
	}

	.flex-row-top-assemblies{
		flex: 1;
	}
	.flex-row-top-parts{
		flex: 1;
	}

	.main-container-discover-page{
		display: flex;
		flex-direction: column;
		.heatMap{
			width: 100%;
			box-sizing: border-box;
			flex-grow: 1;
		}
	}

	.plmaDisplayHits{
		.flex-grow(1); /* We usually want it to grow to the bottom of the page */

		/* Fix for iPad */
		.panels-container{
			position: absolute;
			top: 0;
			bottom: 0;
			width: 100%;
		}
	}

	.docLightbox{
		width: 80%;
		max-height: none;
	}

	.plma-preferences-column{
		.display-flex();
		flex-direction: column;
		.preference-widget-issue{
			&.plma-preferences{
				.flex(1);
				width: 100%;
				.widgetContent{
					height: 100%;
				}
			}
		}
	}

	.indexPage {
		background-color: white;
		.pageTitleContainer {
			background-color: #f4f5f6;
			.paga-title {
				margin-left: 17px;
			}
		}
		.use-cases-container {
			background-color: #f4f5f6;
			.widget-container {
				height: 270px;
			}
		}
	}

	/* IE fixes */
	&.ie {
		.pageTitleContainer{
			z-index:6;
		}
	}
}
