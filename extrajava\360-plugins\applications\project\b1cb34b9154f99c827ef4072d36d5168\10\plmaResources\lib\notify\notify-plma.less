.mashup .notifyjs-corner {
	z-index: 1000000;
    margin-left: calc(50vw - 25vw);
	.notifyjs-wrapper{
        width: 50vw;
		margin: auto;
	}
	.notifyjs-container{
		margin: auto;
		width: max-content;
		max-width: 100%;
	}
}

.notifyjs-plma-base,.notifyjs-plmabutton-base, .notifyjs-plmahtml-base {
	
	/* 3DXP Guidelines */
	padding: 10px 38px 10px 0;
	box-shadow: 0 1px 1px 0px rgba(0, 0, 0, 0.3);
	font-size: 12pt;
    font-family: 'entypo';
	color: #3d3d3d;
	display: inline-flex;
    align-items: center;

    /* Defaults to Info feedback */
	background-color: #f2f5f7;
	border-left: 5px solid #00B8DE;
	
	.icon {
		font-size: 22px;
		line-height: 22px;
		margin: 0 8px;
		display: none;
		
	}
	
	/* Info feedback */
	&.notifyjs-plma-info,&.notifyjs-plmabutton-info,&.notifyjs-plmahtml-info {
		.icon.icon-info {
			display: inline;
			color: #00B8DE;
		}
	}

	/* Success feedback */
	&.notifyjs-plma-success,&.notifyjs-plmabutton-success,&.notifyjs-plmahtml-success {
		background-color: #EDF6EB;
		border-color: #57B847;
		
		.icon.icon-success {
			display: inline;
			color: #57B847;
		}
	}
	
	/* Warning feedback */
	&.notifyjs-plma-warn,&.notifyjs-plmabutton-warn,&.notifyjs-plmahtml-warn {
		background-color: #FFF3E9;
		border-color: #E87B00;
		
		.icon.icon-warn {
			display: inline;
			color: #E87B00;
		}
	}
	
	/* Error feedback */
	&.notifyjs-plma-error,&.notifyjs-plmabutton-error,&.notifyjs-plmahtml-error {
		background-color: #FFF0EE;
		border-color: #EA4F37;
		
		.icon.icon-error {
			display: inline;
			color: #EA4F37;
		}
	}

	.button {
		position: absolute;
		right: 3px;
		top: 15px;
		color: #57B847;
		font-size: 20px;
	}
}