/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:21:04 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.recentSearches;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class widget_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(14);
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/widget.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/i18n.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fmt.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/request.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/map.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetEntry_0026_005fvar_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetMetaValue_0026_005fvar_005fmetaName_005fentry_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fsetOption_0026_005fvalue_005fname_005fcomponent_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdoEval_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fforEachSubWidget_0026_005fiteration;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fgetUcssId_0026_005fvar_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderOnce_0026_005fid;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetEntry_0026_005fvar_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetMetaValue_0026_005fvar_005fmetaName_005fentry_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fsetOption_0026_005fvalue_005fname_005fcomponent_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdoEval_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fforEachSubWidget_0026_005fiteration = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fgetUcssId_0026_005fvar_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderOnce_0026_005fid = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetEntry_0026_005fvar_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetMetaValue_0026_005fvar_005fmetaName_005fentry_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fconfig_005fsetOption_0026_005fvalue_005fname_005fcomponent_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdoEval_005fnobody.release();
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fwidget_005fforEachSubWidget_0026_005fiteration.release();
    _005fjspx_005ftagPool_005fwidget_005fgetUcssId_0026_005fvar_005fnobody.release();
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.release();
    _005fjspx_005ftagPool_005frender_005frenderOnce_0026_005fid.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f4(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f5(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f6(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_request_005fgetParameterValue_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_search_005fgetFeed_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_search_005fgetEntry_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_search_005fgetMetaValue_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fchoose_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptionComposite_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptionComposite_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fchoose_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fif_005f3(_jspx_page_context))
        return;
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_config_005fgetOption_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(14,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setVar("mode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(14,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setName("mode");
      int[] _jspx_push_body_count_config_005fgetOption_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f0 = _jspx_th_config_005fgetOption_005f0.doStartTag();
        if (_jspx_th_config_005fgetOption_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f0);
      _jspx_th_config_005fgetOption_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(15,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setVar("tooltipTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(15,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setName("tooltipTitle");
      int[] _jspx_push_body_count_config_005fgetOption_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f1 = _jspx_th_config_005fgetOption_005f1.doStartTag();
        if (_jspx_th_config_005fgetOption_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f1);
      _jspx_th_config_005fgetOption_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(16,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setName("entryUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(16,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setVar("entryUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(16,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setDefaultValue("window.location.pathname + window.location.search + window.location.hash");
      int[] _jspx_push_body_count_config_005fgetOption_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f2 = _jspx_th_config_005fgetOption_005f2.doStartTag();
        if (_jspx_th_config_005fgetOption_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f2);
      _jspx_th_config_005fgetOption_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f3_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(17,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setName("metaForId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(17,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setVar("metaForId");
      int[] _jspx_push_body_count_config_005fgetOption_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f3 = _jspx_th_config_005fgetOption_005f3.doStartTag();
        if (_jspx_th_config_005fgetOption_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f3);
      _jspx_th_config_005fgetOption_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f4 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f4_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f4.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(18,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setName("multiselect");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(18,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setVar("multiselect");
      int[] _jspx_push_body_count_config_005fgetOption_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f4 = _jspx_th_config_005fgetOption_005f4.doStartTag();
        if (_jspx_th_config_005fgetOption_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f4);
      _jspx_th_config_005fgetOption_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f4, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f5(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f5 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f5_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f5.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f5.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(19,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setName("iconCssFormultipleCompare");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(19,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setVar("iconCssFormultipleCompare");
      int[] _jspx_push_body_count_config_005fgetOption_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f5 = _jspx_th_config_005fgetOption_005f5.doStartTag();
        if (_jspx_th_config_005fgetOption_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f5);
      _jspx_th_config_005fgetOption_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f5, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f6(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f6 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f6_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f6.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f6.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(20,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setName("displayHitsMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(20,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setVar("displayHitsMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(20,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setDefaultValue("default");
      int[] _jspx_push_body_count_config_005fgetOption_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f6 = _jspx_th_config_005fgetOption_005f6.doStartTag();
        if (_jspx_th_config_005fgetOption_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f6);
      _jspx_th_config_005fgetOption_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f6, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(22,0) name = varWidget type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarWidget("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(22,0) name = varFeeds type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarFeeds("feeds");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetParameterValue_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getParameterValue
    com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag _jspx_th_request_005fgetParameterValue_005f0 = (com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag) _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag.class);
    boolean _jspx_th_request_005fgetParameterValue_005f0_reused = false;
    try {
      _jspx_th_request_005fgetParameterValue_005f0.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetParameterValue_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(23,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setVar("loadColletionDetailsView");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(23,0) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setName("loadColletionDetailsView");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(23,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setDefaultValue("false");
      int[] _jspx_push_body_count_request_005fgetParameterValue_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetParameterValue_005f0 = _jspx_th_request_005fgetParameterValue_005f0.doStartTag();
        if (_jspx_th_request_005fgetParameterValue_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetParameterValue_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetParameterValue_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetParameterValue_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_request_005fgetParameterValue_005f0);
      _jspx_th_request_005fgetParameterValue_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetParameterValue_005f0, _jsp_getInstanceManager(), _jspx_th_request_005fgetParameterValue_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFeed_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFeed
    com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag _jspx_th_search_005fgetFeed_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag) _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag.class);
    boolean _jspx_th_search_005fgetFeed_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFeed_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFeed_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(25,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeed_005f0.setVar("feed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(25,0) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeed_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFeed_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFeed_005f0 = _jspx_th_search_005fgetFeed_005f0.doStartTag();
        if (_jspx_th_search_005fgetFeed_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFeed_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFeed_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFeed_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody.reuse(_jspx_th_search_005fgetFeed_005f0);
      _jspx_th_search_005fgetFeed_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFeed_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFeed_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetEntry_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getEntry
    com.exalead.cv360.searchui.view.jspapi.search.GetEntryTag _jspx_th_search_005fgetEntry_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetEntryTag) _005fjspx_005ftagPool_005fsearch_005fgetEntry_0026_005fvar_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetEntryTag.class);
    boolean _jspx_th_search_005fgetEntry_005f0_reused = false;
    try {
      _jspx_th_search_005fgetEntry_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetEntry_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(26,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetEntry_005f0.setVar("entry");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(26,0) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetEntry_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetEntry_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetEntry_005f0 = _jspx_th_search_005fgetEntry_005f0.doStartTag();
        if (_jspx_th_search_005fgetEntry_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetEntry_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetEntry_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetEntry_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetEntry_0026_005fvar_005ffeeds_005fnobody.reuse(_jspx_th_search_005fgetEntry_005f0);
      _jspx_th_search_005fgetEntry_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetEntry_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetEntry_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetMetaValue_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getMetaValue
    com.exalead.cv360.searchui.view.jspapi.search.GetMetaValueTag _jspx_th_search_005fgetMetaValue_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetMetaValueTag) _005fjspx_005ftagPool_005fsearch_005fgetMetaValue_0026_005fvar_005fmetaName_005fentry_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetMetaValueTag.class);
    boolean _jspx_th_search_005fgetMetaValue_005f0_reused = false;
    try {
      _jspx_th_search_005fgetMetaValue_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetMetaValue_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(27,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetMetaValue_005f0.setVar("entryId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(27,0) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetMetaValue_005f0.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(27,0) name = metaName type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetMetaValue_005f0.setMetaName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${metaForId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetMetaValue_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetMetaValue_005f0 = _jspx_th_search_005fgetMetaValue_005f0.doStartTag();
        if (_jspx_th_search_005fgetMetaValue_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetMetaValue_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetMetaValue_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetMetaValue_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetMetaValue_0026_005fvar_005fmetaName_005fentry_005fnobody.reuse(_jspx_th_search_005fgetMetaValue_005f0);
      _jspx_th_search_005fgetMetaValue_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetMetaValue_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetMetaValue_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f0_reused = false;
    try {
      _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(28,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setVar("query");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(28,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(28,0) '${pageContext.request.queryString}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${pageContext.request.queryString}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
      if (_jspx_th_c_005fset_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
      _jspx_th_c_005fset_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent(null);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_c_005fwhen_005f1(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(31,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mode == 'getter' || mode == 'listener'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_config_005fsetOption_005f0(_jspx_th_c_005fwhen_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_config_005fsetOption_005f1(_jspx_th_c_005fwhen_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fsetOption_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:setOption
    com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag _jspx_th_config_005fsetOption_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag) _005fjspx_005ftagPool_005fconfig_005fsetOption_0026_005fvalue_005fname_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag.class);
    boolean _jspx_th_config_005fsetOption_005f0_reused = false;
    try {
      _jspx_th_config_005fsetOption_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fsetOption_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(32,2) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f0.setName("collectionDef");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(32,2) name = value type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f0.setValue("history##db##recent_searches_history##remove-old##fonticon-navigation-history##1##user");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(32,2) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f0.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_config_005fsetOption_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fsetOption_005f0 = _jspx_th_config_005fsetOption_005f0.doStartTag();
        if (_jspx_th_config_005fsetOption_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fsetOption_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fsetOption_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fsetOption_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fsetOption_0026_005fvalue_005fname_005fcomponent_005fnobody.reuse(_jspx_th_config_005fsetOption_005f0);
      _jspx_th_config_005fsetOption_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fsetOption_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fsetOption_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fsetOption_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:setOption
    com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag _jspx_th_config_005fsetOption_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag) _005fjspx_005ftagPool_005fconfig_005fsetOption_0026_005fvalue_005fname_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag.class);
    boolean _jspx_th_config_005fsetOption_005f1_reused = false;
    try {
      _jspx_th_config_005fsetOption_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fsetOption_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(33,2) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f1.setName("buttonsDef");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(33,2) name = value type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f1.setValue("##");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(33,2) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f1.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_config_005fsetOption_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fsetOption_005f1 = _jspx_th_config_005fsetOption_005f1.doStartTag();
        if (_jspx_th_config_005fsetOption_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fsetOption_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fsetOption_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fsetOption_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fsetOption_0026_005fvalue_005fname_005fcomponent_005fnobody.reuse(_jspx_th_config_005fsetOption_005f1);
      _jspx_th_config_005fsetOption_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fsetOption_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fsetOption_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f1 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f1_reused = false;
    try {
      _jspx_th_c_005fwhen_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(35,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mode == 'pin' || mode == 'pins_getter'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f1 = _jspx_th_c_005fwhen_005f1.doStartTag();
      if (_jspx_eval_c_005fwhen_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_config_005fsetOption_005f2(_jspx_th_c_005fwhen_005f1, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_config_005fsetOption_005f3(_jspx_th_c_005fwhen_005f1, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f1);
      _jspx_th_c_005fwhen_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fsetOption_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:setOption
    com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag _jspx_th_config_005fsetOption_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag) _005fjspx_005ftagPool_005fconfig_005fsetOption_0026_005fvalue_005fname_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag.class);
    boolean _jspx_th_config_005fsetOption_005f2_reused = false;
    try {
      _jspx_th_config_005fsetOption_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fsetOption_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(36,2) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f2.setName("collectionDef");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(36,2) name = value type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f2.setValue("pin##db##recent_searches_pins##remove-old##fonticon-favorite-on##1##user");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(36,2) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f2.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_config_005fsetOption_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fsetOption_005f2 = _jspx_th_config_005fsetOption_005f2.doStartTag();
        if (_jspx_th_config_005fsetOption_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fsetOption_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fsetOption_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fsetOption_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fsetOption_0026_005fvalue_005fname_005fcomponent_005fnobody.reuse(_jspx_th_config_005fsetOption_005f2);
      _jspx_th_config_005fsetOption_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fsetOption_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fsetOption_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fsetOption_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:setOption
    com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag _jspx_th_config_005fsetOption_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag) _005fjspx_005ftagPool_005fconfig_005fsetOption_0026_005fvalue_005fname_005fcomponent_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.SetOptionTag.class);
    boolean _jspx_th_config_005fsetOption_005f3_reused = false;
    try {
      _jspx_th_config_005fsetOption_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fsetOption_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(37,2) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f3.setName("buttonsDef");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(37,2) name = value type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f3.setValue("fonticon-favorite-on##fonticon-favorite-off");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(37,2) name = component type = com.exalead.cv360.config.elements.CustomComponentParameterContainer reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fsetOption_005f3.setComponent((com.exalead.cv360.config.elements.CustomComponentParameterContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", com.exalead.cv360.config.elements.CustomComponentParameterContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_config_005fsetOption_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fsetOption_005f3 = _jspx_th_config_005fsetOption_005f3.doStartTag();
        if (_jspx_th_config_005fsetOption_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fsetOption_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fsetOption_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fsetOption_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fsetOption_0026_005fvalue_005fname_005fcomponent_005fnobody.reuse(_jspx_th_config_005fsetOption_005f3);
      _jspx_th_config_005fsetOption_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fsetOption_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fsetOption_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionComposite_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionCompositeTag _jspx_th_config_005fgetOptionComposite_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdoEval_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionComposite_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOptionComposite_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionComposite_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(40,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f0.setName("collectionDef");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(40,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f0.setVar("collectionDef");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(40,0) name = mapIndex type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f0.setMapIndex(true);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(40,0) name = doEval type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f0.setDoEval(true);
      int[] _jspx_push_body_count_config_005fgetOptionComposite_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionComposite_005f0 = _jspx_th_config_005fgetOptionComposite_005f0.doStartTag();
        if (_jspx_th_config_005fgetOptionComposite_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionComposite_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionComposite_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionComposite_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdoEval_005fnobody.reuse(_jspx_th_config_005fgetOptionComposite_005f0);
      _jspx_th_config_005fgetOptionComposite_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionComposite_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionComposite_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionComposite_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionCompositeTag _jspx_th_config_005fgetOptionComposite_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdoEval_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionComposite_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOptionComposite_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionComposite_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(41,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f1.setName("buttonsDef");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(41,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f1.setVar("buttonsDef");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(41,0) name = mapIndex type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f1.setMapIndex(true);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(41,0) name = doEval type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionComposite_005f1.setDoEval(true);
      int[] _jspx_push_body_count_config_005fgetOptionComposite_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionComposite_005f1 = _jspx_th_config_005fgetOptionComposite_005f1.doStartTag();
        if (_jspx_th_config_005fgetOptionComposite_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionComposite_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionComposite_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionComposite_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionComposite_0026_005fvar_005fname_005fmapIndex_005fdoEval_005fnobody.reuse(_jspx_th_config_005fgetOptionComposite_005f1);
      _jspx_th_config_005fgetOptionComposite_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionComposite_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionComposite_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f1_reused = false;
    try {
      _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(42,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setVar("wuidSubWidget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(42,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(42,0) ''",_jsp_getExpressionFactory().createValueExpression("",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
      if (_jspx_th_c_005fset_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
      _jspx_th_c_005fset_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f1 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f1_reused = false;
    try {
      _jspx_th_c_005fchoose_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f1.setParent(null);
      int _jspx_eval_c_005fchoose_005f1 = _jspx_th_c_005fchoose_005f1.doStartTag();
      if (_jspx_eval_c_005fchoose_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_c_005fwhen_005f2(_jspx_th_c_005fchoose_005f1, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_c_005fwhen_005f4(_jspx_th_c_005fchoose_005f1, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_c_005fwhen_005f5(_jspx_th_c_005fchoose_005f1, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f1);
      _jspx_th_c_005fchoose_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f2 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f2_reused = false;
    try {
      _jspx_th_c_005fwhen_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(45,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mode == 'custom' || mode == 'getter' || mode == 'pins_getter'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f2 = _jspx_th_c_005fwhen_005f2.doStartTag();
      if (_jspx_eval_c_005fwhen_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_widget_005fwidget_005f0(_jspx_th_c_005fwhen_005f2, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f2);
      _jspx_th_c_005fwhen_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fwidget_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:widget
    com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag _jspx_th_widget_005fwidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag) _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss.get(com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag.class);
    boolean _jspx_th_widget_005fwidget_005f0_reused = false;
    try {
      _jspx_th_widget_005fwidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fwidget_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(46,2) name = varUcssId type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setVarUcssId("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(46,2) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setExtraCss((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("recentSearches ${mode} collection-disabled ${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_widget_005fwidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fwidget_005f0 = _jspx_th_widget_005fwidget_005f0.doStartTag();
        if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fwidget_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fwidget_005f0);
          }
          do {
            out.write("\r\n");
            out.write("			");
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_c_005fchoose_005f2(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("				<span class=\"action-details label-section \">\r\n");
            out.write("					<span class=\"fonticon ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collectionDef.iconCss}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\">\r\n");
            out.write("						<span class=\"counter\">-</span>\r\n");
            out.write("					</span>\r\n");
            out.write("					<span class=\"title\">");
            if (_jspx_meth_config_005fgetOption_005f7(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("\r\n");
            out.write("				</span>\r\n");
            out.write("				");
            if (_jspx_meth_c_005fif_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_i18n_005fmessage_005f2(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("				<span class=\"action-clear fonticon fonticon-trash\" title=\"");
            if (_jspx_meth_i18n_005fmessage_005f3(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\"></span>\r\n");
            out.write("			</div>\r\n");
            out.write("			<div class=\"collection-details\">\r\n");
            out.write("				");
            if (_jspx_meth_c_005fif_005f1(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_c_005fif_005f2(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("			</div>\r\n");
            out.write("		");
            int evalDoAfterBody = _jspx_th_widget_005fwidget_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fwidget_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fwidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fwidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fwidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fwidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss.reuse(_jspx_th_widget_005fwidget_005f0);
      _jspx_th_widget_005fwidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fwidget_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fwidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f2 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f2_reused = false;
    try {
      _jspx_th_c_005fchoose_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      int _jspx_eval_c_005fchoose_005f2 = _jspx_th_c_005fchoose_005f2.doStartTag();
      if (_jspx_eval_c_005fchoose_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("				");
          if (_jspx_meth_c_005fwhen_005f3(_jspx_th_c_005fchoose_005f2, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("				");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f2, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("			");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f2);
      _jspx_th_c_005fchoose_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f3 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f3_reused = false;
    try {
      _jspx_th_c_005fwhen_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(49,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tooltipTitle !=null}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f3 = _jspx_th_c_005fwhen_005f3.doStartTag();
      if (_jspx_eval_c_005fwhen_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("					<div class=\"recent-header collection-header\" title=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tooltipTitle}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("				");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f3);
      _jspx_th_c_005fwhen_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f2);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("					<div class=\"recent-header collection-header\">\r\n");
          out.write("				");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f7 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f7_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f7.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(60,25) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setName("title");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(60,25) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f7 = _jspx_th_config_005fgetOption_005f7.doStartTag();
        if (_jspx_th_config_005fgetOption_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f7);
      _jspx_th_config_005fgetOption_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f7, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(63,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${multiselect == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("				<span class=\"fonticon fonticon-open-down\" ></span>\r\n");
          out.write("				<div class=\"button-popup hidden\">\r\n");
          out.write("					<div class=\"action-details-multi-select label-section addEntries\" title=\"");
          if (_jspx_meth_i18n_005fmessage_005f0(_jspx_th_c_005fif_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\">\r\n");
          out.write("					<span class=\"multiselect-fonticon fonticon ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${iconCssFormultipleCompare}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"> </span><span class=\"multiselect-title\"></span>\r\n");
          out.write("					</div>\r\n");
          out.write("				<div class=\"action-details-multi-select label-section clearEntries\" title=\"");
          if (_jspx_meth_i18n_005fmessage_005f1(_jspx_th_c_005fif_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\"\">\r\n");
          out.write("\r\n");
          out.write("					<span class=\"multiselect-fonticon fonticon fonticon-trash\"> </span><span class=\"multiselect-title\"></span>\r\n");
          out.write("				</div>\r\n");
          out.write("				</div>\r\n");
          out.write("			");
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f0 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f0_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f0.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(67,78) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setCode("plma.hits.collection.labels.add-entry");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(67,78) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f0 = _jspx_th_i18n_005fmessage_005f0.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f0);
      _jspx_th_i18n_005fmessage_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f0, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f1 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f1_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f1.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(70,79) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setCode("plma.hits.collection.labels.trashTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(70,79) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f1 = _jspx_th_i18n_005fmessage_005f1.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f1);
      _jspx_th_i18n_005fmessage_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f1, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f2 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f2_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f2.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(76,4) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(76,4) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setCode("recentSearches.delete");
      int[] _jspx_push_body_count_i18n_005fmessage_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f2 = _jspx_th_i18n_005fmessage_005f2.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f2);
      _jspx_th_i18n_005fmessage_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f2, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f3 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f3_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f3.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(77,62) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f3.setCode("plma.hits.collection.labels.trashTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(77,62) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f3.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f3 = _jspx_th_i18n_005fmessage_005f3.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f3);
      _jspx_th_i18n_005fmessage_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f3, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(80,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${loadColletionDetailsView == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("					");
          if (_jspx_meth_render_005ftemplate_005f0(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("				");
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f0);
    try {
      _jspx_th_render_005ftemplate_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f0.setParent(_jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(81,5) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setTemplate("templates/hits.jsp");
      _jspx_th_render_005ftemplate_005f0.setJspBody(new Helper( 0, _jspx_page_context, _jspx_th_render_005ftemplate_005f0, _jspx_push_body_count_widget_005fwidget_005f0));
      _jspx_th_render_005ftemplate_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f0);
    try {
      _jspx_th_render_005fparameter_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f0.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(82,6) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setName("feed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(82,6) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f1);
    try {
      _jspx_th_render_005fparameter_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f1.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(83,6) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setName("iconCss");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(83,6) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collectionDef.iconCss}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f2);
    try {
      _jspx_th_render_005fparameter_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f2.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(84,6) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setName("collectionId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(84,6) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collectionDef.id}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(87,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${displayHitsMode == 'subwidget'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("					");
          if (_jspx_meth_widget_005fforEachSubWidget_005f0(_jspx_th_c_005fif_005f2, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("				");
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fforEachSubWidget_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:forEachSubWidget
    com.exalead.cv360.searchui.view.jspapi.widget.ForEachSubWidgetTag _jspx_th_widget_005fforEachSubWidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.ForEachSubWidgetTag) _005fjspx_005ftagPool_005fwidget_005fforEachSubWidget_0026_005fiteration.get(com.exalead.cv360.searchui.view.jspapi.widget.ForEachSubWidgetTag.class);
    boolean _jspx_th_widget_005fforEachSubWidget_005f0_reused = false;
    try {
      _jspx_th_widget_005fforEachSubWidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fforEachSubWidget_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(88,5) name = iteration type = int reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fforEachSubWidget_005f0.setIteration(1);
      int[] _jspx_push_body_count_widget_005fforEachSubWidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fforEachSubWidget_005f0 = _jspx_th_widget_005fforEachSubWidget_005f0.doStartTag();
        if (_jspx_eval_widget_005fforEachSubWidget_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("						");
            if (_jspx_meth_widget_005fgetUcssId_005f0(_jspx_th_widget_005fforEachSubWidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fforEachSubWidget_005f0))
              return true;
            out.write("\r\n");
            out.write("						<div class=\"hidden wuid ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${wuidSubWidget}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\"></div>\r\n");
            out.write("					");
            int evalDoAfterBody = _jspx_th_widget_005fforEachSubWidget_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_widget_005fforEachSubWidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fforEachSubWidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fforEachSubWidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fforEachSubWidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fforEachSubWidget_0026_005fiteration.reuse(_jspx_th_widget_005fforEachSubWidget_005f0);
      _jspx_th_widget_005fforEachSubWidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fforEachSubWidget_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fforEachSubWidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fgetUcssId_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fforEachSubWidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fforEachSubWidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:getUcssId
    com.exalead.cv360.searchui.view.jspapi.widget.GetUcssIdTag _jspx_th_widget_005fgetUcssId_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.GetUcssIdTag) _005fjspx_005ftagPool_005fwidget_005fgetUcssId_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.widget.GetUcssIdTag.class);
    boolean _jspx_th_widget_005fgetUcssId_005f0_reused = false;
    try {
      _jspx_th_widget_005fgetUcssId_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fgetUcssId_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fforEachSubWidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(89,6) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fgetUcssId_005f0.setVar("wuidSubWidget");
      int[] _jspx_push_body_count_widget_005fgetUcssId_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fgetUcssId_005f0 = _jspx_th_widget_005fgetUcssId_005f0.doStartTag();
        if (_jspx_th_widget_005fgetUcssId_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fgetUcssId_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fgetUcssId_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fgetUcssId_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fgetUcssId_0026_005fvar_005fnobody.reuse(_jspx_th_widget_005fgetUcssId_005f0);
      _jspx_th_widget_005fgetUcssId_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fgetUcssId_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fgetUcssId_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f4 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f4_reused = false;
    try {
      _jspx_th_c_005fwhen_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(96,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mode == 'pin'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f4 = _jspx_th_c_005fwhen_005f4.doStartTag();
      if (_jspx_eval_c_005fwhen_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_widget_005fwidget_005f1(_jspx_th_c_005fwhen_005f4, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_render_005frenderScript_005f0(_jspx_th_c_005fwhen_005f4, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_c_005fset_005f2(_jspx_th_c_005fwhen_005f4, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f4);
      _jspx_th_c_005fwhen_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fwidget_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:widget
    com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag _jspx_th_widget_005fwidget_005f1 = (com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag) _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag.class);
    boolean _jspx_th_widget_005fwidget_005f1_reused = false;
    try {
      _jspx_th_widget_005fwidget_005f1.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fwidget_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(97,2) name = varUcssId type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f1.setVarUcssId("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(97,2) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f1.setExtraCss("collection-modifier pin fonticon");
      int[] _jspx_push_body_count_widget_005fwidget_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fwidget_005f1 = _jspx_th_widget_005fwidget_005f1.doStartTag();
        if (_jspx_th_widget_005fwidget_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fwidget_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fwidget_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fwidget_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss_005fnobody.reuse(_jspx_th_widget_005fwidget_005f1);
      _jspx_th_widget_005fwidget_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fwidget_005f1, _jsp_getInstanceManager(), _jspx_th_widget_005fwidget_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f0_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(98,2) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f0.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f0 = _jspx_th_render_005frenderScript_005f0.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f0);
          }
          do {
            out.write("\r\n");
            out.write("			window.HitsCollectionManager.getCollection('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("').then(function(helper){\r\n");
            out.write("				helper.registerButton({\r\n");
            out.write("					cssSelector: '.");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(".collection-modifier',\r\n");
            out.write("					entry: {\r\n");
            out.write("						id: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entryId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("						url: ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entryUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\r\n");
            out.write("					}\r\n");
            out.write("				});\r\n");
            out.write("			});\r\n");
            out.write("		");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f0);
      _jspx_th_render_005frenderScript_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f2_reused = false;
    try {
      _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(109,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setVar("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(109,2) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(109,2) ''",_jsp_getExpressionFactory().createValueExpression("",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
      if (_jspx_th_c_005fset_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
      _jspx_th_c_005fset_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f5 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f5_reused = false;
    try {
      _jspx_th_c_005fwhen_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(111,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mode == 'listener'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f5 = _jspx_th_c_005fwhen_005f5.doStartTag();
      if (_jspx_eval_c_005fwhen_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_render_005frenderScript_005f1(_jspx_th_c_005fwhen_005f5, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f5);
      _jspx_th_c_005fwhen_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f1 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f1_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f1.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(112,2) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f1.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f1 = _jspx_th_render_005frenderScript_005f1.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f1[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f1);
          }
          do {
            out.write("\r\n");
            out.write("		window.HitsCollectionManager.getCollection('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("').then(function(helper){\r\n");
            out.write("			helper.addEntry({\r\n");
            out.write("				id: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entryId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("				url: ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entryUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\r\n");
            out.write("			});\r\n");
            out.write("		});\r\n");
            out.write("		");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f1[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f1);
      _jspx_th_render_005frenderScript_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f1, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(123,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${loadColletionDetailsView == 'false'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_render_005frenderScript_005f2(_jspx_th_c_005fif_005f3, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f2 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f2_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f2.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(124,1) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f2.setPosition("BEFORE");
      int[] _jspx_push_body_count_render_005frenderScript_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f2 = _jspx_th_render_005frenderScript_005f2.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f2 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f2[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f2);
          }
          do {
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_render_005frenderOnce_005f0(_jspx_th_render_005frenderScript_005f2, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f2))
              return true;
            out.write('\r');
            out.write('\n');
            out.write('	');
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f2.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f2 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f2[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f2);
      _jspx_th_render_005frenderScript_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f2, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderOnce_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderOnce
    com.exalead.cv360.searchui.view.jspapi.render.RenderOnceTag _jspx_th_render_005frenderOnce_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderOnceTag) _005fjspx_005ftagPool_005frender_005frenderOnce_0026_005fid.get(com.exalead.cv360.searchui.view.jspapi.render.RenderOnceTag.class);
    boolean _jspx_th_render_005frenderOnce_005f0_reused = false;
    try {
      _jspx_th_render_005frenderOnce_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderOnce_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(125,2) name = id type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderOnce_005f0.setId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_render_005frenderOnce_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderOnce_005f0 = _jspx_th_render_005frenderOnce_005f0.doStartTag();
        if (_jspx_eval_render_005frenderOnce_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderOnce_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderOnce_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderOnce_005f0);
          }
          do {
            out.write("\r\n");
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_config_005fgetOption_005f8(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_config_005fgetOption_005f9(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_config_005fgetOption_005f10(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_config_005fgetOption_005f11(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_config_005fgetOption_005f12(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_config_005fgetOption_005f13(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_config_005fgetOption_005f14(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_config_005fgetOption_005f15(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_config_005fgetOption_005f16(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_config_005fgetOption_005f17(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_c_005fset_005f3(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_c_005fif_005f4(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_c_005fset_005f5(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fif_005f5(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("		window.HitsCollectionManager.createCollection({\r\n");
            out.write("			uCssId: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("			id:'");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("			store: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collectionDef.store}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("			storeKey: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collectionDef.storekey}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("			storeType: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${storeType}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("			limit: ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${limit}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(",\r\n");
            out.write("			tooltipTitle: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tooltipTitle}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("			minDisplaySize: ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${minDisplaySize}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(",\r\n");
            out.write("			limitStrategy: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collectionDef.limitStrategy}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("			disableAlert: ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${disableAlert}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(",\r\n");
            out.write("			buttonConfig:{\r\n");
            out.write("				entryPresentIcon: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${buttonsDef.entryPresentIcon}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("				entryAbsentIcon: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${buttonsDef.entryAbsentIcon}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("'\r\n");
            out.write("			},\r\n");
            out.write("            labels: {\r\n");
            out.write("            	label: \"");
            if (_jspx_meth_config_005fgetOption_005f18(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("            	");
            if (_jspx_meth_i18n_005fmessage_005f4(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	counter: \"");
            if (_jspx_meth_i18n_005fmessage_005f5(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("            	");
            if (_jspx_meth_i18n_005fmessage_005f6(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	tooltip: \"");
            if (_jspx_meth_i18n_005fmessage_005f7(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("            	");
            if (_jspx_meth_i18n_005fmessage_005f8(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	emptyCollection: \"");
            if (_jspx_meth_i18n_005fmessage_005f9(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("            	");
            if (_jspx_meth_i18n_005fmessage_005f10(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	addEntry: \"");
            if (_jspx_meth_i18n_005fmessage_005f11(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("            	");
            if (_jspx_meth_i18n_005fmessage_005f12(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	removeEntry: \"");
            if (_jspx_meth_i18n_005fmessage_005f13(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("				");
            if (_jspx_meth_i18n_005fmessage_005f14(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	addMultipleEntry: \"");
            if (_jspx_meth_i18n_005fmessage_005f15(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("				");
            if (_jspx_meth_i18n_005fmessage_005f16(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	clearMultipleEntry: \"");
            if (_jspx_meth_i18n_005fmessage_005f17(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("				");
            if (_jspx_meth_i18n_005fmessage_005f18(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	collectionOptions: \"");
            if (_jspx_meth_i18n_005fmessage_005f19(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\"\r\n");
            out.write("             },\r\n");
            out.write("            alerts: {\r\n");
            out.write("            	");
            if (_jspx_meth_i18n_005fmessage_005f20(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	add: \"");
            if (_jspx_meth_i18n_005fmessage_005f21(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("            	");
            if (_jspx_meth_i18n_005fmessage_005f22(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	remove: \"");
            if (_jspx_meth_i18n_005fmessage_005f23(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("            	");
            if (_jspx_meth_i18n_005fmessage_005f24(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	removeAll: \"");
            if (_jspx_meth_i18n_005fmessage_005f25(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("            	");
            if (_jspx_meth_i18n_005fmessage_005f26(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	limitReached: \"");
            if (_jspx_meth_i18n_005fmessage_005f27(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("            	");
            if (_jspx_meth_i18n_005fmessage_005f28(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	removeOld: \"");
            if (_jspx_meth_i18n_005fmessage_005f29(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("            	");
            if (_jspx_meth_i18n_005fmessage_005f30(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\r\n");
            out.write("            	minDisplaySize: \"");
            if (_jspx_meth_i18n_005fmessage_005f31(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\"\r\n");
            out.write("            },\r\n");
            out.write("			detailsView: {\r\n");
            out.write("				activateToggle: ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${activateToggle}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(",\r\n");
            out.write("				displayHitsMode: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${displayHitsMode}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("				metaForId: '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${metaForId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',\r\n");
            out.write("				");
            if (_jspx_meth_c_005fchoose_005f3(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("				\r\n");
            out.write("			}				\r\n");
            out.write("		});\r\n");
            out.write("		");
            int evalDoAfterBody = _jspx_th_render_005frenderOnce_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderOnce_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderOnce_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderOnce_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderOnce_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderOnce_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderOnce_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderOnce_0026_005fid.reuse(_jspx_th_render_005frenderOnce_005f0);
      _jspx_th_render_005frenderOnce_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderOnce_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderOnce_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f8 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f8_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f8.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(127,2) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setName("maxSearches");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(127,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setVar("limit");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(127,2) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setDefaultValue("10");
      int[] _jspx_push_body_count_config_005fgetOption_005f8 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f8 = _jspx_th_config_005fgetOption_005f8.doStartTag();
        if (_jspx_th_config_005fgetOption_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f8[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f8.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f8.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f8);
      _jspx_th_config_005fgetOption_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f8, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f9 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f9_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f9.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(128,2) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setName("elemPerPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(128,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setVar("elemPerPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(128,2) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setDefaultValue("4");
      int[] _jspx_push_body_count_config_005fgetOption_005f9 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f9 = _jspx_th_config_005fgetOption_005f9.doStartTag();
        if (_jspx_th_config_005fgetOption_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f9[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f9.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f9.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f9);
      _jspx_th_config_005fgetOption_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f9, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f10 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f10_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f10.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f10.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(129,2) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setName("activateToggle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(129,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setVar("activateToggle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(129,2) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f10 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f10 = _jspx_th_config_005fgetOption_005f10.doStartTag();
        if (_jspx_th_config_005fgetOption_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f10[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f10.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f10.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f10);
      _jspx_th_config_005fgetOption_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f10, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f11 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f11_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f11.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f11.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(130,2) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setName("showInPopup");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(130,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setVar("showInPopup");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(130,2) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f11 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f11 = _jspx_th_config_005fgetOption_005f11.doStartTag();
        if (_jspx_th_config_005fgetOption_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f11[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f11.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f11.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f11);
      _jspx_th_config_005fgetOption_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f11, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f12 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f12_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f12.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f12.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(131,2) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setName("onInit");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(131,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setVar("onInit");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(131,2) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setDefaultValue("undefined");
      int[] _jspx_push_body_count_config_005fgetOption_005f12 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f12 = _jspx_th_config_005fgetOption_005f12.doStartTag();
        if (_jspx_th_config_005fgetOption_005f12.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f12[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f12.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f12.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f12);
      _jspx_th_config_005fgetOption_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f12, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f13 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f13_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f13.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f13.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(132,2) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setName("onDbUpdate");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(132,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setVar("onDbUpdate");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(132,2) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setDefaultValue("undefined");
      int[] _jspx_push_body_count_config_005fgetOption_005f13 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f13 = _jspx_th_config_005fgetOption_005f13.doStartTag();
        if (_jspx_th_config_005fgetOption_005f13.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f13[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f13.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f13.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f13);
      _jspx_th_config_005fgetOption_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f13, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f14 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f14_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f14.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f14.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(133,2) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setName("onShow");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(133,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setVar("onShow");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(133,2) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setDefaultValue("undefined");
      int[] _jspx_push_body_count_config_005fgetOption_005f14 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f14 = _jspx_th_config_005fgetOption_005f14.doStartTag();
        if (_jspx_th_config_005fgetOption_005f14.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f14[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f14.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f14.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f14);
      _jspx_th_config_005fgetOption_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f14, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f15(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f15 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f15_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f15.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f15.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(134,2) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f15.setName("onHide");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(134,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f15.setVar("onHide");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(134,2) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f15.setDefaultValue("undefined");
      int[] _jspx_push_body_count_config_005fgetOption_005f15 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f15 = _jspx_th_config_005fgetOption_005f15.doStartTag();
        if (_jspx_th_config_005fgetOption_005f15.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f15[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f15.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f15.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f15);
      _jspx_th_config_005fgetOption_005f15_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f15, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f15_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f16(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f16 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f16_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f16.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f16.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(135,2) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f16.setName("onAfterShow");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(135,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f16.setVar("onAfterShow");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(135,2) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f16.setDefaultValue("undefined");
      int[] _jspx_push_body_count_config_005fgetOption_005f16 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f16 = _jspx_th_config_005fgetOption_005f16.doStartTag();
        if (_jspx_th_config_005fgetOption_005f16.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f16[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f16.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f16.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f16);
      _jspx_th_config_005fgetOption_005f16_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f16, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f16_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f17(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f17 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f17_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f17.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f17.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(136,2) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f17.setName("disableAlert");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(136,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f17.setVar("disableAlert");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(136,2) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f17.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f17 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f17 = _jspx_th_config_005fgetOption_005f17.doStartTag();
        if (_jspx_th_config_005fgetOption_005f17.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f17[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f17.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f17.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f17);
      _jspx_th_config_005fgetOption_005f17_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f17, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f17_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f3_reused = false;
    try {
      _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(137,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setVar("minDisplaySize");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(137,2) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(137,2) '1'",_jsp_getExpressionFactory().createValueExpression("1",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
      if (_jspx_th_c_005fset_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
      _jspx_th_c_005fset_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f4_reused = false;
    try {
      _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(138,2) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty collectionDef.minDisplaySize}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
      if (_jspx_eval_c_005fif_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("			");
          if (_jspx_meth_c_005fset_005f4(_jspx_th_c_005fif_005f4, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
            return true;
          out.write("\r\n");
          out.write("		");
          int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      _jspx_th_c_005fif_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f4_reused = false;
    try {
      _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(139,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setVar("minDisplaySize");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(139,3) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(139,3) '${collectionDef.minDisplaySize}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${collectionDef.minDisplaySize}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
      if (_jspx_th_c_005fset_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
      _jspx_th_c_005fset_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f5 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f5_reused = false;
    try {
      _jspx_th_c_005fset_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(141,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setVar("storeType");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(141,2) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(141,2) 'user'",_jsp_getExpressionFactory().createValueExpression("user",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f5 = _jspx_th_c_005fset_005f5.doStartTag();
      if (_jspx_th_c_005fset_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f5);
      _jspx_th_c_005fset_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f5 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f5_reused = false;
    try {
      _jspx_th_c_005fif_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(142,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty collectionDef.storeType}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f5 = _jspx_th_c_005fif_005f5.doStartTag();
      if (_jspx_eval_c_005fif_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("			");
          if (_jspx_meth_c_005fset_005f6(_jspx_th_c_005fif_005f5, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f5);
      _jspx_th_c_005fif_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f6 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f6_reused = false;
    try {
      _jspx_th_c_005fset_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(143,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setVar("storeType");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(143,3) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(143,3) '${collectionDef.storeType}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${collectionDef.storeType}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f6 = _jspx_th_c_005fset_005f6.doStartTag();
      if (_jspx_th_c_005fset_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f6);
      _jspx_th_c_005fset_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f18(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f18 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f18_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f18.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f18.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(161,21) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f18.setName("title");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(161,21) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f18.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f18 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f18 = _jspx_th_config_005fgetOption_005f18.doStartTag();
        if (_jspx_th_config_005fgetOption_005f18.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f18[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f18.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f18.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f18);
      _jspx_th_config_005fgetOption_005f18_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f18, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f18_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f4 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f4_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f4.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(162,13) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f4.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(162,13) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f4.setCode("plma.hits.collection.labels.counter");
      int[] _jspx_push_body_count_i18n_005fmessage_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f4 = _jspx_th_i18n_005fmessage_005f4.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f4);
      _jspx_th_i18n_005fmessage_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f4, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f5 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f5_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f5.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(163,23) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f5.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.labels.counter.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(163,23) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f5.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f5 = _jspx_th_i18n_005fmessage_005f5.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f5);
      _jspx_th_i18n_005fmessage_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f5, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f6 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f6_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f6.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(164,13) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f6.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(164,13) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f6.setCode("plma.hits.collection.labels.tooltip");
      int[] _jspx_push_body_count_i18n_005fmessage_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f6 = _jspx_th_i18n_005fmessage_005f6.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f6);
      _jspx_th_i18n_005fmessage_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f6, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f7 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f7_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f7.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(165,23) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f7.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.labels.tooltip.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(165,23) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f7.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f7 = _jspx_th_i18n_005fmessage_005f7.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f7);
      _jspx_th_i18n_005fmessage_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f7, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f8 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f8_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f8.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(166,13) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f8.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(166,13) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f8.setCode("plma.hits.collection.labels.emptyCollection");
      int[] _jspx_push_body_count_i18n_005fmessage_005f8 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f8 = _jspx_th_i18n_005fmessage_005f8.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f8[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f8.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f8.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f8);
      _jspx_th_i18n_005fmessage_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f8, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f9 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f9_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f9.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(167,31) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f9.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.labels.emptyCollection.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(167,31) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f9.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f9 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f9 = _jspx_th_i18n_005fmessage_005f9.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f9[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f9.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f9.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f9);
      _jspx_th_i18n_005fmessage_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f9, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f10 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f10_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f10.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f10.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(168,13) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f10.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(168,13) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f10.setCode("plma.hits.collection.add-entry");
      int[] _jspx_push_body_count_i18n_005fmessage_005f10 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f10 = _jspx_th_i18n_005fmessage_005f10.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f10[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f10.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f10.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f10);
      _jspx_th_i18n_005fmessage_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f10, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f11 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f11_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f11.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f11.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(169,24) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f11.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.add-entry.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(169,24) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f11.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f11 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f11 = _jspx_th_i18n_005fmessage_005f11.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f11[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f11.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f11.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f11);
      _jspx_th_i18n_005fmessage_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f11, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f12 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f12_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f12.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f12.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(170,13) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f12.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(170,13) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f12.setCode("plma.hits.collection.remove-entry");
      int[] _jspx_push_body_count_i18n_005fmessage_005f12 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f12 = _jspx_th_i18n_005fmessage_005f12.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f12.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f12[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f12.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f12.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f12);
      _jspx_th_i18n_005fmessage_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f12, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f13 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f13_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f13.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f13.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(171,27) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f13.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.remove-entry.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(171,27) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f13.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f13 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f13 = _jspx_th_i18n_005fmessage_005f13.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f13.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f13[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f13.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f13.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f13);
      _jspx_th_i18n_005fmessage_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f13, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f14 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f14_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f14.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f14.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(172,4) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f14.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(172,4) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f14.setCode("plma.hits.collection.labels.add-multiple-entry");
      int[] _jspx_push_body_count_i18n_005fmessage_005f14 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f14 = _jspx_th_i18n_005fmessage_005f14.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f14.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f14[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f14.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f14.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f14);
      _jspx_th_i18n_005fmessage_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f14, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f15(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f15 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f15_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f15.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f15.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(173,32) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f15.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.labels.add-multiple-entry.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(173,32) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f15.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f15 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f15 = _jspx_th_i18n_005fmessage_005f15.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f15.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f15[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f15.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f15.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f15);
      _jspx_th_i18n_005fmessage_005f15_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f15, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f15_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f16(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f16 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f16_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f16.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f16.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(174,4) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f16.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(174,4) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f16.setCode("plma.hits.collection.labels.remove-multiple-entry");
      int[] _jspx_push_body_count_i18n_005fmessage_005f16 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f16 = _jspx_th_i18n_005fmessage_005f16.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f16.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f16[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f16.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f16.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f16);
      _jspx_th_i18n_005fmessage_005f16_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f16, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f16_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f17(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f17 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f17_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f17.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f17.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(175,34) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f17.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.labels.remove-multiple-entry.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(175,34) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f17.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f17 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f17 = _jspx_th_i18n_005fmessage_005f17.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f17.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f17[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f17.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f17.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f17);
      _jspx_th_i18n_005fmessage_005f17_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f17, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f17_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f18(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f18 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f18_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f18.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f18.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(176,4) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f18.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(176,4) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f18.setCode("plma.hits.collection.labels.open-collection-option");
      int[] _jspx_push_body_count_i18n_005fmessage_005f18 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f18 = _jspx_th_i18n_005fmessage_005f18.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f18.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f18[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f18.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f18.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f18);
      _jspx_th_i18n_005fmessage_005f18_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f18, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f18_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f19(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f19 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f19_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f19.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f19.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(177,33) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f19.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.labels.open-collection-option.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(177,33) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f19.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f19 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f19 = _jspx_th_i18n_005fmessage_005f19.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f19.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f19[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f19.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f19.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f19);
      _jspx_th_i18n_005fmessage_005f19_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f19, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f19_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f20(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f20 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f20_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f20.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f20.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(180,13) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f20.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(180,13) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f20.setCode("plma.hits.collection.alerts.add");
      int[] _jspx_push_body_count_i18n_005fmessage_005f20 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f20 = _jspx_th_i18n_005fmessage_005f20.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f20.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f20[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f20.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f20.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f20);
      _jspx_th_i18n_005fmessage_005f20_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f20, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f20_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f21(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f21 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f21_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f21.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f21.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(181,19) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f21.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.alerts.add.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(181,19) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f21.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f21 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f21 = _jspx_th_i18n_005fmessage_005f21.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f21.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f21[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f21.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f21.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f21);
      _jspx_th_i18n_005fmessage_005f21_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f21, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f21_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f22(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f22 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f22_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f22.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f22.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(182,13) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f22.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(182,13) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f22.setCode("plma.hits.collection.alerts.remove");
      int[] _jspx_push_body_count_i18n_005fmessage_005f22 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f22 = _jspx_th_i18n_005fmessage_005f22.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f22.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f22[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f22.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f22.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f22);
      _jspx_th_i18n_005fmessage_005f22_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f22, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f22_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f23(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f23 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f23_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f23.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f23.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(183,22) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f23.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.alerts.remove.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(183,22) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f23.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f23 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f23 = _jspx_th_i18n_005fmessage_005f23.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f23.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f23[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f23.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f23.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f23);
      _jspx_th_i18n_005fmessage_005f23_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f23, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f23_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f24(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f24 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f24_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f24.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f24.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(184,13) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f24.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(184,13) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f24.setCode("plma.hits.collection.alerts.removeAll");
      int[] _jspx_push_body_count_i18n_005fmessage_005f24 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f24 = _jspx_th_i18n_005fmessage_005f24.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f24.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f24[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f24.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f24.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f24);
      _jspx_th_i18n_005fmessage_005f24_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f24, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f24_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f25(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f25 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f25_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f25.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f25.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(185,25) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f25.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.alerts.removeAll.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(185,25) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f25.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f25 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f25 = _jspx_th_i18n_005fmessage_005f25.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f25.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f25[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f25.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f25.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f25);
      _jspx_th_i18n_005fmessage_005f25_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f25, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f25_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f26(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f26 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f26_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f26.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f26.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(186,13) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f26.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(186,13) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f26.setCode("plma.hits.collection.alerts.limitReached");
      int[] _jspx_push_body_count_i18n_005fmessage_005f26 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f26 = _jspx_th_i18n_005fmessage_005f26.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f26.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f26[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f26.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f26.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f26);
      _jspx_th_i18n_005fmessage_005f26_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f26, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f26_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f27(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f27 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f27_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f27.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f27.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(187,28) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f27.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.alerts.limitReached.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(187,28) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f27.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f27 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f27 = _jspx_th_i18n_005fmessage_005f27.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f27.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f27[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f27.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f27.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f27);
      _jspx_th_i18n_005fmessage_005f27_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f27, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f27_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f28(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f28 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f28_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f28.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f28.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(188,13) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f28.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(188,13) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f28.setCode("plma.hits.collection.alerts.removeOld");
      int[] _jspx_push_body_count_i18n_005fmessage_005f28 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f28 = _jspx_th_i18n_005fmessage_005f28.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f28.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f28[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f28.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f28.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f28);
      _jspx_th_i18n_005fmessage_005f28_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f28, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f28_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f29(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f29 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f29_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f29.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f29.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(189,25) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f29.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.alerts.removeOld.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(189,25) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f29.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f29 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f29 = _jspx_th_i18n_005fmessage_005f29.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f29.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f29[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f29.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f29.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f29);
      _jspx_th_i18n_005fmessage_005f29_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f29, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f29_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f30(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f30 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f30_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f30.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f30.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(190,13) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f30.setVar("defaultText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(190,13) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f30.setCode("plma.hits.collection.alerts.minDisplaySize");
      int[] _jspx_push_body_count_i18n_005fmessage_005f30 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f30 = _jspx_th_i18n_005fmessage_005f30.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f30.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f30[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f30.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f30.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f30);
      _jspx_th_i18n_005fmessage_005f30_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f30, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f30_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f31(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f31 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f31_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f31.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f31.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(191,30) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f31.setCode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("plma.hits.collection.alerts.minDisplaySize.${collectionDef.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(191,30) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f31.setText((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${defaultText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f31 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f31 = _jspx_th_i18n_005fmessage_005f31.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f31.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f31[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f31.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f31.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f31);
      _jspx_th_i18n_005fmessage_005f31_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f31, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f31_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f3 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f3_reused = false;
    try {
      _jspx_th_c_005fchoose_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      int _jspx_eval_c_005fchoose_005f3 = _jspx_th_c_005fchoose_005f3.doStartTag();
      if (_jspx_eval_c_005fchoose_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("					");
          if (_jspx_meth_c_005fwhen_005f6(_jspx_th_c_005fchoose_005f3, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
            return true;
          out.write("\r\n");
          out.write("					");
          if (_jspx_meth_c_005fwhen_005f7(_jspx_th_c_005fchoose_005f3, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
            return true;
          out.write("\r\n");
          out.write("					");
          if (_jspx_meth_c_005fwhen_005f8(_jspx_th_c_005fchoose_005f3, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
            return true;
          out.write("\r\n");
          out.write("				");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f3);
      _jspx_th_c_005fchoose_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f6 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f6_reused = false;
    try {
      _jspx_th_c_005fwhen_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(198,5) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f6.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${displayHitsMode == 'default'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f6 = _jspx_th_c_005fwhen_005f6.doStartTag();
      if (_jspx_eval_c_005fwhen_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("						showInPopup: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${showInPopup}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(",\r\n");
          out.write("						feedName: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("						elemPerPage: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${elemPerPage}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("						\r\n");
          out.write("					");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f6);
      _jspx_th_c_005fwhen_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f7 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f7_reused = false;
    try {
      _jspx_th_c_005fwhen_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(203,5) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f7.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${displayHitsMode == 'subwidget'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f7 = _jspx_th_c_005fwhen_005f7.doStartTag();
      if (_jspx_eval_c_005fwhen_005f7 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("						showInPopup: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${showInPopup}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(",\r\n");
          out.write("						feedName: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("						containerCss: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${wuidSubWidget}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("						onAfterShow: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${onAfterShow}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(",\r\n");
          out.write("						onHide: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${onHide}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(",\r\n");
          out.write("					");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f7.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f7);
      _jspx_th_c_005fwhen_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f8 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f8_reused = false;
    try {
      _jspx_th_c_005fwhen_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/widget.jsp(210,5) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f8.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${displayHitsMode == 'custom'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f8 = _jspx_th_c_005fwhen_005f8.doStartTag();
      if (_jspx_eval_c_005fwhen_005f8 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("						showInPopup: false,\r\n");
          out.write("						onInit: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${onInit}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(",\r\n");
          out.write("						onShow: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${onShow}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(",\r\n");
          out.write("						onHide: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${onHide}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(",\r\n");
          out.write("						onDbUpdate: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${onDbUpdate}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\r\n");
          out.write("					");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f8.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f8);
      _jspx_th_c_005fwhen_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f8_reused);
    }
    return false;
  }

  private class Helper
      extends org.apache.jasper.runtime.JspFragmentHelper
  {
    private jakarta.servlet.jsp.tagext.JspTag _jspx_parent;
    private int[] _jspx_push_body_count;

    public Helper( int discriminator, jakarta.servlet.jsp.JspContext jspContext, jakarta.servlet.jsp.tagext.JspTag _jspx_parent, int[] _jspx_push_body_count ) {
      super( discriminator, jspContext, _jspx_parent );
      this._jspx_parent = _jspx_parent;
      this._jspx_push_body_count = _jspx_push_body_count;
    }
    public boolean invoke0( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("						");
      if (_jspx_meth_render_005fparameter_005f0(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("						");
      if (_jspx_meth_render_005fparameter_005f1(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("						");
      if (_jspx_meth_render_005fparameter_005f2(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("					");
      return false;
    }
    public void invoke( java.io.Writer writer )
      throws jakarta.servlet.jsp.JspException
    {
      jakarta.servlet.jsp.JspWriter out = null;
      if( writer != null ) {
        out = this.jspContext.pushBody(writer);
      } else {
        out = this.jspContext.getOut();
      }
      try {
        Object _jspx_saved_JspContext = this.jspContext.getELContext().getContext(jakarta.servlet.jsp.JspContext.class);
        this.jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,this.jspContext);
        switch( this.discriminator ) {
          case 0:
            invoke0( out );
            break;
        }
        jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,_jspx_saved_JspContext);
      }
      catch( java.lang.Throwable e ) {
        if (e instanceof jakarta.servlet.jsp.SkipPageException)
            throw (jakarta.servlet.jsp.SkipPageException) e;
        throw new jakarta.servlet.jsp.JspException( e );
      }
      finally {
        if( writer != null ) {
          this.jspContext.popBody();
        }
      }
    }
  }
}
