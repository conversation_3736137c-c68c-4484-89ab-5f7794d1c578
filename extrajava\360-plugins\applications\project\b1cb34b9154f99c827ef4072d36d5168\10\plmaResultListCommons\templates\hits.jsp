<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import parameters="widget,feeds,uCssId" />
<render:import parameters="columnsConfig" ignore="true"/>
<render:import parameters="isGridTable, doTranspose, isCompare" ignore="true"/>

<c:choose>
    <c:when test="${isCompare}">
        <c:set var="defaultTemplateValue" value="templates/comparetable.jsp"/>
        <c:set var="defaultWidgetValue" value="plmaResultCompareTable"/>
    </c:when>
    <c:otherwise>
        <c:set var="defaultTemplateValue" value="${isGridTable ? 'templates/gridtable.jsp' : 'templates/tile.jsp'}"/>
        <c:set var="defaultWidgetValue" value="${isGridTable ? 'plmaResultGridTable' : 'plmaResultTiles'}"/>
    </c:otherwise>
</c:choose>

<config:getOption var="customJspPathHit" name="customJspPathHit"/>
<config:getOption var="defaultJspPathHit" name="defaultJspPathHit" defaultValue="${defaultTemplateValue}"/>

<search:forEachFeed feeds="${feeds}" var="feed" varStatus="accessFeedsStatus">
    <search:forEachEntry var="entry" feed="${feed}" varStatus="entryStatus">
    
        <%-- Retrieve hit properties  --%>
        <config:getOption var="hitUrl" name="hitUrl" entry="${entry}" feed="${feed}" />
        <config:getOption var="hitTitle" name="hitTitle" entry="${entry}" feed="${feed}" />

        <c:choose>
            <c:when test="${empty hitTitle}">
                <search:getMeta var="titleMeta" metaName="title" entry="${entry}"/>
                <c:set var="hitTitle" value="${plma:getHighlightedValues(titleMeta, true, ', ' )}"/>
                <c:set var="hitTitleTooltip" value="${plma:getRawValues(titleMeta, true, ', ' )}"/>
            </c:when>
            <c:otherwise>
                <c:set var="hitTitle" value="${entry.title}"/>
            </c:otherwise>
        </c:choose>
    
        <%-- Renders a custom view for this hit --%>
        <render:template template="${customJspPathHit}" defaultTemplate="${defaultJspPathHit}" widget="${defaultWidgetValue}">
            <render:parameter name="accessFeeds" value="${feeds}" />
            <render:parameter name="feed" value="${feed}" />
            <render:parameter name="uCssId" value="${uCssId}"/>
            <render:parameter name="entry" value="${entry}" />
            <render:parameter name="widget" value="${widget}" />
            <c:if test="${hitUrl != null}">
                <render:parameter name="hitUrl" value="${hitUrl}" />
            </c:if>
            <c:if test="${hitTitle != null}">
                <render:parameter name="hitTitle" value="${hitTitle}" />
            </c:if>
            <c:if test="${hitTitleTooltip != null}">
                <render:parameter name="hitTitleTooltip" value="${hitTitleTooltip}" />
            </c:if>
            <render:parameter name="accessFeedsStatus" value="${accessFeedsStatus}" />
            <render:parameter name="entryStatus" value="${entryStatus}" />
			<render:parameter name="columnsConfig" value="${columnsConfig}"/>
            <render:parameter name="doTranspose" value="${doTranspose}"/>
        </render:template>

<%--        TODO activate when OK--%>
<%--        <render:template template="templates/select-api.jsp" />--%>
    </search:forEachEntry>
</search:forEachFeed>