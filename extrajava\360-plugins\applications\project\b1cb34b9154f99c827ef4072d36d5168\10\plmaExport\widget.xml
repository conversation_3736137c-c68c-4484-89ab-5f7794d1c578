<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA export" group="PLM Analytics/Layout" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>Export widget to export data in streaming (recommended) or feed mode .</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/plmaButton/images/preview.png" alt="PLMA Button" />
        ]]>
	</Preview>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/exportButtonHandler.js" />
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ONE" />
	<SupportI18N supported="true">
		<JsKeys>
		</JsKeys> 
	</SupportI18N>
	
	<OptionsGroup name="General">
		<Option id="iconCss" name="Icon CSS" >
			<Description>Specifies the CSS class name of the icon added to the button. If empty, no icon is displayed.</Description>
		</Option>
		<Option id="label" name="Label" isEvaluated="true" arity="ONE">
			<Description>Specifies the label of the button. Briefly explains the purpose of the button (3 words max).</Description>
		</Option>
		<Option id="showLabel" name="Show label" arity="ONE">
			<Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>

		<Option id="numHits" name="Hits limit" arity="ONE">
			<Description>
				(Only for &lt;b&gt;Hits&lt;/b&gt; mode) The maximum number of hits to export. Set to &lt;code&gt;-1&lt;/code&gt; to set no limit on the export.&lt;br/&gt;
				&lt;b&gt;WARNING&lt;/b&gt;: exporting a large set of results could take a while. It is highly recommended to use the
				&lt;b&gt;Search API&lt;/b&gt; mode for high number of hits, as it allows streaming results.
			</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="columnsConfig" name="ResultList ID" arity="ONE">
			<Description>Columns external config ID, export</Description>
		</Option>
		<Option id="exportMode" name="Export mode" arity="ONE">
			<Description>
				(Only for &lt;b&gt;Hits&lt;/b&gt; mode) The underlying API used to export the data. Only used in &lt;b&gt;Hits&lt;/b&gt; mode (in &lt;b&gt;Synthesis&lt;/b&gt; mode, the data is fully loaded).&lt;br/&gt;
				&lt;b&gt;Access API&lt;/b&gt; mode will execute the feed and attached Access triggers to get the data, querying page after page to get the desired number of results.&lt;br/&gt;
				&lt;b&gt;Search API&lt;/b&gt; mode will execute the provided query to get the data, and stream the results from the index without needing to paginate. It is more efficient than the Access API.&lt;br/&gt;
				&lt;b&gt;WARNINGS&lt;/b&gt;
				&lt;ul&gt;
				&lt;li&gt;
				In &lt;b&gt;Search API&lt;/b&gt; mode, the row order in the exported file is not guaranteed to be the same as displayed in the table. This is because this mode uses
				the streaming option of the Search API, which allows fast download but prevents from sorting documents. Make sure to adapt the hits limit so your users have all the data they need.
				&lt;/li&gt;
				&lt;li&gt;
				In &lt;b&gt;Search API&lt;/b&gt; mode, MEL expressions are evaluated from the &lt;b&gt;entry&lt;/b&gt; scope only. In the Data tab, when configuring column values, the MEL expressions should
				not use any other scopes as the MEL evaluator will not be aware of them (such as &lt;b&gt;feeds&lt;/b&gt;).
				&lt;/li&gt;
				&lt;li&gt;
				The &lt;b&gt;Search API&lt;/b&gt; mode only works with CloudView feeds.
				&lt;/li&gt;
				&lt;/ul&gt;
			</Description>
			<Values>
				<Value>Search API</Value>
				<Value>Access API</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['Access API'], showOptions:['exportPerPage']})</Display>
			</Functions>
		</Option>
		<Option id="exportPerPage" name="Override 'per_page'" arity="ZERO_OR_ONE">
			<Description>
				When using the &lt;b&gt;Access API&lt;/b&gt; export mode, you can override the &lt;code&gt;per_page&lt;/code&gt; parameter of the feed. Setting a higher value will allow to produce less queries to the index.
				Set to &lt;code&gt;-1&lt;/code&gt; to fetch all hits in one query (the &lt;b&gt;Hits limit&lt;/b&gt; still applies). Leave empty to use the value set on the feed.
			</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="fileName" name="File name" isEvaluated="true" >
			<Description>The name of the created CSV file. Defaults to 'export.csv'</Description>
		</Option>
		<Option id="exportEncoding" name="Encoding" arity="ZERO_OR_ONE" >
			<Description>You can choose an encoding for the exported CSV file. Defaults to UTF-8.</Description>
		</Option>
		<Option id="exportSeparator" name="Separator" arity="ZERO_OR_ONE">
			<Description>Character to use as a separator in the exported CSV file. Defaults to &lt;b&gt;;&lt;/b&gt;.</Description>
		</Option>
		<Option id="recordDelimiter" name="Record Delimiter">
			<Description>
				Character that is used to separate consecutive records in the output file.&lt;br/&gt;Default option will take the value based on OS.&lt;br/&gt;
				&lt;ul&gt;
				&lt;li&gt;Unix -> LF&lt;/li&gt;
				&lt;li&gt;Windows -> CR + LF&lt;/li&gt;
				&lt;li&gt;Mac -> CR&lt;/li&gt;
				&lt;/ul&gt;
			</Description>
			<Values>
				<Value>Default</Value>
				<Value>LF</Value>
				<Value>CR+LF</Value>
				<Value>CR</Value>
			</Values>
		</Option>
		<Option id="addBOM" name="Add BOM">
			<Description>Whether to add the byte order mark (BOM) at the start of the file.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="exportRawValues" name="Export raw values">
			<Description>Export raw values or I18N values (applicable for facets and headers).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="iconCss">fonticon fonticon-export-multiple </DefaultValue>
		<DefaultValue name="showLabel">false</DefaultValue>
		<DefaultValue name="label">Export data</DefaultValue>
		<DefaultValue name="numHits">1000</DefaultValue>
		<DefaultValue name="exportMode">Search API</DefaultValue>
		<DefaultValue name="recordDelimiter">Default</DefaultValue>
		<DefaultValue name="fileName">export</DefaultValue>
		<DefaultValue name="addBOM">true</DefaultValue>
		<DefaultValue name="exportRawValues">false</DefaultValue>
	</DefaultValues>
</Widget>
