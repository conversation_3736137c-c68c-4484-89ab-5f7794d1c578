/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:31 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.activeFilters;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class widget_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_0;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_1;

static {
  _jspx_fnmap_0= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:toLowerCase", org.apache.taglibs.standard.functions.Functions.class, "toLowerCase", new Class[] {java.lang.String.class});
  _jspx_fnmap_1= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:length", org.apache.taglibs.standard.functions.Functions.class, "length", new Class[] {java.lang.Object.class});
}

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(15);
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/widget.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/i18n.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/list.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/url.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/request.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/map.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdoEval_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fmap_005fnew_0026_005fvar_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005furl_005furl_0026_005fvar_005fkeepQueryString_005fforceRefineOn;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fheader_0026_005fextraCss;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fcontent;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fGetChartboardPageSecurity_0026_005fwriteRights_005freadRights_005fpageId_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvar_005ffeeds;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowState_005froot_005fiterationMode;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvarStatus_005fvar_005ffeeds;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fnobody;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdoEval_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fmap_005fnew_0026_005fvar_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005furl_005furl_0026_005fvar_005fkeepQueryString_005fforceRefineOn = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fheader_0026_005fextraCss = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fcontent = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fGetChartboardPageSecurity_0026_005fwriteRights_005freadRights_005fpageId_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvar_005ffeeds = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowState_005froot_005fiterationMode = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvarStatus_005fvar_005ffeeds = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdoEval_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fmap_005fnew_0026_005fvar_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.release();
    _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fnobody.release();
    _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.release();
    _005fjspx_005ftagPool_005furl_005furl_0026_005fvar_005fkeepQueryString_005fforceRefineOn.release();
    _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody.release();
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fwidget_005fheader_0026_005fextraCss.release();
    _005fjspx_005ftagPool_005fwidget_005fcontent.release();
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.release();
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fGetChartboardPageSecurity_0026_005fwriteRights_005freadRights_005fpageId_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvar_005ffeeds.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowState_005froot_005fiterationMode.release();
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvarStatus_005fvar_005ffeeds.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fnobody.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f4(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f5(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f6(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f7(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptionsComposite_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptionsComposite_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptionsComposite_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptionsComposite_005f3(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f8(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_plma_005fgetConstantValueFromNameTag_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fif_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fset_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fforEach_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fforEach_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fif_005f3(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_list_005fnew_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f6(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fif_005f7(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(15,0) name = varWidget type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarWidget("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(15,0) name = varFeeds type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarFeeds("feeds");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdoEval_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(17,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setName("title");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(17,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setVar("title");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(17,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setDefaultValue("");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(17,0) name = doEval type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setDoEval(true);
      int[] _jspx_push_body_count_config_005fgetOption_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f0 = _jspx_th_config_005fgetOption_005f0.doStartTag();
        if (_jspx_th_config_005fgetOption_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdoEval_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f0);
      _jspx_th_config_005fgetOption_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(18,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setName("displaySavedFilter");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(18,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setVar("displaySavedFilter");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(18,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f1 = _jspx_th_config_005fgetOption_005f1.doStartTag();
        if (_jspx_th_config_005fgetOption_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f1);
      _jspx_th_config_005fgetOption_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(19,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setName("showReset");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(19,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setVar("showReset");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(19,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setDefaultValue("true");
      int[] _jspx_push_body_count_config_005fgetOption_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f2 = _jspx_th_config_005fgetOption_005f2.doStartTag();
        if (_jspx_th_config_005fgetOption_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f2);
      _jspx_th_config_005fgetOption_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f3_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(20,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setName("hideWidget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(20,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setVar("hideWidget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(20,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setDefaultValue("true");
      int[] _jspx_push_body_count_config_005fgetOption_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f3 = _jspx_th_config_005fgetOption_005f3.doStartTag();
        if (_jspx_th_config_005fgetOption_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f3);
      _jspx_th_config_005fgetOption_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f4 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f4_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f4.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(21,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setName("displayMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(21,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setVar("displayMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(21,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setDefaultValue("Compact");
      int[] _jspx_push_body_count_config_005fgetOption_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f4 = _jspx_th_config_005fgetOption_005f4.doStartTag();
        if (_jspx_th_config_005fgetOption_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f4);
      _jspx_th_config_005fgetOption_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f4, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f5(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f5 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f5_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f5.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f5.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(22,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setVar("searchParameter");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(22,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setName("searchParameter");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(22,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f5 = _jspx_th_config_005fgetOption_005f5.doStartTag();
        if (_jspx_th_config_005fgetOption_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f5);
      _jspx_th_config_005fgetOption_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f5, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f6(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f6 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f6_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f6.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f6.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(23,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setVar("feedsList");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(23,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setName("feedsList");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(23,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f6 = _jspx_th_config_005fgetOption_005f6.doStartTag();
        if (_jspx_th_config_005fgetOption_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f6);
      _jspx_th_config_005fgetOption_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f6, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f7(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f7 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f7_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f7.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f7.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(24,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setName("enableSuggest");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(24,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setVar("enableSuggest");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(24,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f7 = _jspx_th_config_005fgetOption_005f7.doStartTag();
        if (_jspx_th_config_005fgetOption_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f7);
      _jspx_th_config_005fgetOption_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f7, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(25,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setName("customLinks");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(25,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setVar("customLinks");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(25,0) name = mapIndex type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setMapIndex(true);
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f0 = _jspx_th_config_005fgetOptionsComposite_005f0.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f0);
      _jspx_th_config_005fgetOptionsComposite_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(26,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setName("customButtons");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(26,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setVar("customButtons");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(26,0) name = mapIndex type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f1.setMapIndex(true);
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f1 = _jspx_th_config_005fgetOptionsComposite_005f1.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f1);
      _jspx_th_config_005fgetOptionsComposite_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(27,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f2.setVar("customDateRefine");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(27,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f2.setName("customDateRefine");
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f2 = _jspx_th_config_005fgetOptionsComposite_005f2.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f2);
      _jspx_th_config_005fgetOptionsComposite_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f3_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(28,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f3.setVar("customParams");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(28,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f3.setName("customParams");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(28,0) name = mapIndex type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f3.setMapIndex(true);
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f3 = _jspx_th_config_005fgetOptionsComposite_005f3.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f3);
      _jspx_th_config_005fgetOptionsComposite_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f8(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f8 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f8_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f8.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f8.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(30,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setName("separateFeeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(30,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setVar("separateFeeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(30,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f8 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f8 = _jspx_th_config_005fgetOption_005f8.doStartTag();
        if (_jspx_th_config_005fgetOption_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f8[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f8.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f8.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f8);
      _jspx_th_config_005fgetOption_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f8, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetConstantValueFromNameTag_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getConstantValueFromNameTag
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag _jspx_th_plma_005fgetConstantValueFromNameTag_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag) _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag.class);
    boolean _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(33,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setVar("simpleDateRefine");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(33,0) name = constantName type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setConstantName("SIMPLE_DATE_PARAM");
      int[] _jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetConstantValueFromNameTag_005f0 = _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doStartTag();
        if (_jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody.reuse(_jspx_th_plma_005fgetConstantValueFromNameTag_005f0);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetConstantValueFromNameTag_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(36,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${separateFeeds == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_config_005fgetOptionsComposite_005f4(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_map_005fnew_005f0(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_c_005fforEach_005f0(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f4 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f4_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f4.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(37,1) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f4.setName("feedDisplayNames");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(37,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f4.setVar("feedDisplayNames");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(37,1) name = mapIndex type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f4.setMapIndex(true);
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f4 = _jspx_th_config_005fgetOptionsComposite_005f4.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f4);
      _jspx_th_config_005fgetOptionsComposite_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f4, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fnew_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:new
    com.exalead.cv360.searchui.view.jspapi.map.NewTag _jspx_th_map_005fnew_005f0 = (com.exalead.cv360.searchui.view.jspapi.map.NewTag) _005fjspx_005ftagPool_005fmap_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.NewTag.class);
    boolean _jspx_th_map_005fnew_005f0_reused = false;
    try {
      _jspx_th_map_005fnew_005f0.setPageContext(_jspx_page_context);
      _jspx_th_map_005fnew_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(38,1) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fnew_005f0.setVar("feedNames");
      int[] _jspx_push_body_count_map_005fnew_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fnew_005f0 = _jspx_th_map_005fnew_005f0.doStartTag();
        if (_jspx_th_map_005fnew_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fnew_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fnew_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fnew_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_map_005fnew_005f0);
      _jspx_th_map_005fnew_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fnew_005f0, _jsp_getInstanceManager(), _jspx_th_map_005fnew_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f0_reused = false;
    try {
      _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(39,1) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(39,1) '${feedDisplayNames}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${feedDisplayNames}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(39,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setVar("feedDisplayName");
      int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
        if (_jspx_eval_c_005fforEach_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_map_005fput_005f0(_jspx_th_c_005fforEach_005f0, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
              return true;
            out.write('\r');
            out.write('\n');
            out.write('	');
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
      _jspx_th_c_005fforEach_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f0 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f0_reused = false;
    try {
      _jspx_th_map_005fput_005f0.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(40,2) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f0.setKey((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedDisplayName.feedId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(40,2) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedDisplayName.feedDisplayName}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(40,2) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f0.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedNames}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f0 = _jspx_th_map_005fput_005f0.doStartTag();
        if (_jspx_th_map_005fput_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f0);
      _jspx_th_map_005fput_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f0, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f0_reused = false;
    try {
      _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(45,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setVar("display");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(45,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(45,0) '${fn:toLowerCase(displayMode)}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_0),"${fn:toLowerCase(displayMode)}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
      if (_jspx_th_c_005fset_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
      _jspx_th_c_005fset_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f1_reused = false;
    try {
      _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(47,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setVar("IsDateFilters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(47,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(47,0) 'false'",_jsp_getExpressionFactory().createValueExpression("false",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
      if (_jspx_th_c_005fset_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
      _jspx_th_c_005fset_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f1 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f1_reused = false;
    try {
      _jspx_th_c_005fforEach_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(48,0) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f1.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(48,0) '${customDateRefine}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${customDateRefine}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(48,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f1.setVar("refine");
      int[] _jspx_push_body_count_c_005fforEach_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f1 = _jspx_th_c_005fforEach_005f1.doStartTag();
        if (_jspx_eval_c_005fforEach_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write('\r');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_c_005fif_005f1(_jspx_th_c_005fforEach_005f1, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f1);
      _jspx_th_c_005fforEach_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(49,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param[refine[1]] != undefined}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_c_005fset_005f2(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f2_reused = false;
    try {
      _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(50,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setVar("IsDateFilters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(50,2) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(50,2) 'true'",_jsp_getExpressionFactory().createValueExpression("true",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
      if (_jspx_th_c_005fset_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
      _jspx_th_c_005fset_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f3_reused = false;
    try {
      _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(53,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setVar("isCustomFilters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(53,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(53,0) 'false'",_jsp_getExpressionFactory().createValueExpression("false",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
      if (_jspx_th_c_005fset_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
      _jspx_th_c_005fset_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f2 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f2_reused = false;
    try {
      _jspx_th_c_005fforEach_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(54,0) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f2.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(54,0) '${customParams}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${customParams}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(54,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f2.setVar("customParam");
      int[] _jspx_push_body_count_c_005fforEach_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f2 = _jspx_th_c_005fforEach_005f2.doStartTag();
        if (_jspx_eval_c_005fforEach_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write('\r');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_request_005fgetParameterValue_005f0(_jspx_th_c_005fforEach_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f2))
              return true;
            out.write('\r');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_c_005fif_005f2(_jspx_th_c_005fforEach_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f2))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f2.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f2);
      _jspx_th_c_005fforEach_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetParameterValue_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getParameterValue
    com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag _jspx_th_request_005fgetParameterValue_005f0 = (com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag) _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag.class);
    boolean _jspx_th_request_005fgetParameterValue_005f0_reused = false;
    try {
      _jspx_th_request_005fgetParameterValue_005f0.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetParameterValue_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(55,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setVar("paramValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(55,1) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customParam.paramName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_request_005fgetParameterValue_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetParameterValue_005f0 = _jspx_th_request_005fgetParameterValue_005f0.doStartTag();
        if (_jspx_th_request_005fgetParameterValue_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetParameterValue_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetParameterValue_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetParameterValue_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_request_005fgetParameterValue_005f0);
      _jspx_th_request_005fgetParameterValue_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetParameterValue_005f0, _jsp_getInstanceManager(), _jspx_th_request_005fgetParameterValue_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(56,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty paramValue}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_c_005fset_005f4(_jspx_th_c_005fif_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f2))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f4_reused = false;
    try {
      _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(57,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setVar("isCustomFilters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(57,2) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(57,2) 'true'",_jsp_getExpressionFactory().createValueExpression("true",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
      if (_jspx_th_c_005fset_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
      _jspx_th_c_005fset_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(60,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param[simpleDateRefine] != undefined}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_c_005fset_005f5(_jspx_th_c_005fif_005f3, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f5 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f5_reused = false;
    try {
      _jspx_th_c_005fset_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(61,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setVar("IsDateFilters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(61,1) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(61,1) 'true'",_jsp_getExpressionFactory().createValueExpression("true",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f5 = _jspx_th_c_005fset_005f5.doStartTag();
      if (_jspx_th_c_005fset_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f5);
      _jspx_th_c_005fset_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fnew_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:new
    com.exalead.cv360.searchui.view.jspapi.list.NewTag _jspx_th_list_005fnew_005f0 = (com.exalead.cv360.searchui.view.jspapi.list.NewTag) _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.NewTag.class);
    boolean _jspx_th_list_005fnew_005f0_reused = false;
    try {
      _jspx_th_list_005fnew_005f0.setPageContext(_jspx_page_context);
      _jspx_th_list_005fnew_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(65,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fnew_005f0.setVar("filters");
      int[] _jspx_push_body_count_list_005fnew_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fnew_005f0 = _jspx_th_list_005fnew_005f0.doStartTag();
        if (_jspx_th_list_005fnew_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fnew_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fnew_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fnew_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_list_005fnew_005f0);
      _jspx_th_list_005fnew_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fnew_005f0, _jsp_getInstanceManager(), _jspx_th_list_005fnew_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f6(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f6 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f6_reused = false;
    try {
      _jspx_th_c_005fset_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f6.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(66,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setVar("widgetContent");
      int _jspx_eval_c_005fset_005f6 = _jspx_th_c_005fset_005f6.doStartTag();
      if (_jspx_eval_c_005fset_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_c_005fset_005f6 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_c_005fset_005f6);
        }
        do {
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_c_005fchoose_005f0(_jspx_th_c_005fset_005f6, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_c_005fif_005f6(_jspx_th_c_005fset_005f6, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fset_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_c_005fset_005f6 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
        }
      }
      if (_jspx_th_c_005fset_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar.reuse(_jspx_th_c_005fset_005f6);
      _jspx_th_c_005fset_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fset_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fset_005f6);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(69,2) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${separateFeeds == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("			");
          if (_jspx_meth_search_005fforEachFeed_005f0(_jspx_th_c_005fwhen_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachFeed_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachFeed
    com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag _jspx_th_search_005fforEachFeed_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag) _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag.class);
    boolean _jspx_th_search_005fforEachFeed_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachFeed_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachFeed_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(70,3) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(70,3) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f0.setVar("feed");
      int[] _jspx_push_body_count_search_005fforEachFeed_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachFeed_005f0 = _jspx_th_search_005fforEachFeed_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachFeed_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("				");
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_c_005fset_005f7(_jspx_th_search_005fforEachFeed_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFeed_005f0))
              return true;
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_c_005fset_005f8(_jspx_th_search_005fforEachFeed_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFeed_005f0))
              return true;
            out.write("\r\n");
            out.write("				");
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_c_005fif_005f4(_jspx_th_search_005fforEachFeed_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFeed_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_search_005fforEachFeed_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachFeed_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachFeed_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachFeed_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachFeed_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.reuse(_jspx_th_search_005fforEachFeed_005f0);
      _jspx_th_search_005fforEachFeed_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachFeed_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachFeed_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFeed_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFeed_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f7 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f7_reused = false;
    try {
      _jspx_th_c_005fset_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFeed_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(72,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setVar("nbFilters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(72,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(72,4) '${fn:length(filters)}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_1),"${fn:length(filters)}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f7 = _jspx_th_c_005fset_005f7.doStartTag();
      if (_jspx_th_c_005fset_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f7);
      _jspx_th_c_005fset_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFeed_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFeed_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f8 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f8_reused = false;
    try {
      _jspx_th_c_005fset_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFeed_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(73,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f8.setVar("feedFilters");
      int _jspx_eval_c_005fset_005f8 = _jspx_th_c_005fset_005f8.doStartTag();
      if (_jspx_eval_c_005fset_005f8 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        if (_jspx_eval_c_005fset_005f8 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          _jspx_push_body_count_search_005fforEachFeed_005f0[0]++;
          out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_c_005fset_005f8);
        }
        do {
          out.write("\r\n");
          out.write("					<div class=\"feedFilters\">\r\n");
          out.write("						<div class=\"secondaryTitle feedName\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty feedNames[feed.id] ? feedNames[feed.id] : feed.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</div>\r\n");
          out.write("						<div class=\"filtersWrapper\">\r\n");
          out.write("							");
          if (_jspx_meth_render_005ftemplate_005f0(_jspx_th_c_005fset_005f8, _jspx_page_context, _jspx_push_body_count_search_005fforEachFeed_005f0))
            return true;
          out.write("\r\n");
          out.write("						</div>\r\n");
          out.write("					</div>\r\n");
          out.write("				");
          int evalDoAfterBody = _jspx_th_c_005fset_005f8.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
        if (_jspx_eval_c_005fset_005f8 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
          out = _jspx_page_context.popBody();
          _jspx_push_body_count_search_005fforEachFeed_005f0[0]--;
        }
      }
      if (_jspx_th_c_005fset_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar.reuse(_jspx_th_c_005fset_005f8);
      _jspx_th_c_005fset_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fset_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFeed_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f0);
    try {
      _jspx_th_render_005ftemplate_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f0.setParent(_jspx_th_c_005fset_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(77,7) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("templates/${display}Mode.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005ftemplate_005f0.setJspBody(new Helper( 0, _jspx_page_context, _jspx_th_render_005ftemplate_005f0, _jspx_push_body_count_search_005fforEachFeed_005f0));
      _jspx_th_render_005ftemplate_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f0);
    try {
      _jspx_th_render_005fparameter_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f0.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(78,8) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setName("feed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(78,8) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f1);
    try {
      _jspx_th_render_005fparameter_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f1.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(79,8) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setName("filters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(79,8) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${filters}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFeed_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFeed_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f4_reused = false;
    try {
      _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFeed_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(85,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fn:length(filters) > nbFilters}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_1)).booleanValue());
      int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
      if (_jspx_eval_c_005fif_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("					");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedFilters}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\r\n");
          out.write("				");
          int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      _jspx_th_c_005fif_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("			");
          if (_jspx_meth_c_005fif_005f5(_jspx_th_c_005fotherwise_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f5 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f5_reused = false;
    try {
      _jspx_th_c_005fif_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(91,3) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty feeds}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f5 = _jspx_th_c_005fif_005f5.doStartTag();
      if (_jspx_eval_c_005fif_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("				");
          if (_jspx_meth_render_005ftemplate_005f1(_jspx_th_c_005fif_005f5, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("			");
          int evalDoAfterBody = _jspx_th_c_005fif_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f5);
      _jspx_th_c_005fif_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f1);
    try {
      _jspx_th_render_005ftemplate_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f1.setParent(_jspx_th_c_005fif_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(92,4) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f1.setTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("templates/${display}Mode.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005ftemplate_005f1.setJspBody(new Helper( 1, _jspx_page_context, _jspx_th_render_005ftemplate_005f1, null));
      _jspx_th_render_005ftemplate_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f2);
    try {
      _jspx_th_render_005fparameter_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f2.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(93,5) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setName("feeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(93,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f3 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f3);
    try {
      _jspx_th_render_005fparameter_005f3.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f3.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(94,5) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setName("filters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(94,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${filters}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f3.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f3);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fset_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f6 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f6_reused = false;
    try {
      _jspx_th_c_005fif_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fset_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(99,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f6.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${empty filters && IsDateFilters=='false' && isCustomFilters=='false'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f6 = _jspx_th_c_005fif_005f6.doStartTag();
      if (_jspx_eval_c_005fif_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		<div class=\"no-filters\">");
          if (_jspx_meth_i18n_005fmessage_005f0(_jspx_th_c_005fif_005f6, _jspx_page_context))
            return true;
          out.write("</div>\r\n");
          out.write("	");
          int evalDoAfterBody = _jspx_th_c_005fif_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f6);
      _jspx_th_c_005fif_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f0 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f0_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f0.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(100,26) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setCode("widgets.activeFilters.label.noFilter");
      int[] _jspx_push_body_count_i18n_005fmessage_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f0 = _jspx_th_i18n_005fmessage_005f0.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f0);
      _jspx_th_i18n_005fmessage_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f0, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f7(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f7 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f7_reused = false;
    try {
      _jspx_th_c_005fif_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f7.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(105,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f7.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hideWidget != 'true' || fn:length(filters) > 0 || IsDateFilters == 'true' }", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_1)).booleanValue());
      int _jspx_eval_c_005fif_005f7 = _jspx_th_c_005fif_005f7.doStartTag();
      if (_jspx_eval_c_005fif_005f7 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("	");
          out.write('\r');
          out.write('\n');
          out.write('	');
          if (_jspx_meth_c_005fif_005f8(_jspx_th_c_005fif_005f7, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("\r\n");
          out.write("	");
          if (_jspx_meth_c_005fchoose_005f1(_jspx_th_c_005fif_005f7, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("	");
          if (_jspx_meth_render_005frenderScript_005f0(_jspx_th_c_005fif_005f7, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          int evalDoAfterBody = _jspx_th_c_005fif_005f7.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f7);
      _jspx_th_c_005fif_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f8 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f8_reused = false;
    try {
      _jspx_th_c_005fif_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f7);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(108,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f8.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${showReset == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f8 = _jspx_th_c_005fif_005f8.doStartTag();
      if (_jspx_eval_c_005fif_005f8 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_i18n_005fmessage_005f1(_jspx_th_c_005fif_005f8, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_list_005fnew_005f1(_jspx_th_c_005fif_005f8, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_search_005fforEachFeed_005f1(_jspx_th_c_005fif_005f8, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_c_005fforEach_005f3(_jspx_th_c_005fif_005f8, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_url_005furl_005f0(_jspx_th_c_005fif_005f8, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          int evalDoAfterBody = _jspx_th_c_005fif_005f8.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f8);
      _jspx_th_c_005fif_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f1 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f1_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f1.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(109,2) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setVar("cancelAllTooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(109,2) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setCode("widgets.activeFilters.tooltip.cancelAll");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(109,2) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setText("Remove all filters");
      int[] _jspx_push_body_count_i18n_005fmessage_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f1 = _jspx_th_i18n_005fmessage_005f1.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f1);
      _jspx_th_i18n_005fmessage_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f1, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fnew_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:new
    com.exalead.cv360.searchui.view.jspapi.list.NewTag _jspx_th_list_005fnew_005f1 = (com.exalead.cv360.searchui.view.jspapi.list.NewTag) _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.NewTag.class);
    boolean _jspx_th_list_005fnew_005f1_reused = false;
    try {
      _jspx_th_list_005fnew_005f1.setPageContext(_jspx_page_context);
      _jspx_th_list_005fnew_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(110,2) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fnew_005f1.setVar("feedIds");
      int[] _jspx_push_body_count_list_005fnew_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fnew_005f1 = _jspx_th_list_005fnew_005f1.doStartTag();
        if (_jspx_th_list_005fnew_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fnew_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fnew_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fnew_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_list_005fnew_005f1);
      _jspx_th_list_005fnew_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fnew_005f1, _jsp_getInstanceManager(), _jspx_th_list_005fnew_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachFeed_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachFeed
    com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag _jspx_th_search_005fforEachFeed_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag) _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag.class);
    boolean _jspx_th_search_005fforEachFeed_005f1_reused = false;
    try {
      _jspx_th_search_005fforEachFeed_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachFeed_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(111,2) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f1.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(111,2) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f1.setVar("feed");
      int[] _jspx_push_body_count_search_005fforEachFeed_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachFeed_005f1 = _jspx_th_search_005fforEachFeed_005f1.doStartTag();
        if (_jspx_eval_search_005fforEachFeed_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_list_005fadd_005f0(_jspx_th_search_005fforEachFeed_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachFeed_005f1))
              return true;
            out.write("\r\n");
            out.write("		");
            int evalDoAfterBody = _jspx_th_search_005fforEachFeed_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachFeed_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachFeed_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachFeed_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachFeed_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.reuse(_jspx_th_search_005fforEachFeed_005f1);
      _jspx_th_search_005fforEachFeed_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachFeed_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fforEachFeed_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fadd_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFeed_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFeed_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:add
    com.exalead.cv360.searchui.view.jspapi.list.AddTag _jspx_th_list_005fadd_005f0 = (com.exalead.cv360.searchui.view.jspapi.list.AddTag) _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.AddTag.class);
    boolean _jspx_th_list_005fadd_005f0_reused = false;
    try {
      _jspx_th_list_005fadd_005f0.setPageContext(_jspx_page_context);
      _jspx_th_list_005fadd_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFeed_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(112,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed.id}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(112,3) name = list type = java.util.Collection reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f0.setList((java.util.Collection) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedIds}", java.util.Collection.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_list_005fadd_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fadd_005f0 = _jspx_th_list_005fadd_005f0.doStartTag();
        if (_jspx_th_list_005fadd_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fadd_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fadd_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fadd_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.reuse(_jspx_th_list_005fadd_005f0);
      _jspx_th_list_005fadd_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fadd_005f0, _jsp_getInstanceManager(), _jspx_th_list_005fadd_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f3 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f3_reused = false;
    try {
      _jspx_th_c_005fforEach_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(114,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f3.setVar("feedName");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(114,2) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f3.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(114,2) '${feedsList}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${feedsList}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int[] _jspx_push_body_count_c_005fforEach_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f3 = _jspx_th_c_005fforEach_005f3.doStartTag();
        if (_jspx_eval_c_005fforEach_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_list_005fadd_005f1(_jspx_th_c_005fforEach_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f3))
              return true;
            out.write("\r\n");
            out.write("		");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f3.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f3);
      _jspx_th_c_005fforEach_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fadd_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f3)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:add
    com.exalead.cv360.searchui.view.jspapi.list.AddTag _jspx_th_list_005fadd_005f1 = (com.exalead.cv360.searchui.view.jspapi.list.AddTag) _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.AddTag.class);
    boolean _jspx_th_list_005fadd_005f1_reused = false;
    try {
      _jspx_th_list_005fadd_005f1.setPageContext(_jspx_page_context);
      _jspx_th_list_005fadd_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(115,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f1.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedName}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(115,3) name = list type = java.util.Collection reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f1.setList((java.util.Collection) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedIds}", java.util.Collection.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_list_005fadd_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fadd_005f1 = _jspx_th_list_005fadd_005f1.doStartTag();
        if (_jspx_th_list_005fadd_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fadd_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fadd_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fadd_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.reuse(_jspx_th_list_005fadd_005f1);
      _jspx_th_list_005fadd_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fadd_005f1, _jsp_getInstanceManager(), _jspx_th_list_005fadd_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_url_005furl_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  url:url
    com.exalead.cv360.searchui.view.jspapi.url.UrlTag _jspx_th_url_005furl_005f0 = (com.exalead.cv360.searchui.view.jspapi.url.UrlTag) _005fjspx_005ftagPool_005furl_005furl_0026_005fvar_005fkeepQueryString_005fforceRefineOn.get(com.exalead.cv360.searchui.view.jspapi.url.UrlTag.class);
    boolean _jspx_th_url_005furl_005f0_reused = false;
    try {
      _jspx_th_url_005furl_005f0.setPageContext(_jspx_page_context);
      _jspx_th_url_005furl_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(117,2) name = keepQueryString type = boolean reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005furl_005f0.setKeepQueryString(java.lang.Boolean.valueOf("true"));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(117,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005furl_005f0.setVar("cancelAllUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(117,2) name = forceRefineOn type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005furl_005f0.setForceRefineOn((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedIds}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_url_005furl_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_url_005furl_005f0 = _jspx_th_url_005furl_005f0.doStartTag();
        if (_jspx_eval_url_005furl_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_url_005furl_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_url_005furl_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_url_005furl_005f0);
          }
          do {
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_url_005fparameter_005f0(_jspx_th_url_005furl_005f0, _jspx_page_context, _jspx_push_body_count_url_005furl_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_c_005fforEach_005f4(_jspx_th_url_005furl_005f0, _jspx_page_context, _jspx_push_body_count_url_005furl_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_c_005fforEach_005f5(_jspx_th_url_005furl_005f0, _jspx_page_context, _jspx_push_body_count_url_005furl_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_url_005fparameter_005f3(_jspx_th_url_005furl_005f0, _jspx_page_context, _jspx_push_body_count_url_005furl_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_c_005fforEach_005f6(_jspx_th_url_005furl_005f0, _jspx_page_context, _jspx_push_body_count_url_005furl_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            int evalDoAfterBody = _jspx_th_url_005furl_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_url_005furl_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_url_005furl_005f0[0]--;
          }
        }
        if (_jspx_th_url_005furl_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_url_005furl_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_url_005furl_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_url_005furl_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005furl_005furl_0026_005fvar_005fkeepQueryString_005fforceRefineOn.reuse(_jspx_th_url_005furl_005f0);
      _jspx_th_url_005furl_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_url_005furl_005f0, _jsp_getInstanceManager(), _jspx_th_url_005furl_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_url_005fparameter_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_url_005furl_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_url_005furl_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  url:parameter
    com.exalead.cv360.searchui.view.jspapi.url.ParameterTag _jspx_th_url_005fparameter_005f0 = (com.exalead.cv360.searchui.view.jspapi.url.ParameterTag) _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.url.ParameterTag.class);
    boolean _jspx_th_url_005fparameter_005f0_reused = false;
    try {
      _jspx_th_url_005fparameter_005f0.setPageContext(_jspx_page_context);
      _jspx_th_url_005fparameter_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_url_005furl_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(118,3) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005fparameter_005f0.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${searchParameter}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(118,3) name = value type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005fparameter_005f0.setValue("");
      int[] _jspx_push_body_count_url_005fparameter_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_url_005fparameter_005f0 = _jspx_th_url_005fparameter_005f0.doStartTag();
        if (_jspx_th_url_005fparameter_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_url_005fparameter_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_url_005fparameter_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_url_005fparameter_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody.reuse(_jspx_th_url_005fparameter_005f0);
      _jspx_th_url_005fparameter_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_url_005fparameter_005f0, _jsp_getInstanceManager(), _jspx_th_url_005fparameter_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_url_005furl_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_url_005furl_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f4 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f4_reused = false;
    try {
      _jspx_th_c_005fforEach_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_url_005furl_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(119,3) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f4.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(119,3) '${customDateRefine}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${customDateRefine}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(119,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f4.setVar("refine");
      int[] _jspx_push_body_count_c_005fforEach_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f4 = _jspx_th_c_005fforEach_005f4.doStartTag();
        if (_jspx_eval_c_005fforEach_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_url_005fparameter_005f1(_jspx_th_c_005fforEach_005f4, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f4))
              return true;
            out.write("\r\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f4.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f4);
      _jspx_th_c_005fforEach_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_url_005fparameter_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f4)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  url:parameter
    com.exalead.cv360.searchui.view.jspapi.url.ParameterTag _jspx_th_url_005fparameter_005f1 = (com.exalead.cv360.searchui.view.jspapi.url.ParameterTag) _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.url.ParameterTag.class);
    boolean _jspx_th_url_005fparameter_005f1_reused = false;
    try {
      _jspx_th_url_005fparameter_005f1.setPageContext(_jspx_page_context);
      _jspx_th_url_005fparameter_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(120,4) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005fparameter_005f1.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refine[1]}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(120,4) name = value type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005fparameter_005f1.setValue("");
      int[] _jspx_push_body_count_url_005fparameter_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_url_005fparameter_005f1 = _jspx_th_url_005fparameter_005f1.doStartTag();
        if (_jspx_th_url_005fparameter_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_url_005fparameter_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_url_005fparameter_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_url_005fparameter_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody.reuse(_jspx_th_url_005fparameter_005f1);
      _jspx_th_url_005fparameter_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_url_005fparameter_005f1, _jsp_getInstanceManager(), _jspx_th_url_005fparameter_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_url_005furl_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_url_005furl_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f5 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f5_reused = false;
    try {
      _jspx_th_c_005fforEach_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_url_005furl_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(122,3) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f5.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(122,3) '${customParams}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${customParams}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(122,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f5.setVar("customParam");
      int[] _jspx_push_body_count_c_005fforEach_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f5 = _jspx_th_c_005fforEach_005f5.doStartTag();
        if (_jspx_eval_c_005fforEach_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_url_005fparameter_005f2(_jspx_th_c_005fforEach_005f5, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f5))
              return true;
            out.write("\r\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f5.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f5);
      _jspx_th_c_005fforEach_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_url_005fparameter_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f5)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  url:parameter
    com.exalead.cv360.searchui.view.jspapi.url.ParameterTag _jspx_th_url_005fparameter_005f2 = (com.exalead.cv360.searchui.view.jspapi.url.ParameterTag) _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.url.ParameterTag.class);
    boolean _jspx_th_url_005fparameter_005f2_reused = false;
    try {
      _jspx_th_url_005fparameter_005f2.setPageContext(_jspx_page_context);
      _jspx_th_url_005fparameter_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(123,4) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005fparameter_005f2.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customParam.paramName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(123,4) name = value type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005fparameter_005f2.setValue("");
      int[] _jspx_push_body_count_url_005fparameter_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_url_005fparameter_005f2 = _jspx_th_url_005fparameter_005f2.doStartTag();
        if (_jspx_th_url_005fparameter_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_url_005fparameter_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_url_005fparameter_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_url_005fparameter_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody.reuse(_jspx_th_url_005fparameter_005f2);
      _jspx_th_url_005fparameter_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_url_005fparameter_005f2, _jsp_getInstanceManager(), _jspx_th_url_005fparameter_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_url_005fparameter_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_url_005furl_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_url_005furl_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  url:parameter
    com.exalead.cv360.searchui.view.jspapi.url.ParameterTag _jspx_th_url_005fparameter_005f3 = (com.exalead.cv360.searchui.view.jspapi.url.ParameterTag) _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.url.ParameterTag.class);
    boolean _jspx_th_url_005fparameter_005f3_reused = false;
    try {
      _jspx_th_url_005fparameter_005f3.setPageContext(_jspx_page_context);
      _jspx_th_url_005fparameter_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_url_005furl_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(125,3) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005fparameter_005f3.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${simpleDateRefine}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(125,3) name = value type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005fparameter_005f3.setValue("");
      int[] _jspx_push_body_count_url_005fparameter_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_url_005fparameter_005f3 = _jspx_th_url_005fparameter_005f3.doStartTag();
        if (_jspx_th_url_005fparameter_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_url_005fparameter_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_url_005fparameter_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_url_005fparameter_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody.reuse(_jspx_th_url_005fparameter_005f3);
      _jspx_th_url_005fparameter_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_url_005fparameter_005f3, _jsp_getInstanceManager(), _jspx_th_url_005fparameter_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_url_005furl_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_url_005furl_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f6 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f6_reused = false;
    try {
      _jspx_th_c_005fforEach_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_url_005furl_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(126,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f6.setVar("feedId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(126,3) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f6.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(126,3) '${feedIds}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${feedIds}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int[] _jspx_push_body_count_c_005fforEach_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f6 = _jspx_th_c_005fforEach_005f6.doStartTag();
        if (_jspx_eval_c_005fforEach_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_url_005fparameter_005f4(_jspx_th_c_005fforEach_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f6))
              return true;
            out.write("\r\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f6.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f6);
      _jspx_th_c_005fforEach_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_url_005fparameter_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f6)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  url:parameter
    com.exalead.cv360.searchui.view.jspapi.url.ParameterTag _jspx_th_url_005fparameter_005f4 = (com.exalead.cv360.searchui.view.jspapi.url.ParameterTag) _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.url.ParameterTag.class);
    boolean _jspx_th_url_005fparameter_005f4_reused = false;
    try {
      _jspx_th_url_005fparameter_005f4.setPageContext(_jspx_page_context);
      _jspx_th_url_005fparameter_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(127,4) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005fparameter_005f4.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedId}.r", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(127,4) name = value type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005fparameter_005f4.setValue("");
      int[] _jspx_push_body_count_url_005fparameter_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_url_005fparameter_005f4 = _jspx_th_url_005fparameter_005f4.doStartTag();
        if (_jspx_th_url_005fparameter_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_url_005fparameter_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_url_005fparameter_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_url_005fparameter_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005furl_005fparameter_0026_005fvalue_005fname_005fnobody.reuse(_jspx_th_url_005fparameter_005f4);
      _jspx_th_url_005fparameter_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_url_005fparameter_005f4, _jsp_getInstanceManager(), _jspx_th_url_005fparameter_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f1 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f1_reused = false;
    try {
      _jspx_th_c_005fchoose_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f7);
      int _jspx_eval_c_005fchoose_005f1 = _jspx_th_c_005fchoose_005f1.doStartTag();
      if (_jspx_eval_c_005fchoose_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_c_005fwhen_005f1(_jspx_th_c_005fchoose_005f1, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_c_005fotherwise_005f1(_jspx_th_c_005fchoose_005f1, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f1);
      _jspx_th_c_005fchoose_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f1 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f1_reused = false;
    try {
      _jspx_th_c_005fwhen_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(134,2) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${displayMode == 'Compact' }", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f1 = _jspx_th_c_005fwhen_005f1.doStartTag();
      if (_jspx_eval_c_005fwhen_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("			");
          if (_jspx_meth_widget_005fwidget_005f0(_jspx_th_c_005fwhen_005f1, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f1);
      _jspx_th_c_005fwhen_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fwidget_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:widget
    com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag _jspx_th_widget_005fwidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag) _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss.get(com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag.class);
    boolean _jspx_th_widget_005fwidget_005f0_reused = false;
    try {
      _jspx_th_widget_005fwidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fwidget_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(135,3) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setExtraCss("activeFilters compactMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(135,3) name = varUcssId type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setVarUcssId("uCssId");
      int[] _jspx_push_body_count_widget_005fwidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fwidget_005f0 = _jspx_th_widget_005fwidget_005f0.doStartTag();
        if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fwidget_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fwidget_005f0);
          }
          do {
            out.write("\r\n");
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_render_005ftemplate_005f2(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("				<div class=\"widgetTitle\">");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${title}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("</div>\r\n");
            out.write("				<div class=\"activeFiltersContainer\">\r\n");
            out.write("						");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widgetContent}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\r\n");
            out.write("				</div>\r\n");
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_c_005fif_005f9(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("				\r\n");
            out.write("				");
            if (_jspx_meth_c_005fif_005f10(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_render_005ftemplate_005f3(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_render_005ftemplate_005f4(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_widget_005fwidget_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fwidget_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fwidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fwidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fwidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fwidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss.reuse(_jspx_th_widget_005fwidget_005f0);
      _jspx_th_widget_005fwidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fwidget_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fwidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f2);
    try {
      _jspx_th_render_005ftemplate_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f2.setParent(_jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(137,4) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f2.setTemplate("templates/customButtons.jsp");
      _jspx_th_render_005ftemplate_005f2.setJspBody(new Helper( 2, _jspx_page_context, _jspx_th_render_005ftemplate_005f2, _jspx_push_body_count_widget_005fwidget_005f0));
      _jspx_th_render_005ftemplate_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f4 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f4);
    try {
      _jspx_th_render_005fparameter_005f4.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f4.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(138,5) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setName("customButtons");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(138,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customButtons}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f4.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f4);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f5 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f5);
    try {
      _jspx_th_render_005fparameter_005f5.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f5.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(139,5) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setName("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(139,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f5.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f5);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f6 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f6);
    try {
      _jspx_th_render_005fparameter_005f6.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f6.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(140,5) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setName("positioning");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(140,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setValue("left");
      _jspx_th_render_005fparameter_005f6.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f6);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f7 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f7);
    try {
      _jspx_th_render_005fparameter_005f7.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f7.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(141,20) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setName("extraCss");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(141,20) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setValue("align-start");
      _jspx_th_render_005fparameter_005f7.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f7);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f9 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f9_reused = false;
    try {
      _jspx_th_c_005fif_005f9.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(149,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f9.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${enableSuggest}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f9 = _jspx_th_c_005fif_005f9.doStartTag();
      if (_jspx_eval_c_005fif_005f9 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("					<div class=\"quickFilters\">\r\n");
          out.write("						<div class=\"quickFiltersContainer\"></div>\r\n");
          out.write("						<div class=\"quickFiltersContainer-droppable\"></div>\r\n");
          out.write("						<div class=\"quickFiltersButtons\">\r\n");
          out.write("							<input class=\"quickFilters-suggest\" name=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" placeholder=\"");
          if (_jspx_meth_i18n_005fmessage_005f2(_jspx_th_c_005fif_005f9, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\" />\r\n");
          out.write("							<button class=\"quickFilters-cancel\" style=\"display: none;\" title=\"");
          if (_jspx_meth_i18n_005fmessage_005f3(_jspx_th_c_005fif_005f9, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\"><i class=\"fonticon fonticon-wrong\"></i></button>\r\n");
          out.write("							<button class=\"quickFilters-apply\" style=\"display: none;\" title=\"");
          if (_jspx_meth_i18n_005fmessage_005f4(_jspx_th_c_005fif_005f9, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\"><i class=\"fonticon fonticon-check\"></i></button>\r\n");
          out.write("						</div>\r\n");
          out.write("					</div>\r\n");
          out.write("				");
          int evalDoAfterBody = _jspx_th_c_005fif_005f9.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f9);
      _jspx_th_c_005fif_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f9, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f2 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f2_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f2.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f9);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(154,73) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setCode("widgets.activeFilters.quickFilters.placeholder");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(154,73) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setText("Add a filter...");
      int[] _jspx_push_body_count_i18n_005fmessage_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f2 = _jspx_th_i18n_005fmessage_005f2.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f2);
      _jspx_th_i18n_005fmessage_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f2, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f9, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f3 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f3_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f3.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f9);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(155,73) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f3.setCode("generic.cancel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(155,73) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f3.setText("Cancel");
      int[] _jspx_push_body_count_i18n_005fmessage_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f3 = _jspx_th_i18n_005fmessage_005f3.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f3);
      _jspx_th_i18n_005fmessage_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f3, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f9, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f4 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f4_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f4.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f9);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(156,72) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f4.setCode("generic.apply");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(156,72) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f4.setText("Apply");
      int[] _jspx_push_body_count_i18n_005fmessage_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f4 = _jspx_th_i18n_005fmessage_005f4.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f4);
      _jspx_th_i18n_005fmessage_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f4, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f10 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f10_reused = false;
    try {
      _jspx_th_c_005fif_005f10.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f10.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(161,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f10.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${showReset == 'true' && (not empty filters || IsDateFilters == 'true' || isCustomFilters == 'true')}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f10 = _jspx_th_c_005fif_005f10.doStartTag();
      if (_jspx_eval_c_005fif_005f10 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("					<a href=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cancelAllUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" class=\"fonticon fonticon-trash reset-btn\" title=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cancelAllTooltip}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"></a>\r\n");
          out.write("				");
          int evalDoAfterBody = _jspx_th_c_005fif_005f10.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f10);
      _jspx_th_c_005fif_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f10, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f3 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f3);
    try {
      _jspx_th_render_005ftemplate_005f3.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f3.setParent(_jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(165,4) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f3.setTemplate("templates/customLinks.jsp");
      _jspx_th_render_005ftemplate_005f3.setJspBody(new Helper( 3, _jspx_page_context, _jspx_th_render_005ftemplate_005f3, _jspx_push_body_count_widget_005fwidget_005f0));
      _jspx_th_render_005ftemplate_005f3.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f3);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f8 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f8);
    try {
      _jspx_th_render_005fparameter_005f8.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f8.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(166,5) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f8.setName("customLinks");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(166,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f8.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customLinks}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f8.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f8);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f9 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f9);
    try {
      _jspx_th_render_005fparameter_005f9.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f9.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(167,5) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f9.setName("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(167,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f9.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f9.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f9);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f4 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f4);
    try {
      _jspx_th_render_005ftemplate_005f4.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f4.setParent(_jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(169,3) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f4.setTemplate("templates/customButtons.jsp");
      _jspx_th_render_005ftemplate_005f4.setJspBody(new Helper( 4, _jspx_page_context, _jspx_th_render_005ftemplate_005f4, _jspx_push_body_count_widget_005fwidget_005f0));
      _jspx_th_render_005ftemplate_005f4.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f4);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f10 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f10);
    try {
      _jspx_th_render_005fparameter_005f10.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f10.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(170,5) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f10.setName("customButtons");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(170,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f10.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customButtons}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f10.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f10);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f11 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f11);
    try {
      _jspx_th_render_005fparameter_005f11.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f11.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(171,5) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f11.setName("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(171,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f11.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f11.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f11);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f12 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f12);
    try {
      _jspx_th_render_005fparameter_005f12.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f12.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(172,20) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f12.setName("positioning");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(172,20) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f12.setValue("right");
      _jspx_th_render_005fparameter_005f12.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f12);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f1 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f1_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      int _jspx_eval_c_005fotherwise_005f1 = _jspx_th_c_005fotherwise_005f1.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("			");
          if (_jspx_meth_widget_005fwidget_005f1(_jspx_th_c_005fotherwise_005f1, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f1);
      _jspx_th_c_005fotherwise_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fwidget_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:widget
    com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag _jspx_th_widget_005fwidget_005f1 = (com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag) _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss.get(com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag.class);
    boolean _jspx_th_widget_005fwidget_005f1_reused = false;
    try {
      _jspx_th_widget_005fwidget_005f1.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fwidget_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(177,3) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f1.setExtraCss("activeFilters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(177,3) name = varUcssId type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f1.setVarUcssId("uCssId");
      int[] _jspx_push_body_count_widget_005fwidget_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fwidget_005f1 = _jspx_th_widget_005fwidget_005f1.doStartTag();
        if (_jspx_eval_widget_005fwidget_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fwidget_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fwidget_005f1[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fwidget_005f1);
          }
          do {
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_c_005fif_005f11(_jspx_th_widget_005fwidget_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f1))
              return true;
            out.write("\r\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_widget_005fwidget_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fwidget_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fwidget_005f1[0]--;
          }
        }
        if (_jspx_th_widget_005fwidget_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fwidget_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fwidget_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fwidget_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss.reuse(_jspx_th_widget_005fwidget_005f1);
      _jspx_th_widget_005fwidget_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fwidget_005f1, _jsp_getInstanceManager(), _jspx_th_widget_005fwidget_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f11 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f11_reused = false;
    try {
      _jspx_th_c_005fif_005f11.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f11.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(178,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f11.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty title}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f11 = _jspx_th_c_005fif_005f11.doStartTag();
      if (_jspx_eval_c_005fif_005f11 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("					");
          if (_jspx_meth_widget_005fheader_005f0(_jspx_th_c_005fif_005f11, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f1))
            return true;
          out.write("\r\n");
          out.write("					");
          if (_jspx_meth_widget_005fcontent_005f0(_jspx_th_c_005fif_005f11, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f1))
            return true;
          out.write("\r\n");
          out.write("				");
          int evalDoAfterBody = _jspx_th_c_005fif_005f11.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f11);
      _jspx_th_c_005fif_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f11, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fheader_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f11, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:header
    com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag _jspx_th_widget_005fheader_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag) _005fjspx_005ftagPool_005fwidget_005fheader_0026_005fextraCss.get(com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag.class);
    boolean _jspx_th_widget_005fheader_005f0_reused = false;
    try {
      _jspx_th_widget_005fheader_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fheader_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f11);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(179,5) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fheader_005f0.setExtraCss("sectionTitle");
      int[] _jspx_push_body_count_widget_005fheader_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fheader_005f0 = _jspx_th_widget_005fheader_005f0.doStartTag();
        if (_jspx_eval_widget_005fheader_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fheader_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fheader_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fheader_005f0);
          }
          do {
            out.write("\r\n");
            out.write("						");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${title}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\r\n");
            out.write("						");
            if (_jspx_meth_c_005fif_005f12(_jspx_th_widget_005fheader_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fheader_005f0))
              return true;
            out.write("\r\n");
            out.write("					");
            int evalDoAfterBody = _jspx_th_widget_005fheader_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fheader_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fheader_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fheader_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fheader_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fheader_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fheader_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fheader_0026_005fextraCss.reuse(_jspx_th_widget_005fheader_005f0);
      _jspx_th_widget_005fheader_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fheader_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fheader_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fheader_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fheader_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f12 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f12_reused = false;
    try {
      _jspx_th_c_005fif_005f12.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f12.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fheader_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(181,6) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f12.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${showReset == 'true' && (not empty filters || IsDateFilters == 'true' || isCustomFilters == 'true')}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f12 = _jspx_th_c_005fif_005f12.doStartTag();
      if (_jspx_eval_c_005fif_005f12 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("							");
          if (_jspx_meth_i18n_005fmessage_005f5(_jspx_th_c_005fif_005f12, _jspx_page_context, _jspx_push_body_count_widget_005fheader_005f0))
            return true;
          out.write("\r\n");
          out.write("							");
          if (_jspx_meth_list_005fnew_005f2(_jspx_th_c_005fif_005f12, _jspx_page_context, _jspx_push_body_count_widget_005fheader_005f0))
            return true;
          out.write("\r\n");
          out.write("							");
          if (_jspx_meth_search_005fforEachFeed_005f2(_jspx_th_c_005fif_005f12, _jspx_page_context, _jspx_push_body_count_widget_005fheader_005f0))
            return true;
          out.write("\r\n");
          out.write("							<a href=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cancelAllUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" class=\"fonticon fonticon-trash reset-btn\"\r\n");
          out.write("							   title=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cancelAllTooltip}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"></a>\r\n");
          out.write("						");
          int evalDoAfterBody = _jspx_th_c_005fif_005f12.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f12.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f12);
      _jspx_th_c_005fif_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f12, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f12, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fheader_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f5 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f5_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f5.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f12);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(182,7) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f5.setVar("cancelAllTooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(182,7) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f5.setCode("widgets.activeFilters.tooltip.cancelAll");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(182,7) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f5.setText("Remove all filters");
      int[] _jspx_push_body_count_i18n_005fmessage_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f5 = _jspx_th_i18n_005fmessage_005f5.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f5);
      _jspx_th_i18n_005fmessage_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f5, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fnew_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f12, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fheader_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:new
    com.exalead.cv360.searchui.view.jspapi.list.NewTag _jspx_th_list_005fnew_005f2 = (com.exalead.cv360.searchui.view.jspapi.list.NewTag) _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.NewTag.class);
    boolean _jspx_th_list_005fnew_005f2_reused = false;
    try {
      _jspx_th_list_005fnew_005f2.setPageContext(_jspx_page_context);
      _jspx_th_list_005fnew_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f12);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(184,7) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fnew_005f2.setVar("feedIds");
      int[] _jspx_push_body_count_list_005fnew_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fnew_005f2 = _jspx_th_list_005fnew_005f2.doStartTag();
        if (_jspx_th_list_005fnew_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fnew_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fnew_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fnew_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_list_005fnew_005f2);
      _jspx_th_list_005fnew_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fnew_005f2, _jsp_getInstanceManager(), _jspx_th_list_005fnew_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachFeed_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f12, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fheader_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachFeed
    com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag _jspx_th_search_005fforEachFeed_005f2 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag) _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag.class);
    boolean _jspx_th_search_005fforEachFeed_005f2_reused = false;
    try {
      _jspx_th_search_005fforEachFeed_005f2.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachFeed_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f12);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(185,7) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f2.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(185,7) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f2.setVar("feed");
      int[] _jspx_push_body_count_search_005fforEachFeed_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachFeed_005f2 = _jspx_th_search_005fforEachFeed_005f2.doStartTag();
        if (_jspx_eval_search_005fforEachFeed_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("								");
            if (_jspx_meth_list_005fadd_005f2(_jspx_th_search_005fforEachFeed_005f2, _jspx_page_context, _jspx_push_body_count_search_005fforEachFeed_005f2))
              return true;
            out.write("\r\n");
            out.write("							");
            int evalDoAfterBody = _jspx_th_search_005fforEachFeed_005f2.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachFeed_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachFeed_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachFeed_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachFeed_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.reuse(_jspx_th_search_005fforEachFeed_005f2);
      _jspx_th_search_005fforEachFeed_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachFeed_005f2, _jsp_getInstanceManager(), _jspx_th_search_005fforEachFeed_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fadd_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFeed_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFeed_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:add
    com.exalead.cv360.searchui.view.jspapi.list.AddTag _jspx_th_list_005fadd_005f2 = (com.exalead.cv360.searchui.view.jspapi.list.AddTag) _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.AddTag.class);
    boolean _jspx_th_list_005fadd_005f2_reused = false;
    try {
      _jspx_th_list_005fadd_005f2.setPageContext(_jspx_page_context);
      _jspx_th_list_005fadd_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFeed_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(186,8) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f2.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed.id}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(186,8) name = list type = java.util.Collection reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f2.setList((java.util.Collection) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedIds}", java.util.Collection.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_list_005fadd_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fadd_005f2 = _jspx_th_list_005fadd_005f2.doStartTag();
        if (_jspx_th_list_005fadd_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fadd_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fadd_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fadd_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.reuse(_jspx_th_list_005fadd_005f2);
      _jspx_th_list_005fadd_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fadd_005f2, _jsp_getInstanceManager(), _jspx_th_list_005fadd_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fcontent_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f11, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:content
    com.exalead.cv360.searchui.view.jspapi.widget.ContentTag _jspx_th_widget_005fcontent_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.ContentTag) _005fjspx_005ftagPool_005fwidget_005fcontent.get(com.exalead.cv360.searchui.view.jspapi.widget.ContentTag.class);
    boolean _jspx_th_widget_005fcontent_005f0_reused = false;
    try {
      _jspx_th_widget_005fcontent_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fcontent_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f11);
      int[] _jspx_push_body_count_widget_005fcontent_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fcontent_005f0 = _jspx_th_widget_005fcontent_005f0.doStartTag();
        if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fcontent_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fcontent_005f0);
          }
          do {
            out.write("\r\n");
            out.write("						");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widgetContent}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\r\n");
            out.write("						");
            if (_jspx_meth_c_005fif_005f13(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("						");
            if (_jspx_meth_c_005fif_005f14(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("					");
            int evalDoAfterBody = _jspx_th_widget_005fcontent_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fcontent_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fcontent_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fcontent_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fcontent_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fcontent_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fcontent.reuse(_jspx_th_widget_005fcontent_005f0);
      _jspx_th_widget_005fcontent_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fcontent_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fcontent_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f13 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f13_reused = false;
    try {
      _jspx_th_c_005fif_005f13.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f13.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(194,6) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f13.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty customLinks && not empty customLinks[0] && not empty customLinks[0][0]}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f13 = _jspx_th_c_005fif_005f13.doStartTag();
      if (_jspx_eval_c_005fif_005f13 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("							<div class=\"secondaryTitle\">");
          if (_jspx_meth_i18n_005fmessage_005f6(_jspx_th_c_005fif_005f13, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("</div>\r\n");
          out.write("							");
          if (_jspx_meth_render_005ftemplate_005f5(_jspx_th_c_005fif_005f13, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("\r\n");
          out.write("						");
          int evalDoAfterBody = _jspx_th_c_005fif_005f13.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f13.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f13);
      _jspx_th_c_005fif_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f13, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f13, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f6 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f6_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f6.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f13);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(195,35) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f6.setCode("widgets.activeFilters.section.links");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(195,35) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f6.setText("links");
      int[] _jspx_push_body_count_i18n_005fmessage_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f6 = _jspx_th_i18n_005fmessage_005f6.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f6);
      _jspx_th_i18n_005fmessage_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f6, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f13, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f5 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f5);
    try {
      _jspx_th_render_005ftemplate_005f5.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f5.setParent(_jspx_th_c_005fif_005f13);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(197,7) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f5.setTemplate("templates/customLinks.jsp");
      _jspx_th_render_005ftemplate_005f5.setJspBody(new Helper( 5, _jspx_page_context, _jspx_th_render_005ftemplate_005f5, _jspx_push_body_count_widget_005fcontent_005f0));
      _jspx_th_render_005ftemplate_005f5.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f5);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f13 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f13);
    try {
      _jspx_th_render_005fparameter_005f13.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f13.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(198,8) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f13.setName("customLinks");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(198,8) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f13.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customLinks}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f13.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f13);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f14 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f14);
    try {
      _jspx_th_render_005fparameter_005f14.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f14.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(199,8) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f14.setName("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(199,8) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f14.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f14.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f14);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f14 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f14_reused = false;
    try {
      _jspx_th_c_005fif_005f14.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f14.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(202,6) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f14.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty customButtons && not empty customButtons[0] && not empty customButtons[0][0]}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f14 = _jspx_th_c_005fif_005f14.doStartTag();
      if (_jspx_eval_c_005fif_005f14 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("							<div class=\"secondaryTitle\">");
          if (_jspx_meth_i18n_005fmessage_005f7(_jspx_th_c_005fif_005f14, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("</div>\r\n");
          out.write("							");
          if (_jspx_meth_render_005ftemplate_005f6(_jspx_th_c_005fif_005f14, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("\r\n");
          out.write("						");
          int evalDoAfterBody = _jspx_th_c_005fif_005f14.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f14.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f14);
      _jspx_th_c_005fif_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f14, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f14, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f7 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f7_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f7.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f14);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(203,35) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f7.setCode("widgets.activeFilters.section.links");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(203,35) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f7.setText("links");
      int[] _jspx_push_body_count_i18n_005fmessage_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f7 = _jspx_th_i18n_005fmessage_005f7.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f7);
      _jspx_th_i18n_005fmessage_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f7, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f14, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f6 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f6);
    try {
      _jspx_th_render_005ftemplate_005f6.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f6.setParent(_jspx_th_c_005fif_005f14);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(205,7) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f6.setTemplate("templates/customButtons.jsp");
      _jspx_th_render_005ftemplate_005f6.setJspBody(new Helper( 6, _jspx_page_context, _jspx_th_render_005ftemplate_005f6, _jspx_push_body_count_widget_005fcontent_005f0));
      _jspx_th_render_005ftemplate_005f6.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f6);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f15(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f15 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f15);
    try {
      _jspx_th_render_005fparameter_005f15.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f15.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(206,8) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f15.setName("customButtons");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(206,8) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f15.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customButtons}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f15.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f15);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f16(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f16 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f16);
    try {
      _jspx_th_render_005fparameter_005f16.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f16.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(207,8) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f16.setName("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(207,8) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f16.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f16.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f16);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f17(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f17 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f17);
    try {
      _jspx_th_render_005fparameter_005f17.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f17.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(208,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f17.setName("positioning");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(208,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f17.setValue("right");
      _jspx_th_render_005fparameter_005f17.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f17);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f0_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f7);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(217,1) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f0.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f0 = _jspx_th_render_005frenderScript_005f0.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f0);
          }
          do {
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_request_005fgetParameterValue_005f1(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_search_005fgetFeed_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_c_005fset_005f9(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_plma_005fGetChartboardPageSecurity_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_string_005feval_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("		var options = {};\r\n");
            out.write("		options.displaySavedFilter = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${displaySavedFilter}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		options.pageName = '");
            if (_jspx_meth_url_005fgetPageName_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("		options.pageId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		options.dateParam = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${simpleDateRefine}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		options.feedName = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${nameOfFeed}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		options.readers = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${readers}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		options.writers = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${writers}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		options.user = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${user}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		new SavedFilters('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("', options);\r\n");
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_c_005fif_005f15(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write('\r');
            out.write('\n');
            out.write('	');
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f0);
      _jspx_th_render_005frenderScript_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetParameterValue_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getParameterValue
    com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag _jspx_th_request_005fgetParameterValue_005f1 = (com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag) _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag.class);
    boolean _jspx_th_request_005fgetParameterValue_005f1_reused = false;
    try {
      _jspx_th_request_005fgetParameterValue_005f1.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetParameterValue_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(218,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f1.setVar("pageId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(218,2) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f1.setName("pageId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(218,2) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f1.setDefaultValue("");
      int[] _jspx_push_body_count_request_005fgetParameterValue_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetParameterValue_005f1 = _jspx_th_request_005fgetParameterValue_005f1.doStartTag();
        if (_jspx_th_request_005fgetParameterValue_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetParameterValue_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetParameterValue_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetParameterValue_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_request_005fgetParameterValue_005f1);
      _jspx_th_request_005fgetParameterValue_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetParameterValue_005f1, _jsp_getInstanceManager(), _jspx_th_request_005fgetParameterValue_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFeed_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFeed
    com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag _jspx_th_search_005fgetFeed_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag) _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFeedTag.class);
    boolean _jspx_th_search_005fgetFeed_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFeed_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFeed_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(219,2) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeed_005f0.setVar("feed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(219,2) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFeed_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFeed_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFeed_005f0 = _jspx_th_search_005fgetFeed_005f0.doStartTag();
        if (_jspx_th_search_005fgetFeed_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFeed_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFeed_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFeed_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFeed_0026_005fvar_005ffeeds_005fnobody.reuse(_jspx_th_search_005fgetFeed_005f0);
      _jspx_th_search_005fgetFeed_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFeed_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFeed_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f9 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f9_reused = false;
    try {
      _jspx_th_c_005fset_005f9.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(220,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f9.setVar("nameOfFeed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(220,2) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f9.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(220,2) '${feed.id}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${feed.id}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f9 = _jspx_th_c_005fset_005f9.doStartTag();
      if (_jspx_th_c_005fset_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f9);
      _jspx_th_c_005fset_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fGetChartboardPageSecurity_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:GetChartboardPageSecurity
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.chartboard.GetChartboardPageSecurity _jspx_th_plma_005fGetChartboardPageSecurity_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.chartboard.GetChartboardPageSecurity) _005fjspx_005ftagPool_005fplma_005fGetChartboardPageSecurity_0026_005fwriteRights_005freadRights_005fpageId_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.chartboard.GetChartboardPageSecurity.class);
    boolean _jspx_th_plma_005fGetChartboardPageSecurity_005f0_reused = false;
    try {
      _jspx_th_plma_005fGetChartboardPageSecurity_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fGetChartboardPageSecurity_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(222,2) name = pageId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fGetChartboardPageSecurity_005f0.setPageId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(222,2) name = readRights type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fGetChartboardPageSecurity_005f0.setReadRights("readers");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(222,2) name = writeRights type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fGetChartboardPageSecurity_005f0.setWriteRights("writers");
      int[] _jspx_push_body_count_plma_005fGetChartboardPageSecurity_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fGetChartboardPageSecurity_005f0 = _jspx_th_plma_005fGetChartboardPageSecurity_005f0.doStartTag();
        if (_jspx_th_plma_005fGetChartboardPageSecurity_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fGetChartboardPageSecurity_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fGetChartboardPageSecurity_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fGetChartboardPageSecurity_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fGetChartboardPageSecurity_0026_005fwriteRights_005freadRights_005fpageId_005fnobody.reuse(_jspx_th_plma_005fGetChartboardPageSecurity_005f0);
      _jspx_th_plma_005fGetChartboardPageSecurity_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fGetChartboardPageSecurity_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fGetChartboardPageSecurity_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f0_reused = false;
    try {
      _jspx_th_string_005feval_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(224,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setVar("user");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(224,2) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setString("${security.username}");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(224,2) name = isJsEscape type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setIsJsEscape(true);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(224,2) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005feval_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f0 = _jspx_th_string_005feval_005f0.doStartTag();
        if (_jspx_th_string_005feval_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005fnobody.reuse(_jspx_th_string_005feval_005f0);
      _jspx_th_string_005feval_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f0, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_url_005fgetPageName_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  url:getPageName
    com.exalead.cv360.searchui.view.jspapi.url.GetPageNameTag _jspx_th_url_005fgetPageName_005f0 = (com.exalead.cv360.searchui.view.jspapi.url.GetPageNameTag) _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.url.GetPageNameTag.class);
    boolean _jspx_th_url_005fgetPageName_005f0_reused = false;
    try {
      _jspx_th_url_005fgetPageName_005f0.setPageContext(_jspx_page_context);
      _jspx_th_url_005fgetPageName_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int[] _jspx_push_body_count_url_005fgetPageName_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_url_005fgetPageName_005f0 = _jspx_th_url_005fgetPageName_005f0.doStartTag();
        if (_jspx_th_url_005fgetPageName_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_url_005fgetPageName_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_url_005fgetPageName_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_url_005fgetPageName_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody.reuse(_jspx_th_url_005fgetPageName_005f0);
      _jspx_th_url_005fgetPageName_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_url_005fgetPageName_005f0, _jsp_getInstanceManager(), _jspx_th_url_005fgetPageName_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f15(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f15 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f15_reused = false;
    try {
      _jspx_th_c_005fif_005f15.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f15.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(236,2) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f15.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${enableSuggest}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f15 = _jspx_th_c_005fif_005f15.doStartTag();
      if (_jspx_eval_c_005fif_005f15 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("			var currentFilters = {\r\n");
          out.write("				refined: [],\r\n");
          out.write("				excluded: []\r\n");
          out.write("			};\r\n");
          out.write("			");
          if (_jspx_meth_search_005fforEachFacet_005f0(_jspx_th_c_005fif_005f15, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
            return true;
          out.write("\r\n");
          out.write("			var facetsList = {};\r\n");
          out.write("			");
          if (_jspx_meth_search_005fforEachFacet_005f1(_jspx_th_c_005fif_005f15, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("			$('.quickFilters').quickFilters({\r\n");
          out.write("				suggestUrl: '");
          if (_jspx_meth_c_005furl_005f0(_jspx_th_c_005fif_005f15, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
            return true;
          out.write("',\r\n");
          out.write("				pageId: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("				pageName: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("				wuid: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget.wuid}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("				feedName: '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${nameOfFeed}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("',\r\n");
          out.write("				facetsList: facetsList,\r\n");
          out.write("				droppableItemsSelector: '");
          if (_jspx_meth_config_005fgetOption_005f9(_jspx_th_c_005fif_005f15, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
            return true;
          out.write("',\r\n");
          out.write("				currentFilters: currentFilters\r\n");
          out.write("			});\r\n");
          out.write("		");
          int evalDoAfterBody = _jspx_th_c_005fif_005f15.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f15.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f15);
      _jspx_th_c_005fif_005f15_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f15, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f15_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachFacet_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f15, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachFacet
    com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag _jspx_th_search_005fforEachFacet_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag) _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvar_005ffeeds.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag.class);
    boolean _jspx_th_search_005fforEachFacet_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachFacet_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachFacet_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f15);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(242,3) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setVar("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(242,3) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fforEachFacet_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachFacet_005f0 = _jspx_th_search_005fforEachFacet_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachFacet_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_search_005fforEachCategory_005f0(_jspx_th_search_005fforEachFacet_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFacet_005f0))
              return true;
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_search_005fforEachCategory_005f1(_jspx_th_search_005fforEachFacet_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFacet_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_search_005fforEachFacet_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachFacet_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachFacet_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachFacet_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachFacet_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvar_005ffeeds.reuse(_jspx_th_search_005fforEachFacet_005f0);
      _jspx_th_search_005fforEachFacet_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachFacet_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachFacet_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachCategory_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFacet_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFacet_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachCategory
    com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag _jspx_th_search_005fforEachCategory_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag) _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowState_005froot_005fiterationMode.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag.class);
    boolean _jspx_th_search_005fforEachCategory_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachCategory_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachCategory_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFacet_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(243,4) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setVar("category");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(243,4) name = root type = com.exalead.access.feedapi.CategoryTreeContainer reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setRoot((com.exalead.access.feedapi.CategoryTreeContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.CategoryTreeContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(243,4) name = showState type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setShowState("REFINED");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(243,4) name = iterationMode type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setIterationMode("ALL");
      int[] _jspx_push_body_count_search_005fforEachCategory_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachCategory_005f0 = _jspx_th_search_005fforEachCategory_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachCategory_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("					");
            if (_jspx_meth_string_005fescape_005f0(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("					currentFilters.refined.push('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${catId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("');\r\n");
            out.write("				");
            int evalDoAfterBody = _jspx_th_search_005fforEachCategory_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachCategory_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachCategory_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachCategory_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachCategory_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowState_005froot_005fiterationMode.reuse(_jspx_th_search_005fforEachCategory_005f0);
      _jspx_th_search_005fforEachCategory_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachCategory_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachCategory_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f0_reused = false;
    try {
      _jspx_th_string_005fescape_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(244,5) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setVar("catId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(244,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005fescape_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f0 = _jspx_th_string_005fescape_005f0.doStartTag();
        if (_jspx_th_string_005fescape_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_string_005fescape_005f0);
      _jspx_th_string_005fescape_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f0, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachCategory_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFacet_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFacet_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachCategory
    com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag _jspx_th_search_005fforEachCategory_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag) _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowState_005froot_005fiterationMode.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag.class);
    boolean _jspx_th_search_005fforEachCategory_005f1_reused = false;
    try {
      _jspx_th_search_005fforEachCategory_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachCategory_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFacet_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(247,4) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f1.setVar("category");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(247,4) name = root type = com.exalead.access.feedapi.CategoryTreeContainer reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f1.setRoot((com.exalead.access.feedapi.CategoryTreeContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.CategoryTreeContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(247,4) name = showState type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f1.setShowState("EXCLUDED");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(247,4) name = iterationMode type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f1.setIterationMode("ALL");
      int[] _jspx_push_body_count_search_005fforEachCategory_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachCategory_005f1 = _jspx_th_search_005fforEachCategory_005f1.doStartTag();
        if (_jspx_eval_search_005fforEachCategory_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("					");
            if (_jspx_meth_string_005fescape_005f1(_jspx_th_search_005fforEachCategory_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f1))
              return true;
            out.write("\r\n");
            out.write("					currentFilters.excluded.push('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${catId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("');\r\n");
            out.write("				");
            int evalDoAfterBody = _jspx_th_search_005fforEachCategory_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachCategory_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachCategory_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachCategory_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachCategory_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowState_005froot_005fiterationMode.reuse(_jspx_th_search_005fforEachCategory_005f1);
      _jspx_th_search_005fforEachCategory_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachCategory_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fforEachCategory_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f1 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f1_reused = false;
    try {
      _jspx_th_string_005fescape_005f1.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(248,5) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setVar("catId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(248,5) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005fescape_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f1 = _jspx_th_string_005fescape_005f1.doStartTag();
        if (_jspx_th_string_005fescape_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_string_005fescape_005f1);
      _jspx_th_string_005fescape_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f1, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachFacet_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f15, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachFacet
    com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag _jspx_th_search_005fforEachFacet_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag) _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvarStatus_005fvar_005ffeeds.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag.class);
    boolean _jspx_th_search_005fforEachFacet_005f1_reused = false;
    try {
      _jspx_th_search_005fforEachFacet_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachFacet_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f15);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(253,3) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f1.setVar("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(253,3) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f1.setVarStatus("status");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(253,3) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f1.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fforEachFacet_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachFacet_005f1 = _jspx_th_search_005fforEachFacet_005f1.doStartTag();
        if (_jspx_eval_search_005fforEachFacet_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("				");
            if (_jspx_meth_search_005fgetFacetLabel_005f0(_jspx_th_search_005fforEachFacet_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachFacet_005f1))
              return true;
            out.write("\r\n");
            out.write("				facetsList['");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("'] = '");
            if (_jspx_meth_string_005fescape_005f2(_jspx_th_search_005fforEachFacet_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachFacet_005f1))
              return true;
            out.write("';\r\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_search_005fforEachFacet_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachFacet_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachFacet_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachFacet_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachFacet_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvarStatus_005fvar_005ffeeds.reuse(_jspx_th_search_005fforEachFacet_005f1);
      _jspx_th_search_005fforEachFacet_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachFacet_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fforEachFacet_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacetLabel_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFacet_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFacet_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacetLabel
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag _jspx_th_search_005fgetFacetLabel_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag) _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag.class);
    boolean _jspx_th_search_005fgetFacetLabel_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFacetLabel_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacetLabel_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFacet_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(254,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetLabel_005f0.setVar("facetLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(254,4) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetLabel_005f0.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacetLabel_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacetLabel_005f0 = _jspx_th_search_005fgetFacetLabel_005f0.doStartTag();
        if (_jspx_th_search_005fgetFacetLabel_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacetLabel_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacetLabel_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacetLabel_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody.reuse(_jspx_th_search_005fgetFacetLabel_005f0);
      _jspx_th_search_005fgetFacetLabel_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacetLabel_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacetLabel_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFacet_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFacet_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f2 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f2_reused = false;
    try {
      _jspx_th_string_005fescape_005f2.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFacet_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(255,33) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f2.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(255,33) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f2.setEscapeType("JAVASCRIPT");
      int[] _jspx_push_body_count_string_005fescape_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f2 = _jspx_th_string_005fescape_005f2.doStartTag();
        if (_jspx_th_string_005fescape_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f2);
      _jspx_th_string_005fescape_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f2, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f15, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f0 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f0_reused = false;
    try {
      _jspx_th_c_005furl_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f15);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(259,17) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f0.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("/plma/suggest/widget/${feedName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int _jspx_eval_c_005furl_005f0 = _jspx_th_c_005furl_005f0.doStartTag();
      if (_jspx_th_c_005furl_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f0);
      _jspx_th_c_005furl_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f0, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f15, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f9 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f9_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f9.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f15);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/activeFilters/widget.jsp(265,29) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setName("draggableFiltersSelector");
      int[] _jspx_push_body_count_config_005fgetOption_005f9 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f9 = _jspx_th_config_005fgetOption_005f9.doStartTag();
        if (_jspx_th_config_005fgetOption_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f9[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f9.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f9.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f9);
      _jspx_th_config_005fgetOption_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f9, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f9_reused);
    }
    return false;
  }

  private class Helper
      extends org.apache.jasper.runtime.JspFragmentHelper
  {
    private jakarta.servlet.jsp.tagext.JspTag _jspx_parent;
    private int[] _jspx_push_body_count;

    public Helper( int discriminator, jakarta.servlet.jsp.JspContext jspContext, jakarta.servlet.jsp.tagext.JspTag _jspx_parent, int[] _jspx_push_body_count ) {
      super( discriminator, jspContext, _jspx_parent );
      this._jspx_parent = _jspx_parent;
      this._jspx_push_body_count = _jspx_push_body_count;
    }
    public boolean invoke0( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("								");
      if (_jspx_meth_render_005fparameter_005f0(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("								");
      if (_jspx_meth_render_005fparameter_005f1(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("							");
      return false;
    }
    public boolean invoke1( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("					");
      if (_jspx_meth_render_005fparameter_005f2(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("					");
      if (_jspx_meth_render_005fparameter_005f3(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("				");
      return false;
    }
    public boolean invoke2( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("					");
      if (_jspx_meth_render_005fparameter_005f4(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("					");
      if (_jspx_meth_render_005fparameter_005f5(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("					");
      if (_jspx_meth_render_005fparameter_005f6(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                    ");
      if (_jspx_meth_render_005fparameter_005f7(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("				");
      return false;
    }
    public boolean invoke3( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("					");
      if (_jspx_meth_render_005fparameter_005f8(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("					");
      if (_jspx_meth_render_005fparameter_005f9(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("				");
      return false;
    }
    public boolean invoke4( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("					");
      if (_jspx_meth_render_005fparameter_005f10(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("					");
      if (_jspx_meth_render_005fparameter_005f11(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                    ");
      if (_jspx_meth_render_005fparameter_005f12(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("				");
      return false;
    }
    public boolean invoke5( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("								");
      if (_jspx_meth_render_005fparameter_005f13(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("								");
      if (_jspx_meth_render_005fparameter_005f14(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("							");
      return false;
    }
    public boolean invoke6( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("								");
      if (_jspx_meth_render_005fparameter_005f15(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("								");
      if (_jspx_meth_render_005fparameter_005f16(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f17(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("							");
      return false;
    }
    public void invoke( java.io.Writer writer )
      throws jakarta.servlet.jsp.JspException
    {
      jakarta.servlet.jsp.JspWriter out = null;
      if( writer != null ) {
        out = this.jspContext.pushBody(writer);
      } else {
        out = this.jspContext.getOut();
      }
      try {
        Object _jspx_saved_JspContext = this.jspContext.getELContext().getContext(jakarta.servlet.jsp.JspContext.class);
        this.jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,this.jspContext);
        switch( this.discriminator ) {
          case 0:
            invoke0( out );
            break;
          case 1:
            invoke1( out );
            break;
          case 2:
            invoke2( out );
            break;
          case 3:
            invoke3( out );
            break;
          case 4:
            invoke4( out );
            break;
          case 5:
            invoke5( out );
            break;
          case 6:
            invoke6( out );
            break;
        }
        jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,_jspx_saved_JspContext);
      }
      catch( java.lang.Throwable e ) {
        if (e instanceof jakarta.servlet.jsp.SkipPageException)
            throw (jakarta.servlet.jsp.SkipPageException) e;
        throw new jakarta.servlet.jsp.JspException( e );
      }
      finally {
        if( writer != null ) {
          this.jspContext.popBody();
        }
      }
    }
  }
}
