.hit-checkbox-container {
	width: 16px;
	height: 16px;
	position: absolute;
	top: 5px;
	right: 8px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	&.select-all-button {
		bottom: 8px;
		right: 15px;
		top: auto;
		width: 18px;
		height: 18px;
		.hit-checkbox-label {
			width: 18px;
			height: 18px;			
		}
	}
	input {
		position: absolute;
		opacity: 0;
		cursor: pointer;
		~ .hit-checkbox-label {
			background-color: #f1f1f1;
			border: 1px solid #b4b6ba;
			border-radius: 2px;
		}
		&:checked {
			~ .hit-checkbox-label {
				background-color: #42A2DA;
				border-color: #368EC4;
				&:after {
					display: block;
				}
			}
		}
		&:disabled {
			~ .hit-checkbox-label {
				background-color: #f9f9f9;
				border-color: #f1f1f1;
			}
		}
	}
	.hit-checkbox-label {
		position: absolute;
		top: 0;
		left: 0;
		height: 16px;
		width: 16px;
		&:after {
			content: "";
			position: absolute;
			display: none;
			left: 4px;
			top: 0;
			width: 5px;
			height: 10px;
			border: solid white;
			border-width: 0 3px 3px 0;
			-webkit-transform: rotate(45deg);
			-ms-transform: rotate(45deg);
			transform: rotate(45deg);
			color: white;
		}
	}
	&:hover {
		input {
			~ .hit-checkbox-label {
				background-color: #E2E4E3;
				border-color: #77797C;
			}
			&:checked {
				~ .hit-checkbox-label {
					background-color: #368EC4;
					border-color: #003C5A;
				}
			}
			&:disabled {
				~ .hit-checkbox-label {
					background-color: #f9f9f9;
					border-color: #f1f1f1;
				}
			}
		}
	}
}

