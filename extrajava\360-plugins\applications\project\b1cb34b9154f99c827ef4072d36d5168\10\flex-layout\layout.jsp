<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>

<render:import parameters="layout,parentFeed,parentEntry" />
<render:import parameters="prefix,tableCssClass,wrapperCssClass" ignore="true" />

<div class="flex-layout-wrapper<c:if test="${wrapperCssClass != null && wrapperCssClass != ''}"> ${wrapperCssClass}</c:if>">

	<c:set var="widthFormat" value="${layout.widthFormat}" />
	<c:if test="${widthFormat == null || widthFormat == ''}">
		<c:set var="widthFormat" value="%" />
	</c:if>

	<c:forEach var="table" varStatus="tableStatus" items="${layout.tables}">
		<c:forEach var="row" items="${table.rows}" varStatus="rowStatus">
			<config:getOption var="rowId" name="cssId" component="${row}" defaultValue="${cssId}_row_${rowStatus.index}" />
			<config:getOption var="rowCssClass" name="cssClass" component="${row}" defaultValue="" />
			
			<map:new var="colWidth"/>
			<c:forEach var="colConfig" items="${table.colsConfig}" varStatus="colStatus">
				<map:put key="${colStatus.index}" value="${colConfig.width}" map="${colWidth}"/>
			</c:forEach>
			
			<div id="${rowId}" 
				class="flex-layout-row flex-layout-row-${tableStatus.index}-${rowStatus.index} ${not empty rowCssClass ? rowCssClass : ''} ${wrap == 'true' ? 'wrap-panels' : ''}">
				<c:forEach varStatus="statusCell" var="cell" items="${row.cells}">
					<config:getOption var="cellId" name="cssId" component="${cell}" defaultValue="${rowId}_cell_${statusCell.index}" />
					<config:getOption var="cellCssClass" name="cssClass" component="${cell}" defaultValue="" />
					<div id="${cellId}" class="flex-layout-item flex-layout-col-${colWidth[statusCell.index]} flex-layout-item-${tableStatus.index}-${rowStatus.index}-${statusCell.index}<c:if test="${cellCssClass != null && cellCssClass != ''}"> ${cellCssClass}</c:if>">
						<widget:forEachSubWidget widgetContainer="${cell}" feed="${parentFeed}" entry="${parentEntry}">
							<render:widget />
						</widget:forEachSubWidget>
					</div>
				</c:forEach>
			</div>
		</c:forEach>
	</c:forEach>
	
</div>