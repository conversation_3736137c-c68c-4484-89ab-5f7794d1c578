<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA flex container with header" group="PLM Analytics/Layout/Flex display" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>This widget is a flex container which renders header (title on left side and toolbar on right side).
		Some features can be simply activated by option like full screen mode and information.</Description>
	
	<Includes>
    	<Include type="css" path="css/style.less" />
		<Include type="js" path="js/plmaFlexContainerWithHeader.js"/>
    </Includes>
    
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportWidgetsId arity="ZERO_OR_MANY" displayType="LAYOUT" />
	<SupportI18N supported="true"/>

	<Dependencies>
		<Widget name="preferences" />
		<Widget name="plmaResources" />
	</Dependencies>
	
	<OptionsGroup name="General">
		<Option id="width" name="Width" arity="ZERO_OR_ONE">
			<Description>Specifies the table width. Defaults to '100'.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="widthFormat" name="Width format" arity="ZERO_OR_ONE">
			<Description>Specifies the width format of the table. Defaults to '%'.</Description>
			<Values>
				<Value></Value>
				<Value>%</Value>
				<Value>px</Value>
			</Values>
		</Option>
		<Option id="buttonsIconSize" name="Icons font size" arity="ZERO_OR_ONE"  isEvaluated="true">
			<Description>Specifies buttons font size (in pixels). You must enter an integer. Default inherited.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<OptionComposite id="variables" name="Additional CSS variables" arity="ZERO_OR_MANY" glue="##" isEvaluated="true">
			<Description>Add variables to widget div, it can be useful to configure CSS using variables for example.</Description>
			<Option id="name" name="Name" arity="ONE" isEvaluated="true">
				<Description>Variable name to add.</Description>
				<Functions>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="value" name="Value" arity="ONE" isEvaluated="true">
				<Description>Aggregation function expression.</Description>
				<Functions>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
		</OptionComposite>
		<Option name="Collapsible container" id="collapsible" arity="ONE">
			<Description>Collapsible container (add button to toolbar).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option name="Container with border" id="addBorder" arity="ONE">
			<Description>Add border to container div.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Header">
		<Description>Specifies header configuration. Title can be configured through a MEL expression or JSP path;
			if nothing is configured and 'headerContainer' is configured as subwidget, this subwidget is rendered in header title div </Description>
		<Option id="title" name="Title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Header title (can be empty).</Description>
		</Option>
		<Option id="titleTemplatePath" name="Title JSP path" arity="ZERO_OR_ONE">
			<Description>Title JSP path (can be empty).</Description>
		</Option>
		<Option id="titleTemplateWidget" name="Title JSP widget" arity="ZERO_OR_ONE">
			<Description>Title JSP widget (can be empty).</Description>
		</Option>
		<Option id="headerCSS" name="Header additional CSS class" arity="ZERO_OR_ONE">
			<Description>Header div additional CSS class, can be useful to add custom CSS behavior.</Description>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Toolbar">
		<Description>Specifies toolbar configuration. If nothing is configured and 'toolbarContainer' is configured as
			subwidget, this subwidget is rendered in header toolbar div </Description>
		<Option name="Enable full screen" id="enableFullScreen" arity="ONE">
			<Description>Display full screen button in toolbar.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="toolbarTemplatePath" name="Toolbar JSP path" arity="ZERO_OR_ONE">
			<Description>Toolbar JSP path (can be empty).</Description>
		</Option>
		<Option id="toolbarTemplateWidget" name="Toolbar JSP widget" arity="ZERO_OR_ONE">
			<Description>Toolbar JSP widget (can be empty).</Description>
		</Option>
		<Option id="toolbarCSS" name="Toolbar additional CSS class" arity="ZERO_OR_ONE">
			<Description>Toolbar div additional CSS class, can be useful to add custom CSS behavior.</Description>
		</Option>
	</OptionsGroup>


	<OptionsGroup name="Advanced">
		<Option id="templateBasePath" name="Base path of the JSP templates" arity="ZERO_OR_ONE">
			<Description>You can replace the relative path by an absolute path like /WEB-INF/jsp/mydirectory/.</Description>
			<Functions>
				<Check>isDirectory</Check>
			</Functions>
		</Option>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="templateBasePath">templates/</DefaultValue>
		<DefaultValue name="collapsible">false</DefaultValue>
		<DefaultValue name="enableFullScreen">false</DefaultValue>
		<DefaultValue name="addBorder">false</DefaultValue>
	</DefaultValues>
</Widget>
