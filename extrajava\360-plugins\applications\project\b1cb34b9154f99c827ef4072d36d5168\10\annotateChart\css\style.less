@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/styles/responsive.less";
@import "../../plmaResources/css/styles/variables.less";


.mashup .searchWidget.plmaChartAnnotation{
    padding:0;
    margin:0;
    .hidden {
        display: none;
    }
}

button.annotating{
    height: 32px;
    padding: 0 5px;
    position: absolute;
    right: 0;
    margin-right: 15px;
    margin-top: 15px;
    z-index: 1000;
}

//this property hide svg element which should not be rendered, as we use label as HTML ...
.highcharts-label-box.highcharts-annotation-label-box{
    visibility: hidden;
}

.annotation-mode{
    cursor: crosshair;
	border: 1px solid @clink-active;
	box-sizing: border-box;
	.highcharts-point {
		cursor: crosshair;
	}
    .highcharts-label.highcharts-annotation-label{
        cursor:move;

        > span{
            &:hover{
                background:@cblock-bg-lite;
            }
        }
    }
}

/* set always visible as highcharts may set hidden when annotation are overlapping */
.annotation-always-visible(){
    visibility: visible !important;
    opacity: 1 !important;
}

.highcharts-label.highcharts-annotation-label {
    /* set always visible on annotation container & content */
    .annotation-always-visible();
    > span{
        .annotation-always-visible();
    }

    .resize{
        background: black;
        width: 6px;
        height: 6px;
        position: absolute;
        right: 0;
        bottom: 0;
        cursor: se-resize;
    }

    i.remove-annotation{
        cursor:pointer;
        position: relative;
        top: -6px;
        right: 3px;
        background: white;
        border-radius: 25px;
        width: 15px;
        height: 15px;
        border: 1px solid;
        font-size: 10px;

        &:before{
            position: relative;
            top: 2px;
        }
    }

    > span{
        white-space: inherit !important;
        padding: 5px;
        border: 1px solid;
        overflow-wrap: break-word;
        min-width:10px !important;
		overflow: hidden;
		max-height: 225px;
    }
}

.annotation-popup.plmalightbox-box{
    background:white;
    //to popup a head of full screen
    z-index: 20000;

    .annotation-title{
        padding: 15px;
        background: @clink;
        color: white;
    }

    .annotation-input-container{
        .display-flex();
        padding: 15px;
    }

    .annotation-author{
        .display-flex();
        padding: 15px;
        .justify-content(flex-end);
    }

    .annotation-action{
        .display-flex();
        .justify-content(flex-end);
        padding-right: 15px;
        padding-bottom: 15px;

        > button{
            margin-right: 5px;
        }
    }
}
