/**
 * An object to manage layout panels in a given container.
 *
 * Usage:
 * var $mainContainer = $('#mainContainer'); // The DOM element containing the panels. Panels are identified with the "collapsiblePanel" class.
 * var options = {toggle: false, cookie: false};
 * var myPanelManager = new PanelManager($mainContainer, options);
 *
 */
(function () {

	/*  CONSTANTS ********************/
	var COLLAPSIBLE_PANEL_CLASS = 'collapsiblePanel';
	var COOKIE_NAME = 'panelManagement';
	var defaults = {
		/* Whether to close other panels when one is opened. */
		toggle: false,
		/* Whether to store the state of the panels in a cookie. Can be either false or
		 * a string representing a key where the state will be stored. */
		cookie: false
	};


	/* UTILITY METHODS ***************/
	var openPanel = function ($panel) {
		if (!isOpen($panel)) {
			$panel.removeClass('hidden');
			$panel.trigger('plma:panel-open');
			/* Resize highcharts */
			$(window).trigger('plma:resize', [300]);
		}
	};

	var closePanel = function ($panel) {
		if (isOpen($panel)) {
			$panel.addClass('hidden');
			$panel.trigger('plma:panel-close');
			/* Resize highcharts */
			$(window).trigger('plma:resize', [300]);
		}
	};

	var activate = function ($button) {
		$button.addClass('active');
	};

	var deactivate = function ($button) {
		$button.removeClass('active');
	};

	var isOpen = function ($panel) {
		return !$panel.hasClass('hidden');
	};

	var loadState = function (key) {
		var state = [];
		if ($.cookie) {
			var cookieValue = $.cookie(COOKIE_NAME);
			if (cookieValue) {
				state = JSON.parse(cookieValue)[key];
			}
		} else {
			throw new Error('Missing $.cookie library.');
		}
		return state;
	};

	var saveState = function (key, state) {
		if ($.cookie) {
			var cookieValue = $.cookie(COOKIE_NAME);
			var globalState;
			if (cookieValue) {
				globalState = JSON.parse(cookieValue);
			} else {
				globalState = {};
			}
			globalState[key] = state;
			$.cookie(COOKIE_NAME, JSON.stringify(globalState), {path: '/'});
		} else {
			throw new Error('Missing $.cookie library.');
		}
	};

	/* CONSTRUCTOR *******************/
	var PanelManager = function (mainContainer, options) {
		this.options = $.extend({}, defaults, options);
		this.mainContainer = mainContainer;
		mainContainer.data('PanelManager', this);
		this.$panels = mainContainer.find('> .' + COLLAPSIBLE_PANEL_CLASS);
        if(this.options.$panels && this.options.$panels.length >0){
            this.$panels = this.options.$panels;
        }
		this._panels = {};
		this.state = [];
		this.loadState();

		//Close panel from other widget
		$(document).on('plma:close-refine-panel', $.proxy(function (event, data) {
			this.toggle('refinesPanel');
            this.deactiveSidebarBtn('refinesPanel');
		}, this));

		return this;
	};

	/* PROTOTYPE METHODS *************/
	PanelManager.prototype.getPanel = function (panelName) {
		if (!this._panels.hasOwnProperty(panelName)) {
			this._panels[panelName] = this.$panels.filter('.' + panelName);
		}
		return this._panels[panelName];
	};

	/**
	 * Calls open() or close() on the specified panel depending on it being respectively closed or open.
	 */
	PanelManager.prototype.toggle = function (panelName) {
		if (isOpen(this.getPanel(panelName))) {
			this.close(panelName);
		} else {
			this.open(panelName);
		}
	};

    /**
     * Deactive a menu item from the sidebar if item has class cssClass
     */
    PanelManager.prototype.deactiveSidebarBtn = function (cssClass) {
        $(".sideBarMenu").find(".menu-item").each(function(i, e) {
            var $item = $(e);
            if ($item.data('togglepanel') === cssClass) {
                $item.removeClass('active');
                return false;
            }
        });
    };


	/**
	 * Opens the specified panel, and close the other collapsible panels if the 'toggle' option is
	 * set to true.
	 */
	PanelManager.prototype.open = function (panelName) {
		if (this.options.toggle) {
			this.closeAll(panelName);
		}
		openPanel(this.getPanel(panelName));
		if (this.state.indexOf(panelName) === -1) {
			this.state.push(panelName);
		}
		this.saveState();
	};

	/**
	 * Closes the specified panel.
	 */
	PanelManager.prototype.close = function (panelName) {
		closePanel(this.getPanel(panelName));
		var index = this.state.indexOf(panelName)
		if (index > -1) {
			this.state.splice(index, 1);
		}
		this.saveState();
	};

	/**
	 * Closes all panels except panelName. This latter is the one which initiated the call of this function.
	 * @param {String} panelName - Name of the panel that remains open
	 */
	PanelManager.prototype.closeAll = function (panelName) {
		closePanel(this.$panels.not(this.getPanel(panelName)));
		this.state = [panelName];
		this.saveState();
	};

	/**
	 * Will make the provided '$button' jQuery element open or close the specified
	 * panel.
	 */
	PanelManager.prototype.registerButton = function ($button, panelName) {
		$button.on('click.panelmanager', $.proxy(function () {
			this.toggle(panelName);
		}, this));
		this.getPanel(panelName)
			.on('plma:panel-open.panelmanager', function () {
				activate($button);
			})
			.on('plma:panel-close.panelmanager', function () {
				deactivate($button);
			});
		if (isOpen(this.getPanel(panelName))) {
			activate($button);
		}
	};

    PanelManager.disableSideBarBtn = function($panel, cssClass){
        if($panel.length>0){
            $panel.on("plma:panel-close", function(e){
                var $menuItems = $('.'+cssClass).find('.menu-item');
                $menuItems.each(function(i, menuItem){
                    if ($(menuItem).data('togglepanel') === panelClass) {
                        $(menuItem).removeClass('active');
                    }
                });
            });
        }
    };

	/**
	 * Reads the state from the cookie (if enabled) and apply it.
	 */
	PanelManager.prototype.loadState = function () {
		if (this.options.cookie) {
			this.state = loadState(this.options.cookie);
			for (var i = 0; i < this.state.length; i++) {
				this.open(this.state[i]);
			}
		}
	};

	/**
	 * If enabled, stores the current state in the cookie.
	 */
	PanelManager.prototype.saveState = function () {
		if (this.options.cookie) {
			saveState(this.options.cookie, this.state);
		}
	};

	window.PanelManager = PanelManager;

})();


