<?xml version="1.0" encoding='UTF-8'?>
<Widget name="3DExperience - Publish To 3DDashboard Button" group="PLM Analytics/Layout" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description><![CDATA[
<p> Publish Button helps publish the content to 3DExperience.<br />
<b>What to publish?</b><br />
&nbsp;&nbsp;<b>Dynamic Context:</b> Publish the Current context(direct url), to enable opening/reopening the datshboard/app/content-page,filters. <br />
&nbsp;&nbsp;<b>Snapshots: </b> Publish the PDF snapshot of the current page.<br />
<i>If you Drop back the Dynamic Context to Exalead widget, it will open the same context as available in object. (Incase the object has multiple page contexts, the first published context will be loaded). </i>
<br />
<b>Where to publish?</b><br />
&nbsp;&nbsp;<b>Bookmark :</b> Publish to Document(new/existing) object and attach to existing bookmark selected.<br />
&nbsp;&nbsp;<b>Swym :</b> Publish to selected community either as Post,Idea,or question or in conversation.<br />
</p>
        ]]></Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/plmaButton/images/preview.png" alt="PLMA Button" />
        ]]>
	</Preview>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="css" path="css/publishto3ddashboardform.less" />

		<Include type="js" path="js/publishTo3DDashboard.js"/>
		<Include type="js" path="js/SwymPublishManager.js"/>
		<Include type="js" path="js/publishTo3DDashboardUtils.js"/>
		<Include type="js" path="../plmaResources/js/exportTo3DSpace/documentManager3DSpace.js"/>
		<Include type="js" path="../plmaResources/js/exportTo3DSpace/exportTo3DSpace.js"/>
		<Include type="js" path="../plmaResources/js/exportTo3DSpace/EXPUtils.js"/>

	</Includes>

	<Dependencies>
		<Widget name="plmaResources" />
		<Widget name="pdfCreator" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO_OR_ONE" label="pdfCreator or Equivalent" >
	</SupportWidgetsId>
	<SupportFeedsId arity="ZERO_OR_MANY" />
	<SupportI18N supported="true">
		<JsKeys>
		</JsKeys>
	</SupportI18N>

	<OptionsGroup name="General">
		<Description><![CDATA[
			<i>NOTE: if the <b>subwidget(pdfCreator or Equivalent)</b> is provided, Then File Export will be enabled.</i>
		]]></Description>
		<Option id="iconCss" name="Icon CSS" >
			<Description>Specifies the CSS class name of the icon added to the button. If empty, default published icon is displayed.</Description>
		</Option>
		<Option id="label" name="Label" isEvaluated="true" arity="ONE">
			<Description>Specifies the label of the publish button. Briefly explains the purpose of the button (3 words max).</Description>
		</Option>
		<Option id="showLabel" name="Show label" arity="ONE">
			<Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="onInit" name="On init" isEvaluated="true">
			<Description>The JavaScript code that is executed once when the button is loaded on the page.</Description>
			<Placeholder>function() {}</Placeholder>
			<Values>
				<Value>function() {
  /* Use 'this' to handle the button as a jQuery object.*/
}</Value>
			</Values>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="onDone" name="On Done" isEvaluated="true">
			<Description>The JavaScript code that is executed when the Publish Form is displayed . </Description>
			<Placeholder>function(e) {}</Placeholder>
			<Values>
				<Value>function() {
  /* Use 'this' to handle the button as a jQuery object.*/
}</Value>
			</Values>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="publishToSwym" name="Enable Publish to Swym" arity="ONE">
			<Description>Publish to Swym Community.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="publishToBookmark" name="Enable Publish to Bookmarks" arity="ONE">
			<Description>Publish to Bookmark Editor.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="enableReloadContext" name="Enable Context Reload" arity="ONE">
			<Description>If the document object is droped then do we want to read the description to find the context link and reload
			the context. Default 'true'</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>
	
	<DefaultValues displayName="Normal" previewUrl="">
		<DefaultValue name="iconCss">fonticon fonticon-publish</DefaultValue>
		<DefaultValue name="showLabel">true</DefaultValue>
		<DefaultValue name="enableReloadContext">true</DefaultValue>
	</DefaultValues>

</Widget>
