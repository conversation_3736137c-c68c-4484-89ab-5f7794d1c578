var CreatePDF = function(fileProperties, options, showPopup){
	if(showPopup && CreatePDF.lightbox == undefined){
		CreatePDF.lightbox = new PLMALightbox({
			title: 'PDF Preview',
			content: $('<div class="preview-container pdf-preview-container"></div>'),
			extraCss: 'create-pdf-preview',
			onShow: (function(){
			}).bind(this),
			onHide: (function(){
				this.$previewContainer.empty();
			}).bind(this),
			draggable : false
		});
	}
	this.$previewContainer = $('.create-pdf-preview .pdf-preview-container');
	this.fileProperties = fileProperties;
    this.options = $.extend({}, CreatePDF.DEFAULT_OPTIONS, options);
	this.showPopup = showPopup;
	return this;
}

CreatePDF.lightbox = undefined;

CreatePDF.prototype.setPreviewContainer = function($previewContainer){
	this.$previewContainer = $previewContainer;
}
CreatePDF.prototype.getPreviewContainer = function(){
	return this.$previewContainer;
}

CreatePDF.prototype.create = function(widgetsInfoArray){
	if(this.showPopup){ CreatePDF.lightbox.show(); }
	this.$previewContainer.showPLMASpinner({overlay: true});
	
	return new Promise(function(resolve,reject) {
		var doc = new jspdf.jsPDF(this.options.orientation, this.options.unit);
		doc.setDocumentProperties({
			'creator': this.fileProperties.creator,
			'title': this.fileProperties.title,
			'author': this.fileProperties.author
		});
		
		let headerFooterInfo = {
			header:{
				title: this.options.pageheader.title,
				description: this.options.pageheader.description,
				date: moment().format(this.options.pageheader.dateFormat),
				logoPromise: CreatePDFUtils.fetchImage(this.options.pageheader.logoPath, this.options.pageheader.logoPath.split(".").pop())
			},
			footer:{
				delimeter:this.options.pagefooter.delimeter,
				copyright: this.options.pagefooter.copyright
			},
			activeFilters: {
				label: this.options.activeFilters.label,
				filters: $(this.options.activeFilters.selector)
			}
		}

		let builder = new PLMAPDFBuilder('default', this.options, this.options.template);
		let pagePromiseList = [];
		let widgetsNotFound = [];
		widgetsInfoArray.map(function(widgetsInfo){
		    //If no widget exist on the dom based wid.selector skip that particular iteration
			let $widgets = $(widgetsInfo.selector);

		    if($widgets.length == 0){
				widgetsNotFound.push(widgetsInfo);
				return;
			}

			widgetsInfo.headerSelector =  widgetsInfo.headerSelector? widgetsInfo.headerSelector : '.widgetHeader .widgetTitle',

			$widgets.each(function(i, widget){
				let widgetsInfoCp = JSON.parse(JSON.stringify(widgetsInfo))
				widgetsInfoCp.$widget = $(widget);
				let pagePromise = builder.buildPage(doc, headerFooterInfo, widgetsInfoCp);
				pagePromiseList.push(pagePromise);
				pagePromise.then(function(pageBuilder){
					doc.addPage();
				});
			}.bind(this));
		});

		if(widgetsNotFound.length > 0){
		    $.notify('Few of the configured widgets are not loaded in the page. Hence ignored', 'warn');
			console.error('Few of the configured widgets are not loaded in the page.\n WidgetList: ', widgetsNotFound);
			console.warn('Please load widgets before stating pdf creation. You can use On Init and onDone methods.');
		}
		// Handle All promises within the Loop and return document only when prepared
        Promise.all(pagePromiseList).then(function(){
			doc.deletePage(doc.internal.getNumberOfPages());
			builder.complete(doc);
			this.$previewContainer.hidePLMASpinner();
			resolve(doc);
        }.bind(this))
		.catch(function(err){
			this.$previewContainer.hidePLMASpinner();
			reject("Error while create snapshots of widget.May be options are missing or are wrong.");
			console.error("Error while create snapshots of widget.May be options are missing or are wrong.",err);
        });		
	}.bind(this));
}

CreatePDF.showPreview = function($previewContainer, pdfDoc, fileName, id){
	if (typeof pdfDoc !== "undefined" && pdfDoc.getNumberOfPages() > 0){
		try {
			let dataUrl = pdfDoc.output("datauristring");
			let $link = $('<a>',{
				class: 'download-link',
				download: fileName,
				onclick: "this.setAttribute('href', this.children[0].getAttribute('src'))" // TO enable download.
			});
			$link.append($('<span>', {
				text: fileName,
				src: dataUrl,
				alt: fileName,
				class: "fonticon fonticon-download"
			}));
			
			$previewContainer
				.append($link)
				.append('<div class="pdf-preview-pane"></div>');
			PDFObject.embed(dataUrl, $previewContainer.find('.pdf-preview-pane'), {
				id: id
			});
		} catch (e) {
			$.notify('Unable to render PDF Preview. Internal error', 'error');
			console.log("Error " + e);
		}
	}else{
		$.notify('Unable to render PDF Preview, as PDFDocument is empty!', 'error');
	}
}

CreatePDF.DEFAULT_OPTIONS = {
  template:PDFDefaultTemplate,
  pageheader: {
    title: $('.pageTitleWidget h1.pageTitle').text(),
    description: '',
    dateFormat: 'DD-MMM-YYYY, hh:mm:ss A',
    logoPath: mashup.baseUrl + '/resources/logo/images/cvlogo_mobile.png'
  },
  pagefooter: {
    delimeter:'/',
    copyright:'Dassault Systemes',
  },
  activeFilters: {
    label: $('.stickyContainer .activeFilters .widgetTitle').text(),
    selector: '.stickyContainer .activeFilters .activeFilter' 
  },
  widgetHeader: {
    height: 20,
    fontSize: 12
  },
  widgetContent: {
    fontSize: 10
  },
  
  /* jsPDF Option*/
  orientation:'p', /*portrait*/
  unit: 'pt',
  format: 'a4',
  putOnlyUsedFonts:true,
  fontFamily: 'times',
  fontSize: 14,
  textColor: '#000000',
  margin: {
    top: 15,
    bottom: 15,
    left: 10,
    right: 10
  },
  header: {
    height: 50,
    fontSize: 10,
    titleFontSize: 18
  },
  footer: {
    height: 30,
    fontSize: 10
  }
}

CreatePDFUtils = (function(){
	return{
		fetchImage: function(url, type){
			return fetch(url)
				.then((response) => {
					if (!response.ok) {
						throw Error(response.statusText);
					}
					return response.blob();
				}).then((blob) => {				
					return new Promise(function(resolve,reject) {
						var reader = new FileReader();
						reader.onload = function(){ 
							//this.result` contains a base64 data URI	
							resolve({
								dataUrl: this.result,
								type: type
							});
						};
						reader.readAsDataURL(blob);
					});
				}).catch(function(error) {
					console.log(error);
				});
		},
		/*  getTableContent
		*   @ params
		*   $widget : table widget html element
		*   @return {Object} - header and row content of table
		*/
		getTableContent: function($widget){
            return {
                header : CreatePDFUtils.getTableHeader($widget),
                rows: CreatePDFUtils.getTableRows($widget)
            };
		},
		/*  getTableHeader
        *   @ params
        *   $widget : table widget html element
        *   @return [] - header of every column in the table
        */
		getTableHeader: function($widget) {
            let _this = this;
            const columnHeader = [];
            const header= [];
            //find the first table only incase of enable scroll header option in data table clones of header gets created so just push the original header
            $widget.find('table:first thead tr th:visible').each(function(){
               columnHeader.push($(this).html().trim());
            });
            if(columnHeader.length > 0)
                header.push(columnHeader);
            return header;
		},
		/*  getTableRows
        *   @ params
        *   $widget : table widget html element
        *   @return [[]] - Array of row contents
        */
		getTableRows: function($widget) {
		    let _this = this;
            const rows = [];
            $widget.find('tbody > tr').each(function(){
                const rowContent = [];
                $(this).find('td:visible').each(function(){
                    rowContent.push($(this).html().trim());
                });
                rows.push(rowContent);
            });
            return rows;
		}
	}
})();