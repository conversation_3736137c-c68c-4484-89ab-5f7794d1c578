/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:21:11 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.recentSearches.templates;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class hits_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_0;

static {
  _jspx_fnmap_0= org.apache.jasper.runtime.ProtectedFunctionMapper.getInstance();
  _jspx_fnmap_0.mapFunction("fn:length", org.apache.taglibs.standard.functions.Functions.class, "length", new Class[] {java.lang.Object.class});
  _jspx_fnmap_0.mapFunction("fn:split", org.apache.taglibs.standard.functions.Functions.class, "split", new Class[] {java.lang.String.class, java.lang.String.class});
}

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(11);
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/i18n.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fmt.tld", Long.valueOf(1602854172000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvar_005ffeed;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fisHtmlEscaped_005ffeeds_005fentry_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetMetaValue_0026_005fvar_005fmetaName_005fentry_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffacetId_005fentry_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005froot;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005fentry_005fcategory_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetCategoryColor_0026_005fvar_005fcategory_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005ffeeds_005fentry_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachMeta_0026_005fvar_005fshowEmptyMetas_005fmetasList_005ffilterMode_005fentry;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetMetaLabel_0026_005fvar_005fmeta_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachMetaValue_0026_005fvarStatus_005fvarRaw_005fvar_005fmeta_005fentry;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvarStatus_005fvar_005fshowEmptyFacets_005ffilterMode_005ffacetsList_005fentry;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005ffacetId_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005froot_005fiterationMode;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetCategoryLabel_0026_005fvar_005fcategory_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005ffmt_005fformatNumber_0026_005fvar_005fvalue_005ftype_005fpattern_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fend_005fbegin;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvar_005ffeed = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fisHtmlEscaped_005ffeeds_005fentry_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetMetaValue_0026_005fvar_005fmetaName_005fentry_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffacetId_005fentry_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005froot = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005fentry_005fcategory_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetCategoryColor_0026_005fvar_005fcategory_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005ffeeds_005fentry_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachMeta_0026_005fvar_005fshowEmptyMetas_005fmetasList_005ffilterMode_005fentry = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetMetaLabel_0026_005fvar_005fmeta_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachMetaValue_0026_005fvarStatus_005fvarRaw_005fvar_005fmeta_005fentry = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvarStatus_005fvar_005fshowEmptyFacets_005ffilterMode_005ffacetsList_005fentry = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005ffacetId_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005froot_005fiterationMode = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryLabel_0026_005fvar_005fcategory_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005ffmt_005fformatNumber_0026_005fvar_005fvalue_005ftype_005fpattern_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fend_005fbegin = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvar_005ffeed.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fisHtmlEscaped_005ffeeds_005fentry_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetMetaValue_0026_005fvar_005fmetaName_005fentry_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffacetId_005fentry_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005froot.release();
    _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005fentry_005fcategory_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetCategoryColor_0026_005fvar_005fcategory_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005ffeeds_005fentry_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachMeta_0026_005fvar_005fshowEmptyMetas_005fmetasList_005ffilterMode_005fentry.release();
    _005fjspx_005ftagPool_005fsearch_005fgetMetaLabel_0026_005fvar_005fmeta_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachMetaValue_0026_005fvarStatus_005fvarRaw_005fvar_005fmeta_005fentry.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvarStatus_005fvar_005fshowEmptyFacets_005ffilterMode_005ffacetsList_005fentry.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005ffacetId_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005froot_005fiterationMode.release();
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryLabel_0026_005fvar_005fcategory_005fnobody.release();
    _005fjspx_005ftagPool_005ffmt_005fformatNumber_0026_005fvar_005fvalue_005ftype_005fpattern_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fend_005fbegin.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("<div class=\"history hidden\">\r\n");
      out.write("	");
      if (_jspx_meth_c_005fset_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      out.write('	');
      if (_jspx_meth_search_005fforEachEntry_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("</div>\r\n");
      out.write("<div class=\"pagination\">\r\n");
      out.write("	");
      if (_jspx_meth_c_005fif_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("</div>");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(11,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setParameters("feed,iconCss,collectionId");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(13,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setName("elemPerPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(13,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setVar("elemPerPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(13,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setDefaultValue("4");
      int[] _jspx_push_body_count_config_005fgetOption_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f0 = _jspx_th_config_005fgetOption_005f0.doStartTag();
        if (_jspx_th_config_005fgetOption_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f0);
      _jspx_th_config_005fgetOption_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(14,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setName("metaForId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(14,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setVar("metaForId");
      int[] _jspx_push_body_count_config_005fgetOption_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f1 = _jspx_th_config_005fgetOption_005f1.doStartTag();
        if (_jspx_th_config_005fgetOption_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f1);
      _jspx_th_config_005fgetOption_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(15,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setVar("facetId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(15,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setName("facetId");
      int[] _jspx_push_body_count_config_005fgetOption_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f2 = _jspx_th_config_005fgetOption_005f2.doStartTag();
        if (_jspx_th_config_005fgetOption_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f2);
      _jspx_th_config_005fgetOption_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f0_reused = false;
    try {
      _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(18,1) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setVar("entriesCount");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(18,1) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(18,1) '${0}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${0}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
      if (_jspx_th_c_005fset_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
      _jspx_th_c_005fset_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachEntry_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachEntry
    com.exalead.cv360.searchui.view.jspapi.search.ForEachEntryTag _jspx_th_search_005fforEachEntry_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachEntryTag) _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvar_005ffeed.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachEntryTag.class);
    boolean _jspx_th_search_005fforEachEntry_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachEntry_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachEntry_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(19,1) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachEntry_005f0.setVar("entry");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(19,1) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachEntry_005f0.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fforEachEntry_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachEntry_005f0 = _jspx_th_search_005fforEachEntry_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachEntry_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_c_005fset_005f1(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_config_005fgetOption_005f3(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_search_005fgetMetaValue_005f0(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_search_005fgetFacet_005f0(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_search_005fforEachCategory_005f0(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("		<a class=\"hidden\" href=\"#\" title=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${description}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\">\r\n");
            out.write("			<div data-entry-id=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${currentEntryId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" class=\"entry\">\r\n");
            out.write("				<div class=\"header\">\r\n");
            out.write("					<div class=\"header-container\">\r\n");
            out.write("						<div class=\"icon-container\">\r\n");
            out.write("							<div class=\"icon\" data-color=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${typeCategoryColor}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\">\r\n");
            out.write("								<i class=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${typeCategoryIcon}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\"></i>\r\n");
            out.write("							</div>\r\n");
            out.write("						</div>\r\n");
            out.write("						<div class=\"title\">\r\n");
            out.write("							");
            if (_jspx_meth_string_005fescape_005f2(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("						</div>\r\n");
            out.write("					</div>\r\n");
            out.write("					<div class=\"main-container\">\r\n");
            out.write("						");
            if (_jspx_meth_config_005fgetOption_005f5(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("						");
            if (_jspx_meth_search_005fforEachMeta_005f0(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("					</div>\r\n");
            out.write("					<div class=\"facets\">\r\n");
            out.write("						");
            if (_jspx_meth_config_005fgetOption_005f6(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("						");
            if (_jspx_meth_search_005fforEachFacet_005f0(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("					</div>\r\n");
            out.write("				</div>\r\n");
            out.write("				<span class=\"collection-icons-div fonticon ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${iconCss}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\">				\r\n");
            out.write("			</div>\r\n");
            out.write("		</a>\r\n");
            out.write("	");
            int evalDoAfterBody = _jspx_th_search_005fforEachEntry_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachEntry_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachEntry_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachEntry_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachEntry_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvar_005ffeed.reuse(_jspx_th_search_005fforEachEntry_005f0);
      _jspx_th_search_005fforEachEntry_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachEntry_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachEntry_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f1_reused = false;
    try {
      _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(20,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setVar("entriesCount");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(20,2) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(20,2) '${entriesCount + 1}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${entriesCount + 1}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
      if (_jspx_th_c_005fset_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
      _jspx_th_c_005fset_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fisHtmlEscaped_005ffeeds_005fentry_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f3_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(21,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setVar("description");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(21,2) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setName("description");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(21,2) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(21,2) name = feeds type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(21,2) name = isHtmlEscaped type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setIsHtmlEscaped(true);
      int[] _jspx_push_body_count_config_005fgetOption_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f3 = _jspx_th_config_005fgetOption_005f3.doStartTag();
        if (_jspx_th_config_005fgetOption_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fisHtmlEscaped_005ffeeds_005fentry_005fnobody.reuse(_jspx_th_config_005fgetOption_005f3);
      _jspx_th_config_005fgetOption_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetMetaValue_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getMetaValue
    com.exalead.cv360.searchui.view.jspapi.search.GetMetaValueTag _jspx_th_search_005fgetMetaValue_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetMetaValueTag) _005fjspx_005ftagPool_005fsearch_005fgetMetaValue_0026_005fvar_005fmetaName_005fentry_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetMetaValueTag.class);
    boolean _jspx_th_search_005fgetMetaValue_005f0_reused = false;
    try {
      _jspx_th_search_005fgetMetaValue_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetMetaValue_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(22,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetMetaValue_005f0.setVar("currentEntryId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(22,2) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetMetaValue_005f0.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(22,2) name = metaName type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetMetaValue_005f0.setMetaName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${metaForId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetMetaValue_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetMetaValue_005f0 = _jspx_th_search_005fgetMetaValue_005f0.doStartTag();
        if (_jspx_th_search_005fgetMetaValue_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetMetaValue_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetMetaValue_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetMetaValue_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetMetaValue_0026_005fvar_005fmetaName_005fentry_005fnobody.reuse(_jspx_th_search_005fgetMetaValue_005f0);
      _jspx_th_search_005fgetMetaValue_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetMetaValue_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetMetaValue_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacet_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacet
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag _jspx_th_search_005fgetFacet_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag) _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffacetId_005fentry_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag.class);
    boolean _jspx_th_search_005fgetFacet_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFacet_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacet_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(23,2) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f0.setVar("typeFacet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(23,2) name = facetId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f0.setFacetId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(23,2) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f0.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacet_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacet_005f0 = _jspx_th_search_005fgetFacet_005f0.doStartTag();
        if (_jspx_th_search_005fgetFacet_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacet_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacet_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacet_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffacetId_005fentry_005fnobody.reuse(_jspx_th_search_005fgetFacet_005f0);
      _jspx_th_search_005fgetFacet_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacet_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacet_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachCategory_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachCategory
    com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag _jspx_th_search_005fforEachCategory_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag) _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005froot.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag.class);
    boolean _jspx_th_search_005fforEachCategory_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachCategory_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachCategory_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(24,2) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setVar("typeCategory");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(24,2) name = root type = com.exalead.access.feedapi.CategoryTreeContainer reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setRoot((com.exalead.access.feedapi.CategoryTreeContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${typeFacet}", com.exalead.access.feedapi.CategoryTreeContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fforEachCategory_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachCategory_005f0 = _jspx_th_search_005fforEachCategory_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachCategory_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_plma_005fgetIconName_005f0(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_string_005fescape_005f0(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_plma_005fgetCategoryColor_005f0(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_string_005fescape_005f1(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("		");
            int evalDoAfterBody = _jspx_th_search_005fforEachCategory_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachCategory_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachCategory_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachCategory_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachCategory_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005froot.reuse(_jspx_th_search_005fforEachCategory_005f0);
      _jspx_th_search_005fforEachCategory_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachCategory_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachCategory_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetIconName_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getIconName
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryIconTag _jspx_th_plma_005fgetIconName_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryIconTag) _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005fentry_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryIconTag.class);
    boolean _jspx_th_plma_005fgetIconName_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetIconName_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetIconName_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(25,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetIconName_005f0.setVar("typeCategoryIcon");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(25,3) name = category type = com.exalead.access.feedapi.Category reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetIconName_005f0.setCategory((com.exalead.access.feedapi.MergedCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${typeCategory}", com.exalead.access.feedapi.MergedCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(25,3) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetIconName_005f0.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetIconName_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetIconName_005f0 = _jspx_th_plma_005fgetIconName_005f0.doStartTag();
        if (_jspx_th_plma_005fgetIconName_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetIconName_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetIconName_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetIconName_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005fentry_005fcategory_005fnobody.reuse(_jspx_th_plma_005fgetIconName_005f0);
      _jspx_th_plma_005fgetIconName_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetIconName_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetIconName_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f0_reused = false;
    try {
      _jspx_th_string_005fescape_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(26,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setVar("typeCategoryIcon");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(26,3) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${typeCategoryIcon}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(26,3) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f0 = _jspx_th_string_005fescape_005f0.doStartTag();
        if (_jspx_th_string_005fescape_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f0);
      _jspx_th_string_005fescape_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f0, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetCategoryColor_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getCategoryColor
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryColorTag _jspx_th_plma_005fgetCategoryColor_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryColorTag) _005fjspx_005ftagPool_005fplma_005fgetCategoryColor_0026_005fvar_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryColorTag.class);
    boolean _jspx_th_plma_005fgetCategoryColor_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetCategoryColor_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetCategoryColor_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(27,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryColor_005f0.setVar("typeCategoryColor");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(27,3) name = category type = com.exalead.access.feedapi.Category reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryColor_005f0.setCategory((com.exalead.access.feedapi.MergedCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${typeCategory}", com.exalead.access.feedapi.MergedCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetCategoryColor_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetCategoryColor_005f0 = _jspx_th_plma_005fgetCategoryColor_005f0.doStartTag();
        if (_jspx_th_plma_005fgetCategoryColor_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetCategoryColor_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetCategoryColor_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetCategoryColor_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetCategoryColor_0026_005fvar_005fcategory_005fnobody.reuse(_jspx_th_plma_005fgetCategoryColor_005f0);
      _jspx_th_plma_005fgetCategoryColor_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetCategoryColor_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetCategoryColor_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f1 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f1_reused = false;
    try {
      _jspx_th_string_005fescape_005f1.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(28,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setVar("typeCategoryColor");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(28,3) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${typeCategoryColor}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(28,3) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f1 = _jspx_th_string_005fescape_005f1.doStartTag();
        if (_jspx_th_string_005fescape_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f1);
      _jspx_th_string_005fescape_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f1, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f2 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f2_reused = false;
    try {
      _jspx_th_string_005fescape_005f2.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(40,7) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f2.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f2 = _jspx_th_string_005fescape_005f2.doStartTag();
        if (_jspx_eval_string_005fescape_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_string_005fescape_005f2 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_string_005fescape_005f2[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_string_005fescape_005f2);
          }
          do {
            out.write("\r\n");
            out.write("								");
            if (_jspx_meth_config_005fgetOption_005f4(_jspx_th_string_005fescape_005f2, _jspx_page_context, _jspx_push_body_count_string_005fescape_005f2))
              return true;
            out.write("\r\n");
            out.write("							");
            int evalDoAfterBody = _jspx_th_string_005fescape_005f2.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_string_005fescape_005f2 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_string_005fescape_005f2[0]--;
          }
        }
        if (_jspx_th_string_005fescape_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType.reuse(_jspx_th_string_005fescape_005f2);
      _jspx_th_string_005fescape_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f2, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_string_005fescape_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_string_005fescape_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f4 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005ffeeds_005fentry_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f4_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f4.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_string_005fescape_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(41,8) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setName("content");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(41,8) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(41,8) name = feeds type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_config_005fgetOption_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f4 = _jspx_th_config_005fgetOption_005f4.doStartTag();
        if (_jspx_th_config_005fgetOption_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005ffeeds_005fentry_005fnobody.reuse(_jspx_th_config_005fgetOption_005f4);
      _jspx_th_config_005fgetOption_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f4, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f5 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f5_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f5.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(46,6) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setVar("metasList");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(46,6) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setName("metas");
      int[] _jspx_push_body_count_config_005fgetOption_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f5 = _jspx_th_config_005fgetOption_005f5.doStartTag();
        if (_jspx_th_config_005fgetOption_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f5);
      _jspx_th_config_005fgetOption_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f5, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachMeta_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachMeta
    com.exalead.cv360.searchui.view.jspapi.search.ForEachMetaTag _jspx_th_search_005fforEachMeta_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachMetaTag) _005fjspx_005ftagPool_005fsearch_005fforEachMeta_0026_005fvar_005fshowEmptyMetas_005fmetasList_005ffilterMode_005fentry.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachMetaTag.class);
    boolean _jspx_th_search_005fforEachMeta_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachMeta_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachMeta_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(47,6) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachMeta_005f0.setVar("meta");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(47,6) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachMeta_005f0.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(47,6) name = filterMode type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachMeta_005f0.setFilterMode("INCLUDE");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(47,6) name = metasList type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachMeta_005f0.setMetasList((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${metasList}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(47,6) name = showEmptyMetas type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachMeta_005f0.setShowEmptyMetas(java.lang.Boolean.valueOf("true"));
      int[] _jspx_push_body_count_search_005fforEachMeta_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachMeta_005f0 = _jspx_th_search_005fforEachMeta_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachMeta_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("							");
            if (_jspx_meth_search_005fgetMetaLabel_005f0(_jspx_th_search_005fforEachMeta_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMeta_005f0))
              return true;
            out.write("\r\n");
            out.write("							");
            if (_jspx_meth_c_005fset_005f2(_jspx_th_search_005fforEachMeta_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMeta_005f0))
              return true;
            out.write("\r\n");
            out.write("							");
            out.write("\r\n");
            out.write("							");
            if (_jspx_meth_c_005fset_005f3(_jspx_th_search_005fforEachMeta_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMeta_005f0))
              return true;
            out.write("\r\n");
            out.write("							");
            if (_jspx_meth_search_005fforEachMetaValue_005f0(_jspx_th_search_005fforEachMeta_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMeta_005f0))
              return true;
            out.write("\r\n");
            out.write("							<div class=\"metas\">\r\n");
            out.write("								<div class=\"type\">");
            if (_jspx_meth_string_005fescape_005f3(_jspx_th_search_005fforEachMeta_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMeta_005f0))
              return true;
            out.write("</div>\r\n");
            out.write("								<div class=\"meta-values\">");
            if (_jspx_meth_string_005fescape_005f4(_jspx_th_search_005fforEachMeta_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMeta_005f0))
              return true;
            out.write("</div>\r\n");
            out.write("							</div>\r\n");
            out.write("						");
            int evalDoAfterBody = _jspx_th_search_005fforEachMeta_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachMeta_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachMeta_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachMeta_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachMeta_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachMeta_0026_005fvar_005fshowEmptyMetas_005fmetasList_005ffilterMode_005fentry.reuse(_jspx_th_search_005fforEachMeta_005f0);
      _jspx_th_search_005fforEachMeta_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachMeta_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachMeta_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetMetaLabel_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachMeta_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMeta_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getMetaLabel
    com.exalead.cv360.searchui.view.jspapi.search.GetMetaLabelTag _jspx_th_search_005fgetMetaLabel_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetMetaLabelTag) _005fjspx_005ftagPool_005fsearch_005fgetMetaLabel_0026_005fvar_005fmeta_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetMetaLabelTag.class);
    boolean _jspx_th_search_005fgetMetaLabel_005f0_reused = false;
    try {
      _jspx_th_search_005fgetMetaLabel_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetMetaLabel_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachMeta_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(48,7) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetMetaLabel_005f0.setVar("metaLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(48,7) name = meta type = com.exalead.access.feedapi.Meta reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetMetaLabel_005f0.setMeta((com.exalead.access.feedapi.Meta) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${meta}", com.exalead.access.feedapi.Meta.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetMetaLabel_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetMetaLabel_005f0 = _jspx_th_search_005fgetMetaLabel_005f0.doStartTag();
        if (_jspx_th_search_005fgetMetaLabel_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetMetaLabel_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetMetaLabel_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetMetaLabel_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetMetaLabel_0026_005fvar_005fmeta_005fnobody.reuse(_jspx_th_search_005fgetMetaLabel_005f0);
      _jspx_th_search_005fgetMetaLabel_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetMetaLabel_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetMetaLabel_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachMeta_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMeta_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f2_reused = false;
    try {
      _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachMeta_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(49,7) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setVar("metaValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(49,7) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(49,7) ''",_jsp_getExpressionFactory().createValueExpression("",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
      if (_jspx_th_c_005fset_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
      _jspx_th_c_005fset_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachMeta_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMeta_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f3_reused = false;
    try {
      _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachMeta_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(51,7) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setVar("rawMetaValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(51,7) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(51,7) ''",_jsp_getExpressionFactory().createValueExpression("",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
      if (_jspx_th_c_005fset_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
      _jspx_th_c_005fset_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachMetaValue_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachMeta_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMeta_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachMetaValue
    com.exalead.cv360.searchui.view.jspapi.search.ForEachMetaValueTag _jspx_th_search_005fforEachMetaValue_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachMetaValueTag) _005fjspx_005ftagPool_005fsearch_005fforEachMetaValue_0026_005fvarStatus_005fvarRaw_005fvar_005fmeta_005fentry.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachMetaValueTag.class);
    boolean _jspx_th_search_005fforEachMetaValue_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachMetaValue_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachMetaValue_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachMeta_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(52,7) name = meta type = com.exalead.access.feedapi.Meta reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachMetaValue_005f0.setMeta((com.exalead.access.feedapi.Meta) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${meta}", com.exalead.access.feedapi.Meta.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(52,7) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachMetaValue_005f0.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(52,7) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachMetaValue_005f0.setVar("value");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(52,7) name = varRaw type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachMetaValue_005f0.setVarRaw("rawValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(52,7) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachMetaValue_005f0.setVarStatus("loop");
      int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachMetaValue_005f0 = _jspx_th_search_005fforEachMetaValue_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachMetaValue_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("								");
            if (_jspx_meth_c_005fchoose_005f0(_jspx_th_search_005fforEachMetaValue_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMetaValue_005f0))
              return true;
            out.write("\r\n");
            out.write("								");
            if (_jspx_meth_c_005fif_005f0(_jspx_th_search_005fforEachMetaValue_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMetaValue_005f0))
              return true;
            out.write("\r\n");
            out.write("								");
            if (_jspx_meth_c_005fif_005f1(_jspx_th_search_005fforEachMetaValue_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMetaValue_005f0))
              return true;
            out.write("\r\n");
            out.write("							");
            int evalDoAfterBody = _jspx_th_search_005fforEachMetaValue_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachMetaValue_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachMetaValue_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachMetaValue_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachMetaValue_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachMetaValue_0026_005fvarStatus_005fvarRaw_005fvar_005fmeta_005fentry.reuse(_jspx_th_search_005fforEachMetaValue_005f0);
      _jspx_th_search_005fforEachMetaValue_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachMetaValue_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachMetaValue_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachMetaValue_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachMetaValue_005f0);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("									");
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMetaValue_005f0))
            return true;
          out.write("\r\n");
          out.write("									");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMetaValue_005f0))
            return true;
          out.write("\r\n");
          out.write("								");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(54,9) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fn:length(fn:split(value,'#')) > 1}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_0)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("										");
          if (_jspx_meth_c_005fset_005f4(_jspx_th_c_005fwhen_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMetaValue_005f0))
            return true;
          out.write("\r\n");
          out.write("										");
          if (_jspx_meth_c_005fset_005f5(_jspx_th_c_005fwhen_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMetaValue_005f0))
            return true;
          out.write("\r\n");
          out.write("									");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f4_reused = false;
    try {
      _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(55,10) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setVar("metaValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(55,10) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(55,10) '${metaValue}${fn:split(value,'#')[1]}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_0),"${metaValue}${fn:split(value,'#')[1]}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
      if (_jspx_th_c_005fset_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
      _jspx_th_c_005fset_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f5 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f5_reused = false;
    try {
      _jspx_th_c_005fset_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(56,10) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setVar("rawMetaValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(56,10) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(56,10) '${rawMetaValue}${fn:split(rawValue,'#')[1]}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_0),"${rawMetaValue}${fn:split(rawValue,'#')[1]}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f5 = _jspx_th_c_005fset_005f5.doStartTag();
      if (_jspx_th_c_005fset_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f5);
      _jspx_th_c_005fset_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("										");
          if (_jspx_meth_c_005fset_005f6(_jspx_th_c_005fotherwise_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMetaValue_005f0))
            return true;
          out.write("\r\n");
          out.write("										");
          if (_jspx_meth_c_005fset_005f7(_jspx_th_c_005fotherwise_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMetaValue_005f0))
            return true;
          out.write("\r\n");
          out.write("									");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f6 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f6_reused = false;
    try {
      _jspx_th_c_005fset_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(59,10) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setVar("metaValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(59,10) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(59,10) '${metaValue}${value}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${metaValue}${value}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f6 = _jspx_th_c_005fset_005f6.doStartTag();
      if (_jspx_th_c_005fset_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f6);
      _jspx_th_c_005fset_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f7 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f7_reused = false;
    try {
      _jspx_th_c_005fset_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(60,10) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setVar("rawMetaValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(60,10) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(60,10) '${rawMetaValue}${rawValue}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${rawMetaValue}${rawValue}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f7 = _jspx_th_c_005fset_005f7.doStartTag();
      if (_jspx_th_c_005fset_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f7);
      _jspx_th_c_005fset_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachMetaValue_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachMetaValue_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(63,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not loop.last}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          if (_jspx_meth_c_005fset_005f8(_jspx_th_c_005fif_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachMetaValue_005f0))
            return true;
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f8 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f8_reused = false;
    try {
      _jspx_th_c_005fset_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(63,38) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f8.setVar("metaValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(63,38) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f8.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(63,38) '${metaValue}, '",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${metaValue}, ",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f8 = _jspx_th_c_005fset_005f8.doStartTag();
      if (_jspx_th_c_005fset_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f8);
      _jspx_th_c_005fset_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachMetaValue_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachMetaValue_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(64,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not loop.last}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          if (_jspx_meth_c_005fset_005f9(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachMetaValue_005f0))
            return true;
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMetaValue_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f9 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f9_reused = false;
    try {
      _jspx_th_c_005fset_005f9.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(64,38) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f9.setVar("rawMetaValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(64,38) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f9.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(64,38) '${rawMetaValue}, '",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${rawMetaValue}, ",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f9 = _jspx_th_c_005fset_005f9.doStartTag();
      if (_jspx_th_c_005fset_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f9);
      _jspx_th_c_005fset_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachMeta_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMeta_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f3 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f3_reused = false;
    try {
      _jspx_th_string_005fescape_005f3.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachMeta_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(67,26) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f3.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f3 = _jspx_th_string_005fescape_005f3.doStartTag();
        if (_jspx_eval_string_005fescape_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_string_005fescape_005f3 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_string_005fescape_005f3[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_string_005fescape_005f3);
          }
          do {
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${metaLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            int evalDoAfterBody = _jspx_th_string_005fescape_005f3.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_string_005fescape_005f3 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_string_005fescape_005f3[0]--;
          }
        }
        if (_jspx_th_string_005fescape_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType.reuse(_jspx_th_string_005fescape_005f3);
      _jspx_th_string_005fescape_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f3, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachMeta_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachMeta_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f4 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f4_reused = false;
    try {
      _jspx_th_string_005fescape_005f4.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachMeta_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(68,33) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f4.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f4 = _jspx_th_string_005fescape_005f4.doStartTag();
        if (_jspx_eval_string_005fescape_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_string_005fescape_005f4 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_string_005fescape_005f4[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_string_005fescape_005f4);
          }
          do {
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${metaValue}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            int evalDoAfterBody = _jspx_th_string_005fescape_005f4.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_string_005fescape_005f4 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_string_005fescape_005f4[0]--;
          }
        }
        if (_jspx_th_string_005fescape_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fescapeType.reuse(_jspx_th_string_005fescape_005f4);
      _jspx_th_string_005fescape_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f4, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f6 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f6_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f6.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(73,6) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setVar("hitFacetsList");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(73,6) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setName("hitFacetsList");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(73,6) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f6 = _jspx_th_config_005fgetOption_005f6.doStartTag();
        if (_jspx_th_config_005fgetOption_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f6);
      _jspx_th_config_005fgetOption_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f6, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachFacet_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachFacet
    com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag _jspx_th_search_005fforEachFacet_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag) _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvarStatus_005fvar_005fshowEmptyFacets_005ffilterMode_005ffacetsList_005fentry.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag.class);
    boolean _jspx_th_search_005fforEachFacet_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachFacet_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachFacet_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(74,6) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setVar("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(74,6) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(74,6) name = filterMode type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setFilterMode("INCLUDE");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(74,6) name = facetsList type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setFacetsList((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hitFacetsList}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(74,6) name = showEmptyFacets type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setShowEmptyFacets(java.lang.Boolean.valueOf("true"));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(74,6) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setVarStatus("loop");
      int[] _jspx_push_body_count_search_005fforEachFacet_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachFacet_005f0 = _jspx_th_search_005fforEachFacet_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachFacet_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("							");
            if (_jspx_meth_search_005fgetFacetLabel_005f0(_jspx_th_search_005fforEachFacet_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFacet_005f0))
              return true;
            out.write("\r\n");
            out.write("							");
            if (_jspx_meth_string_005fescape_005f5(_jspx_th_search_005fforEachFacet_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFacet_005f0))
              return true;
            out.write("\r\n");
            out.write("							");
            if (_jspx_meth_plma_005fgetIconName_005f1(_jspx_th_search_005fforEachFacet_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFacet_005f0))
              return true;
            out.write("\r\n");
            out.write("							");
            if (_jspx_meth_string_005fescape_005f6(_jspx_th_search_005fforEachFacet_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFacet_005f0))
              return true;
            out.write("\r\n");
            out.write("							");
            if (_jspx_meth_search_005fforEachCategory_005f1(_jspx_th_search_005fforEachFacet_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFacet_005f0))
              return true;
            out.write("\r\n");
            out.write("						");
            int evalDoAfterBody = _jspx_th_search_005fforEachFacet_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachFacet_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachFacet_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachFacet_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachFacet_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvarStatus_005fvar_005fshowEmptyFacets_005ffilterMode_005ffacetsList_005fentry.reuse(_jspx_th_search_005fforEachFacet_005f0);
      _jspx_th_search_005fforEachFacet_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachFacet_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachFacet_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacetLabel_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFacet_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFacet_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacetLabel
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag _jspx_th_search_005fgetFacetLabel_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag) _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag.class);
    boolean _jspx_th_search_005fgetFacetLabel_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFacetLabel_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacetLabel_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFacet_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(75,7) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetLabel_005f0.setVar("facetLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(75,7) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetLabel_005f0.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacetLabel_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacetLabel_005f0 = _jspx_th_search_005fgetFacetLabel_005f0.doStartTag();
        if (_jspx_th_search_005fgetFacetLabel_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacetLabel_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacetLabel_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacetLabel_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody.reuse(_jspx_th_search_005fgetFacetLabel_005f0);
      _jspx_th_search_005fgetFacetLabel_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacetLabel_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacetLabel_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFacet_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFacet_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f5 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f5_reused = false;
    try {
      _jspx_th_string_005fescape_005f5.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFacet_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(76,7) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f5.setVar("facetLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(76,7) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f5.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(76,7) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f5.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f5 = _jspx_th_string_005fescape_005f5.doStartTag();
        if (_jspx_th_string_005fescape_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f5);
      _jspx_th_string_005fescape_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f5, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetIconName_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFacet_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFacet_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getIconName
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryIconTag _jspx_th_plma_005fgetIconName_005f1 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryIconTag) _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005ffacetId_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryIconTag.class);
    boolean _jspx_th_plma_005fgetIconName_005f1_reused = false;
    try {
      _jspx_th_plma_005fgetIconName_005f1.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetIconName_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFacet_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(77,7) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetIconName_005f1.setVar("facetIcon");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(77,7) name = facetId type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetIconName_005f1.setFacetId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetIconName_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetIconName_005f1 = _jspx_th_plma_005fgetIconName_005f1.doStartTag();
        if (_jspx_th_plma_005fgetIconName_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetIconName_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetIconName_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetIconName_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005ffacetId_005fnobody.reuse(_jspx_th_plma_005fgetIconName_005f1);
      _jspx_th_plma_005fgetIconName_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetIconName_005f1, _jsp_getInstanceManager(), _jspx_th_plma_005fgetIconName_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFacet_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFacet_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f6 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f6_reused = false;
    try {
      _jspx_th_string_005fescape_005f6.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFacet_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(78,7) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f6.setVar("facetIcon");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(78,7) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f6.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetIcon}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(78,7) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f6.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f6 = _jspx_th_string_005fescape_005f6.doStartTag();
        if (_jspx_th_string_005fescape_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f6);
      _jspx_th_string_005fescape_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f6, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachCategory_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFacet_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFacet_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachCategory
    com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag _jspx_th_search_005fforEachCategory_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag) _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005froot_005fiterationMode.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag.class);
    boolean _jspx_th_search_005fforEachCategory_005f1_reused = false;
    try {
      _jspx_th_search_005fforEachCategory_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachCategory_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFacet_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(79,7) name = root type = com.exalead.access.feedapi.CategoryTreeContainer reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f1.setRoot((com.exalead.access.feedapi.CategoryTreeContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.CategoryTreeContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(79,7) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f1.setVar("category");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(79,7) name = iterationMode type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f1.setIterationMode("LEAVES");
      int[] _jspx_push_body_count_search_005fforEachCategory_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachCategory_005f1 = _jspx_th_search_005fforEachCategory_005f1.doStartTag();
        if (_jspx_eval_search_005fforEachCategory_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("								");
            if (_jspx_meth_plma_005fgetIconName_005f2(_jspx_th_search_005fforEachCategory_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f1))
              return true;
            out.write("\r\n");
            out.write("								");
            if (_jspx_meth_string_005fescape_005f7(_jspx_th_search_005fforEachCategory_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f1))
              return true;
            out.write("\r\n");
            out.write("								");
            if (_jspx_meth_plma_005fgetCategoryColor_005f1(_jspx_th_search_005fforEachCategory_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f1))
              return true;
            out.write("\r\n");
            out.write("								");
            if (_jspx_meth_string_005fescape_005f8(_jspx_th_search_005fforEachCategory_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f1))
              return true;
            out.write("\r\n");
            out.write("								");
            if (_jspx_meth_search_005fgetCategoryLabel_005f0(_jspx_th_search_005fforEachCategory_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f1))
              return true;
            out.write("\r\n");
            out.write("								");
            if (_jspx_meth_string_005fescape_005f9(_jspx_th_search_005fforEachCategory_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f1))
              return true;
            out.write("\r\n");
            out.write("								<div class=\"facet\">\r\n");
            out.write("									<span class=\"categoryIcon fonticon ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty categoryIcon ? categoryIcon : facetIcon}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" style=\"color: ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryColor}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(";\"></span>\r\n");
            out.write("									<span class=\"categoryName\" title=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(':');
            out.write(' ');
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write('"');
            out.write('>');
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("</span>\r\n");
            out.write("								</div>\r\n");
            out.write("							");
            int evalDoAfterBody = _jspx_th_search_005fforEachCategory_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachCategory_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachCategory_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachCategory_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachCategory_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005froot_005fiterationMode.reuse(_jspx_th_search_005fforEachCategory_005f1);
      _jspx_th_search_005fforEachCategory_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachCategory_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fforEachCategory_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetIconName_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getIconName
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryIconTag _jspx_th_plma_005fgetIconName_005f2 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryIconTag) _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005fentry_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryIconTag.class);
    boolean _jspx_th_plma_005fgetIconName_005f2_reused = false;
    try {
      _jspx_th_plma_005fgetIconName_005f2.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetIconName_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(80,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetIconName_005f2.setVar("categoryIcon");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(80,8) name = category type = com.exalead.access.feedapi.Category reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetIconName_005f2.setCategory((com.exalead.access.feedapi.MergedCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.MergedCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(80,8) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetIconName_005f2.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetIconName_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetIconName_005f2 = _jspx_th_plma_005fgetIconName_005f2.doStartTag();
        if (_jspx_th_plma_005fgetIconName_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetIconName_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetIconName_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetIconName_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetIconName_0026_005fvar_005fentry_005fcategory_005fnobody.reuse(_jspx_th_plma_005fgetIconName_005f2);
      _jspx_th_plma_005fgetIconName_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetIconName_005f2, _jsp_getInstanceManager(), _jspx_th_plma_005fgetIconName_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f7 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f7_reused = false;
    try {
      _jspx_th_string_005fescape_005f7.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(81,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f7.setVar("categoryIcon");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(81,8) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f7.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryIcon}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(81,8) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f7.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f7 = _jspx_th_string_005fescape_005f7.doStartTag();
        if (_jspx_th_string_005fescape_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f7);
      _jspx_th_string_005fescape_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f7, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetCategoryColor_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getCategoryColor
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryColorTag _jspx_th_plma_005fgetCategoryColor_005f1 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryColorTag) _005fjspx_005ftagPool_005fplma_005fgetCategoryColor_0026_005fvar_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryColorTag.class);
    boolean _jspx_th_plma_005fgetCategoryColor_005f1_reused = false;
    try {
      _jspx_th_plma_005fgetCategoryColor_005f1.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetCategoryColor_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(82,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryColor_005f1.setVar("categoryColor");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(82,8) name = category type = com.exalead.access.feedapi.Category reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryColor_005f1.setCategory((com.exalead.access.feedapi.MergedCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.MergedCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetCategoryColor_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetCategoryColor_005f1 = _jspx_th_plma_005fgetCategoryColor_005f1.doStartTag();
        if (_jspx_th_plma_005fgetCategoryColor_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetCategoryColor_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetCategoryColor_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetCategoryColor_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetCategoryColor_0026_005fvar_005fcategory_005fnobody.reuse(_jspx_th_plma_005fgetCategoryColor_005f1);
      _jspx_th_plma_005fgetCategoryColor_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetCategoryColor_005f1, _jsp_getInstanceManager(), _jspx_th_plma_005fgetCategoryColor_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f8 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f8_reused = false;
    try {
      _jspx_th_string_005fescape_005f8.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(83,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f8.setVar("categoryColor");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(83,8) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f8.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryColor}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(83,8) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f8.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f8 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f8 = _jspx_th_string_005fescape_005f8.doStartTag();
        if (_jspx_th_string_005fescape_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f8[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f8.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f8.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f8);
      _jspx_th_string_005fescape_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f8, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetCategoryLabel_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getCategoryLabel
    com.exalead.cv360.searchui.view.jspapi.search.GetCategoryLabelTag _jspx_th_search_005fgetCategoryLabel_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetCategoryLabelTag) _005fjspx_005ftagPool_005fsearch_005fgetCategoryLabel_0026_005fvar_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetCategoryLabelTag.class);
    boolean _jspx_th_search_005fgetCategoryLabel_005f0_reused = false;
    try {
      _jspx_th_search_005fgetCategoryLabel_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetCategoryLabel_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(84,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryLabel_005f0.setVar("categoryLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(84,8) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryLabel_005f0.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetCategoryLabel_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetCategoryLabel_005f0 = _jspx_th_search_005fgetCategoryLabel_005f0.doStartTag();
        if (_jspx_th_search_005fgetCategoryLabel_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetCategoryLabel_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetCategoryLabel_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetCategoryLabel_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetCategoryLabel_0026_005fvar_005fcategory_005fnobody.reuse(_jspx_th_search_005fgetCategoryLabel_005f0);
      _jspx_th_search_005fgetCategoryLabel_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetCategoryLabel_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetCategoryLabel_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f9 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f9_reused = false;
    try {
      _jspx_th_string_005fescape_005f9.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(85,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f9.setVar("categoryLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(85,8) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f9.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(85,8) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f9.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f9 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f9 = _jspx_th_string_005fescape_005f9.doStartTag();
        if (_jspx_th_string_005fescape_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f9[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f9.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f9.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f9);
      _jspx_th_string_005fescape_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f9, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(100,1) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${elemPerPage > 0 && entriesCount > elemPerPage}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("		");
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_fmt_005fformatNumber_005f0(_jspx_th_c_005fif_005f2, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("		");
          if (_jspx_meth_c_005fforEach_005f0(_jspx_th_c_005fif_005f2, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          out.write('	');
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_fmt_005fformatNumber_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  fmt:formatNumber
    org.apache.taglibs.standard.tag.rt.fmt.FormatNumberTag _jspx_th_fmt_005fformatNumber_005f0 = (org.apache.taglibs.standard.tag.rt.fmt.FormatNumberTag) _005fjspx_005ftagPool_005ffmt_005fformatNumber_0026_005fvar_005fvalue_005ftype_005fpattern_005fnobody.get(org.apache.taglibs.standard.tag.rt.fmt.FormatNumberTag.class);
    boolean _jspx_th_fmt_005fformatNumber_005f0_reused = false;
    try {
      _jspx_th_fmt_005fformatNumber_005f0.setPageContext(_jspx_page_context);
      _jspx_th_fmt_005fformatNumber_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(102,2) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_fmt_005fformatNumber_005f0.setVar("nbPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(102,2) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_fmt_005fformatNumber_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entriesCount/elemPerPage + (entriesCount/elemPerPage % 1 == 0 ? 0 : 0.5)}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(102,2) name = type type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_fmt_005fformatNumber_005f0.setType("number");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(102,2) name = pattern type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_fmt_005fformatNumber_005f0.setPattern("#");
      int _jspx_eval_fmt_005fformatNumber_005f0 = _jspx_th_fmt_005fformatNumber_005f0.doStartTag();
      if (_jspx_th_fmt_005fformatNumber_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005ffmt_005fformatNumber_0026_005fvar_005fvalue_005ftype_005fpattern_005fnobody.reuse(_jspx_th_fmt_005fformatNumber_005f0);
      _jspx_th_fmt_005fformatNumber_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_fmt_005fformatNumber_005f0, _jsp_getInstanceManager(), _jspx_th_fmt_005fformatNumber_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fend_005fbegin.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f0_reused = false;
    try {
      _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(108,2) name = begin type = int reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setBegin(((java.lang.Integer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${0}", int.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).intValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(108,2) name = end type = int reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setEnd(((java.lang.Integer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${nbPage - 1}", int.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).intValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/recentSearches/templates/hits.jsp(108,2) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setVarStatus("loop");
      int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
        if (_jspx_eval_c_005fforEach_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("			<span data-page-nb=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${loop.index}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" class=\"page-button ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${loop.index == 0 ? \"selected\" : \"\"}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\"></span>\r\n");
            out.write("		");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvarStatus_005fend_005fbegin.reuse(_jspx_th_c_005fforEach_005f0);
      _jspx_th_c_005fforEach_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f0_reused);
    }
    return false;
  }
}
