/**
 * Bookmarks library
 *
 * @param uCssId Widget CSS UUID
 * @param options Library options
 * @constructor Constructor
 */
var SavePage = function (uCssId, options) {
    this.uCssId = uCssId;
    this.widget = $('.' + uCssId);
    this.options = options;

    this.labelEl = this.widget.find('.label-input');
    this.descriptionEl = this.widget.find('.description-input');
    this.iconTooltipEl = this.widget.find('.icon-tooltip');
    this.iconEl = this.widget.find('.icon-input');
    this.options.params = window.location.href.split("?")[1];

    this.init();
};

SavePage.DEFAULT_ICON = 'fonticon fonticon-doc';

/**
 * Init button event handler
 */
SavePage.prototype.init = function () {
    this.initSavePageButton();
    this.initCancelButton();
    this.initSaveButton();
    this.initIconTooltip();
    this.initIconElements();
}

SavePage.prototype.initSavePageButton = function () {
    var savePage = this.widget.find('.plmaButton.fonticon-floppy');

    $(savePage).on('click', $.proxy(function (e) {
        // Disable parent div event
        e.stopPropagation();

        this.resetPopup();
        this.togglePopup();

    }, this));
}

SavePage.prototype.initCancelButton = function () {
    var cancelButton = this.widget.find('.popup .cancel');
    $(cancelButton).on('click', $.proxy(function (e) {
        // Disable parent div event
        e.stopPropagation();

        this.togglePopup();
        this.resetPopup();

    }, this));
}

SavePage.prototype.initSaveButton = function () {
    var self = this;
    var saveButton = this.widget.find('.popup .save');
    $(saveButton).on('click', $.proxy(function (e) {
        // Disable parent div event
        e.stopPropagation();

        if (!self.options.params) {
            $.notify('No filter/query applied.', "error");
            self.togglePopup();
            self.resetPopup();
            return;
        }

        $.ajax({
            method: 'POST',
            url: self.options.url + '/save',
            dataType: 'JSON',
            async: false,
            data: {
                label: self.labelEl.val(),
                description: self.descriptionEl.val(),
                params: self.options.params,
                icon: "fonticon " + self.iconEl.attr('class').trim().split(' ').pop(),
                template: self.options.page,
                filterMode: self.options.paramsListMode,
                filters: self.options.paramsList
            },
            success: function (data) {
                $.notify('Page successfully saved', "success");

                const MENU_WUID = $('.sideBarMenu').attr('class').trim().split(' ')[1];

                if (MENU_WUID) {
                    // In layout mode --> use different base URL to load widget content (used class because not possible to add data attributes in widget taglib)
                    var menuClient = new PlmaAjaxClient(self.widget, {baseUrl: $('.sideBarMenu').hasClass('mode-layout') ? self.options.baseAjaxReload : null});
                    menuClient.addWidget(MENU_WUID, true, false);
                    menuClient.update();
                } else {
                    window.location.reload();
                }

            },
            error: function (data) {
                $.notify('Error saving page (' + data.responseJSON.error + ')', "error");
            }
        });

        self.togglePopup();
        self.resetPopup();
    }, this));
}

SavePage.prototype.initIconTooltip = function () {
    var self = this;
    $(self.iconTooltipEl).hide();

    $(self.iconEl).on('click', $.proxy(function (e) {
        // Disable parent div event
        e.stopPropagation();

        $(self.iconTooltipEl).show();
        self.initIconSearch();

    }, this));

}

SavePage.prototype.initIconElements = function () {
    var self = this;
    var iconContainer = this.widget.find('.fonticon-container');
    $(iconContainer).on('click', function (e) {
        e.stopPropagation();

        var tooltipIconsEl = $(e.target);
        if (tooltipIconsEl.attr('class').trim().split(' ')[0] === 'fonticon-elem') {
            self.iconEl.text('');

            var oldIcon = self.iconEl.attr('class').trim().split(' ').pop();
            var newIcon = tooltipIconsEl.attr('class').trim().split(' ').pop();
            self.iconEl.removeClass(oldIcon).addClass(newIcon);
        }
    });
}


SavePage.prototype.initIconSearch = function () {
    this.widget.find('.icon-tooltip-input').on('keyup', function () {
        var searchValue = this.value;
        var iconList = $('.save-page .fonticon-container > span');
        for (var i = 0; i < iconList.length; i++) {
            var elemClassList = iconList[i].getClassList();
            if (elemClassList[2].indexOf(searchValue.replace('fonticon ', '')) !== -1) {
                elemClassList.remove('hidden');
            } else {
                elemClassList.add('hidden');
            }
        }
    });
};

SavePage.prototype.resetPopup = function () {
    this.labelEl.val('');
    this.descriptionEl.val('');

    $(this.iconTooltipEl).hide();
    var oldIcon = this.iconEl.attr('class').trim().split(' ').pop();
    this.iconEl.removeClass(oldIcon).addClass(SavePage.DEFAULT_ICON);

    this.widget.find('.icon-tooltip-input').val('');
    this.widget.find('.fonticon-container > span').each(function () {
        $(this).removeClass('hidden');
    });
};

SavePage.prototype.togglePopup = function () {
    var popup = this.widget.find('.popup');
    $(popup).toggleClass('hidden');
}