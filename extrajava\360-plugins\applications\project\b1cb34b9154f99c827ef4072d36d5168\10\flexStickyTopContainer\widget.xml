<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Sticky top container" group="PLM Analytics/Layout/Flex display" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget sticks specified subwidgets at the top of their container when this container is scrolled down.</Description>

	<Preview>
	<![CDATA[
		<img src="/resources/widgets/flexStickyTopContainer/images/preview.jpg" alt="Sticky top" />
	]]>
	</Preview>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/stickyTop.js" />
	</Includes>

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportWidgetsId arity="ZERO_OR_MANY" />
	<SupportFeedsId arity="ZERO"/>
	<SupportI18N supported="true" />
	
	<OptionsGroup name="General">
	</OptionsGroup>
	
</Widget>
