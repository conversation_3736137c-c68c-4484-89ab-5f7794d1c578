var SwymPublishManager = function($uiContainer,options) {
    this.$uiContainer = $uiContainer;
    this.options = options;
	this.formInstance = undefined;
	this.previewInfo = undefined;
    this.init();
    return this;
}
SwymPublishManager.prototype.getMessage = function (code) {
    return mashupI18N.get('plmaResources', code);
}

SwymPublishManager.prototype.init = function() {
    if (!window.dashboardController) {
        $.notify(this.getMessage('plma.swympublishmanager.error.outside.3dexp'), 'warn');
        return;
    }
	
	this.$SWYM_FORM_CONTAINER = $('<div class="swym-publish-form-container"></div>');
	this.$uiContainer.append(this.$SWYM_FORM_CONTAINER);
    this.renderForm();
}

SwymPublishManager.prototype.previewLoaded = function(previewInfo){
	if(this.options.fileMode){
        if(previewInfo.getDataUrl == undefined || typeof previewInfo.getDataUrl != 'function'){
            $.notify(this.getMessage('plma.exportto3dspace.error.configurations'), 'error');
            throw Error("Need a function 'getDataUrl' to get the DataUrl of the Content to be Exported");
        }
        if(previewInfo.fileName == undefined){
            $.notify(this.getMessage('plma.exportto3dspace.error.configurations'), 'error');
            throw Error("Need a string for 'fileName' to be used for fileName");
        }
    }

    this.previewInfo = previewInfo;
	if(this.$publishBtn){
		this.$publishBtn.attr('disabled', null);
	}
}

/**
* renderForm is method helps to render the Swym Form using "DS/SwymPublishForm/script/publish-form-view" API.
* This API returns a form instance which is the appended to a form container.
* The form instance has HTML Input Controls such as dropdown for community list,dropdown for content type to post in community,title and description inputArea and a Publish Button
* */
SwymPublishManager.prototype.renderForm = function() {
    if(this.$SWYM_FORM_CONTAINER !== undefined){
        this.$SWYM_FORM_CONTAINER.empty();
    }
    var onFailure = function(data){
        this.$SWYM_FORM_CONTAINER.hidePLMASpinner();
		if(data.stage == 'Fetch3DwymUrl'){
			this.$SWYM_FORM_CONTAINER.append($('<span class="fonticon noswym">' +
				this.getMessage('plma.swympublishmanager.error.noswym') +
			'</span>'));
			$.notify(this.getMessage('plma.swympublishmanager.error.noswym'), 'error');
		}else{
			$.notify(this.getMessage('plma.swympublishmanager.error.apiexecution'), 'error');
		}
        throw '[' + data.stage + ']' + data.error;
    }.bind(this);
	
	this.$SWYM_FORM_CONTAINER.showPLMASpinner({overlay: true});
	var that = this;
	EXPUtils.getWafData().then(function(API){
		// URL -> CSRF -> UploadMediaToSwym
		let data = {
			API: API,
			swymUrl: undefined,
			failureCallback: onFailure,
			successCallback: function(data){
				that.options.swymUrl = data.swymUrl;
				EXPUtils.getSwymPublishForm().then(function(SwymPublishForm){
					that.formInstance = new SwymPublishForm({
						mode: "jsevent",
						contentTitle:"",
						contentDescription: "",
						isDescriptionHTML:true
					});
					that.formInstance.addEvents({
						CancelShare: $.noop,
						StartUpload: that.onStartPublish.bind(that),
						ContentCreated: that.onContentCreated.bind(that)
					});
					
					// To make sure the forms allow html tags within description
					that.formInstance._config.isDescriptionHTML = true;
					
					that.$SWYM_FORM_CONTAINER.append(that.$SWYM_FORM_CONTAINER);
					that.formInstance.render(that.$SWYM_FORM_CONTAINER.get(0));
					
					// Remove Cancel Button and rearrange Publish Button
					that.$publishBtn = that.$SWYM_FORM_CONTAINER.find('.footer-cnt button.publish-share-button');
					that.$publishBtn
						.addClass("publish-btn")
						.attr('disabled', that.previewInfo? null : '')
						.prependTo(that.$SWYM_FORM_CONTAINER.find($('.publish-form-body')));
					
					that.$SWYM_FORM_CONTAINER.find('.footer-cnt').remove();
					that.$SWYM_FORM_CONTAINER.hidePLMASpinner();
				})
			}
		};
		EXPUtils.fetch3DSwymUrl(data)
	})    
    ['catch'](function (err) {
        onFailure(err);
    });
}

/**
* onStartPublish is callback method called when the publish button is clicked in Swym Form
* Upload Media If any and create Content in Swym based on the inputs provided in the form such as title,description,pdf file,community,content_type
* after createContent() is executed the onContentCreated callback is triggered.
* @param {Object} - event object
* */
SwymPublishManager.prototype.onStartPublish = function(e) {
	this.$SWYM_FORM_CONTAINER.showPLMASpinner({overlay: true});
	if(!this.options.fileMode && JSON.parse(e).content_type == 'media'){
		$.notify('No Media(file) found to publish. Choose another type', 'error');
	}
	
    this.uploadMedia(this.formInstance)
		.then(function(mediaId){
			//For Media Content type set community id as undefined to throw an error from API
			if(!this.options.fileMode && JSON.parse(e).content_type == 'media'){
				this.formInstance._currentCommunityId = undefined;
			}
			this.formInstance._config.contentDescription = 
				this.$SWYM_FORM_CONTAINER.find('.description-input-cnt textarea').val().trim();
			this.formInstance._config.contentTitle = 
				this.$SWYM_FORM_CONTAINER.find('.title-input-cnt input').val().trim();
			if(this.previewInfo.updateSwymInfo){
				this.previewInfo.updateSwymInfo(this.formInstance._config);				
			}
			this.formInstance.createContent(mediaId);
		}.bind(this));
}

/**
* onContentCreated is callback method called when the content is successfully created in a community or conversation
* @param {Object} - event object
* */
SwymPublishManager.prototype.onContentCreated = function(e) {
    this.formInstance._publishButton.setDisabled(false);
	this.$SWYM_FORM_CONTAINER.hidePLMASpinner();	
    let event = JSON.parse(e);
    if(event.success) {
        $.notify(this.getMessage("plma.swympublishmanager.success.publish"), 'info');
    }else {
        $.notify(this.getMessage("plma.swympublishmanager.error.not.publish"),'error');
    }
}

/**
* uploadMedia is method used to upload a media in Swym Post, media can be any type of file supported by swym posts.
* to upload media the swym API expects FormData object that holds BLOB of the media to be uploaded
* @param {Object} - form instance object has form details such as selected community,content_type
* */
SwymPublishManager.prototype.uploadMedia = function(formDetails){
    let previewInfo = this.options.fileMode? this.previewInfo : undefined;
	var onFailure = function(data){
		$.notify(this.getMessage('plma.exportto3dspace.error.apiexecution'), 'error');
		throw '[' + data.stage + ']' + data.error;
	}.bind(this);
	
	let swymUrl =  this.options.swymUrl;
	return new Promise(function (resolve, reject) {
		if(!previewInfo){
			resolve(undefined);
		}else{		
			//Upload the media in the community
			EXPUtils.getWafData().then(function(API){
				// URL -> CSRF -> UploadMediaToSwym
				let data = {
					API: API,
					swymUrl: swymUrl,
					mediaData: {
						fileName: previewInfo.fileName,
						getDataUrl: previewInfo.getDataUrl,
						published: 1,
						community_id: formDetails._currentCommunityId
					},
					failureCallback: onFailure,
					successCallback: function(data){
						// Received URL
						data.successCallback = function(data){
							// Received CSRF
							data.successCallback = function(data){
								resolve(data.result.id);
							}
							EXPUtils.uploadMediaToSwym(data);
						}
						EXPUtils.fetchSwymCSRFToken(data);
					}
				};
				EXPUtils.fetch3DSwymUrl(data)
			})
			['catch'](function (err) {
				reject(err);
				onFailure({
					stage: 'GetWafData',
					error: 'Unable to get WAFData [' + err + ']'
				});
			});
		}
    });
}
