var RefinePanelSearch = function (uCssId, options) {
	this.uCssId = uCssId;
	this.$widget = $('.' + this.uCssId);
	this.$input = $('.' + this.uCssId + ' .searchInput');
	this.options = options;

	this.searchFinished = false;
	this.displaySearchFinished = false;
	this.currentSearch = '';

	this.init();
};

RefinePanelSearch.PARAMETER_IS_SUGGEST_SEARCH = 'isSuggestSearch';
RefinePanelSearch.PARAMETER_IS_SUGGEST_SEARCH_EMPTY = 'isSuggestSearchEmpty';
RefinePanelSearch.PARAMETER_SPINNER_TIMEOUT = 700;
RefinePanelSearch.PARAMETER_SPINNER_RELOAD_TIMEOUT = 1500;
RefinePanelSearch.WIDGET_NAME = 'refinePanel';
RefinePanelSearch.TYPE_FACET = '.filter-list-facet-elem-facet-';
RefinePanelSearch.TYPE_DATE = '.filter-list-facet-elem-';

RefinePanelSearch.prototype.init = function () {
	/* init suggest */
	$('.refinepanel-suggest').remove();
	if (this.options.hasSuggest) {
		$('#' + this.options.cssId + ' input[name=' + this.options.cssId + ']').enablePlmaSuggest(
			this.options.suggestUrl,
			this.options.wuid,
			{
				autoSubmit: false,
				suggestBoxClass: 'refinepanel-suggest',
				successCallback: $.proxy(function (suggestions) {
					this.suggestions = suggestions;
					this.displaySearchFinished = false;
				}, this),
				errorCallback: function (xhr, textStatus, suggestContext) {
					$.notify(RefinePanelSearch.getMessage('refinepanel.facets.error'), 'error');
					if (console && console.warn) {
						console.warn('An error occurred while contacting the server. Make sure that suggests are already built.');
					}
				}
			}
		);
	}

	var timer = 0;
	this.$input.on('keyup', $.proxy(function () {
		// Check if Ctrl+A is pressed
		if (event.ctrlKey && event.key === 'a') {
			// Prevent default behavior for Ctrl+A
			event.preventDefault();
			return;
		}
		const newSearchValue = this.$input.val();
		if (this.currentSearch !== newSearchValue) {
			this.currentSearch = newSearchValue;
			this.showSpinner();
			clearTimeout(timer);

			timer = setTimeout($.proxy(() => {
				this.searchFinished = true;
				this.suggestions && this.computeSuggestResults();

				if (this.displaySearchFinished) {
					this.hideSpinner();
					this.displaySearchFinished = false;
					this.searchFinished = false;
				}
			}, this), RefinePanelSearch.PARAMETER_SPINNER_TIMEOUT);
		}
	}, this));
};

RefinePanelSearch.prototype.computeSuggestResults = function () {
	var $suggests = $('.refinepanel-suggest ul');
	var widgetContent = $('.widgetContent.facets');
	var inDomSearch = false;

	/* Search facet lables in DOM */
	widgetContent[0].querySelectorAll("span.facet-name").forEach(span => {
		const isMatch = span.innerText.toLowerCase().match(this.$input.val().toLowerCase());
		if(isMatch!= null) {
			[$(RefinePanelSearch.TYPE_FACET + span.id),$(RefinePanelSearch.TYPE_DATE + span.id)].forEach($type => $type.hasClass("hidden") ? $type.toggleClass("hidden") : false);

			inDomSearch = true;
		} else if(!$(RefinePanelSearch.TYPE_FACET + span.id).hasClass("hidden")) {
			[$(RefinePanelSearch.TYPE_FACET + span.id), $(RefinePanelSearch.TYPE_DATE + span.id)].forEach($type => $type.addClass("hidden"));
		}
	});

	widgetContent.find('div.empty-facets').remove();

	if (this.$input.val() === '') {
		$suggests.empty();
		this.searchResult({});
	} else if (inDomSearch || (this.suggestions.entries && this.suggestions.entries.length > 0)) {
		this.searchResult(this.getSuggests(this.suggestions));
	} else {
		$suggests.empty();
		const $facets = this.$widget.find('.facets');
		if (!$facets.find('div.empty-facets').length) this.displayEmptyMessage($facets);
		this.hideSpinner();
		this.displaySearchFinished = this.searchFinished = false;
	}
};

RefinePanelSearch.prototype.getSuggests = function (suggestions) {
    const suggestResults = {};

	suggestions.entries.filter((entry) => entry.facet == true && entry.logicFacet != null).forEach((entry) => {
		const facetName = entry.logicFacet;
		const categoryPath = entry.categoryPath;
		// Check if facet is already in the list
		suggestResults[facetName] = suggestResults[facetName] ? [...suggestResults[facetName], categoryPath] : [categoryPath];
	});

    return suggestResults;
};

RefinePanelSearch.prototype.addRefines = function (client, params, loadFacetParameter) {
    for (var paramKey in params) {
        if (paramKey.indexOf('.r') !== -1 || paramKey.indexOf('.zr') !== -1) {
            for (var i = 0; i < params[paramKey].length; i++) {
                client.getAjaxUrl().addParameter(loadFacetParameter, params[paramKey][i].split('/')[1], false);
            }
        }
    }
}

RefinePanelSearch.prototype.searchResult = function (suggestResults) {
	/* Get the updated widget */
	var currentUrl = new BuildUrl(window.location.href);
	var ajaxOptions = {baseUrl: this.options.baseAjaxReload, method: 'POST'};
	var client = new PlmaAjaxClient(this.$widget, ajaxOptions);
	var parameters = currentUrl.getParameters();
	var facetListToLoad = '';
	var categoryListToHighlight = '';
	var widgetContent = $('.widgetContent.facets');

	/* Add refines on loadFacetParameter */
	if (Object.keys(suggestResults).length > 0) {
		this.addRefines(client, parameters, this.options.loadFacetParameter);

		/* Get savedFilters */
		if (this.filters) {
			this.addRefines(client, this.filters.params, this.options.loadFacetParameter);
		}
	}

	/* parse suggest results */
	for (var key in suggestResults) {
        facetListToLoad += ',' + key;
        client.getAjaxUrl().addParameter(this.options.paramNameReload, key, false);
        client.getAjaxUrl().addParameter(this.options.loadFacetParameter, key, false);
        for (var i = 0; i < suggestResults[key].length; i++) {
            if (suggestResults[key][i].split('#').length > 1 && suggestResults[key][i].charAt(0) !== "#") {
                categoryListToHighlight += this.options.customDelimiter + suggestResults[key][i].split('#')[0].split(' ').join('.');
            } else {
                categoryListToHighlight += this.options.customDelimiter + suggestResults[key][i];
            }
        }
    }

	client.getAjaxUrl().addParameter('panelId', this.options.refinesPanelId);
	// Execute only relevant feeds
	client.getAjaxUrl().addParameter('use_page_feeds', this.options.feeds.join(','));

	if (facetListToLoad !== "") {
		client.getAjaxUrl().addParameter("searchrefine", facetListToLoad.replace(',', ''), true);
	} else {
		delete client.getAjaxUrl().params.searchrefine;
	}

	if (categoryListToHighlight !== "") {
		client.getAjaxUrl().addParameter("catsearchrefine", categoryListToHighlight.replace(this.options.customDelimiter, ''), true);
	} else {
		delete client.getAjaxUrl().params.catsearchrefine;
	}

	client.getAjaxUrl().addParameter(RefinePanelSearch.PARAMETER_IS_SUGGEST_SEARCH, true, true);
	if (this.$input.val() === '') {
		client.getAjaxUrl().addParameter(RefinePanelSearch.PARAMETER_IS_SUGGEST_SEARCH_EMPTY, true, true);
	}

	/*  set wuids to update */
	client.addWidget(this.uCssId, true, false);

	client.getWidget($.proxy(function (widgets, scripts) {
		var $facets = $(widgets[0].html).find('.facets');
		if (this.$widget.find('.spinner-container').hasClass('active')) {
			$facets.find('.overlay-facets').addClass('active');
		}
		this.hideEmptyFacets($facets);
		widgetContent[0].querySelectorAll("span.facet-name").forEach(span => {
			if(categoryListToHighlight !== "" || (categoryListToHighlight == "" && this.$input.val() == "")) {
				/* Replace found entires in orignal DOM with new DOM*/
				if($facets.find(RefinePanelSearch.TYPE_FACET + span.id).length != 0){
					widgetContent.find(RefinePanelSearch.TYPE_FACET + span.id).replaceWith($facets.find(RefinePanelSearch.TYPE_FACET + span.id));
				}
				else if($facets.find(RefinePanelSearch.TYPE_DATE + span.id).length != 0) {
					widgetContent.find(RefinePanelSearch.TYPE_DATE + span.id).replaceWith($facets.find(RefinePanelSearch.TYPE_DATE + span.id));
				}
			}
			else if(!widgetContent.find(RefinePanelSearch.TYPE_FACET + span.id).hasClass('hidden')) {
					widgetContent.find(RefinePanelSearch.TYPE_FACET + span.id).replaceWith($facets.find(RefinePanelSearch.TYPE_FACET + span.id));
				}
		});

		$('#mainWrapper').append(scripts);

		this.displaySearchFinished = true;
		if (this.searchFinished) {
			this.hideSpinner();
			this.displaySearchFinished = false;
			this.searchFinished = false;
		}
	}, this));
};

RefinePanelSearch.prototype.hideEmptyFacets = function ($facets) {
	$facets.find('.filter-list-facet-elem').each($.proxy(function (i, e) {
		var $e = $(e);
		if ($e.find('table.facet tbody').length === 0 && !$e.hasClass('filter-list-facet-elem-numerical')) {
			$e.remove();
		}
	}, this));
};

RefinePanelSearch.prototype.displayEmptyMessage = function ($facets) {
    const messageText = RefinePanelSearch.getMessage('refinepanel.facets.empty');
    const $message = $(`<div class="empty-facets">
                            <span class="fonticon fonticon-doc-delete"></span>
                            <span class="message">${messageText}</span>
                        </div>`);

    $facets.prepend($message);
};

RefinePanelSearch.prototype.showSpinner = function () {
    this.$widget.find('.spinner-container, .overlay-facets').addClass('active');
};

RefinePanelSearch.prototype.hideSpinner = function () {
    this.$widget.find('.spinner-container, .overlay-facets').removeClass('active');
};

RefinePanelSearch.getMessage = function (code) {
	return mashupI18N.get(RefinePanelSearch.WIDGET_NAME, code);
};
