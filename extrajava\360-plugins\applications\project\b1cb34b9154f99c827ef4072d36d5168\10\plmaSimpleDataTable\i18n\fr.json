{"aria": {"orderable": "Activer pour trier", "orderableRemove": "Activer pour supprimer le tri", "orderableReverse": "Activer pour inverser le tri", "paginate": {"first": "Première", "last": "<PERSON><PERSON><PERSON>", "next": "Suivante", "previous": "Précédente"}}, "autoFill": {"cancel": "Annuler", "fill": "Remplir toutes les cellules avec <i>%d</i>", "fillHorizontal": "Remplir les cellules horizontalement", "fillVertical": "Remp<PERSON>r les cellules verticalement", "info": ""}, "buttons": {"collection": "Collection", "colvis": "Visibilité colonnes", "colvisRestore": "Rétablir visibilité", "copy": "<PERSON><PERSON><PERSON>", "copyKeys": "Appuyez sur ctrl ou u2318 + C pour copier les données du tableau dans votre presse-papier.", "copySuccess": {"_": "%d lignes copiées dans le presse-papier", "1": "1 ligne copiée dans le presse-papier"}, "copyTitle": "Copier dans le presse-papier", "createState": "C<PERSON>er un état", "csv": "CSV", "excel": "Excel", "pageLength": {"_": "Afficher %d lignes", "-1": "A<PERSON>iche<PERSON> toutes les lignes", "1": "Afficher 1 ligne"}, "pdf": "PDF", "print": "<PERSON><PERSON><PERSON><PERSON>", "removeAllStates": "Supprimer tous les états", "removeState": "<PERSON><PERSON><PERSON><PERSON>", "renameState": "<PERSON>mmer", "savedStates": "États sauvegardés", "stateRestore": "État %d", "updateState": "Mettre à jour"}, "columnControl": {"buttons": {"searchClear": "Effacer la recherche"}, "colVis": "Visibilité colonnes", "colVisDropdown": "Visibilité colonnes", "dropdown": "Plus...", "list": {"all": "<PERSON><PERSON>", "empty": "Vide", "none": "Désé<PERSON><PERSON>ner", "search": "Rechercher..."}, "orderAddAsc": "A<PERSON>ter tri croissant", "orderAddDesc": "A<PERSON>ter tri décroissant", "orderAsc": "Tri croissant", "orderClear": "<PERSON>ff<PERSON><PERSON> le tri", "orderDesc": "Tri décrois<PERSON>t", "orderRemove": "Supprimer du tri", "reorder": "Réorganiser les colonnes", "reorderLeft": "<PERSON>é<PERSON>r la colonne vers la gauche", "reorderRight": "Déplacer la colonne vers la droite", "search": {"datetime": {"empty": "Vide", "equal": "Égal à", "greater": "<PERSON><PERSON> le", "less": "Avant le", "notEmpty": "Non vide", "notEqual": "<PERSON>ff<PERSON><PERSON> de"}, "number": {"empty": "Vide", "equal": "Égal à", "greater": "Sup<PERSON>ur à", "greaterOrEqual": "Su<PERSON><PERSON><PERSON> ou égal à", "less": "Inférieur à", "lessOrEqual": "Inférieur ou égal à", "notEmpty": "Non vide", "notEqual": "<PERSON>ff<PERSON><PERSON> de"}, "text": {"contains": "Contient", "empty": "Vide", "ends": "Se termine par", "equal": "Égal à", "notContains": "Ne contient pas", "notEmpty": "Non vide", "notEqual": "<PERSON>ff<PERSON><PERSON> de", "starts": "Commence par"}}, "searchClear": "Effacer la recherche", "searchDropdown": "<PERSON><PERSON><PERSON>"}, "datetime": {"amPm": {"0": "am", "1": "pm"}, "hours": "<PERSON><PERSON>", "minutes": "Minutes", "months": {"0": "<PERSON><PERSON>", "1": "<PERSON><PERSON><PERSON><PERSON>", "10": "Novembre", "11": "Décembre", "2": "Mars", "3": "Avril", "4": "<PERSON>", "5": "Juin", "6": "<PERSON><PERSON><PERSON>", "7": "Août", "8": "Septembre", "9": "Octobre"}, "next": "Suivant", "previous": "Précédent", "seconds": "Secondes", "unknown": "-", "weekdays": {"0": "<PERSON><PERSON>", "1": "<PERSON>n", "2": "Mar", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON>", "5": "Ven", "6": "Sam"}}, "decimal": "", "editor": {"close": "<PERSON><PERSON><PERSON>", "create": {"button": "Nouveau", "submit": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> une nouvelle entrée"}, "edit": {"button": "Editer", "submit": "Mettre à jour", "title": "<PERSON><PERSON>"}, "error": {"system": "Une erreur système s'est produite (<a target=\"\\\" rel=\"nofollow\" href=\"\\\">Plus d'information</a>)."}, "multi": {"info": "Les éléments sélectionnés contiennent différentes valeurs pour cette entrée. Pour modifier et définir tous les éléments de cette entrée à la même valeur, cliquez ou tapez ici, sinon ils conserveront leurs valeurs individuelles.", "noMulti": "Ce champ peut être modifié individuellement, mais ne fait pas partie d'un groupe. ", "restore": "Annuler les modifications", "title": "Valeurs multiples"}, "remove": {"button": "<PERSON><PERSON><PERSON><PERSON>", "confirm": {"_": "Êtes-vous sûr de vouloir supprimer %d lignes ?", "1": "Êtes-vous sûr de vouloir supprimer 1 ligne ?"}, "submit": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>"}}, "emptyTable": "Aucune donnée disponible dans le tableau", "info": "Affichage de _START_ à _END_ sur _TOTAL_ entrées", "infoEmpty": "Affichage de 0 à 0 sur 0 entrées", "infoFiltered": "(filtrées depuis un total de _MAX_ entrées)", "infoPostFix": "", "infoThousands": " ", "lengthLabels": {"-1": "<PERSON>ut"}, "lengthMenu": "Afficher _MENU_ entrées", "loadingRecords": "Chargement...", "orderClear": "<PERSON>ff<PERSON><PERSON> le tri", "processing": "Traitement...", "search": "Rechercher :", "searchBuilder": {"add": "Ajouter une condition", "button": {"_": "Recherche avancée (%d)", "0": "Recherche avancée"}, "clearAll": "Effacer tout", "condition": "Condition", "conditions": {"array": {"contains": "Contient", "empty": "Vide", "equals": "Égal à", "not": "<PERSON>ff<PERSON><PERSON> de", "notEmpty": "Non vide", "without": "Sans"}, "date": {"after": "<PERSON><PERSON> le", "before": "Avant le", "between": "<PERSON><PERSON>", "empty": "Vide", "equals": "Égal à", "not": "<PERSON>ff<PERSON><PERSON> de", "notBetween": "<PERSON><PERSON> entre", "notEmpty": "Non vide"}, "number": {"between": "<PERSON><PERSON>", "empty": "Vide", "equals": "Égal à", "gt": "Sup<PERSON>ur à", "gte": "Su<PERSON><PERSON><PERSON> ou égal à", "lt": "Inférieur à", "lte": "Inférieur ou égal à", "not": "<PERSON>ff<PERSON><PERSON> de", "notBetween": "<PERSON><PERSON> entre", "notEmpty": "Non vide"}, "string": {"contains": "Contient", "empty": "Vide", "endsWith": "Se termine par", "equals": "Égal à", "not": "<PERSON>ff<PERSON><PERSON> de", "notContains": "Ne contient pas", "notEmpty": "Non vide", "notEndsWith": "Ne termine pas par", "notStartsWith": "Ne commence pas par", "startsWith": "Commence par"}}, "data": "<PERSON><PERSON><PERSON>", "deleteTitle": "Supprimer la règle de filtrage", "leftTitle": "Désindenter le critère", "logicAnd": "Et", "logicOr": "Ou", "rightTitle": "<PERSON><PERSON><PERSON> le critère", "search": "<PERSON><PERSON><PERSON>", "title": {"_": "Recherche avancée (%d)", "0": "Recherche avancée"}, "value": "<PERSON><PERSON>"}, "searchPanes": {"clearMessage": "Effacer tout", "collapse": {"_": "Volet de recherche (%d)", "0": "Volet de recherche"}, "collapseMessage": "<PERSON><PERSON><PERSON><PERSON> tout", "count": "{total}", "countFiltered": "{shown} ({total})", "emptyMessage": "<em>vide</em>", "emptyPanes": "Pas de volet de recherche", "loadMessage": "Chargement du volet de recherche...", "showMessage": "<PERSON><PERSON> tout", "title": "Filtres actifs - %d"}, "searchPlaceholder": "", "select": {"cells": {"_": "%d cellules sélectionnées", "0": "", "1": "1 cellule sélectionnée"}, "columns": {"_": "%d colonnes sélectionnées", "0": "", "1": "1 colonne sélectionnée"}, "rows": {"_": "%d lignes sélectionnées", "0": "", "1": "1 ligne sélectionnée"}}, "stateRestore": {"creationModal": {"button": "<PERSON><PERSON><PERSON>", "columns": {"search": "Recherche par colonne", "visible": "Visibilité des colonnes"}, "name": "Nom :", "order": "Tri", "paging": "Pagination", "scroller": "Position du défilement", "search": "Recherche", "searchBuilder": "Recherche avancée", "select": "Sélection", "title": "Créer un nouvel état", "toggleLabel": "Inclus :"}, "duplicateError": "Il existe déjà un état avec ce nom.", "emptyError": "Le nom ne peut pas être vide.", "emptyStates": "Aucun état sauvegardé", "removeConfirm": "Voulez vous vraiment supprimer %s ?", "removeError": "Échec de la suppression de l'état.", "removeJoiner": "et", "removeSubmit": "<PERSON><PERSON><PERSON><PERSON>", "removeTitle": "Supprimer l'état", "renameButton": "<PERSON>mmer", "renameLabel": "Nouveau nom pour %s :", "renameTitle": "Renommer l'état"}, "thousands": " ", "zeroRecords": "Aucune entrée correspondante trouvée"}