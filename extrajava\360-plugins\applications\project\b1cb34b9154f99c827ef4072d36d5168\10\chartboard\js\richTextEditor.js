var RichTextEditor = function(widget, options) {
	this.widget = widget;
	this.options = $.extend({}, {
		sideArrow: null,
		user: null,
		tinymceUrl: null
	}, options);
	
	this.currentElt = null;
	this.currentIframeContents = null;
};

RichTextEditor.prototype.openRichtextPopup = function(elt){
	this.currentElt = elt;
	this.widget.find('.elem-active-editor').removeClass('elem-active-editor');
	this.currentElt.addClass('elem-active-editor');
	
	var backgroundColor = this.currentElt.data('background-color'); 
	
	if (!tinymce.activeEditor) {
		this.initTinyMCE();
		this.initClickEvents();
		this.currentIframeContents = this.widget.find('.chartboard-tinymce-container .chartboard-tinymce-editor-container iframe').contents();
	}
	
	/* get value from div and put it in the editor (watch for template class, title and text) */
	if(this.currentElt.find('.editor-text').length > 0){
		tinymce.activeEditor.setContent(this.currentElt.find('.editor-text')[0].innerHTML);
		this.widget.find('.chartboard-tinymce-title-container input')[0].value = this.currentElt.find('.editor-title')[0].innerHTML;
		this.setActiveTemplate(this.findTemplateClass(this.currentElt[0].getClassList()));
	}else{
		tinymce.activeEditor.setContent(this.currentElt[0].innerHTML);
		this.widget.find('.chartboard-tinymce-title-container input')[0].value = "";
	}
	//Set background if exists
	if (backgroundColor) {
		this.currentIframeContents.find('body').css('background-color', backgroundColor).data('background-color', backgroundColor);
		this.currentIframeContents.find('html').css('background-color', backgroundColor);
	}else{
		this.currentIframeContents.find('body').css('background-color','white').data('background-color', null);
		this.currentIframeContents.find('html').css('background-color','white');
	}
	if(this.currentElt.data('arrow-side')){
		this.options.sideArrow = this.currentElt.data('arrow-side');
	}else{
		this.options.sideArrow = 'left';
	}
	
	//display the editor
	this.showRichtextPopup();
	
};

RichTextEditor.prototype.loadScript = function() {
	$.getScript(this.options.tinymceUrl);	
};

RichTextEditor.prototype.initTinyMCE = function() {
	tinyMCE.baseURL = this.options.tinymceUrl.split('/tinymce.min.js')[0];
	tinymce.init({
		selector: '.chartboard-popup-tinymce .chartboard-tinymce-editor',
		theme: 'modern',
		plugins: 'textcolor',
		branding: false,
		menubar: false,
		resize: false,
		toolbar1: 'formatselect | bold italic underline strikethrough forecolor backcolor | link | alignleft aligncenter alignright alignjustify  | numlist bullist outdent indent  | removeformat | addSignature | backcolor | changeArrow',
		setup: $.proxy(function(editor) {
			editor.addButton('addSignature', {
				tooltip: Chartboard.getMessage('chartboard.signature.tooltip'),
				icon: 'add-signature fonticon fonticon-vcard',
				onclick: $.proxy(function() {
					var content = editor.getContent();
					var date = new Date();
					content += '<br/><br/><span style="font-style: italic; float: right;">' + this.options.user + ' ' + Chartboard.getMessage('chartboard.date.on') + ' ' + date.toLocaleDateString() + '</span>';
					editor.setContent(content);
				},this)
			});
			editor.addButton('changeArrow', {
				tooltip: Chartboard.getMessage('chartboard.arrow.tooltip'),
				type: 'listbox',
				icon: 'change-arrow fonticon fonticon-arrow-double-combo',
				onselect: $.proxy(function (e) {
					this.options.sideArrow = e.target.value();
				},this),
				values: [
					{ text: Chartboard.getMessage('chartboard.arrow.left'), value: 'left' },
					{ text: Chartboard.getMessage('chartboard.arrow.top'), value: 'top' },
					{ text: Chartboard.getMessage('chartboard.arrow.right'), value: 'right' },
					{ text: Chartboard.getMessage('chartboard.arrow.bottom'), value: 'bottom' }
				],
				onPostRender: function () {
					// Select the second item by default
					this.value('left');
				}
			});
		},this)
	});
};

RichTextEditor.prototype.initClickEvents = function(){
//	if(this.widget.find('.chartboard-tinymce-container .chartboard-tinymce-title .mce-save-button').length == 0){

	/* Remove already existing buttons - a better solution would be to prevent them from remaining after the popup is closed */
	var existingSaveButton = this.widget.find('.chartboard-tinymce-container .chartboard-tinymce-title .mce-save-button');
	if(existingSaveButton.length > 0) {
		existingSaveButton.siblings('.mce-close-button').remove();
		existingSaveButton.remove();
	}

    var saveTooltip = mashupI18N.get("chartboard", 'chartboard.save.tooltip');
    var cancelTooltip = mashupI18N.get("chartboard", 'chartboard.cancel.tooltip');
	var cancelButton = $('<span class="mce-close-button fonticon fonticon-cancel" title=' + cancelTooltip + '></span>');
	var saveButton = $('<span class="mce-save-button fonticon fonticon-check" title=' + saveTooltip + '></span>');
	
	this.widget.find('.chartboard-tinymce-container .chartboard-tinymce-title')
		.append(cancelButton)
		.append(saveButton);
//	}
	
	saveButton.on('click',$.proxy(function(e){
		this.currentElt.empty();
		//get title and add it in a title div
		var divTitle = $('<div class="editor-title">' + this.widget.find('.chartboard-tinymce-title-container input')[0].value + '</div>');
		//get text and add it in a text div
		var divText = $('<div class="editor-text">' + tinymce.activeEditor.getContent() + '</div>')
		//add both divs in the elem (replace the old one if they exists)
		this.currentElt.append(divTitle);
		this.currentElt.append(divText);
		//add template class in the elem
		this.currentElt.removeClass(this.findTemplateClass(this.currentElt[0].getClassList()));
		var activeTemplate = this.getActiveTemplate();
		this.currentElt.addClass(activeTemplate);
		this.currentElt.closest('.grid-stack-item').data('cell-cssclass',activeTemplate);
		//Add background color if exists
		var backgroundColor = this.currentIframeContents.find('body').data('background-color');
		if (backgroundColor) {
			this.currentElt
				.css('background-color', backgroundColor)
				.data('background-color', backgroundColor)
				.removeClass('no-color');
		}else{
			this.currentElt.addClass('no-color');
		}
		//if arrow mode add the right class
		if(activeTemplate == 'chartboard-tinymce-template-arrow'){
			this.currentElt
				.removeClass('left top right bottom')
				.addClass(this.options.sideArrow)
				.data('arrow-side', this.options.sideArrow);
			
			var arrowContainer = $('<div class="arrow-container"><div class="arrow-relative-container"></div></div>');
			this.currentElt.append(arrowContainer);
			arrowContainer.addClass(this.options.sideArrow);
			var sideOpp = "";
			if(this.options.sideArrow == 'left'){
				sideOpp = "right";
			}else if(this.options.sideArrow == 'top'){
				sideOpp = "bottom";
			}else if(this.options.sideArrow == 'right'){
				sideOpp = "left";
			}else{
				sideOpp = "top";
			}
			arrowContainer.find('.arrow-relative-container').css('border-' + sideOpp + '-color',this.currentElt.css('background-color'));
		}
		
		/* close the editor */
		this.hideRichtextPopup();
	},this));
	
	cancelButton.on('click',$.proxy(function(e){
		this.hideRichtextPopup();
	},this));
	
	this.widget.find('.chartboard-popup-tinymce').on('click',$.proxy(function(e){
		if(e.target.getClassList().contains('chartboard-popup-tinymce')){
			this.hideRichtextPopup();
		}
	},this));
	
	this.widget.find('.chartboard-tinymce-template-elem').on('click',$.proxy(function(e){
		this.setActiveTemplate($(e.target).closest('.chartboard-tinymce-template-elem')[0].getClassList()[1]);
	},this));
	
	//Change icon and tooltip for block background color
	this.widget.find('.chartboard-tinymce-container .chartboard-tinymce-editor-container .mce-colorbutton.mce-first.mce-last .mce-i-backcolor').addClass('fonticon fonticon-palette').removeClass('mce-ico mce-i-backcolor').css('font-family','entypo').css('font-size','22px');
	this.widget.find('.chartboard-tinymce-container .chartboard-tinymce-editor-container .mce-colorbutton.mce-first.mce-last').attr('aria-label',Chartboard.getMessage('chartboard.background.tooltip'));
	
	this.widget.find('.chartboard-tinymce-container .chartboard-tinymce-editor-container .mce-colorbutton.mce-first.mce-last .mce-open').on('click',$.proxy(function(e){
		setTimeout($.proxy(function(){
			var idColorPicker = this.getBlockColorPicker();
			//add it only once
			if(! $._data($('#' + idColorPicker + ' .mce-grid-cell').get(0),"events")){
				$('#' + idColorPicker + ' .mce-grid-cell').on('click',$.proxy(function(e){
					//Click on second background color item, change background color of the block
					e.stopPropagation();
					this.currentIframeContents.find('body').css('background-color',$(e.target).data('mce-color'));
					this.currentIframeContents.find('html').css('background-color',$(e.target).data('mce-color'));
					this.currentIframeContents.find('body').data('background-color',$(e.target).data('mce-color'));
				},this));
			}
		},this),200);
	},this));
	
};

RichTextEditor.prototype.hideRichtextPopup = function(){
	this.widget.find('.chartboard-popup-tinymce').removeClass('visible');
};

RichTextEditor.prototype.showRichtextPopup = function(){
	this.widget.find('.chartboard-popup-tinymce').addClass('visible');
};

RichTextEditor.prototype.getActiveTemplate = function(){
	var template = '';
	this.widget.find('.chartboard-tinymce-template-elem').each(function(i,e){
		if($(e).hasClass('active')){
			template = e.getClassList()[1];
		}
	});
	return template;
};

RichTextEditor.prototype.findTemplateClass = function(classes){
	var templateClass = "";
	for(var i=0; i<classes.length; i++){
		if(classes[i].indexOf('chartboard-tinymce-template') != -1){
			templateClass = classes[i];
		}
	}
	return templateClass;
};

RichTextEditor.prototype.setActiveTemplate = function(template){
	var hasActiveTemplate = false;
	this.widget.find('.chartboard-tinymce-template-elem').each(function(i,e){
		$(e).removeClass('active');
		if($(e).hasClass(template)){
			hasActiveTemplate = true;
			$(e).addClass('active');
		}
	});
	if(!hasActiveTemplate){
		this.widget.find('.chartboard-tinymce-template-elem')[0].getClassList().add('active');
	}
};

RichTextEditor.prototype.getBlockColorPicker = function(){
	var id = '';
	var currentTop = 0;
	var currentLeft = 0;
	$('.mce-panel.mce-floatpanel.mce-popover').each($.proxy(function(i,e){
		var $e = $(e);
		if(parseFloat($e.css('top')) > currentTop || (parseFloat($e.css('top')) == currentTop && parseFloat($e.css('left')) > currentLeft)){
			id = $e.attr('id');
			currentTop = parseFloat($e.css('top'));
			currentLeft = parseFloat($e.css('left'));
		}
	},this));
	
	return id;
};
