var RefineEventHandler = function (parentSelector, options) {
    this.parentElt = $('.' + parentSelector);
    this.paramNameReload = options.paramNameReload;
    this.loadDateFacetParameter = options.loadDateFacetParameter;
    this.refineEventMode = options.refineEventMode;
    this.loadFacetParameter = options.loadFacetParameter;
    this.init();
};

RefineEventHandler.prototype.init = function () {
    $(this.parentElt).find('.refine-link').click($.proxy(function (e) {
        this.refine(e);
    }, this));
};

RefineEventHandler.prototype.cleanURLParameters = function (url) {
    var tempUrl = new BuildUrl(url);
    tempUrl.removeParameter(this.loadDateFacetParameter);
    tempUrl.removeParameter(this.loadFacetParameter);
    tempUrl.removeParameter(this.paramNameReload);
    return tempUrl.toString();
};

RefineEventHandler.prototype.refine = function (e) {
    var $refineLink = $(e.target).closest('.refine-link');
    var url = $refineLink.data('url');

    // Clean the URL parameters.
    url = this.cleanURLParameters(url);

    if (this.refineEventMode) {
        this.fireRefineEvent(url);
    } else {
        /* Else, then go to url */
        var url = new BuildUrl(url);
        url.removeParameter("_");

        /* Increment counter for most used filters in the quick filters */
        if (window.refineCounter) {
            var categoryId = $refineLink.data('categoryid');
            /* Increment only if a refine is performed (no zap) */
            if (categoryId && $refineLink.closest('tr.category').hasClass('displayed')) {
                var newState = window.refineCounter.incrementCategory(categoryId);
                if (!newState[categoryId].label) {
                    window.refineCounter.setCategoryLabel(categoryId, $refineLink.text().trim());
                }
            }
        }

        window.location.assign(url.toString());
    }
};

/**
 * refresh refinePanel widget and fire a 'refinePanel:refine' event with the @param url as value
 * @param url of the refine
 */
RefineEventHandler.prototype.fireRefineEvent = function (url) {
    var $refineWiget = this.$widget;
    var client = new PlmaAjaxClient($("body"), {
        success: function () {
            /*after refresh trigger event*/
            $(document).trigger("refinePanel:refine", url);
        }
    });
    client.addWidget(this.uCssId);
    var currentUrl = new BuildUrl(url);
    for (var paramName in currentUrl.params) {
        if (currentUrl.getParameters().hasOwnProperty(paramName)) {
            client.addParameters(paramName, currentUrl.getParameters()[paramName]);
        }
    }
    client.update();
};