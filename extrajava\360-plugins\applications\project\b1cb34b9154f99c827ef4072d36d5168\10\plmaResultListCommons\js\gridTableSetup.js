var GridTableSetup = function($widget, options){
	this.$widget = $widget;
	this.isGridTableLayout = this.$widget.find('.scroll-container.gridtable-layout').length > 0;
	this.options = {
		maxContent: 40,
		globalToolbarSelector: '.resultsTitle.gridtable-layout .global-toolbar',
		hitSelector : '.scroll-container.gridtable-layout ul.hits li.hit',
		toolbarContainerSelector: '.td.fixed-column .title-container .toolbarContainer',
		buttonSelectors : [],
		switchListTable: true
	}
	$(".freezeIcon ").attr("title",mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable.pin'));
	this.options = $.extend(true, this.options, options);
	this.init();
	return this;
}

GridTableSetup.VIEW_PARAMETER = 'gridtable';
GridTableSetup.VIEW_DEFAULT = 'default';
GridTableSetup.VIEW_TRANSPOSED = 'transpose';

GridTableSetup.prototype.init = function(){
	var switchListTableCSS = "fonticon fonticon-table";
	var switchListTableTitle = mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable');
	if(this.isGridTableLayout){
		switchListTableCSS = "fonticon fonticon-view-big-tile";
		switchListTableTitle = mashupI18N.get('plmaResultList', 'plma.resultlist.view.default');
		this.initSwitchView();
		this.$widget.parents('.flexPanelsContainer-item.result-panel').addClass('flex-40');
		this.handleRows();	
		$(document).ajaxComplete(function(event, request, settings){
			var url = new BuildUrl(settings.url);
			var cssId = url.getParameter('infiniteScroll')
				? url.getParameter('infiniteScroll')
				: url.getParameter('paginationLoad');
			if(this.$widget.hasClass(cssId)){
				this.handleRows(url.getParameter('infiniteScroll') == undefined);
			}
		}.bind(this));
	}
	//this.$widget.parents('.flexPanelsContainer-item.result-panel').addClass('flex-30');
	if(this.options.switchListTable){
		let $switchListTableButton = $('<span class="action-switch-listtable" title="' + switchListTableTitle + '">' +
				'<span class="' + switchListTableCSS + '"/>' +
			'</span>');
		let cssSelector = this.options.globalToolbarSelector.replace('.gridtable-layout','') + ' .insert-before';
		this.$widget.find(cssSelector).before( $switchListTableButton );
		$switchListTableButton.on('click', function(){
			let isListViewNeeded = $(this).find('span.fonticon-view-big-tile').length == 1;
			let newUrl = new BuildUrl(window.location.href);
			if(isListViewNeeded){
				newUrl.removeParameter(GridTableSetup.VIEW_PARAMETER);
			}else{
				newUrl.addParameter(GridTableSetup.VIEW_PARAMETER, GridTableSetup.VIEW_DEFAULT, true);
			}
			window.location.href = newUrl.toString() + window.location.hash;
		});
	}
}

GridTableSetup.prototype.initSwitchView = function(){
	let switchParamValue = GridTableSetup.VIEW_TRANSPOSED;
	let switchIcon = 'fonticon-table-row-column';
	let switchTitle = mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable.columns');

	if(this.$widget.find('.scroll-container.gridtable-transpose').length > 0){
		switchParamValue = GridTableSetup.VIEW_DEFAULT;
		switchIcon = 'fonticon-table-column-row';
		switchTitle = mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable.rows');
		$(".freezeIcon ").attr("title",mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable.pincol'));
	}

	let $switchViewButton = $('<span class="action-switch-view" data-switch-param-value="' + switchParamValue + '" title="' + switchTitle + '">' +
			'<span class="fonticon ' + switchIcon + '"/>' +
		'</span>');
	let cssSelector = this.options.globalToolbarSelector + ' .insert-after';
	this.$widget.find(cssSelector).after( $switchViewButton );
	$switchViewButton.on('click', function(){
		let newSwitchParamValue = $(this).attr('data-switch-param-value');
		let newUrl = new BuildUrl(window.location.href);
		newUrl.addParameter(GridTableSetup.VIEW_PARAMETER, newSwitchParamValue, true);
		window.location.href = newUrl.toString() + window.location.hash;
	});
}

GridTableSetup.prototype.handleRows = function(isPaginated){
	this.handleColumnVisibility();
	var maxIndex = this.$widget.attr('data-li-max-index')? parseInt(this.$widget.attr('data-li-max-index')) : 0;
	if(isPaginated){ maxIndex = 0; }
	var $hits = this.$widget.find(this.options.hitSelector);
	if($hits.length > maxIndex){
		$hits.each(function(li_index, li){
			if($(li).attr('data-li-index') == undefined){
				this.handleToolbarDisplay($(li), li_index);
				this.handleMaxContent($(li));
				$(li).attr('data-li-index', li_index);
				maxIndex += 1;
			}
		}.bind(this));
		this.$widget.attr('data-li-max-index', 	maxIndex);
	}
}

GridTableSetup.prototype.handleToolbarDisplay = function($hit){
	var $toolbarContainer = $hit.find(this.options.toolbarContainerSelector);
	this.options.buttonSelectors.forEach(function(buttonSelector){
		let $button = $hit.find(buttonSelector);
		$button.appendTo($toolbarContainer);
	}.bind(this));
}

GridTableSetup.prototype.handleMaxContent = function($hit){
	$hit.find('.td.table-column.hitFacet:not(.fixed-column)').each(function(col_id, col){
		if($(col).text().trim().length > this.options.maxContent){
			$(col).append('<p title="' + mashupI18N.get('plmaResultList', 'truncate-tag-show') + '">...</p>');
			$(col).addClass('large-content');
		}
	}.bind(this));
}

GridTableSetup.prototype.handleColumnVisibility = function(){
	var $columns = this.$widget.find('.scroll-container.gridtable-layout .table-header .th:not(.cellsHasContent)[data-column-show=ifNotEmpty]');
	$columns.each(function(i, el){
		var $column = $(el);
		var colName = $column.attr('data-column-name');
		var $cells = this.$widget.find(".scroll-container.gridtable-layout .td[data-column-name='" + colName + "']");
		if($cells.text().trim().length == 0){
			$cells.addClass('no-content-hidden');
			$column.addClass('no-content-hidden');
		}else{
			// Once found that the cells have content, no need to check again and again.
			$column.addClass('cellsHasContent');
			$cells.removeClass('no-content-hidden');
			$column.removeClass('no-content-hidden');
		}
	}.bind(this));
}
GridTableSetup.initFreezeRow = function($widget, $plmaButton) {
	if($plmaButton.parents('.scroll-container.gridtable-layout').length == 1){
		$plmaButton.removeClass('hidden');
		$plmaButton.find(".fonticon ").attr("title",mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable.pin'));
	}
}

/*Freeze Row old Functionality, plmaButton */
GridTableSetup.handleFreezeRow = function($widget, $plmaButton) {
	var $icon = $plmaButton.find('i');
	var $li = $plmaButton.parents('li.hit');
	var $thead = $widget.find('.scroll-container.gridtable-layout ul.fix-header');
	var $tbody = $widget.find('.scroll-container.gridtable-layout ul.hits.hitCustomList');
	if($thead.length == 0 || $tbody.length == 0){
		return;
	}

	if($icon.hasClass('fonticon-pin')){
		/*UN Freeze*/
		$plmaButton.find(".fonticon ").attr("title",mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable.pin'));
		var index = parseInt($li.attr('data-li-index'));
		$li.hide(500);
		if(index == 0){
			$tbody.prepend($li);
		}else{
			for(var sid = index-1; sid > -1; sid--){
				var $prevLi = $tbody.find('li.hit[data-li-index="' + sid + '"]');
				if($prevLi.length == 1){
					break;
				}
			}
			if($prevLi.length == 0){
				$tbody.prepend($li);
			}else{
				$prevLi.after($li);
			}
		}
		$li.show(500);
	}else{
		/*Freeze*/
		$plmaButton.find(".fonticon ").attr("title",mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable.unpin'));
		$li.hide(500);
		$thead.append($li);
		$li.show(500);
	}
	$icon.toggleClass('fonticon-pin-off fonticon-pin');
}

/*Freeze Row new functionality From config of resultList */
GridTableSetup.handleFreezeRowConfig = function(e,cssId) {
    e.stopPropagation();
    var $li = $(".hits.hitCustomList .hit.hit-list."+cssId);
    var $pinButton;
    const currentURL = window.location.href;
    const urlParams = new URLSearchParams(currentURL.split('?')[1]);
    const gridtableParam = urlParams.get('gridtable');

    if($li.length!=0 && !$li.hasClass("pinned")){
		/*Freeze*/
		$pinButton = $li.find('.freezeButtonClass').find('.freezeIcon');
		var $fixedHeader = $(".fix-header.hitCustomList");
		$li.hide(500).addClass("pinned");
		$fixedHeader.append($li);
		if (gridtableParam === 'transpose'){
            $li.find(".freezeIcon ").attr("title",mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable.unpincol'));
        }else{
            $li.find(".freezeIcon ").attr("title",mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable.unpin'));
        }
		$li.show(500);
	}else{
		/*UN Freeze*/
		$li = $(".fix-header .hit.hit-list."+cssId);
        $pinButton= $li.find('.freezeButtonClass').find('.freezeIcon');
        var $tbody = $(".hits.hitCustomList.gridtable-layout");
		var index = parseInt($li.attr('data-li-index'));
		$li.hide(500);
		if(index == 0){
			$tbody.prepend($li);
		}else{
			for(var sid = index-1; sid > -1; sid--){
				var $prevLi = $tbody.find('li.hit[data-li-index="' + sid + '"]');
				if($prevLi.length == 1){
					break;
				}
			}
			if($prevLi.length == 0){
				$tbody.prepend($li);
			}else{
				$prevLi.after($li);
			}
        }
        if(gridtableParam === 'transpose'){
            $li.find(".freezeIcon ").attr("title",mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable.pincol'));
        }else{
            $li.find(".freezeIcon ").attr("title",mashupI18N.get('plmaResultList', 'plma.resultlist.view.gridtable.pin'));
        }
        $li.show(500).removeClass("pinned");
    }
    $pinButton.toggleClass('fonticon-pin-off fonticon-pin');
 }