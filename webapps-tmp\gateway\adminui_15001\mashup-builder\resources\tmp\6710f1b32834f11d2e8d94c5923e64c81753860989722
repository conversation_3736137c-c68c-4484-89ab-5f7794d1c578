

/**
 * File: /resources/mashupBuilder/js/Window.js
 */
/**
 * Implementation of Mashup Window
 * 
 * @constructor
 * @this {Mashup_Window}
 * @param parent The window container
 * @param content The window Content class
 * @param options Additional options for the window
 */
function Mashup_Window(options) {
	options = options || {};
	this.title = options.title || '';
	this.className = $.trim('window' + ' ' + (options.className || ''));
	this.showCloseButton = options.showCloseButton != undefined ? options.showCloseButton : true;
}

/**
 * Sets the content for this Window (mandatory)
 * 
 * @returns {object}
 */
Mashup_Window.prototype.setContent = function(content) {
	this.content = content;
	this.content.setWindow(this);
	this.content.setMaxMenuWidth(600);
};

/**
 * Returns the content of this Window
 * 
 * @returns {Mashup_Window_Content}
 */
Mashup_Window.prototype.getContent = function() {
	return this.content;
};

/**
 * Returns whether the current Window is readonly or not
 * 
 * @returns
 */
Mashup_Window.prototype.isReadOnly = function() {
	return this.content.isReadOnly();
};

/**
 * Returns the DOM element for this window
 * 
 * @returns
 */
Mashup_Window.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('<div class="' + this.className + '"></div>');
		this.el.append(this.getElHeader());
		this.el.append(this.getElContent());
		this.el.on('click', $.proxy(this.handleEventOnClick, this));
		this.el.data('_this', this);
		this.el.css(this.getWindowSize());
	}
	return this.el;
};

/**
 * Returns the DOM element for the header
 * 
 * @returns
 */
Mashup_Window.prototype.getElHeader = function() {
	var html = '';
	if (this.title != '' || this.showCloseButton == true) {
		html += '<div class="window-header">';
		if (this.title != '') {
			html += '<div class="window-title">' + this.title + '</div>';
		}
		if (this.showCloseButton == true) {
			html += '<span class="icon window-close" name="doCloseWindow"></span>';
		}
		html += '</div>';
	}
	return $(html);
};

/**
 * Returns the DOM element for the content
 * 
 * @returns
 */
Mashup_Window.prototype.getElContent = function() {
	var $elContent = $('<div class="window-wrapper"></div>');
	$elContent.append(this.content.getEl());
	return $elContent;
};

/**
 * Handles 'onClick' jQuery Event
 * 
 * @param e
 */
Mashup_Window.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		if (target.attr('name') == 'doCloseWindow') {
			window.mashupBuilder.removeOpenedBottomToolbar();
			return false;
		}
	}
	return this.content.onClick({ target: target, event: e });
};

/**
 * Called when the user resized the browser
 */
Mashup_Window.prototype.onResize = function() {
	// retrieve the new size
	var windowSize = this.getWindowSize(true);

	// resize our window
	this.getEl().css(windowSize);

	// resize the content
	this.content.onResize();
};

/**
 * Called before the Window is removed
 * 
 * @param context
 * @returns {boolean} whether we can remove or not the Window
 */
Mashup_Window.prototype.canRemove = function(context) {
	return this.content.canRemove(context);
};

/**
 * Called when the Window is removed from the DOM
 * 
 * @param  context
 */
Mashup_Window.prototype.onRemove = function(context) {
	return this.content.onRemove(context);
};

/**
 * Called when the Window is closed (whether it is removed or not)
 * 
 * @param context
 */
Mashup_Window.prototype.onClose = function(context) {
	return this.content.onClose(context);
};

/**
 * Called when the window is opened
 * 
 * @param context
 */
Mashup_Window.prototype.onOpen = function(context) {
	return this.content.onOpen(context);
};

/**
 * Returns the size and position of the window
 * 
 * @param force Specifies if it should return the cached size or not
 * @returns {object}
 */
Mashup_Window.prototype.getWindowSize = function(force) {
	if (this.windowSize == undefined || (force == true || force == undefined)) {
		var $window = $(window);
		this.windowSize = {
			height: $window.height() - 100, // 50px top & bottom
			width: 1000,
			top: 50,
			left: ($window.width() / 2) - 500
		};
	}
	return this.windowSize;
};

/**
 * Removes this window from the DOM
 */
Mashup_Window.prototype.remove = function() {
	this.content.remove();
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Window/Content.js
 */
/**
 * Implementation of a Content for a Mashup Window
 * with errors handling and resizable menu
 * 
 * @constructor
 * @this {Mashup_Window_Content}
 * @param parent The parent of this content (for status propagation)
 * @param options Additional options for the content
 */
function Mashup_Window_Content(parent, options) {
	this.parent = parent;

	options = options || {};

	this.readOnly = options.isReadOnly;

	this.$errorClassEl = options.$errorClassEl || $();
	this.$errorClassElCount = options.$errorClassElCount || $();
	this.errorClassName = options.errorClassName || 'error';

	this.onCreateCallbackData = options.onCreateCallbackData || {};
	this.onCreateCallback = options.onCreateCallback || null;

	this.onOpenCallbackData = options.onOpenCallbackData || {};
	this.onOpenCallback = options.onOpenCallback || null;

	this.onCloseCallbackData = options.onCloseCallbackData || {};
	this.onCloseCallback = options.onCloseCallback || null;

	this.canRemoveCallbackData = options.canRemoveCallbackData || {};
	this.canRemoveCallback = options.canRemoveCallback || null;

	this.onRemoveCallbackData = options.onRemoveCallbackData || {};
	this.onRemoveCallback = options.onRemoveCallback || null;

	this.onResizeCallbacks = [];

	// sets errors counter
	this.errorsCount = 0;

	// default size of the side menu (if any)
	this.menuWidth = this.getDefaultMenuWidth() || 290;
	this.menuWidthMax = this.menuWidth;

	this.onMouseDownCallback = $.proxy(this.handleEventOnMouseDown, this);
	this.onMouseMoveCallback = $.proxy(this.handleEventOnMouseMove, this);
	this.onMouseUpCallback = $.proxy(this.handleEventOnMouseUp, this);
}

/**
 * Sets the window that contains this content
 * 
 * @param parent
 */
Mashup_Window_Content.prototype.setWindow = function(window) {
	this.window = window;
};

/**
 * Returns the window for this content
 * 
 * @returns
 */
Mashup_Window_Content.prototype.getWindow = function() {
	return this.window;
};

/**
 * Sets the parent for this content
 * 
 * @param menu
 */
Mashup_Window_Content.prototype.setParent = function(parent) {
	this.parent = parent;
	if (this.getMenu() != null) {
		this.getMenu().setParent(parent);
	}
};

/**
 * Returns the parent for this content
 * 
 * The parent can be a widget, a row, a cell, ...
 * 
 * @returns
 */
Mashup_Window_Content.prototype.getParent = function() {
	return this.parent;
};

/**
 * Sets the menu for this content
 * 
 * @param menu
 */
Mashup_Window_Content.prototype.setMenu = function(menu) {
	this.menu = menu;
	if (this.getParent() != null) {
		this.menu.setParent(this.getParent());
	}
};

/**
 * Returns the menu for this content
 * 
 * @param menu
 */
Mashup_Window_Content.prototype.getMenu = function() {
	return this.menu;
};

/**
 * Returns the DOM node for the menu wrapper
 * 
 * @returns
 */
Mashup_Window_Content.prototype.getElMenu = function() {
	return this.elMenu;
};

/**
 * Returns the DOM node for the content wrapper
 * 
 * @returns
 */
Mashup_Window_Content.prototype.getElContent = function() {
	return this.elContent;
};

/**
 * Returns whether this content has tabs or not
 * 
 * @returns
 */
Mashup_Window_Content.prototype.hasTabs = function() {
	return false;
};

/**
 * Returns whether the current content is readonly or not
 * 
 * @returns
 */
Mashup_Window_Content.prototype.isReadOnly = function() {
	if (this.readOnly == undefined) {
		return this.parent.isReadOnly();
	}
	return this.readOnly;
};

/**
 * Sets the maximum width for the menu
 * 
 * @param width
 */
Mashup_Window_Content.prototype.setMaxMenuWidth = function(width) {
	this.menuWidthMax = width;
};

/**
 * Sets the default menu width
 * 
 * @param width
 */
Mashup_Window_Content.prototype.saveDefaultMenuWidth = function(width) {
	mashupBuilder.state.save('m', 'w', parseInt(width));
};

/**
 * Returns the default menu width
 * 
 * @returns
 */
Mashup_Window_Content.prototype.getDefaultMenuWidth = function() {
	return parseInt(mashupBuilder.state.get('m', 'w', null));
};

/**
 * Updates the error count
 * 
 * @param {number} errorsCount Can be positive (more errors) or negative (less errors)
 */
Mashup_Window_Content.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;
	if (this.errorsCount > 0) {
		this.$errorClassEl.addClass(this.errorClassName);
		this.$errorClassElCount.html(this.errorsCount).css('display', 'inline-block');
	} else {
		this.$errorClassEl.removeClass(this.errorClassName);
		this.$errorClassElCount.hide();
	}
	this.parent.updateError(errorsCount);
};

/**
 * Returns the DOM element for this content
 */
Mashup_Window_Content.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<table cellspacing="0" cellpadding="0" border="0">' +
				'<tr>' +
					'<td class="window-menu-wrapper" rowspan="2"></td>' +
					'<td class="resize window-menu-resize" rowspan="2"></td>' +
				'</tr>' +
				'<tr>' +
					'<td class="window-content-wrapper">' +
						'<div class="window-content">' +
						'</div>' +
					'</td>' +
				'</tr>' +
			'</table>');

		this.el.data('_this', this);

		this.elMenu = this.el.find('.window-menu-wrapper');
		this.elContent = this.el.find('.window-content');

		if (this.menu != undefined) {
			this.menu.getEl().hide();
			this.elMenu.append(this.menu.getEl());
			this.el.find('.window-menu-resize').on('mousedown', this.onMouseDownCallback);
		} else {
			this.elMenu.hide();
			this.el.find('td.window-menu-resize').hide();
		}

		this.onCreate();

		this.onResize();
	}
	return this.el;
};

/**
 * Handles jQuery event 'onMouseDown'
 * 
 * @param e
 * @returns
 */
Mashup_Window_Content.prototype.handleEventOnMouseDown = function(e) {
	e.stopPropagation();

	this.getEl().addClass('disable-user-select');

	this.startX = this.menuWidth - e.pageX;

	$(document).on('mousemove', this.onMouseMoveCallback);
	$(document).one('mouseup', this.onMouseUpCallback);
};

/**
 * Handles jQuery event 'onMouseMove'
 * 
 * @param e
 * @returns
 */
Mashup_Window_Content.prototype.handleEventOnMouseMove = function(e) {
	e.stopPropagation();

	var newWidth = this.startX + e.pageX;
	if (newWidth < 250) {
		newWidth = 250;
	} else if (newWidth > this.menuWidthMax) {
		newWidth = this.menuWidthMax;
	}
	this.menuWidth = newWidth;

	this.onResize();
};

/**
 * Handles jQuery event 'onMouseUp'
 * 
 * @param e
 * @returns
 */
Mashup_Window_Content.prototype.handleEventOnMouseUp = function(e) {
	e.stopPropagation();
	this.getEl().removeClass('disable-user-select');
	$(document).unbind('mousemove', this.onMouseMove);
	this.saveDefaultMenuWidth(this.menuWidth);
};

/**
 * Called when an input inside this window gained the focus
 * 
 * @param input
 */
Mashup_Window_Content.prototype.onInputFocus = function(input) {
	if (this.menu != null) {
		if (typeof(this.menu.onInputFocus) == 'function') {
			this.menu.onInputFocus(input);
			this.menu.show();
		}
	}
};

/**
 * Called when the user clicked in the window
 */
Mashup_Window_Content.prototype.onClick = function(context) {
	var target = context.target;

	// ignore click on resize handle
	if (target.hasClass('window-menu-resize')) {
		return false;
	}

	// handle focus when clicking on label
	if (target.is('label')) {
		var parameterClass = target.closest('.parameter').data('_this');
		if (parameterClass != null) {
			parameterClass.focus();
		}
	}

	// remove any focused input
	if (this.getMenu() != null) {
		this.getMenu().onInputFocus(undefined);
	}

	context.event.stopPropagation();
};

/**
 * Called when the size of the window changed
 */
Mashup_Window_Content.prototype.onResize = function() {
	var windowSize = this.getWindow().getWindowSize();

	// update size of the table
	var tableSize = {
		width: '100%',
		height: windowSize.height - 25 // header(25)
	};
	this.el.css(tableSize);

	// update size of window-content
	var contentSize = {
		width: windowSize.width - this.menuWidth - 17, // resize(5) border(2) padding(10)
		height: tableSize.height - 20 // margin(10) bottom(10)
	};
	this.elContent.css(contentSize);

	// resize the menu if set
	if (this.menu != undefined) {
		this.elMenu.css('width', this.menuWidth + 5); // margin(5)
		this.menu.resize(this.menuWidth, tableSize.height);
	}

	// calls the onResize callbacks
	for (var i = 0; i < this.onResizeCallbacks.length; i++) {
		this.onResizeCallbacks[i].call(this);
	}
};

/**
 * Called when the content is created
 * 
 * @param context
 */
Mashup_Window_Content.prototype.onCreate = function() {
	if (this.onCreateCallback != null) {
		context = {};
		context.data = this.onCreateCallbackData;
		return this.onCreateCallback.call(this, context);
	}
};

/**
 * Called when the window is opened
 * 
 * @param context
 */
Mashup_Window_Content.prototype.onOpen = function(context) {
	if (this.onOpenCallback != null) {
		context = context || {};
		context.data = this.onOpenCallbackData;
		return this.onOpenCallback.call(this, context);
	}
};

/**
 * Called when the Window is closed (whether it is removed or not)
 * 
 * @param context
 */
Mashup_Window_Content.prototype.onClose = function(context) {
	if (this.onCloseCallback != null) {
		context = context || {};
		context.data = this.onCloseCallbackData;
		return this.onCloseCallback.call(this, context);
	}
};

/**
 * Called before the Window is removed
 * 
 * @param context
 * @returns {boolean} whether we can remove or not the Window
 */
Mashup_Window_Content.prototype.canRemove = function(context) {
	if (this.canRemoveCallback != null) {
		context = context || {};
		context.data = this.canRemoveCallbackData;
		return this.canRemoveCallback.call(this, context);
	} else if (this.errorsCount > 0) {
		// by default, don't remove the window when some errors are present
		return false;
	}
	return true;
};

/**
 * Called when the Window is removed from the DOM
 * 
 * @param  context
 */
Mashup_Window_Content.prototype.onRemove = function(context) {
	if (this.onRemoveCallback != null) {
		context = context || {};
		context.data = this.onRemoveCallbackData;
		return this.onRemoveCallback.call(this, context);
	}
};

/**
 * Registers a custom onResize callback
 * 
 * @param func
 */
Mashup_Window_Content.prototype.registerOnResize = function(func) {
	this.onResizeCallbacks.push(func);
};

/**
 * Unregisters a custom onResize callback
 * 
 * @param func
 */
Mashup_Window_Content.prototype.unregisterOnResize = function(func) {
	if ((idx = this.onResizeCallbacks.indexOf(func)) != -1) {
		this.onResizeCallbacks.splice(idx, 1);
	}
};

/**
 * Removes this content from the DOM
 */
Mashup_Window_Content.prototype.remove = function() {
	if (this.menu != undefined) {
		this.menu.remove();
	}
	if (this.el != null) {
		this.el.remove();
		this.el = undefined;
	}
};

/**
 * Returns this content as JSON
 * 
 * @returns {array}
 */
Mashup_Window_Content.prototype.getJson = function() {
	return [];
};


/**
 * File: /resources/mashupBuilder/js/Window/TabContent.js
 */
/**
 * Implementation of a Content for a Mashup Window
 * with errors handling, resizable menu and tab handling
 * 
 * @constructor
 * @this {Mashup_Window_TabContent}
 * @param parent The parent of this content (for status propagation)
 * @param options Additional options for the content
 */
Inherit(Mashup_Window_TabContent, Mashup_Window_Content);
function Mashup_Window_TabContent(parent, options) {
	Mashup_Window_TabContent.superclass.constructor.call(this, parent, options);
	this.tabs = [];
}

/**
 * Returns whether this content has tabs or not
 * 
 * @returns
 */
Mashup_Window_TabContent.prototype.hasTabs = function() {
	return this.tabs.length > 0;
};

/**
 * Returns the index of the given tab
 * 
 * @returns
 */
Mashup_Window_TabContent.prototype.indexOf = function(tab) {
	return this.tabs.indexOf(tab);
};

/**
 * Returns the DOM element for the tabs container
 */
Mashup_Window_TabContent.prototype.getElTabs = function() {
	return this.elTabs;
};

/**
 * Returns the DOM element for this content
 */
Mashup_Window_TabContent.prototype.getEl = function() {
	if (this.el == null) {

		var $elTabs = $('' +
			'<div class="window-tabs-wrapper">' +
				'<ul class="window-tabs">' +
				'</ul>' +
			'</div>'
		);

		this.getWindow().getEl().find('.window-header').append($elTabs);
		
		this.el = Mashup_Window_TabContent.superclass.getEl.call(this);
		this.elTabs = $elTabs.find('.window-tabs');

		for (var i = 0 ; i < this.tabs.length; i++) {
			var tab = this.tabs[i];
			tab.getEl().hide();
			tab.getEl().appendTo(this.getElContent());
			tab.getTabEl().appendTo(this.getElTabs());
		}

		this.openFirstTab();
	}
	return this.el;
};

/**
 * Called when the size of the window changed
 */
Mashup_Window_TabContent.prototype.onResize = function() {
	Mashup_Window_TabContent.superclass.onResize.call(this);
	for (var i = 0; i < this.tabs.length; i++) {
		this.tabs[i].onResize();
	}
};

/**
 * Called when the user clicked in the window
 */
Mashup_Window_TabContent.prototype.onClick = function(context) {
	if ((ret = Mashup_Window_TabContent.superclass.onClick.call(this, context)) != false) {
		if (context.target.attr('name') == 'openBottomTab') {
			this.openTab(context.target.closest('li').data('tabId'));
			return false;
		}
	}
	return ret;
};

/**
 * Opens the first tab
 * 
 * @returns {boolean} True if a tab has been opened
 */
Mashup_Window_TabContent.prototype.openFirstTab = function() {
	if (this.tabs.length == 0) {
		return false;
	}
	this.openTab(this.tabs[0]);
	return true;
};

/**
 * Opens the last tab
 * 
 * @returns {boolean} True if tab has been opened
 */
Mashup_Window_TabContent.prototype.openLastTab = function() {
	if (this.tabs.length == 0) {
		return false;
	}
	this.openTab(this.tabs[this.tabs.length - 1]);
	return true;
};

/**
 * Adds a Tab and append it to the DOM if already displayed
 * 
 * @param tab
 * @param idx
 * @returns
 */
Mashup_Window_TabContent.prototype.addTab = function(tab, idx) {
	tab.setParent(this);

	if (this.el != null) {
		tab.getEl().hide();
	}

	if (idx == undefined || idx == this.tabs.length) {
		this.tabs.push(tab);
		if (this.el != null) {
			tab.getEl().appendTo(this.getElContent());
			tab.getTabEl().appendTo(this.getElTabs());
		}
	} else {
		this.tabs.splice(idx, 0, tab);
		if (this.el != null) {
			tab.getEl().insertBefore(this.tabs[idx + 1].getEl());
			tab.getTabEl().insertBefore(this.tabs[idx + 1].getTabEl());
		}
	}

	return tab;
};

/**
 * Removes a tab either by its ID or by its reference
 * 
 * @param tab
 */
Mashup_Window_TabContent.prototype.removeTab = function(tab) {
	if (typeof(tab) == 'string') {
		tab = this.getTab(tab);
	}
	tab.remove();
	this.tabs.splice(this.tabs.indexOf(tab), 1);
};

/**
 * Returns the Tab for the given ID
 * 
 * @param tabId
 * @returns
 */
Mashup_Window_TabContent.prototype.getTab = function(tabId) {
	for (var i = 0 ; i < this.tabs.length; i++) {
		if (this.tabs[i].getId() == tabId) {
			return this.tabs[i];
		}
	}
	return null;
};

/**
 * Returns all the tabs
 * 
 * @returns
 */
Mashup_Window_TabContent.prototype.getTabs = function() {
	return this.tabs;
};

/**
 * Returns the currently opened tab
 * 
 * @returns
 */
Mashup_Window_TabContent.prototype.getOpenedTab = function() {
	return this.openedTab;
};

/**
 * Opens a tab either by its ID or by its reference
 * 
 * @param tab
 */
Mashup_Window_TabContent.prototype.openTab = function(tab) {
	if (typeof(tab) == 'string') {
		tab = this.getTab(tab);
	}

	if (tab == undefined || !tab.canOpen()) {
		return;
	}

	closeCodeMirrorFullScreen();

	if (this.openedTab != null) {
		this.openedTab.onClose();
		this.openedTab.getEl().hide();
		this.openedTab.getTabEl().removeClass('selected');
	}

	tab.getTabEl().addClass('selected');
	tab.getEl().show();
	tab.onOpen();
	this.openedTab = tab;
};

/**
 * Removes the content from the DOM
 */
Mashup_Window_TabContent.prototype.remove = function() {
	for (var i = 0; i < this.tabs.length; i++) {
		this.tabs[i].remove();
	}
	this.tabs = [];

	if (this.elTabs != undefined) {
		this.elTabs.remove();
		this.elTabs = undefined;
	}

	Mashup_Window_TabContent.superclass.remove.call(this);
};

/**
 * Returns this content as JSON
 * 
 * @returns {array}
 */
Mashup_Window_TabContent.prototype.getJson = function() {
	var json = [];
	for (var i = 0 ; i < this.tabs.length; i++) {
		$.merge(json, this.tabs[i].getJson());
	}
	return json;
};


/**
 * File: /resources/mashupBuilder/js/Window/TabContent/EditProperties.js
 */
/**
 * Implementation of a TabContent for properties edition of cells, widgets and rows
 * 
 * @constructor
 * @this {Mashup_Window_TabContent_EditProperties}
 * @param parent The parent of the content (for status propagation and properties edition)
 * @param options Additional options for the content
 * @param properties The properties to edit
 * @param configuration The properties configuration (default to parent.json.parameters)
 */
Inherit(Mashup_Window_TabContent_EditProperties, Mashup_Window_TabContent);
function Mashup_Window_TabContent_EditProperties(parent, options, properties, configuration) {
	Mashup_Window_TabContent_EditProperties.superclass.constructor.call(this, parent, $.extend({
		onRemoveCallbackData: { component: parent },
		onRemoveCallback: function(context) {
			context.data.component.bottomToolbar = null;
			return true;
		},

		onOpenCallbackData: { component: parent },
		onOpenCallback: function(context) {
			context.data.component.getEl().addClass('highlight');

			// focus first tab in error
			for (var i = 0; i < this.tabs.length; i++) {
				if (this.tabs[i].getErrorsCount() > 0) {
					this.openTab(this.tabs[i]);
					return;
				}
			}
		},

		onCloseCallbackData: { component: parent },
		onCloseCallback: function(context) {
			context.data.component.getEl().removeClass('highlight');
		}
	}, options));

	// create the tabs
	var config = Mashup_Parameter_Factory.createInvertedConfigParameters(configuration || parent.json.parameters);
	for (var i = 0; i < properties.length; i++) {
		this.addTab(new Mashup_Window_TabContent_Tab_Parameters(properties[i].container, properties[i].parameters, config));
	}

	// set the menu
	this.setMenu(new Mashup_Window_TabMenu_Context());
}


/**
 * File: /resources/mashupBuilder/js/Window/TabContent/WidgetDefinition.js
 */
Inherit(Mashup_Window_TabContent_WidgetDefinition, Mashup_Window_TabContent);
function Mashup_Window_TabContent_WidgetDefinition(parent, jsonOptionsGroup, options) {
	Mashup_Window_TabContent_WidgetDefinition.superclass.constructor.call(this, parent, options);
	var nbGroups = jsonOptionsGroup.length;
	if (nbGroups > 0) {
		for (var i = 0; i < nbGroups; i++) {
			var group = jsonOptionsGroup[i];
			this.addTab(new Mashup_Window_TabContent_Tab_OptionsGroup({
				id: group.name,
				jsonOptions: group.options,
				parent: this
			}));
		}
	} else {
		this.addTab(new Mashup_Window_TabContent_Tab_OptionsGroup({
			id: _.WB_TOOLBAR_GROUP_TITLE(),
			jsonOptions: [],
			parent: this
		}));
	}

	this.addTab(new Mashup_Window_TabContent_Tab_WidgetStyles());
	this.addTab(new Mashup_Window_TabContent_Tab_WidgetJavascript());

	this.setMenu(new Mashup_Window_TabMenu_Context());
}

/**
 * Returns this content as JSON
 * 
 * @returns {array}
 */
Mashup_Window_TabContent_WidgetDefinition.prototype.getJson = function() {
	var json = [];
	for (var i = 0 ; i < this.tabs.length - 2; i++) { // skip JS/Styles
		json.push(this.tabs[i].getJson());
	}
	return json;
};

/**
 * Returns the configuration sets as JSON
 * 
 * @returns {Array}
 */
Mashup_Window_TabContent_WidgetDefinition.prototype.getJsonConfigurationSets = function() {
	var defaultValues = [];
	for (var i = 0 ; i < this.tabs.length - 2; i++) { // skip JS/Styles
		$.merge(defaultValues, this.tabs[i].getJsonDefaultValues());
	}
	return [createDefaultConfigurationSet(null, defaultValues)];
};


/**
 * File: /resources/mashupBuilder/js/Window/TabContent/Tab.js
 */
/**
 * Empty tab for a TabContent
 * 
 * @constructor
 * @this {Mashup_Window_TabContent_Tab}
 * @param {hash} options Additional options for this tab
 */
function Mashup_Window_TabContent_Tab(options) {
	this.id = options.id || '';
	this.label = options.label || this.id;
	this.parent = options.parent || null;

	this.options = $.extend({
		description: '',

		isReadOnly: undefined,

		onClickCallbackData: {},
		onClickCallback: null,

		onCreateCallbackData: {},
		onCreateCallback: null
	}, options);
}

/**
 * Returns the ParameterContainer id
 * 
 * @returns {string}
 */
Mashup_Window_TabContent_Tab.prototype.getId = function() {
	return this.id;
};

/**
 * Returns the ParameterContainer label
 * 
 * @returns {string}
 */
Mashup_Window_TabContent_Tab.prototype.getLabel = function() {
	return this.label;
};

/**
 * Returns whether the ParameterContainer is ReadOnly
 * 
 * @returns {boolean}
 */
Mashup_Window_TabContent_Tab.prototype.isReadOnly = function() {
	if (this.options.isReadOnly == undefined) {
		return this.parent != null ? this.parent.isReadOnly() : false;
	}
	return this.options.isReadOnly;
};

/**
 * Returns the parent of this tab
 */
Mashup_Window_TabContent_Tab.prototype.getParent = function() {
	return this.parent;
};

/**
 * Sets the parent of this tab
 * 
 * The parent is usefull to propagate events or
 * status such as validation, read only, etc..
 * 
 * @param {object} parent
 */
Mashup_Window_TabContent_Tab.prototype.setParent = function(parent) {
	this.parent = parent;
};

/**
 * onClick event handler
 * 
 * @param e
 * @returns
 */
Mashup_Window_TabContent_Tab.prototype.handleEventOnClick = function(e) {
	var _this = e.data._this;
	if (_this.options.onClickCallback != null) {
		var target = getEventTarget(e);
		_this.options.onClickCallback.call(_this, {
			target: target,
			event: e,
			data: _this.options.onClickCallbackData
		});
	}
};

/**
 * onCreate event
 * 
 * @param context
 */
Mashup_Window_TabContent_Tab.prototype.onCreate = function(context) {
	if (this.options.onCreateCallback != null) {
		context = context || {};
		context.data = this.options.onCreateCallbackData;
		this.options.onCreateCallback.call(this, context);
	}
};

/**
 * onResize event
 * 
 * @param context
 */
Mashup_Window_TabContent_Tab.prototype.onResize = function() {
};

/**
 * Returns whether this tab can be opened or not
 * 
 * @param context
 */
Mashup_Window_TabContent_Tab.prototype.canOpen = function() {
	return true;
};

/**
 * onOpen event
 * 
 * @param context
 */
Mashup_Window_TabContent_Tab.prototype.onOpen = function() {
};

/**
 * onClose event
 * 
 * @param context
 */
Mashup_Window_TabContent_Tab.prototype.onClose = function() {
};

/**
 * Returns the DOM element for this tab label
 */
Mashup_Window_TabContent_Tab.prototype.getTabEl = function() {
	if (this.elTab == null) {
		this.elTab = $('<li class="window-tab" name="openBottomTab">' + this.label + '</li>');
		this.elTab.data('tabId', this.id);
	}
	return this.elTab;
};

/**
 * Returns the DOM element for this tab content
 * 
 * @returns
 */
Mashup_Window_TabContent_Tab.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
			'<form class="parameter-container ' + this.id + '">' +
			'</form>'
		);
		this.el.data('_this', this);

		if (this.options.description) {
			this.el.prepend('<div class="parameter-container-description"> <span class="icon"></span> ' + this.options.description + '</div>');
		}

		this.el.bind('click', {_this: this}, this.handleEventOnClick);
		this.el.bind('submit', false);

		this.onCreate();
	}
	return this.el;
};

/**
 * Removes this Tab from the DOM
 */
Mashup_Window_TabContent_Tab.prototype.remove = function() {
	if (this.el != null) {
		this.el.remove();
	}
	if (this.elTab != null) {
		this.elTab.remove();
	}
};

/**
 * Returns the JSON for this tab
 * 
 * @returns {array}
 */
Mashup_Window_TabContent_Tab.prototype.getJson = function() {
	return [];
};

/**
 * File: /resources/mashupBuilder/js/Window/TabContent/Tab/Parameters.js
 */
/**
 * BottomToolbar Tab to display Mashup Parameters
 * 
 * @constructor
 * @this {Mashup_Window_TabContent_Tab_Parameters}
 * @param {hash} tabOptions Additional options for the tab
 * @param {array} parameters The definitions of the parameters
 * @param {hash} config The values of the parameters
 */
Inherit(Mashup_Window_TabContent_Tab_Parameters, Mashup_Parameter_Container);
function Mashup_Window_TabContent_Tab_Parameters(tabOptions, parameters, config) {
	Mashup_Window_TabContent_Tab_Parameters.superclass.constructor.call(this, $.extend({
		onChangeCallback: function(context) {
			// TabContent > Component
			var component = this.getParent().getParent();
			component.json.parameters = this.getParent().getJson();
			component.onUpdate(context);
		}
	}, tabOptions));

	if (parameters != undefined) {
		config = config || {};
		for (var i = 0; i < parameters.length; i++) {
			this.addParameter(new Mashup_Parameter(defaultJsonCustomComponentParameter['class'], $.extend(parameters[i], {
				width: 450,
				values: config[parameters[i].name] ? config[parameters[i].name] : []
			})));
		}
	}
}

/**
 * Updates the error count
 * 
 * @param {number} errorsCount Can be positive (more errors) or negative (less errors)
 */
Mashup_Window_TabContent_Tab_Parameters.prototype.updateError = function(errorsCount) {
	this.errorsCount += errorsCount;
	if (this.errorsCount > 0) {
		this.getTabEl().find('.icon-error').html(this.errorsCount).css('display', 'inline-block');
	} else {
		this.getTabEl().find('.icon-error').hide();
	}
	if (this.parent != null && typeof(this.parent.updateError) == 'function') {
		this.parent.updateError(errorsCount);
	}
};

/**
 * Called when one of its input gained the focus
 * 
 * @param {Mashup_Parameter_Input_Abstract} input
 */
Mashup_Window_TabContent_Tab_Parameters.prototype.onInputFocus = function(input) {
	this.getParent().onInputFocus(input);
};

/**
 * Returns whether this tab can be opened or not
 */
Mashup_Window_TabContent_Tab_Parameters.prototype.canOpen = function() {
	return true;
};

/**
 * Called when this tab is open
 */
Mashup_Window_TabContent_Tab_Parameters.prototype.onOpen = function() {
	this.onDisplay();
};

/**
 * Called when this tab is closed
 */
Mashup_Window_TabContent_Tab_Parameters.prototype.onClose = function() {
};

/**
 * Called when the window is resized
 */
Mashup_Window_TabContent_Tab_Parameters.prototype.onResize = function() {
};

/**
 * Returns the DOM element for this tab label
 * 
 * @returns
 */
Mashup_Window_TabContent_Tab_Parameters.prototype.getTabEl = function() {
	if (this.elTab == null) {
		this.elTab = $('' +
			'<li class="window-tab" name="openBottomTab">' +
				this.getLabel() +
				'<span class="icon icon-error" name="openBottomTab">(errors)</span>' +
			'</li>'
		);
		this.elTab.data('tabId', this.getId());
	}
	return this.elTab;
};

/**
 * Removes this tab from the DOM
 */
Mashup_Window_TabContent_Tab_Parameters.prototype.remove = function() {
	Mashup_Window_TabContent_Tab_Parameters.superclass.remove.call(this);
	if (this.elTab != null) {
		this.elTab.remove();
	}
};


/**
 * File: /resources/mashupBuilder/js/Window/TabContent/Tab/OptionsGroup.js
 */
/**
 * Description of a Widget Options Group
 * 
 * @see WidgetBuilder
 * @param parent Mashup_BottomToolbar_WidgetDefinition
 * @param options
 * @returns {Mashup_Window_TabContent_Tab_OptionsGroup}
 */
Inherit(Mashup_Window_TabContent_Tab_OptionsGroup, Mashup_Window_TabContent_Tab_Parameters);
function Mashup_Window_TabContent_Tab_OptionsGroup(options) {
	this.id = options.id;
	this.parent = options.parent;
	this.jsonOptions = options.jsonOptions;
	Mashup_Window_TabContent_Tab_OptionsGroup.superclass.constructor.call(this, $.extend({
		onClickCallbackData: { _this: this },
		onClickCallback: function(context) {
			var _this = context.data._this;

			if (context.target != null) {
				switch (context.target.attr('name')) {
				case 'doCreateGroup':
					_this.doCreateGroup();
					return false;

				case 'doRemoveGroup':
					_this.doRemoveGroup();
					return false;

				case 'doCreateOption':
					_this.doCreateOption();
					return false;

				case 'doRemoveOption':
					var parameter = context.target.closest('.parameter-container-wrapper').data('_this');
					if (parameter != null) {
						_this.doRemoveOption(parameter);
					}
					return false;
				}
			}
			return true;
		}
	}, options));

	for (var i = 0; i < this.jsonOptions.length; i++) {
		this.addParameter(this.createOption(i, this.jsonOptions[i]));
	}

	this.addButtons([{
		name: 'doCreateOption',
		label: _.WB_TOOLBAR_OPTION_CREATE(),
		position: 'top'
	}, {
		name: 'doCreateGroup',
		label: _.WB_TOOLBAR_GROUP_CREATE(),
		position: 'top'
	}]);

	// cannot remove the CSS group
	if (this.id != 'CSS') {
		this.addButton({
			name: 'doRemoveGroup',
			label: _.WB_TOOLBAR_GROUP_DELETE(),
			position: 'top'
		});
	}
}

/**
 * Called when something has been updated
 * 
 * @returns {object}
 */
Mashup_Window_TabContent_Tab_OptionsGroup.prototype.onUpdate = function() {
	window.widgetBuilder.jsonWidgetDefinition.optionsGroup = this.getParent().getJson();
	window.widgetBuilder.jsonWidgetDefinition.defaultConfigurationSets = this.getParent().getJsonConfigurationSets();
	window.widgetBuilder.toolbox.updateAvailableActions();
	this.getParent().getParent().onUpdate();
};

/**
 * User process to create an option
 */
Mashup_Window_TabContent_Tab_OptionsGroup.prototype.doCreateOption = function() {
	var jsonOption = clone(defaultJsonOption);
	this.addParameter(this.createOption(this.jsonOptions.length, jsonOption));
	this.jsonOptions.push(jsonOption);
	this.onUpdate();
};

/**
 * User process to remove an option
 */
Mashup_Window_TabContent_Tab_OptionsGroup.prototype.doRemoveOption = function(parameter) {
	var _this = this;
	new Mashup_Popup_Confirm({
		title: _.WB_TOOLBAR_OPTION_DELETE_TITLE(),
		text: _.WB_TOOLBAR_OPTION_DELETE_TEXT(),
		buttonOkLabel: _.YES(),
		buttonCancelLabel: _.NO(),
		onClickOkCallback: function() {
			parameter.getParameterContainer().removeParameter(parameter);
			_this.onUpdate();
			this.remove();
		}
	}).show();
};

/**
 * User process to create a new group
 */
Mashup_Window_TabContent_Tab_OptionsGroup.prototype.doCreateGroup = function() {
	var _this = this;
	new Mashup_Popup_Prompt({
		title: _.WB_TOOLBAR_GROUP_CREATE_TITLE(),
		label: _.WB_TOOLBAR_GROUP_CREATE_TEXT(),
		onClickOkCallback: function(context, groupId) {
			this.remove();
			if (isEmpty(groupId) == false) {
				if (_this.parent.getTab(groupId) == null) {
					_this.parent.addTab(new Mashup_Window_TabContent_Tab_OptionsGroup({
						id: groupId,
						jsonOptions: []
					}), _this.parent.indexOf(_this));
					_this.onUpdate();
				}
				_this.parent.openTab(groupId);
			}
		}
	}).show();
};

/**
 * User process to remove this group
 */
Mashup_Window_TabContent_Tab_OptionsGroup.prototype.doRemoveGroup = function() {
	var _this = this;
	new Mashup_Popup_Confirm({
		title: _.WB_TOOLBAR_GROUP_DELETE_TITLE(),
		text: _.WB_TOOLBAR_GROUP_DELETE_TEXT(),
		buttonOkLabel: _.YES(),
		buttonCancelLabel: _.NO(),
		onClickOkCallback: function() {
			_this.parent.removeTab(_this);
			_this.onUpdate();
			if (_this.parent.openFirstTab() == false) {
				// Couldn't open the first tab
				var newOptionsGroup = new Mashup_Window_TabContent_Tab_OptionsGroup({
					id: _.WB_TOOLBAR_GROUP_TITLE(),
					jsonOptions: []
				});
				_this.parent.addTab(newOptionsGroup, 0);
				_this.parent.openTab(newOptionsGroup);
			}
			this.remove();
		}
	}).show();
};

/**
 * Returns a parameter option for the given option ID
 * 
 * @param jsonOption
 * @returns {Mashup_Parameter_Container}
 */
Mashup_Window_TabContent_Tab_OptionsGroup.prototype.createOption = function(idx, jsonOption) {
	var parameterContainer = new Mashup_Parameter_Container({
		id: 'property_' + idx,
		label: _.WB_TOOLBAR_OPTION() + (jsonOption.name != null ? (' ' + jsonOption.name) : ''),
		showToggle: true,
		parent: this.getParent(),
		showLabel: true,
		onChangeCallbackData : { _this : this },
		onChangeCallback : function(context) {
			context.data._this.onUpdate();
		}
	});

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'id',
		label: _.WB_TOOLBAR_ID(),
		values: [jsonOption.id],
		arity: PARAMETER_ARITY.ONE,
		description: '',
		width: 400
	})));

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'name',
		label: _.WB_TOOLBAR_NAME(),
		values: [jsonOption.name],
		arity: PARAMETER_ARITY.ONE,
		description: '',
		width: 400,
		onChangeCallback: function(context) {
			this.getParameterContainer().getEl().find('.parameter-container-title').html(_.WB_TOOLBAR_OPTION() + ' ' + this.getValue().escapeHTML());
		}
	})));

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'arity',
		label: _.WB_TOOLBAR_ARITY(),
		values: [jsonOption.arity],
		arity: PARAMETER_ARITY.ONE,
		possibleValues: ['ONE', 'ZERO_OR_ONE'],
		//possibleValues: ['ZERO', 'ZERO_OR_ONE', 'ONE', 'ZERO_OR_MANY', 'MANY'],
		description: '',
		width: 400
	})));

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'description',
		label: _.WB_TOOLBAR_DESCRIPTION(),
		values: [jsonOption.description],
		description: '',
		width: 400
	})));

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'placeholder',
		label: _.WB_TOOLBAR_PLACEHOLDER(),
		values: [jsonOption.placeholder],
		description: '',
		width: 400
	})));

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'value',
		label: _.WB_TOOLBAR_VALUES(),
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		values: jsonOption.value,
		description: _.WB_TOOLBAR_VALUES_DESCRIPTION(),
		width: 400
	})));

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'defaultValue',
		label: _.WB_TOOLBAR_DEFAULT_VALUE(),
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		values: jsonOption.defaultValue, // injected by WidgetBuilder
		description: _.WB_TOOLBAR_DEFAULT_VALUE_DESCRIPTION(),
		width: 400
	})));

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'isEvaluated',
		label: _.WB_TOOLBAR_EVALUATED(),
		arity: PARAMETER_ARITY.ONE,
		values: [jsonOption.isEvaluated + ''],
		possibleValues: ['false', 'true'],
		description: _.WB_TOOLBAR_EVALUATED_DESCRIPTION(),
		width: 400
	})));

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'isXmlEscaped',
		label: _.WB_TOOLBAR_ESCAPED(),
		arity: PARAMETER_ARITY.ONE,
		values: [jsonOption.isXmlEscaped + ''],
		possibleValues: ['false', 'true'],
		description: _.WB_TOOLBAR_ESCAPED_DESCRIPTION(),
		width: 400
	})));
	
	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'isJsEscaped',
		label: _.WB_TOOLBAR_JS_ESCAPED(),
		arity: PARAMETER_ARITY.ONE,
		values: [jsonOption.isJsEscaped + ''],
		possibleValues: ['false', 'true'],
		description: _.WB_TOOLBAR_JS_ESCAPED_DESCRIPTION(),
		width: 400
	})));
	
	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'isHtmlEscaped',
		label: _.WB_TOOLBAR_HTML_ESCAPED(),
		arity: PARAMETER_ARITY.ONE,
		values: [jsonOption.isHtmlEscaped + ''],
		possibleValues: ['false', 'true'],
		description: _.WB_TOOLBAR_HTML_ESCAPED_DESCRIPTION(),
		width: 400
	})));

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'isUrlEncoded',
		label: _.WB_TOOLBAR_ENCODED(),
		arity: PARAMETER_ARITY.ONE,
		values: [jsonOption.isUrlEncoded + ''],
		possibleValues: ['false', 'true'],
		description: _.WB_TOOLBAR_ENCODED_DESCRIPTION(),
		width: 400
	})));

	var checkFcts = [{
		display: 'isInteger',
		value: 'isInteger()'
	}, {
		display: 'isAlphanum',
		value: 'isAlphanum()'
	}, {
		display: 'isPageName',
		value: 'isPageName()'
	}, {
		display: 'isEmpty',
		value: 'isEmpty()'
	}, {
		display: 'isJspPath',
		value: 'isJspPath()'
	}, {
		display: 'isBagKey',
		value: 'isBagKey()'
	}];

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'functionsCheck',
		label: _.WB_TOOLBAR_CHECK(),
		values: (jsonOption.functions.length > 0 ? jsonOption.functions[0].functionsCheck : []),
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		possibleValues: [''],
		description: '',
		width: 400,
		context: [{ name: 'addContext', parameters: [_.WB_TOOLBAR_CHECK(), checkFcts] }, { name: 'emptyOnChange', parameters: [] }]
	})));

	var ctxFcts = [{
		display: 'Metas',
		value: 'Metas()',
		title: _.WB_TOOLBAR_CTX_METAS()
	}, {
		display: 'Feeds',
		value: 'Feeds()',
		title: _.WB_TOOLBAR_CTX_FEEDS()
	}, {
		display: 'Facets',
		value: 'Facets()',
		title: _.WB_TOOLBAR_CTX_FACETS()
	}, {
		display: 'Normal facets',
		value: 'NormalFacets()',
		title: _.WB_TOOLBAR_CTX_FACETS_NORMAL()
	}, {
		display: 'Geographic facets',
		value: 'GeoFacets()',
		title: _.WB_TOOLBAR_CTX_FACETS_GEO()
	}, {
		display: 'Hierarchical 2D facets',
		value: 'Hierarchical2DFacets()',
		title: _.WB_TOOLBAR_CTX_FACETS_HIERARCHICAL()
	}, {
		display: 'Multi dimension facets',
		value: 'MultiDimensionFacets()',
		title: _.WB_TOOLBAR_CTX_FACETS_MULTIDIM()
	}, {
		display: 'Date facets',
		value: 'DateFacets()',
		title: _.WB_TOOLBAR_CTX_FACETS_DATE()
	}, {
		display: 'Numerical facets',
		value: 'NumericalFacets()',
		title: _.WB_TOOLBAR_CTX_FACETS_NUMERICAL()
	}, {
		display: 'Aggregations',
		value: 'Aggregations()',
		title: _.WB_TOOLBAR_CTX_AGGR()
	}, {
		display: 'Eval',
		value: 'Eval()',
		title: _.WB_TOOLBAR_CTX_EVAL()
	}, {
		display: 'Pages',
		value: 'Pages()',
		title: _.WB_TOOLBAR_CTX_PAGES()
	}, {
		display: 'Page parameters',
		value: 'PageParameters()',
		title: _.WB_TOOLBAR_CTX_PAGEPARAMS()
	}, {
		display: 'Sorts',
		value: 'Sorts()',
		title: _.WB_TOOLBAR_CTX_SORTS()
	}, {
		display: 'Fields',
		value: 'Fields()',
		title: _.WB_TOOLBAR_CTX_FIELDS()
	}, {
		display: 'WUIDS',
		value: 'WUIDS()',
		title: _.WB_TOOLBAR_CTX_WUIDS()
	}, {
		display: 'OnChange: empty',
		value: 'emptyOnChange()',
		title: _.WB_TOOLBAR_CTX_EMPTYONCHANGE()
	}, {
		display: 'OnChange: append',
		value: 'appendOnChange(\', \')',
		title: _.WB_TOOLBAR_CTX_APPENDONCHANGE()
	}];

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'functionsContextMenu',
		label: _.WB_TOOLBAR_CONTEXT(),
		values: (jsonOption.functions.length > 0 ? jsonOption.functions[0].functionsContextMenu : []),
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		possibleValues: [''],
		description: '',
		width: 400,
		context: [{ name: 'addContext', parameters: [_.WB_TOOLBAR_CONTEXT(), ctxFcts] }, { name: 'emptyOnChange', parameters: [] }]
	})));

	var displayFcts = [{
		display: 'Password',
		value: 'SetType(\'password\')',
		title: _.WB_TOOLBAR_DISPLAY_TYPE_PASSWORD()
	}, {
		display: 'Number',
		value: 'SetType(\'number\')',
		title: _.WB_TOOLBAR_DISPLAY_TYPE_NUMBER()
	}, {
		display: 'Radio',
		value: 'SetType(\'radio\')',
		title: _.WB_TOOLBAR_DISPLAY_TYPE_RADIO()
	}, {
		display: 'Textarea',
		value: 'SetType(\'textarea\')',
		title: _.WB_TOOLBAR_DISPLAY_TYPE_TEXTAREA()
	}, {
		display: 'Code editor',
		value: 'SetType(\'code\', \'js\')',
		title: _.WB_TOOLBAR_DISPLAY_TYPE_CODE()
	}, {
		display: 'Text editor',
		value: 'SetType(\'tinymce\')',
		title: _.WB_TOOLBAR_DISPLAY_TYPE_RICHTEXT()
	}, {
		display: 'ToggleDisplay',
		value: 'ToggleDisplay({ valueToMatch: \'\', hideOptions: [], showOptions: [], ifEquals: true })',
		title: _.WB_TOOLBAR_DISPLAY_TOGGLE_DISPLAY()
	}, {
		display: 'SetHeight',
		value: 'SetHeight(lineHeight)',
		title: _.WB_TOOLBAR_DISPLAY_SET_HEIGHT()
	}];

	parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		name: 'functionsDisplay',
		label: _.WB_TOOLBAR_DISPLAY(),
		values: (jsonOption.functions.length > 0 ? jsonOption.functions[0].functionsDisplay : []),
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		possibleValues: [''],
		description: '',
		width: 400,
		context: [{ name: 'addContext', parameters: [_.WB_TOOLBAR_DISPLAY(), displayFcts] }, { name: 'emptyOnChange', parameters: [] }]
	})));

	parameterContainer.addButton({
		name: 'doRemoveOption',
		label: _.WB_TOOLBAR_OPTION_DELETE()
	});

	return parameterContainer;
};

/**
 * Returns the container JSON formatted
 * 
 * @param container
 */
Mashup_Window_TabContent_Tab_OptionsGroup.prototype.getContainerJson = function(container, json) {
	json.id = container.getParameter('id').getValue();
	json.name = container.getParameter('name').getValue();
	json.arity = container.getParameter('arity').getValue();
	json.description = container.getParameter('description').getValue() || '';
	json.placeholder = container.getParameter('placeholder').getValue() || '';
	json.value = jQuery.makeArray(container.getParameter('value').getValues());

	json.isEvaluated = container.getParameter('isEvaluated').getValue() == 'true';
	json.isXmlEscaped = container.getParameter('isXmlEscaped').getValue() == 'true';
	json.isUrlEncoded = container.getParameter('isUrlEncoded').getValue() == 'true';
	json.isJsEscaped = container.getParameter('isJsEscaped').getValue() == 'true';
	json.isHtmlEscaped = container.getParameter('isHtmlEscaped').getValue() == 'true';

	json.functions[0] = $.extend({}, defaultJsonOptionsFunctions, {
		functionsCheck: jQuery.makeArray(container.getParameter('functionsCheck').getValues().filter(function(item) { return (item.length != 0); })),
		functionsContextMenu: jQuery.makeArray(container.getParameter('functionsContextMenu').getValues().filter(function(item) { return (item.length != 0); })),
		functionsDisplay: jQuery.makeArray(container.getParameter('functionsDisplay').getValues().filter(function(item) { return (item.length != 0); }))
	});

	return json;
};

/**
 * Returns this OptionsGroup as JSON
 * 
 * @returns {object}
 */
Mashup_Window_TabContent_Tab_OptionsGroup.prototype.getJson = function() {
	var group = createOptionsGroup(this.getId());
	for (var i = 0; i < this.parameterClasses.length; i++) {
		group.options.push(this.getContainerJson(this.parameterClasses[i], this.jsonOptions[i]));
	}
	return group;
};

/**
 * Returns this DefaultValues as JSON
 * 
 * @returns {object}
 */
Mashup_Window_TabContent_Tab_OptionsGroup.prototype.getJsonDefaultValues = function() {
	var defaultValues = [];
	for (var i = 0; i < this.parameterClasses.length; i++) {
		var container = this.parameterClasses[i];
		var name = container.getParameter('id').getValue();
		if (name.length > 0) {
			var values = container.getParameter('defaultValue').getValues();
			for (var j = 0; j < values.length; j++) {
				if (values[j].length > 0) {
					defaultValues.push(createDefaultValue(name, values[j]));
				}
			}
		}
	}
	return defaultValues;
};


/**
 * File: /resources/mashupBuilder/js/Window/TabContent/Tab/WidgetInfos.js
 */
Inherit(Mashup_Window_TabContent_Tab_WidgetInfos, Mashup_Window_TabContent_Tab);
function Mashup_Window_TabContent_Tab_WidgetInfos(widget) {
	Mashup_Window_TabContent_Tab_WidgetInfos.superclass.constructor.call(this, {
		id: 'widgetInfos',
		label: '<span class="icon icon-help" name="openBottomTab"></span>',
		parent: widget,
		isReadOnly: true
	});

	this.containers = [];

	var definition = widget.getWidgetDefinition();
	this.registerInformationsContainer(widget, definition);
	this.registerSupportedFeedsContainer(widget, definition);
	this.registerSupportedWidgetsContainer(widget, definition);
	this.registerI18NContainer(widget, definition);
	this.registerIncludesContainer(widget, definition);
	this.registerDependenciesContainer(widget, definition);
	this.registerInteractionsContainer(widget, definition);
	this.registerStorageKeysContainer(widget, definition);
}

/**
 * Returns the number of errors (needed because implemented by Mashup_Window_TabContent_Tab_Parameters)
 */
Mashup_Window_TabContent_Tab_WidgetInfos.prototype.getErrorsCount = function() {
	return 0;
};

Mashup_Window_TabContent_Tab_WidgetInfos.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('<div class="parameter-container-wrapper-list"></div>');
		for (var i = 0; i < this.containers.length; i++) {
			this.el.append(this.containers[i].getEl());
		}
	}
	return this.el;
};

Mashup_Window_TabContent_Tab_WidgetInfos.prototype.registerInformationsContainer = function(widget, definition) {
	var container = new Mashup_Parameter_Container({
		id: 'widgetInfos',
		label: _.WIDGET_TOOLBAR_INFOS(),
		showToggle: true,
		showLabel: true,
		parent: this
	});

	container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		type: 'Literal',
		width: 450,
		name: 'id',
		label: 'ID',
		values: [widget.getId()]
	})));

	container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		type: 'Literal',
		width: 450,
		name: 'wuid',
		label: 'WUID',
		description: _.WIDGET_WUID_DESC(),
		values: [widget.getWuid()]
	})));

	if (definition.group != null) {
		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			type: 'Literal',
			width: 450,
			name: 'group',
			label: _.WIDGET_TOOLBAR_INFOS_GROUP(),
			values: [definition.group.replace('/', ' > ')]
		})));
	}

	container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		type: 'Literal',
		width: 450,
		name: 'description',
		label: _.WIDGET_TOOLBAR_INFOS_DESCRIPTION(),
		values: [definition.description]
	})));

	var platforms = definition.platforms;
	if (platforms.length > 0) {
		var platformValues = [];
		for (var i = 0; i < platforms.length; i++) {
			if (platforms[i].supported == true) {
				platformValues.push(platforms[i].type);
			}
		}

		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			type: 'Literal',
			width: 450,
			name: 'platform',
			arity: PARAMETER_ARITY.ZERO_OR_MANY,
			label: _.WIDGET_TOOLBAR_INFOS_PLATFORMS(),
			values: platformValues
		})));
	}

	this.containers.push(container);
};

Mashup_Window_TabContent_Tab_WidgetInfos.prototype.registerSupportedFeedsContainer = function(widget, definition) {
	if (definition.supportFeedsId.arity == 'ZERO') {
		return;
	}

	var container = new Mashup_Parameter_Container({
		id: 'widgetFeedIds',
		label: _.WIDGET_TOOLBAR_SUPPORTEDFEEDS(),
		showToggle: true,
		showLabel: true,
		parent: this
	});

	container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		type: 'Literal',
		width: 450,
		name: 'arity',
		arity: PARAMETER_ARITY.ONE,
		label: _.WIDGET_TOOLBAR_SUPPORTFEEDS_ARITY(),
		values: [definition.supportFeedsId.arity]
	})));

	var feedIds;
	if (definition.supportFeedsId.feedsId.length > 0) {
		feedIds = definition.supportFeedsId.feedsId;
	} else {
		feedIds = [_.WIDGET_TOOLBAR_SUPPORTFEEDS_CLASSNAMES_NORESTRICT()];
	}

	container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		type: 'Literal',
		width: 450,
		name: 'arity',
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		label: _.WIDGET_TOOLBAR_SUPPORTFEEDS_CLASSNAMES(),
		values: feedIds
	})));

	this.containers.push(container);
};

Mashup_Window_TabContent_Tab_WidgetInfos.prototype.registerSupportedWidgetsContainer = function(widget, definition) {
	if (definition.supportWidgetsId.arity == 'ZERO') {
		return;
	}

	var container = new Mashup_Parameter_Container({
		id: 'widgetSubwidgets',
		label: _.WIDGET_TOOLBAR_SUPPORTWIDGETS(),
		showToggle: true,
		showLabel: true,
		parent: this
	});

	container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		type: 'Literal',
		width: 450,
		name: 'arity',
		arity: PARAMETER_ARITY.ONE,
		label: _.WIDGET_TOOLBAR_SUPPORTWIDGETS_ARITY(),
		values: [definition.supportWidgetsId.arity]
	})));

	var labels = [];
	if (definition.supportWidgetsId.widgetsId.length > 0) {
		var widgetsId = definition.supportWidgetsId.widgetsId;
		for (var i = 0; i < widgetsId.length; i++) {
			if ((def = getWidgetDefinition(widgetsId[i])) != null) {
				labels.push(def.name);
			} else {
				labels.push(widgetsId[i]);
			}
		}
	} else {
		labels.push(_.WIDGET_TOOLBAR_SUPPORTWIDGETS_IDS_NORESTRICT());
	}

	container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		type: 'Literal',
		width: 450,
		name: 'arity',
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		label: _.WIDGET_TOOLBAR_SUPPORTWIDGETS_IDS(),
		values: labels
	})));

	this.containers.push(container);
};

Mashup_Window_TabContent_Tab_WidgetInfos.prototype.registerI18NContainer = function(widget, definition) {
	var languages = definition.supportI18N.languages;
	if (definition.supportI18N.supported == false || languages.length == 0) {
		return;
	}

	var container = new Mashup_Parameter_Container({
		id: 'widgetI18n',
		label: _.WIDGET_TOOLBAR_I18N(),
		showToggle: true,
		showLabel: true,
		parent: this
	});

	var i18nValues = [];
	for (var i = 0; i < languages.length; i++) {
		var cc = languages[i];
		i18nValues.push('<img src="' + window.mashup.baseUrl + 'resources/commons/images/flags/' + cc + '.gif" alt="' + cc + '" /> ' + cc);
	}

	container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
		type: 'Literal',
		width: 450,
		name: 'i18n',
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		label: _.WIDGET_TOOLBAR_I18N_SUPPORTED(),
		values: i18nValues
	})));

	this.containers.push(container);
};

Mashup_Window_TabContent_Tab_WidgetInfos.prototype.registerIncludesContainer = function(widget, definition) {
	if (definition.includes.length == 0) {
		return;
	}

	var container = new Mashup_Parameter_Container({
		id: 'widgetIncludes',
		label: _.WIDGET_TOOLBAR_INCLUDES(),
		showToggle: true,
		showLabel: true,
		parent: this
	});

	var cssValues = [], jsValues = [];
	for (var i = 0; i < definition.includes.length; i++) {
		var include = definition.includes[i];
		var text = include.path;

		if (text == undefined || text.length == 0) {
			text = 'undefined';
		} else if (text[0] != '/') {
			if (isWidgetFromPlugin(widget.getId())) {
				text = '/WEB-INF/jsp/widgets_plugin_tmp/' + widget.getId() + '/' + text;
			} else {
				text = '/WEB-INF/jsp/widgets/' + widget.getId() + '/' + text;
			}
		}

		if (include.includeCondition) {
			text += ' <span class="conditions">(if ' + include.includeCondition + ')</span>';
		}

		if (include.type == 'JS') {
			jsValues.push(text);
		} else {
			cssValues.push(text);
		}
	}

	if (cssValues.length > 0) {
		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			type: 'Literal',
			width: 450,
			name: 'css',
			arity: PARAMETER_ARITY.ZERO_OR_MANY,
			label: _.WIDGET_TOOLBAR_INCLUDES_CSS(),
			values: cssValues
		})));
	}

	if (jsValues.length > 0) {
		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			type: 'Literal',
			width: 450,
			name: 'js',
			arity: PARAMETER_ARITY.ZERO_OR_MANY,
			label: _.WIDGET_TOOLBAR_INCLUDES_JS(),
			values: jsValues
		})));
	}

	this.containers.push(container);
};

Mashup_Window_TabContent_Tab_WidgetInfos.prototype.registerDependenciesContainer = function(widget, definition) {
	if (definition.dependencies.triggers.length == 0 && definition.dependencies.widgets.length == 0) {
		return;
	}

	var container = new Mashup_Parameter_Container({
		id: 'widgetDependencies',
		label: _.WIDGET_TOOLBAR_DEPENDENCIES(),
		showToggle: true,
		showLabel: true,
		parent: this
	});

	var widgetValues = [];
	for (var i = 0; i < definition.dependencies.widgets.length; i++) {
		widgetValues.push(definition.dependencies.widgets[i].name);
	}

	if (widgetValues.length > 0) {
		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			type: 'Literal',
			width: 450,
			name: 'widgets',
			arity: PARAMETER_ARITY.ZERO_OR_MANY,
			label: _.WIDGET_TOOLBAR_DEPENDENCIES_WIDGET(),
			description: _.WIDGET_TOOLBAR_DEPENDENCIES_WIDGET_DESCRIPTION(),
			values: widgetValues
		})));
	}

	var triggerValues = [];
	for (var i = 0; i < definition.dependencies.triggers.length; i++) {
		triggerValues.push('<b>' + definition.dependencies.triggers[i].position + '</b> ' + definition.dependencies.triggers[i].className);
	}

	if (triggerValues.length > 0) {
		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			type: 'Literal',
			width: 450,
			name: 'triggers',
			arity: PARAMETER_ARITY.ZERO_OR_MANY,
			label: _.WIDGET_TOOLBAR_DEPENDENCIES_TRIGGER(),
			values: triggerValues
		})));
	}

	this.containers.push(container);
};

Mashup_Window_TabContent_Tab_WidgetInfos.prototype.registerInteractionsContainer = function(widget, definition) {
	if (definition.interactions.length == 0) {
		return;
	}

	var container = new Mashup_Parameter_Container({
		id: 'widgetInteractions',
		label: _.WIDGET_TOOLBAR_INTERACTIONS(),
		showToggle: true,
		showLabel: true,
		parent: this
	});

	var interationsIn = [], interationsOut = [];
	for (var i = 0; i < definition.interactions.length; i++) {
		var interaction = definition.interactions[i];
		if (interaction.type == 'IN') {
			interationsIn.push(interaction.id);
		} else {
			interationsOut.push(interaction.id);
		}
	}

	if (interationsIn.length > 0) {
		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			type: 'Literal',
			width: 450,
			name: 'interationsIn',
			arity: PARAMETER_ARITY.ZERO_OR_MANY,
			label: _.WIDGET_TOOLBAR_INTERACTIONS_IN(),
			values: interationsIn
		})));
	}

	if (interationsOut.length > 0) {
		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			type: 'Literal',
			width: 450,
			name: 'interationsOut',
			arity: PARAMETER_ARITY.ZERO_OR_MANY,
			label: _.WIDGET_TOOLBAR_INTERACTIONS_OUT(),
			values: interationsOut
		})));
	}

	this.containers.push(container);
};

Mashup_Window_TabContent_Tab_WidgetInfos.prototype.registerStorageKeysContainer = function(widget, definition) {
	if (definition.storageKeys.length == 0) {
		return;
	}

	var container = new Mashup_Parameter_Container({
		id: 'widgetStorageKeys',
		label: _.WIDGET_TOOLBAR_STORAGEKEYS(),
		showToggle: true,
		showLabel: true,
		parent: this
	});

	var keyValues = [], optValues = [];
	for (var i = 0; i < definition.storageKeys.length; i++) {
		var storageKey = definition.storageKeys[i];
		if (storageKey.key != null) {
			keyValues.push(storageKey.key + ' / '+ storageKey.resource);
		} else {
			optValues.push(storageKey.optionId + ' / '+ storageKey.resource);
		}
	}

	if (keyValues.length > 0) {
		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			type: 'Literal',
			width: 450,
			name: 'keys',
			arity: PARAMETER_ARITY.ZERO_OR_MANY,
			label: _.WIDGET_TOOLBAR_STORAGEKEYS_KEYS(),
			values: keyValues
		})));
	}

	if (optValues.length > 0) {
		container.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			type: 'Literal',
			width: 450,
			name: 'options',
			arity: PARAMETER_ARITY.ZERO_OR_MANY,
			label: _.WIDGET_TOOLBAR_STORAGEKEYS_OPTIONS(),
			values: optValues
		})));
	}

	this.containers.push(container);
};


/**
 * File: /resources/mashupBuilder/js/Window/TabContent/Tab/WidgetJavascript.js
 */
/**
 * Handles the inlined and external Javascript of a widget
 * 
 * @see WidgetBuilder
 * @param parent Mashup_BottomToolbar_WidgetDefinition
 * @param options
 * @returns {Mashup_Window_TabContent_Tab_WidgetJavascript}
 */
Inherit(Mashup_Window_TabContent_Tab_WidgetJavascript, Mashup_Window_TabContent_Tab_Parameters);
function Mashup_Window_TabContent_Tab_WidgetJavascript() {
	Mashup_Window_TabContent_Tab_WidgetJavascript.superclass.constructor.call(this, {
		id: _.WB_TOOLBAR_JAVASCRIPT_TITLE(),
		onChangeCallbackData : { _this : this },
		onChangeCallback : function(context) {
			context.data._this.onUpdate();
		}
	});
	this.addParameter(new Mashup_Parameter('', {
		name: 'jsFile',
		label: _.WB_TOOLBAR_JAVASCRIPT_FILE_LABEL(),
		description: _.WB_TOOLBAR_JAVASCRIPT_FILE_DESC(),
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		width: 400,
		values: getJsonParameterValues(window.widgetBuilder.getPage().getUI().json.parameters, 'jsFile'),
		inputs: [{
			type: 'Input',
			name: 'jsFile'
		}],
		onChangeCallbackData: { _this: this },
		onChangeCallback: function(context) {
			setJsonParameter(window.widgetBuilder.getPage().getUI().json.parameters, 'jsFile', this.getValues());
		}
	}));

	this.addParameter(new Mashup_Parameter('', {
		name: 'jsCode',
		label: _.WB_TOOLBAR_JAVASCRIPT_INLINED_LABEL(),
		description: _.WB_TOOLBAR_JAVASCRIPT_INLINED_DESC(),
		arity: PARAMETER_ARITY.ZERO_OR_ONE,
		width: 400,
		values: getJsonParameterValues(window.widgetBuilder.getPage().getUI().json.parameters, 'jsCode'),
		inputs: [{
			type: 'Code',
			name: 'jsCode',
			options: {
				arguments: 'js'
			}
		}],
		onChangeCallbackData: { _this: this },
		onChangeCallback: function(context) {
			setJsonParameter(window.widgetBuilder.getPage().getUI().json.parameters, 'jsCode', this.getValue());
		}
	}));
}

/**
 * Called when something has been updated
 * 
 * @returns {object}
 */
Mashup_Window_TabContent_Tab_WidgetJavascript.prototype.onUpdate = function() {
	this.getParent().getParent().onUpdate();
};

/**
 * File: /resources/mashupBuilder/js/Window/TabContent/Tab/WidgetPreview.js
 */
Inherit(Mashup_Window_TabContent_Tab_WidgetPreview, Mashup_Window_TabContent_Tab);
function Mashup_Window_TabContent_Tab_WidgetPreview(widget) {
	Mashup_Window_TabContent_Tab_WidgetPreview.superclass.constructor.call(this, {
		id: 'widgetPreview',
		label: '<span class="icon icon-preview" name="openBottomTab"></span>'
	});
	this.widget = widget;
	this.onSaveCallback = $.proxy(this.refreshDetachedPreview, this);
}

/**
 * Returns the number of errors (needed because implemented by Mashup_Window_TabContent_Tab_Parameters)
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.getErrorsCount = function() {
	return 0;
};

/**
 * Returns whether this tab can be opened or not
 *
 * @param context
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.canOpen = function() {
	if ((nbErrors = window.mashupBuilder.getErrorsCount()) > 0) {
		new Mashup_Popup_Error(nbErrors).show();
		return false;
	}
	return true;
};

/**
 * onOpen event
 *
 * @param context
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.onOpen = function() {
	if (this.widget.getPageUI().getPage().isSecurityEnable()) {
		this.getEl().html('<div class="widget-livepreview-unavailable">' + _.WIDGET_TOOLBAR_PREVIEW_UNAVAILABLE_SECURITY() + '</div>');
		this.getElIframe().detach();
	} else if (!this.widget.getPageUI().getPage().isUpToDate()) {
		this.getEl().html('<div class="widget-livepreview-unavailable">' + _.WIDGET_TOOLBAR_PREVIEW_UNAVAILABLE_NOTUPTODATE() + '</div>');
		this.getElIframe().detach();
	} else {
		if (window.mashupBuilder.isUpdated()) {
			this.widget.json.parameters = this.getParent().getJson();
			window.mashupBuilder.config.save({
				doApply: false,
				doWait: true
			});
		}
		this.getElIframe().appendTo(this.getEl().find('.widget-livepreview'));
		this.reloadIframe();
	}
};

/**
 * onClose event
 *
 * @param context
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.onClose = function() {
	this.getElIframe().detach();
};

/**
 * onClick event
 *
 * @param context
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.onClick = function(context) {
	switch (context.target.attr('name')) {
	case 'doRefreshPreview':
		this.reloadIframe();
		return false;
	case 'doDetachPreview':
		this.detachPreview();
		return false;
	}
	return true;
};

/**
 * onResize event
 *
 * @param context
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.onResize = function() {
	var windowSize = this.getParent().getWindow().getWindowSize(),
		iframeHeight = windowSize.height - 70; // header(25) margin(10) bottom(10) space(20)

	this.getElIframe().css('height', iframeHeight);
};

/**
 * Called when one of its input gained the focus
 *
 * @param {Mashup_Parameter_Input_Abstract} input
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.onInputFocus = function(input) {
	this.getParent().onInputFocus(input);
};

/**
 * Returns whether a detach preview exists or not
 * 
 * @returns {Boolean}
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.hasDetachedPreview = function() {
	return this.popupWindow != undefined && !this.popupWindow.closed;
};

/**
 * Detaches the widget preview in a new window
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.detachPreview = function() {
	if (this.popupWindow == undefined) {
		window.mashupBuilder.config.registerOnSave(this.onSaveCallback);
	}
	var width = this.getElIframe().width(), height = this.getElIframe().height();
	this.popupWindow = window.open(this.getPreviewUrl(), 'widgetpreview', 'titlebar=0,location=0,directories=0,status=0,menubar=0,toolbar=0,width=' + width + ',height=' + height);
};

/**
 * Refreshes the detach preview
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.refreshDetachedPreview = function() {
	if (this.hasDetachedPreview()) {
		this.detachPreview();
	}
};

/**
 * Reloads the iframe
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.reloadIframe = function() {
	var iframe = this.getElIframe()[0];

	// update the iframe src
	iframe.src = this.getPreviewUrl();

	// handle loading spinner
	var _this = this;
	this.getElSpinner().appendTo(this.getEl());
	bindIframeOnLoad(iframe, function() {
		_this.getElSpinner().detach();
	});
};

/**
 * Returns the URL of the preview
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.getPreviewUrl = function() {
	// compute the iframe URL
	var urlBuilder = new BuildUrl(getMashupUIStaging() + '/' + this.widget.getWuid() + '/' + this.widget.getPageUI().getPageName());
	var parameters = this.filterContainer.getJson();
	for (var i = 0; i < parameters.length; i++) {
		if (parameters[i].name != 'debugMode' && parameters[i].name != 'previewParameter' && parameters[i].name != 'maxResults') {
			var rawValue = parameters[i].value.split('##'),
				name = rawValue[0], value = rawValue[1];
			if (name.length > 0) {
				urlBuilder.addParameter(name, value);
			}
		}
	}

	var maxResults = this.widget.getPageUI().getPage().getState('mr', 1);
	if (maxResults != undefined && maxResults != '') {
		urlBuilder.addParameter('__maxResults', parseInt(maxResults));
	}

	// adds a random number to prevent cache issue
	urlBuilder.addParameter('__time', (new Date()).getTime());

	return urlBuilder.toString();
};

Mashup_Window_TabContent_Tab_WidgetPreview.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = $('' +
			'<div class="widget-livepreview-wrapper">' +
				'<div class="widget-livepreview-config">' +
				'</div>' +
				'<div class="widget-livepreview">' +
				'</div>' +
		'</div>');

		this.el.find('.widget-livepreview-config').append(this.getFilterContainer().getEl());
		this.getFilterContainer().getEl().find('.parameter-container-title').append('<span class="icon icon-detach-preview" name="doDetachPreview" title="' + _.WIDGET_TOOLBAR_PREVIEW_DETACH() + '"></span>');
	}
	return this.el;
};

/**
 * Returns the DOM element for the iframe
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.getElIframe = function() {
	if (this.elIframe == undefined) {
		this.elIframe = $('<iframe class="widget-livepreview-iframe"></iframe>');
	}
	return this.elIframe;
};

/**
 * Returns the DOM element for the spinner
 *
 * @returns
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.getElSpinner = function() {
	if (this.elSpinner == undefined) {
		this.elSpinner = $('' +
			'<div class="widget-livepreview-spinner">' +
				'<img src="' + window.mashup.baseUrl + 'resources/commons/images/spinner.gif" />' +
				'<span class="widget-livepreview-spinner-text">' + _.WIDGET_TOOLBAR_PREVIEW_LOADING_TEXT() + '</span>' +
			'</div>');
	}
	return this.elSpinner;
};


/**
 * Returns the filter container
 *
 * @returns {Mashup_Parameter_Container}
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.getFilterContainer = function() {
	if (this.filterContainer == undefined) {
		this.filterContainer = this.initFilterContainer();
	}
	return this.filterContainer;
};

/**
 * Initialize the filter container
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.initFilterContainer = function() {
	var page = this.widget.getPageUI().getPage();

	var container = new Mashup_Parameter_Container({
		id: 'widgetPreview',
		label: _.WIDGET_TOOLBAR_PREVIEW_TITLE(),
		showToggle: false,
		showLabel: true,
		parent: this,
		isReadOnly: false, // force 'false' for readonly
		onClickCallback: $.proxy(this.onClick, this)
	});

	var isSet = function(name, array) {
		for (var i = 0; i < array.length; i++) {
			if (array[i] != undefined && array[i].indexOf(name) == 0) {
				return true;
			}
		}
		return false;
	};

	var filterValues = page.getState('p', []);
	var pageParameters = window.mashupBuilder.getToolboxContainer().getToolbox('pageParameters').getJson();
	for (var i = 0; i < pageParameters.length; i++) {
		if (isSet(pageParameters[i].name + '##', filterValues) == false) {
			filterValues.push(pageParameters[i].name + '##' + pageParameters[i].value);
		}
	}

	container.addParameter(new Mashup_Parameter('', {
		name: 'qs',
		width: 185,
		values: filterValues,
		isValueOrdered: false,
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		inputs: [{
			type: 'Input',
			name: 'qs_name',
			label: _.WIDGET_TOOLBAR_PREVIEW_LABEL_NAME(),
			options: {
				context: [{ name: 'PageParameters', parameters: [] }, { name: 'Feeds', parameters: [] }],
				width: 50
			}
		}, {
			type: 'Input',
			name: 'qs_value',
			label: _.WIDGET_TOOLBAR_PREVIEW_LABEL_VALUE(),
			options: {
				context: [{ name: 'FacetsId', parameters: [] }],
				width: 50
			}
		}],
		onChangeCallbackData: { page: page, },
		onChangeCallback: function(context) {
			var json = this.getJson();

			// get the user values
			var values = [];
			for (var i = 0; i < json.length; i++) {
				if (json[i].name != 'debugMode' && json[i].name != 'previewParameter' && json[i].name != 'maxResults') {
					values.push(json[i].value);
				}
			}

			// filter against page params
			var pageParameters = window.mashupBuilder.getToolboxContainer().getToolbox('pageParameters').getJson();
			for (var i = 0; i < pageParameters.length; i++) {
				var value = pageParameters[i].name + '##' + pageParameters[i].value;
				if ((idx = values.indexOf(value)) != -1) {
					values.splice(idx, 1);
				}
			}

			// save the filtred values
			context.data.page.saveState('p', values.length == 0 ? null : values);
		}
	}));


	var _this = this;
	// button to refresh the preview
	container.addParameter(new Mashup_Parameter('', {
		name: 'doRefreshPreview',
		label: _.TOOLBOX_PREVIEWPARAMETER_LABEL_REFRESH(),
		width: 185,
		inputs: [{
			type: 'Button',
			options: {
				label: _.TOOLBOX_PREVIEWPARAMETER_BUTTON_REFRESH(),
				width: 55,
				onClickCallback: function() {
					if (!window.mashupBuilder.getOpenedPage().isSecurityEnable()) {
						_this.reloadIframe();
						_this.refreshDetachedPreview();
					}
					return false;
				}
			}
		}]
	}));

	// set the max results
	container.addParameter(new Mashup_Parameter('', {
		name: 'maxResults',
		label: _.TOOLBOX_PREVIEWPARAMETER_LABEL_MAXRESULTS(),
		width: 185,
		values: [page.getState('mr', '')],
		onChangeCallback: function() {
			var value = this.getValue();
			page.saveState('mr', value.length > 0 ? value : null);
			if (!window.mashupBuilder.getOpenedPage().isSecurityEnable()) {
				_this.reloadIframe();
				_this.refreshDetachedPreview();
			}
			return false;
		},
		inputs: [{
			name: 'maxResults',
			type: 'Select',
			options: {
				possibleValues: ['', '0', '1', '2', '5', '10', '20', '50'],
				width: 30
			}
		}]
	}));

	// adds the debug mode
	container.addParameter(new Mashup_Parameter('', {
		name: 'debugMode',
		label: _.APP_DEV_DEVMODE_LABEL(),
		description: _.APP_DEV_DEVMODE_DESCRIPTION(),
		width: 185,
		values: [window.mashupBuilder.isDebugMode() + ''],
		onChangeCallback: function() {
			window.mashupBuilder.services.application.setDebugMode(this.getValue() == 'true', function() {
				if (!this.getOpenedPage().isSecurityEnable()) {
					_this.reloadIframe();
					_this.refreshDetachedPreview();
				}
			});
			return false;
		},
		inputs: [{
			type: 'Checkbox',
			name: 'debugMode',
			options: {
				possibleValues: ['false', 'true'],
				width: 55
			}
		}]
	}));

	return container;
};

/**
 * Removes the tab from the DOM
 */
Mashup_Window_TabContent_Tab_WidgetPreview.prototype.remove = function() {
	this.filterContainer.remove();
	if (this.hasDetachedPreview()) {
		window.mashupBuilder.config.unregisterOnSave(this.onSaveCallback);
		this.popupWindow.close();
		this.popupWindow = undefined;
	}
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
	this.elIframe = undefined;
};


/**
 * File: /resources/mashupBuilder/js/Window/TabContent/Tab/WidgetStyles.js
 */
/**
 * Handles the inlined and external styles of a widget
 * 
 * @see WidgetBuilder
 * @param parent Mashup_BottomToolbar_WidgetDefinition
 * @param options
 * @returns {Mashup_Window_TabContent_Tab_WidgetStyles}
 */
Inherit(Mashup_Window_TabContent_Tab_WidgetStyles, Mashup_Window_TabContent_Tab_Parameters);
function Mashup_Window_TabContent_Tab_WidgetStyles() {
	Mashup_Window_TabContent_Tab_WidgetStyles.superclass.constructor.call(this, {
		id: _.WB_TOOLBAR_STYLES_TITLE(),
		onChangeCallbackData : { _this : this },
		onChangeCallback : function(context) {
			context.data._this.onUpdate();
		}
	});

	this.addParameter(new Mashup_Parameter('', {
		name: 'cssFile',
		label: _.WB_TOOLBAR_STYLES_FILE_LABEL(),
		description: _.WB_TOOLBAR_STYLES_FILE_DESC(),
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		width: 400,
		values: getJsonParameterValues(window.widgetBuilder.getPage().getUI().json.parameters, 'cssFile'),
		inputs: [{
			type: 'Input',
			name: 'cssFile'
		}],
		onChangeCallbackData: { _this: this },
		onChangeCallback: function(context) {
			setJsonParameter(window.widgetBuilder.getPage().getUI().json.parameters, 'cssFile', this.getValues());
		}
	}));

	this.addParameter(new Mashup_Parameter('', {
		name: 'cssStyle',
		label: _.WB_TOOLBAR_STYLES_INLINED_LABEL(),
		description: _.WB_TOOLBAR_STYLES_INLINED_DESC(),
		arity: PARAMETER_ARITY.ZERO_OR_ONE,
		width: 400,
		values: getJsonParameterValues(window.widgetBuilder.getPage().getUI().json.parameters, 'cssStyle'),
		inputs: [{
			type: 'Code',
			name: 'cssStyle',
			options: {
				arguments: 'css'
			}
		}],
		onChangeCallbackData: { _this: this },
		onChangeCallback: function(context) {
			setJsonParameter(window.widgetBuilder.getPage().getUI().json.parameters, 'cssStyle', this.getValue());
		}
	}));
}

/**
 * Called when something has been updated
 * 
 * @returns {object}
 */
Mashup_Window_TabContent_Tab_WidgetStyles.prototype.onUpdate = function() {
	this.getParent().getParent().onUpdate();
};

/**
 * File: /resources/mashupBuilder/js/Window/Menu.js
 */
/**
 * Implementation of a Menu for a Mashup Window
 * 
 * @constructor
 * @this {Mashup_Window_Menu}
 */
function Mashup_Window_Menu(options) {
	options = options || {};

	this.title = options.title || '';
	this.className = options.className || '';
	this.showReload = options.showReload != undefined ? options.showReload : true;
	this.showClose = options.showClose != undefined ? options.showClose : false;

	this.onCreateCallback = options.onCreateCallback || null;
	this.onCreateCallbackData = options.onCreateCallbackData || {};

	this.onClickCallback = options.onClickCallback || null;
	this.onClickCallbackData = options.onClickCallbackData || {};
}

/**
 * Sets the parent for this Menu
 * 
 * The parent can be a widget, a row, a cell, ..
 * 
 * @param parent
 */
Mashup_Window_Menu.prototype.setParent = function(parent) {
	this.parent = parent;
};

/**
 * Returns the parent of the Menu
 * 
 * @returns
 */
Mashup_Window_Menu.prototype.getParent = function() {
	return this.parent;
};

/**
 * Sets the Parameter Input for this Menu
 * 
 * @param parameterInput
 */
Mashup_Window_Menu.prototype.onInputFocus = function(parameterInput) {
	// to implement
};

/**
 * Returns the Parameter Input or NULL
 * 
 * @returns
 */
Mashup_Window_Menu.prototype.getParameterInput = function() {
	return this.parameterInput;
};

/**
 * Returns the Parameter Value or NULL
 * 
 * @returns
 */
Mashup_Window_Menu.prototype.getParameterValue = function() {
	if ((input = this.getParameterInput()) != null) {
		return input.getParameterValue();
	}
	return null;
};

/**
 * Returns the Parameter or NULL
 * 
 * @returns
 */
Mashup_Window_Menu.prototype.getParameter = function() {
	if ((value = this.getParameterValue()) != null) {
		return value.getParameter();
	}
	return null;
};

/**
 * Returns the Parameter Container or NULL
 * @returns
 */
Mashup_Window_Menu.prototype.getParameterContainer = function() {
	if ((parameter = this.getParameter()) != null) {
		return parameter.getParameterContainer();
	}
	return null;
};

/**
 * @param {event} e
 * @private
 */
Mashup_Window_Menu.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'reload':
			this.refresh();
			break;
		case 'close':
			this.onInputFocus(undefined);
			break;
		case 'doOpenDoc':
			window.mashup.documentation.doOpenDoc(target);
			break;
		}
	}

	if (this.onClickCallback != null) {
		var target = getEventTarget(e);
		this.onClickCallback.call(this, {
			target: target,
			event: e,
			data: this.onClickCallbackData
		});
	}

	if (this.parameterInput != null) {
		this.parameterInput.focus();
	}

	return false;
};

/**
 * Create/Return the element of the menu
 * @private
 */
Mashup_Window_Menu.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
			'<div class="window-menu ' + (this.className != '' ? ' ' + this.className : '') + '">' +
				'<div class="window-menu-header">' +
					'<span class="window-menu-header-icons">' +
						'<span class="icon icon-reload" name="reload" title="' + _.CTX_RELOAD_MENU() + '"></span>' +
						'<span class="icon icon-documentation" name="doOpenDoc" title="' + _.CTX_OPEN_DOCUMENTATION() + '"></span>' +
						'<span class="icon icon-close" name="close" title="' + _.CTX_CLOSE_MENU() + '"></span>' +
					'</span>' +
					'</div>' +
			'</div>'
		);
		this.el.data('_this', this);

		if (this.title != '') {
			this.el.find('.window-menu-header').prepend('<h4 class="window-menu-title">' + this.title + '</h4>');
		}

		this.el.find('.icon-documentation').hide();
		if (this.showReload == false) {
			this.el.find('.icon-reload').hide();
		}
		if (this.showClose == false) {
			this.el.find('.icon-close').hide();
		}

		this.el.on('click', $.proxy(this.handleEventOnClick, this));
		this.onCreate();
	}
	return this.el;
};

/**
 * Called when the menu is created
 * 
 * @param context
 */
Mashup_Window_Menu.prototype.onCreate = function() {
	if (this.onCreateCallback != null) {
		context = {};
		context.data = this.onCreateCallbackData;
		return this.onCreateCallback.call(this, context);
	}
};

/**
 * Resizes the Menu
 * 
 * @param width
 * @param height
 */
Mashup_Window_Menu.prototype.resize = function(width, height) {
	height -= 10; // margin(10)

	if (this.width != width || this.height != height) {
		this.getEl().css({ width: width, height: height });
	}

	this.width = width;
	this.height = height;
};

/**
 * Refresh the Menu
 */
Mashup_Window_Menu.prototype.refresh = function() {
	// to implement
};

/**
 * Remove the menu from the DOM
 */
Mashup_Window_Menu.prototype.remove = function() {
	if (this.el != null) {
		this.el.remove();
	}
};


/**
 * File: /resources/mashupBuilder/js/Window/TabMenu.js
 */
/**
 * Implementation of a Menu for a Mashup Window
 * 
 * @constructor
 * @this {Mashup_Window_TabMenu}
 */
Inherit(Mashup_Window_TabMenu, Mashup_Window_Menu);
function Mashup_Window_TabMenu(options) {
	Mashup_Window_TabMenu.superclass.constructor.call(this, options);
	this.tabs = [];
}

/**
 * @param {event} e
 * @private
 */
Mashup_Window_TabMenu.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		if (target.attr('name') == 'doOpenTab') {
			var tabId = target.attr('data-tabid');
			window.mashupBuilder.state.save('m', 'c', tabId);
			this.openTab(tabId);
		} else if (this.openedTab != null) {
			this.openedTab.onClick({
				target: target,
				event: e
			});
		}
	}
	return Mashup_Window_TabMenu.superclass.handleEventOnClick.call(this, e);
};

/**
 * Create/Return the element of the menu
 * @private
 */
Mashup_Window_TabMenu.prototype.getEl = function() {
	if (this.el == null) {
		this.el = Mashup_Window_TabMenu.superclass.getEl.call(this);
		this.el.find('.window-menu-header').prepend('<ul class="window-menu-tabs"></ul>');

		this.elTabs = this.el.find('.window-menu-tabs');
		for (var i = 0; i < this.tabs.length; i++) {
			var tab = this.tabs[i];
			tab.getEl().hide();
			tab.getEl().appendTo(this.el);
			tab.getElTab().appendTo(this.elTabs);
		}

		if ((defaultTabId = this.getDefaultTabId()) != null) {
			this.openTab(defaultTabId);
		}
	}
	return this.el;
};

/**
 * Resizes the Menu
 * 
 * @param width
 * @param height
 */
Mashup_Window_TabMenu.prototype.resize = function(width, height) {
	Mashup_Window_TabMenu.superclass.resize.call(this, width, height);
	this.getEl().find('.window-menu-tab-content').css('height', this.height - 32); // header(26) padding(6)
};

/**
 * Refresh the Menu
 */
Mashup_Window_TabMenu.prototype.refresh = function() {
	if (this.el == undefined) {
		return; // nothing to refresh
	}

	for (var i = 0; i < this.tabs.length; i++) {
		this.tabs[i].refresh();
	}

	if (this.openedTab != undefined) {
		this.openTab(this.openedTab);
	}
};

/**
 * Returns the default tab name
 */
Mashup_Window_TabMenu.prototype.getDefaultTabId = function() {
	// check saved tab name
	if ((tabId = window.mashupBuilder.state.get('m', 'c', null)) != null) {
		if ((tab = this.getTab(tabId)) != null && tab.isVisible()) {
			return tabId;
		}
	}

	// otherwise take the first visible tab
	for (var i = 0; i < this.tabs.length; i++) {
		if (this.tabs[i].isVisible()) {
			return this.tabs[i].getId();
		}
	}

	return null;
};

/**
 * Opens the first tab
 */
Mashup_Window_TabMenu.prototype.openFirstTab = function() {
	for (var i = 0; i < this.tabs.length; i++) {
		if (this.tabs[i].isVisible()) {
			this.openTab(this.tabs[i]);
			return;
		}
	}
};

/**
 * Opens a tab
 * 
 * @param tab The tab ID or the tab class
 */
Mashup_Window_TabMenu.prototype.openTab = function(tab) {
	// retrieve the tab to open
	if (typeof(tab) == 'string') {
		tab = this.getTab(tab);
		if (tab == null) {
			return;
		}
	}

	// close previous tab
	if (this.openedTab != null) {
		this.openedTab.getElTab().removeClass('selected');
		this.openedTab.getEl().hide();
	}
	this.openedTab = tab;

	// adds the tab to the DOM
	tab.getEl().show();
	tab.getElTab().addClass('selected');

	// enables or disables documentation for this tab
	var $docIcon = this.getEl().find('span.icon[name=doOpenDoc]');
	var docChapter = tab.getDocumentation();
	$docIcon.css('display', docChapter ? 'inline-block' : 'none');
	$docIcon.attr('data-chapter', docChapter);
};

/**
 * Adds a tab to the Menu
 * 
 * @param tab
 * @returns
 */
Mashup_Window_TabMenu.prototype.addTab = function(tab) {
	this.tabs.push(tab);
	if (this.el != null) {
		tab.getEl().hide();
		tab.getEl().appendTo(this.el);
		tab.getEl().css('height', this.height - 30);
		tab.getElTab().appendTo(this.elTabs);
	}
	return tab;
};

/**
 * Removes a tab from the Menu
 * 
 * @param tab
 */
Mashup_Window_TabMenu.prototype.removeTab = function(tab) {
	tab.remove();
	this.tabs.splice(this.tabs.indexOf(tab), 1);
};

/**
 * Returns the currently opened tab
 * 
 * @returns
 */
Mashup_Window_TabMenu.prototype.getOpenedTab = function() {
	return this.openedTab;
};

/**
 * Returns a tab by its ID
 * 
 * @param tabId
 * @returns
 */
Mashup_Window_TabMenu.prototype.getTab = function(tabId) {
	var nbTabClasses = this.tabs.length;
	for (var i = 0; i < nbTabClasses; i++) {
		if (this.tabs[i].id == tabId) {
			return this.tabs[i];
		}
	}
	return null;
};

/**
 * Remove the menu from the DOM
 */
Mashup_Window_TabMenu.prototype.remove = function() {
	for (var i = 0; i < this.tabs.length; i++) {
		this.tabs[i].remove();
	}
	this.tabs = [];

	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Window/TabMenu/Tab.js
 */
function Mashup_Window_TabMenu_Tab(parent, id, name) {
	this.parent = parent;
	this.id = id;
	this.name = name;
}

/**
 * Returns the ID of the tab
 * 
 * @returns
 */
Mashup_Window_TabMenu_Tab.prototype.getId = function() {
	return this.id;
};

/**
 * Returns whether this tab is visible or not
 * 
 * @returns
 */
Mashup_Window_TabMenu_Tab.prototype.isVisible = function() {
	return this.visible != undefined ? this.visible : true;
};

/**
 * Force whether this tab is visible or not
 * 
 * @returns
 */
Mashup_Window_TabMenu_Tab.prototype.setVisible = function(visible) {
	this.visible = visible;
};

Mashup_Window_TabMenu_Tab.prototype.getMenu = function() {
	return this.parent;
};

Mashup_Window_TabMenu_Tab.prototype.getParent = function() {
	return this.getMenu().getParent();
};

Mashup_Window_TabMenu_Tab.prototype.getDocumentation = function() {
	return null;
};

/**
 * Called when the user clicked on the tab
 * 
 * @param context
 */
Mashup_Window_TabMenu_Tab.prototype.onClick = function(context) {
	
};

Mashup_Window_TabMenu_Tab.prototype.getElTab = function() {
	if (this.elTab == null) {
		this.elTab = $('<li class="window-menu-tab ' + this.id + '" name="doOpenTab" data-tabid="' + this.id + '">' + this.name + '</li>');
	}
	return this.elTab;
};

Mashup_Window_TabMenu_Tab.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('<div class="window-menu-tab-content ' + this.id + '"></div>');
		this.el.on('mousewheel DOMMouseScroll', mashupBuilder.handleEventOnScroll);
	}
	return this.el;
};

/**
 * Called when the context of the tab were updated
 */
Mashup_Window_TabMenu_Tab.prototype.refresh = function() {
};

Mashup_Window_TabMenu_Tab.prototype.remove = function() {
	if (this.el != null) {
		this.getEl().remove();
	}
};

/**
 * File: /resources/mashupBuilder/js/Window/TabMenu/Context.js
 */
/**
 * Implementation of a Menu for a Mashup Window
 * 
 * @constructor
 * @this {Mashup_Window_TabMenu_Context}
 */
Inherit(Mashup_Window_TabMenu_Context, Mashup_Window_TabMenu);
function Mashup_Window_TabMenu_Context(options) {
	options = options || {};
	Mashup_Window_TabMenu_Context.superclass.constructor.call(this, $.extend(options, {
		className: 'window-menu-context'
	}));

	this.onChange = { empty: false, append: '' };

	this.addTab(new Mashup_Window_TabMenu_Context_Tab_Values(this, 'values', _.CTX_TAB_VALUES_TITLE()));
	this.addTab(new Mashup_Window_TabMenu_Context_Tab_MELFunctions(this, 'functions', _.CTX_TAB_FUNCTIONS_TITLE()));
	this.addTab(new Mashup_Window_TabMenu_Context_Tab_MELDebug(this, 'debug',  _.CTX_TAB_DEBUG_TITLE()));

	window.mashupBuilder.context.registerMenu(this);
}

/**
 * Displays the context menu if there is something to display
 */
Mashup_Window_TabMenu_Context.prototype.show = function() {
	var isMenuVisible = false;

	for (var i = 0; i < this.tabs.length; i++) {
		var tab = this.tabs[i], isVisible = tab.isVisible();
		isMenuVisible |= isVisible;
		tab.getElTab().css('display', isVisible ? 'inline-block' : 'none');
	}

	if (isMenuVisible) {
		if ((this.getOpenedTab() == undefined || !this.getOpenedTab().isVisible())) {
			this.openTab(this.getDefaultTabId());
		}
		this.getEl().show();
	}
};

/**
 * Returns whether this menu has a visible tab or not
 */
Mashup_Window_TabMenu_Context.prototype.hasVisibleTab = function() {
	for (var i = 0; i < this.tabs.length; i++) {
		if (this.tabs[i].isVisible()) {
			return true;
		}
	}
	return false;
};

/**
 * Returns the current input
 * 
 * @param parameterInput
 */
Mashup_Window_TabMenu_Context.prototype.getParameterInput = function() {
	return this.parameterInput;
};

/**
 * Sets the Parameter Input for this Context Menu
 * 
 * @param parameterInput
 */
Mashup_Window_TabMenu_Context.prototype.onInputFocus = function(parameterInput) {
	// same input, do nothing
	if (parameterInput == this.parameterInput) {
		return;
	}

	// remove focus of the previous input
	if (this.parameterInput != undefined) {
		this.parameterInput.getEl().removeClass('focus');
	}

	// sets focus to the new input
	this.parameterInput = parameterInput;
	if (parameterInput != undefined) {
		this.parameterInput.getEl().addClass('focus');
	}

	// refresh the context menu
	this.onChange = { empty: false, append: '' };
	this.refresh(false);

	// shows the context menu if values, hides otherwise
	if (!this.hasVisibleTab()) {
		this.getEl().hide();
	} else {
		this.getEl().show();
	}
};

/**
 * @param {event} e
 * @private
 */
Mashup_Window_TabMenu_Context.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'doFillInput':
			if (this.parameterInput != null) {
				if (this.onChange.empty == true) {
					this.parameterInput.val('');
				}

				var value = target.closest('li').attr('valueOnClick');
				if (MELVariable.isVariable(value)) {
					value = new MELVariable(value)
								.setUrlEncoded(this.parameterInput.isUrlEncodedByDefault())
								.setXmlEscaped(this.parameterInput.isXmlEscapedByDefault())
								.setHighlighted(this.parameterInput.isHighlightedByDefault())
								.setJsEscaped(this.parameterInput.isJsEscapedByDefault())
								.setHtmlEscaped(this.parameterInput.isHtmlEscapedByDefault())
								.toString();
				}

				// try to match a pattern in the appended value
				var matches = value.match(/__([^_]+)__/);

				this.parameterInput.insert(value + this.onChange.append);

				// if there was a pattern then sets focus on it
				if (matches != null) {
					value = this.parameterInput.val();
					if ((indexOf = value.lastIndexOf(matches[0])) != -1) {
						this.parameterInput.setSelection(indexOf, indexOf + matches[0].length);
					}
				}

				this.parameterInput.change();
			}
			return false;

		case 'toggleList':
			target.closest('h4').next("ul").slideToggle(200);
			target.closest('h4').find("span:first").toggleClass('icon-right icon-expanded');
			return false;

		case 'filter':
			e.stopPropagation();
			return true;
		}
	}
	return Mashup_Window_TabMenu_Context.superclass.handleEventOnClick.call(this, e);
};

/**
 * Refresh the Context Menu
 * 
 * @param reloadContext
 */
Mashup_Window_TabMenu_Context.prototype.refresh = function(reloadContext) {
	if (reloadContext == undefined || reloadContext == true) {
		window.mashupBuilder.context.reload({ global: true, forceNow: true });
	}
	Mashup_Window_TabMenu_Context.superclass.refresh.call(this);
};

/**
 * Remove the context menu from the DOM
 */
Mashup_Window_TabMenu_Context.prototype.remove = function() {
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
	window.mashupBuilder.context.unregisterMenu(this);
	Mashup_Window_TabMenu_Context.superclass.remove.call(this);
};


/**
 * File: /resources/mashupBuilder/js/Window/TabMenu/Context/Tab/Values.js
 */
/**
 * Implementation of the tab of the Context Menu which contains the values
 * 
 * @constructor
 * @this {Mashup_Window_TabMenu_Context_Tab_Values}
 */
Inherit(Mashup_Window_TabMenu_Context_Tab_Values, Mashup_Window_TabMenu_Tab);
function Mashup_Window_TabMenu_Context_Tab_Values(menu, id, name) {
	Mashup_Window_TabMenu_Context_Tab_Values.superclass.constructor.call(this, menu, id, name);
	this.hasValues = false;
}

/**
 * Returns the feeds for the current context or the given parameterId
 *
 * @returns {Array}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.getFeeds = function(parameterId) {
	// retrieve feeds from context
	if (parameterId == undefined) {
		if ((feed = this.getFeed()) != null) {
			if (feed.getId().length > 0) {
				return [feed];
			}
		} else if ((widget = this.getWidget()) != null) {
			return widget.getFeedClasses(true, false);
		}
		return [];
	}

	// retrieve feeds from parameter values
	if ((values = this._getInputValues(parameterId)).length == 0) {
		return [];
	}
	if ((page = window.mashupBuilder.getOpenedPage()) == null || !page.hasAPI()) {
		return [];
	}
	var feeds = [];
	for (var i = 0; i < values.length; i++) {
		if ((feed = page.getAPI().getFeed(values[i])) != null) {
			feeds.push(feed);
		}
	}
	return feeds;
};

/**
 * Returns the feed or NULL if not attached to a feed
 *
 * @returns {Mashup_Page_API_Layout_Feed}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.getFeed = function() {
	if (this.getParent().constructor == Mashup_Page_API_Layout_Feed) {
		return this.getParent();
	} else if (this.getParent().constructor == Mashup_Page_Layout_Trigger && this.getParent().getParent().constructor == Mashup_Page_API_Layout_Feed) {
		return this.getParent().getParent();
	}
	return null;
};

/**
 * Returns the widget or NULL if not attached to a widget
 *
 * @returns {Mashup_Page_UI_Layout_Widget}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.getWidget = function() {
	if (isWidget(this.getParent())) {
		return this.getParent();
	} else if (this.getParent().constructor == Mashup_Page_Layout_Trigger && isWidget(this.getParent().getParent())) {
		return this.getParent().getParent();
	}
	return null;
};

/**
 * Returns the trigger or NULL if not attached to a trigger
 *
 * @returns {Mashup_Page_Layout_Trigger}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.getTrigger = function() {
	if (this.getParent().constructor == Mashup_Page_Layout_Trigger) {
		return this.getParent();
	}
	return null;
};

/**
 * Returns the feed trigger or NULL if not attached to a trigger
 *
 * @returns {Mashup_Page_Layout_Trigger}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.getFeedTrigger = function() {
        if (this.getParent().constructor == Mashup_Page_Layout_Trigger) {
            var trigger = this.getParent();
            if (trigger.getType() == 'feedTrigger') {
                return trigger;
            }
    }
    return null;
};

/**
 * Returns the page set in the current option ID if given or current page
 * otherwise
 * 
 * @param parameterId
 * @returns {Mashup_Page}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.getPage = function(parameterId) {
	var page;
	if (typeof(parameterId) == 'undefined') {
		page = window.mashupBuilder.getOpenedPage();
		if (page.getName() == null || page.getName() == '') {
			return null;
		}
	} else {
		// get value of parameter id 'parameterId'
		if ((values = this._getInputValues(parameterId)).length == 0) {
			return null;
		}
		page = window.mashupBuilder.getPageClass(values[0]);
		if (page == null) {
			/*//*/ console.error(_.CTX_ERROR_PAGE_PARAMS_INVALID_PAGE(values[0]));
			return null;
		}
	}
	return page;
};

/**
 * Returns the facet id set in the current option ID
 * 
 * @param parameterId
 * @returns {Array}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.getFacetIds = function(parameterId) {
	var facetIds = [];
	if (parameterId != undefined) {
		if ((facetIds = this._getInputValues(parameterId)).length == 0) {
			return null;
		}
		if (facetIds.length == 1) {
			facetIds = $.map(facetIds[0].split(','), function(e) { return $.trim(e); });
		}
	}
	return facetIds;
};

/**
 * Returns the first value for the given input name or NULL
 * 
 * @param inputName
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.getOptionValue = function(inputName) {
	if ((values = this.getOptionValues(inputName)) != null) {
		if (values[0].length > 0) {
			return values[0];
		}
	}
	return null;
};

/**
 * Returns the values for the given input names or NULL
 * 
 * @param inputName
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.getOptionValues = function(inputName) {
	if ((values = this._getInputValues(inputName)).length > 0) {
		return values;
	}
	return null;
};

/**
 * Returns the values for the given input names
 * 
 * @param inputName
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype._getInputValues = function(inputName) {
	// search for the inputs in a wider scope than our container
	var $inputs = this.parent.getParameterContainer().getParent().getEl().find('[name=' + inputName + ']');

	// returns the input values
	if ($inputs.length == 0) {
		/*//*/ console.error(_.CTX_ERROR_INVALID_PARAMETER(inputName));
	}
	return $.map($inputs, function(e) { return $(e).val(); });
};

/**
 * Returns whether or not we are in an evaluated context
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.isEvaluated = function() {
	return this.parent.getParameterInput().isEvaluatedByDefault();
};

/**
 * Returns whether this tab should be visible or not
 *
 * @returns {Boolean}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.isVisible = function() {
	return this.hasValues;
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = Mashup_Window_TabMenu_Context_Tab_Values.superclass.getEl.call(this);
		this.el.prepend('' +
			'<div class="values-filter-wrapper">' +
				'<input type="text" name="filter" class="filter-context-input" />' +
			'</div>' +
			'<div class="values-wrapper">' +
			'</div>'
		);
		this.el.find('.filter-context-input').on('keyup', { _this: this }, this.handleEventOnKeyUp);
		this.el.find('.values-wrapper').html(this._displayContext(this.getContextFunctions()));
	}
	return this.el;
};

/**
 * jQuery 'keyUp' event handler
 * 
 * @param e
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.handleEventOnKeyUp = function(e) {
	var value = $.trim($(this).val()).toLowerCase();
	e.data._this.filterKeywords = value.length == 0 ? undefined : value.split(' ');
	e.data._this.refresh();
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype.refresh = function() {
	if (this.el != undefined) {
		this.el.find('.values-wrapper').html(this._displayContext(this.getContextFunctions()));
	}
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype.getContextFunctions = function() {
	var parameterInput = this.parent.getParameterInput();
	if (parameterInput == undefined) {
		return [];
	}

	var contextFunctions = parameterInput.getContextFunctions();

	// inject special widget builder context if missing
	var hasCompositeContext = false;
	for (var i = 0; i < contextFunctions.length; i++) {
		hasCompositeContext |= contextFunctions[i].name == '_widgetComposite';
	}
	if (hasCompositeContext == false) {
		contextFunctions.push({ name: '_widgetComposite', parameters: [] });
	}

	return contextFunctions;
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype._displayContext = function(contextFunctions) {
	// Call all functions using contextOptions list
	var feedContext = {}, globalContext = {};
	var feeds = [];
	this.hasValues = false;
	for (var i = 0; i < contextFunctions.length; i++) {
		var contextFunction = contextFunctions[i];

		if (typeof(Mashup_Window_TabMenu_Context_Tab_Values.prototype[contextFunction.name]) != 'function') {
			/*//*/ console.warn(_.CTX_ERROR_INVALID_FCT(contextFunction.name));
			continue;
		}

		// context function other than injected by default
		if (contextFunction.name != '_widgetComposite' && contextFunction.name != 'DefaultValues') {
			this.hasValues = true;
		}

		var entries = Mashup_Window_TabMenu_Context_Tab_Values.prototype[contextFunction.name].apply(this, contextFunction.parameters);

		for (var j = 0; j < entries.length; j++) {
			var entry = entries[j];

			// retrieve the context to populate
			var context;
			if (entry.feedName == null || entry.feedName == '') {
				context = globalContext;
			} else {
				if (feedContext[entry.feedName] == undefined) {
					feedContext[entry.feedName] = {};
					// we want to keep order except for __context__ feed which should be first
					if (entry.feedName == '__context__') {
						feeds.unshift(entry.feedName);
					} else {
						feeds.push(entry.feedName);
					}
				}
				context = feedContext[entry.feedName];
			}

			// merge the values in the category (without duplicates)
			if (context[entry.category] == null) {
				context[entry.category] = [];
			}

			// filter the values
			var filtred = [];
			if (this.filterKeywords == undefined) {
				filtred = entry.list;
			} else {
				for (var k = 0; k < entry.list.length; k++) {
					if (this._matchFilter(entry.list[k].display.toLowerCase()) == true) {
						filtred.push(entry.list[k]);
					}
				}
			}

			if (entry.feedName == '__context__') {
				// remove duplicates
				this._mergeCategoryList(context[entry.category], filtred);
			} else {
				// optimized merge
				$.merge(context[entry.category], filtred);
			}
		}
	}

	// do not collapse if 2 or less category
	var isCollapsedByDefault = this._isCollapsedByDefault(globalContext, feedContext);

	// Draw the context menu
	var html = '';
	for (var i = 0; i < feeds.length; i++) {
		var feedName = feeds[i];

		var categoryHtml = '';
		for (var category in feedContext[feedName]) {
			if (feedContext[feedName][category].length > 0) {
				var showCount = category != _.CTX_ERROR_TITLE();
				categoryHtml += '<h4 class="window-menu-title" name="toggleList"><span class="icon icon-' + (!isCollapsedByDefault ? 'expanded' : 'right') + '" name="toggleList"></span>' + category + (showCount ? ' <span class="window-menu-title-desc" name="toggleList">(' + feedContext[feedName][category].length + ')</span>' : '') + '</h4>';
				categoryHtml += '<ul class="window-menu-list-items"' + (!isCollapsedByDefault ? '' : ' style="display:none;"') + '>' + this._displayList(feedContext[feedName][category]) + '</ul>';
			}
		}

		if (categoryHtml != '') {
			var label, color;
			if (feedName == '__context__') {
				label = _.CTX_CONTEXT_FEED();
				color = 'nocolor';
			} else {
				var feedClass = mashupBuilder.getOpenedPage().getAPI().getFeed(feedName);
				label = feedClass.feedDefinition.name + ': ' + feedName;
				color = feedClass.color;
			}
			html += '<div class="window-menu-feed-container bordercolor ' + color + '">';
			html += '<h4 class="window-menu-title bgcolor ' + color + '">' + label + '</h4>';
			html += categoryHtml;
			html += '</div>';
			html += '<div class="window-menu-list-separator"></div>';
		}
	}

	for (var category in globalContext) {
		if (globalContext[category].length > 0) {
			html += '<h4 class="window-menu-title" name="toggleList"><span class="icon icon-' + (!isCollapsedByDefault ? 'expanded' : 'right') + '" name="toggleList"></span>' + category + ' <span class="window-menu-title-desc" name="toggleList">(' + globalContext[category].length + ')</span></h4>';
			html += '<ul class="window-menu-list-items"' + (!isCollapsedByDefault ? '' : ' style="display:none;"') + '>' + this._displayList(globalContext[category]) + '</ul>',
			html += '<div class="window-menu-list-separator"></div>';
		}
	}

	if (html.length == 0) {
		html = '<p>' + _.CTX_MSG_NO_VALUES() + '</p>';
	}

	return html;
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype._matchFilter = function(value) {
	for (var i = 0; i < this.filterKeywords.length; i++) {
		if (value.indexOf(this.filterKeywords[i]) == -1) {
			return false;
		}
	}
	return true;
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype._mergeCategoryList = function(first, second) {
	var secondLength = second.length;
	for (var i = 0; i < secondLength; i++) {
		var found = false;
		for (var j = 0; j < first.length; j++) {
			if (second[i].value == first[j].value) {
				found = true;
				break;
			}
		}
		if (!found) {
			// new value: push it
			first.push(second[i]);
		}
	}
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype._isCollapsedByDefault = function(globalContext, feedContext) {
	// do not collapse if has filter
	if (this.filterKeywords != undefined) {
		return false;
	}

	var categoryCount = 0;

	for (var feedName in feedContext) {
		for (var category in feedContext[feedName]) {
			if (feedContext[feedName][category].length > 0) {
				if (++categoryCount > 2) {
					return true;
				}
			}
		}
	}

	for (var category in globalContext) {
		if (globalContext[category].length > 0) 
			if (++categoryCount > 2) {
				return true;
			}
	}

	return false;
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype._displayList = function(data) {
	data.sort(this.sortByDisplay);
	var displayedErrors = [];
	var html = '';
	for (var i = 0; i < data.length; i++) {
		var value = (data[i].value || '').escapeHTML(),
			display = data[i].display.escapeHTML(),
			title = data[i].title != undefined ? data[i].title.escapeHTML() : display,
			type = data[i].type;

		if (data[i].error != undefined) {
			if (displayedErrors.indexOf(data[i].display) == -1) {
				html += '<li class="window-menu-list-item error">' + data[i].display + '</li>';
				displayedErrors.push(data[i].display);
			}
		} else if (type != undefined) {
			html += '' +
				'<li class="window-menu-list-item" name="doFillInput" valueOnClick="' + value + '">' +
					'<div class="window-menu-list-item-wrapper">' +
						'<span name="doFillInput" class="type" title="' + type + '">' + type + '</span>' +
						'<span name="doFillInput" class="display" title="' + title + '">' + display + '</span>' +
					'</div>' +
				'</li>';
		} else {
			html += '' +
			'<li class="window-menu-list-item" name="doFillInput" valueOnClick="' + value + '">' +
				'<span name="doFillInput" class="display" title="' + title + '">' + display + '</span>' +
			'</li>';
		}
	}
	return html;
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype.sortByCategory = function(objA, objB) {
	var categoryA = objA.category.toLowerCase(), categoryB = objB.category.toLowerCase();
	if (categoryA < categoryB) {
		return -1;
	} else if (categoryA > categoryB) {
		return 1;
	} else {
		return 0;
	}
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype.sortByDisplay = function(objA, objB) {
	var displayNameA = objA.display.toLowerCase(), displayNameB = objB.display.toLowerCase();
	if (displayNameA < displayNameB) {
		return -1;
	} else if (displayNameA > displayNameB) {
		return 1;
	} else {
		return 0;
	}
};


/**
 * File: /resources/mashupBuilder/js/Window/TabMenu/Context/Tab/Values/Impl.js
 */
/**
 * Empty the active input after a click in the context menu (behavior change)
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.emptyOnChange = function() {
	this.parent.onChange.empty = true;
	return [];
};

/**
 * Appends into the active input after a click in the context menu (behavior change)
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.appendOnChange = function(str) {
	this.parent.onChange.append = str;
	return [];
};

/**
 * Appends all the available pages names
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Pages = function() {
	return [{
		feedName: null,
		category: _.CTX_PAGES_TITLE(),
		list: ContextHelper.getPageNames()
	}];
};

/**
 * Appends all the available themes
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Themes = function() {
	return [{
		feedName: null,
		category: _.CTX_THEMES_TITLE(),
		list: ContextHelper.getThemes()
	}];
};

/**
 * Appends all the available layouts
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Layouts = function() {
	return [{
		feedName: null,
		category: _.CTX_LAYOUTS_TITLE(),
		list: ContextHelper.getLayouts()
	}];
};

/**
 * Appends a custom context
 *
 * @param name
 * @param values list ["a", "b"] or [{display:"A",value:"a_"},{display:"B",value:"b_"}]
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.addContext = function(name, values) {
	return [{
		feedName: null,
		category: name,
		list: ContextHelper.getCustomContext(values)
	}];
};

/**
 * Appends the page parameters
 *
 * @param parameterId optional parameter Id that contains the page name to retrieve parameters from
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.PageParameters = function(parameterId) {
	var ret = {feedName: null, category: _.CTX_PAGE_PARAMS_TITLE(), list: [] };
	if (this.getFeed() != null) {
		ret.list = ContextHelper.getFeedPageParameters(this.getPage(parameterId), this.isEvaluated());
	} else {
		ret.list = ContextHelper.getDesignPageParameters(this.getPage(parameterId), this.isEvaluated());
	}
	return [ret];
};

/**
 * Appends the available feeds
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Feeds = function() {
	return [{
		feedName: null,
		category: _.CTX_FEEDS_TITLE(),
		list: ContextHelper.getFeeds(this.getPage(), this.isEvaluated())
	}];
};

/**
 * Appends the feeds variables
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.FeedVariables = function() {
	var ret = [];
	var feeds = this.getFeeds();
	for (var i = 0; i < feeds.length; i++) {
		if (window.widgetBuilder == null) {
			// Feed Attributes
			ret.push({
				feedName: feeds[i].getId(),
				category: _.CTX_FEED_VARS_TITLE(),
				list: ContextHelper.getFeedMELContext('ui', feeds[i].getId())
			});
		}

		// Feed Attributes (contextual)
		ret.push({
			feedName: '__context__',
			category: _.CTX_FEED_VARS_TITLE(),
			list: ContextHelper.getFeedMELContext('ui')
		});
	}
	return ret;
};

/**
 * Appends the entries variables
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.EntryVariables = function() {
	var ret = [];
	var feeds = this.getFeeds();
	for (var i = 0; i < feeds.length; i++) {
		if (window.widgetBuilder == null) {
			// Hit Attributes
			ret.push({
				feedName: feeds[i].getId(),
				category: _.CTX_HIT_VARS_TITLE(),
				list: ContextHelper.getEntryMELContext('ui', feeds[i].getId())
			});
		}

		// Hit Attributes (contextual)
		ret.push({
			feedName: '__context__',
			category: _.CTX_HIT_VARS_TITLE(),
			list: ContextHelper.getEntryMELContext('ui')
		});
	}
	return ret;
};

/**
 * Appends the page variable (alias to the first feed of the page)
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.PageVariables = function() {
	if (((page = this.getPage()) != null) && page.hasAPI() && page.getAPI().hasFeeds()) {
		return [{
			feedName: null,
			category: _.CTX_PAGEFEED_INFOS_TITLE(),
			list: ContextHelper.getFeedMELContext('ui')
		}];
	}
	return [];
};

/**
 * Appends the available widgets WUIDS
 *
 * @param eventType The widgets must implement the given event
 *
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WUIDS = function(eventType) {
	return [{
		feedName: null,
		category: _.CTX_WUIDS_TITLE(),
		list: eventType == undefined ? ContextHelper.getWuids(this.getPage()) : ContextHelper.getWuidsForEventType(this.getPage(), eventType)
	}];
};

/**
 * Appends the available metas
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Metas = function() {
	if ((feed = this.getFeed()) != null) {
		var _this = this;
		function getParentFeedMetas(feed) {
			var ret = [];
			if ((parentFeed = feed.getParentFeedContainer().getParentFeed()) != null) {
				if (parentFeed != null && parentFeed.getId().length > 0) {
					$.merge(ret, _this._buildMetasForFeed(parentFeed));
				}
				$.merge(ret, getParentFeedMetas(parentFeed)); // recursive
			}
			return ret;
		}
		return getParentFeedMetas(feed);
	}
	return this.SelfMetas();
};

/**
 * Appends the available self metas
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.SelfMetas = function() {
	var ret = [];
	var feeds = this.getFeeds();
	for (var i = 0; i < feeds.length; i++) {
		$.merge(ret, this._buildMetasForFeed(feeds[i]));
	}
	return ret;
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype.SubMetas = function() {
	var ret = [];
	if ((feed = this.getFeed()) != null) {
		// special case for "PRE" input of properties mapping, no access to submetas
		// as it is executed BEFORE the query
		var container = this.parent.getParameterContainer();
		if (container != null && container.id == 'properties-mapping') {
			var parameterValue = this.parent.getParameterValue();
			if (parameterValue != null && parameterValue.getInput(0).val() == 'PRE') {
				return [];
			}
		}
		var subFeeds = feed.getFeedContainer().getFeeds();
		for (var i = 0; i < subFeeds.length; i++) {
			$.merge(ret, this._buildMetasForFeed(subFeeds[i]));
		}
	}
	return ret;
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype._buildMetasForFeed = function(feed) {
	var feedContext = ContextHelper.getFeedContext(feed.getId());
	if (feedContext == null) {
		return [];
	}

	if (feedContext.error != undefined && feedContext.error == true) {
		return [{feedName: feed.getId(), category: _.CTX_ERROR_TITLE(), list: [{ error: true, display: feedContext.message }]}];
	}

	var ret = [];
	for (var type in feedContext) {
		if (window.widgetBuilder == null) {
			// Hit Infos
			ret.push({
				feedName: feed.getId(),
				category: _.CTX_HIT_INFOS_TITLE(type),
				list: ContextHelper.getEntryInfos(feed, feedContext[type], this.isEvaluated())
			});
		}

		// Hit Infos (contextual)
		ret.push({
			feedName: '__context__',
			category: _.CTX_HIT_INFOS_TITLE(),
			list: ContextHelper.getEntryInfos(null, feedContext[type], this.isEvaluated())
		});

		if (window.widgetBuilder == null) {
			// Hit Metas
			ret.push({
				feedName: feed.getId(),
				category: _.CTX_HIT_METAS_TITLE(type),
				list: ContextHelper.getMetas(feed, feedContext[type], this.isEvaluated())
			});
		}

		// Hit Metas (contextual)
		ret.push({
			feedName: '__context__',
			category: _.CTX_HIT_METAS_TITLE(),
			list: ContextHelper.getMetas(null, feedContext[type], this.isEvaluated())
		});

		if (window.widgetBuilder == null) {
			// Feed Infos
			ret.push({
				feedName: feed.getId(),
				category: _.CTX_FEED_INFOS_TITLE(type),
				list: ContextHelper.getFeedInfos(feed, feedContext[type], this.isEvaluated())
			});
		}

		// Feed Infos (contextual)
		ret.push({
			feedName: '__context__',
			category: _.CTX_FEED_INFOS_TITLE(),
			list: ContextHelper.getFeedInfos(null, feedContext[type], this.isEvaluated())
		});

		if (window.widgetBuilder == null) {
			// Feed Facets
			ret.push({
				feedName: feed.getId(),
				category: _.CTX_FACETS_TITLE(type),
				list: ContextHelper.getFacets(feed.getId(), feedContext[type], this.isEvaluated())
			});
		}

		// Feed Facets (contextual)
		ret.push({
			feedName: '__context__',
			category: _.CTX_FACETS_TITLE(),
			list: ContextHelper.getFacets(null, feedContext[type], this.isEvaluated())
		});
	}
	ret.sort(this.sortByCategory);

	return ret;
};

/**
 * Appends the available suggest services
 * 
 * @param typeOptionId option which contains the type (suggest/dispatcher)
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.SuggestServices = function(typeOptionId) {
	var type = undefined;
	if (typeOptionId != undefined) {
		type = this.getOptionValue(typeOptionId);
	}

	return [{
		feedName: null,
		category: _.CTX_SUGGEST_TITLE(),
		list: ContextHelper.getSuggestServices(false, type)
	}];
};

/**
 * Appends the available suggest services
 * 
 * @param typeOptionId option which contains the type (suggest/dispatcher)
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.SuggestNames = function(typeOptionId) {
	var type = undefined;
	if (typeOptionId != undefined) {
		type = this.getOptionValue(typeOptionId);
	}

	return [{
		feedName: null,
		category: _.CTX_SUGGEST_TITLE(),
		list: ContextHelper.getSuggestServices(true, type)
	}];
};

/**
 * Appends the available data model classes
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.DataModelClass = function() {
	var ret = [];
	var feeds = this.getFeeds();
	for (var i = 0; i < feeds.length; i++) {
		var feedContext = ContextHelper.getFeedContext(feeds[i].getId());
		if (feedContext == null) {
			continue;
		}

		if (feedContext.error != undefined && feedContext.error == true) {
			return [{feedName: feeds[i].getId(), category: _.CTX_ERROR_TITLE(), list: [{ error: true, display: feedContext.message }]}];
		}

		// only works in "all" mode
		if (feedContext.hasOwnProperty('all') == false || feedContext.all.dataModelClasses.length == 0) {
			continue;
		}

		var values = ContextHelper.getDataModelClass(feedContext.all);

		ret.push({
			feedName: feeds[i].getId(),
			category: _.CTX_DATAMODELCLASS_TITLE(),
			list: values
		});

		ret.push({
			feedName: '__context__',
			category: _.CTX_DATAMODELCLASS_TITLE(),
			list: values
		});
	}
	return ret;
};

/**
 * Appends the available query prefixes
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.QueryPrefixes = function() {
	var ret = [];
	var feeds = this.getFeeds();
	for (var i = 0; i < feeds.length; i++) {
		var feedContext = ContextHelper.getFeedContext(feeds[i].getId());
		if (feedContext == null) {
			continue;
		}

		if (feedContext.error != undefined && feedContext.error == true) {
			return [{feedName: feeds[i].getId(), category: _.CTX_ERROR_TITLE(), list: [{ error: true, display: feedContext.message }]}];
		}

		for (var type in feedContext) {
			var queryPrefixes = ContextHelper.getQueryPrefixes(feedContext[type]);

			if (window.widgetBuilder == null) {
				ret.push({
					feedName: feeds[i].getId(),
					category: _.CTX_QUERY_PREFIXES_TITLE(type),
					list: queryPrefixes
				});
			}

			ret.push({
				feedName: '__context__',
				category: _.CTX_QUERY_PREFIXES_TITLE(),
				list: queryPrefixes
			});
		}

		ret.sort(this.sortByCategory);
	}
	return ret;
};

/**
 * Appends the available API config
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.ApiConfig = function() {
	return [{
		feedName: null,
		category: _.CTX_APICONFIG_TITLE(),
		list: ContextHelper.getApiConfig()
	}];
};

/**
 * Appends the available API commands
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.ApiCommand = function() {
	return [{
		feedName: null,
		category: _.CTX_APICOMMAND_TITLE(),
		list: ContextHelper.getApiCommand()
	}];
};

/**
 * Appends the available reporters
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Reporters = function() {
	return [{
		feedName: null,
		category: _.CTX_REPORTERS_TITLE(),
		list: ContextHelper.getReporters()
	}];
};

/**
 * Appends the available search targets
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.SearchTargets = function() {
	return [{
		feedName: null,
		category: _.CTX_SEARCHTARGET_TITLE(),
		list: ContextHelper.getSearchTargets()
	}];
};

/**
 * Appends the available search logics
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.SearchLogics = function() {
	return [{
		feedName: null,
		category: _.CTX_SEARCHLOGIC_TITLE(),
		list: ContextHelper.getSearchLogics()
	}];
};

/**
 * Appends the available security sources
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.SecuritySources = function() {
	return [{
		feedName: null,
		category: _.CTX_SECURITY_SOURCE_TITLE(),
		list: ContextHelper.getSecuritySources()
	}];
};

/**
 * Appends the cookies related values
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Cookies = function() {
	if (this.getFeed() == null) {
		return [{
			feedName: null,
			category: _.CTX_COOKIES_TITLE(),
			list: ContextHelper.getMELContext('ui', 'cookies')
		}];
	}
	return [];
};

/**
 * Appends the request related values
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Request = function() {
	if (this.getFeed() == null) {
		return [{
			feedName: null,
			category: _.CTX_REQUEST_TITLE(),
			list: ContextHelper.getMELContext('ui', 'request')
		}];
	}
	return [];
};

/**
 * Appends the session related values
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Session = function() {
	if (this.getFeed() == null) {
		return [{
			feedName: null,
			category: _.CTX_SESSION_TITLE(),
			list: ContextHelper.getMELContext('ui', 'session')
		}];
	}
	return [];
};

/**
 * Appends the security related values
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Security = function() {
	if (this.getFeed() == null && window.mashupBuilder.security.hasSecurityProvider()) {
		return [{
			feedName: null,
			category: _.CTX_SECURITY_TITLE(),
			list: ContextHelper.getMELContext('ui', 'security')
		}];
	}
	return [];
};

/**
 * Appends the I18N values
 *
 * @returns {Array}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.I18N = function() {
	return [{
		feedName: null,
		category: _.CTX_I18N_TITLE(),
		list: [{
			display: 'code',
			value: '${i18n[\'__code__\']}',
			title: '${i18n[\'__code__\']}'
		}]
	}];
};

/**
 * Appends the exported I18N JS values
 *
 * @returns {Array}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.JsKeys = function() {
	if ((widget = this.getWidget()) != null) {
		var definition = widget.getWidgetDefinition();
		if (definition.supportI18N.jsKeys != undefined) {
			var values = [];
			var keys = definition.supportI18N.jsKeys;
			for (var i = 0; i < keys.length; i++) {
				values.push({
					display: keys[i],
					value: 'mashupI18N.get(\'' + widget.getId() + '\', \'' + keys[i] + '\')',
					title: 'mashupI18N.get(\'' + widget.getId() + '\', \'' + keys[i] + '\')'
				});
			}
			return [{
				feedName: null,
				category: _.CTX_JSKEY_TITLE(),
				list: values
			}];
		}
	}
	return [];
};

/**
 * Appends the available attributes for a meta evaluation
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.EvalMeta = function() {
	return [{
		feedName: null,
		category: _.CTX_META_TITLE(),
		list: ContextHelper.getMELContext('ui', 'meta')
	}];
};

/**
 * Appends the available attributes for a category evaluation
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.EvalCategory = function() {
	return [{
		feedName: null,
		category: _.CTX_CATEGORY_TITLE(),
		list: ContextHelper.getMELContext('ui', 'category')
	}];
};

/**
 * Appends the available attributes for a facet evaluation
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.EvalFacet = function() {
	return [{
		feedName: null,
		category: _.CTX_FACET_TITLE(),
		list: ContextHelper.getMELContext('ui', 'facet')
	}];
};

/**
 * Appends the available aggregations
 *
 * @param facetOptionId optional option ID which contains a facet ids
 * @returns {Array}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Aggregations = function(facetOptionId) {
	var ret = [];
	var facetIds = this.getFacetIds(facetOptionId);
	var feeds = this.getFeeds();
	for (var i = 0; i < feeds.length; i++) {
		var feedContext = ContextHelper.getFeedContext(feeds[i].getId());
		if (feedContext == null) {
			continue;
		}

		if (feedContext.error != undefined && feedContext.error == true) {
			return [{feedName: feeds[i].getId(), category: _.CTX_ERROR_TITLE(), list: [{ error: true, display: feedContext.message }]}];
		}

		for (var type in feedContext) {
			var aggr = ContextHelper.getAggregations(facetIds, feedContext[type]);

			if (window.widgetBuilder == null) {
				ret.push({
					feedName: feeds[i].getId(),
					category: _.CTX_AGGREGATIONS_TITLE(type),
					list: aggr
				});
			}

			ret.push({
				feedName: '__context__',
				category: _.CTX_AGGREGATIONS_TITLE(),
				list: aggr
			});
		}

		ret.sort(this.sortByCategory);
	}
	return ret;
};

/**
 * Appends the available facets of the given type/policy for the feed(s) set in the given option id
 * 
 * @param feedOptionId
 * @param type
 * @param refinementPolicy
 * @returns {Array}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.FacetsForFeed = function(feedOptionId, facetType, refinementPolicy) {
	var ret = [];
	var feeds = this.getFeeds(feedOptionId);
	for (var i = 0; i < feeds.length; i++) {
		$.merge(ret, this._buildFacetsForFeed(feeds[i], facetType, refinementPolicy));
	}
	ret.sort(this.sortByCategory);
	return ret;
};

/**
 * Appends the available simple facets (no multidim, date, etc..)
 *
 * @param refinementPolicy
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.NormalFacets = function(refinementPolicy) {
	return this.Facets('NORMAL', refinementPolicy);
};

/**
 * Appends the available geo facets
 *
 * @param refinementPolicy
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.GeoFacets = function(refinementPolicy) {
	return this.Facets(['GEO', 'AUTOTILE'], refinementPolicy);
};

/**
 * Appends the available 2D-MultiDimension facets
 *
 * @param refinementPolicy
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.MultiDimension2DFacets = function(refinementPolicy) {
	return this.Facets('MULTI2D', refinementPolicy);
};

/**
 * Appends the available hierarchical 2D facets
 *
 * @param refinementPolicy
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Hierarchical2DFacets = function(refinementPolicy) {
	return this.Facets('H2D', refinementPolicy);
};

/**
 * Appends the available MultiDimension facets
 *
 * @param refinementPolicy
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.MultiDimensionFacets = function(refinementPolicy) {
	return this.Facets('MULTI', refinementPolicy);
};

/**
 * Appends the available dates facets
 *
 * @param refinementPolicy
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.DateFacets = function(refinementPolicy) {
	return this.Facets(['DATE', 'DYNDATE'], refinementPolicy);
};

/**
 * Appends the available numerical facets
 *
 * @param refinementPolicy
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.NumericalFacets = function(refinementPolicy) {
	return this.Facets(['NUM_DYNAMIC', 'NUM_FIXED', 'NUM_EXPLICIT'], refinementPolicy);
};

/**
 * Appends the available facets of the given type/policy
 *
 * @param type
 * @param refinementPolicy
 * @returns {Array}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Facets = function(facetType, refinementPolicy) {
	var ret = [];
	var feeds = this.getFeeds();
	for (var i = 0; i < feeds.length; i++) {
		$.merge(ret, this._buildFacetsForFeed(feeds[i], facetType, refinementPolicy));
	}
	ret.sort(this.sortByCategory);
	return ret;
};

Mashup_Window_TabMenu_Context_Tab_Values.prototype._buildFacetsForFeed = function(feed, facetType, refinementPolicy) {
	var feedContext = ContextHelper.getFeedContext(feed.getId());
	if (feedContext == null) {
		return [];
	}

	if (feedContext.error != undefined && feedContext.error == true) {
		return [{feedName: feed.getId(), category: _.CTX_ERROR_TITLE(), list: [{ error: true, display: feedContext.message }]}];
	}

	var ret = [];
	for (var type in feedContext) {
		var facets = ContextHelper.getFacets(feed.getId(), feedContext[type], false, facetType, refinementPolicy);

		if (window.widgetBuilder == null) {
			ret.push({
				feedName: feed.getId(),
				category:_.CTX_FACETS_NAME_TITLE(type),
				list: facets
			});
		}

		ret.push({
			feedName: '__context__',
			category:_.CTX_FACETS_NAME_TITLE(),
			list: facets
		});
	}
	return ret;
};

/**
 * Appends the available fields
 *
 * @returns {Array}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Fields = function() {
	var ret = [];
	var feeds = this.getFeeds();
	for (var i = 0; i < feeds.length; i++) {
		var feedContext = ContextHelper.getFeedContext(feeds[i].getId());
		if (feedContext == null) {
			continue;
		}

		if (feedContext.error != undefined && feedContext.error == true) {
			return [{feedName: feeds[i].getId(), category: _.CTX_ERROR_TITLE(), list: [{ error: true, display: feedContext.message }]}];
		}

		for (var type in feedContext) {
			var fields = ContextHelper.getFields(feedContext[type]);

			if (window.widgetBuilder == null) {
				ret.push({
					feedName: feeds[i].getId(),
					category: _.CTX_FIELDS_TITLE(type),
					list: fields
				});
			}

			ret.push({
				feedName: '__context__',
				category: _.CTX_FIELDS_TITLE(),
				list: fields
			});
		}

		ret.sort(this.sortByCategory);
	}
	return ret;
};

/**
 * Appends the available sorts
 *
 * @returns {Array}
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Sorts = function() {
	var ret = [];
	var feeds = this.getFeeds();
	for (var i = 0; i < feeds.length; i++) {
		var feedContext = ContextHelper.getFeedContext(feeds[i].getId());
		if (feedContext == null) {
			continue;
		}

		if (feedContext.error != undefined && feedContext.error == true) {
			return [{feedName: feeds[i].getId(), category: _.CTX_ERROR_TITLE(), list: [{ error: true, display: feedContext.message }]}];
		}

		for (var type in feedContext) {
			var sorts = ContextHelper.getSorts(feedContext[type]);

			if (window.widgetBuilder == null) {
				ret.push({
					feedName: feeds[i].getId(),
					category: _.CTX_SORTS_TITLE(type),
					list: sorts
				});
			}

			ret.push({
				feedName: '__context__',
				category: _.CTX_SORTS_TITLE(),
				list: sorts
			});
		}

		ret.sort(this.sortByCategory);
	}
	return ret;
};

/**
 * Appends all the available context for evaluated parameters
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.Eval = function() {
	var isFeedContext = this.getFeed() != null || this.getFeedTrigger() != null;

	var ret = [];

	if (!isFeedContext) {
		$.merge(ret, this.FeedVariables());
		$.merge(ret, this.EntryVariables());
	}

	$.merge(ret, this.Metas());
	$.merge(ret, this.PageParameters());

	if (!isFeedContext) {
		$.merge(ret, this.Request());
		$.merge(ret, this.Session());
		$.merge(ret, this.Security());
		$.merge(ret, this.Cookies());
		$.merge(ret, this.I18N());
	}

	ret.sort(this.sortByCategory);

	return ret;
};

/**
 * Appends the default value of the given option
 *
 * @param optionId
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.DefaultValues = function(optionId) {
	var definition = null;
	if ((parentClass = this.getTrigger()) != null) {
		definition = parentClass.getTriggerDefinition();
	} else if ((parentClass = this.getWidget()) != null) {
		definition = parentClass.getWidgetDefinition();
	} else if ((parentClass = this.getFeed()) != null) {
		definition = parentClass.getFeedDefinition();
	}

	if (definition == null) {
		return [];
	}

	var ret = [];

	var dcs = getConfigurationSets(definition);
	for (var i = 0, dcsLength = dcs.length; i < dcsLength; i++) {
		var dc = dcs[i];

		var list = [];
		for (var j = 0, defaultValuesLength = dc.defaultValues.length; j < defaultValuesLength; j++) {
			var defaultValue = dc.defaultValues[j];
			if (defaultValue.name == optionId) {
				if (defaultValue.value.length > 0) {
					var display = defaultValue.value;
					if (display.length > 100) {
						display = display.substr(0, 100) + ' ...';
					}
					list.push({ 'display': display, 'title': defaultValue.value, 'value': defaultValue.value });
				}
			}
		}

		if (list.length > 0) {
			ret.push({ feedName: null, category: _.CTX_DEFAULTSVALUES_TITLE(dc.displayName), list: list });
		}
	}

	return ret;
};

/**
 * Appends the options of the parent component
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype.ParentOptions = function() {
	var triggerClass = this.getTrigger();
	if (triggerClass == null) {
		return [];
	}

	var ret = [];
	var parent = triggerClass.getParent();
	if (isWidget(parent)) {
		var optionsGroup = parent.getWidgetDefinition().optionsGroup;
		for (var i = 0; i < optionsGroup.length; i++) {
			var category = { feedName: '', category: optionsGroup[i].name, list: [] };
			var options = optionsGroup[i].options;
			for (var j = 0; j < options.length; j++) {
				category.list.push({
					display:  options[j].name,
					value: options[j].id
				});
			}
			ret.push(category);
		}
	}
	return ret;
};

/**
 * Appends widget composite options
 *
 * @private
 */
Mashup_Window_TabMenu_Context_Tab_Values.prototype._widgetComposite = function() {
	var arr = [];

	// only appends for widget or triggers in widget builder context
	if (window.widgetBuilder != null && (
		window.mashupBuilder.getOpenedBottomToolbar() == null ||
		window.mashupBuilder.getOpenedBottomToolbar().getContent().getParent().constructor != Mashup_Page_UI
	)) {
		var nbGroups = window.widgetBuilder.jsonWidgetDefinition.optionsGroup.length;
		for (var i = 0; i < nbGroups; i++) {
			var group = window.widgetBuilder.jsonWidgetDefinition.optionsGroup[i];
			var ret = {
				feedName: null,
				category: _.CTX_COMPOSITE_TITLE(group.name),
				list: []
			};

			var nbOptions = group.options.length;
			for (var j = 0; j < nbOptions; j++) {
				if (group.options[j].id.length > 0) {
					ret.list.push({
						'display': '%{option.' + group.options[j].id + '}',
						'value': '%{option.' + group.options[j].id + '}'
					});
				}
			}
			arr.push(ret);
		}

	}
	return arr;
};

/*
 * DEPRECATED FUNCTIONS
 */

Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getAvailablePagesName = function() { return this.Pages(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_add = function(categoryName, list) { return this.addContext(categoryName, list); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getPageParameters = function(a, b, c, parameterId) { return this.PageParameters(parameterId); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getSessionAttributes = function() { return this.Session(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getSecurityAttributes = function() { return this.Security(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_emptyOnChange = function() { return this.emptyOnChange(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_appendOnChange = function(str) { return this.appendOnChange(str); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getSearchLogics = function() { return this.SearchLogics(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getSearchTargets = function() { return this.SearchTargets(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getSecuritySources = function() { return this.SecuritySources(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getSuggestServices = function() { return this.SuggestServices(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getSuggestNames = function() { return this.SuggestNames(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getAPIConfig = function() { return this.ApiConfig(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getAPICommand = function() { return this.ApiCommand(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getDefaultValues = function(optionId) { return this.DefaultValues(optionId); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.COMMON_getI18N = function() { return this.I18N(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getWUIDS = function() { return this.WUIDS(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getWUIDSforEventType = function(eventType) { return this.WUIDS(eventType); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getFeeds = function() { return this.Feeds(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getPossibleSorts = function() { return this.Sorts(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getDataModelClass = function() { return this.DataModelClass(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getQueryPrefixes = function() { return this.QueryPrefixes(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getPossibleFields = function() { return this.Fields(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getFirstCurrentFeedMetas = function() { return this.Metas(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getCurrentFeedMetas = function() { return this.SelfMetas(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getCurrentFeedFacets = function(type, refinementPolicy) { return this.Facets(type, refinementPolicy); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getCurrentFeedInfos = function() { return this.FeedVariables(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getFirstFeedInfos = function() { return this.PageVariables(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_getAggregations = function(optionId) { return this.Aggregations(optionId); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_EvalFacet = function() { return this.EvalFacet(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.WIDGET_EvalCategory = function() { return this.EvalCategory(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.FEED_getQueryPrefixes = function() { return this.QueryPrefixes(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.FEED_getPossibleSorts = function() { return this.Sorts(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.FEED_getPossibleFields = function() { return this.Fields(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.FEED_getCurrentFeedMetas = function() { return this.SelfMetas(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.FEED_getCurrentFeedFacets = function(type, refinementPolicy) { return this.Facets(type, refinementPolicy); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.FEED_getParentFeedMetas = function() { return this.Metas(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.FEED_getDataModelClass = function() { return this.DataModelClass(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.FEED_getSubFeedMetas = function(startsWith, endsWith, includeFeedName) { return this.SubMetas(); };
Mashup_Window_TabMenu_Context_Tab_Values.prototype.FacetsId = function(type, refinementPolicy) { return this.Facets(type, refinementPolicy); };


/**
 * File: /resources/mashupBuilder/js/Window/TabMenu/Context/Tab/MELFunctions.js
 */
Inherit(Mashup_Window_TabMenu_Context_Tab_MELFunctions, Mashup_Window_TabMenu_Tab);
function Mashup_Window_TabMenu_Context_Tab_MELFunctions(menu, id, name) {
	Mashup_Window_TabMenu_Context_Tab_MELFunctions.superclass.constructor.call(this, menu, id, name);
	this.openedByDefault = [];
}

/**
 * Returns whether this tab is visible or not
 *
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype.isVisible = function() {
	var parameterInput = this.getMenu().getParameterInput();
	if (parameterInput == null || !parameterInput.isEvaluatedByDefault()) {
		return false;
	}
	return true;
};

Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype.getEl = function() {
	if (this.el == undefined) {
		this.el = Mashup_Window_TabMenu_Context_Tab_MELFunctions.superclass.getEl.call(this);
		this.el.prepend('' +
			'<div class="values-filter-wrapper">' +
				'<input type="text" name="filter" class="filter-context-input" />' +
			'</div>' +
			'<div class="values-wrapper">' +
			'</div>'
		);
		this.el.find('.filter-context-input').on('keyup', { _this: this }, this.handleEventOnKeyUp);
		this.el.find('.values-wrapper').html(this._displayMEL());
	}
	return this.el;
};

/**
 * jQuery 'keyUp' event handler
 * 
 * @param e
 */
Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype.handleEventOnKeyUp = function(e) {
	var value = $.trim($(this).val()).toLowerCase();
	e.data._this.filterKeywords = value.length == 0 ? undefined : value.split(' ');
	e.data._this.refresh();
};

Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype.refresh = function() {
	if (this.el != undefined) {
		var $icons = this.el.find('.icon-expanded'), length = $icons.length;
		for (var i = 0; i < length; i++) {
			this.openedByDefault.push($($icons[i]).parent().text());
		}
		this.el.find('.values-wrapper').html(this._displayMEL());
		this.openedByDefault = [];
	}
};

/**
 * Try to refresh automatically until the context is properly loaded
 *
 * @param ms
 */
Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype.tryRefreshEvery = function(ms) {
	var _this = this;
	var timer = null;
	timer = setInterval(function() {
		if (window.mashupBuilder.context.get('melFunctions') != null) {
			clearInterval(timer);
			_this.refresh();
		}
	}, ms);
};

Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype._displayMEL = function() {
	var sb = new StringBuilder();

	var melIntrospection = window.mashupBuilder.context.get('melIntrospection');
	if (melIntrospection != null) {
		if (melIntrospection.error != undefined && melIntrospection.error == true) {
			sb.append('<h4 class="window-menu-title">' + _.CTX_MEL_TITLE() + '</h4>');
			sb.append('<ul class="window-menu-list-items">');
			sb.append('<li class="window-menu-list-item error">' + melIntrospection.message + '</li>');
			sb.append('</ul>');
		} else {

			// retrieve all namespaces and map functions by namespaces
			var namespaces = [];
			var functionsByNamespace = {};
			melFunctions = (ContextHelper.findMELResolver(melIntrospection, this.getMELType()) || {}).MELFunctions;
			if (melFunctions != undefined) {
				// filter the values
				for (var i = 0; i < melFunctions.length; i++) {
					var melFunction = melFunctions[i];
					if (!this._matchFilter(melFunction)) {
						continue;
					}
					if (namespaces.indexOf(melFunction.namespace) == -1) {
						namespaces.push(melFunction.namespace);
						functionsByNamespace[melFunction.namespace] = [];
					}
					functionsByNamespace[melFunction.namespace].push(melFunction);
				}
			}

			namespaces.sort();

			// foreach namespaces
			for (var i = 0; i < namespaces.length; i++) {
				var isCollapsedByDefault = this._isCollapsedByDefault(namespaces[i]);

				sb.append('<h4 class="window-menu-title" name="toggleList"><span class="icon icon-' + (!isCollapsedByDefault ? 'expanded' : 'right') + '" name="toggleList"></span>' + _.CTX_MEL_NAMESPACE_TITLE(namespaces[i]) + '</h4>');
				sb.append('<ul class="window-menu-list-items"' + (!isCollapsedByDefault ? '' : ' style="display:none;"') + '>');
				var melFunctions = functionsByNamespace[namespaces[i]];
				for (var j = 0; j < melFunctions.length; j++) {
					var melFunction = melFunctions[j];
					var verbosePrototype = this._getVerboseMELPrototype(melFunction, true),
						verbosePrototypeTitle = this._getVerboseMELPrototype(melFunction, false);
					var prototype = this._getMELPrototype(melFunction);
					sb.append('<li class="window-menu-list-item" name="doFillInput" valueOnClick="' + prototype + '">');
					sb.append('<span class="icon icon-help"><div class="description">' + melFunction.description + '</div></span>');
					sb.append('<span name="doFillInput" class="display" title="' + verbosePrototypeTitle + '">' + verbosePrototype + '</span>');
					sb.append('</li>');
				}
				sb.append('</ul>');
			}
		}
	} else {
		sb.append('<h4 class="window-menu-title">' + _.CTX_MEL_TITLE() + '</h4>');
		sb.append('<ul class="window-menu-list-items">');
		sb.append('<li>' + _.CTX_MEL_ERROR_UNINITIALIZED() + '<li>');
		sb.append('</ul>');
		this.tryRefreshEvery(500);
	}

	return sb.toString();
};

Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype._isCollapsedByDefault = function(namespace) {
	// do not collapse if has filter
	if (this.filterKeywords != undefined) {
		return false;
	}

	return this.openedByDefault.indexOf(namespace) == -1;
};

Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype._matchFilter = function(melFunction) {
	if (this.filterKeywords != undefined) {
		var tmp = [melFunction.name];
		for (var i = 0; i < melFunction.parameters.length; i++) {
			tmp.push(melFunction.parameters[i].name);
		}
		var value = tmp.join(' ').toLowerCase();
		for (var i = 0; i < this.filterKeywords.length; i++) {
			if (value.indexOf(this.filterKeywords[i]) == -1) {
				return false;
			}
		}
	}
	return true;
};

/**
 * Returns the prototype of the given MEL function definition
 *
 * @param melFunction
 */
Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype._getMELPrototype = function(melFunction) {
	var parameters = [];
	var parametersLength = melFunction.parameters.length;
	for (var p = 0; p < parametersLength; p++) {
		parameters.push(melFunction.parameters[p].name + (p == parametersLength -1 && melFunction.varArgs == true ? '...' : ''));
	}
	return '${' + melFunction.namespace + ':' + melFunction.name + '(' + parameters.join(', ') + ')}';
};

Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype._getVerboseMELPrototype = function(melFunction, withMarkup) {
	if (withMarkup) {
		var parameters = [];
		var parametersLength = melFunction.parameters.length;
		for (var p = 0; p < parametersLength; p++) {
			parameters.push(
				'<span class="mel-type" name="doFillInput">' +
					melFunction.parameters[p].type +
					(p == parametersLength - 1 && melFunction.varArgs == true ? '... ' : ' ') +
				'</span>' +
				'<span class="mel-parameter" name="doFillInput">' +
					melFunction.parameters[p].name +
				'</span>'
			);
		}
		return '<span class="mel-function" name="doFillInput">' + melFunction.name +  '</span>' + '<span class="mel-separator" name="doFillInput">(</span>' + parameters.join('<span class="mel-separator" name="doFillInput">, </span>') + '<span class="mel-separator" name="doFillInput">)</span>';
	} else {
		var parameters = [];
		var parametersLength = melFunction.parameters.length;
		for (var p = 0; p < parametersLength; p++) {
			parameters.push(melFunction.parameters[p].type + (p == parametersLength -1 && melFunction.varArgs == true ? '... ' : ' ') + melFunction.parameters[p].name);
		}
		return melFunction.name + '(' + parameters.join(', ') + ')';
	}
};

/**
 * Sorts callback
 *
 * @param melFunctionA
 * @param melFunctionB
 * @returns {Number}
 */
Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype.sortByFuncName = function(melFunctionA, melFunctionB) {
	if (melFunctionA.name < melFunctionB.name) {
		return -1;
	} else if (melFunctionA.name > melFunctionB.name) {
		return 1;
	} else {
		return 0;
	}
};

/**
 * Returns the MEL type
 *
 * @returns {String}
 */
Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype.getMELType = function() {
	if (this.MELType == undefined) {
		this.MELType = getComponentType(this.getMenu().getParent());
	}
	return this.MELType;
};

/**
 * Returns the chapter for the documentation
 *
 * @returns {String}
 */
Mashup_Window_TabMenu_Context_Tab_MELFunctions.prototype.getDocumentation = function() {
	return 'MEL';
};


/**
 * File: /resources/mashupBuilder/js/Window/TabMenu/Context/Tab/MELDebug.js
 */
Inherit(Mashup_Window_TabMenu_Context_Tab_MELDebug, Mashup_Window_TabMenu_Tab);
function Mashup_Window_TabMenu_Context_Tab_MELDebug(menu, id, name) {
	Mashup_Window_TabMenu_Context_Tab_MELDebug.superclass.constructor.call(this, menu, id, name);
}

/**
 * Returns whether this tab is visible or not
 * 
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_MELDebug.prototype.isVisible = function() {
	var parameterInput = this.getMenu().getParameterInput();
	if (parameterInput == null || !parameterInput.isEvaluatedByDefault()) {
		return false;
	}
	return true;
};

/**
 * Returns the DOM element for this tab
 * 
 * @returns
 */
Mashup_Window_TabMenu_Context_Tab_MELDebug.prototype.getEl = function() {
	if (this.el == null) {
		this.el = Mashup_Window_TabMenu_Context_Tab_MELDebug.superclass.getEl.call(this);
		this.el.html('' +
			'<div class="debug-tab-action-wrapper">' +
				'<label class="debug-tab-action-label">' + _.CTX_DEBUG_VALIDATE_SYNTAX() + '</label> <span class="text-button" name="doValidateSyntax">' + _.CTX_DEBUG_VALIDATE() + '</span>' +
			'</div class="debug-tab-action-wrapper">' +
			/* TODO
			'<div class="debug-tab-action-wrapper">' +
				'<label class="debug-tab-action-label">' + _.CTX_DEBUG_VALIDATE_RESULT() + '</label>' + '<span class="text-button" name="doValidateResult">' + _.CTX_DEBUG_VALIDATE() + '</span>' +
			'</div>' +
			*/
			'<div class="debug-tab-action-result"></div>'
		);
	}
	return this.el;
};

/**
 * Called when the context of the tab were updated
 * 
 * @param context
 */
Mashup_Window_TabMenu_Context_Tab_MELDebug.prototype.refresh = function(context) {
	this.getEl().find('.debug-tab-action-result').empty().hide(); // empty the validation result
};

/**
 * Called when the user clicked on the menu
 * 
 * @param context
 */
Mashup_Window_TabMenu_Context_Tab_MELDebug.prototype.onClick = function(context) {
	if (context.target.attr('name') == 'doValidateSyntax') {
		this.validate();
	}
};

/**
 * Validates the MEL expression syntax
 */
Mashup_Window_TabMenu_Context_Tab_MELDebug.prototype.validate = function() {
	var input = this.getMenu().getParameterInput();
	if (input == null) {
		this.getEl().find('.debug-tab-action-result').empty().hide();
		return;
	}

	var $result = this.getEl().find('.debug-tab-action-result');
	$.getJSON(getCurrentPath() + '/isValidMELExpr?expr=' +  encodeURIComponent(input.val()) + '&type=' + this.getMELType(), function(data) {
		if (data.isValid == true) {
			$result.removeClass('invalid').addClass('valid').html(_.CTX_DEBUG_SYNTAX_IS_VALID());
		} else {
			$result.removeClass('valid').addClass('invalid').html('<label class="debug-tab-result-label">' + _.CTX_DEBUG_ERROR_LABEL() + '</label><pre class="debug-tab-result-message">' + data.output + '</ERR>' + '</pre><label class="debug-tab-result-label">' + _.CTX_DEBUG_ERROR_DETAILS() + '</label><pre class="debug-tab-result-message">' + data.errorMessage.escapeHTML() + '</pre>');
		}
		$result.show();
	});
};

/**
 * Returns the chapter for the documentation of this tab
 * 
 * @returns {String}
 */
Mashup_Window_TabMenu_Context_Tab_MELDebug.prototype.getDocumentation = function() {
	return 'MEL';
};

/**
 * Returns the MEL type
 * 
 * @returns {String}
 */
Mashup_Window_TabMenu_Context_Tab_MELDebug.prototype.getMELType = function() {
	if (this.MELType == undefined) {
		this.MELType = getComponentType(this.getMenu().getParent());
	}
	return this.MELType;
};


/**
 * File: /resources/mashupBuilder/js/BottomToolbar.js
 */
/**
 * Implementation of a Mashup Window as a resizable BottomToolbar
 * 
 * @constructor
 * @this {Mashup_BottomToolbar}
 * @param parent The bottomtoolbar container
 * @param content The bottomtoolbar Content class
 * @param options Additional options for the bottomtoolbar
 */
Inherit(Mashup_BottomToolbar, Mashup_Window);
function Mashup_BottomToolbar(options) {
	options = options || {};
	options.className = 'window-bottom ' + (options.className || '');
	Mashup_BottomToolbar.superclass.constructor.call(this, options);

	this.toolbarHeight = this.getDefaultToolbarHeight() || 350;
	this.toolbarHeightMax = window.mashupBuilder.height - 15;

	this.onMouseDownCallback = $.proxy(this.handleEventOnMouseDown, this);
	this.onMouseMoveCallback = $.proxy(this.handleEventOnMouseMove, this);
	this.onMouseUpCallback = $.proxy(this.handleEventOnMouseUp, this);
}

/**
 * Saves the new default height for the Bottom Toolbar
 * 
 * @param height
 */
Mashup_BottomToolbar.prototype.saveDefaultToolbarHeight = function(height) {
	mashupBuilder.state.save('m', 'h', parseInt(height));
};

/**
 * Returns the default height for the Bottom Toolbar
 * 
 * @returns
 */
Mashup_BottomToolbar.prototype.getDefaultToolbarHeight = function() {
	return parseInt(mashupBuilder.state.get('m', 'h', null));
};

/**
 * Returns the DOM element for the header
 * 
 * @returns
 */
Mashup_BottomToolbar.prototype.getElHeader = function() {
	var $elHeader = Mashup_BottomToolbar.superclass.getElHeader.call(this);
	$elHeader.addClass('resize window-resize-height');
	$elHeader.on('mousedown', this.onMouseDownCallback);
	return $elHeader;
};

/**
 * Returns the DOM element for the content
 * 
 * @returns
 */
Mashup_BottomToolbar.prototype.getElContent = function() {
	var $elContent = Mashup_BottomToolbar.superclass.getElContent.call(this);
	$elContent.append('<div class="window-reset-height" name="resetHeight"><span class="icon"></span>' + _.TOOLBAR_RESET_HEIGHT() + '</div>');
	return $elContent;
};

/**
 * Handles jQuery event 'onClick'
 * 
 * @param e
 * @returns
 */
Mashup_BottomToolbar.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		// ignore click on resize height handle
		if (target.hasClass('window-resize-height')) {
			return false;
		}

		// reset the bottom toolbar height
		if (target.attr('name') == 'resetHeight') {
			this.setHeight(350);
			this.saveDefaultToolbarHeight(350);
			return false;
		}

		if (target.attr('name') == 'doCloseWindow') {
			window.mashupBuilder.removeOpenedBottomToolbar();
			return false;
		}
	}
	return Mashup_BottomToolbar.superclass.handleEventOnClick.call(this, e);
};

/**
 * Handles jQuery event 'onMouseDown'
 * 
 * @param e
 * @returns
 */
Mashup_BottomToolbar.prototype.handleEventOnMouseDown = function(e) {
	e.stopPropagation();

	$('#mashupBuilder').addClass('disable-user-select');

	this.startY = this.toolbarHeight + e.pageY;

	$(document).on('mousemove', this.onMouseMoveCallback);
	$(document).one('mouseup', this.onMouseUpCallback);
};

/**
 * Handles jQuery event 'onMouseMove'
 * 
 * @param e
 * @returns
 */
Mashup_BottomToolbar.prototype.handleEventOnMouseMove = function(e) {
	e.stopPropagation();

	var newHeight = this.startY - e.pageY;
	if (newHeight < 200) {
		newHeight = 200;
	} else if (newHeight > this.toolbarHeightMax) {
		newHeight = this.toolbarHeightMax;
	}

	this.setHeight(newHeight);
};

/**
 * Handles jQuery event 'onMouseUp'
 * 
 * @param e
 * @returns
 */
Mashup_BottomToolbar.prototype.handleEventOnMouseUp = function(e) {
	e.stopPropagation();
	$(document).unbind('mousemove', this.onMouseMove);
	$('#mashupBuilder').removeClass('disable-user-select');
	this.saveDefaultToolbarHeight(this.toolbarHeight);
};

/**
 * Called when the user resized the browser
 */
Mashup_BottomToolbar.prototype.onResize = function() {
	this.toolbarHeightMax = window.mashupBuilder.height - 15;
	Mashup_BottomToolbar.superclass.onResize.call(this);
};

/**
 * Called when the Window is closed (whether it is removed or not)
 * 
 * @param context
 */
Mashup_BottomToolbar.prototype.onClose = function(context) {
	return this.content.onClose(context);
};

/**
 * Called when the window is opened
 * 
 * @param context
 */
Mashup_BottomToolbar.prototype.onOpen = function(context) {
	// because of the draggable plugin on component, clicking on them
	// prevent the default behavior of bluring the current active element (ex: search input)
	// thus we handle manually the blur here
	if (document.activeElement && document.activeElement.blur) {
		if (document.activeElement.nodeName.toLowerCase() !== "body") {
			// lulz @ IE
			// http://tjvantoll.com/2013/08/30/bugs-with-document-activeelement-in-internet-explorer/
			try {
				document.activeElement.blur();
			} catch (error) {
				// do nothing
			}
		}
	}
	return this.content.onOpen(context);
};

/**
 * Sets the new height for the BottomToolbar
 * 
 * @param height
 */
Mashup_BottomToolbar.prototype.setHeight = function(height) {
	if (height != this.toolbarHeight) {
		this.toolbarHeight = height;
		this.onResize();
	}
};

/**
 * Returns the size of the Window
 * 
 * @param force Specifies if it should return the cached size or not
 * @returns
 */
Mashup_BottomToolbar.prototype.getWindowSize = function(force) {
	if (this.windowSize == undefined || force == true) {
		// reset current height if too high
		if (this.toolbarHeight > this.toolbarHeightMax) {
			this.toolbarHeight = this.toolbarHeightMax;
		}
		this.windowSize = { height: this.toolbarHeight, width: mashupBuilder.width, bottom: 0, left: 0 };
	}
	return this.windowSize;
};

/**
 * Removes this window from the DOM
 */
Mashup_BottomToolbar.prototype.remove = function() {
	if (this.el != undefined) {
		this.el.remove();
		this.el = undefined;
	}
	Mashup_BottomToolbar.superclass.remove.call(this);
};


/**
 * File: /resources/mashupBuilder/js/Popup/Error.js
 */
Inherit(Mashup_Popup_Error, Mashup_Popup_Alert);
function Mashup_Popup_Error(nbErrors) {
	Mashup_Popup_Error.superclass.constructor.call(this, {
		text: _.CORRECT_ERRORS_BEFORE(nbErrors),
		level: BoxLevel.ERROR,
		closeAction: 'ok',
		onClickOkCallback: function() {
			this.remove();
			window.mashupBuilder.doOpenError();
		}
	});
}
