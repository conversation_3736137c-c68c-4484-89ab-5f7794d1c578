/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:36 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.refinePanel.templates.default_;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class normalFilter_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_0;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_1;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_2;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_3;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_4;

static {
  _jspx_fnmap_0= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:toLowerCase", org.apache.taglibs.standard.functions.Functions.class, "toLowerCase", new Class[] {java.lang.String.class});
  _jspx_fnmap_1= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:getKeys", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "getKeys", new Class[] {java.util.Map.class, java.lang.String.class});
  _jspx_fnmap_2= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:getFirstFeedID", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "getFirstFeedID", new Class[] {java.util.Map.class});
  _jspx_fnmap_3= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:getApplicationId", com.exalead.cv360.searchui.services.context.PLMAConfigContext.class, "getApplicationName", new Class[] {});
  _jspx_fnmap_4= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:getJSON", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "getJSON", new Class[] {java.lang.Object.class, java.lang.String.class});
}

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(18);
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/deprecated/DisplayHitsWidgetHelper.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/3dxpSearchAndRefine/tld/experience.tld", Long.valueOf(1751968403638L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/widget.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/i18n.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/list.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/url.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fmt.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/request.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/map.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetPaginationInfo_0026_005fvar_005fuiPagination_005ffeeds_005ffacetId_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetPaginationInfo_0026_005fvar_005ffeeds_005ffacetId_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fisRefineFacetChartMode_0026_005fvar_005ffacet_005fcookie_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005frefinePanelSerie_0026_005fvar_005frefineConfig_005fperPage_005fpaginate_005ffeeds_005ffacet_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fjspTemplatePathCheck_0026_005fwidget_005fvar_005fdefaultJspPath_005fcustomJspPath_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowEmptyCategories_005froot_005fiterationMode_005fend;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffacet_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetFacetDisplayConfigAsJson_0026_005ffacet_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fname_005fdefaultValue_005fnobody;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetPaginationInfo_0026_005fvar_005fuiPagination_005ffeeds_005ffacetId_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetPaginationInfo_0026_005fvar_005ffeeds_005ffacetId_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fisRefineFacetChartMode_0026_005fvar_005ffacet_005fcookie_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005frefinePanelSerie_0026_005fvar_005frefineConfig_005fperPage_005fpaginate_005ffeeds_005ffacet_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fjspTemplatePathCheck_0026_005fwidget_005fvar_005fdefaultJspPath_005fcustomJspPath_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowEmptyCategories_005froot_005fiterationMode_005fend = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffacet_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetFacetDisplayConfigAsJson_0026_005ffacet_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fplma_005fgetPaginationInfo_0026_005fvar_005fuiPagination_005ffeeds_005ffacetId_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
    _005fjspx_005ftagPool_005fplma_005fgetPaginationInfo_0026_005fvar_005ffeeds_005ffacetId_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fisRefineFacetChartMode_0026_005fvar_005ffacet_005fcookie_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005frefinePanelSerie_0026_005fvar_005frefineConfig_005fperPage_005fpaginate_005ffeeds_005ffacet_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fjspTemplatePathCheck_0026_005fwidget_005fvar_005fdefaultJspPath_005fcustomJspPath_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowEmptyCategories_005froot_005fiterationMode_005fend.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffacet_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.release();
    _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetFacetDisplayConfigAsJson_0026_005ffacet_005fnobody.release();
    _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody.release();
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fname_005fdefaultValue_005fnobody.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fset_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fset_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_i18n_005fmessage_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_i18n_005fmessage_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fchoose_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_plma_005fisRefineFacetChartMode_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_plma_005frefinePanelSerie_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("<div class=\"filter-list-facet-elem filter-list-facet-elem-facet filter-list-facet-elem-facet-");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\" data-facetPath=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.path}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\" data-facetId=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\">\r\n");
      out.write("    <div class=\"facet-spinner-container\">\r\n");
      out.write("        <div class=\"facet-spinner\"></div>\r\n");
      out.write("    </div>\r\n");
      out.write("    <div class=\"facet-value-container\">\r\n");
      out.write("        ");
      if (_jspx_meth_plma_005fjspTemplatePathCheck_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_render_005ftemplate_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("        ");
      if (_jspx_meth_c_005fif_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("        <span class=\"view-container\">\r\n");
      out.write("			<div class=\"facet-container-list ");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.collapseFacet || (defaultChartMode && isSuggestSearch != 'true' && suggestApiAction == 'access') ? 'hidden' : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\">\r\n");
      out.write("				<table class=\"facet table table-layout table-facets table-hover table-list-facet-");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\"\r\n");
      out.write("                       data-page=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${paginationInfo.page}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\">\r\n");
      out.write("					");
      if (_jspx_meth_render_005ftemplate_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("				</table>\r\n");
      out.write("			</div>\r\n");
      out.write("\r\n");
      out.write("			");
      if (_jspx_meth_c_005fif_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("			");
      if (_jspx_meth_c_005fset_005f7(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("			");
      if (_jspx_meth_config_005fgetOption_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("			");
      if (_jspx_meth_string_005feval_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("			");
      if (_jspx_meth_c_005fset_005f8(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("			");
      out.write("\r\n");
      out.write("			");
      if (_jspx_meth_c_005fif_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("		</span>\r\n");
      out.write("    </div>\r\n");
      out.write("</div>\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fif_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(18,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setParameters("facet,widget,feeds,uCssId,facetConfig,isSuggestSearch,suggestApiAction,facetsSuggestContext,refinePanelCfg,suggestCfg,refinesPanelId,refinesPanelMode");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f0_reused = false;
    try {
      _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(23,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setVar("isPaginated");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(23,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(23,0) '${refinePanelCfg.paginated && refinePanelCfg.perPage > 0}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${refinePanelCfg.paginated && refinePanelCfg.perPage > 0}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
      if (_jspx_th_c_005fset_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
      _jspx_th_c_005fset_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f1_reused = false;
    try {
      _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(25,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setVar("facetTemplateDirectory");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(25,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(25,0) 'templates/default'",_jsp_getExpressionFactory().createValueExpression("templates/default",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
      if (_jspx_th_c_005fset_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
      _jspx_th_c_005fset_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f2_reused = false;
    try {
      _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(27,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setVar("collapseValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(27,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(27,0) '${refinePanelCfg.collapseFacet ? 'hidden' : ''}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${refinePanelCfg.collapseFacet ? 'hidden' : ''}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
      if (_jspx_th_c_005fset_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
      _jspx_th_c_005fset_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f0 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f0_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f0.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(29,0) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setCode("plma.tools.search");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(29,0) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setVar("searchI18n");
      int[] _jspx_push_body_count_i18n_005fmessage_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f0 = _jspx_th_i18n_005fmessage_005f0.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f0);
      _jspx_th_i18n_005fmessage_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f0, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f1 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f1_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f1.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(30,0) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setCode("refines.noresult");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(30,0) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setVar("noResultI18n");
      int[] _jspx_push_body_count_i18n_005fmessage_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f1 = _jspx_th_i18n_005fmessage_005f1.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f1);
      _jspx_th_i18n_005fmessage_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f1, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent(null);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(34,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isPaginated}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_plma_005fgetPaginationInfo_005f0(_jspx_th_c_005fwhen_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetPaginationInfo_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getPaginationInfo
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetPaginationInfoTag _jspx_th_plma_005fgetPaginationInfo_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetPaginationInfoTag) _005fjspx_005ftagPool_005fplma_005fgetPaginationInfo_0026_005fvar_005fuiPagination_005ffeeds_005ffacetId_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetPaginationInfoTag.class);
    boolean _jspx_th_plma_005fgetPaginationInfo_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetPaginationInfo_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetPaginationInfo_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(35,8) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetPaginationInfo_005f0.setVar("paginationInfo");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(35,8) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetPaginationInfo_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(35,8) name = facetId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetPaginationInfo_005f0.setFacetId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(35,8) name = uiPagination type = java.lang.Integer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetPaginationInfo_005f0.setUiPagination((java.lang.Integer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.perPage}", java.lang.Integer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetPaginationInfo_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetPaginationInfo_005f0 = _jspx_th_plma_005fgetPaginationInfo_005f0.doStartTag();
        if (_jspx_th_plma_005fgetPaginationInfo_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetPaginationInfo_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetPaginationInfo_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetPaginationInfo_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetPaginationInfo_0026_005fvar_005fuiPagination_005ffeeds_005ffacetId_005fnobody.reuse(_jspx_th_plma_005fgetPaginationInfo_005f0);
      _jspx_th_plma_005fgetPaginationInfo_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetPaginationInfo_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetPaginationInfo_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_plma_005fgetPaginationInfo_005f1(_jspx_th_c_005fotherwise_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetPaginationInfo_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getPaginationInfo
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetPaginationInfoTag _jspx_th_plma_005fgetPaginationInfo_005f1 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetPaginationInfoTag) _005fjspx_005ftagPool_005fplma_005fgetPaginationInfo_0026_005fvar_005ffeeds_005ffacetId_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetPaginationInfoTag.class);
    boolean _jspx_th_plma_005fgetPaginationInfo_005f1_reused = false;
    try {
      _jspx_th_plma_005fgetPaginationInfo_005f1.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetPaginationInfo_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(38,8) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetPaginationInfo_005f1.setVar("paginationInfo");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(38,8) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetPaginationInfo_005f1.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(38,8) name = facetId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetPaginationInfo_005f1.setFacetId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetPaginationInfo_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetPaginationInfo_005f1 = _jspx_th_plma_005fgetPaginationInfo_005f1.doStartTag();
        if (_jspx_th_plma_005fgetPaginationInfo_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetPaginationInfo_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetPaginationInfo_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetPaginationInfo_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetPaginationInfo_0026_005fvar_005ffeeds_005ffacetId_005fnobody.reuse(_jspx_th_plma_005fgetPaginationInfo_005f1);
      _jspx_th_plma_005fgetPaginationInfo_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetPaginationInfo_005f1, _jsp_getInstanceManager(), _jspx_th_plma_005fgetPaginationInfo_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fisRefineFacetChartMode_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:isRefineFacetChartMode
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.ChartDisplayModeTag _jspx_th_plma_005fisRefineFacetChartMode_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.ChartDisplayModeTag) _005fjspx_005ftagPool_005fplma_005fisRefineFacetChartMode_0026_005fvar_005ffacet_005fcookie_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.ChartDisplayModeTag.class);
    boolean _jspx_th_plma_005fisRefineFacetChartMode_005f0_reused = false;
    try {
      _jspx_th_plma_005fisRefineFacetChartMode_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fisRefineFacetChartMode_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(42,0) name = facet type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fisRefineFacetChartMode_005f0.setFacet((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(42,0) name = cookie type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fisRefineFacetChartMode_005f0.setCookie("refine-panel-facet-option");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(42,0) name = var type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fisRefineFacetChartMode_005f0.setVar("defaultChartMode");
      int[] _jspx_push_body_count_plma_005fisRefineFacetChartMode_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fisRefineFacetChartMode_005f0 = _jspx_th_plma_005fisRefineFacetChartMode_005f0.doStartTag();
        if (_jspx_th_plma_005fisRefineFacetChartMode_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fisRefineFacetChartMode_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fisRefineFacetChartMode_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fisRefineFacetChartMode_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fisRefineFacetChartMode_0026_005fvar_005ffacet_005fcookie_005fnobody.reuse(_jspx_th_plma_005fisRefineFacetChartMode_005f0);
      _jspx_th_plma_005fisRefineFacetChartMode_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fisRefineFacetChartMode_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fisRefineFacetChartMode_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005frefinePanelSerie_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:refinePanelSerie
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.RefinePanelSerieTag _jspx_th_plma_005frefinePanelSerie_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.RefinePanelSerieTag) _005fjspx_005ftagPool_005fplma_005frefinePanelSerie_0026_005fvar_005frefineConfig_005fperPage_005fpaginate_005ffeeds_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.RefinePanelSerieTag.class);
    boolean _jspx_th_plma_005frefinePanelSerie_005f0_reused = false;
    try {
      _jspx_th_plma_005frefinePanelSerie_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005frefinePanelSerie_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(43,0) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005frefinePanelSerie_005f0.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(43,0) name = var type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005frefinePanelSerie_005f0.setVar("paginatedSerie");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(43,0) name = paginate type = java.lang.Boolean reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005frefinePanelSerie_005f0.setPaginate(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isPaginated && paginationInfo.uiPagination}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(43,0) name = perPage type = java.lang.Long reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005frefinePanelSerie_005f0.setPerPage(((java.lang.Long) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.perPage}", long.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).longValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(43,0) name = refineConfig type = com.exalead.apps.plma_tools.commons.config.RefineBy reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005frefinePanelSerie_005f0.setRefineConfig((com.exalead.apps.plma_tools.commons.config.RefineBy) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetConfig}", com.exalead.apps.plma_tools.commons.config.RefineBy.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(43,0) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005frefinePanelSerie_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005frefinePanelSerie_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005frefinePanelSerie_005f0 = _jspx_th_plma_005frefinePanelSerie_005f0.doStartTag();
        if (_jspx_th_plma_005frefinePanelSerie_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005frefinePanelSerie_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005frefinePanelSerie_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005frefinePanelSerie_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005frefinePanelSerie_0026_005fvar_005frefineConfig_005fperPage_005fpaginate_005ffeeds_005ffacet_005fnobody.reuse(_jspx_th_plma_005frefinePanelSerie_005f0);
      _jspx_th_plma_005frefinePanelSerie_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005frefinePanelSerie_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005frefinePanelSerie_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fjspTemplatePathCheck_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  plma:jspTemplatePathCheck
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.widget.JSPTemplatePathCheck _jspx_th_plma_005fjspTemplatePathCheck_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.widget.JSPTemplatePathCheck) _005fjspx_005ftagPool_005fplma_005fjspTemplatePathCheck_0026_005fwidget_005fvar_005fdefaultJspPath_005fcustomJspPath_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.widget.JSPTemplatePathCheck.class);
    boolean _jspx_th_plma_005fjspTemplatePathCheck_005f0_reused = false;
    try {
      _jspx_th_plma_005fjspTemplatePathCheck_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fjspTemplatePathCheck_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(51,8) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fjspTemplatePathCheck_005f0.setVar("categoryTemplatePath");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(51,8) name = defaultJspPath type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fjspTemplatePathCheck_005f0.setDefaultJspPath((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/category.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(51,8) name = customJspPath type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fjspTemplatePathCheck_005f0.setCustomJspPath((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/${fn:toLowerCase(facet.path)}/category.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_0));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(51,8) name = widget type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fjspTemplatePathCheck_005f0.setWidget("refinePanel");
      int _jspx_eval_plma_005fjspTemplatePathCheck_005f0 = _jspx_th_plma_005fjspTemplatePathCheck_005f0.doStartTag();
      if (_jspx_th_plma_005fjspTemplatePathCheck_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fplma_005fjspTemplatePathCheck_0026_005fwidget_005fvar_005fdefaultJspPath_005fcustomJspPath_005fnobody.reuse(_jspx_th_plma_005fjspTemplatePathCheck_005f0);
      _jspx_th_plma_005fjspTemplatePathCheck_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fjspTemplatePathCheck_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fjspTemplatePathCheck_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f0);
    try {
      _jspx_th_render_005ftemplate_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(55,8) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/${fn:toLowerCase(facet.path)}/facet.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_0));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(55,8) name = defaultTemplate type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setDefaultTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/facet.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(55,8) name = widget type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setWidget("refinePanel");
      _jspx_th_render_005ftemplate_005f0.setJspBody(new Helper( 0, _jspx_page_context, _jspx_th_render_005ftemplate_005f0, null));
      _jspx_th_render_005ftemplate_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f0);
    try {
      _jspx_th_render_005fparameter_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f0.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(57,12) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setName("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(57,12) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f1);
    try {
      _jspx_th_render_005fparameter_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f1.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(58,12) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setName("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(58,12) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f2);
    try {
      _jspx_th_render_005fparameter_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f2.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(59,12) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setName("accessFeeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(59,12) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f3 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f3);
    try {
      _jspx_th_render_005fparameter_005f3.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f3.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(60,12) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setName("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(60,12) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f3.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f3);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f4 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f4);
    try {
      _jspx_th_render_005fparameter_005f4.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f4.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(61,12) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setName("facetConfig");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(61,12) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetConfig}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f4.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f4);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f5 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f5);
    try {
      _jspx_th_render_005fparameter_005f5.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f5.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(62,12) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setName("collapseValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(62,12) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collapseValue}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f5.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f5);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f6 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f6);
    try {
      _jspx_th_render_005fparameter_005f6.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f6.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(63,12) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setName("isSuggestSearch");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(63,12) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isSuggestSearch}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f6.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f6);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(66,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.activateFilter}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fset_005f3(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fset_005f4(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_search_005fforEachCategory_005f0(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fset_005f6(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("            <div class=\"inputFilterCategory table-list-facet-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("-input ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${showInput ? '' : 'hidden'}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("                <div id=\"filter-list-facet-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("-noresult\"\r\n");
          out.write("                     class=\" filter-list-facet-noresult filter-list-facet-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("-noresult hidden\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${noResultI18n}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</div>\r\n");
          out.write("            </div>\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f3_reused = false;
    try {
      _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(67,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setVar("showInput");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(67,12) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(67,12) '${false}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${false}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
      if (_jspx_th_c_005fset_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
      _jspx_th_c_005fset_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f4_reused = false;
    try {
      _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(68,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setVar("categoryCount");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(68,12) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(68,12) '0'",_jsp_getExpressionFactory().createValueExpression("0",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
      if (_jspx_th_c_005fset_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
      _jspx_th_c_005fset_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachCategory_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachCategory
    com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag _jspx_th_search_005fforEachCategory_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag) _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowEmptyCategories_005froot_005fiterationMode_005fend.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag.class);
    boolean _jspx_th_search_005fforEachCategory_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachCategory_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachCategory_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(69,12) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setVar("category");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(69,12) name = root type = com.exalead.access.feedapi.CategoryTreeContainer reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setRoot((com.exalead.access.feedapi.CategoryTreeContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.CategoryTreeContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(69,12) name = iterationMode type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setIterationMode("all");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(69,12) name = end type = int reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setEnd(((java.lang.Integer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.filterMin+1}", int.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).intValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(69,12) name = showEmptyCategories type = boolean reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setShowEmptyCategories(false);
      int[] _jspx_push_body_count_search_005fforEachCategory_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachCategory_005f0 = _jspx_th_search_005fforEachCategory_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachCategory_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_c_005fset_005f5(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("            ");
            int evalDoAfterBody = _jspx_th_search_005fforEachCategory_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachCategory_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachCategory_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachCategory_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachCategory_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvar_005fshowEmptyCategories_005froot_005fiterationMode_005fend.reuse(_jspx_th_search_005fforEachCategory_005f0);
      _jspx_th_search_005fforEachCategory_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachCategory_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachCategory_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f5 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f5_reused = false;
    try {
      _jspx_th_c_005fset_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(75,16) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setVar("categoryCount");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(75,16) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(75,16) '${categoryCount + 1}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${categoryCount + 1}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f5 = _jspx_th_c_005fset_005f5.doStartTag();
      if (_jspx_th_c_005fset_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f5);
      _jspx_th_c_005fset_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f6 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f6_reused = false;
    try {
      _jspx_th_c_005fset_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(77,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setVar("showInput");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(77,12) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(77,12) '${categoryCount >= refinePanelCfg.filterMin}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${categoryCount >= refinePanelCfg.filterMin}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f6 = _jspx_th_c_005fset_005f6.doStartTag();
      if (_jspx_th_c_005fset_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f6);
      _jspx_th_c_005fset_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f1);
    try {
      _jspx_th_render_005ftemplate_005f1.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(87,5) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f1.setTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/${fn:toLowerCase(facet.path)}/subCategory.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_0));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(87,5) name = defaultTemplate type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f1.setDefaultTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/subCategory.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(87,5) name = widget type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f1.setWidget("refinePanel");
      _jspx_th_render_005ftemplate_005f1.setJspBody(new Helper( 1, _jspx_page_context, _jspx_th_render_005ftemplate_005f1, null));
      _jspx_th_render_005ftemplate_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f7 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f7);
    try {
      _jspx_th_render_005fparameter_005f7.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f7.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(90,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setName("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(90,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f7.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f7);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f8 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f8);
    try {
      _jspx_th_render_005fparameter_005f8.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f8.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(91,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f8.setName("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(91,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f8.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f8.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f8);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f9 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f9);
    try {
      _jspx_th_render_005fparameter_005f9.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f9.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(92,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f9.setName("accessFeeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(92,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f9.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f9.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f9);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f10 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f10);
    try {
      _jspx_th_render_005fparameter_005f10.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f10.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(93,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f10.setName("categoryTemplatePath");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(93,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f10.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryTemplatePath}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f10.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f10);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f11 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f11);
    try {
      _jspx_th_render_005fparameter_005f11.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f11.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(94,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f11.setName("paginationInfo");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(94,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f11.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${paginationInfo}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f11.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f11);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f12 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f12);
    try {
      _jspx_th_render_005fparameter_005f12.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f12.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(95,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f12.setName("facetConfig");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(95,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f12.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetConfig}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f12.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f12);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f13 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f13);
    try {
      _jspx_th_render_005fparameter_005f13.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f13.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(96,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f13.setName("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(96,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f13.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f13.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f13);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f14 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f14);
    try {
      _jspx_th_render_005fparameter_005f14.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f14.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(97,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f14.setName("facetsSuggestContext");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(97,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f14.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetsSuggestContext}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f14.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f14);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f15(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f15 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f15);
    try {
      _jspx_th_render_005fparameter_005f15.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f15.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(98,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f15.setName("refinePanelCfg");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(98,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f15.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f15.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f15);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f16(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f16 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f16);
    try {
      _jspx_th_render_005fparameter_005f16.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f16.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(99,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f16.setName("suggestCfg");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(99,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f16.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${suggestCfg}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f16.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f16);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(104,3) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty facetConfig && isSuggestSearch != 'true' || suggestApiAction != 'access'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                <div class=\"highcharts-facet-container ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.collapseFacet || !defaultChartMode ? 'hidden' : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"></div>\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f7(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f7 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f7_reused = false;
    try {
      _jspx_th_c_005fset_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f7.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(108,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setVar("maxPagination");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(108,3) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(108,3) '${paginationInfo.pages}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${paginationInfo.pages}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f7 = _jspx_th_c_005fset_005f7.doStartTag();
      if (_jspx_th_c_005fset_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f7);
      _jspx_th_c_005fset_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(109,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setVar("reloadCondition");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(109,3) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setName("reloadCondition");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(109,3) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setDefaultValue("true");
      int[] _jspx_push_body_count_config_005fgetOption_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f0 = _jspx_th_config_005fgetOption_005f0.doStartTag();
        if (_jspx_th_config_005fgetOption_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f0);
      _jspx_th_config_005fgetOption_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f0_reused = false;
    try {
      _jspx_th_string_005feval_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(110,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setVar("reloadCondition");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(110,3) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${reloadCondition}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(110,3) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005feval_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f0 = _jspx_th_string_005feval_005f0.doStartTag();
        if (_jspx_th_string_005feval_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffacet_005fnobody.reuse(_jspx_th_string_005feval_005f0);
      _jspx_th_string_005feval_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f0, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f8(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f8 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f8_reused = false;
    try {
      _jspx_th_c_005fset_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f8.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(111,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f8.setVar("reloadCondition");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(111,3) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f8.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(111,3) '${refinePanelCfg.activateReload && reloadCondition}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${refinePanelCfg.activateReload && reloadCondition}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f8 = _jspx_th_c_005fset_005f8.doStartTag();
      if (_jspx_th_c_005fset_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f8);
      _jspx_th_c_005fset_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(113,3) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${paginationInfo.paginated && (not isSuggestSearch || suggestApiAction != 'access')}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("				<div class=\"view pagination ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collapseValue}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("					<span class=\"previous-page fonticon fonticon-left-open\"></span>\r\n");
          out.write("					<span class=\"current-page\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${paginationInfo.page}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</span> / <span class=\"max-page\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${paginationInfo.pages}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</span>\r\n");
          out.write("					<span class=\"next-page fonticon fonticon-right-open\"></span>\r\n");
          out.write("				</div>\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(125,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not isSuggestSearch || suggestApiAction != 'access'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_render_005frenderScript_005f0(_jspx_th_c_005fif_005f3, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f0_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(126,4) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f0.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f0 = _jspx_th_render_005frenderScript_005f0.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f0);
          }
          do {
            out.write("\r\n");
            out.write("        var options = {};\r\n");
            out.write("        options.pageName = '");
            if (_jspx_meth_search_005fgetPageName_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("        options.feeds = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getKeys(feeds, '[]')}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_1));
            out.write(";\r\n");
            out.write("        options.refinesPanelId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinesPanelId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("        options.baseAjaxUrl = '");
            if (_jspx_meth_c_005furl_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write('/');
            if (_jspx_meth_search_005fgetPageName_005f1(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write('/');
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getFirstFeedID(feeds)}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_2));
            out.write("';\r\n");
            out.write("        options.baseAjaxReload = '");
            if (_jspx_meth_c_005furl_005f1(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write('/');
            if (_jspx_meth_search_005fgetPageName_005f2(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write('/');
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinesPanelMode}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("        options.applicationId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getApplicationId()}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_3));
            out.write("';\r\n");
            out.write("        options.pagination = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getJSON(paginationInfo, '{}')}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_4));
            out.write(";\r\n");
            out.write("        options.paginatedSerie = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getJSON(paginatedSerie, '{}')}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_4));
            out.write(";\r\n");
            out.write("        options.facetId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("        options.facetDisplayConfig = ");
            if (_jspx_meth_plma_005fgetFacetDisplayConfigAsJson_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write(";\r\n");
            out.write("        options.paramNameReload = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.paramNameReload}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("        options.suggestApiAction = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${suggestApiAction}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fif_005f4(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("        options.paramNameReloadValue = [];\r\n");
            out.write("        ");
            if (_jspx_meth_request_005fgetParameterValues_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fforEach_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_request_005fgetParameterValues_005f1(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fforEach_005f1(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("        options.loadDateFacetParameter = '");
            if (_jspx_meth_config_005fgetOption_005f1(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("        options.loadFacetParameter = '");
            if (_jspx_meth_plma_005fgetConstantValueFromNameTag_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("        options.facetPage = '");
            if (_jspx_meth_request_005fgetParameterValue_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("        options.refineEventMode = false;\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fif_005f5(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("        $('.aggregation-bar').empty();\r\n");
            out.write("        options.customDelimiter = \"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${suggestCfg.customDelimiter}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\";\r\n");
            out.write("        var facetRefine = new FacetRefine('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("', options);\r\n");
            out.write("        facetRefine.init();\r\n");
            out.write("    ");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f0);
      _jspx_th_render_005frenderScript_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetPageName_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getPageName
    com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag _jspx_th_search_005fgetPageName_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag) _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag.class);
    boolean _jspx_th_search_005fgetPageName_005f0_reused = false;
    try {
      _jspx_th_search_005fgetPageName_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetPageName_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int[] _jspx_push_body_count_search_005fgetPageName_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetPageName_005f0 = _jspx_th_search_005fgetPageName_005f0.doStartTag();
        if (_jspx_th_search_005fgetPageName_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetPageName_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetPageName_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetPageName_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.reuse(_jspx_th_search_005fgetPageName_005f0);
      _jspx_th_search_005fgetPageName_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetPageName_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetPageName_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f0 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f0_reused = false;
    try {
      _jspx_th_c_005furl_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(131,31) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f0.setValue("/plma/ajax/feed");
      int _jspx_eval_c_005furl_005f0 = _jspx_th_c_005furl_005f0.doStartTag();
      if (_jspx_th_c_005furl_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f0);
      _jspx_th_c_005furl_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f0, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetPageName_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getPageName
    com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag _jspx_th_search_005fgetPageName_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag) _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag.class);
    boolean _jspx_th_search_005fgetPageName_005f1_reused = false;
    try {
      _jspx_th_search_005fgetPageName_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetPageName_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int[] _jspx_push_body_count_search_005fgetPageName_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetPageName_005f1 = _jspx_th_search_005fgetPageName_005f1.doStartTag();
        if (_jspx_th_search_005fgetPageName_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetPageName_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetPageName_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetPageName_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.reuse(_jspx_th_search_005fgetPageName_005f1);
      _jspx_th_search_005fgetPageName_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetPageName_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fgetPageName_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f1 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f1_reused = false;
    try {
      _jspx_th_c_005furl_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(132,34) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f1.setValue("/plma/ajax/refine");
      int _jspx_eval_c_005furl_005f1 = _jspx_th_c_005furl_005f1.doStartTag();
      if (_jspx_th_c_005furl_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f1);
      _jspx_th_c_005furl_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f1, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetPageName_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getPageName
    com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag _jspx_th_search_005fgetPageName_005f2 = (com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag) _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag.class);
    boolean _jspx_th_search_005fgetPageName_005f2_reused = false;
    try {
      _jspx_th_search_005fgetPageName_005f2.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetPageName_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int[] _jspx_push_body_count_search_005fgetPageName_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetPageName_005f2 = _jspx_th_search_005fgetPageName_005f2.doStartTag();
        if (_jspx_th_search_005fgetPageName_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetPageName_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetPageName_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetPageName_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.reuse(_jspx_th_search_005fgetPageName_005f2);
      _jspx_th_search_005fgetPageName_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetPageName_005f2, _jsp_getInstanceManager(), _jspx_th_search_005fgetPageName_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetFacetDisplayConfigAsJson_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getFacetDisplayConfigAsJson
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetFacetDisplayConfigAsJsonTag _jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetFacetDisplayConfigAsJsonTag) _005fjspx_005ftagPool_005fplma_005fgetFacetDisplayConfigAsJson_0026_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetFacetDisplayConfigAsJsonTag.class);
    boolean _jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(137,37) name = facet type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0.setFacet((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetFacetDisplayConfigAsJson_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetFacetDisplayConfigAsJson_005f0 = _jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0.doStartTag();
        if (_jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetFacetDisplayConfigAsJson_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetFacetDisplayConfigAsJson_0026_005ffacet_005fnobody.reuse(_jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0);
      _jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetFacetDisplayConfigAsJson_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f4_reused = false;
    try {
      _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(140,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty facetConfig}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
      if (_jspx_eval_c_005fif_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            options.facetConfig = ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getJSON(facetConfig, '{}')}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_4));
          out.write(";\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      _jspx_th_c_005fif_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetParameterValues_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getParameterValues
    com.exalead.cv360.searchui.view.jspapi.request.GetParameterValuesTag _jspx_th_request_005fgetParameterValues_005f0 = (com.exalead.cv360.searchui.view.jspapi.request.GetParameterValuesTag) _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetParameterValuesTag.class);
    boolean _jspx_th_request_005fgetParameterValues_005f0_reused = false;
    try {
      _jspx_th_request_005fgetParameterValues_005f0.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetParameterValues_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(144,8) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValues_005f0.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.paramNameReload}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(144,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValues_005f0.setVar("paramNameReloads");
      int[] _jspx_push_body_count_request_005fgetParameterValues_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetParameterValues_005f0 = _jspx_th_request_005fgetParameterValues_005f0.doStartTag();
        if (_jspx_th_request_005fgetParameterValues_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetParameterValues_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetParameterValues_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetParameterValues_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_request_005fgetParameterValues_005f0);
      _jspx_th_request_005fgetParameterValues_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetParameterValues_005f0, _jsp_getInstanceManager(), _jspx_th_request_005fgetParameterValues_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f0_reused = false;
    try {
      _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(145,8) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(145,8) '${paramNameReloads}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${paramNameReloads}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(145,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setVar("paramName");
      int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
        if (_jspx_eval_c_005fforEach_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("            options.paramNameReloadValue.push('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${paramName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("');\r\n");
            out.write("        ");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
      _jspx_th_c_005fforEach_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetParameterValues_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getParameterValues
    com.exalead.cv360.searchui.view.jspapi.request.GetParameterValuesTag _jspx_th_request_005fgetParameterValues_005f1 = (com.exalead.cv360.searchui.view.jspapi.request.GetParameterValuesTag) _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetParameterValuesTag.class);
    boolean _jspx_th_request_005fgetParameterValues_005f1_reused = false;
    try {
      _jspx_th_request_005fgetParameterValues_005f1.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetParameterValues_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(148,8) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValues_005f1.setName("plma_facet_sort_reload");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(148,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValues_005f1.setVar("paramNameReloadsSort");
      int[] _jspx_push_body_count_request_005fgetParameterValues_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetParameterValues_005f1 = _jspx_th_request_005fgetParameterValues_005f1.doStartTag();
        if (_jspx_th_request_005fgetParameterValues_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetParameterValues_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetParameterValues_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetParameterValues_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_request_005fgetParameterValues_005f1);
      _jspx_th_request_005fgetParameterValues_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetParameterValues_005f1, _jsp_getInstanceManager(), _jspx_th_request_005fgetParameterValues_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f1 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f1_reused = false;
    try {
      _jspx_th_c_005fforEach_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(149,8) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f1.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(149,8) '${paramNameReloadsSort}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${paramNameReloadsSort}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(149,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f1.setVar("paramName");
      int[] _jspx_push_body_count_c_005fforEach_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f1 = _jspx_th_c_005fforEach_005f1.doStartTag();
        if (_jspx_eval_c_005fforEach_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("            options.paramNameReloadValue.push('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${paramName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("');\r\n");
            out.write("        ");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f1);
      _jspx_th_c_005fforEach_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(152,42) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setName("loadDateFacetParameter");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(152,42) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f1 = _jspx_th_config_005fgetOption_005f1.doStartTag();
        if (_jspx_th_config_005fgetOption_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f1);
      _jspx_th_config_005fgetOption_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetConstantValueFromNameTag_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getConstantValueFromNameTag
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag _jspx_th_plma_005fgetConstantValueFromNameTag_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag) _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag.class);
    boolean _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(153,38) name = constantName type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setConstantName("CLEAN_FEED_PARAMETER");
      int[] _jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetConstantValueFromNameTag_005f0 = _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doStartTag();
        if (_jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody.reuse(_jspx_th_plma_005fgetConstantValueFromNameTag_005f0);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetConstantValueFromNameTag_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetParameterValue_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getParameterValue
    com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag _jspx_th_request_005fgetParameterValue_005f0 = (com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag) _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag.class);
    boolean _jspx_th_request_005fgetParameterValue_005f0_reused = false;
    try {
      _jspx_th_request_005fgetParameterValue_005f0.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetParameterValue_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(155,29) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setName("reloadFacetPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(155,29) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setDefaultValue("1");
      int[] _jspx_push_body_count_request_005fgetParameterValue_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetParameterValue_005f0 = _jspx_th_request_005fgetParameterValue_005f0.doStartTag();
        if (_jspx_th_request_005fgetParameterValue_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetParameterValue_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetParameterValue_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetParameterValue_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_request_005fgetParameterValue_005f0);
      _jspx_th_request_005fgetParameterValue_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetParameterValue_005f0, _jsp_getInstanceManager(), _jspx_th_request_005fgetParameterValue_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f5 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f5_reused = false;
    try {
      _jspx_th_c_005fif_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/normalFilter.jsp(157,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.mode.equals('Event')}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f5 = _jspx_th_c_005fif_005f5.doStartTag();
      if (_jspx_eval_c_005fif_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            options.refineEventMode = true;\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f5);
      _jspx_th_c_005fif_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f5_reused);
    }
    return false;
  }

  private class Helper
      extends org.apache.jasper.runtime.JspFragmentHelper
  {
    private jakarta.servlet.jsp.tagext.JspTag _jspx_parent;
    private int[] _jspx_push_body_count;

    public Helper( int discriminator, jakarta.servlet.jsp.JspContext jspContext, jakarta.servlet.jsp.tagext.JspTag _jspx_parent, int[] _jspx_push_body_count ) {
      super( discriminator, jspContext, _jspx_parent );
      this._jspx_parent = _jspx_parent;
      this._jspx_push_body_count = _jspx_push_body_count;
    }
    public boolean invoke0( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("            ");
      if (_jspx_meth_render_005fparameter_005f0(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("            ");
      if (_jspx_meth_render_005fparameter_005f1(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("            ");
      if (_jspx_meth_render_005fparameter_005f2(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("            ");
      if (_jspx_meth_render_005fparameter_005f3(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("            ");
      if (_jspx_meth_render_005fparameter_005f4(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("            ");
      if (_jspx_meth_render_005fparameter_005f5(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("            ");
      if (_jspx_meth_render_005fparameter_005f6(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("        ");
      return false;
    }
    public boolean invoke1( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f7(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f8(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f9(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f10(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f11(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f12(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f13(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f14(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f15(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f16(_jspx_parent, _jspx_page_context))
        return true;
      out.write("\r\n");
      out.write("                    ");
      return false;
    }
    public void invoke( java.io.Writer writer )
      throws jakarta.servlet.jsp.JspException
    {
      jakarta.servlet.jsp.JspWriter out = null;
      if( writer != null ) {
        out = this.jspContext.pushBody(writer);
      } else {
        out = this.jspContext.getOut();
      }
      try {
        Object _jspx_saved_JspContext = this.jspContext.getELContext().getContext(jakarta.servlet.jsp.JspContext.class);
        this.jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,this.jspContext);
        switch( this.discriminator ) {
          case 0:
            invoke0( out );
            break;
          case 1:
            invoke1( out );
            break;
        }
        jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,_jspx_saved_JspContext);
      }
      catch( java.lang.Throwable e ) {
        if (e instanceof jakarta.servlet.jsp.SkipPageException)
            throw (jakarta.servlet.jsp.SkipPageException) e;
        throw new jakarta.servlet.jsp.JspException( e );
      }
      finally {
        if( writer != null ) {
          this.jspContext.popBody();
        }
      }
    }
  }
}
