<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>

<render:import varWidget="widget" varFeeds="feeds"/>
<render:import parameters="menuConfig,menuMode" ignore="true"/>

<c:if test="${empty menuConfig}">
    <plma:getConfig var="menuConfig" id="layout_menu" widget="${widget}"/>
</c:if>

<c:if test="${empty menuMode}">
    <c:set var="menuMode" value="widget"/>
</c:if>

<plma:getSavedPages var="savedPages"/>
<plma:getSharedPages var="sharedPages"/>
<plma:currentPageId var="currentPage" varPageName="templatePage"/>

<i18n:message code="widget.action.expand" var="tooltipExpand"/>
<i18n:message code="widget.action.collapse" var="tooltipCollapse"/>
<i18n:message code="widget.action.hide" var="tooltipHide"/>
<i18n:message code="widget.action.menu" var="tooltipMenu"/>

<request:getCookieValue name="sideBarMenu_displayState" var="displayState" defaultValue=""/>
<plma:getMenuItems varMainItems="mainItems" varSecondaryItems="secondaryItems"/>
<c:set var="displayDefault" value=""/>
<c:if test="${displayState == '' && menuConfig.display == 'Responsive'}">
    <c:set var="displayDefault" value="expanded"/>
</c:if>
<c:if test="${displayState == ''}">
    <c:set var="displayState" value="${fn:toLowerCase(menuConfig.display)}"/>
</c:if>

<c:set var="backgroundStyle"><c:if test="${not empty menuConfig.background}">background-color:${menuConfig.background};</c:if></c:set>
<widget:widget varUcssId="uCssId" extraCss="sideBarMenu ${displayState} ${displayDefault} mode-${menuMode}" disableStyles="true"
               extraStyles="${backgroundStyle}">

    <div class="side-bar-menu-header">
        <div class="search-item-container">
            <c:if test="${menuConfig.displaySearch}">
                <input type="text" class="search-item-input"
                       placeholder="<i18n:message code="widget.plmaMenu.search"/>"/>
                <i class="fonticon fonticon-search ${menuConfig.disableEditMode ? 'no-edit' : ''}"></i>
            </c:if>
        </div>

        <c:if test="${menuConfig.showUserMenu && !menuConfig.disableEditMode}">
            <div class="display-mode-edit fonticon fonticon-pencil"
                 title="<i18n:message code="widget.plmaMenu.edit"/>"></div>
        </c:if>
        <div class="display-mode-switch">
            <div class="display-mode-collapse ${menuConfig.collapseIcon}" title="${tooltipCollapse}"></div>
            <div class="display-mode-expand ${menuConfig.expandIcon}" title="${tooltipExpand}"></div>
        </div>
    </div>

    <div class="icon-tooltip hidden">
        <input class="icon-tooltip-input" />
        <plma:iconList var="iconList" filePath="" appName="${appName}" />
        <div class="fonticon-container">
            <c:forEach var="icon" items="${iconList}">
                <span class="fonticon-elem fonticon ${icon}"></span>
            </c:forEach>
        </div>
    </div>

    <div class="item-menu-container">
        <c:if test="${menuConfig.showUserMenu}">
            <c:set var="queryString" value=""/>
            <c:if test="${menuConfig.keepUserItemQueryString}">
                <url:url var="url" keepQueryString="true">
                    <url:parameter name="pageId" value="" override="true"/>
                    <url:parameter name="chartboardId" value="" override="true"/>
                </url:url>
                <string:split var="urlSplit" string="${url}" separator="?"/>
                <c:if test="${fn:length(urlSplit) > 1}">
                    <c:set var="queryString" value="${urlSplit[1]}"/>
                </c:if>
            </c:if>

            <c:if test="${fn:length(savedPages) > 0}">
                <div class="items user-items user-items-pages user-items-default">
                    <div class="user-section-container">
                        <i18n:message code="widget.plmaMenu.section.userItems" var="userSection" text="My pages"/>
                        <render:template template="templates/section.jsp">
                            <render:parameter name="section" value="${userSection}"/>
                            <render:parameter name="extraCss" value="user-section"/>
                        </render:template>

                            <%-- Loop over saved pages --%>
                        <c:forEach var="page" items="${savedPages}" varStatus="status">
                            <render:template template="templates/item.jsp">
                                <render:parameter name="uCssId" value="${uCssId}"/>
                                <render:parameter name="descCssId" value="saved-page-${page.id}"/>
                                <render:parameter name="pageId" value="${page.id}"/>
                                <render:parameter name="extraCss" value="isLink"/>
                                <render:parameter name="label" value="${page.label}"/>
                                <render:parameter name="description" value="${page.description}"/>
                                <render:parameter name="url" value="${page.template}?pageId=${page.id}&${page.params}"/>
                                <render:parameter name="iconCss" value="fonticon ${page.icon}"/>
                                <render:parameter name="type" value="saved-page"/>
                                <render:parameter name="extraCss" value="${itemCssId} editable-item ${currentPage == page.id ? 'active':''}"/>
                            </render:template>
                        </c:forEach>
                    </div>
                </div>
            </c:if>

            <c:if test="${fn:length(sharedPages) > 0}">
                <div class="items shared-items shared-items-pages user-items-default">
                    <div class="user-section-container">
                        <i18n:message code="widget.plmaMenu.section.userSharedItems" var="userSection" text="My shared pages"/>
                        <render:template template="templates/section.jsp">
                            <render:parameter name="section" value="${userSection}"/>
                            <render:parameter name="extraCss" value="user-section"/>
                        </render:template>

                            <%-- Loop over shared pages --%>
                        <c:forEach var="page" items="${sharedPages}" varStatus="status">
                            <render:template template="templates/item.jsp">
                                <render:parameter name="uCssId" value="${uCssId}"/>
                                <render:parameter name="descCssId" value="saved-page-${page.id}"/>
                                <render:parameter name="pageId" value="${page.id}"/>
                                <render:parameter name="extraCss" value="isLink"/>
                                <render:parameter name="label" value="${page.label}"/>
                                <render:parameter name="description" value="${page.description}"/>
                                <render:parameter name="url" value="${page.template}?pageId=${page.id}&${page.params}"/>
                                <render:parameter name="iconCss" value="fonticon ${page.icon}"/>
                                <render:parameter name="type" value="shared-page"/>
                                <render:parameter name="extraCss" value="${itemCssId} ${currentPage == page.id ? 'active':''}"/>
                            </render:template>
                        </c:forEach>
                    </div>
                </div>
            </c:if>

        </c:if>

        <div class="items main-items">
            <c:if test="${showUserMenu == 'true'}">
                <render:template template="templates/section.jsp">
                    <render:parameter name="extraCss" value="user-separation-section"/>
                </render:template>
            </c:if>
            <render:template template="templates/items.jsp">
                <render:parameter name="items" value="${mainItems}"/>
                <render:parameter name="uCssId" value="${uCssId}"/>
                <render:parameter name="type" value="main"/>
            </render:template>
        </div>
    </div>

    <%-- Generates descriptions div to simplify display (not managed in a complex way using javascript) --%>
    <div class="item-description-container">
        <c:if test="${menuConfig.showDescription}">
            <c:if test="${menuConfig.showUserMenu}">
                <%-- Loop over saved pages --%>
                <c:forEach var="page" items="${savedPages}" varStatus="status">
                    <div class="item-description hidden" id="saved-page-${page.id}">
                        <div class="description-title">${page.label}</div>
                        <div class="description-text">${page.description}</div>
                    </div>
                </c:forEach>
                <c:forEach var="page" items="${sharedPages}" varStatus="status">
                    <div class="item-description hidden" id="saved-page-${page.id}">
                        <div class="description-title">${page.label}</div>
                        <div class="description-text">${page.description}</div>
                    </div>
                </c:forEach>
            </c:if>
            <c:forEach var="mainItem" items="${mainItems}" varStatus="itemStatus">
                <render:template template="templates/description.jsp">
                    <render:parameter name="item" value="${mainItem}"/>
                    <render:parameter name="type" value="main-desc"/>
                    <render:parameter name="id" value="${itemStatus.index}"/>
                </render:template>
            </c:forEach>
            <c:forEach var="secondaryItem" items="${secondaryItems}" varStatus="itemStatus">
                <render:template template="templates/description.jsp">
                    <render:parameter name="item" value="${secondaryItem}"/>
                    <render:parameter name="type" value="secondary-desc"/>
                    <render:parameter name="id" value="${itemStatus.index}"/>
                </render:template>
            </c:forEach>
        </c:if>
    </div>

    <div class="subwidgets">
        <render:subWidgets/>
    </div>
    <div class="secondary-items">
        <render:template template="templates/items.jsp">
            <render:parameter name="items" value="${secondaryItems}"/>
            <render:parameter name="uCssId" value="${uCssId}"/>
            <render:parameter name="type" value="secondary"/>
        </render:template>
    </div>
</widget:widget>

<render:renderScript position="READY">
    new PLMAMenu('${uCssId}', {
        display: '${display}',
        displayState: '${displayState}',
        sidebarCookie : ${menuConfig.sidebarCookie},
        currentPage: '${currentPage}',
        url: '<c:url value="/savedPages"/>',
        pageBaseUrl: '<c:url value="/page"/>',
        baseAjaxReload: '<c:url value="/plma/ajax/menu"/>/<search:getPageName/>/${menuMode}'
    });
    <%--    new EditMenu('${uCssId}');--%>
</render:renderScript>
