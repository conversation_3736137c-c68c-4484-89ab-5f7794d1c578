@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/responsive.less";
@import "../../plmaResources/css/styles/colorPreferences.less";

.mashup {
	.wuid.note {
		height: 100%;
		background: @cblock-bg-alt;
		margin: 10px;
		&.dragging {
			.widgetContent {
				.action-container {
					border-color: @clink-active;
					.add-note {
						display: none;
					}
					.delete-note {
						width: 100%;
						height: 100%;
						display: flex;
						justify-content: center;
						align-items: center;
						.trash-icon {
							font-size: 18px;
							border: 1px solid @cblock-border;
							padding: 5px;
							border-radius: 30px;
						}
					}
				}
			}
		}
		.widgetContent {
			background: transparent;
			border: none;
			padding: 20px;
			height: 100%;
			.title {
				font-size: 18px;
				color: @clink-active;
			}
			.action-container {
				margin-top: 10px;
				margin-bottom: 20px;
				border: 1px dashed @cblock-border;
				background-color: @cbody-bg;
				height: 40px;
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				.grid-stack-item {
					display: flex;
					align-items: center;
				}
				.custom-widget-title {
					font-size: 15px;
					color: @cblock-border-alt;
				}
				.editable-custom-widget-icon {
					display: none;
				}
				.delete-note {
					display: none;
				}
			}
			.note-layout {
				.custom-note {
					.tile {
						border: 1px solid @cblock-border;
						background-color: white;
						box-shadow: 0 2px 4px 0 @cblock-border;
						padding: 10px;
						cursor: pointer;
						.editable-custom-widget-icon {
							position: absolute;
							top: 3px;
							right: 3px;
						}
					}
				}
			}
			.chartboard-popup-tinymce {
				display: none;
				&.visible {
					.display-flex();
				}
				position: absolute;
				top: 0;
				left: 0;
				background-color: rgba(0, 0, 0, 0.3);
				width: 100%;
				height: 100%;
				z-index: 10;
				align-items: center;
				.chartboard-popup-tinymce-popup {
					margin-left: 20%;
					width: 60%;
					height: 60%;
					.display-flex();
					background-color: white;
					.mce-i-add-signature {
						font-family: entypo;
						font-size: 22px;
					}
					.chartboard-tinymce-container {
						.flex(4 0 0);
						flex-grow: 4;
						flex-basis: 0;
						border: 1px solid @cblock-border;
						.display-flex();
						flex-direction: column;
						.chartboard-tinymce-title {
							height: 20px;
							font-size: 28px;
							margin-left: 15px;
							margin-top: 15px;
							margin-bottom: 30px;
							.mce-save-button {
								font-family: entypo;
								float: right;
								cursor: pointer;
								font-size: 28px;
							}
							.mce-close-button {
								font-family: entypo;
								float: right;
								cursor: pointer;
								font-size: 28px;
							}
						}
						.chartboard-tinymce-editor-container {
							width: 90%;
							left: 5%;
							position: relative;
							.flex(1 0 0);
							flex-grow: 1;
						}
					}
				}
			}
			.mce-tinymce {
				z-index: 10;
				height: 95%;
				.mce-container-body.mce-stack-layout {
					height: 100%;
					display: flex;
					flex-direction: column;
					.mce-edit-area {
						flex-grow: 1;
					}
					.mce-toolbar-grp {
						.mce-stack-layout {
							width: 100%;
							left: 0;
						}
					}
				}
				.mce-edit-area {
					.display-flex();
					flex-direction: column;
					iframe {
						.flex(1 0 0);
						flex-grow: 1;
					}
				}
			}
		}
	}
}
