<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds" varParentEntry="parentEntry" varParentFeed="parentFeed"/>
<config:getOption var="hasHitDetails" name="hasHitDetails" defaultValue="true"/>
<config:getOption var="hasReloadContainer" name="hasReloadContainer" defaultValue="true"/>
<config:getOption name="resultListId" var="resultListId"/>
<config:getOption name="buttonsIconSize" var="buttonsIconSize" defaultValue=""/>
<config:getOption name="defaultView" var="defaultView" defaultValue="tile"/>

<config:getOption name="ajaxSort" var="ajaxSort" defaultValue="false"/>
<config:getOption var="enableExport" name="enableExport" defaultValue="false"/>
<config:getOption var="customExportJSP" name="customExportJSP" defaultValue="templates/exportDisplay.jsp"/>
<config:getOptions var="channelNames" name="channels"/>
<config:getOption var="enablePublication" name="enablePublication" defaultValue="false"/>
<config:getOption var="enableSubscription" name="enableSubscription" defaultValue="false"/>
<config:getOptionsComposite var="additionalVariables" name="variables" mapIndex="true" />

<plma:resultListConfig var="columnsConfig" resultListId="${resultListId}" toMap="false"/>

<request:getParameterValue var="displayView" name="displayView" defaultValue="${defaultView}"/>
<list:new var="excludeList"/>
<list:add value="plmaResultTiles" list="${excludeList}"/>
<list:add value="plmaResultGridTable" list="${excludeList}"/>
<list:add value="plmaResultCompare" list="${excludeList}"/>

<widget:widget disableStyles="true" varCssId="cssId" varUcssId="uCssId" extraCss="flexResultListContainer result-detail-container" extraStyles="${plma:getCssStyles(additionalVariables)}">

    <c:set var="layout" value="${widget.layout}"/>
    <c:set var="widthFormat" value="${layout.widthFormat}"/>
    <c:if test="${widthFormat == null || widthFormat == ''}">
        <c:set var="widthFormat" value="%"/>
    </c:if>

    <c:forEach var="table" varStatus="tableStatus" items="${layout.tables}">
        <c:forEach var="row" items="${table.rows}" varStatus="rowStatus">
            <config:getOption var="rowId" name="cssId" component="${row}" defaultValue="${cssId}_row_${rowStatus.index}"/>
            <config:getOption var="rowCssClass" name="cssClass" component="${row}" defaultValue=""/>

            <map:new var="colWidth"/>
            <c:forEach var="colConfig" items="${table.colsConfig}" varStatus="colStatus">
                <map:put key="${colStatus.index}" value="${colConfig.width}" map="${colWidth}"/>
            </c:forEach>

            <div id="${rowId}"
                 class="flexPanelsContainer-row flexPanelsContainer-row-${tableStatus.index}-${rowStatus.index} ${not empty rowCssClass ? rowCssClass : ''} ${wrap == 'true' ? 'wrap-panels' : ''}" style="${plma:format('--icons-size:%spx;', buttonsIconSize, '')}">
                <c:forEach varStatus="statusCell" var="cell" items="${row.cells}">
                    <config:getOption var="cellId" name="cssId" component="${cell}" defaultValue="${rowId}_cell_${statusCell.index}"/>
                    <config:getOption var="cellCssClass" name="cssClass" component="${cell}" defaultValue=""/>

                    <c:choose>
                        <c:when test="${statusCell.first}">
                            <%-- In case of first column: consider it as PLMA menu container--%>
                            <c:set var="flexColCSS" value="result-panel"/>
                        </c:when>
                        <c:when test="${statusCell.index == 1 && hasHitDetails == 'true'}">
                            <%-- In case of second column: consider it as PLMA hit Details container--%>
                            <c:set var="flexColCSS" value="detail-panel collapsiblePanel hidden"/>
                        </c:when>
                        <c:when test="${statusCell.index == 2 && hasReloadContainer == 'true'}">
                            <%-- In case of third column: consider it as PLMA Reload container--%>
                            <c:set var="flexColCSS" value="selection-panel collapsiblePanel hidden"/>
                        </c:when>
                        <c:otherwise>
                            <%-- Otherwise simply consider main container(s) --%>
                            <c:set var="flexColCSS" value=""/>
                        </c:otherwise>
                    </c:choose>

                    <plma:getFlexPanelCSSClassesTag var="hidePanelClass" flexContainerCssClasses="${cellCssClass}" cookie="${panelManagementCookie}"/>
                    <div id="${cellId}"
                         class="flexPanelsContainer-item flexPanelsContainer-col-${colWidth[statusCell.index]} flexPanelsContainer-item-${tableStatus.index}-${rowStatus.index}-${statusCell.index}<c:if test="${hidePanelClass != null && hidePanelClass != ''}"> ${hidePanelClass}</c:if> ${flexColCSS}">
                            <%--  --%>
                        <c:if test="${plma:getBooleanParam(widget,'displayHeader',true) && statusCell.first}">
                            <render:template template="templates/resultListHeader.jsp" widget="plmaResultListCommons">
                                <render:parameter name="feeds" value="${feeds}"/>
                                <render:parameter name="columnsConfig" value="${columnsConfig}"/>
                                <render:parameter name="wuid" value="${uCssId}"/>
                                <render:parameter name="layoutName" value="tile-layout"/>
                                <render:parameter name="recursiveButtonsWidgets" value="headerButtonsContainer"/>
                                <render:parameter name="directButtonsWidgets" value="plmaButtonsContainer,plmaButton"/>
                                <render:parameter name="buttonsIconSize" value="${buttonsIconSize}"/>
                                <render:parameter name="defaultView" value="${defaultView}"/>
                                <render:parameter name="parentFeed" value="${parentFeed}"/>
                                <render:parameter name="parentEntry" value="${parentEntry}"/>
                                <%-- Pass header container which contains export options as parameter to avoid passing all parameters to template --%>
                                <render:parameter name="headerContainer" value="${widget}"/>
                                <%-- Parameters for JS creation  --%>
                                <render:parameter name="ajaxSort" value="${ajaxSort}"/>
                                <render:parameter name="enableExport" value="${enableExport}"/>
                                <render:parameter name="channelNames" value="${channelNames}"/>
                                <render:parameter name="enablePublication" value="${enablePublication}"/>
                                <render:parameter name="enableSubscription" value="${enableSubscription}"/>
                            </render:template>
                        </c:if>

                        <c:choose>
                            <c:when test="${statusCell.first}">
                                <div class="flexResultListViewPanel">
                                    <c:choose>
                                        <c:when test="${plma:hasSubWidgetWithParam(cell, 'displayViewId', displayView)}">
                                            <plma:forEachSubWidgetWithParam param="displayViewId" value="${displayView}" widgetContainer="${cell}"
                                                                            feed="${parentFeed}" entry="${parentEntry}">
                                                <%-- Render view subwidget --%>
                                                <render:widget/>
                                            </plma:forEachSubWidgetWithParam>
                                        </c:when>
                                        <c:otherwise>
                                            <div class="display-view-warning">No result list view '${displayView}' available...</div>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <plma:forEachSubWidget excludes="headerButtonsContainer,plmaButtonsContainer,plmaButton" widgetContainer="${cell}"
                                                       feed="${parentFeed}" entry="${parentEntry}">
                                    <%-- Render actions subwidgets in header --%>
                                    <render:widget/>
                                </plma:forEachSubWidget>
                            </c:otherwise>
                        </c:choose>

                            <%--
                            <widget:forEachSubWidget widgetContainer="${cell}" feed="${parentFeed}" entry="${parentEntry}">
                                <render:widget />
                            </widget:forEachSubWidget>
                            --%>
                    </div>
                </c:forEach>
            </div>
        </c:forEach>
    </c:forEach>

    <%-- --%>

    <render:renderScript position="READY">
        new UrlHitStelector('${uCssId}', {});
    </render:renderScript>

    <render:renderLater>
        <style type="text/css">
            <c:forEach var="table" varStatus="tableStatus" items="${layout.tables}">
                <c:forEach var="colConfig" items="${table.colsConfig}" varStatus="colStatus">
                .${uCssId} > .flexPanelsContainer-row > .flexPanelsContainer-col-${colStatus.index} {
                    flex-grow: ${colConfig.width};
                    flex-basis: 0;
                }

                </c:forEach>
            </c:forEach>
        </style>
    </render:renderLater>

</widget:widget>
