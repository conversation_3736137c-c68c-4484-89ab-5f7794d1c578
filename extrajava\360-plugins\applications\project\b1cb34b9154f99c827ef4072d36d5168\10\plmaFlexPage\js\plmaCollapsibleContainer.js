var PLMACollapsibleContainer = function (uCssId, options) {
    'use strict';

    var defaults = {
        animationDuration: 200
    };

    this.options = $.extend({}, defaults, options);
    this.uCssId = uCssId;

    if (uCssId) {
        this.widget = $('.' + uCssId);
    } else {
        this.widget = $();
    }

    if (this.widget.length === 0) {
        throw new Error('Unable to initialize widget PLMACollapsibleContainer : widget not found (uCssId: "' + uCssId + '").');
    } else {
        this.init();
    }
};

PLMACollapsibleContainer.REFINES_CONTAINER_COOKIE_NAME = "refinesContainer_displayState";

PLMACollapsibleContainer.prototype.init = function () {
    'use strict';
    this.refinesContainerCollapse = this.widget.find('.refinesContainerCollapse');
    this.displayModeExpand = this.widget.find('.refinesContainerExtend');

    this.refinesContainerCollapse.on('click', $.proxy(function () {
        console.info('Collapse refine container');
        this.collapseContainer();
    }, this));

    this.displayModeExpand.on('click', $.proxy(function () {
        console.info('Expand refine container');
        this.expandContainer();
    }, this));

    //Close panel from other widget
    $(document).on('plma:close-refine-panel', $.proxy(function (event, data) {
        this.collapseContainer();
    }, this));
};

PLMACollapsibleContainer.prototype.collapseContainer = function () {
    this.widget.find('.refinesContainer').addClass('state-collapsed').removeClass('state-expanded');
    $.cookie(PLMACollapsibleContainer.REFINES_CONTAINER_COOKIE_NAME, 'collapsed');
};

PLMACollapsibleContainer.prototype.expandContainer = function () {
    this.widget.find('.refinesContainer').addClass('state-expanded').removeClass('state-collapsed');
    $.cookie(PLMACollapsibleContainer.REFINES_CONTAINER_COOKIE_NAME, 'expanded');
    $(window).trigger('plma:resize', [0]);
    /* To resize highcharts */
};