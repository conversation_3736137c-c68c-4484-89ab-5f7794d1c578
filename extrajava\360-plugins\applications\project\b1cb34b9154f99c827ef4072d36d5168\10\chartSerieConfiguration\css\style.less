@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/styles/responsive.less";
@import "../../plmaResources/css/styles/variables.less";


.plmaLightboxWidgetWrapper{
    .display-flex();
}

.plmaChartSerieConfigurator.searchWidget{
      height: 80vh;
  .widgetContent{
      .display-flex();
      .flex-direction(column);
      border:none;
	  height: 100%;
      a{
        cursor:pointer;
      }

      .title{
          .display-flex();
          max-height:60px;
          .flex-grow(1);
          .flex-shrink(0);
          .flex-basis(60px);
          .justify-content(center);

        .title-text{
            flex-grow: 1;
            .align-items(center);
            padding: 25px;
            font-size: @m-font;
            border: 1px solid @ctext-weaker;
        }
      }

      .widgetContent-container{
          overflow-y: auto;
          .display-flex();
          .flex-direction(column);
      }

      .container{
          .display-flex();
          padding:15px;
          .flex-grow(1);
          .flex-shrink(0);
          .flex-basis(auto);

          .series-container{
			  .label-container {
				  .display-flex();
				  margin-right: 15px;
				  .column-label-series {
					  flex-grow: 1;
				  }
				  .column-label-representation {
					  flex-grow: 0;
					  flex-basis: 121px;
				  }
			  }
              ul.series-list{
                  .display-flex();
                  .flex-direction(column);
                  li.serie{
                      padding-top: 12px;
                      padding-bottom: 12px;
					  .display-flex();
					  margin-right: 15px;
					  height: 32px;
                      .plma-checkbox-container {
                          display: inline-block;
						  padding-top: 5px;
						  flex-grow: 1;
                      }

                      .representation-container{
                          padding-bottom: 5px;
                      }

                      &.hidden {
                          display: none;
                      }
                  }
              }
          }

          .operator-container, .representation-container{
			  .label-container {
				  .display-flex();
				  .column-label {
					  flex-grow: 1;
				  }
			  }
              select {
                  overflow: hidden;
                  background-color: white;
                  color: @ctext-bold;
                  line-height: 24px;
                  padding-right: 14px;
                  padding-left: 14px;
                  border: 1px solid @ctext-weak;
                  border-radius: 4px;
                  padding-top: 1px;
                  padding-bottom: 1px;
                  margin-top: 5px;
              }
          }

          ul.operator-series-list{
              li.serie.hidden{
                  display: none;
              }
              li.serie{
                  padding-top: 12px;
                  padding-bottom: 12px;
                  .display-flex();
				  label {
					  margin-top: 5px;
					  margin-left: 10px;
					  color: white;
					  width: 0;
				  }
              }
          }
      }

      .option-container {
          .display-flex();
          .flex-grow(1);
          .flex-shrink(0);
          .flex-basis(auto);
		  .legend-container{
			  flex: 1;
			  .display-flex();
			  padding: 15px;
			  .flex-direction(column);

			  .plma-checkbox-container{
				  padding-top: 12px;
				  padding-bottom: 12px;
				  width: auto;
			  }

		  }
		  .stacking-container {
			  flex: 1;
			  padding: 15px;
			  .label {
				  padding-bottom: 12px;
			  }
			  select {
				  overflow: hidden;
				  background-color: white;
				  color: @ctext-bold;
				  line-height: 24px;
				  padding-right: 14px;
				  padding-left: 14px;
				  border: 1px solid @ctext-weak;
				  border-radius: 4px;
                  padding-top: 1px;
                  padding-bottom: 1px;
			  }
		  }
      }



      .annotation-container{
          padding:15px;
          padding-top: 0;
          .display-flex();
          .flex-direction(column);
          .flex-grow(1);
          .flex-shrink(0);
          .flex-basis(auto);
          .annotation-zone{
              textarea{
                width:100%;
              }
          }
      }

      .action-container{
          padding-right: 15px;
          padding-bottom: 15px;
          padding-left: 15px;
          .display-flex();
          .flex-grow(1);
          .flex-shrink(0);
          .flex-basis(auto);
          .justify-content(flex-end);
		  .restore-btn {
			  margin-right: 15px;
		  }
      }
  }
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    /* Hack to display all the content in IE */
    .plmaChartSerieConfigurator.searchWidget {
        max-height: 80vh;
    }
}