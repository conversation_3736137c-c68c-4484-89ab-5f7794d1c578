/**
 * Regression analysis chart
 */
var RegressionAnalysisChart = function (uCssId, options) {
	this.chartId = 'chart_' + uCssId;
	this.container = $('#' + this.chartId);
	this.chart = Highcharts.chart(this.chartId, options);
}

/**
 * Returns regression outputs (equation, correlation...).
 */
RegressionAnalysisChart.prototype.getRegressionOutputs = function () {
	var series = this.chart.series[0];
	if (series) {
		return series.options.regressionOutputs;
	}
}

/**
 * Adds a series to the chart.
 */
RegressionAnalysisChart.prototype.addSeries = function (series) {
	this.chart.addSeries(series);
}

/**
 * Removes all series.
 */
RegressionAnalysisChart.prototype.removeAllSeries = function () {
	// clean axis
	this.updateAxisTitle('x', '');
	this.updateAxisTitle('y', '');
	
	while (this.chart.series.length > 0) {
		this.chart.series[0].remove(true);
	}
}

/**
 * Returns the series.
 */
RegressionAnalysisChart.prototype.getSeries = function () {
	return this.chart.series;
}

/**
 * Sets axis title.
 */
RegressionAnalysisChart.prototype.updateAxisTitle = function (axis, title) {
	$.each(this.chart.series, function (index, series) {
		if (axis === 'x') {
			series.xAxis.setTitle({
				text: title
			});
		} else {
			series.yAxis.setTitle({
				text: title
			});
		}
	});
}

/**
 * If there are more than 2 series (1 for regression curve, 1 for data, others for predictions),
 * then removes the series for predictions.
 */
RegressionAnalysisChart.prototype.removePredictions = function () {
	var series = this.chart.series;
	while (series.length > 2) {
		series[series.length - 1].remove(true);
	}
}

/**
 * Updates the axis type.
 */
RegressionAnalysisChart.prototype.updateAxisType = function (axis, type) {
	var options = {};
	var typeOption = {
		type: type,
	};
	if (axis === 'x') {
		options.xAxis = typeOption;
	} else {
		options.yAxis = typeOption;
	}
	this.updateChart(options);
}

/**
 * Updates the tooltip to be compliant with dates.
 */
RegressionAnalysisChart.prototype.updateTooltipForDate = function (axis, showDate, dateFormat) {
	var tooltipFormat = this.chart.options.tooltip.pointFormat;
	var VARIABLE_X = '{point.x}';
	var VARIABLE_Y = '{point.y}';
	var VARIABLE_X_AS_DATE = '{point.x:' + dateFormat + '}';
	var VARIABLE_Y_AS_DATE = '{point.y:' + dateFormat + '}';
	var newTooltipFormat;
	if (axis === 'x') {
		newTooltipFormat = showDate
			? tooltipFormat.replace(VARIABLE_X, VARIABLE_X_AS_DATE)
			: tooltipFormat.replace(VARIABLE_X_AS_DATE, VARIABLE_X);
	} else {
		newTooltipFormat = showDate
			? tooltipFormat.replace(VARIABLE_Y, VARIABLE_Y_AS_DATE)
			: tooltipFormat.replace(VARIABLE_Y_AS_DATE, VARIABLE_Y);
	}
	this.updateChart({
		tooltip: {
			pointFormat: newTooltipFormat
		}
	});
}

/**
 * Change the axis type and the tooltip format to be compliant with dates.
 */
RegressionAnalysisChart.prototype.updateForDate = function (axis, isDate, tooltipDateFormat) {
	this.updateAxisType(axis, isDate ? 'datetime' : undefined);
	this.updateTooltipForDate(axis, isDate, tooltipDateFormat);
}

/**
 * Updates the chart with the given options.
 */
RegressionAnalysisChart.prototype.updateChart = function (options) {
	this.chart.update(options);
}

/**
 * Destroys the chart.
 */
RegressionAnalysisChart.prototype.destroy = function() {
	this.chart.destroy();
}