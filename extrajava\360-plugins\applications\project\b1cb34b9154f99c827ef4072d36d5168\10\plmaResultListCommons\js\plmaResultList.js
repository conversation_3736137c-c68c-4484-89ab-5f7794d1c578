PlmaResultList.prototype = new SelectionWidget();
PlmaResultList.prototype.constructor = PlmaResultList;

function PlmaResultList(uCssId, options) {
	if (uCssId) {
		this.widget = $('.' + uCssId);
		this.widget.data('widget', this);
	} else {
		this.widget = $();
	}
	this.sortContainer = this.widget.find('.sortContainer');
	this.uCssId = uCssId;
	this.options = options;
	this.setSelectionConfig(this.options.selectionConfig);
	if (this.widget.length === 0) {
		throw new Error('Unable to initialize widget custom hit list : widget not found (uCssId: "' + uCssId + '").');
	} else {
		this.init();
	}
};

PlmaResultList.DESCRIPTION_COOKIE_NAME = "description_result_list";
PlmaResultList.SORT_DESC = 's=desc';
PlmaResultList.SORT_ASC = 's=asc';
/**
 * Current URL params
 * @type {object}
 */
PlmaResultList._PARAMS = {};

PlmaResultList.prototype.init = function () {
	this.resultsPanel = this.widget.find('.results-panel');
	this.hitList = this.resultsPanel.find('.scroll-container > .hits');

	this.initHeight();
	this.initLayout();
	this.initSort();
	this.initDescription();
	//this.initColorWithOpacity();
	this.intCategoryLinks();
	if(this.options.enableExport){
        this.initExportData();
    }
};

PlmaResultList.prototype.initExportData = function (){
	var headerExportButton = this.widget.find('.plmaResultListExport');
	var controllerExportForm = this.widget.find('.export-data-form');
	headerExportButton.off('click').on('click', function () {
		controllerExportForm.submit();
	}.bind(this));
}

PlmaResultList.prototype.intCategoryLinks = function () {
	this.widget.find('.hits .hit .hitFacet .category-link').on('click', function (e) {
        // Disable opening of Hit Details panel.
		e.stopImmediatePropagation();
		e.stopPropagation();
	});
};

PlmaResultList.prototype.initColorWithOpacity = function () {
	this.widget.find('.hit-container .hitIcon').each(function (i, e) {
        hitWidgetUtils.initColorWithOpacity($(e));
	});
};

PlmaResultList.prototype.initDescription = function () {
	this.widget.find('.display-description-button').on('click', $.proxy(function () {
		this.toggleDescription();
	}, this));
};

PlmaResultList.prototype.toggleDescription = function () {
	this.widget.find('.display-description-button span').toggleClass('hidden');
	this.widget.find('.hitContent').toggleClass('hidden');
	var cookie = !this.widget.find('.display-description-button .icon-visible').hasClass('hidden');
	$.cookie(PlmaResultList.DESCRIPTION_COOKIE_NAME + this.uCssId, JSON.stringify(cookie), {path: '/', expires: 30});
};

PlmaResultList.prototype.initHeight = function () {
	if (!this.widget.height()) {
		this.widget.height(800);
	}
};

PlmaResultList.prototype.initLayout = function () {
	this.layoutButtons = this.widget.find('.icon-button-template');
	this.layoutNames = [];
	this.layoutButtons.each($.proxy(function (i, e) {
		this.layoutNames.push('layout-' + $(e).data('layout'));
	}, this));

	this.layoutButtons.on('click', $.proxy(function (e) {
		var button = $(e.target).closest('.icon-button-template');
		this.selectLayout(button.data('layout'));
	}, this));

	var selected = this.getCookie();
	if (selected) {
		this.selectLayout(selected);
	}

	if (this.widget.find('.hits').hasClass('responsive-layout')) {
		var helper = new DetailHitHelper(true);
		helper.initResponsive(this.uCssId);
	}
};

PlmaResultList.prototype.selectLayout = function (layoutName) {
	var button = this.layoutButtons.filter('.button-layout-' + layoutName);
	if (button.length > 0) {
		this.layoutButtons.removeClass('selected');
		button.addClass('selected');
		this.hitList
			.removeClass(this.layoutNames.join(' '))
			.addClass('layout-' + button.data('layout'));
		this.setCookie(button.data('layout'));
		$(window).trigger('resize'); //To trigger infinite scroll
	}
};

PlmaResultList.prototype.initSort = function () {
    var articleView = this.widget.find('.resultsTitle').hasClass('article-view');

    /* display sort on column */
    this.displayColumnSort(articleView);
	
	if(this.sortContainer.find('.sortOption.activeSort').length > 0){
		this.sortContainer.find('.sort-dropdown').addClass('hasSort');
	}else{
		this.sortContainer.find('.sort-dropdown').removeClass('hasSort');
	}
	this.sortContainer.find('.action-sorting').on('click', function () {
		$(this).toggleClass('active');
	});
	this.sortContainer.find('.sortOption .label').on('click', $.proxy(function (e) {
		this.sortSearch($(e.target).closest('.sortOption'));
		this.pushState(null, null);
	}, this));

	if(!articleView){
        /* sort click action on chevron fonticon click */
        this.sortTileClickActions();
        this.sortTableClickActions();
    }else{
        this.sortTileClickActions();
        /* sort action on column click */
        this.sortTableOnColumnAction();
    }
};

PlmaResultList.prototype.displayColumnSort = function(articleView){
    this.sortContainer.find('.sortOption').each($.proxy(function (i, e) {
        /* Add buttons on columns */
        var column = this.widget.find(".table-header .column[data-column-name='" + $(e).data('columnName') + "']");
        column.addClass("sortable-column");
        var context = {
            sorturl: $(e).data('sorturl'), // asc by default
            sortActive: $(e).data('sortActive')
        };
        var descContext = $.extend({}, context, { sorturl: $(e).data('descsorturl') });
        if (column.length > 0) {
            /* on article view chevron are hidden */
            var chevronCssClass = articleView?' hidden':'';
            column.append($('<span/>', {
                'class': 'fonticon fonticon-expand-down icon-sort-desc' + ($(e).data('sortActive') === 'ordered_desc' ? ' selected' : chevronCssClass),
                title: mashupI18N.get('plmaResultList', 'hitcustom.sort_dsc')
            }).data(descContext));
            column.append($('<span/>', {
                'class': 'fonticon fonticon-expand-up icon-sort-asc' + ($(e).data('sortActive') === 'ordered_asc' ? ' selected' : chevronCssClass),
                title: mashupI18N.get('plmaResultList', 'hitcustom.sort_asc')
            }).data(context));
        }
        /* Add data on buttons */
		$(e).find('.sortLinkOrder')
			.find('.icon-sort-desc')
				.data(descContext)
			.end()
			.find('.icon-sort-asc')
				.data(context);
    }, this));
};

PlmaResultList.prototype.sortTileClickActions = function(){
    this.sortContainer.find('.sortLinkOrder .icon-sort-asc').on('click', this.onSortClick.bind(this));
    this.sortContainer.find('.sortLinkOrder .icon-sort-desc').on('click', this.onSortClick.bind(this));
};

PlmaResultList.prototype.sortTableClickActions = function(){
    this.widget.find('.table-header .column .icon-sort-asc').on('click', this.onSortClick.bind(this));
    this.widget.find('.table-header .column .icon-sort-desc').on('click', this.onSortClick.bind(this));
};

PlmaResultList.prototype.sortTableOnColumnAction = function(){
    this.widget.find('.table-header .column.sortable-column').on('click', this.onColumnClick.bind(this));
};

PlmaResultList.prototype.getSortClickUrl = function($target, currentSort, sortUrl){
	var finalUrl = '';
    if ($target.hasClass('icon-sort-asc')) {
        finalUrl = currentSort === 'ordered_asc'
            ? this.removeSortParameter(sortUrl) /* Disable sort if already sorted */
            : sortUrl;
    } else if ($target.hasClass('icon-sort-desc')) {
        finalUrl = currentSort === 'ordered_desc'
            ? this.removeSortParameter(sortUrl) /* Disable sort if already sorted */
            : sortUrl;
    }
	
    return finalUrl;
};

PlmaResultList.prototype.getCustomSortClickUrl = function($target, sortUrl){
    if ($target.hasClass('icon-sort-desc') && $target.hasClass('selected')) {
        sortUrl = this.removeSortParameter(sortUrl);
    }
    return sortUrl;
};

PlmaResultList.prototype.onSortClick = function(e) {
	var $target = $(e.target);
	var sortUrl = $target.data('sorturl');
	var currentSort = $target.data('sortActive');

	sortUrl = this.getSortClickUrl($target, currentSort, sortUrl);
    this.renderSort(sortUrl);
};

PlmaResultList.prototype.onColumnClick = function(e){
    var $target = $(e.currentTarget).find(".fonticon:not('.icon').selected");
    if($target.length === 0){
        //if no sort selected get asc
        $target = $(e.currentTarget).find(".fonticon:not('.icon').icon-sort-asc");
    }
    var sortUrl = $target.data('sorturl');
    sortUrl = this.getCustomSortClickUrl($target, sortUrl);
    this.renderSort(sortUrl);
};

PlmaResultList.prototype.renderSort = function(finalUrl){
	if (!this.options.ajaxSorting) {
        window.location.assign(finalUrl);
    } else {
        this.refreshWidgetWithSort(finalUrl);
    }
};

PlmaResultList.prototype.removeSortParameter = function(sortUrl) {
	var url = new BuildUrl(sortUrl);
	this.options.feedsName.forEach(function(feedName) {
		url.removeParameter(feedName + '.s');
	});
	return url.toString();
};

PlmaResultList.prototype.refreshWidgetWithSort = function(sortUrl){
    var client = new PlmaAjaxClient($('.'+this.uCssId));
    client.addWidget(this.uCssId);
    var parameters = new BuildUrl(sortUrl);
    PlmaResultList._PARAMS = parameters.params;
    $.each(parameters.params, function(key){
        client.addParameters(key, parameters.params[key]);
	});
  	client.update();
};

PlmaResultList.prototype.sortSearch = function ($sortOption) {
	var isAsc = $sortOption.find('.sortLinkOrder .icon-sort-asc.selected').length > 0;
	if(isAsc) {
		$sortOption.find('.sortLinkOrder .icon-sort-desc').trigger('click');
	}else{ // Sort asc if desc or no sorting on current option.
		$sortOption.find('.sortLinkOrder .icon-sort-asc').trigger('click');
	}
};

PlmaResultList.prototype.pushState = function () {
	//We push the state in the browser history
	if (!(typeof history.pushState === 'undefined')) {
		var stateObj = {};
		window.history.pushState(stateObj, "myPage", document.location.href);
	}
};

PlmaResultList.prototype.onHitsSelect = function (selectedHits) {
	if (selectedHits && selectedHits.length === 1) {
		this.hitList.find('.hit[data-hit-id="' + selectedHits[0] + '"]').trigger('click', [{preventPublish: true}]);
	}
};


(function () {
	/* Cookie management */
	var cookieKey = 'plmaResultHits_layout';

	PlmaResultList.prototype.getCookie = function () {
		var cookies = document.cookie.split(';');
		for (var i = 0; i < cookies.length; i++) {
			var keyValue = cookies[i].split('=');
			if (keyValue.length > 1 && keyValue[0].trim() === cookieKey) {
				return keyValue[1];
			}
		}
	};

	PlmaResultList.prototype.setCookie = function (value) {
		document.cookie = cookieKey + '=' + value;
	}
})();
