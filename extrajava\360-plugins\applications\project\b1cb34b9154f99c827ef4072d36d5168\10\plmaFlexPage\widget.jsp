<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>

<render:import varWidget="widget" varParentEntry="parentEntry" varParentFeed="parentFeed" />
<config:getOption var="wrap" name="wrap" defaultValue="false"/>
<config:getOption var="hasRefinePanel" name="hasRefinePanel" defaultValue="true"/>
<config:getOption var="displayHideButton" name="displayHideButton" defaultValue="true"/>
<config:getOption var="refinesContainerMaxWith" name="refinesContainerMaxWith"/>
<config:getOption var="buttonsIconSize" name="buttonsIconSize"/>

<config:getOptionsComposite var="additionalVariables" name="variables" mapIndex="true" />

<request:getCookieValue var="refinesContainer_displayState" name="refinesContainer_displayState" defaultValue="expanded"/>

<widget:widget disableStyles="true" varCssId="cssId" varUcssId="uCssId" extraStyles="${plma:getCssStyles(additionalVariables)}">
	<c:set var="layout" value="${widget.layout}" />
	<c:set var="widthFormat" value="${layout.widthFormat}" />
	<c:if test="${widthFormat == null || widthFormat == ''}">
		<c:set var="widthFormat" value="%" />
	</c:if>

	<c:forEach var="table" varStatus="tableStatus" items="${layout.tables}">
		<c:forEach var="row" items="${table.rows}" varStatus="rowStatus">
			<config:getOption var="rowId" name="cssId" component="${row}" defaultValue="${cssId}_row_${rowStatus.index}" />
			<config:getOption var="rowCssClass" name="cssClass" component="${row}" defaultValue="" />
			<div id="${rowId}" class="plmaPageContainer ${not empty rowCssClass ? rowCssClass : ''}" style="${plma:format('--icons-size:%spx;', buttonsIconSize, '')}">
				<c:forEach varStatus="statusCell" var="cell" items="${row.cells}">
					<c:set var="refinesExtraStyles" value="--refines-flex-grow:${plma:getFexGrow(table, statusCell.index)}%;"/>
					<c:set var="flexGrowCSS" value="plmaFlexContainer-col-${plma:getFexGrow(table, statusCell.index)}"/>
					<c:choose>
						<c:when test="${statusCell.first}">
							<%-- In case of first column: cansider it as PLMA menu container--%>
							<c:set var="flexColCSS" value="menuContainer"/>
						</c:when>
						<c:when test="${statusCell.last && hasRefinePanel == 'true'}">
							<%-- If maximum size is configured for refines container: pass CSS parameters --%>
							<c:if test="${refinesContainerMaxWith != null}">
								<c:set var="refinesExtraStyles" value="--refines-max-width:${refinesContainerMaxWith}px;--refines-flex-grow:${plma:getFexGrow(table, statusCell.index)}%;"/>
							</c:if>
							<%-- In case of last column: cansider it as PLMA refine panel container--%>
							<c:set var="flexColCSS" value="refinesContainer refinesPanel state-${refinesContainer_displayState}"/>
						</c:when>
						<c:otherwise>
							<%-- Otherwise simply consider main container(s) --%>
							<c:set var="flexColCSS" value="plmaMainContainer"/>
						</c:otherwise>
					</c:choose>
					<config:getOption var="cellId" name="cssId" component="${cell}" defaultValue="${rowId}_cell_${statusCell.index}" />
					<config:getOption var="cellCssClass" name="cssClass" component="${cell}" defaultValue="" />

					<div id="${cellId}" class="plmaPageColumn ${flexGrowCSS} ${flexColCSS} <c:if test="${hidePanelClass != null && hidePanelClass != ''}"> ${hidePanelClass}</c:if>" style="${refinesExtraStyles}">
						<%-- Now process header, main content and footer....--%>
						<c:choose>
							<c:when test="${flexColCSS == 'plmaMainContainer'}">
								<div class="plmaMainContent">
									<%-- Render sub-widgets in header --%>
									<c:if test="${plma:hasSubWidget(cell, 'pageTitle')}" >
										<div class="plmaMainItem plmaHeader pageTitleContainer">
											<plma:forEachSubWidget widgetContainer="${cell}" includes="pageTitle" feed="${parentFeed}" entry="${parentEntry}">
												<render:widget/>
											</plma:forEachSubWidget>
										</div>
									</c:if>
									<%-- Render sub-widgets main container (except page title and footer) --%>
									<div class="plmaMainItem widgetsContainer">
										<plma:forEachSubWidget widgetContainer="${cell}" excludes="pageTitle,plmaFooter" feed="${parentFeed}" entry="${parentEntry}">
											<render:widget/>
										</plma:forEachSubWidget>
									</div>
									<%-- Render footer --%>
									<c:if test="${plma:hasSubWidget(cell, 'plmaFooter')}">
										<div class="plmaMainItem plmaFooter">
											<plma:forEachSubWidget widgetContainer="${cell}" includes="plmaFooter" feed="${parentFeed}" entry="${parentEntry}">
												<render:widget/>
											</plma:forEachSubWidget>
										</div>
									</c:if>
								</div>
							</c:when>
							<c:when test="${flexColCSS.startsWith('refinesContainer')}">
								<c:if test="${displayHideButton == 'true'}">
									<div class="refinesContainerCollapse refinesPanelToggle">
										<span class="refinesContainerCollapse fonticon fonticon-fast-forward"/>
									</div>
								</c:if>
								<c:if test="${displayOpenButton == 'true'}">
									<div class="refinesContainerExtend refinesPanelToggle">
										<div><span class="display-mode-expand fonticon fonticon-filter"/></div>
										<div class="filtersLabel"><i18n:message code="widget.flexpage.filters"/></div>
									</div>
								</c:if>
								<div class="refinesPanelContent">
									<widget:forEachSubWidget widgetContainer="${cell}" feed="${parentFeed}" entry="${parentEntry}">
										<render:widget />
									</widget:forEachSubWidget>
								</div>
							</c:when>
							<c:otherwise>
								<%-- Simply render subwidgets --%>
								<widget:forEachSubWidget widgetContainer="${cell}" feed="${parentFeed}" entry="${parentEntry}">
									<render:widget />
								</widget:forEachSubWidget>
							</c:otherwise>
						</c:choose>
					</div>
				</c:forEach>
			</div>
		</c:forEach>
	</c:forEach>

	<render:renderScript position="READY">
		new PLMACollapsibleContainer('${uCssId}', {});
	</render:renderScript>
	
</widget:widget>
