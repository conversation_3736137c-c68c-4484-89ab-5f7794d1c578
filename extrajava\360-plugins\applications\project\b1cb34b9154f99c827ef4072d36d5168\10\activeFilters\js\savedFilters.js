var SavedFilters = function (uCssId, options) {
	this.widget = $('.' + uCssId);
	this.displaySavedFilter = 'false';
	this.pageName = options.pageName;
	this.pageId = options.pageId;
	this.dateParam = options.dateParam;
	this.feedName = options.feedName;
	this.readers = options.readers;
	this.writers = options.writers;
	this.user = options.user;

	if (this.displaySavedFilter === 'true' && typeof ChartboardStorageManager !== 'undefined') {
		var pageId = this.pageId;
		if (pageId === '') {
			this.pageId = this.pageName;
			pageId = this.pageName;
		}
		this.model = new ChartboardStorageManager();
		this.currentUrl = new BuildUrl(window.location.href);
		this.hasChanges = false;
		this.model.getFilterFromPage(pageId, pageId !== this.pageName, $.proxy(function (filters) {
			this.filters = filters;
			this.init();
		}, this), $.proxy(function () {

		}, this));
	}
};

SavedFilters.prototype.init = function () {
	/* Create div for savedFilters */
	if (this.filters && this.filters.params && Object.keys(this.filters.params).length > 0) {
		this.createSavedFiltersBlock();

		/* For each filter displayed in active filters */
		this.widget.find('.activeFiltersContainer .activeFilter').each($.proxy(function (index, item) {

			/* Facet refine case, Facet zap-refine case, Facet cancel-refine case, date case, other */
			var filterRefine = $(item).data('facet');
			var refineType = $(item).data('type');
			var queryType = $(item).data('query');
			if (this.findFacetRefineInFilters(filterRefine, refineType, queryType)) {
				/* Move it to the savedFilters block */
				$(item).detach().appendTo(this.savedFilterBlock.find('.saved-filter-elem-container')).addClass('inactive').off().prop('onclick', null).removeAttr('href');
			}
		}, this));

		if (this.widget.find('.activeFiltersContainer .activeFilter').length === 0) {
			this.widget.find('.fonticon-trash.reset-btn').remove();
		}

		/* Verify rights */
		var hasWriteRights = this.writers === 'true';

		if (this.pageId !== this.pageName && !hasWriteRights) {
			this.savedFilterBlock.addClass('right-lock');
		} else {
			this.savedFilterBlock.on('click', $.proxy(function (e) {
				if ($(e.currentTarget).hasClass('locked')) {
					this.unlockSavedFilters();
				}
			}, this));
			this.savedFilterBlock.find('.saved-filters-label .saved-filters-save-label').on('click', $.proxy(function (e) {
				if ($(e.currentTarget).closest('.savedFilterContainer').hasClass('unlocked')) {
					this.lockSavedFilters();
				}
			}, this));
		}
	}
};

SavedFilters.prototype.createSavedFiltersBlock = function () {
	this.savedFilterBlock = $('<div class="savedFilterContainer locked" title="' + this.getMessage('widgets.activeFilters.savedFilters.title.unlock') + '"></div>');

	var textSavedBookmark = $('<div class="saved-filters-label"></div>');
	textSavedBookmark.append($('<span class="saved-filters-main-label">' + this.getMessage('widgets.activeFilters.savedFilters.main') + '</span>'));
	textSavedBookmark.append();
	textSavedBookmark.append($('<span class="saved-filters-save-label" title="' + this.getMessage('widgets.activeFilters.savedFilters.title.save') + '">' + this.getMessage('widgets.activeFilters.savedFilters.save') + '</span>'));
	this.savedFilterBlock.append(textSavedBookmark);
	this.savedFilterBlock.append($('<div class="saved-filters-unlock-label"><span>' + this.getMessage('widgets.activeFilters.savedFilters.unlock') + '</span><span class="saved-filter-lock fonticon fonticon-lock"></span></div>'));
	this.savedFilterBlock.append($('<div class="saved-filters-drop-container"><span class="fonticon fonticon-plus"></span><span class="saved-filters-drop-label">' + this.getMessage('widgets.activeFilters.savedFilters.drop') + '</span></div>'));
	this.savedFilterBlock.append($('<div class="saved-filter-lock-container"><span class="saved-filter-lock fonticon fonticon-lock"></span><span class="saved-filter-lock-open fonticon fonticon-lock-open"></span></div>'));
	this.savedFilterBlock.append($('<div class="saved-filter-elem-container"></div>'));
	// this.savedFilterBlock.append($('<div class="drop-zone"><span class="fonticon fonticon-plus"></span></div>'));
	this.savedFilterBlock.append($('<span class="saved-filter-trash hidden fonticon fonticon-trash" title="' + this.getMessage('widgets.activeFilters.savedFilters.title.removeAll') + '"></span>'));

	this.widget.find('.activeFiltersContainer').before(this.savedFilterBlock);
};

SavedFilters.prototype.findFacetRefineInFilters = function (facet, refineType, queryType) {
	for (var key in this.filters.params) {
		if (key.indexOf('.r') !== -1 && this.hasParamFacetValue(key, facet) && refineType === 'r') {
			return true;
		} else if (((key.indexOf('.r') !== -1 && this.hasParamFacetValue(key, "-")) || key.indexOf('.cr') !== -1) && this.hasParamFacetValue(key, facet) && refineType === 'cr') {
			return true;
		} else if (key.indexOf('q') !== -1 && this.hasParamFacetValue(key, facet) && refineType === 'q' && queryType !== 'unsaved') {
			return true;
		} else if (key.indexOf(this.dateParam) !== -1 && this.hasParamFacetValue(key, facet) && refineType === 'date') {
			return true;
		} else if (refineType === 'custom') {
			return true;
		}
	}
	return false;
};

SavedFilters.prototype.hasParamFacetValue = function (key, value) {
	for (var i = 0; i < this.filters.params[key].length; i++) {
		if (this.filters.params[key][i].indexOf(value) !== -1) {
			return true;
		}
	}
	return false;
};

SavedFilters.prototype.lockSavedFilters = function () {
	/* Desactivate remove filter */
	this.savedFilterBlock.find('.activeFilter').addClass('inactive').off();

	/* Desactivate remove all filters */
	this.savedFilterBlock.find('.saved-filter-trash').off();

	/* Desactivate drag and drop of new filter */
	this.widget.find('.activeFiltersContainer .activeFilter .drag-icon').remove();
	this.widget.find('.activeFiltersContainer .activeFilter').draggable("disable");

	/* Display lock button */
	this.savedFilterBlock.find('.saved-filter-lock-container').removeClass('unlocked').addClass('locked');
	this.savedFilterBlock.find('.saved-filter-trash').addClass('hidden');

	/* Display cancel icon for active normal filters (enable onclick) and remove draggable icon */
	this.widget.find('.activeFiltersContainer .activeFilter .drag-icon').remove();
	this.widget.find('.activeFiltersContainer .activeFilter .fonticon-cancel').removeClass('hidden');

	/* Save changes and init lists */
	this.saveFilterChanges();
};

SavedFilters.prototype.unlockSavedFilters = function () {
	/* Activate remove filter */
	this.savedFilterBlock.find('.activeFilter').removeClass('inactive').on('click', $.proxy(function (e) {
		var $e = $(e.currentTarget);
		this.removeFilter($e.data('type'), $e.data('facet'), $e.data('query'));
		$e.remove();
		this.hasChanges = true;
	}, this));

	/* Active remove all filters */
	this.savedFilterBlock.find('.saved-filter-trash').on('click', $.proxy(function () {
		for (var filter in this.filters.params) {
			if (this.filters.params.hasOwnProperty(filter)) {
				delete this.filters.params[filter];
			}
		}
		this.widget.find('.savedFilterContainer .activeFilter').each($.proxy(function (index, item) {
			var $item = $(item);
			$item.remove();
		}, this));
		this.hasChanges = true;
	}, this));

	/* Activate drag and drop of new filter */
	this.widget.find('.activeFiltersContainer .activeFilter').draggable({
		revert: 'invalid',
		helper: 'clone',
		zIndex: 20000,
		start: $.proxy(function (event, ui) {
			$(event.currentTarget).addClass('dragged');
		}, this),
		stop: $.proxy(function (event, ui) {
			$(event.target).removeClass('dragged');
		}, this)
	});
	this.savedFilterBlock.droppable({
		accept: '.activeFilter',
		tolerance: 'pointer',
		hoverClass: 'drop-zone-active',
		activeClass: 'drag-active',
		drop: $.proxy(function (event, ui) {
			this.onDropRefine(event, ui);
		}, this)
	});

	this.savedFilterBlock.prop('title', '');

	/* Hide cancel icon for active normal filters (block onclick) and add draggable icon */
	this.widget.find('.activeFiltersContainer .activeFilter').prepend($('<span class="drag-icon fonticon fonticon-drag-grip"></span>'));
	this.widget.find('.activeFiltersContainer .activeFilter .fonticon-cancel').addClass('hidden');

	/* Display unlock button */
	this.savedFilterBlock.removeClass('locked').addClass('unlocked');
	this.savedFilterBlock.find('.saved-filter-trash').removeClass('hidden');
};

SavedFilters.prototype.onDropRefine = function (event, ui) {
	var item = ui.draggable;
	item.find('.fonticon-drag-grip').remove();
	item.find('.fonticon-plus').removeClass('hidden');

	/* Delete original filter */
	item.detach();

	if (item.data('type') === 'zr') {
		/* Remove the associated refine */
		this.widget.find('.saved-filter-elem-container .activeFilter').each($.proxy(function (i, e) {
			var $e = $(e);
			if ($e.data('type') === item.data('rtype') && item.data('facet') === $e.data('facet')) {
				this.removeFilter($e.data('type'), $e.data('facet'), $e.data('query'));
				this.currentUrl.removeParameterWithValue_();
				for (var filter in this.currentUrl.params) {
					if (filter.indexOf('.zr') !== -1) {
						/* Remove it from url */
						this.currentUrl.removeParameterWithValue_(filter, '+' + $e.data('facet'));
						this.currentUrl.removeParameterWithValue_(filter, '-' + $e.data('facet'));
						this.currentUrl.removeParameterWithValue_(filter, $e.data('facet'));
						break;
					}
				}
				$e.remove();
				this.hasChanges = true;
			}

		}, this));
	} else {
		this.addFilter(item.data('type'), item.data('facet'));
		this.savedFilterBlock.find('.saved-filter-elem-container').append(item);
		item.off().prop('onclick', null).removeAttr('href');
		this.hasChanges = true;
		item.on('click', $.proxy(function (e) {
			var $e = $(e.currentTarget);
			this.removeFilter($e.data('type'), $e.data('facet'), $e.data('query'));
			$e.remove();
			this.hasChanges = true;
		}, this));
	}
};

SavedFilters.prototype.removeFilter = function (filterType, filterFacet, filterQuery) {
	var removeFilterKey = '';
	var removeFilterValue = '';
	if (filterType === 'cr') {
		removeFilterKey = '.r';
		removeFilterValue = '-' + filterFacet;
	} else if (filterType === 'r' || filterType === 'zr') {
		removeFilterKey = '.' + filterType;
		removeFilterValue = filterFacet;
	} else if (filterType === 'custom') {
		removeFilterKey = filterQuery;
		removeFilterValue = filterFacet;
	} else if (filterType === 'date') {
		removeFilterKey = this.dateParam;
		removeFilterValue = filterFacet;
	} else {
		removeFilterKey = filterType;
		removeFilterValue = filterFacet;
	}

	for (var filter in this.filters.params) {
		if (filter.indexOf(removeFilterKey) !== -1) {
			var nbRemoved = 0;
			var clone = this.filters.params[filter].slice(0);
			for (var i = 0 ; i < clone.length ; i++) {
				if (clone[i].indexOf(removeFilterValue) !== -1) {
					this.filters.params[filter].splice(i - nbRemoved,1);
					nbRemoved ++;
				}
			}

			if (this.filters.params[filter].length === 0) {
				delete this.filters.params[filter];
			}
		}
	}
};

SavedFilters.prototype.addFilter = function (filterType, filterFacet) {
	var addFilterKey = '';
	var addFilterValue = '';
	if (filterType === 'cr') {
		addFilterKey = '.r';
		addFilterValue = '-' + filterFacet;
	} else if (filterType === 'r' || filterType === 'zr') {
		addFilterKey = '.' + filterType;
		addFilterValue = filterFacet;
	} else if (filterType === 'date') {
		addFilterKey = this.dateParam;
		addFilterValue = filterFacet;
	} else {
		addFilterKey = filterType;
		addFilterValue = filterFacet;
	}

	var filterAlreadyExists = false;
	for (var filter in this.filters.params) {
		if (filter.indexOf(addFilterKey) !== -1) {
			this.filters.params[filter].push(addFilterValue);
			/* Remove it from url */
			this.currentUrl.removeParameterWithValue_(filter, '+' + addFilterValue);
			this.currentUrl.removeParameterWithValue_(filter, '-' + addFilterValue);
			this.currentUrl.removeParameterWithValue_(filter, addFilterValue);
			filterAlreadyExists = true;
			break;
		}
	}
	if (!filterAlreadyExists) {
		if (addFilterKey.indexOf('.r') !== -1 || addFilterKey.indexOf('.cr') !== -1) {
			addFilterKey = this.feedName + addFilterKey;
		}
		if (!this.filters.params[addFilterKey]) {
			this.filters.params[addFilterKey] = [];
		}
		this.filters.params[addFilterKey].push(addFilterValue);
		delete this.currentUrl.params[addFilterKey];
	}
};

SavedFilters.prototype.saveFilterChanges = function () {
	if (this.hasChanges) {
		if (this.filters && this.filters.params && Object.keys(this.filters.params).length === 0) {
			this.model.removeFilter(this.filters, $.proxy(function () {
				this.hasChanges = false;
				this.model.getPage(this.pageId, this.pageId !== this.pageName, $.proxy(function (page) {
					page.filters = undefined;
					this.model.savePage(page, this.pageId !== this.pageName, $.proxy(function () {
						window.location.replace(this.currentUrl.toString());
					}, this), $.proxy(function () {
						window.location.replace(this.currentUrl.toString());
					}, this));
				}, this), $.proxy(function () {
					window.location.replace(this.currentUrl.toString());
				}, this));
			}, this), $.proxy(function () {

			}, this));
		} else {
			this.model.saveFilter(this.filters, $.proxy(function () {
				this.hasChanges = false;
				window.location.replace(this.currentUrl.toString());
			}, this), $.proxy(function () {

			}, this));
		}
	} else {
		window.location.replace(this.currentUrl.toString());
	}
};

SavedFilters.prototype.getMessage = function (code) {
	return mashupI18N.get('activeFilters', code);
};
