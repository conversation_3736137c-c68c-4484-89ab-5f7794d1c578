<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Tabs" group="PLM Analytics/Layout/Hide and show" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>A tab-like widget that allows you to hide and show widgets. No smart lazy loading yet.</Description>

	
	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/hideShowTabs.js" />
		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js" />
		<Include type="js" path="../plmaResources/js/plmaSpinner.js" />
	</Includes>
	
	<SupportWidgetsId arity="ZERO_OR_MANY"/>
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportI18N supported="true" />
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<OptionsGroup name="General">
		<Option id="title" name="Title" arity="ZERO_OR_ONE"  isEvaluated="true">
			<Description>The title to display.</Description>
		</Option>
		<OptionComposite id="tabs" name="Tabs" arity="ZERO_OR_MANY" isEvaluated="true">
			<Description>The tabs to display.</Description>
			<Option id="iconCSS" name="Icon CSS">
				<Description>Specifies the CSS class name of this tab icon.</Description>
			</Option>
			<Option id="label" name="Label" isEvaluated="true" arity="">
				<Description>Specifies a label for this tab.</Description>
			</Option>
			<Option id="show" name="Show classes" arity="ZERO_OR_MANY">
				<Description>CSS classes of the items to show when this tab is selected.</Description>
			</Option>
			<Option id="defaultActivation" name="Active by default" arity="ONE">
				<Description>Selects this tab by default when the page is displayed.</Description>
				<Values>
					<Value>true</Value>
					<Value>false</Value>
				</Values>
			</Option>
			<Option id="activationCondition" name="Activation condition" arity="ZERO_OR_ONE" isEvaluated="true">
				<Description>
					Selects the tab based on a MEL expression. The tab is active when expression is 'true'.
					This options takes precedence over the "Active by default" option.
				</Description>
			</Option>
		</OptionComposite>
		<Option id="showPopup" name="Activate tooltip">
			<Description>Displays an information icon and a tooltip.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['popup']})</Display>
			</Functions>
		</Option>
		<Option id="popup" name="Tooltip value" arity="ONE" isEvaluated="true">
			<Description>The value of the tooltip.</Description>
			<Functions>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="activateAsynch" name="Render second widget only on click" arity="ONE">
			<Description>Allows you to load the hidden widget only on first click on switch button.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="secondWidgetCallback" name="Callback on second widget rendering">
			<Description>Allows you to execute callback after second widget rendering.</Description>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
		<Option id="tabClickCallback" name="Callback after tab selection">
			<Description>Allows you to execute a function after clicking on a tab.</Description>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Style">
		<Option id="height" name="Height" arity="ZERO_OR_ONE">
			<Description>The height of the tabs content (in pixels). Leave empty if you want it to adapt to inner widgets.</Description>
		</Option>
		<Option id="minwidth" name="Min Width">
			<Description>Specifies the minimum width of the widget (pixels). You must enter an integer.</Description>		
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="secondWidgetCallback">function(){}</DefaultValue>
		<DefaultValue name="tabClickCallback">function(){}</DefaultValue>
	</DefaultValues>

</Widget>
