@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/styles/colorPreferences.less";
@import "../../plmaResources/css/styles/variables.less";

// Base Colors
@teal: #1abc9c;
@range-track-color: @cblock-border;
@range-label-color: @cblock-border;

// Range Slider
@range-width: 100%;
@range-handle-size: 8px;
@range-track-height: 3px;
@range-label-width: 30px;


.mashup.mashup-style {

	.refinepanel-suggest {
		display: none !important;
	}

	.refine-link-draggable {
		background-color: #d5e8f2;
		border-radius: 3px;
		padding: 9px 7px 9px 7px;
		opacity: 0.5;

		&.exclude {
			background-color: #fad4ce;
			text-decoration: line-through;
		}
	}

	#panelsContainer.flexPanelsContainer .flexPanelsContainer-row .flexPanelsContainer-item.collapsiblePanel.refinesPanel {
		padding: 0;
	}

	.overlay-active-container {
		position: relative;

		.overlay-active {
			position: absolute;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.3);
			top: 0;
			left: 0;
		}
	}

	#panelsContainer.flexPanelsContainer .flexPanelsContainer-row .flexPanelsContainer-item.panel-hidden {
		.flex(0 0 0);
		flex-grow: 0;
		transition: ease-out 0.5s;
	}

	.searchWidget {
		&.refinePanel {
			position: relative;

			.spinner-container {
				position: absolute;
				width: 100%;
				height: 100%;
				justify-content: center;
				display: none;

				&.active {
					display: flex;
				}

				.spinner {
					border: 16px solid #f3f3f3;
					border-top: 16px solid @clink-active;
					border-radius: 50%;
					width: 60px;
					height: 60px;
					animation: spin 2s linear infinite;
					z-index: 20;
					align-self: center;
				}
			}

			.facets {
				position: relative;

				.empty-facets {
					padding: 7px 0 0 7px;
					font-size: 14px;
					line-height: 24px;
					margin-top: 100px;
					display: flex;
					justify-content: center;
					flex-wrap: wrap;

					.fonticon {
						font-size: 30px;
						display: block;
						flex-basis: 100%;
						margin-bottom: 20px;
					}

					.message {
						display: block;
					}
				}
				.icons-container{
					font-size: 15px;
					display: flex;
					margin-bottom: 5px;
					.icon{
						margin-right: 5px;
						padding: 5px;
						flex-grow: 1;
						&:before{
							margin-right: 5px;
						}
						&:hover{
							color: white;
							background-color: @clink-active;
						}
					}
				}
				&.advanced-active .icons-container{
					flex-basis: 100%;
				}
			}

			.overlay-facets {
				display: none;

				&.active {
					position: absolute;
					display: block;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					z-index: 10;
					background-color: rgba(0, 0, 0, 0.3);
				}
			}

			padding: 0;
			.display-flex();
			height: 100%;

			.refine-main-container {
				.flex(4 1 0);
				flex-grow: 4;
				flex-basis: 0px;
				.display-flex();
				flex-direction: column;

				.searchFormRefine {
					margin: 0;
					padding: 7px 10px 10px 0px;
					border-bottom: 1px solid @grey-3;
				}
			}

			.searchWidget {
				.display-flex();
				padding: 0;

				.searchContainer {
					.flex(1 1 0);
					flex-grow: 1;
					padding: 7px 10px 5px 7px;
					min-height: 15px;

					.searchFormContent {
						border: 1px solid #e2e4e3;
						border-radius: 3px;
						padding: 0 0 0 5px;
						line-height: 26px;
						height: 26px;
						text-align: left;
						width: 100%;

						.searchInput {
							border: none;
							width: 90%;
							height: 24px;
							line-height: 24px;
						}
					}
				}

                .closeButton, .collapseButton, .expandButton {
                    font-size: 16px;
                    padding-top: 14px;

                    &.active {
                        .fonticon-cancel {
                            color: @clink;
                        }
                    }

                    .fonticon {
                        cursor: pointer;
                    }

					.fonticon-fast-forward {
						color: @clink;
					}
                }

				&.advanced {
					.searchContainer {
						border-bottom: 1px solid @cblock-border;
					}
				}
			}
		}

		.searchFormContent {
			white-space: nowrap;
			font-family: "3ds";
			font-size: 14px;
			position: relative;

			.searchInput {
				border: 1px solid @cblock-border;
				border-radius: 3px;
				padding: 0 0 0 5px;
				line-height: 26px;
				height: 26px;
				text-align: left;
				width: 100%;
			}

			.searchButton {
				cursor: pointer;
				display: inline-block;
				line-height: 26px;
				height: 26px;
				vertical-align: top;
				position: absolute;
				right: 7px;
			}
		}

	}

	.refinePanel.refines {
		.table-collapsed {
			display: none;
		}

		.table-collapsable {
		}

		.filter-list-facet-noresult {
			margin-top: 10px;
			margin-left: 5px;

			&.hidden {
				display: none;
			}
		}

		text-align: left;

		.widgetContent.facets {
			border: none;
			padding: 7px;
			.flex(1 1 0);
			flex-grow: 1;
			overflow: auto;

			.title-container.sub-header-collapsible {
				.toolbox {
					position: absolute;
					right: 23px;
					border-radius: 20px 0px 0px 20px;
					background-color: #f9f9f9;
					padding-top: 1px;
					height: 15px;
					padding-right: 2px;
					.menu-icon, .view-menu, .sort-menu {
						display: inline-flex;
						.hidden {
                            display: none;
                        }
					}

					.fonticon-list, .fonticon-chart-bar, .fonticon-chart-pie,
					.fonticon-sort-alpha-asc, .fonticon-sort-alpha-desc,
					.fonticon-sort-num-desc, .fonticon-sort-num-asc, .fonticon-reset {
						padding: 0px 3px;
						&:hover {
							color: #3d3d3d;
						}
					}
				}

				.selected {
					color: @clink-active;
				}

				.hidden{
					display: none;
				}

				cursor: pointer;

				.facet-name, .icon-collapse-button{
					&:hover {
						color: @clink-active;
					}
				}

				.icon-collapse-button {
					padding-top: 1px;
					height: 15px;
					z-index: 1;
					position: relative;
					float: right;
					font-size: 20px;
					border-radius: 20px 0px 2px 20px;
					background-color: #F1F1F1;
				}
			}

			&.advanced-active {
				.filter-list-facet-elem {
					min-height: 150px;

					.facet-container-list {
						max-height: 250px;
					}

					&.filter-list-facet-elem-facet {
						.facet-container-list {
							max-height: 177px;
						}

					}

					.title-container {
						font-size: 17px;
						text-align: center;
						color: #368EC4;
						margin-top: 5px;
						margin-bottom: 10px;
					}

					.calendar-button-container {
						margin-right: 10px;
					}
				}
			}

			&.advanced-active {
				.display-flex();
				flex-wrap: wrap;

				.filter-list-facet-elem {
					.flex(1 1 500px);

					height: 200px;
					border: 1px solid @cblock-border;
					margin: 15px;
					position: relative;

					.facet-value-container {
						.flex(2 1 0);
						flex-grow: 2;
						flex-shrink: 1;
						flex-basis: 0px;
					}

					.pie-facet-value {
						display: block;
						position: absolute;
						font-size: 20px;
						top: 97px;
						left: 55px;
						color: #FF8A2E;
						z-index: 2;
					}

					td.refineName {
						.refinecontainer {
							&.active {
								transform: translateX(20px);
								font-weight: bolder;

								.refine-link {
									color: #FF8A2E;
								}
							}
						}
					}

					.facet-container-list {
						.calendar-container {
							@media ( max-width: 1500px) {
								:last-of-type.input-calendar-container {
									display: inline-block;
								}
							}
						}
					}
				}
			}

			.filter-list-facet-elem {
				margin-bottom: 10px;
				padding-bottom: 10px;
				border-bottom: 1px solid @cblock-border;
				position: relative;

				.facet-spinner-container {
					position: absolute;
					width: 100%;
					height: 100%;
					justify-content: center;
					display: none;

					&.active {
						display: flex;
					}

					.facet-spinner {
						border: 16px solid #f3f3f3;
						border-top: 16px solid #42a2da;
						border-radius: 50%;
						width: 60px;
						height: 60px;
						animation: spin 2s linear infinite;
						z-index: 20;
						align-self: center;
					}
				}

				.display-flex();

				&.filter-list-facet-elem-date {
					.title-container {
						cursor: pointer;
						margin: 0;
						padding: 0;
						height: 20px;
						margin-left: 4px;
						position: relative;
						overflow: hidden;
						display: flex;
						align-items: center;

						&:hover {
							color: @clink-bg-active;
						}

						.facet-value {
							color: @ctext-weak;
							font-size: 10px;
							display: block;
							margin-left: 10px;
						}

						.calendar-icon {
							position: absolute;
							right: 5px;
						}
					}

					h3 {
						&:first-child {
							&:hover {
								border-bottom: 1px;
							}
						}
					}
				}

				.title-container {
					.fonticon-resize-full {
						cursor: pointer;
						float: right;
						color: @ctext;

						&.hidden {
							display: none;
						}
					}

					.calendar-icon {
						float: right;
						font-size: 19px;
					}
				}

				&.fullsize {
					height: inherit;
					flex-direction: column-reverse;

					.facet-value-container {
						.flex(1 1 0);
						flex-grow: 1;
					}

					.sub-header-collapsable {
						font-size: 23px;

						.fonticon-resize-full {
							color: @clink;
						}
					}
				}

				.pie-facet-value {
					display: none;
				}

				&.unique-cat {
					min-height: inherit;
				}

				.facet-value-container {
					.flex(4 1 0);
					flex-grow: 4;
					flex-shrink: 1;
					.display-flex();
					flex-direction: column;
					width: 100%;

					.highcharts-facet-container{
							position: relative !important;
							height: 220px;
							margin-top: 10px;

					.highcharts-container {
								position: absolute !important;
							}
					.highcharts-button-box {
								opacity: 0;
							}
					}
				}

				.facet-container-column {
						position: relative !important;
						height: 220px;
						margin-top: 10px;

						.highcharts-container {
							position: absolute !important;
						}
						.highcharts-button-box {
							opacity: 0;
						}
				}

				.facet-container-pie {
						position: relative;
						height: 220px;
						margin-top: 10px;

						.highcharts-container {
							position: absolute !important;
						}
						.highcharts-button-box {
							opacity: 0;
						}
				}

				&.hidden {
					display: none;
				}

				// Facet title
				h3 {

					margin-top: 0;

					&:first-child {
						border-top: 0px;

						&:hover {
							border: none;
							background: inherit;
						}
					}

					.infos {
						float: right;
						margin-right: 3px;
						color: #666;
						font-size: 10px;
						display: none;
					}

					.icon-collapsable {
						margin-right: 2px;
						width: 15px;
						height: 15px;
						vertical-align: text-top;

						display: inline-block;
						/* IE6 display inline block */
						zoom: 1;
						*display: inline;
					}

					.icon-collapsed {
						.icon-collapsable;
						background-position: 0 0;
					}
				}

				.facet-container-list {
					&.hidden {
						display: none;
					}

					max-height: 250px;
					.transition(@transition-simple);
					overflow: auto;

					.calendar-container {
						.icon-calendar-from {
							font-size: 19px;
						}

						.icon-calendar-to {
							font-size: 19px;
						}

						.input-calendar-container {
							display: inline-block;
							border: 1px solid #ccc;
							border-radius: 4px;
							color: #555;
							height: 25px;
							line-height: 25px;
							vertical-align: middle;
							margin: 0 0 5px 0;
							width: 105px;

							input {
								border: none;
								width: 70px;
								height: 19px;
								line-height: 19px;
								cursor: pointer;

							}
						}

						@media ( max-width: 1500px) {
							:last-of-type.input-calendar-container {
								display: block;
							}
						}

						.calendar-text {
							font-size: 15px;
						}

						.input-calendar-facet-from {
						}

						.input-calendar-facet-to {
						}
					}

					.calendar-preference-container {
						margin-top: 10px;

						.list-custom-range {
							display: flex;
							flex-wrap: wrap;
							justify-content: center;

							.range-button {
								margin: 5px 5px;
								font-size: 13px;
								background: #f5f5f5;
								border: 1px solid #f5f5f5;
								border-radius: 4px;
								color: #08c;
								padding: 3px 12px;
								cursor: pointer;
								width: 120px;
								.flex(1);
								flex-basis: 30%;

								&.active {
									background: #08c;
									border: 1px solid #08c;
									color: #f5f5f5;
								}

								&:hover {
									background: #08c;
									border: 1px solid #08c;
									color: #f5f5f5;
								}
							}
						}
					}

					.calendar-button-container {
						margin-top: 10px;

						button {
							cursor: pointer;
							color: #3D3D3D;
							background-color: #F1F1F1;
							background-image: none;
							font-size: 14px;
							border: 1px solid #B4B6Ba;
							border-radius: 4px;
							min-width: 72px;
							line-height: 1.42857;
							padding: 5px 10px;
							margin-left: 5px;
							float: right;

							&:hover {
								color: #3D3D3D;
								background-color: #E2E4E3;
								border-color: #77797C;
							}
						}
					}
				}

				.facet {
					table-layout: fixed;
					border-collapse: collapse;
					line-height: 19px;
					width: 100%;

					&.collapsed {
						display: none;
					}

					.arrows {
						margin-left: 20px;
						.display-flex();
						.justify-content(center);

						.fonticon {
							cursor: pointer;
						}

						.left-arrow {
							font-size: 15px;
							margin: 0;
						}

						.right-arrow {
							font-size: 15px;
							margin: 0;
						}

						.page-number {
							margin-left: 5px;
							margin-right: 5px;
							font-size: 14px;
						}

						.hidden {
							display: none;
						}
					}

					.category {
						color: @ctext-weaker;
						font-size: 11px;

						&.hidden {
							display: none;
						}

						&.refined {
							td {
								&.refinePie {
									.pie-container {
										.pie-center {
											background-color: #d3e7f3;
										}
									}
								}
							}
						}

						&:hover {
							td {
								&.refinePie {
									.pie-container {
										.pie-clip {
											.pie-item {
												&.filled {
													background-color: @ctext-bold;
												}

												&.blanked {
													background-color: white;
												}
											}
										}

										.pie-center {
											background-color: #368ec4;
										}
									}
								}
							}
						}

						// Default styles and colors

						td {
							vertical-align: top;
							text-align: center;
							padding: 0;

							a {
								display: block;
								white-space: normal;
								overflow: hidden;

								&:hover {
									text-decoration: none;
								}
							}

							&.disjunctive {
								width: 20px;
							}

							&.refineName {
								text-align: left;
								.wordbreak();

								.refinecontainer {
									padding-top: 0;
									border-bottom: 1px solid @cblock-border;

									&.active-searched {
										background-color: @clink;
										font-weight: bolder;

										.refine-link {
											color: #FF8A2E;
										}
									}

									.refine-link {
										padding-top: 3.5px;
									}
								}
							}

							&.exclude {
								.refine-link {
									cursor: pointer;
								}
							}

							&.refinePie {
								width: 16px;
								padding-top: 2px;
								padding-left: 2px;

								.pie-container {
									position: relative;
									width: 16px;
									height: 16px;
									border-radius: 100%;
									overflow: hidden;

									.pie-clip {
										border-radius: 50%;
										clip: rect(0px, 16px, 16px, 8px);
										height: 100%;
										position: absolute;
										width: 100%;

										.pie-item {
											border-radius: 50%;
											clip: rect(0px, 8px, 16px, 0px);
											height: 100%;
											position: absolute;
											width: 100%;
											font-family: monospace;
											font-size: 1.5rem;

											&.filled {
												background-color: @clink;
											}

											&.blanked {
												background-color: @cblock-border;
											}
										}
									}

									.pie-center {
										background: white;
										position: absolute;
										top: 2px;
										left: 2px;
										height: 12px;
										width: 12px;
										border-radius: 7px;
									}
								}
							}

							&.count {
								white-space: nowrap;
								text-align: right;
								padding-right: 3px;
								width: 50px;

								.countcontainer {
									display: inline-block;
									font-size: 9px;
									height: 14px;
									line-height: 14px;
									width: auto;
									min-width: 20px;
									margin: 2px 0;
									padding: 0 2px 0 2px;
									color: #3d3d3d;
									font-weight: 600;
								}
							}

							&.exclude {
								width: 15px;

								a {
									color: @cblock-border-alt;
									font-weight: bold;
								}
							}
						}

						// refined row

						&.refined {
                            .aggregation-bar{
                                display: none;
                            }
							background-color: #d5e8f2;
							color: darken(@clink, 5%);

							a {
								color: darken(@clink, 5%);
							}

							.refinecontainer {
								&:hover {
									background-color: #005686;
									color: white;

									a {
										color: white;
									}
								}
							}

						}

						// excluded row

						&.excluded {
							background-color: #fad4ce;

							.refinecontainer {
								&:hover {
									background-color: #CC092F;
									color: white;

									a {
										color: white;
									}

									.countcontainer {
										color: white;
									}
								}
							}

						}

						// hovered row

						td.refineName {
							.refinecontainer {
								cursor: pointer;
								transition: transform ease-out 500ms;

								&:hover {
									background-color: @clink-active;
									color: white;

									a {
										color: white;
									}
								}
							}
						}

						background-color: inherit;

						&:hover {
							background-color: inherit;
							color: @ctext;

							a {
								color: @ctext;
							}

							.countcontainer {
								color: @ctext;
							}
						}

						// Depth levels
						&.depthLevel_0 .refinecontainer {
							padding-left: 5px;
						}

						&.depthLevel_1 .refinecontainer {
							padding-left: 12px;
						}

						&.depthLevel_2 .refinecontainer {
							padding-left: 19px;
						}

						&.depthLevel_3 .refinecontainer {
							padding-left: 26px;
						}

						&.depthLevel_4 .refinecontainer {
							padding-left: 33px;
						}

						&.depthLevel_5 .refinecontainer {
							padding-left: 40px;
						}

						&.depthLevel_6 .refinecontainer {
							padding-left: 47px;
						}

						&.depthLevel_7 .refinecontainer {
							padding-left: 49px;
						}

						&.depthLevel_8 .refinecontainer {
							padding-left: 51px;
						}

						&.depthLevel_9 .refinecontainer {
							padding-left: 53px;
						}

						&.depthLevel_10 .refinecontainer {
							padding-left: 55px;
						}

						&.depthLevel_11 .refinecontainer {
							padding-left: 57px;
						}

						&.depthLevel_12 .refinecontainer {
							padding-left: 59px;
						}

						&.depthLevel_13 .refinecontainer {
							padding-left: 61px;
						}

						&.depthLevel_14 .refinecontainer {
							padding-left: 63px;
						}

						&.depthLevel_15 .refinecontainer {
							padding-left: 65px;
						}
					}
				}

				&.filter-list-facet-elem-numerical {
					.title-container {
						.facet-name {
							text-transform: capitalize;
						}
					}

					.facet-container-list {
						overflow: visible;

						.facet-value {
							margin-top: 10px;

							.refine-panel-numerical-slider {
								-webkit-appearance: none;
								height: 3px;
								border-radius: 5px;
								background: #f6f6f6;
								position: relative;
								top: 7px;
								margin-left: 4px;
								margin-right: 12px;
								margin-bottom: 10px;

								.ui-slider-handle {
									width: @range-handle-size;
									height: @range-handle-size;
									border-radius: 50%;
									cursor: pointer;
									top: -4px;

									&.ui-state-active {
										background: @clink;
										color: @clink;
										border-color: @clink;
									}
								}

								.ui-slider-range {
									background: @clink;
									border-color: @clink;
									height: 3px;

									&.exclude {
										background: #EA4F37;
										border-color: #EA4F37;
									}
								}
							}

							.slider-extreme-container {
								color: @ctext-weak;
								position: relative;

								.slider-min-value {
									position: absolute;
									left: 2px;
								}

								.slider-max-value {
									position: absolute;
									right: 10px;
								}
							}

							.slider-action-container {
								padding-top: 5px;

								.slider-inputs {
									width: 120px;
									margin: 0 auto;
									padding-right: 20px;
								}

								.value {
									margin-left: 5px;
									position: relative;
									top: 10px;
									font-size: 15px;

									&.min-value, &.max-value {
										width: 50px;
										height: 25px;
										text-align: center;
										border-radius: 4px;
										border: 1px solid @cblock-border;

										&.error {
											border: 1px solid #EA4F37;
											color: #EA4F37;
										}
									}
								}
							}

							.slider-button {
								color: @ctext-weak;
								font-size: 20px;
								float: right;
								top: -11px;
								position: relative;
								right: 8px;
								cursor: pointer;

								&:hover {
									color: @clink-active;
									border-color: @clink-active;
								}

								&.disabled {
									cursor: not-allowed;
								}

								&.slider-refine-button {
									&.active {
										color: @clink;
									}
								}

								&.slider-exclude-button {
									&.active {
										color: #EA4F37;
									}
								}
							}

							.slider-refine-container {
								.slider-refine {
									margin-top: 20px;
									background: #d5e8f2;
									height: 22px;
									cursor: pointer;

									.slider-refine-label {
										color: #3080b0;
										position: relative;
										top: 3px;
										left: 6px;
									}

									.fonticon {
										float: right;
										position: relative;
										top: 3px;
										right: 1px;
										color: #d1d4d4;
									}

									&:hover {
										color: white;
										background-color: #368EC4;

										.slider-refine-label {
											color: white;
										}
									}

									&.exclude {
										background-color: #fad4ce;

										.slider-refine-label {
											text-decoration: line-through;
										}

										&:hover {
											background-color: #EA4F37;
										}
									}
								}
							}
						}
					}
				}
			}

			.pagination {
				margin: (@line-height / 2) @line-height 0 @line-height;
				font-size: @m-font;
				text-align: start;

				.previous-page, .next-page, .previous-page-loading, .next-page-loading {
					cursor: pointer;
				}

				.see-more {
					cursor: pointer;

					&.fonticon {
						font-size: 25px;
						font-weight: bold;
					}

					&:hover {
						color: @ctext-bold;
					}
				}
			}
		}

		img.flag {
			.box-shadow(0, 0, 2px, #555555);
			vertical-align: middle;
		}
	}
}

.daterangepicker {
	&.dropdown-menu {
		border: 1px solid @cblock-border;
		margin-top: 10px;
		margin-right: 0;
		padding: 0;
		display: none;

		.daterangepicker_input {
			display: none;
		}

		.calendar {
			margin: 0;
			padding: 7px;

			&.left {
				padding-right: 0;

				tr:first-child {
					th:last-child {
						cursor: default;

						.to-start-icon {
							cursor: pointer;
							font-size: 14px;
						}
					}
				}
			}

			&.right {
				border-right: 1px solid @cblock-border;
				padding-left: 0;

				tr:first-child {
					th:first-child {
						cursor: default;

						.to-end-icon {
							cursor: pointer;
							font-size: 14px;
						}
					}
				}
			}

			select {
				border: 1px solid @cblock-border-alt;
				border-radius: 4px;
				font-size: 14px;
			}

			td {
				position: relative;

				&.active {
					&:not(.off) {
						background-color: #ebf4f8;
						border-radius: 0;
						color: #000;

						&.start-date {
							border: none;
							border-left: 3px solid #357ebd;
						}

						&.end-date {
							border: none;
							border-right: 3px solid #357ebd;
						}
					}
				}

				.valueFacetBorder {
					bottom: 0;
					background: orange;
					width: 100%;
					opacity: 0.4;
					position: absolute;
					left: 0;
				}
			}

			.calendar-table {
				.table-condensed {
					tr:first-child {
						th:last-child, th:first-child {
							cursor: default;
							&:hover {
								background: inherit;
							}
							i {
								cursor: pointer;
								&:hover {
									background: #eee;
								}
							}
							.fonticon {
								cursor: pointer;
							}
						}

					}
				}
			}
		}

		.fa.glyphicon {
			font-family: entypo;
			font-size: 18px;
			font-style: normal;
			font-weight: normal;

			&.fa-chevron-left {
				&:before {
					content: '\e140';
				}
			}

			&.fa-chevron-right {
				&:before {
					content: '\e141';
				}
			}

		}

		.ranges {
			display: flex;
			flex-direction: column;
			margin: 0;
			height: 220px;
			width: 180px;

			.list-custom-ranges {
				flex: 1;
				width: 180px;
				padding-top: 7px;
			}

			.current-date {
				height: 10px;
				border-top: 1px solid @cblock-border;
				color: @ctext;
				text-align: center;
				vertical-align: middle;
				padding-top: 10px;
				padding-bottom: 10px;
				font-size: 13px;
				span {
					line-height: 30px;
					&.from, &.to {
						cursor: pointer;
						&:hover {
							color: @clink;
						}
					}
				}
			}

			.range_inputs {
				border-top: 1px solid @cblock-border;
				padding: 7px;
				display: flex;
				justify-content: space-between;

				.btn {
					color: #3d3d3d;
					background-color: #f1f1f1;
					border-color: #b4b6ba;
					width: 74px;
					height: 30px;
					padding-top: 5px;

					&:hover {
						border-color: @clink-bg-active;
						color: @clink-bg-active;
					}
				}
			}

			li {
				background: none;
				border: none;
				border-radius: 0;
				margin: 0;
				padding: 7px;
				color: @ctext-bold;

				&:hover {
					border: none;
					background-color: @cblock-border;
					color: @ctext-bold;
				}

				&.active {
					background: #08c;
					border: 1px solid #08c;
					color: #fff;
				}
			}
		}
	}
}

/* Control buttons */
.mashup {
	.refinespanel-toolbar-buttons {
		font-size: 16px;
		padding-top: 14px;
		cursor: pointer;
		.widget-menu-container.toolbar-mode {
			width: unset !important;
		}
	}
	.aggregation-bar{
		position: absolute;

		&.position-under {
			height: 100%;
			border-bottom: 1px solid @cblock-border-alt;
		}

		&.position-behind {
			height: @line-height;
			margin: 5px;
			background-color: @cblock-border-alt;
		}

		&.position-left {
			height: 10px;
			margin-top: 8px;
			border-radius: 3px;
			background-color: @cblock-border-alt;
		}

		&.position-right {
			height: 16px;
			margin-top: 2px;
			opacity: 0.3;
			right: 1px;
			border-radius: 3px;
			background-color: @cblock-border-alt;
		}
	}
}

