var ReloadContainer = function(selector, uCssId, eventName, paramName) {
	this.selector = selector;
	this.uCssId = uCssId;
	this.widget = $('.' + this.uCssId);
	this.eventName = eventName;
	this.paramName = paramName;
};

ReloadContainer.prototype.init = function () {
	if (this.selector === '') {
		this.selector = document;
	}

	$(this.selector).off(this.eventName);
	$(this.selector).on(this.eventName, $.proxy(function (e, params) {
		var client = new PlmaAjaxClient(this.widget);
		client.addWidget(this.uCssId);
		client.addParameters(this.paramName, params);
		client.update();
	}, this));
};