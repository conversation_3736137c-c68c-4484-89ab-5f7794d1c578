/* Here is some extended method of highcharts */

(function (H) {
    // This is a self executing function
    // The global variable Highcharts is passed along with a reference H

    /**
     * @id : annotation id
     * @return highchart annotation object or undefined if not found
     */
    H.wrap(H.Chart.prototype, 'getAnnotation', function (proceed, id) {
        for(var i=0;i<this.annotations.length;i++){
            var annnotation = this.annotations[i];
            if(annnotation.options.id === id){
                return annnotation;
            }
        }
        return undefined;
    });

    H.addEvent(H.Chart, 'load', function (e) {
       $(e.target.container).trigger("plma:chart-is-loaded");
    });

    H.addEvent(H.Chart, "redraw", function(e){
        $(e.target.container).trigger("plma:chart-is-drawn");
    });

    H.addEvent(H.Series, 'hide', function(e) {
        $(document).trigger('plma:series-is-hidden', [e.target.chart.container]);
    });

    H.addEvent(H.Series, 'show', function(e) {
        $(document).trigger('plma:series-is-shown', [e.target.chart.container]);
    });

}(Highcharts));