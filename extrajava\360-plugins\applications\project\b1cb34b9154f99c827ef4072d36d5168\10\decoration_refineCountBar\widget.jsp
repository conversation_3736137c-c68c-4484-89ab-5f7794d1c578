<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>

<render:import varWidget="widget" />

<config:getOptions name="wuids" var="wuids" />
<config:getOption name="selector" var="selector" defaultValue="" />
<config:getOption name="color" var="color" defaultValue="#F4F5F6" />
<config:getOption name="position" var="position" defaultValue="Under" />

<c:choose>
	<c:when test="${not empty selector}">
		<c:set var="widgetSelector">${selector}</c:set>
	</c:when>
	<c:when test="${fn:length(wuids) > 0}">
		<c:set var="widgetSelector" value="."/>
		<c:forEach items="${wuids}" var="wuid" varStatus="loop">
			<c:set var="widgetSelector" value="${widgetSelector}${wuid}"/>
			<c:if test="${loop.index < fn:length(wuids) -1}">
				<c:set var="widgetSelector" value="${widgetSelector}, ."/>
			</c:if>
		</c:forEach>
	</c:when>
	<c:otherwise>
		<c:set var="widgetSelector" value=".mashup .refines" />
	</c:otherwise>
</c:choose>

<widget:widget varUcssId="uCssId" varCssId="cssId" disableStyles="true" extraCss="facet-count-bars">
	
	<render:renderScript position="READY">
	
		refineCountBar.init("${uCssId}",$("${widgetSelector}"),"${fn:toLowerCase(position)}","${color}");
		
	</render:renderScript>
</widget:widget>