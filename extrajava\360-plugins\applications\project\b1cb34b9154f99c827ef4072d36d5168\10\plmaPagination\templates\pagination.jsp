<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>

<render:import parameters="accessFeeds,cssId,wuid" ignore="true" />

<search:getPaginationInfos varFeed="feedWithMaxResults" varCurrentPage="currentPage" varLastPage="lastPage" feeds="${accessFeeds}" />
<config:getOption var="nbPageToShowInPagination" name="nbPageToShowInPagination" defaultValue="9" />

<c:set var="displayPagination" value="${feedWithMaxResults != null && nbPageToShowInPagination > 0 && lastPage > 1}" />
<c:if test="${feedWithMaxResults != null && displayPagination}">
	<div class="pagination-content">
		<%-- Pagination --%>
		<c:if test="${displayPagination}">
			<ol class="fonticon btn-group">
				<c:if test="${currentPage > 1}">
					<li class="btn"><a class="first-page"><i class="fonticon fonticon-double-chevron-left"></i></a></li>
					<li class="btn"><a class="previous-page"><i class="fonticon fonticon-chevron-left "></i></a></li>
				</c:if>
				<search:forEachPage var="pageNumber" varIsSelected="isCurrentPage" feeds="${accessFeeds}" nbPageToShow="${nbPageToShowInPagination}">
					<li class="btn ${isCurrentPage ? 'active' : ''}">
						<a class="${isCurrentPage ? 'current' : ''} other-page">${pageNumber}</a>
					</li>
				</search:forEachPage>
				<c:if test="${currentPage < lastPage}">
					<li class="btn"><a class="next-page"><i class="fonticon fonticon-chevron-right "></i></a></li>
					<li class="btn"><a class="last-page"><i class="fonticon fonticon-double-chevron-right"></i></a></li>
				</c:if>
			</ol>
		</c:if>
		<%-- /Pagination --%>
	</div>

	<search:getFeed feeds="${accessFeeds}" var="feed" />
	<render:renderScript position="READY">
		var options = {};
		options.lastPage = "${lastPage}";
		options.currentPage = "${currentPage}";
		options.feedName = "${feed.id}";
		options.url = '<url:url keepQueryString="true" feeds="${accessFeeds}" xmlEscape="false"/>'
		new paginationManager("${cssId}","${wuid}", options);
	</render:renderScript>

</c:if>
