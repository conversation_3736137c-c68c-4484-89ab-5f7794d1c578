/**
 * Manage similar hit list action (add/remove/restore) on field issue APP.
 */
(function () {


    var SimilarDocumentManager = function(ucssId, options){

        this.DOCUMENT_STATUS = {added:"added", removed : "removed"};
        this.options = options;

        if (ucssId){
            this.widget = $('.' + ucssId);
        }else{
            this.widget = $();
        }

        if (this.widget.length === 0){
            throw new Error('Unable to initialize widget : widget not found (ucssId: "' + ucssId + '").');
        }else{
            this.ucssId = ucssId;
            this.dataLoaded = false;
            this.init();
            SimilarDocumentManager.setInstance(this);
        }
    };

    SimilarDocumentManager.setInstance = function(self){
        self.widget.data("similarity-widget", self);
    };

    SimilarDocumentManager.getInstance = function($widget){
        return $widget.data("similarity-widget");
    };

    SimilarDocumentManager.getInstanceFromUcssId = function(uCssId){
        return $("."+uCssId).data("similarity-widget");
    };

    SimilarDocumentManager.utils = {
        registered : false,
        $widget : undefined,
        /* Register buttons to : add a document to the case */
        registerAddButtons : function(uCssId, $button){
            /* register btn after data from storage has been loaded */
            SimilarDocumentManager.utils.self = SimilarDocumentManager.getInstanceFromUcssId(uCssId);
            if(SimilarDocumentManager.utils.self !== undefined){
                if(SimilarDocumentManager.utils.self.dataLoaded !== true){
                    $(document).on("plma:document-manager-loaded", function(){
                        SimilarDocumentManager.utils.initRegisterAddButtons($button);
                    });
                }else{
                    /* data from storage has been loaded, register right now */
                    SimilarDocumentManager.utils.initRegisterAddButtons($button);
                }
            }
        },
        initRegisterAddButtons : function($button){
            SimilarDocumentManager.utils.initAddButtonState($button);
            SimilarDocumentManager.utils.addButtonEvent($button);
        },
        initAddButtonState : function($button){
            $item = $button.closest("li.hit");
            if($item.length>0){
                var uri = $item.data("uri");
                if(this.self.uris.all.indexOf(uri) !== -1){
                    $button.addClass("hidden");
                }
            }
        },
        addButtonEvent : function($button){
            var self = this.self;
            $button.on("click", $.proxy(function (e) {
                var $btn = $(e.currentTarget);
                var docUri = $btn.closest("li.hit").data("uri");
                self.addDocument(self.options.username, docUri, $btn);
            }, this));
        },
        /* Register a button to : show or hide list of removed items */
        registerShowHideRemovedDocBtn : function(uCssId, $button) {
            var self = SimilarDocumentManager.getInstanceFromUcssId(uCssId);
            $button.on("click", $.proxy(function () {
                SimilarDocumentManager.utils.hideShowRemovedDocuments(self);
            }, this));
        },
        hideShowRemovedDocuments : function (self) {
            if ( $('.' + self.ucssId).find("li.hit:not(.removed)").hasClass("hidden")) {
                $('.' + self.ucssId).find("li.hit:not(.removed)").removeClass("hidden");
            } else {

                $('.' + self.ucssId).find("li.hit:not(.removed)").addClass("hidden");
            }
        },
        notifyEmpty : function(i18nMessage, $widget, targetClass){
            $widget.find(".empty-carousel").remove();
            if($widget.length >0 ){
                if($widget.find("li.hit:not(.hidden)").length === 0){
                    $message = $("<li class='empty-carousel'>"+i18nMessage+"</li>");
                    if(targetClass){
                        $widget.find('.'+targetClass).append($message);
                    }
                }
            }
        },
        clearAllCase : function(successCallback, errorCallback){
            var storage = new StorageClient('shared');
            if(SimilarDocumentManager.utils.self !== undefined){
                if(SimilarDocumentManager.utils.self.options.similarityStorageKey !== undefined){
                    storage.del(SimilarDocumentManager.utils.self.options.similarityStorageKey, successCallback, errorCallback);
                }
            }
        }
    };

    SimilarDocumentManager.prototype.getAddedRemovedItems = function(callback){
        this.uris = {added : [], removed : [], all : []};
        var storage = new StorageClient("shared");
        storage.get(this.options.similarityStorageKey, $.proxy(function (items) {
            for (var i = 0; i < items.length; i++) {
                var item = items[i].value;
                var jsonItem = JSON.parse(item);
                if (jsonItem.status === this.DOCUMENT_STATUS.added) {
                    this.uris.added.push(jsonItem.docUri);
                    this.uris.all.push(jsonItem.docUri);
                } else if (jsonItem.status === this.DOCUMENT_STATUS.removed) {
                    this.uris.removed.push(jsonItem.docUri);
                    this.uris.all.push(jsonItem.docUri);
                }
            }
            if(typeof callback == "function"){
                callback(this.uris);
            }
        }, this));
    };

    SimilarDocumentManager.prototype.init = function () {

       var items = this.getAddedRemovedItems($.proxy(function(items){
           for (var i = 0; i < items.added.length; i++) {
               var $element = this.widget.find("li.hit[data-uri=" + items.added[i] + "]");
               if($element.length>0){
                   $element.addClass("added");
                   this.addTagAdded($element, this.options.states.added);
                   this.initActionStatus($element);
               }
           }
           for (var i = 0; i < items.removed.length; i++) {
               var $element = this.widget.find("li.hit[data-uri=" + items.removed[i] + "]");
               if($element.length>0){
                   $element.addClass("removed");
                   this.addTagRemoved($element, this.options.states.removed);
                   this.initActionStatus($element);
               }
           }
           // similar doc doesn't have a restore btn
           this.widget.find("li.hit:not(.added, .removed)").find(".restore-btn").addClass("hidden");
           $("."+this.ucssId).trigger("plma:document-manager-loaded");
           this.dataLoaded = true;
       }, this));
    };

    /* Register a btn to : remove OR restore a doc to the list */
    SimilarDocumentManager.prototype.registerDocumentAction = function($btn){
        //used for paginated hit
        this.initHitStatus($btn);
        //remove or restore a document from list
        $btn.on("click", $.proxy(function (e) {
            var $e = $(e.currentTarget);
            if($e.closest("li.hit").hasClass("removed")){
                this.restoreDeletedItem($e);
            }else{
                this.removeDocument($e);
            }
        }, this));
    };

    SimilarDocumentManager.prototype.initActionStatus = function($item){
        if($item.hasClass("removed")){
            $item.find(".remove-btn").addClass("hidden");
            $item.find(".restore-btn").removeClass("hidden");
        }else if($item.hasClass("added")){
            $item.find(".restore-btn").addClass("hidden");
            $item.find(".remove-btn").removeClass("hidden");
        }else{
            $item.find(".restore-btn").addClass("hidden");
            $item.find(".remove-btn").removeClass("hidden")
        }
    };

    SimilarDocumentManager.prototype.addTag = function ($item, $badge) {
        var $container = $item.find('.hitContent');
        if ($container.find('.badge').length === 0) {
            $container.append($badge);
        }
    };

    SimilarDocumentManager.prototype.addTagAdded = function(item, i18nState){
        var $badge = $("<div class='badge badge-primary'>"+i18nState+"</div>");
        this.addTag($(item), $badge);
    };

    SimilarDocumentManager.prototype.addTagRemoved = function(item, i18nState){
        var $badge = $("<div class='badge badge-error'>"+i18nState+"</div>");
        this.addTag($(item), $badge);
    };

    SimilarDocumentManager.prototype.removeTag = function(item){
        $(item).find(".badge").remove();
    };

    SimilarDocumentManager.prototype.initHitStatus = function($btn){
        var $hit = $btn.closest("li.hit");
        var uri = $hit.data("uri");
        if(uri !== undefined){
            if(this.uris.removed.indexOf(uri)>-1){
                $hit.addClass("removed");
                this.addTagRemoved($hit, this.options.states.removed);
                this.initActionStatus($hit);
            }else if(this.uris.added.indexOf(uri)>-1){
                $hit.addClass("added");
                this.addTagAdded($hit, this.options.states.added);
                this.initActionStatus($hit);
            }else{
                this.initActionStatus($hit);
            }
        }
    };

    SimilarDocumentManager.prototype.addDocument = function(username, docUri, $btn){
        var storage = new StorageClient("shared");
        storage.put(this.options.similarityStorageKey, JSON.stringify({"status": this.DOCUMENT_STATUS.added, "owner": username, "docUri":docUri, time:new Date().getTime()}),
            $.proxy(function () {
                /* hide btn with visibility hidden (not display none) to still fire infinite scroll */
                $btn.css("visibility", "hidden");
                this.refreshWidget();
            }, this),
            function(){
            //error callback
        });
    };

    SimilarDocumentManager.prototype.removeDocument = function ($e) {
        var $li = $e.closest('li.hit');
        var docUri = $li.data("uri");
        if ($li.hasClass("added")) {
            //this item has been added by user
            this.restoreDeletedItem($e);
        } else {
            //this item is in similar query
            var storage = new StorageClient('shared');
            storage.putMany(this.options.similarityStorageKey, [JSON.stringify({
                status: this.DOCUMENT_STATUS.removed,
                owner: this.options.username,
                docUri: docUri,
                time: new Date().getTime()
            })], $.proxy(function () {
                this.refreshWidget();
            }, this), function () {
            });
        }
    };

    SimilarDocumentManager.prototype.restoreDeletedItem = function ($e) {
        var $li = $e.closest('li.hit');
        var docUri = $li.data("uri");
        this.updateStorageValue(this.options.similarityStorageKey, function (values) {
            var index = values.findIndex(function (item) {
                var jsonItem = JSON.parse(item);
                if (jsonItem.docUri === docUri) {
                    return true;
                }
            });
            if (index > -1) {
                values.splice(index, 1);
            }
            return values;
        }, $.proxy(function () {
            this.refreshWidget();
        }, this));
    };

    SimilarDocumentManager.prototype.updateStorageValue = function (storageKey, callback, success, error) {
        var storage = new StorageClient('shared');
        storage.get(storageKey, $.proxy(function (items) {
            var values = [];
            for (var i = 0; i < items.length; i++) {
                values.push(items[i].value);
            }
            values = callback.call(this, values);
            if (typeof values != "undefined") {
                storage.setMany(storageKey, values, success, error);
            } else {
                error.call();
            }
        }, this), error);
    };

    SimilarDocumentManager.prototype.enablePreview = function(selectorContainer){
        var $firstHit = $("."+this.ucssId).find("li.hit:first-child");
        if($firstHit.length>0){
            var uri = $firstHit.data("uri");
            if(uri !== undefined){
                var client = new PlmaAjaxClient($(selectorContainer),{
                    success: function(){
                        $firstHit.addClass("selected");
                    }
                });
                var $widget = $(".hit-preview");
                var widgetWuid = $widget.attr("class").split(" ")[1];
                client.addWidget(widgetWuid);
                client.addParameter("hit", uri);
                client.update();
            }
        }
    };

    SimilarDocumentManager.prototype.refreshWidget = function (success) {
        if(! typeof(success) == "function"){
            success = $.noop();
        }
        var client = new MashupAjaxClient($("body"), {success : success});
        client.addWidget(this.ucssId);
        if(this.options.wuid !== undefined){
            for(var i = 0; i< this.options.wuid.length;i++){
                var widgets = $(this.options.wuid[i]);
                if(widgets.length>0){
                    $.each(widgets, function(){
                        var widgetWuid = $(this).attr("class").split(" ")[1];
                        if(widgetWuid !== undefined){
                            client.addWidget(widgetWuid);
                        }
                    });
                }
            }
        }
        client.addParameter(this.options.similarityValueParamName, $("."+this.ucssId).closest(".flexPanelsContainer-item").find(".plma-slider-bar input.range-slider-range").val());
        client.update();
    };

    window.SimilarDocumentManager = SimilarDocumentManager;

})();