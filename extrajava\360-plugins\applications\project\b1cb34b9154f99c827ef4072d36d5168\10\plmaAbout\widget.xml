<?xml version="1.0" encoding='UTF-8'?>
<Widget name="About PLMA application" group="PLM Analytics/Miscellaneous" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>Gives information about the current application: version, revision, date.</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/plmaAbout/images/preview.PNG" alt="PLMA About" />
        ]]>
	</Preview>
	<SupportI18N supported="true" />
	
	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="false" />
	</Platforms>

    <Includes>
        <Include type="css" path="css/style.less"/>
    </Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportWidgetsId arity="ZERO" />
	<SupportI18N supported="true" />
	
	<OptionsGroup name="General">
		<Option id="showLastIndexTime" name="Show last index time" arity="ONE">
			<Description>Allows the user to see the last index time.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['buildGroupNames']})</Display>
			</Functions>
		</Option>
		<Option id="buildGroupNames" name="Build group names" arity="ZERO_OR_MANY">
			<Description>Names of the build groups that one wants the index time. If blank, it defaults to "bg0".</Description>
		</Option>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="showLastIndexTime">false</DefaultValue>
		<DefaultValue name="buildGroupName">bg0</DefaultValue>
	</DefaultValues>
</Widget>
