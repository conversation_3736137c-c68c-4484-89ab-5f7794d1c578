var Flip = function (uCssId, options) {
	'use strict';

	var $window = $(window);
	this.options = options;
	this.widget = $('.' + uCssId);

	this.onclickAction = $.proxy(function (e) {
		$(e.target).closest('.content-wrapper').toggleClass('switched');

		if (this.firstButtonFullScreen !== undefined && this.firstButtonFullScreen !== null) {
			this.firstButtonFullScreen.widget
				.on('click', $.proxy(function(e){
					var $target = $(e.target);
					if ($target.hasClass('searchWidget')) {
						if (this.firstButtonFullScreen.isFullScreen()) {
							this.firstButtonFullScreen.hideFullScreen();
							this.firstButtonFullScreen.hideButton();
						}

						if (this.secondButtonFullScreen !== undefined) {
							this.secondButtonFullScreen.toggleButton();
							this.secondButtonFullScreen.hideButton();
						}
					}
				}, this));
		}
		if (this.secondButtonFullScreen !== undefined && this.secondButtonFullScreen !== null) {
			this.secondButtonFullScreen.widget
				.on('click', $.proxy(function(e){
					var $target = $(e.target);
					if ($target.hasClass('searchWidget')) {
						if (this.secondButtonFullScreen.isFullScreen()) {
							this.secondButtonFullScreen.hideFullScreen();
							this.firstButtonFullScreen.hideButton();
						}

						if (this.firstButtonFullScreen !== undefined) {
							this.firstButtonFullScreen.toggleButton();
							this.firstButtonFullScreen.hideButton();
						}
					}
				}, this));
		}
	}, this);

	this.secondWidgetWuid = options.secondWidgetWuid;

	this.init = function () {
		this.widget = $('.wuid.' + uCssId);
		if (this.widget.length === 0) {
			throw new Error('HideShowTabs : no widget found with uCssid ' + uCssId);
		}
		this.content = this.widget.find('.content-wrapper');

		this.firstButtonManager = new WidgetButtonManager(this.getFirstWidget().attr('class').split(' ')[2], this.options.firstButtonCssType, this.options.firstButtonCssSelector);
		this.secondButtonManager = new WidgetButtonManager(this.getSecondWidget().attr('class').split(' ')[2], this.options.secondButtonCssType, this.options.secondButtonCssSelector);

		if (this.countSubWidget() > 1) {
			//this.addFullScreenButtons(this.firstButtonManager, this.secondButtonManager);

			/* Add first widget buttons */
			this.firstButtonFullScreen = this.addFullScreenButton(this.firstButtonManager);
			var firstButton = this.firstButtonManager.addButton('firstSwitchButton fonticon ' + this.options.firstButtonIcon, this.options.firstButtonTitle, this.onclickAction);
			if (this.options.firstButtonCssType !== 'menu') {
				this.firstButtonManager.container.append(firstButton);
			}

			/* Add second widget buttons */
			this.secondButtonFullScreen = this.addFullScreenButton(this.secondButtonManager);
			var secondButton = this.secondButtonManager.addButton('secondSwitchButton fonticon ' + this.options.secondButtonIcon, this.options.secondButtonTitle, this.onclickAction);
			if (this.options.secondButtonCssType !== 'menu') {
				this.secondButtonManager.container.append(secondButton);
			}

			this.synchronizeFullScreen();

			this.widget.on('buildFlipButton', $.proxy(function (e) {
				if (this.content[0].getElementsByClassName('firstSwitchButton').length === 0) {
					this.firstButtonFullScreen = this.addFullScreenButton(this.firstButtonManager);
					this.firstButtonManager.addButton('firstSwitchButton fonticon ' + options.firstButtonIcon, options.firstButtonTitle, this.onclickAction);
				}
				if (this.getElementsByClassName('secondSwitchButton').length === 0) {
					this.secondButtonFullScreen = this.addFullScreenButton(this.secondButtonManager);
					this.secondButtonManager.addButton('secondSwitchButton fonticon ' + this.options.secondButtonIcon, this.options.secondButtonTitle, this.onclickAction);
				}
				this.synchronizeFullScreen();
			}, this));
		} else if (this.countSubWidget() === 1) {
			this.firstButtonFullScreen = this.addFullScreenButton(this.firstButtonManager);
			var firstButton = this.firstButtonManager.addButton('firstSwitchButton fonticon ' + this.options.firstButtonIcon, this.options.firstButtonTitle, $.proxy(function (e) {
				$(e.target).closest('.content-wrapper').toggleClass('switched');
				if (this.countSubWidget() === 1) {
					this.onclickLoad()
				}
			}, this));
			if (this.options.firstButtonCssType !== 'menu') {
				this.firstButtonManager.container.append(firstButton);
			}
		}

	};

	this.init();
};

Flip.prototype.addFullScreenButton = function(widgetButtonManager) {
	if (this.options.enableFullScreen) {
		var $fullScreenButton = widgetButtonManager.addButton(
			FullScreenWidget.BASE_ICON_CSS_CLASS + ' ' + FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS,
			FullScreenWidget.getMessage('widget.action.fullScreen')
		);
		var fullScreenButton = this.widget.fullScreenWidget({
			button: $fullScreenButton,
			isFullScreen: this.widget.hasClass('full-size-active')
		});
		return fullScreenButton;
	}
	return null;
};

Flip.prototype.synchronizeFullScreen = function() {
	if (this.options.enableFullScreen && this.firstButtonFullScreen !== undefined && this.secondButtonFullScreen !== undefined) {
		this.firstButtonFullScreen.button.on('click', $.proxy(function() {
			this.secondButtonFullScreen.toggleButton();
		}, this));
		this.secondButtonFullScreen.button.on('click', $.proxy(function() {
			this.firstButtonFullScreen.toggleButton();
		}, this));
	}
};

Flip.prototype.getFirstWidget = function () {
	for (var i = 0; i < this.content.children().length; i++) {
		if (this.content.children().eq(i).hasClass('wuid')) {
			return this.content.children().eq(i);
		}
	}
};

Flip.prototype.getSecondWidget = function () {
	var alreadyOne = false;
	for (var i = 0; i < this.content.children().length; i++) {
		if (this.content.children().eq(i).hasClass('wuid') && alreadyOne) {
			return this.content.children().eq(i);
		} else if (this.content.children().eq(i).hasClass('wuid')) {
			alreadyOne = true;
		}
	}
};

Flip.prototype.countSubWidget = function () {
	var nbSubWidget = 0;
	for (var i = 0; i < this.content.children().length; i++) {
		if (this.content.children().eq(i).hasClass('wuid') && !this.content.children().eq(i).hasClass('subWidgetHidden')) {
			nbSubWidget++;
		}
	}
	return nbSubWidget;
};

Flip.prototype.onclickLoad = function () {
	var url = window.location.href;
	var options = {};
	options.success = $.proxy(function () {
		this.secondButtonManager.reInit();
		this.secondButtonFullScreen = this.addFullScreenButton(this.secondButtonManager);
		var secondButton = this.secondButtonManager.addButton('secondSwitchButton fonticon ' + this.options.secondButtonIcon, this.options.secondButtonTitle, this.onclickAction);
		if (this.options.secondButtonCssType !== 'menu') {
			this.secondButtonManager.container.append(secondButton);
		}
		this.synchronizeFullScreen();
		if(this.secondButtonFullScreen !== null) {
            this.secondButtonFullScreen.widget
                .on('click', $.proxy(function(e){
                    var $target = $(e.target);
                    if ($target.hasClass('searchWidget')) {
                        this.secondButtonFullScreen.hideFullScreen();
                        this.secondButtonFullScreen.hideButton();

                        if (this.firstButtonFullScreen !== undefined) {
                            this.firstButtonFullScreen.toggleButton();
                            this.firstButtonFullScreen.hideButton();
                        }
                    }
                }, this));
        }
	}, this);
	options.error = function () {
		$.notify(mashupI18N.get('flipContainer', 'widget.flipContainer.error.server'), 'error');
	};

	var client = new PlmaAjaxClient($('.' + this.secondWidgetWuid), options);
	var params = url.split('?');
	var parameters = this.getUrlParameters(params[1]);

	//Delete the base params
	client.getAjaxUrl().params = {};

	for (var i = 0; i < parameters.length; i++) {
		var key = parameters[i].key;
		var value = parameters[i].value;
		if (key != "" && key != "_") {
			var decodedValue = decodeURIComponent(value.replace(/\+/g, ' '));
			client.getAjaxUrl().addParameter(key, decodedValue, false);
		}
	}

	/* Adding custom params */
	for (var param in this.options.ajaxParams) {
		client.getAjaxUrl().addParameters(param, this.options.ajaxParams[param]);
	}

	// set wuids to update
	client.addWidget(this.secondWidgetWuid);

	client.update();
};

Flip.prototype.getUrlParameters = function (url) {
	var f = [];
	if (url === undefined) {
		return f;
	}

	var t = url.split('&');
	var j = 0;
	for (var i = 0; i < t.length; i++) {
		var x = t[i].split('=');
		if (x.length === 1 && t[i] !== "" && j > 0) {
			f[j - 1].value = f[j - 1].value + '&' + t[i];
		} else if (x.length === 2) {
			f[j] = {
				key: x[0],
				value: x[1]
			};
			j++;
		} else if (x.length === 3) { // CASE refine=test+tokenizer=<EMAIL> => t=['refine', 'test+tokenizer', '<EMAIL>']
			f[j] = {
				key: x[0],
				value: x[1] + '=' + x[2]
			};
			j++;
		}

	}
	return f;
};
