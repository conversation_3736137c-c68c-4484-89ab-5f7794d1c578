/* ***** CHECKBOX/RADIOBUTTON STYLE ***** */
.plma-checkbox-container{
    margin-right:10px;
	label {
		display: inline-block;
		max-width: 100%;
		margin-bottom: 5px;
		color:@clink;
		font-size:15px;
	}
}

.decorate.plma-radiobutton,
.decorate.plma-checkbox {
    position: absolute;
    display: none;
}

.decorate.plma-radiobutton + label,
.decorate.plma-checkbox + label {
    position: relative;
    display: block;
    padding-left: 30px;
    cursor: pointer;
    vertical-align: middle;
    padding-bottom: 1px;
}

/*CHECKBOX*/
.decorate.plma-checkbox input[type=checkbox] {
    margin: 4px 0 0;
    margin-top: 1px\9;
    line-height: normal;
}

.decorate.plma-checkbox input[type=checkbox]{
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0;
}

.decorate.plma-checkbox+label:before {
    border-radius: 3px;
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block;
    width: 20px;
    height: 20px;
    content: '';
    border: 1px solid #c0c0c0;
}

.decorate.plma-checkbox+label:after {
    top: 3px;
    left: 8px;
    box-sizing: border-box;
    width: 6px;
    height: 12px;
    transform: rotate(45deg);
    border-width: 2px;
    border-style: solid;
    border-color: #fff;
    border-top: 0;
    border-left: 0;
    position: absolute;
    display: none;
    content: '';
}

.decorate.plma-checkbox:checked+label:before {
    border-color: @clink;
    background: @clink;
    animation-name: none;
}

.decorate.plma-checkbox:checked+label:after {
    display: block;
}

.decorate.plma-checkbox.partial+label:before {
	background: #fff;
}
.decorate.plma-checkbox.partial+label:after {
	top: 6px;
    left: 6px;
    box-sizing: border-box;
    width: 10px;
    height: 10px;
    position: absolute;
    content: '';
	border: none;
	transform: none;
	background: @clink;
	border-radius: 3px;
	display: block;
}							

/*RADIOBUTTON*/
.decorate.plma-radiobutton+label:before {
    border-radius: 15px;
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block;
    width: 18px;
    height: 18px;
    content: '';
    border: 2px solid #c0c0c0;	
}

.decorate.plma-radiobutton+label:after {
    top: 4px;
    left: 4px;
    box-sizing: border-box;
    width: 14px;
    height: 14px;
	border-radius: 14px;
    position: absolute;
    display: none;
    content: '';
}

.decorate.plma-radiobutton:checked+label:before {
    border-color: @clink;
    animation-name: none;
}

.decorate.plma-radiobutton:checked+label:after {
	background: @clink;
    display: block;
}

/*Misc*/
.truncate{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 450px;
    line-height: 22px;
}

.pull-left {
    float: left!important;
}
