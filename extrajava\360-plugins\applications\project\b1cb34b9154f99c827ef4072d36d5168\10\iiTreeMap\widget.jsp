<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>

<render:import varWidget="widget" varFeeds="feeds" />

<config:getOption var="width" name="width" />
<c:if test="${width != null}"><c:set var="width" value="width:${width}px;" /></c:if>

<widget:widget varCssId="cssId" varUcssId="uCssId" extraCss="highcharts treeMap" extraStyles="${width}">

	<widget:header>
		<span class="widgetChartHeader widgetTitle"><config:getOption name="title" defaultValue="" /></span>
	</widget:header>

	<widget:content>
		<c:choose>

			<%-- IF there are no feeds at all --%>
			<c:when test="${search:hasFeeds(feeds) == false}">
				<render:definition name="noFeeds">
					<render:parameter name="widget" value="${widget}" />
					<render:parameter name="showSuggestion" value="false" />
				</render:definition>
			</c:when>

			<c:otherwise>
				<config:getOption var="height" name="height" />
				<c:set var="styleHeight" value="${height}" />
				<c:choose>
					<c:when test="${not empty height}">
						<c:set var="styleHeight" value="${height}px" />
					</c:when>
					<c:otherwise>
						<c:set var="styleHeight" value="100%" />
					</c:otherwise>
				</c:choose>

                <div class="chart-wrapper" style="height: ${styleHeight};width:100%;">
                    <div class="chart-inner">
                        <div id="chart_${cssId}" class="highChartsSVGWrapper iiTreemap" style="height: ${styleHeight};width:100%;"></div>
                    </div>
                </div>

				<render:renderScript position="READY">
					<render:template template="javascript.jsp">
						<render:parameter name="chartContainerId" value="chart_${cssId}" />
					</render:template>
				</render:renderScript>
				</c:otherwise>
		</c:choose>
	</widget:content>
</widget:widget>