/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:32 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.plmaHitDetails;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class widget_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_0;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_1;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_2;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_3;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_4;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_5;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_6;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_7;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_8;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_9;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_10;

static {
  _jspx_fnmap_0= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("search:hasFeeds", com.exalead.cv360.searchui.view.jspapi.search.Functions.class, "hasFeeds", new Class[] {java.util.Map.class});
  _jspx_fnmap_1= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("search:hasEntries", com.exalead.cv360.searchui.view.jspapi.search.Functions.class, "hasEntries", new Class[] {java.util.Map.class});
  _jspx_fnmap_2= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:hasParam", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "hasParam", new Class[] {com.exalead.cv360.searchui.configuration.v10.Widget.class, java.lang.String.class});
  _jspx_fnmap_3= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:getStringParam", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "getStringParam", new Class[] {com.exalead.cv360.searchui.configuration.v10.Widget.class, java.lang.String.class, java.lang.String.class});
  _jspx_fnmap_4= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:getBooleanParam", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "getBooleanParam", new Class[] {com.exalead.cv360.searchui.configuration.v10.Widget.class, java.lang.String.class, boolean.class});
  _jspx_fnmap_5= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:format", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "format", new Class[] {java.lang.String.class, java.lang.String.class, java.lang.String.class});
  _jspx_fnmap_6= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:hasSubWidget", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "anySubWidget", new Class[] {com.exalead.cv360.searchui.configuration.v10.WidgetContainer.class, java.lang.String.class});
  _jspx_fnmap_7= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:getEntryID", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "getEntryID", new Class[] {com.exalead.access.feedapi.Entry.class, java.lang.String.class});
  _jspx_fnmap_8= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:length", org.apache.taglibs.standard.functions.Functions.class, "length", new Class[] {java.lang.Object.class});
  _jspx_fnmap_9= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("search:cleanEntryId", com.exalead.cv360.searchui.view.jspapi.search.Functions.class, "cleanEntryId", new Class[] {java.lang.Object.class});
  _jspx_fnmap_10= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:toJSArray", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "toJSArray", new Class[] {java.util.List.class});
}

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(12);
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/widget.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/request.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/i18n.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetUsedFeeds_0026_005fvar_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss_005fdisableStyles;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fheader;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fcontent;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvarStatus_005fvar_005ffeeds;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvarStatus_005fvar_005ffeed;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fforEachSubWidget_0026_005fincludes;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005fwidget_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fwidget_005fvar_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvar_005ffeed;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeed_005fentry_005fnobody;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fplma_005fgetUsedFeeds_0026_005fvar_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss_005fdisableStyles = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fheader = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fcontent = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvarStatus_005fvar_005ffeeds = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvarStatus_005fvar_005ffeed = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fforEachSubWidget_0026_005fincludes = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005fwidget_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fwidget_005fvar_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvar_005ffeed = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeed_005fentry_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fplma_005fgetUsedFeeds_0026_005fvar_005fnobody.release();
    _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss_005fdisableStyles.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fwidget_005fheader.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fwidget_005fcontent.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvarStatus_005fvar_005ffeeds.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvarStatus_005fvar_005ffeed.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fforEachSubWidget_0026_005fincludes.release();
    _005fjspx_005ftagPool_005frender_005fwidget_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fwidget_005fvar_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvar_005ffeed.release();
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeed_005fentry_005fnobody.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_plma_005fgetUsedFeeds_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_request_005fgetParameterValues_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f4(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f5(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f6(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f7(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fif_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_widget_005fwidget_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f15(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f16(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_render_005frenderScript_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(12,0) name = varWidget type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarWidget("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(12,0) name = varFeeds type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarFeeds("feeds");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetUsedFeeds_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getUsedFeeds
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.PLMAGetUsedFeeds _jspx_th_plma_005fgetUsedFeeds_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.PLMAGetUsedFeeds) _005fjspx_005ftagPool_005fplma_005fgetUsedFeeds_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.PLMAGetUsedFeeds.class);
    boolean _jspx_th_plma_005fgetUsedFeeds_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetUsedFeeds_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetUsedFeeds_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(14,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetUsedFeeds_005f0.setVar("usedFeeds");
      int[] _jspx_push_body_count_plma_005fgetUsedFeeds_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetUsedFeeds_005f0 = _jspx_th_plma_005fgetUsedFeeds_005f0.doStartTag();
        if (_jspx_th_plma_005fgetUsedFeeds_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetUsedFeeds_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetUsedFeeds_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetUsedFeeds_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetUsedFeeds_0026_005fvar_005fnobody.reuse(_jspx_th_plma_005fgetUsedFeeds_005f0);
      _jspx_th_plma_005fgetUsedFeeds_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetUsedFeeds_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetUsedFeeds_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetParameterValues_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getParameterValues
    com.exalead.cv360.searchui.view.jspapi.request.GetParameterValuesTag _jspx_th_request_005fgetParameterValues_005f0 = (com.exalead.cv360.searchui.view.jspapi.request.GetParameterValuesTag) _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetParameterValuesTag.class);
    boolean _jspx_th_request_005fgetParameterValues_005f0_reused = false;
    try {
      _jspx_th_request_005fgetParameterValues_005f0.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetParameterValues_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(16,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValues_005f0.setVar("hitParamValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(16,0) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValues_005f0.setName("hit");
      int[] _jspx_push_body_count_request_005fgetParameterValues_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetParameterValues_005f0 = _jspx_th_request_005fgetParameterValues_005f0.doStartTag();
        if (_jspx_th_request_005fgetParameterValues_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetParameterValues_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetParameterValues_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetParameterValues_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetParameterValues_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_request_005fgetParameterValues_005f0);
      _jspx_th_request_005fgetParameterValues_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetParameterValues_005f0, _jsp_getInstanceManager(), _jspx_th_request_005fgetParameterValues_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(18,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setVar("useExternalisedConfig");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(18,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setName("useExternalisedConfig");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(18,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f0 = _jspx_th_config_005fgetOption_005f0.doStartTag();
        if (_jspx_th_config_005fgetOption_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f0);
      _jspx_th_config_005fgetOption_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(19,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setVar("disableHitDetailsPrefTab");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(19,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setName("disableHitDetailsPrefTab");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(19,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f1 = _jspx_th_config_005fgetOption_005f1.doStartTag();
        if (_jspx_th_config_005fgetOption_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f1);
      _jspx_th_config_005fgetOption_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(22,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setName("showCloseButton");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(22,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setVar("showCloseButton");
      int[] _jspx_push_body_count_config_005fgetOption_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f2 = _jspx_th_config_005fgetOption_005f2.doStartTag();
        if (_jspx_th_config_005fgetOption_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f2);
      _jspx_th_config_005fgetOption_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f3_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(23,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setName("showOpenButton");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(23,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setVar("showOpenButton");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(23,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setDefaultValue("true");
      int[] _jspx_push_body_count_config_005fgetOption_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f3 = _jspx_th_config_005fgetOption_005f3.doStartTag();
        if (_jspx_th_config_005fgetOption_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f3);
      _jspx_th_config_005fgetOption_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f4 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f4_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f4.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(24,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setName("openButtonTooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(24,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setVar("openButtonTooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(24,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setDefaultValue("Open in Enovia 3DSpace");
      int[] _jspx_push_body_count_config_005fgetOption_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f4 = _jspx_th_config_005fgetOption_005f4.doStartTag();
        if (_jspx_th_config_005fgetOption_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f4);
      _jspx_th_config_005fgetOption_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f4, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f5(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f5 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f5_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f5.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f5.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(25,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setName("closeButtonLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(25,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setVar("closeButtonLabel");
      int[] _jspx_push_body_count_config_005fgetOption_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f5 = _jspx_th_config_005fgetOption_005f5.doStartTag();
        if (_jspx_th_config_005fgetOption_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f5);
      _jspx_th_config_005fgetOption_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f5, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f6(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f6 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f6_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f6.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f6.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(26,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setName("showCloseButtonLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(26,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setVar("showCloseButtonLabel");
      int[] _jspx_push_body_count_config_005fgetOption_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f6 = _jspx_th_config_005fgetOption_005f6.doStartTag();
        if (_jspx_th_config_005fgetOption_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f6);
      _jspx_th_config_005fgetOption_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f6, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f7(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f7 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f7_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f7.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f7.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(30,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setVar("fontSizeOption");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(30,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setName("iconsSize");
      int[] _jspx_push_body_count_config_005fgetOption_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f7 = _jspx_th_config_005fgetOption_005f7.doStartTag();
        if (_jspx_th_config_005fgetOption_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fnobody.reuse(_jspx_th_config_005fgetOption_005f7);
      _jspx_th_config_005fgetOption_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f7, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f0_reused = false;
    try {
      _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(31,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setVar("fontSize");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(31,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(31,0) ''",_jsp_getExpressionFactory().createValueExpression("",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
      if (_jspx_th_c_005fset_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
      _jspx_th_c_005fset_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(32,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fontSizeOption != null}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          if (_jspx_meth_c_005fset_005f1(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f1_reused = false;
    try {
      _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(32,39) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setVar("fontSize");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(32,39) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(32,39) '--icons-size:${fontSizeOption}px;'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"--icons-size:${fontSizeOption}px;",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
      if (_jspx_th_c_005fset_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
      _jspx_th_c_005fset_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fwidget_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:widget
    com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag _jspx_th_widget_005fwidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag) _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss_005fdisableStyles.get(com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag.class);
    boolean _jspx_th_widget_005fwidget_005f0_reused = false;
    try {
      _jspx_th_widget_005fwidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fwidget_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(35,0) name = varCssId type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setVarCssId("cssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(35,0) name = varUcssId type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setVarUcssId("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(35,0) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setExtraCss("plmaHitDetails");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(35,0) name = disableStyles type = boolean reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setDisableStyles(true);
      int[] _jspx_push_body_count_widget_005fwidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fwidget_005f0 = _jspx_th_widget_005fwidget_005f0.doStartTag();
        if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fwidget_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fwidget_005f0);
          }
          do {
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fchoose_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_widget_005fwidget_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fwidget_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fwidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fwidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fwidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fwidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss_005fdisableStyles.reuse(_jspx_th_widget_005fwidget_005f0);
      _jspx_th_widget_005fwidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fwidget_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fwidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("\r\n");
          out.write("        ");
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("        ");
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fwhen_005f1(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(39,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${search:hasFeeds(feeds) == false}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_0)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_widget_005fheader_005f0(_jspx_th_c_005fwhen_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_widget_005fcontent_005f0(_jspx_th_c_005fwhen_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fheader_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:header
    com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag _jspx_th_widget_005fheader_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag) _005fjspx_005ftagPool_005fwidget_005fheader.get(com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag.class);
    boolean _jspx_th_widget_005fheader_005f0_reused = false;
    try {
      _jspx_th_widget_005fheader_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fheader_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      int[] _jspx_push_body_count_widget_005fheader_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fheader_005f0 = _jspx_th_widget_005fheader_005f0.doStartTag();
        if (_jspx_eval_widget_005fheader_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fheader_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fheader_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fheader_005f0);
          }
          do {
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_config_005fgetOption_005f8(_jspx_th_widget_005fheader_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fheader_005f0))
              return true;
            out.write("\r\n");
            out.write("            ");
            int evalDoAfterBody = _jspx_th_widget_005fheader_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fheader_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fheader_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fheader_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fheader_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fheader_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fheader_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fheader.reuse(_jspx_th_widget_005fheader_005f0);
      _jspx_th_widget_005fheader_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fheader_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fheader_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fheader_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fheader_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f8 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f8_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f8.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fheader_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(41,16) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setName("title");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(41,16) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f8 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f8 = _jspx_th_config_005fgetOption_005f8.doStartTag();
        if (_jspx_th_config_005fgetOption_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f8[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f8.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f8.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f8);
      _jspx_th_config_005fgetOption_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f8, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fcontent_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:content
    com.exalead.cv360.searchui.view.jspapi.widget.ContentTag _jspx_th_widget_005fcontent_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.ContentTag) _005fjspx_005ftagPool_005fwidget_005fcontent.get(com.exalead.cv360.searchui.view.jspapi.widget.ContentTag.class);
    boolean _jspx_th_widget_005fcontent_005f0_reused = false;
    try {
      _jspx_th_widget_005fcontent_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fcontent_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      int[] _jspx_push_body_count_widget_005fcontent_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fcontent_005f0 = _jspx_th_widget_005fcontent_005f0.doStartTag();
        if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fcontent_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fcontent_005f0);
          }
          do {
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_render_005fdefinition_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("            ");
            int evalDoAfterBody = _jspx_th_widget_005fcontent_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fcontent_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fcontent_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fcontent_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fcontent_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fcontent_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fcontent.reuse(_jspx_th_widget_005fcontent_005f0);
      _jspx_th_widget_005fcontent_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fcontent_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fcontent_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fdefinition_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:definition
    com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag _jspx_th_render_005fdefinition_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.DefinitionTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fdefinition_005f0);
    try {
      _jspx_th_render_005fdefinition_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005fdefinition_005f0.setParent(_jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(44,16) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fdefinition_005f0.setName("noFeeds");
      _jspx_th_render_005fdefinition_005f0.setJspBody(new Helper( 0, _jspx_page_context, _jspx_th_render_005fdefinition_005f0, _jspx_push_body_count_widget_005fcontent_005f0));
      _jspx_th_render_005fdefinition_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fdefinition_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f0);
    try {
      _jspx_th_render_005fparameter_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f0.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(45,20) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setName("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(45,20) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f1);
    try {
      _jspx_th_render_005fparameter_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f1.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(46,20) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setName("showSuggestion");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(46,20) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setValue("true");
      _jspx_th_render_005fparameter_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f1 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f1_reused = false;
    try {
      _jspx_th_c_005fwhen_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(52,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${!search:hasEntries(feeds)}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_1)).booleanValue());
      int _jspx_eval_c_005fwhen_005f1 = _jspx_th_c_005fwhen_005f1.doStartTag();
      if (_jspx_eval_c_005fwhen_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_widget_005fheader_005f1(_jspx_th_c_005fwhen_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_widget_005fcontent_005f1(_jspx_th_c_005fwhen_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("            ");
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f1);
      _jspx_th_c_005fwhen_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fheader_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:header
    com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag _jspx_th_widget_005fheader_005f1 = (com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag) _005fjspx_005ftagPool_005fwidget_005fheader.get(com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag.class);
    boolean _jspx_th_widget_005fheader_005f1_reused = false;
    try {
      _jspx_th_widget_005fheader_005f1.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fheader_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f1);
      int[] _jspx_push_body_count_widget_005fheader_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fheader_005f1 = _jspx_th_widget_005fheader_005f1.doStartTag();
        if (_jspx_eval_widget_005fheader_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fheader_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fheader_005f1[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fheader_005f1);
          }
          do {
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_config_005fgetOption_005f9(_jspx_th_widget_005fheader_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fheader_005f1))
              return true;
            out.write("\r\n");
            out.write("            ");
            int evalDoAfterBody = _jspx_th_widget_005fheader_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fheader_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fheader_005f1[0]--;
          }
        }
        if (_jspx_th_widget_005fheader_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fheader_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fheader_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fheader_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fheader.reuse(_jspx_th_widget_005fheader_005f1);
      _jspx_th_widget_005fheader_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fheader_005f1, _jsp_getInstanceManager(), _jspx_th_widget_005fheader_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fheader_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fheader_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f9 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f9_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f9.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fheader_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(54,16) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setName("title");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(54,16) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f9 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f9 = _jspx_th_config_005fgetOption_005f9.doStartTag();
        if (_jspx_th_config_005fgetOption_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f9[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f9.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f9.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f9);
      _jspx_th_config_005fgetOption_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f9, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fcontent_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:content
    com.exalead.cv360.searchui.view.jspapi.widget.ContentTag _jspx_th_widget_005fcontent_005f1 = (com.exalead.cv360.searchui.view.jspapi.widget.ContentTag) _005fjspx_005ftagPool_005fwidget_005fcontent.get(com.exalead.cv360.searchui.view.jspapi.widget.ContentTag.class);
    boolean _jspx_th_widget_005fcontent_005f1_reused = false;
    try {
      _jspx_th_widget_005fcontent_005f1.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fcontent_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f1);
      int[] _jspx_push_body_count_widget_005fcontent_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fcontent_005f1 = _jspx_th_widget_005fcontent_005f1.doStartTag();
        if (_jspx_eval_widget_005fcontent_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fcontent_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fcontent_005f1[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fcontent_005f1);
          }
          do {
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_config_005fgetOption_005f10(_jspx_th_widget_005fcontent_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f1))
              return true;
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_config_005fgetOption_005f11(_jspx_th_widget_005fcontent_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f1))
              return true;
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_render_005ftemplate_005f0(_jspx_th_widget_005fcontent_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f1))
              return true;
            out.write("\r\n");
            out.write("            ");
            int evalDoAfterBody = _jspx_th_widget_005fcontent_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fcontent_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fcontent_005f1[0]--;
          }
        }
        if (_jspx_th_widget_005fcontent_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fcontent_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fcontent_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fcontent_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fcontent.reuse(_jspx_th_widget_005fcontent_005f1);
      _jspx_th_widget_005fcontent_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fcontent_005f1, _jsp_getInstanceManager(), _jspx_th_widget_005fcontent_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f10 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f10_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f10.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f10.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(57,16) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setVar("noResultsJspPath");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(57,16) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setName("noResultsJspPath");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(57,16) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setDefaultValue("template/noHitSelected.jsp");
      int[] _jspx_push_body_count_config_005fgetOption_005f10 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f10 = _jspx_th_config_005fgetOption_005f10.doStartTag();
        if (_jspx_th_config_005fgetOption_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f10[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f10.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f10.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f10);
      _jspx_th_config_005fgetOption_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f10, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f11 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f11_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f11.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f11.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(58,16) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setVar("noResultJspPathWidget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(58,16) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setName("noResultJspPathWidget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(58,16) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setDefaultValue("plmaResources");
      int[] _jspx_push_body_count_config_005fgetOption_005f11 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f11 = _jspx_th_config_005fgetOption_005f11.doStartTag();
        if (_jspx_th_config_005fgetOption_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f11[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f11.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f11.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f11);
      _jspx_th_config_005fgetOption_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f11, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f0);
    try {
      _jspx_th_render_005ftemplate_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f0.setParent(_jspx_th_widget_005fcontent_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(59,16) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${noResultsJspPath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(59,16) name = widget type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setWidget((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${noResultJspPathWidget}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005ftemplate_005f0.setJspBody(new Helper( 1, _jspx_page_context, _jspx_th_render_005ftemplate_005f0, _jspx_push_body_count_widget_005fcontent_005f1));
      _jspx_th_render_005ftemplate_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f2);
    try {
      _jspx_th_render_005fparameter_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f2.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(60,20) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setName("accessFeeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(60,20) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            <span id=\"hitParamValue\" class=\"hit-param\" style=\"display: none;\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hitParamValue[0]}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</span>\r\n");
          out.write("\r\n");
          out.write("            <span id=\"timelineLabel\" style=\"display: none;\">");
          if (_jspx_meth_i18n_005fmessage_005f0(_jspx_th_c_005fotherwise_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("</span>\r\n");
          out.write("\r\n");
          out.write("            ");
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_search_005fforEachFeed_005f0(_jspx_th_c_005fotherwise_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("            ");
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_config_005fgetOptionsComposite_005f0(_jspx_th_c_005fotherwise_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fif_005f12(_jspx_th_c_005fotherwise_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f0 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f0_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f0.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(69,60) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setCode("hitdetail.timeline");
      int[] _jspx_push_body_count_i18n_005fmessage_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f0 = _jspx_th_i18n_005fmessage_005f0.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f0);
      _jspx_th_i18n_005fmessage_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f0, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachFeed_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachFeed
    com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag _jspx_th_search_005fforEachFeed_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag) _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvarStatus_005fvar_005ffeeds.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag.class);
    boolean _jspx_th_search_005fforEachFeed_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachFeed_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachFeed_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(72,12) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(72,12) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f0.setVar("feed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(72,12) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f0.setVarStatus("accessFeedsStatus");
      int[] _jspx_push_body_count_search_005fforEachFeed_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachFeed_005f0 = _jspx_th_search_005fforEachFeed_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachFeed_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_search_005fforEachEntry_005f0(_jspx_th_search_005fforEachFeed_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFeed_005f0))
              return true;
            out.write("\r\n");
            out.write("            ");
            int evalDoAfterBody = _jspx_th_search_005fforEachFeed_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachFeed_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachFeed_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachFeed_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachFeed_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvarStatus_005fvar_005ffeeds.reuse(_jspx_th_search_005fforEachFeed_005f0);
      _jspx_th_search_005fforEachFeed_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachFeed_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachFeed_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachEntry_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFeed_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFeed_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachEntry
    com.exalead.cv360.searchui.view.jspapi.search.ForEachEntryTag _jspx_th_search_005fforEachEntry_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachEntryTag) _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvarStatus_005fvar_005ffeed.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachEntryTag.class);
    boolean _jspx_th_search_005fforEachEntry_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachEntry_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachEntry_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFeed_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(73,16) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachEntry_005f0.setVar("entry");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(73,16) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachEntry_005f0.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(73,16) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachEntry_005f0.setVarStatus("entryStatus");
      int[] _jspx_push_body_count_search_005fforEachEntry_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachEntry_005f0 = _jspx_th_search_005fforEachEntry_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachEntry_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                    ");
            out.write("\r\n");
            out.write("                    ");
            if (_jspx_meth_config_005fgetOption_005f12(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("                    ");
            if (_jspx_meth_config_005fgetOption_005f13(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("                    <div class=\"hitDetailsHeaderWithToolbar widgetHeaderWithToolbar\">\r\n");
            out.write("                        <div class=\"hitDetailsHeaderTitle\">\r\n");
            out.write("                            ");
            if (_jspx_meth_c_005fif_005f1(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("                            ");
            if (_jspx_meth_c_005fif_005f2(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("                        </div>\r\n");
            out.write("\r\n");
            out.write("                        <div class=\"headerToolbar\" style=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:format('--icons-size:%spx;', buttonsIconSize, '')}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_5));
            out.write("\">\r\n");
            out.write("                                ");
            out.write("\r\n");
            out.write("                            ");
            if (_jspx_meth_c_005fif_005f3(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("                        </div>\r\n");
            out.write("                    </div>\r\n");
            out.write("\r\n");
            out.write("                    <ul class=\"hits\">\r\n");
            out.write("                            ");
            out.write("\r\n");
            out.write("                        ");
            if (_jspx_meth_config_005fgetOption_005f14(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("                        ");
            if (_jspx_meth_render_005ftemplate_005f4(_jspx_th_search_005fforEachEntry_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
              return true;
            out.write("\r\n");
            out.write("                    </ul>\r\n");
            out.write("\r\n");
            out.write("                ");
            int evalDoAfterBody = _jspx_th_search_005fforEachEntry_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachEntry_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachEntry_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachEntry_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachEntry_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvarStatus_005fvar_005ffeed.reuse(_jspx_th_search_005fforEachEntry_005f0);
      _jspx_th_search_005fforEachEntry_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachEntry_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachEntry_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f12 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f12_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f12.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f12.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(75,20) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setVar("hitTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(75,20) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setName("hitTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(75,20) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setDefaultValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry.title}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(75,20) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(75,20) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f12.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_config_005fgetOption_005f12 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f12 = _jspx_th_config_005fgetOption_005f12.doStartTag();
        if (_jspx_th_config_005fgetOption_005f12.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f12[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f12.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f12.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f12);
      _jspx_th_config_005fgetOption_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f12, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f13 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f13_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f13.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f13.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(76,20) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setVar("hitUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(76,20) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setName("hitUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(76,20) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(76,20) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f13.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_config_005fgetOption_005f13 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f13 = _jspx_th_config_005fgetOption_005f13.doStartTag();
        if (_jspx_th_config_005fgetOption_005f13.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f13[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f13.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f13.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fnobody.reuse(_jspx_th_config_005fgetOption_005f13);
      _jspx_th_config_005fgetOption_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f13, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(80,28) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:hasParam(widget, 'title')}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_2)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                <span class=\"widgetTitle\">\r\n");
          out.write("                                   ");
          if (_jspx_meth_string_005feval_005f0(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                </span>\r\n");
          out.write("                            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f0_reused = false;
    try {
      _jspx_th_string_005feval_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(82,35) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getStringParam(widget, 'title','' )}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_3));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(82,35) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005feval_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f0 = _jspx_th_string_005feval_005f0.doStartTag();
        if (_jspx_th_string_005feval_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeeds_005fnobody.reuse(_jspx_th_string_005feval_005f0);
      _jspx_th_string_005feval_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f0, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(86,28) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty hitTitle && plma:getBooleanParam(widget, 'titleInHeader', false)}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_4)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                <div class=\"hitHeader\">\r\n");
          out.write("                                    ");
          if (_jspx_meth_render_005ftemplate_005f1(_jspx_th_c_005fif_005f2, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                </div>\r\n");
          out.write("                            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f1);
    try {
      _jspx_th_render_005ftemplate_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f1.setParent(_jspx_th_c_005fif_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(88,36) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f1.setTemplate("templates/hit-details-title.jsp");
      _jspx_th_render_005ftemplate_005f1.setJspBody(new Helper( 2, _jspx_page_context, _jspx_th_render_005ftemplate_005f1, _jspx_push_body_count_search_005fforEachEntry_005f0));
      _jspx_th_render_005ftemplate_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f3 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f3);
    try {
      _jspx_th_render_005fparameter_005f3.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f3.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(89,40) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setName("hitTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(89,40) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hitTitle}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f3.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f3);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f4 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f4);
    try {
      _jspx_th_render_005fparameter_005f4.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f4.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(90,40) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setName("entry");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(90,40) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f4.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f4);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f5 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f5);
    try {
      _jspx_th_render_005fparameter_005f5.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f5.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(91,40) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setName("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(91,40) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f5.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f5);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f6 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f6);
    try {
      _jspx_th_render_005fparameter_005f6.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f6.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(92,40) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setName("iconContainer");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(92,40) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${false}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f6.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f6);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(100,28) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${showCloseButton == 'true' || showOpenButton == 'true' || plma:hasSubWidget(widget, 'plmaButtonsContainer')}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_6)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                <div class=\"hitDetailsButtons iconsPlaceholder\" style=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fontSize}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("                                        ");
          out.write("\r\n");
          out.write("                                    ");
          if (_jspx_meth_c_005fif_005f4(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("                                        ");
          out.write("\r\n");
          out.write("                                    ");
          if (_jspx_meth_c_005fif_005f6(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("                                        ");
          out.write("\r\n");
          out.write("                                    ");
          if (_jspx_meth_c_005fif_005f7(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                    <span class=\"iconDiv\">\r\n");
          out.write("                                        ");
          if (_jspx_meth_c_005fif_005f8(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                        ");
          if (_jspx_meth_c_005fif_005f9(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                    </span>\r\n");
          out.write("                                </div>\r\n");
          out.write("                            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f4_reused = false;
    try {
      _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(103,36) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${showCloseButton == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
      if (_jspx_eval_c_005fif_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                        <div class=\"closeHitDetailsButton\">\r\n");
          out.write("										<span class=\"plmaButton iconDiv\">\r\n");
          out.write("											<i class=\"fonticon fonticon-cancel\" title=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${closeButtonLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"></i>\r\n");
          out.write("											");
          if (_jspx_meth_c_005fif_005f5(_jspx_th_c_005fif_005f4, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("										</span>\r\n");
          out.write("                                        </div>\r\n");
          out.write("                                    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      _jspx_th_c_005fif_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f5 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f5_reused = false;
    try {
      _jspx_th_c_005fif_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(107,11) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${showCloseButtonLabel}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f5 = _jspx_th_c_005fif_005f5.doStartTag();
      if (_jspx_eval_c_005fif_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                                <span class=\"button-label\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${closeButtonLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</span>\r\n");
          out.write("                                            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f5);
      _jspx_th_c_005fif_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f6 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f6_reused = false;
    try {
      _jspx_th_c_005fif_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(115,36) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f6.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty hitUrl && showOpenButton == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f6 = _jspx_th_c_005fif_005f6.doStartTag();
      if (_jspx_eval_c_005fif_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                        <div class=\"openHitBtn iconDiv\">\r\n");
          out.write("                                            <a href=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hitUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" target=\"_blank\" class=\"action-btn\" title=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${openButtonTooltip}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("                                                <span class=\"fonticon fonticon-export\"></span>\r\n");
          out.write("                                            </a>\r\n");
          out.write("                                        </div>\r\n");
          out.write("                                    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f6);
      _jspx_th_c_005fif_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f7 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f7_reused = false;
    try {
      _jspx_th_c_005fif_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(124,36) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f7.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:hasSubWidget(widget, 'plmaButtonsContainer')}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_6)).booleanValue());
      int _jspx_eval_c_005fif_005f7 = _jspx_th_c_005fif_005f7.doStartTag();
      if (_jspx_eval_c_005fif_005f7 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                        ");
          if (_jspx_meth_plma_005fforEachSubWidget_005f0(_jspx_th_c_005fif_005f7, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f7.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f7);
      _jspx_th_c_005fif_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fforEachSubWidget_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:forEachSubWidget
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.widget.ForEachSubWidgetTag _jspx_th_plma_005fforEachSubWidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.widget.ForEachSubWidgetTag) _005fjspx_005ftagPool_005fplma_005fforEachSubWidget_0026_005fincludes.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.widget.ForEachSubWidgetTag.class);
    boolean _jspx_th_plma_005fforEachSubWidget_005f0_reused = false;
    try {
      _jspx_th_plma_005fforEachSubWidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fforEachSubWidget_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f7);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(125,40) name = includes type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fforEachSubWidget_005f0.setIncludes("plmaButtonsContainer");
      int[] _jspx_push_body_count_plma_005fforEachSubWidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fforEachSubWidget_005f0 = _jspx_th_plma_005fforEachSubWidget_005f0.doStartTag();
        if (_jspx_eval_plma_005fforEachSubWidget_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                                            <div class=\"iconDiv\">\r\n");
            out.write("                                                ");
            if (_jspx_meth_render_005fwidget_005f0(_jspx_th_plma_005fforEachSubWidget_005f0, _jspx_page_context, _jspx_push_body_count_plma_005fforEachSubWidget_005f0))
              return true;
            out.write("\r\n");
            out.write("                                            </div>\r\n");
            out.write("                                        ");
            int evalDoAfterBody = _jspx_th_plma_005fforEachSubWidget_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_plma_005fforEachSubWidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fforEachSubWidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fforEachSubWidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fforEachSubWidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fforEachSubWidget_0026_005fincludes.reuse(_jspx_th_plma_005fforEachSubWidget_005f0);
      _jspx_th_plma_005fforEachSubWidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fforEachSubWidget_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fforEachSubWidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fwidget_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_plma_005fforEachSubWidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_plma_005fforEachSubWidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:widget
    com.exalead.cv360.searchui.view.jspapi.render.WidgetTag _jspx_th_render_005fwidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.WidgetTag) _005fjspx_005ftagPool_005frender_005fwidget_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.render.WidgetTag.class);
    boolean _jspx_th_render_005fwidget_005f0_reused = false;
    try {
      _jspx_th_render_005fwidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005fwidget_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_plma_005fforEachSubWidget_005f0);
      int[] _jspx_push_body_count_render_005fwidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005fwidget_005f0 = _jspx_th_render_005fwidget_005f0.doStartTag();
        if (_jspx_th_render_005fwidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005fwidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005fwidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005fwidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005fwidget_005fnobody.reuse(_jspx_th_render_005fwidget_005f0);
      _jspx_th_render_005fwidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005fwidget_005f0, _jsp_getInstanceManager(), _jspx_th_render_005fwidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f8 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f8_reused = false;
    try {
      _jspx_th_c_005fif_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(132,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f8.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getBooleanParam(widget, 'enableCompare', false)}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_4)).booleanValue());
      int _jspx_eval_c_005fif_005f8 = _jspx_th_c_005fif_005f8.doStartTag();
      if (_jspx_eval_c_005fif_005f8 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                            ");
          if (_jspx_meth_c_005fset_005f2(_jspx_th_c_005fif_005f8, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                            ");
          if (_jspx_meth_i18n_005fmessage_005f1(_jspx_th_c_005fif_005f8, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                            ");
          if (_jspx_meth_render_005ftemplate_005f2(_jspx_th_c_005fif_005f8, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f8.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f8);
      _jspx_th_c_005fif_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f2_reused = false;
    try {
      _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(133,44) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setVar("compareIdMetaName");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(133,44) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(133,44) '${plma:getStringParam(widget, 'compareIdMetaName', '')}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_3),"${plma:getStringParam(widget, 'compareIdMetaName', '')}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
      if (_jspx_th_c_005fset_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
      _jspx_th_c_005fset_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f1 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fwidget_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f1_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f1.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(134,44) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setVar("compareLabelI18N");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(134,44) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setCode("widgets.plma.hitdetails.compare.add");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(134,44) name = widget type = com.exalead.cv360.searchui.configuration.v10.Widget reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setWidget((com.exalead.cv360.searchui.configuration.v10.Widget) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", com.exalead.cv360.searchui.configuration.v10.Widget.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f1 = _jspx_th_i18n_005fmessage_005f1.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fwidget_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f1);
      _jspx_th_i18n_005fmessage_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f1, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f2);
    try {
      _jspx_th_render_005ftemplate_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f2.setParent(_jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(135,44) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f2.setTemplate("templates/compareHit.jsp");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(135,44) name = widget type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f2.setWidget("compareHit");
      _jspx_th_render_005ftemplate_005f2.setJspBody(new Helper( 3, _jspx_page_context, _jspx_th_render_005ftemplate_005f2, _jspx_push_body_count_search_005fforEachEntry_005f0));
      _jspx_th_render_005ftemplate_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f7 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f7);
    try {
      _jspx_th_render_005fparameter_005f7.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f7.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(136,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setName("compareCollection");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(136,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getStringParam(widget, 'compareCollection', 'compare' )}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_3));
      _jspx_th_render_005fparameter_005f7.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f7);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f8 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f8);
    try {
      _jspx_th_render_005fparameter_005f8.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f8.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(138,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f8.setName("hitID");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(138,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f8.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getEntryID(entry, compareIdMetaName)}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_7));
      _jspx_th_render_005fparameter_005f8.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f8);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f9 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f9);
    try {
      _jspx_th_render_005fparameter_005f9.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f9.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(139,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f9.setName("compareLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(139,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f9.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getStringParam(widget, 'compareLabel', 'Add to Compare')}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_3));
      _jspx_th_render_005fparameter_005f9.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f9);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f10 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f10);
    try {
      _jspx_th_render_005fparameter_005f10.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f10.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(140,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f10.setName("showCompareLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(140,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f10.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getBooleanParam(widget, 'showCompareLabel', 'true' )}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_4));
      _jspx_th_render_005fparameter_005f10.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f10);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f9 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f9_reused = false;
    try {
      _jspx_th_c_005fif_005f9.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(143,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f9.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getBooleanParam(widget, 'enableFavorite', false)}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_4)).booleanValue());
      int _jspx_eval_c_005fif_005f9 = _jspx_th_c_005fif_005f9.doStartTag();
      if (_jspx_eval_c_005fif_005f9 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                            ");
          if (_jspx_meth_c_005fset_005f3(_jspx_th_c_005fif_005f9, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                            ");
          if (_jspx_meth_i18n_005fmessage_005f2(_jspx_th_c_005fif_005f9, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                            ");
          if (_jspx_meth_render_005ftemplate_005f3(_jspx_th_c_005fif_005f9, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f0))
            return true;
          out.write("\r\n");
          out.write("                                        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f9.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f9);
      _jspx_th_c_005fif_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f9, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f3_reused = false;
    try {
      _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f9);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(144,44) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setVar("favoriteIdMetaName");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(144,44) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(144,44) '${plma:getStringParam(widget, 'favoriteIdMetaName', '')}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_3),"${plma:getStringParam(widget, 'favoriteIdMetaName', '')}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
      if (_jspx_th_c_005fset_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
      _jspx_th_c_005fset_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f9, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f2 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fwidget_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f2_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f2.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f9);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(145,44) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setVar("favoriteLabelI18N");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(145,44) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setCode("widgets.plma.hitdetails.favorite.add");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(145,44) name = widget type = com.exalead.cv360.searchui.configuration.v10.Widget reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setWidget((com.exalead.cv360.searchui.configuration.v10.Widget) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", com.exalead.cv360.searchui.configuration.v10.Widget.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_i18n_005fmessage_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f2 = _jspx_th_i18n_005fmessage_005f2.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fwidget_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f2);
      _jspx_th_i18n_005fmessage_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f2, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f9, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f3 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f3);
    try {
      _jspx_th_render_005ftemplate_005f3.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f3.setParent(_jspx_th_c_005fif_005f9);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(146,44) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f3.setTemplate("templates/preferredHit.jsp");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(146,44) name = widget type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f3.setWidget("preferredHit");
      _jspx_th_render_005ftemplate_005f3.setJspBody(new Helper( 4, _jspx_page_context, _jspx_th_render_005ftemplate_005f3, _jspx_push_body_count_search_005fforEachEntry_005f0));
      _jspx_th_render_005ftemplate_005f3.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f3);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f11 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f11);
    try {
      _jspx_th_render_005fparameter_005f11.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f11.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(147,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f11.setName("feeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(147,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f11.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f11.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f11);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f12 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f12);
    try {
      _jspx_th_render_005fparameter_005f12.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f12.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(148,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f12.setName("entry");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(148,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f12.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f12.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f12);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f13 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f13);
    try {
      _jspx_th_render_005fparameter_005f13.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f13.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(149,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f13.setName("label");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(149,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f13.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getStringParam(widget, 'favoriteLabel', 'Add to Favorites' )}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_3));
      _jspx_th_render_005fparameter_005f13.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f13);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f14 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f14);
    try {
      _jspx_th_render_005fparameter_005f14.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f14.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(150,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f14.setName("showLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(150,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f14.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getBooleanParam(widget, 'showFavoriteLabel', 'false' )}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_4));
      _jspx_th_render_005fparameter_005f14.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f14);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f15(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f15 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f15);
    try {
      _jspx_th_render_005fparameter_005f15.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f15.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(151,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f15.setName("collection");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(151,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f15.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getStringParam(widget, 'favoriteCollection', 'favoriteHits' )}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_3));
      _jspx_th_render_005fparameter_005f15.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f15);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f16(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f16 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f16);
    try {
      _jspx_th_render_005fparameter_005f16.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f16.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(153,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f16.setName("counterSelector");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(153,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f16.setValue(".preferred-hits");
      _jspx_th_render_005fparameter_005f16.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f16);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f17(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f17 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f17);
    try {
      _jspx_th_render_005fparameter_005f17.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f17.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(154,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f17.setName("hitID");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(154,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f17.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getEntryID(entry, favoriteIdMetaName)}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_7));
      _jspx_th_render_005fparameter_005f17.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f17);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f18(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f18 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f18);
    try {
      _jspx_th_render_005fparameter_005f18.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f18.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(155,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f18.setName("buttonPrefix");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(155,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f18.setValue("favorite");
      _jspx_th_render_005fparameter_005f18.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f18);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f14 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f14_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f14.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f14.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(166,24) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setVar("customJspPathHit");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(166,24) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setName("customJspPathHit");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(166,24) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(166,24) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(166,24) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f14.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f14 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f14 = _jspx_th_config_005fgetOption_005f14.doStartTag();
        if (_jspx_th_config_005fgetOption_005f14.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f14[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f14.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f14.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005ffeed_005fentry_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f14);
      _jspx_th_config_005fgetOption_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f14, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f4 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f4);
    try {
      _jspx_th_render_005ftemplate_005f4.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f4.setParent(_jspx_th_search_005fforEachEntry_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(167,24) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f4.setTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customJspPathHit}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(167,24) name = defaultTemplate type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f4.setDefaultTemplate("templates/hit-detail.jsp");
      _jspx_th_render_005ftemplate_005f4.setJspBody(new Helper( 5, _jspx_page_context, _jspx_th_render_005ftemplate_005f4, _jspx_push_body_count_search_005fforEachEntry_005f0));
      _jspx_th_render_005ftemplate_005f4.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f4);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f19(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f19 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f19);
    try {
      _jspx_th_render_005fparameter_005f19.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f19.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(168,28) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f19.setName("accessFeeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(168,28) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f19.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f19.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f19);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f20(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f20 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f20);
    try {
      _jspx_th_render_005fparameter_005f20.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f20.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(169,28) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f20.setName("feed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(169,28) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f20.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f20.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f20);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f21(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f21 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f21);
    try {
      _jspx_th_render_005fparameter_005f21.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f21.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(170,28) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f21.setName("showCloseButton");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(170,28) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f21.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${showCloseButton}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f21.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f21);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f22(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f22 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f22);
    try {
      _jspx_th_render_005fparameter_005f22.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f22.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(171,28) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f22.setName("entry");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(171,28) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f22.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f22.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f22);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f23(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f23 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f23);
    try {
      _jspx_th_render_005fparameter_005f23.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f23.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(172,28) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f23.setName("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(172,28) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f23.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f23.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f23);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f10 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f10_reused = false;
    try {
      _jspx_th_c_005fif_005f10.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f10.setParent(new jakarta.servlet.jsp.tagext.TagAdapter((jakarta.servlet.jsp.tagext.SimpleTag) _jspx_parent));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(173,28) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f10.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hitUrl != null}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f10 = _jspx_th_c_005fif_005f10.doStartTag();
      if (_jspx_eval_c_005fif_005f10 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                ");
          if (_jspx_meth_render_005fparameter_005f24(_jspx_th_c_005fif_005f10, _jspx_page_context, _jspx_push_body_count))
            return true;
          out.write("\r\n");
          out.write("                            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f10.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        throw new jakarta.servlet.jsp.SkipPageException();
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f10);
      _jspx_th_c_005fif_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f10, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f24(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f10, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f24 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f24);
    try {
      _jspx_th_render_005fparameter_005f24.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f24.setParent(_jspx_th_c_005fif_005f10);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(174,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f24.setName("hitUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(174,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f24.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hitUrl}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f24.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f24);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f11 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f11_reused = false;
    try {
      _jspx_th_c_005fif_005f11.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f11.setParent(new jakarta.servlet.jsp.tagext.TagAdapter((jakarta.servlet.jsp.tagext.SimpleTag) _jspx_parent));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(176,28) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f11.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hitTitle != null}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f11 = _jspx_th_c_005fif_005f11.doStartTag();
      if (_jspx_eval_c_005fif_005f11 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                ");
          if (_jspx_meth_render_005fparameter_005f25(_jspx_th_c_005fif_005f11, _jspx_page_context, _jspx_push_body_count))
            return true;
          out.write("\r\n");
          out.write("                            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f11.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        throw new jakarta.servlet.jsp.SkipPageException();
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f11);
      _jspx_th_c_005fif_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f11, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f25(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f11, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f25 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f25);
    try {
      _jspx_th_render_005fparameter_005f25.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f25.setParent(_jspx_th_c_005fif_005f11);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(177,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f25.setName("hitTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(177,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f25.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hitTitle}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f25.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f25);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f26(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f26 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f26);
    try {
      _jspx_th_render_005fparameter_005f26.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f26.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(179,28) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f26.setName("accessFeedsStatus");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(179,28) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f26.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${accessFeedsStatus}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f26.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f26);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f27(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f27 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f27);
    try {
      _jspx_th_render_005fparameter_005f27.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f27.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(180,28) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f27.setName("entryStatus");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(180,28) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f27.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entryStatus}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f27.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f27);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(187,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setVar("decorators");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(187,12) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setName("exa.io.HitDecorationInterface");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(187,12) name = separator type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setSeparator("##");
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f0 = _jspx_th_config_005fgetOptionsComposite_005f0.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fseparator_005fname_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f0);
      _jspx_th_config_005fgetOptionsComposite_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f12 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f12_reused = false;
    try {
      _jspx_th_c_005fif_005f12.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f12.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(188,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f12.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fn:length(decorators)>0}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_8)).booleanValue());
      int _jspx_eval_c_005fif_005f12 = _jspx_th_c_005fif_005f12.doStartTag();
      if (_jspx_eval_c_005fif_005f12 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_render_005frenderScript_005f0(_jspx_th_c_005fif_005f12, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f12.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f12.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f12);
      _jspx_th_c_005fif_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f12, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f12, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f0_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f12);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(189,16) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f0.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f0 = _jspx_th_render_005frenderScript_005f0.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f0);
          }
          do {
            out.write("\r\n");
            out.write("                    ");
            if (_jspx_meth_c_005fforEach_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("                ");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f0);
      _jspx_th_render_005frenderScript_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f0_reused = false;
    try {
      _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(190,20) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setVar("decorator");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(190,20) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(190,20) '${decorators}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${decorators}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
        if (_jspx_eval_c_005fforEach_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                        ");
            if (_jspx_meth_c_005fif_005f13(_jspx_th_c_005fforEach_005f0, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
              return true;
            out.write("\r\n");
            out.write("                    ");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
      _jspx_th_c_005fforEach_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f13 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f13_reused = false;
    try {
      _jspx_th_c_005fif_005f13.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f13.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(191,24) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f13.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fn:length(decorator[1])>0}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_8)).booleanValue());
      int _jspx_eval_c_005fif_005f13 = _jspx_th_c_005fif_005f13.doStartTag();
      if (_jspx_eval_c_005fif_005f13 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_c_005fset_005f4(_jspx_th_c_005fif_005f13, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_c_005fif_005f14(_jspx_th_c_005fif_005f13, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                            exa.io.register('exa.io.HitDecorationInterface', '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${decorator[0]}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("', function (hitDecorationInterface) {\r\n");
          out.write("                            ");
          if (_jspx_meth_search_005fforEachFeed_005f1(_jspx_th_c_005fif_005f13, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                            });\r\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f13.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f13.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f13);
      _jspx_th_c_005fif_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f13, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f13, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f4_reused = false;
    try {
      _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f13);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(192,28) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setVar("insertMethod");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(192,28) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(192,28) '${decorator[2]}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${decorator[2]}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
      if (_jspx_th_c_005fset_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
      _jspx_th_c_005fset_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f13, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f14 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f14_reused = false;
    try {
      _jspx_th_c_005fif_005f14.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f14.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f13);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(193,28) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f14.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fn:length(insertMethod)==0}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_8)).booleanValue());
      int _jspx_eval_c_005fif_005f14 = _jspx_th_c_005fif_005f14.doStartTag();
      if (_jspx_eval_c_005fif_005f14 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                ");
          if (_jspx_meth_c_005fset_005f5(_jspx_th_c_005fif_005f14, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f14.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f14.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f14);
      _jspx_th_c_005fif_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f14, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f14, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f5 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f5_reused = false;
    try {
      _jspx_th_c_005fset_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f14);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(194,32) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setVar("insertMethod");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(194,32) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(194,32) 'prepend'",_jsp_getExpressionFactory().createValueExpression("prepend",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f5 = _jspx_th_c_005fset_005f5.doStartTag();
      if (_jspx_th_c_005fset_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f5);
      _jspx_th_c_005fset_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachFeed_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f13, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachFeed
    com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag _jspx_th_search_005fforEachFeed_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag) _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvarStatus_005fvar_005ffeeds.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag.class);
    boolean _jspx_th_search_005fforEachFeed_005f1_reused = false;
    try {
      _jspx_th_search_005fforEachFeed_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachFeed_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f13);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(197,28) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f1.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(197,28) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f1.setVar("feed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(197,28) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f1.setVarStatus("accessFeedsStatus");
      int[] _jspx_push_body_count_search_005fforEachFeed_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachFeed_005f1 = _jspx_th_search_005fforEachFeed_005f1.doStartTag();
        if (_jspx_eval_search_005fforEachFeed_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                                ");
            if (_jspx_meth_search_005fforEachEntry_005f1(_jspx_th_search_005fforEachFeed_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachFeed_005f1))
              return true;
            out.write("\r\n");
            out.write("                            ");
            int evalDoAfterBody = _jspx_th_search_005fforEachFeed_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachFeed_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachFeed_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachFeed_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachFeed_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvarStatus_005fvar_005ffeeds.reuse(_jspx_th_search_005fforEachFeed_005f1);
      _jspx_th_search_005fforEachFeed_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachFeed_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fforEachFeed_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachEntry_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFeed_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFeed_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachEntry
    com.exalead.cv360.searchui.view.jspapi.search.ForEachEntryTag _jspx_th_search_005fforEachEntry_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachEntryTag) _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvar_005ffeed.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachEntryTag.class);
    boolean _jspx_th_search_005fforEachEntry_005f1_reused = false;
    try {
      _jspx_th_search_005fforEachEntry_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachEntry_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFeed_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(198,32) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachEntry_005f1.setVar("entry");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(198,32) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachEntry_005f1.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fforEachEntry_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachEntry_005f1 = _jspx_th_search_005fforEachEntry_005f1.doStartTag();
        if (_jspx_eval_search_005fforEachEntry_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                                    $('#");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("').find('");
            if (_jspx_meth_string_005feval_005f1(_jspx_th_search_005fforEachEntry_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachEntry_005f1))
              return true;
            out.write("')\r\n");
            out.write("                                    .each(function() {\r\n");
            out.write("                                    var decorationHtml = hitDecorationInterface.getDecoration('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${search:cleanEntryId(entry)}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_9));
            out.write("');\r\n");
            out.write("                                    if (decorationHtml) {\r\n");
            out.write("                                    $(decorationHtml).");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${insertMethod}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("To(this);\r\n");
            out.write("                                    hitDecorationInterface.afterDecorating('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${search:cleanEntryId(entry)}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_9));
            out.write("');\r\n");
            out.write("                                    }\r\n");
            out.write("                                    });\r\n");
            out.write("                                ");
            int evalDoAfterBody = _jspx_th_search_005fforEachEntry_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachEntry_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachEntry_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachEntry_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachEntry_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachEntry_0026_005fvar_005ffeed.reuse(_jspx_th_search_005fforEachEntry_005f1);
      _jspx_th_search_005fforEachEntry_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachEntry_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fforEachEntry_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachEntry_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachEntry_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f1 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeed_005fentry_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f1_reused = false;
    try {
      _jspx_th_string_005feval_005f1.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachEntry_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(199,57) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${decorator[1]}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(199,57) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${entry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(199,57) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005feval_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f1 = _jspx_th_string_005feval_005f1.doStartTag();
        if (_jspx_th_string_005feval_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeed_005fentry_005fnobody.reuse(_jspx_th_string_005feval_005f1);
      _jspx_th_string_005feval_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f1, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f15(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f15 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f15_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f15.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f15.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(218,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f15.setName("onInit");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(218,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f15.setVar("onInit");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(218,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f15.setDefaultValue("function(){}");
      int[] _jspx_push_body_count_config_005fgetOption_005f15 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f15 = _jspx_th_config_005fgetOption_005f15.doStartTag();
        if (_jspx_th_config_005fgetOption_005f15.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f15[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f15.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f15.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f15);
      _jspx_th_config_005fgetOption_005f15_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f15, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f15_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f16(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f16 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f16_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f16.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f16.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(219,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f16.setName("scrollTop");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(219,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f16.setVar("scrollTop");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(219,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f16.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f16 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f16 = _jspx_th_config_005fgetOption_005f16.doStartTag();
        if (_jspx_th_config_005fgetOption_005f16.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f16[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f16.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f16.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f16);
      _jspx_th_config_005fgetOption_005f16_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f16, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f16_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f1 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f1_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f1.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(220,0) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f1.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f1 = _jspx_th_render_005frenderScript_005f1.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f1[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f1);
          }
          do {
            out.write("\r\n");
            out.write("    $('.hit-detail-link-refine').each(function(iLink, eLink){\r\n");
            out.write("    var url = new BuildUrl(eLink.href);\r\n");
            out.write("    url.removeParameter('hit');\r\n");
            out.write("    eLink.href = url.toString() + '#");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hitParamValue[0]}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("    });\r\n");
            out.write("\r\n");
            out.write("    var hitDetail = new HitDetail('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("', {'feeds':");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:toJSArray(usedFeeds)}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_10));
            out.write(", 'scrollTop': '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${scrollTop}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("'});\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fif_005f15(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write("\r\n");
            out.write("    (");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${onInit}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(").call(null, $('.");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("'));\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fif_005f16(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("    // Trigger an event on any result list view\r\n");
            out.write("    $('.resultlistView').trigger('hit-details:opened');\r\n");
            out.write("\r\n");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f1[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f1);
      _jspx_th_render_005frenderScript_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f1, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f15(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f15 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f15_reused = false;
    try {
      _jspx_th_c_005fif_005f15.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f15.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(228,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f15.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${useExternalisedConfig == 'true' && disableHitDetailsPrefTab == 'false'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f15 = _jspx_th_c_005fif_005f15.doStartTag();
      if (_jspx_eval_c_005fif_005f15 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        new PlmaHitDetailsPrefTab();\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f15.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f15.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f15);
      _jspx_th_c_005fif_005f15_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f15, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f15_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f16(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f16 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f16_reused = false;
    try {
      _jspx_th_c_005fif_005f16.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f16.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaHitDetails/widget.jsp(233,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f16.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${showCloseButton == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f16 = _jspx_th_c_005fif_005f16.doStartTag();
      if (_jspx_eval_c_005fif_005f16 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        new PLMAHitDetailsClose('");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("');\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f16.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f16.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f16);
      _jspx_th_c_005fif_005f16_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f16, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f16_reused);
    }
    return false;
  }

  private class Helper
      extends org.apache.jasper.runtime.JspFragmentHelper
  {
    private jakarta.servlet.jsp.tagext.JspTag _jspx_parent;
    private int[] _jspx_push_body_count;

    public Helper( int discriminator, jakarta.servlet.jsp.JspContext jspContext, jakarta.servlet.jsp.tagext.JspTag _jspx_parent, int[] _jspx_push_body_count ) {
      super( discriminator, jspContext, _jspx_parent );
      this._jspx_parent = _jspx_parent;
      this._jspx_push_body_count = _jspx_push_body_count;
    }
    public boolean invoke0( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                    ");
      if (_jspx_meth_render_005fparameter_005f0(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                    ");
      if (_jspx_meth_render_005fparameter_005f1(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                ");
      return false;
    }
    public boolean invoke1( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                    ");
      if (_jspx_meth_render_005fparameter_005f2(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                ");
      return false;
    }
    public boolean invoke2( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                                        ");
      if (_jspx_meth_render_005fparameter_005f3(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                        ");
      if (_jspx_meth_render_005fparameter_005f4(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                        ");
      if (_jspx_meth_render_005fparameter_005f5(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                        ");
      if (_jspx_meth_render_005fparameter_005f6(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                    ");
      return false;
    }
    public boolean invoke3( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f7(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f8(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f9(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f10(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                            ");
      return false;
    }
    public boolean invoke4( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f11(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f12(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f13(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f14(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f15(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f16(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f17(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f18(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                            ");
      return false;
    }
    public boolean invoke5( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                            ");
      if (_jspx_meth_render_005fparameter_005f19(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                            ");
      if (_jspx_meth_render_005fparameter_005f20(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                            ");
      if (_jspx_meth_render_005fparameter_005f21(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                            ");
      if (_jspx_meth_render_005fparameter_005f22(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                            ");
      if (_jspx_meth_render_005fparameter_005f23(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                            ");
      if (_jspx_meth_c_005fif_005f10(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                            ");
      if (_jspx_meth_c_005fif_005f11(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                            ");
      if (_jspx_meth_render_005fparameter_005f26(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                            ");
      if (_jspx_meth_render_005fparameter_005f27(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                        ");
      return false;
    }
    public void invoke( java.io.Writer writer )
      throws jakarta.servlet.jsp.JspException
    {
      jakarta.servlet.jsp.JspWriter out = null;
      if( writer != null ) {
        out = this.jspContext.pushBody(writer);
      } else {
        out = this.jspContext.getOut();
      }
      try {
        Object _jspx_saved_JspContext = this.jspContext.getELContext().getContext(jakarta.servlet.jsp.JspContext.class);
        this.jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,this.jspContext);
        switch( this.discriminator ) {
          case 0:
            invoke0( out );
            break;
          case 1:
            invoke1( out );
            break;
          case 2:
            invoke2( out );
            break;
          case 3:
            invoke3( out );
            break;
          case 4:
            invoke4( out );
            break;
          case 5:
            invoke5( out );
            break;
        }
        jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,_jspx_saved_JspContext);
      }
      catch( java.lang.Throwable e ) {
        if (e instanceof jakarta.servlet.jsp.SkipPageException)
            throw (jakarta.servlet.jsp.SkipPageException) e;
        throw new jakarta.servlet.jsp.JspException( e );
      }
      finally {
        if( writer != null ) {
          this.jspContext.popBody();
        }
      }
    }
  }
}
