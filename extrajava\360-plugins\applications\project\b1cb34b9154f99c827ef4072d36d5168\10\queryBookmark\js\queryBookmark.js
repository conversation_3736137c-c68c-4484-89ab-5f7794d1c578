/**
 * Bookmarks library
 *
 * @param uCssId Widget CSS UUID
 * @param options Library options
 * @constructor Constructor
 */
var QueryBookmarks = function (uCssId, options) {
    this.uCssId = uCssId;
    this.widget = $('.' + uCssId);
    this.bookmarkssContainer = this.widget.find('.bookmarks-layout');
    this.options = options;
    this.selectMenuId = '#' + this.uCssId + '-bookmark-select';
    this.init();
};

QueryBookmarks.prototype.init = function () {
    $("#bmkInput").focus(function () {
        if ($('.bookmarks-list').text().trim() != ''){
            $('.bookmarks-list').removeClass('hidden');
        }
    });

    this.initListEvents();

    $('.bookmark-add-input').on('click', $.proxy(function (e) {
        var url = window.location.href;
        if (url.indexOf("?") == -1) {
            $.notify('No parameters applied, will not save bookmark', "error");
        } else {
            var params = url.split("?")[1];
            var name = $('#bmkInput').val();
            var bookmarksMgt = this;
            $.ajax({
                method: 'POST',
                url: this.options.url + '/save',
                dataType: 'JSON',
                async: false,
                data: {
                    page: this.options.page,
                    name: name,
                    params: params
                },
                success: function (data) {
                    console.log(data);
                    $('.bookmark-selected').addClass('bookmark-element');
                    $('.bookmark-selected').removeClass('bookmark-selected');
                    $.notify('Bookmark successfully added', "success");
                    bookmarksMgt.addBookmark(data);
                    $('.bookmarks-list').addClass('hidden');
                    bookmarksMgt.initListEvents();
                    $(e.target).addClass('hidden');
                },
                error: function (data) {
                    $.notify('Error adding bookmark (' + data.responseJSON.error + ')', "error");
                }
            });
        }
    }, this));

    // If text is not empty -> display add button
    $('#bmkInput').on('input', $.proxy(function (e) {
        var text = $(e.target).val();
        if (text.length > 0) {
            $('.bookmark-add-input').removeClass('hidden');
            this.filter(text);
        } else {
            $('.bookmark-add-input').addClass('hidden');
            $('.bookmark-item').removeClass('hidden');
        }
    }, this));

    this.widget.find(".fonticon-bookmark").on('click', $.proxy(function (e) {
        this.widget.find('.searchContainer').toggleClass('hidden');
        if (!this.widget.find('.bookmarks-list').hasClass('hidden')){
            this.widget.find('.bookmarks-list').addClass('hidden')
        }
    }, this));
}

QueryBookmarks.prototype.initListEvents = function () {
    $(document).off('click', '.bookmark-item').on('click', '.bookmark-item', $.proxy(function (e) {
        e.stopPropagation();
        var targetUrl = window.location.href;
        if (targetUrl.indexOf("?") != -1) {
            targetUrl = window.location.href.split("?")[0];
        }
        var bookmarkItem = $(e.target).closest('.bookmark-item');
        var bookmarkUrl = new BuildUrl(targetUrl + '?' + bookmarkItem.data('params'));
        bookmarkUrl.addParameter('bookmark', bookmarkItem.data('id'), true);
        targetUrl = bookmarkUrl.toString();

        window.location.href = targetUrl;
    }, this));

    $(document).off('click', '.bookmark-delete').on('click', '.bookmark-delete', $.proxy(function (e) {
        e.stopPropagation();
        $.ajax({
            method: 'DELETE',
            url: this.options.url + '/delete/' + e.target.dataset.id,
            dataType: 'JSON',
            async: false,
            success: function (data) {
                $(e.currentTarget).closest('.bookmark-item').remove();
                $.notify('Bookmark successfully deleted', "success");
            },
            error: function (data) {
                $.notify('Error deleting bookmark (' + data.responseJSON.error + ')', "error");
            }
        });
    }, this));
    $('.bookmark-item').removeClass('hidden');
}

QueryBookmarks.prototype.addBookmark = function (data) {
    $('.bookmarks-list').prepend('<div class="bookmark-item bookmark-selected" data-params="' + data.params + '" data-id="' + data.itemId.id +
        '"><span class="fonticon fonticon-trash query-bookmark bookmark-delete" title="Delete bookmark" data-id="' + data.itemId.id + '"></span><span class="bookmark-name" id="bookmarks-select-' + data.itemId.id +
        '" data-mode="bookmark">' + data.name + '</span></div>');
}

QueryBookmarks.prototype.filter = function (name) {
    $(".bookmark-name").each(function () {
        if ($(this).text().toLowerCase().indexOf(name.toLowerCase()) == -1) {
            $(this).closest('.bookmark-item').addClass('hidden');
        } else {
			 $(this).closest('.bookmark-item').removeClass('hidden');
		}
    });
};


