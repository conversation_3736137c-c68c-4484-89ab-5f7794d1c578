<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import varWidget="widget" varParentEntry="parentEntry" varParentFeed="parentFeed" varFeeds="feeds"/>
<config:getOption var="buttonsIconSize" name="buttonsIconSize"/>
<%--Header title (displayed by default if not empty)--%>
<config:getOption var="title" name="title"/>
<%--Title custom JSP path and widget--%>
<config:getOption var="titleTemplatePath" name="titleTemplatePath"/>
<config:getOption var="titleJspWidget" name="titleJspWidget"/>
<%--Header div additional CSS (not mandatory)--%>
<config:getOption var="headerCSS" name="headerCSS" defaultValue=""/>
<%--Toolbar options (templates and default possible actions)--%>
<%--Toolbar custom JSP path and widget--%>
<config:getOption var="toolbarJspPath" name="toolbarJspPath"/>
<config:getOption var="toolbarJspWidget" name="toolbarJspWidget"/>
<%--Toolbar div additional CSS (not mandatory)--%>
<config:getOption var="toolbarCSS" name="toolbarCSS" defaultValue=""/>
<%--Additional CSS variables to add to parent div--%>
<config:getOptionsComposite var="additionalVariables" name="variables" mapIndex="true" />

<c:set var="extraParentSyles" value="${plma:getCssStyles(additionalVariables)}"/>

<c:if test="${not empty buttonsIconSize}">
	<c:set var="extraParentSyles" value="${plma:format('--icons-size:%spx;', buttonsIconSize, '')} ${plma:getCssStyles(additionalVariables)}"/>
</c:if>

<widget:widget disableStyles="true" varCssId="cssId" varUcssId="uCssId" extraCss="flexContainerWithHeader ${plma:getBooleanParam(widget, 'addBorder', false) ? 'with-border' : ''}"
			   extraStyles="${extraParentSyles}">
	<c:set var="layout" value="${widget.layout}" />
	<c:set var="widthFormat" value="${layout.widthFormat}" />
	<c:if test="${widthFormat == null || widthFormat == ''}">
		<c:set var="widthFormat" value="%" />
	</c:if>

	<%-- Generate container header --%>
	<div id="${uCssId}_header" class="widgetHeaderWithToolbar ${headerCSS}">
		<div class="headerTitle">
			<%-- Generate title part, configured template or title option --%>
			<c:choose>
				<c:when test="${plma:hasParam(widget, 'titleTemplatePath')}">
					<render:template
							template="${plma:getStringParam(widget, 'templateBasePath', '')}${plma:getStringParam(widget, 'titleTemplatePath', '')}"
							widget="${plma:getStringParam(widget, 'titleTemplateWidget', widget.id)}">
					</render:template>
				</c:when>
				<c:otherwise>
					<string:eval string="${plma:getStringParam(widget, 'title','' )}" feeds="${feeds}"/>
				</c:otherwise>
			</c:choose>
		</div>

		<div class="headerToolbar ${toolbarCSS}">
			<%-- If template toolbar is configured: render it --%>
			<c:if test="${plma:hasParam(widget, 'toolbarTemplatePath')}">
				<render:template
						template="${plma:getStringParam(widget, 'templateBasePath', '')}${plma:getStringParam(widget, 'toolbarTemplatePath', '')}"
						widget="${plma:getStringParam(widget, 'toolbarTemplateWidget', widget.id)}">
				</render:template>
				<span class="insert-after"></span>
			</c:if>
			<%-- Get all buttons containers but not recusrsively in subwidgets of cells --%>
			<c:forEach var="table" varStatus="tableStatus" items="${layout.tables}">
				<c:forEach var="row" items="${table.rows}" varStatus="rowStatus">
					<c:forEach varStatus="statusCell" var="cell" items="${row.cells}">
						<c:if test="${plma:hasSubWidget(cell, 'headerButtonsContainer,plmaButtonsContainer,plmaButton')}">
							<plma:forEachSubWidget includes="headerButtonsContainer,plmaButtonsContainer,plmaButton"
												   feed="${parentFeed}" entry="${parentEntry}" widgetContainer="${cell}">
								<%-- Render actions subwidgets in header --%>
								<render:widget />
							</plma:forEachSubWidget>
							<span class="insert-after"></span>
						</c:if>
					</c:forEach>
				</c:forEach>
			</c:forEach>
			<%-- Render default options--%>
			<c:if test="${plma:getBooleanParam(widget, 'enableFullScreen', false)}">
				<span class="fonticon fonticon-resize-full fullscreen-button" title="<i18n:message code="widget.action.fullScreen" widget="${widget}"/>"></span>
				<span class="fonticon fonticon-resize-small fullscreen-button hidden" title="<i18n:message code="widget.action.exitFullScreen" widget="${widget}"/>"></span>
			</c:if>
			<c:if test="${plma:getBooleanParam(widget, 'collapsible', false)}">
				<span class="fonticon fonticon-chevron-down  collapsible-button" title="<i18n:message code="widget.action.collapse" widget="${widget}"/>"></span>
				<span class="fonticon fonticon-chevron-up collapsible-button hidden" title="<i18n:message code="widget.action.expand" widget="${widget}"/>"></span>
			</c:if>
		</div>
	</div>

	<div id="${uCssId}_content" class="widgetContent">
		<%-- After header generation (considered as one row), let's generate layout rows --%>
		<c:forEach var="table" varStatus="tableStatus" items="${layout.tables}">
			<c:forEach var="row" items="${table.rows}" varStatus="rowStatus">
				<config:getOption var="rowId" name="cssId" component="${row}" defaultValue="${cssId}_row_${rowStatus.index}" />
				<config:getOption var="rowCssClass" name="cssClass" component="${row}" defaultValue="" />
				<div id="${rowId}" class="flexContainerRow ${not empty rowCssClass ? rowCssClass : ''}">
					<c:forEach varStatus="statusCell" var="cell" items="${row.cells}">
						<c:set var="flexGrowCSS" value="plmaFlexContainer-col-${plma:getFexGrow(table, statusCell.index)}"/>
						<config:getOption var="cellId" name="cssId" component="${cell}" defaultValue="${rowId}_cell_${statusCell.index}" />
						<config:getOption var="cellCssClass" name="cssClass" component="${cell}" defaultValue="" />

						<div id="${cellId}" class="${flexGrowCSS} ${cellCssClass} plmaFlexContainerCol flexPanelsContainer-item">
							<%-- Simply render subwidgets except header toolbar ones --%>
							<plma:forEachSubWidget excludes="headerButtonsContainer,plmaButtonsContainer,plmaButton" widgetContainer="${cell}"
												   feed="${parentFeed}" entry="${parentEntry}">
								<%-- Render actions subwidgets in header --%>
								<render:widget/>
							</plma:forEachSubWidget>
						</div>
					</c:forEach>
				</div>
			</c:forEach>
		</c:forEach>
	</div>
</widget:widget>

<render:renderScript position="READY">
	new PLMAFlexContainerWithHeader('${uCssId}', {});
</render:renderScript>