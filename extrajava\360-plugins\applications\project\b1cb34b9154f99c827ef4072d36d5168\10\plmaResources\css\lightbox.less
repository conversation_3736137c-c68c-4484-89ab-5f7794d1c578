@import "styles/utils.less";
@import "polyfills.less";

.plmalightbox-wrapper{
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 20000; /* ahead of widgets in full screen */
    .display-flex();
    .align-items(center);
    .justify-content(center);
    
    &.hidden{
        display: none;
    }
}

.plmalightbox-overlay{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    
    &.visible{
        background-color: rgba(0, 0, 0, 0.3);
    }
}

.plmalightbox-box{
    background-color: @cblock-bg;
    box-shadow: 0px 1px 10px 2px rgba(0,0,0,0.33);
    z-index: 1001;
    position: relative; /* For IE */
    max-height: 80%;
    max-width: 1024px;
    border-radius: 5px;
    overflow: auto;
    margin-bottom: 0;
    .transition(@transition-normal, margin-bottom);
    
    &.hidden{
        margin-bottom: @line-height;
        .transition(@transition-normal, margin-bottom);
    }

    .lightbox-hide-button {
        position: absolute;
        cursor: pointer;
        font-size: 23px;
        top: 8px;
        right: 0;
    }
}

.plmalightbox-header{
    width: 100%;
    font-size :@l-font;
	text-align: center;
	line-height: 100%;
}

.plmalightbox-close{
    float: right;
    cursor: pointer;
    margin: 0;
}
