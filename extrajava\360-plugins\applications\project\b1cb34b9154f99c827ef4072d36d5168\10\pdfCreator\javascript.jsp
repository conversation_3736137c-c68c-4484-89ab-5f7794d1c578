<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="security" uri="http://www.exalead.com/jspapi/security" %>

<render:import varFeeds="feeds" parameters="buttonSelector,fileName,fileOptions,widgetOptions,onInit,onDone" />
<render:import parameters="getPreviewContainer,previewHandler" ignore="true" />

<string:eval var="currentUser" string="${security.displayName}" isJsEscape="true"/>
<c:if test="${empty previewHandler}">
    <c:set var="previewHandler" value="CreatePDF.showPreview"/>
</c:if>
(function() {
    var $pdfButtonWidget = $('${buttonSelector}');
    var options = ${fileOptions};
    var widgetsInfoArray = [];
    <c:forEach items="${widgetOptions}" var="widgetInfo">
        widgetsInfoArray.push(${widgetInfo});
    </c:forEach>
    var fileProperties = {
        creator: 'EXALEAD PLMA Application',
        title: options.pageheader.title,
        author: '${currentUser}'
    };

    (${onInit}).call($pdfButtonWidget);

    $pdfButtonWidget.on('click', function(e) {
        $pdfButtonWidget.toggleClass('active');
        var pdfCreator = new CreatePDF(fileProperties, options, ${empty getPreviewContainer});
        <c:if test="${not empty getPreviewContainer}">
            pdfCreator.setPreviewContainer((${getPreviewContainer})())
        </c:if>
        pdfCreator
            .create(widgetsInfoArray)
            .then(function(pdfDocument){
                $pdfButtonWidget.toggleClass('active');
                (${previewHandler})(pdfCreator.getPreviewContainer(), pdfDocument, '${fileName}.pdf', '${cssId}-preview');
                (${onDone}).call(pdfDocument);
            });
    });
})();