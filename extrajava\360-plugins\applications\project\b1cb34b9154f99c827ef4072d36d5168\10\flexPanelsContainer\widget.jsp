<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varParentEntry="parentEntry" varParentFeed="parentFeed" />
<config:getOption var="wrap" name="wrap" defaultValue="false"/>
<request:getCookieValue var="panelManagementCookie" name="panelManagement" defaultValue=""/>
<config:getOptionsComposite var="additionalVariables" name="variables" mapIndex="true" />

<widget:widget disableStyles="true" varCssId="cssId" varUcssId="uCssId" extraCss="flexPanelsContainer" extraStyles="${plma:getCssStyles(additionalVariables)}">

	<c:set var="layout" value="${widget.layout}" />
	<c:set var="widthFormat" value="${layout.widthFormat}" />
	<c:if test="${widthFormat == null || widthFormat == ''}">
		<c:set var="widthFormat" value="%" />
	</c:if>
		
	<%-- 
	<style type="text/css" >
		<c:forEach var="table" varStatus="tableStatus" items="${layout.tables}">
			<c:forEach var="colConfig" items="${table.colsConfig}" varStatus="colStatus">
				.${uCssId} > .flexPanelsContainer-row > .flexPanelsContainer-col-${colStatus.index} {
					flex-grow: ${colConfig.width};
					flex-basis: 0;
				}
			</c:forEach>
		</c:forEach>
	</style>
	--%>

	<c:forEach var="table" varStatus="tableStatus" items="${layout.tables}">
		<c:forEach var="row" items="${table.rows}" varStatus="rowStatus">
			<config:getOption var="rowId" name="cssId" component="${row}" defaultValue="${cssId}_row_${rowStatus.index}" />
			<config:getOption var="rowCssClass" name="cssClass" component="${row}" defaultValue="" />
			
			<map:new var="colWidth"/>
			<c:forEach var="colConfig" items="${table.colsConfig}" varStatus="colStatus">
				<map:put key="${colStatus.index}" value="${colConfig.width}" map="${colWidth}"/>
			</c:forEach>
			
			<div id="${rowId}" 
				class="flexPanelsContainer-row flexPanelsContainer-row-${tableStatus.index}-${rowStatus.index} ${not empty rowCssClass ? rowCssClass : ''} ${wrap == 'true' ? 'wrap-panels' : ''}">
				<c:forEach varStatus="statusCell" var="cell" items="${row.cells}">
					<config:getOption var="cellId" name="cssId" component="${cell}" defaultValue="${rowId}_cell_${statusCell.index}" />
					<config:getOption var="cellCssClass" name="cssClass" component="${cell}" defaultValue="" />
                    <plma:getFlexPanelCSSClassesTag var="hidePanelClass" flexContainerCssClasses="${cellCssClass}" cookie="${panelManagementCookie}" />
					<div id="${cellId}" class="flexPanelsContainer-item flexPanelsContainer-col-${colWidth[statusCell.index]} flexPanelsContainer-item-${tableStatus.index}-${rowStatus.index}-${statusCell.index}<c:if test="${hidePanelClass != null && hidePanelClass != ''}"> ${hidePanelClass}</c:if>">
						<widget:forEachSubWidget widgetContainer="${cell}" feed="${parentFeed}" entry="${parentEntry}">
							<render:widget />
						</widget:forEachSubWidget>
					</div>
				</c:forEach>
			</div>
		</c:forEach>
	</c:forEach>
	
	<%-- --%>
	
	<render:renderLater>
		<style type="text/css" >
			<c:forEach var="table" varStatus="tableStatus" items="${layout.tables}">
				<c:forEach var="colConfig" items="${table.colsConfig}" varStatus="colStatus">
					.${uCssId} > .flexPanelsContainer-row > .flexPanelsContainer-col-${colStatus.index} {
						flex-grow: ${colConfig.width};
						flex-basis: 0;
					}
				</c:forEach>
			</c:forEach>
		</style>
	</render:renderLater>
	
</widget:widget>
