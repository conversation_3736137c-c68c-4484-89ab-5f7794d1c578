<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Lightbox" group="PLM Analytics/Layout/Hide and show" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>A lightbox to display inner widgets.</Description>
	
	<SupportI18N supported="true" />
	
	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="false" />
	</Platforms>

	<Includes>
		<Include type="css" path="css/style.less"/>

		<Include type="js" path="/resources/jquery-ui-1.11.4.custom/jquery-ui.min.js" includeCondition="enableDragging=true" />
		<Include type="js" path="../plmaResources/js/lightbox.js"/>
		<Include type="css" path="../plmaResources/css/lightbox.less"/>
	</Includes>

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportWidgetsId arity="MANY" />
	<SupportI18N supported="true" />
	
	<OptionsGroup name="General">
		<Option id="title" name="Title" isEvaluated="true">
			<Description>A title for the lightbox. If empty, the lightbox header is not displayed.</Description>
		</Option>
		<Option id="buttonSelector" name="Button selector">
			<Description>The CSS selector of the button that will trigger this lightbox.</Description>
		</Option>
		<Option id="event" name="Event">
			<Description>The javascript event that will trigger the lightbox.</Description>
		</Option>
		<Option id="showOverlay" name="Show overlay" arity="ONE">
			<Description>Displays the lightbox on top of an overlay.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="enableDragging" name="Enable dragging" arity="ONE">
			<Description>Whether to enable dragging on child widgets.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Javascript">
		<Option id="onInit" name="On init" isEvaluated="true">
			<Description>A JS function called at initialization.</Description>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
		<Option id="onShow" name="On show" isEvaluated="true">
			<Description>A JS function called when the lightbox shows up.</Description>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
		<Option id="onHide" name="On hide" isEvaluated="true">
			<Description>A JS function called when the lightbox is closed.</Description>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="event">click</DefaultValue>
		<DefaultValue name="showOverlay">true</DefaultValue>
	</DefaultValues>
	
</Widget>
