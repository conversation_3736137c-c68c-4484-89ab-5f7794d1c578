var ChartboardStorageManager = function () {
    this.clientUser = new StorageClient('user');
};

ChartboardStorageManager.PAGE_ID_PARAMETER = 'pageId';
ChartboardStorageManager.PAGE_DELETED_EVENT = 'chartboard:page_deleted';
ChartboardStorageManager.PAGES_STORAGE_KEY = 'chartboard_pages';

ChartboardStorageManager.prototype.savePage = function (page, url, config, isDuplicate) {
	const urlSearchParams = new URLSearchParams(window.location.search);
	urlSearchParams.delete('pageId');

	const params = urlSearchParams.toString();

	$.ajax({
		method: 'POST',
		url: url + 'savedPages/save',
		dataType: 'JSON',
		async: false,
		data: {
			id: isDuplicate ? '' : page.id,
			label: page.label,
			description: page.description,
			params: params,
			icon: 'fonticon ' + page.icon.trim().split(' ').pop(),
			template: page.templatePage,
			tiles: JSON.stringify(config.tiles)
		},
		success: function (data) {
			$.notify('Page successfully updated', "success");
			if (isDuplicate) {
				const url = new URL(window.location.href);
				url.searchParams.set('pageId', data.itemId.id);
				window.location.replace(url.href);
			} else {
				window.location.reload();
			}


		},
		error: function (data) {
			$.notify('Error updating page (' + data.responseJSON.error + ')', "error");
		}
	});
}

ChartboardStorageManager.prototype.deletePage = function (id, url) {
	$.ajax({
		method: 'DELETE',
		url: url + 'savedPages/delete/' + id,
		dataType: 'JSON',
		async: false,
		success: function (data) {
			$.notify('Page successfully deleted', "success");
			const baseUrl = window.location.href.split('?')[0];
			window.location.replace(baseUrl);
		},
		error: function (data) {
			$.notify('Error deleting page (' + data.responseJSON.error + ')', "error");
		}
	});
}

ChartboardStorageManager.prototype.saveNote = function (layout, storageKey, successCallback, errorCallback) {
	if (layout && storageKey) {
		this.clientUser.set(storageKey, JSON.stringify(layout.tiles),
			function () {
				/* success callback */
				successCallback.call();
			}, function () {
				/* error callback */
				if (errorCallback) {
					errorCallback.call();
				}
			});
	}
}
