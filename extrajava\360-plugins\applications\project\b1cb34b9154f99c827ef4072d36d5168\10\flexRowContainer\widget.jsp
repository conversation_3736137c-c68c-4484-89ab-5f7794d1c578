<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds" />

<config:getOption var="widgetTags" name="widgetTags" defaultValue="false"/>
<config:getOptionsComposite var="additionalVariables" name="variables" mapIndex="true" />

<c:choose>
	<c:when test="${widgetTags == 'true'}">
		<config:getOption var="title" name="title" defaultValue=""/>
		
		<widget:widget extraCss="flexRowContainer flexbox-container" varUcssId="uCssId" extraStyles="${plma:getCssStyles(additionalVariables)}">
			<widget:header>
				${title}
			</widget:header>
			<widget:content>
				<render:template template="flexlayout.jsp" >
					<render:parameter name="uCssId" value="${uCssId}" />
				</render:template>	
			</widget:content>
		</widget:widget>
	</c:when>
	<c:otherwise>
		<widget:getUcssId var="uCssId"/>
		<config:getOption var="cssClass" name="cssClass" defaultValue=""/>
        <config:getOption var="cssId" name="cssId" defaultValue=""/>
        <div
            <c:if test="${not empty cssId}">
            id="${cssId}"
            </c:if>
            class="flexbox-container ${uCssId} ${cssClass}"
			style="${plma:getCssStyles(additionalVariables)}">
            <render:template template="flexlayout.jsp" >
                <render:parameter name="uCssId" value="${uCssId}" />
            </render:template>
        </div>
	</c:otherwise>
</c:choose>
