
.mashup {

  .daterangepicker.dropdown-menu.alerting-daterangepicker {
    z-index: 20001;
  }

  .alert-message {
    z-index: 100;
    top: 0px;
    position: absolute;
    margin: 5px auto;
    padding: 15px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
    max-width: 100%;
    flex: 1 0 auto;
    word-break: break-word;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    &.fade.in {
      opacity: 1;
    }

    &.alert-primary {
      border-left: 3px solid #00B8DE;
      background-color: #F4F5F6;
    }

    &.alert-has-icon {
      padding-left: 38px;
    }

    &.alert-closable {
      padding-right: 32px;
    }

    &.fade {
      opacity: 0;
      transition: opacity 0.15s linear;
    }

  }

  .alert-primary {
    border-left: 3px solid #00B8DE;
    background-color: #F4F5F6;
  }


  .alert_list{
      overflow: auto;
    width: 100%;

    &.wrapped{
      width: 55%;
    }

    li {
      padding: 10px;
      background-color: #F1F1F1;
      border: 1px solid #D8D8F8;

      &.selected {
        border: 2px solid #42a2da;
        border-right: 0;
      }

      .hitHeader{
        display: flex;
        min-height: 23px;

        .hit-name {
          font-family: '3ds';
          font-weight: normal;
          color: #77797c;
          font-size: 16px;
          cursor: pointer;
          width: 100%;
          line-height: 23px;
        }
        .toggle-switch{
          display: inline-flex;
          margin: 0;
          padding: 0;
          min-height: 23px;
        }
        .fonticon{
          cursor: pointer;
          display: inline-flex;
          font-size: 21px;
          line-height: 23px;
          color: darkgray;
        }
      }
    }



    h3 {
      margin-bottom: 10px;
    }
  }
  .alert_details{
    width: 45%;
    overflow: auto;
    margin-top: 24px;

    &.active {
      border: 2px solid #42a2da;
    }

    &.hidden {
      display: none;
    }

    .alert_form {
      border: 2px solid #42a2da;



      input.alert_thr_series {
        width: 35%;
      }

      input.alert_thr_categ{
        width: 35%;
        margin-right: 10px;
      }
    }

    .buttons_div {
      background-color: inherit;

      .btn {
        margin-left: 5px;
      }
    }
  }




  .alerting {

    .widgetHeader{
      text-align: center;
      padding: 15px 0 15px 0;
      display: block !important;
    }
  }
  .alerting_buttons{
    //display: inline-block;
  }


  .alerting-check-btn, .alerting-get-btn {
    text-align: center;
    cursor: pointer;
    color: #3d3d3d;
    background-color: #F1F1F1;
    font-size: 14px;
    border: 1px solid #b4b6ba;
    border-radius: 4px;
    line-height: 1.42857;
    padding: 5px 10px;
    display: block;
    margin: 10px auto 0 auto;
  }

  .subscription_popup {
    //width : 100%;
    padding: 20px;

    tr {
      line-height: 30px;
    }

    .thresholds-list{

      li{
        list-style: disc;
        margin-left: 15px;
      }
    }

    .td_label {
      font-weight: bold;
      font-size: 14px;
      margin-right: 40px;
      display: block;
    }

    .td_value {
      font-size: 12px;
    }

    .buttons_div {
      text-align: center;
      margin-top: 20px;
    }
  }


  .alert_form{
    margin: 0;
    width: 100%;
    box-sizing: border-box;
    border-bottom: 1px solid #e2e4e3;
    padding-top: 10px;

    .wrong-field{
      border: 1px solid #EA4F37 !important;
    }

    select.form-control {
      width: auto;

      &:before {
        width: 38px;
        right: 0;
        top: 0;
        height: 100%;
        bottom: 0;
        border: 1px solid #B4B6Ba;
        border-radius: 0 4px 4px 0;
        background-color: #F1F1F1;
        content: "";
        position: absolute;
        z-index: 1;
        box-sizing: border-box;
      }
    }

    .colored_background{
      background-color: #F1F1F1;
      border: 1px solid #D8D8F8;
      padding: 10px;
      box-sizing: border-box;
    }
    .multi_lines{
      //height: 101px;

      .fields_row{
        display: block;

        span.badge-primary.badge {
          margin-right: 5px;
          padding: 8px;
          font-family: arial;
          font-size: 14px;

          .badge-content {
            margin-right: 5px;
            overflow-wrap: break-word;
          }
          .fonticon-cancel {
            cursor: pointer;
          }
        }

        .alert_thr_series, .alert_thr_categ  {
          width: 35%;
        }

        .alert_thr_series {
          margin-right: 10px;
        }

        .alert_thr_condition, .alert_thr_type {
          width: auto;
        }
        .alert_thr_value{
          width: 45px;
          margin-right: 10px;
        }
      }
    }

    select {
      margin-right: 10px;
    }

    .alert_property {
      display: flex;
      margin-bottom: 10px;
      padding: 0px 14px;

      .additional_section {
        display: flex;
        align-items: center;
        .span_text {
          margin-right: 10px;
        }
        &.hidden {
          display: none;
        }
      }


      &.recipients{
        height: auto;

        .fields_row{
          width: auto;
        }

      }


      &.single_line{
        height: 44px;
        //margin: 10px 0;

        .fields_row{
          display: flex;
          width: 80%;
        }
      }

      .label_row{
        display: flex;
        align-items: center;
        width: 20%;
        min-width: 75px;
        font-family: Arial;
        font-weight: bold;
        font-size: 14px;
      }
      .fields_wrapper{
        width: 80%;
      }
      .fields_row{
        //width: 80%;
        align-items: center;


        .section_1 {
          display: flex;
          align-items: center;
          width: 100%;
        }
        .section_2{
          display: flex;
          align-items: center;

          span.fonticon{
            font-size: 14px;
            color: #b4b6ba;
            cursor: pointer;
          }
        }
        .span_text{
          font-family: Arial;
          font-size: 12px;
        }
        .form-control {
          height: 38px;
          padding-left: 10px;
          box-sizing: border-box;
        }
        .line{
          display: flex;
          align-items: center;
          margin-bottom: 5px;

          &.mails-badges {
            display: block;
            width: 95%;
          }

          .alert_thr_value{
            width: 60px;
            margin-right: 10px;
          }

        }
      }
    }

    .from_span, .to_span{
      margin-right: 10px;
    }
    .to_span{
      margin-left: 10px;
    }

    .recipient_info{
      margin-left: 10px;
      .add-recipient {
        min-width: 0px;
        width: 38px;
        > .fonticon {
          position: relative;
          right: 3px;
        }
      }
    }

    .alert_thr_value{
      width: 30px;
    }

    .recipients h3, .dates h3{
      width: 33%;
    }

    .recipients {

      .alert_notif_recipients {
        width: 60%;
      }
    }


    //select {
    //  margin-bottom: 10px;
    //}

    h3{
      //margin-bottom: 15px;
      margin-right: 10px;
      display: inline-block;
    }

    .empty_span{
      display: none;
    }


    .empty_error{
      display: block;
      color: #CC092F;
      text-align: center;
      margin-top: 10px;
    }




    /*.btn {
      cursor: pointer;
      color: @ctext;
      background-color: #F1F1F1;
      background-image: none;
      font-size: 12px;
      border: 1px solid @ctext-weak;
      border-radius: 4px;
      padding: 5px 10px;
      margin-left: 5px;
      width: 80px;
      height: 38px;

      &.create_btn{
        margin-right: 10px;
      }
    }*/

    .alert_title{
      border: 1px solid #ccc;
      border-radius: 4px;
      height: 20px;
      vertical-align: middle;
      padding-left: 5px;
    }

    .bm_desc{
      width: 167px;
      resize: none;
      border: 1px solid #ccc;
      border-radius: 4px;
      vertical-align: middle;
      padding-left: 5px;
    }

    .bm_desc:focus{
      outline: none;
    }

    .red_border.alert_title{
      border: 2px solid #CC092F;
    }

    .active-btn:hover {
      background: @clink;
      color: white;
      border: 1px solid @clink;
    }

    .inactive-btn{
      cursor: initial;
      background: @ctext-weak;
    }
  }

  .buttons_div{
    display: flex;
    padding: 14px;
    justify-content: flex-end;
    background-color: #F1F1F1;

    .create_btn{
      margin-right: 10px;
    }

    .btn {
      height: 38px;
    }
  }

  .plmalightbox-box.alert_lb{
    display: block;
    overflow: auto;

    .plmalightbox-header{
      height: 44px;
      padding-left: 14px;
      box-sizing: border-box;
      display: flex;
      border-bottom: 1px solid #e2e4e3;

      h2{
        line-height: 44px;
        width: 100%;
      }
      .plmalightbox-close{
        float: none;
        width: 44px;
        height: 44px;
        line-height: 44px;
      }
    }

    .plmalightbox-contentwrapper{
      //padding: 10px 14px;
      display: block;
    }
  }

  .preference-alerts-container{
    .owned_alerts{
      h3{
        margin-bottom: 10px;
      }
    }

    .refresh-alerts{
      margin-left: 10px;
      cursor: pointer;
    }


    .subscribed_alerts{
      h3{
        margin-top: 30px;
        margin-bottom: 10px;
      }
    }
    .preference-block-container{
      display: flex;
      height: 100%;
      width: 100%;
      padding: 10px;
      box-sizing: border-box;
      margin-right: auto;
      margin-left: auto;
    }
  }
}