var EditMenu = function (uCssId, url, pageUrl, currentPage,baseAjaxReload) {
	this.uCssId = uCssId;
	this.widget = $('.' + uCssId);
	this.url = url;
	this.pageUrl = pageUrl;
	this.baseAjaxReload = baseAjaxReload;
	this.currentPage = currentPage;
	this.button = this.widget.find('.display-mode-edit');
	this.iconTooltipEl = this.widget.find('.icon-tooltip');
	this.iconEl = this.widget.find('.item-menu-container .user-items .menu-item .item-icon, .item-menu-container .shared-items .editable-item .item-icon');
	this.isCurrentPageModified = false;

	this.init();
};

EditMenu.prototype.init = function () {
	// Init active edit mode on click on edit button 
	this.button.on('click', $.proxy(function () {
		this.toggleEditMode();
	}, this));
	
};

EditMenu.prototype.toggleEditMode = function () {
	if (this.button.hasClass('active')) {
		if(this.isCurrentPageModified) {
			window.location.reload();
		} else {
			this.reload();
		}
	} else {
		this.openEditMode();
	}
};

EditMenu.prototype.openEditMode = function () {
	var self = this;
	this.initIconTooltip();
	// Set button to active 
	this.button.addClass('active');

	// Set Edit mode style 
	this.widget.find('.item-menu-container').addClass('edit-active');

	// Add delete section block
	var dropDiv = $('<div class="drop-section" title="Drop saved pages here to delete"><span class="drop-icon fonticon fonticon-trash"></span></div>');
	this.widget.find('.item-menu-container').prepend(dropDiv);
	dropDiv.droppable({
		accept: '.user-section-container .menu-item',
		activeClass: 'drop-active',
		tolerance: 'pointer',
		drop: $.proxy(function (event, ui) {
			var container = $(ui.draggable);
			let id = $(container).data('descid').split('saved-page-')[1];
			$.ajax({
				type: 'DELETE',
				url: this.url + '/delete/' + id,
				dataType: 'JSON',
				async: 'false',
				context: this,
				success: function (data) {
					$.notify('Page successfully deleted', "success");
					if (data.id == this.currentPage){
						window.location.href = this.pageUrl + '/' + data.template + '?' + data.params;
					}
					container.remove();
				},
				error: function (err) {
					$.notify('Error deleting saved page (' + err.responseJSON.error + ')', "error");
				}
			});
		}, this)
	});

	// Add drag-drop icon for each elem
	var dragDropIcon = $('<i class="drag-drop-icon fonticon fonticon-drag-grip"></i>');
	this.widget.find('.item-menu-container .user-items .menu-item').append(dragDropIcon.clone().attr('title','Drag and drop over bin to delete saved page.'));


	// Init drag and drop
	this.widget.find('.item-menu-container .user-items .user-section-container').sortable({
		items: '.menu-item',
		cancel: '.user-items .user-section-container .item-label',
		revert: true,
		tolerance: 'pointer',
		connectWith: '.item-menu-container .user-items .user-section-container',
		update: function(event, ui) {
			var menuItems = $(this).find('.menu-item');
			var idsArr = [];
			
			menuItems.each(function() {
				var id = $(this).data('descid').split('saved-page-')[1];
				idsArr.push(id);
			});
			
			$.ajax({
				method: 'POST',
				url: self.url + '/sort',
				dataType: 'JSON',
				data: { ids: idsArr},
				async: 'false',
				error: function (err) {
					$.notify('Error updating page order (' + err.responseJSON.error + ')', "error");
				}
			});
		}
	});
	
	this.widget.find('.item-menu-container .user-items .user-section-container .menu-item .item-label').bind('dragover drop', function(event){
		event.preventDefault();
		return false;
	});
	this.widget.find('.item-menu-container .user-items .user-section-container .section-separation .section-label').bind('dragover drop', function(event){
		event.preventDefault();
		return false;
	});	
	
	// Set content editable 
	this.widget.find('.item-menu-container .user-items .menu-item .item-label, .item-menu-container .shared-items .editable-item .item-label').attr('contenteditable', 'true');
	this.widget.find('.item-menu-container .user-items .menu-item .item-label, .item-menu-container .shared-items .editable-item .item-label').on('focus', $.proxy(function (e) {
		var $e = $(e.currentTarget);
		if (!$e.data('originValue')) {
			$e.data('originValue', $e.text());
		}
	}, this)).on('keypress', $.proxy(function (e) {
		
		var $e = $(e.currentTarget);
		
		// On pressing Enter key
		if(e.which === 13) {
			e.preventDefault();
			if ($e.html() !== $e.data('originValue')) {
				let label = $e.text();
				let id = $e.closest('.menu-item').data('descid').split('saved-page-')[1];
				
				$.ajax({
					method: 'PATCH',
					url: self.url + '/update/' + id + '?' + $.param({
						label: label
					}),
					dataType: 'JSON',
					context: this,
					async: 'false',
					success: function (data) {
						$.notify('Updated page label', "success");
						if (data.id == this.currentPage){
							this.isCurrentPageModified = true;
						}
					},
					error: function (err) {
						$.notify('Error updating icon (' + err.responseJSON.error + ')', "error");
					}
				});
			}
			$e.blur();
		}
		
	}, this));
	
	// Save original icon
	this.widget.find('.item-menu-container .user-items .menu-item .item-icon, .item-menu-container .shared-items .editable-item .item-icon').on('click', $.proxy(function (e) {
		var $e = $(e.currentTarget);
		if (!$e.data('originValue')) {
			$e.data('originValue', $e.attr('class').trim().split(' ').pop());
		}
	}, this));
	
	// Remove link on click on page 
	this.widget.find('.item-menu-container .items .menu-item a').on('click', $.proxy(function (e) {
		e.preventDefault();
	}, this));

	// Remove dropdown click action 
	this.widget.find('.section-separation').on('click', $.proxy(function (e) {
		e.preventDefault();
	}, this));
	
	this.widget.find('.item-menu-container .items .menu-item item-icon').on('click', $.proxy(function (e) {
		e.preventDefault();
	}, this));
};

EditMenu.prototype.reload = function () {
	// Reload widget without saving
	var client = new PlmaAjaxClient(this.widget, {baseUrl: this.baseAjaxReload});
	client.addWidget(this.uCssId, true, false);
	client.update();
};

EditMenu.prototype.initIconTooltip = function () {
	var self = this;
	$(self.iconTooltipEl).removeAttr('style');
	
	self.initIconElements();

	$(self.iconEl).on('click', $.proxy(function (e) {
        // Disable parent div event
        e.stopPropagation();
		e.preventDefault();
		$(e.target).closest('.item-menu-container .user-items .menu-item, .item-menu-container .shared-items .editable-item').after(self.iconTooltipEl);
		$(self.iconTooltipEl).toggleClass('hidden');
		self.initIconSearch();

    }, this));

}

EditMenu.prototype.initIconElements = function () {
	var self = this;
	var iconContainer = this.widget.find('.fonticon-container');
	$(iconContainer).on('click', function(e) {
		e.stopPropagation();
		
		var tooltipIconsEl = $(e.target);
		if(tooltipIconsEl.attr('class').split(' ')[0] === 'fonticon-elem') {
						
			var pageIconEl = tooltipIconsEl.closest(".icon-tooltip").prev().find(".item-icon");
			
			var oldIcon = pageIconEl.attr('class').trim().split(' ').pop();
			var newIcon = tooltipIconsEl.attr('class').trim().split(' ').pop();
			
			// If new icon is clicked
			if (tooltipIconsEl.attr('class').trim().split(' ').pop() !== pageIconEl.data('originValue')) {
				let id = pageIconEl.closest('.menu-item').data('descid').split('saved-page-')[1];
				
				$.ajax({
					method: 'PATCH',
					url: self.url + '/update/' + id + '?' + $.param({
						icon: 'fonticon ' + newIcon
					}),
					dataType: 'JSON',
					context: this,
					async: 'false',
					success: function (data) {
						$.notify('Updated page icon', "success");
						if (data.id == this.currentPage){
							this.isCurrentPageModified = true;
						}
					},
					error: function (err) {
						$.notify('Error saving page (' + err.responseJSON.error + ')', "error");
					}
				});
			}
			
			pageIconEl.removeClass(oldIcon);
			pageIconEl.addClass(newIcon);
			pageIconEl.data('originValue', newIcon);
			self.resetIconTooltip();
		}
	});
}

EditMenu.prototype.initIconSearch = function () {
	var iconTooltipInput = this.widget.find('.icon-tooltip-input');
	$(iconTooltipInput).on('keyup', function () {
		var searchValue = this.value;
		var iconList = $('.fonticon-container > span');
		for (var i = 0; i < iconList.length; i++) {
			var elemClassList = iconList[i].getClassList();
			if (elemClassList[2].indexOf(searchValue.replace('fonticon ', '')) !== -1) {
				elemClassList.remove('hidden');
			} else {
				elemClassList.add('hidden');
			}
		}
	});
};

EditMenu.prototype.resetIconTooltip = function () {
	$(this.iconTooltipEl).addClass('hidden');

	this.widget.find('.icon-tooltip-input').val('');
	this.widget.find('.fonticon-container > span').each(function() {
		$(this).removeClass('hidden');
	});
};
