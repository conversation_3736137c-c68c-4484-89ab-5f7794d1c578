<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Pinned pages" group="PLM Analytics/Navigation" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>
		&lt;p&gt;Depending on the selected mode, this widget either allows the user to pin pages or displays them. There are two modes:&lt;/p&gt;
		&lt;ul&gt;
			&lt;li&gt;Pin: The widget displays a button that allows the user to pin/unpin the current page.&lt;/li&gt;
			&lt;li&gt;List: The widget displays all the pinned pages.&lt;/li&gt;
		&lt;/ul&gt;
	</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/pinnedPages/images/preview.PNG" alt="Pinned Pages" />
        ]]>
	</Preview>
	<Includes>
		<Include type="css" path="css/style.less"></Include>
	
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="../plmaResources/js/i18nClient.js" />
		<Include type="js" path="../chartboard/js/model.js" />
		<Include type="js" path="../pinnedPages/js/pinnedPages.js" />
	</Includes>
	<SupportFeedsId arity="ZERO" />
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>pinnedPages.pin</JsKey>
			<JsKey>pinnedPages.unpin</JsKey>
		</JsKeys>
	</SupportI18N>
	<OptionsGroup name="General">
		<Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
		</Option>
		<Option id="mode" name="Mode">
			<Description>
			Specifies the widget mode:
			&lt;ul&gt;
				&lt;li&gt;Pin: The widget displays a button that allows the user to pin/unpin the current page.&lt;/li&gt;
				&lt;li&gt;List: The widget displays all the pinned pages.&lt;/li&gt;
			&lt;/ul&gt;
			</Description>
			<Values>
				<Value>pin</Value>
				<Value>list</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['list'], showOptions:['elemPerPage']})</Display>
			</Functions>
		</Option>
		<Option id="elemPerPage" name="Number of elements displayed per page">
			<Description>Number of pinned pages displayed per pagination page in the widget (default is 6). Enter 0 if you do not want any pagination.</Description>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Advanced">
		<Description>These options are only used in "Pin" mode.</Description>
		<Option id="queryStringFields" name="Query string fields" arity="ZERO_OR_MANY">
			<Description>Fields of the query string that must be kept while saving the page URL. By default, 'pageId' is kept.</Description>
		</Option>
		<Option id="displayLabel" name="Display label">
			<Description>Displays the button label next to the button icon. If set to false, the label is displayed as a tooltip when hovering over the button icon. Defaults to false.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<DefaultValues displayName="Pin">
		<DefaultValue name="mode">pin</DefaultValue>
		<DefaultValue name="queryStringFields">pageId</DefaultValue>
		<DefaultValue name="displayLabel">false</DefaultValue>
		<DefaultValue name="cssDisableStyle">true</DefaultValue>
	</DefaultValues>
	<DefaultValues displayName="List">
		<DefaultValue name="mode">list</DefaultValue>
		<DefaultValue name="cssDisableStyle">true</DefaultValue>
	</DefaultValues>
</Widget>
