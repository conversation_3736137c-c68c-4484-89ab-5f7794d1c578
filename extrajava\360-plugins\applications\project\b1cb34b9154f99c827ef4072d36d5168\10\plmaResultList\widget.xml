<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA simple result list" group="PLM Analytics/Results Rendering" premium="true">

	<Description>This widget displays the search results of a PLM Analytics app in a list view.
		This widget only displays a set of results in its simplest form.</Description>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="../3dxpSearchAndRefine/js/message-handler.js"/>
		<Include type="js" path="../plmaResources/js/experienceAPIs/Session3DContent.js"/>
		<Include type="js" path="../plmaResources/js/select.js" />
		<Include type="js" path="../plmaResources/js/SelectionWidget.js" />
		<Include type="js" path="js/plmaResultList.js" />
		<Include type="js" path="js/gridTableSetup.js" />
		<Include type="js" path="js/compareUiManager.js"/>
		<Include type="js" path="/resources/javascript/exa/io/io.js" />
		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js"/>
		<Include type="js" path="../plmaResources/js/PlmaInfiniteScroll.js"/>
		<Include type="js" path="../plmaResources/js/detailHitHelper.js"/>
		<Include type="js" path="../plmaResources/js/historyState.js"/>
		<Include type="js" path="../plmaResources/js/lightbox.js"/>
		<Include type="css" path="css/resultlist-pref.less" />
		<Include type="js" path="js/plmaResultListPref.js"/>
	</Includes>

	<Preview>
		<![CDATA[
			<img src="img/preview.png" />
		]]>
	</Preview>
	
	<Dependencies>
		<Widget name="thumbnail" includeCondition="showThumbnail=true" />
		<Widget name="plmaResources" />
		<Widget name="plmaPagination" />
	</Dependencies>
	
	<SupportWidgetsId arity="ZERO_OR_MANY" label="foreach hit" />
	<SupportFeedsId arity="MANY" consumeFeed="true"/>


	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>hitcustom.sort_asc</JsKey>
			<JsKey>hitcustom.sort_dsc</JsKey>
			<JsKey>plma.resultlist.view.default</JsKey>
			<JsKey>plma.resultlist.view.gridtable</JsKey>
			<JsKey>plma.resultlist.view.gridtable.rows</JsKey>
			<JsKey>plma.resultlist.view.gridtable.columns</JsKey>
			<JsKey>plma.resultlist.view.gridtable.pin</JsKey>
			<JsKey>plma.resultlist.view.gridtable.pincol</JsKey>
			<JsKey>plma.resultlist.view.gridtable.unpincol</JsKey>
			<JsKey>plma.resultlist.view.gridtable.unpin</JsKey>
			<JsKey>plma.resultlist.compare.hidesimilarcols.hide</JsKey>
			<JsKey>plma.resultlist.compare.hidesimilarcols.show</JsKey>

			<JsKey>plma.resultlist.tab.label</JsKey>
			<JsKey>plma.resultlist.tab.title</JsKey>
			<JsKey>plma.resultlist.tab.description</JsKey>
			<JsKey>plma.resultlist.button.selectall</JsKey>
			<JsKey>plma.resultlist.button.deselectall</JsKey>
			<JsKey>plma.resultlist.column.state.false</JsKey>
			<JsKey>plma.resultlist.column.state.ifnotempty</JsKey>
			<JsKey>plma.resultlist.column.state.true</JsKey>
			<JsKey>plma.resultlist.save</JsKey>
			<JsKey>plma.resultlist.reset</JsKey>
			<JsKey>plma.resultList.exportNotification</JsKey>
			<JsKey>plma.resultList.exportNotification.allResults</JsKey>
		</JsKeys>
	</SupportI18N>
	
	<Interactions>
		<Interaction type="in" id="exa.io.HitDecorationInterface">
			<Description>Decorate your hit.</Description>
			<Option id="hitDecorationCssSelector" name="Css path" arity="ONE">
				<Description>
				<![CDATA[
					CSS path where the decoration will be appended in the hit display.<br />
					Generally, the default value does not need to be changed for the standard hit display widget.
				]]>
				</Description>
				<Functions>
					<ContextMenu>COMMON_add('Default template', [{display:'Header left',value:'li.${entry:cleanId(entry)} .rounded-top'}, ])</ContextMenu>
					<ContextMenu>Eval()</ContextMenu>
				</Functions>
			</Option>
			<Option id="hitDecorationPosition" name="Decoration position" arity="ONE">
				<Description>
				<![CDATA[
					The decoration will be appended or prepended.
				]]>
				</Description>
				<Values>
					<Value></Value>
					<Value>append</Value>
					<Value>prepend</Value>
				</Values>
			</Option>
		</Interaction>
	</Interactions>

	<OptionsGroup name="Hit config">
		<Option id="hitTitle" name="Hit title" isEvaluated="true">
			<Description>Specifies the title displayed for each hit. Leave empty to use the &lt;i&gt;Title&lt;/i&gt; defined in the source feed. </Description>
		</Option>
		<Option id="hitUrl" name="Hit title url" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Specifies the destination URL when clicking the hit title.</Description>
		</Option>
		<Option id="hitContent" name="Hit content" isEvaluated="true">
			<Description>Specifies the content displayed for each hit. Leave empty to use the &lt;i&gt;Content&lt;/i&gt; defined in the source feed.</Description>
		</Option>
		<Option id="hitContentDisplay" name="Hit content displayment" isEvaluated="true">
			<Description>Displays the hit content by default. Content will be hidden otherwise.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="maxContentLength" name="Max length">
			<Description>Maximum length (number of chars) of the displayed content. Any characters above this limit are truncated. 0 means no limit.
                WARNING : this option should not be used with highlighting search feature as it can break highlighting style.
            </Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="typeFacet" name="Type facet">
			<Description>You can specify a facet to display hits differently based on it.</Description>
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
			</Functions>
		</Option>
		<Option id="typeFacetIcon" name="Type facet default icon">
			<Description>You can specify a default icon associated with the hit's type (default is fonticon-legend).</Description>
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
			</Functions>
		</Option>
		<Option id="pinRow" name="Freeze row" arity="ONE">
			<Description>
				Freeze row below the header. Defaults to false.
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="iconTitleContainerWidth" name="Container Size" arity="ONE">
			<Description>Modify icon and title container width</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['iconContainerWidth', 'titleContainerWidth']})</Display>
			</Functions>
		</Option>
		<Option id="iconContainerWidth" name="Icons Container Size" arity="ZERO_OR_ONE"
				isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
				isXmlEscaped="false">
			<Description>Specifies icons container size (in pixels). You must enter an integer. Default 100 px.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="titleContainerWidth" name="Title Container Size" arity="ZERO_OR_ONE"
				isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
				isXmlEscaped="false">
			<Description>Specifies title container size (in pixels). You must enter an integer. Default 341px(including icon container width).</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>

		<!-- Thumbnail Config Section -->
		<Option id="section-hitThumbnail" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Hit thumbnail Section &lt;/b&gt;&lt;/span&gt;">
			<Functions>
				<Display>SetType('literal')</Display>				
			</Functions>
		</Option>		
		<Option id="showThumbnail" name="Display thumbnails" arity="ONE">
			<Description>Displays hit thumbnails.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('false', [], ['useThumbnailPreview', 'urlDefaultThumbnail', 'tbHeight', 'tbWidth'], false, false)</Display>
				<Display>ToggleDisplay({ valueToMatch: ['true', 'false'], hideOptions: ['mainContainerId'] })</Display>
			</Functions>
		</Option>
		<Option id="useThumbnailPreview" name="Use CloudView thumbnails" arity="ONE">
			<Description>Uses the fetcher to retrieve CloudView's computed thumbnails.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="urlDefaultThumbnail" name="Default URL thumbnail" isEvaluated="true">
			<Description>This thumbnail is displayed if there are no thumbnails available in the feed's hit. Leave this field blank to have default, MIME type dependent thumbnails.</Description>
		</Option>
		<Option id="tbHeight" name="Thumbnail Height" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Specifies the thumbnails height (pixels). You must enter an integer.</Description>	  
		</Option>
		<Option id="tbWidth" name="Thumbnail Width" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Specifies the thumbnails width (pixels). You must enter an integer.</Description>	   
		</Option>
		<Option id="mainContainerId" name="On-scroll loading container id" isEvaluated="true">
			<Description>If set, on scroll-loading will be applied to the specific container instead of the entire window. You must set the css id of a scrollable element.</Description>
		</Option>
	</OptionsGroup>
	
	<OptionsGroup name="Classic Config">
		<Description><![CDATA[
			<span style="color:black; font-size:14px;"><b>***Depricated***</b></span> Merged 'Hit Metas','Hit Facets', and Hit Sort configurations in one tabs.
		]]></Description>
		<!-- Meta Config Section -->
		<Option id="section-hitMetas" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Hit Metas Section &lt;/b&gt;&lt;/span&gt;">
			<Functions>
				<Display>SetType('literal')</Display>
			</Functions>
		</Option>
		<Option id="showHitMetas" name="Display metas" arity="ONE">
			<Description>Displays metas in the hit content.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('true', [], ['customDisplay', 'metaUrlTarget', 'showEmptyMetas', 'filterMetas', 'metas', 'customMetasDisplay', 'sortModeMetas'], true, false)</Display>
			</Functions>
		</Option>
		<Option id="metaUrlTarget" name="Meta URL Target" isUrlEncoded="true">
			<Description>Specifies where to open metas with URLs.</Description>
			<Values>
				<Value>Current Page</Value>
				<Value>New Page</Value>
			</Values>
		</Option>
		<Option id="showEmptyMetas" name="Display empty metas" arity="ONE">
			<Description>Displays the names of metas with no (or empty) values.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="filterMetas" name="Meta filtering method" arity="ONE">
			<Description>Specifies the filtering method to apply to the metas. For the 'Exclude' and 'Include' methods, the 'Meta list' field allows you to specify the metas to include or exclude.</Description>
			<Values>
				<Value>No filtering</Value>
				<Value>Exclude</Value>
				<Value>Include</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('No filtering', ['metas'], [], true, false)</Display>
			</Functions>
		</Option>
		<Option id="metas" name="Meta list" isEvaluated="true">
			<Description>The specified metas, separated by commas, will be either the only ones displayed ('Include' method) or the only ones hidden ('Exclude' method).</Description>
			<Functions>
				<ContextMenu>Metas()</ContextMenu>
				<ContextMenu>COMMON_appendOnChange(', ')</ContextMenu>
			</Functions>
		</Option>
		<Option id="sortModeMetas" name="Meta sort method">
			<Description>Specifies how metas will be sorted.</Description>
			<Values>
				<Value>default</Value>
				<Value>description-asc</Value>
				<Value>description-desc</Value>
				<Value>shuffle</Value>
			</Values>
		</Option>
		<Option id="showOnlyMetasName" name="Display only metas name" arity="ONE">
			<Description>Displays only the names of metas and not the ids.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="showMetasName" name="Display metas name" arity="ONE">
			<Description>Displays the names of metas before their values (in tile mode).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		
		<!-- Facet Config Section -->
		<Option id="section-hitFacets" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Hit Facets Section &lt;/b&gt;&lt;/span&gt;">
			<Functions>
				<Display>SetType('literal')</Display>
			</Functions>
		</Option>
		<Option id="showHitFacets" name="Display facets" arity="ONE">
			<Description>Displays facets in the hit content.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('true', [], ['hitFacetPageName', 'showEmptyFacets', 'hitFilterFacetsType', 'hitFacetsList', 'hitFacetSortStrategy', 'sortModeFacets'], true, false)</Display>
			</Functions>
		</Option>
		<Option id="hitFacetPageName" name="Destination page on click" isUrlEncoded="true" isEvaluated="true">
			<Description>Indicates the URL that must be accessed for a refinement option. If blank, the user stays on the current page.</Description>
			<Functions>
				<ContextMenu>Pages()</ContextMenu>
			</Functions>
		</Option>
		<Option id="showEmptyFacets" name="Display empty facet">
			<Description>Displays empty facets, that is to say, facets that do not have any categories.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="hitFilterFacetsType" name="Facet list mode" arity="ONE">
			<!-- Named facetListMode in other widgets - name kept for compatibility reasons -->
			<Description>
				<![CDATA[
					Specifies the facet list filtering mode.<br />
					For the 'Exclude' and 'Include' modes, the 'Facet list' field allows you to specify the metas to include or exclude.
				]]>
			</Description>
			<Values>
				<Value>No filtering</Value>
				<Value>Include</Value>
				<Value>Exclude</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('No filtering', ['hitFacetsList'], [], true, false)</Display>
			</Functions>
		</Option>
		<Option id="hitFacetsList" name="Facet list" isEvaluated="true">
			<Description>The specified facets, separated by commas, will be either the only ones displayed ('Include' mode) or the only ones hidden ('Exclude' mode). Leaving the facet list empty iterates over all facets.</Description>
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
				<ContextMenu>COMMON_appendOnChange(', ')</ContextMenu>
			</Functions>
		</Option>
		<Option id="sortModeFacets" name="Sort facets by">
			<Description>Sorts the facets using the selected method. Selecting anything but 'default' impacts performance.</Description>
			<Values>
				<Value>default</Value>
				<Value>description-asc</Value>
				<Value>description-desc</Value>
				<Value>shuffle</Value>
			</Values>
		</Option>
		<Option id="hitFacetSortStrategy" name="Sort categories by" arity="ONE">
			<Description>
				<![CDATA[
					Sorts the categories using the selected method. Selecting anything but 'default' impacts performance.<br />
					<ul>
						<li>'default' requires no processing.</li>
						<li>'shuffle', 'desc*' impact performance on the server side.</li>
					</ul>
				]]>
			</Description>
			<Values>
				<Value>default</Value>
				<Value>description-asc</Value>
				<Value>description-desc</Value>
				<Value>range-asc</Value>
				<Value>range-desc</Value>
				<Value>shuffle</Value>
			</Values>
		</Option>
		<Option id="showFacetsName" name="Display facets name" arity="ONE">
			<Description>Displays the names of facets before their values (in tile mode).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		
		<!-- Sort Config Section -->
		<Option id="section-hitSort" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Hit Sort Configs Section &lt;/b&gt;&lt;/span&gt;">
			<Functions>
				<Display>SetType('literal')</Display>
			</Functions>
		</Option>
		<OptionComposite id="sortListConfig" name="Sorts Config" arity="ZERO_OR_MANY" glue="##">
			<Option id="metaName" name="Field name">
				<Description>Name of the sort or RAM-based field to sort on. You should give the name of the field to sort on, its label, its icon and the name of the column it should be attached to (for table view).</Description>
				<Functions>
					<ContextMenu>Sorts()</ContextMenu>
					<ContextMenu>Fields()</ContextMenu>
					<ContextMenu>emptyOnChange()</ContextMenu>
					<Display>SetWidth(45)</Display>
				</Functions>
			</Option>
			<Option id="displayName" name="Display name" isEvaluated="true">
				<Description>Specifies the name to display for this sort.</Description>
			</Option>
			<Option id="icon" name="Icon">
				<Description>Specifies the CSS class of the icon to display. You can supply 3diff icon separated by comma e.g. [type-icon,asc-icon,desc-icon]</Description>
			</Option>
			<Option id="sortColumnName" name="Column Name">
				<Description>Meta or facet name for this sort's corresponding column. Use 'title' for the first column.</Description>
			</Option>
		</OptionComposite>
	</OptionsGroup>
	
	<OptionsGroup name="New Config">
		<Description>New Configurations enable to tidyup and have more control on widget display. The configuration also can be available as user preferences to modify from UI.</Description>
		<Option id="enableNewConfig" name="Use New config" arity="ONE">
			<Description>Use New configurations Mode. If true, anything configured in the Classic Config tab will be ignored.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({ valueToMatch:'false', showOptions:[], hideOptions:['useExternalisedConfig', 'columnsConfig', 'externalConfig']})</Display>
			</Functions>
		</Option>
		<Option id="useExternalisedConfig" name="Use external config" arity="ONE">
			<Description>Use fields configurations from config xml file.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({ valueToMatch:'true', showOptions:['externalConfig'], hideOptions:['columnsConfig']})</Display>
			</Functions>
		</Option>
		<OptionComposite id="columnsConfig" name="Fields &lt;i style=&quot;position: absolute; left: 400px;font-size:12px&quot;&gt;Used only when &quot;Use New Config&quot; is True&lt;/i&gt;" arity="ZERO_OR_MANY" glue="##" isEvaluated="false">
			<Description>Specify the Fields to be desplayed and their configuration.</Description>
			<Option id="id" name="Id" arity="ZERO_OR_ONE">
				<Description>Id of the columns. used for storing the prefrences(order, show/hide).</Description>				
			</Option>
			<Option id="order" name="Order" arity="ZERO_OR_ONE">
				<Description>Default Order of the field in the table.</Description>
				<Functions>
					<Display>SetType('number')</Display>
					<Check>isInteger</Check>
				</Functions>
			</Option>
			<Option id="meta" name="Meta" arity="ZERO_OR_ONE" isEvaluated="true">
				<Description>Name of the Field. The value of the field is used for display</Description>
				<Functions>
					<ContextMenu>Metas()</ContextMenu>
					<ContextMenu>Facets()</ContextMenu>
					<ContextMenu>emptyOnChange()</ContextMenu>	
				</Functions>
			</Option>			
			<Option id="type" name="Type">
				<Description>Type of the value in the field. Used for Sorting display and compare mode.</Description>
				<Values>
					<Value></Value>
					<Value>String</Value>
					<Value>Number</Value>
					<Value>Date</Value>
				</Values>
			</Option>
			<Option id="sortField" name="Sort" arity="ZERO_OR_ONE">
				<Description>The index field on which to sort when this field header is clicked. Must be RAM-based. Leave empty to disable sort. &lt;b&gt;Warning&lt;/b&gt;: having too many RAM-based index fields will have an impact on memory footprint and performances.</Description>
				<Placeholder>disabled</Placeholder>
				<Functions>
					<ContextMenu>Sorts()</ContextMenu>
					<ContextMenu>Fields()</ContextMenu>
					<ContextMenu>emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="sortLabel" name="Sort Label" isEvaluated="true">
				<Description>Specifies the name to display for this sort.</Description>				
			</Option>
			<Option id="sortIcon" name="Sort Icon">
				<Description>Specifies the CSS class of the icon to display.</Description>				
			</Option>
			<Option id="isShown" name="Show?" arity="ZERO_OR_ONE">
				<Description>&lt;b&gt;Show:&lt;/b&gt; Whether this field should be displayed by default. Otherwise it will be hidden, and the user can choose to display it from the settings.
If you just updated the plugin and this option was not here before, it will default to true even though the box is not checked; if you edit this widget, it will then behave normally..</Description>
				<Values>
					<Value>true</Value>
					<Value>false</Value>
					<Value>ifNotEmpty</Value>
					<Value>titleConfig</Value>
				</Values>
			</Option>
			<Option id="isFacet" name="Facet?" arity="ZERO_OR_ONE">
				<Description>&lt;b&gt;Is Facet:&lt;/b&gt;Whether this filed is Facet(if true entry.facet and app.xml used to display the value).</Description>
				<Values>
					<Value>false</Value>
					<Value>true</Value>
				</Values>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
		<OptionComposite id="externalConfig" name="External Config" isEvaluated="false" arity="ONE">
			<Option id="resultListId" name="ResultList Id" isEvaluated="true" arity="ONE">
				<Description>ResultList Configurations ID to use. You can provide MEL expression as well if you need conditional display.</Description>
			</Option>
			<Option id="configName" name="Configuration Name" isEvaluated="true" arity="ZERO_OR_ONE">
				<Description>Configuration name (use application config if empty).</Description>
			</Option>
		</OptionComposite>
		<!-- Meta Config Section -->
		<Option id="section-hitMetas" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Hit Metas Section &lt;/b&gt;&lt;/span&gt;">
			<Functions>
				<Display>SetType('literal')</Display>
			</Functions>
		</Option>
		<Option id="showMetaName" name="Show meta's name" arity="ONE">
			<Description>Displays the names of metas before their values (in tile mode).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<!-- Facet Config Section -->
		<Option id="section-hitFacets" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Hit Facets Section &lt;/b&gt;&lt;/span&gt;">
			<Functions>
				<Display>SetType('literal')</Display>
			</Functions>
		</Option>
		<Option id="showFacetName" name="Show facet's name" arity="ONE">
			<Description>Displays the names of facets before their values (in tile mode).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="showFacetIcon" name="show facet's icon" arity="ONE">
			<Description>Displays the icon of facets before their values.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="hideCategoryValue" name="Hide Category Value" arity="ONE">
			<Description>Hides the value of the category of the facet(in tile mode).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="hideCategoryIcon" name="Hide Category icon" arity="ONE">
			<Description>Hides the category icon which is shown before the category values(in tile mode).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>
		
	<OptionsGroup name="Hit templates">
		<Option id="customJspPathHit" name="Hit JSP template" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>
				<![CDATA[
					Specifies the JSP template to use.<br />
					Using variables for finding a valid template is useful if you want to customize hit display based for example, on the TYPE.<br />
					Hit templates are in /WEB-INF/jsp/widgets/displayHits/templates/.
				]]>
			</Description>
			<Functions>
				<Check>isJspPath</Check>
				<ContextMenu>JAVA_listFiles('templates/', '*/default.jsp', 'JSP base templates')</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="defaultJspPathHit" name="Default template (mandatory)" arity="ONE">
			<Description>
				<![CDATA[
					Defines the default template to use when the Hit JSP template does not exist or is not defined.<br />
					For example, when no custom template for the current TYPE has been found.<br />
					The hit templates are in the 'templates' base path.
				]]>
			</Description>
			<Functions>
				<Check>isJspPath</Check>
				<ContextMenu>JAVA_listFiles('templates/', '*/default.jsp', 'JSP base templates')</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="customMetaJSP" name="Custom Meta/Column Display" arity="ZERO_OR_ONE">
			<Description> <![CDATA[
				If you want to add custom content to the resultlist, provide customJSP. <br/>
				e.g Sample implementation is provided at
				/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaResources/template/resultListCustomMetaSample.jsp
			]]>
			</Description>
			<Functions>
				<Check>isJspPath</Check>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="General">
		<Option id="title" name="Widget Title" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
		</Option>
		<Option id="disableResultListPrefTab" name="preferences tab" arity="ONE">
			<Description>Disable resultList preference tab, default to false.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="renderCategoryLinks" name="Render Category Links">
			<Description>Default false. If enabled, the category links will be rendered to enable filtering.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['underlineCategoryLinks']})</Display>
			</Functions>
		</Option>
		<Option id="underlineCategoryLinks" name="Underline Category Links">
			<Description>Default to false. If enabled, the category links will be underlined</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="ignoreCategoryColor" name="Ignore Category Color">
			<Description>Default to false. If enabled, the color configs from app.xmp will be ignored for all facetDisplays (so all category color is set to blue), only for GridTable mode</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="ajaxSort" name="Enable sort with ajax">
			<Description>Enable sorting of the widget with an ajax request.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="enableInfiniteScroll" name="Enable infiniteScroll" arity="ONE">
			<Description>Enables infinite scroll.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
            <Functions>
                <Display>ToggleDisplay({valueToMatch:['true'], showOptions:['customAbsoluteBlock']})</Display>
            </Functions>
		</Option>
        <Option id="customAbsoluteBlock" name="Custom Absolute Block">
            <Description>Specify a custom absolute block selector for the infinite scroll.
                It Allows to enable infinite scroll if scroll has been moved to a parent container.</Description>
        </Option>
		<OptionComposite id="infiniteScrollExtraParameters" name="Infinite Scroll parameters" arity="ZERO_OR_MANY" glue="##">
			<Description>Add extra parameters to infinite scroll request</Description>
			<Option id="parameterName" name="Parameter name">
				<Description>Name of the parameter.</Description>
			</Option>
			<Option id="parameterValue" name="Parameter value" isEvaluated="true">
				<Description>Value of the parameter.</Description>
			</Option>
		</OptionComposite>
        <Option id="containerHeight" name="Widget container height" arity="ONE_OR_ZERO">
            <Description>Specifies a fixed height for the container (default to 100%).</Description>
            <Functions>
                <Check>isInteger</Check>
            </Functions>
        </Option>
		<Option id="defaultLayout" name="Default layout" arity="ZERO_OR_ONE">
			<Description>The default way to display hits. 'Table' displays each hit on a line, 'Tiles' displays each hit in a block,
				and 'Responsive' switches between 'Table' and 'Tiles' depending on the viewport. Defaults to 'Responsive'.
				'Article Response' displays a responsive view for article content.</Description>
			<Values>
				<Value>Table</Value>
				<Value>Tile</Value>
				<Value>Article</Value>
				<Value>Responsive</Value>
				<Value>Article Responsive</Value>
				<Value>GridTable</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['GridTable'], showOptions:['switchLayout','doTrasnspose', 'enableMetaClass', 'metaList', 'enableMetai18n', 'pinCompareRef']})</Display>
			</Functions>
		</Option>
		<Option id="switchLayout" name="Switch Layout" arity="ZERO_OR_ONE">
			<Description>Specify the layout when switching from GridTable layout.</Description>
			<Values>
				<Value>Tile</Value>
				<Value>Table</Value>
				<Value>Article</Value>
				<Value>Responsive</Value>
				<Value>Article Responsive</Value>
			</Values>
		</Option>
		<Option id="doTrasnspose" name="Transpose the Table" arity="ONE">
			<Description>Show Column as rows and rows as column. This is the default state of the table. if you want to switch add url parameter 'gridtable=default' or 'gridtable=transpose'. If the URL param 'gridtable' is present and not empty, then the we transpose only when 'gridtable=transpose'.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="pinCompareRef" name="Pin Compare Ref Hit" arity="ONE">
			<Description>Enabling this will pin the hit you mark as reference for comparison. This will bring the hit forward for better comparison.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="enableMetaClass" name="Enable Class" arity="ONE">
			<Description>Add metaValue as CSS class. default to false</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['metaList','enableMetai18n']})</Display>
			</Functions>
		</Option>
		<Option id="metaList" name="Meta Names" isEvaluated="false">
			<Description>List of meta for which add metaValue as CSS class. default to NONE</Description>
		</Option>
		<Option id="enableMetai18n" name="Enable i18n" arity="ONE">
			<Description>Add i18n of metaValue. default to false</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="enableResizeColumn" name="Enable column resize" >
			<Description>Default To false. Allows column resizing by dragging the column only in row view.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Advanced">
		<Option id="customHTMLNoResultMessage" name="No result template (html)" isEvaluated="true">
			<Description>Specifies a custom HTML text message when there are no results. Can be a MEL expression.</Description>
			<Functions>
				<Display>SetType('code', { mode: 'html', fullscreen: false })</Display>
			</Functions>
		</Option>
		<Option id="noResultsJspPathHit" name="JSP path to use if no results" arity="ONE">
			<Description>If there are no results, the widget is either hidden using a trigger, or a message is displayed using the content of a JSP file.</Description>
			<Functions>
				<Check>isJspPath</Check>
			</Functions>
		</Option>
		<Option id="templateBasePath" name="Base path of the JSP templates" arity="ONE">
			<Description>You can replace the relative path by an absolute path like /WEB-INF/jsp/mydirectory/.</Description>
			<Functions>
				<Check>isDirectory</Check>
			</Functions>
		</Option>
		<Option id="forceRefineOnFeeds" name="Force refinement for feeds" arity="ZERO_OR_MANY">
			<Description>Forces refinements on the specified feeds.</Description>
			<Functions>
				<ContextMenu>Feeds()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="JSMode" name="Configure JavaScript" arity="one">
			<Description><![CDATA[
				Automatic - JS Configuration will be done for you automatically. <br />
				Custom - You can write/modify your own custom JS configuration. <br/>
				Note : Use Custom mode for compare resultlist.
			]]></Description>
			<Values>
				<Value>Custom JS</Value>
				<Value>Automatic</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['Custom JS'], showOptions:['onInit','onHitClick']})</Display>
			</Functions>
		</Option>
		<Option id="onInit" name="On init" isEvaluated="true">
			<Description>A JS function called on widget initialisation.</Description>
			<Placeholder>function(e) {}</Placeholder>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
		<Option id="onHitClick" name="On hit click">
			<Description>A JS function called when clicking a hit.</Description>
			<Placeholder>function(e) {}</Placeholder>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
		<Option id="hitParamName" name="Hit parameter name" arity="ONE_OR_ZERO">
			<Description>The name of the URL parameter that will contain the selected hit URI when loading details in Ajax (default is 'hit').</Description>
		</Option>
		<Option id="hitParamValue" name="Hit parameter meta" arity="ONE_OR_ZERO">
			<Description>The name of the meta value to put in the url param when loading details in Ajax. By default, we take the meta uri (default is 'entryUri').</Description>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Export">
		<Description>ReslutList Export only supports exportMode to 'Access API' and view to 'Gridtable'. </Description>
		<Option id="enableExport" name="Enable" arity="ONE">
			<Description>
				Enables an "export" button that allows the user to download data to a CSV file. &lt;br/&gt;
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['customExportJSP','resultListId','numHits', 'excludeMetas' , 'exportMode', 'fileName', 'exportEncoding', 'exportSeparator', 'addBOM', 'enableExportAll', 'recordDelimiter', 'exportIcon']})</Display>
			</Functions>
		</Option>
		<Option id="customExportJSP" name="Custom Export Display" arity="ZERO_OR_ONE">
			<Description> <![CDATA[
				If you want to render custom export button to the resultlist, provide custom Export JSP. <br/>
				e.g Default Sample implementation is provided at
				/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaResultList/templates/exportDisplay.jsp
			]]>
			</Description>
			<Functions>
				<Check>isJspPath</Check>
			</Functions>
		</Option>
		<Option id="resultListId" name="ResultList Id" isEvaluated="true" arity="ZERO_OR_ONE">
			<Description>Columns external config ID, by default, result list ID configured in “New Config” tab is used but it’s possible to configure different metadata for export.</Description>
		</Option>
		<Option id="numHits" name="Hits limit" arity="ONE">
			<Description>
				(Only for &lt;b&gt;Hits&lt;/b&gt; mode) The maximum number of hits to export. Set to &lt;code&gt;-1&lt;/code&gt; to set no limit on the export.&lt;br/&gt;
				&lt;b&gt;WARNING&lt;/b&gt;: exporting a large set of results could take a while. It is highly recommended to use the
				&lt;b&gt;Search API&lt;/b&gt; mode for high number of hits, as it allows streaming results.
			</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="excludeMetas" name="Exclude metas" isEvaluated="false">
			<Description>List of metas, for which export is not enable. Default to None</Description>
		</Option>
		<Option id="fileName" name="File name" isEvaluated="true" >
			<Description>The name of the created CSV file. Defaults to 'export.csv'</Description>
		</Option>
		<Option id="exportEncoding" name="Encoding" arity="ZERO_OR_ONE" >
			<Description>You can choose an encoding for the exported CSV file. Defaults to UTF-8.</Description>
		</Option>
		<Option id="exportSeparator" name="Separator" arity="ZERO_OR_ONE">
			<Description>Character to use as a separator in the exported CSV file. Defaults to &lt;b&gt;;&lt;/b&gt;.</Description>
		</Option>
		<Option id="exportIcon" name="Export Icon" arity="ZERO_OR_ONE">
			<Description>Specifies the class name of the icon added to the export button that is displayed on the global toolbar. Defaults to "fonticon-download"</Description>
		</Option>
		<Option id="recordDelimiter" name="Record Delimiter">
			<Description>
				Character that is used to separate consecutive records in the output file.&lt;br/&gt;Default option will take the value based on OS.&lt;br/&gt;
				&lt;ul&gt;
				&lt;li&gt;Unix -> LF&lt;/li&gt;
				&lt;li&gt;Windows -> CR + LF&lt;/li&gt;
				&lt;li&gt;Mac -> CR&lt;/li&gt;
				&lt;/ul&gt;
			</Description>
			<Values>
				<Value>Default</Value>
				<Value>LF</Value>
				<Value>CR+LF</Value>
				<Value>CR</Value>
			</Values>
		</Option>
		<Option id="addBOM" name="Add BOM">
			<Description>Whether to add the byte order mark (BOM) at the start of the file.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Select API">
		<Description>This widget can interact with other widgets in a 3DDashboard. It can publish the user's selection and can subscribe to other widgets' publication.</Description>
		<Option id="enablePublication" name="Enable publication">
			<Description>Enable publication of the user's selection.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="enableSubscription" name="Enable subscription">
			<Description>Enable subscription of selected hits from other widgets.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="channels" name="Channels" arity="ZERO_OR_MANY">
			<Description>Channels to use.</Description>
		</Option>
		<Option id="useApplicationConfig" name="Use the application configuration">
			<Description>Use the application configuration or specify custom configuration.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({ valueToMatch: ['true'], hideOptions: ['channelsConfig'] })</Display>
			</Functions>
		</Option>
		<OptionComposite id="channelsConfig" name="Channels configuration" arity="ZERO_OR_MANY">
			<Option id="topic" name="Topic">
				<Description>Name of the topic.</Description>
			</Option>
			<Option id="data" name="Data" isEvaluated="true">
				<Description>
					<![CDATA[
						JavaScript object that contains useful data. You can use MEL (scope: entry). The hit URL as key is mandatory.<br />
						Example:
<pre>
{
    '${entry.metas['url']}': {
        fullName: '${entry.metas['fullname']}'
    }
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Option id="normalizer" name="Normalizer">
				<Description>
					<![CDATA[
						JavaScript function that returns the appropriate message based on two parameters:
						<ol>
							<li><i>selectedHits</i>: array of hit URLs</li>
							<li><i>data</i>: data previously described</li>
						</ol>
						Example:
<pre>
function (selectedHits, data) {
    return {
        type: 'hello',
        data: selectedHits.map(function (hitUrl) {
            return data[hitUrl].fullName;
        })
    };
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Option id="denormalizer" name="Denormalizer">
				<Description>
					<![CDATA[
						JavaScript function that returns the selected hits based on two parameters:
						<ol>
							<li><i>message</i>: message sent by other widgets</li>
							<li><i>data</i>: data previously described</li>
						</ol>
						Example:
<pre>
function (message, data) {
    return data.map(function (fullName) {
        for (var hitUrl in data) {
            if (data[hitUrl].fullName === fullName) {
                return hitUrl;
            }
        }
        return null;
    });
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="showHitMetas">true</DefaultValue>
		<DefaultValue name="hitUrlTarget">Current Page</DefaultValue>
		<DefaultValue name="filterMetas">No filtering</DefaultValue>
		<DefaultValue name="customDisplay">false</DefaultValue>
		<DefaultValue name="sortModeMetas">default</DefaultValue>
		<DefaultValue name="metaUrlTarget">Current Page</DefaultValue>
		<DefaultValue name="showHitIcon">true</DefaultValue>
		<DefaultValue name="showHitId">true</DefaultValue>
		<DefaultValue name="showTextOnTop">true</DefaultValue>
		<DefaultValue name="showTextOnTopTruncate">500</DefaultValue>
		<DefaultValue name="onClick">//$hit.addClass('selected');</DefaultValue>
		<DefaultValue name="defaultJspPathHit">templates/hit-default.jsp</DefaultValue>
		<DefaultValue name="showHitFacets">true</DefaultValue>
		<DefaultValue name="showEmptyFacets">false</DefaultValue>
		<DefaultValue name="hitFilterFacetsType">No filtering</DefaultValue>
		<DefaultValue name="sortModeFacets">default</DefaultValue>
		<DefaultValue name="enableNewConfig">true</DefaultValue>
		<DefaultValue name="hitFacetSortStrategy">default</DefaultValue>
		<DefaultValue name="showThumbnail">on the left</DefaultValue>
		<DefaultValue name="useThumbnailPreview">true</DefaultValue>
		<DefaultValue name="showPreview">true</DefaultValue>
		<DefaultValue name="showDownload">true</DefaultValue>
		<DefaultValue name="noResultsJspPathHit">/WEB-INF/jsp/commons/noResults.jsp</DefaultValue>
		<DefaultValue name="templateBasePath">templates/</DefaultValue>
		<DefaultValue name="enableInfiniteScroll">true</DefaultValue>
		<DefaultValue name="hitContentDisplay">false</DefaultValue>
		<DefaultValue name="ajaxSort">false</DefaultValue>
		<DefaultValue name="JSMode">Automatic</DefaultValue>
		<DefaultValue name="pinRow">false</DefaultValue>
		<DefaultValue name="iconTitleContainerWidth">false</DefaultValue>
		<DefaultValue name="underlineCategoryLinks">false</DefaultValue>
		<DefaultValue name="onInit" value="">function() {
			new GridTableSetup(widget, {
			buttonSelectors: [
			'.plmaButton.freeze-row-button',
			'.plmaButton.favorite-button',
			'.plmaButton.compare-button'
			]
			});

			var helper = new DetailHitHelper($('.plmaResultList .hits').hasClass('responsive-layout'));
			//helper.initResponsive();
			/* if an anchor is present, select the right hit and open the detail panel */
			if(window.location.href.split('#').length > 1) {
			var anchor = window.location.href.split('#')[1];

			helper.selectHitFromAnchor(anchor);
			helper.loadDetailFromAnchor(anchor,'hit');
			}
			}</DefaultValue>
		<DefaultValue name="onHitClick" value="">function(e) {
			if($(e.target).parent().is('.category-link')){
				return;
			}
			/* Reload the detail widget */
			var $hit = $(e.target).closest('.hit');
			/* if an anchor is present, check if hit already selected and deselect it */
			if (window.location.href.split('#').length > 1) {
			var anchor = window.location.href.split('#')[1];
			}
			if ($hit.data('uri') != anchor ) {
			var helper = new DetailHitHelper($('.plmaResultList .hits').hasClass('responsive-layout'));
			helper.selectHit($hit);
			var entryUri = $hit.data('uri');
			var paramName = $hit.data('parameter');
			$('.selection-panel').addClass('hidden');
			helper.loadDetail(paramName,entryUri);
			$(window).trigger('resize');
			/* push state in url */
			helper.pushState(entryUri);
			} else {
			// Close detail panel
			var helper = new DetailHitHelper();
			helper.closeDetailPanel();
			helper.pushState();
			helper.deselectHit();
			helper.displayTileView();
			$(window).trigger('resize');
			}
			} </DefaultValue>
		<DefaultValue name="disableResultListPrefTab">false</DefaultValue>
		<DefaultValue name="enableMetaClass">false</DefaultValue>
		<DefaultValue name="enableMetai18n">false</DefaultValue>
		<DefaultValue name="enableExport">false</DefaultValue>
		<DefaultValue name="customExportJSP">templates/exportDisplay.jsp</DefaultValue>
		<DefaultValue name="numHits">1000</DefaultValue>
		<DefaultValue name="exportEncoding">UTF-8</DefaultValue>
		<DefaultValue name="exportSeparator">;</DefaultValue>
		<DefaultValue name="recordDelimiter">Default</DefaultValue>
		<DefaultValue name="fileName">export</DefaultValue>
		<DefaultValue name="excludeMetas">None</DefaultValue>
	</DefaultValues>
</Widget>
