<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Add Bookmarks - Deprecated" group="PLM Analytics/Bookmarks" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>A widget to bookmark pages.</Description>


	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO_OR_MANY" />
	<SupportI18N supported="true" />
	
	<Includes>
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="js/addBookmarks.js" />
		<Include type="css" path="css/style.less" />
		<Include type="js" path="../plmaResources/js/popupLib.js" />
	</Includes>

	<Dependencies>
		<Trigger position="self" className="com.exalead.cv360.searchui.triggers.impl.RemoveIfNotLoggedIn" />
		<Widget name="plmaResources" />
	</Dependencies>
	
	<OptionsGroup name="General">
		<Option id="dbKey" name="Storage database key" arity="ONE">
			<Description>Defines the key used in the storage service to store bookmarks</Description>
		</Option>
		<Option id="savedBookmarksWuid" name="Saved Bookmarks wuid" arity="ZERO_OR_ONE">
			<Description>Defines the wuid of the associated Saved Bookmarks widget</Description>
		</Option>
		<Option id="additionalParameters" name="Additional parameters" arity="ZERO_OR_MANY">
			<Description>Defines the additional parameters.</Description>
		</Option>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="dbKey">saved_views[]</DefaultValue>
	</DefaultValues>

</Widget>
