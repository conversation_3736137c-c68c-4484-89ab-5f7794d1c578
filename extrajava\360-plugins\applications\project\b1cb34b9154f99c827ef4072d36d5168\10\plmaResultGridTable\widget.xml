<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA GridTable result list" group="PLM Analytics/Results Rendering" premium="true">

    <Description>This widget displays the search results of a PLM Analytics app in a GridTable view.
        This widget only displays a set of results in its tabular form.</Description>

    <Includes>
    </Includes>

    <Preview>
        <![CDATA[
			<img src="img/preview.png" />
		]]>
    </Preview>

    <Dependencies>
        <Widget name="plmaResultListCommons" />
        <Widget name="preferredHit" />
        <Widget name="plmaExport" />
        <Widget name="compareHit" />
    </Dependencies>

    <SupportWidgetsId arity="ZERO_OR_MANY" label="foreach hit" />
    <SupportFeedsId arity="MANY" consumeFeed="true"/>


    <SupportI18N supported="true">
        <JsKeys>
            <JsKey>hitcustom.sort_asc</JsKey>
            <JsKey>hitcustom.sort_dsc</JsKey>
            <JsKey>plma.resultlist.view.default</JsKey>
            <JsKey>plma.resultlist.view.gridtable</JsKey>
            <JsKey>plma.resultlist.view.gridtable.rows</JsKey>
            <JsKey>plma.resultlist.view.gridtable.columns</JsKey>
            <JsKey>plma.resultlist.view.gridtable.pin</JsKey>
            <JsKey>plma.resultlist.view.gridtable.pincol</JsKey>
            <JsKey>plma.resultlist.view.gridtable.unpincol</JsKey>
            <JsKey>plma.resultlist.view.gridtable.unpin</JsKey>
            <JsKey>plma.resultlist.compare.hidesimilarcols.hide</JsKey>
            <JsKey>plma.resultlist.compare.hidesimilarcols.show</JsKey>

            <JsKey>plma.resultlist.tab.label</JsKey>
            <JsKey>plma.resultlist.tab.title</JsKey>
            <JsKey>plma.resultlist.tab.description</JsKey>
            <JsKey>plma.resultlist.button.selectall</JsKey>
            <JsKey>plma.resultlist.button.deselectall</JsKey>
            <JsKey>plma.resultlist.column.state.false</JsKey>
            <JsKey>plma.resultlist.column.state.ifnotempty</JsKey>
            <JsKey>plma.resultlist.column.state.true</JsKey>
            <JsKey>plma.resultlist.save</JsKey>
            <JsKey>plma.resultlist.reset</JsKey>
        </JsKeys>
    </SupportI18N>

    <OptionsGroup name="Hit config">
        <!-- General config -->
        <Option id="section-config" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; General config &lt;/b&gt;&lt;/span&gt;">
            <Functions>
                <Display>SetType('literal')</Display>
            </Functions>
        </Option>

        <Option id="hitTitle" name="Hit title" isEvaluated="true">
            <Description>Specifies the title displayed for each hit. Leave empty to use the &lt;i&gt;Title&lt;/i&gt; defined in the source feed. </Description>
        </Option>
        <Option id="hitUrl" name="Hit title url" arity="ZERO_OR_ONE" isEvaluated="true">
            <Description>Specifies the destination URL when clicking the hit title.</Description>
        </Option>
        <Option id="hitContent" name="Hit content" isEvaluated="true">
            <Description>Specifies the content displayed for each hit. Leave empty to use the &lt;i&gt;Content&lt;/i&gt; defined in the source feed.</Description>
        </Option>
        <Option id="hitContentDisplay" name="Hit content displayment" isEvaluated="true">
            <Description>Displays the hit content by default. Content will be hidden otherwise.</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>
        <Option id="maxContentLength" name="Max length">
            <Description>Maximum length (number of chars) of the displayed content. Any characters above this limit are truncated. 0 means no limit.
                WARNING : this option should not be used with highlighting search feature as it can break highlighting style.
            </Description>
            <Functions>
                <Check>isInteger</Check>
            </Functions>
        </Option>
        <Option id="typeFacet" name="Type facet">
            <Description>You can specify a facet to display hits differently based on it.</Description>
            <Functions>
                <ContextMenu>Facets()</ContextMenu>
            </Functions>
        </Option>
        <Option id="typeFacetIcon" name="Type facet default icon">
            <Description>You can specify a default icon associated with the hit's type (default is fonticon-legend).</Description>
            <Functions>
                <ContextMenu>Facets()</ContextMenu>
            </Functions>
        </Option>
        <Option id="pinRow" name="Freeze row" arity="ONE">
            <Description>
                Freeze row below the header. Defaults to false.
            </Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>

        <Option id="displayHeader" name="Display header" isEvaluated="true">
            <Description>Display header with number of hits and actions buttons.</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>

        <!-- External config reference -->
        <Option id="section-config" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; External application hits config &lt;/b&gt;&lt;/span&gt;">
            <Functions>
                <Display>SetType('literal')</Display>
            </Functions>
        </Option>
        <Option id="resultListId" name="ResultList Id" isEvaluated="true" arity="ONE">
            <Description>ResultList Configurations ID to use. You can provide MEL expression as well if you need conditional display.</Description>
        </Option>

        <!-- Thumbnail Config Section -->
        <Option id="section-hitThumbnail" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Hit thumbnail Section &lt;/b&gt;&lt;/span&gt;">
            <Functions>
                <Display>SetType('literal')</Display>
            </Functions>
        </Option>
        <Option id="showThumbnail" name="Display thumbnails" arity="ONE">
            <Description>Displays hit thumbnails.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
            <Functions>
                <Display>PARAMETER_doHide('false', [], ['useThumbnailPreview', 'urlDefaultThumbnail', 'tbHeight', 'tbWidth'], false, false)</Display>
                <Display>ToggleDisplay({ valueToMatch: ['true', 'false'], hideOptions: ['mainContainerId'] })</Display>
            </Functions>
        </Option>
        <Option id="useThumbnailPreview" name="Use CloudView thumbnails" arity="ONE">
            <Description>Uses the fetcher to retrieve CloudView's computed thumbnails.</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>
        <Option id="urlDefaultThumbnail" name="Default URL thumbnail" isEvaluated="true">
            <Description>This thumbnail is displayed if there are no thumbnails available in the feed's hit. Leave this field blank to have default, MIME type dependent thumbnails.</Description>
        </Option>
        <Option id="tbHeight" name="Thumbnail Height" arity="ZERO_OR_ONE" isEvaluated="true">
            <Description>Specifies the thumbnails height (pixels). You must enter an integer.</Description>
        </Option>
        <Option id="tbWidth" name="Thumbnail Width" arity="ZERO_OR_ONE" isEvaluated="true">
            <Description>Specifies the thumbnails width (pixels). You must enter an integer.</Description>
        </Option>
        <Option id="mainContainerId" name="On-scroll loading container id" isEvaluated="true">
            <Description>If set, on scroll-loading will be applied to the specific container instead of the entire window. You must set the css id of a scrollable element.</Description>
        </Option>
    </OptionsGroup>

    <OptionsGroup name="General">
        <Option id="title" name="Widget Title" isEvaluated="true">
            <Description>Widget title. If empty, no name is displayed.</Description>
        </Option>

        <Option id="disableResultListPrefTab" name="Disable preferences tab" arity="ONE">
            <Description>Disable resultList preference tab, default to false.</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>

        <Option id="renderCategoryLinks" name="Render Category Links">
            <Description>Default false. If enabled, the category links will be rendered to enable filtering.</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
            <Functions>
                <Display>ToggleDisplay({valueToMatch:['true'], showOptions:['underlineCategoryLinks']})</Display>
            </Functions>
        </Option>

        <Option id="underlineCategoryLinks" name="Underline Category Links">
            <Description>Default to false. If enabled, the category links will be underlined</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>

        <Option id="ignoreCategoryColor" name="Ignore Category Color">
            <Description>Default to false. If enabled, the color configs from app.xmp will be ignored for all facetDisplays (so all category color is set to blue), only for GridTable mode</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>

        <Option id="ajaxSort" name="Enable sort with ajax">
            <Description>Enable sorting of the widget with an ajax request.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>

        <Option id="enableInfiniteScroll" name="Enable infiniteScroll" arity="ONE">
            <Description>Enables infinite scroll.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
            <Functions>
                <Display>ToggleDisplay({valueToMatch:['true'], showOptions:['customAbsoluteBlock']})</Display>
            </Functions>
        </Option>

        <OptionComposite id="infiniteScrollExtraParameters" name="Infinite Scroll parameters" arity="ZERO_OR_MANY" glue="##">
            <Description>Add extra parameters to infinite scroll request</Description>
            <Option id="parameterName" name="Parameter name">
                <Description>Name of the parameter.</Description>
            </Option>
            <Option id="parameterValue" name="Parameter value" isEvaluated="true">
                <Description>Value of the parameter.</Description>
            </Option>
        </OptionComposite>

        <Option id="containerHeight" name="Widget container height" arity="ONE_OR_ZERO">
            <Description>Specifies a fixed height for the container (default to 100%).</Description>
            <Functions>
                <Check>isInteger</Check>
            </Functions>
        </Option>
        <Option id="doTranspose" name="Allow transpose mode" arity="ONE">
            <Description>Show Column as rows and rows as column. This is the default state of the table. if you want to switch add url parameter 'gridtable=default' or 'gridtable=transpose'. If the URL param 'gridtable' is present and not empty, then the we transpose only when 'gridtable=transpose'.</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>
        <Option id="enableMetaClass" name="Enable Class" arity="ONE">
            <Description>Add metaValue as CSS class. default to false</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
            <Functions>
                <Display>ToggleDisplay({valueToMatch:['true'], showOptions:['metaList','enableMetai18n']})</Display>
            </Functions>
        </Option>
        <Option id="metaList" name="Meta Names" isEvaluated="false">
            <Description>List of meta for which add metaValue as CSS class. default to NONE</Description>
        </Option>
        <Option id="enableMetai18n" name="Enable i18n" arity="ONE">
            <Description>Add i18n of metaValue. default to false</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>
    </OptionsGroup>

    <OptionsGroup name="Compare">
        <Description>Compare option, de-activated by default. It allows to display compare view (only usable if compare view is present in result list layout) </Description>
        <Option id="enableCompare" name="Enable" arity="ONE">
            <Description>
                Enables "compare" button that allows user to compare multiple hits.&lt;br/&gt;
            </Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
            <Functions>
                <Display>ToggleDisplay({valueToMatch:['true'], showOptions:['enableCompareDelete', 'minCompareSize', 'compareCollection', 'compareMode', 'compareLabel', 'showCompareLabel', 'compareIdMetaName']})</Display>
            </Functions>
        </Option>

        <Option id="enableCompareDelete" name="Display delete button" arity="ONE">
            <Description>
                Display delete button.&lt;br/&gt;
            </Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>

        <Option id="minCompareSize" name="Minimum items to display button" arity="ZERO_OR_ONE">
            <Description>Minimum items in compare collection to display button.</Description>
            <Functions>
                <Check>isInteger</Check>
            </Functions>
        </Option>

        <Option id="compareCollection" name="Compare collection name">
            <Description>Collection name.</Description>
        </Option>

        <Option id="compareMode" name="Compare collection mode">
            <Description>Collection parameter creation mode (JS to pass hits in URL, UI trigger to forge param in PreRequestTrigger).</Description>
            <Values>
                <Value>trigger</Value>
                <Value>js</Value>
            </Values>
        </Option>

        <Option id="compareIdMetaName" name="ID meta name" isEvaluated="true" arity="ZERO_OR_ONE">
            <Description>Specifies metadata used in compare collection, by default, raw URL is used if empty.</Description>
        </Option>

        <Option id="showCompareLabel" name="Show label" arity="ONE">
            <Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>
    </OptionsGroup>

    <OptionsGroup name="Favorites">
        <Description>Favorites options, de-activated by default. It allows to display hit favorite button (only usable if compare view is present in result list layout) </Description>
        <Option id="enableFavorite" name="Enable" arity="ONE">
            <Description>
                Enables "favorite" button that allows user to add/remove hit from favorites.&lt;br/&gt;
            </Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
            <Functions>
                <Display>ToggleDisplay({valueToMatch:['true'], showOptions:['enableFavoriteDelete', 'favoriteCollection', 'favoriteCounterSelector', 'favoriteMode', 'favoriteIdMetaName']})</Display>
            </Functions>
        </Option>

        <Option id="enableFavoriteDelete" name="Display delete button" arity="ONE">
            <Description>
                Display delete button.&lt;br/&gt;Only used if header is rendered by view itself.
            </Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>

        <Option id="favoriteCollection" name="Favorites collection name">
            <Description>Collection name.</Description>
        </Option>

        <Option id="favoriteMode" name="Favorites collection mode">
            <Description>Collection parameter creation mode (JS to pass hits in URL, UI trigger to forge param in PreRequestTrigger).</Description>
            <Values>
                <Value>trigger</Value>
                <Value>js</Value>
            </Values>
        </Option>

        <Option id="favoriteIdMetaName" name="ID meta name" isEvaluated="true" arity="ZERO_OR_ONE">
            <Description>Specifies metadata used in favorites collection, by default, raw URL is used if empty.</Description>
        </Option>
    </OptionsGroup>

    <OptionsGroup name="Multi-select">
        <Description>Activate multi-selection</Description>
        <Option id="enableSelect" name="Enable" arity="ONE">
            <Description>
                Enables "select" button.&lt;br/&gt;
            </Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>

            <Functions>
                <Display>ToggleDisplay({valueToMatch:['true'], showOptions:['selectCollection', 'selectIdMetaName']})</Display>
            </Functions>
        </Option>

        <Option id="selectCollection" name="Collection name">
            <Description>Select collection name.</Description>
        </Option>

        <Option id="selectIdMetaName" name="ID meta name" isEvaluated="true" arity="ZERO_OR_ONE">
            <Description>Specifies metadata used in selected items collection, by default, raw URL is used if empty.</Description>
        </Option>
    </OptionsGroup>

    <OptionsGroup name="Export">
        <Description>ReslutList Export option (recommended to use streaming mode to avoid too many feed queries on server side). </Description>
        <Option id="enableExport" name="Enable" arity="ONE">
            <Description>
                Enables an "export" button that allows the user to download data to a CSV file. &lt;br/&gt;
            </Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
            <Functions>
                <Display>ToggleDisplay({valueToMatch:['true'], showOptions:['exportIconCss','exportLabel','exportShowLabel','exportNumHits','exportColumnsConfig','exportMode','exportPerPage','exportFileName','exportEncoding','exportSeparator','exportDelimiter','exportAddBOM','exportRawValues']})</Display>
            </Functions>
        </Option>
        <Option id="exportIconCss" name="Icon CSS" >
            <Description>Specifies the CSS class name of the icon added to the button. If empty, no icon is displayed.</Description>
        </Option>
        <Option id="exportLabel" name="Label" isEvaluated="true" arity="ONE">
            <Description>Specifies the label of the button. Briefly explains the purpose of the button (3 words max).</Description>
        </Option>
        <Option id="exportShowLabel" name="Show label" arity="ONE">
            <Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>
        <Option id="exportNumHits" name="Hits limit" arity="ONE">
            <Description>
                (Only for &lt;b&gt;Hits&lt;/b&gt; mode) The maximum number of hits to export. Set to &lt;code&gt;-1&lt;/code&gt; to set no limit on the export.&lt;br/&gt;
                &lt;b&gt;WARNING&lt;/b&gt;: exporting a large set of results could take a while. It is highly recommended to use the
                &lt;b&gt;Search API&lt;/b&gt; mode for high number of hits, as it allows streaming results.
            </Description>
            <Functions>
                <Display>SetType('number')</Display>
                <Check>isInteger</Check>
            </Functions>
        </Option>
        <Option id="exportColumnsConfig" name="ResultList ID" arity="ZERO_OR_ONE">
            <Description>Columns external config ID</Description>
        </Option>
        <Option id="exportMode" name="Export mode" arity="ONE">
            <Description>
                (Only for &lt;b&gt;Hits&lt;/b&gt; mode) The underlying API used to export the data. Only used in &lt;b&gt;Hits&lt;/b&gt; mode (in &lt;b&gt;Synthesis&lt;/b&gt; mode, the data is fully loaded).&lt;br/&gt;
                &lt;b&gt;Access API&lt;/b&gt; mode will execute the feed and attached Access triggers to get the data, querying page after page to get the desired number of results.&lt;br/&gt;
                &lt;b&gt;Search API&lt;/b&gt; mode will execute the provided query to get the data, and stream the results from the index without needing to paginate. It is more efficient than the Access API.&lt;br/&gt;
                &lt;b&gt;WARNINGS&lt;/b&gt;
                &lt;ul&gt;
                &lt;li&gt;
                In &lt;b&gt;Search API&lt;/b&gt; mode, the row order in the exported file is not guaranteed to be the same as displayed in the table. This is because this mode uses
                the streaming option of the Search API, which allows fast download but prevents from sorting documents. Make sure to adapt the hits limit so your users have all the data they need.
                &lt;/li&gt;
                &lt;li&gt;
                In &lt;b&gt;Search API&lt;/b&gt; mode, MEL expressions are evaluated from the &lt;b&gt;entry&lt;/b&gt; scope only. In the Data tab, when configuring column values, the MEL expressions should
                not use any other scopes as the MEL evaluator will not be aware of them (such as &lt;b&gt;feeds&lt;/b&gt;).
                &lt;/li&gt;
                &lt;li&gt;
                The &lt;b&gt;Search API&lt;/b&gt; mode only works with CloudView feeds.
                &lt;/li&gt;
                &lt;/ul&gt;
            </Description>
            <Values>
                <Value>Search API</Value>
                <Value>Access API</Value>
            </Values>
            <Functions>
                <Display>ToggleDisplay({valueToMatch:['Access API'], showOptions:['exportPerPage']})</Display>
            </Functions>
        </Option>
        <Option id="exportPerPage" name="Override 'per_page'" arity="ZERO_OR_ONE">
            <Description>
                When using the &lt;b&gt;Access API&lt;/b&gt; export mode, you can override the &lt;code&gt;per_page&lt;/code&gt; parameter of the feed. Setting a higher value will allow to produce less queries to the index.
                Set to &lt;code&gt;-1&lt;/code&gt; to fetch all hits in one query (the &lt;b&gt;Hits limit&lt;/b&gt; still applies). Leave empty to use the value set on the feed.
            </Description>
            <Functions>
                <Display>SetType('number')</Display>
                <Check>isInteger</Check>
            </Functions>
        </Option>
        <Option id="exportFileName" name="File name" isEvaluated="true" >
            <Description>The name of the created CSV file. Defaults to 'export.csv'</Description>
        </Option>
        <Option id="exportEncoding" name="Encoding" arity="ZERO_OR_ONE" >
            <Description>You can choose an encoding for the exported CSV file. Defaults to UTF-8.</Description>
        </Option>
        <Option id="exportSeparator" name="Separator" arity="ZERO_OR_ONE">
            <Description>Character to use as a separator in the exported CSV file. Defaults to &lt;b&gt;;&lt;/b&gt;.</Description>
        </Option>
        <Option id="exportDelimiter" name="Record Delimiter">
            <Description>
                Character that is used to separate consecutive records in the output file.&lt;br/&gt;Default option will take the value based on OS.&lt;br/&gt;
                &lt;ul&gt;
                &lt;li&gt;Unix -> LF&lt;/li&gt;
                &lt;li&gt;Windows -> CR + LF&lt;/li&gt;
                &lt;li&gt;Mac -> CR&lt;/li&gt;
                &lt;/ul&gt;
            </Description>
            <Values>
                <Value>Default</Value>
                <Value>LF</Value>
                <Value>CR+LF</Value>
                <Value>CR</Value>
            </Values>
        </Option>
        <Option id="exportAddBOM" name="Add BOM">
            <Description>Whether to add the byte order mark (BOM) at the start of the file.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>
        <Option id="exportRawValues" name="Export raw values">
            <Description>Export raw values or I18N values (applicable for facets and headers).</Description>
            <Values>
                <Value>false</Value>
                <Value>true</Value>
            </Values>
        </Option>
    </OptionsGroup>

    <OptionsGroup name="Advanced">
        <Option id="customHTMLNoResultMessage" name="No result template (html)" isEvaluated="true">
            <Description>Specifies a custom HTML text message when there are no results. Can be a MEL expression.</Description>
            <Functions>
                <Display>SetType('code', { mode: 'html', fullscreen: false })</Display>
            </Functions>
        </Option>
        <Option id="noResultsJspPathHit" name="JSP path to use if no results" arity="ONE">
            <Description>If there are no results, the widget is either hidden using a trigger, or a message is displayed using the content of a JSP file.</Description>
            <Functions>
                <Check>isJspPath</Check>
            </Functions>
        </Option>
        <Option id="templateBasePath" name="Base path of the JSP templates" arity="ONE">
            <Description>You can replace the relative path by an absolute path like /WEB-INF/jsp/mydirectory/.</Description>
            <Functions>
                <Check>isDirectory</Check>
            </Functions>
        </Option>
        <Option id="hitParamName" name="Hit parameter name" arity="ONE_OR_ZERO">
            <Description>The name of the URL parameter that will contain the selected hit URI when loading details in Ajax (default is 'hit').</Description>
        </Option>
        <Option id="hitParamValue" name="Hit parameter meta" arity="ONE_OR_ZERO">
            <Description>The name of the meta value to put in the url param when loading details in Ajax. By default, we take the meta uri (default is 'entryUri').</Description>
        </Option>
        <Option id="displayViewId" name="Display view ID" arity="ONE">
            <Description>Result list display view ID, it is used by result list layout to choose displayed view and header buttons based on URL parameter ID.</Description>
        </Option>
        <Option id="displayViewIcon" name="Display view icon" arity="ONE">
            <Description>View icon to display in header.</Description>
        </Option>
        <Option id="displayViewTitle" name="Display view title" arity="ONE">
            <Description>View icon title to display in header.</Description>
        </Option>
        <Option id="titleTemplatePath" name="Title template path" arity="ZERO_OR_ONE" >
            <Description>Result list view title template path.</Description>
        </Option>
        <Option id="titleTemplateWidget" name="Title template widget" arity="ZERO_OR_ONE">
            <Description>Result list view title template widget ID (can be different from current view, for example to factorize code).</Description>
        </Option>
    </OptionsGroup>

    <OptionsGroup name="Style">
        <Option id="fixedColumnWidth" name="Fixed column width (px)" isEvaluated="true" >
            <Description>Fixed column width (in pixels) (can be empty, default value is 500px)</Description>
            <Functions>
                <Check>isInteger</Check>
            </Functions>
        </Option>
        <Option id="toolbarWidth" name="Toolbar width (px)" isEvaluated="true" >
            <Description>Toolbar width (in pixels) (can be empty, default value is 100px). This toolbar contains hits action buttons.</Description>
            <Functions>
                <Check>isInteger</Check>
            </Functions>
        </Option>
    </OptionsGroup>

    <DefaultValues>
        <DefaultValue name="showThumbnail">on the left</DefaultValue>
        <DefaultValue name="useThumbnailPreview">true</DefaultValue>
        <DefaultValue name="noResultsJspPathHit">/WEB-INF/jsp/commons/noResults.jsp</DefaultValue>
        <DefaultValue name="templateBasePath">templates/</DefaultValue>
        <DefaultValue name="enableInfiniteScroll">true</DefaultValue>
        <DefaultValue name="hitContentDisplay">false</DefaultValue>
        <DefaultValue name="ajaxSort">false</DefaultValue>
        <DefaultValue name="pinRow">false</DefaultValue>
        <DefaultValue name="underlineCategoryLinks">false</DefaultValue>
        <DefaultValue name="excludeMetas">None</DefaultValue>
        <DefaultValue name="displayHeader">false</DefaultValue>
        <DefaultValue name="enableMetaClass">false</DefaultValue>
        <DefaultValue name="enableMetai18n">false</DefaultValue>
        <DefaultValue name="enableCompare">false</DefaultValue>
        <DefaultValue name="enableSelect">false</DefaultValue>
        <DefaultValue name="compareCollection">compare</DefaultValue>
        <DefaultValue name="selectCollection">selected-hits</DefaultValue>
        <DefaultValue name="minCompareSize">2</DefaultValue>
        <DefaultValue name="compareMode">trigger</DefaultValue>
        <DefaultValue name="displayViewId">gridtable</DefaultValue>
        <DefaultValue name="displayViewIcon">fonticon-table</DefaultValue>
        <DefaultValue name="displayViewTitle">${i18n['plma.resultlist.view.gridtable']}</DefaultValue>
        <DefaultValue name="enableFavorite">false</DefaultValue>
        <DefaultValue name="enableFavoriteDelete">true</DefaultValue>
        <DefaultValue name="favoriteCollection">favoriteHits</DefaultValue>
        <DefaultValue name="favoriteMode">trigger</DefaultValue>
        <DefaultValue name="enableExport">false</DefaultValue>
        <DefaultValue name="exportEncoding">UTF-8</DefaultValue>
        <DefaultValue name="exportSeparator">;</DefaultValue>
        <DefaultValue name="exportFileName">export</DefaultValue>
        <DefaultValue name="exportIconCss">fonticon fonticon-export-multiple</DefaultValue>
        <DefaultValue name="exportShowLabel">false</DefaultValue>
        <DefaultValue name="exportLabel">Export data</DefaultValue>
        <DefaultValue name="exportNumHits">1000</DefaultValue>
        <DefaultValue name="exportMode">Search API</DefaultValue>
        <DefaultValue name="exportDelimiter">Default</DefaultValue>
        <DefaultValue name="exportAddBOM">true</DefaultValue>
        <DefaultValue name="exportRawValues">false</DefaultValue>
        <DefaultValue name="disableResultListPrefTab">false</DefaultValue>
    </DefaultValues>
</Widget>
