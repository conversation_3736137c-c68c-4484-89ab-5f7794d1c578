/**
 * Simple API to manage the back button behavior.
 */
var BackButton = (function() {

	var REFERER_COOKIE_KEY = 'plma.referer';
	var REFERER_COOKIE_PATH = '/';
	/**
	 * LIFO structure
	 * @private
	 * @example
	 * [
	 * 		{label: 'Related object 2', url: 'related_object_2_url'}, // most recent
	 * 		{label: 'Related object 1', url: 'related_object_1_url'},
	 * 		{label: 'Search', url: 'search_page_url'},
	 * 		{label: 'My dashboard', url: 'dashboard_url'}
	 * ]
	 */
	var history = [];

	function save() {
		$.cookie(REFERER_COOKIE_KEY, JSON.stringify(history), {path: REFERER_COOKIE_PATH});
	}

	$(function init() {
		var value = $.cookie? $.cookie(REFERER_COOKIE_KEY) : undefined;
		history = value ? JSON.parse(value) : [];
	});

	/**
	 * Returns true if the browser needs to refresh.
	 *
	 * @param {String} targetUrl - Target url
	 * @returns {Boolean} true if the browser needs to refresh
	 */
	function willRefresh(targetUrl) {
		return targetUrl.split('#')[0] !== window.location.href.split('#')[0];
	}

	return {
		HISTORY_DROPDOWN_CLASS: 'history-dropdown',
		createDropdown: function() {
			var $dropdown = $('<ul/>', {
				'class': BackButton.HISTORY_DROPDOWN_CLASS + ' hidden'
			});
			history.forEach(function(entry, i) {
				$dropdown.append($('<li/>', {'class': 'history-entry' + (entry.dashboardPage ? ' dashboardPage' : '')}).append(
					$('<a/>', {
						href: entry.url,
						text: entry.label,
						click: function(e) {
							e.preventDefault();
							this.goBack(i);
						}.bind(this)
					}).prepend(
						$('<i/>', {'class': entry.icon})
					)
				));
			}, this);
			return $dropdown;
		},
		addHistoryEntry: function(entry) {
			history.unshift(entry);
			save();
		},
		goBack: function(index) {
			index = index || 0;
			if (index >= history.length) {
				throw new Error('Out of bounds');
			}
			if (history.length) {
				var entry = history[index];
				var referer = entry.url;
				/* Remove the first (index + 1) entries of the history */
				history.splice(0, index + 1);
				save();
				if (!willRefresh(referer)) {
					/*
					 * Case when only the referer's hash changes with respect to window.location.href
					 * window.location.assign won't refresh the page so we must use the history instead in order to refresh
					 */
					window.history.pushState({}, 'referer', referer);
					window.location.reload();
				} else {
					window.location.assign(referer);
				}
			} else {
				/* No history: mimic the browser's back button. */
				window.history.back();
			}
		},
		shouldShowButton: function() {
			return !!history.length;
		},
		clearHistory: function() {
			history = [];
			save();
		}
	};

})();