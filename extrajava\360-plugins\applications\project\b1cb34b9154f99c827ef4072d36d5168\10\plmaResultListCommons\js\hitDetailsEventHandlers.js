var HitDetailsEventsHandler = function (uCssId) {
    this.uCssId = uCssId;
    this.widget = $('.' + uCssId);

    if (this.widget.length === 0) {
        throw new Error('Unable to initialize widget HitDetailsEventsHandler : widget not found (uCssId: "' + uCssId + '").');
    } else {
        this.init();
    }
};

HitDetailsEventsHandler.prototype.init = function () {
    // Init event handlers ...
    this.widget.find("li.hit").off('click').on('click', $.proxy(function (e) {
        e.currentTarget.scrollIntoView({ block: 'nearest' })
        let selectedUri = window.location.hash;
        let anchor = "";
        if (selectedUri != ""){
            anchor = selectedUri.substring(1);
        }
        let $hit = $(e.target).closest('.hit');
        /* if an anchor is present, check if hit already selected and deselect it */
        let helper = new DetailHitHelper(true);
        if ($hit.data('hit-id') != anchor) {
            helper.selectHit($hit);
            let entryUri = $hit.data('hit-id');
            let paramName = $hit.data('parameter');
            $('.selection-panel').addClass('hidden');
            helper.loadDetail(paramName, entryUri);
            //$(window).trigger('resize');
            /* push state in url */
            helper.pushState(entryUri);
        } else {
            helper.closeDetailPanel();
            helper.pushState();
            helper.deselectHit();
        }
    }, this));
}

