var FacetRange = function (facetRanges, uCssId, options) {
	this.facetRanges = facetRanges;
	this.uCssId = uCssId;
	var defaultOptions = {};
	this.options = $.extend({}, defaultOptions, options);
	this.widget = $('.' + this.uCssId);
	this.container = $('.' + this.uCssId + ' .preference-facet-range-container');
};

FacetRange.prototype.init = function () {
	this.initTitle();
	this.initTree();
	this.initButtons();
};

FacetRange.prototype.initTitle = function () {
	var titleBlock = $('<div class="preference-title"></div>');
	titleBlock.append($('<div class="main">' + Preferences.getMessage('plma.preference.facet.range.title') + '</div>'));
	titleBlock.append($('<div class="description">' + Preferences.getMessage('plma.preference.facet.range.description') + '</div>'));

	this.container.append(titleBlock);
};

FacetRange.prototype.initTree = function () {
	var blockContainer = $('<div class="preference-block-container"></div>');
	var facetRangeContainer = $('<div class="preference-block"></div>');
	var setsContainer = $('<div class="preference-elem-block preference-sets-block"></div>');
	for (var i = 0; i < this.facetRanges.length; i++) {
		var set = this.facetRanges[i];
		var setBlock = $('<div class="elem-block set-block collapsed"></div>');
		var setDescription = $('<div class="elem-description set-description" data-id="' + _.escape(set.id) + '"></div>');
		setDescription.append($('<span class="elem-icon set-icon fonticon fonticon-folder"></span>'));
		setDescription.append($('<span class="elem-label set-label">' + _.escape(set.id) + '</span>'));
		if (set.facetRange.length > 0) {
			var dropdownButton = $('<span class="elem-dropdown-icon set-dropdown-icon fonticon fonticon-down-open"></span>');
			setDescription.append(dropdownButton);
		}
		// var trashIcon = $('<span class="trash-icon fonticon fonticon-trash"></span>');
		// setDescription.append(trashIcon);
		// trashIcon.on('click', $.proxy(function (e) {
		// 	$e = $(e.target).closest('.set-block');
		// 	this.deleteElem($e);
		// }, this));
		setDescription.on('click', $.proxy(function (e) {
			$e = $(e.target);
			this.container.find('.set-block').addClass('collapsed');
			$e.closest('.set-block').removeClass('collapsed');
			this.container.find('.selected').removeClass('selected');
			$e.closest('.set-block').addClass('selected');
			this.hideAllDetails();
			this.displayFacetRange();
		}, this));

		setBlock.append(setDescription);
		var setCategories = $('<div class="elem-categories set-categories"></div>');
		for (var j = 0; j < set.facetRange.length; j++) {
			var facetRange = set.facetRange[j];
			var facetRangeDescription = $('<div class="facet-range-description"></div>');
			var rangeLabel = $('<div class="facet-range-label"></div>');
			rangeLabel.append($('<span class="label">' + _.escape(facetRange.label) + '</span>'));
			rangeLabel.append($('<span class="range-id" style="display: none">' + _.escape(facetRange.id) + '</span>'));
			var trashRange = $('<span class="trash-icon fonticon fonticon-trash"></span>');
			rangeLabel.append(trashRange);
			trashRange.on('click', $.proxy(function (e) {
				$e = $(e.target).closest('.facet-range-description');
				this.deleteElem($e);
			}, this));
			facetRangeDescription.append(rangeLabel);
			var facetRangeObject = $('<div class="facet-range"></div>');
			facetRangeObject.append($('<span class="range-start"></span>'));
			var subRangeContainer = $('<div class="sub-ranges-container"></div>');
			if (facetRange.subRanges && facetRange.subRanges.subRange) {
				for (var k = 0; k < facetRange.subRanges.subRange.length; k++) {
					var subRange = facetRange.subRanges.subRange[k];
					var subRangeDiv = $('<div class="sub-range-container collapsed"></div>');
					var subRangeLabel = $('<div class="sub-range-label"></div>');
					subRangeLabel.append($('<span class="label">' + _.escape(subRange.label) + '</span>'));
					subRangeLabel.append($('<span class="sub-range-id" style="display: none">' + _.escape(subRange.id) + '</span>'));
					var trashSubRange = $('<span class="trash-icon fonticon fonticon-trash"></span>');
					subRangeLabel.append(trashSubRange);
					trashSubRange.on('click', $.proxy(function (e) {
						$e = $(e.target).closest('.sub-range-container');
						this.deleteElem($e);
						e.stopPropagation();
					}, this));
					subRangeDiv.append(subRangeLabel);
					subRangeDiv.append($('<div class="sub-range-range"><span class="start"></span><span class="end"></span></div>'));
					subRangeDiv.append($('<div class="sub-range-values"><span class="start">' + _.escape(subRange.start) + '</span><span class="end">' + _.escape(subRange.end) + '</span></div>'));
					subRangeContainer.append(subRangeDiv);
				}
				var plusSubRange = $('<div class="sub-range-container sub-range-plus collapsed"></div>');
				var plusSubRangeDesc = $('<div class="sub-range-range"><span class="plus-icon sub-range-plus fonticon fonticon-plus"></span></div>');
				plusSubRangeDesc.on('click', $.proxy(function () {
					this.addSubRange();
				}, this));
				plusSubRange.append(plusSubRangeDesc);
				subRangeContainer.append(plusSubRange);
			}
			facetRangeObject.on('click', $.proxy(function (e) {
				$e = $(e.target).closest('.facet-range');
				this.container.find('.selected').removeClass('selected');
				$e.closest('.set-block').addClass('selected');
				$e.closest('.facet-range-description').addClass('selected');
				this.container.find('.sub-range-container').addClass('collapsed');
				$e.closest('.facet-range-description').find('.sub-range-container').removeClass('collapsed');
				this.container.find('.active').removeClass('active');
				$e.closest('.facet-range-description').addClass('active');
				//Open detail panel
				this.hideAllDetails();
				this.displaySubRange();
			}, this));
			facetRangeObject.append(subRangeContainer);
			facetRangeObject.append($('<span class="range-end"></span>'));

			facetRangeDescription.append(facetRangeObject);
			facetRangeDescription.append($('<div class="facet-range-values"><span class="start">' + _.escape(facetRange.start) + '</span><span class="end">' + _.escape(facetRange.end) + '</span></div>'));
			setCategories.append(facetRangeDescription);
		}
		var plusDescription = $('<div class="facet-range-description plus-facet-range"></div>');
		var plusButton = $('<div class="facet-range"><span class="facet-range-icon plus-icon fonticon fonticon-plus"></span></div>');
		plusDescription.append(plusButton);
		plusButton.on('click', $.proxy(function () {
			this.addRange();
		}, this));
		setCategories.append(plusDescription);

		setBlock.append(setCategories);
		setsContainer.append(setBlock);
	}
	// var plusContainer = $('<div class="elem-block elem-block-plus set-block collapsed"></div>');
	// plusContainer.append($('<div class="elem-description set-description"><span class="elem-icon facet-icon icon-plus fonticon fonticon-plus"></span></div>'));
	// plusContainer.on('click', $.proxy(function () {
	// 	this.addSet();
	// }, this));
	// setsContainer.append(plusContainer);

	facetRangeContainer.append(setsContainer);

	blockContainer.append(facetRangeContainer);
	this.container.append(blockContainer);
};

FacetRange.prototype.initButtons = function () {
	/* Add Save / cancel buttons */
	var buttonsContainer = $('<div class="button-container"></div>');
	var closeButton = $('<span class="close-button">' + Preferences.getMessage('plma.preference.close') + '</span>');
	var resetButton = $('<span class="reset-button">' + Preferences.getMessage('plma.preference.reset') + '</span>');
	buttonsContainer.append(closeButton);
	buttonsContainer.append(resetButton);
	this.container.append(buttonsContainer);

	resetButton.on('click', $.proxy(function () {
		$.ajax({
			type: 'DELETE',
			url: this.options.url + 'config/user/facet/range/reset',
			success: $.proxy(function () {
				$.notify(Preferences.getMessage('plma.preference.save.success'), 'success');
				setTimeout($.proxy(function() {
					this.reload();
				},this),1000);
			}, this)
		});

	}, this));
	
	closeButton.on('click', $.proxy(function () {
		window.location.reload();
	}, this));
};

FacetRange.prototype.displayFacetRange = function () {
	var range = this.findFacetRange();
	if (!this.facetRange) {
		this.initFacetRange(range);
	} else {
		this.updateFacetRange(range);
	}
	this.facetRange.removeClass('hidden');
};

FacetRange.prototype.hideFacetRange = function () {
	if (this.facetRange) {
		this.facetRange.addClass('hidden');
	}
};

FacetRange.prototype.initFacetRange = function (range) {
	if (!range) {
		range = {};
		range.id = 'newRange';
	}
	this.facetRange = $('<div class="preference-detail preference-facet-range-detail hidden"></div>');
	this.facetRange.append($('<div class="title-container">' + Preferences.getMessage('plma.preference.detail.facet.range.title') + '</div>'));
	this.facetRange.append($('<div class="id-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.id') + ': </span><input class="id-input" type="text" value="' + range.id + '"/></div>'));
	var rangeContainer = $('<div class="facet-range-detail-container"></div>');
	for (var i = 0; i < range.ranges.length - 1; i++) {
		var rangeObject = $('<div class="facet-range-detail-object"></div>');
		rangeObject.append($('<div class="label-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.label') + ': </span><input class="label-input" type="text" value="' + range.ranges[i].label + '"/></div>'));
		rangeObject.append($('<div class="start-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.start') + ': </span><input class="start-input" type="text" value="' + range.ranges[i].start + '"/></div>'));
		rangeObject.append($('<div class="end-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.end') + ': </span><input class="end-input" type="text" value="' + range.ranges[i].end + '"/></div>'));

		/* Listen to modifications */
		var timerLabel = 0;
		rangeObject.find('.label-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.facet-range-detail-object').index();
			clearTimeout(timerLabel);
			timerLabel = setTimeout($.proxy(function () {
				this.editFacetRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description .facet-range-label .label')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));
		var timerStart = 0;
		rangeObject.find('.start-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.facet-range-detail-object').index();
			clearTimeout(timerStart);
			timerStart = setTimeout($.proxy(function () {
				this.editFacetRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description .facet-range-values .start')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));
		var timerEnd = 0;
		rangeObject.find('.end-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.facet-range-detail-object').index();
			clearTimeout(timerEnd);
			timerEnd = setTimeout($.proxy(function () {
				this.editFacetRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description .facet-range-values .end')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));

		rangeContainer.append(rangeObject);
	}
	this.facetRange.append(rangeContainer);

	var timer = 0;
	this.facetRange.find('.id-input').on('keyup', $.proxy(function (e) {
		this.successTemp();
		var inputValue = e.target.value;
		clearTimeout(timer);
		timer = setTimeout($.proxy(function () {
			//this.editSetRangeTemp();
			this.container.find('.set-block.selected .set-description .set-label').text(inputValue);
		}, this), 2000);
	}, this));

	this.container.find('.preference-block-container').append(this.facetRange);
};

FacetRange.prototype.updateFacetRange = function (range) {
	if (!range) {
		range = {};
		range.id = 'newRange';
	}
	this.facetRange.find('.id-container .id-input')[0].value = range.id;
	this.facetRange.find('.facet-range-detail-container').remove();

	var rangeContainer = $('<div class="facet-range-detail-container"></div>');
	for (var i = 0; i < range.ranges.length - 1; i++) {
		var rangeObject = $('<div class="facet-range-detail-object"></div>');
		rangeObject.append($('<div class="label-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.label') + ': </span><input class="label-input" type="text" value="' + range.ranges[i].label + '"/></div>'));
		rangeObject.append($('<div class="start-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.start') + ': </span><input class="start-input" type="text" value="' + range.ranges[i].start + '"/></div>'));
		rangeObject.append($('<div class="end-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.end') + ': </span><input class="end-input" type="text" value="' + range.ranges[i].end + '"/></div>'));

		/* Listen to modifications */
		var timerLabel = 0;
		rangeObject.find('.label-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.facet-range-detail-object').index();
			clearTimeout(timerLabel);
			timerLabel = setTimeout($.proxy(function () {
				this.editFacetRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description .facet-range-label .label')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));
		var timerStart = 0;
		rangeObject.find('.start-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.facet-range-detail-object').index();
			clearTimeout(timerStart);
			timerStart = setTimeout($.proxy(function () {
				this.editFacetRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description .facet-range-values .start')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));
		var timerEnd = 0;
		rangeObject.find('.end-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.facet-range-detail-object').index();
			clearTimeout(timerEnd);
			timerEnd = setTimeout($.proxy(function () {
				this.editFacetRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description .facet-range-values .end')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));

		rangeContainer.append(rangeObject);
	}
	this.facetRange.append(rangeContainer);
};

FacetRange.prototype.findFacetRange = function () {
	var range = {};
	if (this.container.find('.set-block.selected').length > 0) {
		range.id = this.container.find('.set-block.selected .set-description .set-label').text();
		range.ranges = [];
		var ranges = this.container.find('.set-block.selected .set-categories .facet-range-description');
		for (var i = 0; i < ranges.length; i++) {
			var rangeTemp = {};
			rangeTemp.label = $(ranges[i]).find('.facet-range-label .label').text();
			rangeTemp.start = $(ranges[i]).find('.facet-range-values .start').text();
			rangeTemp.end = $(ranges[i]).find('.facet-range-values .end').text();
			range.ranges.push(rangeTemp);
		}

		return range;
	}
	return undefined;
};

FacetRange.prototype.displaySubRange = function () {
	var subRange = this.findSubRange();
	if (!this.subRange) {
		this.initSubRange(subRange);
	} else {
		this.updateSubRange(subRange);
	}
	this.subRange.removeClass('hidden');
};

FacetRange.prototype.hideSubRange = function () {
	if (this.subRange) {
		this.subRange.addClass('hidden');
	}
};

FacetRange.prototype.initSubRange = function (subRange) {
	if (!subRange) {
		subRange = {};
		subRange.label = 'newSubRange';
	}
	this.subRange = $('<div class="preference-detail preference-sub-range-detail hidden"></div>');
	this.subRange.append($('<div class="title-container">' + Preferences.getMessage('plma.preference.detail.facet.range.title') + '</div>'));
	var subRangeContainer = $('<div class="sub-range-detail-container"></div>');
	for (var i = 0; i < subRange.subRanges.length - 1; i++) {
		var subRangeObject = $('<div class="sub-range-detail-object"></div>');
		subRangeObject.append($('<div class="sub-label-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.label') + ': </span><input class="sub-label-input" type="text" value="' + subRange.subRanges[i].label + '"/></div>'));
		subRangeObject.append($('<div class="start-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.start') + ': </span><input class="start-input" type="text" value="' + subRange.subRanges[i].start + '"/></div>'));
		subRangeObject.append($('<div class="end-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.end') + ': </span><input class="end-input" type="text" value="' + subRange.subRanges[i].end + '"/></div>'));

		/* Listen to modifications */
		var timerLabel = 0;
		subRangeObject.find('.sub-label-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.sub-range-detail-object').index();
			clearTimeout(timerLabel);
			timerLabel = setTimeout($.proxy(function () {
				this.editSubRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description.selected .sub-range-container .sub-range-label .label')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));
		var timerStart = 0;
		subRangeObject.find('.start-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.sub-range-detail-object').index();
			clearTimeout(timerStart);
			timerStart = setTimeout($.proxy(function () {
				this.editSubRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description.selected .sub-range-container .sub-range-values .start')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));
		var timerEnd = 0;
		subRangeObject.find('.end-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.sub-range-detail-object').index();
			clearTimeout(timerEnd);
			timerEnd = setTimeout($.proxy(function () {
				this.editSubRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description.selected .sub-range-container .sub-range-values .end')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));

		subRangeContainer.append(subRangeObject);
	}
	this.subRange.append(subRangeContainer);
	this.container.find('.preference-block-container').append(this.subRange);
};

FacetRange.prototype.updateSubRange = function (subRange) {
	if (!subRange) {
		subRange = {};
		subRange.id = 'newSubRange';
	}

	this.subRange.find('.sub-range-detail-container').remove();

	var subRangeContainer = $('<div class="sub-range-detail-container"></div>');
	for (var i = 0; i < subRange.subRanges.length - 1; i++) {
		var subRangeObject = $('<div class="sub-range-detail-object"></div>');
		subRangeObject.append($('<div class="sub-label-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.label') + ': </span><input class="sub-label-input" type="text" value="' + subRange.subRanges[i].label + '"/></div>'));
		subRangeObject.append($('<div class="start-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.start') + ': </span><input class="start-input" type="text" value="' + subRange.subRanges[i].start + '"/></div>'));
		subRangeObject.append($('<div class="end-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.end') + ': </span><input class="end-input" type="text" value="' + subRange.subRanges[i].end + '"/></div>'));

		/* Listen to modifications */
		var timerLabel = 0;
		subRangeObject.find('.sub-label-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.sub-range-detail-object').index();
			clearTimeout(timerLabel);
			timerLabel = setTimeout($.proxy(function () {
				this.editSubRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description.selected .sub-range-container .sub-range-label .label')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));
		var timerStart = 0;
		subRangeObject.find('.start-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.sub-range-detail-object').index();
			clearTimeout(timerStart);
			timerStart = setTimeout($.proxy(function () {
				this.editSubRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description.selected .sub-range-container .sub-range-values .start')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));
		var timerEnd = 0;
		subRangeObject.find('.end-input').on('keyup', $.proxy(function (e) {
			this.successTemp();
			var inputValue = e.target.value;
			var indexChangedObject = $(e.target).closest('.sub-range-detail-object').index();
			clearTimeout(timerEnd);
			timerEnd = setTimeout($.proxy(function () {
				this.editSubRangeTemp(indexChangedObject);
				$(this.container.find('.set-block.selected .set-categories .facet-range-description.selected .sub-range-container .sub-range-values .end')[indexChangedObject]).text(inputValue);
			}, this), 2000);
		}, this));

		subRangeContainer.append(subRangeObject);
	}
	this.subRange.append(subRangeContainer);
};

FacetRange.prototype.findSubRange = function () {
	var range = {};
	if (this.container.find('.set-block .facet-range-description.selected').length > 0) {
		range.label = this.container.find('.set-block .facet-range-description.selected .facet-range-label').text();
		range.subRanges = [];
		var ranges = this.container.find('.set-block .facet-range-description.selected .sub-ranges-container .sub-range-container');
		for (var i = 0; i < ranges.length; i++) {
			var rangeTemp = {};
			rangeTemp.label = $(ranges[i]).find('.sub-range-label .label').text();
			rangeTemp.start = $(ranges[i]).find('.sub-range-values .start').text();
			rangeTemp.end = $(ranges[i]).find('.sub-range-values .end').text();
			range.subRanges.push(rangeTemp);
		}

		return range;
	}
	return undefined;
};

FacetRange.prototype.hideAllDetails = function () {
	this.hideFacetRange();
	this.hideSubRange();
};

FacetRange.prototype.addSubRange = function () {
	/* Add sub range */
	var newSubRange = $('<div class="sub-range-container"></div>');
	var subRangeLabel = $('<div class="sub-range-label"></div>');
	subRangeLabel.append($('<span class="label">' + (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) + '</span>'));
	subRangeLabel.append($('<span class="sub-range-id" style="display: none;">' + this.newId() + '</span>'));
	var trashButton = $('<span class="trash-icon fonticon fonticon-trash"></span>');
	trashButton.on('click', $.proxy(function (e) {
		$e = $(e.target).closest('.sub-range-container');
		this.deleteElem($e);
		e.stopPropagation();
	}, this));
	subRangeLabel.append(trashButton);
	newSubRange.append(subRangeLabel);
	newSubRange.append($('<div class="sub-range-range"><span class="start"></span><span class="end"></span></div>'));
	newSubRange.append($('<div class="sub-range-values"><span class="start"></span><span class="end"></span></div>'));

	this.container.find('.facet-range-description.selected .sub-ranges-container .sub-range-container:last').before(newSubRange);
	this.container.find('.facet-range-description.selected').addClass('active');
	this.container.find('.facet-range-description.selected .facet-range .sub-range-container').removeClass('collapsed');
	this.hideAllDetails();
	this.displaySubRange();

	this.addSubRangeTemp();
};

FacetRange.prototype.addRange = function () {
	/* Add range */

	this.container.find('.facet-range-description.selected').removeClass('selected');

	var newRange = $('<div class="facet-range-description selected"></div>');
	var rangeLabel = $('<div class="facet-range-label"></div>');
	rangeLabel.append($('<span class="label">New Range</span>'));
	rangeLabel.append($('<span class="range-id" style="display: none;">' + this.newId() + '</span>'));
	var trashButton = $('<span class="trash-icon fonticon fonticon-trash"></span>');
	trashButton.on('click', $.proxy(function (e) {
		$e = $(e.target).closest('.facet-range-description');
		this.deleteElem($e);
	}, this));
	rangeLabel.append(trashButton);
	newRange.append(rangeLabel);
	var rangeContainer = $('<div class="facet-range"></div>');
	rangeContainer.append($('<span class="range-start"></span>'));
	var subRangeContainer = $('<div class="sub-ranges-container"></div>');
	var plusSubRange = $('<div class="sub-range-container sub-range-plus collapsed"></div>');
	var plusSubRangeDesc = $('<div class="sub-range-range"><span class="plus-icon sub-range-plus fonticon fonticon-plus"></span></div>');
	plusSubRangeDesc.on('click', $.proxy(function () {
		this.addSubRange();
	}, this));
	plusSubRange.append(plusSubRangeDesc);
	subRangeContainer.append(plusSubRange);
	rangeContainer.append(subRangeContainer);
	rangeContainer.append($('<span class="range-end"></span>'));
	newRange.append(rangeContainer);
	var rangeValues = $('<div class="facet-range-values"></div>');
	rangeValues.append($('<span class="start"></span>'));
	rangeValues.append($('<span class="end"></span>'));
	newRange.append(rangeValues);

	rangeContainer.on('click', $.proxy(function (e) {
		$e = $(e.target).closest('.facet-range');
		this.container.find('.selected').removeClass('selected');
		$e.closest('.set-block').addClass('selected');
		$e.closest('.facet-range-description').addClass('selected');
		this.container.find('.sub-range-container').addClass('collapsed');
		$e.closest('.facet-range-description').find('.sub-range-container').removeClass('collapsed');
		this.container.find('.active').removeClass('active');
		$e.closest('.facet-range-description').addClass('active');
		//Open detail panel
		this.hideAllDetails();
		this.displaySubRange();
	}, this));

	this.container.find('.set-block.selected .set-categories .facet-range-description:last').before(newRange);
	this.hideAllDetails();
	this.displayFacetRange();

	this.addFacetRangeTemp();
};

FacetRange.prototype.addSet = function () {
	/* Add set of ranges */
	var newSet = $('<div class="elem-block set-block selected"></div>');
	var setDescription = $('<div class="elem-description set-description"></div>');
	setDescription.append($('<span class="elem-icon set-icon fonticon fonticon-folder"></span>'));
	setDescription.append($('<span class="elem-label set-label">' + (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) + '</span>'));
	setDescription.append($('<span class="elem-dropdown-icon set-dropdown-icon fonticon fonticon-down-open"></span>'));
	var trashIcon = $('<span class="trash-icon fonticon fonticon-trash"></span>');
	setDescription.append(trashIcon);
	trashIcon.on('click', $.proxy(function (e) {
		$e = $(e.target).closest('.set-block');
		this.deleteElem($e);
	}, this));
	newSet.append(setDescription);
	var setCategories = $('<div class="elem-categories set-categories"></div>');
	var plusDescription = $('<div class="facet-range-description plus-facet-range"></div>');
	var plusButton = $('<div class="facet-range"><span class="facet-range-icon plus-icon fonticon fonticon-plus"></span></div>');
	plusDescription.append(plusButton);
	plusButton.on('click', $.proxy(function () {
		this.addRange();
	}, this));
	setCategories.append(plusDescription);
	newSet.append(setCategories);

	setDescription.on('click', $.proxy(function (e) {
		$e = $(e.target);
		this.container.find('.set-block').addClass('collapsed');
		$e.closest('.set-block').removeClass('collapsed');
		this.container.find('.selected').removeClass('selected');
		$e.closest('.set-block').addClass('selected');
		this.hideAllDetails();
		this.displayFacetRange();
	}, this));

	this.container.find('.preference-sets-block .set-block:last').before(newSet);
	this.hideAllDetails();
	this.displayFacetRange();

	this.addSetRangeTemp();
};

FacetRange.prototype.deleteElem = function (elem) {
	if (elem.hasClass('set-block')) {
		this.removeSetRangeTemp(elem.find('.set-label').text());
	} else if (elem.hasClass('facet-range-description')) {
		this.removeFacetRangeTemp(elem.closest('.set-block').find('.set-label').text(), elem.find('.range-id').text());
	} else if (elem.hasClass('sub-range-container')) {
		this.removeSubRangeTemp(elem.closest('.set-block').find('.set-label').text(), elem.closest('.facet-range-description').find('.range-id').text(), elem.find('.sub-range-id').text());
	}

	elem.remove();
	this.hideAllDetails();
};

FacetRange.prototype.editSetRangeTemp = function () {
	var realSetId = this.container.find('.set-block.selected .set-description .set-label').text();
	// ID not supposed to be modified...
	// var newSetId = this.facetRange.find('.id-container .id-input')[0].value;
	$.ajax({
		type: 'PUT',
		url: this.options.url + 'config/user/set/facet/range/edit',
		data: {
			confName: this.options.configName,
			id: realSetId,
			setId: realSetId
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetRange.prototype.addSetRangeTemp = function () {
	var newSetId = this.facetRange.find('.id-container .id-input')[0].value;
	$.ajax({
		type: 'POST',
		url: this.options.url + 'config/user/set/facet/range/add',
		data: {
			confName: this.options.configName,
			setId: newSetId
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetRange.prototype.removeSetRangeTemp = function (setId) {
	$.ajax({
		type: 'DELETE',
		url: this.options.url + 'config/user/set/facet/range/remove',
		data: {
			confName: this.options.configName,
			setId: setId
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetRange.prototype.editFacetRangeTemp = function (indexRange) {
	var setId = this.container.find('.set-block.selected .set-description .set-label').text();
	var rangeId = $(this.container.find('.set-block.selected .set-categories .facet-range-description')[indexRange]).find('.facet-range-label .range-id').text();
	var facetStart = $(this.facetRange.find('.facet-range-detail-container .facet-range-detail-object')[indexRange]).find('.start-input')[0].value;
	var facetEnd = $(this.facetRange.find('.facet-range-detail-container .facet-range-detail-object')[indexRange]).find('.end-input')[0].value;
	var facetLabel = $(this.facetRange.find('.facet-range-detail-container .facet-range-detail-object')[indexRange]).find('.label-input')[0].value;
	$.ajax({
		type: 'PUT',
		url: this.options.url + 'config/user/facet/range/edit',
		data: {
			confName: this.options.configName,
			facetId: rangeId,
			setId: setId,
			facetStart: facetStart,
			facetEnd: facetEnd,
			facetLabel: facetLabel
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetRange.prototype.addFacetRangeTemp = function () {
	var setId = this.container.find('.set-block.selected .set-description .set-label').text();
	var rangeId = $(this.container.find('.set-block.selected .facet-range-description')[this.container.find('.set-block.selected .facet-range-description').length - 2]).find('.facet-range-label .range-id').text();
	$.ajax({
		type: 'PUT',
		url: this.options.url + 'config/user/facet/range/add',
		data: {
			confName: this.options.configName,
			facetId: rangeId,
			setId: setId
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetRange.prototype.removeFacetRangeTemp = function (setId, rangeId) {
	$.ajax({
		type: 'DELETE',
		url: this.options.url + 'config/user/facet/range/remove',
		data: {
			confName: this.options.configName,
			facetId: rangeId,
			setId: setId
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetRange.prototype.editSubRangeTemp = function (indexSubRange) {
	var setId = this.container.find('.set-block.selected .set-description .set-label').text();
	var rangeId = this.container.find('.set-block.selected .set-categories .facet-range-description.selected .facet-range-label .range-id').text();
	var subRangeId = $(this.container.find('.set-block.selected .set-categories .facet-range-description.selected .facet-range .sub-ranges-container .sub-range-container')[indexSubRange]).find('.sub-range-label .sub-range-id').text();
	var subRangeStart = $(this.subRange.find('.sub-range-detail-container .sub-range-detail-object')[indexSubRange]).find('.start-input')[0].value;
	var subRangeEnd = $(this.subRange.find('.sub-range-detail-container .sub-range-detail-object')[indexSubRange]).find('.end-input')[0].value;
	var subRangeLabel = $(this.subRange.find('.sub-range-detail-container .sub-range-detail-object')[indexSubRange]).find('.sub-label-input')[0].value;
	$.ajax({
		type: 'PUT',
		url: this.options.url + 'config/user/sub/facet/range/edit',
		data: {
			confName: this.options.configName,
			facetId: rangeId,
			setId: setId,
			subId: subRangeId,
			subStart: subRangeStart,
			subEnd: subRangeEnd,
			subLabel: subRangeLabel
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetRange.prototype.addSubRangeTemp = function () {
	var setId = this.container.find('.set-block.selected .set-description .set-label').text();
	var rangeId = this.container.find('.set-block.selected .set-categories .facet-range-description.selected .facet-range-label .range-id').text();
	var subRangeId = $(this.container.find('.set-block.selected .set-categories .facet-range-description.selected .facet-range .sub-ranges-container .sub-range-container')[this.container.find('.set-block.selected .set-categories .facet-range-description.selected .facet-range .sub-ranges-container .sub-range-container').length - 2]).find('.sub-range-label .sub-range-id').text();
	$.ajax({
		type: 'GET',
		url: this.options.url + 'config/user/sub/facet/range/add',
		data: {
			appName: this.options.appName,
			confName: this.options.configName,
			facetId: rangeId,
			setId: setId,
			subId: subRangeId
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetRange.prototype.removeSubRangeTemp = function (setId, rangeId, subId) {
	$.ajax({
		type: 'DELETE',
		url: this.options.url + 'config/user/sub/facet/range/remove',
		data: {
			confName: this.options.configName,
			facetId: rangeId,
			setId: setId,
			subId: subId
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

FacetRange.prototype.newId = function () {
	var S4 = function () {
		return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
	};
	return (S4() + "-" + S4() + "-" + S4() + "-" + S4());
};

FacetRange.prototype.successTemp = function () {
	this.widget.find('.button-container .save-button').addClass('active');
	this.widget.find('.button-container .cancel-button').addClass('active');
};

FacetRange.prototype.failureTemp = function () {
	$.notify(Preferences.getMessage('plma.preference.save.error'), 'error');
};

FacetRange.prototype.reload = function () {
	// var client = new PlmaAjaxClient(this.widget);
	// client.addWidget(this.uCssId);
	// client.update();
	location.reload();
};
