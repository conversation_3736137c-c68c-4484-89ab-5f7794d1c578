var ChartboardFilters = function (chartboard) {
	this.chartboard = chartboard;

	this.exceptionList = ['pageId', '_context', '_', 'lang', '', 'hit'];
};

ChartboardFilters.prototype.init = function () {
	if (this.chartboard.page && this.chartboard.page.filters) {
		this.filtersId = this.chartboard.page.filters;
	}
	this.chartboardStorageManager = new ChartboardStorageManager();
};

ChartboardFilters.prototype.saveFilters = function () {
	/* Create Filter object */
	this.createFilters($.proxy(function (filter) {
		filter.lastEdit = moment();
		/* Save Filter object */
		this.chartboardStorageManager.saveFilter(filter, $.proxy(function () {
			/* Save filterId in page object */
			this.filtersId = filter.id;
			this.chartboard.savePageFilters();
		}, this), $.proxy(function () {
			this.chartboard.displayErrorMessage(Chartboard.getMessage('chartboard.error.message.save'));
		}, this));
	}, this));
};

ChartboardFilters.prototype.removeFilters = function () {
	if (this.filtersId) {
		this.chartboardStorageManager.removeFilter(this.filtersId, $.proxy(function () {
			this.chartboard.removePageFilters();
		}, this), $.proxy(function () {
			this.chartboard.displayErrorMessage(Chartboard.getMessage('chartboard.error.message.save'));
		}, this));
	}
};

ChartboardFilters.prototype.createFilters = function (successCallback) {
	var filters = {};
	if (this.filtersId) {
		this.chartboardStorageManager.getFilter(this.filtersId, $.proxy(function (filter) {
			filters = jQuery.extend(true, {}, filter[this.filtersId]);
			this.addUrlParameters(filters);
			successCallback.call(null, filters);
		}, this), $.proxy(function () {
			this.chartboard.displayErrorMessage(Chartboard.getMessage('chartboard.error.message.save'));
		}, this));
	} else {
		filters.id = this.chartboard.newId();
		this.addUrlParameters(filters);
		successCallback.call(null, filters);
	}
};

ChartboardFilters.prototype.addUrlParameters = function (filters) {
	if (!filters.params) {
		filters.params = {};
	}
	var currentUrl = new BuildUrl(document.location.href);
	for (var param in currentUrl.params) {
		if (!this.exceptionList.includes(param)) {
			if (! filters.params[param]) {
				filters.params[param] = [];
			}
			for(var i=0 ; i< currentUrl.params[param].length ; i++) {
				filters.params[param].push( decodeURIComponent(currentUrl.params[param][i]) );
			}
			//Remove Duplicates.
			filters.params[param] = _.uniq(filters.params[param]);
		}
	}

	// Cleanup urls values....Remove Zapped Refines.
	for(var param in filters.params){
		if(param.endsWith('.zr')){
		    var zapParams = filters.params[param];
			for(var index = 0; index < zapParams.length; index++){
				// Remove from refine param. : Bring value to neutral form 'f/current/create' (remove +/- at start);
				var value = zapParams[index].replace(/[+-]f\//, 'f/');
				var removedValues = _.remove(filters.params[param.replace('.zr','.r')], function (entry) {
					// This will remove all forms -f/current/create and +f/current/create or f/current/create
					return entry.search(value) > -1;
				});
                // if the param removed from the refine params remove from zapParams as well.
                // If nothing removed, meaning the zap param exists without refine param so remove zap param.
                zapParams[index] = "";
			}
			// Remove the empty parameters.
			_.remove(zapParams, function (entry) {
				return entry === "";
			});
		}
	}
	// Remove the refine param if the filter is included/excluded both at same time.
	for(var param in filters.params){
        if(param.endsWith('.r')){
            var refineParams = filters.params[param];
            for(var index = 0; index < refineParams.length; index++){
                // Remove from refine param. :Bring value to neutral form 'f/current/create' (remove +/- at start);
                var value = refineParams[index].replace(/[+-]f\//, 'f/');
                if(value != ""){
                    for(var nextIndex = index+1; nextIndex < refineParams.length; nextIndex++){
                        if(refineParams[nextIndex].search(value) > -1){
                            refineParams[nextIndex] = "";
                            refineParams[index] = ""; // If found any such case remove the param.
                        }
                    }
                }
            }
            // Remove the empty parameters.
            _.remove(refineParams, function (entry) {
                return entry === "";
            });
        }
    }
};

