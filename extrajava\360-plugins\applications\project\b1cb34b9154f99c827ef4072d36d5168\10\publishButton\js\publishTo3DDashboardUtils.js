PublishTo3DDashboardUtils = ( function(){
    return {
		/**
         * render_custom_widget is callback method helps to render a custom Data table widget in Project App(Health_Assesment Page).
           It is called in PDFBuilder.js.
           The Widget is PLMA Data Table that has colors in different cells based on data i.e. 0 -  #57B847,1 - #fee000, 2 - #EA4F37
         * @params
         * template : PDF template Provided in file properties option
         * doc : PDF doc object
         * widgetData : widget Regarding Details
         * headerFooterData : Header and Footer Regarding Details
         * @return {Object} - Promise Object
         * */
        render_custom_widget: function (template,doc,widgetData,headerFooterData){
            let dfd = $.Deferred();
            let newPages = [];
            let tableContent = CreatePDFUtils.getTableContent(widgetData.$widget);
            if(tableContent.header.length == 0){
                dfd.reject(template);
                return dfd.promise();
            }
            doc.autoTable({
                margin: { top: template.currentY + 10, left: template.options.margin.left, right: template.options.margin.right },
                head: tableContent.header,
                body: tableContent.rows,
                styles: {fontSize: 9, fontStyle: "bold", halign: 'center', valign: 'middle', cellPadding: 1, lineWidth: 1, lineColor: [44, 62, 80]},
                columnStyles: {},
                horizontalPageBreak: true,
                theme: "striped",
                pageBreak: "auto",
                rowPageBreak: "auto",
                horizontalPageBreakRepeat: 0,
                didDrawPage: function (data) {
                    // Called after the plugin has finished drawing everything on a page.
                    // As the render header is promise, we cant resolve the dfd here, hence
                    // collect the pages and at once render the header/footer and widget header on page.
                    if(data.pageNumber != 1){
						// Skip first page of the table as it is already rendered with header/footer and widget header.
						newPages.push(doc.getCurrentPageInfo().pageNumber);
					}
                }.bind(template),
                didParseCell: function (data) {
                    if(data.section === 'body' && data.cell.raw == 0){
                        data.cell.styles.textColor = "#57B847";
                        data.cell.styles.fillColor = "#57B847";
                    }
                    else if(data.section === 'body' && data.cell.raw == 1) {
                        data.cell.styles.textColor = "#fee000";
                        data.cell.styles.fillColor = "#fee000";
                    }
                    else if(data.section === 'body' && data.cell.raw == 2) {
                        data.cell.styles.textColor = "#EA4F37";
                        data.cell.styles.fillColor = "#EA4F37";
                    }
                }
            });
            let params = {
                newPages:newPages,
                headerFooterData: headerFooterData,
                widgetData: widgetData,
                doc: doc,
                dfd:dfd
            };
            template.renderPageBreak(params);
            return dfd.promise();
        }        
    }

 })();