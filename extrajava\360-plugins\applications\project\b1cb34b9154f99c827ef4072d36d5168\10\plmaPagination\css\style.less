@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/styles/responsive.less";

.mashup {
	.pagination {
		text-align: center;
		clear: both;
		padding: 6px;
		//box-shadow: 6px -6px 10px -6px #b4b6ba;
		z-index: 50;
	
		.pagination-content{
			.display-flex();
			.align-items(center);
			height: 35px;

			ol.btn-group{
				.flex-grow(1);
				line-height: 30px;
				li {
					list-style: none;
					font-size: 14px;
					padding: 0px;
					display: inline-flex;
					min-width: auto;
					&.active{
						background-color: #d5e8f2;
					}
					&:hover{
						background-color: #e2e4e3;
					}
					a {
						padding: 5px 14px;
						i.fonticon{
							margin: 0px;
						}
						&.current {
							font-weight: bold;
						}
					}
				}
			}
		}
	}
}