@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/responsive.less";

.mashup .plmaHitDetails {
	//width: 100%;
	padding: 0;
	&.hidden {
		display: none;
	}
	height: 100%;
	.display-flex();
	.flex-flow(column nowrap);
	overflow-y: hidden;

	> .hitDetailsHeaderWithToolbar{
		border-bottom: 1px solid @cblock-border;
		z-index: 50;
		background-color: @cbody-bg;

		.hitHeader{
			.categoryIcon{
				margin-right: 5px;
			}
			.hitTitle{
				font-size: 18px;
				line-height: 18px;
			}
		}

		.widgetTitle{
			color: @clink;
			font-size: 18px;
			line-height: 18px;
		}

		> .headerToolbar{
			> .hitDetailsButtons{
				.hit-action{
					cursor: pointer;
				}
				> .closeHitDetailsButton{
					margin-right: 5px;
					&:hover {
						color: @clink-active;
					}
				}
			}
		}
	}

	.hitDetailsHeaderTitle{
		min-width: 0;
		.display-flex();
		.align-items(center);

		> .hitHeader{
			.display-flex();
			align-items: center;

			.fonticon {
				&:hover {
					color: @clink-active;
				}
			}

			.icon-container {
				.display-flex();
				justify-content: center;
				align-items: center;
				border-radius: 35px;
				width: 35px;
				height: 35px;
				margin-right: 5px;
				//position: relative;
				//top: 4px;
				background-color: #d5e8f2;
				.categoryIcon {
					width: 30px;
					font-size: 17px;
					color: @clink;
				}
			}

			> .hitTitle-container {
				.display-flex();
				flex-direction: column;
				//position: relative;
				//top: 10px;
				//height: 40px;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;

				.hitTitle{
					//font-size: 2em;
					//font-weight: bold;
					color: @clink;
					//position: relative;
					//top: 6px;
				}

				.hitSubTitleDescription {
					font-size: 12px;
					color: @ctext-weak;
					.display-flex();
				}
			}
		}
	}

	> .hits{
		overflow-y: auto;
		background: white;

		> .hitDetailsHeader{
			.display-flex();
			justify-content: space-between;

			line-height: (@line-height *2);
			background-color: @cblock-bg;
			padding: 10px 10px 20px 20px;

			.hitTitle-container {
				.hitTitle{
					font-weight: bold;
					font-size: 2em;
					//top: 6px;
				}
			}
		}

		> .hit {
			> .hitDetailsContent{
				font-size: @m-font;
				background-color: @cblock-bg;
				padding: @line-height (@line-height * 2) (@line-height * 2)  20px;
				white-space: pre-line;
				word-break: break-word;
				//width: 70%;
			}
		}
	}
	.container-detail {
		position: relative;
	}
	.noHitSelected{
		margin-top: 50px;
		font-size: @m-font;
		padding: @line-height;
		background: @cblock-bg;
		border-bottom: 1px solid @cblock-border;
		border-top: 1px solid @cblock-border;
	}
	
	.categoryIcon{
		font-size:@m-font;
		vertical-align: middle;
	}

	.action-btn {
		float: right;
		//font-size: @l-font;
		color: @ctext;
		//margin: (@line-height / 2);
		&:hover {
			text-decoration: none;
		}
	}

	.metaWrapper {
		.display-flex();
		.flex-flow(row wrap);
		background-color: @cblock-bg;
		border-bottom: 1px solid @cblock-border;
		margin-bottom: 10px;
		box-shadow: -3px 6px 10px -6px @ctext-weak;
		padding: 10px 20px 20px;
        gap: 20px;
		.imgDetailWrapper {
			display: inline-block;
			vertical-align: top;
			height: 300px;
		}
		
		.metaGroup {
			.flex(1 0 250px);

			> table {
				width: 100%;
				colgroup {
                    :nth-child(1) {
                        width: 50%;
                    }
                    :nth-child(2) {
                        width: 50%;
                    }
				}
			}
			
			@media (max-width: @screen-sm-max) {
				margin: 0 (@line-height / 2);
			}
		}

		.metaGroupTitle {
			color: @clink;
			font-size: 17px;
			font-weight: normal;
			display: block;
			margin-bottom: (@line-height / 2);
			border-bottom: 1px solid @clink;
			padding-bottom: (@line-height / 2);
			
			span.groupinfo {
				float: right;
				position:relative;
				font-size: 12px;
				.groupinfo-tooltip{
					font-family: entypo;
					display: none;
					position: absolute;
					top: 21px;
					right: 0;
					border: 1px solid #e2e4e3;
					border-radius: 5px;
					background-color: #FFFFFF;
					padding: 5px 10px;
					color: #77797c;
					width: 200px;
					z-index: 1;
					flex-direction: column;
					row-gap: 5px;

					&::before {
						content: "";
						width: 8px;
						height: 8px;
						transform: rotate(45deg);
						box-shadow: inset 1px 1px 0px 0px #e2e4e3;
						background: #fff;
						position: absolute;
						z-index: -1;
						right: 3.5px;
						top: -4px;
					  }

					.expr{
					    display: none;
					    color: #0087A3;
					}
					.group-condDescription{
					    font-style: italic;
                        display: inline-block;
					}

				}
				&:hover .groupinfo-tooltip{
					display: inline-flex;
				}
			}
		}

		.metaDetailWrapper {
			font-size: @m-font;
			line-height: (@line-height * 1.5);
			padding: (@line-height / 2) 0;
		}

		
		.metaLabel {
			color: @ctext-weak;
		}
		.metaValue {
			word-break: break-word;
			float: right;
		}
	}

	.facetWrapper {
		.facetLabel {
			display: block;
			font-size: 1.2em;
			text-decoration: underline;
			cursor: pointer;
		}
	}
	
	.analysisDate {
		text-align: right;
		font-style: italic;
		font-size: small;
		margin: 20px 10px 10px;
	}
}
