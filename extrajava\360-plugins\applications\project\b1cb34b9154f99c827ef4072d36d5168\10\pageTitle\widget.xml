<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Page title" group="PLM Analytics/Layout/Structure" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget displays the page title. You can add subwidgets to display additional buttons on the right side of the widget.</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/pageTitle/images/preview.PNG" alt="Page Title" />
        ]]>
	</Preview>
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>

	<Includes>
		<Include type="js" path="../chartboard/js/model.js" includeCondition="userConfig=true"/>
		<Include type="css" path="css/style.less" />
	</Includes>

	<SupportWidgetsId arity="ZERO_OR_MANY" label="Buttons"/>
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportI18N supported="true" />

	<OptionsGroup name="General">
		<Option id="userConfig" name="User configuration" arity="ONE">
			<Description>
				If set, the displayed title will be the value stored in the Chartboard feature, i.e. a value customized by the user.
				Otherwise, it will be the value specified in this widget.
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="title" name="Title" arity="ONE" isEvaluated="true">
			<Description>The title to display (if User configuration is checked, this will be the title default value).</Description>
		</Option>
		<Option id="height" name="Height" arity="ZERO_OR_ONE"
				isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
				isXmlEscaped="false">
			<Description>Specifies the height of the widget (pixels). You must enter an integer.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="iconsSize" name="Icons size" arity="ZERO_OR_ONE"
				isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
				isXmlEscaped="false">
			<Description>Specifies icons font size (in pixels). You	must enter an integer. Default 24.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="userConfig">true</DefaultValue>
	</DefaultValues>
</Widget>
