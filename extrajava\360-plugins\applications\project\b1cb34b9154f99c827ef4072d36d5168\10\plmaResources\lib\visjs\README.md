## Download latest Libraries from unpkg
- Download using npm command (if npm is not installed check https://docs.npmjs.com/downloading-and-installing-node-js-and-npm)
```
cd C:\temp
npm-install vis-timeline
```
- Libraries will be downloaded in C:\temp\node_modules\vis-timeline
- Copy following to the <WidgetCommonResourcesPath>\libs\visjs
```shell
cd <WidgetCommonResourcesPath>\libs\visjs
copy C:\temp\node_modules\vis-timeline\standalone\umd\vis-timeline-graph2d.min.js .
copy C:\temp\node_modules\vis-timeline\standalone\umd\vis-timeline-graph2d.min.js.map .
copy C:\temp\node_modules\vis-timeline\styles\vis-timeline-graph2d.min.css .
copy C:\temp\node_modules\vis-timeline\styles\vis-timeline-graph2d.min.css.map .
```
