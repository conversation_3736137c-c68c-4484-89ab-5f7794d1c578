<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="security" uri="http://www.exalead.com/jspapi/security" %>
<%@ taglib prefix="storage" uri="http://www.exalead.com/jspapi/storage" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>


<widget:widget extraCss="plmaChartAnnotation" varCssId="cssId" varUcssId="uCssId">

    <div class="annotation-popup-template hidden" data-css-id="${uCssId}">
        <div class="annotation-title"><i18n:message code="chart.annotation.title"/></div>
        <div class="annotation-input-container">
            <textarea name="textarea" rows="10" cols="30" id="annotation-input" class="form-control">
            </textarea>
        </div>
        <div class="annotation-author">
            <security:getUser var="user"/>
            <i class="fonticon fonticon-user-alt "></i>${user.login}
        </div>
        <div class="annotation-action">
            <button class="btn btn-default add"><i18n:message code="chart.annotation.add"/></button>
            <button class="btn btn-default close"><i18n:message code="chart.annotation.cancel"/></button>
        </div>
    </div>

</widget:widget>

<config:getOption name="enableFullScreen" var="enableFullScreen" />
<config:getOption name="storageKey" var="storageKey" />
<config:getOption name="waitForTrigger" var="waitForTrigger"/>
<config:getOption name="btnSelector" var="btnSelector" />
<config:getOption name="btnIcon" var="btnIcon" />
<config:getOption name="annotationScope" var="annotationScope" defaultValue="shared" />
<render:renderScript>
    var options = {};
    options.author = "${user.login}";
    options.storageKey = "${storageKey}";
    options.enableFullScreen = "${enableFullScreen}";
    options.waitForTrigger = "${waitForTrigger}";
    options.btnSelector = "${btnSelector}";
    options.btnIcon = "${btnIcon}";
    options.annotationScope = "${annotationScope}";
    new annotateChart(options);
</render:renderScript>
