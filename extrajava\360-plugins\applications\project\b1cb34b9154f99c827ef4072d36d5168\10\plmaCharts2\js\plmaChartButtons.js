var PlmaChartButton = function (uCssId, options, extraParameter) {
	this.uCssId = uCssId;
	this.widget = $('.' + this.uCssId);
	this.options = options;
    this.extraParameter = extraParameter;
};

PlmaChartButton.prototype.init = function () {
	this.buttonManager = new WidgetButtonManager(this.uCssId, 'menu', '.headerChartContainer');
	if (this.options.buttons.length > 0) {
		for (var i = 0 ; i< this.options.buttons.length ; i++) {
			var button = this.options.buttons[i];
			if (!this.buttonManager.hasButton(button.icon, button.label)) {
				var $button = this.buttonManager.addButton(button.icon, button.label, button.action, this.extraParameter, button.cssClass);
				if (button.icon.includes(FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS)) {
					this.widget.fullScreenWidget({
						button: $button
					});
				}
                if (button.onInit && (typeof(button.onInit) === "function")) {
                    button.onInit($button);
				}
			}
		}
	}
};


