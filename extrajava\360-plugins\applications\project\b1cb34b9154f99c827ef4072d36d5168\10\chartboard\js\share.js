var SharePage = function (chartboard) {
	this.chartboard = chartboard;

	this.init();
};

SharePage.prototype.init = function () {
	this.createPopup();
	this.getRights();
	this.initActions();
};

SharePage.prototype.createPopup = function () {
	var container = $('<div class="share-popup-container"></div>');
	container.append($('<div class="overlay visible"></div>'));

	var wrapper = $('<div class="share-popup-wrapper"></div>');
	wrapper.append($('<div class="title">' + Chartboard.getMessage('chartboard.share.title') + '</div>'));
	wrapper.append($('<div class="page-owner"></div>'));

	var shareOptions = $('<div class="share-popup-options"></div>');
	var writeRights = $('' +
		'<div class="share-line share-write" data-type="write">' +
		'<span class="label">' + Chartboard.getMessage('chartboard.share.write') + '</span>' +
		'<div class="users-container write-users-container">' +
		'<div class="users-input-container write-users-input-container">' +
		'<input class="share-input share-write" name="share-write-input" type="text"/>' +
		'<span class="add-button write-add-button fonticon fonticon-plus" title="' + Chartboard.getMessage('chartboard.share.add.tooltip') + '"></span>' +
		'</div>' +
		'<div class="user-list write-user-list"></div>' +
		'</div>' +
		'<div class="share-select share-select-all-button">' +
		'<input type="checkbox" class="share-checkbox-input" name="' + this.uCssId + '-share-write-checkbox" id="' + this.uCssId + '-share-write-checkbox"/>' +
		'<label class="share-checkbox-label" for="' + this.uCssId + '-share-write-checkbox">' + Chartboard.getMessage('chartboard.share.all') + '</label>' +
		'</div>' +
		'</div>');
	var readRights = $('' +
		'<div class="share-line share-read" data-type="read">' +
		'<span class="label">' + Chartboard.getMessage('chartboard.share.read') + '</span>' +
		'<div class="users-container read-users-container">' +
		'<div class="users-input-container read-users-input-container">' +
		'<input class="share-input share-read" name="share-read-input" type="text"/>' +
		'<span class="add-button read-add-button fonticon fonticon-plus" title="' + Chartboard.getMessage('chartboard.share.add.tooltip') + '"></span>' +
		'</div>' +
		'<div class="user-list read-user-list">' +
		'</div>' +
		'</div>' +
		'<div class="share-select share-select-all-button">' +
		'<input type="checkbox" class="share-checkbox-input" name="' + this.uCssId + '-share-read-checkbox" id="' + this.uCssId + '-share-read-checkbox"/>' +
		'<label class="share-checkbox-label" for="' + this.uCssId + '-share-read-checkbox">' + Chartboard.getMessage('chartboard.share.all') + '</label>' +
		'</div>' +
		'</div>');
	shareOptions.append(writeRights).append(readRights);

	var saveButton = $('<div class="btn btn-default share-save-button">' + Chartboard.getMessage('chartboard.share.apply') + '</div>');

	wrapper.append(shareOptions).append(saveButton);

	var closeButton = $('<span class="close-share-popup-button fonticon fonticon-cancel" title="' + Chartboard.getMessage('chartboard.share.cancel.tooltip') + '"></span>');
	wrapper.append(closeButton);

	container.append(wrapper);

	this.chartboard.widget.find('> .widgetContent').append(container);

	this.chartboard.widget.find('.widgetContent .share-popup-container .overlay').on('click', $.proxy(function () {
		this.hidePopup();
	}, this));

	closeButton.on('click', $.proxy(function () {
		this.hidePopup();
	}, this));

	saveButton.on('click', $.proxy(function () {
		this.saveShare();
	}, this));
};

SharePage.prototype.getRights = function () {
	/* The first writer is the creator of the page, he cannot be deleted */
	if (this.chartboard.options.userWriters && this.chartboard.options.userWriters.length > 1) {
		for (var i = 1; i < this.chartboard.options.userWriters.length; i++) {
			if (this.chartboard.options.userWriters[i].trim() !== '' && this.chartboard.options.userWriters[i] !== 'ALL') {
				this.addUser(this.chartboard.options.userWriters[i].trim(), 'write', true);
			}
			if (this.chartboard.options.userWriters[i] === 'ALL') {
				this.chartboard.widget.find('.widgetContent .share-popup-container .share-write .share-checkbox-input').prop('checked', true);
			}
		}
	}
	if (this.chartboard.options.groupWriters) {
		for (var i = 0; i < this.chartboard.options.groupWriters.length; i++) {
			if (this.chartboard.options.groupWriters[i].trim() !== '') {
				this.addUser(this.chartboard.options.groupWriters[i].trim(), 'write', false);
			}
		}
	}

	if (this.chartboard.options.userReaders) {
		for (var j = 0; j < this.chartboard.options.userReaders.length; j++) {
			if (this.chartboard.options.userReaders[j].trim() !== '' && this.chartboard.options.userReaders[j] !== 'ALL') {
				this.addUser(this.chartboard.options.userReaders[j].trim(), 'read', true);
			}
			if (this.chartboard.options.userReaders[j] === 'ALL') {
				this.chartboard.widget.find('.widgetContent .share-popup-container .share-read .share-checkbox-input').prop('checked', true);
			}
		}
	}
	if (this.chartboard.options.groupReaders) {
		for (var j = 0; j < this.chartboard.options.groupReaders.length; j++) {
			if (this.chartboard.options.groupReaders[j].trim() !== '') {
				this.addUser(this.chartboard.options.groupReaders[j].trim(), 'read', false);
			}
		}
	}

	this.chartboard.widget.find('.page-owner').text(Chartboard.getMessage('chartboard.share.page.owner') + ': ' + this.chartboard.options.userWriters[0]);
};

SharePage.prototype.initActions = function () {
	/* On click on add button, add a new user */
	this.chartboard.widget.find('.share-popup-container .share-line .add-button').on('click', $.proxy(function (e) {
		var type = $(e.currentTarget).closest('.share-line').data('type');
		var user = this.chartboard.widget.find('.share-popup-container .share-' + type + ' .share-input').val().trim();
		if (user !== '') {
			this.addUser(user, type, true);
			this.chartboard.widget.find('.share-popup-container .share-' + type + ' .share-input').val('');
			this.chartboard.widget.find('.share-popup-container .share-' + type + ' .users-input-container .share-suggest-container').remove();
			/* Add message that we add the text as user */
			this.chartboard.widget.find('.share-popup-container .share-popup-options .share-info').remove();
			this.chartboard.widget.find('.share-popup-container .share-popup-options').prepend(
				$('<div class="share-info">' + Chartboard.getMessage('chartboard.share.added.user') + '</div>')
			);
		}
	}, this));
	this.chartboard.widget.find('.share-popup-container .share-line .share-input').on('keypress', $.proxy(function (e) {
		if (e.which === 13) {
			var type = $(e.currentTarget).closest('.share-line').data('type');
			var user = this.chartboard.widget.find('.share-popup-container .share-' + type + ' .share-input').val().trim();
			if (user !== '') {
				this.addUser(user, type, true);
				this.chartboard.widget.find('.share-popup-container .share-' + type + ' .share-input').val('');
				this.chartboard.widget.find('.share-popup-container .share-' + type + ' .users-input-container .share-suggest-container').remove();
				/* Add message that we add the text as user */
				this.chartboard.widget.find('.share-popup-container .share-popup-options .share-info').remove();
				this.chartboard.widget.find('.share-popup-container .share-popup-options').prepend(
					$('<div class="share-info">' + Chartboard.getMessage('chartboard.share.added.user') + '</div>')
				);
			}
		}
	}, this));


	this.chartboard.widget.find('.share-popup-container .share-line .share-input').on('keyup', $.proxy(function (e) {

		if (window.dashboardController) {
			var search = $(e.currentTarget).val().trim();

			/* Get user suggest from 3dxp */
			window.dashboardController.getUsers(search,
				$.proxy(function (data, urlPicture) {
					/* Display Suggest result */
					var type = $(e.currentTarget).closest('.share-line').data('type');
					this.userSuggest = {};
					this.userSuggest.data = data;
					this.userSuggest.urlPicture = urlPicture;
					this.updateSuggests(type);
				}, this)
			);

            /* Get group suggest from 3dxp */
            window.dashboardController.getUserDetails('current', function(userInfo){
                var type = $(e.currentTarget).closest('.share-line').data('type');
                this.groupSuggest = {};
                this.groupSuggest.suggestions = userInfo.getCollabSpaces(search);
                this.updateSuggests(type);
            }.bind(this));
		}
	}, this));

	this.chartboard.widget.find('.share-popup-container').on('click', $.proxy(function (e) {
		if (!$(e.currentTarget).hasClass('share-write')) {
			this.chartboard.widget.find('.share-popup-container .share-write .users-input-container .share-suggest-container').remove();
		}
		if (!$(e.currentTarget).hasClass('share-read')) {
			this.chartboard.widget.find('.share-popup-container .share-read .users-input-container .share-suggest-container').remove();
		}
	}, this));
};

SharePage.prototype.updateSuggests = function (type) {
	this.chartboard.widget.find('.share-popup-container .share-' + type + ' .users-input-container .share-suggest-container').remove();

	var suggest = $('<div class="share-suggest-container share-suggest-"' + type + '"></div>');

	if (this.userSuggest && this.userSuggest.data) {
		for (var i = 0; i < this.userSuggest.data.length; i++) {
			/* if urlPicture, then display picture */
			var user = this.userSuggest.data[i];
			if (this.userSuggest.urlPicture && this.userSuggest.urlPicture !== '') {
				suggest.append($('<div class="share-suggest-user" data-name="' + user.name + '"><img class="img-user-share" src="' + this.userSuggest.urlPicture + user.name + '/width/20/height/20" width="20" height="20"/>' + user.lastname + ' ' + user.firstname + ' - ' + user.name + '</div>'));
			} else {
				suggest.append($('<div class="share-suggest-user" data-name="' + user.name + '"><span class="icon-user fonticon fonticon-user"></span>' + user.lastname + ' ' + user.firstname + ' - ' + user.name + '</div>'));
			}
		}
	}

	if (this.groupSuggest && this.groupSuggest.suggestions) {
		for (var i = 0; i < this.groupSuggest.suggestions.length; i++) {
			var suggestion = this.groupSuggest.suggestions[i];
			suggest.append($('<div class="share-suggest-user share-suggest-group" data-name="' + suggestion + '"><span class="icon-group fonticon fonticon-users"></span>' + suggestion + '</div>'));
		}
	}

	suggest.find('.share-suggest-user').on('click', $.proxy(function (e) {
		this.addUser($(e.currentTarget).data('name'), type, !$(e.currentTarget).hasClass('share-suggest-group'));
		this.chartboard.widget.find('.share-popup-container .share-' + type + ' .share-input').val('');
		this.chartboard.widget.find('.share-popup-container .share-' + type + ' .users-input-container .share-suggest-container').remove();
	}, this));

	if ((this.userSuggest && this.userSuggest.data && this.userSuggest.data.length > 0) || (this.groupSuggest && this.groupSuggest.suggestions && this.groupSuggest.suggestions.length > 0)) {
		this.chartboard.widget.find('.share-popup-container .share-' + type + '  .users-input-container').append(suggest);
	}
};

SharePage.prototype.addUser = function (user, type, isUser) {
	var userContainer = $('' +
		'<div class="user">' +
		'<span class="share-icon fonticon"></span>' +
		'<span class="user-name">' + user.trim() + '</span>' +
		'<span class="remove-user fonticon fonticon-cancel" title="' + Chartboard.getMessage('chartboard.share.remove.tooltip') + '"></span>' +
		'</div>');

	if (isUser) {
		userContainer.addClass('share-user');
		userContainer.find('.share-icon').addClass('fonticon-user').prop('title', Chartboard.getMessage('chartboard.share.change.group'));
	} else {
		userContainer.addClass('share-group');
		userContainer.find('.share-icon').addClass('fonticon-users').prop('title', Chartboard.getMessage('chartboard.share.change.user'));
	}

	userContainer.on('click', $.proxy(function (e) {
		$(e.currentTarget).remove();
		this.chartboard.widget.find('.share-popup-container .share-popup-options .share-info').remove();
	}, this));

	userContainer.find('.share-icon').on('click', $.proxy(function (e) {
		var $e = $(e.currentTarget);
		$e.toggleClass('fonticon-user').toggleClass('fonticon-users');
		if ($e.hasClass('fonticon-user')) {
			$e.prop('title', Chartboard.getMessage('chartboard.share.change.group'));
		} else {
			$e.prop('title', Chartboard.getMessage('chartboard.share.change.user'));
		}
		$e.closest('.user').toggleClass('share-user').toggleClass('share-group');
		this.chartboard.widget.find('.share-popup-container .share-popup-options .share-info').remove();
		e.stopImmediatePropagation();
	}, this));

	this.chartboard.widget.find('.share-popup-container .share-popup-options .share-info').remove();
	this.chartboard.widget.find('.share-popup-container .share-line.share-' + type + ' .users-container .user-list').append(userContainer);
};

SharePage.prototype.displayPopup = function (readMode) {
	/* If the user is only in read mode, disable the inputs */
	if (readMode) {
		this.chartboard.widget.find('.share-popup-container input').prop('disabled', true);
		this.chartboard.widget.find('.users-input-container').addClass('hidden');
		this.chartboard.widget.find('.write-user-list .user-creator').remove();
		// this.chartboard.widget.find('.write-user-list').prepend($('<div class="user user-creator"><span class="user-name">' + this.chartboard.page.writers[0] + '</span></div>'));
		this.chartboard.widget.find('.user-list .remove-user').remove();
		this.chartboard.widget.find('.user-list .share-icon').off();
		this.chartboard.widget.find('.user-list .user').off();
		this.chartboard.widget.find('.share-save-button').remove();
	}
	this.chartboard.widget.find('.share-popup-container').addClass('active');
};

SharePage.prototype.hidePopup = function () {
	this.chartboard.widget.find('.share-popup-container').removeClass('active');
};

SharePage.prototype.saveShare = function () {
	if (this.chartboard.page) {
		var creator = this.chartboard.page.writers[0];

		this.chartboard.page.writers = [];
		this.chartboard.page.groupsWriters = [];
		var writers = this.chartboard.widget.find('.widgetContent .share-popup-container .share-write .user-list .user');
		for (var i = 0; i < writers.length; i++) {
			if ($(writers[i]).hasClass('share-user')) {
				this.chartboard.page.writers.push($(writers[i]).find('.user-name').text());
			} else {
				this.chartboard.page.groupsWriters.push($(writers[i]).find('.user-name').text());
			}
		}
		this.chartboard.page.writers.splice(0, 0, creator);
		if (this.chartboard.widget.find('.widgetContent .share-popup-container .share-write .share-checkbox-input').prop('checked')) {
			this.chartboard.page.writers.push('ALL');
		}

		this.chartboard.page.readers = [];
		this.chartboard.page.groupsReaders = [];
		var readers = this.chartboard.widget.find('.widgetContent .share-popup-container .share-read .user-list .user');
		for (var j = 0; j < readers.length; j++) {
			if ($(readers[j]).hasClass('share-user')) {
				this.chartboard.page.readers.push($(readers[j]).find('.user-name').text());
			} else {
				this.chartboard.page.groupsReaders.push($(readers[j]).find('.user-name').text());
			}
		}
		if (this.chartboard.widget.find('.widgetContent .share-popup-container .share-read .share-checkbox-input').prop('checked')) {
			this.chartboard.page.readers.push('ALL');
		}

		this.chartboard.savePage($.proxy(function () {
			this.hidePopup();
		}, this), this.chartboard.page.layout ? this.chartboard.page.layout : "undefined");
	}
};