<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>

<render:import varWidget="widget" />

<config:getOption name="title" var="title" defaultValue="" doEval="true"/>
<config:getOptionsComposite name="tabs" var="tabs" mapIndex="true"/>
<config:getOption name="height" var="height"/>
<config:getOption name="activateAsynch" var="activateAsynch"/>

<config:getOption var="minwidth" name="minwidth" />
<c:if test="${minwidth != null}"><c:set var="minwidth" value="min-width:${minwidth}px;" /></c:if>

<widget:widget extraCss="hideShowTabs" varUcssId="uCssId" disableStyles="true" extraStyles="${minwidth}">
	<c:if test="${not empty title}">
		<widget:header extraCss="sectionTitle">
			${title}
		</widget:header>
	</c:if>
	
	<c:set var="defaultTab" value="-1" />
	<c:set var="defaultTabClass" value="" />
	
	<widget:content extraCss="tabs">
		<c:forEach items="${tabs}" var="tab" varStatus="loop">
			<%-- Check active tab based on condition --%>
			<c:set var="isActiveTab" value="${string:trim(tab.activationCondition) == 'true'}"/>
			<%-- The first one that validates the condition is active --%>
			<c:if test="${isActiveTab && defaultTab == -1}">
				<c:set var="defaultTab" value="${loop.index}"/>
				<c:set var="defaultTabClass" value="${tab.show}" />
			</c:if>

			<%-- Render tabs --%>
			<div class="tab tab_${loop.index} ${tab.show}">
				<span class="tabIcon ${tab.iconCSS}"></span>
				<span class="tabLabel">${tab.label}</span>
			</div>
		</c:forEach>

		<%-- Otherwise, use default active tab --%>
		<c:if test="${defaultTab == -1}">
			<c:forEach items="${tabs}" var="tab" varStatus="loop">
				<%-- The first one that is active by default is selected --%>
				<c:if test="${tab.defaultActivation == 'true' && defaultTab == -1}">
					<c:set var="defaultTab" value="${loop.index}"/>
					<c:set var="defaultTabClass" value="${tab.show}" />
				</c:if>
			</c:forEach>
		</c:if>

		<config:getOption name="showPopup" var="showPopup" defaultValue="false" />
		<c:if test="${showPopup}">
			<config:getOption name="popup" var="popup" />
			<render:template template="../plmaResources/template/tooltip/tooltip.jsp">
				<render:parameter name="content" value="${popup}" />
				<render:parameter name="direction" value="top" />
				<render:parameter name="buttonId" value="overview-tabs-tooltip-${uCssId}" />
				<render:parameter name="extraCss" value="overview-tabs-tooltip" />
			</render:template>
		</c:if>
	</widget:content>
	
	<widget:content extraCss="content ${not empty height?'fixedHeight':''}" extraStyles="${not empty height?'height:':''}${height}${not empty height?'px':''}">
		<div class="content-wrapper">
			<c:choose>
				<c:when test="${activateAsynch == 'true'}">
					<widget:forEachSubWidget>
						<widget:getUcssId var="wuidSecondWidget"/>
						<config:getOption name="cssClass" var="cssClass"/>
						<c:choose>
							<c:when test="${fn:contains(cssClass,defaultTabClass)}">
								<render:widget/>
							</c:when>
							<c:otherwise>
								<div class="subWidgetHidden wuid ${wuidSecondWidget} ${cssClass}"></div>
							</c:otherwise>
						</c:choose>
					</widget:forEachSubWidget>
				</c:when>
				<c:otherwise>
					<render:subWidgets/>
				</c:otherwise>
			</c:choose>
		</div>
	</widget:content>

	<config:getOption name="secondWidgetCallback" var="secondWidgetCallback" defaultValue="function(){}" />
	<config:getOption name="tabClickCallback" var="tabClickCallback" defaultValue="function(){}" />
	<render:renderScript position="READY">
		<c:set var="selectors" value=""/>
		
		<c:forEach items="${tabs}" var="tab" varStatus="loop">
			<c:set var="tabSelectors" value=""/>
			<c:set var="first" value="true"/>
			<c:forEach items="${tab.show}" var="cssClass">
				<c:if test="${not first}">
					<c:set var="tabSelectors" value="${tabSelectors}, "/>
				</c:if>
				<c:set var="tabSelectors" value="${tabSelectors}.${fn:trim(cssClass)}"/>
				<c:set var="first" value="false"/>
			</c:forEach>
			
			<c:set var="selectors" value="${selectors}'${tabSelectors}', "/>
			
		</c:forEach>
	
		new HideShowTabs('${uCssId}', {
			showSelectors: [${selectors}],
			defaultTab: ${defaultTab},
			activateAsynch: ${activateAsynch == 'true'},
			secondWidgetCallback : ${secondWidgetCallback},
			tabClickCallback : ${tabClickCallback}
		});
	
	</render:renderScript>
	
</widget:widget>
