var SaveState = function(options) {
    this.options = options;
    this.storage = new StorageClient('user');
    this.activated = false;

    this._init();
};

SaveState.SAVE_STATE_STORAGE_KEY = 'preference.save_state';

SaveState.prototype._init = function() {
    this.storage.get(SaveState.SAVE_STATE_STORAGE_KEY, $.proxy(function(res) {
        this.activated = res.length > 0 ? JSON.parse(res[0].value) : false;
        this._save();
	}, this));
};

SaveState.prototype._save = function() {
    if (this.activated && window.dashboardController) {
        window.dashboardController.setPreference('url', PinnedPages.utils.getCleanUrl(window.location.href, this.options.whitelist));
    }
};

SaveState.prototype.activate = function(activate) {
    this.storage.set(SaveState.SAVE_STATE_STORAGE_KEY, JSON.stringify(activate), $.proxy(function() {
        this.activated = activate;
        this._save();
    }, this));
};