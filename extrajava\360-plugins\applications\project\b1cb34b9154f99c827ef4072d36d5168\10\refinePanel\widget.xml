<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Refine panel" group="PLM Analytics/Facets" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>This widget displays a panel containing refinements that can be added as filters to the page.</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/refinePanel/images/preview.PNG" alt="Refine Panel" />
        ]]>
	</Preview>
	<Dependencies>
		<Widget name="formForm" /> <!-- include all exa/ and searchAction.js -->
		<Widget name="plmaResources" />
	</Dependencies>
	<Includes>
    	<Include type="css" path="css/style.less" />
		<Include type="js" path="../plmaResources/js/i18nClient.js" />
    	<Include type="js" path="js/refinePanel.js" />
		<Include type="js" path="js/facetRefine.js" />
		<Include type="js" path="js/dateFacetRefine.js" />
		<Include type="js" path="js/numericalFacetRefine.js" />
		<Include type="js" path="js/refinePanelSearch.js" />
		<Include type="js" path="js/refinePanelSearchAccess.js" />
		<Include type="js" path="js/refineEventHandler.js" />
		<Include type="js" path="/resources/javascript/jquery.cookie.js" />
		<Include type="js" path="/resources/highcharts/js/highcharts.src.js" />
		<Include type="js" path="../plmaResources/js/moment-with-locales.min.js" />
		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js" />
		<Include type="js" path="../plmaResources/js/plmaSpinner.js" />
		<Include type="js" path="../plmaResources/js/daterangepicker.js" />
		<Include type="js" path="../plmaResources/js/polyfill-classList.js" />
		<Include type="js" path="../plmaResources/js/jquery.plmaSuggest.js" />
		<Include type="js" path="../plmaResources/lib/notify/notify.js" />
		<Include type="js" path="../plmaResources/js/refineCounter.js" />
		<Include type="js" path="../plmaResources/js/widgetButtonManager.js"/>
		<!-- Used to reload charts on 'plma:resize' event -->
		<Include type="js" path="../plmaResources/js/plmaHighcharts.js"/>
    </Includes>
	<SupportWidgetsId arity="ZERO_OR_MANY" />
	<SupportFeedsId arity="MANY" consumeFeed="false"/>
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>refinepanel.facets.empty</JsKey>
			<JsKey>refinepanel.facets.error</JsKey>
			<JsKey>rangeselector.from</JsKey>
			<JsKey>rangeselector.to</JsKey>
			<JsKey>rangeselector.calendar.select.from</JsKey>
			<JsKey>rangeselector.calendar.select.to</JsKey>
			<JsKey>refinepanel.toolbar.label</JsKey>
			<JsKey>refinepanel.toolbar.item.cancel.tooltip</JsKey>
			<JsKey>refinepanel.toolbar.item.expandall.tooltip</JsKey>
			<JsKey>refinepanel.toolbar.item.collapseall.tooltip</JsKey>
			<JsKey>refinepanel.toolbar.item.cancel.label</JsKey>
			<JsKey>refinepanel.toolbar.item.expandall.label</JsKey>
			<JsKey>refinepanel.toolbar.item.collapseall.label</JsKey>
			<JsKey>refinepanel.toolbar.item.fonticon.collapseall</JsKey>
			<JsKey>refinepanel.toolbar.item.fonticon.expandall</JsKey>
		</JsKeys>
	</SupportI18N>
	<OptionsGroup name="General">
		<Option id="displayCloseBtn" name="Display close button">
            <Description>Whether close button (cross icon) can be displayed. Defaults to true.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>
		<Option id="refineMode" name="Refine panel mode">
			<Description>Choose the refine panel default behavior mode. 'Default' : filter are added inside url.
				'Event' : on click an event "refinePanel:refine" is triggered with the refined url. Widgets have to deal with this
				event and apply by their own the refined url.
			</Description>
			<Values>
				<Value>Default</Value>
				<Value>Event</Value>
			</Values>
		</Option>
		<Option id="collapseFacet" name="Collapse facet" arity="ONE">
			<Description>Displays the facet as collapsed. Defaults to false.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Search">
		<Description>Search in facets categories.&lt;br/&gt;
			This options block allows to filter displayed categories based on search input field, it is possible to use
			only suggest and filter categories on UI side (in 'service' or 'dispatcher' action mode), which is not recommended; or use
			'access' mode where facets are filtered on backend side.&lt;br/&gt;
			In 'access' mode, it is also possible to use a request dispatcher suggest service before calling feed (this
			configuration is optional) in order to calculate only facets matching search for at least one category.&lt;br/&gt;
			In 'access' mode, if suggest dispatcher option is not empty, only categories matching query in suggests will
			be displayed and I18N values will not be taken into account (not present in suggests).&lt;br/&gt;
			Only 'access' mode support correct display of hierarchical facets where a leaf matches query (parent categories display).&lt;br/&gt;
			'access' mode requires configuration of facets management triggers (UI and Access) in order to take into account parameters
			sent by refine panel (https://docs.factory.exalead.com/plma-docs/plma-tools/Facets-management-triggers/)&lt;br/&gt;
			PS: if suggest dispatcher or service is configured, it requires to build suggests regularly, it has a cost...
		</Description>
		<Option id="placeHolder" name="Placeholder" isEvaluated="true">
			<Description>Text displayed in the text input field when it is empty.</Description>
		</Option>
		<Option id="enableSuggest" name="Enable suggest" arity="ONE">
			<Description>Enables suggest service on the search field. You must then select a suggest service - previously defined in the Administration Console - from 'Suggest Name'.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('false',['suggestNames', 'suggestApiAction', 'suggestApiConfig', 'suggestCustomSearchAPIs', 'suggestApiCommand', 'minLength', 'maxSuggests'],[], true, false)</Display>
			</Functions>
		</Option>
		<Option id="minLength" name="Minimum search input length" arity="ZERO_OR_ONE">
			<Description>Minimum input length to trigger suggest search (default 3).</Description>
			<Functions>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="maxSuggests" name="Maximum suggest values" arity="ZERO_OR_ONE">
			<Description>Maximum number of suggests values (in 'access' mode, default 100).</Description>
			<Functions>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="suggestApiAction" name="Action" arity="ONE">
			<Description>Specifies whether suggest should use a simple service, suggest dispatcher or simply access trigger filtering (requires facets management trigger in this case) which varies depending on the query input. </Description>
			<Values>
				<Value>access</Value>
				<Value>dispatcher</Value>
				<Value>service</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:'access',hideOptions:['suggestNames', 'customDelimiter'], showOptions:['dispatcherName','delayTime']},
					{valueToMatch:['dispatcher', 'service'], hideOptions:['dispatcherName'], showOptions:['suggestNames', 'customDelimiter']})</Display>
			</Functions>
		</Option>
		<Option id="suggestNames" name="Suggest name(s)" arity="MANY">
			<Description>Suggest service name.</Description>
			<Functions>
				<ContextMenu>SuggestNames('suggestApiAction')</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="dispatcherName" name="Dispatcher name" arity="ZERO_OR_ONE">
			<Description>Dispatcher name used to pre-filter calculated facets. This parameter is not mandatory and can be empty,
				in this case, suggest service is not called and feed is executed without any facet disabled and categories
				are filtered based on query, in some cases, it can be more efficient to call suggest webservice to get matching
				facets and then execute feed with only these facets.</Description>
			<Functions>
				<ContextMenu>SuggestNames('suggestApiAction')</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="delayTime" name="Delay time">
			<Description>Mention the delay time for the suggest service to be called.</Description>
			<Placeholder>700</Placeholder>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="suggestApiConfig" name="Config" arity="ZERO_OR_ONE">
			<Description>
				<![CDATA[
					Indicates the name of the default Search API, for example, <code>sapi0</code>.
				]]>
			</Description>
			<Placeholder>sapi0</Placeholder>
			<Functions>
				<ContextMenu>ApiConfig()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				<Check>isSpecified('ONE', 'suggestCustomSearchAPIs')</Check>
			</Functions>
		</Option>
		<Option id="suggestCustomSearchAPIs" name="Search API URL" arity="ZERO_OR_MANY">
			<Description>Defines the URL that will be used by the Search API.</Description>
			<Placeholder>http://HOST:PORT/</Placeholder>
			<Functions>
				<Check>isSpecified('ONE', 'suggestApiConfig')</Check>
			</Functions>
		</Option>
		<Option id="suggestApiCommand" name="API command" arity="ONE">
			<Description>Specifies the suggest API command name that will be appended to the 'Search API URL'.</Description>
			<Placeholder>suggest</Placeholder>
			<Functions>
				<ContextMenu>ApiCommand()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="customDelimiter" name="Delimiter" arity="ZERO_OR_ONE">
			<Description>Specifies custom delimiter for 'catsearchrefine' parameter, default to comma.</Description>
			<Placeholder>,</Placeholder>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Facet List">
		<Description>
		<![CDATA[
			If using 'External Config', you can achieve 
			<ul>
				<li>For each facet configured, you can specify Display Condition. Note you can use '${pageName}'</li>
				<li>Ordering of facet of any kind date1,facet1,numericFacet1,facet2 etc.</li>
				<li>For individual facet you can define [ 'Aggregation', 'isDisjunctive', 'displayExclude', 'sortMode', 'iterMode', 'nbSubFacets', 'drillDown', 'defaultView', 'enableSort']</li>
				<li><i>Example: &lt;refineBy id="type" order="1" type="facet" isDisjunctive="false" sortMode="count-asc" nbSubFacets="10" iterMode="flat" drillDown="true" displayExclude="false" defaultView:"pie" enableSort:"true"&gt;</i></li>
			</ul>
			<br/>
			<b>NOTE:</b>
			<ul>
				<li><i><b>1) enableSort is 'true' following trigger is need to add</b></i></li>
				<li>-'Set facet sort from config' trigger for that particular feed <i><b>[enableSort won't work for facet if sort is configured in feed]</b></i></li>
			</ul>
			<ul>
				<li><i><b>2) defaultView is 'pie' and to overide LimitFacetCategoryTrigger following trigger is need to add</b></i></li>
				<li>-'Cookie to parameter' trigger in Design, having fields parameter and cookie name set as 'refine-panel-facet-option'.</li>
			</ul>
			<i><b>3) If LimitFacetCategoryTrigger is used, sort in searchServer and refinePanel widget 'sortMode' is different then it leads to conflict in the result.</b></i>
		]]>
		</Description>
		<Option id="useExternalisedConfig" name="Use external config" arity="ONE">
			<Description>Externalized Configurations exposes some advanced functionalities such as Facet ordering etc.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({
					valueToMatch:'true', 
					showOptions:['externalConfig'], 
					hideOptions:['sortModeFacets', 'facetsListMode', 'facetsList', 'values', 'dateFacetsList', 'numericalFacets', 'isDisjunctive', 'displayExclude', 'sortMode', 'iterMode', 'nbSubFacets', 'drillDown']})</Display>
			</Functions>
		</Option>
		<Option id="sortModeFacets" name="Sort facets by">
			<Description>
				Sorts the facets using the selected method. Selecting anything but 'default' impacts performance.
			</Description>
			<Values>
				<Value>default</Value>
				<Value>description-asc</Value>
				<Value>description-desc</Value>
				<Value>shuffle</Value>
			</Values>
		</Option>
		<Option id="facetsListMode" name="Facet root list mode" arity="ONE">
			<Description>
				<![CDATA[
					Specifies the facet list filtering mode.<br />
					For the 'Exclude' and 'Include' modes, the 'Facet list' field allows to specify the metas to exclude or include.<br />
					Leaving 'Facet list' empty forces iteration over all facets whatever the chosen mode.
				]]>
			</Description>
			<Values>
				<Value>No filtering</Value>
				<Value>Include</Value>
				<Value>Exclude</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_dependsOn('No filtering', ['facetsList'], false)</Display>
			</Functions>
		</Option>
		<Option id="facetsList" name="Facets list by default" isEvaluated="true">
			<Description>
				<![CDATA[
				  The specified facets, separated by commas, will be either the only ones displayed ('Include' mode) or the only ones hidden ('Exclude' mode).<br />
				  The facet order in this field corresponds to the facet order in the UI display.<br />
				  Leaving the facet list empty iterates over all facets.
				]]>
			</Description>
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
				<ContextMenu>COMMON_appendOnChange(', ')</ContextMenu>
			</Functions>
		</Option>
		<Option id="values" name="Aggregation" arity="ZERO_OR_ONE">
			<Description>Specifies the type of aggregation that is displayed next to each category name.</Description>
			<Functions>
				<ContextMenu>Aggregations()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="dateFacetsList" name="Date filters by default" isEvaluated="true">
			<Description>
				<![CDATA[
				  The specified date filters, separated by commas, will be the ones displayed, in the corresponding order.<br />
				  For the date filter to work, you should add the trigger ParameterBasedQueryRewriter to the corresponding feeds.<br />
				  Date filters are actually appended to the query (they do not use the facet mechanism), so you should use <b>prefix handlers</b> here.
				]]>
			</Description>
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
				<ContextMenu>COMMON_appendOnChange(', ')</ContextMenu>
			</Functions>
		</Option>
		<OptionComposite id="numericalFacets" name="Numerical Facets list" arity="ZERO_OR_MANY">
			<Description>Adds numerical facets to the widget.</Description>
			<Option id="name" name="Facet Name" isEvaluated="true" arity="ONE">
				<Description>The class attribute of the icon displayed next to the label.</Description>
				<Functions>
					<ContextMenu>Facets()</ContextMenu>
				</Functions>
			</Option>
			<Option id="min" name="Minimum" isEvaluated="true" arity="ONE">
				<Description>Minimum displayed for the slider.</Description>
			</Option>
			<Option id="max" name="Maximum" isEvaluated="true" arity="ONE">
				<Description>Maximum displayed for the slider.</Description>
			</Option>
			<Option id="step" name="Step" isEvaluated="true" arity="ONE">
				<Description>Step available for the slider.</Description>
			</Option>
		</OptionComposite>
		<Option id="isDisjunctive" name="Disjunctive facets">
			<Description>Displays a check box next to each category.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="displayExclude" name="Display exclude">
			<Description>Displays an exclude link next to each category that allows the user to exclude categories from their refinement.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="sortMode" name="Sort categories by" arity="ONE">
			<Description>
				<![CDATA[
					Sorts the categories using the selected method. Selecting anything but 'default' impacts performance.<br />
					<ul>
						<li>'default' requires no processing.</li>
						<li>'shuffle', 'desc*', 'score*', 'count*' and 'range*' impact performance on the client side.</li>
						<li>If LimitFacetCategoryTrigger is used, Sorting in the SearchServer and refinePanel widget is different then it leads to Conflict in the result.</li>
					</ul>
				]]>
			</Description>
			<Values>
				<Value>default</Value>
				<Value>description-asc</Value>
				<Value>description-desc</Value>
				<Value>score-asc</Value>
				<Value>score-desc</Value>
				<Value>count-asc</Value>
				<Value>count-desc</Value>
				<Value>range-asc</Value>
				<Value>range-desc</Value>
				<Value>shuffle</Value>
			</Values>
		</Option>
		<Option id="iterMode" name="Iteration mode" arity="ONE">
			<Description>
				<![CDATA[
					Determines what data is displayed:
					<ul>
						<li>'all' visits the first child of each node before processing its siblings,</li>
						<li>'leaves' iterates recursively, returning the end nodes,</li>
						<li>'flat' returns only the first level.</li>
					</ul>
				]]>
			</Description>
			<Functions>
				<Display>PARAMETER_dependsOn('flat', ['drillDown'], true)</Display>
			</Functions>
			<Values>
				<Value>all</Value>
				<Value>flat</Value>
				<Value>leaves</Value>
			</Values>
		</Option>
		<Option id="nbSubFacets" name="Max number of categories">
			<Description>Specifies the maximum number of categories to display, no value means "no limit". Defaults to "1000".</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="drillDown" name="Drill down">
			<Description>The drill-down option only works if the iteration mode is set to 'flat'. Otherwise, it makes no sense.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<OptionComposite id="externalConfig" name="External Config" isEvaluated="false" arity="ONE">
			<Option id="refinesPanelId" name="RefinesPanel Id" isEvaluated="true" arity="ONE">
				<Description>RefinesPanel Configurations ID to use. You can provide MEL expression as well if you need conditional display.</Description>
			</Option>
			<Option id="appName" name="Application Name" isEvaluated="true" arity="ONE">
				<Description>Application name, if not provided then current mashup application name will be choosen. This is the name defined in Applications Config.</Description>
			</Option>
			<Option id="configName" name="Configuration Name" isEvaluated="true" arity="ONE">
				<Description>Configuration name.</Description>
			</Option>
		</OptionComposite>
	</OptionsGroup>
	<OptionsGroup name="Facet Options">
		<Description><![CDATA[
		Note if you choose <b>IterationMode other than 'all'</b> then the page count, as well as categories shown may not match expectation, as iteration happens on client side, but search server evaluates all the categories. <b>In such cases better to exclude such facets from Limit Facet Categories trigger.</b>
		]]></Description>
		<Option id="showEmptyFacets" name="Display empty facet">
			<Description><![CDATA[
			Displays empty facets, that is to say, facets that do not have any categories.<br />
			<b>Note</b> you need to add facet_facetName(facetName is your facetId) in MessageList of I18N file to see correct label.
		    ]]></Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="isPaginated" name="Paginated facets">
			<Description>Activates facets pagination on UI side, it is not recommended but manage facets
				pagination on backend side using facets management UI and Access triggers.
				It must be considered as a failover mode for small facets (few categories).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['perPage','paginateCondition']})</Display>
			</Functions>
		</Option>
		<Option id="perPage" name="Page size">
			<Description>Specifies the number of categories displayed per pagination page.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option arity="ZERO_OR_ONE" name="Paginate Condition" id="paginateCondition" isEvaluated="true">
			<Description>Condition to display the pagination (default is true).</Description>
			<Functions>
				<Display>SetType('code', 'mel')</Display>
			</Functions>
		</Option>
		<Option id="activateFilter" name="Activate filter on categories">
			<Description>Activates filters on category names.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['filterMin']})</Display>
			</Functions>
		</Option>
		<Option id="filterMin" name="Filter minimum">
			<Description>Specifies the minimum number of categories to activate the search input.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="enableDragging" name="Enable dragging">
			<Description>Enables dragging of categories. Can be useful for the quick filters.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="onHoverInfo" name="Display path on hover">
			<Description>Display category path with titles on mouse hover (not really useful and recommended).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Advanced">
		<Option id="activateReload" name="Activate Reload Facet" arity="ONE">
			<Description>Activates a button to load additional categories, if any.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['labelReload','iconReload','paramNameReload','paramValueReload','reloadCondition']})</Display>
			</Functions>
		</Option>
		<Option id="paramNameReload" name="Parameter Name" arity="ZERO_OR_ONE">
			<Description>Name of the parameter to add.</Description>
		</Option>
		<Option arity="ZERO_OR_ONE" name="Reload Button Condition" id="reloadCondition" >
			<Description>Condition to display the button (default is true)</Description>
			<Functions>
				<Display>SetType('code', 'mel')</Display>
			</Functions>
		</Option>
		<Option id="noResultsJspPathHit" name="JSP path to use if no results" arity="ONE">
			<Description>If there are no results, the widget is either hidden using a trigger, or a message is displayed using the content of a JSP file.</Description>
			<Functions>
				<Check>isJspPath</Check>
			</Functions>
		</Option>
		<Option id="forceRefineOnFeeds" name="Force refinement for feeds" arity="ZERO_OR_MANY">
			<Description>Apply refine also to other feeds. NOT RECOMMENDED OPTION !!!!
				(it adds lots of parameters in URL with duplicated refines values and can lead with side effects, too big URLs).
				It's recommended to use 'Facets management trigger" on feed side instead to manage refine propagation to multiple
				feeds (Access API side).</Description>
			<Functions>
				<ContextMenu>Feeds()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="keepExtraRefinements" name="Keep Extra Refinements">
			<Description>Remove refinements in URL for feeds not present in refinement widget or not targeted by Force refinement option. Defaults to true.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="keepParameters" name="Keep Parameters" isEvaluated="true" arity="ZERO_OR_MANY">
			<Description>Keep parameters from URL.</Description>
		</Option>
		<Option arity="ZERO_OR_ONE" name="Calendar Format" id="calendarFormat" isEvaluated="true">
			<Description>Specify the format for the date filters (default is MM/DD/YYYY)</Description>
			<Functions>
				<Display>SetType('code', 'mel')</Display>
			</Functions>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Count Bar">
		<Option id="enableFacetCountBars" name="Enable Facet Count Bar" arity="ONE">
			<Description>Enable to add bar visualizations to the count of your facet widgets.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['color', 'position']})</Display>
			</Functions>
		</Option>
		<Option id="color" name="Bar color" arity="ZERO_OR_ONE">
			<Description>The color of the bars. Defaults to #FF8A2E</Description>
		</Option>
		<Option id="position" name="Bar position" arity="ZERO_OR_ONE">
			<Description>Whether to position the bar under the category title or behind it. Defaults to 'Right'</Description>
			<Values>
				<Value>Right</Value>
				<Value>Left</Value>
				<Value>Under</Value>
				<Value>Behind</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<DefaultValues>
		<DefaultValue name="refineMode">Default</DefaultValue>
		<DefaultValue name="collapseFacet">false</DefaultValue>
        <DefaultValue name="displayCloseBtn">true</DefaultValue>
		<DefaultValue name="inputName">q</DefaultValue>
		<DefaultValue name="placeHolder">${i18n['widgets.refinePanel.search-filter.placeholder']}</DefaultValue>
		<DefaultValue name="buttonName">${i18n['search']}</DefaultValue>
		<DefaultValue name="enableSuggest">false</DefaultValue>
		<DefaultValue name="suggestApiAction">access</DefaultValue>
		<DefaultValue name="suggestApiConfig">sapi0</DefaultValue>
		<DefaultValue name="suggestApiCommand">suggest</DefaultValue>
		<DefaultValue name="showEmptyFacets">false</DefaultValue>
		<DefaultValue name="facetsListMode">No filtering</DefaultValue>
		<DefaultValue name="sortModeFacets">default</DefaultValue>
		<DefaultValue name="sortMode">default</DefaultValue>
		<DefaultValue name="isDisjunctive">false</DefaultValue>
		<DefaultValue name="nbSubFacets">1000</DefaultValue>
		<DefaultValue name="values">count</DefaultValue>
		<DefaultValue name="displayExclude">false</DefaultValue>
		<DefaultValue name="iterMode">all</DefaultValue>
		<DefaultValue name="drillDown">false</DefaultValue>
		<DefaultValue name="facetTemplateDirectory">default</DefaultValue>
		<DefaultValue name="noResultsJspPathHit">/WEB-INF/jsp/commons/noFacets.jsp</DefaultValue>
		<DefaultValue name="templateBasePath">templates/</DefaultValue>
		<DefaultValue name="keepExtraRefinements">false</DefaultValue>
		<DefaultValue name="enableFacetCountBars">false</DefaultValue>
		<DefaultValue name="color">#FF8A2E</DefaultValue>
		<DefaultValue name="position">Right</DefaultValue>
		<DefaultValue name="customDelimiter">,</DefaultValue>
		<DefaultValue name="keepParameters">false</DefaultValue>
		<DefaultValue name="minLength">3</DefaultValue>
		<DefaultValue name="maxSuggests">100</DefaultValue>
		<DefaultValue name="onHoverInfo">false</DefaultValue>
	</DefaultValues>
</Widget>
