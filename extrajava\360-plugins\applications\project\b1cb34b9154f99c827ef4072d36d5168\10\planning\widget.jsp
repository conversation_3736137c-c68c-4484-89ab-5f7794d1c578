<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import varWidget="widget" varFeeds="accessFeeds"/>

<config:getOption name="height" var="height" defaultValue=""/>
<config:getOption name="title" var="title" defaultValue=""/>
<config:getOption name="groupBy" var="groupBy" defaultValue="" doEval="false"/>

<config:getOption name="startInterval" var="startInterval" defaultValue=""/>
<config:getOption name="extendToValue" var="extendToValue" defaultValue="false"/>

<config:getOption name="id" var="id" defaultValue=""/>
<config:getOption name="label" var="label" defaultValue=""/>
<config:getOption name="refineDateStart" var="refineDateStart" defaultValue=""/>
<config:getOption name="refineDateEnd" var="refineDateEnd" defaultValue=""/>
<config:getOption name="start" var="start" defaultValue=""/>
<config:getOption name="end" var="end" defaultValue=""/>
<config:getOption name="secondStart" var="secondStart" defaultValue=""/>
<config:getOption name="secondEnd" var="secondEnd" defaultValue=""/>
<config:getOption name="additionalParams" var="additionalParams" defaultValue=""/>
<config:getOption name="colorMeta" var="colorMeta" defaultValue=""/>
<config:getOption name="additionalMetas" var="additionalMetas" defaultValue=""/>
<config:getOption name="extraClasses" var="extraClasses" defaultValue=""/>

<config:getOption name="enableSubObjects" var="enableSubObjects" defaultValue=""/>
<config:getOption name="nameSubItem" var="nameSubItem" defaultValue=""/>
<config:getOption name="idSubItem" var="idSubItem" defaultValue=""/>
<config:getOption name="dateMeta" var="dateMeta" defaultValue=""/>
<config:getOption name="relatedMeta" var="relatedMeta" defaultValue=""/>
<config:getOption name="subDisplayType" var="subDisplayType" defaultValue=""/>
<config:getOption name="additionalParamsSubItems" var="additionalParamsSubItems" defaultValue=""/>
<config:getOption name="additionalSubItemMetas" var="additionalSubItemMetas" defaultValue=""/>
<config:getOption name="subItemColorMeta" var="subItemColorMeta" defaultValue=""/>
<config:getOption var="fullScreen" name="enableFullScreen"/>
<config:getOption var="displayDoc" name="displayDoc" defaultValue="false"/>
<config:getOption var="doc" name="doc" defaultValue=""/>

<config:getOption var="loadAll" name="loadAll" defaultValue="false"/>
<config:getOption var="nbObject" name="nbObject" defaultValue="50"/>

<c:set var="realHeight" value="${height != '' ? height+94 : 0}"/>

<widget:widget extraCss="srcTimelinevis" varUcssId="uCssId"
			   extraStyles="${height != '' ? 'height: ' : ''}${height != '' ? realHeight : ''}${height != '' ? 'px;' : ''}">

	<c:set value="timeline-vis-${uCssId}" var="timelineId"/>

	<c:if test="${not empty title}">
		<widget:header>
			<span class="widgetHeaderIcon fonticon fonticon-calendar"></span>
			<div class="widgetChartHeader widgetTitle">${title}</div>
			<%-- fullScreen option --%>
			<%--<c:if test="${fullScreen == 'true'}">--%>
				<%--<span class="resize-button fonticon fonticon-resize-full widgetHeaderButton"></span>--%>
			<%--</c:if>--%>
		</widget:header>
	</c:if>

	<widget:content>
		<div class="loader"></div>
		<div class="main-widget">
			<div class="timeline-tooltip hidden">
				<plma:getColorsFromFacetConfig var="catMap" facetName="${colorMeta}"/>
				<c:if test="${fn:length(catMap) > 0}">
					<div class="facetTitle">Color for facet ${colorMeta}</div>
					<c:forEach items="${catMap}" var="cat">
						<string:escape var="bgColor" value="${cat.value}" escapeType="HTML" />
						<div class="elemTooltip">
							<span class="colorTooltip" style="background-color: ${bgColor}"></span>
							<span class="catTooltip">
								<string:escape escapeType="HTML">
									${cat.key}
								</string:escape>
							</span>
						</div>
					</c:forEach>
				</c:if>
				<c:if test="${subItemColorMeta != colorMeta}">
					<plma:getColorsFromFacetConfig var="subCatMap" facetName="${subItemColorMeta}"/>
					<c:if test="${fn:length(subCatMap) > 0}">
						<div class="facetTitle">Color for facet ${subItemColorMeta}</div>
						<c:forEach items="${subCatMap}" var="cat">
							<string:escape var="bgColor" value="${cat.value}" escapeType="HTML" />
							<div class="elemTooltip">
								<span class="colorTooltip" style="background-color: ${bgColor}"></span>
								<span class="catTooltip">
									<string:escape escapeType="HTML">
										${cat.key}
									</string:escape>
								</span>
							</div>
						</c:forEach>
					</c:if>
				</c:if>
			</div>
			<div class="timeline-vis" id="${timelineId}"></div>
			<div class="timeline-info">
				<div class="info-title">
					<span class="title-value"></span>
					<span class="refine-button fonticon fonticon-filter"></span>
					<span class="close-button fonticon fonticon-cancel"></span>
				</div>
				<div class="info-main">
					<div class="info-main-data info-main-date-start">
						<i18n:message var="metaStartLabel" code="meta_${start}"/>
						<span class="label">
							<c:choose>
								<c:when test="${metaStartLabel != undefined && metaStartLabel != ''}">${metaStartLabel}: </c:when>
								<c:otherwise>${start}: </c:otherwise>
							</c:choose>
						</span>
						<span class="value"></span>
					</div>
					<div class="info-main-data info-main-date-end">
						<i18n:message var="metaEndLabel" code="meta_${end}"/>
						<span class="label">
							<c:choose>
								<c:when test="${metaEndLabel != undefined && metaEndLabel != ''}">${metaEndLabel}: </c:when>
								<c:otherwise>${end}: </c:otherwise>
							</c:choose>
						</span>
						<span class="value"></span>
					</div>
					<div class="info-main-data info-second-date-start">
						<i18n:message var="metaSecondStartLabel" code="meta_${secondStart}"/>
						<span class="label">
							<c:choose>
								<c:when test="${metaSecondStartLabel != undefined && metaSecondStartLabel != ''}">${metaSecondStartLabel}: </c:when>
								<c:otherwise>${secondStart}: </c:otherwise>
							</c:choose>
						</span>
						<span class="value"></span>
					</div>
					<div class="info-main-data info-second-date-end">
						<i18n:message var="metaSecondEndLabel" code="meta_${secondEnd}"/>
						<span class="label">
							<c:choose>
								<c:when test="${metaSecondEndLabel != undefined && metaSecondEndLabel != ''}">${metaSecondEndLabel}: </c:when>
								<c:otherwise>${secondEnd}: </c:otherwise>
							</c:choose>
						</span>
						<span class="value"></span>
					</div>
					<div class="info-main-data info-related-parent">
						<i18n:message var="metaRelatedLabel" code="meta_${groupBy}"/>
						<span class="label">
							<c:choose>
								<c:when test="${metaRelatedLabel != undefined && metaRelatedLabel != ''}">${metaRelatedLabel}: </c:when>
								<c:otherwise>${groupBy}: </c:otherwise>
							</c:choose>
						</span>
						<span class="value"></span>
					</div>
					<div class="info-main-data info-color">
						<i18n:message var="metaColorLabel" code="meta_${colorMeta}"/>
						<span class="label">
							<c:choose>
								<c:when test="${metaColorLabel != undefined && metaColorLabel != ''}">${metaColorLabel}: </c:when>
								<c:otherwise>${colorMeta}: </c:otherwise>
							</c:choose>
						</span>
						<span class="value"></span>
					</div>
				</div>
				<div class="info-sub-item">
					<div class="info-sub-item-elem">
						<div class="info-sub-item-title"></div>
						<div class="info-sub-item-main">
							<div class="info-sub-item-data info-sub-item-start">
								<i18n:message var="metaSubItemDate" code="meta_${dateMeta}"/>
								<span class="label">
									<c:choose>
										<c:when test="${metaSubItemDate != undefined && metaSubItemDate != ''}">${metaSubItemDate}: </c:when>
										<c:otherwise>${dateMeta}: </c:otherwise>
									</c:choose>
								</span>
								<span class="value"></span>
							</div>
							<div class="info-sub-item-data info-sub-item-color-meta">
								<i18n:message var="metaSubItemColor" code="meta_${subItemColorMeta}"/>
								<span class="label">
									<c:choose>
										<c:when test="${metaSubItemColor != undefined && metaSubItemColor != ''}">${metaSubItemColor}: </c:when>
										<c:otherwise>${subItemColorMeta}: </c:otherwise>
									</c:choose>
								</span>
								<span class="value"></span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div id="log" class="timelineFooter">
			<div class="searchContainer">
				<input type="text" class="searchTimelineInput hidden"/>
				<span class="searchTimelineButton fonticon fonticon-search"></span>
			</div>
			<c:if test="${fn:length(catMap) > 0 || fn:length(subCatMap) > 0}">
				<div class="tooltip-container">
					<span class="tooltip-button fonticon fonticon-help"></span>
				</div>
			</c:if>
			<div class="loadAllData">
				<span class="loadAllDataButton fonticon fonticon-tag-more" title="<i18n:message code='widget.planning.loadAll' />"></span>
			</div>
			<div class="moveButtons buttonsContainer">
				<span class="actionButton moveLeftButton fonticon fonticon-left"></span>
				<span class="actionButton moveRightButton fonticon fonticon-right"></span>
			</div>
			<div class="zoomButtons buttonsContainer">
				<span class="actionButton zoomOutButton fonticon fonticon-minus"></span>
				<span class="actionButton zoomInButton fonticon fonticon-plus"></span>
			</div>
		</div>

		<div class="doc-container hidden">
			<div class="container">${doc}</div>
			<span class="close-doc fonticon fonticon-cancel" title="<i18n:message code='generic.close' />"></span>
		</div>
	</widget:content>

	<render:renderScript position="READY">
		var buttonOptions = {};
		buttonOptions.buttons = [];
		<c:if test="${displayDoc}">
			var docButton = {};
			docButton.icon = 'fonticon fonticon-info';
			docButton.label = '<i18n:message code="widget.planning.menu.documentation" javaScriptEscape="${true}"/>';
			docButton.action = function(e) {
				var planning = $(e.target).closest('.srcTimelinevis');
				var docContainer = planning.find('.doc-container');
				docContainer.removeClass('hidden');
			};
			buttonOptions.buttons.push(docButton);
			$('.${uCssId}').find('.doc-container .close-doc').on('click', function(e) {
				var planning = $(e.target).closest('.srcTimelinevis');
			planning.find('.doc-container').addClass('hidden');
			});
		</c:if>
		<c:if test="${fullScreen == 'true'}">
			var elem = {};
			elem.icon = FullScreenWidget.BASE_ICON_CSS_CLASS + ' ' + FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS;
			elem.label = FullScreenWidget.getMessage('widget.action.fullScreen');
			buttonOptions.buttons.push(elem);
		</c:if>


		var timelineOptions = {};

		timelineOptions.buttons = buttonOptions;

		timelineOptions.startInterval = '${startInterval}';
		timelineOptions.extendToValue = '${extendToValue}';

		timelineOptions.height = '${height}';
		timelineOptions.url = '<c:url value="/timeline/config"/>';
		timelineOptions.urlI18n = '<c:url value="/c/i18n/get/"/>';
		timelineOptions.feeds = '${not empty accessFeeds ? accessFeeds.keySet() : '[]'}';
		timelineOptions.groupBy = '${groupBy}';
		timelineOptions.id = '${id}';
		timelineOptions.label = '${label}';
		timelineOptions.refineDateStart = '${refineDateStart}';
		timelineOptions.refineDateEnd = '${refineDateEnd}';
		timelineOptions.start = '${start}';
		timelineOptions.end = '${end}';
		timelineOptions.secondStart = '${secondStart}';
		timelineOptions.secondEnd = '${secondEnd}';
		timelineOptions.additionalParams = '${additionalParams}';
		timelineOptions.colorMeta = '${colorMeta}';
		timelineOptions.additionalMetas = '${additionalMetas}';
		timelineOptions.extraClasses = '${extraClasses}';
		timelineOptions.pageName = '<url:getPageName/>';
		timelineOptions.pageId = '<request:getParameterValue name="pageId" defaultValue=""/>';
		timelineOptions.urlParams = window.location.href.split('?').length > 1 ? decodeURIComponent(window.location.href.split('?')[1]) : "";

		timelineOptions.enableSubObjects = '${enableSubObjects}';
		timelineOptions.nameSubItem = '${nameSubItem}';
		timelineOptions.idSubItem = '${idSubItem}';
		timelineOptions.dateMeta = '${dateMeta}';
		timelineOptions.relatedMeta = '${relatedMeta}';
		timelineOptions.subDisplayType = '${subDisplayType}';
		timelineOptions.additionalParamsSubItems = '${additionalParamsSubItems}';
		timelineOptions.additionalSubItemMetas = '${additionalSubItemMetas}';
		timelineOptions.subItemColorMeta = '${subItemColorMeta}';

		timelineOptions.loadAll = '${loadAll}';
		timelineOptions.nbObject = '${nbObject}';

		new PLMAPlanning('${uCssId}',timelineOptions);


	</render:renderScript>

</widget:widget>
