<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA data table" group="PLM Analytics/Data" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>
		&lt;p&gt;This widget aims at presenting data in a table and enabling the user to easily interact and browse these data.&lt;/p&gt;
		&lt;p&gt;
			The data presented in the widget can either be a list of hits (i.e. documents from the index) or series based on aggregations computed on facets:
			&lt;ul&gt;
				&lt;li&gt;
					Hits mode: each row represents a hit, each column in the table can be configured individually. The displayed value is defined by a MEL expression. A sort field can be set to allow sorting on each column. Pagination control and sorting are done through back-end calls.
				&lt;/li&gt;
				&lt;li&gt;
					Synthesis mode: the content of the table comes from series, each of which consists in an aggregation computed on a facet (series are configured in the widget options). Each row represents a facet category. On 1-dimension facets, each serie will add a new column to the table; on multidimension facets, each serie will add as many columns as categories in the second dimension. Sorting is enabled on all columns. Sorting, pagination and search are off-line, front-end calls.
				&lt;/li&gt;
			&lt;/ul&gt;
		&lt;/p&gt;
	</Description>
	
	<Preview>
	<![CDATA[
		<img src="/resources/widgets/plmaDataTable/images/preview.png" alt="Table" />
	]]>
	</Preview>
	
	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>
	
	<Includes>
		<Include type="css" path="../plmaResources/lib/datatables/DataTables-1.10.16/css/jquery.dataTables.min.css"/>
		<Include type="css" path="../plmaResources/lib/datatables/Buttons-1.4.2/css/buttons.dataTables.min.css"/>
		<Include type="css" path="../plmaResources/lib/datatables/ColReorder-1.4.1/css/colReorder.dataTables.min.css"/>
		<Include type="css" path="../plmaResources/lib/datatables/FixedColumns-3.2.3/css/fixedColumns.dataTables.min.css"/>
		<Include type="css" path="../plmaResources/lib/datatables/FixedHeader-3.1.3/css/fixedHeader.dataTables.min.css"/>
		<Include type="css" path="../plmaResources/lib/datatables/KeyTable-2.3.2/css/keyTable.dataTables.min.css"/>
		<Include type="css" path="../plmaResources/lib/datatables/Select-1.3.0/css/select.dataTables.min.css"/>
		<Include type="css" path="../plmaResources/lib/datatables/Scroller-2.0.0/css/scroller.dataTables.min.css"/>
		<Include type="css" path="../plmaResources/lib/notify/notify-plma.less"/>
		<Include type="css" path="../plmaResources/css/styles/spinner.less"/>
		<Include type="css" path="../plmaResources/css/styles/popupstyle.less"/>
		<Include type="css" path="../plmaResources/css/fullScreenWidget.less"/>
		<Include type="css" path="css/style.less"/>
	
		<Include type="js" path="../plmaResources/lib/datatables/DataTables-1.10.16/js/jquery.dataTables.min.js"/>
		<Include type="js" path="../plmaResources/lib/datatables/Buttons-1.4.2/js/dataTables.buttons.min.js"/>
		<Include type="js" path="../plmaResources/lib/datatables/Buttons-1.4.2/js/buttons.colVis.min.js"/>
		<Include type="js" path="../plmaResources/lib/datatables/Buttons-1.4.2/js/buttons.html5.min.js"/>
		<Include type="js" path="../plmaResources/lib/datatables/ColReorder-1.4.1/js/dataTables.colReorder.min.js"/>
		<Include type="js" path="../plmaResources/lib/datatables/FixedColumns-3.2.3/js/dataTables.fixedColumns.min.js"/>
		<Include type="js" path="../plmaResources/lib/datatables/FixedHeader-3.1.3/js/dataTables.fixedHeader.min.js"/>
		<Include type="js" path="../plmaResources/lib/datatables/KeyTable-2.3.2/js/dataTables.keyTable.min.js"/>
		<Include type="js" path="../plmaResources/lib/datatables/Select-1.3.0/js/dataTables.select.min.js"/>
		<Include type="js" path="../plmaResources/lib/datatables/Scroller-2.0.0/js/dataTables.scroller.min.js"/>
		<Include type="js" path="../plmaResources/lib/notify/notify.js"/>
		<Include type="js" path="../plmaResources/lib/notify/notify-plma.js"/>
		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js"/>
		<Include type="js" path="../plmaResources/js/popupLib.js"/>
		<Include type="js" path="../plmaResources/js/fullScreenWidget.js"/>
		<Include type="js" path="../plmaResources/js/queueManager.js"/>
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js"/>
		<Include type="js" path="../3dxpSearchAndRefine/js/message-handler.js"/>
		<Include type="js" path="../plmaResources/js/experienceAPIs/Session3DContent.js"/>
		<Include type="js" path="../plmaResources/js/select.js" />
		<Include type="js" path="../plmaResources/js/SelectionWidget.js" />
		<Include type="js" path="js/plmaDataTable.js"/>
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ONE" />
	
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>widget.plmaDataTable.error.server</JsKey>
			<JsKey>widget.plmaDataTable.error.noFeeds</JsKey>
			<JsKey>widget.plmaDataTable.error.feedError</JsKey>
			<JsKey>widget.plmaDataTable.error.saveState</JsKey>
			<JsKey>widget.plmaDataTable.export</JsKey>
			<JsKey>widget.plmaDataTable.export.success</JsKey>
			<JsKey>widget.plmaDataTable.config.section.displayedColumns</JsKey>
			<JsKey>widget.plmaDataTable.config.section.exportAllColumns</JsKey>
			<JsKey>widget.plmaDataTable.config.section.exportAllColumns.info</JsKey>
			<JsKey>widget.plmaDataTable.config.section.fixedColumns</JsKey>
			<JsKey>widget.plmaDataTable.config.section.fixedRows</JsKey>
			<JsKey>widget.plmaDataTable.config.fixedColumns.number</JsKey>
			<JsKey>widget.plmaDataTable.reset.success</JsKey>
			<JsKey>widget.plmaDataTable.transpose.columnTitle</JsKey>
			<JsKey>widget.plmaDataTable.config.section.fixedRows.info</JsKey>
			<JsKey>widget.plmaDataTable.config.section.fixedRows.ok</JsKey>
			<JsKey>widget.plmaDataTable.config.section.fixedRows.select</JsKey>
			<JsKey>widget.plmaDataTable.dom.error</JsKey>
		</JsKeys>
	</SupportI18N>
	
	<OptionsGroup name="General">
		<Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Widget title. If blank, no title is displayed.</Description>
		</Option>
		<Option id="height" name="Height">
			<Description>Height to give to the table, in pixels. 0 means no fixed height.</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="fullScreen" name="Enable full screen" arity="ONE">
			<Description>Allows the user to view this widget in full screen (via the widget menu).</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="fixHeader" name="Fix header" arity="ONE">
			<Description>You can check this option to always have the header displayed when scrolling.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['scrollContainerSelector']})</Display>
			</Functions>
		</Option>
		<Option id="scrollContainerSelector" name="Scroll container">
			<Description>
				CSS selector of the scrollable container in which to set the fixed header. The container will be the closest ancestor of the table which matches the given selector (see jQuery.closest()).&lt;br/&gt;
				If the &lt;b&gt;Height&lt;/b&gt; option is no set to 0, defaults to the widget. Otherwise, defaults to the window.
			</Description>
			<Functions>
				<ContextMenu>addContext("Container examples", ['.table-wrapper', 'window', '#mainWrapper'])</ContextMenu>
			</Functions>
		</Option>
		<Option id="enableScrolling" name="Enable scrolling" arity="ONE">
			<Description><![CDATA[
				If enabled, use scrolling instead of pagination. Only in <b>Hits</b> mode and the <i>per_page</i> parameter has to be whitelisted.
			]]>
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Data">
		<Option id="mode" name="Mode" arity="ONE">
			<Description>
				Whether this table should show data from hits or from synthesis (facets). &lt;br/&gt;
				In &lt;b&gt;Hits&lt;/b&gt; mode, the number of hits displayed in a page will match the feed configuration (&lt;code&gt;per_page&lt;/code&gt; parameter).
				To allow the user to change the number of hits per page using the length control input,
				make sure that the &lt;code&gt;per_page&lt;/code&gt; parameter of the feed is whitelisted so the widget can override it.
			</Description>
			<Values>
				<Value>Hits</Value>
				<Value>Synthesis</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['Synthesis'], showOptions:['facetSeries', 'enableFilters', 'pagination', 'pageSize', 'defaultValue']})</Display>
				<Display>ToggleDisplay({valueToMatch:['Hits'], showOptions:['hitSeries', 'hitUrl', 'hitTargetFrame' , 'nullSort']})</Display>
			</Functions>
		</Option>
		<Option id="nullSort" name="Null sort" arity="ONE">
			<Description>
				Default to false.&lt;br/&gt;
				If enabled - null values will also be sorted&lt;br/&gt;
				ascending : Null values at top&lt;br/&gt;
				descending : Null values at bottom&lt;br/&gt;
				If disabled - default sorting of cloudview will be applied.
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<OptionComposite id="facetSeries" name="Series" arity="ZERO_OR_MANY" glue="#@#">
			<Option id="facetId" name="Facet" arity="ONE">
				<Description>The facet to use for this serie.</Description>
				<Functions>
					<ContextMenu>Facets()</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="aggregation" name="Aggregation" arity="ZERO_OR_ONE">
				<Description>The aggregation (calculated on the specified facet) to display in this serie. Defaults to 'count'.</Description>
				<Functions>
					<ContextMenu>Aggregations('facetId')</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="aggregationLabel" name="Label" arity="ZERO_OR_ONE" isEvaluated="true">
				<Description>The label to display for this aggregation (only displayed for 1D facets). If empty, the aggregation name is used.</Description>
			</Option>
			<Option id="aggregationFormat" name="Num. format">
				<Description>You can specify a Java number format to format aggregation values. Use '0' to display each digit, '#' to only display digits that are not heading 0s. The user's locale is taken into account.</Description>
				<Functions>
					<ContextMenu>addContext("format", ["#", "#.##", "###,###.##", "000000.00", "$ ###.###,##", "€ ### ###,##"])</ContextMenu>
				</Functions>
			</Option>
			<Option id="aggregationCssClass" name="CSS Class" arity="ZERO_OR_ONE">
				<Description>CSS Class name to be added for the column</Description>
			</Option>

		</OptionComposite>
		<Option id="pagination" name="Paginate" arity="ONE">
			<Description>Check this option if you don't want to display all rows at once.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="pageSize" name="Page size" arity="ZERO_OR_ONE">
			<Description>Number of rows to display on a single page. Defaults to 10 if pagination is enabled. Ignored if pagination is disabled.</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
        <Option id="defaultValue" name="Default value" arity="ZERO_OR_ONE">
            <Description>If no value is available for the aggregation on a specific category, this value will be displayed instead. Defaults to 0.</Description>
            <Functions>
                <Check>isInteger</Check>
            </Functions>
        </Option>
		<OptionComposite id="hitSeries" name="Columns" arity="ZERO_OR_MANY" glue="#@#" isEvaluated="false">
			<Option id="columnLabel" name="Label" arity="ONE" isEvaluated="true">
				<Description>A label for this column.</Description>
				<Functions>
					<Display>PARAMETER_doResize({ width: 50 })</Display>
				</Functions>
			</Option>
			<Option id="columnValue" name="Value" arity="ONE" isEvaluated="true">
				<Description>
				The value to display in the column. You can use MEL and HTML.&lt;br/&gt;
				If exporting data is enabled and uses the &lt;b&gt;Search API&lt;/b&gt; mode, make sure to only access the &lt;b&gt;entry&lt;/b&gt; scope (e.g.: &lt;code&gt;${entry.meta['name']}&lt;/code&gt;),
				as the resolver will be unaware of other scopes (like &lt;b&gt;feeds&lt;/b&gt;).
				</Description>
				<Functions>
					<Display>PARAMETER_doResize({ width: 50 })</Display>
				</Functions>
			</Option>
			<Option id="exportedValue" name="Export" arity="ZERO_OR_ONE" isEvaluated="true">
				<Description>
				The value to export in the column. If blank, it defaults to the displayed value. You can use MEL.&lt;br/&gt;
				If exporting data is enabled and uses the &lt;b&gt;Search API&lt;/b&gt; mode, make sure to only access the &lt;b&gt;entry&lt;/b&gt; scope (e.g.: &lt;code&gt;${entry.meta['name']}&lt;/code&gt;),
				as the resolver will be unaware of other scopes (like &lt;b&gt;feeds&lt;/b&gt;).
				</Description>
				<Functions>
					<Display>PARAMETER_doResize({ width: 40 })</Display>
				</Functions>
			</Option>
			<Option id="sortField" name="Sort" arity="ZERO_OR_ONE">
				<Description>The index field on which to sort when this column header is clicked. Must be RAM-based. Leave empty to disable sort. &lt;b&gt;Warning&lt;/b&gt;: having too many RAM-based index fields will have an impact on memory footprint and performances.</Description>
				<Placeholder>disabled</Placeholder>
				<Functions>
					<ContextMenu>Fields()</ContextMenu>
					<Display>PARAMETER_doResize({ width: 20 })</Display>
				</Functions>
			</Option>
			<Option id="metaNumberFormat" name="Num. format">
				<Description>You can specify a Java number format if the corresponding meta contains numerical values. Use &lt;code&gt;0&lt;/code&gt; to display each digit, &lt;code&gt;#&lt;/code&gt; to only display digits that are not heading 0s.</Description>
				<Functions>
					<ContextMenu>addContext("format", ["###,###.##", "000000.00", "$ ###.###,##", "€ ### ###,##"])</ContextMenu>
					<Display>PARAMETER_doResize({ width: 20 })</Display>
				</Functions>
			</Option>
			<Option id="columnCssClass" name="Css Class" arity="ZERO_OR_ONE">
				<Description>CSS Class name to be added for the column</Description>
				<Functions>
					<Display>PARAMETER_doResize({ width: 30 })</Display>
				</Functions>
			</Option>
			<Option id="displayColumn" name="Show" arity="ZERO_OR_ONE">
				<Description>
					Whether this column should be displayed by default. Otherwise it will be hidden, and the user can choose to display it from the settings. &lt;br/&gt;
					If you just updated the plugin and this option was not here before, it will default to true even though the box is not checked; if you edit this widget, it will then behave normally.
				</Description>
				<Values>
					<Value>true</Value>
					<Value>false</Value>
				</Values>
			</Option>
		</OptionComposite>
		<OptionComposite id="technicalData" name="Technical Data" arity="ZERO_OR_MANY" glue="#@#" isEvaluated="false">
			<Option id="technicalName" name="Name" arity="ONE" isEvaluated="false">
				<Description>Property name. Default pre-fix to 'data-'</Description>
				<Functions>
					<Display>PARAMETER_doResize({ width: 50 })</Display>
				</Functions>
			</Option>
			<Option id="technicalValue" name="Value" arity="ONE" isEvaluated="true">
				<Description>Property value</Description>
				<Functions>
					<Display>PARAMETER_doResize({ width: 50 })</Display>
				</Functions>
			</Option>
		</OptionComposite>
		<Option id="hitUrl" name="Link URL" isEvaluated="false">
			<Description>URL to go to when a row is clicked. Leave empty to disable links on rows.</Description>
			<Functions>
				<ContextMenu>Eval()</ContextMenu>
				<Display>ToggleDisplay({valueToMatch:[''], hideOptions:['hitTargetFrame']})</Display>
			</Functions>
		</Option>
		<Option id="hitTargetFrame" name="Frame" arity="ZERO_OR_ONE">
			<Description>
				<![CDATA[
						The frame in which to open the hit link. Defaults to &lt;code&gt;_self&lt;/code&gt;.
						Use &lt;code&gt;_blank&lt;/code&gt; to open in a new page, &lt;code&gt;_top&lt;/code&gt; to open in the full body of the window, or type a frame name to open in a specific frame.
						<b>NOTE</b>: Safari browser not allow Frame(target) parameter in windows.open(): <a href="https://developer.apple.com/library/safari/documentation/Tools/Conceptual/SafariExtensionGuide/WorkingwithWindowsandTabs/WorkingwithWindowsandTabs.html">Documentation</a>
				]]>
			</Description>
			<Functions>
				<ContextMenu>addContext("target", ["_self", "_blank", "_parent", "_top"])</ContextMenu>
			</Functions>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="User actions">
		<Option id="enableFilters" name="Enable filters" arity="ONE">
			<Description>Allows the user to refine on a category by clicking the corresponding row or column. Not available in &lt;b&gt;Hits&lt;/b&gt; mode.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['target', 'forceRefineOnFeeds', 'forceRefineOnFacets', 'forceRefineOnMulti']})</Display>
				<Display>PARAMETER_doHide('true',['onRowClickAction'],[], true, false)</Display>
			</Functions>
		</Option>
		<Option id="target" name="Destination page on click" isUrlEncoded="true" isEvaluated="true">
			<Description>Indicates the URL that must be accessed for a refinement option. If blank, you stay on the same page.</Description>
			<Functions>
				<ContextMenu>Pages()</ContextMenu>
			</Functions>
		</Option>
		<Option id="forceRefineOnFeeds" name="Force refinement for feeds" arity="ZERO_OR_MANY">
			<Description>Forces refinements on the specified feeds.</Description>
			<Functions>
				<ContextMenu>Feeds()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="forceRefineOnFacets" name="Force refinement for facets" arity="ZERO_OR_MANY">
			<Description>Applies refinements on the specified facets instead of the original one. Only works for 1D facets.</Description>
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="forceRefineOnMulti" name="Force refinement for multidimension facet" arity="ZERO_OR_ONE">
			<Description>
				If the displayed facet is an AxB multidimension facet, you can check this option to apply refinements on A both on A and AxB.
				This will enable drilling down on the corresponding dimension. &lt;br/&gt;
				By default, this option is deactivated and refinements only apply on the 1-dimension facet. 
				In PLM Analytics app, propagating these refinements to multidim facets is handled by a trigger.
				Only check this option if you specifically want refinements to add the multidim refine parameter to the URL.
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="removeParameters" name="Remove URL parameters" arity="ZERO_OR_MANY">
			<Description>You can specify URL parameter names that will be discarded when generating the filter URLs.</Description>
			<Functions>
				<ContextMenu></ContextMenu>
			</Functions>
		</Option>
		<Option id="hideEmpty" name="Hide Empty Series" arity="ONE">
			<Description>Hides series with no values.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="onInitAction" name="Action on table init" isEvaluated="true">
			<Description>A JS function called when table is initiated.</Description>
			<Placeholder>function(e) {}</Placeholder>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
		<Option id="onHoverAction" name="Action on row hover" isEvaluated="true">
			<Description>A JS function called when hovering a row.</Description>
			<Placeholder>function(e) {}</Placeholder>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
		<Option id="onRowClickAction" name="Action on row click" isEvaluated="true">
			<Description>A JS function called when clicking a row.</Description>
			<Placeholder>function(e) {}</Placeholder>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Advanced">
		<Option id="customHTMLNoResultMessage" name="No result template (html)" isEvaluated="true">
			<Description>Specifies a custom HTML text message when there are no results. Can be a MEL expression.</Description>
			<Functions>
				<Display>SetType('code', { mode: 'html', fullscreen: false })</Display>
			</Functions>
		</Option>
		<Option id="noResultsJspPathHit" name="JSP path to use if no results" arity="ZERO_OR_ONE">
			<Description>If there are no results, the widget is either hidden using a trigger, or a message is displayed using the content of a JSP file(Default Path is /WEB-INF/jsp/commons/noResults.jsp).</Description>
			<Functions>
				<Check>isJspPath</Check>
			</Functions>
		</Option>
		<Option id="fixPaginationToolbar" name="Fixed Pagination Toolbar" arity="ONE">
			<Description>
				<![CDATA[
					For making l, i, and p DOM options fix, you need to wrap them into another class
					<ul>
					 	<li>"fix-page-toolbar fix-header": Fix at header</li>
					 	<li>"fix-page-toolbar fix-footer": Fix at footer</li>
					 	<li>"fix-page-toolbar": Fix pangination toolbar for horizontal scroll</li>
					</ul>
					Further reading <a href="https://datatables.net/examples/basic_init/dom.html"> DataTable DOM positioning </a>
				]]>
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="dataTablesDom" name="DataTables DOM" arity="ZERO_OR_ONE">
			<Description>
				Enables you to override the datatables &lt;code&gt;dom&lt;/code&gt; option, with a string that indicates the order of functional elements in the table. Possible letters are:
				&lt;ul&gt;
					&lt;li&gt;&lt;b&gt;l&lt;/b&gt;: length changing input control (number of rows per page). If you use it, make sure that the 'per_page' parameter of the feed is whitelisted so the widget can override it.&lt;/li&gt;
					&lt;li&gt;&lt;b&gt;f&lt;/b&gt;: filtering input (search form, hidden anyway in &lt;b&gt;Hits&lt;/b&gt; mode).&lt;/li&gt;
					&lt;li&gt;&lt;b&gt;t&lt;/b&gt;: the table (please don't omit that)&lt;/li&gt;
					&lt;li&gt;&lt;b&gt;i&lt;/b&gt;: table information summary (number of displayed rows, number of total rows)&lt;/li&gt;
					&lt;li&gt;&lt;b&gt;p&lt;/b&gt;: pagination control. If you use it, make sur that the 'page' parameter of the feed is whitelisted so the widget can override it.&lt;/li&gt;
				&lt;/ul&gt;
				You can put the same element several times, for example to have pagination control both on top and at the bottom of the table. You can also omit elements, such as the length control, if you don't need them.
			</Description>
			<Functions>
				<ContextMenu>addContext("DataTables DOM examples", [{value:'liptlip', display: 'Toolbar on top and bottom, no search'}, {value:'ftlip', display: 'Toolbar at bottom, search enabled'}, {value:'flipt', display: 'Toolbar on top, search enabled'}, {value:'ftip', display: 'Toolbar at bottom, fixed number of rows'}])</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="rowIds" name="Row ids" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Name of the meta to set as table's rows id (Default value is uri).</Description>
		</Option>
		<Option id="activateFixedRow" name="Enable fixed row option">
			<Description>Specifies if the feature to fix row on top on table is enabled (default is true).</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="transposedHeader" name="Header when transposed" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>The value displayed in the top left cell of the table when it is transposed. Leave empty to use the default value, which will be the name of the 2nd facet if the data comes from a 2 dimension facet, or "Values" otherwise.</Description>
		</Option>
		<Option id="saveState" name="Save state" arity="ONE">
			<Description>
				Each time the user changes the table state (displayed columns, sort, etc.), it will be persisted in the Storage Service. &lt;br/&gt;
				&lt;b&gt;WARNING&lt;/b&gt;: in &lt;b&gt;Hits&lt;/b&gt; mode, checking this option might have an impact on performance, as the widget will automatically execute its feed again once it is loaded.
				Indeed, as the user might have modified the table state (e.g. added a sort or changed the number of results per page), the widget cannot rely on the data provided by the feed
				(which is unaware of these changes), so it needs to query it again with additional parameters. On a basic page, each instance of this widget will execute the feed again. &lt;br/&gt;
				In most of standard PLMA applications, ajax mechanisms allow to avoid useless feeds executions if the PLMA Data Table is not displayed by default when the page is loaded. &lt;br/&gt;
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="saveScrollPositions" name="Save scroll positions" arity="ONE">
			<Description>
				<![CDATA[
					<b>For hits mode only.</b> When the table contains a lot of columns or rows, a scrollbar can appear. <br />
					If the user performs an action that changes the displayed data (apply filter, change page, change the number of hits per page...), the scroll positions will reset. <br />
					Enable this option to save the scroll positions.
				]]>
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Custom content">
		<Description>This tab let you add custom DOM elements in a dedicated column for each row. You can use it to add buttons for example.</Description>
		<Option id="extraColumnOptions" name="Options">
			<Description>
				<![CDATA[
						Column options as a JS object. For more details, see datatables.net column options.<br />
						Example:
<pre>
{
    title: 'Actions',
    width: '200px'
}
</pre>
				]]>
			</Description>
			<Values>
				<Value>{}</Value>
			</Values>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
		<OptionComposite id="elements" name="Elements" arity="ZERO_OR_MANY">
			<Description>List of DOM elements included in the column.</Description>
			<Option id="renderer" name="Renderer">
				<Description>
					<![CDATA[
						Function that returns the desired element. 2 parameters are given:
						<ol>
							<li><b>data</b>: data for the row</li>
							<li><b>meta</b>: information about the cell (row index, column index...)</li>
						</ol>
						Example:
<pre>
function (data, meta) {
    return '&lt;button class="my-button"&gt;' +
               '&lt;i class="fonticon fonticon-home"&gt;&lt;/i&gt;Click me!' +
            '&lt;/button&gt;';
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Option id="onClick" name="On click">
				<Description>
					<![CDATA[
						Function to be called when the element is clicked. 3 parameters are given:
						<ol>
							<li><b>e</b>: the click event</li>
							<li><b>data</b>: data for the row</li>
							<li><b>index</b>: row index</li>
						</ol>
						Example:
<pre>
function (e, data, index) {
    console.log('click on row ' + index);
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>
	<OptionsGroup name="Export">
		<Option id="enableExport" name="Enable" arity="ONE">
			<Description>
				Enables an "export" button that allows the user to download data to a CSV file. &lt;br/&gt;
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['numHits', 'exportMode', 'fileName', 'exportEncoding', 'exportSeparator', 'addBOM', 'enableExportAll', 'recordDelimiter']})</Display>
			</Functions>
		</Option>
		<Option id="numHits" name="Hits limit" arity="ONE">
			<Description>
				(Only for &lt;b&gt;Hits&lt;/b&gt; mode) The maximum number of hits to export. Set to &lt;code&gt;-1&lt;/code&gt; to set no limit on the export.&lt;br/&gt;
				&lt;b&gt;WARNING&lt;/b&gt;: exporting a large set of results could take a while. It is highly recommended to use the 
				&lt;b&gt;Search API&lt;/b&gt; mode for high number of hits, as it allows streaming results.
			</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="exportMode" name="Export mode" arity="ONE">
			<Description>
				(Only for &lt;b&gt;Hits&lt;/b&gt; mode) The underlying API used to export the data. Only used in &lt;b&gt;Hits&lt;/b&gt; mode (in &lt;b&gt;Synthesis&lt;/b&gt; mode, the data is fully loaded).&lt;br/&gt;
				&lt;b&gt;Access API&lt;/b&gt; mode will execute the feed and attached Access triggers to get the data, querying page after page to get the desired number of results.&lt;br/&gt;
				&lt;b&gt;Search API&lt;/b&gt; mode will execute the provided query to get the data, and stream the results from the index without needing to paginate. It is more efficient than the Access API.&lt;br/&gt;
				&lt;b&gt;WARNINGS&lt;/b&gt;
				&lt;ul&gt;
					&lt;li&gt;
					In &lt;b&gt;Search API&lt;/b&gt; mode, the row order in the exported file is not guaranteed to be the same as displayed in the table. This is because this mode uses
					the streaming option of the Search API, which allows fast download but prevents from sorting documents. Make sure to adapt the hits limit so your users have all the data they need.
					&lt;/li&gt;
					&lt;li&gt;
					In &lt;b&gt;Search API&lt;/b&gt; mode, MEL expressions are evaluated from the &lt;b&gt;entry&lt;/b&gt; scope only. In the Data tab, when configuring column values, the MEL expressions should
					not use any other scopes as the MEL evaluator will not be aware of them (such as &lt;b&gt;feeds&lt;/b&gt;).
					&lt;/li&gt;
					&lt;li&gt;
					The &lt;b&gt;Search API&lt;/b&gt; mode only works with CloudView feeds.
					&lt;/li&gt;
				&lt;/ul&gt;
			</Description>
			<Values>
				<Value>Access API</Value>
				<Value>Search API</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['Access API'], showOptions:['exportPerPage']})</Display>
			</Functions>
		</Option>
		<Option id="exportPerPage" name="Override 'per_page'" arity="ZERO_OR_ONE">
			<Description>
				When using the &lt;b&gt;Access API&lt;/b&gt; export mode, you can override the &lt;code&gt;per_page&lt;/code&gt; parameter of the feed. Setting a higher value will allow to produce less queries to the index.
				Set to &lt;code&gt;-1&lt;/code&gt; to fetch all hits in one query (the &lt;b&gt;Hits limit&lt;/b&gt; still applies). Leave empty to use the value set on the feed.
			</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="fileName" name="File name" isEvaluated="true" >
			<Description>The name of the created CSV file. Defaults to 'export.csv'</Description>
		</Option>
		<Option id="exportEncoding" name="Encoding" arity="ZERO_OR_ONE" >
			<Description>You can choose an encoding for the exported CSV file. Defaults to UTF-8.</Description>
		</Option>
		<Option id="exportSeparator" name="Separator" arity="ZERO_OR_ONE">
			<Description>Character to use as a separator in the exported CSV file. Defaults to &lt;b&gt;;&lt;/b&gt;.</Description>
		</Option>
		<Option id="recordDelimiter" name="Record Delimiter">
			<Description>
				Character that is used to separate consecutive records in the output file.&lt;br/&gt;Default option will take the value based on OS.&lt;br/&gt;
				&lt;ul&gt;
					&lt;li&gt;Unix -> LF&lt;/li&gt;
					&lt;li&gt;Windows -> CR + LF&lt;/li&gt;
					&lt;li&gt;Mac -> CR&lt;/li&gt;
				&lt;/ul&gt;
			</Description>
			<Values>
				<Value>Default</Value>
				<Value>LF</Value>
				<Value>CR+LF</Value>
				<Value>CR</Value>
			</Values>
		</Option>
		<Option id="addBOM" name="Add BOM">
			<Description>Whether to add the byte order mark (BOM) at the start of the file.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="enableExportAll" name="Enable export all">
			<Description>Export all columns of DataTable even if some are hidden in UI(default to true)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Reload">
		<Option id="activateReloadChart" name="Activate Reload Chart" arity="ONE">
			<Description>Activate a button to reload chart with additionnal parameters</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['labelReload','iconReload','paramNameReload','paramValueReload','reloadCondition']})</Display>
			</Functions>
		</Option>
		<Option id="labelReload" name="Button label" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Reload button label.</Description>
		</Option>
		<Option id="iconReload" name="Button icon" arity="ZERO_OR_ONE"> 
			<Description>Reload button css icon.</Description>
		</Option>
		<Option id="paramNameReload" name="Parameter Name" arity="ZERO_OR_ONE">
			<Description>Name of the parameter to add.</Description>
		</Option>
		<Option id="paramValueReload" name="Parameter value" arity="ZERO_OR_ONE">
			<Description>Value of the parameter to add.</Description>
		</Option>
		<Option arity="ZERO_OR_ONE" name="Reload Button Condition" id="reloadCondition" isEvaluated="true">
			<Description>Condition to display the reload button (default is true)</Description>
			<Functions>
				<Display>SetType('code', 'mel')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Select API">
		<Description>This widget can interact with other widgets in a 3DDashboard (only in Hits mode). It can publish the user's selection and can subscribe to other widgets' publication.</Description>
		<Option id="enablePublication" name="Enable publication">
			<Description>Enable publication of the user's selection.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="enableSubscription" name="Enable subscription">
			<Description>Enable subscription of selected hits from other widgets.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="channels" name="Channels" arity="ZERO_OR_MANY">
			<Description>Channels to use.</Description>
		</Option>
		<Option id="useApplicationConfig" name="Use the application configuration">
			<Description>Use the application configuration or specify custom configuration.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({ valueToMatch: ['true'], hideOptions: ['channelsConfig'] })</Display>
			</Functions>
		</Option>
		<OptionComposite id="channelsConfig" name="Channels configuration" arity="ZERO_OR_MANY">
			<Option id="topic" name="Topic">
				<Description>Name of the topic.</Description>
			</Option>
			<Option id="data" name="Data" isEvaluated="true">
				<Description>
					<![CDATA[
						JavaScript object that contains useful data. You can use MEL (scope: entry). The hit URL as key is mandatory.<br />
						Example:
<pre>
{
    '${entry.metas['url']}': {
        fullName: '${entry.metas['fullname']}'
    }
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Option id="normalizer" name="Normalizer">
				<Description>
					<![CDATA[
						JavaScript function that returns the appropriate message based on two parameters:
						<ol>
							<li><i>selectedHits</i>: array of hit URLs</li>
							<li><i>data</i>: data previously described</li>
						</ol>
						Example:
<pre>
function (selectedHits, data) {
    return {
        type: 'hello',
        data: selectedHits.map(function (hitUrl) {
            return data[hitUrl].fullName;
        })
    };
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Option id="denormalizer" name="Denormalizer">
				<Description>
					<![CDATA[
						JavaScript function that returns the selected hits based on two parameters:
						<ol>
							<li><i>message</i>: message sent by other widgets</li>
							<li><i>data</i>: data previously described</li>
						</ol>
						Example:
<pre>
function (message, data) {
    return data.map(function (fullName) {
        for (var hitUrl in data) {
            if (data[hitUrl].fullName === fullName) {
                return hitUrl;
            }
        }
        return null;
    });
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>
	<OptionsGroup name="Drag">
		<Description>Configuration for drag interaction.</Description>
		<Option id="enableDrag" name="Enable drag">
			<Description>Whether to enable drag. If so, the dragged elements are the selected rows.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({ valueToMatch: ['true'], showOptions: ['messageBuilder'] })</Display>
			</Functions>
		</Option>
		<Option id="messageBuilder" name="Message builder">
			<Description>
				<![CDATA[
						JavaScript function to build the message based on two parameters:
						<ol>
							<li><i>data</i>: data of the dragged rows</li>
							<li><i>indexes</i>: indexes of the dragged rows</li>
						</ol>
						Example:
<pre>
function (data, indexes) {
    return {
        "protocol": "3DXContent",
        "version": "2.0",
        "source": "MyCusto_AP",
        "data": {
            "items": data.map(function (rowData) {
                return {
                   "envId": "OnPremise",
                   "serviceId": "3DSpace",
                   "contextId": "VPLMProjectLeader.MyCompany.Common Space",
                   "objectId": rowData._hit_id,
                   "objectType": "3DShape"
                };
            })
        }
    };
}
</pre>
					]]>
			</Description>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Documentation">
		<Option id="displayDoc" name="Enable Documentation">
			<Description>If true, adds a button to display a HTML documentation to give the user information about the chart. (Default value is false)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['doc']})</Display>
			</Functions>
		</Option>
		<Option id="doc" name="Description" isEvaluated="true">
			<Description>This is an HTML code to describe the chart.</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
		<Option id="techDoc" name="Technical Reference" isEvaluated="true">
			<Description>This is the chart's technical reference (it won't be displayed in the application).</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
	</OptionsGroup>
	
	<DefaultValues displayName="Hits">
		<!--<DefaultValue name="hitSeries">##########true</DefaultValue>-->
		<DefaultValue name="displayColumn">true</DefaultValue>
		<DefaultValue name="height">0</DefaultValue>
		<DefaultValue name="fixHeader">false</DefaultValue>
		<DefaultValue name="nullSort">false</DefaultValue>
		<DefaultValue name="enableScrolling">false</DefaultValue>
		<DefaultValue name="mode">Hits</DefaultValue>
		<DefaultValue name="enableFilters">false</DefaultValue>
		<DefaultValue name="dataTablesDom">liptlip</DefaultValue>
		<DefaultValue name="fixPaginationToolbar">false</DefaultValue>
		<DefaultValue name="saveState">false</DefaultValue>
		<DefaultValue name="activateReloadChart">false</DefaultValue>
		<DefaultValue name="enableExport">false</DefaultValue>
		<DefaultValue name="numHits">1000</DefaultValue>
		<DefaultValue name="exportMode">Search API</DefaultValue>
		<DefaultValue name="exportEncoding">UTF-8</DefaultValue>
		<DefaultValue name="exportSeparator">;</DefaultValue>
		<DefaultValue name="recordDelimiter">Default</DefaultValue>
		<DefaultValue name="fileName">export</DefaultValue>
		<DefaultValue name="enableExportAll">true</DefaultValue>
	</DefaultValues>
	<DefaultValues displayName="Synthesis">
		<DefaultValue name="height">0</DefaultValue>
		<DefaultValue name="fixHeader">false</DefaultValue>
		<DefaultValue name="mode">Synthesis</DefaultValue>
		<DefaultValue name="pagination">false</DefaultValue>
		<DefaultValue name="pageSize">10</DefaultValue>
        <DefaultValue name="defaultValue">0</DefaultValue>
		<DefaultValue name="forceRefineOnMulti">false</DefaultValue>
		<DefaultValue name="dataTablesDom">ftlip</DefaultValue>
		<DefaultValue name="fixPaginationToolbar">false</DefaultValue>
		<DefaultValue name="activateReloadChart">false</DefaultValue>
		<DefaultValue name="enableExport">false</DefaultValue>
		<DefaultValue name="numHits">1000</DefaultValue>
		<DefaultValue name="exportMode">Search API</DefaultValue>
		<DefaultValue name="recordDelimiter">Default</DefaultValue>
		<DefaultValue name="enableExportAll">true</DefaultValue>
		<DefaultValue name="noResultsJspPathHit">/WEB-INF/jsp/commons/noResults.jsp</DefaultValue>
	</DefaultValues>

</Widget>
