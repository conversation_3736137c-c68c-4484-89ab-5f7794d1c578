var FullScreenWidget = function(widget, options) {
	this.init(widget, options);
	return this;
};

(function(){
	
	
	var fullScreenButton = $('<i/>', {
		'class': ['widgetHeaderButton', FullScreenWidget.BASE_ICON_CSS_CLASS, FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS].join(' ')
	});
	
	var defaults = {
		button: null,
		onShow: null,
		onHide: null
	};
	
	FullScreenWidget.CSS_CLASS = 'full-size-active';
	FullScreenWidget.BASE_ICON_CSS_CLASS = 'fonticon';
	FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS = 'fonticon-resize-full';
	FullScreenWidget.EXIT_FULL_SCREEN_ICON_CSS_CLASS = 'fonticon-resize-small';
	FullScreenWidget.WIDGET_RELOAD = 'reload.fullScreenWidget';
	
	FullScreenWidget.prototype.init = function(widget, options) {
		this.options = $.extend({}, defaults, options);
		this.widget = widget.closest('.wuid');
		
		this.button = options.button;
		if (this.button === null) {
			this.button = fullScreenButton.clone().appendTo(this.widget.children('.widgetHeader'));
		}

		if (this.options.isFullScreen) {
			var $icon = this.button.hasClass(FullScreenWidget.BASE_ICON_CSS_CLASS)
				? this.button
				: this.button.find('.' + FullScreenWidget.BASE_ICON_CSS_CLASS);
			$icon.toggleClass([FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS, FullScreenWidget.EXIT_FULL_SCREEN_ICON_CSS_CLASS].join(' '));
			this._changeText('widget.action.exitFullScreen');
		}

		this.button.on('click', $.proxy(function(e, animationTimeout){
			this.toggleFullScreen(animationTimeout);
		}, this));

		this.widget
			.on('click', $.proxy(function(e){
				var $target = $(e.target);
				if ($target.hasClass('searchWidget')) {
					this.hideFullScreen();
				}
			}, this));
		$(window)
			.on('keydown', $.proxy(function(e){
				/* Escape */
				if (e.which === 27 && this.isFullScreen()) {
					this.hideFullScreen();
				}
			}, this));
		$(document).on(FullScreenWidget.WIDGET_RELOAD, function (e, uCssId) {
			// when reloading a widget, the reference must be updated
			if (this.widget.hasClass(uCssId)) {
				this.widget = $('.' + uCssId);
			}
		}.bind(this));
	};

	FullScreenWidget.prototype.toggleFullScreen = function(animationTimeout) {
		if (!this.isFullScreen()) {
			this.showFullScreen(animationTimeout);
		} else {
			this.hideFullScreen(animationTimeout);
		}
	};

	FullScreenWidget.prototype.toggleButton = function() {
		if (!this.isFullScreen()) {
			this.processButton('widget.action.fullScreen');
		} else {
			this.processButton('widget.action.exitFullScreen');
		}
	};


	FullScreenWidget.prototype.hideButton = function() {
		var $icon = this.button.hasClass(FullScreenWidget.BASE_ICON_CSS_CLASS)
			? this.button
			: this.button.find('.' + FullScreenWidget.BASE_ICON_CSS_CLASS);
		$icon.removeClass(FullScreenWidget.EXIT_FULL_SCREEN_ICON_CSS_CLASS);
		$icon.addClass(FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS);
	};
	FullScreenWidget.prototype.processButton = function(code) {
		var $icon = this.button.hasClass(FullScreenWidget.BASE_ICON_CSS_CLASS)
			? this.button
			: this.button.find('.' + FullScreenWidget.BASE_ICON_CSS_CLASS);
		$icon.toggleClass([FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS, FullScreenWidget.EXIT_FULL_SCREEN_ICON_CSS_CLASS].join(' '));
		this._changeText(code);
	};

	FullScreenWidget.prototype.isFullScreen = function(){
		return this.widget.hasClass(FullScreenWidget.CSS_CLASS);
	};

	FullScreenWidget.prototype.showFullScreen = function(animationTimeout) {
		this.process('widget.action.exitFullScreen', this.options.onShow, animationTimeout);
	};

	FullScreenWidget.prototype.hideFullScreen = function(animationTimeout) {
		this.process('widget.action.fullScreen', this.options.onHide, animationTimeout);
	};

	FullScreenWidget.prototype.process = function(code, functionToCall, animationTimeout) {
		this.widget.toggleClass(FullScreenWidget.CSS_CLASS);
		var $icon = this.button.hasClass(FullScreenWidget.BASE_ICON_CSS_CLASS)
			? this.button
			: this.button.find('.' + FullScreenWidget.BASE_ICON_CSS_CLASS);
		$icon.toggleClass([FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS, FullScreenWidget.EXIT_FULL_SCREEN_ICON_CSS_CLASS].join(' '));
		this._changeText(code);
		this.widget.closest('.grid-stack-item').toggleClass(FullScreenWidget.CSS_CLASS);
		if ($.isFunction(functionToCall)) {
			functionToCall.call(this, this.widget);
		}
		let chartIndex = this.button.closest('.highcharts.plmaCharts').find('.chart-wrapper .chart-inner .highChartsSVGWrapper').data('highchartsChart');

		$(window).trigger('plma:resize',[animationTimeout != undefined? animationTimeout : 100, chartIndex]);
	};

	FullScreenWidget.prototype._changeText = function(code){
		this.button.attr('title', FullScreenWidget.getMessage(code));
		this.button.find('.label').text(FullScreenWidget.getMessage(code));
	}
	
	FullScreenWidget.getMessage = function(code) {
		return mashupI18N.get('plmaResources', code);
	};
})();


(function($){

	$.fn.fullScreenWidget = function(options) {
		var fullScreenWidget = new FullScreenWidget(this, options);
		return fullScreenWidget;
	};
	
})(jQuery);
