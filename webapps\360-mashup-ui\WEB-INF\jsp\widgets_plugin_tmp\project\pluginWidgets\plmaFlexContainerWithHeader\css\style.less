@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/variables.less";

.flexContainerWithHeader {
  .display-flex();
  .flex-direction(column);
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  flex: 1 0 0px;
  position: relative;

  &.with-border {
    border: 1px solid @cblock-border;
  }

  &.full-size-active {
    position: fixed;
    width: 100%;
    height: 100% !important;
    top: 0;
    left: 0;
    z-index: 20000;
    padding: 20px;
    background-color: rgba(249, 249, 249, 0.9);
  }

  > .widgetHeaderWithToolbar {
    border-bottom: 1px solid #e2e4e3;
    z-index: 50;
    width: 100%;
  }

  > .widgetContent{
    width: 100%;

    > .flexContainerRow {
      .display-flex();
      .flex-direction(row);
      .flex-wrap(nowrap);
      .align-items(stretch);
      width: 100%;

      > .flexPanelsContainer-item {
        .flex-shrink(1);
        .flex-basis(0);
        min-width: 0;
      }

      > .plmaFlexContainerCol {
        .display-flex();
        .flex-direction(column);
      }
    }
  }
}

/* Cannot use LESS loops because guards are only available in LESS 1.5.0 */
.plmaFlexContainer-col-1 {
  .flex-grow(1);
}

.plmaFlexContainer-col-2 {
  .flex-grow(2);
}

.plmaFlexContainer-col-3 {
  .flex-grow(3);
}

.plmaFlexContainer-col-4 {
  .flex-grow(4);
}

.plmaFlexContainer-col-5 {
  .flex-grow(5);
}

.plmaFlexContainer-col-6 {
  .flex-grow(6);
}

.plmaFlexContainer-col-7 {
  .flex-grow(7);
}

.plmaFlexContainer-col-8 {
  .flex-grow(8);
}

.plmaFlexContainer-col-9 {
  .flex-grow(9);
}

.plmaFlexContainer-col-10 {
  .flex-grow(10);
}

.plmaFlexContainer-col-11 {
  .flex-grow(11);
}

.plmaFlexContainer-col-12 {
  .flex-grow(12);
}

.plmaFlexContainer-col-13 {
  .flex-grow(13);
}

.plmaFlexContainer-col-14 {
  .flex-grow(14);
}

.plmaFlexContainer-col-15 {
  .flex-grow(15);
}

.plmaFlexContainer-col-16 {
  .flex-grow(16);
}

.plmaFlexContainer-col-17 {
  .flex-grow(17);
}

.plmaFlexContainer-col-18 {
  .flex-grow(18);
}

.plmaFlexContainer-col-19 {
  .flex-grow(19);
}

.plmaFlexContainer-col-20 {
  .flex-grow(20);
}

.plmaFlexContainer-col-21 {
  .flex-grow(21);
}

.plmaFlexContainer-col-22 {
  .flex-grow(22);
}

.plmaFlexContainer-col-23 {
  .flex-grow(23);
}

.plmaFlexContainer-col-24 {
  .flex-grow(24);
}

.plmaFlexContainer-col-25 {
  .flex-grow(25);
}

.plmaFlexContainer-col-26 {
  .flex-grow(26);
}

.plmaFlexContainer-col-27 {
  .flex-grow(27);
}

.plmaFlexContainer-col-28 {
  .flex-grow(28);
}

.plmaFlexContainer-col-29 {
  .flex-grow(29);
}

.plmaFlexContainer-col-30 {
  .flex-grow(30);
}

.plmaFlexContainer-col-31 {
  .flex-grow(31);
}

.plmaFlexContainer-col-32 {
  .flex-grow(32);
}

.plmaFlexContainer-col-33 {
  .flex-grow(33);
}

.plmaFlexContainer-col-34 {
  .flex-grow(34);
}

.plmaFlexContainer-col-35 {
  .flex-grow(35);
}

.plmaFlexContainer-col-36 {
  .flex-grow(36);
}

.plmaFlexContainer-col-37 {
  .flex-grow(37);
}

.plmaFlexContainer-col-38 {
  .flex-grow(38);
}

.plmaFlexContainer-col-39 {
  .flex-grow(39);
}

.plmaFlexContainer-col-40 {
  .flex-grow(40);
}

.plmaFlexContainer-col-41 {
  .flex-grow(41);
}

.plmaFlexContainer-col-42 {
  .flex-grow(42);
}

.plmaFlexContainer-col-43 {
  .flex-grow(43);
}

.plmaFlexContainer-col-44 {
  .flex-grow(44);
}

.plmaFlexContainer-col-45 {
  .flex-grow(45);
}

.plmaFlexContainer-col-46 {
  .flex-grow(46);
}

.plmaFlexContainer-col-47 {
  .flex-grow(47);
}

.plmaFlexContainer-col-48 {
  .flex-grow(48);
}

.plmaFlexContainer-col-49 {
  .flex-grow(49);
}

.plmaFlexContainer-col-50 {
  .flex-grow(50);
}

.plmaFlexContainer-col-51 {
  .flex-grow(51);
}

.plmaFlexContainer-col-52 {
  .flex-grow(52);
}

.plmaFlexContainer-col-53 {
  .flex-grow(53);
}

.plmaFlexContainer-col-54 {
  .flex-grow(54);
}

.plmaFlexContainer-col-55 {
  .flex-grow(55);
}

.plmaFlexContainer-col-56 {
  .flex-grow(56);
}

.plmaFlexContainer-col-57 {
  .flex-grow(57);
}

.plmaFlexContainer-col-58 {
  .flex-grow(58);
}

.plmaFlexContainer-col-59 {
  .flex-grow(59);
}

.plmaFlexContainer-col-60 {
  .flex-grow(60);
}

.plmaFlexContainer-col-61 {
  .flex-grow(61);
}

.plmaFlexContainer-col-62 {
  .flex-grow(62);
}

.plmaFlexContainer-col-63 {
  .flex-grow(63);
}

.plmaFlexContainer-col-64 {
  .flex-grow(64);
}

.plmaFlexContainer-col-65 {
  .flex-grow(65);
}

.plmaFlexContainer-col-66 {
  .flex-grow(66);
}

.plmaFlexContainer-col-67 {
  .flex-grow(67);
}

.plmaFlexContainer-col-68 {
  .flex-grow(68);
}

.plmaFlexContainer-col-69 {
  .flex-grow(69);
}

.plmaFlexContainer-col-70 {
  .flex-grow(70);
}

.plmaFlexContainer-col-71 {
  .flex-grow(71);
}

.plmaFlexContainer-col-72 {
  .flex-grow(72);
}

.plmaFlexContainer-col-73 {
  .flex-grow(73);
}

.plmaFlexContainer-col-74 {
  .flex-grow(74);
}

.plmaFlexContainer-col-75 {
  .flex-grow(75);
}

.plmaFlexContainer-col-76 {
  .flex-grow(76);
}

.plmaFlexContainer-col-77 {
  .flex-grow(77);
}

.plmaFlexContainer-col-78 {
  .flex-grow(78);
}

.plmaFlexContainer-col-79 {
  .flex-grow(79);
}

.plmaFlexContainer-col-80 {
  .flex-grow(80);
}

.plmaFlexContainer-col-81 {
  .flex-grow(81);
}

.plmaFlexContainer-col-82 {
  .flex-grow(82);
}

.plmaFlexContainer-col-83 {
  .flex-grow(83);
}

.plmaFlexContainer-col-84 {
  .flex-grow(84);
}

.plmaFlexContainer-col-85 {
  .flex-grow(85);
}

.plmaFlexContainer-col-86 {
  .flex-grow(86);
}

.plmaFlexContainer-col-87 {
  .flex-grow(87);
}

.plmaFlexContainer-col-88 {
  .flex-grow(88);
}

.plmaFlexContainer-col-89 {
  .flex-grow(89);
}

.plmaFlexContainer-col-90 {
  .flex-grow(90);
}

.plmaFlexContainer-col-91 {
  .flex-grow(91);
}

.plmaFlexContainer-col-92 {
  .flex-grow(92);
}

.plmaFlexContainer-col-93 {
  .flex-grow(93);
}

.plmaFlexContainer-col-94 {
  .flex-grow(94);
}

.plmaFlexContainer-col-95 {
  .flex-grow(95);
}

.plmaFlexContainer-col-96 {
  .flex-grow(96);
}

.plmaFlexContainer-col-97 {
  .flex-grow(97);
}

.plmaFlexContainer-col-98 {
  .flex-grow(98);
}

.plmaFlexContainer-col-99 {
  .flex-grow(99);
}

.plmaFlexContainer-col-100 {
  .flex-grow(100);
}