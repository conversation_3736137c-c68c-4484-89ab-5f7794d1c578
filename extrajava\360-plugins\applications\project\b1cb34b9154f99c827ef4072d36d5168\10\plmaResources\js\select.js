(function (window) {
	'use strict';
	if (!window.SelectAPI) {

		/**
		 *	Bridge between PLMA widgets and the dashboardController for the selection event.
		 */
		window.SelectAPI = (function () {

			var userStorage = new StorageClient('user');

			/**
			 * Storage key containing all the user choices (UserChoice[]}.
			 * @type {String}
			 */
			var SELECT_API_STORAGE_KEY = 'plma.select_api';

			/**
			 * Object containing all the information needed to perform PubSub between the PLMA app and other 3dxp widgets.
			 * @type {Object}
			 * @example
			 * {
			 *     topicName1: {
			 *         name: topicName1, // topic name
			 *         data: data1, // business-related data
			 *         normalizer: function1, // function to create a message based on the selected hits and the data
			 *         denormalizer: function2, // function to retrieve the selected hits from the message
			 *         callbacks: [function3, function4], // functions to call when a publication is made by other 3dxp widgets
			 *         subscribed: true // whether the PLMA app is subscribed to the topic
			 *     },
			 *     ...
			 * }
			 */
			var topics = {};

			/**
			 * Performs a subscription of the PLMA app to a topic.
			 *
			 * @param {Object} topic - Topic to be subscribed
			 */
			var subscribe = function (topic) {
				if (window.dashboardController) {
					window.dashboardController.subscribeTopic(topic.name, onMessageReceive.bind(null, topic));
					topic.subscribed = true;
				}
			};

			/**
			 * Callback to call when a message is received.
			 *
			 * @param {Object} topic - Topic data to use
			 * @param {Object} message - Message to parse
			 */
			 var lastHandledMsgTS = undefined;
			var onMessageReceive = function (topic, message) {
				if (userChoice.listeningSelection && lastHandledMsgTS != message.metadata.timestamp) {
					// We receive many message again and again...need to check if we already handled the message....
					// If we remove this, we get a flikering effect...as the multiselect is keep on updating...
					// We can also handle the multiselect... if current selected == new selectedHits...
					// TEST and find better implementations....
					lastHandledMsgTS = message.metadata.timestamp;
					topic.callbacks.forEach(function (message, topic, callback) {
						callback(topic.denormalizer.call(null, message, topic.data));
					}.bind(null, message, topic));
				}
			};

			/**
			 * @typedef UserChoice
			 * @type {Object}
			 * @property {Boolean} listeningSelection - Whether to allow listening other widgets selection. Can be modified by the user.
			 * @property {Boolean} sendingSelection - Whether to allow sending selection to other widgets. Can be modified by the user.
			 * @property {String} widgetId - Identifier of the widget instance in 3dxp.
			 */

			/**
			 * @type {UserChoice}
			 */
			var userChoice = {
				listeningSelection: false,
				sendingSelection: true,
				widgetId: null
			};

			/**
			 * Returns the first object having the key property equals to value.
			 * @param {Object[]} array
			 * @param {String} key
			 * @param {*} value
			 * @returns {Object|undefined}
			 */
			var findBy = function (array, key, value) {
				return array.find(function (obj) {
					return obj[key] === value;
				});
			};

			/**
			 * Reads the user choice in the user storage.
			 */
			(function init() {
				if (window.MessageHandler) {
					MessageHandler.afterInit.done(function (dashboardController) {
						userChoice.widgetId = dashboardController.widgetInfos.widgetId;
						userStorage.get(
							SELECT_API_STORAGE_KEY,
							function (res) {
								if (res.length > 0) {
									var data = findBy(JSON.parse(res[0].value), 'widgetId', userChoice.widgetId);
									if (data) {
										userChoice = data;
									}
								}
								addTabInPreferences();
							},
							function () {
								console.error('SelectAPI: Error while initializing');
							}
						);
					});
				}
			})();

			/**
			 * After initialization, creates a tab in the preferences to control the SelectAPI.
			 */
			var addTabInPreferences = function () {
				// Create tab in preferences
				if (window.Preferences) {
					Preferences.getInitCallbacks().add(function (preferences) {
						preferences.addTab({
							onInit: onTabCreate,
							tab: $(
								'<div class="preference-tab preference-selection-tab">' +
									'<span class="tab-icon fonticon fonticon-sync"></span>' +
									'<span class="label">' + Preferences.getMessage('plma.preference.selection') + '</span>' +
								'</div>'
							),
							container: $(
								'<div class="preference-container preference-selection-container">' +
									'<div class="preference-title">' +
										'<div class="main">' + Preferences.getMessage('plma.preference.selection.title') + '</div>' +
										'<div class="description">' + Preferences.getMessage('plma.preference.selection.description') + '</div>' +
									'</div>' +
									'<div class="preference-block-container">' +
										'<div class="preference-block">' +
											'<div class="toggle toggle-switch">' +
												'<input id="' + preferences.uCssId + '_send_selection" type="checkbox" />' +
												'<label for="' + preferences.uCssId + '_send_selection" class="control-label">' + Preferences.getMessage('plma.preference.selection.send') + '</label>' +
											'</div>' +
											'<div class="toggle toggle-switch">' +
												'<input id="' + preferences.uCssId + '_listen_selection" type="checkbox" />' +
												'<label for="' + preferences.uCssId + '_listen_selection" class="control-label">' + Preferences.getMessage('plma.preference.selection.listen') + '</label>' +
											'</div>' +
										'</div>' +
									'</div>' +
								'</div>'
							)
						});
					});
				}
			};

			/**
			 * Function to call when the tab is created.
			 */
			var onTabCreate = function () {
				this.widget.find('#' + this.uCssId + '_listen_selection')
					.prop('checked', window.SelectAPI.isListeningSelection())
					.off('change')
					.change(function () {
						window.SelectAPI.setListeningSelection($(this).prop('checked'));
					});
				this.widget.find('#' + this.uCssId + '_send_selection')
					.prop('checked', window.SelectAPI.isSendingSelection())
					.off('change')
					.change(function () {
						window.SelectAPI.setSendingSelection($(this).prop('checked'));
					});
			};

			/**
			 * Stores the user choice in the user storage.
			 *
			 * @param {Object} value - Value to save
			 * @param {Function} successCallback - Success callback to call
			 * @param {Function} errorCallback - Error callback to call
			 */
			var onUserChoiceUpdate = function (value, successCallback, errorCallback) {
				var newUserChoice = $.extend({}, userChoice, value);
				userStorage.get(
					SELECT_API_STORAGE_KEY,
					function (res) {
						var data;
						if (res.length > 0) {
							data = JSON.parse(res[0].value);
							var config = findBy(data, 'widgetId', userChoice.widgetId);
							if (config) {
								$.extend(config, value);
							} else {
								data.push(newUserChoice);
							}
						} else {
							data = [newUserChoice];
						}
						userStorage.set(SELECT_API_STORAGE_KEY, JSON.stringify(data), successCallback, errorCallback);
					},
					errorCallback
				);
			};

			/**
			 * Callback to be used when the storage update fails.
			 */
			var onUserChoiceUpdateFail = function () {
				console.error('SelectAPI: error while updating');
			};

			/**
			 * Ensure the topic object is initialized.
			 *
			 * @param {String} topicName - Name of the topic
			 */
			var ensureTopic = function (topicName) {
				if (!topics[topicName]) {
					topics[topicName] = {
						name: topicName,
						data: {},
						normalizer: $.noop,
						denormalizer: $.noop,
						callbacks: []
					};
				}
			};

			return {

				/**
				 * Register a topic
				 *
				 * @param {String} topicName - Name of the topic
				 * @param {Object} data - Useful data
				 * @param {Function} normalizer - Function to create a message
				 * @param {Function} denormalizer - Function to retrieve selected hits
				 */
				addTopic: function (topicName, data, normalizer, denormalizer) {
					ensureTopic(topicName);
					$.extend(topics[topicName].data, data);
					topics[topicName].normalizer = normalizer;
					topics[topicName].denormalizer = denormalizer;
				},

				/**
				 * Subscribe a PLMA widget to particular topics
				 *
				 * @param {String[]} topicNames - Topic names
				 * @param {Function} callback - Function to call when receiving selected hits
				 */
				subscribeToTopics: function (topicNames, callback) {
					topicNames.forEach(function (topicName) {
						this.subscribeToTopic(topicName, callback);
					}, this);
				},

				/**
				 * Subscribe a PLMA widget to a topic.
				 *
				 * @param {String} topicName - Topic name
				 * @param {Function} callback - Function to call when receiving selected hits
				 */
				subscribeToTopic: function (topicName, callback) {
					ensureTopic(topicName);
					var topic = topics[topicName];
					topic.callbacks.push(callback);
					if (!topic.subscribed) {
						subscribe(topic);
					}
				},

				/**
				 * Publish selection of a PLMA widget
				 *
				 * @param {String[]} selectedHits - Array of identifiers
				 * @param {String[]} topicNames - Array of topic names
				 */
				publish: function (selectedHits, topicNames) {
					selectedHits = selectedHits || [];
					topicNames = topicNames || [];
					if (userChoice.sendingSelection) {
						topicNames.forEach(function (topicName) {
							ensureTopic(topicName);
							var topic = topics[topicName];
							if (topic && window.dashboardController) {
								window.dashboardController.publishTopic(topicName, topic.normalizer.call(this, selectedHits, topic.data));
							}
						});
					}
				},

				/**
				 * Returns true if it listen selection from 3dxp widgets, false otherwise.
				 *
				 * @returns {Boolean}
				 */
				isListeningSelection: function () {
					return userChoice.listeningSelection;
				},

				/**
				 * Set true to listen 3dxp widgets selection, false on the contrary.
				 *
				 * @param {Boolean} value
				 */
				setListeningSelection: function (value) {
					onUserChoiceUpdate(
						{listeningSelection: value},
						function () {
							userChoice.listeningSelection = value;
						},
						onUserChoiceUpdateFail
					);
				},

				/**
				 * Returns true if it can send selection to 3dxp widgets, false otherwise
				 *
				 * @returns {Boolean}
				 */
				isSendingSelection: function () {
					return userChoice.sendingSelection;
				},

				/**
				 * Set true to allow sending selection to 3dxp widgets, false on the contrary.
				 *
				 * @param {Boolean }value
				 */
				setSendingSelection: function (value) {
					onUserChoiceUpdate(
						{sendingSelection: value},
						function () {
							userChoice.sendingSelection = value;
						},
						onUserChoiceUpdateFail
					)
				}
			};
		})();

		window.SelectAPI.utils = (function () {
			return {

				/**
				 * Returns an random alphanumeric string with a defined length
				 *
				 * @param {int} length - Length of the desired string
				 * @returns {String}
				 */
				getRandomString: function (length) {
					return Math.round((Math.pow(36, length + 1) - Math.random() * Math.pow(36, length))).toString(36).slice(1);
				}
			};
		})();
	}

	window.SelectChannelHelper = (function () {
		const SEL_SEARCH_PARAM = '_selsearch';
		return {
			/*Return references(do not contain '/') as-is and paths(contains '/') filtered 
			for loadedRootIds. The paths should start from one of the rootId.*/
            getFilteredPaths: function(hitData, loadedRootIds){
				let pathsOrRefs = [];
				hitData.rawPaths.forEach(function(path){
					if(path.search('/') != -1){
						var nodes = path.split('/');
						nodes.forEach(function(n, index){
							if(loadedRootIds == undefined || loadedRootIds.includes(n)){
								pathsOrRefs.push(nodes.slice(index));
							}
						});
					}else{
						pathsOrRefs.push(path);
					}
				});
                return pathsOrRefs;
            },

            setPartsData: function(options, selectionData){
				var pathsOrRefs = [];
				let loadedRootIds = SessionContent? SessionContent.getContent() : undefined;
				if(loadedRootIds != undefined && loadedRootIds.length == 0){
					selectionData.data.paths = [];
					return selectionData;
				}
				
				options.selectedHits.forEach(function(hit){
                    // Spread operator : Merge arrays of pathsOrRefs of all the selectedHits.
                    pathsOrRefs.push(...SelectChannelHelper.getFilteredPaths(options.data[hit], loadedRootIds));
                });
				
				pathsOrRefs.forEach(function(pathOrRef){
					if(Array.isArray(pathOrRef)){
						selectionData.data.paths = [];
						selectionData.data.paths.push(pathOrRef);
					}else{
						selectionData.data.referenceIds = [];
						selectionData.data.referenceIds.push(pathOrRef);
					}				
				});
				return selectionData;
            },

            getMatchingHitIds: function(options){
				/*
                * We always get paths in the message for the part based on the Loaded Product Structure.
                * e.g [prd1, VPMInstance, subprd1, VPMInstance, part1 <, VPMRepInstance, 3dshape1>]
                * we are not adding 3DShape/VPMRepInstance in the path. hence remove from the received path
                * before checking the attributes.
                * -----------------------------------------------------------------------------
                * if path (rawpath has '/')
                *   Consider rawPath [ prd1/VPMInstance/subprd1/VPMInstance/part1 ]
                *   Find hit where the rawPath matches fully or as subpath(ending with)...
                *       Full Match = [ prd1, VPMInstance, subprd1, VPMInstance, part1]
                *       Subpath Match(ending With) = [ subprd1, VPMInstance, part1]
                *           (when sbprd1 is loaded in the 3DNavigate view, we receive this kind of path)
				*			We Can also check if the path starts with to filter all documents for selected assembly/sub-assembly.
                * -----------------------------------------------------------------------------
                * if referenceId (rawpath do not have '/')
                *   rawPath = part1
                *   In this case rawPath will contain referenceId only. Hence Only check that last element from the
                *   received path[prd1, VPMInstance, subprd1, VPMInstance, part1] is = rowPath.
                */

				let pathsToFind = options.message.data.paths;
				let nodeAttributes = options.message.data.attributes;

                let hitIds = [];
				let physicalIds = [];
				// DO this check to handle multiselect in 3D, or when selected parts parent is selected.
				// In such cases, even if one object mapped, other selected objects will be missing.
				// In this case we searchIfNotLoaded, we rerun the query to select/bring all the selected
				// Objects.
				let unmatchedCount = pathsToFind.length;
                pathsToFind.forEach(function(pathToFind){
                    let pathToFindStr = pathToFind.join('/');
					// Remove "3DShape" & "VPMRepInstance" as these are not included in the path while aggregation.
					if(	pathToFind.length > 2 &&
						nodeAttributes[pathToFind[pathToFind.length-1]].type == "3DShape" &&
						nodeAttributes[pathToFind[pathToFind.length-2]].type == "VPMRepInstance"){
						pathToFindStr = pathToFind.slice(0, pathToFind.length-2).join('/');
						physicalIds.push(pathToFind[pathToFind.length-3]);
					}else{
						physicalIds.push(pathToFind[pathToFind.length-1]);
					}
					let fullMatchFound = false;
                    _.findKey(options.data, function(hitData, hitId){
                        let hitMatched = false;
                        hitData.rawPaths.every(function(rawPathOrRef){
							if(rawPathOrRef.search('/') != -1){
								// StartsWith enable filtering of all the changes/parts of assembly(e.g. all changes for subprd1)
								// EndsWith enables filtering the change/parts, which can start from any node(e.g. subprd1,
								//		not necessorily always root assembly is loaded).
								hitMatched = rawPathOrRef.startsWith(pathToFindStr) || rawPathOrRef.endsWith(pathToFindStr);
								fullMatchFound = fullMatchFound || pathToFindStr == rawPathOrRef;
							}else{
							    hitMatched = pathToFindStr.endsWith(rawPathOrRef);
								fullMatchFound = fullMatchFound || hitMatched;
                            }
							return !hitMatched; // false = Break loop, true = Continue loop...
                        });
                        if(hitMatched){
                            hitIds.push(hitId);							
                        }
                        // Continue to find next hitIds.... hence returning 'undefined' (returning nonthing...)
                    });
					if(fullMatchFound) {
						unmatchedCount--;
					}
                });

				// As hitIds are searched in the loaded hits, so can be empty although the index has parts/objetcs. 
				// Hence returning physicalIds aswell if caller wants to reload page with filter query for the physical Ids received.
				return {
					hitIds: hitIds,
					physicalIds: physicalIds,
					fullyMatched: unmatchedCount == 0
				};
            },
			/**
			 * When objects in 3DNavigate are selected, but not loaded in current exalead context(result list) then
			 * you can use this functionality. This will generate query using the selected physicalIds and reloads the page
			 * also add the history for back navigation..
			 * @param {Object} matches - As returned by getMatchingHitIds 
			 * @param {Boolean} force - force search or not. Set true, when you want to always perform search whether the id is present 
			 *						In current hits or not.
			 * @param {String} queryPrefix - prefix to perform search e.g. 'change_physicalproductpath'
			 * @returns nothing Reloads the page by applying query.
			 */			
			searchIfNotLoaded: function(matches, force, queryPrefix){
				if(matches.hitIds == null || (!force && matches.fullyMatched)){
					return;
				}
				
				if(document[SEL_SEARCH_PARAM]){
					// Still loading the last redirect. This happens because there are duplicate selection events received.
					// plmaResultList & multiSelection widget both register the selection channels.
					return;
				}
				
				// This is called when we are already loaded the objects mathich the last selection.
				// Now we can have 2 cases. 1. selection is empty, or 2. other object is selected.
				if(matches.physicalIds.length == 0){
					// empty selection
					return;
				}
				
				let q = queryPrefix + ':(';
				matches.physicalIds.forEach(function(pid, index){
					q += '"' + pid + '"'
					if(index+1 != matches.physicalIds.length){
						q += ' OR ';
					}
				});
				q += ')'; 
				
				// Check if the selection is changed. If changed rerun the query with current ids.
				var url = new BuildUrl(window.location.href);
				let insideSelSearch = url.getParameter(SEL_SEARCH_PARAM) != undefined && url.getParameter('q') != undefined;
				if(insideSelSearch){
					let qVal = url.getParameter('q')[0];
					if(qVal == q){
						return; // As the query match, no need to reload.
					}
				}
				$('body').showSpinner();
				if(BackButton && !insideSelSearch){ // Add History....
					BackButton.addHistoryEntry({
						url: window.location.href,
						label: mashupI18N.get('plmaResources', 'plma.selectapi.selsearch.history.label'),
						icon: 'fonticon fonticon-target',
						dashboardPage: true
					});
				}
				document[SEL_SEARCH_PARAM] = true;
				window.location.assign('?q=' + q + '&' + SEL_SEARCH_PARAM + '=t');
			},
			
			normalizer: function (options) {
				var infos = window.dashboardController.widgetInfos;
				var selectionData = {
					metadata: {
						uid: SelectAPI.utils.getRandomString(4),
						originUid: infos.widgetId,
						timestamp: (new Date()).getTime(),
						appid: infos.appId,
						originWidgetId: infos.widgetId
					},
					data: {
						tenant: infos.tenantId,
						version: '1.1'
					}
				};
				SelectChannelHelper.setPartsData(options, selectionData);
				
				if(options.reframe && selectionData.data.paths && selectionData.data.paths.length > 0){
					// Zoom to the parts and load the parts if not already loaded(3DNavigate loads small parts when we zooom
					// to that area, perf optimization)
					// We perform this only when we have paths. with referenceIds the 'reframe' not found working correctly.
					let reframeData = JSON.parse(JSON.stringify(selectionData));
					reframeData.data.intent = 'reframe';
					dashboardController.publishTopic('DS/PADUtils/PADCommandProxy/focus', reframeData); 
				}
				return selectionData;
			},

			denormalizer: function (options) {
				var infos = window.dashboardController.widgetInfos;
				var matches = {
					hitIds: null,
					physicalIds: null
				};
				if (options.message.metadata.originUid === infos.widgetId || options.message.metadata.appid === infos.appId) {
					return matches;
				}
				matches = SelectChannelHelper.getMatchingHitIds(options);
				return matches;
			}
        }
    })();
	
	window.ObjectChannelHelper = (function () {
		const SEL_SEARCH_PARAM = '_selsearch';
		return {
			normalizer: function (options) {
				var infos = window.dashboardController.widgetInfos;
				var selectionData = {
					metadata: {
						uid: SelectAPI.utils.getRandomString(4),
						originUid: infos.widgetId,
						timestamp: (new Date()).getTime(),
						appid: infos.appId,
						originWidgetId: infos.widgetId
					},
					data: {
						tenant: infos.tenantId,
						version: '1.1'
					}
				};
				
				selectionData.data.attributes = {};
				selectionData.data.paths = [];
				options.selectedHits.forEach(function (hit) {
					var physicalId = options.data[hit].physicalId;
					selectionData.data.attributes[physicalId] = { id: physicalId };
					selectionData.data.paths.push([physicalId]);
				});
				return selectionData;
			},

			denormalizer: function (options) {
				var infos = window.dashboardController.widgetInfos;
				var matches = {
					hitIds: null,
					physicalIds: null
				};
				if (options.message.metadata.originUid === infos.widgetId || options.message.metadata.appid === infos.appId) {
					return matches;
				}
				matches.hitIds = [];
				matches.physicalIds = [];
				let unmatchedCount = options.message.data.paths.length;
				options.message.data.paths.forEach(function (path) {
					let hit = undefined;
					if(Array.isArray(path)){
						matches.physicalIds.push(path[0]);
						hit = _.findKey(options.data, {physicalId: path[0]});
					}else{
						matches.physicalIds.push(path);
						hit = _.findKey(options.data, {physicalId: path});
					}
					if(hit){
						matches.hitIds.push(hit);
						unmatchedCount--;
					}
				});
				matches.fullyMatched = unmatchedCount == 0;
				return matches;
			},
			
			searchIfNotLoaded: function(matches, force, message){
				if(matches.hitIds == null || (!force && matches.fullyMatched)){
					return;
				}
				
				if(document[SEL_SEARCH_PARAM]){
					// Still loading the last redirect. This happens because there are duplicate selection events received.
					// plmaResultList & multiSelection widget both register the selection channels.
					return;
				}
				// This is called when we are already loaded the objects mathich the last selection.
				// Now we can have 2 cases. 1. selection is empty, or 2. other object is selected.
				if(matches.physicalIds.length == 0){
					// empty selection
					return;
				}
				
				let attributes = message.data.attributes;
				let q = 'name:(';
				matches.physicalIds.forEach(function(pid, index){
					q += '"' + attributes[pid].name + '"'
					if(index+1 != matches.physicalIds.length){
						q += ' OR ';
					}
				});
				q += ')'; 
				
				// Check if the selection is changed. If changed rerun the query with current ids.
				var url = new BuildUrl(window.location.href);
				let insideSelSearch = url.getParameter(SEL_SEARCH_PARAM) != undefined;
				if(insideSelSearch){
					let qVal = url.getParameter('q')[0];
					if(qVal == q){
						return; // As the query match, no need to reload.
					}
				}
				$('body').showSpinner();
				if(BackButton && !insideSelSearch){ // Add History....
					BackButton.addHistoryEntry({
						url: window.location.href,
						label: mashupI18N.get('plmaResources', 'plma.selectapi.selsearch.history.label'),
						icon: 'fonticon fonticon-target',
						dashboardPage: true
					});
				}
				document[SEL_SEARCH_PARAM] = true;
				window.location.assign('?q=' + q + '&' + SEL_SEARCH_PARAM + '=t');
			}
        }
    })();
})(window);