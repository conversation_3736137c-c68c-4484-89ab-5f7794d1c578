var PLMALightbox = (function () {
	/* Constants and templates */
	var defaults = {
		containerSelector: '#mainWrapper', /* Where to append the overlay and the box */
		container: null, /* jQuery DOM element to append the overlay and the box to - overrides containerSelector */
		showOverlay: true, /* whether grey out the rest of the container */
		title: null, /* Lightbox title ; if null, no header displayed */
		content: null, /* jQuery DOM element to display in the box */
		extraCss: '', /* CSS classes to add to the box */
		triggerButton: null, /* The jQuery DOM element that will trigger the lightbox to show up */
		triggerEvent: "click", /* The event, triggerred on the button, that will make the lightbox show up */
		onInit: $.noop, /* A function applied when the lightbox inits */
		onShow: $.noop, /* A function applied when the lightbox opens */
		onHide: $.noop, /* A function applied when the lightbox closes */
		draggable: false /* Whether to enable dragging. jQueryUI must be included. */
	};
	var wrapper = $('<div class="plmalightbox plmalightbox-wrapper hidden"></div>')
	var overlay = $('<div class="plmalightbox-overlay"></div>');
	var box = $('<div class="plmalightbox-box">' +
		'<div class="plmalightbox-header">' +
		'<div class="plmalightbox-close fonticon fonticon-cancel" title="' + mashupI18N.get('plmaResources', 'widget.action.close') + '"></div>' +
		'</div>' +
		'<div class="plmalightbox-contentwrapper"></div>' +
		'</div>');

	/* Constructor */
	return function (options) {

		this.options = $.extend({}, defaults, options);
		this.wrapper = wrapper.clone();
		this.overlay = overlay.clone();
		this.box = box.clone();
		this.init();
	}


})();

PLMALightbox.prototype.init = function () {
	this.header = this.box.children(".plmalightbox-header");
	this.contentWrapper = this.box.children(".plmalightbox-contentwrapper");
	this.content = this.options.content;
	this.button = this.options.triggerButton;
	this.event = this.options.triggerEvent ? this.options.triggerEvent : "click";
	this.wrapper.data('uCssId', this.options.uCssId);

	/* Filling the box */
	this.box.addClass(this.options.extraCss);
	if (this.options.title !== null) {
		this.header.prepend(this.options.title);
	} else {
		this.header.detach();
		this.hideButton = $('<span class="lightbox-hide-button fonticon fonticon-cancel"></span>');
		this.box.append(this.hideButton);
	}
	this.contentWrapper.append(this.content);

	/* Putting the box in the DOM */
	if (!this.options.containerSelector) {
		throw new Error('PLMALightbox : no container defined.')
	}
	if (this.options.container instanceof $) {
		this.container = this.options.container
	} else {
		this.container = $(this.options.containerSelector);
	}
	if (this.container.css('position') === 'static'
		|| this.container.css('position') === 'initial') {
		this.container.css('position', 'relative');
	}

	if (this.options.showOverlay) {
		this.overlay.addClass("visible");
	}

	this.wrapper.append(this.overlay);
	this.wrapper.append(this.box);
	this.container.append(this.wrapper);

	if (this.options.draggable && $.fn.draggable) {
		this.box.draggable({
			cursor: 'move',
			containment: 'parent'
		});
	}

	/* Binding to the button */
	this.isShown = false;
	if (this.button != null) {
		this.button.off();
		this.button.on(this.options.triggerEvent + '.plmalightbox', $.proxy(function () {
			if (this.isShown) {
				this.hide();
			} else {
				this.show();
			}
		}, this));
	}
	this.overlay.on('click', $.proxy(this.hide, this));
	if (this.hideButton) {
		this.hideButton.on('click', $.proxy(this.hide, this));
	}
	this.header.find('.plmalightbox-close').on('click', $.proxy(this.hide, this));
	this.options.onInit.call(this);
};

PLMALightbox.prototype.show = function () {
	this.wrapper.removeClass('hidden');
	this.isShown = true;
	this.options.onShow.call(this);
};

PLMALightbox.prototype.hide = function () {
	this.wrapper.addClass('hidden');
	this.isShown = false;
	this.options.onHide.call(this);
};

PLMALightbox.prototype.remove = function () {
	this.wrapper.remove();
	if(this.button){
		this.button.off(this.options.triggerEvent + '.plmalightbox');
	}
	this.isShown = false;
};

/**
 * jQuery plugin.
 * Usage : $("#myButton").plmalightbox($("#myContent"), options)
 * or : $("#myButton").plmalightbox({
 * 	content: $("#myContent"),
 * 	...
 * })
 * To call methods such as 'hide' on an existing
 * lightbox : $('#myButton).plmalightbox('hide');
 */
$.fn.plmalightbox = function (content, options) {
	if (content instanceof $) {
		if (typeof options === "undefined") {
			options = {};
		}
		options.triggerButton = this;
		options.content = content;
		this.data('plmalightbox', new PLMALightbox(options));
	} else if ($.isPlainObject(content)) {
		content.triggerButton = this;
		this.data('plmalightbox', new PLMALightbox(content));
	} else if (typeof content == 'string') {
		var lightbox = this.data('plmalightbox');
		if (lightbox) {
			if (typeof lightbox[content] === 'function') {
				lightbox[content].call(lightbox, options);
			} else {
				throw new Error('No such method available on PLMALightbox: ' + content);
			}
		} else {
			throw new Error('No PLMALightbox defined on this object', this);
		}
	}
	return this;
};
