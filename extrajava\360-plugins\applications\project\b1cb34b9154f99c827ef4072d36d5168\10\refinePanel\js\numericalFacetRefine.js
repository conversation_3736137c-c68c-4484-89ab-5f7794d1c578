var PLMANumericalRefine = function (options) {
	this.options = options;
	this.widget = $('.' + this.options.uCssId + ' .filter-list-facet-elem-numerical.filter-list-facet-elem-' + this.options.facet);
	this.sliderContainer = this.widget.find('.facet-container-list-numerical-slider-' + this.options.facet);
	this.slider = this.sliderContainer.find('.refine-panel-numerical-slider');
	this.refineButton = this.sliderContainer.find('.slider-button.slider-refine-button');
	this.excludeButton = this.sliderContainer.find('.slider-button.slider-exclude-button');
	this.textMinValue = this.sliderContainer.find('.min-value');
	this.textMaxValue = this.sliderContainer.find('.max-value');
	this.textValue = this.sliderContainer.find('.value');
	this.currentUrl = new BuildUrl(window.location.href);
};

PLMANumericalRefine.COLLAPSIBLE_COOKIE_NAME = 'refine-panel-numerical-collapsible';

PLMANumericalRefine.prototype.init = function () {
	this.initSlider();
	this.initEvents();
	this.initTextInput();
	this.initCollapsibleCookie();
};

PLMANumericalRefine.prototype.initSlider = function () {
	var refine = this.getRefine();
	var extremes = this.getExtremes(refine);
	this.textMinValue.val(extremes.min);
	this.textMaxValue.val(extremes.max);
	var min = parseFloat(this.options.min);
	if (min > extremes.min) {
		min = extremes.min;
	}
	var max = parseFloat(this.options.max);
	if (max < extremes.max) {
		max = extremes.max;
	}
	this.slider.slider({
		range: true,
		min: min,
		max: max,
		values: [parseFloat(extremes.min), parseFloat(extremes.max)],
		step: parseFloat(this.options.step),
		slide: function (event, ui) {
			this.textMinValue.val(ui.values[0]);
			this.textMaxValue.val(ui.values[1]);
		}.bind(this)
	});

	this.setRangeBounds(min,max);

	this.activateRefine(refine);
};

PLMANumericalRefine.prototype.setRangeBounds = function (min, max) {
	this.widget.find('.slider-min-value').text(min);
	this.widget.find('.slider-max-value').text(max);
};

PLMANumericalRefine.prototype.getRefine = function () {
	var refines = [];
	this.options.feedList.forEach(function (feed) {
		refines = refines.concat(this.currentUrl.getParameter(feed + BuildUrl.REFINE_CATEGORY_PARAMETER));
	}, this);
	for (var i = 0; i < refines.length; i++) {
		var refine = refines[i];
		if (refine && refine.indexOf(this.options.facet) !== -1 && refine.split('/').length === 3 && refine.split('/')[2].split(';').length === 2) {
			return refine;
		}
	}

	return undefined;
};

PLMANumericalRefine.prototype.getExtremes = function (refine) {
	/* If facet is refined from min to a value, set the slider to the corresponding value */
	var result = {};
	if (refine) {
		var min = parseFloat(refine.split('/')[2].split(';')[0].replace('[', ''));
		var max = parseFloat(refine.split('/')[2].split(';')[1].replace(']', ''));
		result.min = min;
		result.max = max;
		return result;
	}

	return {min: this.options.min, max: this.options.max};
};

PLMANumericalRefine.prototype.isExclude = function (refine) {
	return refine && refine.indexOf('-f') !== -1
};

PLMANumericalRefine.prototype.createRefineBlock = function () {
	var label = '[ ' + this.textMinValue.val() + ' ; ' + this.textMaxValue.val() + ' ]';
	var refineBlock = $('<div class="slider-refine">' +
		'<span class="slider-refine-label">' + label + '</span>' +
		'<span class="slider-refine-value"></span>' +
		'<span class="fonticon fonticon-cancel"></span>' +
		'</div>');

	this.widget.find('.slider-refine-container').removeClass('hidden').append(refineBlock);
};

PLMANumericalRefine.prototype.activateRefine = function (refine) {
	var isExclude = this.isExclude(refine);
	if (refine && isExclude) {
		this.slider.find('.ui-slider-range').addClass('exclude');
		this.excludeButton.addClass('active');
		this.createRefineBlock();
		this.widget.find('.slider-refine-container .slider-refine').addClass('exclude');
	} else if (refine) {
		this.refineButton.addClass('active');
		this.createRefineBlock();
	}
};

PLMANumericalRefine.prototype.initEvents = function () {
	this.refineButton.on('click', function () {
		if (!this.refineButton.hasClass('disabled')) {
			this.options.feedList.forEach(function (feed) {
				this.removeParameters(feed);
				this.currentUrl.addParameter(feed + BuildUrl.REFINE_CATEGORY_PARAMETER, '+f/' + this.options.facet + '/[' + this.textMinValue.val() + ';' + this.textMaxValue.val() + ']');
			}, this);
			window.location.replace(this.currentUrl.toString());
		}
	}.bind(this));
	this.excludeButton.on('click', function () {
		if (!this.excludeButton.hasClass('disabled')) {
			this.options.feedList.forEach(function (feed) {
				this.removeParameters(feed);
				this.currentUrl.addParameter(feed + BuildUrl.REFINE_CATEGORY_PARAMETER, '-f/' + this.options.facet + '/[' + this.textMinValue.val() + ';' + this.textMaxValue.val() + ']');
			}, this);
		}
		window.location.replace(this.currentUrl.toString());
	}.bind(this));
	this.sliderContainer.on('click', '.fonticon-cancel', function () {
		this.options.feedList.forEach(function (feed) {
			this.removeParameters(feed);
		}, this);
		window.location.replace(this.currentUrl.toString());
	}.bind(this));
};

PLMANumericalRefine.prototype.removeParameters = function (feed) {
	this.currentUrl.removeParameterWithPrefixedValue_(feed + BuildUrl.REFINE_CATEGORY_PARAMETER, '+f/' + this.options.facet);
	this.currentUrl.removeParameterWithPrefixedValue_(feed + BuildUrl.REFINE_CATEGORY_PARAMETER, 'f/' + this.options.facet);
	this.currentUrl.removeParameterWithPrefixedValue_(feed + BuildUrl.REFINE_CATEGORY_PARAMETER, '-f/' + this.options.facet);
};

PLMANumericalRefine.prototype.initTextInput = function () {
	/* Set the input to editable and check for modifications */
	this.textValue.on('change', function (e) {
		/* Check if the value is right */
		var minValue = this.textMinValue.val();
		var maxValue = this.textMaxValue.val();
		if (!isNaN(parseFloat(minValue)) && parseFloat(minValue) <= parseFloat(maxValue)) {
			/* Set the value into the slider */
			this.excludeButton.removeClass('disabled');
			this.refineButton.removeClass('disabled');
			this.textMaxValue.removeClass('error');
			this.textMinValue.removeClass('error');
			this.slider.slider("option", "values", [parseFloat(minValue), parseFloat(maxValue)]);
		} else {
			/* Disable refine/exclude button */
			this.excludeButton.addClass('disabled');
			this.refineButton.addClass('disabled');
			this.textMaxValue.addClass('error');
			this.textMinValue.addClass('error');
			e.preventDefault();
		}
	}.bind(this));
};

PLMANumericalRefine.prototype.initCollapsibleCookie = function () {
	/* Get initial cookie value to display or hide facet */
	var cookieVal = $.cookie(PLMANumericalRefine.COLLAPSIBLE_COOKIE_NAME);
	if (cookieVal !== null && typeof cookieVal !== 'undefined' && cookieVal !== '') {
		cookieVal = JSON.parse(cookieVal);
	} else {
		cookieVal = {};
	}
	if (cookieVal[this.facet] === true) {
		this.widget.find('.icon-collapse-button').toggleClass('hidden');
		this.sliderContainer.toggleClass('hidden');
	}

	/* Init click button to hide facet*/
	this.widget.find('.sub-header-collapsible').click(function () {
		this.widget.find('.icon-collapse-button').toggleClass('hidden');
		this.sliderContainer.toggleClass('hidden');
		/* get cookies */
		var cookie = JSON.parse($.cookie(PLMANumericalRefine.COLLAPSIBLE_COOKIE_NAME));
		/* update cookies */
		if (!cookie) {
			cookie = {};
		}
		cookie[this.facet] = this.widget.find('.icon-collapsible-button').hasClass('hidden');
		$.cookie(PLMANumericalRefine.COLLAPSIBLE_COOKIE_NAME, JSON.stringify(cookie), {expires: 30});
	}.bind(this));
};
