<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="menu" uri="http://www.exalead.com/jspapi/widget-menu" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import parameters="items,uCssId,type"/>

<request:getCookieValue var="showPanel" name="sideBarMenu_showPanel" defaultValue=""/>
<plma:currentPageId var="currentPage"/>

<c:set var="currentSection" value=""/>
<c:forEach items="${items}" var="item" varStatus="itemStatus">
    <c:if test="${item.section != currentSection}">
        <c:set var="currentSection" value="${item.section}"/>
        <render:template template="../plmaMenu/templates/section.jsp">
            <render:parameter name="section" value="${currentSection}"/>
        </render:template>
    </c:if>

    <c:set var="isLink" value="${not empty item.target && not fn:startsWith(item.target, 'panel:')}"/>
    <c:set var="isPanelToggle" value="${fn:startsWith(item.target, 'panel:')}"/>
    <c:set var="panelClassId" value="${isPanelToggle ? fn:replace(item.target, 'panel:', '') : ''}"/>
    <c:set var="itemCssId" value="menu-item-${type}-${itemStatus.index}"/>
    <c:set var="descCssId" value="${type}-desc-${itemStatus.index}"/>

    <c:set var="url" value=""/>
    <c:if test="${isLink}">
        <plma:menuItemUrl var="url" config="${item}" ignoreParams="chartboardId,pageId"/>
        <%--
        <url:url var="url" value="${item.target}" keepQueryString="${item.keepQueryString == true}">
            Removing user pages info
            <url:parameter name="chartboardId" value="" override="true"/>
            <url:parameter name="pageId" value="" override="true"/>
        </url:url> --%>
    </c:if>

    <render:template template="../plmaMenu/templates/item.jsp">
        <render:parameter name="uCssId" value="${uCssId}"/>
        <render:parameter name="itemCssId" value="${itemCssId}"/>
        <render:parameter name="descCssId" value="${descCssId}"/>
        <render:parameter name="pageId" value="${item.target}"/>
        <render:parameter name="extraCss"
                          value="${itemCssId}  ${(item.id == null && currentPage == item.target) || currentPage == item.id || (isPanelToggle && showPanel == panelClassId) ? 'active':''} ${isLink ? 'isLink' : ''} ${isPanelToggle ? 'isPanelToggle' : ''} indent-${not empty item.indent ? item.indent:'0'}"/>
        <render:parameter name="label" value="${item.label}"/>
        <render:parameter name="description" value="${item.description}"/>
        <render:parameter name="togglePanel" value="${panelClassId}"/>
        <render:parameter name="url" value="${url}"/>
        <render:parameter name="isFolding" value="${item.folding}"/>
        <render:parameter name="iconCss" value="${item.iconCss}"/>
        <render:parameter name="iconUrl" value="${item.iconUrl}"/>
        <render:parameter name="section" value="${item.section}"/>
        <render:parameter name="onClick" value="${item.onClick}"/>
        <render:parameter name="type" value="${type}"/>
    </render:template>
</c:forEach>
