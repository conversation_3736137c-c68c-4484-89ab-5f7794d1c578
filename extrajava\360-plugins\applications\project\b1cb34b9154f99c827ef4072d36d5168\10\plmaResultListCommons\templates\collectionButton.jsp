<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import parameters="collection,hitId,buttonPrefix,actionName" ignore="true"/>

<plma:hasCollectionItem var="hasCollectionItem" id="${hitId}" collection="${collection}"/>

<c:choose>
    <c:when test="${hasCollectionItem}">
        <span class="hit-action selected hit-action-${actionName} fonticon fonticon-${buttonPrefix}-on ${actionName}-${hitId}" data-id="${hitId}" data-status="on" data-collection="${collection}"></span>
    </c:when>
    <c:otherwise>
        <span class="hit-action hit-action-${actionName} fonticon fonticon-${buttonPrefix}-off ${actionName}-${hitId}" data-id="${hitId}" data-status="off" data-collection="${collection}"></span>
    </c:otherwise>
</c:choose>

<render:renderScript position="READY">
    var options = {};
    options.actionName = '${actionName}';
    options.url = '<c:url value="/collections"/>';
    options.collection = '${collection}';
    options.buttonPrefix = '${buttonPrefix}';
    options.hitId = '${hitId}';
    options.callbackFunction = function(data){};
    new ResultListCollectionManager(options);
</render:renderScript>