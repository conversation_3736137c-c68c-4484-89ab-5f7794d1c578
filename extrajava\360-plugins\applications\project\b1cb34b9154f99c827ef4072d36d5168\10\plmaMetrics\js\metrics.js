
var refineFromMetrics = function(newRefine){
	var url = new BuildUrl(window.location.href);
	url.addParameter(newRefine.split('=')[0],newRefine.split('=')[1]);
		
	window.location.replace(url.toString());
};

var refinesFromMetrics = function(newRefines){
	var url = new BuildUrl(window.location.href);
	var newRefinesTab = newRefines.split(',');
	for(var j=0; j<newRefinesTab.length; j++){
		url.addParameter(newRefinesTab[j].split('=')[0],newRefinesTab[j].split('=')[1]);
	}
	window.location.replace(url.toString());
};


(function() {
		
})(jQuery);
