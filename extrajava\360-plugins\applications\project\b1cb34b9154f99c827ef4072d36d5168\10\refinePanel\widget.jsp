<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="displayHits" uri="http://www.exalead.com/displayHits-widget-helpers" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds"/>
<render:import parameters="refinePanelCfg,refinesPanelId,refinesPanelMode" ignore="true"/>

<%-- By default, if this widget is not called from layout but configured widget, this mode is empty, sot set it to 'widget' --%>
<c:if test="${empty refinesPanelMode}">
    <c:set var="refinesPanelMode" value="widget"/>
</c:if>

<%-- Try to get refine panel Id from widget config if empty... (not triggered through 'render:template') --%>
<c:if test="${empty refinesPanelId}">
    <plma:getOptionComposite var="refinesPanelId" name="externalConfig" key="refinesPanelId" defaultValue=""/>
</c:if>

<%-- Try to get refine panel config from widget config if empty... Config can be present in widget parameter in JSON format if set by layout --%>
<c:if test="${empty refinePanelCfg}">
    <plma:getConfig var="refinePanelCfg" id="refine_panel" widget="${widget}"/>
</c:if>

<%-- If refines panel ID is still empty, try to get it from refine panel config (supposed to be present in Ajax mode) --%>
<c:if test="${empty refinesPanelId && not empty refinePanelCfg.refinesPanelId}">
    <c:set var="refinesPanelId" value="${refinePanelCfg.refinesPanelId}"/>
</c:if>

<c:set var="suggestCfg" value="${refinePanelCfg.search}"/>

<widget:widget varCssId="cssId" varUcssId="uCssId" extraCss="refinePanel refines">
    <%-- Suggest placeholder is MEL expression, eval from --%>
    <string:eval string="${suggestCfg.placeHolder}" var="placeHolder" feeds="${feeds}"/>

    <plma:getFacetSuggestContext var="facetsSuggestContext" feeds="${feeds}"/>

    <c:choose>
        <c:when test="${refinePanelCfg.useExternalisedConfig}">
            <plma:refinesPanelConfig var="allFacets" refinesPanelId="${refinesPanelId}"/>
        </c:when>
        <c:otherwise>
            <plma:refineFacetsOldConfig var="allFacets" feeds="${feeds}"/>
        </c:otherwise>
    </c:choose>


    <div class="spinner-container">
        <div class="spinner"></div>
    </div>

    <div class="refine-main-container">
            <%--		<c:if test="${not isSuggestSearch}">--%>
        <div class="searchWidget searchFormRefine">
            <c:if test="${refinePanelCfg.displayCloseBtn}">
                <!-- Add Close Button -->
                <div class="closeButton">
                    <i18n:message var="closeButtonTooltip" code="refinepanel.toolbar.item.cancel.tooltip" text="Close Refine Panel"/>
                    <span class="fonticon fonticon-fast-forward" title="${closeButtonTooltip}"></span>
                </div>
            </c:if>

            <!-- Add collapse Button -->
            <div class="collapseButton">
                <i18n:message var="collapseButtonTooltip" code="refinepanel.toolbar.item.collapseall.tooltip" text="Collapse all"/>
                <span class="fonticon fonticon-collapse-all" title="${collapseButtonTooltip}"></span>
            </div>
            <!-- Add expand Button -->
            <div class="expandButton">
                <i18n:message var="expandButtonTooltip" code="refinepanel.toolbar.item.expandall.tooltip" text="Expand all"/>
                <span class="fonticon fonticon-expand-all" title="${expandButtonTooltip}"></span>
            </div>

                <%-- Search form --%>
            <div class="searchContainer">
                <c:if test="${refinePanelCfg.enableSuggest}">
                    <div class="searchFormContent">
                        <input type="text"
                               name="${cssId}"
                               value="${facetsSuggestContext.suggestContext ? facetsSuggestContext.suggestQuery : ''}"
                               class="searchInput"
                               placeholder="${placeHolder}"/>
                        <i18n:message var="searchFilterText" code="widgets.refinePanel.search-filter.placeholder"
                                      text="Search filters..."/>
                        <div class="searchButton fonticon fonticon-search-filter" title="${searchFilterText}">
                        </div>
                    </div>

                    <c:choose>
                        <c:when test="${suggestCfg.suggestApiAction == 'access'}">
                            <render:renderScript position="READY">
                                var options = {};
                                options.cssId = '${cssId}';
                                options.feedName = '${feedName}';
                                options.refinesPanelId = '${refinesPanelId}';
                                options.baseSuggestURL = '<c:url value="/plma/suggest/facets"/>/<search:getPageName/>';
                                options.baseAjaxReload = '<c:url value="/plma/ajax/refine"/>/<search:getPageName/>/${refinesPanelMode}';
                                options.feeds = ${plma:getKeys(feeds, '[]')};
                                options.wuid = '${widget.wuid}';
                                options.pageName = '<url:getPageName/>';
                                options.minLength = '${suggestCfg.minLength}';
                                options.suggestFacets = '${plma:getSuggestFacets(allFacets)}';
                                options.paramNameReload = '${refinePanelCfg.paramNameReload}';
                                options.dispatcherName = '${refinePanelCfg.search.dispatcherName}';
                                options.delayTime = '${refinePanelCfg.search.delayTime}';
                                options.loadDateFacetParameter = '';
                                options.loadFacetParameter = '<plma:getConstantValueFromNameTag constantName="CLEAN_FEED_PARAMETER"/>';
                                options.maxSuggests = ${suggestCfg.maxSuggests};
                                <c:if test="${refineMode.equals('Event')}">
                                    options.refineEventMode = true;
                                </c:if>
                                var refinePanelSearch = new RefinePanelSearchAccess('${uCssId}', options);
                            </render:renderScript>
                        </c:when>
                        <c:otherwise>
                            <render:renderScript position="READY">
                                var options = {};
                                options.paramNameReload = '${refinePanelCfg.paramNameReload}';
                                options.hasSuggest = ${refinePanelCfg.enableSuggest};
                                options.cssId = '${cssId}';
                                options.suggestUrl = '<c:url value="/plma/suggest/widget/${feedName}"/>';
                                options.feedName = '${feedName}';
                                options.refinesPanelId = '${refinesPanelId}';
                                options.baseAjaxReload = '<c:url value="/plma/ajax/refine"/>/<search:getPageName/>/${refinesPanelMode}';
                                options.feeds = ${plma:getKeys(feeds, '[]')};
                                options.wuid = '${widget.wuid}';
                                options.loadFacetParameter = '<plma:getConstantValueFromNameTag constantName="CLEAN_FEED_PARAMETER"/>';
                                options.pageName = '<url:getPageName/>';
                                options.pageId = '<request:getParameterValue name="pageId" defaultValue=""/>';
                                options.customDelimiter = '${suggestCfg.customDelimiter}';
                                options.minLength = '${suggestCfg.minLength}';
                                var refinePanelSearch = new RefinePanelSearch('${uCssId}', options);
                            </render:renderScript>
                        </c:otherwise>
                    </c:choose>
                </c:if>
            </div>
        </div>
            <%--		</c:if>--%>

        <c:set var="facetTemplateDirectory" value="templates/default"/>
        <c:set var="noResults" value="true"/>

        <div class="is-filter-activated" style="display: none;">${refinePanelCfg.activateFilter}</div>
        <div class="activation-filter-min" style="display: none;">${refinePanelCfg.filterMin}</div>

        <request:getCookieValue var="cookieVal" name="refineWidget" defaultValue=""/>
        <string:unescape var="cookieVal" escapeType="URL">${cookieVal}</string:unescape>
        <string:split var="cookieValues" separator=";" string="${cookieVal}"/>
        <c:set var="cookieClassName" value="table-collapsable"/>

        <i18n:message code="plma.tools.search" var="searchI18n"/>
        <i18n:message code="refines.noresult" var="noResultI18n"/>

        <div class="widgetContent facets">
            <div class="overlay-facets"></div>
            <div id="currentLangRefinePanel" style="display: none;"><i18n:getLang/></div>
            <c:forEach items="${allFacets}" var="facetConfig">
                <c:set var="isShow" value="true"/>
                <c:if test="${facetConfig.displayCondition != null && not empty facetConfig.displayCondition.expr}">
                    <string:eval var="isShow" string="${facetConfig.displayCondition.expr}" feeds="${feeds}"/>
                </c:if>

                <c:choose>
                    <c:when test="${isShow && facetConfig.type == 'facet'}">
                        <search:forEachFacet
                                var="facet"
                                feeds="${feeds}"
                                filterMode="INCLUDE"
                                facetsList="${facetConfig.id}"
                                showEmptyFacets="${refinePanelCfg.showEmptyFacets}">
                            <c:remove var="noResults"/>
                            <render:template template="${facetTemplateDirectory}/normalFilter.jsp"
                                             defaultTemplate="${facetTemplateDirectory}/normalFilter.jsp" widget="refinePanel">
                                <render:parameter name="facet" value="${facet}"/>
                                <render:parameter name="widget" value="${widget}"/>
                                <render:parameter name="feeds" value="${feeds}"/>
                                <render:parameter name="uCssId" value="${uCssId}"/>
                                <render:parameter name="facetConfig" value="${facetConfig}"/>
                                <render:parameter name="isSuggestSearch" value="${facetsSuggestContext.suggestContext}"/>
                                <render:parameter name="facetsSuggestContext" value="${facetsSuggestContext}"/>
                                <render:parameter name="suggestApiAction" value="${suggestCfg.suggestApiAction}"/>
                                <render:parameter name="refinePanelCfg" value="${refinePanelCfg}"/>
                                <render:parameter name="suggestCfg" value="${suggestCfg}"/>
                                <render:parameter name="refinesPanelId" value="${refinesPanelId}"/>
                                <render:parameter name="refinesPanelMode" value="${refinesPanelMode}"/>
                            </render:template>
                        </search:forEachFacet>
                    </c:when>
                    <c:when test="${isShow && facetConfig.type == 'date'}">
                        <c:if test="${not facetsSuggestContext.suggestContext}">
                            <c:remove var="noResults"/>
                            <c:set var="facet" value="${fn:trim(facetConfig.id)}"/>
                            <div class="filter-list-facet-elem filter-list-facet-elem-date filter-list-facet-elem-${facet}">
                                <div class="facet-value-container">
                                    <div class="facet-container-list">
                                        <table class="facet table table-layout table-facets table-hover ${cookieClassName} table-list-facet-${facet}">
                                            <tr class="currentPageNumber" style="display: none;">
                                                <td>1</td>
                                            </tr>
                                            <render:template template="${facetTemplateDirectory}/dateFilter.jsp"
                                                             defaultTemplate="${facetTemplateDirectory}/dateFilter.jsp" widget="refinePanel">
                                                <render:parameter name="widget" value="${widget}"/>
                                                <render:parameter name="facet" value="${facet}"/>
                                                <render:parameter name="accessFeeds" value="${feeds}"/>
                                                <render:parameter name="uCssId" value="${uCssId}"/>
                                            </render:template>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </c:if>
                    </c:when>
                    <c:when test="${isShow && facetConfig.type == 'range'}">
                        <c:if test="${not facetsSuggestContext.suggestContext}">
                            <c:remove var="noResults"/>
                            <render:template template="${facetTemplateDirectory}/numericalFilter.jsp"
                                             defaultTemplate="${facetTemplateDirectory}/numericalFilter.jsp" widget="refinePanel">
                                <render:parameter name="widget" value="${widget}"/>
                                <render:parameter name="feeds" value="${feeds}"/>
                                <render:parameter name="facet" value="${facetConfig.id}"/>
                                <render:parameter name="min" value="${facetConfig.min}"/>
                                <render:parameter name="max" value="${facetConfig.max}"/>
                                <render:parameter name="step" value="${facetConfig.step}"/>
                                <render:parameter name="uCssId" value="${uCssId}"/>
                                <render:parameter name="refinePanelCfg" value="${refinePanelCfg}"/>
                            </render:template>
                        </c:if>
                    </c:when>
                </c:choose>
            </c:forEach>
        </div>
    </div>

    <div class="subwidgets">
        <render:subWidgets/>
    </div>

</widget:widget>

<render:renderScript position="READY">
    <render:renderOnce id="initRefinesWidget">
        refinesWidget.initI18N({
        singular: "<i18n:message code="refines.singular"/>",
        plural: "<i18n:message code="refines.plural"/>"
        });
    </render:renderOnce>
    refinesWidget.initToggle($('#${cssId}'));
    refinesWidget.initMenu($('#${cssId}'), '${uCssId}');
    <c:if test="${refinePanelCfg.displayCloseBtn}">
        refinesWidget.initCloseButtonBehavior($('#${cssId}'));
    </c:if>
    <c:if test="${refinePanelCfg.disjunctive}">
        var options = {};
        options.paramNameReload = '${refinePanelCfg.paramNameReload}';
        options.loadDateFacetParameter = '';
        options.loadFacetParameter = '<plma:getConstantValueFromNameTag constantName="CLEAN_FEED_PARAMETER"/>';
        refinesWidget.initCheckboxes($('#${cssId}'),options);
    </c:if>
    <c:if test="${refinePanelCfg.enableDragging}">
        refinesWidget.enableDragging();
    </c:if>
</render:renderScript>
