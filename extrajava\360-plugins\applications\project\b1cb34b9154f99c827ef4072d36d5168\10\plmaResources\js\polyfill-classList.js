(function(){

	/* Utils */
	function addClass(element, classList, klass){
		if (classList.indexOf(klass) == -1){
			classList.push(klass);
			if (element.className) {
				element.className += ' ' + klass;	
			} else {
				element.className = klass;
			}
		}
	}
	function removeClass(element, classList, klass){
		var i = classList.indexOf(klass);
		if (i > -1){
			classList.splice(i, 1);
			element.className = classList.join(' ');	
		}
	}
	function toggleClass(element, classList, klass){
		var i = classList.indexOf(klass);
		if (i > -1){
			removeClass(element, classList, klass);
			return false;
		}else{
			addClass(element, classList, klass);
			return true;
		}
	}
	
	
	/**
	 * If Element.classList is defined, returns it, else create an object that mimics it and returns it.
	 * If the 'force' argument is provided and is true, act as if Element.classList were not defined.
	 *  */
	Element.prototype.getClassList = function(force){
		if (this.classList && !force){
			return this.classList;
		}else{
			var classList;
			if (this.className !== undefined && this.className.split) {
				classList = this.className.split(/\s+/);
			} else {
				/* The selected element does not support className (e.g. a SVG node),
				 * return an empty list */
				classList = [];
			}
			var element = this;
			
			/**
			 * Add specified class values. If these classes already exist in attribute of the element, then they are ignored.
			 */
			classList.add = function(){
				for (var i=0; i < arguments.length; i++){
					addClass(element, this, arguments[i]);
				}
			};
			/**
			 * Remove specified class values.
			 */
			classList.remove = function(){
				for (var i=0; i < arguments.length; i++){
					removeClass(element, this, arguments[i]);
				}
			};
			/**
			 * Return class value by index in collection.
			 */
			classList.item = function(index){
				return this[index];
			}
			/**
			 * When only one argument is present: Toggle class value; i.e., if class exists then remove it and return false, if not, then add it and return true.
			 * When a second argument is present: If the second argument is true, add specified class value, and if it is false, remove it.
			 */
			classList.toggle = function(klass, force){
				if (typeof force === 'boolean'){
					if (force){
						this.add(klass);
					}else{
						this.remove(klass);
					}
				}else{
					return toggleClass(element, this, klass);
				}
			};
			/**
			 * Checks if specified class value exists in class attribute of the element.
			 */
			classList.contains = function(klass){
				return (this.indexOf(klass) > -1);
			}
			
			return classList;
		}
	}
})();
