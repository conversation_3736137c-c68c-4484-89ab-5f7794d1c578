<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>

<render:import varWidget="widget" varFeeds="feeds" parameters="uCssId" />
<config:getOption name="minWidth" var="minWidth"/>

<div class="row">
	<widget:forEachSubWidget >
		<render:widget /> <%-- No extraCss option to set the 'item' class :( --%>
	</widget:forEachSubWidget>
</div>

<%-- Setting the 'item' class afterwards on widgets --%>
	<render:renderScript position="READY">
		var childrenWidgets = $('.'+'${uCssId}'+'.flexbox-container > .row > .wuid');
		childrenWidgets.addClass('item');
		for(var i=0 ; i<childrenWidgets.length ; i++){
			childrenWidgets[i].style.minWidth = '${minWidth}';
		}
		$('.flexbox-container > .row > .wuid').style
		if (typeof Highcharts !== 'undefined'){
			$(window).resize();
		}
	</render:renderScript>
