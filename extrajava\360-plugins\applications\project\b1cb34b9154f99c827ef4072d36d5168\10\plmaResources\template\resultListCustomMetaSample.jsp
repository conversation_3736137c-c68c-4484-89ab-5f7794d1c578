<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>

<render:import parameters="accessFeeds,widget,uCssId,isGridTable"/>
<render:import parameters="feed,entry" ignore="true"/>

<c:if test="${empty entry}">
	<search:getMetaLabel var="metaLabel" metaName="state_start_complete"/>
	<div class="${isGridTable? 'th' : ''} column table-column" data-column-name="state_start_complete" 
		data-colgroup-id="custom-grp-0" data-column-show="true" data-column-type="String" title="${metaLabel}">
		<span class="label">${metaLabel}</span>
	</div>
</c:if>

<c:if test="${not empty entry}">
	<c:set var="metaValue" value=""/>
	<c:set var="rawMetaValue" value=""/>	<%-- Value with no highlight --%>
	<c:set var="metaLabel" value=""/>
	<search:getMeta var="meta" metaName="state_start_complete" entry="${entry}"/>
	<search:getMetaLabel var="metaLabel" metaName="state_start_complete"/>
	<search:forEachMetaValue meta="${meta}" entry="${entry}" var="value" varRaw="rawValue" varStatus="loop">
		<c:set var="metaValue" value="${metaValue}${value}"/>
		<c:set var="rawMetaValue" value="${rawMetaValue}${rawValue}"/>
		<c:if test="${not loop.last}"><c:set var="metaValue" value="${metaValue}, "/></c:if>
		<c:if test="${not loop.last}"><c:set var="rawMetaValue" value="${rawMetaValue}, "/></c:if>
	</search:forEachMetaValue>
	<span class="${isGridTable? 'td' : ''} table-column hitMeta" data-column-name="state_start_complete" 
		data-colgroup-id="custom-grp-0" data-column-show="true" data-column-type="String" title="${metaLabel}: ${rawMetaValue}">
		<span class="metaValue">${metaValue}</span>
	</span>
</c:if>