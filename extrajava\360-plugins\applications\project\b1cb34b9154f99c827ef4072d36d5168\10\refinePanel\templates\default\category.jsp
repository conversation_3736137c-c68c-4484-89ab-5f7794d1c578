<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<render:import varWidget="widget" parameters="category,feeds,refinePanelCfg"/>
<render:import parameters="categoryUrl" ignore="true"/>
<render:import parameters="facetLabel" ignore="true"/>
<render:import parameters="catLabel" ignore="true"/>
<render:import parameters="depthLevel" ignore="true"/>

<plma:getFacetSuggestContext var="facetsSuggestContext" feeds="${feeds}"/>

<c:set var="catId" value="${fn:split(category.id, '/')}"/>
<c:set var="catIdLength" value="${fn:length(catId)}"/>
<c:set var="categoryLabelConcat" value=""/>
<c:set var="joinItem" value=""/>

<c:choose>
    <c:when test="${refinePanelCfg.onHoverInfo}">
        <plma:getCategoryPathTitle var="catHoverTitle" category="${category}" feeds="${feeds}"/>
        <a class="refine-link" data-url="${categoryUrl}" data-categoryid="${category.id}"
           title="${facetLabel}: ${plma:escapeHTML(catHoverTitle)}">
            <plma:getCategoryLabel category="${category}" feeds="${feeds}" suggestContext="${facetsSuggestContext}"/>
        </a>
    </c:when>
    <c:otherwise>
        <a class="refine-link" data-url="${categoryUrl}" data-categoryid="${category.id}">
            <plma:getCategoryLabel category="${category}" feeds="${feeds}" suggestContext="${facetsSuggestContext}"/>
        </a>
    </c:otherwise>
</c:choose>


