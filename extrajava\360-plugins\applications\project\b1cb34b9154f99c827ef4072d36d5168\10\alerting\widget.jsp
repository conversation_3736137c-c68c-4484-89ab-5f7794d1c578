<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="security" uri="http://www.exalead.com/jspapi/security" %>

<render:import varWidget="widget" varFeeds="feeds"/>

<config:getOption var="dbKey" name="dbKey" defaultValue="" />
<config:getOption name="waitForTrigger" var="waitForTrigger" defaultValue="false" />
<config:getOption var="alertingWuid" name="alertingWuid" />
<config:getOption var="lightboxContainer" name="lightboxContainer" defaultValue="#mainWrapper"/>

<security:getUser var="user"/>

<div class="alertingTemplates hidden">
    <render:template template="templates/form.jsp"/>
    <render:template template="templates/buttons.jsp"/>
    <render:template template="templates/subscription.jsp"/>
    <render:template template="templates/listItems.jsp"/>
    <render:template template="templates/preferencesTab.jsp"/>
</div>

<render:renderScript position="READY">
    var options = {
        user: '${user.login}'
    };
    options.waitForTrigger = "${waitForTrigger}";

    <%-- render alert btn on plmaChart widget --%>
    AlertingPopup.utils.renderAlertBtn(options);

    <%--  render popup after alert btn click event --%>
    AlertingPopup.utils.renderPopupAfterClick(options);

    <%-- Add tab in preferences --%>
    new Alerting(options);
</render:renderScript>
