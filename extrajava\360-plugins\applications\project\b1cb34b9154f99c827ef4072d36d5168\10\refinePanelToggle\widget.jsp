<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import varWidget="widget" />

<request:getCookieValue var="refinesContainer_displayState" name="refinesContainer_displayState" defaultValue="expanded"/>

<widget:widget varUcssId="uCssId" varCssId="cssId" disableStyles="true">
    <div class="refineButtonToggle state-${refinesContainer_displayState}">
        <span class="fonticon fonticon-filter"></span>
        <span class="fonticon fonticon-filter-delete"></span>
    </div>
</widget:widget>

<render:renderScript position="READY">
    var options = {};
    new RefinePanelToggle(options);
</render:renderScript>