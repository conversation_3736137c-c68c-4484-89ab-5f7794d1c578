@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/styles/responsive.less";
@import "../../plmaResources/css/styles/variables.less";

.mashup.mashup-style .icons-div > div.preferred-hits.compact-header {
  .collection-header {
    .display-flex();

    .label-section {
      .title {
        font-size: 18px;
        color: @clink-active;
        vertical-align: middle;
        display: none;
      }

      .fonticon {
        .counter {
          width: min-content;
          font-size: 10px;
          position: relative;
          margin-left: -10px;
          color: #3d3d3d;
          background-color: white;
          border: 1px solid;
          padding: 1px 6px;
          border-radius: 9px;
        }
      }
    }

    // if count = 0 --> don't display this div
    .count-aware[data-count="0"] {
      display: none;
    }
  }
}
