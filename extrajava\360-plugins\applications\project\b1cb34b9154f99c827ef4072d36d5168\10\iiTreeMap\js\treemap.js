(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory(require('react'), require('charts')) :
        typeof define === 'function' && define.amd ? define(['react', 'charts'], factory) :
            (global = global || self, global.WidgetTreemap = factory(global.React, global.iiChart));
}(this, function (React, charts) { 'use strict';

    var React__default = 'default' in React ? React['default'] : React;

    function _extends() {
        _extends = Object.assign || function (target) {
            for (var i = 1; i < arguments.length; i++) {
                var source = arguments[i];

                for (var key in source) {
                    if (Object.prototype.hasOwnProperty.call(source, key)) {
                        target[key] = source[key];
                    }
                }
            }

            return target;
        };

        return _extends.apply(this, arguments);
    }

    function _inheritsLoose(subClass, superClass) {
        subClass.prototype = Object.create(superClass.prototype);
        subClass.prototype.constructor = subClass;
        subClass.__proto__ = superClass;
    }

    function unwrapExports (x) {
        return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
    }

    function createCommonjsModule(fn, module) {
        return module = { exports: {} }, fn(module, module.exports), module.exports;
    }

    var reactIs_production_min = createCommonjsModule(function (module, exports) {

        Object.defineProperty(exports, "__esModule", {
            value: !0
        });
        var b = "function" === typeof Symbol && Symbol.for,
            c = b ? Symbol.for("react.element") : 60103,
            d = b ? Symbol.for("react.portal") : 60106,
            e = b ? Symbol.for("react.fragment") : 60107,
            f = b ? Symbol.for("react.strict_mode") : 60108,
            g = b ? Symbol.for("react.profiler") : 60114,
            h = b ? Symbol.for("react.provider") : 60109,
            k = b ? Symbol.for("react.context") : 60110,
            l = b ? Symbol.for("react.async_mode") : 60111,
            m = b ? Symbol.for("react.concurrent_mode") : 60111,
            n = b ? Symbol.for("react.forward_ref") : 60112,
            p = b ? Symbol.for("react.suspense") : 60113,
            q = b ? Symbol.for("react.memo") : 60115,
            r = b ? Symbol.for("react.lazy") : 60116;

        function t(a) {
            if ("object" === typeof a && null !== a) {
                var u = a.$$typeof;

                switch (u) {
                    case c:
                        switch (a = a.type, a) {
                            case l:
                            case m:
                            case e:
                            case g:
                            case f:
                            case p:
                                return a;

                            default:
                                switch (a = a && a.$$typeof, a) {
                                    case k:
                                    case n:
                                    case h:
                                        return a;

                                    default:
                                        return u;
                                }

                        }

                    case r:
                    case q:
                    case d:
                        return u;
                }
            }
        }

        function v(a) {
            return t(a) === m;
        }

        exports.typeOf = t;
        exports.AsyncMode = l;
        exports.ConcurrentMode = m;
        exports.ContextConsumer = k;
        exports.ContextProvider = h;
        exports.Element = c;
        exports.ForwardRef = n;
        exports.Fragment = e;
        exports.Lazy = r;
        exports.Memo = q;
        exports.Portal = d;
        exports.Profiler = g;
        exports.StrictMode = f;
        exports.Suspense = p;

        exports.isValidElementType = function (a) {
            return "string" === typeof a || "function" === typeof a || a === e || a === m || a === g || a === f || a === p || "object" === typeof a && null !== a && (a.$$typeof === r || a.$$typeof === q || a.$$typeof === h || a.$$typeof === k || a.$$typeof === n);
        };

        exports.isAsyncMode = function (a) {
            return v(a) || t(a) === l;
        };

        exports.isConcurrentMode = v;

        exports.isContextConsumer = function (a) {
            return t(a) === k;
        };

        exports.isContextProvider = function (a) {
            return t(a) === h;
        };

        exports.isElement = function (a) {
            return "object" === typeof a && null !== a && a.$$typeof === c;
        };

        exports.isForwardRef = function (a) {
            return t(a) === n;
        };

        exports.isFragment = function (a) {
            return t(a) === e;
        };

        exports.isLazy = function (a) {
            return t(a) === r;
        };

        exports.isMemo = function (a) {
            return t(a) === q;
        };

        exports.isPortal = function (a) {
            return t(a) === d;
        };

        exports.isProfiler = function (a) {
            return t(a) === g;
        };

        exports.isStrictMode = function (a) {
            return t(a) === f;
        };

        exports.isSuspense = function (a) {
            return t(a) === p;
        };
    });
    unwrapExports(reactIs_production_min);
    var reactIs_production_min_1 = reactIs_production_min.typeOf;
    var reactIs_production_min_2 = reactIs_production_min.AsyncMode;
    var reactIs_production_min_3 = reactIs_production_min.ConcurrentMode;
    var reactIs_production_min_4 = reactIs_production_min.ContextConsumer;
    var reactIs_production_min_5 = reactIs_production_min.ContextProvider;
    var reactIs_production_min_6 = reactIs_production_min.Element;
    var reactIs_production_min_7 = reactIs_production_min.ForwardRef;
    var reactIs_production_min_8 = reactIs_production_min.Fragment;
    var reactIs_production_min_9 = reactIs_production_min.Lazy;
    var reactIs_production_min_10 = reactIs_production_min.Memo;
    var reactIs_production_min_11 = reactIs_production_min.Portal;
    var reactIs_production_min_12 = reactIs_production_min.Profiler;
    var reactIs_production_min_13 = reactIs_production_min.StrictMode;
    var reactIs_production_min_14 = reactIs_production_min.Suspense;
    var reactIs_production_min_15 = reactIs_production_min.isValidElementType;
    var reactIs_production_min_16 = reactIs_production_min.isAsyncMode;
    var reactIs_production_min_17 = reactIs_production_min.isConcurrentMode;
    var reactIs_production_min_18 = reactIs_production_min.isContextConsumer;
    var reactIs_production_min_19 = reactIs_production_min.isContextProvider;
    var reactIs_production_min_20 = reactIs_production_min.isElement;
    var reactIs_production_min_21 = reactIs_production_min.isForwardRef;
    var reactIs_production_min_22 = reactIs_production_min.isFragment;
    var reactIs_production_min_23 = reactIs_production_min.isLazy;
    var reactIs_production_min_24 = reactIs_production_min.isMemo;
    var reactIs_production_min_25 = reactIs_production_min.isPortal;
    var reactIs_production_min_26 = reactIs_production_min.isProfiler;
    var reactIs_production_min_27 = reactIs_production_min.isStrictMode;
    var reactIs_production_min_28 = reactIs_production_min.isSuspense;

    var reactIs_development = createCommonjsModule(function (module, exports) {
    });
    unwrapExports(reactIs_development);
    var reactIs_development_1 = reactIs_development.typeOf;
    var reactIs_development_2 = reactIs_development.AsyncMode;
    var reactIs_development_3 = reactIs_development.ConcurrentMode;
    var reactIs_development_4 = reactIs_development.ContextConsumer;
    var reactIs_development_5 = reactIs_development.ContextProvider;
    var reactIs_development_6 = reactIs_development.Element;
    var reactIs_development_7 = reactIs_development.ForwardRef;
    var reactIs_development_8 = reactIs_development.Fragment;
    var reactIs_development_9 = reactIs_development.Lazy;
    var reactIs_development_10 = reactIs_development.Memo;
    var reactIs_development_11 = reactIs_development.Portal;
    var reactIs_development_12 = reactIs_development.Profiler;
    var reactIs_development_13 = reactIs_development.StrictMode;
    var reactIs_development_14 = reactIs_development.Suspense;
    var reactIs_development_15 = reactIs_development.isValidElementType;
    var reactIs_development_16 = reactIs_development.isAsyncMode;
    var reactIs_development_17 = reactIs_development.isConcurrentMode;
    var reactIs_development_18 = reactIs_development.isContextConsumer;
    var reactIs_development_19 = reactIs_development.isContextProvider;
    var reactIs_development_20 = reactIs_development.isElement;
    var reactIs_development_21 = reactIs_development.isForwardRef;
    var reactIs_development_22 = reactIs_development.isFragment;
    var reactIs_development_23 = reactIs_development.isLazy;
    var reactIs_development_24 = reactIs_development.isMemo;
    var reactIs_development_25 = reactIs_development.isPortal;
    var reactIs_development_26 = reactIs_development.isProfiler;
    var reactIs_development_27 = reactIs_development.isStrictMode;
    var reactIs_development_28 = reactIs_development.isSuspense;

    var reactIs = createCommonjsModule(function (module) {

        {
            module.exports = reactIs_production_min;
        }
    });

    /*
    object-assign
    (c) Sindre Sorhus
    @license MIT
    */
    /* eslint-disable no-unused-vars */

    var getOwnPropertySymbols = Object.getOwnPropertySymbols;
    var hasOwnProperty = Object.prototype.hasOwnProperty;
    var propIsEnumerable = Object.prototype.propertyIsEnumerable;

    function toObject(val) {
        if (val === null || val === undefined) {
            throw new TypeError('Object.assign cannot be called with null or undefined');
        }

        return Object(val);
    }

    function shouldUseNative() {
        try {
            if (!Object.assign) {
                return false;
            } // Detect buggy property enumeration order in older V8 versions.
            // https://bugs.chromium.org/p/v8/issues/detail?id=4118


            var test1 = new String('abc'); // eslint-disable-line no-new-wrappers

            test1[5] = 'de';

            if (Object.getOwnPropertyNames(test1)[0] === '5') {
                return false;
            } // https://bugs.chromium.org/p/v8/issues/detail?id=3056


            var test2 = {};

            for (var i = 0; i < 10; i++) {
                test2['_' + String.fromCharCode(i)] = i;
            }

            var order2 = Object.getOwnPropertyNames(test2).map(function (n) {
                return test2[n];
            });

            if (order2.join('') !== '0123456789') {
                return false;
            } // https://bugs.chromium.org/p/v8/issues/detail?id=3056


            var test3 = {};
            'abcdefghijklmnopqrst'.split('').forEach(function (letter) {
                test3[letter] = letter;
            });

            if (Object.keys(Object.assign({}, test3)).join('') !== 'abcdefghijklmnopqrst') {
                return false;
            }

            return true;
        } catch (err) {
            // We don't expect any of the above to throw, but better to be safe.
            return false;
        }
    }

    var objectAssign = shouldUseNative() ? Object.assign : function (target, source) {
        var from;
        var to = toObject(target);
        var symbols;

        for (var s = 1; s < arguments.length; s++) {
            from = Object(arguments[s]);

            for (var key in from) {
                if (hasOwnProperty.call(from, key)) {
                    to[key] = from[key];
                }
            }

            if (getOwnPropertySymbols) {
                symbols = getOwnPropertySymbols(from);

                for (var i = 0; i < symbols.length; i++) {
                    if (propIsEnumerable.call(from, symbols[i])) {
                        to[symbols[i]] = from[symbols[i]];
                    }
                }
            }
        }

        return to;
    };

    /**
     * Copyright (c) 2013-present, Facebook, Inc.
     *
     * This source code is licensed under the MIT license found in the
     * LICENSE file in the root directory of this source tree.
     */

    var ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';
    var ReactPropTypesSecret_1 = ReactPropTypesSecret;

    var has = Function.call.bind(Object.prototype.hasOwnProperty);

    function emptyFunction() {}

    function emptyFunctionWithReset() {}

    emptyFunctionWithReset.resetWarningCache = emptyFunction;

    var factoryWithThrowingShims = function factoryWithThrowingShims() {
        function shim(props, propName, componentName, location, propFullName, secret) {
            if (secret === ReactPropTypesSecret_1) {
                // It is still safe when called from React.
                return;
            }

            var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');
            err.name = 'Invariant Violation';
            throw err;
        }
        shim.isRequired = shim;

        function getShim() {
            return shim;
        }
        // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.

        var ReactPropTypes = {
            array: shim,
            bool: shim,
            func: shim,
            number: shim,
            object: shim,
            string: shim,
            symbol: shim,
            any: shim,
            arrayOf: getShim,
            element: shim,
            elementType: shim,
            instanceOf: getShim,
            node: shim,
            objectOf: getShim,
            oneOf: getShim,
            oneOfType: getShim,
            shape: getShim,
            exact: getShim,
            checkPropTypes: emptyFunctionWithReset,
            resetWarningCache: emptyFunction
        };
        ReactPropTypes.PropTypes = ReactPropTypes;
        return ReactPropTypes;
    };

    var propTypes = createCommonjsModule(function (module) {
        /**
         * Copyright (c) 2013-present, Facebook, Inc.
         *
         * This source code is licensed under the MIT license found in the
         * LICENSE file in the root directory of this source tree.
         */
        {
            // By explicitly using `prop-types` you are opting into new production behavior.
            // http://fb.me/prop-types-in-prod
            module.exports = factoryWithThrowingShims();
        }
    });

    var WidgetTreemap =
        /*#__PURE__*/
        function (_Component) {
            _inheritsLoose(WidgetTreemap, _Component);

            function WidgetTreemap(props) {
                var _this;

                _this = _Component.call(this, props) || this;
                _this.wrapper = React__default.createRef();
                _this.state = {
                    tooltip: null
                };
                return _this;
            }

            var _proto = WidgetTreemap.prototype;

            _proto.render = function render() {
                var _this2 = this;

                var _this$props = this.props,
                    height = _this$props.height,
                    data = _this$props.data,
                    paddingInner = _this$props.paddingInner,
                    paddingOuter = _this$props.paddingOuter,
                    onClick = _this$props.onClick,
                    customColors = _this$props.customColors,
                    highlight = _this$props.highlight,
                    width = _this$props.width,
                    hideLabel = _this$props.hideLabel;
                var tooltip = this.state.tooltip;
                var displayLabels = this.props.displayLabels || [];
                var events = {};
                var spacing = {
                    x: 0,
                    y: 0
                };

                if (tooltip && !tooltip.type) {
                    spacing = {
                        x: 10,
                        y: 10
                    };
                }

                if (onClick) {
                    events.onClick = function (e, node) {
                        if (node) {
                            onClick(e, node);
                        }
                    };
                }

                return React__default.createElement("div", {
                    style: {
                        position: 'relative'
                    },
                    ref: this.wrapper
                }, React__default.createElement(charts.Svg, {
                    width: width,
                    height: height
                }, React__default.createElement(charts.Treemap, _extends({
                    margin: {
                        top: 50,
                        right: 50,
                        bottom: 50,
                        left: 50
                    },
                    data: data,
                    paddingInner: paddingInner,
                    paddingOuter: paddingOuter,
                    hideLabel: hideLabel,
                    displayLabels: displayLabels,
                    colorScaleLinear: true,
                    customColors: customColors,
                    highlight: highlight,
                    onMouseMove: function onMouseMove(e, node) {
                        if (node && !displayLabels.some(function (label) {
                            return label.name === node.data.name;
                        })) {
                            var _tooltip = new charts.Utils.Tooltip(node.data.name, e.clientX, e.clientY, spacing);

                            var tooltipData = _tooltip.setType('treemap').setTooltipArea(_this2.wrapper.current).setParentSize({
                                x: e.data.meta.x,
                                y: e.data.meta.y,
                                height: e.data.meta.height,
                                width: e.data.meta.width
                            }).getData();

                            _this2.setState({
                                tooltip: tooltipData
                            });
                        } else {
                            _this2.setState({
                                tooltip: null
                            });
                        }
                    }
                }, events))), tooltip && React__default.createElement(charts.Tooltip, _extends({}, tooltip, {
                    spacing: spacing
                })));
            };

            _proto.componentDidMount = function componentDidMount() {};

            return WidgetTreemap;
        }(React.Component);

    WidgetTreemap.propTypes = {
        colors: propTypes.arrayOf(propTypes.string),
        width: propTypes.oneOfType([propTypes.string, propTypes.number]),
        paddingInner: propTypes.number,
        paddingOuter: propTypes.number,
        displayLabels: propTypes.arrayOf(propTypes.exact({
            name: propTypes.string,
            alias: propTypes.string
        })),
        onClick: propTypes.func,
        highlight: propTypes.object,
        customColors: propTypes.arrayOf(propTypes.object)
    };
    WidgetTreemap.defaultProps = {
        width: '100%'
    };

    return WidgetTreemap;

}));
