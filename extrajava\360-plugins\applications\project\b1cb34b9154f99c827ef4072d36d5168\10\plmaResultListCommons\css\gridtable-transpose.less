@header-width: 200px;
@row-height: 50px;
@fixed-col-height: min-content; /*Including toolbar height*/
@toolbar-row-height: 40px;
@row-col-width: 180px;

.mashup .result-panel .plmaResultList .results-panel{
	.scroll-container.gridtable-transpose{
		overflow:auto;
		display: grid;
		grid-auto-flow: column;
		grid-auto-columns: min-content;
		padding-top: 0px;
		padding-left:0px;
		
		&::-webkit-scrollbar-track {
			margin-left: @header-width;
			margin-top: 105px;
		}
		
		.fix-header{
			position: sticky;
			left: 0;
			z-index: 10;
			box-shadow: 5px 0px 10px 0px #b4b6ba;
		    display: grid;
		    grid-auto-flow: column;
		    grid-auto-columns: @row-col-width;
		    grid-template-columns: @header-width;

			.table-header .th {
				background: #F1F1F1;
				font-size: 12px;
				color: #3D3D3D;
				padding: 0px 5px;
				display: inline-flex;
				align-items: center;
				
				&.table-column{
					position: relative;
					.label{
						//position: inherit;
						//display: block;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
						width: 95%;
						bottom: 14px;
						padding-left: 6px;
					}
				}

				&.table-column.sortable-column{
					.label{
						width: 85%;
					}
				}
				
				&> span{
					line-height: @row-height;
					
					&.fonticon.fonticon-expand-down,
					&.fonticon.fonticon-expand-up {
						line-height: 10px;
						position: absolute;
						right: 5px;
						top: 10px;
					}
					&.fonticon.fonticon-expand-down{
						bottom: 19.5px;
						right: 0;
						top: auto;
						&:before{
							content: '\e149';
						}
					}
					&.fonticon.fonticon-expand-up{
						right: 12px;
						top: 19.6px;
						&:before{
							content: '\e148';
						}
					}
				}
				&.fixed-column{
					padding-bottom: 3px;
					.fonticon.fonticon-expand-up {
						bottom: 30px;
						top: auto;
					}
				}
			}			
		}
				
		.grid-row{
			display: grid;
			grid-auto-flow: row;
			grid-template-rows: @fixed-col-height;
			grid-auto-rows: @row-height;
			background: white;
			.fixed-column{
				position: sticky;
				top: 0;
				height: auto;
				z-index: 9;
				box-shadow: 0px 10px 16px -5px #b4b6ba;
				background: #ffffff;				
			}	
		}
		
		li.hit .hit-container.grid-row:hover{
			border:1px solid #77797c;
			border-width: 0px 1px;
			.mainColumn{
				border-bottom-color: #77797c;
			}
		}
		
		.hits.gridtable-layout{
			display: grid;
			grid-auto-flow: column;
			grid-auto-columns: @row-col-width;
			margin-top: 0px;
			padding: 0px;
		}
		
		li.hit{
			margin: 0px;
			border-width: 0px;
			.hit-container{
				overflow: inherit;
				border:1px solid #d1d4d4;
				border-width: 0px 1px;
				.mainColumn{
					border-bottom: 5px solid transparent;
					.hitContent { 
						display: none; 
					}
				}
			}	
			.subwidgets{
				height: 0px;
				div { padding: 0px; }
			}
			&.selected{
				border-width: 0px;
				.mainColumn{
					border-bottom-color: #42a2da;
				}
			}
		}
		
		.grid-row{
			.fixed-column .title-container {
				display: grid;
				grid-auto-flow: row;
				grid-template-columns: 45px;
				align-items: center;
				
				.hitThumbnail{
					grid-row: 1;
					grid-column: span 2;					
				}
				.icon-container {
					grid-row: 3;
					padding: 0px 5px 0px 5px;
				}
				.hitTitle{
					grid-row: 3;
					height: @row-height;
					line-height: @row-height;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
					display: inline-block;
					color: #368ec4;
					font-size: 16px;
				}
				.toolbarContainer{
					grid-row: 2;
					grid-column: span 2;
					height: @toolbar-row-height;
					position:relative;
					grid-auto-columns: @toolbar-row-height;
					.freezeButtonClass:hover {
                        color:#42a2da;
                    }
				}			
				
				&.with-thumbnail{
					.hitThumbnail:has(img){
						height: 80px;
						padding: 0px;
						display:inline-flex;
						align-items: center;
						justify-content: center;
					}
					.hitThumbnail:not(:has(img)){
						display: none;
					}
					.hitTitle{
						grid-column: span 2;
						.icon{
							width: 22px;
						}
					}
				}
			}
				
			.th.fixed-column .title-container .hitTitle{
				grid-column: span 2;
			}
		}
		
		.th, .td {
			border-bottom: 1px solid #e2e4e3;
			word-break: break-word;
			overflow: hidden;
		}
		.td.hitFacet, .td.hitMeta, .td.hitType {
			padding: 7px 3px 3px 12px;
			line-height: 40px;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			text-align: center;
		}
	}
}