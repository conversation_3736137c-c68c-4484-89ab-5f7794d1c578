<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="security" uri="http://www.exalead.com/jspapi/security" %>
<%@ taglib prefix="experience" uri="http://www.exalead.com/jspapi/experience" %>

<experience:is3DXP var="is3DXP" />
<url:getPageName var="pageName"/>

<render:import varWidget="widget" varFeeds="feeds" varParentEntry="parentEntry" varParentFeed="parentFeed"/>
<render:import parameters="preferencesCfg" ignore="true"/>

<c:if test="${empty preferencesCfg}">
	<plma:getConfig var="preferencesCfg" id="preferences" widget="${widget}"/>
</c:if>

<widget:widget extraCss="plma-preferences" varUcssId="uCssId">
	<plma:getApplicationConfig var="appConfig"/>
	<plma:getMenuItems varMainItems="mainItems" varSecondaryItems="secondaryItems"/>
	<plma:getSavedPages var="savedPages"/>
    <plma:toJSON object="${savedPages}" defaultValue="[]" var="jsonSavedPages"/>
    <plma:toJSON object="${mainItems}" defaultValue="[]" var="jsonMainItems"/>

	<widget:content>
		<div class="spinner-container"><div class="spinner"></div></div>
		<div class="overlay-preference"></div>
		<div class="preference-widget">
			<div class="preference-config">
				<div class="preference-tabs">
					<div class="preference-tab preference-facet-display-tab hidden"><span class="tab-icon fonticon fonticon-chart-pie"></span><span class="label">Facet Displays</span></div>
					<div class="preference-tab preference-facet-range-tab hidden"><span class="tab-icon fonticon fonticon-arrow-combo-horizontal"></span><span class="label">Facet Ranges</span></div>
					<div class="preference-tab preference-date-range-tab hidden"><span class="tab-icon fonticon fonticon-calendar"></span><span class="label">Date Ranges</span></div>
					<div class="preference-tab preference-menu-tab hidden"><span class="tab-icon fonticon fonticon-doc"></span><span class="label">Pages</span></div>
				</div>


				<div class="preference-config-flex-container">
					<div class="preference-container preference-facet-display-container"></div>
					<div class="preference-container preference-facet-range-container"></div>
					<div class="preference-container preference-date-range-container"></div>
					<div class="preference-container preference-menu-container"></div>
				</div>
			</div>
		</div>

		<div id="preference-color-tooltip" class="preference-color-tooltip" style="display: none;">
			<div class="main-column-line preference-color-column-main-grey">
				<c:forEach begin="0" end="7">
					<span class="color">&nbsp;</span>
				</c:forEach>
			</div>
			<div class="main-column-line preference-color-column-main-blue">
				<c:forEach begin="0" end="5">
					<span class="color">&nbsp;</span>
				</c:forEach>
			</div>
			<div class="main-column-line line line-1">
				<c:forEach begin="0" end="2">
					<span class="color">&nbsp;</span>
				</c:forEach>
			</div>
			<div class="main-column-line line line-2">
				<c:forEach begin="0" end="2">
					<span class="color">&nbsp;</span>
				</c:forEach>
			</div>
			<div class="main-column-line line line-3">
				<c:forEach begin="0" end="2">
					<span class="color">&nbsp;</span>
				</c:forEach>
			</div>
			<div class="main-column-line line line-4">
				<c:forEach begin="0" end="2">
					<span class="color">&nbsp;</span>
				</c:forEach>
			</div>

			<div class="preference-color-other-column">
				<c:forEach begin="0" end="20">
					<span class="color">&nbsp;</span>
				</c:forEach>
			</div>
		</div>

		<div id="preference-icon-tooltip" class="preference-icon-tooltip" style="display: none;">
			<input class="preference-icon-tooltip-input" />
			<plma:iconList var="iconList" filePath="" appName="${appName}" />
			<c:forEach var="icon" items="${iconList}">
				<span class="fonticon-elem fonticon ${icon}"></span>
			</c:forEach>
		</div>
		
		<div class="add-box-action-container hidden">
			<div class="add-box-action-overlay"></div>
			<div class="add-box-action">
				<span class="title"><span><i18n:message code="plma.preference.add.title"/> </span><span></span></span>
				<span class="value"><i18n:message code="plma.preference.add.id"/> : <input class="add-box-input" type="text" /></span>
				<span class="button"><i18n:message code="plma.preference.add.create"/></span>
			</div>
		</div>
		<div class="delete-box-action-container hidden">
			<div class="delete-box-action-overlay"></div>
		</div>
		<div class="confirm-box-action-container hidden">
			<div class="confirm-box-action-overlay"></div>
			<div class="confirm-box-action">
				<span class="title"><i18n:message code="plma.preference.reset.title"/></span>
				<span class="button button-confirm"><i18n:message code="plma.preference.reset.confirm"/></span>
				<span class="button button-cancel"><i18n:message code="plma.preference.reset.cancel"/></span>
			</div>
		</div>
		<div class="cancel-box-action-container hidden">
			<div class="cancel-box-action-overlay"></div>
			<div class="cancel-box-action">
				<span class="title"><i18n:message code="plma.preference.cancelbox.title"/></span>
				<span class="button button-confirm"><i18n:message code="plma.preference.cancelbox.confirm"/></span>
				<span class="button button-cancel"><i18n:message code="plma.preference.cancelbox.cancel"/></span>
			</div>
		</div>
	</widget:content>
	
	<render:renderScript position="READY">
		var options = {};
		options.configureTabs = ${preferencesCfg.configureTabs};
		options.tabList = ${plma:toJSArray(preferencesCfg.tab)};
		options.url = '<c:url value="/" />';
		options.sharePagesUrl = '<c:url value="/share/rights/pages"/>';
		options.suggestUrl = '<c:url value="/utils/suggest" />';
		options.pageName = '<url:getPageName />';
		options.config = ${appConfig};
		options.myPages = ${jsonSavedPages};
		options.mainItems = ${jsonMainItems};
		options.isIn3DXP = ${is3DXP};
		options.wuid = '${widget.wuid}';
		options.user = '<string:eval string="\${security.username}" isJsEscape="true" feeds="${feeds}"/>';
		if (options.isIn3DXP) {
			options.landingPageManager = new LandingPageManager(
				{
					uCssId: '${uCssId}',
					whitelist: ${plma:toJSArray(preferencesCfg.parameterWhitelist)}
				}
			);
		}
		new Preferences('${uCssId}', options);
	</render:renderScript>
</widget:widget>

