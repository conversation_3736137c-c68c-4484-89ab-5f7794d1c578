/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:36 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.refinePanel.templates.default_;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class category_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_0;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_1;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_2;

static {
  _jspx_fnmap_0= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:split", org.apache.taglibs.standard.functions.Functions.class, "split", new Class[] {java.lang.String.class, java.lang.String.class});
  _jspx_fnmap_1= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:length", org.apache.taglibs.standard.functions.Functions.class, "length", new Class[] {java.lang.Object.class});
  _jspx_fnmap_2= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:escapeHTML", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "escapeHTML", new Class[] {java.lang.String.class});
}

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(7);
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetFacetSuggestContext_0026_005fvar_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetCategoryPathTitle_0026_005fvar_005ffeeds_005fcategory_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fsuggestContext_005ffeeds_005fcategory_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fplma_005fgetFacetSuggestContext_0026_005fvar_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetCategoryPathTitle_0026_005fvar_005ffeeds_005fcategory_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fsuggestContext_005ffeeds_005fcategory_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fplma_005fgetFacetSuggestContext_0026_005fvar_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fplma_005fgetCategoryPathTitle_0026_005fvar_005ffeeds_005fcategory_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fsuggestContext_005ffeeds_005fcategory_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_render_005fimport_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_render_005fimport_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_render_005fimport_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_render_005fimport_005f4(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_plma_005fgetFacetSuggestContext_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fset_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f3(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fchoose_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(7,0) name = varWidget type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarWidget("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(7,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setParameters("category,feeds,refinePanelCfg");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fimport_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f1);
    try {
      _jspx_th_render_005fimport_005f1.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(8,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f1.setParameters("categoryUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(8,0) name = ignore type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f1.setIgnore(true);
      _jspx_th_render_005fimport_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fimport_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f2);
    try {
      _jspx_th_render_005fimport_005f2.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(9,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f2.setParameters("facetLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(9,0) name = ignore type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f2.setIgnore(true);
      _jspx_th_render_005fimport_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fimport_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f3 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f3);
    try {
      _jspx_th_render_005fimport_005f3.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(10,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f3.setParameters("catLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(10,0) name = ignore type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f3.setIgnore(true);
      _jspx_th_render_005fimport_005f3.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f3);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fimport_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f4 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f4);
    try {
      _jspx_th_render_005fimport_005f4.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(11,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f4.setParameters("depthLevel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(11,0) name = ignore type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f4.setIgnore(true);
      _jspx_th_render_005fimport_005f4.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f4);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetFacetSuggestContext_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getFacetSuggestContext
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetFacetSuggestContextTag _jspx_th_plma_005fgetFacetSuggestContext_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetFacetSuggestContextTag) _005fjspx_005ftagPool_005fplma_005fgetFacetSuggestContext_0026_005fvar_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetFacetSuggestContextTag.class);
    boolean _jspx_th_plma_005fgetFacetSuggestContext_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetFacetSuggestContext_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetFacetSuggestContext_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(13,0) name = var type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetFacetSuggestContext_005f0.setVar("facetsSuggestContext");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(13,0) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetFacetSuggestContext_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetFacetSuggestContext_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetFacetSuggestContext_005f0 = _jspx_th_plma_005fgetFacetSuggestContext_005f0.doStartTag();
        if (_jspx_th_plma_005fgetFacetSuggestContext_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetFacetSuggestContext_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetFacetSuggestContext_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetFacetSuggestContext_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetFacetSuggestContext_0026_005fvar_005ffeeds_005fnobody.reuse(_jspx_th_plma_005fgetFacetSuggestContext_005f0);
      _jspx_th_plma_005fgetFacetSuggestContext_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetFacetSuggestContext_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetFacetSuggestContext_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f0_reused = false;
    try {
      _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(15,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setVar("catId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(15,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(15,0) '${fn:split(category.id, '/')}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_0),"${fn:split(category.id, '/')}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
      if (_jspx_th_c_005fset_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
      _jspx_th_c_005fset_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f1_reused = false;
    try {
      _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(16,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setVar("catIdLength");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(16,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(16,0) '${fn:length(catId)}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_1),"${fn:length(catId)}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
      if (_jspx_th_c_005fset_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
      _jspx_th_c_005fset_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f2_reused = false;
    try {
      _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(17,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setVar("categoryLabelConcat");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(17,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(17,0) ''",_jsp_getExpressionFactory().createValueExpression("",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
      if (_jspx_th_c_005fset_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
      _jspx_th_c_005fset_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f3_reused = false;
    try {
      _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(18,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setVar("joinItem");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(18,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(18,0) ''",_jsp_getExpressionFactory().createValueExpression("",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
      if (_jspx_th_c_005fset_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
      _jspx_th_c_005fset_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent(null);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(21,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.onHoverInfo}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_plma_005fgetCategoryPathTitle_005f0(_jspx_th_c_005fwhen_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("        <a class=\"refine-link\" data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" data-categoryid=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("           title=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(':');
          out.write(' ');
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:escapeHTML(catHoverTitle)}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_2));
          out.write("\">\r\n");
          out.write("            ");
          if (_jspx_meth_plma_005fgetCategoryLabel_005f0(_jspx_th_c_005fwhen_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("        </a>\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetCategoryPathTitle_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getCategoryPathTitle
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetCategoryPathTitle _jspx_th_plma_005fgetCategoryPathTitle_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetCategoryPathTitle) _005fjspx_005ftagPool_005fplma_005fgetCategoryPathTitle_0026_005fvar_005ffeeds_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetCategoryPathTitle.class);
    boolean _jspx_th_plma_005fgetCategoryPathTitle_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetCategoryPathTitle_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetCategoryPathTitle_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(22,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryPathTitle_005f0.setVar("catHoverTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(22,8) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryPathTitle_005f0.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(22,8) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryPathTitle_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetCategoryPathTitle_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetCategoryPathTitle_005f0 = _jspx_th_plma_005fgetCategoryPathTitle_005f0.doStartTag();
        if (_jspx_th_plma_005fgetCategoryPathTitle_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetCategoryPathTitle_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetCategoryPathTitle_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetCategoryPathTitle_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetCategoryPathTitle_0026_005fvar_005ffeeds_005fcategory_005fnobody.reuse(_jspx_th_plma_005fgetCategoryPathTitle_005f0);
      _jspx_th_plma_005fgetCategoryPathTitle_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetCategoryPathTitle_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetCategoryPathTitle_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetCategoryLabel_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getCategoryLabel
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryLabelTag _jspx_th_plma_005fgetCategoryLabel_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryLabelTag) _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fsuggestContext_005ffeeds_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryLabelTag.class);
    boolean _jspx_th_plma_005fgetCategoryLabel_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetCategoryLabel_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetCategoryLabel_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(25,12) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryLabel_005f0.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(25,12) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryLabel_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(25,12) name = suggestContext type = com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.dto.FacetsSuggestContext reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryLabel_005f0.setSuggestContext((com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.dto.FacetsSuggestContext) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetsSuggestContext}", com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.dto.FacetsSuggestContext.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetCategoryLabel_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetCategoryLabel_005f0 = _jspx_th_plma_005fgetCategoryLabel_005f0.doStartTag();
        if (_jspx_th_plma_005fgetCategoryLabel_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetCategoryLabel_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetCategoryLabel_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetCategoryLabel_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fsuggestContext_005ffeeds_005fcategory_005fnobody.reuse(_jspx_th_plma_005fgetCategoryLabel_005f0);
      _jspx_th_plma_005fgetCategoryLabel_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetCategoryLabel_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetCategoryLabel_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        <a class=\"refine-link\" data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" data-categoryid=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("            ");
          if (_jspx_meth_plma_005fgetCategoryLabel_005f1(_jspx_th_c_005fotherwise_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("        </a>\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetCategoryLabel_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getCategoryLabel
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryLabelTag _jspx_th_plma_005fgetCategoryLabel_005f1 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryLabelTag) _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fsuggestContext_005ffeeds_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryLabelTag.class);
    boolean _jspx_th_plma_005fgetCategoryLabel_005f1_reused = false;
    try {
      _jspx_th_plma_005fgetCategoryLabel_005f1.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetCategoryLabel_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(30,12) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryLabel_005f1.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(30,12) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryLabel_005f1.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/category.jsp(30,12) name = suggestContext type = com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.dto.FacetsSuggestContext reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryLabel_005f1.setSuggestContext((com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.dto.FacetsSuggestContext) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetsSuggestContext}", com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.dto.FacetsSuggestContext.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetCategoryLabel_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetCategoryLabel_005f1 = _jspx_th_plma_005fgetCategoryLabel_005f1.doStartTag();
        if (_jspx_th_plma_005fgetCategoryLabel_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetCategoryLabel_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetCategoryLabel_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetCategoryLabel_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fsuggestContext_005ffeeds_005fcategory_005fnobody.reuse(_jspx_th_plma_005fgetCategoryLabel_005f1);
      _jspx_th_plma_005fgetCategoryLabel_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetCategoryLabel_005f1, _jsp_getInstanceManager(), _jspx_th_plma_005fgetCategoryLabel_005f1_reused);
    }
    return false;
  }
}
