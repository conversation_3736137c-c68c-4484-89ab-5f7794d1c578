<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>


<render:import varWidget="widget" varFeeds="feeds" />

<config:getOptionsComposite name="facetSeries" var="facetSeries" mapIndex="true" />
<config:getOption var="onClick" name="onClick" />

<widget:widget varCssId="cssId" extraStyles="width: 100%" >

	<widget:header>
		<config:getOption name="title" defaultValue="" />
	</widget:header>

	<widget:content >
		<c:choose>

			<%-- IF there are no feeds at all --%>
			<c:when test="${search:hasFeeds(feeds) == false}">
				<render:definition name="noFeeds">
					<render:parameter name="widget" value="${widget}" />
					<render:parameter name="showSuggestion" value="false" />
				</render:definition>
			</c:when>

			<c:otherwise>
				<config:getOption var="height" name="height" />

				<plma:heatmapJSON 
					var="json"
					feeds="${feeds}"
					facetSerieConfig="${facetSeries[0]}"
				/>

                <div style="height: ${height}px;">
                    <div id="chart_${cssId}" class="iiHeatmap"></div>
                </div>
               
				<render:renderScript position="READY">
					if(jQuery) {
						jQuery(window).on("plma:resize", function() {
							var event = document.createEvent("Event");

							event.initEvent("iichart:resize", true, true);
							setTimeout(function() {window.dispatchEvent(event)}, 400);
						})
					}

					var format_x = <config:getOption name="format_x"/>;
					var format_y = <config:getOption name="format_y"/>;
					var colors = "<config:getOptions name="colors"/>".replace("[", "").replace("]","");
					var json = ${json};
                    if(Object.keys(json).length === 0 && json.constructor === Object){
                        var chart = document.querySelector("#chart_${cssId}");
                        var p = document.createElement("p");
                        p.className = "no-data";
                        var text = document.createTextNode("<i18n:message code='iiHeatmap.nodata' />");
                        p.appendChild(text);
                        chart.appendChild(p)
                    }else{
                        var props = {
                            "height": ${height},
                            "onClick": ${onClick},
                            "tree": {
                                "data": json.hierarchy,
                                "dataConfig": {
                                    "id": "path",
                                    "parent": "parent",
                                    "getLabel": function(item) {
                                        return typeof format_y === "function" ? format_y(item.value) : item.value;
                                    }
                                }
                            },
                            "xLabels": {
                                "data": json.labels,
                                "dataConfig": {
                                    id: "path",
                                    "getLabel": function(item) {
                                        return typeof format_x === "function" ? format_x(item.value) : item.value;
                                    }
                                }
                            },
                            "data": json.data,
                            "cloudview":{
                                "refineUrl": window.mashup.baseUrl + "/heatmap",
                                "dataSource": "${facetSeries[0]}",
                                "feed": json.feed,
                                "page": window.location.pathname.substr(window.location.pathname.lastIndexOf('/') + 1 )
                            }
                        };

                        if (colors.length > 0) {
                            props.colors = colors.split(",");
                        }
                        ReactDOM.render(
                            React.createElement(WidgetHeatmap, props),
                            document.getElementById('chart_${cssId}')
                        );
                    }

				</render:renderScript>
				</c:otherwise>
		</c:choose>
	</widget:content>
</widget:widget>
