var StickyTop = function (uCssId) {
	this.uCssId = uCssId;
	this.widget = $('.' + uCssId);
	this.init();
};

StickyTop.prototype.init = function () {
	this.initTop = this.widget.position().top;

	this.widget.parent().scroll($.proxy(function(e){
		var scrollY = this.widget.parent().scrollTop();
		if (scrollY > this.initTop) {
			var currentTop = scrollY - this.initTop;
			this.widget.css('z-index','5');
			this.widget.css('transform','translateY(' + currentTop + 'px)');
		} else {
			this.widget.css('transform','translateY(0px)');
		}
	}, this));
};
