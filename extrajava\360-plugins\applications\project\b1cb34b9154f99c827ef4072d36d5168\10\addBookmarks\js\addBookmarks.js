(function($) {
	var defaults = {
		className : 'tag',
		classEmptyStar : 'fonticon-star-empty',
		classFullStar : 'fonticon-star',
		classWaitingStar : 'waiting-star',
		popupContent: '',
		selector : 'h3.rounded-top',
		scope : 'user',
		storageKey : 'bookmarks[]',
		action : '',
		wuid : ''
	};

	var AddBookmarks = function(base, wuid, userOptions) {
		var options = {};
		$.extend(options, defaults, userOptions);
		var self = this;
		var content = options.popupContent;
		this.savedBookmarksWuid = options.savedBookmarksWuid;
		this.db = null;
		this.widget = $(base);
		this.star = self.widget.find('.addbookmarks-star');
		var optionsPop = {};
		optionsPop.button = this.star;
		optionsPop.content =  $(content);
		optionsPop.direction = 'down';
		optionsPop.mode = 'click';
		optionsPop.closeOnClickOutside = true;
		optionsPop.appendTo = this.star;
		optionsPop.delay = 0;
		this.popup = new AppsPopup(optionsPop);
		this.appspopup = self.widget.find(".appspopup");
		this.name = self.widget.find(".bm_name");
		this.description = self.widget.find(".bm_desc");
		this.addBtn = self.widget.find(".add_bm");
		this.removeBtn = self.widget.find(".remove_bm");
		this.editBtn = self.widget.find(".edit_bm");
		this.filtersList = options.filtersList;
		this.bookmarkDiv = self.widget.find(".bookmark_div");
		this.url = getContextFreeUrl(window.location.href);
		this.pageTitle = $(".pageTitle").text();
		this.additionalParameters = options.additionalParameters;
		this.additionalParamValues = {};
		self.additionalParameters.forEach(function(param){
			self.additionalParamValues[param] = new BuildUrl(self.url).getParameter(param);
		});


		function closePopup(){
			self.popup.close();
		}

		function enableButton(button){
			button.prop('disabled', false);
			button.removeClass("inactive-btn").addClass("active-btn");
		}

		function disableButton(button){
			button.prop('disabled', true);
			button.removeClass("active-btn").addClass("inactive-btn");
		}

		function makeEntry(entry){
			var name = $("<div>").text(self.name.val()).html();
			var desc = $("<div>").text(self.description.val()).html();
			var pageTitle = $(".pageTitle").text();
			if(name==""){
				self.name.addClass("red_border");
				self.widget.find(".empty_span").addClass("empty_error").removeClass("empty_span");
				setTimeout(function(){
					self.name.removeClass("red_border");
					self.widget.find(".empty_error").addClass("empty_span").removeClass("empty_error");
				},3000)
			}
			else{
				entry = {
					name : name,
					url : self.url,
		 			description : desc,
		 		 	pageTitle : pageTitle,
	 		  		filtersList : self.filtersList,
	 		   		additionalParamValues : self.additionalParamValues
				};
				return entry;
			}
		}

		function createSortPattern(arrayToFill, filtersList){
			for(var i = 0; i < filtersList.length; i++){
				if(filtersList[i].showState=="REFINED"){
					arrayToFill.push(filtersList[i].categoryPath+"+");
				}
				else{
					arrayToFill.push(filtersList[i].categoryPath+"-");
				}
			}
		}

		function addFullStar(bmData){
			self.star.removeClass(options.classEmptyStar).addClass(options.classFullStar);
			self.name.val($("<div>").html(bmData.name).text());
			self.description.val($("<div>").html(bmData.description).text());
			disableButton(self.addBtn);
			enableButton(self.editBtn);
		}

		function addEmptyStar(){
			self.star.removeClass(options.classFullStar).addClass(options.classEmptyStar);
			disableButton(self.removeBtn);
			disableButton(self.editBtn);
			enableButton(self.addBtn);
		}

		this.init = function() {
			var pageTitle = $(".pageTitle");
			self.db = new StorageClient(options.scope);
			options.wuid = wuid;
			if((self.name.val())==""){
				self.name.val(pageTitle.text());
			}
			self.getStoredBookmarks();

			self.star.click(function(e){
				/* Re-positionning the popup */
				setTimeout(function(){
					var top = parseInt(self.appspopup.css("top")) - 9;
					self.appspopup.css("top",top+"px");
				},50);
			});

			self.appspopup.click(function(e){
				e.stopPropagation();
			});
			self.addBtn.click(function(e){
				var entry = makeEntry(entry);
				if(entry){
					self.addBookmark(entry);
					disableButton(self.addBtn);
					enableButton(self.removeBtn);
					closePopup();
					return false;
				}
			});
			self.removeBtn.click(function(){
				var currentUrl = getContextFreeUrl(window.location.href);
				self.db.get(options.storageKey, function(entries) {
					for (var i = 0; i < entries.length; i++) {
						var bmData = JSON.parse(entries[i].value);
						if(bmData.url==currentUrl || getContextFreeUrl(bmData.url)==currentUrl){
							var realKey = entries[i].key;
							self.removeBookmark(realKey, true);
						}
					}
				});
				self.name.val(pageTitle.text());
				self.description.val("");
				enableButton(self.addBtn);
				disableButton(self.removeBtn);
				closePopup();
				return false;
			});
			self.editBtn.click(function(){
				var entry = makeEntry(entry);
				if(entry){
					self.editBookmark();
					closePopup();
					return false;
				}
			});
			$(document).keyup(function(e) {
				// Enter keyboard click event
				if(e.which == 13) {
					if(self.addBtn.prop('disabled')==false && (self.appspopup.css("display"))=="block"){
						self.addBtn.click();
					}
				}
				// Esc keyboard click event
				if(e.which == 27) {
					if(self.appspopup.css("display")=="block"){
						closePopup();
					}
				}
				return false;
			});
		}

		function compareParams(selfAdditionalParam, bmAdditionalParam){
			return JSON.stringify(selfAdditionalParam) == JSON.stringify(bmAdditionalParam);
		}

		/**
		* Compares page's active filters with bookmarks ones.
		* Returns true if filters are the same.
		* Returns false otherwise
		*/
		function compareActiveFilters(bmData, pageActiveFilters){
			if(bmData.filtersList.length != self.filtersList.length){
				return false;
			}
			var bmActiveFilters = [];
			// creating sort pattern for bookmark using categoryPath and showState
			createSortPattern(bmActiveFilters, bmData.filtersList);
			// Sorting the bookmark's filters array
			bmActiveFilters.sort();
			//Comparison between page's current filters and bookmark's filters
			for(var k = 0; k < bmActiveFilters.length; k++){
				if(bmActiveFilters[k]!=pageActiveFilters[k]){
					return false;
				}
			}
			return true;
		}

		this.RefreshUI = function(entries) {
			if(entries.length!=0){
				var pageActiveFilters = [];
				createSortPattern(pageActiveFilters, self.filtersList);
				pageActiveFilters.sort();
				for (var i = 0; i < entries.length; i++) {
					var bmData = JSON.parse(entries[i].value);
					if(bmData.pageTitle == self.pageTitle && compareParams(self.additionalParamValues, bmData.additionalParamValues)
						&& compareActiveFilters(bmData, pageActiveFilters)){
						addFullStar(bmData);
						return;
					}
				}
			}
			addEmptyStar();
		}



		this.getStoredBookmarks = function() {
			self.db.get(options.storageKey, function(items) {
				self.RefreshUI(items);
			}, self.callBackError);
		};

		this.callBackError = function(evt, args) {
			if(typeof console != "undefined" && console.error !== undefined){
				console.error(args.responseText);
			}
			self.RefreshUI([]);
		};

		this.addBookmark = function(entry) {
			entry = JSON.stringify(entry);
			if(!self.star.hasClass("edited_star")){
				self.star.toggleClass(options.classEmptyStar);
				self.star.toggleClass(options.classWaitingStar);
			}
			self.db.set(options.storageKey, entry, function() {
				self.getStoredBookmarks();
				if(!self.star.hasClass("edited_star")){
					self.star.toggleClass(options.classWaitingStar);
				}
				self.notify();
			}, self.callBackError);
		};

		this.editBookmark = function() {
			var currentUrl = getContextFreeUrl(window.location.href), found = 0, newEntry, realKey;
			self.db.get(options.storageKey, function(entries) {
				for (var i = 0; i < entries.length; i++) {
					var bmData = JSON.parse(entries[i].value);
					if(bmData.url==currentUrl || getContextFreeUrl(bmData.url)==currentUrl){
						self.star.addClass("edited_star");
						self.star.toggleClass(options.classFullStar);
						self.star.toggleClass(options.classWaitingStar);
						realKey = entries[i].key;
						newEntry = makeEntry(newEntry);
						self.removeBookmark(realKey, false);
						self.addBookmark(newEntry);
						self.star.toggleClass(options.classWaitingStar);
						found=1;
					}
				}
				if(found==0){
					newEntry = makeEntry(newEntry);
					self.addBookmark(newEntry);
				}
				self.star.removeClass("edited_star");
			}, self.callBackError);
		};

		this.removeBookmark = function(key, notify) {
			if(!self.star.hasClass("edited_star")){
				self.star.toggleClass(options.classFullStar);
				self.star.toggleClass(options.classWaitingStar);
			}
			self.db.del(key, function() {
				self.getStoredBookmarks();
				if(!self.star.hasClass("edited_star")){
					self.star.toggleClass(options.classWaitingStar);
				}
				if(notify){
					self.notify();
				}
			}, self.callBackError);
		};

		this.notify = function(){
			if(self.savedBookmarksWuid!=''){
				$('.' + self.savedBookmarksWuid)[0].savedbookmarks.getStoredBookmarks();
			}
		};

		this.init();

	};

	/**
	 * Giver a full URL, this method returns the same URL without all the context, ie
	 * only the page and the query string.
	 * "http://HOST:PORT/APP/myPage?myParam=myValue" -> "myPage?myParam=myValue"
	 * @param url
	 */
	function getContextFreeUrl(url) {
		if (url) {
			var parseUrl = new BuildUrl(url);
			var contextFreeUrl = parseUrl.toString().replace(parseUrl.path, parseUrl.getPage());
			return contextFreeUrl;
		}
		return url;
	}


	$.fn.addbookmarks = function(wuid, options) {
		this.each(function() {
			this.addbookmarks = new AddBookmarks(this, wuid, options);
		});
		return this;
	};

}(jQuery));
