/*Export*/	
var PLMAPlanningExporter = function(plmaPlanning, options){
	this.plmaPlanning = plmaPlanning;
	this.options = $.extend({
	    fileNamePrefix: 'planning_' + this.plmaPlanning.uCssId,
    }, PLMAPlanningExporter.DEFAULT_OPTIONS, options);
	
	this.userOptions = {};
	this.modOptions = {};
	this.init();
}
PLMAPlanningExporter.CONFIG_KEY = 'exportConfig';

/*
@getTimeRange From Configurations = timeRange
@xAxisScale: From Configurations(time.scale) = reqScale
@getDimensions: function($widget, timeRange, reqScale)
    Provide the dimensions of the final widget. Note that the based on provided dimension.width
    Auto scaling will happen inside visjs. Hence the xAxisScale will be Ignored.
*/
PLMAPlanningExporter.DEFAULT_OPTIONS = {
    getDimensions: function($widget, timeRange, reqScale){
        return  {
		   height: PLMAPlanningExporter.guessedHeight($widget),
		   width: PLMAPlanningExporter.guessedWidth($widget, timeRange, reqScale),
	    };
    },
    fileNamePrefix: undefined,
}

PLMAPlanningExporter.prototype.init = function(){
	this.createButton();
	this.createConfig();
}

PLMAPlanningExporter.prototype.createConfig = function(){
	// Build Custom Options for timerange.
	let exportConfigHandler = this.plmaPlanning.configurator.getCustomConfigHandler('export-config config-section');
	this.userOptions = exportConfigHandler.getUserOptions(PLMAPlanningExporter.CONFIG_KEY);
	
	exportConfigHandler.$container.append($(
		'<div class="config-section-header" >' +
			'Export Configurations: '+
			'<i class="fonticon fonticon-info tooltip" title="Provide Scale & offset from Current Time. If no range, then full data range will be used."/>'+
		'</div>'
	));
	
	exportConfigHandler.$container.append($(
		'<div class="config-section row level-1 time-scale">' +
			'<label for="time-scale" class="label range-label">Time Scale:</label>' +
			'<select name="time-scale" id="time-scale" class="user-input time-scale">' +
				'<option value="" selected disabled hidden>Select</option>' +				
			'</select>' +
			'<i class="fonticon fonticon-info tooltip" title="Default is months"/>'+
		'</div>'
	));
	
	exportConfigHandler.$container.append($(
		'<div class="config-section row level-1 range-start">' +
			'<label for="start-value" class="label range-label">Start Time Offset:</label>' +
			'<input type="number" id="start-value" name="value" class="user-input range-value">' +
			'<select name="unit" id="start-unit" class="user-input range-unit">' +
				'<option value="" selected disabled hidden>Select</option>' +				
			'</select>' +
			'<i class="fonticon fonticon-info tooltip" title="If one bound is empty used now."/>'+
		'</div>'
	));
	exportConfigHandler.$container.append($(
		'<div class="config-section row level-1 range-end">' +
			'<label for="end-value" class="label range-label">End Time Offset:</label>' +
			'<input type="number" id="end-value" name="value" class="user-input range-value">' +
			'<select name="unit" id="end-unit" class="user-input range-unit">' +
				'<option value="" selected disabled hidden>Select</option>' +
			'</select>' +
			'<i class="fonticon fonticon-info tooltip" title="If one bound is empty used now."/>'+
		'</div>'
	));
	exportConfigHandler.$container.append($(
		'<div class="config-section range-calculated">'+
			'<span class="calculations"></span>' +
			'<i class="fonticon fonticon-eye range-preview-button hidden" title="Preview"></i>' +
		'</div>'
	));
	
	let $rangeCalculated = exportConfigHandler.$container.find('.range-calculated .calculations');
	let $timeScaleSelect = exportConfigHandler.$container.find('#time-scale');
	let $startUnitSelect = exportConfigHandler.$container.find('#start-unit');
	let $endUnitSelect = exportConfigHandler.$container.find('#end-unit');
		
	PLMAPlanningTimeRange.UNITS.forEach(function(unitInfo){
		$timeScaleSelect.append($('<option value="' + unitInfo[0] + '">' + unitInfo[1] + '</option>'));
		$startUnitSelect.append($('<option value="' + unitInfo[0] + '">' + unitInfo[1] + '</option>'));
		$endUnitSelect.append($('<option value="' + unitInfo[0] + '">' + unitInfo[1] + '</option>'));
	});
	
	if(this.userOptions.time && this.userOptions.time.scale){
		$timeScaleSelect.val(this.userOptions.time.scale);
	}else{
		$timeScaleSelect.val('M');	
	}
	if(this.userOptions.start && this.userOptions.start.value && this.userOptions.start.unit){
		exportConfigHandler.$container.find('#start-value').val(this.userOptions.start.value);
		$startUnitSelect.val(this.userOptions.start.unit);
	}
	if(this.userOptions.end && this.userOptions.end.value && this.userOptions.end.unit){
		exportConfigHandler.$container.find('#end-value').val(this.userOptions.end.value);
		$endUnitSelect.val(this.userOptions.end.unit);
	}
	
	let $previewButton = exportConfigHandler.$container.find('i.range-preview-button');
	let previewRangeHandler = function(range){
		this.plmaPlanning.timeline.setWindow({
            start: range.start,
            end: range.end,
            animation:true,
        }, function(){
        });
	};
	
	let range = undefined;
	let handelChange = function(what, val){
		let keys = what.split('-');
		let option = { useNow: true };
		option[keys[0]] = {};
		option[keys[0]][keys[1]] = val;		
		this.modOptions = $.extend(true, {}, this.userOptions, this.modOptions, option);
		exportConfigHandler.updateModification(PLMAPlanningExporter.CONFIG_KEY, this.modOptions);
		
		if(what == 'time-scale'){
			return;
		}
		
		range = PLMAPlanningTimeRange.make(this.modOptions);
		let rangeInvalid = PLMAPlanningTimeRange.isValid(range) == false;
		let rangeHtml = '<span class="' + (rangeInvalid?'error':'') + '">' + range.toString() + '</span>';
		$rangeCalculated.html(rangeHtml);
		
		//Enable Preview
		if(rangeInvalid){
			$previewButton.addClass('hidden').off('click', previewRangeHandler.bind(this, range));
		}else{
			$previewButton.removeClass('hidden').on('click', previewRangeHandler.bind(this, range));
		}
	}.bind(this);
	
	exportConfigHandler.$container.find('.user-input').on('change', function(e){
		handelChange($(this).attr('id'), $(this).val())
	});
}

PLMAPlanningExporter.prototype.getCurrentOptions = function(){
	if(this.modOptions.start != undefined || this.modOptions.end != undefined){
		console.warn('Unsaved Configurations Exists!');
	}

	return $.extend(true,{time: { scale: 'M'} }, this.userOptions, this.modOptions);
}

PLMAPlanningExporter.prototype.getTimeScale = function(){
	let rangeOptions = this.getCurrentOptions()
	return rangeOptions.time.scale;
}

PLMAPlanningExporter.prototype.getTimeRange = function(){
	let rangeOptions = this.getCurrentOptions();
	let range = PLMAPlanningTimeRange.make(rangeOptions);
	return PLMAPlanningTimeRange.isValid(range)? 
		range.format() :
		PLMAPlanningTimeRange.makeFromTimeline(this.plmaPlanning.timeline, false).format();// Send all data range.
}

PLMAPlanningExporter.prototype.createButton = function(){
	const EXPORT_ICON = 'fonticon fonticon-picture';
	const EXPORT_LABEL = PLMAPlanningNew.getMessage('widget.plma.planning.menu.exportimage');
	
	if(this.plmaPlanning.buttonManager.hasButton(EXPORT_ICON, EXPORT_LABEL)){
		return;
	}
	this.$button = this.plmaPlanning.buttonManager.addButton(
		EXPORT_ICON, 
		EXPORT_LABEL, 
		function(){
			/* Call showPopup only when the data is available. if no data available then notify error.*/
			if (this.plmaPlanning.timelineData.items.length == 0) {
				$.notify(PLMAPlanningNew.getMessage('widget.plma.planning.export.err.nodata'), 'error');
			}else{
				this.plmaPlanning.closeAllPanels();
				var previewHelper = window.exportTo3DSpace.startDocumentExport({
					id:"DocumentExport",
					title: mashupI18N.get('plmaResources', 'plma.exportto3dspace.title'),
					fileMode: true,
					fileExtension: 'png',
					enableAddNewDocument: true,
					isBookmarkEnable: true
				});
				previewHelper.container.closest('.plmalightbox-box.export-to-3dspace').addClass('timeline-export');
				this.exportImage({
					imageType: 'png',
					container: previewHelper.container,
					doneCallback: previewHelper.doneCallback
				});
			}
		}.bind(this)
	);
}

/*
exportOpts = {
    imageType: 'png', // Only supported type as of now.
    container: previewHelper.container, // Where to append image
    doneCallback: previewHelper.doneCallback // Callback once image is created.
}
*/
PLMAPlanningExporter.prototype.exportImage = function(exportOpts) {
	exportOpts.container.showPLMASpinner({overlay: true});
	let imageName = this.options.fileNamePrefix + '-' + moment().format('YYYYMMDD-HHmmss') + '.' + exportOpts.imageType;
	this.getImageDataUrl().then(function(imageData){
		exportOpts.container.find('.preview-header').append('<span class="title">' + imageName + '</span>');
		let $preview = $('<a>',{
			download: imageName,
			onclick: "this.setAttribute('href', this.children[0].getAttribute('src'))" // TO enable download.
		});
		$preview.append($('<img>', {
			src: imageData.dataUrl,
			alt: imageName,
			style: "display: block; margin-left: auto; margin-right: auto;"
		}));
		exportOpts.container.find('.preview-content').append($preview);
		exportOpts.container.hidePLMASpinner();
		exportOpts.doneCallback(function(){
			return $preview.find('img').attr('src');
		}, imageName);
	}.bind(this))
	.catch((function(error) {
		$.notify(PLMAPlanningNew.getMessage('widget.plma.planning.export.err.convert'), 'error');
		console.error(error);
	}).bind(this));
}

PLMAPlanningExporter.prototype.getImageDataUrl = function() {
    let dataForReset = {};
	return new Promise(function(resolve, reject){
		this.prepare(dataForReset).then(function(dataForReset){
			html2canvas(this.plmaPlanning.widget.get(0)).then(function(canvas){
				this.reset(dataForReset).then(function(){
					resolve({
						dataUrl: canvas.toDataURL('image/png', 1.0),
					});	
				});
			}.bind(this));
		}.bind(this));
	}.bind(this))
	.catch((function(error) {
		$.notify(PLMAPlanningNew.getMessage('widget.plma.planning.export.err.convert'), 'error');
		console.error(error);
	}).bind(this));
}

PLMAPlanningExporter.prototype.getWidgetDim = function(){
    let dim = { modified: false, };

    // Get Current width & height properties of the widget. This will be useful in reset
    let styleStr = this.plmaPlanning.widget.attr('style');
    if(styleStr){
        styleStr.split(';')
            .filter(function(x){
                return x.length > 0 && (x.indexOf('height') > -1 ||x.indexOf('width') > -1);
            }).forEach(function(x){
                let xarr = x.split(':');
                $.extend(dim, JSON.parse('{ "'+ xarr[0].trim() +'": "' + xarr[1].trim() + '" }'));
            });
    }
    return dim;
}

PLMAPlanningExporter.prototype.prepare = function(dataForReset){
    let newRange = this.getTimeRange();
	let newDim = this.options.getDimensions(
            this.plmaPlanning.widget,
            newRange,
            this.getTimeScale());

    let exporter = this;
    return new Promise(function(resolve, reject){
        exporter.setDimensions(dataForReset, newDim).then(function(dataForReset){
            exporter.setWindow(dataForReset, newRange).then(function(dataForReset){
                resolve(dataForReset);
            });
        });
    })
    .catch(function(error) {
        $.notify(PLMAPlanningNew.getMessage('widget.plma.planning.export.err.convert'), 'error');
        console.error(error);
    });
}

PLMAPlanningExporter.prototype.reset = function(dataForReset){
    let oldDim = dataForReset.dimensions;
    let oldRange = dataForReset.range;
    let exporter = this;

    return new Promise(function(resolve, reject){
        exporter.setDimensions(dataForReset, oldDim, true).then(function(dataForReset){
            exporter.setWindow(dataForReset, oldRange, true).then(function(dataForReset){
                resolve(dataForReset);
            });
        });
    })
    .catch(function(error) {
        $.notify(PLMAPlanningNew.getMessage('widget.plma.planning.export.err.convert'), 'error');
        console.error(error);
    });
}

PLMAPlanningExporter.prototype.setWindow = function(dataForReset, newRange, isReset){
    dataForReset = $.extend({}, dataForReset, {
        // Get Current Window
        range: PLMAPlanningTimeRange.makeFromTimeline(this.plmaPlanning.timeline, true).format()
    });

    let exporter = this;
    return new Promise(function(resolve, reject){
		 // Handle redraw event when the width/height are changed.
        let onRangeChanged = function(){
            clearTimeout( exporter.timeoutID ); // As Redraw is called, clear timer.
            exporter.plmaPlanning.timeline.off('rangechanged', onRangeChanged);
            resolve(dataForReset);
        };
        exporter.plmaPlanning.timeline.on('rangechanged', onRangeChanged);

        exporter.plmaPlanning.timeline.setWindow({
            start: newRange.start,
            end: newRange.end,
            animation:false,
        }, function(){
        });

		exporter.timeoutID = setTimeout( () => {
            onRangeChanged();
        }, 10000 );
    })
    .catch(function(error) {
        $.notify(PLMAPlanningNew.getMessage('widget.plma.planning.export.err.convert'), 'error');
        console.error(error);
    });
}

PLMAPlanningExporter.prototype.setDimensions = function(dataForReset, newDim, isReset){
    dataForReset = $.extend({}, dataForReset, {dimensions: this.getWidgetDim()});
    if(isReset != true){
        // If no width/height is supplied, then do not change the view.
        if(!newDim || (!newDim.width && !newDim.height)){
            newDim = undefined;
        }else if( (newDim.width && 'number' !== typeof newDim.width) ||
                 (newDim.height && 'number' !== typeof newDim.height) ){
            newDim = undefined;
            console.error('Error: Provide appropriate dimensions for export.\n' +
                'Valid Inputs = { width: undefined|number, height:undefined|number }');
        }
    }else if(!newDim.modified){
        resolve(false);
        return;
    }

    let exporter = this;
    return new Promise(function(resolve, reject){
        // Set Width & Height
        if(!newDim){
            resolve(dataForReset);
            return;
        }
        // Handle redraw event when the width/height are changed.
        let onRedraw = function(){
            clearTimeout( exporter.timeoutID ); // As Redraw is called, clear timer.
            exporter.plmaPlanning.timeline.off('changed', onRedraw);
            resolve(dataForReset);
        };
        exporter.plmaPlanning.timeline.on('changed', onRedraw);

        if(isReset != true){
            // At least one of(width/height) is valid at this stage hence set css if valid.
            newDim.width && exporter.plmaPlanning.widget.css('width', '' + newDim.width + 'px');
            newDim.height && exporter.plmaPlanning.widget.css('height', '' + newDim.height + 'px');
            dataForReset.dimensions.modified = true;
        }else{
            exporter.plmaPlanning.widget.css('width', newDim.width ? newDim.width : '');
            exporter.plmaPlanning.widget.css('height', newDim.height ? newDim.height : '');
        }

        // It can happen that the current dim and new dim are same. We cant compare dim
        // as the formats are different. In such case, the redraw event is not called by timeline.
        // Hence we need to break the waiting and resolve the promise.
        exporter.timeoutID = setTimeout( () => {
            onRedraw();
        }, 10000 );
    })
    .catch(function(error) {
        $.notify(PLMAPlanningNew.getMessage('widget.plma.planning.export.err.convert'), 'error');
        console.error(error);
    });
}

PLMAPlanningExporter.guessedHeight = function($widget){
	let widgetH = $widget.height();
	let visibleLeftPanelH = $widget.find('.vis-panel.vis-left').outerHeight(true);
	let max = 0, count = 0;
	$widget.find('.vis-panel.vis-left > .vis-content > .vis-labelset > .vis-label').each(function(i,el){
		let elH = $(el).outerHeight(true);
		if(elH > max){
			max = elH;
		}
		count++;
	});

	let height = widgetH - visibleLeftPanelH + (count * max) + 15; // 15px for buffer
	return height < 500? 500 : height;
}

PLMAPlanningExporter.guessedWidth = function($widget, timeRangeInput, reqScale){
	let timeRange = timeRangeInput.timeline? timeRangeInput.timeline.getWindow() : timeRangeInput;

	if(!timeRange || !timeRange.start || !timeRange.end){
		throw 'Error Expecte time range in {start: Date, end: Date} format\n' +
			timeRange;
	}

	let widgetW = $widget.width();
	let visibleTopPanelW = $widget.find('.vis-panel.vis-top').outerWidth(true);
	let max = 45, count = 0;
	PLMAPlanningTimeRange.UNITS.every(function(unitInfo){
		if(unitInfo[0] == reqScale){
			max = unitInfo[2];
			return false;
		}
		return true;
	});
	count = moment(timeRange.end).diff(moment(timeRange.start), reqScale);

	let width = widgetW - visibleTopPanelW + (count * max);
	return width < 500? 500 : width;
}
