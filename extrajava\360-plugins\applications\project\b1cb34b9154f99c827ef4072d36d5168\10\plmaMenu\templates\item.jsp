<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="menu" uri="http://www.exalead.com/jspapi/widget-menu" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import parameters="uCssId" />
<render:import parameters="itemCssId,descCssId,pageId,extraCss,label,description,togglePanel,url,isFolding,iconCss,iconUrl,section,onClick,type" ignore="true" />

<string:eval var="label" string="${label}"/>
<string:eval var="section" string="${section}"/>
<string:eval var="description" string="${description}"/>

<string:escape var="label" value="${label}" escapeType="HTML"/>
<string:escape var="section" value="${section}" escapeType="HTML"/>
<string:escape var="description" value="${description}" escapeType="HTML"/>
<string:escape var="extraCss" value="${extraCss}" escapeType="HTML"/>
<string:escape var="iconCss" value="${iconCss}" escapeType="HTML"/>

<div class="menu-item ${extraCss} ${plma:isEditable(pageContext.request, pageId) ? 'editable-item': ''}"
	 title="${not empty section ? section.concat(' - ').concat(label) : label}"
	 data-togglepanel="${togglePanel}"
     data-descId="${descCssId}" >

	<render:link href="${url}" >
        <div class="item-wrapper">
            <c:if test="${isFolding}">
                <span class="fold-icon fonticon fonticon-down-open"></span>
                <span class="fold-icon fonticon fonticon-right-open hidden"></span>
            </c:if>
            <div class="item-icon ${iconCss} ${isFolding ? 'icon-folder' : ''}">
                <c:if test="${not empty iconUrl}">
                    <url:resource file="${iconUrl}"/>
                </c:if>
                <c:if test="${not empty onClick && not empty itemCssId}">
                    <render:renderScript position="READY">
                        $('.${uCssId} .${itemCssId}').on('click', function(){
                        ${onClick}
                        });
                    </render:renderScript>
                </c:if>
            </div>
            <c:if test="${not empty label}">
                <div class="item-label ${isFolding ? 'label-folder' : ''}">${label}</div>
            </c:if>
            <plma:isPreferredPage var="isPreferredPage" id="${pageId}"/>
            <c:if test="${isPreferredPage}">
                <i class="icon-favorite fonticon fonticon-favorite-on"></i>
            </c:if>
        </div>
	</render:link>
</div>
