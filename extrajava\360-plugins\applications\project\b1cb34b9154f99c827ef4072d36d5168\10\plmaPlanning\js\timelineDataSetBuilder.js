/**
 * This class designed to help build the DataSet 
 * to update the timeline with new data. While adding
 * new items or groups, it performs check to validate.
 */
var TimelineDataSetBuilder = function(uCssId){
	this.planningObj = $('.'+uCssId).data('widget');
	this.items = [];
	this.groups = {};
	this.bounds = {};
	// Bug in VisJS. Ref: https://github.com/visjs/vis-timeline/issues/1314
	this.issue1314 = true;
	
	this.refGroupIds = {
		itemRefs: [],
		nestedRefs: []
	};
}

TimelineDataSetBuilder.prototype.setPaginiationInfo = function(paginationInfo){
	if(paginationInfo.perpage == 0){
		console.warn(PLMAPlanningNew.getMessage('widget.plma.planning.err.perpage'));
		return;
	}
	this.planningObj.updatePaginationInfo(paginationInfo);
}

TimelineDataSetBuilder.prototype._checkItem = function(itemData){
	let KO = !itemData.id || !itemData.content || !itemData.start;
	KO = KO || (['range', 'background'].indexOf(itemData.type) > -1 && !itemData.end);
	KO && console.error('Item data must have [id,content,start] and if type=(range/background)[end] is must', itemData);	
	return !KO;
}

TimelineDataSetBuilder.prototype.getDummyItem = function(groupId){
	let dummyItem = {
		id: 'dummy',
		content: '&nbsp;',
		type: 'background',
		class: 'dummy hidden',
		start: moment().format(PLMAPlanningNew.utils.DATE_FORMAT),
		end: moment().format(PLMAPlanningNew.utils.DATE_FORMAT),
	}
	if(groupId){
		dummyItem.group = groupId;
	}
	return dummyItem;
}

TimelineDataSetBuilder.prototype.addItem = function(itemData){
	if(itemData.type == 'estact'){
		this.processEstActual(itemData);
		return;
	}
	!itemData.content && (itemData.content = '&nbsp;');
	if(this._checkItem(itemData)){
		itemData.start = itemData.start.trim();
		itemData.start = PLMAPlanningNew.utils.getVisDateStr(itemData.start);
		
		let startVal = moment(itemData.start).valueOf();
		if(!this.bounds.min || this.bounds.min > startVal){
			this.bounds.min = startVal;
		}
		
		if(itemData.end){
			itemData.end = itemData.end.trim();
			itemData.end = PLMAPlanningNew.utils.getVisDateStr(itemData.end);
			let endVal = moment(itemData.end).valueOf()
			if(!this.bounds.max || this.bounds.max < endVal){
				this.bounds.max = endVal;
			}
			this.issue1314 = false;
		}else{
			if(!this.bounds.max || this.bounds.max < startVal){
				this.bounds.max = startVal;
			}
			// Bug in VisJS. Ref: https://github.com/visjs/vis-timeline/issues/1314
			//itemData.end = "";
		}
		if(itemData.group)
			this.refGroupIds.itemRefs.push(itemData.group);
		
		itemData.className = (itemData.className?(itemData.className + ' '):'') + itemData.idClass;
		this.items.push(itemData);
	}
}


TimelineDataSetBuilder.prototype._checkGroup = function(groupData){
	let KO = !groupData.id || !groupData.content;
	KO && console.error('Group data must have [id,content]', groupData);
	return !KO;
}

TimelineDataSetBuilder.prototype.addGroup = function(groupData){
	if(this._checkGroup(groupData)){
		if(groupData.nestedGroups.length > 1){
			groupData.nestedGroups = 
				groupData.nestedGroups.split(',')
					.filter(function(x){ 
						return x && x.length > 0; 
					});
		}else{
			delete groupData.nestedGroups;
		}
		if(this.groups[groupData.id] == undefined){
			groupData.nestedGroups && groupData.nestedGroups.forEach(function(x){ 
				this.refGroupIds.nestedRefs.push(x); 
			}.bind(this));
			this.groups[groupData.id] = (groupData);
		}else{
			// Duplicate groups : we need to merge nestedGroups if any.
			if(groupData.nestedGroups){
				if(!this.groups[groupData.id].nestedGroups){
					this.groups[groupData.id].nestedGroups = [];
				}
				groupData.nestedGroups.forEach(function(ng){
					this.groups[groupData.id].nestedGroups.push(ng);
				}.bind(this));
			}
		}
	}
}

TimelineDataSetBuilder.prototype.update = function(){
	if(this.items.length == 0){
		$.notify(PLMAPlanningNew.getMessage('widget.plma.planning.err.nodata'), 'error');
		return;
	}
	// Bug in VisJS. Ref: https://github.com/visjs/vis-timeline/issues/1314
	if(this.issue1314){ // Add once
		this.items.push(this.getDummyItem(Object.keys(this.groups)[0]));
	}
	
	let missingGroups = this.refGroupIds.itemRefs.filter(function(x){
		return this.groups[x] == undefined;
	}.bind(this));
	
	if(missingGroups.length > 0){
		console.warn('Missing Groups refered in Items: ', missingGroups);
	}
	
	missingGroups = this.refGroupIds.nestedRefs.filter(function(x){
		return this.groups[x] == undefined;
	}.bind(this));
	
	if(missingGroups.length > 0){
		console.warn('Missing Groups refered in nestedGroups: ', missingGroups);
	}
	//console.log('DataSets: Items ', this.items);
	//console.log('DataSets: Groups ', this.groups);
	let groupsArr = Object.keys(this.groups).map(function(x){ return this.groups[x]; }.bind(this));
	this.planningObj.update(this.items, groupsArr, this.bounds);
}

/*Code to create the visualization for Estimated actual item.
Some items like project(est-start,est-end, act-start, act-end) need a complex visualization.
In this case we decompose the itemDef provided and create 3 new items.
1. est : est-start,est-end
2. act : act-start,act-end
3. wrapper: max and min of (est/act start end & today). It will act as just a wrapping box.
We make sure they behave like one item, by unique and same subgroup for all three.
*/
TimelineDataSetBuilder.prototype.snapEndToToday = function(itemData, boundManager){
	if(itemData.end && itemData.end.length > 0){ return; }
	boundManager.noteDate(moment().valueOf());
	itemData.end = moment().format("YYYY/MM/DD HH:mm:ss");
}

TimelineDataSetBuilder.preareColor = function(color){
	// Set and get the color on dummy element. We always get rgb color format.
	let rgb = $('<span/>')
		.css('background-color', color)
		.css('background-color');
	return rgb.replace('(', 'a(').replace(')', ', 0.4)'); //Add transparancy

}

TimelineDataSetBuilder.prototype.processEstActual = function(itemData){
	let boundManager = (function(){
		let min = undefined;
		let max = undefined;
		return {
			noteDate: function(dateMilli){
				if(min == undefined || dateMilli < min ){
					min = dateMilli;
				}
				if(max == undefined || max < dateMilli){
					max = dateMilli;
				}
			},
			getBounds: function(){
				return { min: moment(min).format("YYYY/MM/DD HH:mm:ss"), max: moment(max).format("YYYY/MM/DD HH:mm:ss") };
			}
		}
	})();
	
	{
		let estRangeItem = $.extend({}, itemData);
		estRangeItem.id = itemData.id + '-est';
		estRangeItem.idClass = itemData.idClass + '-est';
		estRangeItem.content = '&nbsp;';
		estRangeItem.subgroup = itemData.id;
		estRangeItem.type = 'range';
		estRangeItem.className = (itemData.className?itemData.className:' ') + 'estact estact-est';
		if(estRangeItem.typeColor){
			estRangeItem.style = (estRangeItem.style?(estRangeItem.style+';'):'') 
				+ 'background-color:'+ TimelineDataSetBuilder.preareColor(estRangeItem.typeColor);
		}
		delete estRangeItem.actStart;
		delete estRangeItem.actEnd;
		this.addItem(estRangeItem);
	}
	let statusValues = {
		start: TimelineDataSetBuilder.calculateStatus(itemData.start, itemData.actStart, boundManager),
		finish: TimelineDataSetBuilder.calculateStatus(itemData.end, itemData.actEnd, boundManager),
	}
	if(itemData.actStart && itemData.actStart.length > 0){
		let actRangeItem = $.extend({}, itemData);
		actRangeItem.id = itemData.id + '-act';
		actRangeItem.idClass = itemData.idClass + '-act';
		actRangeItem.content = '&nbsp;';
		actRangeItem.subgroup = itemData.id;
		actRangeItem.type = 'range';
		actRangeItem.className = (itemData.className?itemData.className:' ') + 'estact estact-act';
		actRangeItem.start = itemData.actStart;
		actRangeItem.end = itemData.actEnd;
		delete actRangeItem.actStart;
		delete actRangeItem.actEnd;
		actRangeItem.statusValues = statusValues;
		
		/*if(actRangeItem.typeColor){
			actRangeItem.style = (actRangeItem.style?(actRangeItem.style+';'):'') 
				+ 'border-color:'+ TimelineDataSetBuilder.preareColor(actRangeItem.typeColor);
		}*/
		
		this.snapEndToToday(actRangeItem, boundManager);
		this.addItem(actRangeItem);
	}
		
	{
		let wrappedRange = boundManager.getBounds();
		let wrapper = $.extend({}, itemData);
		wrapper.id = itemData.id;
		wrapper.subgroup = itemData.id;
		wrapper.type = 'range';
		wrapper.start = wrappedRange.min;
		wrapper.end = wrappedRange.max;
		wrapper.className = 'estact estact-wrapper';
		wrapper.style = '';
		delete wrapper.actStart;
		delete wrapper.actEnd;
		this.addItem(wrapper);
	}
}

TimelineDataSetBuilder.calculateStatus = function(plannedDateStr, actualDateStr, boundManager){
	let nowMilli = moment().valueOf();
	let plannedMilli = moment(PLMAPlanningNew.utils.getVisDateStr(plannedDateStr)).valueOf();
	boundManager && boundManager.noteDate(plannedMilli);
	if(actualDateStr == ''){
		return plannedMilli < nowMilli ? 'pastdue' : 'upcoming';
	}	
	let actualMilli = moment(PLMAPlanningNew.utils.getVisDateStr(actualDateStr)).valueOf();
	boundManager && boundManager.noteDate(actualMilli);
	return actualMilli < plannedMilli ? 'early' : (actualMilli > plannedMilli ? 'late' : 'ontime');
}

TimelineDataSetBuilder.STATUS_CLASSES = {
	early: 'early fonticon-expand-up ',
	late: 'late fonticon-expand-down ',
	ontime: 'ontime fonticon-record ',
	pastdue: 'pastdue fonticon-record ',
	upcoming: 'upcoming fonticon-stop ',
}

/*Adds the start and end status valus and visualizations in item template.*/
TimelineDataSetBuilder.renderStatus = function(item, $element){
	if(item.statusValues == undefined){
		return item.content;
	}
	
	if($element.parent().find('.status-info').length == 0){
		let $statusInfo = $('<span class="status-info">' +
			'<span class="status-start fonticon ' + TimelineDataSetBuilder.STATUS_CLASSES[item.statusValues.start] + '"></span>' +
			'<span class="status-finish fonticon ' + TimelineDataSetBuilder.STATUS_CLASSES[item.statusValues.finish] + '"></span>' +
		'</span>');
		$element.parent().append($statusInfo);
	}
	return item.content;
}

TimelineDataSetBuilder.renderLegend = function($container){
	let classMap = TimelineDataSetBuilder.STATUS_CLASSES;
	$container.append($(
		'<span class="legendHeader">Follwoing is the element Information</span>' +
		'<ul class="legendContent status-info timeline-vis">' +
			'<li><span class="estact estact-est" style="margin-right:7px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span>Estimate Time Span</span></li>' +
			'<li><span class="estact estact-act" style="margin-right:7px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span>Actual Time Span</span></li>' +			
		'</ul>'
	));
	$container.append($(
		'<span class="legendHeader">Follwoing Status applicable to Start/End</span>' +
		'<ul class="legendContent status-info">' +
			'<li><span class="status-start fonticon ' + classMap['early'] +' ">Completed Early</span></li>' +
			'<li><span class="status-start fonticon ' + classMap['late'] +' ">Completed Late</span></li>' +
			'<li><span class="status-start fonticon ' + classMap['ontime'] +' ">Completed On-Time</span></li>' +
			'<li><span class="status-start fonticon ' + classMap['pastdue'] +' ">Past Due Date</span></li>' +
			'<li><span class="status-start fonticon ' + classMap['upcoming'] +' ">Due in Future</span></li>' +				
		'</ul>'
	));
}