<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="ajax" uri="http://www.exalead.com/jspapi/ajax" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<plma:getBookmarks var="bookmarks"/>

<widget:widget extraCss="bookmarks-widget" varUcssId="uCssId">
    <c:set var="selectedBookmarkTitle" value=""/>
    <c:if test="${bookmarks.size() > 0}">
        <c:forEach var="bookmark" items="${bookmarks}" varStatus="status">
            <c:if test="${bookmark.id == param['bookmark']}">
                <c:set var="selectedBookmarkTitle" value="${bookmark.name}"/>
            </c:if>
        </c:forEach>
    </c:if>

    <div class="bookmarkSearchContainer">
        <span class="fonticon fonticon-bookmark" title="Active bookmark"></span>
        <div class="searchContainer hidden">
            <div class="searchFormContent" id="bookmark-search-add">
                <c:choose>
                    <c:when test="${selectedBookmarkTitle == ''}">
                        <input class="searchInput" type="text" autocomplete="off" placeholder="Add or search bookmark..." id="bmkInput"/>
                    </c:when>
                    <c:otherwise>
                        <input class="searchInput" type="text" autocomplete="off" placeholder="${selectedBookmarkTitle}" id="bmkInput"/>
                    </c:otherwise>
                </c:choose>
                <span class="fonticon fonticon-bookmark-add query-bookmark bookmark-add-input hidden" id="bookmark-add-${uCssId}"
                      title="Add bookmark from page context" hidden></span>
            </div>
        </div>
        <div class="bookmarks-list hidden">
            <c:if test="${bookmarks.size() > 0}">
                <c:forEach var="bookmark" items="${bookmarks}" varStatus="status">
                    <c:choose>
                        <c:when test="${bookmark.id == param['bookmark']}">
                            <c:set var="bookmarkClass" value="bookmark-selected"/>
                            <c:set var="selectedBookmarkTitle" value="${bookmark.name}"/>
                        </c:when>
                        <c:otherwise>
                            <c:set var="bookmarkClass" value="bookmark-element"/>
                        </c:otherwise>
                    </c:choose>
                    <div class="bookmark-item ${bookmarkClass}" data-params="${bookmark.params}" data-id="${bookmark.id}">
                        <span class="fonticon fonticon-trash query-bookmark bookmark-delete" title="Delete bookmark" data-id="${bookmark.id}"></span>
                        <span class="bookmark-name" id="bookmarks-select-${bookmark.id}" data-mode="bookmark">${bookmark.name}</span>
                    </div>
                </c:forEach>
            </c:if>
        </div>
    </div>

    <render:renderScript position="READY">
        var options = {};
        options.url = '<c:url value="/bookmark"/>';
        options.page = '<search:getPageName/>';

        new QueryBookmarks('${uCssId}',options);
    </render:renderScript>
</widget:widget>