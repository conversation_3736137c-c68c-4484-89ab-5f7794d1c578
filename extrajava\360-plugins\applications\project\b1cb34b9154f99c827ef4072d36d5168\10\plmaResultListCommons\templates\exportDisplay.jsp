<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>

<render:import varFeeds="feeds" varWidget="widget"/>
<render:import parameters="columnsConfig,buttonWidgetCssId" ignore="true"/>

<search:getFeed var="feed" feeds="${feeds}"/>

<%--	Configure widget ID, does not seem to work if not set... --%>
<render:template template="templates/exportButton.jsp" widget="plmaExport">
    <render:parameter name="uid" value="${buttonWidgetCssId}"/>
    <render:parameter name="feed" value="${feed}"/>
    <render:parameter name="numHits" value="${plma:getIntegerParam(widget, 'exportNumHits', -1)}"/>
    <render:parameter name="columnsConfig" value="${plma:getStringParam(widget, 'exportColumnsConfig', columnsConfig)}"/>
    <render:parameter name="exportMode" value="${plma:getStringParam(widget, 'exportMode', 'Search API')}"/>
    <render:parameter name="exportPerPage" value="${plma:getIntegerParam(widget, 'exportPerPage', -1)}"/>
    <render:parameter name="fileName" value="${plma:getStringParam(widget, 'exportFileName', 'export')}"/>
    <render:parameter name="exportEncoding" value="${plma:getStringParam(widget, 'exportEncoding', 'UTF-8')}"/>
    <render:parameter name="exportSeparator" value="${plma:getStringParam(widget, 'exportEncoding', ';')}"/>
    <render:parameter name="recordDelimiter" value="${plma:getStringParam(widget, 'exportDelimiter', 'Default')}"/>
    <render:parameter name="addBOM" value="${plma:getBooleanParam(widget, 'exportAddBOM', true)}"/>
    <render:parameter name="exportRawValues" value="${plma:getBooleanParam(widget, 'exportRawValues', false)}"/>
    <render:parameter name="iconCss" value="${plma:getStringParam(widget, 'exportIconCss', 'fonticon fonticon-export-multiple')}"/>
    <render:parameter name="label" value="${plma:getStringParam(widget, 'exportLabel', 'Export date')}"/>
    <render:parameter name="showLabel" value="${plma:getBooleanParam(widget, 'exportShowLabel', false)}"/>
</render:template>