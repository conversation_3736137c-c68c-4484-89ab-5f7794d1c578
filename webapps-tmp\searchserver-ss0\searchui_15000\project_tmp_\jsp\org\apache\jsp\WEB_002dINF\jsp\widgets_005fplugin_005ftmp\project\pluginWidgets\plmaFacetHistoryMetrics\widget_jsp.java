/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:32 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.plmaFacetHistoryMetrics;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class widget_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(14);
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/widget.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/META-INF/tiles-jsp.tld", Long.valueOf(1748332421007L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/i18n.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/list.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/map.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005ffacetHistoryMetrics_0026_005fnbCategories_005ffeeds_005ffacet_005fcolorMappings_005faggregation_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fcontent_0026_005fextraCss;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fmap_005fnew_0026_005fvar_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fhighchartsJSON_0026_005fvar_005ftakeFirstValues_005fmaxCategories_005fforceRefineOnFeeds_005fforceRefineOnFacets_005ffeeds_005fcategoriesOrder_005fbaseUrl_005faxisLabelsY_005faxisLabelX_005fadditionalCategories;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fhighchartsSerie_0026_005fserieConfig_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005ffacetHistoryMetrics_0026_005fnbCategories_005ffeeds_005ffacet_005fcolorMappings_005faggregation_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fcontent_0026_005fextraCss = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fmap_005fnew_0026_005fvar_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fhighchartsJSON_0026_005fvar_005ftakeFirstValues_005fmaxCategories_005fforceRefineOnFeeds_005fforceRefineOnFacets_005ffeeds_005fcategoriesOrder_005fbaseUrl_005faxisLabelsY_005faxisLabelX_005fadditionalCategories = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fhighchartsSerie_0026_005fserieConfig_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005ffacetHistoryMetrics_0026_005fnbCategories_005ffeeds_005ffacet_005fcolorMappings_005faggregation_005fnobody.release();
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss.release();
    _005fjspx_005ftagPool_005fwidget_005fcontent_0026_005fextraCss.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.release();
    _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.release();
    _005fjspx_005ftagPool_005fmap_005fnew_0026_005fvar_005fnobody.release();
    _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fhighchartsJSON_0026_005fvar_005ftakeFirstValues_005fmaxCategories_005fforceRefineOnFeeds_005fforceRefineOnFacets_005ffeeds_005fcategoriesOrder_005fbaseUrl_005faxisLabelsY_005faxisLabelX_005fadditionalCategories.release();
    _005fjspx_005ftagPool_005fplma_005fhighchartsSerie_0026_005fserieConfig_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f4(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f5(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f6(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f7(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f8(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f9(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f10(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOptionsComposite_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_plma_005ffacetHistoryMetrics_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_widget_005fwidget_005f0(_jspx_page_context))
        return;
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(14,0) name = varWidget type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarWidget("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(14,0) name = varFeeds type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarFeeds("feeds");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(16,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setVar("linkIcon");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(16,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setName("linkIcon");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(16,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f0 = _jspx_th_config_005fgetOption_005f0.doStartTag();
        if (_jspx_th_config_005fgetOption_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f0);
      _jspx_th_config_005fgetOption_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(17,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setVar("linkValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(17,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setName("linkValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(17,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f1 = _jspx_th_config_005fgetOption_005f1.doStartTag();
        if (_jspx_th_config_005fgetOption_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f1);
      _jspx_th_config_005fgetOption_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(18,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setVar("linkTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(18,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setName("linkTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(18,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f2 = _jspx_th_config_005fgetOption_005f2.doStartTag();
        if (_jspx_th_config_005fgetOption_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f2);
      _jspx_th_config_005fgetOption_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f3_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(19,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setVar("limit");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(19,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setName("limit");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(19,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setDefaultValue("0");
      int[] _jspx_push_body_count_config_005fgetOption_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f3 = _jspx_th_config_005fgetOption_005f3.doStartTag();
        if (_jspx_th_config_005fgetOption_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f3);
      _jspx_th_config_005fgetOption_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f4 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f4_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f4.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(20,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setVar("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(20,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setName("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(20,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f4 = _jspx_th_config_005fgetOption_005f4.doStartTag();
        if (_jspx_th_config_005fgetOption_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f4);
      _jspx_th_config_005fgetOption_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f4, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f5(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f5 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f5_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f5.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f5.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(21,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setVar("aggregation");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(21,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setName("aggregation");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(21,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f5 = _jspx_th_config_005fgetOption_005f5.doStartTag();
        if (_jspx_th_config_005fgetOption_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f5);
      _jspx_th_config_005fgetOption_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f5, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f6(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f6 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f6_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f6.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f6.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(22,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setVar("chartType");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(22,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setName("chartType");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(22,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f6 = _jspx_th_config_005fgetOption_005f6.doStartTag();
        if (_jspx_th_config_005fgetOption_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f6);
      _jspx_th_config_005fgetOption_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f6, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f7(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f7 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f7_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f7.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f7.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(23,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setVar("tooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(23,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setName("tooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(23,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setDefaultValue("0");
      int[] _jspx_push_body_count_config_005fgetOption_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f7 = _jspx_th_config_005fgetOption_005f7.doStartTag();
        if (_jspx_th_config_005fgetOption_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f7);
      _jspx_th_config_005fgetOption_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f7, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f8(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f8 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f8_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f8.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f8.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(24,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setVar("hideChart");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(24,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setName("hideChart");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(24,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f8 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f8 = _jspx_th_config_005fgetOption_005f8.doStartTag();
        if (_jspx_th_config_005fgetOption_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f8[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f8.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f8.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f8);
      _jspx_th_config_005fgetOption_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f8, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f9(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f9 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f9_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f9.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f9.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(25,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setVar("displayDoc");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(25,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setName("displayDoc");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(25,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f9.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f9 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f9 = _jspx_th_config_005fgetOption_005f9.doStartTag();
        if (_jspx_th_config_005fgetOption_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f9[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f9.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f9.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f9);
      _jspx_th_config_005fgetOption_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f9, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f10(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f10 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f10_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f10.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f10.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(26,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setVar("doc");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(26,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setName("doc");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(26,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f10.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f10 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f10 = _jspx_th_config_005fgetOption_005f10.doStartTag();
        if (_jspx_th_config_005fgetOption_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f10[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f10.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f10.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f10);
      _jspx_th_config_005fgetOption_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f10, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOptionsComposite_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOptionsComposite
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag _jspx_th_config_005fgetOptionsComposite_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag) _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionsCompositeTag.class);
    boolean _jspx_th_config_005fgetOptionsComposite_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOptionsComposite_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOptionsComposite_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(27,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setVar("colors");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(27,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setName("colors");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(27,0) name = mapIndex type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOptionsComposite_005f0.setMapIndex(true);
      int[] _jspx_push_body_count_config_005fgetOptionsComposite_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOptionsComposite_005f0 = _jspx_th_config_005fgetOptionsComposite_005f0.doStartTag();
        if (_jspx_th_config_005fgetOptionsComposite_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOptionsComposite_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOptionsComposite_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOptionsComposite_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOptionsComposite_0026_005fvar_005fname_005fmapIndex_005fnobody.reuse(_jspx_th_config_005fgetOptionsComposite_005f0);
      _jspx_th_config_005fgetOptionsComposite_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOptionsComposite_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOptionsComposite_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005ffacetHistoryMetrics_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:facetHistoryMetrics
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.PrepareHistoryMetrics _jspx_th_plma_005ffacetHistoryMetrics_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.PrepareHistoryMetrics) _005fjspx_005ftagPool_005fplma_005ffacetHistoryMetrics_0026_005fnbCategories_005ffeeds_005ffacet_005fcolorMappings_005faggregation_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.PrepareHistoryMetrics.class);
    boolean _jspx_th_plma_005ffacetHistoryMetrics_005f0_reused = false;
    try {
      _jspx_th_plma_005ffacetHistoryMetrics_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005ffacetHistoryMetrics_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(29,0) name = facet type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ffacetHistoryMetrics_005f0.setFacet((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(29,0) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ffacetHistoryMetrics_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(29,0) name = colorMappings type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ffacetHistoryMetrics_005f0.setColorMappings((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${colors}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(29,0) name = nbCategories type = java.lang.Integer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ffacetHistoryMetrics_005f0.setNbCategories(((java.lang.Integer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${limit}", int.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).intValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(29,0) name = aggregation type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ffacetHistoryMetrics_005f0.setAggregation((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${aggregation}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005ffacetHistoryMetrics_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005ffacetHistoryMetrics_005f0 = _jspx_th_plma_005ffacetHistoryMetrics_005f0.doStartTag();
        if (_jspx_th_plma_005ffacetHistoryMetrics_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005ffacetHistoryMetrics_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005ffacetHistoryMetrics_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005ffacetHistoryMetrics_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005ffacetHistoryMetrics_0026_005fnbCategories_005ffeeds_005ffacet_005fcolorMappings_005faggregation_005fnobody.reuse(_jspx_th_plma_005ffacetHistoryMetrics_005f0);
      _jspx_th_plma_005ffacetHistoryMetrics_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005ffacetHistoryMetrics_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005ffacetHistoryMetrics_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fwidget_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:widget
    com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag _jspx_th_widget_005fwidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag) _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss.get(com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag.class);
    boolean _jspx_th_widget_005fwidget_005f0_reused = false;
    try {
      _jspx_th_widget_005fwidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fwidget_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(31,0) name = varUcssId type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setVarUcssId("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(31,0) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setExtraCss("plmaFacetHistoryMetrics");
      int[] _jspx_push_body_count_widget_005fwidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fwidget_005f0 = _jspx_th_widget_005fwidget_005f0.doStartTag();
        if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fwidget_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fwidget_005f0);
          }
          do {
            out.write('\r');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_widget_005fcontent_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_widget_005fwidget_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fwidget_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fwidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fwidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fwidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fwidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fextraCss.reuse(_jspx_th_widget_005fwidget_005f0);
      _jspx_th_widget_005fwidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fwidget_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fwidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fcontent_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:content
    com.exalead.cv360.searchui.view.jspapi.widget.ContentTag _jspx_th_widget_005fcontent_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.ContentTag) _005fjspx_005ftagPool_005fwidget_005fcontent_0026_005fextraCss.get(com.exalead.cv360.searchui.view.jspapi.widget.ContentTag.class);
    boolean _jspx_th_widget_005fcontent_005f0_reused = false;
    try {
      _jspx_th_widget_005fcontent_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fcontent_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(32,1) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fcontent_005f0.setExtraCss("metrics");
      int[] _jspx_push_body_count_widget_005fcontent_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fcontent_005f0 = _jspx_th_widget_005fcontent_005f0.doStartTag();
        if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fcontent_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fcontent_005f0);
          }
          do {
            out.write("		\r\n");
            out.write("		<div class=\"top-bar\">\r\n");
            out.write("\r\n");
            out.write("			<span class=\"metric\"></span>\r\n");
            out.write("			<span class=\"link ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${linkIcon}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" onclick=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${linkValue}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" title=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${linkTitle}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\"></span>\r\n");
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_c_005fif_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("		</div>\r\n");
            out.write("		<div class=\"middle-lane\">\r\n");
            out.write("			<span class=\"title widgetTitle\">");
            if (_jspx_meth_config_005fgetOption_005f11(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("		</div>\r\n");
            out.write("		<div id=\"chart-container-");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\" class=\"chart-container\">\r\n");
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_list_005fnew_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_list_005fnew_005f1(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_list_005fadd_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_list_005fnew_005f2(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_list_005fnew_005f3(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fnew_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fput_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fput_005f1(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fput_005f2(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fput_005f3(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fput_005f4(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fput_005f5(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fput_005f6(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fput_005f7(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fput_005f8(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fput_005f9(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            if (_jspx_meth_map_005fput_005f10(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			\r\n");
            out.write("			");
            if (_jspx_meth_plma_005fhighchartsJSON_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("			\r\n");
            out.write("		</div>\r\n");
            out.write("\r\n");
            out.write("		<div class=\"doc-container hidden\">\r\n");
            out.write("			<div class=\"container\">");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${doc}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("</div>\r\n");
            out.write("			<span class=\"close-doc fonticon fonticon-cancel\" title=\"");
            if (_jspx_meth_i18n_005fmessage_005f1(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\"></span>\r\n");
            out.write("		</div>\r\n");
            out.write("\r\n");
            out.write("		");
            if (_jspx_meth_render_005frenderScript_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write('\r');
            out.write('\n');
            out.write('	');
            int evalDoAfterBody = _jspx_th_widget_005fcontent_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fcontent_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fcontent_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fcontent_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fcontent_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fcontent_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fcontent_0026_005fextraCss.reuse(_jspx_th_widget_005fcontent_005f0);
      _jspx_th_widget_005fcontent_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fcontent_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fcontent_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(38,3) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${displayDoc}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("				");
          if (_jspx_meth_i18n_005fmessage_005f0(_jspx_th_c_005fif_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("\r\n");
          out.write("				<span class=\"doc-button fonticon fonticon-info\" title=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${docTitle}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"></span>\r\n");
          out.write("			");
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f0 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f0_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f0.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(39,4) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setVar("docTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(39,4) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setCode("widget.metrics.menu.documentation");
      int[] _jspx_push_body_count_i18n_005fmessage_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f0 = _jspx_th_i18n_005fmessage_005f0.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f0);
      _jspx_th_i18n_005fmessage_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f0, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f11 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f11_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f11.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f11.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(44,35) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setName("title");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(44,35) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f11.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f11 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f11 = _jspx_th_config_005fgetOption_005f11.doStartTag();
        if (_jspx_th_config_005fgetOption_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f11[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f11.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f11.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f11);
      _jspx_th_config_005fgetOption_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f11, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fnew_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:new
    com.exalead.cv360.searchui.view.jspapi.list.NewTag _jspx_th_list_005fnew_005f0 = (com.exalead.cv360.searchui.view.jspapi.list.NewTag) _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.NewTag.class);
    boolean _jspx_th_list_005fnew_005f0_reused = false;
    try {
      _jspx_th_list_005fnew_005f0.setPageContext(_jspx_page_context);
      _jspx_th_list_005fnew_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(48,3) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fnew_005f0.setVar("categoriesOrderAsList");
      int[] _jspx_push_body_count_list_005fnew_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fnew_005f0 = _jspx_th_list_005fnew_005f0.doStartTag();
        if (_jspx_th_list_005fnew_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fnew_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fnew_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fnew_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_list_005fnew_005f0);
      _jspx_th_list_005fnew_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fnew_005f0, _jsp_getInstanceManager(), _jspx_th_list_005fnew_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fnew_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:new
    com.exalead.cv360.searchui.view.jspapi.list.NewTag _jspx_th_list_005fnew_005f1 = (com.exalead.cv360.searchui.view.jspapi.list.NewTag) _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.NewTag.class);
    boolean _jspx_th_list_005fnew_005f1_reused = false;
    try {
      _jspx_th_list_005fnew_005f1.setPageContext(_jspx_page_context);
      _jspx_th_list_005fnew_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(49,3) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fnew_005f1.setVar("yAxisLabelsAsList");
      int[] _jspx_push_body_count_list_005fnew_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fnew_005f1 = _jspx_th_list_005fnew_005f1.doStartTag();
        if (_jspx_th_list_005fnew_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fnew_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fnew_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fnew_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_list_005fnew_005f1);
      _jspx_th_list_005fnew_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fnew_005f1, _jsp_getInstanceManager(), _jspx_th_list_005fnew_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fadd_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:add
    com.exalead.cv360.searchui.view.jspapi.list.AddTag _jspx_th_list_005fadd_005f0 = (com.exalead.cv360.searchui.view.jspapi.list.AddTag) _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.AddTag.class);
    boolean _jspx_th_list_005fadd_005f0_reused = false;
    try {
      _jspx_th_list_005fadd_005f0.setPageContext(_jspx_page_context);
      _jspx_th_list_005fadd_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(50,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f0.setValue("");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(50,3) name = list type = java.util.Collection reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f0.setList((java.util.Collection) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${yAxisLabelsAsList}", java.util.Collection.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_list_005fadd_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fadd_005f0 = _jspx_th_list_005fadd_005f0.doStartTag();
        if (_jspx_th_list_005fadd_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fadd_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fadd_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fadd_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.reuse(_jspx_th_list_005fadd_005f0);
      _jspx_th_list_005fadd_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fadd_005f0, _jsp_getInstanceManager(), _jspx_th_list_005fadd_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fnew_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:new
    com.exalead.cv360.searchui.view.jspapi.list.NewTag _jspx_th_list_005fnew_005f2 = (com.exalead.cv360.searchui.view.jspapi.list.NewTag) _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.NewTag.class);
    boolean _jspx_th_list_005fnew_005f2_reused = false;
    try {
      _jspx_th_list_005fnew_005f2.setPageContext(_jspx_page_context);
      _jspx_th_list_005fnew_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(51,3) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fnew_005f2.setVar("forceRefineOnFeeds");
      int[] _jspx_push_body_count_list_005fnew_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fnew_005f2 = _jspx_th_list_005fnew_005f2.doStartTag();
        if (_jspx_th_list_005fnew_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fnew_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fnew_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fnew_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_list_005fnew_005f2);
      _jspx_th_list_005fnew_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fnew_005f2, _jsp_getInstanceManager(), _jspx_th_list_005fnew_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fnew_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:new
    com.exalead.cv360.searchui.view.jspapi.list.NewTag _jspx_th_list_005fnew_005f3 = (com.exalead.cv360.searchui.view.jspapi.list.NewTag) _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.NewTag.class);
    boolean _jspx_th_list_005fnew_005f3_reused = false;
    try {
      _jspx_th_list_005fnew_005f3.setPageContext(_jspx_page_context);
      _jspx_th_list_005fnew_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(52,3) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fnew_005f3.setVar("forceRefineOnFacets");
      int[] _jspx_push_body_count_list_005fnew_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fnew_005f3 = _jspx_th_list_005fnew_005f3.doStartTag();
        if (_jspx_th_list_005fnew_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fnew_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fnew_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fnew_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_list_005fnew_005f3);
      _jspx_th_list_005fnew_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fnew_005f3, _jsp_getInstanceManager(), _jspx_th_list_005fnew_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fnew_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:new
    com.exalead.cv360.searchui.view.jspapi.map.NewTag _jspx_th_map_005fnew_005f0 = (com.exalead.cv360.searchui.view.jspapi.map.NewTag) _005fjspx_005ftagPool_005fmap_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.NewTag.class);
    boolean _jspx_th_map_005fnew_005f0_reused = false;
    try {
      _jspx_th_map_005fnew_005f0.setPageContext(_jspx_page_context);
      _jspx_th_map_005fnew_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(53,3) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fnew_005f0.setVar("serieConfig");
      int[] _jspx_push_body_count_map_005fnew_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fnew_005f0 = _jspx_th_map_005fnew_005f0.doStartTag();
        if (_jspx_th_map_005fnew_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fnew_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fnew_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fnew_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_map_005fnew_005f0);
      _jspx_th_map_005fnew_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fnew_005f0, _jsp_getInstanceManager(), _jspx_th_map_005fnew_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f0 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f0_reused = false;
    try {
      _jspx_th_map_005fput_005f0.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(55,3) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f0.setKey("facetId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(55,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(55,3) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f0.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f0 = _jspx_th_map_005fput_005f0.doStartTag();
        if (_jspx_th_map_005fput_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f0);
      _jspx_th_map_005fput_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f0, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f1 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f1_reused = false;
    try {
      _jspx_th_map_005fput_005f1.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(56,3) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f1.setKey("aggregation");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(56,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f1.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${aggregation}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(56,3) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f1.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f1 = _jspx_th_map_005fput_005f1.doStartTag();
        if (_jspx_th_map_005fput_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f1);
      _jspx_th_map_005fput_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f1, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f2 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f2_reused = false;
    try {
      _jspx_th_map_005fput_005f2.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(57,3) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f2.setKey("representation");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(57,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f2.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${chartType}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(57,3) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f2.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f2 = _jspx_th_map_005fput_005f2.doStartTag();
        if (_jspx_th_map_005fput_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f2);
      _jspx_th_map_005fput_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f2, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f3 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f3_reused = false;
    try {
      _jspx_th_map_005fput_005f3.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(58,3) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f3.setKey("axis");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(58,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f3.setValue("0");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(58,3) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f3.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f3 = _jspx_th_map_005fput_005f3.doStartTag();
        if (_jspx_th_map_005fput_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f3);
      _jspx_th_map_005fput_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f3, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f4 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f4_reused = false;
    try {
      _jspx_th_map_005fput_005f4.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(59,3) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f4.setKey("stacking");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(59,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f4.setValue("Disabled");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(59,3) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f4.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f4 = _jspx_th_map_005fput_005f4.doStartTag();
        if (_jspx_th_map_005fput_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f4);
      _jspx_th_map_005fput_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f4, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f5 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f5_reused = false;
    try {
      _jspx_th_map_005fput_005f5.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(60,3) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f5.setKey("stack");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(60,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f5.setValue("0");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(60,3) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f5.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f5 = _jspx_th_map_005fput_005f5.doStartTag();
        if (_jspx_th_map_005fput_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f5);
      _jspx_th_map_005fput_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f5, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f6 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f6_reused = false;
    try {
      _jspx_th_map_005fput_005f6.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(61,3) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f6.setKey("legend");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(61,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f6.setValue("");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(61,3) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f6.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f6 = _jspx_th_map_005fput_005f6.doStartTag();
        if (_jspx_th_map_005fput_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f6);
      _jspx_th_map_005fput_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f6, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f7 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f7_reused = false;
    try {
      _jspx_th_map_005fput_005f7.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(62,3) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f7.setKey("pointLegend");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(62,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f7.setValue("");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(62,3) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f7.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f7 = _jspx_th_map_005fput_005f7.doStartTag();
        if (_jspx_th_map_005fput_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f7);
      _jspx_th_map_005fput_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f7, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f8 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f8_reused = false;
    try {
      _jspx_th_map_005fput_005f8.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(63,3) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f8.setKey("colorConfig");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(63,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f8.setValue("");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(63,3) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f8.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f8 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f8 = _jspx_th_map_005fput_005f8.doStartTag();
        if (_jspx_th_map_005fput_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f8[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f8.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f8.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f8);
      _jspx_th_map_005fput_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f8, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f9 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f9_reused = false;
    try {
      _jspx_th_map_005fput_005f9.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(64,3) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f9.setKey("maxCategories");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(64,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f9.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${limit}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(64,3) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f9.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f9 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f9 = _jspx_th_map_005fput_005f9.doStartTag();
        if (_jspx_th_map_005fput_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f9[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f9.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f9.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f9);
      _jspx_th_map_005fput_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f9, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_map_005fput_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  map:put
    com.exalead.cv360.searchui.view.jspapi.map.PutTag _jspx_th_map_005fput_005f10 = (com.exalead.cv360.searchui.view.jspapi.map.PutTag) _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.map.PutTag.class);
    boolean _jspx_th_map_005fput_005f10_reused = false;
    try {
      _jspx_th_map_005fput_005f10.setPageContext(_jspx_page_context);
      _jspx_th_map_005fput_005f10.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(65,3) name = key type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f10.setKey("takeFirstValues");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(65,3) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f10.setValue("true");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(65,3) name = map type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_map_005fput_005f10.setMap((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_map_005fput_005f10 = new int[] { 0 };
      try {
        int _jspx_eval_map_005fput_005f10 = _jspx_th_map_005fput_005f10.doStartTag();
        if (_jspx_th_map_005fput_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_map_005fput_005f10[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_map_005fput_005f10.doCatch(_jspx_exception);
      } finally {
        _jspx_th_map_005fput_005f10.doFinally();
      }
      _005fjspx_005ftagPool_005fmap_005fput_0026_005fvalue_005fmap_005fkey_005fnobody.reuse(_jspx_th_map_005fput_005f10);
      _jspx_th_map_005fput_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_map_005fput_005f10, _jsp_getInstanceManager(), _jspx_th_map_005fput_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fhighchartsJSON_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:highchartsJSON
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsJsonTag _jspx_th_plma_005fhighchartsJSON_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsJsonTag) _005fjspx_005ftagPool_005fplma_005fhighchartsJSON_0026_005fvar_005ftakeFirstValues_005fmaxCategories_005fforceRefineOnFeeds_005fforceRefineOnFacets_005ffeeds_005fcategoriesOrder_005fbaseUrl_005faxisLabelsY_005faxisLabelX_005fadditionalCategories.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsJsonTag.class);
    boolean _jspx_th_plma_005fhighchartsJSON_005f0_reused = false;
    try {
      _jspx_th_plma_005fhighchartsJSON_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fhighchartsJSON_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(67,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setVar("highchartsJSON");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(67,3) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(67,3) name = categoriesOrder type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setCategoriesOrder((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoriesOrderAsList}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(67,3) name = axisLabelX type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setAxisLabelX("");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(67,3) name = axisLabelsY type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setAxisLabelsY((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${yAxisLabelsAsList}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(67,3) name = maxCategories type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setMaxCategories(((java.lang.Integer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${limit}", int.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).intValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(67,3) name = takeFirstValues type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setTakeFirstValues(java.lang.Boolean.valueOf("true"));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(67,3) name = additionalCategories type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setAdditionalCategories("Hide");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(67,3) name = baseUrl type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setBaseUrl("");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(67,3) name = forceRefineOnFeeds type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setForceRefineOnFeeds((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${forceRefineOnFeeds}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(67,3) name = forceRefineOnFacets type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsJSON_005f0.setForceRefineOnFacets((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${forceRefineOnFacets}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fhighchartsJSON_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fhighchartsJSON_005f0 = _jspx_th_plma_005fhighchartsJSON_005f0.doStartTag();
        if (_jspx_eval_plma_005fhighchartsJSON_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_plma_005fhighchartsJSON_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_plma_005fhighchartsJSON_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_plma_005fhighchartsJSON_005f0);
          }
          do {
            out.write("\r\n");
            out.write("				\r\n");
            out.write("				");
            if (_jspx_meth_plma_005fhighchartsSerie_005f0(_jspx_th_plma_005fhighchartsJSON_005f0, _jspx_page_context, _jspx_push_body_count_plma_005fhighchartsJSON_005f0))
              return true;
            out.write("\r\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_plma_005fhighchartsJSON_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_plma_005fhighchartsJSON_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_plma_005fhighchartsJSON_005f0[0]--;
          }
        }
        if (_jspx_th_plma_005fhighchartsJSON_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fhighchartsJSON_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fhighchartsJSON_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fhighchartsJSON_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fhighchartsJSON_0026_005fvar_005ftakeFirstValues_005fmaxCategories_005fforceRefineOnFeeds_005fforceRefineOnFacets_005ffeeds_005fcategoriesOrder_005fbaseUrl_005faxisLabelsY_005faxisLabelX_005fadditionalCategories.reuse(_jspx_th_plma_005fhighchartsJSON_005f0);
      _jspx_th_plma_005fhighchartsJSON_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fhighchartsJSON_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fhighchartsJSON_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fhighchartsSerie_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_plma_005fhighchartsJSON_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_plma_005fhighchartsJSON_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:highchartsSerie
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsSerieTag _jspx_th_plma_005fhighchartsSerie_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsSerieTag) _005fjspx_005ftagPool_005fplma_005fhighchartsSerie_0026_005fserieConfig_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsSerieTag.class);
    boolean _jspx_th_plma_005fhighchartsSerie_005f0_reused = false;
    try {
      _jspx_th_plma_005fhighchartsSerie_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fhighchartsSerie_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_plma_005fhighchartsJSON_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(79,4) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsSerie_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(79,4) name = serieConfig type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fhighchartsSerie_005f0.setSerieConfig((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${serieConfig}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fhighchartsSerie_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fhighchartsSerie_005f0 = _jspx_th_plma_005fhighchartsSerie_005f0.doStartTag();
        if (_jspx_th_plma_005fhighchartsSerie_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fhighchartsSerie_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fhighchartsSerie_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fhighchartsSerie_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fhighchartsSerie_0026_005fserieConfig_005ffeeds_005fnobody.reuse(_jspx_th_plma_005fhighchartsSerie_005f0);
      _jspx_th_plma_005fhighchartsSerie_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fhighchartsSerie_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fhighchartsSerie_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f1 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f1_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f1.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(86,59) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setCode("generic.close");
      int[] _jspx_push_body_count_i18n_005fmessage_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f1 = _jspx_th_i18n_005fmessage_005f1.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f1);
      _jspx_th_i18n_005fmessage_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f1, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f0_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(89,2) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f0.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f0 = _jspx_th_render_005frenderScript_005f0.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f0);
          }
          do {
            out.write("\r\n");
            out.write("			var options = {};\r\n");
            out.write("			options.tooltip = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tooltip}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(";\r\n");
            out.write("			options.colors = [];\r\n");
            out.write("			");
            if (_jspx_meth_c_005fforEach_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("			options.limit = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${limit}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("			options.reverse = 'true';\r\n");
            out.write("			options.hideChart = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hideChart}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("			var plmaFacetHistoryMetrics = new PLMAFacetHistoryMetrics('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write('\'');
            out.write(',');
            out.write('\'');
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${highchartsJSON}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',options);\r\n");
            out.write("			plmaFacetHistoryMetrics.init();\r\n");
            out.write("		");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f0);
      _jspx_th_render_005frenderScript_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f0_reused = false;
    try {
      _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(93,3) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(93,3) '${colors}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${colors}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaFacetHistoryMetrics/widget.jsp(93,3) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setVar("color");
      int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
        if (_jspx_eval_c_005fforEach_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("				var colorTemp = {};\r\n");
            out.write("				colorTemp.value = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${color.color}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("				colorTemp.start = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${color.startRange}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("				colorTemp.end = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${color.endRange}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("				options.colors.push(colorTemp);\r\n");
            out.write("			");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
      _jspx_th_c_005fforEach_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f0_reused);
    }
    return false;
  }
}
