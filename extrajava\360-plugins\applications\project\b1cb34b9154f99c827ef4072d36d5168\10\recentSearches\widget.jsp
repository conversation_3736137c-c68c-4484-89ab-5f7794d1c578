<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<config:getOption var="mode" name="mode" />
<config:getOption var="tooltipTitle" name="tooltipTitle" />
<config:getOption name="entryUrl" var="entryUrl" defaultValue="window.location.pathname + window.location.search + window.location.hash" />
<config:getOption name="metaForId" var="metaForId" />
<config:getOption name="multiselect" var="multiselect" />
<config:getOption name="iconCssFormultipleCompare" var="iconCssFormultipleCompare" />
<config:getOption name="displayHitsMode" var="displayHitsMode" defaultValue="default"/>

<render:import varWidget="widget" varFeeds="feeds"/>
<request:getParameterValue var="loadColletionDetailsView" name="loadColletionDetailsView" defaultValue="false"/>

<search:getFeed var="feed" feeds="${feeds}" />
<search:getEntry var="entry" feeds="${feeds}" />
<search:getMetaValue var="entryId" entry="${entry}" metaName="${metaForId}" />
<c:set var="query" value="${pageContext.request.queryString}" />
<%-- Set default values to adapt(old modes) with the new backend code of hitsCollection --%>
<c:choose>
	<c:when test="${mode == 'getter' || mode == 'listener'}">
		<config:setOption name="collectionDef" value="history##db##recent_searches_history##remove-old##fonticon-navigation-history##1##user" component="${widget}"/>
		<config:setOption name="buttonsDef" value="##" component="${widget}"/>
	</c:when>
	<c:when test="${mode == 'pin' || mode == 'pins_getter'}">
		<config:setOption name="collectionDef" value="pin##db##recent_searches_pins##remove-old##fonticon-favorite-on##1##user" component="${widget}"/>
		<config:setOption name="buttonsDef" value="fonticon-favorite-on##fonticon-favorite-off" component="${widget}"/>
	</c:when>
</c:choose>
<config:getOptionComposite name="collectionDef" var="collectionDef" mapIndex="true" doEval="true"/>
<config:getOptionComposite name="buttonsDef" var="buttonsDef" mapIndex="true" doEval="true"/>
<c:set var="wuidSubWidget" value=""/>

<c:choose>
	<c:when test="${mode == 'custom' || mode == 'getter' || mode == 'pins_getter'}">
		<widget:widget varUcssId="uCssId" extraCss="recentSearches ${mode} collection-disabled ${collectionDef.id}">
			<%-- <div class="collection-overlay"></div> --%>
			<c:choose>
				<c:when test="${tooltipTitle !=null}">
					<div class="recent-header collection-header" title="${tooltipTitle}">
				</c:when>
				<c:otherwise>
					<div class="recent-header collection-header">
				</c:otherwise>
			</c:choose>
				<span class="action-details label-section ">
					<span class="fonticon ${collectionDef.iconCss}">
						<span class="counter">-</span>
					</span>
					<span class="title"><config:getOption name="title" defaultValue=""/></span>

				</span>
				<c:if test="${multiselect == 'true'}">

				<span class="fonticon fonticon-open-down" ></span>
				<div class="button-popup hidden">
					<div class="action-details-multi-select label-section addEntries" title="<i18n:message code='plma.hits.collection.labels.add-entry' text='${defaultText}'/>">
					<span class="multiselect-fonticon fonticon ${iconCssFormultipleCompare}"> </span><span class="multiselect-title"></span>
					</div>
				<div class="action-details-multi-select label-section clearEntries" title="<i18n:message code='plma.hits.collection.labels.trashTitle' text='${defaultText}'/>"">

					<span class="multiselect-fonticon fonticon fonticon-trash"> </span><span class="multiselect-title"></span>
				</div>
				</div>
			</c:if>
				<i18n:message var="defaultText" code="recentSearches.delete"/>
				<span class="action-clear fonticon fonticon-trash" title="<i18n:message code='plma.hits.collection.labels.trashTitle' text='${defaultText}'/>"></span>
			</div>
			<div class="collection-details">
				<c:if test="${loadColletionDetailsView == 'true'}">
					<render:template template="templates/hits.jsp">
						<render:parameter name="feed" value="${feed}"/>
						<render:parameter name="iconCss" value="${collectionDef.iconCss}"/>
						<render:parameter name="collectionId" value="${collectionDef.id}"/>
					</render:template>
				</c:if>
				<c:if test="${displayHitsMode == 'subwidget'}">
					<widget:forEachSubWidget iteration="1">
						<widget:getUcssId var="wuidSubWidget"/>
						<div class="hidden wuid ${wuidSubWidget}"></div>
					</widget:forEachSubWidget>
				</c:if>
			</div>
		</widget:widget>
	</c:when>
	<c:when test="${mode == 'pin'}">
		<widget:widget varUcssId="uCssId" extraCss="collection-modifier pin fonticon"></widget:widget>
		<render:renderScript position="READY">
			window.HitsCollectionManager.getCollection('${collectionDef.id}').then(function(helper){
				helper.registerButton({
					cssSelector: '.${uCssId}.collection-modifier',
					entry: {
						id: '${entryId}',
						url: ${entryUrl}
					}
				});
			});
		</render:renderScript>
		<c:set var="uCssId" value=""/>
	</c:when>
	<c:when test="${mode == 'listener'}">
		<render:renderScript position="READY">
		window.HitsCollectionManager.getCollection('${collectionDef.id}').then(function(helper){
			helper.addEntry({
				id: '${entryId}',
				url: ${entryUrl}
			});
		});
		</render:renderScript>
	</c:when>
</c:choose>

<c:if test="${loadColletionDetailsView == 'false'}">
	<render:renderScript position="BEFORE">
		<render:renderOnce id="${collectionDef.id}">

		<config:getOption name="maxSearches" var="limit" defaultValue="10"/>
		<config:getOption name="elemPerPage" var="elemPerPage" defaultValue="4" />
		<config:getOption name="activateToggle" var="activateToggle" defaultValue="false"/>
		<config:getOption name="showInPopup" var="showInPopup" defaultValue="false"/>
		<config:getOption name="onInit" var="onInit" defaultValue="undefined"/>
		<config:getOption name="onDbUpdate" var="onDbUpdate" defaultValue="undefined"/>
		<config:getOption name="onShow" var="onShow" defaultValue="undefined"/>
		<config:getOption name="onHide" var="onHide" defaultValue="undefined"/>
		<config:getOption name="onAfterShow" var="onAfterShow" defaultValue="undefined"/>
		<config:getOption name="disableAlert" var="disableAlert" defaultValue="false"/>
		<c:set var="minDisplaySize" value="1"/>
		<c:if test="${not empty collectionDef.minDisplaySize}">
			<c:set var="minDisplaySize" value="${collectionDef.minDisplaySize}"/>
		</c:if>
		<c:set var="storeType" value="user"/>
        <c:if test="${not empty collectionDef.storeType}">
			<c:set var="storeType" value="${collectionDef.storeType}"/>
        </c:if>
		window.HitsCollectionManager.createCollection({
			uCssId: '${uCssId}',
			id:'${collectionDef.id}',
			store: '${collectionDef.store}',
			storeKey: '${collectionDef.storekey}',
			storeType: '${storeType}',
			limit: ${limit},
			tooltipTitle: '${tooltipTitle}',
			minDisplaySize: ${minDisplaySize},
			limitStrategy: '${collectionDef.limitStrategy}',
			disableAlert: ${disableAlert},
			buttonConfig:{
				entryPresentIcon: '${buttonsDef.entryPresentIcon}',
				entryAbsentIcon: '${buttonsDef.entryAbsentIcon}'
			},
            labels: {
            	label: "<config:getOption name='title' defaultValue=''/>",
            	<i18n:message var='defaultText' code='plma.hits.collection.labels.counter'/>
            	counter: "<i18n:message code='plma.hits.collection.labels.counter.${collectionDef.id}' text='${defaultText}'/>",
            	<i18n:message var='defaultText' code='plma.hits.collection.labels.tooltip'/>
            	tooltip: "<i18n:message code='plma.hits.collection.labels.tooltip.${collectionDef.id}' text='${defaultText}'/>",
            	<i18n:message var='defaultText' code='plma.hits.collection.labels.emptyCollection'/>
            	emptyCollection: "<i18n:message code='plma.hits.collection.labels.emptyCollection.${collectionDef.id}' text='${defaultText}'/>",
            	<i18n:message var='defaultText' code='plma.hits.collection.add-entry'/>
            	addEntry: "<i18n:message code='plma.hits.collection.add-entry.${collectionDef.id}' text='${defaultText}'/>",
            	<i18n:message var='defaultText' code='plma.hits.collection.remove-entry'/>
            	removeEntry: "<i18n:message code='plma.hits.collection.remove-entry.${collectionDef.id}' text='${defaultText}'/>",
				<i18n:message var='defaultText' code='plma.hits.collection.labels.add-multiple-entry'/>
            	addMultipleEntry: "<i18n:message code='plma.hits.collection.labels.add-multiple-entry.${collectionDef.id}' text='${defaultText}'/>",
				<i18n:message var='defaultText' code='plma.hits.collection.labels.remove-multiple-entry'/>
            	clearMultipleEntry: "<i18n:message code='plma.hits.collection.labels.remove-multiple-entry.${collectionDef.id}' text='${defaultText}'/>",
				<i18n:message var='defaultText' code='plma.hits.collection.labels.open-collection-option'/>
            	collectionOptions: "<i18n:message code='plma.hits.collection.labels.open-collection-option.${collectionDef.id}' text='${defaultText}'/>"
             },
            alerts: {
            	<i18n:message var='defaultText' code='plma.hits.collection.alerts.add'/>
            	add: "<i18n:message code='plma.hits.collection.alerts.add.${collectionDef.id}' text='${defaultText}'/>",
            	<i18n:message var='defaultText' code='plma.hits.collection.alerts.remove'/>
            	remove: "<i18n:message code='plma.hits.collection.alerts.remove.${collectionDef.id}' text='${defaultText}'/>",
            	<i18n:message var='defaultText' code='plma.hits.collection.alerts.removeAll'/>
            	removeAll: "<i18n:message code='plma.hits.collection.alerts.removeAll.${collectionDef.id}' text='${defaultText}'/>",
            	<i18n:message var='defaultText' code='plma.hits.collection.alerts.limitReached'/>
            	limitReached: "<i18n:message code='plma.hits.collection.alerts.limitReached.${collectionDef.id}' text='${defaultText}'/>",
            	<i18n:message var='defaultText' code='plma.hits.collection.alerts.removeOld'/>
            	removeOld: "<i18n:message code='plma.hits.collection.alerts.removeOld.${collectionDef.id}' text='${defaultText}'/>",
            	<i18n:message var='defaultText' code='plma.hits.collection.alerts.minDisplaySize'/>
            	minDisplaySize: "<i18n:message code='plma.hits.collection.alerts.minDisplaySize.${collectionDef.id}' text='${defaultText}'/>"
            },
			detailsView: {
				activateToggle: ${activateToggle},
				displayHitsMode: '${displayHitsMode}',
				metaForId: '${metaForId}',
				<c:choose>
					<c:when test="${displayHitsMode == 'default'}">
						showInPopup: ${showInPopup},
						feedName: '${feed.id}',
						elemPerPage: ${elemPerPage}						
					</c:when>
					<c:when test="${displayHitsMode == 'subwidget'}">
						showInPopup: ${showInPopup},
						feedName: '${feed.id}',
						containerCss: '${wuidSubWidget}',
						onAfterShow: ${onAfterShow},
						onHide: ${onHide},
					</c:when>
					<c:when test="${displayHitsMode == 'custom'}">
						showInPopup: false,
						onInit: ${onInit},
						onShow: ${onShow},
						onHide: ${onHide},
						onDbUpdate: ${onDbUpdate}
					</c:when>
				</c:choose>				
			}				
		});
		</render:renderOnce>
	</render:renderScript>
</c:if>