@import "../polyfills.less";
/**
 * Style specifically for PLMA search pages, based on plmaDisplayHits.
 */
.flexPanelsContainer > .flexPanelsContainer-row > .flexPanelsContainer-item{
	.detailPanel{
		.flex-basis(450px);
		-webkit-transition: flex 0.5s ease-in-out;
	    -moz-transition: flex 0.5s ease-in-out;
	    -ms-transition: flex 0.5s ease-in-out;
	    -o-transition: flex 0.5s ease-in-out;
	}
}

.flexPanelsContainer > .flexPanelsContainer-row{
	.container-second-flex{
		.flexPanelsContainer-row{
			.flex-flow(row wrap-reverse);
		}
	}
}

.flexPanelsContainer > .flexPanelsContainer-row > .flexPanelsContainer-item{
	.customDisplayHits{
		.flex-basis(350px);
		-webkit-transition: flex 0.5s ease-in-out;
	    -moz-transition: flex 0.5s ease-in-out;
	    -ms-transition: flex 0.5s ease-in-out;
	    -o-transition: flex 0.5s ease-in-out;
	}
}
.plmaDisplayHits {
    .results-panel {
        .resultsTitle{
            .title{
                vertical-align: top;
                font-size: 17px;
            }
        }
                
    }
    .details-panel {
        
        /* In collapsible blocks, we don't want resultCarousel
         * to change the page width. Hence position:absolute.
         */
        .collapsibleBlock {
            .resultCarousel {
                height: 210px;
                overflow: auto;
                position: relative;
                
                > .widgetContent {
                    position: absolute;
                    height: 100%;
				    width: 100%;
				    box-sizing: border-box;
                }
            }
        }
    }
    
}

.customHitPanel-hidden(){
	&.isDetailOpen{
		.flex-basis(0);
    	.flex-grow(0);
    	-webkit-transition: flex 0.5s ease-in-out;
	    -moz-transition: flex 0.5s ease-in-out;
	    -ms-transition: flex 0.5s ease-in-out;
	    -o-transition: flex 0.5s ease-in-out;
	}
}
	
.customDetailHitPanel-hidden(){
	.hitDetailInfo{
		.closeDetailSmallArrow{
			&.fonticon{
				display: inline-block;
				opacity: 1;
			}
		}
	}
	.closeHitsArrow{
		display: none;
		opacity: 0;
	}
}
	
@media (max-width: 950px) {  
	.flexPanelsContainer > .flexPanelsContainer-row > .flexPanelsContainer-item .customDisplayHits {
		.customHitPanel-hidden();
	}
	.flexPanelsContainer > .flexPanelsContainer-row > .flexPanelsContainer-item .detailPanel  {
		.customDetailHitPanel-hidden();
	}
}