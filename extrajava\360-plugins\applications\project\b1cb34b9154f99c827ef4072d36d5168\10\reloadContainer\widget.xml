<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Reload Container" group="PLM Analytics/Results Rendering" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <Description>This widget reloads itself and its subwidgets with parameters on JavaScript event trigger.</Description>

    <Includes>
        <Include type="js" path="js/reloadContainer.js" />
    </Includes>

    <SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
    <SupportWidgetsId arity="ZERO_OR_MANY" />
    <SupportI18N supported="true" />

    <Dependencies>
        <Widget name="plmaResources" />
    </Dependencies>

    <OptionsGroup name="General">
        <Option id="selector" name="Custom selector" arity="ZERO_OR_ONE">
            <Description>You can use a custom CSS selector for the JavaScript event listener. Default is document.</Description>
        </Option>
        <Option id="eventName" name="Event Name" arity="ONE">
            <Description>Name of the event to listen. Default is 'plma:hitSelection'.</Description>
        </Option>
        <Option id="paramName" name="Parameter name" arity="ZERO_OR_ONE">
            <Description>If values are sent by the trigger, they will be added as values for this parameter name in the feed. Default is id.</Description>
        </Option>
    </OptionsGroup>

    <DefaultValues>
    </DefaultValues>

</Widget>
