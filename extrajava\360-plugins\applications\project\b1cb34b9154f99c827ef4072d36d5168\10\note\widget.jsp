<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="security" uri="http://www.exalead.com/jspapi/security" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>

<config:getOption var="cellHeight" name="cellHeight" defaultValue="50"/>

<c:set var="nbDivisions" value="12"/>
<c:set var="baseX" value="'auto'"/>
<c:set var="baseY" value="60"/>
<c:set var="marginX" value="10"/>
<c:set var="marginY" value="10"/>

<widget:widget extraCss="note" varUcssId="uCssId">
	<widget:content>
		<plma:getNoteConfig var="noteConfig"
							storageKey="note_${uCssId}"
							nbColumns="${nbDivisions}"
							cellHeight="${cellHeight}"/>

		<div class="chartboard-popup-tinymce">
			<div class="chartboard-popup-tinymce-popup">
				<div class="chartboard-tinymce-container">
					<div class="chartboard-tinymce-title"><i18n:message code="note.editor"/></div>
					<div class="chartboard-tinymce-editor-container">
						<div class="chartboard-tinymce-editor"></div>
					</div>
				</div>
			</div>
		</div>

		<div class="title"><config:getOption name="title" defaultValue=""/></div>
		<div class="action-container">
			<div class="add-note">
				<div class="grid-stack-item custom-note"
					 data-is-not-saved="true"
					 data-cell-cssclass="chartboard-tinymce-template-note"
					 data-gs-x="0"
					 data-gs-y="0"
					 data-gs-width="2"
					 data-gs-height="1">
					<div class="tile grid-stack-item-content">
						<div class="content chartboard-tinymce-template-note"><span
								class="custom-widget-title"><i18n:message code="note.editor.add" /></span></div>
						<div class="editable-custom-widget-icon fonticon fonticon-pencil"
							 title="Edit Note"></div>
					</div>
				</div>
			</div>
			<div class="delete-note">
				<div class="trash-icon fonticon fonticon-trash">
				</div>
			</div>
		</div>

		<div class="note-layout grid-stack">
			<c:set var="customWidgetCount" value="0"/>
			<c:forEach var="tileConfig" items="${noteConfig.tiles}">
				<%-- If there is a cellId, it is a normal widget --%>
				<c:set var="isCustomWidget" value="${empty tileConfig.cellId}"/>
				<%--<c:if test="${tileConfig.displayed}">--%>
				<div class="grid-stack-item custom-note"
					 <c:if test="${not empty tileConfig.cssId}">id="${tileConfig.cssId}" </c:if>
					 data-custom-cell-id="chartboard-custom-widget-${customWidgetCount}"
						<c:set var="customWidgetCount" value="${customWidgetCount + 1}"/>
					 data-cell-cssclass="${tileConfig.cssClass}"
					 data-gs-x="${tileConfig.x}"
					 data-gs-y="${tileConfig.y}"
					 data-gs-width="${tileConfig.width}"
					 data-gs-height="${tileConfig.height}"
					 data-displayed="true">
					<div class="tile grid-stack-item-content">
						<div class="content ${not empty tileConfig.cssClass ? tileConfig.cssClass : ''}">
								<%-- The text of our custom widget is in the preview prop --%>
								${tileConfig.preview}
						</div>
						<div class="editable-custom-widget-icon fonticon fonticon-pencil"
							 title="<i18n:message code="note.editor.edit"/>"></div>
					</div>
				</div>
				<%--</c:if>--%>
			</c:forEach>
		</div>
	</widget:content>

	<render:renderScript position="READY">
		var options = {};
		options.baseY = ${baseY};
		options.marginY = ${marginY};
		<%--options.originalConfig = ${not empty noteConfig && not empty noteConfig.tiles ? noteConfig.tiles : '[]'};--%>
		options.url = '<c:url value="/"/>';
		options.tinymceUrl = '<c:url value="/resources/widgets/plmaResources/lib/tinymce/tinymce.min.js"/>';
		options.user = '<string:eval string="\${security.username}" isJsEscape="true" feeds="${feeds}"/>';
		options.hasUser = '<security:isUserConnected/>';

		new Note('${uCssId}',options);
	</render:renderScript>

</widget:widget>