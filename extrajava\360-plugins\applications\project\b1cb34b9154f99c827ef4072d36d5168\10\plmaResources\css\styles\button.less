@import "variables.less";
@import "colorPreferences";

/* BUTTONS ==================================== */

/* BASE STYLES ==================================== */

.btn {
    display: inline-block;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    padding: 9px 14px 8px 14px;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
    min-width: 72px;
    line-height: 1.428571429;
    overflow: hidden;
    text-overflow: ellipsis;
}

.btn:focus {
    outline: none;
}

.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
    pointer-events: none;
    cursor: not-allowed;
    box-shadow: none;
    opacity: .65;
}

/* VARIATIONS ==================================== */

/* DEFAULT ------------------ */
.btn-default {
    color: @grey-7;
    background-color: @grey-2;
    border-color: @grey-5;
}

.btn-default:hover {
    color: @grey-7;
    background-color: @grey-3;
    border-color: @grey-6;
}

.btn-default:focus {
    color: @blue-3;
    background-color: @grey-3;
    border-color: @blue-3;
}

.btn-default:active,
.btn-default.active {
    color: @grey-7;
    background-color: @grey-5;
    border-color: @grey-7;
}

fieldset[disabled],
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
opacity: 0.4;
}

.btn-default .badge {
    color: #fff;
    background-color: @grey-7;
}

/* PRIMARY ------------------ */
.btn-primary {
    color: white;
    background-color: @blue-3;
}

.btn-primary:hover {
    color: #FFFFFF;
    border-color: @blue-4;
    background-color: @blue-4;
}

.btn-primary:focus {
    color: #FFFFFF;
    border-color: @blue-4;
    background-color: @blue-3;
}

.btn-primary:active,
.btn-primary.active {
    color: #FFFFFF;
    border-color: @blue-3;
    background-color: @blue-5;
}

.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
opacity: 0.4;
}

.btn-primary .badge {
    color: @blue-3;
    background-color: #fff;
}

/* SUCCESS ------------------ */
.btn-success {
    color: @green-1;
    border-color: @green-1;
    background-color: transparent;
}

.btn-success:hover {
    color: @green-2;
    border-color: @green-2;
    background-color: transparent;
}

.btn-success:focus {
    color: @green-2;
    border-color: @green-2;
}

.btn-success:active,
.btn-success.active {
    color: white;
    border-color: @green-2;
    background-color: @green-2;
}

.btn-success.disabled,
.btn-success[disabled],
fieldset[disabled] .btn-success,
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled:active,
.btn-success[disabled]:active,
fieldset[disabled] .btn-success:active,
.btn-success.disabled.active,
.btn-success[disabled].active,
fieldset[disabled] .btn-success.active {
opacity: 0.4;
}

.btn-success .badge {
    color: @green-1;
    background-color: #fff;
}

/* INFO ------------------ */
.btn-info {
    color: @cyan-1;
    border-color: @cyan-1;
    background-color: transparent;
}

.btn-info:hover {
    color: @cyan-2;
    border-color: @cyan-2;
    background-color: transparent;
}

.btn-info:focus {
    color: @cyan-2;
    border-color: @cyan-2;
}


.btn-info:active,
.btn-info.active {
    color: white;
    border-color: @cyan-2;
    background-color: @cyan-2;
}

.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active {
opacity: 0.4;
}

.btn-info .badge {
    color: @cyan-1;
    background-color: #fff;
}

/* WARNING ------------------ */
.btn-warning {
    color: @orange-1;
    border-color: @orange-1;
    background-color: transparent;
}

.btn-warning:hover {
    color: @orange-2;
    border-color: @orange-2;
    background-color: transparent;
}

.btn-warning:focus {
    color: @orange-2;
    border-color: @orange-2;
}

.btn-warning:active,
.btn-warning.active {
    color: white;
    border-color: @orange-2;
    background-color: @orange-2;
}

.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active {
opacity: 0.4;
}

.btn-warning .badge {
    color: @orange-1;
    background-color: #fff;
}

/* ERROR ------------------ */
.btn-error {
    color: @red-1;
    border-color: @red-1;
    background-color: transparent;
}

.btn-error:hover {
    color: @red-2;
    border-color: @red-2;
    background-color: transparent;
}

.btn-error:focus {
    color: @red-2;
    border-color: @red-2;
}

.btn-error:active,
.btn-error.active {
    color: white;
    border-color: @red-2;
    background-color: @red-2;
}

.btn-error.disabled,
.btn-error[disabled],
fieldset[disabled] .btn-error,
.btn-error.disabled:hover,
.btn-error[disabled]:hover,
fieldset[disabled] .btn-error:hover,
.btn-error.disabled:focus,
.btn-error[disabled]:focus,
fieldset[disabled] .btn-error:focus,
.btn-error.disabled:active,
.btn-error[disabled]:active,
fieldset[disabled] .btn-error:active,
.btn-error.disabled.active,
.btn-error[disabled].active,
fieldset[disabled] .btn-error.active {
opacity: 0.4;
}

.btn-error .badge {
    color: @red-1;
    background-color: #fff;
}

/* LINK ------------------ */
.btn-link {
    font-weight: normal;
    color: @blue-3;
    cursor: pointer;
    border-radius: 0;
}

.btn-link,
.btn-link:active,
.btn-link.active {
    background-color: transparent;
    box-shadow: none;
}

.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link:active,
.btn-link.active {
    border-color: transparent;
}

.btn-link:active,
.btn-link.active {
    color: @blue-4;
}

.btn-link.active:hover {
    color: @blue-3;
}

.btn-link:hover,
.btn-link:focus,
.btn-link:active,
.btn-link.active {
    text-decoration: underline;
    background-color: transparent;
}

.btn-link:focus  {
    color: @blue-5;
}

a.btn {
    padding-top: 9px;
}

a.btn .btn-xs {
    padding-top: 3px;
}

a.btn:hover {
    text-decoration: none;
}

/* BUTTONS SIZES ==================================== */

.btn-lg,
.btn-grp-lg > .btn {
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.33;
    border-radius: 6px;
    height: inherit;
}

.btn-xs,
.btn-sm,
.btn-grp-xs > .btn,
.btn-grp-sm > .btn {
    padding: 1px 14px;
    line-height: 1.5;
    border-radius: 4px;
    font-size: 12px;
    min-width: 65px;
}

/* BLOCK BUTTON ==================================== */

.btn-block {
    display: block;
    width: 100%;
    padding-right: 0;
    padding-left: 0;
}

.btn-block + .btn-block {
    margin-top: 5px;
}

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
    width: 100%;
}

/* ICON BUTTON ==================================== */

.btn .fonticon {
    width: 14px;
    margin: 0 14px 0 0;
    display: initial;
}

.btn .fonticon.right {
    float: right;
    margin: 0 0 0 14px;
}

.btn-block .fonticon {
    float: none;
}

.btn.btn-with-icon {
    min-width: 100px;
}

.btn-xs.btn-with-icon {
    min-width: 90px;
}

.btn.btn-without-label {
    min-width: inherit;
}

.btn.btn-without-label .fonticon {
    margin: 0;
}

/* BUTTON CARET ==================================== */

.btn .caret {
    margin-left: 14px;
    border-top: 8px solid;
    border-right: 5px solid transparent;
    border-left: 5px solid transparent;
}

.btn-xs .caret {
    margin-left: 14px;
    border-top: 6px solid;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
}

/* BUTTON LABEL ==================================== */

.btn .btn-label {
    min-width: 72px;
}

.btn-xs .btn-label {
    min-width: 62px;
}

/* LARGE ICON BUTTON =============================== */

.btn.large-icon-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 130px;
    height: 100px;
    justify-content: space-around;
    font-size: 14px;
    white-space: normal;
    margin: 5px;
    padding: 10px 14px;
}

.btn.large-icon-btn .fonticon  {
    margin: 0;
    font-size: 40px;
    line-height: 40px;
    width: auto;
}

/*  =====================================================================
BUTTON GROUP
====================================================================== */

.btn-grp,
.btn-grp-vertical {
    position: relative;
    display: inline-block;
    vertical-align: middle;
}

.btn-grp > .btn,
.btn-grp-vertical > .btn {
    position: relative;
    float: left;
    margin-right: 2px;
}

/* Bring the "active" button to the front */
.btn-grp > .btn:hover,
.btn-grp-vertical > .btn:hover,
.btn-grp > .btn:focus,
.btn-grp-vertical > .btn:focus,
.btn-grp > .btn:active,
.btn-grp-vertical > .btn:active,
.btn-grp > .btn.active,
.btn-grp-vertical > .btn.active {
    z-index: 2;
}

/* Prevent double borders when buttons are next to each other */
.btn-grp .btn + .btn {
    margin-left: -1px;
}

/* Adjust radius */
.btn-grp > .btn:not(:first-of-type):not(:last-of-type) {
    border-radius: 0;
}

.btn-grp > .btn:first-of-type {
    margin-left: 0;
}

.btn-grp > .btn:first-of-type:not(:last-of-type) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-grp > .btn:last-of-type:not(:first-of-type) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* Specific to only a caret */
.btn-grp > .btn + .dropdown-toggle {
    min-width: inherit !important;
    padding-left: 7px;
    padding-right: 7px;
}

.btn-grp > .btn .btn-touch + .dropdown-toggle {
    min-width: inherit !important;
    padding-left: 14px;
    padding-right: 14px;
}

.btn-grp .dropdown-toggle.btn-link {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.btn-grp .dropdown-toggle .caret {
    margin: 0;
}

.btn-lg .caret {
    border-width: 5px 5px 0;
    border-bottom-width: 0;
}

/* Vertical disposition */
.btn-grp-vertical > .btn,
.btn-grp-vertical > .btn-grp,
.btn-grp-vertical > .btn-grp > .btn {
    display: block;
    float: none;
    width: 100%;
    max-width: 100%;
}

.btn-grp-vertical > .btn-grp > .btn {
    float: none;
}

.btn-grp-vertical > .btn + .btn,
.btn-grp-vertical > .btn + .dropdown,
.btn-grp-vertical > .dropdown + .btn {
    margin-top: -1px;
    margin-left: 0;
}

.btn-grp-vertical > .btn:not(:first-of-type):not(:last-of-type) {
    border-radius: 0;
}

.btn-grp-vertical > .btn:first-of-type:not(:last-of-type) {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-grp-vertical > .btn:last-of-type:not(:first-of-type) {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: 4px;
}

/* ====================================================================== */

.widget-menu-container {
    background: #ffffff;
    margin-top: 0;
    border: none;
    border-radius: 0;
    -moz-box-shadow: 0 1px 6px 0 @ctext-weak;
    -webkit-box-shadow: 0 1px 6px 0 @ctext-weak;
    box-shadow: 0 1px 6px 0 @ctext-weak;
    position: absolute;
    top: 40px;
    right: 10px;
    z-index: 1100;
    &.hidden {
        display: none;
    }
}

.widget-menu-item.with-label {
    cursor: pointer;
    font-family: Arial;
    color: #77797c;
    font-weight: normal;
    line-height: 42px;
    border-bottom: 1px solid @cblock-border;
    padding-right: 14px;
    white-space: nowrap;
    &:hover {
        background-color: @cblock-bg-alt;
    }
    i {
        color: @ctext-weak;
        font-size: 1.35em;
        width: 44px;
    }
}

.widget-menu-container.toolbar-mode{
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-end;
	width: 205px;

    .widget-menu-item.with-label{
	    padding-right: 0px;
	}

	.widget-menu-item .label{
	    display: none;
    }
}