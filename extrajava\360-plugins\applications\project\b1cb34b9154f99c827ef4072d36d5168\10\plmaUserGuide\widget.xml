<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA user guide" group="PLM Analytics/Documentation" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget allows you to display a user guide on the application.</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/plmaUserGuide/images/preview.PNG" alt="User Guide" />
        ]]>
	</Preview>
	
	<Includes>
		<Include type="css" path="css/style.less"/>
		<Include type="css" path="/resources/jquery-ui-1.11.4.custom/jquery-ui.min.css"/>
		<Include type="js" path="/resources/jquery-ui-1.11.4.custom/jquery-ui.min.js"/>
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportWidgetsId arity="ZERO"/>
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportI18N supported="true"/>
	
	<OptionsGroup name="General">
		<Option id="title" name="Title" arity="ZERO_OR_ONE"  isEvaluated="true">
			<Description>The title to display.</Description>
		</Option>
		<Option id="sections" name="Sections" arity="ZERO_OR_MANY">
			<Description>List of sections to display</Description>
				<Functions>
					<ContextMenu>addContext("Sections", ["configuring_charts", "using_charts", "refining_content", "modifying_pages", "setting_the_landing_page"])</ContextMenu>
				</Functions>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Advanced">
		<OptionComposite id="additionalSections" name="Additional sections" arity="ZERO_OR_MANY" >
			<Description>You can add sections to the user guide to help users get to know your application.</Description>
			<Option id="sectionTitle" name="Title" arity="ZERO_OR_ONE" isEvaluated="true">
				<Description>A title for the section.</Description>
			</Option>
			<Option id="sectionContent" name="Content" arity="ONE" isEvaluated="true" >
				<Description>The text of your documentation section. Can contain HTML.</Description>
				<Functions>
					<Display>PARAMETER_doResize({ minHeight: 12 })</Display>
					<Display>SetType('code', 'html')</Display>
				</Functions>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="title">${i18n['plma.navigation.doc']}</DefaultValue>
		<DefaultValue name="sections">configuring_charts</DefaultValue>
		<DefaultValue name="sections">using_charts</DefaultValue>
		<DefaultValue name="sections">refining_content</DefaultValue>
		<DefaultValue name="sections">modifying_pages</DefaultValue>
		<DefaultValue name="sections">setting_the_landing_page</DefaultValue>
	</DefaultValues>

</Widget>
