<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import parameters="accessFeeds,feed,entry,widget,uCssId"/>
<render:import parameters="hitUrl,hitTitle" ignore="true"/>

<%-- retrieve the widget options --%>
<config:getOption var="hitUrlTarget" name="hitUrlTarget" defaultValue=""/>
<config:getOption var="hitContent" name="hitContent" defaultValue="${entry.content}" entry="${entry}" feed="${feed}"/>
<config:getOption name="hitContentDisplay" var="hitContentDisplay" defaultValue="false"/>
<request:getCookieValue name="description_result_list${uCssId}" var="displayDescription" defaultValue="${hitContentDisplay}"/>
<config:getOption var="maxContentLength" name="maxContentLength" defaultValue="0"/>
<config:getOption var="showHitFacets" name="showHitFacets" defaultValue="false"/>
<config:getOption var="showHitMetas" name="showHitMetas" defaultValue="false"/>
<config:getOption var="showThumbnail" name="showThumbnail" defaultValue="false"/>
<config:getOption var="typeFacetId" name="typeFacet"/>
<config:getOption var="typeFacetIcon" name="typeFacetIcon" defaultValue="fonticon-legend"/>
<config:getOption var="showMetasName" name="showMetasName" defaultValue="false"/>
<config:getOption var="showFacetsName" name="showFacetsName" defaultValue="false"/>
<config:getOption var="renderCategoryLinks" name="renderCategoryLinks" defaultValue="false"/>

<search:getFacet var="typeFacet" facetId="${typeFacetId}" entry="${entry}"/>

<c:if test="${showHitMetas == 'true'}">
	<config:getOption var="filterMetas" name="filterMetas" defaultValue="No filtering"/>
	<config:getOption var="metasList" name="metas"/>
	<config:getOption var="showEmptyMetas" name="showEmptyMetas" defaultValue="true"/>
	<config:getOption var="sortModeMetas" name="sortModeMetas" defaultValue="default"/>
	<config:getOption var="showOnlyMetasName" name="showOnlyMetasName" defaultValue="false"/>
</c:if>
<c:if test="${showHitFacets == 'true'}">
	<config:getOption var="hitFilterFacetsType" name="hitFilterFacetsType" defaultValue="No filtering"/>
	<config:getOption var="hitFacetsList" name="hitFacetsList" defaultValue=""/>
	<config:getOption var="sortModeFacets" name="sortModeFacets" defaultValue="default"/>
	<config:getOption var="showEmptyFacets" name="showEmptyFacets" defaultValue="true"/>
	<config:getOption var="hitFacetSortStrategy" name="hitFacetSortStrategy" defaultValue="default"/>
</c:if>

<c:set var="hitUrlTarget" value="${hitUrlTarget == 'New Page' ? '_blank' : ''}"/>

<%-- class="${wh:cleanEntryId(entry.id)}" is used by GMap widgets to append markers --%>
<c:set var="entryIdCssClass" value="${search:cleanEntryId(entry)}"/>

<config:getOption name="defaultLayout" var="defaultLayout" defaultValue="Responsive"/>
<c:set var="isGridTable" value="${defaultLayout == 'GridTable'}"/>

<%--
'entryHitId' must be url value, as used by selection code.
'entryUri' will be same as value of 'hitParamValue' if not empty else equals to 'entryHitId'
--%>
<search:getEntryInfo var="rawEntryUri" name="url" entry="${entry}"/>
<string:escape var="entryHitId" value="${rawEntryUri}" escapeType="HTML"/>
<config:getOption name="hitParamValue" var="hitParamValue" defaultValue=""/>
<config:getOption name="hitParamName" var="hitParamName" defaultValue="hit"/>

<c:set var="entryUri" value="${entryHitId}"/>
<c:if test="${hitParamValue != null && hitParamValue != ''}">
    <search:getMetaValue var="metaValue" entry="${entry}" metaName="${hitParamValue}"/>
    <c:if test="${metaValue != null && metaValue != ''}">
        <c:set var="entryUri" value="${metaValue}"/>
    </c:if>
</c:if>

<li class="hit hit-list ${entryIdCssClass}"
	data-hit-id="${entryHitId}"
	data-uri="${entryUri}"
	data-parameter="${hitParamName}"
	data-id-css-class="${entryIdCssClass}">

	<i18n:message var="titleLabel" code="hitcustom.column.label"/>
	<c:if test="${!isGridTable}">
		<div class="hit-container hit-default">
			<c:if test="${not empty hitTitle}">
				<div class="hitHeader ${empty typeFacetId ? 'no-icon' : ''}">
					<c:choose>
						<c:when test="${showThumbnail == 'true'}">
							<div class="hitThumbnail">
								<render:template template="thumbnail.jsp" widget="thumbnail">
									<render:parameter name="feed" value="${feed}"/>
									<render:parameter name="entry" value="${entry}"/>
									<render:parameter name="widget" value="${widget}"/>
									<render:parameter name="hitUrl" value="${hitUrl}"/>
								</render:template>
							</div>
						</c:when>
						<c:when test="${not empty typeFacetId}">
							<search:forEachCategory var="typeCategory" root="${typeFacet}">
								<plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
								<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
								<plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
								<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
								<div class="icon-container">
									<div class="hitIcon" style="background-color:${plma:toRGB(categoryColor, '.3')};">
										<i class="icon fonticon ${not empty categoryIcon? categoryIcon : typeFacetIcon}"
										   style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"></i>
									</div>
								</div>
							</search:forEachCategory>
						</c:when>
					</c:choose>
					<div class="hitMainContainer">
						<div class="hitTitle" title="${titleLabel}: ${hitTitle}">
							<render:link href="${hitUrl}" target="${hitUrlTarget}">
								<c:if test="${showThumbnail == 'true' && not empty typeFacetId}">
									<search:forEachCategory var="typeCategory" root="${typeFacet}">
										<plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
										<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
										<plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
										<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
										<i class="icon fonticon ${not empty categoryIcon? categoryIcon : typeFacetIcon}"
										   style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"></i>										
									</search:forEachCategory>
								</c:if>
								${hitTitle}
							</render:link>
						</div>
						<div class="metaContainer">
							<c:if test="${not empty typeFacetId}">
								<search:forEachCategory var="typeCategory" root="${typeFacet}">
									<div class="hitType"><search:getCategoryLabel category="${typeCategory}"/></div>
								</search:forEachCategory>
							</c:if>
							<c:if test="${showHitMetas == 'true'}">
								<div class="hitMetas">
									<search:forEachMeta var="meta" entry="${entry}" filterMode="${filterMetas}"
										metasList="${metasList}" showEmptyMetas="${showEmptyMetas}" sortMode="${sortModeMetas}">
										<search:getMetaLabel var="metaLabel" meta="${meta}"/>
										<c:set var="metaValue" value=""/>
										<c:set var="rawMetaValue" value=""/>	<%-- Value with no highlight --%>
										<search:forEachMetaValue meta="${meta}" entry="${entry}" var="value" varRaw="rawValue" varStatus="loop">
											<c:choose>
												<c:when test="${showOnlyMetasName && fn:length(fn:split(value,'#')) > 1}">
													<c:set var="metaValue" value="${metaValue}${fn:split(value,'#')[1]}"/>
													<c:set var="rawMetaValue" value="${rawMetaValue}${fn:split(rawValue,'#')[1]}"/>
												</c:when>
												<c:otherwise>
													<c:set var="metaValue" value="${metaValue}${value}"/>
													<c:set var="rawMetaValue" value="${rawMetaValue}${rawValue}"/>
												</c:otherwise>
											</c:choose>
											<c:if test="${not loop.last}"><c:set var="metaValue" value="${metaValue}, "/></c:if>
											<c:if test="${not loop.last}"><c:set var="rawMetaValue" value="${rawMetaValue}, "/></c:if>
										</search:forEachMetaValue>

										<c:if test="${metaValue != ''}">
											<span class="hitMeta" title="${metaLabel}: ${rawMetaValue}">
												<span class="metaName ${showMetasName == 'true' ? '' : 'hidden'}">${metaLabel}:</span>
												<span class="metaValue">${metaValue}</span>
											</span>
										</c:if>
									</search:forEachMeta>
								</div>
							</c:if>
						</div>
					</div>
				</div>
			</c:if>
			<div class="hitContent ${empty hitContent ? 'empty' : ''} ${displayDescription == 'true' ? '' : 'hidden'}">
				<c:choose>
					<c:when test="${maxContentLength > 0}">
						${fn:substring(hitContent, 0, maxContentLength)}${fn:length(hitContent) > maxContentLength ? '...' : ''}
					</c:when>
					<c:otherwise>${hitContent}</c:otherwise>
				</c:choose>
			</div>
			<c:if test="${showHitFacets == 'true'}">
				<div class="hitFacets">
					<search:forEachFacet var="facet" entry="${entry}" filterMode="${hitFilterFacetsType}"
						facetsList="${hitFacetsList}" showEmptyFacets="${showEmptyFacets}"
						sortMode="${sortModeFacets}" varStatus="loop">
						<plma:getFacetLabel var="facetLabel" feed="${feed}" facetId="${facet.id}"/>
						<plma:getIconName var="facetIcon" facetId="${facet.id}"/>
						<string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML"/>
						<span class="hitFacet">
							<span class="facetLabel">${facetLabel}:</span>
							<search:forEachCategory root="${facet}" var="category" iterationMode="LEAVES"
								sortMode="${hitFacetSortStrategy}">
								<plma:getIconName var="categoryIcon" category="${category}" entry="${entry}"/>
								<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
								<plma:getCategoryColor var="categoryColor" category="${category}"/>
								<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
								<search:getCategoryLabel var="categoryLabel" category="${category}"/>
								
								<c:if test="${renderCategoryLinks}">
									<search:getCategoryUrl var="categoryUrl" category="${category}" baseUrl="" feeds="${accessFeeds}"/>
									<search:getCategoryState var="catState" varClassName="catStateCss" category="${category}"/>
									<a class="category-link ${fn:toLowerCase(catState)}" href="${categoryUrl}">
								</c:if>
									<span class="categoryIcon fonticon ${not empty categoryIcon ? categoryIcon : facetIcon}"
										  style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"
										  title="${facetLabel} : ${categoryLabel}"></span>
									<span class="categoryName" title="${facetLabel}: ${categoryLabel}"><c:if
										test="${showFacetsName == 'true'}">${facetLabel}: </c:if>${categoryLabel}</span>
								<c:if test="${renderCategoryLinks}">
									</a>
								</c:if>
							</search:forEachCategory>
						</span>
					</search:forEachFacet>
				</div>
			</c:if>
		</div>
	</c:if>
	
	<div class="hit-container ${isGridTable? 'grid-row' : 'hit-table'}">
		<c:if test="${isGridTable || not empty hitTitle}">
			<div class="${isGridTable? 'td fixed-column' : ''} table-column mainColumn" data-column-name="title">
				<div class="title-container ${empty typeFacetId ? 'no-icon' : ''} ${showThumbnail? 'with-thumbnail' : 'no-thumbnail'}">
					<span class="toolbarContainer"></span>
					<c:choose>
						<c:when test="${showThumbnail == 'true'}">
							<div class="hitThumbnail">
								<render:template template="thumbnail.jsp" widget="thumbnail">
									<render:parameter name="feed" value="${feed}"/>
									<render:parameter name="entry" value="${entry}"/>
									<render:parameter name="widget" value="${widget}"/>
									<render:parameter name="hitUrl" value="${hitUrl}"/>
								</render:template>
							</div>
						</c:when>
						<c:when test="${not empty typeFacetId}">
							<search:forEachCategory var="typeCategory" root="${typeFacet}">
								<plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
								<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
								<plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
								<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
								<div class="icon-container">
									<div class="hitIcon" style="background-color:${plma:toRGB(categoryColor, '.3')};">
										<i class="icon fonticon ${not empty categoryIcon? categoryIcon : typeFacetIcon}"
										   style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"></i>
									</div>
								</div>
							</search:forEachCategory>
						</c:when>
					</c:choose>
					<div class="hitTitle" title="${titleLabel}: ${hitTitle}">
						<render:link href="${hitUrl}" target="${hitUrlTarget}">
							<c:if test="${showThumbnail == 'true' && not empty typeFacetId}">
								<search:forEachCategory var="typeCategory" root="${typeFacet}">
									<plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
									<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
									<plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
									<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
									<i class="icon fonticon ${not empty categoryIcon? categoryIcon : typeFacetIcon}"
									   style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"></i>									
								</search:forEachCategory>
							</c:if>
							${hitTitle}
						</render:link>
					</div>
				</div>
				<div class="hitContent ${empty hitContent ? 'empty' : ''} ${displayDescription == 'true' ? '' : 'hidden'}">
					<span class="content">
					<c:choose>
						<c:when test="${maxContentLength > 0}">
							${fn:substring(hitContent, 0, maxContentLength)}${fn:length(hitContent) > maxContentLength ? '...' : ''}
						</c:when>
						<c:otherwise>${hitContent}</c:otherwise>
					</c:choose>
					</span>
				</div>
			</div>
		</c:if>

		<c:if test="${!isGridTable && not empty typeFacetId}">
			<div class="${isGridTable? 'td' : ''} table-column hitType" data-column-name="${typeFacetId}">
				<search:forEachCategory var="typeCategory" root="${typeFacet}">
					<search:getCategoryLabel var="categoryLabel" category="${typeCategory}"/>
					<c:if test="${defaultLayout == 'Article Responsive'}">
						<plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
						<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
						<plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
						<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
						<div class="icon-container">
							<div class="hitIcon"
								 style="background-color:${plma:toRGB(categoryColor, '.3')};">
								<i class="icon fonticon ${not empty categoryIcon? categoryIcon : typeFacetIcon}"
								   style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"></i>
							</div>
						</div>
					</c:if>
					<span class="content">${categoryLabel}</span>
				</search:forEachCategory>
			</div>
		</c:if>

		<c:if test="${showHitFacets == 'true'}">
			<search:forEachFacet var="facet" entry="${entry}" filterMode="${hitFilterFacetsType}"
				facetsList="${hitFacetsList}" showEmptyFacets="true" sortMode="${sortModeFacets}" varStatus="loop">
				<plma:getFacetLabel var="facetLabel" feed="${feed}" facetId="${facet.id}"/>
				<plma:getIconName var="facetIcon" facetId="${facet.id}"/>
				<string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML"/>
				<div class="${isGridTable? 'td' : ''} table-column hitFacet ${empty facetIcon ? 'no-icon' : ''}" data-column-name="${facet.id}">
					<search:forEachCategory root="${facet}" var="category" iterationMode="LEAVES" sortMode="${hitFacetSortStrategy}">
						<plma:getCategoryColor var="categoryColor" category="${category}"/>
						<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
						<search:getCategoryLabel var="categoryLabel" category="${category}"/>
						
						<c:if test="${renderCategoryLinks}">
							<search:getCategoryUrl var="categoryUrl" category="${category}" baseUrl="" feeds="${accessFeeds}"/>
							<search:getCategoryState var="catState" varClassName="catStateCss" category="${category}"/>						 
							<a class="category-link ${catStateCss}" href="${categoryUrl}">
						</c:if>
							<span class="categoryName" title="${facetLabel}: ${categoryLabel}"
								  style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"><c:if
									test="${showFacetsName == 'true'}">${facetLabel}: </c:if>${categoryLabel}</span>
						<c:if test="${renderCategoryLinks}">
							</a>
						</c:if>
					</search:forEachCategory>
				</div>
			</search:forEachFacet>
		</c:if>

		<c:if test="${showHitMetas == 'true'}">
			<search:forEachMeta var="meta" entry="${entry}" filterMode="${filterMetas}"
				metasList="${metasList}" showEmptyMetas="true" sortMode="${sortModeMetas}">
				<search:getMetaLabel var="metaLabel" meta="${meta}"/>
				<c:set var="metaValue" value=""/>
				<c:set var="rawMetaValue" value=""/>	<%-- Value with no highlight --%>
				<search:forEachMetaValue meta="${meta}" entry="${entry}" var="value" varRaw="rawValue" varStatus="loop">
					<c:choose>
						<c:when test="${showOnlyMetasName && fn:length(fn:split(value,'#')) > 1}">
							<c:set var="metaValue" value="${metaValue}${fn:split(value,'#')[1]}"/>
							<c:set var="rawMetaValue" value="${rawMetaValue}${fn:split(rawValue,'#')[1]}"/>
						</c:when>
						<c:otherwise>
							<c:set var="metaValue" value="${metaValue}${value}"/>
							<c:set var="rawMetaValue" value="${rawMetaValue}${rawValue}"/>
						</c:otherwise>
					</c:choose>
					<c:if test="${not loop.last}"><c:set var="metaValue" value="${metaValue}, "/></c:if>
					<c:if test="${not loop.last}"><c:set var="rawMetaValue" value="${rawMetaValue}, "/></c:if>
				</search:forEachMetaValue>

				<plma:getIconName var="facetIcon" facetId="${meta.name}"/>
				<string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML"/>
				<span class="${isGridTable? 'td' : ''} table-column hitMeta ${empty facetIcon ? 'no-icon' : ''}" data-column-name="${meta.name}"
					  title="${metaLabel}: ${rawMetaValue}">
					<span class="metaValue">${metaValue}</span>
				</span>
			</search:forEachMeta>
		</c:if>
	</div>
	<div class="subwidgets">
		<widget:forEachSubWidget widgetContainer="${widget}" feed="${feed}" entry="${entry}">
			<render:widget/>
		</widget:forEachSubWidget>
	</div>
</li>
