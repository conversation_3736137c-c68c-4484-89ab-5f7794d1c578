var ActiveFilters = function (uCssId, options) {

	var defaults = {
		aggregation: 'count'
	};

	this.options = $.extend({}, defaults, options);
	this.widget = $('.' + uCssId);

	this.init();



	return this;
};

ActiveFilters.prototype.init = function () {
	this.facetData = {};
	this.widget.find('.activeFilter').on('mouseenter', $.proxy(function (e) {
		var $filter = $(e.target);
		var categoryId = $filter.data('facet');
		if (categoryId !== undefined) {
			var categoryPath = categoryId.split('/');
			var facetId;
			if (categoryPath.length >= 3) {
				facetId = categoryPath[1];
			}
		}
		this.getCategoryData(facetId, categoryId, function (categoryData) {
//			console.log(categoryData);
		});


	}, this));
};

ActiveFilters.prototype.getCategoryData = function (facetId, categoryId, callback) {
	var executeCallBack = $.proxy(function (facetData, facetId, categoryId, callback) {
		var categoryData = {
			totalUnfiltered: facetData[facetId] ? facetData[facetId].totalUnfiltered : 0,
			empty: facetData[facetId] ? facetData[facetId].categories['f/' + facetId + '/' + ActiveFilters.constants.EMPTY_CAT] : 0,
			value: facetData[facetId] ? facetData[facetId].categories[categoryId] : 0
		};
		callback.call(this, categoryData);
	}, this);

	if (!this.facetData.hasOwnProperty(facetId)) {
		this.loadFacetData(facetId, $.proxy(function () {
			executeCallBack(this.facetData, facetId, categoryId, callback);
		}, this));
	} else {
		executeCallBack(this.facetData, facetId, categoryId, callback);
	}
};

ActiveFilters.prototype.loadFacetData = function (facetId, callback) {
	var url = new BuildUrl(window.location.href);
	var params = {withEntries: 'true'};
	var aggregation = this.options.aggregation;
	for (paramName in url.params) {
		if (url.params.hasOwnProperty(paramName)) {

			if (paramName.endsWith('.r') || paramName.endsWith('.zr')) {
				params[paramName] = url.params[paramName].filter(function (e) {
					return e.indexOf('/' + facetId + '/') === -1;
				});
			} else {
				params[paramName] = url.params[paramName];
			}
		}
	}
	$.ajax({
		url: mashup.baseUrl + '/ajax/resultFeed/' + url.getPage(),
		data: params,
		dataType: 'json',
		success: $.proxy(function (data, textStatus, jqXHR) {
			for (var iFeed = 0; iFeed < data.entries[0].subfeeds.length; iFeed++) {
				var feed = data.entries[0].subfeeds[iFeed];
				var foundFacet = false;
				var facetData = {
					totalUnfiltered: 0,
					categories: {}
				};
				for (var iFacet = 0; iFacet < feed.facets.length; iFacet++) {
					var facet = feed.facets[iFacet];
					if (facet.name === facetId) {
						foundFacet = true;
						facetData.totalUnfiltered = feed.infos.nmatches;
						for (var iCat = 0; iCat < facet.categories.length; iCat++) {
							var category = facet.categories[iCat];
							facetData.categories[category.id] = aggregation === 'count' ? category.count : category.aggregations[aggregation];
						}
						break;
					}
				}

				if (foundFacet) {
					this.facetData[facetId] = facetData;
					break;
				}
			}
			callback.call(this);
		}, this)
	});
};

ActiveFilters.constants = {
	EMPTY_CAT: '__empty__'
};

ActiveFilters.utils = {
	/*We remove the refine provided from the url. While doing so for hierarchical categories
	Check if we have to add effective refine. */
    deleteRefine: function (refineToDelete) {
        var url = new BuildUrl(window.location.href);
        var finalUrl = new BuildUrl(window.location.href);
        for (var paramKey in url.params) {
            var paramValues = url.params[paramKey];
            if (paramKey.indexOf('.r') !== -1) {
                // This code removes current category refine and all the child category refines.
                // e.g. for f/facetname/a/b will remove f/facetname/a/b, f/facetname/a/b/c ...
                // In such case, We should still keep the refine f/facetname/a to match the zap refine behaviour.
                for (var i = 0; i < paramValues.length; i++) {
                    var paramValue = paramValues[i];
                    if (paramValue.indexOf(refineToDelete) !== -1) {
                        finalUrl.removeParameterWithValue_(paramKey,paramValue);
                    }
                }

                // Find effective refine if any. process all the refines which are longer than 'f/facetname/a'
                var refineParts = refineToDelete.split('/');
                if(refineParts.length > 3){
                    let effectiveRefine = refineParts.slice(0, refineParts.length-1).join('/');
                    finalUrl.addParameter(paramKey, effectiveRefine);
                }
            }
        }
        window.location.assign(finalUrl.toString());
    },

	deleteZapRefine: function (refineToDelete) {
		var url = new BuildUrl(window.location.href);

		var finalUrl = url;
		for (var paramKey in url.params) {
			var paramValues = url.params[paramKey];
			if (paramKey.indexOf('.zr') !== -1) {
				for (var i = 0; i < paramValues.length; i++) {
					var paramValue = paramValues[i];
					if (paramValue.indexOf(refineToDelete) !== -1) {
						finalUrl.removeParameterWithValue_(paramKey,paramValue);
					}
				}
			}
		}

		window.location.assign(finalUrl.toString());
	},

	/* highlight similar search refinements */
	highlightRefinements: function (refinementClass, highlight) {
		var similarRefinements = $("." + refinementClass);
		if (highlight == true) {
			for (var i = 0; i < similarRefinements.length; i++) {
				$(similarRefinements[i]).addClass("similarRefinement");
			}
		} else {
			for (var i = 0; i < similarRefinements.length; i++) {
				$(similarRefinements[i]).removeClass("similarRefinement");
			}
		}
	}
};

var ActiveFiltersCustomLink = function (uCssId) {
	this.uCssId = uCssId;
	this.widget = $('.' + uCssId);
};

ActiveFiltersCustomLink.prototype.init = function () {
	this.widget.find('.linkContainer .customLink').on('click', $.proxy(function (e) {
		var $e = $(e.currentTarget);
		if ($e.data('url')) {
			var destinationUrl = new BuildUrl($e.data('url'));
			if ($e.data('keep') && window.location.href.split('?').length > 1) {
				var currentUrl = window.location.href.split('?')[1].split('&');
				for (var i = 0; i < currentUrl.length; i++) {
					var param = decodeURIComponent(currentUrl[i]).split('=');
					if (param.length > 1) {
						destinationUrl.addParameter(param[0], param[1], false);
					}
				}
			}
			window.location.replace(destinationUrl.toString());
		}

	}, this));
};

$(function () {
	//We watch if there is some hierarchical refine
	var links = $('.activeFilters .activeFiltersContainer a');
	var path = [];
	for (var i = 0; i < links.length; i++) {
		var oldPath = path;
		path = links[i].getAttribute("data-facet") ? links[i].getAttribute("data-facet").split('/') : [];
		if (i > 0 && path.length > 0 && path.length == oldPath.length + 1 && oldPath.length > 0 && path[1] == oldPath[1]) {
			//Then add a class hasHierarchicalParent
			links[i].getClassList().add('hasHierarchicalParent');
		}

		if (i < links.length - 1) {
			var nextPath = links[i + 1].getAttribute("data-facet") ? links[i + 1].getAttribute("data-facet").split('/') : [];
			if (path.length == nextPath.length - 1 && path[1] == nextPath[1]) {
				//Then add a class hasHierarchicalChild
				links[i].getClassList().add('hasHierarchicalChild');
			}
		}
	}
});