<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Multi-selection" group="PLM Analytics/Results Rendering" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget allows the user to select several hits. Must be added as a subwidget of a result list widget.</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/multiSelection/images/preview.PNG" alt="Multi Selection" />
        ]]>
	</Preview>
	<Includes>
    	<Include type="css" path="css/style.less" />
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="../3dxpSearchAndRefine/js/message-handler.js"/>
		<Include type="js" path="../plmaResources/js/experienceAPIs/Session3DContent.js"/>
		<Include type="js" path="../plmaResources/js/select.js" />
		<Include type="js" path="../plmaResources/js/SelectionWidget.js" />
    	<Include type="js" path="js/multiSelection.js" />
    </Includes>
	
	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO_OR_MANY" />
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>widget.multiSelection.selectAll</JsKey>
			<JsKey>widget.multiSelection.deselectAll</JsKey>
		</JsKeys> 
	</SupportI18N>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<OptionsGroup name="General">
		<Option id="renderAllways" name="Render script execution allways" arity="ONE">
			<Description>Renders Javascript code allways. Defaults to false. If this option true and "selectHitContainerSelector" option is configured, needful changes have to make</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="selector" name="Custom selector" arity="ZERO_OR_ONE">
			<Description>You can use a custom CSS selector to target selectable result items. Overrides the '.hit' option.</Description>
		</Option>
		<Option id="triggerEvent" name="Trigger Event on selection change" arity="ONE">
			<Description>Triggers a 'plma:hitSelection' JavasScript event (with the list of selected hits) on each hit when it is selected or deselected.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="limit" name="Limit" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Specifies the maximum number of hits that can be selected. Defaults to 20.</Description>
		</Option>
		<Option id="selectAll" name="Enable select all" arity="ONE">
			<Description>Allows the user to select all the displayed hits at once (up to the specified maximum number of hits).</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['selectAllLimit', 'selectAllContainerSelector']})</Display>
			</Functions>
		</Option>
		<Option id="selectAllContainerSelector" name="Select All Container selector" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>CSS selector of the container to which the "Select all" button will be appended to. Defaults to '.resultsTitle'.</Description>
			<Functions>
				<Display>SetType('code', 'mel')</Display>
			</Functions>
		</Option>
		<Option id="selectHitContainerSelector" name="Select Hit Container selector" arity="ZERO_OR_ONE"  isEvaluated="true">
			<Description>CSS selector of the container to which the "Select Hit" button will be appended to. Defaults to 'Custom selector/.hit' configuration. If the selector is provided it will be searched inside 'Custom selector/.hit'. If "Render script execution once" is disabled and using plma-resultList, '.hit-checkbox-container' css class need to add in 'on init()' funtion under 'buttonSelectors[]' of plam-resultList.</Description>
			<Functions>
				<Display>SetType('code', 'mel')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Advanced">
		<Option id="metaList" name="List of meta names" arity="ZERO_OR_MANY">
			<Description>This widget can retrieve meta values of the selected hits. Specifies which metas should be used.</Description>
			<Functions>
				<ContextMenu>Metas()</ContextMenu>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Select API">
		<Description>This widget can interact with other widgets in a 3DDashboard. It can publish the user's selection and can subscribe to other widgets' publication.</Description>
		<Option id="enablePublication" name="Enable publication">
			<Description>Enable publication of the user's selection.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="enableSubscription" name="Enable subscription">
			<Description>Enable subscription of selected hits from other widgets.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="channels" name="Channels" arity="ZERO_OR_MANY">
			<Description>Channels to use.</Description>
		</Option>
		<Option id="useApplicationConfig" name="Use the application configuration">
			<Description>Use the application configuration or specify custom configuration.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({ valueToMatch: ['true'], hideOptions: ['channelsConfig'] })</Display>
			</Functions>
		</Option>
		<OptionComposite id="channelsConfig" name="Channels configuration" arity="ZERO_OR_MANY">
			<Option id="topic" name="Topic">
				<Description>Name of the topic.</Description>
			</Option>
			<Option id="data" name="Data" isEvaluated="true">
				<Description>
					<![CDATA[
						JavaScript object that contains useful data. You can use MEL (scope: entry). The hit URL as key is mandatory.<br />
						Example:
<pre>
{
    '${entry.metas['url']}': {
        fullName: '${entry.metas['fullname']}'
    }
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Option id="normalizer" name="Normalizer">
				<Description>
					<![CDATA[
						JavaScript function that returns the appropriate message based on two parameters:
						<ol>
							<li><i>selectedHits</i>: array of hit URLs</li>
							<li><i>data</i>: data previously described</li>
						</ol>
						Example:
<pre>
function (selectedHits, data) {
    return {
        type: 'hello',
        data: selectedHits.map(function (hitUrl) {
            return data[hitUrl].fullName;
        })
    };
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Option id="denormalizer" name="Denormalizer">
				<Description>
					<![CDATA[
						JavaScript function that returns the selected hits based on two parameters:
						<ol>
							<li><i>message</i>: message sent by other widgets</li>
							<li><i>data</i>: data previously described</li>
						</ol>
						Example:
<pre>
function (message, data) {
    return data.map(function (fullName) {
        for (var hitUrl in data) {
            if (data[hitUrl].fullName === fullName) {
                return hitUrl;
            }
        }
        return null;
    });
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="limit">20</DefaultValue>
		<DefaultValue name="renderAllways">false</DefaultValue>
		<DefaultValue name="selectAll">true</DefaultValue>
		<DefaultValue name="selectAllContainerSelector">.resultsTitle</DefaultValue>
	</DefaultValues>

</Widget>
