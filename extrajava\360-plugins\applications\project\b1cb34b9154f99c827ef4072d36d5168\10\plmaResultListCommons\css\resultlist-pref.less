.plmalightbox .plmalightbox-box.resultlist-preference{
	width: 100%;
	.plmalightbox-header{
		padding: 13px 10px;
		border-bottom: 1px solid #e2e4e3;
		width: 95%;
		align-self: center;
	}
	.plmalightbox-contentwrapper{
		overflow-y: auto;
		.resultlist-preference-form{
			width: 100%;
		}
	}
}
.resultlist-preference-form{
	position: relative;
	padding: 5px 10px;
	.header{
		margin: 10px 0px;
		.label{
			font-size: 2em;
			font-weight: bold;
			padding: 10px;
		}
		.description{
			font-size: 1.3em;
			font-style: italic;
		}
	}
	.groups-config.single-group {
		.group-header{
			display: none;
		}
	}
	.groups-config.multi-group {
		display: flex;
		row-gap: 5px;
		flex-direction: column;
		margin-bottom: 10px;
		.group{
			border: 1px solid;
			padding: 5px;
			.group-header{
				padding: 0px;
				height: 30px;
				cursor: move;
				&:hover{
					background-color: #f1f1f1;
				}
				span{
				    line-height: 100%;
					vertical-align: middle;
				}
				span.icon{
					font-size: 18px;
					margin: 0px;
				}
				span.icon.move{
					font-size: 30px;
					margin-left: 0px;
				}
				&> .label{
					font-size: 2em;
					font-weight: bold;
					padding: 0px 10px;
				}
				&> .description{
					font-size: 1.3em;
					font-style: italic;
				}
			}
			&.ui-state-highlight{
				height: 50px;
				line-height: 50px;
			}
			&.ui-sortable-helper{
				background-color: white;
			}
		}
	}
	
	.columns-config .column {
		cursor: move;
		font-size: 1.4em;
		padding: 6px 0px;
		border-bottom: 1px solid #e2e4e3;

		&:not(.edit-disabled):hover{
			background-color: #f1f1f1;
		}

		.icon.checkbox{
			cursor: default;
		}

		&.edit-disabled{
			cursor: not-allowed;
			color: #844138;
			.icon.checkbox{
				cursor: not-allowed;
			}
		}
		&.ui-state-highlight{
			height: 14px;
			line-height: 14px;
		}
	}
	
	span.condition {
		float: right;
		flex-grow: 1;
		text-align: end;
		color: #00B8DE;
		position:relative;
		font-size: 12px;
		.condition-tooltip{
			font-family: entypo;
			display: none;
			position: absolute;
			top: 14px;
			right: 0px;
			border: 1px solid black;
			padding: 5px 10px;
			background: #EDF6EB;
			color: black;
			text-align: left;
			z-index: 1;
			flex-direction: column;
			row-gap: 5px;
			.expr{
				color: #0087A3;
			}
		}
		&.show-right .condition-tooltip{
			right: auto;
		}
		&:hover .condition-tooltip{
			display: inline-flex;
		}
	}
	
	.button-container{
		border: 1px solid #e2e4e3;
		height: 45px;
		.button{
			margin: 7px;
			cursor: pointer;
			color: #3d3d3d;
			background-color: #F1F1F1;
			font-size: 14px;
			border: 1px solid #b4b6ba;
			border-radius: 4px;
			line-height: 1.42857;
			padding: 5px 10px;
			float: right;
		}

		.save-button.active {
			background-color: #EDF6EB;
			border-color: #57B847;
			color: #57B847;
		}

		&.button-container-top{
			border-width: 0px;
			border-bottom-width: 1px;
			.button{
				float: left;
				font-size: 12px;
				color: black;
				font-style: italic;
			}

			.legend{
				float: right;
				font-size: 14px;
				line-height: 15px;
				background-color: #F2F5F7;
				cursor: help;
				display: inline-grid;
				position: absolute;
				right: 11px;
				top: 5px;
				padding: 0px 6px;
				span{
					margin-left: 7px;
					font-family: 'entypo';
				}
			}
		}
	}
}

/*Styles for Result List Preferences Inside Global Preference Widget*/
.mashup.mashup-style .plma-preferences .preference-widget .preference-config .preference-config-flex-container{
	.preference-container.preference-resultlist-container .button-container{
		margin: 0px;
		border: 1px solid #e2e4e3;
		&.button-container-top{
			border-width: 0px;
			border-bottom-width: 1px;
		}
		.save-button.active {
			background-color: #EDF6EB;
			border-color: #57B847;
			color: #57B847;
		}
	}
}

.preference-resultlist-container{
	.resultlist-preference-form .header{
		display: none;
	}
	.resultlist_list{
		overflow: auto;
		width: 100%;
		&.wrapped{
			width: 35%;
		}

		li {
			padding: 10px;
			background-color: #F1F1F1;
			border: 1px solid #D8D8F8;

			&.selected {
				border: 2px solid #42a2da;
				border-right: 0;
			}

			.hitHeader{
				display: flex;
				min-height: 23px;

				.hit-name {
					font-family: '3ds';
					font-weight: normal;
					color: #77797c;
					font-size: 16px;
					cursor: pointer;
					width: 30%;
					line-height: 23px;
				}
			}
		}
	}
	.resultlist_details{
		width: 65%;
		overflow: auto;

		&.active {
			border: 2px solid #42a2da;
		}
	}
}

.plmalightbox-overlay.visible{
	pointer-events:none;
}