<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Recent searches" group="PLM Analytics/Search" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>
		<![CDATA[
		<p>Depending on the selected mode, this widget either memorizes hits or displays them.</p>
		<table style="border: 1px solid black;border-collapse: collapse;width: 100%;">
			<thead>
				<tr>
					<th rowspan="2" style="border: 1px solid black;border-collapse: collapse;text-align: center;">type of memorization</th>
					<th colspan="2" style="border: 1px solid black;border-collapse: collapse;text-align: center;">Widget modes</th>
				</tr>
				<tr>
					<th style="border: 1px solid black;border-collapse: collapse;text-align: center;">memorization</th>
					<th style="border: 1px solid black;border-collapse: collapse;text-align: center;">display</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td style="border: 1px solid black;border-collapse: collapse;">automatic</td>
					<td style="border: 1px solid black;border-collapse: collapse;">listener</td>
					<td style="border: 1px solid black;border-collapse: collapse;">getter</td>
				</tr>
				<tr>
					<td style="border: 1px solid black;border-collapse: collapse;">manual</td>
					<td style="border: 1px solid black;border-collapse: collapse;">pin</td>
					<td style="border: 1px solid black;border-collapse: collapse;">pins_getter</td>
				</tr>
				<tr>
					<td style="border: 1px solid black;border-collapse: collapse;">manual</td>
					<td style="border: 1px solid black;border-collapse: collapse;">external button event</td>
					<td style="border: 1px solid black;border-collapse: collapse;">default, subwidge, custom</td>
				</tr>
			</tbody>
		</table>
		<b>Default Display Mode</b> In this mode the default internal view will be available. You can use this view in popup with toggle On.<br/>
		<b>Subwidget Display Mode</b> In this mode the subwidget will be loaded in ajax call by passing the query and feed.per_page = limit (when limit=0, per_page=20)as parameters.
		You can have pagination/infinite scroll in your widget to handle display of all the entries. You can have a callback after the subwidget disply is done. 
		e.g. you can sort the displed hits as per the order in DB.<br/>
		<b>Custom Display Mode</b> In this mode you can write your business logic in callbacks init, dbUpdate, display, hidden. 
		Sample view handler is provided for reference : HitsViewInPageReload.<br/>
		<hr/>
		<b>Note on I18n Codes used:</b> Sufix codes with id of the collection. If the sepcific codes not found default will be used.
		<ul>
			<li><i style="margin-right:5px;">"plma.hits.collection.labels.counter.{id}"</i> Lable for counter. e.g. "{count}/{limit}"</li>
			<li><i style="margin-right:5px;">"plma.hits.collection.labels.tooltip.{id}"</i> Tooltip of collection. e.g. "Show {count}/{limit} entries", where {*} will be replaced with values. count=count of entries and limit=limit set for collection.</li>
			<li><i style="margin-right:5px;">"plma.hits.collection.labels.emptyCollection.{id}"</i> Label when collection is empty</li>
			<li><i style="margin-right:5px;">"plma.hits.collection.labels.trashTitle.{id}"</i> Tooltip for clear button of collection</li>
			<li><i style="margin-right:5px;">"plma.hits.collection.add-entry.{id}"</i> Tooltip of button when the entry is not in collection</li>
			<li><i style="margin-right:5px;">"plma.hits.collection.remove-entry.{id}"</i> Tooltip of button when the entry exists in collection</li>
			<li><i style="margin-right:5px;">"plma.hits.collection.alerts.add.{id}"</i> Entry added to collection</li>
			<li><i style="margin-right:5px;">"plma.hits.collection.alerts.remove.{id}"</i> Entry removed from collection</li>
			<li><i style="margin-right:5px;">"plma.hits.collection.alerts.removeAll.{id}"</i> All the entries are removed from collection</li>
			<li><i style="margin-right:5px;">"plma.hits.collection.alerts.limitReached.{id}"</i> Limit for collection is reached (for freeze mode)</li>
			<li><i style="margin-right:5px;">"plma.hits.collection.alerts.removeOld.{id}"</i> Limit reached but adding new entry by removing old(for remove-old mode)</li>
			<li><i style="margin-right:5px;">"plma.hits.collection.alerts.minDisplaySize.{id}"</i> Hits collection size is less than {minDisplaySize}. Hence display will be turned off.</li>
		</ul>
		<hr/>
		]]>
	</Description>
	<Dependencies>
		<Trigger position="self" className="com.exalead.cv360.searchui.triggers.impl.RemoveIfNotLoggedIn" />
		<Widget name="plmaResources" />
	</Dependencies>
	<Includes>
		<Include type="css" path="css/style.less"></Include>
		<Include type="css" path="/resources/widgets/plmaResources/lib/notify/notify-plma.less" />

		<Include type="js" path="/resources/javascript/jquery.cookie.js" />
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="/resources/widgets/plmaResources/js/lodash.min.js" />
		<Include type="js" path="/resources/widgets/plmaResources/lib/notify/notify.js" />
		<Include type="js" path="/resources/widgets/plmaResources/lib/notify/notify-plma.js" />
		<Include type="js" path="js/hitsCollectionManager.js"></Include>
		<Include type="js" path="js/hitsStorage.js"></Include>	
		<Include type="js" path="js/hitsViewInPageReload.js"></Include>
	</Includes>
	<Preview>
		<![CDATA[
			<img src="img/preview.png" />
		]]>
	</Preview>
	<SupportWidgetsId arity="ZERO_OR_ONE"/>
	<SupportFeedsId arity="ONE" consumeFeed="false"/>
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>recentSearches.pin</JsKey>
			<JsKey>recentSearches.unpin</JsKey>
			<JsKey>plma.hits.collection.labels.counter</JsKey>
			<JsKey>plma.hits.collection.labels.tooltip</JsKey>
			<JsKey>plma.hits.collection.labels.emptyCollection</JsKey>
			<JsKey>plma.hits.collection.labels.trashTitle</JsKey>
			<JsKey>plma.hits.collection.add-entry</JsKey>
			<JsKey>plma.hits.collection.remove-entry</JsKey>
			<JsKey>plma.hits.collection.alerts.add</JsKey>
			<JsKey>plma.hits.collection.alerts.remove</JsKey>
			<JsKey>plma.hits.collection.alerts.removeAll</JsKey>
			<JsKey>plma.hits.collection.alerts.limitReached</JsKey>
			<JsKey>plma.hits.collection.alerts.removeOld</JsKey>
			<JsKey>plma.hits.collection.alerts.minDisplaySize</JsKey>
		</JsKeys>
	</SupportI18N>
	<OptionsGroup name="General">
		<Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
			<Functions>
				<ContextMenu>addContext('Title', [${i18n['plma.hits.collection.labels.label.colletionId']}, "${i18n['recentSearches.getter.title']}", "${i18n['recentSearches.pins_getter.title']}"])</ContextMenu>
			</Functions>
		</Option>
		<Option id="tooltipTitle" name="Widget tooltip" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Add custom tooltip for the widget. Leave empty to not use it.</Description>
		</Option>
		<Option id="mode" name="Widget mode" arity="ONE">
			<Description>
				<![CDATA[
				<ul>
					<li><b>Listener mode:</b> Memorizes which hits have been opened by the user.</li>
					<li><b>Getter mode:</b> Displays all the memorized hits.</li>
					<li><b>Pin mode:</b> The user can mark a hit as a favorite.</li>
					<li><b>Pins getter mode:</b> Displays all the favorite hits.</li>
					<li><b>Customer mode:</b> Act as Collection Definition and provides sigleton object in js for updating the collection.</li>
				</ul>
				]]>
			</Description>
			<Values>
				<Value>listener</Value>
				<Value>getter</Value>
				<Value>pin</Value>
				<Value>pins_getter</Value>
				<Value>custom</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['custom'], showOptions:['collectionDef', 'buttonsDef']})</Display>
			</Functions>
		</Option>
		<Option id="maxSearches" name="Maximum number of searches">
			<Description>Max Collection Size. 0 means no limit.</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>		
		<Option id="metaForId" name="Meta used as identifier" arity="ONE">
			<Description>Name of the meta used as identifier.</Description>
			<Functions>
				<ContextMenu>Metas()</ContextMenu>
			</Functions>
		</Option>
		<Option id="disableAlert" name="Disable alert" arity="ONE">
			<Description>Disable add/remove hit alert, default to false.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="multiselect" name="Enable multiple Selection" arity="ONE">
			<Description>Enable to select the "maxsearches" number of entries</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['iconCssFormultipleCompare'], hideOptions:[]})</Display>
			</Functions>
		</Option>
		<Option id="iconCssFormultipleCompare" name="Icon CSS for multiple compare" arity="ZERO_OR_ONE">
			<Description>Specifies the CSS class name of the icon added to the button. If empty, no icon is displayed.</Description>
		</Option>
		<OptionComposite id="collectionDef" name="Collection Definition" arity="ONE">
			<Description>Definition of the Hits collection to be created and managed.</Description>
			<Option id="id" name="Id" arity="ONE">
				<Description>Defines the Id of the collection. It is used as identifier of the collection</Description>
			</Option>
			<Option id="store" name="Store Type" arity="ONE">
				<Description>Define where to store collection information.</Description>
				<Values>
					<Value>db</Value>
					<Value>browser</Value>			
				</Values>
				<Functions>
					<Display>ToggleCompositeDisplay({valueToMatch:['db'], showOptions:['storeType'], hideOptions:[]})</Display>
				</Functions>
			</Option>
			<Option id="storekey" name="Store Key" arity="ONE">
				<Description>Define collection storage key.</Description>				
			</Option>
			<Option id="limitStrategy" name="Overflow Strategy" arity="ONE">
				<Description>What to do when Max Size is exceeded. When freeze strategy selected, no new entries will be allowed in collection.</Description>
				<Values>
					<Value>remove-old</Value>
					<Value>freeze</Value>								
				</Values>
			</Option>
			<Option id="iconCss" name="Icon CSS" arity="ZERO_OR_ONE">
				<Description>Specifies the CSS class name of the icon added to the button. If empty, no icon is displayed.</Description>
			</Option>
			<Option id="minDisplaySize" name="Minimum Size for Display" arity="ZERO_OR_ONE">
				<Description>Min size of collection to enable display. Defaults to 1</Description>
				<Functions>
					<Display>SetType('number')</Display>
					<Check>isInteger</Check>
				</Functions>
			</Option>
			<Option id="storeType" name="Store Type" arity="ZERO_OR_ONE">
				<Description>Type of store to use 'shared' or 'user'. Default is 'user'</Description>
				<Values>
					<Value>user</Value>
					<Value>shared</Value>
				</Values>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
		<OptionComposite id="buttonsDef" name="Button Definition" arity="ONE">
			<Description>Definition for the buttons. The collection take ownership of state of the buttons registerd using collection.registerButton(). Buttons can be render using PLMAButton.</Description>
			<Option id="entryPresentIcon" name="Entry in collection Icon" arity="ONE">
				<Description>Specifies css icon describing 'entry exists in collection'. e.g. 'fonticon-compare-on'</Description>
			</Option>
			<Option id="entryAbsentIcon" name="Entry not in collection Icon" arity="ONE">
				<Description>Specifies css icon describing 'entry not exists in collection'. e.g. 'fonticon-compare-off'</Description>
			</Option>
		</OptionComposite>
	</OptionsGroup>
	
	<OptionsGroup name="Data">
		<Description>Fill this section only in 'getter' or 'pins_getter' or 'custom' mode.</Description>
		<Option id="activateToggle" name="Activate Hit Details Toggle" arity="ONE">
			<Description>Allows you to load hits/details on by clickin on header.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="showInPopup" name="Show Hit Details in popup" arity="ONE">
			<Description>Show hits in popup Only when the Toggle is true and for default/subwidget display hits modes.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="displayHitsMode" name="Display Hits Mode" arity="ONE">
			<Description>
				<![CDATA[
				Defines the How to render details view type. 
				<ul>
					<li><b>Default:</b> Load Default view is defined by the widget.</li>
					<li><b>Subwidget:</b> Load the subwidgets for default view.</li>
					<li><b>Custom:</b> Only available through the JS code to manage the collection. No UI created/Managed.</li>
				</ul>
				]]>
			</Description>
			<Values>
				<Value>default</Value>
				<Value>subwidget</Value>
				<Value>custom</Value>					
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['default'], showOptions:['elemPerPage', 'header', 'content', 'description', 'facetId', 'metas', 'hitFacetsList', 'entryUrl'], hideOptions:['onHide']})</Display>
				<Display>ToggleDisplay({valueToMatch:['subwidget'], showOptions:['onAfterShow']})</Display>
				<Display>ToggleDisplay({valueToMatch:['custom'], showOptions:['onInit', 'onDbUpdate', 'onShow'], hideOptions:['showInPopup']})</Display>
			</Functions>
		</Option>
		<Option id="onInit" name="On Collection Init" arity="ONE">
			<Description>
				<![CDATA[
				JS Function to handled when the collection is initialized
				]]>
			</Description>
			<Placeholder>function(data){ /*Add Initialization code here.*/}</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="onDbUpdate" name="On Collection DB Updated" arity="ZERO_OR_ONE">
			<Description>
				<![CDATA[
				JS Function to handled when the collection DB is updated. No UI handling expected. 
				For handling the UI onShow will be called.
				]]>
			</Description>
			<Placeholder>function(data){ /*Store is updated(added/removed some values)*/}</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="onShow" name="On Collection Display" arity="ONE">
			<Description>
				<![CDATA[
				JS Function to handled when the collection header is clicked to show the details.
				]]>
			</Description>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="onAfterShow" name="After Collection Display" arity="ZERO_OR_ONE">
			<Description>
				<![CDATA[
				optional : JS Function to handled when after the hits are displayed. (only available in subwidget mode).
				You can sort the list based on the order in the collection. Also can use URL which was saved while adding entry in collection.
				]]>
			</Description>
			<Placeholder>function(data){ /*you can perform sorting if you need to show the same order as in store.*/}</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="onHide" name="On Collection Hidden" arity="ONE">
			<Description>
				<![CDATA[
				JS Fucntion to handle, Collection Update event.
				]]>
			</Description>
			<Placeholder>function(data){ /*Collection is hiding do the cleanup*/}</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>		
		<Option id="elemPerPage" name="Number Entries displayed by page">
			<Description>Number of memorized hits displayed per pagination page in the widget (default is 4). Enter 0 if you do not want any pagination.</Description>
		</Option>
		<Option id="header" name="Header" isEvaluated="true">
			<Description>Content of the header. Using MEL is possible with the following scopes: entry, feeds.</Description>
		</Option>
		<Option id="content" name="Content" isEvaluated="true">
			<Description>Main content. Using MEL is possible with the following scopes: entry, feeds.</Description>
		</Option>
		<Option id="description" name="Description" isEvaluated="true">
			<Description>Content displayed when the user hovers over the entry. Using MEL is possible with the following scopes: entry, feeds.</Description>
		</Option>
		<Option id="facetId" name="Facet type">
			<Description>Name of the facet to display searches differently. It will be used for retrieving the icon associated to the object.</Description>
		</Option>
		<Option id="metas" name="Meta list">
			<Description>The specified metas, separated by commas, will be the only ones displayed.</Description>
			<Functions>
				<ContextMenu>Metas()</ContextMenu>
				<ContextMenu>COMMON_appendOnChange(', ')</ContextMenu>
			</Functions>
		</Option>
		<Option id="hitFacetsList" name="Facet list">
			<Description>The specified facets, separated by commas, will be the only ones displayed.</Description>
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
				<ContextMenu>COMMON_appendOnChange(', ')</ContextMenu>
			</Functions>
		</Option>
		<Option id="entryUrl" name="Entry url" isEvaluated="true">
			<Description><![CDATA[
				Set entry url. (Default value : window.location.pathname + window.location.search + window.location.hash)
				Note Do not provide function Object "function(){ return "url";  }", it won;t work.
				You can write self executing function in if needed. "(function(){ return "url"; })()"
			]]></Description>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
	</OptionsGroup>
	<DefaultValues displayName="Listener">
		<DefaultValue name="mode">listener</DefaultValue>
		<DefaultValue name="maxSearches">5</DefaultValue>
	</DefaultValues>
	<DefaultValues displayName="Getter">
		<DefaultValue name="mode">getter</DefaultValue>
		<DefaultValue name="maxSearches">5</DefaultValue>
	</DefaultValues>
	<DefaultValues displayName="Pin">
		<DefaultValue name="mode">pin</DefaultValue>
		<DefaultValue name="maxSearches">5</DefaultValue>
	</DefaultValues>
	<DefaultValues displayName="Pins getter">
		<DefaultValue name="mode">pins_getter</DefaultValue>
		<DefaultValue name="maxSearches">5</DefaultValue>
	</DefaultValues>
	<DefaultValues displayName="Custom">
		<DefaultValue name="mode">custom</DefaultValue>		
	</DefaultValues>
	<DefaultValues>
		<DefaultValue name="mode">custom</DefaultValue>		
	</DefaultValues>
	<DefaultValue name="disableAlert">false</DefaultValue>
</Widget>
