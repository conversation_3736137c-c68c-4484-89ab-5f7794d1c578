/**
 * This class is simpler than 'RefinePanelSearch' one since it delegates filtering to access API but does not post-filter facet in JS based on
 * suggest service answer and exhaustive facet result
 *
 * This implementation has prerequisites, it depends on facets management triggers
 * (https://gitlab.paris.exalead.com/apps/plmaonpremise/plma-tools/-/wikis/Facets-management-triggers)
 *
 * @param uCssId Refine panel widget unique ID
 * @param options Panel search options
 * @constructor Constructor
 */
var RefinePanelSearchAccess = function (uCssId, options) {
    this.uCssId = uCssId;
    this.$widget = $('.' + this.uCssId);
    this.$input = $('.' + this.uCssId + ' .searchInput');
    this.$button = this.$widget.find('.searchButton');
    this.options = options;
    this.spinnerTimeout = options.delayTime || 700; // Use delayTime if provided, else default to 700

    this.searchFinished = false;
    this.displaySearchFinished = false;
    this.currentSearch = '';
    this.searchTimer = null;

    this.init();
};

//RefinePanelSearchAccess.PARAMETER_SPINNER_TIMEOUT = 700;

RefinePanelSearchAccess.prototype.init = function () {
    this.initialQuery = this.$input.val().trim();
    // Focus handling when widget loads
    if (this.$input.val().length > 0) {
        this.$input.focus()[0].setSelectionRange(this.$input.val().length, this.$input.val().length);
    }

    // Store the original title for reuse
    this.$button.data('searchFilterText', this.$button.attr('title'));

    this.initCancelButton();
    this.updateButton();

    this.$input.on('input', $.proxy(function () {
        if (this.searchTimer) {
            clearTimeout(this.searchTimer);
        }

        if (this.currentSearch !== this.$input.val() || this.$input.val().length == 0) {
            if (this.$input.val().length > 0 && this.$input.val().length < this.options.minLength){
                // We clean facets div only if initial query was not empty to avoid clearing div if no
                // query has been executed at least once
                if (this.initialQuery.length > 0) {
                    // Let's consider clearing facets div since we don't execute any query
                    this.$widget.find('.facets').empty();
                    this.currentSearch = '';
                }
            } else {
                // only consider query if query empty or length greater than min length
                this.currentSearch = this.$input.val().trim();
                this.handleSuggestions();
            }
        }
    }, this));

    var refineEvtHandler = new RefineEventHandler(this.uCssId, this.options);
};

RefinePanelSearchAccess.prototype.handleSuggestions = function() {
    //Refresh if no suggest search or length greater than configured length
    if (this.currentSearch.length >= this.options.minLength || this.currentSearch.length == 0) {
        this.searchTimer = setTimeout($.proxy(function() {
            // In case of configured suggest dispatcher -> first call suggest dispatcher to get target matching facets.
            // If not, let's execute feed and post-filter facets without any facet parameter
            let targetSuggestFacets = this.options.suggestFacets;
            let reloadWidget = true;
            if (this.options.dispatcherName !== '' && this.currentSearch.trim().length > 0){
                // Call dispatcher only if query not empty
                $.ajax({
                    method: 'GET',
                    url: this.options.baseSuggestURL,
                    dataType: 'JSON',
                    async: false,
                    context: this,
                    data: { wuid: this.options.wuid, q: this.currentSearch.trim() },
                    success: function (data) {
                        if (data.answer.length > 0) {
                            targetSuggestFacets = data.answer.join();
                        } else {
                            // No facet returned by suggest, skip widget reload, simply display empty div
                            reloadWidget = false;
                            this.$widget.find('.facets').empty();
                        }
                    },
                    complete: function (jqXHR){
                        if (jqXHR.status === 204) {
                            console.log("No suggest dispatcher defined, will execute all facets and post-filter them...");
                        }
                    }
                });
            }

            // Reload refine panel only if no suggest dispatcher is configured  or if suggest dispatcher returns at least
            // one facet
            if (reloadWidget) {
                var url = new BuildUrl(window.location.href);
                var ajaxOptions = {baseUrl: this.options.baseAjaxReload};
                var client = new PlmaAjaxClient(this.$widget, ajaxOptions);
                var parameters = url.getParameters();

                /* Delete the base params */
                client.getAjaxUrl().params = {};
                $.each(parameters, function (index, value) {
                    if (index !== "" && index !== "_") {
                        for (var i = 0; i < value.length; i++) {
                            client.getAjaxUrl().addParameter(index, value[i], false);
                        }
                    }
                });

                client.getAjaxUrl().addParameter('panelId', this.options.refinesPanelId);
                // Execute only relevant feeds
                client.getAjaxUrl().addParameter('use_page_feeds', this.options.feeds.join(','));

                if (this.currentSearch.trim().length >= this.options.minLength) {
                    this.options.feeds.forEach(feedName => {
                        if (targetSuggestFacets != '') {
                            client.getAjaxUrl().addParameter(feedName + '-use_synthesis_facets', targetSuggestFacets);
                        }
                        client.getAjaxUrl().addParameter(feedName + '-facets_suggest', this.currentSearch.trim());
                        // Add maximum suggests number
                        client.getAjaxUrl().addParameter(feedName + '-max_suggests', this.options.maxSuggests);
                    });
                }

                /* set wuids to update */
                client.addWidget(this.uCssId, true, false);
                client.update();
            }

            // Keep focus on the search input
            this.$input.focus();
        }, this), this.spinnerTimeout);
    }
}

/**
 * Updates the search/cancel button UI based on input value.
 */
RefinePanelSearchAccess.prototype.updateButton = function () {
    var $input = this.$input, $button = this.$button;
    if ($input.val().trim().length > 0) {
        if (!$button.hasClass('fonticon-cancel')) {
            $button.removeClass('fonticon-search-filter').addClass('fonticon-cancel');
            $button.attr('title', 'Clear search');
        }
    } else {
        if (!$button.hasClass('fonticon-search-filter')) {
            $button.removeClass('fonticon-cancel').addClass('fonticon-search-filter');
            $button.attr('title', $button.data('searchFilterText') || 'Search filters...');
        }
    }
};

/**
 * Initializes the cancel/clear button logic.
 */
RefinePanelSearchAccess.prototype.initCancelButton = function () {
    var $input = this.$input, $button = this.$button;

    // Remove previous click handlers to avoid duplicates
    $button.off('click');

    $button.on('click', $.proxy(function () {
        if ($input.val().trim().length > 0) {
            $input.val('');
            this.handleSuggestions(); // This will update button and trigger search
        }
    }, this));
};
