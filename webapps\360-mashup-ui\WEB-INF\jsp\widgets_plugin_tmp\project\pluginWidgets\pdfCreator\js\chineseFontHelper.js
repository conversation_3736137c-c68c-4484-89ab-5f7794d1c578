/**
 * Chinese Font Helper for jsPDF
 * Alternative approach using web fonts and canvas rendering
 */

class ChineseFontHelper {
    constructor() {
        this.webFontsLoaded = false;
        this.loadWebFonts();
    }

    /**
     * Load Chinese web fonts
     */
    loadWebFonts() {
        // Load Google Fonts or other web fonts that support Chinese
        const link = document.createElement('link');
        link.href = 'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700&display=swap';
        link.rel = 'stylesheet';
        document.head.appendChild(link);

        // Wait for fonts to load
        if (document.fonts && document.fonts.ready) {
            document.fonts.ready.then(() => {
                this.webFontsLoaded = true;
                console.log('Chinese web fonts loaded successfully');
            });
        } else {
            // Fallback for older browsers
            setTimeout(() => {
                this.webFontsLoaded = true;
                console.log('Chinese web fonts loaded (fallback)');
            }, 2000);
        }
    }

    /**
     * Render Chinese text using canvas and embed as image
     */
    renderChineseTextAsImage(doc, text, x, y, options = {}) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Configuration
        const fontSize = options.fontSize || 12;
        const fontWeight = options.fontWeight || 'normal';
        const color = options.color || '#000000';
        const fontFamily = options.fontFamily || '"Noto Sans SC", "Microsoft YaHei", "SimSun", sans-serif';
        
        // Set font and measure text
        ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`;
        const metrics = ctx.measureText(text);
        
        // Set canvas size with padding
        const padding = 4;
        canvas.width = Math.ceil(metrics.width) + padding * 2;
        canvas.height = fontSize * 1.5 + padding * 2;
        
        // Clear and set styles again (canvas resets after size change)
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`;
        ctx.fillStyle = color;
        ctx.textBaseline = 'top';
        ctx.textAlign = 'left';
        
        // Enable anti-aliasing for better quality
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // Draw text
        ctx.fillText(text, padding, padding);
        
        // Convert to image and add to PDF
        const imageData = canvas.toDataURL('image/png', 1.0);
        const imgWidth = canvas.width * 0.75; // Convert pixels to points (96 DPI to 72 DPI)
        const imgHeight = canvas.height * 0.75;
        
        // Add image to PDF
        doc.addImage(imageData, 'PNG', x, y - imgHeight + fontSize, imgWidth, imgHeight);
        
        return {
            width: imgWidth,
            height: imgHeight
        };
    }

    /**
     * Check if text contains Chinese characters
     */
    containsChinese(text) {
        // Comprehensive Chinese character detection
        const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff\u3000-\u303f]/;
        return chineseRegex.test(text);
    }

    /**
     * Split text into Chinese and non-Chinese segments
     */
    splitMixedText(text) {
        const segments = [];
        let currentSegment = '';
        let isCurrentChinese = false;
        
        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            const isChinese = this.containsChinese(char);
            
            if (i === 0) {
                isCurrentChinese = isChinese;
                currentSegment = char;
            } else if (isChinese === isCurrentChinese) {
                currentSegment += char;
            } else {
                segments.push({
                    text: currentSegment,
                    isChinese: isCurrentChinese
                });
                currentSegment = char;
                isCurrentChinese = isChinese;
            }
        }
        
        if (currentSegment) {
            segments.push({
                text: currentSegment,
                isChinese: isCurrentChinese
            });
        }
        
        return segments;
    }

    /**
     * Render mixed text (Chinese + English) with proper handling
     */
    renderMixedText(doc, text, x, y, options = {}) {
        const segments = this.splitMixedText(text);
        let currentX = x;
        
        segments.forEach(segment => {
            if (segment.isChinese) {
                // Render Chinese as image
                const result = this.renderChineseTextAsImage(doc, segment.text, currentX, y, options);
                currentX += result.width;
            } else {
                // Render English with regular jsPDF
                doc.text(segment.text, currentX, y);
                const textWidth = doc.getTextWidth(segment.text);
                currentX += textWidth;
            }
        });
        
        return currentX - x; // Return total width
    }
}

// Export for use in other files
window.ChineseFontHelper = ChineseFontHelper;
