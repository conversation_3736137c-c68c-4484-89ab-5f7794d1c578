/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 07:39:16 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;
import java.util.*;
import com.google.gwt.libideas.server.LocaleMatcher;
import com.exalead.gwt.bconsole.client.i18n.LocaleUtils;

public final class index_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("java.util");
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = new java.util.HashSet<>();
    _jspx_imports_classes.add("com.exalead.gwt.bconsole.client.i18n.LocaleUtils");
    _jspx_imports_classes.add("com.google.gwt.libideas.server.LocaleMatcher");
  }

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html;charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.1//EN\" \"http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd\">\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<html>\r\n");
      out.write("<head>\r\n");
      out.write("<meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\" />\r\n");
      out.write("<meta http-equiv=\"X-UA-Compatible\" content=\"IE=9\" />\r\n");
      out.write("<meta http-equiv=\"X-UA-Compatible\" content=\"IE=8\" />\r\n");
      out.write("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\r\n");
      out.write("<meta charset=\"UTF-8\" />\r\n");
      out.write("<meta name=\"gwt:property\"\r\n");
      out.write("	content=\"locale=");
 
			Cookie[] cookies = request.getCookies();
			String locale = "";
			if (cookies != null) {
				for (Cookie cookie : cookies) {
					if (cookie.getName().equals(LocaleUtils.COOKIE_NAME)) {
						if (cookie.getValue() != null) {
							locale = cookie.getValue();
						}
						break;
					}
				}
			}
			if (locale.equals("")) {
				List<String> availableLanguages = Arrays.asList("en", "fr");
				LocaleMatcher localeMatcher = new LocaleMatcher(availableLanguages);
				locale = localeMatcher.findBestMatch(request.getHeader("Accept-language"));
			}
			out.print(locale);
      out.write("\" />\r\n");
      out.write("<title>Business Console</title>\r\n");
      out.write("\r\n");
      out.write("<script type=\"text/javascript\" src=\"jquery-3.7.1.min.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"js/highcharts.js\"></script>\r\n");
      out.write("\r\n");
      out.write("<!-- CodeMirror stuff -->\r\n");
      out.write("<link rel=\"StyleSheet\" type=\"text/css\" href=\"codemirror/lib/codemirror.css\" />\r\n");
      out.write("<link rel=\"StyleSheet\" type=\"text/css\" href=\"codemirror/lib/codemirror-custom.css\" />\r\n");
      out.write("<script type=\"text/javascript\" src=\"codemirror/lib/codemirror.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"codemirror/lib/codemirror-assets.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"configclassesdefinition\"></script>\r\n");
      out.write("<!-- End of CodeMirror stuff -->\r\n");
      out.write("\r\n");
      out.write("<link rel=\"StyleSheet\" href=\"blue/widget.css\"></link>\r\n");
      out.write("<link type=\"text/css\" rel=\"stylesheet\" href=\"livegrid.css\" />\r\n");
      out.write("<link type=\"text/css\" rel=\"stylesheet\" href=\"enterprise_css/style.css\" />\r\n");
      out.write("<link type=\"text/css\" rel=\"stylesheet\" href=\"layout/leftmenu/menu.css\"></link>\r\n");
      out.write("<link type=\"text/css\" rel=\"stylesheet\" href=\"layout/layout.css\"></link>\r\n");
      out.write("<link type=\"text/css\" rel=\"stylesheet\" href=\"commons/commons.css\"></link>\r\n");
      out.write("<link type=\"text/css\" rel=\"stylesheet\" href=\"commons/icons.css\"></link>\r\n");
      out.write("<link type=\"text/css\" rel=\"stylesheet\" href=\"css/BusinessConsole.css\" />\r\n");
      out.write("\r\n");
      out.write("<link type=\"text/css\" rel=\"shortcut icon\" href=\"images/favicon.ico\" type=\"image/x-icon\" />\r\n");
      out.write("\r\n");
      out.write("<script type=\"text/javascript\" language=\"javascript\"\r\n");
      out.write("	src=\"bconsole.nocache.js\"></script>\r\n");
      out.write("</head>\r\n");
      out.write("<body>\r\n");
      out.write("	<iframe src=\"javascript:''\" id=\"__gwt_historyFrame\"\r\n");
      out.write("		style=\"position: absolute; width: 0; height: 0; border: 0\" />\r\n");
      out.write("	<noscript>\r\n");
      out.write("		<div\r\n");
      out.write("			style=\"width: 22em; position: absolute; left: 50%; margin-left: -11em; color: red; background-color: white; border: 1px solid red; padding: 4px; font-family: sans-serif\">\r\n");
      out.write("			Your web browser must have JavaScript enabled in order for this\r\n");
      out.write("			application to display correctly.</div>\r\n");
      out.write("	</noscript>\r\n");
      out.write("</body>\r\n");
      out.write("</html>\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
