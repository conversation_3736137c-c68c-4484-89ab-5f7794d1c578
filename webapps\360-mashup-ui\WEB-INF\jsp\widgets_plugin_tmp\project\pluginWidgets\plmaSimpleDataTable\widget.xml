<?xml version="1.0" encoding='UTF-8'?>
<Widget name="[BETA] PLMA simple data table" group="PLM Analytics/Data" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>
		&lt;p&gt;This widget aims at presenting data in a table and enabling the user to easily interact and browse these data.&lt;/p&gt;
		&lt;p&gt;This widget renders data similarly as 'plmaDataTable' widget but using external configuration, ajax reloading is also optimized.&lt;/p&gt;
	</Description>
	
	<Preview>
	<![CDATA[
		<img src="/resources/widgets/plmaSimpleDataTable/images/preview.png" alt="Table" />
	]]>
	</Preview>
	
	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>
	
	<Includes>
		<Include type="css" path="../plmaResources/lib/notify/notify-plma.less"/>
		<Include type="css" path="../plmaResources/css/styles/spinner.less"/>
		<Include type="css" path="../plmaResources/css/styles/popupstyle.less"/>
		<Include type="css" path="../plmaResources/css/fullScreenWidget.less"/>
		<Include type="css" path="css/datatables.css"/>
		<Include type="css" path="css/style.less"/>
		<!--<Include type="css" path="css/dataTables.jqueryui.css"/>-->
		<!-- Does not work, so, remove this plugin, to be investigated....
		<Include type="css" path="css/jquery.dataTables.colResize.css"/>
		-->

		<Include type="js" path="../plmaResources/lib/notify/notify.js"/>
		<Include type="js" path="../plmaResources/lib/notify/notify-plma.js"/>
		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js"/>
		<Include type="js" path="../plmaResources/js/popupLib.js"/>
		<Include type="js" path="../plmaResources/js/fullScreenWidget.js"/>
		<Include type="js" path="../plmaResources/js/queueManager.js"/>
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js"/>
		<Include type="js" path="../3dxpSearchAndRefine/js/message-handler.js"/>
		<Include type="js" path="../plmaResources/js/experienceAPIs/Session3DContent.js"/>
		<Include type="js" path="../plmaResources/js/select.js" />
		<Include type="js" path="../plmaResources/js/SelectionWidget.js" />
		<Include type="js" path="js/datatables.js"/>
		<Include type="js" path="js/plmaSimpleDataTable.js"/>
		<Include type="js" path="js/buttons.colVis.js"/>

		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js"/>
		<Include type="js" path="../plmaResources/js/detailHitHelper.js"/>
		<Include type="js" path="../plmaResources/js/plmaUrlBuilder.js"/>
		<!-- Does not work, so, remove this plugin, to be investigated....
		<Include type="js" path="js/jquery.dataTables.colResize.js"/>
		-->
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResultListCommons" />
		<Widget name="preferredHit" />
		<Widget name="plmaExport" />
		<Widget name="compareHit" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO_OR_MANY" label="Subwidgets" />
	<SupportFeedsId arity="ONE" />
	
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>widget.plmaDataTable.error.server</JsKey>
			<JsKey>widget.plmaDataTable.error.noFeeds</JsKey>
			<JsKey>widget.plmaDataTable.error.feedError</JsKey>
			<JsKey>widget.plmaDataTable.error.saveState</JsKey>
			<JsKey>widget.plmaDataTable.export</JsKey>
			<JsKey>widget.plmaDataTable.export.success</JsKey>
			<JsKey>widget.plmaDataTable.config.section.displayedColumns</JsKey>
			<JsKey>widget.plmaDataTable.config.section.exportAllColumns</JsKey>
			<JsKey>widget.plmaDataTable.config.section.exportAllColumns.info</JsKey>
			<JsKey>widget.plmaDataTable.config.section.fixedColumns</JsKey>
			<JsKey>widget.plmaDataTable.config.section.fixedRows</JsKey>
			<JsKey>widget.plmaDataTable.config.fixedColumns.number</JsKey>
			<JsKey>widget.plmaDataTable.reset.success</JsKey>
			<JsKey>widget.plmaDataTable.transpose.columnTitle</JsKey>
			<JsKey>widget.plmaDataTable.config.section.fixedRows.info</JsKey>
			<JsKey>widget.plmaDataTable.config.section.fixedRows.ok</JsKey>
			<JsKey>widget.plmaDataTable.config.section.fixedRows.select</JsKey>
			<JsKey>widget.plmaDataTable.dom.error</JsKey>
			<JsKey>widget.plmaDataTable.button.resetState</JsKey>
		</JsKeys>
	</SupportI18N>
	
	<OptionsGroup name="General">
		<Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Widget title. If blank, no title is displayed.</Description>
		</Option>

		<Option id="resultListId" name="ResultList Id" isEvaluated="true" arity="ONE">
			<Description>ResultList Configurations ID to use. You can provide MEL expression as well if you need conditional display.</Description>
		</Option>

		<Option id="height" name="Height">
			<Description>Height to give to the table, in pixels. 0 means no fixed height.</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>

		<Option id="displayHeader" name="Display header" isEvaluated="true">
			<Description>Display header with number of hits and actions buttons.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>

		<Option id="enableSortButton" name="Display header sort button">
			<Description>Enable sort button in result list header, if applicable for at least one column.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>

		<Option id="descriptionButton" name="Display description button">
			<Description>Enable show/hide description button in result list header.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Callbacks">
		<Option id="enableRowCallbacks" name="Enable row callback(s)" arity="ONE">
			<Description>Enables callback function after row rendering</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['rowCallbackFunction']})</Display>
			</Functions>
		</Option>

		<Option id="rowCallbackFunction" name="Row callback function" arity="ZERO_OR_MANY" isEvaluated="true">
			<Description><![CDATA[ Javascript code executed when row is rendered (also on pagination event). <br/>
									cf. https://datatables.net/reference/option/createdRow <br/>
					function (row, data, dataIndex) {}
				]]></Description>
			<Placeholder>function (row, data, dataIndex) {}</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				<ContextMenu>addContext("PLMADatatable - Generic row callback", ["function (row, data, dataIndex) {}", "function (row, data, dataIndex) {console.log(data)}"])</ContextMenu>
				<ContextMenu>addContext("PLMADatatable - toggle hit details", ["function (row, data, dataIndex) { $(row).toggleHitDetails('hit', data.uri) }"])</ContextMenu>
				<ContextMenu>addContext("PLMADatatable - activate refine links", ["function (row, data, dataIndex) { $(row).addRefineClickEvents() }"])</ContextMenu>
				<ContextMenu>addContext("PLMADatatable - 3D Navigate select row callback", ["function (row, data, dataIndex) {PLMA3DNavigate.select([data.physicalid])}"])</ContextMenu>
			</Functions>
		</Option>

		<Option id="enableDrawCallback" name="Enable table callback" arity="ONE">
			<Description>Enables callback function after table rendering</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['drawCallbackFunction']})</Display>
			</Functions>
		</Option>

		<Option id="drawCallbackFunction" name="Table callback function" isEvaluated="true">
			<Description><![CDATA[ Javascript code executed when table is rendered (also on pagination event). <br/>
									cf. https://datatables.net/reference/option/drawCallback <br/>
					function (settings) {}
				]]></Description>
			<Placeholder>function(settings){ }</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Style/behavior">
		<Option id="scrollY" name="Scroll Y" arity="ONE">
			<Description>Vertical scrolling (https://datatables.net/reference/option/scrollY).</Description>
		</Option>

		<Option id="enablePagination" name="Display pagination button" arity="ONE">
			<Description>Display pagination selection button.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>

		<Option id="fixedHeader" name="Fixed header" arity="ONE">
			<Description>Activate fixed header.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>

		<Option id="responsive" name="Responsive display" arity="ONE">
			<Description>Activate responsive mode.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>

		<Option id="infiniteScroll" name="Infinite scroll" arity="ONE">
			<Description>Activate infinite scroll, page numbers are not displayed in this case.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>

		<Option id="enableColsReorder" name="Enable columns reordering" arity="ONE">
			<Description>Activate columns reorder plugin, simply select a column and drrag and drop it.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>

		<Option id="saveState" name="Save datatable state" arity="ONE">
			<Description>
				Save datatable state in user preferences (state key is resultlist ID).
				State means for now pagination and columns order, we don't store sorted column because not sure it makes
				sense to have sorted resultset when coming first time from another page...
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>

		<Option id="enableColsVisibility" name="Display columns visibility button" arity="ONE">
			<Description>Activate columns visibility plugin, this allows to select displayed columns and selection is saved if save state option is activated.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Export">
		<Description>ReslutList Export option (recommended to use streaming mode to avoid too many feed queries on server side). </Description>
		<Option id="enableExport" name="Enable" arity="ONE">
			<Description>
				Enables an "export" button that allows the user to download data to a CSV file. &lt;br/&gt;
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['exportIconCss','exportLabel','exportShowLabel','exportNumHits','exportColumnsConfig','exportMode','exportPerPage','exportFileName','exportEncoding','exportSeparator','exportDelimiter','exportAddBOM','exportRawValues']})</Display>
			</Functions>
		</Option>
		<Option id="exportIconCss" name="Icon CSS" >
			<Description>Specifies the CSS class name of the icon added to the button. If empty, no icon is displayed.</Description>
		</Option>
		<Option id="exportLabel" name="Label" isEvaluated="true" arity="ONE">
			<Description>Specifies the label of the button. Briefly explains the purpose of the button (3 words max).</Description>
		</Option>
		<Option id="exportShowLabel" name="Show label" arity="ONE">
			<Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="exportNumHits" name="Hits limit" arity="ONE">
			<Description>
				(Only for &lt;b&gt;Hits&lt;/b&gt; mode) The maximum number of hits to export. Set to &lt;code&gt;-1&lt;/code&gt; to set no limit on the export.&lt;br/&gt;
				&lt;b&gt;WARNING&lt;/b&gt;: exporting a large set of results could take a while. It is highly recommended to use the
				&lt;b&gt;Search API&lt;/b&gt; mode for high number of hits, as it allows streaming results.
			</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="exportColumnsConfig" name="ResultList ID" arity="ZERO_OR_ONE">
			<Description>Columns external config ID</Description>
		</Option>
		<Option id="exportMode" name="Export mode" arity="ONE">
			<Description>
				(Only for &lt;b&gt;Hits&lt;/b&gt; mode) The underlying API used to export the data. Only used in &lt;b&gt;Hits&lt;/b&gt; mode (in &lt;b&gt;Synthesis&lt;/b&gt; mode, the data is fully loaded).&lt;br/&gt;
				&lt;b&gt;Access API&lt;/b&gt; mode will execute the feed and attached Access triggers to get the data, querying page after page to get the desired number of results.&lt;br/&gt;
				&lt;b&gt;Search API&lt;/b&gt; mode will execute the provided query to get the data, and stream the results from the index without needing to paginate. It is more efficient than the Access API.&lt;br/&gt;
				&lt;b&gt;WARNINGS&lt;/b&gt;
				&lt;ul&gt;
				&lt;li&gt;
				In &lt;b&gt;Search API&lt;/b&gt; mode, the row order in the exported file is not guaranteed to be the same as displayed in the table. This is because this mode uses
				the streaming option of the Search API, which allows fast download but prevents from sorting documents. Make sure to adapt the hits limit so your users have all the data they need.
				&lt;/li&gt;
				&lt;li&gt;
				In &lt;b&gt;Search API&lt;/b&gt; mode, MEL expressions are evaluated from the &lt;b&gt;entry&lt;/b&gt; scope only. In the Data tab, when configuring column values, the MEL expressions should
				not use any other scopes as the MEL evaluator will not be aware of them (such as &lt;b&gt;feeds&lt;/b&gt;).
				&lt;/li&gt;
				&lt;li&gt;
				The &lt;b&gt;Search API&lt;/b&gt; mode only works with CloudView feeds.
				&lt;/li&gt;
				&lt;/ul&gt;
			</Description>
			<Values>
				<Value>Search API</Value>
				<Value>Access API</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['Access API'], showOptions:['exportPerPage']})</Display>
			</Functions>
		</Option>
		<Option id="exportPerPage" name="Override 'per_page'" arity="ZERO_OR_ONE">
			<Description>
				When using the &lt;b&gt;Access API&lt;/b&gt; export mode, you can override the &lt;code&gt;per_page&lt;/code&gt; parameter of the feed. Setting a higher value will allow to produce less queries to the index.
				Set to &lt;code&gt;-1&lt;/code&gt; to fetch all hits in one query (the &lt;b&gt;Hits limit&lt;/b&gt; still applies). Leave empty to use the value set on the feed.
			</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="exportFileName" name="File name" isEvaluated="true" >
			<Description>The name of the created CSV file. Defaults to 'export.csv'</Description>
		</Option>
		<Option id="exportEncoding" name="Encoding" arity="ZERO_OR_ONE" >
			<Description>You can choose an encoding for the exported CSV file. Defaults to UTF-8.</Description>
		</Option>
		<Option id="exportSeparator" name="Separator" arity="ZERO_OR_ONE">
			<Description>Character to use as a separator in the exported CSV file. Defaults to &lt;b&gt;;&lt;/b&gt;.</Description>
		</Option>
		<Option id="exportDelimiter" name="Record Delimiter">
			<Description>
				Character that is used to separate consecutive records in the output file.&lt;br/&gt;Default option will take the value based on OS.&lt;br/&gt;
				&lt;ul&gt;
				&lt;li&gt;Unix -> LF&lt;/li&gt;
				&lt;li&gt;Windows -> CR + LF&lt;/li&gt;
				&lt;li&gt;Mac -> CR&lt;/li&gt;
				&lt;/ul&gt;
			</Description>
			<Values>
				<Value>Default</Value>
				<Value>LF</Value>
				<Value>CR+LF</Value>
				<Value>CR</Value>
			</Values>
		</Option>
		<Option id="exportAddBOM" name="Add BOM">
			<Description>Whether to add the byte order mark (BOM) at the start of the file.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="exportRawValues" name="Export raw values">
			<Description>Export raw values or I18N values (applicable for facets and headers).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Advanced">
		<Option id="customHTMLNoResultMessage" name="No result template (html)" isEvaluated="true">
			<Description>Specifies a custom HTML text message when there are no results. Can be a MEL expression.</Description>
			<Functions>
				<Display>SetType('code', { mode: 'html', fullscreen: false })</Display>
			</Functions>
		</Option>
		<Option id="noResultsJspPathHit" name="JSP path to use if no results" arity="ONE">
			<Description>If there are no results, the widget is either hidden using a trigger, or a message is displayed using the content of a JSP file.</Description>
			<Functions>
				<Check>isJspPath</Check>
			</Functions>
		</Option>
		<Option id="templateBasePath" name="Base path of the JSP templates" arity="ONE">
			<Description>You can replace the relative path by an absolute path like /WEB-INF/jsp/mydirectory/.</Description>
			<Functions>
				<Check>isDirectory</Check>
			</Functions>
		</Option>

		<Option id="enableHitDetails" name="Enable hit details" arity="ONE">
			<Description>Enables hit details display on row click. It adds a default callback on row selection.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['hitParamName', 'hitParamValue']})</Display>
			</Functions>
		</Option>

		<Option id="hitParamName" name="Hit parameter name" arity="ONE_OR_ZERO">
			<Description>The name of the URL parameter that will contain the selected hit URI when loading details in Ajax (default is 'hit').</Description>
		</Option>
		<Option id="hitParamValue" name="Hit parameter meta" arity="ONE_OR_ZERO">
			<Description>The name of the meta value to put in the url param when loading details in Ajax. By default, we take the meta uri (default is 'entryUri').</Description>
		</Option>

		<Option id="displayViewId" name="Display view ID" arity="ONE">
			<Description>Result list display view ID, it is used by result list layout to choose displayed view and header buttons based on URL parameter ID.</Description>
		</Option>
		<Option id="displayViewIcon" name="Display view icon" arity="ONE">
			<Description>View icon to display in header.</Description>
		</Option>
		<Option id="displayViewTitle" name="Display view title" arity="ONE">
			<Description>View icon title to display in header.</Description>
		</Option>
		<Option id="titleTemplatePath" name="Title template path" arity="ZERO_OR_ONE" >
			<Description>Result list view title template path.</Description>
		</Option>
		<Option id="titleTemplateWidget" name="Title template widget" arity="ZERO_OR_ONE">
			<Description>Result list view title template widget ID (can be different from current view, for example to factorize code).</Description>
		</Option>
	</OptionsGroup>
	
	<DefaultValues>
		<!--<DefaultValue name="hitSeries">##########true</DefaultValue>-->
		<DefaultValue name="displayColumn">true</DefaultValue>
		<DefaultValue name="height">0</DefaultValue>
		<DefaultValue name="fixHeader">false</DefaultValue>
		<DefaultValue name="nullSort">false</DefaultValue>
		<DefaultValue name="enableScrolling">false</DefaultValue>
		<DefaultValue name="mode">Hits</DefaultValue>
		<DefaultValue name="enableFilters">false</DefaultValue>
		<DefaultValue name="dataTablesDom">liptlip</DefaultValue>
		<DefaultValue name="fixPaginationToolbar">false</DefaultValue>
		<DefaultValue name="saveState">false</DefaultValue>
		<DefaultValue name="activateReloadChart">false</DefaultValue>
		<DefaultValue name="enableExport">false</DefaultValue>
		<DefaultValue name="numHits">1000</DefaultValue>
		<DefaultValue name="exportMode">Search API</DefaultValue>
		<DefaultValue name="exportEncoding">UTF-8</DefaultValue>
		<DefaultValue name="exportSeparator">;</DefaultValue>
		<DefaultValue name="recordDelimiter">Default</DefaultValue>
		<DefaultValue name="fileName">export</DefaultValue>
		<DefaultValue name="enableExportAll">true</DefaultValue>
		<DefaultValue name="enableRowCallback">false</DefaultValue>
		<DefaultValue name="enableDrawCallback">false</DefaultValue>
		<DefaultValue name="drawCallbackFunction">function (settings) {}</DefaultValue>
		<DefaultValue name="rowCallbackFunction">function (row, data, dataIndex) {}</DefaultValue>
		<DefaultValue name="displayViewId">datatable</DefaultValue>
		<DefaultValue name="displayViewIcon">fonticon-table</DefaultValue>
		<DefaultValue name="displayViewTitle">Data table</DefaultValue>
		<DefaultValue name="noResultsJspPathHit">/WEB-INF/jsp/commons/noResults.jsp</DefaultValue>
		<DefaultValue name="templateBasePath">templates/</DefaultValue>
		<DefaultValue name="enableHitDetails">false</DefaultValue>
		<DefaultValue name="enableColsReorder">true</DefaultValue>
		<DefaultValue name="saveState">true</DefaultValue>
		<DefaultValue name="enableColsVisibility">true</DefaultValue>
		<DefaultValue name="enablePagination">true</DefaultValue>
		<DefaultValue name="scrollY">100%</DefaultValue>
		<DefaultValue name="fixedHeader">false</DefaultValue>
		<DefaultValue name="infiniteScroll">false</DefaultValue>
		<DefaultValue name="responsive">false</DefaultValue>
		<DefaultValue name="displayHeader">false</DefaultValue>
		<DefaultValue name="enableSortButton">false</DefaultValue>
		<DefaultValue name="descriptionButton">false</DefaultValue>
	</DefaultValues>
</Widget>
