<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="tiles" uri="http://tiles.apache.org/tags-tiles" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>

<render:import varWidget="widget" varFeeds="feeds" />

<config:getOption var="linkIcon" name="linkIcon" defaultValue=""/>
<config:getOption var="linkValue" name="linkValue" defaultValue=""/>
<config:getOption var="linkTitle" name="linkTitle" defaultValue=""/>
<config:getOption var="limit" name="limit" defaultValue="0"/>
<config:getOption var="facet" name="facet" defaultValue=""/>
<config:getOption var="aggregation" name="aggregation" defaultValue=""/>
<config:getOption var="chartType" name="chartType" defaultValue=""/>
<config:getOption var="tooltip" name="tooltip" defaultValue="0"/>
<config:getOption var="hideChart" name="hideChart" defaultValue="false"/>
<config:getOption var="displayDoc" name="displayDoc" defaultValue="false"/>
<config:getOption var="doc" name="doc" defaultValue=""/>
<config:getOptionsComposite var="colors" name="colors" mapIndex="true"/>

<plma:facetHistoryMetrics facet="${facet}" feeds="${feeds}" colorMappings="${colors}" nbCategories="${limit}" aggregation="${aggregation}"/>

<widget:widget varUcssId="uCssId" extraCss="plmaFacetHistoryMetrics">
	<widget:content extraCss="metrics">		
		<div class="top-bar">

			<span class="metric"></span>
			<span class="link ${linkIcon}" onclick="${linkValue}" title="${linkTitle}"></span>

			<c:if test="${displayDoc}">
				<i18n:message var="docTitle" code="widget.metrics.menu.documentation"/>
				<span class="doc-button fonticon fonticon-info" title="${docTitle}"></span>
			</c:if>
		</div>
		<div class="middle-lane">
			<span class="title widgetTitle"><config:getOption name="title" defaultValue=""/></span>
		</div>
		<div id="chart-container-${uCssId}" class="chart-container">

			<list:new var="categoriesOrderAsList"/>
			<list:new var="yAxisLabelsAsList"/>
			<list:add value="" list="${yAxisLabelsAsList}"/>
			<list:new var="forceRefineOnFeeds"/>
			<list:new var="forceRefineOnFacets"/>
			<map:new var="serieConfig"/>

			<map:put key="facetId" value="${facet}" map="${serieConfig}"/>
			<map:put key="aggregation" value="${aggregation}" map="${serieConfig}"/>
			<map:put key="representation" value="${chartType}" map="${serieConfig}"/>
			<map:put key="axis" value="0" map="${serieConfig}"/>
			<map:put key="stacking" value="Disabled" map="${serieConfig}"/>
			<map:put key="stack" value="0" map="${serieConfig}"/>
			<map:put key="legend" value="" map="${serieConfig}"/>
			<map:put key="pointLegend" value="" map="${serieConfig}"/>
			<map:put key="colorConfig" value="" map="${serieConfig}"/>
			<map:put key="maxCategories" value="${limit}" map="${serieConfig}"/>
			<map:put key="takeFirstValues" value="true" map="${serieConfig}"/>
			
			<plma:highchartsJSON var="highchartsJSON"
				feeds="${feeds}" 
				categoriesOrder="${categoriesOrderAsList}" 
				axisLabelX="" 
				axisLabelsY="${yAxisLabelsAsList}"
				maxCategories="${limit}"
				takeFirstValues="true"
				additionalCategories="Hide"
				baseUrl=""
				forceRefineOnFeeds="${forceRefineOnFeeds}"
				forceRefineOnFacets="${forceRefineOnFacets}" >
				
				<plma:highchartsSerie feeds="${feeds}" serieConfig="${serieConfig}"/>
			</plma:highchartsJSON>
			
		</div>

		<div class="doc-container hidden">
			<div class="container">${doc}</div>
			<span class="close-doc fonticon fonticon-cancel" title="<i18n:message code='generic.close' />"></span>
		</div>

		<render:renderScript position="READY">
			var options = {};
			options.tooltip = ${tooltip};
			options.colors = [];
			<c:forEach items="${colors}" var="color">
				var colorTemp = {};
				colorTemp.value = '${color.color}';
				colorTemp.start = '${color.startRange}';
				colorTemp.end = '${color.endRange}';
				options.colors.push(colorTemp);
			</c:forEach>
			options.limit = '${limit}';
			options.reverse = 'true';
			options.hideChart = '${hideChart}';
			var plmaFacetHistoryMetrics = new PLMAFacetHistoryMetrics('${uCssId}','${highchartsJSON}',options);
			plmaFacetHistoryMetrics.init();
		</render:renderScript>
	</widget:content>
</widget:widget>