<?xml version="1.0" encoding="UTF-8" ?>
<Widget name="PLMA Metrics" group="PLM Analytics/Visualizations" premium="true">

	<Description>This widget displays metrics that are based on a MEL expression.</Description>
	
	<Preview>
		<![CDATA[
			<img src="/resources/widgets/metrics/images/preview.png" alt="Metrics" />
		]]>
	</Preview>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/metrics.js" />
		<Include type="js" path="../plmaResources/js/widgetButtonManager.js"/>
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportFeedsId arity="ZERO_OR_MANY" />
	<SupportWidgetsId arity="ZERO" />
	<SupportI18N supported="false" />

	<OptionsGroup name="General">
		<Option arity="ZERO_OR_ONE" name="Title" id="title" isEvaluated="true">
			<Description>Widget Title. It can be used to describe the metric, for example, 'Most Popular Camera'.</Description>
		</Option>

		<OptionComposite id="metrics" name="Metric" arity="MANY" glue="#@~#">
			<Option arity="ONE" name="Value" id="value" isEvaluated="true">
				<Description>The metric value must be defined as a MEL expression.
					See the contextual menu for MEL samples.</Description>
				<Functions>
					<ContextMenu>COMMON_add('Samples', [
						{display:'Evolution',value:'${number:format(((feed.facets["my_facet"].flat[0].count/feed.facets["my_facet"].flat[1].count)-1)*100,1,1000,2,2,true)}%'},
						{display:'Facet count',value:'${feed.facets["my_facet"].count}'},{display:'First category count',value:'${feed.facets["my_facet"].flat[0].count}'}
					])</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option arity="ZERO_OR_ONE" name="Description" id="description" isEvaluated="true">
				<Description>Specifies the description displayed below the metric value. It can either be free text or a MEL expression. If blank, no description is displayed.</Description>
				<Functions>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option arity="ZERO_OR_ONE" name="Trend expression" id="evolution" isEvaluated="true">
<Description><![CDATA[
<p>Specifies a MEL expression that must return a number (n). The following rules are applied:</p>
<ul>
	<li>if n &gt; 0, displays <span style="color:green;">▲</span></li>
	<li>if n &lt; 0, displays <span style="color:red;">▼</span></li>
	<li>if n = 0, displays =</li>
</ul>
<p>This default behavior can be inverted by selecting the <i>Invert trend colors</i> option. Not used if left empty.</p>
]]></Description>
				<Functions>
					<Display>SetType('code', 'mel')</Display>
					<ContextMenu>COMMON_add('Samples', [{display:'Evolution',value:'${feed.facets["my_facet"].flat[0].count-feed.facets["my_facet"].flat[1].count}'}])
					</ContextMenu>
				</Functions>
			</Option>
			<Option arity="ZERO_OR_ONE" name="Invert trend colors" id="invertColor">
				<Description>Inverts trend colors. Values above 0 will be displayed in red. For example, a rise in fuel prices can be considered as a negative event.</Description>
				<Values>
					<Value>false</Value>
					<Value>true</Value>
				</Values>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>

	<OptionsGroup name="Documentation">
        <Option id="displayDoc" name="Enable Documentation">
            <Description>If true, adds a button to display a HTML documentation to give the user information about the chart. (Default value is false)</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
            <Functions>
                <Display>ToggleDisplay({valueToMatch:['true'], showOptions:['doc']})</Display>
            </Functions>
        </Option>
        <Option id="doc" name="User Reference" isEvaluated="true">
            <Description>This is an HTML code to describe the chart.</Description>
            <Functions>
                <Display>SetType('code', 'html')</Display>
            </Functions>
        </Option>
        <Option id="techDoc" name="Technical Reference" isEvaluated="true">
            <Description>This is the chart's technical reference (it won't be displayed in the application).</Description>
            <Functions>
                <Display>SetType('code', 'html')</Display>
            </Functions>
        </Option>
    </OptionsGroup>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="false" />
	</Platforms>
</Widget>
