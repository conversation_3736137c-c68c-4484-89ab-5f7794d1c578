@import "../../plmaResources/css/polyfills.less";

.flexPanelsContainer {
  .display-flex();
  //height: inherit;
  //width: 100%;

  > .flexPanelsContainer-row:only-child {
    height: 100%;
  }

  > .flexPanelsContainer-row {
    .display-flex();
    .flex-direction(row);
    .flex-wrap(nowrap);
    .align-items(stretch);
    width: 100%;

    > .flexPanelsContainer-item {
      .flex-shrink(1);
      .flex-basis(0);
    }

    &.wrapPanels {
      .flex-wrap(wrap);

      > .flexPanelsContainer-item {
        .flex-shrink(0);
      }
    }

  }

  /* Cannot use LESS loops because guards are only available in LESS 1.5.0 */

  .flexPanelsContainer-col-1 {
    .flex-grow(1);
  }

  .flexPanelsContainer-col-2 {
    .flex-grow(2);
  }

  .flexPanelsContainer-col-3 {
    .flex-grow(3);
  }

  .flexPanelsContainer-col-4 {
    .flex-grow(4);
  }

  .flexPanelsContainer-col-5 {
    .flex-grow(5);
  }

  .flexPanelsContainer-col-6 {
    .flex-grow(6);
  }

  .flexPanelsContainer-col-7 {
    .flex-grow(7);
  }

  .flexPanelsContainer-col-8 {
    .flex-grow(8);
  }

  .flexPanelsContainer-col-9 {
    .flex-grow(9);
  }

  .flexPanelsContainer-col-10 {
    .flex-grow(10);
  }

  .flexPanelsContainer-col-11 {
    .flex-grow(11);
  }

  .flexPanelsContainer-col-12 {
    .flex-grow(12);
  }

  .flexPanelsContainer-col-13 {
    .flex-grow(13);
  }

  .flexPanelsContainer-col-14 {
    .flex-grow(14);
  }

  .flexPanelsContainer-col-15 {
    .flex-grow(15);
  }

  .flexPanelsContainer-col-16 {
    .flex-grow(16);
  }

  .flexPanelsContainer-col-17 {
    .flex-grow(17);
  }

  .flexPanelsContainer-col-18 {
    .flex-grow(18);
  }

  .flexPanelsContainer-col-19 {
    .flex-grow(19);
  }

  .flexPanelsContainer-col-20 {
    .flex-grow(20);
  }

  .flexPanelsContainer-col-21 {
    .flex-grow(21);
  }

  .flexPanelsContainer-col-22 {
    .flex-grow(22);
  }

  .flexPanelsContainer-col-23 {
    .flex-grow(23);
  }

  .flexPanelsContainer-col-24 {
    .flex-grow(24);
  }

  .flexPanelsContainer-col-25 {
    .flex-grow(25);
  }

  .flexPanelsContainer-col-26 {
    .flex-grow(26);
  }

  .flexPanelsContainer-col-27 {
    .flex-grow(27);
  }

  .flexPanelsContainer-col-28 {
    .flex-grow(28);
  }

  .flexPanelsContainer-col-29 {
    .flex-grow(29);
  }

  .flexPanelsContainer-col-30 {
    .flex-grow(30);
  }

  .flexPanelsContainer-col-31 {
    .flex-grow(31);
  }

  .flexPanelsContainer-col-32 {
    .flex-grow(32);
  }

  .flexPanelsContainer-col-33 {
    .flex-grow(33);
  }

  .flexPanelsContainer-col-34 {
    .flex-grow(34);
  }

  .flexPanelsContainer-col-35 {
    .flex-grow(35);
  }

  .flexPanelsContainer-col-36 {
    .flex-grow(36);
  }

  .flexPanelsContainer-col-37 {
    .flex-grow(37);
  }

  .flexPanelsContainer-col-38 {
    .flex-grow(38);
  }

  .flexPanelsContainer-col-39 {
    .flex-grow(39);
  }

  .flexPanelsContainer-col-40 {
    .flex-grow(40);
  }

  .flexPanelsContainer-col-41 {
    .flex-grow(41);
  }

  .flexPanelsContainer-col-42 {
    .flex-grow(42);
  }

  .flexPanelsContainer-col-43 {
    .flex-grow(43);
  }

  .flexPanelsContainer-col-44 {
    .flex-grow(44);
  }

  .flexPanelsContainer-col-45 {
    .flex-grow(45);
  }

  .flexPanelsContainer-col-46 {
    .flex-grow(46);
  }

  .flexPanelsContainer-col-47 {
    .flex-grow(47);
  }

  .flexPanelsContainer-col-48 {
    .flex-grow(48);
  }

  .flexPanelsContainer-col-49 {
    .flex-grow(49);
  }

  .flexPanelsContainer-col-50 {
    .flex-grow(50);
  }

  .flexPanelsContainer-col-51 {
    .flex-grow(51);
  }

  .flexPanelsContainer-col-52 {
    .flex-grow(52);
  }

  .flexPanelsContainer-col-53 {
    .flex-grow(53);
  }

  .flexPanelsContainer-col-54 {
    .flex-grow(54);
  }

  .flexPanelsContainer-col-55 {
    .flex-grow(55);
  }

  .flexPanelsContainer-col-56 {
    .flex-grow(56);
  }

  .flexPanelsContainer-col-57 {
    .flex-grow(57);
  }

  .flexPanelsContainer-col-58 {
    .flex-grow(58);
  }

  .flexPanelsContainer-col-59 {
    .flex-grow(59);
  }

  .flexPanelsContainer-col-60 {
    .flex-grow(60);
  }

  .flexPanelsContainer-col-61 {
    .flex-grow(61);
  }

  .flexPanelsContainer-col-62 {
    .flex-grow(62);
  }

  .flexPanelsContainer-col-63 {
    .flex-grow(63);
  }

  .flexPanelsContainer-col-64 {
    .flex-grow(64);
  }

  .flexPanelsContainer-col-65 {
    .flex-grow(65);
  }

  .flexPanelsContainer-col-66 {
    .flex-grow(66);
  }

  .flexPanelsContainer-col-67 {
    .flex-grow(67);
  }

  .flexPanelsContainer-col-68 {
    .flex-grow(68);
  }

  .flexPanelsContainer-col-69 {
    .flex-grow(69);
  }

  .flexPanelsContainer-col-70 {
    .flex-grow(70);
  }

  .flexPanelsContainer-col-71 {
    .flex-grow(71);
  }

  .flexPanelsContainer-col-72 {
    .flex-grow(72);
  }

  .flexPanelsContainer-col-73 {
    .flex-grow(73);
  }

  .flexPanelsContainer-col-74 {
    .flex-grow(74);
  }

  .flexPanelsContainer-col-75 {
    .flex-grow(75);
  }

  .flexPanelsContainer-col-76 {
    .flex-grow(76);
  }

  .flexPanelsContainer-col-77 {
    .flex-grow(77);
  }

  .flexPanelsContainer-col-78 {
    .flex-grow(78);
  }

  .flexPanelsContainer-col-79 {
    .flex-grow(79);
  }

  .flexPanelsContainer-col-80 {
    .flex-grow(80);
  }

  .flexPanelsContainer-col-81 {
    .flex-grow(81);
  }

  .flexPanelsContainer-col-82 {
    .flex-grow(82);
  }

  .flexPanelsContainer-col-83 {
    .flex-grow(83);
  }

  .flexPanelsContainer-col-84 {
    .flex-grow(84);
  }

  .flexPanelsContainer-col-85 {
    .flex-grow(85);
  }

  .flexPanelsContainer-col-86 {
    .flex-grow(86);
  }

  .flexPanelsContainer-col-87 {
    .flex-grow(87);
  }

  .flexPanelsContainer-col-88 {
    .flex-grow(88);
  }

  .flexPanelsContainer-col-89 {
    .flex-grow(89);
  }

  .flexPanelsContainer-col-90 {
    .flex-grow(90);
  }

  .flexPanelsContainer-col-91 {
    .flex-grow(91);
  }

  .flexPanelsContainer-col-92 {
    .flex-grow(92);
  }

  .flexPanelsContainer-col-93 {
    .flex-grow(93);
  }

  .flexPanelsContainer-col-94 {
    .flex-grow(94);
  }

  .flexPanelsContainer-col-95 {
    .flex-grow(95);
  }

  .flexPanelsContainer-col-96 {
    .flex-grow(96);
  }

  .flexPanelsContainer-col-97 {
    .flex-grow(97);
  }

  .flexPanelsContainer-col-98 {
    .flex-grow(98);
  }

  .flexPanelsContainer-col-99 {
    .flex-grow(99);
  }

  .flexPanelsContainer-col-100 {
    .flex-grow(100);
  }
}

/* IE fixes */
.ie .flexPanelsContainer {
  > .flexPanelsContainer-row {
    /* Create a stacking context at row level to allow absolute-positionned elements in
       child cols to overlap other cols. See AppsRefs:#9532 */
    opacity: 0.99;
  }
}
