/**
 * Loading spinner - style in spinner.less
 */

jQuery.fn.showPLMASpinner = function(options) {
	options = options || {};

	var $this = $(this);
	if ($this.length > 0 && $this.is(':visible') && $this.find('> .loading-spinner').length == 0) {
		var position = $this.position();

		// Overlay
		if (options.overlay == true) {
			$this.append('<div class="loading-overlay" style="position: absolute;"></div>');
		}

		// Spinner
		$this.append("<div class='loading-spinner' style='position: absolute;'></div>");
		$this.css('position','relative');
	}
};

jQuery.fn.hidePLMASpinner = function(fade) {
	var $this = $(this);
	if ($this.is(':visible')) {
		if (fade == undefined || fade == true) {
			$this.find("> .loading-spinner").fadeOut('normal', function() {
				$(this).parent().find('> .loading-overlay').remove();
				$(this).remove();
			});
		} else {
			$this.find('> .loading-spinner').remove();
			$this.find('> .loading-overlay').remove();
		}
	}
};