<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import parameters="widget,feeds,uCssId" />
<render:import parameters="columnsConfig,doTrasnspose" ignore="true"/>

<c:set var="defaultTemplateValue" value="templates/hit-default.jsp"/>
<c:if test="${not empty columnsConfig}">
	<c:set var="defaultTemplateValue" value="templates/hit-newconfig.jsp"/>
</c:if>
<config:getOption var="customJspPathHit" name="customJspPathHit"/>
<config:getOption var="defaultJspPathHit" name="defaultJspPathHit" defaultValue="${defaultTemplateValue}"/>

<c:if test="${not empty columnsConfig && defaultJspPathHit == 'templates/hit-default.jsp'}">
	<div class="config-error badge badge-error">Widget configured using application config, but template used is old(hit-default.jsp). Use (hit-newconfig.jsp) instead, else unexpected issues can be experienced.</div>
</c:if>

<search:forEachFeed feeds="${feeds}" var="feed" varStatus="accessFeedsStatus">
    <search:forEachEntry var="entry" feed="${feed}" varStatus="entryStatus">
    
        <%-- Retrieve hit properties  --%>
        <config:getOption var="hitTitle" name="hitTitle" defaultValue="${entry.title}" entry="${entry}" feed="${feed}" />
        <config:getOption var="hitUrl" name="hitUrl" entry="${entry}" feed="${feed}" />
    
        <%-- Renders a custom view for this hit --%>
        <render:template template="${customJspPathHit}" defaultTemplate="${defaultJspPathHit}">
            <render:parameter name="accessFeeds" value="${feeds}" />
            <render:parameter name="feed" value="${feed}" />
            <render:parameter name="uCssId" value="${uCssId}"/>
            <render:parameter name="entry" value="${entry}" />
            <render:parameter name="widget" value="${widget}" />
            <c:if test="${hitUrl != null}">
                <render:parameter name="hitUrl" value="${hitUrl}" />
            </c:if>
            <c:if test="${hitTitle != null}">
                <render:parameter name="hitTitle" value="${hitTitle}" />
            </c:if>
            <render:parameter name="accessFeedsStatus" value="${accessFeedsStatus}" />
            <render:parameter name="entryStatus" value="${entryStatus}" />
			<render:parameter name="columnsConfig" value="${columnsConfig}"/>
			<render:parameter name="doTrasnspose" value="${doTrasnspose}"/>
        </render:template>

        <render:template template="templates/select-api.jsp" />
    </search:forEachEntry>
</search:forEachFeed>