.collapsibleBlock{
    margin-bottom: 2px;
    > .widgetHeader{
        padding: (@line-height /2);
        border-left: 4px solid @clink-active;
		color: @clink-active;
		font-weight: bold;
		font-size: 18px;
        .spinner-container {
			display: inline-block;
			width: 20px;
			height: 20px;
			position: relative;
			top: 4px;
			left: 15px;
			.spinner {
				border: 4px solid @cblock-border;
				border-top: 4px solid @clink-active;
				border-radius: 50%;
				width: 15px;
				height: 15px;
				animation: spin 2s linear infinite;
				z-index: 20;
			}
            &.hidden {
                display: none;
            }
        }
    }
        
    > .collapse-trigger{
        cursor: pointer;
        justify-content: space-between;
		.collapse-icon, .uncollapse-icon{
			float: right;
			font-size: 10px;
		}
    }
    
    &.uncollapsed{
        > .collapse-trigger > .uncollapse-icon{
            display: none;
        }    
    }
    
    
    &.collapsed{
        > .collapse-trigger > .collapse-icon{
            display: none;
        }
		> .collapse-content{
            display: none;
        }                
    }

    &.no-border{
        > h2 {
            border: none;
        }
        > .widgetContent{
            border: none;
        }
    }
}