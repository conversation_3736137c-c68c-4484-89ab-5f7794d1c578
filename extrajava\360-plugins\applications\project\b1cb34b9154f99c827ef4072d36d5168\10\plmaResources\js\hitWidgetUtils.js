/**
 * an utils file that contain behaviours shared by hit widget (plmaResultList / resultCarousel / hitDetails)
 */

var hitWidgetUtils = {
    /**
     * add opacity to color
     * @param  {jquery} $element - the icon element
     */
    initColorWithOpacity : function($element){
        var rgbColor = $element.css('background-color');
        if(rgbColor){
            //use rgba instead of rgb to add fourth parameter opacity ie fix
            rgbaColor = rgbColor.replace("rgb(", "rgba(");
            rgbaColor = rgbaColor.replace(')', ', 0.3)');
            $element.css('background-color', rgbaColor);
        }
    }
};