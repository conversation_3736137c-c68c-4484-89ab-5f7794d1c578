<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Query bookmark" group="PLM Analytics">

    <Includes>
        <Include type="js" path="js/queryBookmark.js" />
        <Include type="css" path="css/style.less" />
    </Includes>

    <Description>Creates a QueryManager that manages saves queries and a drop-down list that displays them.</Description>

    <Preview>
        <![CDATA[
      <img src="/resources/widgets/queryBookmark/images/preview.png" alt="Bookmarks" />
    ]]>
    </Preview>

    <SupportI18N supported="false"/>
    <SupportFeedsId arity="ZERO" />
    <SupportWidgetsId arity="ZERO" />
</Widget>