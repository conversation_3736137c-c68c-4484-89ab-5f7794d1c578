<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds"/>

<request:isAjax var="isAjax"/>
<request:getParameterValues var="infiniteScrollValues" name="infiniteScroll"/>
<request:getParameterValues var="paginationLoad" name="paginationLoad"/>
<request:getParameterValues var="isInfiniteScrollRequest" name="isInfiniteScrollRequest"/>
<c:set var="isAnInfiniteScrollRequest" value="false"/>
<c:forEach var="value" items="${infiniteScrollValues}">
	<c:if test="${value!=''}"><c:set var="isAnInfiniteScrollRequest" value="true"/></c:if>
</c:forEach>
<c:set var="scrollContainerId" value="${uCssId}_scroll-container" />
<c:if test="${showThumbnail}">
	<config:setOption name="mainContainerId" value="${scrollContainerId}" component="${widget}"/>
</c:if>

<config:getOption name="resultListId" var="resultListId"/>
<config:getOption name="enableInfiniteScroll" var="enableInfiniteScroll"/>
<config:getOption var="showThumbnail" name="showThumbnail" defaultValue="false"/>
<config:getOption var="displayHeader" name="displayHeader" defaultValue="false"/>
<config:getOption var="customExportJSP" name="customExportJSP" defaultValue="templates/exportDisplay.jsp" />
<config:getOption name="containerHeight" var="containerHeight"/>
<config:getOption var="doTranspose" name="doTranspose" defaultValue="true"/>
<c:set var="isGridTable" value="true"/>
<c:set var="isCompare" value="true"/>

<config:getOption var="enableSelect" name="enableSelect" defaultValue="false"/>
<config:getOption var="enableCompare" name="enableCompare" defaultValue="false"/>
<config:getOption var="compareCollection" name="compareCollection" defaultValue="compare"/>
<config:getOption var="selectCollection" name="selectCollection" defaultValue="hits-selection"/>
<plma:getCollection varItems="collectionItems" varCount="compareCollectionCount" collection="${compareCollection}"/>

<%--TODO reactivate option in widget.xml ?--%>
<config:getOption name="disableResultListPrefTab" var="disableResultListPrefTab" defaultValue="false" />

<widget:getUcssId var="uCssId"/>
<c:set var="scrollContainerId" value="${uCssId}_scroll-container" />
<c:if test="${showThumbnail}">
	<config:setOption name="mainContainerId" value="${scrollContainerId}" component="${widget}"/>
</c:if>

<config:getOption var="doTrasnspose" name="doTrasnspose" defaultValue="false"/>

<%-- Use config value if parameter is absent else choose from the param value --%>
<request:getParameterValue var="gridtableParam" name="gridtable" defaultValue=""/>
<c:if test="${gridtableParam != ''}">
    <c:set var="doTranspose" value="${gridtableParam == 'transpose'}"/>
    <config:setOption name="defaultLayout" value="GridTable" component="${widget}"/>
    <config:getOption name="defaultLayout" var="defaultLayout" defaultValue="Responsive"/>
    <c:set var="isGridTable" value="true"/>
</c:if>

<plma:resultListConfig var="columnsConfig" resultListId="${resultListId}" toMap="false"/>
<c:set var="gridtableCSS" value="gridtable-layout gridtable${doTranspose? '-transpose' : ''}"/>

<c:choose>
<%--	Simply render hits in infinite scroll context--%>
	<c:when test="${isAjax && (isAnInfiniteScrollRequest || fn:length(paginationLoad) > 0)}">
		<render:template template="templates/hits.jsp" widget="plmaResultListCommons">
			<render:parameter name="feeds" value="${feeds}"/>
			<render:parameter name="widget" value="${widget}"/>
			<render:parameter name="uCssId" value="${uCssId}"/>
			<render:parameter name="columnsConfig" value="${columnsConfig}"/>
			<render:parameter name="isGridTable" value="${isGridTable}"/>
		</render:template>
		<c:if test="${search:hasEntries(feeds) && !enableInfiniteScroll}">
			<div class="pagination">
				<render:template template="templates/pagination.jsp" widget="plmaPagination">
					<render:parameter name="accessFeeds" value="${feeds}"/>
					<%--<render:parameter name="cssId" value="${cssId}"/>--%>
					<render:parameter name="wuid" value="${uCssId}"/>
				</render:template>
			</div>
		</c:if>
	</c:when>

	<c:when test="${compareCollectionCount > 1}">
	    <widget:widget extraCss="plmaResultList plmaGridtableResultList" varCssId="cssId" varUcssId="uCssId"
                                extraStyles="height: ${not empty containerHeight ? containerHeight.concat('px') : '100%'}">
            <c:choose>
                <c:when test="${!search:hasFeeds(feeds)}">
                    <widget:header>
                        <config:getOption name="title" defaultValue=""/>
                    </widget:header>
                    <widget:content>
                        <render:definition name="noFeeds">
                            <render:parameter name="widget" value="${widget}"/>
                            <render:parameter name="showSuggestion" value="true"/>
                        </render:definition>
                        <render:template template="templates/select-api.jsp" widget="plmaResultListCommons" />
                    </widget:content>
                </c:when>

                <%-- If all feeds have no results --%>
                <c:when test="${!search:hasEntries(feeds)}">
                    <widget:header extraCss="empty-result">
                        <config:getOption name="title" defaultValue=""/>
                    </widget:header>
                    <widget:content>
                        <config:getOption var="customHTMLNoResultMessage" name="customHTMLNoResultMessage"/>
                        <c:choose>
                            <c:when test="${not empty customHTMLNoResultMessage}">
                                <div class="noresult">
                                    ${customHTMLNoResultMessage}
                                </div>
                            </c:when>
                            <c:otherwise>
                                <config:getOption var="noResultsJspPathHit" name="noResultsJspPathHit"
                                                  defaultValue="/WEB-INF/jsp/commons/noResults.jsp"/>
                                <render:template template="${noResultsJspPathHit}">
                                    <render:parameter name="accessFeeds" value="${feeds}"/>
                                    <render:parameter name="showSuggestion" value="true"/>
                                </render:template>
                            </c:otherwise>
                        </c:choose>
                        <render:template template="templates/select-api.jsp" widget="plmaResultListCommons" />
                    </widget:content>
                    <%-- /If all feeds have no results --%>
                </c:when>
                <c:otherwise>
                    <div class="panels-container">
                        <div class="results-panel">
                            <div id="${scrollContainerId}" class="scroll-container ${gridtableCSS}" style="height: 800px">
                                <ul class="fix-header hitCustomList">
                                    <li class="grid-row table-header">
                                        <render:template template="templates/tableHeader.jsp" widget="plmaResultListCommons">
                                            <render:parameter name="feeds" value="${feeds}"/>
                                            <render:parameter name="widget" value="${widget}"/>
                                            <render:parameter name="uCssId" value="${uCssId}"/>
                                            <render:parameter name="columnsConfig" value="${columnsConfig}"/>
                                            <render:parameter name="isGridTable" value="${isGridTable}"/>
                                            <render:parameter name="doTranspose" value="${doTranspose? 'transpose' : ''}"/>
                                        </render:template>
                                    </li>
                                </ul>
                                <ul class="hits hitCustomList gridtable-layout" ucssId="${uCssId}">
                                    <render:template template="templates/hits.jsp" widget="plmaResultListCommons">
                                        <render:parameter name="feeds" value="${feeds}"/>
                                        <render:parameter name="widget" value="${widget}"/>
                                        <render:parameter name="uCssId" value="${uCssId}"/>
                                        <render:parameter name="columnsConfig" value="${columnsConfig}"/>
                                        <render:parameter name="isCompare" value="${isCompare}"/>
                                        <render:parameter name="doTranspose" value="${doTranspose? 'transpose' : ''}"/>
                                    </render:template>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <render:renderScript position="READY">
                        <string:escape var="entryUri" value="${entryUri}" escapeType="JAVASCRIPT"/>
                        <search:getPaginationInfos feeds="${feeds}" varCurrentPage="currentPage" varLastPage="maxPages"/>
                        <config:getOptionsComposite var="infiniteScrollExtraParameters" name="infiniteScrollExtraParameters" mapIndex="true"/>
                        var extraParameters = {};
                        <c:forEach items="${infiniteScrollExtraParameters}" var="infiniteScrollExtraParameter">
                            extraParameters["${infiniteScrollExtraParameter.parameterName}"] = ["<string:escape value="${infiniteScrollExtraParameter.parameterValue}" escapeType="JAVASCRIPT"/>"];
                        </c:forEach>
                        <c:if test="${search:hasEntries(feeds) && enableInfiniteScroll}">
                            new PlmaInfiniteScroll('${uCssId}', ${maxPages}, {
                                feedsName:  ${plma:getKeys(feeds, '[]')},
                                scrollOrientation: '${doTranspose? 'x' : 'y' }',
                                pageInWidgetSelector: '.results-panel > .scroll-container .hits',
                                getRelativeBlock: function(){
                                    var relativeBlock = $('.${uCssId} .results-panel > .scroll-container .hits');
                                    return relativeBlock;
                                },
                                getAbsoluteBlock: function(){
                                    var absoluteBlock = $('.${uCssId} .results-panel > .scroll-container');
                                    var customAbsoluteBlock = $("${customAbsoluteBlock}");
                                    absoluteBlock = (customAbsoluteBlock.length>0?customAbsoluteBlock:absoluteBlock);
                                    return absoluteBlock;
                                },
                                pxBeforeEndToTrigger: '100',
                                getNewPageParams: function(){
                                    var hasTableView = !$($('.${uCssId} .hit .hit-table')[0]).hasClass('hidden');
                                    var params = {};
                                    params.isTableView = hasTableView;
                                    params = $.extend(params, PlmaResultList._PARAMS, extraParameters);
                                    return params;
                                },
                                onLoadPageSuccessEnd: function() {
                                    $(window).trigger(PlmaInfiniteScroll.PAGE_LOADED);
                                },
                                loadingBlockSelector: '.loading-indicator-block'
                            });
                        </c:if>

                        <c:if test="${disableResultListPrefTab == 'false'}">
                            <plma:resultListJSONConfig var="resultListJSON" resultListId="${resultListId}"/>
                            var prefOptions = {
                                buttonSelector: '.resultListHeaderWithToolbar .headerToolbar .action-display-settings'
                            };
                            var prefObj = new PlmaResultListPref('${uCssId}', prefOptions, ${resultListJSON});
                            $(prefOptions.buttonSelector).on('click', function (e) {
                                prefObj.$elements.LIGHTBOX.show();
                            }.bind(this));
                        </c:if>
                    </render:renderScript>
                </c:otherwise>
            </c:choose>

        </widget:widget>
	</c:when>
	<c:otherwise>
        <render:renderScript>
            window.location.href = window.location.origin + window.location.pathname;
        </render:renderScript>
    </c:otherwise>
</c:choose>

<render:renderScript position="READY">
	var widget = $('.' + '${uCssId}');
    new HitDetailsEventsHandler('${uCssId}');
    new CompareUiManager(widget, {
       radioBtnSelector: '.plmaButton.ref-compare-button',
       refParam: 'compare_ref',
       refHandler: CompareUiManager.RefChangeReloadHandler
     });
</render:renderScript>

