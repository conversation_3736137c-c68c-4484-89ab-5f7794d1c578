<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import parameters="feeds,entry,showLabel,label,hitID,collection,counterSelector"/>

<plma:isPreferredHit var="isPreferredHit" id="${hitID}" collection="${collection}"/>

<div class="plmaButton favorite-button preferred-hit">
    <c:choose>
        <c:when test="${isPreferredHit}">
            <i class="fonticon fonticon-favorite-on collection-modifier preferred-hit" data-status="on" data-id="${hitID}" title="Remove"></i>
        </c:when>
        <c:otherwise>
            <i class="fonticon fonticon-favorite-off collection-modifier preferred-hit" data-status="off" data-id="${hitID}" title="${label}"></i>
        </c:otherwise>
    </c:choose>

    <c:if test="${showLabel}">
        <span class="button-label">${label}</span>
    </c:if>
</div>

<render:renderScript position="READY">
    var options = {};
    options.url = '<c:url value="/preferredhit"/>';
    options.page = '<search:getPageName/>';
    options.collection = '${collection}';
    options.counterSelector = '.preferred-hits';
    options.docId = '${hitID}';

    new PreferredHit(options);
</render:renderScript>