function PlmaInfiniteScroll(widgetUCssId, maxPages, options) {
    this.options = {
        feedsName: [],
        pageInWidgetSelector: '.widgetContent:first',
        manualTriggerSelector: null,
        scrollOrientation: 'y', /* can be 'x' or 'y' */
        getRelativeBlock: function() {
            return this.widget;
        },
        getAbsoluteBlock: function() {
            return window;
        },
        getLocation: function() {
            return window.location;
        },
        pxBeforeEndToTrigger: '600',
        getNewPageParams: null,
        onInitEnd: function() {},
        onLoadPageSuccessStart: function(pageNum) {},
        onLoadPageSuccessHtmlUpdate: function(widgets, pageNum) {
            if(this.widgetPageContainer.length !== 1)
                throw new Error("PlmaInfiniteScroll: Could not instantiate - no page container found");
            
            $(widgets[0].html).appendTo(this.widgetPageContainer);
        },
        onLoadPageSuccessJSUpdate: function(pageAppendScript, pageNum) {
            $('#mainWrapper').append(pageAppendScript);
        },
        onLoadPageSuccessEnd: function(pageNum) {}
    };

    /* paginated widget */
    this.widgetUCssId = null;
    this.widget = null;
    this.widgetPageContainer = null;
    
    /* Blocks */
    this.relativeBlock = null;
    this.absoluteBlock = null;
    this.loadingBlock = null;
    
    /* status */
    this.page = 1;
    this.maxPages = null;
    this.isLoading = false;
    this.scrollPositionTrigger = null;
    
    this._constructor = function(widgetUCssId, maxPages, options) {
        this.options = $.extend(true, this.options, options);

        this.widget = $('.wuid.' + widgetUCssId);
        if(this.widget.length !== 1)
            throw new Error("PlmaInfiniteScroll: Could not instantiate - no widget to paginate (uCssId: " + widgetUCssId + ")");
        
        this.widgetUCssId = widgetUCssId;
        
        this.widgetPageContainer = $(this.options.pageInWidgetSelector, this.widget);

        this.relativeBlock = $(this.options.getRelativeBlock.call(this));
        if(this.relativeBlock.length !== 1)
            throw new Error("PlmaInfiniteScroll: Could not instantiate - no relativeBlock found", this.relativeBlock);

        this.absoluteBlock = $(this.options.getAbsoluteBlock.call(this));
        if(this.absoluteBlock.length !== 1)
            throw new Error("PlmaInfiniteScroll: Could not instantiate - no absoluteBlock found", this.absoluteBlock);

        this.loadingBlock = $(this.options.loadingBlockSelector, this.widget);

        this.maxPages = maxPages;

        this.init();

        return this;
    };

    this.init = function() {
        if (this.options.manualTriggerSelector != null){
            $(this.options.manualTriggerSelector).on('click', $.proxy(function(event){
                if(!this.isLoading && this.page < this.maxPages) {
                    this.isLoading = true;
                    this.loadingBlock.addClass("active");
                    
                    var onFinish = function() {
                        this.isLoading = false;
                        this.loadingBlock.removeClass("active");
                    };
                    
                    this.loadPage(this.page + 1, $.proxy(onFinish, this), $.proxy(onFinish, this));
                }
            }, this));
        }
        else{
            this.updateScrollPositionTrigger();
            
            $(window).on('resize', $.proxy(function() {
                this.updateScrollPositionTrigger();
                this.absoluteBlock.trigger('scroll'); 
            }, this));
            
            this.absoluteBlock.on('scroll', $.proxy(this.onScroll, this));
            
            // Trigger a scroll if trigger is equal to zero
            if(this.scrollPositionTrigger <= 0)
                this.absoluteBlock.trigger('scroll');            
        }

        this.widget.addClass('withInfiniteScroll');

        // Callback
        if($.isFunction(this.options.onInitEnd))
            this.options.onInitEnd.call(this);
    };
    
    this.onScroll = function(e) {
        /* If we reach the trigger, load next page */
        if (this.shouldLoadPage(e)) {
            this.isLoading = true;
            
            this.loadingBlock.addClass("active");
            
            var onFinish = function() {
                this.isLoading = false;
                this.updateScrollPositionTrigger();
                
                this.loadingBlock.removeClass('active');
                // Trigger a scroll if trigger is equal to zero
                if(this.scrollPositionTrigger <= 0)
                    this.absoluteBlock.trigger('scroll');   
            };
            
            this.loadPage(this.page + 1, $.proxy(onFinish, this), $.proxy(onFinish, this));
        } else if (this.scrollPositionTrigger === 0) {
            // Refresh in case of hidden block at init
            this.updateScrollPositionTrigger();
        }
    };
    
    this.shouldLoadPage = function(e) {
        var scrollPosition = this.options.scrollOrientation === 'y' ? $(e.delegateTarget).scrollTop() : $(e.delegateTarget).scrollLeft();
        var blockIsVisible = this.options.scrollOrientation === 'y' ? this.relativeBlock.height() > 0 : this.relativeBlock.width() > 0;
        return scrollPosition >= this.scrollPositionTrigger
            && blockIsVisible
            && !this.isLoading
            && this.page < this.maxPages;
    };
    
    this.loadPage = function(pageNum, success, fail) {
          var s = success || $.noop;
          var f = fail || $.noop;
                    
          var pageUrl = new BuildUrl(this.options.getLocation().search);
          pageUrl.addParameter('infiniteScroll', this.widgetUCssId, true);
          $.each(this.options.feedsName, function(i, feedName) {
              pageUrl.addParameter(feedName + '.page', pageNum, true);
          });

          // Callback
          if($.isFunction(this.options.getNewPageParams)) {
              var paramsToAdd = this.options.getNewPageParams.call(this, pageNum);
              for(var param in paramsToAdd) {
                  var paramValue = paramsToAdd[param];
                  if (paramValue instanceof Array){
                      pageUrl.addParameters(param, paramValue);
                  }else{
                      pageUrl.addParameter(param, paramValue);
                  }
              }
          }
          
        var ajaxClient = new PlmaAjaxClient(this.widget, {
        });
        ajaxClient.setQueryString(pageUrl.toString().substr(1));
        ajaxClient.addWidget(this.widgetUCssId, false);
        ajaxClient.getWidget($.proxy(function(widgets, pageAppendScript) {
            // Callback
            if($.isFunction(this.options.onLoadPageSuccessStart))
                this.options.onLoadPageSuccessStart.call(this, pageNum);
            
            // Callbacks
            this.options.onLoadPageSuccessHtmlUpdate.call(this, widgets, pageNum);
            this.options.onLoadPageSuccessJSUpdate.call(this, pageAppendScript, pageNum);
            
            this.page = pageNum;
            
            s();

            // Callback
            if($.isFunction(this.options.onLoadPageSuccessEnd))
                this.options.onLoadPageSuccessEnd.call(this, pageNum);
        }, this), $.proxy(f, this));
    };
    
    this.updateScrollPositionTrigger = function() {
        var position;
        if(this.options.scrollOrientation === 'y' && this.relativeBlock.height() > 0)
            position = this.relativeBlock.height() - this.absoluteBlock.height() - this.options.pxBeforeEndToTrigger;
        else if (this.relativeBlock.width() > 0)
            position = this.relativeBlock.width() - this.absoluteBlock.width() - this.options.pxBeforeEndToTrigger;
        
        this.scrollPositionTrigger = position > 0 ? position : 0;
    };
    
    return this._constructor(widgetUCssId, maxPages, options);
};

PlmaInfiniteScroll.PAGE_LOADED = 'page_loaded.plma_infinite_scroll';