widget.plmaDataTable.dom.error=DataTable DOM options are not wrapped precisely.
widget.plmaDataTable.config=Edit settings...
widget.plmaDataTable.config.section.displayedColumns=Displayed columns
widget.plmaDataTable.config.section.exportAllColumns=Export All columns
widget.plmaDataTable.config.section.exportAllColumns.info=If false, only visible columns exported.
widget.plmaDataTable.config.section.fixedColumns=Fixed columns
widget.plmaDataTable.config.fixedColumns.number=Number of fixed columns 
widget.plmaDataTable.error.server=An error occurred while contacting the server.
widget.plmaDataTable.error.saveState=An error occurred while saving the table state, some changes might not be taken into account.
widget.plmaDataTable.error.noFeeds=An error occurred when executing the query: no feeds were found.
widget.plmaDataTable.error.feedError=An error occurred when executing the query.
widget.plmaDataTable.error.export=An error occurred while exporting data from the server.
widget.plmaDataTable.export=Export data
widget.plmaDataTable.export.success=Export will contain limited items
widget.plmaDataTable.fullScreen=Full screen
widget.plmaDataTable.menu=Menu
widget.plmaDataTable.menu.close=Close menu
widget.plmaDataTable.reset=Reset table state
widget.plmaDataTable.reset.success=The table state has been reset.
widget.plmaDataTable.search=Search data
widget.plmaDataTable.transpose=Swap axes
widget.plmaDataTable.transpose.columnTitle=Values
widget.plmaDataTable.doc=Documentation
widget.plmaDataTable.config.section.fixedRows=Select rows
widget.plmaDataTable.config.section.fixedRows.info=Fixed rows are not saved in the table state.
widget.plmaDataTable.config.section.fixedRows.ok=Ok
widget.plmaDataTable.config.section.fixedRows.select=Select rows