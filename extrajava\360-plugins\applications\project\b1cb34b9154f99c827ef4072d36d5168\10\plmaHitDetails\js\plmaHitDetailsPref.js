/*
 * Create tab in the global preferences object.
 * pass { prefObject } in options to immediately add the tab.
 * else it will wait for the window.Preferences.initCallBacks.
 */
var PlmaHitDetailsPrefTab = function(options) {
    if(window.Preferences){
        this.hdConfigList = [];
        this.options = $.extend({},options);

        this.$tab =
            $('<div class="preference-tab preference-hitdetails-tab">' +
                '<span class="tab-icon fonticon fonticon-list "></span>' +
                '<span class="label">' + Preferences.getMessage('plma.preference.hitdetails.tab.label') + '</span>' +
            '</div>');
        this.$tabContainer =
            $('<div class="preference-container preference-hitdetails-container">' +
                '<div class="preference-title">' +
                    '<div class="main">' +
                        '<span>' + Preferences.getMessage('plma.preference.hitdetails.tab.title') + '</span>' +
                    '</div>' +
                    '<div class="description">' + Preferences.getMessage('plma.preference.hitdetails.tab.description') + '</div>' +
                '</div>' +
                '<div class="preference-block-container">' +
                    '<div class="hitdetails_list wrapped"></div>' +
                    '<div class="hitdetails_details active"></div>' +
                '</div>' +
            '</div>');
        this.init();
        return this;
    }
}

PlmaHitDetailsPrefTab.prototype.init = function(){
	if(this.options.prefObject){
		this.displayHitDetailsConfigs(this.options.prefObject);
	}else if(window.Preferences) {
		window.Preferences.getInitCallbacks()
			.add(function (preferences) {
				if(!preferences.hasTab('hitdetails')){
					this.displayHitDetailsConfigs(preferences);
				}
			}.bind(this))
	}
}

PlmaHitDetailsPrefTab.prototype.displayHitDetailsConfigs = function(prefObject){
	prefObject.addTab({
		id: 'hitdetails',
		onInit: $.noop,
		tab: this.$tab,
		container: this.$tabContainer
	});
	//this.bindEvents(this.$container);
	this.options.appConfig = $.extend(false, {}, {
		configName: prefObject.configName()
	}, this.options.appConfig);

	$.ajax({
		type: 'GET',
		url: mashup.baseUrl + '/config/hitdetails/getlist',
		data: {
			confName: this.options.appConfig.configName
		}
	}).then(function(data){
		this.hdConfigList = data.answer;
		this.hdConfigForms = {};
		
		var $ul = $('<ul></ul>');
		this.hdConfigList.forEach(function(hdConfig){
			var $li = 
				$('<li class="hitdetails-config-item" data-id="' + hdConfig.id + '">' +
					'<div class="hitHeader">' + 
						'<span class="hit-name">' + hdConfig.label + '</span>' +
						'<span class="description">' + hdConfig.description + '</span>' +
					'</div>' +
				'</li>');
			$ul.append($li);
			$li.on('click', function(e){
				$ul.find('li').removeClass('selected');
				var $el = $(e.target).closest('li');
				this.$tabContainer.find('.hitdetails_details .hitdetails-preference-form:not(.hidden)').addClass('hidden');
				
				var $form = this.$tabContainer.find('.hitdetails_details #form-' + $el.attr('data-id'));
				$el.addClass('selected');
				$form.removeClass('hidden');					
			}.bind(this));
			
			var $container = $('<div id="form-' + hdConfig.id + '" class="hitdetails-preference-form hidden"></div>');
			var options = $.extend({}, this.options, { 
				container: $container,
				appConfig: {
					hitDetailsId: hdConfig.id,
					configName: this.options.appConfig.configName
				}
			});
			this.hdConfigForms[hdConfig.id] = new PlmaHitDetailsPref(hdConfig.id, options);
			this.$tabContainer.find('.hitdetails_details').append($container);
		}.bind(this));
		this.$tabContainer.find('.hitdetails_list').empty().append($ul);
		$ul.find('li:first').trigger('click');
	}.bind(this));
}

/*
 * Create Result list Preference form and manages the modifications.
 */
var PlmaHitDetailsPref = function (uCssId, options) {
    if(window.Preferences){
        this.uCssId = uCssId;
        this.options = $.extend({
            groupConfigCssId: this.uCssId + '_fieldgroup_sortable'
        },options);
        this.$widget = uCssId? $('.wuid.' + uCssId) : $();
        this.$elements = {};

        this.defaultAjaxData = {
            confName: this.options.appConfig.configName,
            id: this.options.appConfig.hitDetailsId
        }
        this.$elements.PREF_CONTAINER = this.options.container;
        this.initPreferences().then(this.initGroups.bind(this));
        this.initButtons(this.$elements.PREF_CONTAINER);
        return this;
    }
}

PlmaHitDetailsPref.prototype.initPreferences = function () {
	this.$elements.PREF_CONTAINER.append($('<div class="header">' +
		'<span class="label"></span>' +
		'<span class="description"></span>' +
	'</div>'));

	this.$elements.GROUPS_CONTAINER = $('<div id="' + this.options.groupConfigCssId +'" class="groups-config"></div>');
	this.$elements.PREF_CONTAINER.append(this.$elements.GROUPS_CONTAINER);
	
	return $.ajax({
		type: 'GET',
		url: mashup.baseUrl + '/config/hitdetails/getconfig',
		data: $.extend({}, this.defaultAjaxData, {}),
		success: function (data) {
			this.hitDetailsConfig = data;
		}.bind(this)
	}).then(function(){
		if(this.hitDetailsConfig == undefined){
			throw "Error while getting hitDetailsConfig";
		}
		this.$elements.PREF_CONTAINER.find('.header .label').text(this.hitDetailsConfig.label);
		this.$elements.PREF_CONTAINER.find('.header .description').text(this.hitDetailsConfig.description);
	}.bind(this));
}

/*
PlmaResultListPref.prototype.reset = function () {

	We get default config and init groups based on this config (but not user session one, it is not saved in database, if preferences are closed,
	it is reinitialized with user session config but if save button is clicked, default config is sent to controller)

	$.ajax({
		type: 'GET',
		url: mashup.baseUrl + '/config/hitdetails/default',
		data: $.extend({}, this.defaultAjaxData, {}),
		success: function (data) {
			this.hitDetailsConfig = data;
			this.initGroups();
		}.bind(this)
	});
}
*/

PlmaHitDetailsPref.prototype.initGroups = function () {
	this.$elements.GROUPS_CONTAINER.empty();
	this.unsetModified();
	this.hitDetailsConfig.fieldGroup.forEach(function(groupConfig){
		var $item = $('<div class="group" data-groupid="' + groupConfig.id + '">' +
			'<div class="group-header">' +
				'<span class="move icon fonticon fonticon-drag-grip"></span>' +
				'<span class="icon fonticon ' + groupConfig.icon + '"></span>' +
				'<span class="label">' + groupConfig.label + '</span>' +
				'<span class="description">' + groupConfig.description + '</span>' +	
			'</div>' +
		'</div>' );
		this.renderConditional($item.find('.group-header'), groupConfig.displayCondition);
		$item.data('groupData', groupConfig);
		
		var $fieldsContainer = $('<div id="' + this.options.groupConfigCssId + '_' + groupConfig.id +'" class="fields-config"></div>');
		$item.append($fieldsContainer);
		this.initFields($fieldsContainer, groupConfig);
		this.$elements.GROUPS_CONTAINER.append($item);
	}.bind(this));
	
	this.$elements.GROUPS_CONTAINER.sortable({
      items: 'div.group',
	  placeholder: 'group ui-state-highlight',
	  change: function(event, ui){
		  this.setModified();
	  }.bind(this)
    });
	
	this.$elements.GROUPS_CONTAINER.find('div.field .condition').hover(function(){
		if($(this).parent().prev().length == 0 ||
           $(this).parent().offset().left - 20 < $(this).parent().parent().offset().left){
            $(this).addClass('show-right');
		}
	}, function(){
		$(this).removeClass('show-right');
	});
}

PlmaHitDetailsPref.prototype.initFields = function ($container, groupConfig) {
	groupConfig.field.forEach(function(fieldConfig){
		var $item = $('<div class="field" data-fieldid="' + fieldConfig.id + '">' +
			'<span class="move icon fonticon fonticon-drag-grip"></span>' +
			'<span class="label">' + fieldConfig.label + '</span>' +
		'</div>' );
		this.renderConditional($item, fieldConfig.displayCondition);
		$item.data('fieldData', fieldConfig);
		$container.append($item);
	}.bind(this));
	
	$container.sortable({
      items: 'div.field',
	  connectWith: '.fields-config',
	  placeholder: 'field ui-state-highlight',
	  change: function(event, ui){
		  this.setModified();
	  }.bind(this)
    });	
}

PlmaHitDetailsPref.prototype.renderConditional = function ($item, condition) {
	if(condition){
		$item.append($('<span class="condition fonticon fonticon-code">' +
			'<span class="condition-tooltip">' + 
				'<span class="description">' + (condition.description?condition.description:'') + '</span>' +
				'<span class="expr">' + (condition.expr?condition.expr:'') + '</span>' +
			'</span>' +
		'</span>'));
	}
}

PlmaHitDetailsPref.prototype.editHitDetails = function(){
	var $container = this.$elements.PREF_CONTAINER.find('#'+this.options.groupConfigCssId);
	
	// Set orders correctly as per user modifications.
	$container.find('.group').each(function(groupOrder, group){
		let groupData = $(group).data('groupData');
		groupData.order = groupOrder;
	
		$(group).find('.fields-config .field').each(function(fieldOrder, field){
			let fieldData = $(field).data('fieldData');
			fieldData.order = fieldOrder;
			fieldData.groupId = groupData.id;
		});
	}.bind(this));
	return $.ajax({
		type: 'POST',
		dataType: "json",
		contentType: "application/json; charset=utf-8",
		url: mashup.baseUrl + '/config/hitdetails/edit',
		data: JSON.stringify(this.hitDetailsConfig)
	});
}

PlmaHitDetailsPref.prototype.initButtons = function ($container) {
	/* Add Save / cancel buttons */
	this.$elements.BUTTONS_CONTAINER = $('<div class="button-container"></div>');
	this.$elements.SAVE_BUTTTON = $('<span class="button save-button">' + Preferences.getMessage('plma.preference.save') + '</span>');
	this.$elements.RESET_BUTTTON = $('<span class="button reset-button">' + Preferences.getMessage('plma.preference.reset') + '</span>');
	
	this.$elements.BUTTONS_CONTAINER.append(this.$elements.SAVE_BUTTTON);
	this.$elements.BUTTONS_CONTAINER.append(this.$elements.RESET_BUTTTON);
	$container.append(this.$elements.BUTTONS_CONTAINER);

	this.$elements.SAVE_BUTTTON.on('click', function () {
		this.$elements.PREF_CONTAINER.showPLMASpinner({overlay: true});
		this.editHitDetails()
			.done(function(){
				this.unsetModified();
				$.notify(Preferences.getMessage('plma.preference.save.success'), 'success');
				setTimeout($.proxy(function() {
					this.reload();
				},this),1000);
			}.bind(this))
			.fail(function(){
				this.$elements.PREF_CONTAINER.hidePLMASpinner();
				this.$elements.GROUPS_CONTAINER.notify('Error while saving preferences', 'error');
			}.bind(this));
	}.bind(this));
		
	this.$elements.RESET_BUTTTON.on('click', function () {
		this.$elements.PREF_CONTAINER.showPLMASpinner({overlay: true});
		$.ajax({
			type: 'POST',
			url: mashup.baseUrl + '/config/hitdetails/reset',
			data: $.extend({}, this.defaultAjaxData, {}),
			success: function () {
				$.notify(Preferences.getMessage('plma.preference.save.success'), 'success');
				setTimeout($.proxy(function() {
					this.reload();
				},this),1000);
			}.bind(this)
		});

		this.unsetModified();
	}.bind(this));
}

PlmaHitDetailsPref.prototype.setModified = function () {
	this.$elements.SAVE_BUTTTON.addClass('active');
}
PlmaHitDetailsPref.prototype.unsetModified = function () {
	this.$elements.SAVE_BUTTTON.removeClass('active');
}

PlmaHitDetailsPref.prototype.failureTemp = function () {
	$.notify(Preferences.getMessage('plma.preference.save.error'), 'error');
}

PlmaHitDetailsPref.prototype.reload = function () {
	window.location.reload();
}