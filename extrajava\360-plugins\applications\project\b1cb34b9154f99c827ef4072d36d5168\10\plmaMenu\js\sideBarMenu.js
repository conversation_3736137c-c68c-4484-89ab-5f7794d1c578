var SideBarMenu = function (uCssId, options) {
	'use strict';

	var defaults = {
		animationDuration: 200,
		showUserMenu: false
	};

	this.options = $.extend({}, defaults, options);
	this.uCssId = uCssId;

	this.i18nClient = new I18nClient();

	if (uCssId) {
		this.widget = $('.' + uCssId);
	} else {
		this.widget = $();
	}

	if (this.widget.length === 0) {
		throw new Error('Unable to initialize widget sideBarMenu : widget not found (uCssId: "' + uCssId + '").');
	} else {
		this.init();
	}

};

SideBarMenu.PAGE_ID_PARAMETER = ChartboardStorageManager.PAGE_ID_PARAMETER;
SideBarMenu.CHARTBOARD_ID_PARAMETER = ChartboardStorageManager.CHARTBOARD_ID_PARAMETER;
SideBarMenu.COLLAPSIBLE_COOKIE_NAME = "SIDE_BAR_MENU_COLLAPSIBLE_SECTION";

SideBarMenu.prototype.init = function () {
	'use strict';

	this.displayModeCollapse = this.widget.find('.display-mode-collapse');
	this.displayModeExpand = this.widget.find('.display-mode-expand');
	this.displayModeShow = this.widget.find('.display-mode-show');
	this.displayModeHide = this.widget.find('.display-mode-hide');

	this.displayModeCollapse.on('click', $.proxy(function () {
		this.collapse();
	}, this));
	this.displayModeExpand.on('click', $.proxy(function () {
		this.expand();
	}, this));

	/* Init responsive */
	if (this.options.display === 'Responsive') {
		if (this.options.displayState === 'responsive') {
			this.expand();
		}

		$(window).on('resize', $.proxy(function(){
			if ($(window).width() >= 1024) {
				this.expand();
			} else {
				this.collapse();
			}
		}, this));
	}

	this.widget.find('.item-wrapper .icon-shared').removeClass('hidden');
	this.widget.find('.item-wrapper .icon-favorite').removeClass('hidden');

	// Shows pinned pages
	$(document).on(PinnedPages.EVENT, $.proxy(function (e, data) {
		var CLASS_PINNED = 'pinned';
		var pages = data.pages;
		var fields = data.queryStringFields; // TODO: do not use anymore
		// remove existing pins
		$(this.widget).find('a').each(function () {
			$(this).parent().removeClass(CLASS_PINNED);
		});
		// add necessary pins
		for (var i = 0; i < pages.length; i++) {
			$(this.widget).find('a').each(function () {
				// get URL without hostname
				var a = document.createElement('a');
				a.href = $(this).prop('href');
				var url = a.pathname + a.search;
				// for IE only
				if (!url.startsWith('/')) {
					url = '/' + url;
				}
				if (PinnedPages.utils.getCleanUrl(url, pages[i].queryStringFields || fields) === pages[i].url) {
					$(this).parent().addClass(CLASS_PINNED);
				}
			});
		}
		// show pinned pages at the top (user's pages only)
		if (this.options.showUserMenu) {
			$('.user-items.user-items-pages .' + CLASS_PINNED).insertAfter($('.user-items-pages .user-section'));
			$('.user-items.user-items-share .' + CLASS_PINNED).insertAfter($('.user-items-share .user-section'));
		}
	}, this));

	this.initSearch();

	this.currentUrl = new BuildUrl(window.location.href);
	this.itemsByPageId = {};

	this.items = this.widget.find('.menu-item');
	if (this.options.showUserMenu) {
		this.showSpinner();
		this.indexItemsByPageId();
		this.initUserItems(this.postInitUserItems);
	} else {
		this.postInitUserItems();
	}

	//Close panel from other widget
	// $(document).on('plma:close-refine-panel', $.proxy(function (event, data) {
	// 	this.togglePanel(data);
	// }, this));
};

SideBarMenu.prototype.indexItemsByPageId = function () {
	this.items.each($.proxy(function (i, e) {
		var item = $(e);
		if (item.hasClass('isLink')) {
			var url = new BuildUrl(item.find('> a').attr('href'));
			this.itemsByPageId[url.getPage()] = item;
		}
	}, this));
};

SideBarMenu.prototype.initUserItems = function (callback) {
	var getMashupPages = function () {
		this.storageManager.getMashupPagesById($.proxy(function (pages) {
			// var emptyUserMenu = true;
			for (var pageId in pages) {
				var pageConfig = pages[pageId];
				this.customizeItemUrl(pageConfig);
			}

			this.hideSpinner();
			callback.call(this);
		}, this), function () {
		});
	}.bind(this);
	this.userItems = this.widget.find('.user-items-pages .user-section-container');
	this.userSharedItems = this.widget.find('.user-items-share .user-section-container');
	this.userPages = {};
	this.userItemTemplate = $('<div></div>').append(this.widget.find('.user-item-template'));
	this.storageManager = new ChartboardStorageManager();
	this.storageManager.getUserPagesById($.proxy(function (pages) {
		/* Get rights */
		if (pages.constructor === Object && Object.keys(pages).length > 0 ) {
			$.ajax({
				type: 'GET',
				url: this.options.sharePagesUrl,
				data: {
					pagesId: Object.keys(pages)
				},
				dataType: 'json',
				success: $.proxy(function (data) {
					for (var pageId in data) {
						this.userPages[pageId] = pages[pageId];
						var pageConfig = pages[pageId];
						if (data[pageId] && (data[pageId].hasReadRight || data[pageId].hasWriteRight)) {
							/* User page */
							pageConfig.hasWriteRight = data[pageId].hasWriteRight;
							pageConfig.hasReadRight = data[pageId].hasReadRight;
							this.addUserItem(pageConfig);
						}
					}
					getMashupPages();
				}, this)
			});
		} else {
			getMashupPages();
		}
	}, this), function () {
	});
};

SideBarMenu.prototype.initSearch = function () {
	this.widget.find('.search-item-input').on('keyup', $.proxy(function (e) {
		var inputValue = $(e.currentTarget).val();
		var items = this.widget.find('.item-menu-container .menu-item .item-wrapper .item-label');
		/* Display menu items according to search */
		for (var i = 0; i < items.length; i++) {
			var item = $(items[i]);
			if (item.text().toLowerCase().indexOf(inputValue.toLowerCase()) !== -1) {
				item.closest('.menu-item').removeClass('hidden');
			} else {
				item.closest('.menu-item').addClass('hidden');
			}
		}

		/* Hide unused sections */
		var sections = this.widget.find('.item-menu-container .items');
		for (var j = 0; j < sections.length; j++) {
			if (!$(sections[j]).hasClass('main-items')) {
				var sectionContainer = $(sections[j]).find('.user-section-container');
				for (var k = 0; k < sectionContainer.length; k++) {
					if ($(sectionContainer[k]).find('.menu-item:not(.hidden)').length === 0) {
						$(sectionContainer[k]).addClass('hidden');
					} else {
						$(sectionContainer[k]).removeClass('hidden');
					}
				}
			} else {
				var children = $(sections[j]).children();
				var lastSectionIndex = 0;
				var isDisplayedItem = false;
				for (var k = 0; k < children.length; k++) {
					var child = $(children[k]);
					if (child.hasClass('section-separation') && k > 0) {
						if (!isDisplayedItem) {
							$(children[lastSectionIndex]).addClass('hidden');
						} else {
							$(children[lastSectionIndex]).removeClass('hidden');
						}
						lastSectionIndex = k;
						isDisplayedItem = false;
					} else if (child.hasClass('menu-item') && !child.hasClass('hidden')) {
						isDisplayedItem = true;
					}
				}
				if (!isDisplayedItem) {
					$(children[lastSectionIndex]).addClass('hidden');
				} else {
					$(children[lastSectionIndex]).removeClass('hidden');
				}
			}
		}
	}, this));
};

SideBarMenu.prototype.postInitUserItems = function () {
	this.items = this.widget.find('.menu-item');
	this.editMenu = new EditMenu(this.uCssId, this.userPages);

	this.initDescription();
	this.initPanelToggle();
	this.initPinnedPages();
	this.initDropDownSection();
};

SideBarMenu.prototype.addUserItem = function (pageConfig) {
	var userItemHtml = this.userItemTemplate.clone().html();
	userItemHtml = this.replaceVariables(userItemHtml, pageConfig);
	var userItem = $(userItemHtml).removeClass('user-item-template');
	userItem.data('pageId', pageConfig.id);
	userItem.data('orderPage', pageConfig.orderPage);
	if (pageConfig.writers[0] !== this.options.user) {
		userItem.data('sharedPages', true);
	}

	if (this.currentUrl.getPage() === pageConfig.templatePage
		&& this.currentUrl.getParameter(SideBarMenu.PAGE_ID_PARAMETER)
		&& this.currentUrl.getParameter(SideBarMenu.PAGE_ID_PARAMETER)[0] === pageConfig.id) {
		this.items.removeClass('active');
		userItem.addClass('active');
	}

	var items = this.userItems;
	if (pageConfig.section) {
		/* Add in custom section */
		var customSections = this.widget.find('.user-items-custom .user-section-container');
		var hasSection = false;
		for (var i = 1; i < customSections.length; i++) {
			var section = $(customSections[i]);
			if (section.find('.section-label').text() === pageConfig.section) {
				/* If the section exists, get it */
				items = section;
				hasSection = true;
				break;
			}
		}
		/* Create custom section if it does not exist */
		if (!hasSection) {
			var newSection = $(customSections[0]).clone();
			newSection.find('.section-label').text(pageConfig.section);
			newSection.find('.user-section').removeClass('hidden');
			newSection.find('.user-section').removeClass('user-section-custom-template');
			newSection.data('orderSection', pageConfig.orderSection);
			newSection.find('.user-section').attr('data-section-id', pageConfig.sectionId);

			if (pageConfig.orderSection) {
				var isSectionAdded = false;
				for (var j = 0; j < customSections.length; j++) {
					var sectionItem = $(customSections[j]);
					if (parseInt(sectionItem.data('orderSection')) > pageConfig.orderSection) {
						sectionItem.before(newSection);
						isSectionAdded = true;
						break;
					}
				}
				if (!isSectionAdded) {
					this.widget.find('.item-menu-container .user-items-custom').append(newSection);
				}
			} else {
				this.widget.find('.item-menu-container .user-items-custom').append(newSection);
			}

			items = newSection;
		}
	} else if (pageConfig.writers[0] !== this.options.user) {
		/* Add in shared pages */
		items = this.userSharedItems;
		this.widget.find('.user-items-share .user-section').removeClass('hidden');
	} else {
		/* Add in my pages */
		this.widget.find('.user-items-pages .user-section').removeClass('hidden');
	}
	this.widget.find('.user-separation-section').removeClass('hidden');

	/* Add a visual clue to tell if the page is a share one */
	if (pageConfig.writers.length > 1 || pageConfig.readers.length > 0
		|| (pageConfig.groupsWriters && pageConfig.groupsWriters.length > 0) || (pageConfig.groupsReaders && pageConfig.groupsReaders.length > 0)) {
		userItem.addClass('isShared');
	}
	if (pageConfig.hasReadRight && !pageConfig.hasWriteRight) {
		userItem.addClass('readOnly');
		items.addClass('hasReadOnlyPage');
	}

	if (pageConfig.orderPage) {
		var menuItems = items.find('.menu-item');
		var isAdded = false;
		for (var i = 0; i < menuItems.length; i++) {
			var menuItem = $(menuItems[i]);
			if (parseInt(menuItem.data('orderPage')) > pageConfig.orderPage) {
				menuItem.before(userItem);
				isAdded = true;
				break;
			}
		}
		if (!isAdded) {
			items.append(userItem);
		}
	} else {
		items.append(userItem);
	}

	var indexItem = items.find('.menu-item').length;

	if (pageConfig.label && pageConfig.label.split('${i18n[\'').length > 1 && pageConfig.label.split('\']}').length > 1) {
		this.getTranslation(pageConfig.label.split('${i18n[\'')[1].split('\']}')[0], $.proxy(function (translation) {
			items.find(".menu-item")[indexItem - 1].title = translation;
			items.find(".item-label")[indexItem - 1].innerHTML = translation;
		}, this));
	}
};

SideBarMenu.prototype.customizeItemUrl = function (pageConfig) {
	var item = this.itemsByPageId[pageConfig.id];
	if (item) {
		var link = item.find('> a');
		var url = new BuildUrl(link.attr('href'));
		url.removeParameter(SideBarMenu.PAGE_ID_PARAMETER);
		url.removeParameter(SideBarMenu.CHARTBOARD_ID_PARAMETER);
		// if (pageConfig.layout) {
		// 	url.addParameter(SideBarMenu.CHARTBOARD_ID_PARAMETER, pageConfig.layout);
		// }
		link.attr('href', url.toString());
	}
};

/**
 * Given a string containing '${myVariable' markup, replaces
 * the '${myVariable}' chunks with the value found for the
 * key 'myVariable' in the given object.
 */
SideBarMenu.prototype.replaceVariables = (function () {
	var userItemPattern = /\${([\w]+)}/g;

	return function (templateString, values) {
		var result = '';
		if (templateString) {
			result = templateString.replace(userItemPattern, function (match, p1) {
				return _.escape(values[p1])
			});
		}
		return result;
	};
})();

SideBarMenu.prototype.collapse = function () {
	this.widget.addClass('collapsed').removeClass('expanded');
	$.cookie('sideBarMenu_displayState', 'collapsed');
	$(window).trigger('plma:resize', [300]);
	/* To resize highcharts */
};

SideBarMenu.prototype.expand = function () {
	this.widget.addClass('expanded').removeClass('collapsed');
	$.cookie('sideBarMenu_displayState', 'expanded');
	$(window).trigger('plma:resize', [350]);
	/* To resize highcharts */
};

SideBarMenu.prototype.initDescription = function () {
	this.itemDescription = this.widget.find('.item-description');

	if (this.itemDescription.length > 0) {
		var i18nMap = {};
		this.items.each(function () {
			var description = $(this).data('description');
			var match = description.match(/\${i18n\['(.*?)'\]}/);
			if (match) {
				/* Get i18n key */
				i18nMap[match[1]] = '';
			}
		});
		if (!$.isEmptyObject(i18nMap)) {
			this.i18nClient.getI18nList(i18nMap, $.proxy(function (translations) {
				i18nMap = translations;
				this.itemsOnMouseEnter(i18nMap);
			}, this));
		} else {
			this.itemsOnMouseEnter();
		}

		this.widget.on('mouseleave', $.proxy(function () {
			this.itemDescription.addClass('hidden');
			this.itemDescription.data('displayedItem', null);
		}, this));
	}

};

SideBarMenu.prototype.itemsOnMouseEnter = function (i18nMap) {
	this.items.on('mouseenter', $.proxy(function (e) {
		var menuItem = $(e.target).closest('.menu-item');
		var description = menuItem.data('description');
		if (description && menuItem.get(0) !== this.itemDescription.data('displayedItem')) {
			this.itemDescription.addClass('transitive');
			var match = description.match(/\${i18n\['(.*?)'\]}/);
			if (match) {
				description = i18nMap[match[1]];
			}
			setTimeout($.proxy(function () {
				if (this.widget.is(':hover')) {
					var title = menuItem.find('.item-label').text();
					this.itemDescription.find('.description-title').text(title);
					this.itemDescription.find('.description-text').text(description);
					this.itemDescription.removeClass('transitive hidden');
					this.itemDescription.data('displayedItem', menuItem.get(0));
				}
			}, this), this.options.animationDuration);
		}

	}, this));
};

SideBarMenu.prototype.initPanelToggle = function () {
	this.items.filter('.isPanelToggle').each($.proxy(function (i, e) {
		var $item = $(e);
		var panelClass = $item.data('togglepanel');
		if (panelClass) {
			$item.on('click', $.proxy(function () {
				this.togglePanel(panelClass);
			}, this));
		}
	}, this));
	var showPanel = $.cookie('sideBarMenu_showPanel');
	if (showPanel) {
		$('.collapsiblePanel.' + showPanel).removeClass('hidden');
	}
	var panelState = $.cookie('sideBarMenu_displayState');
	if (panelState === 'expanded') {
		this.widget.removeClass('collapsed');
	}
};

SideBarMenu.prototype.initPinnedPages = function () {
	var client = new StorageClient('user');
	client.get(PinnedPages.STORAGE_KEY_FIELDS, function (resp) {
		var queryStringFields = resp.length > 0 ? JSON.parse(resp[0].value) : [];

		client.get(PinnedPages.STORAGE_KEY_PAGES, function (res) {
			var pages = res.length > 0 ? JSON.parse(res[0].value) : [];
			PinnedPages.utils.triggerEvent(pages, queryStringFields);
		});
	});
};


SideBarMenu.prototype.initDropDownSection = function () {
	/* Get cookies to hide or not sections */
	var cookies = JSON.parse($.cookie(SideBarMenu.COLLAPSIBLE_COOKIE_NAME));
	for (var key in cookies) {
		var cookie = cookies[key];
		if (cookie) {
			var section = this.widget.find('.section-separation[data-section-id="' + key + '"]');
			this.toggleSection(section);
		}
	}

	/* Activate toggle on click */
	this.widget.find('.section-separation').on('click', $.proxy(function (e) {
		var section = $(e.currentTarget);
		this.toggleSection(section);
		/* Set cookies */
		var cookie = JSON.parse($.cookie(SideBarMenu.COLLAPSIBLE_COOKIE_NAME));
		if (!cookie) {
			cookie = {};
		}
		cookie[section.data('sectionId')] = section.hasClass('section-hidden');
		$.cookie(SideBarMenu.COLLAPSIBLE_COOKIE_NAME, JSON.stringify(cookie), {expires: 30});
	}, this));
};

SideBarMenu.prototype.toggleSection = function (section) {
	section.toggleClass('section-hidden');
	var page = section.next();
	while (page.hasClass('menu-item')) {
		page.toggleClass('hidden');
		page = page.next();
	}
};

SideBarMenu.prototype.togglePanel = function (panelClass) {
	this.items.filter('.isPanelToggle').each(function (i, e) {
		var item = $(e);
		if (item.data('togglepanel') === panelClass) {
			item.toggleClass('active');
		} else {
			item.removeClass('active');
		}
	});
	$('.collapsiblePanel:not(.manualCollapse)').each(function (i, e) {
		var panel = $(e);
		if (panel.hasClass(panelClass)) {
			if (panel.hasClass('hidden')) {
				panel.removeClass('hidden');
				panel.trigger('plma:panel-open');
			} else {
				panel.addClass('hidden');
				panel.trigger('plma:panel-close');
			}
		} else {
			panel.addClass('hidden');
		}
	});
	if(this.options.sidebarCookie){
	    $.cookie('sideBarMenu_showPanel', $.cookie('sideBarMenu_showPanel') === panelClass ? '' : panelClass, {path: '/'});
    }
	$(window).trigger('plma:resize', [300]);
	/* To resize highcharts */
};

SideBarMenu.prototype.reload = function () {
	var client = new PlmaAjaxClient(this.widget, {baseUrl: this.options.baseAjaxReload});
	client.addWidget(this.uCssId, true, false);
	client.update();
};

SideBarMenu.prototype.getTranslation = function (code, successCallback) {
	this.i18nClient.getI18n(code, $.proxy(function (translation) {
		successCallback.call(null, translation);
	}, this), $.proxy(function (error) {
		successCallback.call(null, code);
	}, this));
};

SideBarMenu.prototype.showSpinner = function () {
	this.widget.showPLMASpinner({overlay: true});
};

SideBarMenu.prototype.hideSpinner = function () {
	this.widget.hidePLMASpinner();
};


var hideOrShowSubItems = function (item) {
	var nextElement = item.nextElementSibling;
	var isSubItem = true;
	var indent = 0;
	var itemClassList = item.getClassList();
	for (var i = 0; i < itemClassList.length; i++) {
		if (itemClassList[i].startsWith('indent-')) {
			indent = parseInt(itemClassList[i].split('indent-')[1]);
		}
	}
	while (isSubItem) {
		var nextElementClassList = nextElement.getClassList();
		for (var i = 0; i < nextElementClassList.length; i++) {
			if (nextElementClassList[i].startsWith('indent-')) {
				if (parseInt(nextElementClassList[i].split('indent-')[1]) > indent) {
					nextElementClassList.toggle('hidden');
				} else {
					isSubItem = false;
				}
			}
		}
		nextElement = nextElement.nextElementSibling;
	}
	itemClassList.add('active-folding');
	item.getElementsByClassName('fold-icon')[0].getClassList().toggle('hidden');
	item.getElementsByClassName('fold-icon')[1].getClassList().toggle('hidden');
};

