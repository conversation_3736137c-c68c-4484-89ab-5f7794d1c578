

/**
 * File: /resources/mashupBuilder/js/Toolbox.js
 */
/**
 * Abstract class for a Mashup Toolbox
 * 
 * @constructor
 * @this {Mashup_Toolbox}
 * @param {object} options
 */
function Mashup_Toolbox(id, options) {
	this.id = id;
	this.title = options.title;
	this.hiddenByDefault = options.hiddenByDefault == undefined ? true : options.hiddenByDefault;
	this.onOpenDoc = options.onOpenDoc;
	this.showToggle = options.showToggle == undefined ? true : options.showToggle;
	this.collapseByDefault = options.collapseByDefault == undefined ? false : options.collapseByDefault;
}

/**
 * Sets the container of this toolbox
 * 
 * @param {Mashup_Toolbox_Container} toolboxContainer
 */
Mashup_Toolbox.prototype.setContainer = function(toolboxContainer) {
	this.toolboxContainer = toolboxContainer;
};

/**
 * Returns the container of this toolbox
 * 
 * @returns
 */
Mashup_Toolbox.prototype.getContainer = function() {
	return this.toolboxContainer;
};

/**
 * Returns the ID of this toolbox
 * @returns
 */
Mashup_Toolbox.prototype.getId = function() {
	return this.id;
};

/**
 * Returns the title of this toolbox
 * @returns
 */
Mashup_Toolbox.prototype.getTitle = function() {
	return this.title;
};

/**
 * jQuery onClick event callback
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox.prototype.handleEventOnClick = function(e) {
	var target = getEventTarget(e);
	if (target != null) {
		switch (target.attr('name')) {
		case 'doSlideDown':
			this.slideDown();
			return false;
		case 'doOpenDoc':
			window.mashup.documentation.doOpenDoc(target);
			return false;
		default:
			return this.onClick({ target: target, event: e });
		}
	}
	return false;
};

/**
 * Called on click
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox.prototype.onClick = function(context) {
	return false;
};

/**
 * Called on toggle
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox.prototype.onToggleEnd = function(context) {
	return false;
};

/**
 * Called at the beginning of the slide
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox.prototype.onToggleStart = function(context) {
	return false;
};

/**
 * User process to toggle the toolbox
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox.prototype.doToggle = function() {
	if (this.getEl().hasClass('toolbox-closed')) {
		this.slideDown();
	} else {
		this.slideUp();
	}
	return false;
};

/**
 * Slide up the toolbox
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox.prototype.slideUp = function() {
	var _this = this;
	this.onToggleStart({ visible: false });
	this.getElInner().slideUp('fast', function() {
		_this.getEl().addClass('toolbox-closed');
		_this.onToggleEnd({ visible: false });
	});
};

/**
 * Slide down the toolbox
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox.prototype.slideDown = function() {
	var _this = this;
	this.onToggleStart({ visible: true });
	this.getElInner().slideDown('fast', function() {
		_this.getEl().removeClass('toolbox-closed');
		_this.onToggleEnd({ visible: true });
	});
};

/**
 * Returns the DOM element for the toolbox
 * 
 * @returns
 */
Mashup_Toolbox.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('' +
		'<div class="toolbox toolbox-' + this.id + (this.showToggle ? ' toolbox-collapsable' : '') + '">' +
			(this.title == undefined ? '' : (
			'<div class="toolbox-header">' +
				'<div class="toolbox-header-title"' + (this.showToggle ? ' name="doSlideDown"' : '') + '>' + this.title + '</div> ' +
				'<div class="toolbox-back-btn">' +
					'<span class="icon icon-back" name="doPrevious"></span>' +
				'</div>' +
				'<div class="top-icons">' +
					(this.onOpenDoc == undefined ? '' :
						'<span class="icon icon-documentation" name="doOpenDoc" data-chapter="' + this.onOpenDoc + '" title="' + _.TOOLBOX_DOC_TITLE() + '"></span>'
					) +
				'</div>' +
			'</div>'
			)) +
			'<div class="middle">' +
			'</div>' +
		'</div>');
		this.el.data('_this', this);

		this.el.on('click', $.proxy(this.handleEventOnClick, this));
		this.showPrevious(false);
		if (this.doShowHeader == false) {
			this.showHeader(false);
		}
		this.getElInnerContent().appendTo(this.getElInner());

		if (this.hiddenByDefault == true) {
			this.hide();
		}

		if (this.collapseByDefault) {
			this.getEl().addClass('toolbox-closed');
		}
	}
	return this.el;
};

/**
 * Returns the DOM node for the header
 */
Mashup_Toolbox.prototype.getElHeader = function() {
	return this.getEl().find('.toolbox-header');
};

/**
 * Returns the DOM node for the top icons
 * @returns
 */
Mashup_Toolbox.prototype.getElHeaderIcons = function() {
	return this.getEl().find('.top-icons');
};

/**
 * Returns the DOM node for the title
 */
Mashup_Toolbox.prototype.getElHeaderTitle = function() {
	return this.getEl().find('.toolbox-header-title');
};

/**
 * Returns the DOM node for the content
 * @returns
 */
Mashup_Toolbox.prototype.getElInner = function() {
	return this.getEl().find('> .middle');
};

/**
 * Called when a tab of the page has been opened
 * 
 * @param tab
 */
Mashup_Toolbox.prototype.onOpenPageTab = function(tab) {
};

/**
 * Returns the content of the Toolbox
 * 
 * This function must be inherited
 */
Mashup_Toolbox.prototype.getElInnerContent = function() {
	return null;
};

/**
 * Called when the toolbox should be redraw
 * @returns
 */
Mashup_Toolbox.prototype.show = function() {
	this.getEl().removeClass('toolbox-hidden');
};

/**
 * Called when the toolbox should be redraw
 * @returns
 */
Mashup_Toolbox.prototype.hide = function() {
	this.getEl().addClass('toolbox-hidden');
};

/**
 * Returns whether the toolbox is hidden or not
 * 
 * @returns
 */
Mashup_Toolbox.prototype.isVisible = function() {
	return !this.getEl().hasClass('toolbox-hidden');
};

/**
 * Returns whether the toolbox is collapsed or not
 * 
 * @returns
 */
Mashup_Toolbox.prototype.isOpen = function() {
	return !this.getEl().hasClass('toolbox-closed');
};

/**
 * Called when the toolbox should be redraw
 * @returns
 */
Mashup_Toolbox.prototype.redraw = function() {
	return null;
};

/**
 * Shows/hides the header
 * 
 * @param show
 */
Mashup_Toolbox.prototype.showHeader = function(show) {
	if (show == true) {
		this.getElHeader().show();
	} else {
		this.getElHeader().hide();
	}
};

/**
 * Shows/hides the previous button
 * 
 * @param show
 */
Mashup_Toolbox.prototype.showPrevious = function(show) {
	if (show == true) {
		this.getEl().find('.toolbox-back-btn').show();
		this.getElHeaderTitle().attr('name', 'doPrevious');
	} else {
		this.getEl().find('.toolbox-back-btn').hide();
		if (this.showToggle) {
			this.getElHeaderTitle().attr('name', 'doSlideDown');
		}
	}
};

/**
 * Removes this toolbox from the DOM
 */
Mashup_Toolbox.prototype.remove = function() {
	if (this.el != null) {
		this.el.remove();
		this.el = undefined;
	}
};


/**
 * File: /resources/mashupBuilder/js/Toolbox/Components.js
 */
/*
 * Mashup_Toolbox_Components.js
 *
 * Abstract class for Widgets and Feeds
 */

/*
 * Constructor
 */
Inherit(Mashup_Toolbox_Components, Mashup_Toolbox);
function Mashup_Toolbox_Components(name, options) {
	options = options || {};
	Mashup_Toolbox_Components.superclass.constructor.call(this, name, {
		title: options.title || '',
		collapseByDefault: options.collapseByDefault,
		onOpenDoc: options.onOpenDoc
	});

	this.onClickCallback = options.onClickCallback || null;
	this.attrId = options.attrId || 'id';
	this.attrName = options.attrName || 'name';
	this.type = options.type || 'widget';
	this.typeLiteral = this.type.replace(/[A-Z]/g, function(a) { return ' ' + a.toLowerCase(); });

	this.history = [];
	this.pageIdToNode = {};
	this.pageId = 1; // page ID count
	this.plugins = window.mashupBuilder.services.plugins;
	this.MAX_SEARCH_RESULTS = 10;
	this.previousSearch = '';
	this.componentsSearch = [];
	this.recentlyUsed = [];
	this.MAX_RECENTLY_USED = 5;
}

/**
 * Sets a new list of components
 * 
 * @param components
 */
Mashup_Toolbox_Components.prototype.setComponents = function(components) {
	this.components = components;
};

/**
 * Sets a new list of components
 * 
 * @param components
 */
Mashup_Toolbox_Components.prototype.addRecentlyUsed = function(componentId) {
	var component = this._findComponent(componentId);
	if (component == null) {
		return;
	}

	// remove from list if already set
	var recentlyUsed = this.getRecentlyUsed();
	for (var i = 0; i < recentlyUsed.length; i++) {
		if (recentlyUsed[i] == componentId) {
			recentlyUsed.splice(i, 1);
			break;
		}
	}

	// push the component
	recentlyUsed.unshift(componentId);

	// strip to 10 elements
	if (recentlyUsed.length > this.MAX_RECENTLY_USED) {
		recentlyUsed.splice(this.MAX_RECENTLY_USED, recentlyUsed.length);
	}

	// save the new list of elements
	window.mashupBuilder.state.save('t', this.type, recentlyUsed);

	// create the list of components
	var components = [];
	for (var i = 0; i < recentlyUsed.length; i++) {
		if ((component = this._findComponent(recentlyUsed[i])) != null) {
			components.push(component);
		}
	}

	// save the new components and reload DOM
	this.pageIdToNode[1].components = components;
	if (components.length > 0) {
		this.getElInnerContent().find('.item-page-1').show();
		var $page = this.getElInnerContent().find('.page-1');
		$page.html(this.getElComponentList(components));
		this._attachDraggable($page);
	} else {
		this.getElInnerContent().find('.item-page-1').hide();
	}
};

/**
 * Returns the recently used widgets
 * 
 * @returns
 */
Mashup_Toolbox_Components.prototype.getRecentlyUsed = function() {
	return window.mashupBuilder.state.get('t', this.type, []);
};

/**
 * Returns the HTML for the given component (to implement)
 * 
 * @param component
 * @param installed
 * @returns
 */
Mashup_Toolbox_Components.prototype.getElComponent = function(component, installed) {
	return null;
};

/**
 * Empty search form if esc triggered
 * 
 * @param e
 */
Mashup_Toolbox_Components.prototype.handleEventOnKeyDown = function(e) {
	if (e.keyCode == 27) {
		if ((target = getEventTarget(e)) != null) {
			if (target.val().length > 0) {
				this.clearSearch();
				this.clearTooltips();
				return false;
			}
		}
	}
	return true;
};

Mashup_Toolbox_Components.prototype.handleEventOnKeyUp = function(e) {
	var target = getEventTarget(e);
	if (target == null) {
		return;
	}

	var _this = e.data._this;
	var value = $.trim(target.val().toLowerCase());
	if (value == _this.previousSearch) {
		return;
	}

	var $pageCurrent;
	var $pageSearch = _this.getElInnerContent().find('div.page-search');
	if (_this.history.length > 0) {
		$pageCurrent = _this.getElInnerContent().find('div[name=' + _this.history[_this.history.length - 1][1] + ']');
	} else {
		$pageCurrent = _this.getElRootPage();
	}

	_this.getElInnerContent().find('.toolbox-search .icon').removeAttr('name').removeClass('delete').addClass('loading');

	_this._delayCallback(function() {

		// close any opened tooltip as event "mouseleave" won't be triggered (DOM element deleted)
		_this.getContainer().getEl().parent().find('.toolboxTooltip').remove();

		if (value.length == 0) {
			_this.clearSearch();
		} else {

			var $searchContent = $('' +
				'<div>' +
					'<div class="list-components-wrapper local">' +
					'</div>' +
				'</div>');

			// Local Search

			var components = [],
				componentsLength = _this.components.length;

			var searchTerms = value.split(/\s+/);
			var contains = function(value, searchTerms) {
				for (var i = 0; i < searchTerms.length; i++) {
					if (value.indexOf(searchTerms[i]) == -1) {
						return false;
					}
				}
				return true;
			};

			// first search by display name
			for (var i = 0; i < componentsLength; i++) {
				if (contains(_this.components[i][_this.attrName].toLowerCase(), searchTerms)) {
					components.push(_this.components[i]);
				}
			}

			// next by keywords
			if (components.length < _this.MAX_SEARCH_RESULTS) {
				for (var i = 0; i < componentsLength; i++) {
					if (_this.components[i].keywords != undefined) {
						for (var j = 0; j < _this.components[i].keywords.length; j++) {
							if (contains(_this.components[i].keywords[j].toLowerCase(), searchTerms) && components.indexOf(_this.components[i]) == -1) {
								components.push(_this.components[i]);
								break;
							}
						}
					}
				}
			}

			// then search by id
			if (components.length < _this.MAX_SEARCH_RESULTS) {
				for (var i = 0; i < componentsLength; i++) {
					if (contains(_this.components[i][_this.attrId].toLowerCase(), searchTerms)) {
						if (components.indexOf(_this.components[i]) == -1) {
							components.push(_this.components[i]);
						}
					}
				}
			}

			if (components.length == 0) {
				$searchContent.find('.local').append('<div class="message">' + _.TOOLBOX_COMPONENTS_SEARCH_NO_RESULT(_this.typeLiteral) + '</div>');
			} else {
				$searchContent.find('.local').append(_this.getElComponentList(components.slice(0, _this.MAX_SEARCH_RESULTS)));
				if (components.length > _this.MAX_SEARCH_RESULTS) {
					$searchContent.find('.local').append('<li class="more"><span class="link">' + _.TOOLBOX_COMPONENTS_SEARCH_MORE_RESULTS(components.length - _this.MAX_SEARCH_RESULTS) + '</span></li>');
					$searchContent.find('.local .more').find('.link').on('click', function() {
						$(this).remove();
						$searchContent.find('.local .list-components').replaceWith(_this.getElComponentList(components));
						_this._attachDraggable($searchContent.find('.local .list-components'));
						_this.getElInnerContent().find('> div.components').animate({ queue: false, height: $pageSearch.outerHeight(true) });
					});
				}
				_this._attachDraggable($searchContent.find('.local'));
			}

			// Search over Customs
			// isalive in Mashup_Service_Plugins is set to false because of RIP custom plugins website.
			if (_this.plugins.isAlive() == true && window.mashupBuilder.permission.canUpdateAppSettings() && window.mashupBuilder.isDevelopmentMode() == false) {
				_this.plugins.search(value, {
					onSuccess: function(components) {

						$searchContent.find('.local').prepend('<div class="list-components-title">' +  _.TOOLBOX_COMPONENTS_LOCAL_RESULTS() + '</div>');
						$searchContent.append('<div class="list-components-wrapper customs"><div class="list-components-title">' + _.TOOLBOX_COMPONENTS_CUSTOMS_RESULTS() + '</div></div>');

						if (components.length == 0) {
							$searchContent.find('.customs').append('<div class="message">' + _.TOOLBOX_COMPONENTS_SEARCH_NO_RESULT(_this.typeLiteral) + '</div>');
						} else {

							// populate mandatory component attributes
							var componentsLength = (components.length > _this.MAX_SEARCH_RESULTS) ? _this.MAX_SEARCH_RESULTS : components.length;
							for (var i = 0; i < componentsLength; i++) {
								components[i][_this.attrId] = components[i].uri;
								components[i].preview = components[i].thumbnailUrls.length > 0 ? '<img src="' + components[i].thumbnailUrls[0] + '" alt="' + components[i].title + '"/>' : '';
								components[i][_this.attrName] = components[i].title;
								components[i].description = components[i].shortDescription;
							}

							// save searched components
							_this.componentsSearch = components;

							$searchContent.find('.customs').append(_this.getElCustomsComponentList(components.slice(0, _this.MAX_SEARCH_RESULTS)));
							if (components.length > _this.MAX_SEARCH_RESULTS) {
								$searchContent.find('.customs').append('<li class="more"><span class="link">' + _.TOOLBOX_COMPONENTS_SEARCH_MORE_RESULTS(components.length - _this.MAX_SEARCH_RESULTS) + '</span></li>');
								$searchContent.find('.customs .more').find('.link').on('click', function() {
									$(this).remove();
									$searchContent.find('.customs .list-components').replaceWith(_this.getElComponentList(components));
									_this._attachDraggable($searchContent.find('.customs .list-components'));
									_this.getElInnerContent().find('> div.components').animate({ queue: false, height: $pageSearch.outerHeight(true) });
								});
							}

							_this._attachDraggable($searchContent.find('.customs'));
						}
						_this.getElInnerContent().find('.toolbox-search .icon').removeClass('loading').addClass('delete').attr('name', 'doCancelSearch');
						_this.getElInnerContent().find('> div.components').animate({ queue: false, height: $pageSearch.outerHeight(true) });
					},
					onError: function(textStatus, errorThrown) {
						_this.getElInnerContent().find('.toolbox-search .icon').removeClass('loading').addClass('delete').attr('name', 'doCancelSearch');
					}
				}, {
					refine: _this.type.replace(/[A-Z]/g, function(a) { return '_' + a.toLowerCase(); })
				});
			} else {
				_this.getElInnerContent().find('.toolbox-search .icon').removeClass('loading').addClass('delete').attr('name', 'doCancelSearch');
			}

			// clear any tooltips

			_this.clearTooltips();

			// update toolbox header

			_this.getElHeaderTitle().html(_.TOOLBOX_COMPONENTS_SEARCH_RESULT_TITLE());
			_this.showPrevious(false);

			// switch page display and resize display

			$pageSearch.css('left', $pageCurrent.css('left'));
			$pageSearch.html($searchContent);
			$pageCurrent.hide();
			$pageSearch.show();
			_this.getElInnerContent().find('> div.components').animate({ queue: false, height: $pageSearch.outerHeight(true) });

			_this.previousSearch = value;
		}
	}, 300);
};

/**
 * Fix the position of the scroll when the toolbox has been hidden
 */
Mashup_Toolbox_Components.prototype.fixScrollPosition = function() {
	if (this.history.length > 0) {
		var $pageWrapper = this.getElInnerContent().find('> div.components');
		var $page = $pageWrapper.find('div[name=' + this.history[this.history.length - 1][1] + ']');
		if ($page != null) {
			$pageWrapper.animate({ scrollLeft: parseInt($page.css('left')) }, { duration: 0, queue: false });
		}
	}
};

/**
 * Returns the DOM node for the input search
 */
Mashup_Toolbox_Components.prototype.getElInputSearch = function() {
	return this.getElInnerContent().find('.toolbox-search').find('input');
};

/**
 * Redraws the toolbox
 */
Mashup_Toolbox_Components.prototype.redraw = function() {
	if (this.el != undefined) {
		// save user search
		var previousSearch = this.previousSearch;

		// redraw the toolbox
		var $backup = this.el;
		this.el = undefined;
		this.elComponents = undefined;
		this.elRootPage = undefined;
		this.history = [];
		this.previousSearch = '';
		this.componentsSearch = [];
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();

		// re-play the search if any
		if (this.isVisible() && this.isOpen() && previousSearch.length > 0) {
			this.getElInputSearch().val(previousSearch);
			this.getElInputSearch().trigger('keyup');
		}

		// clear tooltips
		this.clearTooltips();
	}
};

Mashup_Toolbox_Components.prototype.getElInnerContent = function() {
	if (this.elComponents == null) {

		// build inner HTML
		this.elComponents = $('' +
			'<div>' +
				'<div class="toolbox-search">' +
					'<input type="text" name="search" value="" placeholder="' + _.TOOLBOX_COMPONENTS_SEARCH_PLACEHOLDER() + '" autocomplete="off" /> ' +
					'<span class="icon"></span>' +
				'</div>' +
				'<div class="components list-groups">' +
					'<div class="inner">' +
						this.getElNode(this._getComponentsAsTree()) +
						'<div class="page page-search" name="pageSearch" style="position: absolute; top: 0; left: 0; display: none;"></div>' +
					'</div>' +
				'</div>' +
			'</div>');

		// attach events
		this.elComponents.on('mouseover', $.proxy(this.handleEventOnMouseOver, this));
		this.getElInputSearch().on('keydown', $.proxy(this.handleEventOnKeyDown, this));
		this.getElInputSearch().on('keyup', { _this: this }, this.handleEventOnKeyUp);
		this._attachDraggable(this.elComponents);

		// hides the recently used menu if none
		if (this.getRecentlyUsed().length == 0) {
			this.elComponents.find('.item-page-1').hide();
		}
	}
	return this.elComponents;
};

/**
 * Called when the user click on the toolbox
 * 
 * @param context
 * @returns
 */
Mashup_Toolbox_Components.prototype.onClick = function(context) {
	var target = context.target;

	var $pageWrapper = this.getElInnerContent().find('> div.components');
	switch (target.attr('name')) {

	case 'doPrevious':
		// pop previous page
		var previousPage = this.history.pop();

		// if empty then root page
		if (this.history.length == 0) {
			this.showPrevious(false);
			this.getElHeaderTitle().html(this.title);
			$pageWrapper.animate(
				{ height: this.getElRootPage().outerHeight(true), scrollLeft: 0 },
				{
					duration: 300, queue: false,
					complete: function() {
						$pageWrapper.find('div[name=' + previousPage[1] + ']').hide();
					}
				}
			);
		} else {
			// otherwise retrieve the previous page to display
			var $page = $pageWrapper.find('div[name=' + this.history[this.history.length - 1][1] + ']');
			if ($page != null) {

				// update toolbox header
				this.getElHeaderTitle().html(this.history[this.history.length - 1][0]);

				// update height of toolbox for next page to display and scroll to the page
				$pageWrapper.animate(
					{ height: $page.outerHeight(true), scrollLeft: parseInt($page.css('left')) },
					{
						duration: 300, queue: false,
						complete: function() {
							$pageWrapper.find('div[name=' + previousPage[1] + ']').hide();
						}
					}
				);
			}
		}
		return false;

	case 'doSlideNext':
		// handles click on icon
		if (target.hasClass('icon')) {
			target = target.parent();
		}

		// retrieve the page to display
		if ((pageId = target.attr('data-pageName')) != undefined) {
			if (($page = $pageWrapper.find('div[name=' + target.attr('data-pageName') + ']')) != undefined) {
				$page.show();

				// update toolbox header and keep track of previous page
				this.showPrevious(true);
				this.getElHeaderTitle().html(target.text());
				this.history.push([target.text(), $page.attr('name')]);

				// update height of toolbox for next page to display and scroll to the page
				$pageWrapper.animate(
					{ height: $page.outerHeight(true), scrollLeft: parseInt($page.css('left')) },
					{ duration: 300, queue: false }
				);
			}
		}
		return false;

	case 'doCancelSearch':
		target.parent().find('> input').val('').keyup();
		return false;

	case 'doInstallComponent':
		var component = this._findComponent(target.attr('data-componentId'));
		if (component == null) {
			return; // unable to find definition
		}

		// if already installed
		if (target.hasClass('uninstalled') == false) {
			var alertBox = new Mashup_Popup_Alert({
				title: _.TOOLBOX_COMPONENTS_CUSTOMS_INSTALL_TITLE(),
				text: ''
			});
			alertBox.getElContent().prepend('' +
				'<div class="customsInstall">' +
					_.TOOLBOX_COMPONENTS_CUSTOMS_ALREADY_INSTALLED(component.title, component.group) +
					'<br /><br />' +
					_.TOOLBOX_COMPONENTS_CUSTOMS_MORE_INFORMATIONS(this.typeLiteral, component.detailsUrl) +
				'</div>'
			);
			alertBox.show();
			return;
		}

		// ask for installation
		var confirmBox = new Mashup_Popup_Confirm({
			title: _.TOOLBOX_COMPONENTS_CUSTOMS_INSTALL_TITLE(),
			text: '',
			buttonOkLabel: _.TOOLBOX_COMPONENTS_CUSTOMS_LABEL_INSTALL(),
			onClickOkCallbackData: {_this: this },
			onClickOkCallback: function(context) {
				var _this = context.data._this;

				this.remove();

				var waitingBox = new Mashup_Popup_Wait({
					title: _.TOOLBOX_COMPONENTS_CUSTOMS_INSTALL_TITLE(),
					text: _.TOOLBOX_COMPONENTS_CUSTOMS_INSTALL_IN_PROGRESS()
				}).show();

				$.ajax({
					type:'POST',
					url: getCurrentPath() + '/installPlugin',
					data: 'pluginUri=' + component.downloadUrl,
					success: function() {
						waitingBox.remove();
						mashupBuilder.doReloadAvailableComponents({
							showConfirmBox: true,
							onReloadCallback: function() {
								var successBox = new Mashup_Popup_Alert({
									title: _.TOOLBOX_COMPONENTS_CUSTOMS_INSTALL_TITLE(),
									text: _.TOOLBOX_COMPONENTS_CUSTOMS_INSTALL_SUCCESS(_this.typeLiteral, component.title, _this._findComponentGroup(component.uri))
								});
								successBox.show();
							}
						});
					},
					error: function(xhr, error) {
						waitingBox.remove();
						var alertBox = new Mashup_Popup_Alert({
							title: _.TOOLBOX_COMPONENTS_CUSTOMS_INSTALL_TITLE(),
							text: _.TOOLBOX_COMPONENTS_CUSTOMS_INSTALL_FAILED(xhr.responseText),
							level: BoxLevel.ERROR
						});
						alertBox.show();
					}
				});
			}
		});

		confirmBox.getElContent().prepend('' +
			'<div class="customsInstall">' +
				_.TOOLBOX_COMPONENTS_CUSTOMS_INSTALL(this.typeLiteral, component.title) +
				'<br /><br />' +
				_.TOOLBOX_COMPONENTS_CUSTOMS_MORE_INFORMATIONS(this.typeLiteral, component.detailsUrl) +
			'</div>'
		);

		confirmBox.show();
		return false;

	default:
		if (this.onClickCallback != null) {
			return this.onClickCallback.call(this, target);
		}
	}
	return true;
};

/**
 * Called on toggle
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox_Components.prototype.onToggleEnd = function(context) {
	if (!context.visible) {
		var $pageWrapper = this.getElInnerContent().find('> div.components');

		// clear any tooltips
		this.clearTooltips();

		// if has history then hides the current page & clear state
		if (this.history.length > 0) {
			for (var i = 0; i < this.history.length; i++) {
				$pageWrapper.find('div[name=' + this.history[i][1] + ']').hide();
			}
			this.showPrevious(false);
			this.history = [];
		}

		// clear search state if necessary
		var searchInput = this.getElInputSearch();
		if (searchInput.val().length > 0) {
			searchInput.val('');
			$pageWrapper.find('.page-search').empty().hide();
			this.getElInnerContent().find('.toolbox-search .icon').removeAttr('name').removeClass('delete loading');
		}

		// scroll to root page and resize the wrapper
		this.getElRootPage().show();
		$pageWrapper.scrollLeft(0).css('height', '');

		// reset title
		this.getElHeaderTitle().html(this.title);
	}
};

Mashup_Toolbox_Components.prototype.handleEventOnMouseOver = function(e) {
	var target = getEventTarget(e);
	if (target != null && (target.hasClass('list-components-item') || target.hasClass('list-group-item'))) {
		if ((componentId = target.attr('data-componentId')) != undefined) {
			var component = this._findComponent(componentId);
			if (component != null) {
				this.showTooltipForComponent(component, target);
			}
		} else if ((pageId = target.attr('data-pageName')) != undefined) {
			var node = this.pageIdToNode[pageId.substr(5)];
			if (node != undefined) {
				this.showTooltipForCategory(node, target);
			}
		}
		e.stopPropagation();
	}
};

Mashup_Toolbox_Components.prototype.showTooltipForCategory = function(node, target) {
	var li = '';
	for (var i = 0; i < node.nodes.length; i++) {
		li += '<li class="category">' + node.nodes[i].group + '</li>';
	}
	for (var i = 0; i < node.components.length; i++) {
		li += '<li class="component">' + node.components[i][this.attrName] + '</li>';
	}
	this.showTooltip('<ul>' + li + '</ul>', target);
};

Mashup_Toolbox_Components.prototype.showTooltipForComponent = function(component, target) {
	var html = '' +
		'<div class="toolbox-tooltip-title">' +
			component[this.attrName] +
			(component.premium == true ? '<span class="premium-tag">Premium</span>' : '') +
			(component.plugin_version == undefined ? '' :
				('<span class="toolbox-tooltip-title-annotation">' + component.plugin_version + '</span>')
			) +
		'</div>';

	if (component.rating != undefined) {
		html += '' + 
		'<div class="toolbox-tooltip-rating">' +
			'<div class="star-rating-empty"></div>' +
			'<div class="star-rating-full" style="width:' + parseInt(component.rating * 16) + 'px;"></div>' +
		'</div>';
	}

	if (component.description == null && component.preview == null) {
		html += '<p>' + _.TOOLBOX_COMPONENTS_NO_PREVIEW() + '</p>';
	} else {
		if (component.description != null) {
			html += '<p>' + component.description + '</p>';
		}
		if (component.preview != null && component.preview != '') {
			html += '<div class="toolbox-tooltip-preview">' + component.preview + '</div>';
		}
	}

	if (component.version != undefined) {
		html += '<p class="toolbox-tooltip-legend">' + _.TOOLBOX_COMPONENTS_CUSTOMS_LEGEND(component.version) + '</p>';
	}

	this.showTooltip(html, target);
};

Mashup_Toolbox_Components.prototype.showTooltip = function(content, target) {
	// create new tooltip and append it
	var $tooltip = $('' +
		'<div class="toolbox-tooltip" style="display:none;">' +
			'<span class="tooltox-tooltip-arrow"></span>' +
			content +
		'</div>'
	);
	$tooltip.appendTo(this.getContainer().getEl().parent());

	// position the tooltip next to the target and fade in it
	var _this = this;
	$tooltip.data('timeout', setTimeout(function() {
		$tooltip.css({
			left: 270,
			top: _this.getEl().position().top + target.position().top + 40
		});
		$tooltip.fadeIn('fast');
	}, 350));

	// set events to delete this tooltip on mouseleave
	target.one('mouseleave', function() {
		clearTimeout($tooltip.data('timeout'));
		$tooltip.fadeOut('fast', function() {
			$tooltip.remove();
		});
		return false;
	});
};

/**
 * Removes all the opened tooltips
 */
Mashup_Toolbox_Components.prototype.clearTooltips = function() {
	$('.toolbox-tooltip').remove();
};

/**
 * Clears the search state of the toolbox
 */
Mashup_Toolbox_Components.prototype.clearSearch = function() {
	this.getElInputSearch().val('');

	this.getElInnerContent().find('.toolbox-search .icon').removeAttr('name').removeClass('delete loading');

	// retrieve the search & current pages

	var $pageCurrent;
	var $pageSearch = this.getElInnerContent().find('div.page-search');
	if (this.history.length > 0) {
		$pageCurrent = this.getElInnerContent().find('div[name=' + this.history[this.history.length - 1][1] + ']');
	} else {
		$pageCurrent = this.getElRootPage();
	}

	// update toolbox header

	if (this.history.length > 0) {
		this.getElHeaderTitle().html(this.history[this.history.length - 1][0]);
		this.showPrevious(true);
	} else {
		this.getElHeaderTitle().html(this.title);
	}

	// switch page display and resize display

	$pageCurrent.show();
	$pageSearch.hide();
	$pageSearch.empty();
	this.getElInnerContent().find('> div.components').animate({ queue: false, height: $pageCurrent.outerHeight(true) });
	this.previousSearch = '';
	this.componentsSearch = [];
};

/**
 * Returns the HTML for the given node
 * 
 * @returns {string}
 */
Mashup_Toolbox_Components.prototype.getElNode = function(node) {
	var nodeLength = node.nodes.length, componentLength = node.components.length;

	// compute this node
	var html = [];
	html.push('<div class="page' + (node.id == undefined ? '' : ' page-' + node.id) + '"' + (node.id == undefined ? '' : ' name="page-' + node.id + '" style="position:absolute;top:0;left:' + ((node.deep ? node.deep : 0) * 300) + 'px;display:none;"') + '>');
	if (nodeLength > 0) {
		node.nodes.sort(this._sortByGroup);
		html.push('<ul>');
		for (var i = 0; i < nodeLength; ++i) {
			html.push('' +
				'<li name="doSlideNext" class="list-group-item item-page-' + node.nodes[i].id + '" data-pageName="page-' + node.nodes[i].id + '">' +
					node.nodes[i].group +
					'<span name="doSlideNext" class="icon"></span>' +
				'</li>');
		}
		html.push('</ul>');
	}
	if (componentLength > 0) {
		html.push(this.getElComponentList(node.components));
	}
	html.push('</div>');

	// goes recursively
	if (nodeLength > 0) {
		for (var i = 0; i < nodeLength; ++i) {
			html.push(this.getElNode(node.nodes[i]));
		}
	}

	return html.join('');
};

/**
 * Returns the HTML for the given components
 * 
 * @param components
 * @returns {String}
 */
Mashup_Toolbox_Components.prototype.getElComponentList = function(components) {
	var html = [];
	for (var i = 0, componentsLength = components.length; i < componentsLength; ++i) {
		html.push(this.getElComponent(components[i], true));
	}
	return '<ul class="list-components">' + html.join('') + '</ul>';
};

/**
 * Returns the HTML for the given custom components
 * 
 * @param components
 * @returns {String}
 */
Mashup_Toolbox_Components.prototype.getElCustomsComponentList = function(components) {
	var componentsHash = {};
	for (var i = 0; i < this.components.length; i++) {
		componentsHash[this.components[i][this.attrId]] = true;
	}

	var html = [];
	for (var i = 0, componentsLength = components.length; i < componentsLength; ++i) {
		html.push(this.getElComponent(components[i], componentsHash[components[i].uri] != undefined));
	}
	return '<ul class="list-components">' + html.join('') + '</ul>';
};

/**
 * Returns the node for the root page
 * 
 * @returns
 */
Mashup_Toolbox_Components.prototype.getElRootPage = function() {
	if (this.elRootPage == undefined) {
		this.elRootPage = this.getElInnerContent().find('.page:first');
	}
	return this.elRootPage;
};

/**
 * Returns the installed component as a Tree
 * 
 * @private
 * @returns {object}
 */
Mashup_Toolbox_Components.prototype._getComponentsAsTree = function() {
	var getNode = function(group, nodes) {
		for (var i = 0; i < nodes.length; ++i) {
			if (nodes[i].group == group) {
				return nodes[i];
			}
		}
		return null;
	};

	var categories = {nodes: [], components: []};

	// adds special node 'Recently used'
	var recentlyUsed = this.getRecentlyUsed();
	var subNode = {id: this.pageId, group: _.TOOLBOX_COMPONENTS_GROUP_RECENTLY(), nodes: [], components: [], deep: 1, sticky: true /* do not sort */ };
	for (var i = 0; i < recentlyUsed.length; i++) {
		if ((component = this._findComponent(recentlyUsed[i])) != null) {
			subNode.components.push(component);
		}
	}
	categories.nodes.push(subNode);
	this.pageIdToNode[this.pageId++] = subNode;

	// adds the components as nodes
	for (var i = 0; i < this.components.length; i++) {
		var deep = 1;
		var parts = this.components[i].group != undefined && this.components[i].group != '' ? this.components[i].group.split("/") : [];
		var parentArr = categories;
		for (var j = 0; j < parts.length; j++) {
			var subNode = getNode(parts[j], parentArr.nodes);
			if (subNode == null) {
				subNode = {id: this.pageId, group: parts[j], nodes: [], components: [], deep: deep};
				parentArr.nodes.push(subNode);
				this.pageIdToNode[this.pageId++] = subNode;
			}
			parentArr = subNode;
			++deep;
		}
		parentArr.components.push(this.components[i]);
	}
	return categories;
};

/**
 * Called when the drag start
 */
Mashup_Toolbox_Components.prototype.onDragStart = function(e, obj) {
};

/**
 * Called when the drag end
 */
Mashup_Toolbox_Components.prototype.onDragStop = function(e, obj) {
};

/**
 * Attach the draggable events
 * 
 * @private
 */
Mashup_Toolbox_Components.prototype._attachDraggable = function($container) {
	var type = this.type;
	$container.find('.list-components-item').not('.uninstalled').draggable({
		revert: false,
		zIndex: 4200,
		helper: function() {
			return $('<div class="list-components-item ' + type + '-component">' + $(this).html() + '</div>');
		},
		appendTo: '#page-wrapper',
		start: this.onDragStart,
		stop: this.onDragStop
	});
};

/**
 * Returns the definition of the given component ID for either the searched or installed definition
 * 
 * @private
 * @param componentId
 * @returns
 */
Mashup_Toolbox_Components.prototype._findComponent = function(componentId) {
	for (var i = 0, nbComponents = this.componentsSearch.length; i < nbComponents; i++) {
		if (this.componentsSearch[i][this.attrId] == componentId) {
			return this.componentsSearch[i];
		}
	}
	for (var i = 0, nbComponents = this.components.length; i < nbComponents; i++) {
		if (this.components[i][this.attrId] == componentId) {
			return this.components[i];
		}
	}
	return null;
};

/**
 * Returns the Group for the given component ID (search in installed components)
 * 
 * @private
 * @param componentId
 * @returns
 */
Mashup_Toolbox_Components.prototype._findComponentGroup = function(componentId) {
	for (var i = 0, nbComponents = this.components.length; i < nbComponents; i++) {
		if (this.components[i][this.attrId] == componentId) {
			return this.components[i].group;
		}
	}
	return null;
};

/**
 * Delays the execution of the given callback
 * 
 * Cancel the previous callback if not yet triggered
 * 
 * @private
 */
Mashup_Toolbox_Components.prototype._delayCallback = (function() {
	var timer = 0;
	return function(callback, ms){
		clearTimeout (timer);
		timer = setTimeout(callback, ms);
	};
})();

/**
 * Sort callback that compares nodes group
 * 
 * @private
 * @param nodeA
 * @param nodeB
 * @returns {Number}
 */
Mashup_Toolbox_Components.prototype._sortByGroup = function(nodeA, nodeB) {
	if (nodeA.group < nodeB.group || nodeA.sticky) {
		return -1;
	} else if (nodeA.group > nodeB.group || nodeB.sticky) {
		return 1;
	} else {
		return 0;
	}
};

/**
 * Removes this toolbox from the DOM
 */
Mashup_Toolbox_Components.prototype.remove = function() {
	if (this.elComponents != null) {
		this.elComponents.remove();
	}
};


/**
 * File: /resources/mashupBuilder/js/Toolbox/Container.js
 */
/**
 * Mashup Builder Toolbox Container
 * 
 * @constructor
 * @this {Mashup_Toolbox_Container}
 */
function Mashup_Toolbox_Container() {
	this.toolbox = [];
}

/**
 * Called when the window has been resized
 */
Mashup_Toolbox_Container.prototype.onResize = function() {
	this.getEl().css({
		height: window.mashupBuilder.height
	});
};

/**
 * Called when a tab of a page has been opened (ui, api)
 * 
 * @param tab
 */
Mashup_Toolbox_Container.prototype.onOpenPageTab = function(tab) {
	for (var i = 0; i < this.toolbox.length; i++) {
		this.toolbox[i].onOpenPageTab(tab);
	}
};

/**
 * Hudes all the toolboxes
 * 
 * @param toolboxes
 */
Mashup_Toolbox_Container.prototype.hideToolboxes = function() {
	for (var i = 0; i < this.toolbox.length; i++) {
		this.toolbox[i].hide();
	}
};

/**
 * Called when a set of toolboxes need to be redrawn
 * 
 * @param toolboxes
 */
Mashup_Toolbox_Container.prototype.redrawToolboxes = function(toolboxes) {
	for (var i = 0; i < toolboxes.length; i++) {
		var toolbox = this.getToolbox(toolboxes[i]);
		if (toolbox != null) {
			// populate setters
			if (toolbox.setActivePage != undefined) {
				toolbox.setActivePage(window.mashupBuilder.getOpenedPage());
			}
			if (toolbox.setAvailableWidgets != undefined) {
				toolbox.setAvailableWidgets(window.jsonAvailableWidgets);
			}
			if (toolbox.setAvailableFeeds != undefined) {
				toolbox.setAvailableFeeds(window.jsonAvailableFeeds);
			}
			if (toolbox.setAvailableFeedTriggers != undefined) {
				toolbox.setAvailableFeedTriggers(window.jsonAvailableMashupAPITriggers);
			}
			if (toolbox.setAvailableDesignTriggers != undefined) {
				toolbox.setAvailableDesignTriggers(window.jsonAvailableMashupUITriggers);
			}
			// trigger redraw callback
			toolbox.redraw();
		}
	}
};

/**
 * Adds a toolbox to the container at the given position
 * 
 * @param toolbox
 * @param insertAtPosition
 * @returns
 */
Mashup_Toolbox_Container.prototype.addToolbox = function(toolbox, insertAtPosition) {
	toolbox.setContainer(this);
	if (insertAtPosition == undefined || insertAtPosition == this.toolbox.length) {
		this.toolbox.push(toolbox);
		if (this.el != undefined) {
			this.getEl().append(toolbox.getEl());
		}
	} else {
		this.toolbox.splice(insertAtPosition, 0, toolbox);
		if (this.el != undefined) {
			toolbox.getEl().insertBefore(this.toolbox[insertAtPosition + 1].getEl());
		}
	}
	return toolbox;
};

/**
 * Removes a Toolbox from the container
 * 
 * @param toolbox
 */
Mashup_Toolbox_Container.prototype.removeToolbox = function(toolbox) {
	if (typeof(toolbox) == 'string') {
		toolbox = this.getToolbox(toolbox);
	}
	toolbox.remove();
	this.toolbox.splice(this.toolbox.indexOf(toolbox), 1);
};

/**
 * Returns a toolbox by its ID
 * 
 * @param id
 * @returns {Mashup_Toolbox}
 */
Mashup_Toolbox_Container.prototype.getToolbox = function(id) {
	for (var i = 0; i < this.toolbox.length; ++i) {
		if (this.toolbox[i].getId() == id) {
			return this.toolbox[i];
		}
	}
	return null;
};

/**
 * Returns the DOM element for the toolbox container
 * 
 * @returns
 */
Mashup_Toolbox_Container.prototype.getEl = function() {
	if (this.el == null) {
		this.el = $('<div class="toolbox-container"></div>');
		for (var i = 0; i < this.toolbox.length; ++i) {
			this.el.append(this.toolbox[i].getEl());
		}
	}
	return this.el;
};

/**
 * Removes the toolbox container from the DOM
 */
Mashup_Toolbox_Container.prototype.remove = function() {
	for (var i = 0; i < this.toolbox.length; ++i) {
		this.toolbox[i].remove();
	}
	if (this.el != null) {
		this.el.remove();
	}
};


/**
 * File: /resources/mashupBuilder/js/Toolbox/DesignTriggers.js
 */
/*
 * Mashup_Toolbox_DesignTriggers.js
 */

/*
 * Constructor
 */
Inherit(Mashup_Toolbox_DesignTriggers, Mashup_Toolbox_Components);
function Mashup_Toolbox_DesignTriggers(name, availableTriggers) {
	Mashup_Toolbox_DesignTriggers.superclass.constructor.call(this, name, {
		title: _.TOOLBOX_TRIGGERS_TITLE(),
		type: 'designTrigger',
		attrId: 'className', // use className attribute as identifier
		attrName: 'name',
		onOpenDoc: 'TRIGGERS',
		collapseByDefault: true
	});
	this.setAvailableDesignTriggers(availableTriggers);

	// collapse by default
	window.mashupBuilder.getEl().addClass('hide-designTrigger-drop-zones');
}

/**
 * Sets the available design triggers
 *
 * @param availableTriggers
 */
Mashup_Toolbox_DesignTriggers.prototype.setAvailableDesignTriggers = function(availableTriggers) {
	var triggers = [];
	for (var i = 0; i < availableTriggers.components.length; i++) {
		var trigger = availableTriggers.components[i];

		// do not list parameter adapter triggers, they are handled by widget dependencies
		if (trigger.type == TRIGGER_TYPES.ParameterAdapterWidgetTrigger) {
			continue;
		}

		// only list widget triggers triggers in widget composite context
		if (window.widgetBuilder != null && trigger.type != TRIGGER_TYPES.MashupWidgetTrigger) {
			continue;
		}

		// compute a group name from its interface
		trigger.group = trigger.type.substr(trigger.type.lastIndexOf('.') + 1).replace(/([A-Z])/g, ' \$1').substr(1);

		triggers.push(trigger);
	}
	this.setComponents(triggers);
};

/*
 * Implemented methods
 */

Mashup_Toolbox_DesignTriggers.prototype.onOpenPageTab = function(tab) {
	if (tab == 'ui') {
		this.show();
		this.fixScrollPosition();
	} else {
		this.hide();
	}
};

/**
 * Called on toggle start
 *
 * @returns {Boolean}
 */
Mashup_Toolbox_DesignTriggers.prototype.onToggleStart = function(context) {
	if ((openedPage = window.mashupBuilder.getOpenedPage()) != undefined && openedPage.hasUI()) {
		if (context.visible) {
			// close widgets & layout
			this.getContainer().getToolbox('widgets').slideUp();
			this.getContainer().getToolbox('pageLayout').slideUp();
		}
	}
	Mashup_Toolbox_DesignTriggers.superclass.onToggleStart.call(this, context);
};

/**
 * Called on toggle end
 *
 * @returns {Boolean}
 */
Mashup_Toolbox_DesignTriggers.prototype.onToggleEnd = function(context) {
	if (context.visible) {
		window.mashupBuilder.getEl().removeClass('hide-designTrigger-drop-zones');
	} else {
		window.mashupBuilder.getEl().addClass('hide-designTrigger-drop-zones');
	}
	Mashup_Toolbox_DesignTriggers.superclass.onToggleEnd.call(this, context);
};

Mashup_Toolbox_DesignTriggers.prototype.getEl = function() {
	if (this.el == null) {
		this.el = Mashup_Toolbox_Widgets.superclass.getEl.call(this);
		this.getElHeaderIcons().append('<span class="icon icon-reload" name="doReloadTriggers" title="' + _.TOOLBOX_TRIGGERS_RELOAD_CONFIGURATION() + '"></span>');
	}
	return this.el;
};

Mashup_Toolbox_DesignTriggers.prototype.getElComponent = function(component, installed) {
	return '' +
		'<li data-componentId="' + (component.className || component.uri) + '" class="list-components-item ' + this.type + '-component ' + (installed ? 'designTrigger-draggable' : 'uninstalled') + '"' + (installed ? '' : ' name="doInstallComponent"') + '>' +
			(component.name || component.title) +
		'</li>';
};

/**
 * Called when the drag start
 */
Mashup_Toolbox_DesignTriggers.prototype.onDragStart = function(e, obj) {
	return dragDesignTriggerStart.call(this, e, obj);
};

/**
 * Called when the drag end
 */
Mashup_Toolbox_DesignTriggers.prototype.onDragStop = function(e, obj) {
	return dragDesignTriggerStop.call(this, e, obj);
};


/**
 * File: /resources/mashupBuilder/js/Toolbox/Feeds.js
 */
/*
 * Mashup_Toolbox_Feeds.js
 */

/*
 * Constructor
 */
Inherit(Mashup_Toolbox_Feeds, Mashup_Toolbox_Components);
function Mashup_Toolbox_Feeds(name, availableFeeds) {
	Mashup_Toolbox_Feeds.superclass.constructor.call(this, name, {
		title: _.TOOLBOX_FEEDS_TITLE(),
		type: 'feed',
		attrId: 'className', // use className attribute as identifier
		onOpenDoc: 'ADD_FEEDS',
		collapseByDefault: false
	});
	this.setAvailableFeeds(availableFeeds);
}

Mashup_Toolbox_Feeds.prototype.setAvailableFeeds = function(availableFeeds) {
	this.setComponents(availableFeeds.feed);
};

/**
 * Called on toggle start
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox_Feeds.prototype.onToggleStart = function(context) {
	if ((openedPage = window.mashupBuilder.getOpenedPage()) != undefined && openedPage.hasUI()) {
		if (context.visible) {
			// close widgets & layout
			this.getContainer().getToolbox('feedTriggers').slideUp();
		}
	}
	Mashup_Toolbox_Feeds.superclass.onToggleStart.call(this, context);
};

/**
 * Called on toggle end
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox_Feeds.prototype.onToggleEnd = function(context) {
	if (context.visible) {
		window.mashupBuilder.getEl().removeClass('hide-feed-drop-zones');
	} else {
		window.mashupBuilder.getEl().addClass('hide-feed-drop-zones');
	}
	Mashup_Toolbox_Feeds.superclass.onToggleEnd.call(this, context);
};

/*
 * Implemented methods
 */

Mashup_Toolbox_Feeds.prototype.onOpenPageTab = function(tab) {
	if (tab == 'api') {
		this.show();
		this.fixScrollPosition();
	} else {
		this.hide();
	}
};

Mashup_Toolbox_Feeds.prototype.getEl = function() {
	if (this.el == null) {
		this.el = Mashup_Toolbox_Widgets.superclass.getEl.call(this);
		this.getElHeaderIcons().append('<span class="icon icon-reload" name="doReloadFeeds" title="' + _.TOOLBOX_FEED_RELOAD_CONFIGURATION() + '"></span>');
	}
	return this.el;
};

Mashup_Toolbox_Feeds.prototype.getElComponent = function(component, installed) {
	return '' +
			'<li data-componentId="' + (component.className || component.uri) + '" class="list-components-item ' + this.type + '-component ' + (installed ? 'feed-draggable' : 'uninstalled') + '"' + (installed ? '' : ' name="doInstallComponent"') + '>' +
				(component.name || component.title) +
		'</li>';
};

/**
 * Called when the drag start
 */
Mashup_Toolbox_Feeds.prototype.onDragStart = function(e, obj) {
	return dragFeedStart.call(this, e, obj);
};

/**
 * Called when the drag end
 */
Mashup_Toolbox_Feeds.prototype.onDragStop = function(e, obj) {
	return dragFeedStop.call(this, e, obj);
};


/**
 * File: /resources/mashupBuilder/js/Toolbox/FeedTriggers.js
 */
/*
 * Mashup_Toolbox_FeedTriggers.js
 */

/*
 * Constructor
 */
Inherit(Mashup_Toolbox_FeedTriggers, Mashup_Toolbox_Components);
function Mashup_Toolbox_FeedTriggers(name, availableTriggers) {
	Mashup_Toolbox_FeedTriggers.superclass.constructor.call(this, name, {
		title: _.TOOLBOX_TRIGGERS_TITLE(),
		type: 'feedTrigger',
		attrId: 'className', // use className attribute as identifier
		attrName: 'name',
		onOpenDoc: 'TRIGGERS',
		collapseByDefault: true
	});
	this.setAvailableFeedTriggers(availableTriggers);

	// collapse by default
	window.mashupBuilder.getEl().addClass('hide-feedTrigger-drop-zones');
}

/**
 * Sets the available design triggers
 *
 * @param availableTriggers
 */
Mashup_Toolbox_FeedTriggers.prototype.setAvailableFeedTriggers = function(availableTriggers) {
	var triggers = [];
	for (var i = 0; i < availableTriggers.components.length; i++) {
		var trigger = availableTriggers.components[i];

		// compute a group name from its interface
		trigger.group = trigger.type.substr(trigger.type.lastIndexOf('.') + 1).replace(/([A-Z])/g, ' \$1').substr(1);

		triggers.push(trigger);
	}
	this.setComponents(triggers);
};

/*
 * Implemented methods
 */

Mashup_Toolbox_FeedTriggers.prototype.onOpenPageTab = function(tab) {
	if (tab == 'api') {
		this.show();
		this.fixScrollPosition();
	} else {
		this.hide();
	}
};

/**
 * Called on toggle start
 *
 * @returns {Boolean}
 */
Mashup_Toolbox_FeedTriggers.prototype.onToggleStart = function(context) {
	if ((openedPage = window.mashupBuilder.getOpenedPage()) != undefined && openedPage.hasUI()) {
		if (context.visible) {
			// close widgets & layout
			this.getContainer().getToolbox('feeds').slideUp();
		}
	}
	Mashup_Toolbox_FeedTriggers.superclass.onToggleStart.call(this, context);
};

/**
 * Called on toggle end
 *
 * @returns {Boolean}
 */
Mashup_Toolbox_FeedTriggers.prototype.onToggleEnd = function(context) {
	if (context.visible) {
		window.mashupBuilder.getEl().removeClass('hide-feedTrigger-drop-zones');
	} else {
		window.mashupBuilder.getEl().addClass('hide-feedTrigger-drop-zones');
	}
	Mashup_Toolbox_FeedTriggers.superclass.onToggleEnd.call(this, context);
};


Mashup_Toolbox_FeedTriggers.prototype.getEl = function() {
	if (this.el == null) {
		this.el = Mashup_Toolbox_Widgets.superclass.getEl.call(this);
		this.getElHeaderIcons().append('<span class="icon icon-reload" name="doReloadTriggers" title="' + _.TOOLBOX_TRIGGERS_RELOAD_CONFIGURATION() + '"></span>');
	}
	return this.el;
};

Mashup_Toolbox_FeedTriggers.prototype.getElComponent = function(component, installed) {
	return '' +
			'<li data-componentId="' + (component.className || component.uri) + '" class="list-components-item ' + this.type + '-component ' + (installed ? 'feedTrigger-draggable' : 'uninstalled') + '"' + (installed ? '' : ' name="doInstallComponent"') + '>' +
				(component.name || component.title) +
		'</li>';
};

/**
 * Called when the drag start
 */
Mashup_Toolbox_FeedTriggers.prototype.onDragStart = function(e, obj) {
	return dragFeedTriggerStart.call(this, e, obj);
};

/**
 * Called when the drag end
 */
Mashup_Toolbox_FeedTriggers.prototype.onDragStop = function(e, obj) {
	return dragFeedTriggerStop.call(this, e, obj);
};

/**
 * File: /resources/mashupBuilder/js/Toolbox/Messages.js
 */
/**
 * Implementation of a Mashup toolbox for the events
 *
 * @constructor
 * @this {Mashup_Toolbox_Messages}
 */
Inherit(Mashup_Toolbox_Messages, Mashup_Toolbox);
function Mashup_Toolbox_Messages(name) {
	this.events = [];
	this.eventListener = null;
	Mashup_Toolbox_Messages.superclass.constructor.call(this, name, {
		title: _.TOOLBOX_MESSAGES_TITLE(),
		showToggle: false
	});
}

/**
 * Initialize the events system of the toolbox
 */
Mashup_Toolbox_Messages.prototype.init = function() {
	this.eventListener = window.mashup.events.registerGetEventsCallback({
		onSuccess: $.proxy(this.handleOnGetEvents, this)
	});
	this.onChange();
};

/**
 * Clears the events
 */
Mashup_Toolbox_Messages.prototype.clearEvents = function() {
	window.mashup.events.setLastReceivedDate(0);
	this.events = [];
	this.getElEvents().html('');
};

/**
 * Called when the content of the toolbox changed
 */
Mashup_Toolbox_Messages.prototype.onChange = function() {
	this.refreshEvents();
	this.getElEvents().scrollTop(0);
};

/**
 * Called when a page tab has been opened
 * @param tab
 */
Mashup_Toolbox_Messages.prototype.onOpenPageTab = function(tab) {
	this.show();
};

/**
 * Called when we receive new events
 * 
 * @param events
 */
Mashup_Toolbox_Messages.prototype.handleOnGetEvents = function(data) {
	if (data == null) {
		return;
	}

	var isUpdated = false;

	var configChanges = {};

	// FIXME: move this logic elsewhere

	var events = data.events;
	if (events != undefined && events.length > 0) {
		for (var i = 0; i < events.length; i++) {
			switch (events[i].type) {
			// notified of a flag update
			case 'configChange':
				// keep only the last value per key
				if (events[i].parameters != null) {
					configChanges[events[i].parameters.key] = events[i].parameters.value == 'true';
				}
				break;

			// typespeed game!
			case 'typespeed':
				if (window.mashup.typespeed === undefined) {
					window.mashup.typespeed = new Mashup_Service_TypeSpeed();
					window.mashup.typespeed.now -= 4000; // remove a second, otherwise, events will be ignored (cause too old)
					window.mashup.typespeed.parseEvent(events[i]);
				}
				break;

			// store notification to be displayed
			case 'notification':
				if (events[i].parameters != null) {
					this.events.push(events[i]);
					isUpdated = true;
				}
				break;
			}
		}
	}

	// process the configChange events
	for (var key in configChanges) {
		switch (key) {
		case 'DEBUG_MODE':
			window.mashupBuilder.setDebugMode(configChanges[key] == true);
			break;
		}
	}

	// the list of events has been updated
	if (isUpdated) {
		this.onChange();
	}
};

/**
 * Returns the DOM element for the toolbox content
 *
 * @returns
 */
Mashup_Toolbox_Messages.prototype.getElInnerContent = function() {
	if (this.elMessages == null) {
		this.elMessages = $('' +
			'<div>' +
				'<div class="toolbox-messages-wrapper">' +
				'</div>' +
				'<div class="toolbox-events-wrapper">' +
				'</div>' +
			'</div>'
		);
		this.elMessages.find('.toolbox-messages-wrapper').append(window.mashupBuilder.messages.getEl());
	}
	return this.elMessages;
};

/**
 * Returns the DOM node for the messages wrapper
 *
 * @returns
 */
Mashup_Toolbox_Messages.prototype.getElEvents = function() {
	if (this.elEventsWrapper == null) {
		this.elEventsWrapper = this.getElInnerContent().find('.toolbox-events-wrapper');
	}
	return this.elEventsWrapper;
};

/**
 * Refresh the list of events
 */
Mashup_Toolbox_Messages.prototype.refreshEvents = function() {
	var nbEvents = this.events.length;
	if (nbEvents == 0) {
		return;
	}

	var sb = new StringBuilder();
	for (var i = nbEvents - 1; i >= 0; --i) {
		var event = this.events[i];

		var msg = null;
		if ((msgFunc = _['EVENTS_NOTIFICATION_' + event.parameters.key]) != undefined) {
			msg = msgFunc(event);
		} else {
			msg = event.parameters.key;
		}

		sb.append('<li class="event ' + ((i % 2 == 0) ? 'odd' : 'even') + '">');
		sb.append('<div class="event-message">');
		sb.append(msg);
		sb.append('</div>');
		sb.append('<div class="event-infos">' + this._formatDate(new Date(event.time)) + (event.userName.length > 0 ? ' - ' + event.userName : '') + '</div>');
		sb.append('</li>');
	}

	this.getElEvents().html('<ul>' + sb.toString() + '</ul>');
};

/**
 * Format the given date
 *
 * @param date
 * @returns {String}
 */
Mashup_Toolbox_Messages.prototype._formatDate = function(date) {
	return	[date.getUTCFullYear(), this._zeropad(date.getMonth() + 1), this._zeropad(date.getDate())].join('-') +
			' ' +
			[this._zeropad(date.getHours()), this._zeropad(date.getMinutes()), this._zeropad(date.getSeconds())].join(':');
};

/**
 * Adds a '0' in front of number between 0 and 9
 *
 * @param date
 * @returns {String}
 */
Mashup_Toolbox_Messages.prototype._zeropad = function(number) {
	return "" + (number < 10 ? "0" + number : number);
};

/**
 * Removes this toolbox from the DOM
 */
Mashup_Toolbox_Messages.prototype.remove = function() {
	if (this.eventListener != null) {
		window.mashup.events.unRegisterGetEventsCallback(this.eventListener);
	}
};


/**
 * File: /resources/mashupBuilder/js/Toolbox/Navigation.js
 */
/*
 * Constructor
 */
Inherit(Mashup_Toolbox_Navigation, Mashup_Toolbox);
function Mashup_Toolbox_Navigation(id) {
	Mashup_Toolbox_Navigation.superclass.constructor.call(this, id, {
		hiddenByDefault: true
	});
}

/**
 * Called when the user click on the toolbox
 * 
 * @param context
 * @returns
 */
Mashup_Toolbox_Navigation.prototype.onClick = function(context) {
	var target = context.target;
	if (target.attr('name') == 'doOpenTab') {
		var tabName = target.attr('data-tabId');
		this.page.getContext().onInputFocus(undefined);
		this.page.openTab(tabName);
		window.mashupBuilder.setAnchor(this.page.getName(), tabName);
	}
	return false;
};

/**
 * Sets the new active page (redraw the toolbox)
 * 
 * @param page
 */
Mashup_Toolbox_Navigation.prototype.setActivePage = function(page) {
	this.page = page;
	this.redraw();
	if (page == null) {
		this.getEl().hide();
	} else {
		this.getEl().show();
	}
};

/**
 * Sets the given tab as selected
 * 
 * @param page
 */
Mashup_Toolbox_Navigation.prototype.setActiveTab = function(tabName) {
	this.getElInnerContent().find('.selected').removeClass('selected');
	this.getElInnerContent().find('.' + tabName).addClass('selected');
};

/**
 * Returns the DOM element for the menu
 * 
 * @returns
 */
Mashup_Toolbox_Navigation.prototype.getElInnerContent = function() {
	if (this.elInnerContent == undefined) {
		this.elInnerContent = $('<ul class="list-groups"></ul>');
	}
	return this.elInnerContent;
};

/**
 * Redraw the navigation toolbox
 */
Mashup_Toolbox_Navigation.prototype.redraw = function() {
	// compute the new menu
	var menu = {};
	if (this.page != null) {
		var pages = this.page.getTabs();
		for (var i = 0; i < pages.length; i++) {
			var page = pages[i], group = page.getGroup();
			if (menu[group] == undefined) {
				menu[group] = [];
			}
			menu[group].push({ id: page.getId(), label: page.getLabel() });
		}
	}

	// build HTML
	var sb = new StringBuilder();
	for (var group in menu) {
		var items = menu[group];
		sb.append('<li class="list-group-title">' + group + '</li>');
		for (var i = 0; i < items.length; i++) {
			var item = items[i];

			var errorsCount = 0;
			if ((tab = this.page.getTab(item.id)) != null) {
				errorsCount = tab.getErrorsCount();
			}

			sb.append('' +
				'<li name="doOpenTab" data-tabId="' +  item.id + '" class="list-group-item ' + item.id + '">' +
					item.label +
					'<span class="icon icon-error"' + (errorsCount == 0 ? '' : ' style="display:inline-block;"') + '>' + (errorsCount == 0 ? '' : errorsCount) +  '</span>' +
					'<span class="list-group-item-arrow"></span>' +
				'</li>'
			);
		}
	}

	// update HTML
	this.getElInnerContent().html(sb.toString());
};


/**
 * File: /resources/mashupBuilder/js/Toolbox/PageLayout.js
 */
/*
 * Mashup_Toolbox_PageLayout.js
 */


/*
 * Constructor
 */
Inherit(Mashup_Toolbox_PageLayout, Mashup_Toolbox);
function Mashup_Toolbox_PageLayout(name) {
	Mashup_Toolbox_PageLayout.superclass.constructor.call(this, name, {
		title: _.TOOLBOX_PAGELAYOUT_TITLE(),
		collapseByDefault: true,
		onOpenDoc: 'LAYOUT'
	});

	this.initParameterContainer();
}

/**
 * Called on click
 *
 * @returns {Boolean}
 */
Mashup_Toolbox_PageLayout.prototype.onClick = function(context) {
	switch (context.target.attr('name')) {
	case 'pageTemplate':
		var template = window.mashup.pageTemplates[context.target.closest('.page-template').attr('data-idx')];
		if (template != undefined) {
			this.isUpdating = true;

			// retrieve the current page UI
			var pageUI = window.mashupBuilder.getOpenedPage().getUI();

			// update the page UI with the template data
			pageUI.getLayout().adaptTo(template);

			// if has configurable container then update its values
			if (this.parameterContainer != undefined) {
				this.parameterContainer.getParameter('widthFormat').setValue(pageUI.getLayout().getWidthFormat());
				this.parameterContainer.getParameter('width').setValue(pageUI.getLayout().getWidth());
			}

			this.isUpdating = false;
		}
		return false;
	}
	return false;
};

/**
 *  Configures the parameter for the current page
 *
 * @param page
 */
Mashup_Toolbox_PageLayout.prototype.setActivePage = function(page) {
	// no "layout" container (ex: mobile)
	if (this.parameterContainer == undefined) {
		return;
	}

	var config = Mashup_Parameter_Factory.createInvertedConfigParameters(page.getUI().json.parameters);

	// inject width & widthFormat
	config.width = page.getUI().getLayout().getWidth();
	config.widthFormat = page.getUI().getLayout().getWidthFormat();

	// remove current parent (avoid update propagation of setValue)
	this.parameterContainer.setParent(null);

	// update the values
	var parameters = this.parameterContainer.getParameters();
	for (var i = 0; i < parameters.length; i++) {
		parameters[i].setValue(config[parameters[i].getName()] || ['']);
		parameters[i].clearError();
	}

	// sets new parent
	this.parameterContainer.setParent(page.getUI());

	// re-validate
	for (var i = 0; i < parameters.length; i++) {
		parameters[i].validate();
	}
};

/**
 * Initialize the parameter container
 *
 * @param page
 * @returns
 */
Mashup_Toolbox_PageLayout.prototype.initParameterContainer = function() {
	var properties = Mashup_Parameter_Factory.createPageParameters();
	for (var i = 0; i < properties.length; i++) {
		if (properties[i].container.id == 'layout') {
			this.parameterContainer = new Mashup_Parameter_Container(properties[i].container);

			var parameters = properties[i].parameters;
			for (var j = 0; j < parameters.length; j++) {
				if (parameters[j].name == 'width') {
					parameters[j].inputs[0].options.validation.push({
						name: 'callback',
						parameters: [{
							callback: function(value, results) {
								var format = this.getParameterContainer().getParameter('widthFormat').getValue();
								if (value.length == 0 || (format == 'px' && parseInt(value) < 150) || (format == '%' && parseInt(value) < 20)) {
									results.push(_.CANNOT_BE_LESS_THAN((format == 'px' ? 150 : 20)));
								}
							}
						}]
					});
				}
				if (parameters[j].name == 'widthFormat') {
					parameters[j].inputs[0].options.onChange.push({
						handler: function() {
							this.getParameterContainer().getParameter('width').validate();
						}
					});
				}
				this.parameterContainer.addParameter(new Mashup_Parameter(window.defaultJsonCustomComponentParameter['class'], $.extend(parameters[j], {
					width: 130,
					values: [],
					onChangeCallbackData: { _this: this },
					onChangeCallback: this.onUpdate
				})));
			}
		}
	}
};

/**
 * Called when a value has been updated
 *
 * @param page
 * @returns
 */
Mashup_Toolbox_PageLayout.prototype.onUpdate = function(context) {
	var _this = context.data._this;
	if (this.hasErrors() == false && _this.isUpdating != true) {
		_this.isUpdating = true;

		if ((pageUI = _this.parameterContainer.getParent()) != null) {
			var layout = pageUI.getLayout();
			if (this.getName() == 'width') {
				_this.updateWidthFormatTo(pageUI.getLayout(), this.getValue(), layout.getWidthFormat());
			} else if (this.getName() == 'widthFormat') {
				if (layout.getWidthFormat() == 'px') {
					// was px, now %
					_this.updateWidthFormatTo(pageUI.getLayout(), 100, '%');
				} else {
					// was %, now px
					_this.updateWidthFormatTo(pageUI.getLayout(), 1000, 'px');
				}
			}
			// trigger on update of UI
			pageUI.onUpdate();
		}
		_this.isUpdating = false;
	}
};

/**
 * Updates the width and unit of the table
 * 
 * @param layout
 * @param width
 * @param widthFormat
 */
Mashup_Toolbox_PageLayout.prototype.updateWidthFormatTo = function(layout, width, widthFormat) {
	// update width/format of layout
	layout.setWidth(width);
	layout.setWidthFormat(widthFormat);
	layout.onUpdatePageFormat();

	// update width/unit in inputs
	this.parameterContainer.getParameter('widthFormat').setValue(widthFormat);
	this.parameterContainer.getParameter('width').setValue(width);
};

/**
 * Redraw the page parameter toolbox
 */
Mashup_Toolbox_PageLayout.prototype.redraw = function() {
	if (this.el != undefined) {
		var $backup = this.el;
		this.el = undefined;
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();
	}
};

/**
 * Called when a page tab is opened
 *
 * @param tab
 */
Mashup_Toolbox_PageLayout.prototype.onOpenPageTab = function(tab) {
	if (tab == 'ui') {
		this.show();
	} else {
		this.hide();
	}
};

/**
 * Called on toggle end
 *
 * @returns {Boolean}
 */
Mashup_Toolbox_PageLayout.prototype.onToggleEnd = function(context) {
	if (context.visible) {
		window.mashupBuilder.setViewMode('edit');
	} else {
		// change back the view mode unless already done (by changing view in layout mode)
		if (window.mashupBuilder.getViewMode() == 'edit') {
			window.mashupBuilder.setViewMode('custom');
		}
	}
	Mashup_Toolbox_PageLayout.superclass.onToggleEnd.call(this, context);
};

/**
 * Called on toggle start
 *
 * @returns {Boolean}
 */
Mashup_Toolbox_PageLayout.prototype.onToggleStart = function(context) {
	if ((openedPage = window.mashupBuilder.getOpenedPage()) != undefined && openedPage.hasUI()) {
		if (context.visible) {
			this.getContainer().getToolbox('widgets').slideUp();
			this.getContainer().getToolbox('designTriggers').slideUp();
		}
	}
	Mashup_Toolbox_PageLayout.superclass.onToggleStart.call(this, context);
};

/**
 * Returns the DOM element for this toolbox content
 *
 * @returns
 */
Mashup_Toolbox_PageLayout.prototype.getElInnerContent = function() {
	if (this.elInnerContent == null) {
		this.elInnerContent = $('<div>' +
				'<div class="toolbox-pageLayout-templates"></div>' +
			'</div>');

		var $templates = this.elInnerContent.find('.toolbox-pageLayout-templates');
		if (window.mashup.pageTemplates == undefined) {
			var _this = this;
			$templates.html('<span>' + _.TOOLBOX_PAGELAYOUT_LOADING() + '</span>');
			$.getJSON(getCurrentPath() + '/templates/' + window.mashupBuilder.getType().toLowerCase(), function(data) {
				window.mashup.pageTemplates = data;
				$templates.html(_this.getElPageTemplate());
			});
		} else {
			this.getElPageTemplate().appendTo($templates);
		}

		if (this.parameterContainer != undefined) {
			this.parameterContainer.getEl().prependTo(this.elInnerContent);
		}
	}
	return this.elInnerContent;
};

/**
 * Returns the DOM element for the templates
 * 
 * @returns
 */
Mashup_Toolbox_PageLayout.prototype.getElPageTemplate = function() {
	if (this.elPageTemplate == null) {
		var html = '';
		for (var i = 0; i < window.mashup.pageTemplates.length; i++) {
			html += '<div data-idx="' + i + '" class="page-template" name="pageTemplate">';
			var tables = window.mashup.pageTemplates[i].tables;
			for (var j = 0; j < tables.length; j++) {
				var table = tables[j];

				html += '<table name="pageTemplate" class="page-template-table">'; // 50px / 1000px
				html += '<colgroup>';
				for (var k = 0; k < table.colsConfig.length; k++) {
					html += '<col width="'+parseInt(table.colsConfig[k].width/20)+'px" />';
				}
				html += '</colgroup>';

				for (var k = 0; k < table.rows.length; k++) {
					var cells = table.rows[k].cells;
					html += '<tr name="pageTemplate">';
					for (var l = 0; l < cells.length; l++) {
						html += '<td colspan="'+cells[l].colSpan+'" rowspan="'+cells[l].rowSpan+'" name="pageTemplate" class="page-template-cell">&nbsp;</td>';
					}
					html += '</tr>';
				}
				html += '</table>';
			}
			html += '</div>';
		}

		html += '<br style="clear: both;" />';
		this.elPageTemplate = $(html);
	}
	return this.elPageTemplate;
};

/**
 * Validates this toolbox
 */
Mashup_Toolbox_PageLayout.prototype.validate = function() {
	if (this.parameterContainer != undefined) {
		this.parameterContainer.validate();
	}
};

/**
 * Returns the layout parameters as JSON
 *
 * @returns
 */
Mashup_Toolbox_PageLayout.prototype.getJson = function() {
	if (this.parameterContainer != undefined) {
		return this.parameterContainer.getJson();
	}
	return {};
};

/**
 * Removes the toolbox from the DOM
 */
Mashup_Toolbox_PageLayout.prototype.remove = function() {
	if (this.parameterContainer != undefined) {
		this.parameterContainer.remove();
	}
	Mashup_Toolbox_PageLayout.superclass.remove.call(this);
};


/**
 * File: /resources/mashupBuilder/js/Toolbox/PageParameters.js
 */
/*
 * Mashup_Toolbox_PageParameters.js
 */

/*
 * Constructor
 */
Inherit(Mashup_Toolbox_PageParameters, Mashup_Toolbox);
function Mashup_Toolbox_PageParameters(name) {
	Mashup_Toolbox_PageParameters.superclass.constructor.call(this, name, {
		title: _.TOOLBOX_PAGEPARAMETER_TITLE(),
		showToggle: false
	});
}

/**
 * Sets the parameter for the current page
 * 
 * @param page
 * @returns
 */
Mashup_Toolbox_PageParameters.prototype.setActivePage = function(page) {
	// build the JSON
	var json = page.getAPI().json;
	var values = [];
	for (var i = 0; i < json.parameters.length; i++) {
		var p = json.parameters[i];
		var match = p.value.match(/\|"(.*)"/);
		if (match == null && p.value != '${' + json.id + '.' + p.name + '}') {
			values.push(p.name + '##' + p.value);
		} else {
			values.push(p.name + '##' + (match && match.length == 2 ? match[1].replace(/\\"/g, '"') : ''));
		}
	}

	// create parameter
	this.parameterContainer = new Mashup_Parameter_Container({
		parent: page.getAPI(),
		onChangeCallbackData: { _this: this },
		onChangeCallback: function(context) {
			var _this = context.data._this;
			page.getAPI().json.parameters = _this.getJson();
			page.getAPI().onUpdate();
		}
	});

	this.parameterContainer.addParameter(new Mashup_Parameter('', {
		name: 'pageParameter',
		values: values,
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		width: 185,
		textAddIconOnRight: _.TOOLBOX_PAGEPARAMETER_ADD_PARAMETER(),
		showAddIconOnRight: false,
		isValueOrdered: false,
		inputs: [{
			type: 'Input',
			name: 'pageParameter_name',
			label: _.TOOLBOX_PAGEPARAMETER_NAME(),
			options: {
				width: 55,
				validation: [{name: 'isEmpty', parameters: []}, {name: 'isUnique', parameters: []}, {name: 'isAlphanum', parameters: []}]
			}
		}, {
			type: 'Input',
			name: 'pageParameter_defaultValue',
			label: _.TOOLBOX_PAGEPARAMETER_VALUE(),
			options: {
				width: 45
			}
		}]
	}));

	this.activePage = page;
};

/**
 * Redraw the page parameter toolbox
 */
Mashup_Toolbox_PageParameters.prototype.redraw = function() {
	// re-init the container
	this.setActivePage(this.activePage);

	if (this.el != undefined) {
		var $backup = this.el;
		this.el = undefined;
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();
	}
};

Mashup_Toolbox_PageParameters.prototype.onOpenPageTab = function(tab) {
	if (tab == 'api') {
		this.show();
	} else {
		this.hide();
	}
};

/*
 * DOM Manipulation
 */
Mashup_Toolbox_PageParameters.prototype.getElInnerContent = function() {
	if (this.parameterContainer != undefined) {
		return this.parameterContainer.getEl();
	}
	return $();
};

Mashup_Toolbox_PageParameters.prototype.validate = function() {
	if (this.parameterContainer != undefined) {
		this.parameterContainer.validate();
	}
};

/**
 * Returns the page parameters as JSON
 * 
 * @returns {Array}
 */
Mashup_Toolbox_PageParameters.prototype.getJson = function() {
	var json = [];
	if (this.parameterContainer != undefined) {
		var parameter = this.parameterContainer.getParameter('pageParameter');
		if (parameter != undefined) {
			var values = parameter.getValues();
			for (var i = 0; i < values.length; i++) {
				var svalue = values[i].split('##');
				if (svalue[0] != '') {
					var jsonProperty = clone(defaultJsonFeedParameter);
					jsonProperty.name = svalue[0];
					jsonProperty.value = svalue[1];
					json.push(jsonProperty);
				}
			}
		}
	}
	return json;
};

/**
 * Removes the page parameter toolbox from the DOM
 */
Mashup_Toolbox_PageParameters.prototype.remove = function() {
	if (this.parameterContainer != undefined) {
		this.parameterContainer.remove();
	}
	Mashup_Toolbox_PageParameters.superclass.remove.call(this);
};


/**
 * File: /resources/mashupBuilder/js/Toolbox/PreviewParameters.js
 */
/*
 * Mashup_Toolbox_PreviewParameters.js
 */

/*
 * Constructor
 */
Inherit(Mashup_Toolbox_PreviewParameters, Mashup_Toolbox);
function Mashup_Toolbox_PreviewParameters(name) {
	Mashup_Toolbox_PreviewParameters.superclass.constructor.call(this, name, {
		title: _.TOOLBOX_PREVIEWPARAMETER_TITLE(),
		showToggle: false,
		onOpenDoc: 'PREVIEW'
	});
	this.initParameterContainer();
}

/**
 * Configures the parameter for the current page
 *
 * @param page
 * @returns
 */
Mashup_Toolbox_PreviewParameters.prototype.initParameterContainer = function(page) {
	// create parameter
	this.parameterContainer = new Mashup_Parameter_Container({
		parent: page,
		onChangeCallbackData: { _this: this },
		onChangeCallback: function(context) {
			var json = this.getJson();

			// get the user values
			var values = [];
			for (var i = 0; i < json.length; i++) {
				if (json[i].name != 'debugMode' && json[i].name != 'maxResults') {
					values.push(json[i].value);
				}
			}

			// filter against page params
			var pageParameters = window.mashupBuilder.getToolboxContainer().getToolbox('pageParameters').getJson();
			for (var i = 0; i < pageParameters.length; i++) {
				var value = pageParameters[i].name + '##' + pageParameters[i].value;
				if ((idx = values.indexOf(value)) != -1) {
					values.splice(idx, 1);
				}
			}

			// save the filtred values
			window.mashupBuilder.getOpenedPage().saveState('p', values.length == 0 ? null : values);
		}
	});

	this.parameterContainer.addParameter(new Mashup_Parameter('', {
		name: 'previewParameter',
		values: [],
		arity: PARAMETER_ARITY.ZERO_OR_MANY,
		width: 185,
		isValueOrdered: false,
		inputs: [{
			type: 'Input',
			name: 'previewParameter_name',
			label: _.TOOLBOX_PREVIEWPARAMETER_NAME(),
			options: {
				width: 55
			}
		}, {
			type: 'Input',
			name: 'previewParameter_value',
			label: _.TOOLBOX_PREVIEWPARAMETER_VALUE(),
			options: {
				width: 45
			}
		}]
	}));

	// button to refresh the preview
	this.parameterContainer.addParameter(new Mashup_Parameter('', {
		name: 'doRefreshPreview',
		label: _.TOOLBOX_PREVIEWPARAMETER_LABEL_REFRESH(),
		width: 185,
		inputs: [{
			type: 'Button',
			options: {
				label: _.TOOLBOX_PREVIEWPARAMETER_BUTTON_REFRESH(),
				width: 55,
				onClickCallback: function() {
					if (!window.mashupBuilder.getOpenedPage().isSecurityEnable()) {
						window.mashupBuilder.getOpenedPage().getPreview().reloadIframe();
					}
					return false;
				}
			}
		}]
	}));

	// set the max results
	this.parameterContainer.addParameter(new Mashup_Parameter('', {
		name: 'maxResults',
		label: _.TOOLBOX_PREVIEWPARAMETER_LABEL_MAXRESULTS(),
		width: 185,
		onChangeCallback: function() {
			var value = this.getValue();
			window.mashupBuilder.getOpenedPage().saveState('mr', value.length > 0 ? value : null);
			if (!window.mashupBuilder.getOpenedPage().isSecurityEnable()) {
				window.mashupBuilder.getOpenedPage().getPreview().reloadIframe();
			}
			return false;
		},
		inputs: [{
			type: 'Select',
			name: 'maxResults',
			options: {
				possibleValues: ['', '0', '1', '2', '5', '10', '20', '50'],
				width: 30
			}
		}]
	}));

	// adds the debug mode
	this.parameterContainer.addParameter(new Mashup_Parameter('', {
		name: 'debugMode',
		label: _.APP_DEV_DEVMODE_LABEL(),
		description: _.APP_DEV_DEVMODE_DESCRIPTION(),
		width: 185,
		values: [window.mashupBuilder.isDebugMode() + ''],
		onChangeCallback: function() {
			window.mashupBuilder.services.application.setDebugMode(this.getValue() == 'true', function() {
				if (!this.getOpenedPage().isSecurityEnable()) {
					this.getOpenedPage().getPreview().reloadIframe();
				}
			});
			return false;
		},
		inputs: [{
			type: 'Checkbox',
			name: 'debugMode',
			options: {
				possibleValues: ['false', 'true'],
				width: 55
			}
		}]
	}));
};

/**
 * Called when the user click on the toolbox
 *
 * @param context
 * @returns
 */
Mashup_Toolbox_PreviewParameters.prototype.onClick = function(context) {
	var target = context.target;
	switch (target.attr('name')) {
	case 'doRefreshPreview':
		if (!window.mashupBuilder.getOpenedPage().isSecurityEnable()) {
			window.mashupBuilder.getOpenedPage().getPreview().reloadIframe();
		}
		return false;
	}
	return true;
};

/**
 * Redraw the preview parameter toolbox
 */
Mashup_Toolbox_PreviewParameters.prototype.redraw = function() {
	if (this.el != undefined) {
		var $backup = this.el;
		this.el = undefined;
		this.reloadQueryParameters();
		$backup.replaceWith(this.getEl());
		this.getEl().attr('class', $backup.attr('class')); // copy status
		$backup.remove();
	}
};

/**
 * Reloads the values of the query parameters
 */
Mashup_Toolbox_PreviewParameters.prototype.reloadQueryParameters = function() {
	// helper to build the values
	var isSet = function(name, array) {
		for (var i = 0; i < array.length; i++) {
			if (array[i] != undefined && array[i].indexOf(name) == 0) {
				return true;
			}
		}
		return false;
	};

	// build the values
	var filterValues = window.mashupBuilder.getOpenedPage().getState('p', []);
	var pageParameters = window.mashupBuilder.getToolboxContainer().getToolbox('pageParameters').getJson();
	for (var i = 0; i < pageParameters.length; i++) {
		if (isSet(pageParameters[i].name + '##', filterValues) == false) {
			filterValues.push(pageParameters[i].name + '##' + pageParameters[i].value);
		}
	}

	this.parameterContainer.getParameter('previewParameter').setValue(filterValues);

	// Max results
	var maxResults = window.mashupBuilder.getOpenedPage().getState('mr', '');
	this.parameterContainer.getParameter('maxResults').setValue([maxResults]);
};

/**
 * Called when a page tab is opened
 *
 * @param tab
 */
Mashup_Toolbox_PreviewParameters.prototype.onOpenPageTab = function(tab) {
	if (tab == 'preview') {
		this.reloadQueryParameters();
		this.show();
	} else {
		this.hide();
	}
};

/**
 * Returns the DOM inner element for this toolbox
 */
Mashup_Toolbox_PreviewParameters.prototype.getElInnerContent = function() {
	if (this.parameterContainer != undefined) {
		return this.parameterContainer.getEl();
	}
	return $();
};

/**
 * Returns the preview parameters as JSON
 *
 * @returns {Array}
 */
Mashup_Toolbox_PreviewParameters.prototype.getJson = function() {
	return this.parameterContainer.getJson();
};

/**
 * Removes the preview parameter toolbox from the DOM
 */
Mashup_Toolbox_PreviewParameters.prototype.remove = function() {
	if (this.parameterContainer != undefined) {
		this.parameterContainer.remove();
	}
	Mashup_Toolbox_PreviewParameters.superclass.remove.call(this);
};


/**
 * File: /resources/mashupBuilder/js/Toolbox/Widgets.js
 */
/*
 * Mashup_Toolbox_Widgets.js
 */

/*
 * Constructor
 */
Inherit(Mashup_Toolbox_Widgets, Mashup_Toolbox_Components);
function Mashup_Toolbox_Widgets(name, availableWidgets) {
	Mashup_Toolbox_Widgets.superclass.constructor.call(this, name, {
		type: 'widget',
		collapseByDefault: false,
		onOpenDoc: 'ADD_WIDGETS',
		title: _.TOOLBOX_WIDGETS_TITLE(),
		onClickCallback: function(target) {
			if (target.attr('name') == 'editWidgetComposite') {
				loadWidgetComposite(target.parent().attr('data-componentId'));
				return false;
			}
		}
	});
	this.setAvailableWidgets(availableWidgets);
}

Mashup_Toolbox_Widgets.prototype.setAvailableWidgets = function(availableWidgets) {
	this.isCompositeViewable = window.mashupBuilder.permission.canViewWidgetBuilder();
	var widgets = null;
	if (window.widgetBuilder == null) {
		widgets = availableWidgets.widget;
	} else {
		widgets = [];
		for (var i = 0, widgetList = availableWidgets.widget; i < widgetList.length; i++) {
			if (!widgetList[i].compositeWidget) {
				widgets.push(widgetList[i]);
			}
		}
	}
	this.setComponents(widgets);
};

/**
 * Called at the beginning of the slide
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox_Widgets.prototype.onToggleStart = function(context) {
	if (context.visible) {
		this.getContainer().getToolbox('pageLayout').slideUp();
		this.getContainer().getToolbox('designTriggers').slideUp();
	}
	Mashup_Toolbox_Widgets.superclass.onToggleStart.call(this, context);
};

/**
 * Called on toggle end
 * 
 * @returns {Boolean}
 */
Mashup_Toolbox_Widgets.prototype.onToggleEnd = function(context) {
	if (context.visible) {
		window.mashupBuilder.getEl().removeClass('hide-widget-drop-zones');
	} else {
		window.mashupBuilder.getEl().addClass('hide-widget-drop-zones');
	}
	Mashup_Toolbox_Widgets.superclass.onToggleEnd.call(this, context);
};

/*
 * Implemented methods
 */

Mashup_Toolbox_Widgets.prototype.onOpenPageTab = function(tab) {
	if (tab == 'ui') {
		this.show();
		this.fixScrollPosition();
	} else {
		this.hide();
	}
};

Mashup_Toolbox_Widgets.prototype.getElComponent = function(component, installed) {
	return '' +
		'<li data-componentId="' + (component.id || component.uri) + '" class="list-components-item ' + ((component.compositeWidget && this.isCompositeViewable) ? 'editable-component ' : '') + this.type + '-component ' + (installed ? 'widget-draggable' : 'uninstalled') + '"' + (installed ? '' : ' name="doInstallComponent"') + '>' +
			(component.name || component.title) +
			((component.compositeWidget && this.isCompositeViewable)
				? '<span class="icon icon-editable" name="editWidgetComposite" title="' +  _.WIDGET_MENU_EDIT_COMPOSITE() + '"></span>' : ''
			) +
		'</li>';
};

Mashup_Toolbox_Widgets.prototype.getEl = function() {
	if (this.el == null) {
		this.el = Mashup_Toolbox_Widgets.superclass.getEl.call(this);
		var $headerIcons = this.getElHeaderIcons();
		$headerIcons.append('<span class="icon icon-reload" name="doReloadWidgets" title="' + _.TOOLBOX_WIDGET_RELOAD_CONFIGURATION() + '"></span>');
		if (mashupBuilder.permission.canViewWidgetBuilder() == true) {
			$headerIcons.append('<span class="icon icon-plus icon-new-widget" name="doNewCompositeWidget" title="' + _.WB_MENU_NEW() + '"></span>');
		}
	}
	return this.el;
};

/**
 * Called when the drag start
 */
Mashup_Toolbox_Widgets.prototype.onDragStart = function(e, obj) {
	return dragWidgetStart.call(this, e, obj);
};

/**
 * Called when the drag end
 */
Mashup_Toolbox_Widgets.prototype.onDragStop = function(e, obj) {
	return dragWidgetStop.call(this, e, obj);
};


/**
 * File: /resources/mashupBuilder/js/Toolbox/WidgetDefinition.js
 */
/**
 * Implementation of a Mashup toolbox for the widget composite
 * 
 * @constructor
 * @this {Mashup_Toolbox_Messages}
 */
Inherit(Mashup_Toolbox_WidgetDefinition, Mashup_Toolbox);
function Mashup_Toolbox_WidgetDefinition(name, widgetBuilder) {
	Mashup_Toolbox_WidgetDefinition.superclass.constructor.call(this, name, {
		title: _.WB_TOOLBOX_TITLE(),
		onOpenDoc: 'COMPOSITE_WIDGETS'
	});
	this.widgetBuilder = widgetBuilder;
}

/**
 * Returns whether the toolbox is readonly or not
 * 
 * @returns
 */
Mashup_Toolbox_WidgetDefinition.prototype.isReadOnly = function() {
	return window.widgetBuilder.getPage().getUI().isReadOnly();
};

/**
 * Called when the user click on the toolbox
 * 
 * @param context
 * @returns
 */
Mashup_Toolbox_WidgetDefinition.prototype.onClick = function(context) {
	var target = context.target;

	switch (target.attr('name')) {
	case 'doEditWidgetOptions':
		window.widgetBuilder.openWidgetDefinitionOptionsToolbar();
		return false;
	default:
		// handle focus when clicking on label
		if (target.is('label')) {
			var parameterClass = target.closest('.parameter').data('_this');
			if (parameterClass != null) {
				parameterClass.focus();
			}
		}
	}
	return true;
};

Mashup_Toolbox_WidgetDefinition.prototype.getEl = function() {
	if (this.el == null) {
		this.el = Mashup_Toolbox_WidgetDefinition.superclass.getEl.call(this);
		var $headerIcons = this.getElHeaderIcons();
		$headerIcons.append('<span class="icon icon-close" name="doCloseWidgetBuilder" title="' + _.WB_BUTTON_CLOSE() + '"></span>');
	}
	return this.el;
};

/**
 * Returns the DOM inner element for this toolbox
 * 
 * @returns
 */
Mashup_Toolbox_WidgetDefinition.prototype.getElInnerContent = function() {
	if (this.elSettings == null) {
		var divEl = document.createElement('div');
		this.elSettings = $(divEl);
		this.getParameterContainer().getEl().appendTo(this.elSettings);
	}
	return this.elSettings;
};

Mashup_Toolbox_WidgetDefinition.prototype.updateError = function(errorsCount) {
	return mashupBuilder.getOpenedPage().getUI().updateError(errorsCount);
};

/**
 * Called when an input has been updated within the toolbox
 */
Mashup_Toolbox_WidgetDefinition.prototype.onUpdate = function() {
	this.widgetBuilder.somethingChanged();
};

/**
 * Returns the Parameter Container (lazy load)
 * 
 * @returns {Mashup_Parameter_Container}
 */
Mashup_Toolbox_WidgetDefinition.prototype.getParameterContainer = function() {
	if (this.parameterContainer == undefined) {
		this.parameterContainer = new Mashup_Parameter_Container({
			id:'parameterContainer',
			parent: this,
			onChangeCallbackData: {_this: this},
			onChangeCallback: function(context) {
				context.data._this.updateAPI();
				context.data._this.onUpdate();
			}
		});

		this.parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			name: 'name',
			label: _.WB_TOOLBOX_NAME(),
			values: [this.widgetBuilder.jsonWidgetDefinition.name],
			arity: PARAMETER_ARITY.ONE,
			width: 155
		})));

		this.parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			name: 'group',
			label: _.WB_TOOLBOX_GROUP(),
			values: [this.widgetBuilder.jsonWidgetDefinition.group],
			arity: PARAMETER_ARITY.ZERO_OR_ONE,
			width: 155
		})));

		this.parameterContainer.addParameter(new Mashup_Parameter('', {
			name: 'id',
			label: _.WB_TOOLBOX_ID(),
			arity: PARAMETER_ARITY.ZERO_OR_ONE,
			width: 155,
			values: [this.widgetBuilder.jsonWidgetDefinition.id],
			inputs: [{
				type: 'Literal',
				name: 'id'
			}]
		}));

		this.parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			type: 'Textarea',
			name: 'description',
			label: _.WB_TOOLBOX_DESCRIPTION(),
			values: [this.widgetBuilder.jsonWidgetDefinition.description],
			arity: PARAMETER_ARITY.ZERO_OR_ONE,
			width: 155
		})));

		this.parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			name: 'supportFeeds',
			label: _.WB_TOOLBOX_FEEDS(),
			values: [this.widgetBuilder.jsonWidgetDefinition.supportFeedsId.arity],
			arity: PARAMETER_ARITY.ONE,
			possibleValues: ['ZERO', 'ZERO_OR_MANY', 'ONE', 'MANY'],
			width: 155,
			onDisplay: [{ data: { valueToMatch: 'ZERO', hideOptions: ['forceFeedConfiguration'], showOptions: [], ifEquals: true, emptyHidden: true }, handler: Mashup_Parameter_Display_ToggleDisplay.prototype.handleEventOnChange }],
			onChange: [{ data: { valueToMatch: 'ZERO', hideOptions: ['forceFeedConfiguration'], showOptions: [], ifEquals: true, emptyHidden: true }, handler: Mashup_Parameter_Display_ToggleDisplay.prototype.handleEventOnChange }]
		})));

		var pageNames = [''];
		var pages = mashupBuilder.getPages();
		for (var i = 0; i < pages.length; i++) {
			pageNames.push(pages[i].getName());
		}

		this.parameterContainer.addParameter(new Mashup_Parameter('', {
			name: 'forceFeedConfiguration',
			label: _.WB_TOOLBOX_FORCEFEEDS(),
			arity: PARAMETER_ARITY.ZERO_OR_ONE,
			width: 155,
			textAddIconOnRight:  _.WB_TOOLBOX_ADD_PARAM(),
			showAddIconOnRight: false,
			inputs: [{
				type: 'Select',
				name: 'forceFeedConfiguration_page',
				options: {
					width: 50,
					possibleValues: pageNames,
					onChange: [{
						data: { _this: this },
						handler: function(context) {
							// retrieve new page name
							var pageName = this.val();

							// compute array of feed IDs
							var feedIds = [''];
							if (pageName.length > 0) {
								var page = window.mashupBuilder.getPageClass(pageName);
								if (page != null) {
									var feeds = page.getAPI().getFeeds();
									for (var i = 0; i < feeds.length; i++) {
										feedIds.push(feeds[i].getId());
									}
								}
							}

							// update the other select
							var select = this.getParameterValue().getInput(1);
							select.val('');
							select.setOptions(feedIds);
						}
					}]
				}
			}, {
				type: 'Select',
				name: 'forceFeedConfiguration_feed',
				options: {
					width: 50,
					possibleValues: ['']
				}
			}],
			glue: '##'
		}));

		this.parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			name: 'supportSubWidgets',
			label: _.WB_TOOLBOX_WIDGETS(),
			values: [this.widgetBuilder.jsonWidgetDefinition.supportWidgetsId.arity],
			arity: PARAMETER_ARITY.ONE,
			possibleValues: ['ZERO', 'ZERO_OR_MANY', 'MANY'],
			width: 155
		})));

		// Supported Platforms

		var platforms = this.widgetBuilder.jsonWidgetDefinition.platforms;
		var supported = {};
		for (var i = 0; i < platforms.length; i++) {
			supported[platforms[i].type] = platforms[i].supported + '';
		}

		var supportedValue = [];
		var supportedInputs = [];
		for (var i = 0; i < defaultJsonPlatformTypes.length; i++) {
			supportedValue.push(supported[defaultJsonPlatformTypes[i]] != undefined ? supported[defaultJsonPlatformTypes[i]] : 'false');
			supportedInputs.push({
				type: 'Checkbox',
				name: 'supportedPlatform_' + defaultJsonPlatformTypes[i],
				label: _.WB_TOOLBOX_PLATFORMS(defaultJsonPlatformTypes[i]),
				options: {
					width: parseInt(100 / defaultJsonPlatformTypes.length),
					possibleValues: ['false', 'true']
				}
			});
		}

		this.parameterContainer.addParameter(new Mashup_Parameter('', {
			name: 'supportedPlatform',
			label: _.WB_TOOLBOX_SUPPORTED_PLATFORMS(),
			arity: PARAMETER_ARITY.ZERO_OR_ONE,
			width: 155,
			values: [supportedValue.join('##')],
			inputs: supportedInputs,
			glue: '##'
		}));

		this.parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			name: 'enclosingTag',
			type: 'Radio',
			label: _.WB_TOOLBOX_TAG(),
			values: [getJsonParameterValue(this.widgetBuilder.pageClass.getUI().json.parameters, 'enclosingTag', 'div') || 'div'],
			arity: PARAMETER_ARITY.ONE,
			possibleValues: ['div', 'form'],
			width: 155,
			onDisplay: [{ data: { valueToMatch: 'div', hideOptions: ['enclosingTagAction', 'enclosingTagMethod'], showOptions: [], emptyHidden: false, ifEquals: true}, handler: Mashup_Parameter_Display_ToggleDisplay.prototype.handleEventOnChange }],
			onChange: [{ data: { valueToMatch: 'div', hideOptions: ['enclosingTagAction', 'enclosingTagMethod'], showOptions: [], emptyHidden: false, ifEquals: true}, handler: Mashup_Parameter_Display_ToggleDisplay.prototype.handleEventOnChange }],
			onChangeCallbackData: {_this: this},
			onChangeCallback: function(context) {
				getJsonParameter(context.data._this.widgetBuilder.pageClass.getUI().json.parameters, 'enclosingTag', true).value = this.getValue();
				context.data._this.onUpdate();
			}
		})));

		this.parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			name: 'enclosingTagAction',
			label: _.WB_TOOLBOX_ACTION(),
			description: _.WB_TOOLBOX_ACTION_DESCRIPTION(),
			values: [getJsonParameterValue(this.widgetBuilder.pageClass.getUI().json.parameters, 'enclosingTagAction', '')],
			arity: PARAMETER_ARITY.ZERO_OR_ONE,
			type: 'Select',
			possibleValues: this.getAvailableActions(),
			width: 140,
			onChangeCallbackData: {_this: this},
			onChangeCallback: function(context) {
				getJsonParameter(context.data._this.widgetBuilder.pageClass.getUI().json.parameters, 'enclosingTagAction', true).value = this.getValue();
				context.data._this.onUpdate();
			}
		})));

		this.parameterContainer.addParameter(new Mashup_Parameter('', Mashup_Parameter_Factory.createParameter({
			name: 'enclosingTagMethod',
			label: _.WB_TOOLBOX_METHOD(),
			values: [getJsonParameterValue(this.widgetBuilder.pageClass.getUI().json.parameters, 'enclosingTagMethod', 'GET') || 'GET'],
			arity: PARAMETER_ARITY.ONE,
			type: 'Radio',
			possibleValues: ['GET', 'POST'],
			width: 140,
			onChangeCallbackData: {_this: this},
			onChangeCallback: function(context) {
				getJsonParameter(context.data._this.widgetBuilder.pageClass.getUI().json.parameters, 'enclosingTagMethod', true).value = this.getValue();
				context.data._this.onUpdate();
			}
		})));

		this.parameterContainer.addButton({
			name: 'doEditWidgetOptions',
			label: _.WB_TOOLBOX_ADD_PROPERTY(),
			error: true
		});
	}
	return this.parameterContainer;
};

/**
 * Updates the available actions of the "action" parameter
 */
Mashup_Toolbox_WidgetDefinition.prototype.updateAvailableActions = function() {
	var parameter = this.getParameterContainer().getParameter('enclosingTagAction');
	if (parameter != null) {
		var input = parameter.getValuesClass()[0].getInput(0);
		if (typeof(input.setOptions) == 'function') {
			input.setOptions(this.getAvailableActions());
		}
	}
};

/**
 * Returns the available actions of the "action" parameter
 */
Mashup_Toolbox_WidgetDefinition.prototype.getAvailableActions = function() {
	var actions = [''];
	var optionsGroup = this.widgetBuilder.jsonWidgetDefinition.optionsGroup;
	for (var i = 0; i < optionsGroup.length; i++) {
		var options = optionsGroup[i].options;
		for (var j = 0; j < options.length; j++) {
			actions.push('%{option.' + options[j].id + '}');
		}
	}
	return actions;
};

/**
 * Updates the API side for this widget composite
 */
Mashup_Toolbox_WidgetDefinition.prototype.updateAPI = function() {
	var feedContainer = window.widgetBuilder.getPage().getAPI().getFeedContainer();

	// if this widget supports feeds then adds it
	if (this.getParameterValue('supportFeeds') != 'ZERO') {
		var jsonFeed = null;

		// use the selected feed
		var forceFeed = this.getParameterValue('forceFeedConfiguration');
		if (forceFeed.length > 0) {
			var pageName = forceFeed.split('##')[0], feedName = forceFeed.split('##')[1];
			if ((pageClass = window.mashupBuilder.getPageClass(pageName)) != null) {
				if ((feedClass = pageClass.getAPI().getFeed(feedName)) != null) {
					jsonFeed = clone(feedClass.json);
					jsonFeed.originalId = jsonFeed.id;
					jsonFeed.id = 'this';
				}
			}
		}

		// fallback to dummy feed if none selected
		if (jsonFeed == null) {
			jsonFeed = this.widgetBuilder.getDummyFeed();
		}

		if (!feedContainer.hasFeeds()) {
			feedContainer.addNewFeed(jsonFeed, 0);
		} else {
			// ugly but hey I do not want to remove my feed because it will
			// uncheck the use feeds of all my widgets
			feedContainer.getFeeds(false)[0].json = jsonFeed;
		}

	// otherwise remove the feed if exists
	} else if (feedContainer.hasFeeds()) {
		feedContainer.removeFeed(feedContainer.getFeeds(false)[0]);
	}
};

/**
 * Returns the value of the given parameter name
 * 
 * @param name
 * @returns
 */
Mashup_Toolbox_WidgetDefinition.prototype.getParameterValue = function(name) {
	if ((parameter = this.getParameterContainer().getParameter(name)) != null) {
		return parameter.getValue();
	}
	return '';
};

/**
 * Returns the Widget Definition as JSON
 * 
 * @returns
 */
Mashup_Toolbox_WidgetDefinition.prototype.getJson = function() {
	this.widgetBuilder.jsonWidgetDefinition.group = this.getParameterValue('group');
	this.widgetBuilder.jsonWidgetDefinition.name = this.getParameterValue('name');
	this.widgetBuilder.jsonWidgetDefinition.description = this.getParameterValue('description');
	this.widgetBuilder.jsonWidgetDefinition.supportWidgetsId.arity = this.getParameterValue('supportSubWidgets');
	this.widgetBuilder.jsonWidgetDefinition.supportFeedsId.arity = this.getParameterValue('supportFeeds');
	this.widgetBuilder.jsonWidgetDefinition.supportFeedsId.consumeFeed = false;
	this.widgetBuilder.jsonWidgetDefinition.platforms = [];
	var values = this.getParameterValue('supportedPlatform').split('##');
	for (var i = 0; i < window.defaultJsonPlatformTypes.length; i++) {
		this.widgetBuilder.jsonWidgetDefinition.platforms.push({
			'class': window.defaultJsonEmptyPlatform['class'],
			type: window.defaultJsonPlatformTypes[i],
			supported: (values[i] == 'true' ? true : false)
		});
	}
	return this.widgetBuilder.jsonWidgetDefinition;
};

/**
 * Removes the Toolbox from the DOM
 */
Mashup_Toolbox_WidgetDefinition.prototype.remove = function() {
	if (this.elSettings != undefined) {
		this.elSettings.remove();
		this.elSettings = undefined;
	}

	Mashup_Toolbox_WidgetDefinition.superclass.remove.call(this);
};