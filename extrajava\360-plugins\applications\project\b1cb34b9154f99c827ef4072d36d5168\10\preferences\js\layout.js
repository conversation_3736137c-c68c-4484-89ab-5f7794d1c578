var PreferenceLayout = function (uCssId) {
	this.uCssId = uCssId;
	this.widget = $('.' + this.uCssId);
	this.container = $('.' + this.uCssId + ' .preference-layout-container');
	this.client = new StorageClient('user');
	this.chartboardStorageManager = new ChartboardStorageManager();
};

PreferenceLayout.LAYOUTS_STORAGE_KEY = 'chartboard_layouts';
PreferenceLayout.PAGES_STORAGE_KEY = 'chartboard_pages';

PreferenceLayout.prototype.getLayouts = function (successCallback, errorCallback) {
	this.client.get(PreferenceLayout.LAYOUTS_STORAGE_KEY,
		$.proxy(function (items) {
			var json;
			if (items.length > 0 && items[0].value) {
				json = JSON.parse(items[0].value);
			} else {
				json = {};
			}

			var layouts = {};

			for (var layoutId in json) {
				var layout = json[layoutId];
				if (layouts[layout.templatePage]) {
					layouts[layout.templatePage].push(layout);
				} else {
					layouts[layout.templatePage] = [];
					layouts[layout.templatePage].push(layout);
				}
			}

			this.client.get(PreferenceLayout.PAGES_STORAGE_KEY,
				$.proxy(function (pagesItems) {
					var pagesJson;

					if (pagesItems.length > 0 && pagesItems[0].value) {
						pagesJson = JSON.parse(pagesItems[0].value);
					} else {
						pagesJson = {};
					}

					var pages = {};
					for (var pageId in pagesJson) {
						var page = pagesJson[pageId];
						if (pages[page.layout]) {
							pages[page.layout].push(page.label);
						} else {
							pages[page.layout] = [];
							pages[page.layout].push(page.label);
						}
					}

					successCallback.call(null, layouts, pages);
				}, this),
				$.proxy(function () {
					/* error callback */
					errorCallback.call();
				}, this)
			);
		}, this),
		$.proxy(function () {
			/* error callback */
			errorCallback.call();
		}, this)
	);
};

PreferenceLayout.prototype.saveLayouts = function (successCallback, errorCallback) {
	this.client.get(PreferenceLayout.LAYOUTS_STORAGE_KEY,
		$.proxy(function (items) {
			var json;
			if (items.length > 0 && items[0].value) {
				json = JSON.parse(items[0].value);
			} else {
				json = {};
			}

			var layouts = {};

			for (var pageId in this.layouts) {
				var pageLayouts = this.layouts[pageId];
				for (var i = 0; i < pageLayouts.length; i++) {
					var layout = pageLayouts[i];
					if (json[layout.id]) {
						layouts[layout.id] = layout;
					}
				}
			}

			this.client.set(PreferenceLayout.LAYOUTS_STORAGE_KEY, JSON.stringify(layouts),
				function () {
					/* success callback */
					successCallback.call();
				}, function () {
					/* error callback */
					errorCallback.call();
				}
			);

		}, this),
		$.proxy(function () {
			/* error callback */
			errorCallback.call();
		}, this)
	);
};

PreferenceLayout.prototype.init = function () {
	this.getLayouts($.proxy(function (layouts, pages) {
		this.layouts = layouts;
		this.pages = pages;
		this.deletedElems = [];
		this.initTitle();
		this.initTree();
		this.initButtons();
	}, this));
};

PreferenceLayout.prototype.initTitle = function () {
	var titleBlock = $('<div class="preference-title"></div>');
	titleBlock.append($('<div class="main">' + Preferences.getMessage('plma.preference.layout.title') + '</div>'));
	titleBlock.append($('<div class="description">' + Preferences.getMessage('plma.preference.layout.description') + '</div>'));

	this.container.append(titleBlock);
};

PreferenceLayout.prototype.initTree = function () {
	var blockContainer = $('<div class="preference-block-container"></div>');
	var layoutDisplayContainer = $('<div class="preference-block"></div>');
	var layoutsContainer = $('<div class="preference-elem-block preference-layouts-block"></div>');
	for (templatePage in this.layouts) {
		var layouts = this.layouts[templatePage];
		var layoutBlock = $('<div class="elem-block layout-block collapsed"></div>');
		var templateDescription = $('<div class="elem-description template-description"></div>');
		templateDescription.append($('<span class="elem-icon layout-icon fonticon fonticon-layout"></span>'));
		templateDescription.append($('<span class="elem-label layout-label">' + templatePage + '</span>'));
		templateDescription.append($('<span class="elem-dropdown-icon layout-dropdown-icon fonticon fonticon-down-open"></span>'));
		templateDescription.on('click', $.proxy(function (e) {
			$e = $(e.target);
			this.container.find('.layout-block').addClass('collapsed');
			$e.closest('.layout-block').removeClass('collapsed');
			this.container.find('.selected').removeClass('selected');
			$e.closest('.layout-block').addClass('selected');
		}, this));
		layoutBlock.append(templateDescription);

		var layoutCategories = $('<div class="elem-categories layout-categories"></div>');
		for (var i = 0; i < layouts.length; i++) {
			var layout = layouts[i];
			var layoutDescription = $('<div class="layout-description"></div>');
			var layoutInfo = $('<div class="info-layout"></div>');
			layoutInfo.append($('<span class="label">' + layout.label + '</span>'));
			layoutInfo.append($('<span class="id" style="display: none">' + layout.id + '</span>'));
			layoutInfo.append($('<span class="description">' + layout.description + '</span>'));
			layoutDescription.append(layoutInfo);
			var layoutPages = $('<div class="layout-pages"></div>');
			if (this.pages[layout.id]) {
				for (var j = 0; j < this.pages[layout.id].length; j++) {
					var pageLabel = this.pages[layout.id][j];
					var layoutPage = $('<span class="layout-page fonticon fonticon-doc">' + pageLabel + '</span>');
					layoutPages.append(layoutPage);
				}
			}
			layoutDescription.append(layoutPages);
			var trashLayout = $('<span class="layout-icon layout-trash-icon fonticon fonticon-trash"></span>');
			trashLayout.on('click', $.proxy(function (e) {
				$e = $(e.target).closest('.layout-description');
				this.deleteLayout($e);
			}, this));
			layoutDescription.append(trashLayout);
			layoutDescription.on('click', $.proxy(function (e) {
				$e = $(e.target);
				this.container.find('.selected').removeClass('selected');
				$e.closest('.layout-block').addClass('selected');
				$e.closest('.layout-description').addClass('selected');
				this.hideDetail();
				this.displayDetail();
			}, this));
			layoutCategories.append(layoutDescription);
		}
		layoutBlock.append(layoutCategories);
		layoutsContainer.append(layoutBlock);
	}
	layoutDisplayContainer.append(layoutsContainer);

	blockContainer.append(layoutDisplayContainer);
	this.container.append(blockContainer);
};

PreferenceLayout.prototype.initButtons = function () {
	/* Add Save / cancel buttons */
	var buttonsContainer = $('<div class="button-container"></div>');
	var saveButton = $('<span class="save-button">' + Preferences.getMessage('plma.preference.save') + '</span>');
	var cancelButton = $('<span class="cancel-button">' + Preferences.getMessage('plma.preference.cancel') + '</span>');
	buttonsContainer.append(saveButton);
	buttonsContainer.append(cancelButton);
	this.container.append(buttonsContainer);

	saveButton.on('click', $.proxy(function () {
		if (!this.cannotSave) {
			setTimeout($.proxy(function () {
				/* Save current layouts */
				this.saveLayouts($.proxy(function () {
					$.notify(Preferences.getMessage('plma.preference.save.success'), 'success');
					this.widget.find('.button-container .save-button').removeClass('active');
					this.widget.find('.button-container .cancel-button').removeClass('active');
					setTimeout($.proxy(function() {
						this.reload();
					},this),1000);
				}, this), $.proxy(function () {
					$.notify(Preferences.getMessage('plma.preference.save.error'), 'error');
				}, this));
			}, this), 2000);
		}
	}, this));

	cancelButton.on('click', $.proxy(function () {
		/* Reload layout values */
		var client = new PlmaAjaxClient(this.widget);
		client.addWidget(this.uCssId);
		client.update();
	}, this));
};

PreferenceLayout.prototype.displayDetail = function () {
	var layout = this.findLayout();
	if (!this.layoutDetail) {
		this.initDetail(layout);
	} else {
		this.updateDetail(layout);
	}
	this.layoutDetail.removeClass('hidden');
};

PreferenceLayout.prototype.hideDetail = function () {
	if (this.layoutDetail) {
		this.layoutDetail.addClass('hidden');
	}
};

PreferenceLayout.prototype.initDetail = function (layout) {
	if (!layout) {
		layout = {};
		layout.id = 'newLayout';
	}
	this.layoutDetail = $('<div class="preference-detail preference-layout-detail hidden"></div>');
	this.layoutDetail.append($('<div class="title-container">' + Preferences.getMessage('plma.preference.detail.layout.title') + '</div>'));
	this.layoutDetail.append($('<div class="label-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.label') + ': </span><input class="label-input" type="text" maxlength="100" value="' + layout.label + '"/></div>'));
	this.layoutDetail.append($('<div class="description-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.description') + ': </span><input class="description-input" type="text" maxlength="300" value="' + layout.description + '"/></div>'));
	this.container.find('.preference-block-container').append(this.layoutDetail);

	/* Listen to modifications */
	var timer = 0;
	this.layoutDetail.find('.label-input').on('keyup', $.proxy(function (e) {
		this.widget.find('.button-container .save-button').addClass('active');
		this.widget.find('.button-container .cancel-button').addClass('active');
		var inputValue = e.target.value;
		clearTimeout(timer);
		timer = setTimeout($.proxy(function () {
			if (!inputValue.trim()) {
				$(e.target).addClass('empty');
				this.cannotSave = true;
			} else {
				$(e.target).removeClass('empty');
				this.cannotSave = false;
				this.container.find('.layout-description.selected .info-layout .label').text(inputValue);
				var selectedId = this.container.find('.layout-block.selected .layout-description.selected .id').text();
				var selectedPage = this.container.find('.layout-block.selected .layout-label').text();
				for (layout in this.layouts[selectedPage]) {
					if (this.layouts[selectedPage][layout].id === selectedId) {
						this.layouts[selectedPage][layout].label = inputValue;
					}
				}
			}

		}, this), 2000);
	}, this));
	var timerDescription = 0;
	this.layoutDetail.find('.description-input').on('keyup', $.proxy(function (e) {
		this.widget.find('.button-container .save-button').addClass('active');
		this.widget.find('.button-container .cancel-button').addClass('active');
		var inputValue = e.target.value;
		clearTimeout(timerDescription);
		timerDescription = setTimeout($.proxy(function () {
			this.container.find('.layout-description.selected .info-layout .description').text(inputValue);
			var selectedId = this.container.find('.layout-block.selected .layout-description.selected .id').text();
			var selectedPage = this.container.find('.layout-block.selected .layout-label').text();
			for (layout in this.layouts[selectedPage]) {
				if (this.layouts[selectedPage][layout].id === selectedId) {
					this.layouts[selectedPage][layout].description = inputValue;
					this.widget.find('.button-container .save-button').addClass('active');
					this.widget.find('.button-container .cancel-button').addClass('active');
				}
			}
		}, this), 2000);
	}, this));
};

PreferenceLayout.prototype.updateDetail = function (layout) {
	if (!layout) {
		layout = {};
		layout.id = 'newLayout';
	}
	this.layoutDetail.find('.label-container .label-input')[0].value = layout.label;
	this.layoutDetail.find('.description-container .description-input')[0].value = layout.description;
};

PreferenceLayout.prototype.findLayout = function () {
	var layout = {};
	if (this.container.find('.layout-description.selected').length > 0) {
		layout.label = this.container.find('.layout-description.selected .info-layout .label').text();
		layout.description = this.container.find('.layout-description.selected .info-layout .description').text();

		return layout;
	}
	return undefined;
};

PreferenceLayout.prototype.deleteLayout = function (elem) {
	var selectedPage = elem.closest('.layout-block').find('.layout-label').text();
	var selectedId = elem.find('.id').text();
	var indexToRemove = -1;
	for (index in this.layouts[selectedPage]) {
		if (this.layouts[selectedPage][index].id === selectedId) {
			indexToRemove = index;
			this.widget.find('.button-container .save-button').addClass('active');
			this.widget.find('.button-container .cancel-button').addClass('active');
		}
	}
	if (indexToRemove !== -1) {
		this.layouts[selectedPage].splice(indexToRemove, 1);
	}

	this.deletedElems.push(selectedId);

	elem.remove();
	this.hideDetail();
};

PreferenceLayout.prototype.reload = function () {
	var url = new BuildUrl(window.location.href);
	if (this.deletedElems.length > 0 && url.getParameter('chartboardId')) {
		for (var i = 0; i < this.deletedElems.length; i++) {
			url.removeParameterWithValue_('chartboardId', this.deletedElems[i]);
		}
		this.deleteLayoutFromPages(function() {
			window.location.replace(url.toString());
		}, function() {

		});
	} else {
		location.reload();
	}
};

PreferenceLayout.prototype.deleteLayoutFromPages = function (successCalback, errorCallback) {
	this.client.get(PreferenceLayout.PAGES_STORAGE_KEY,
		$.proxy(function (pagesItems) {
			var pagesJson;

			if (pagesItems.length > 0 && pagesItems[0].value) {
				pagesJson = JSON.parse(pagesItems[0].value);
			} else {
				pagesJson = {};
			}

			var pages = {};
			for (var pageId in pagesJson) {
				var page = pagesJson[pageId];
				if (page.layout && this.deletedElems.includes(page.layout)) {
					page.layout = undefined;
				}
				pages[page.id] = page;
			}

			this.chartboardStorageManager.saveUserPages(pages, successCalback, errorCallback);
		}, this),
		$.proxy(function () {
			/* error callback */
		}, this)
	);
};
