var AlertingClient = (function () {

    function ajaxRequest(type, uri, data) {
        return $.ajax({
            contentType : 'application/x-www-form-urlencoded; charset=UTF-8',
            type: type,
            url: mashup.baseUrl + '/c/alerts' + uri,
            data: data,
            cache: false
        });
    }

    return {
        /**
         * @param {string} id
         * @returns {JQuery.jqXHR}
         */
        getAlertByID: function (id) {
            return ajaxRequest('GET', '', {id: id});
        },

        getAlerts: function () {
            return ajaxRequest('GET', '');
        },

        /**
         * @param {object} data
         * @param {string} data.title
         * @param {string} data.pageName
         * @param {string} [data.pageId]
         * @param {string} data.chartID
         * @param {string} data.condition
         * @param {object[]} data.thresholds
         * @param {number} data.startDate
         * @param {number} data.endDate
         * @param {object[]} data.subscriptions
         * @returns {JQuery.jqXHR}
         */
        createAlert: function (data) {
            return ajaxRequest('POST', '', data);
        },

        /**
         *
         * @param {object} data
         * @param {string} data.id
         * @param {string} data.title
         * @param {string} data.condition
         * @param {object[]} data.thresholds
         * @param {number} data.startDate
         * @param {number} data.endDate
         * @param {object[]} data.subscriptions
         * @returns {JQuery.jqXHR}
         */
        editAlert: function (data) {
            return ajaxRequest('POST', '', data);
        },

        /**
         * @param {string} id
         * @returns {JQuery.jqXHR}
         */
        deleteAlert: function (id) {
            return ajaxRequest('POST', '', {id : id});
        },

        /**
         * @param {object} data
         * @param {string} data.subscriptionId
         * @param {boolean} data.is_subscribed
         * @returns {JQuery.jqXHR}
         */
        updateSubscription: function (data) {
            return ajaxRequest('POST', '', data);
        },

        /**
         * @param {object} data
         * @param {string} data.alert_id
         * @param {string} data.user_id
         * @param {boolean} data.is_seen
         * @param {number} data.last_seen
         * @returns {JQuery.jqXHR}
         */
        updateNotificationInfo: function (data) {
            return ajaxRequest('POST', '', data);
        }
    };
})();