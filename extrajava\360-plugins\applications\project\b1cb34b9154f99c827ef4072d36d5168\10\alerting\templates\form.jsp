<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<plma:getConstantValueFromNameTag var="ALERTING_TITLE_PARAM" constantName="ALERTING_TITLE_PARAM" />
<plma:getConstantValueFromNameTag var="ALERTING_CONDITION_TYPE_PARAM" constantName="ALERTING_CONDITION_TYPE_PARAM" />
<plma:getConstantValueFromNameTag var="ALERTING_SERIES_PARAM" constantName="ALERTING_SERIES_PARAM" />
<plma:getConstantValueFromNameTag var="ALERTING_CATEGORY_PARAM" constantName="ALERTING_CATEGORY_PARAM" />
<plma:getConstantValueFromNameTag var="ALERTING_THRESHOLD_CONDITION_PARAM" constantName="ALERTING_THRESHOLD_CONDITION_PARAM" />
<plma:getConstantValueFromNameTag var="ALERTING_THRESHOLD_VALUE_PARAM" constantName="ALERTING_THRESHOLD_VALUE_PARAM" />
<plma:getConstantValueFromNameTag var="ALERTING_THRESHOLD_TYPE_PARAM" constantName="ALERTING_THRESHOLD_TYPE_PARAM" />
<plma:getConstantValueFromNameTag var="ALERTING_THRESHOLD_TYPE_INFO_PARAM" constantName="ALERTING_THRESHOLD_TYPE_INFO_PARAM" />
<plma:getConstantValueFromNameTag var="ALERTING_NOTIFICATION_TYPE_PARAM" constantName="ALERTING_NOTIFICATION_TYPE_PARAM" />
<plma:getConstantValueFromNameTag var="ALERTING_RECIPIENT_PARAM" constantName="ALERTING_RECIPIENT_PARAM" />
<plma:getConstantValueFromNameTag var="ALERTING_START_DATE_PARAM" constantName="ALERTING_START_DATE_PARAM" />
<plma:getConstantValueFromNameTag var="ALERTING_END_DATE_PARAM" constantName="ALERTING_END_DATE_PARAM" />

<form class="alert_form">
    <div class="alert_property title single_line">
        <label class="label_row"><i18n:message code="alerting.form.title"/></label>
        <div class="fields_row">
            <input name="${ALERTING_TITLE_PARAM}" class="alert_title form-control" type="text">
        </div>
    </div>
    <div class="alert_property condition single_line">
        <label class="label_row"><i18n:message code="alerting.form.when"/></label>
        <div class="fields_row">
            <select name="${ALERTING_CONDITION_TYPE_PARAM}" class="alert_condition form-control">
                <option value="<plma:getConstantValueFromNameTag constantName="ALERTING_CONDITION_TYPE_ANY" />"><i18n:message code="alerting.form.any"/></option>
                <option value="<plma:getConstantValueFromNameTag constantName="ALERTING_CONDITION_TYPE_ALL" />"><i18n:message code="alerting.form.all"/></option>
            </select>
            <span class="span_text"><i18n:message code="alerting.form.conditionsMet"/></span>
        </div>
    </div>
    <div class="alert_property thresholds multi_lines">
        <label class="label_row"></label>
        <div class="fields_wrapper">
            <div class="fields_row colored_background">
                <div class="line">
                    <div class="section_1">
                        <select name="${ALERTING_SERIES_PARAM}" class="alert_thr_series form-control">

                        </select>
                        <select name="${ALERTING_CATEGORY_PARAM}" class="alert_thr_categ form-control">

                        </select>
                        <span class="span_text"><i18n:message code="alerting.form.is"/></span></div>
                    <div class="section_2">
                        <span class="btn-remove fonticon fonticon-trash site-icon" title="<i18n:message code="alerting.form.actions.deleteCondition"/>"></span>
                        <span class="btn-add fonticon fonticon-plus site-icon" title="<i18n:message code="alerting.form.actions.addCondition"/>"></span>
                    </div>
                </div>
                <div class="line">
                    <select name="${ALERTING_THRESHOLD_CONDITION_PARAM}" class="alert_thr_condition form-control">
                        <option value="<plma:getConstantValueFromNameTag constantName="ALERTING_THRESHOLD_CONDITION_ABOVE" />"><i18n:message code="alerting.form.above"/></option>
                        <option value="<plma:getConstantValueFromNameTag constantName="ALERTING_THRESHOLD_CONDITION_BELOW" />"><i18n:message code="alerting.form.below"/></option>
                        <option value="<plma:getConstantValueFromNameTag constantName="ALERTING_THRESHOLD_CONDITION_EQUAL" />"><i18n:message code="alerting.form.equal"/></option>
                    </select>
                    <input name="${ALERTING_THRESHOLD_VALUE_PARAM}" class="alert_thr_value form-control" type="text" placeholder="value">
                    <select name="${ALERTING_THRESHOLD_TYPE_PARAM}" class="alert_thr_type form-control">
                        <option value="<plma:getConstantValueFromNameTag constantName="ALERTING_THRESHOLD_TYPE_VALUE" />"><i18n:message code="alerting.form.value"/></option>
                        <%--
                        Currently, percent is not handled.
                        <option value="<plma:getConstantValueFromNameTag constantName="ALERTING_THRESHOLD_TYPE_PERCENT" />">%</option>
                        --%>
                    </select>
                    <div class="thr_type_info additional_section hidden">
                        <span class="span_text"><i18n:message code="alerting.form.of"/></span>
                        <select name="${ALERTING_THRESHOLD_TYPE_INFO_PARAM}" class="form-control">
                            <option value="<plma:getConstantValueFromNameTag constantName="ALERTING_THRESHOLD_TYPE_INFO_All" />"><i18n:message code="alerting.form.all"/></option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="alert_property recipients multi_lines">
        <label class="label_row"><i18n:message code="alerting.form.then"/></label>
        <div class="fields_wrapper">
            <div class="fields_row colored_background">
                <div class="line">
                    <div class="section_1">
                        <select name="${ALERTING_NOTIFICATION_TYPE_PARAM}" class="alert_notif_type form-control">
                            <option value="<plma:getConstantValueFromNameTag constantName="ALERTING_NOTIFICATION_TYPE_NONE" />" disabled><i18n:message code="alerting.form.selectAction"/></option>
                            <%--
                            Currently, we do not send emails.
                            <option value="<plma:getConstantValueFromNameTag constantName="ALERTING_NOTIFICATION_TYPE_EMAIL" />"><i18n:message code="alerting.form.email"/></option>
                            --%>
                            <option value="<plma:getConstantValueFromNameTag constantName="ALERTING_NOTIFICATION_TYPE_NOTIFICATION" />" selected><i18n:message code="alerting.form.notification"/></option>
                        </select>
                        <span class="span_text"><i18n:message code="alerting.form.to"/></span>
                        <div class="recipient_info additional_section">
                            <div class="input-group">
                                <input name="${ALERTING_RECIPIENT_PARAM}" class="form-control" type="text" placeholder="recipient">
                                <span class="input-group-btn">
                                    <button class="add-recipient btn btn-default" type="button" title="<i18n:message code="alerting.form.actions.addRecipient"/>">
                                        <span class="fonticon fonticon-plus"></span>
                                    </button>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="section_2">
                        <span class="btn-remove fonticon fonticon-trash site-icon" title="<i18n:message code="alerting.form.actions.deleteAction"/>"></span>
                        <span class="btn-add fonticon fonticon-plus site-icon" title="<i18n:message code="alerting.form.actions.addAction"/>"></span>
                    </div>
                </div>
                <div class="line mails-badges"></div>
            </div>
        </div>
    </div>
    <div class="alert_property dates single_line">
        <label class="label_row"><i18n:message code="alerting.form.period"/></label>
        <div class="fields_row">
            <span class="span_text from_span"><i18n:message code="alerting.form.from"/></span>
            <input name="${ALERTING_START_DATE_PARAM}" class="alert_start_date form-control" type="text">
            <span class="span_text to_span"><i18n:message code="alerting.form.to"/></span>
            <input name="${ALERTING_END_DATE_PARAM}" class="alert_end_date form-control" type="text">
        </div>
    </div>
</form>

<render:renderScript>
    AlertingForm.ALERTING_TITLE_PARAM = '${ALERTING_TITLE_PARAM}';
    AlertingForm.ALERTING_CONDITION_TYPE_PARAM = '${ALERTING_CONDITION_TYPE_PARAM}';
    AlertingForm.ALERTING_SERIES_PARAM = '${ALERTING_SERIES_PARAM}';
    AlertingForm.ALERTING_CATEGORY_PARAM = '${ALERTING_CATEGORY_PARAM}';
    AlertingForm.ALERTING_CATEGORY_ANY = '<plma:getConstantValueFromNameTag constantName="ALERTING_CATEGORY_ANY" />';
    AlertingForm.ALERTING_THRESHOLD_CONDITION_PARAM = '${ALERTING_THRESHOLD_CONDITION_PARAM}';
    AlertingForm.ALERTING_THRESHOLD_VALUE_PARAM = '${ALERTING_THRESHOLD_VALUE_PARAM}';
    AlertingForm.ALERTING_THRESHOLD_TYPE_PARAM = '${ALERTING_THRESHOLD_TYPE_PARAM}';
    AlertingForm.ALERTING_THRESHOLD_TYPE_INFO_PARAM = '${ALERTING_THRESHOLD_TYPE_INFO_PARAM}';
    AlertingForm.ALERTING_NOTIFICATION_TYPE_PARAM = '${ALERTING_NOTIFICATION_TYPE_PARAM}';
    AlertingForm.ALERTING_NOTIFICATION_TYPE_EMAIL = '<plma:getConstantValueFromNameTag constantName="ALERTING_NOTIFICATION_TYPE_EMAIL" />';
    AlertingForm.ALERTING_NOTIFICATION_TYPE_NOTIFICATION = '<plma:getConstantValueFromNameTag constantName="ALERTING_NOTIFICATION_TYPE_NOTIFICATION" />';
    AlertingForm.ALERTING_RECIPIENT_PARAM = '${ALERTING_RECIPIENT_PARAM}';
    AlertingForm.ALERTING_START_DATE_PARAM = '${ALERTING_START_DATE_PARAM}';
    AlertingForm.ALERTING_END_DATE_PARAM = '${ALERTING_END_DATE_PARAM}';
</render:renderScript>