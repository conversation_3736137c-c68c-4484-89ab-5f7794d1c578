<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="data" uri="http://www.exalead.com/jspapi/data" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="highcharts" uri="http://www.exalead.com/jspapi/highcharts" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varFeeds="feeds" parameters="chartContainerId,cssId,uCssId,fullScreen,buttons,series" />

<config:getOption var="enableRefines" name="enableRefines" defaultValue="false"/>
<config:getOption var="target" name="target" defaultValue=""/>
<config:getOption var="facetType" name="facetType" defaultValue="normal"/>
<config:getOption var="facetsList" name="facetsList" doEval="true" />
<config:getOptionsComposite var="serieConfiguration" name="basedOn" mapIndex="true"/>
<config:getOptions var="forceRefineOnFeeds" name="forceRefineOnFeeds" />
<config:getOptions var="forceRefineOnFacets" name="forceRefineOnFacets" />
<config:getOption var="forceRefineOnMulti" name="forceRefineOnMulti" defaultValue="false" />
<config:getOption var="maxCategories" name="maxCategories" defaultValue="0" />
<config:getOption var="additionalCategories" name="additionalCategories" defaultValue="Hide" />

<config:getOption var="isGrouping" name="grouping" defaultValue="" />
<config:getOptionsComposite var="dataGroups" name="dataGroups" defaultValue="" />
<config:getOption var="disableRefine" name="disableRefine" defaultValue="false"/>
<config:getOption var="xAxisLabel" name="xAxisLabel" defaultValue=""/>
<config:getOptions var="yAxisLabels" name="yAxisLabels" defaultValue=""/>
<config:getOption var="categoriesOrder" name="categoriesOrder" defaultValue=""/>

<config:getOption var="customSeries" name="customSeries" defaultValue="false" />
<config:getOption var="customSeriesClass" name="customSeriesClass" />

<config:getOption var="displayDoc" name="displayDoc" defaultValue="false"/>
<config:getOption var="exportChart" name="exportChart" defaultValue="false"/>

<config:getOption var="finalSumWaterfall" name="finalSumWaterfall" defaultValue="false"/>
<config:getOption var="finalSumDisplayName" name="finalSumDisplayName" defaultValue=""/>
<config:getOption var="finalSumDisplayColor" name="finalSumDisplayColor" defaultValue=""/>
<config:getOption var="intermediateSumWaterfall" name="intermediateSumWaterfall" defaultValue="false"/>
<config:getOptionComposite var="intermediateSum" name="intermediateSum" defaultValue="" mapIndex="true"/>
<config:getOption var="activateReloadChart" name="activateReloadChart" defaultValue="false"/>
<config:getOptionsComposite var="additionalParams" name="additionalParams" mapIndex="true" />


<c:set var="isWaterFall" value="false" />
var waterFallOptions = {};
<c:forEach var="config" items="${serieConfiguration}">
	<c:if test="${config.representation=='waterfall'}">
		<c:set var="isWaterFall" value="true" />
		waterFallOptions.finalSumWaterfall = '${finalSumWaterfall}';
		waterFallOptions.finalSumDisplayName = '${finalSumDisplayName}';
		waterFallOptions.finalSumDisplayColor = '${finalSumDisplayColor}';
		waterFallOptions.intermediateSumWaterfall = '${intermediateSumWaterfall}';
		waterFallOptions.intermediateSum = {};
		waterFallOptions.intermediateSum.type = '${intermediateSum.intermediateSumType}';
		waterFallOptions.intermediateSum.number = '${intermediateSum.intermediateSumNb}';
		waterFallOptions.intermediateSum.total = '${intermediateSum.isIntermediate}';
		waterFallOptions.intermediateSum.name = '${intermediateSum.intermediateSumName}';
		waterFallOptions.intermediateSum.color = '${intermediateSum.intermediateSumColor}';
	</c:if>
</c:forEach>

<c:set var="nbFacet" value="${fn:length(fn:split(facetsList,','))}" />

<list:new var="yAxisLabelsAsList"/>
<c:forEach items="${yAxisLabels}" var="yAxisLabel">
	<list:add value="${fn:trim(yAxisLabel)}" list="${yAxisLabelsAsList}"/>
</c:forEach>
<list:new var="categoriesOrderAsList"/>
<c:forEach items="${categoriesOrder}" var="category">
	<list:add value="${fn:trim(category)}" list="${categoriesOrderAsList}"/>
</c:forEach>

<plma:highchartsJSON var="highchartsJSON"
	feeds="${feeds}"
	categoriesOrder="${categoriesOrderAsList}" 
	axisLabelX="${xAxisLabel}" 
	axisLabelsY="${yAxisLabelsAsList}"
	maxCategories="${maxCategories}"
	additionalCategories="${additionalCategories}"
	baseUrl="${target}"
	forceRefineOnFeeds="${forceRefineOnFeeds}"
	forceRefineOnFacets="${forceRefineOnFacets}"
	forceRefineOnMulti="${forceRefineOnMulti == 'true'}" >

	<c:forEach items="${serieConfiguration}" var="serieConfig">
		<map:put key="maxCategories" value="${maxCategories}" map="${serieConfig}"/>
		<plma:highchartsSerie feeds="${feeds}" serieConfig="${serieConfig}" customSeries="${customSeries}" customSeriesClass="${customSeriesClass}" />
	</c:forEach>
</plma:highchartsJSON>

var data = ${highchartsJSON};

var reloadParams = [];
<c:forEach items="${additionalParams}" var="additionalParam" varStatus="loop">
	reloadParams.push('${additionalParam.paramName}' + '__' + '${additionalParam.paramValue}');
</c:forEach>

<%--Retrieving feeds selected on plma chart--%>
var selectedFeeds = [];
<c:forEach items="${feeds}" var="feed" varStatus="loop">
    var key = '${feed.key}';
    selectedFeeds.push(key);
</c:forEach>

var feedsDefinition = [];
<search:forEachFeed var="feed" feeds="${feeds}">
    <search:getFeedUri feed="${feed}" var="uri" />
    var feedId = '${feed.id}';
    var definition = {};
    feedsDefinition[feedId] = '${uri}';
    feedsDefinition.push(definition);
</search:forEachFeed>

var dataSeriesConfiguration = [];
var highchartsSeriesName = [];

<%--Retrieve serieName, feed, facet, and aggregations --%>
<search:getFeed var="feed" feeds="${feeds}" /> <%-- First feed --%>
<c:forEach items="${serieConfiguration}" var="serie" varStatus="loop">
    <search:getFacet facetId="${serie.facetId}" var="facet" feeds="${feeds}"/>
    <string:eval string="${serie.pointLegend}" var="pointLegend" feeds="${feeds}" facet="${facet}" isJsEscape="true"/>
    <string:eval string="${serie.legend}" var="legend" feeds="${feeds}" facet="${facet}" isJsEscape="true"/>
    <plma:highchartsSerieName var="serieName" serieConfig="${serie}" feeds="${feeds}" />
	<string:escape var="serieName" value="${serieName}" escapeType="JAVASCRIPT" />
    <i18n:message code="facet_${facet.path}" var="facetI18n" />
	<%-- Not using i18n:message's javaScriptEscape as it does not handle null values --%>
	<string:escape var="facetI18n" value="${facetI18n}" escapeType="JAVASCRIPT" />
	<search:getFacetType var="facetType" facet="${facet}" />
    highchartsSeriesName.push('${serieName}');

    var dataSerieConfiguration = {
        label 			: '${serieName}',
        feed  			: '${not empty serie.feedId ? serie.feedId : feed.id}',
        facet 			: '${serie.facetId}',
        facetI18n       : '${facetI18n}',
		facetType		: '${facetType}',
        isMultiDimensionFacet : false,
        aggregation 	: '${serie.aggregation}',
        pointLegend     : '${pointLegend}'
    };

    <plma:getFacetDimensions id1="facetId1" id2="facetId2" facetId="${serie.facetId}" feeds="${feeds}" />
    <c:if test="${facetId1 != null && facetId2 != null}">
        <string:eval string="${serie.legend}" var="legend" isJsEscape="true"/>
		<search:getFacet  var="facet1" facetId="${facetId1}" feeds="${feeds}"/>
		<search:getFacetType var="facetType1" facet="${facet1}" />
		<search:getFacet  var="facet2" facetId="${facetId2}" feeds="${feeds}"/>
		<search:getFacetType var="facetType2" facet="${facet2}" />
		dataSerieConfiguration = $.extend({}, dataSerieConfiguration, {
			multiDimensionFacetIds : ['${facetId1}', '${facetId2}'],
			multiDimensionFacetTypes : ['${facetType1}', '${facetType2}'],
			isMultiDimensionFacet : true, 
			label: '', 
			legend: '${legend}'
		});
    </c:if>

    dataSeriesConfiguration.push(dataSerieConfiguration);
</c:forEach>

var plmaChart2Options = {
	data: data,
	dataProcessor: <config:getOption name="dataProcessor" defaultValue="function(data) { return data; }" />,
	userOptions: <config:getOption name="opts" defaultValue="{}"/>,
	defaultOptions: {
	chartImage: '<c:url value="/resources/highcharts/images/chart_line.png" />',
	chartName: 'plmaCharts2',
	chartDisplayName: '<i18n:message code="widget.plmacharts.name" javaScriptEscape="true" />',
	chartTitle: <string:escape escapeType="jsonValue"><config:getOption name="title" defaultValue="" /></string:escape> /* Used for multipleCharts */


	},
	baseUrl: '${target}',
	enableRefine: ${enableRefines == 'true'},
	reload: {
	enable: ${activateReloadChart == 'true'},
	params: reloadParams
	},
	hideEmpty: '<config:getOption name="hideEmpty" defaultValue="false" />',
	isWaterFall: ${isWaterFall == 'true'},
	waterFallOptions: waterFallOptions,
    selectedFeeds: selectedFeeds,
    dataSeriesConfiguration: dataSeriesConfiguration,
    feedsDefinition: feedsDefinition,
	fullScreen: ${fullScreen}
};
var plmaChart = new PLMAChart2('${cssId}','${uCssId}', plmaChart2Options);


var buttonOptions = {};
buttonOptions.buttons = [];
<c:if test="${fullScreen == 'true'}">
	var elem = {};
	var isFullScreen = $('.${uCssId}').hasClass('full-size-active');
	elem.icon = FullScreenWidget.BASE_ICON_CSS_CLASS + ' ' + (isFullScreen
		? FullScreenWidget.EXIT_FULL_SCREEN_ICON_CSS_CLASS
		: FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS
	);
	elem.label = isFullScreen
		? FullScreenWidget.getMessage('widget.action.exitFullScreen')
		: FullScreenWidget.getMessage('widget.action.fullScreen');
	buttonOptions.buttons.push(elem);
	<%--elem.onInit = function() {
	$('.${uCssId}').fullScreenWidget({
	button : null
	});
	};--%>
</c:if>



<%-- If stacking is true, show a button for switching the stacking mode --%>
<config:getOption name="stackingButton" var="stacking" defaultValue="false" />

<c:if test="${stacking}">
	var elem = {};
	elem.icon = 'fonticon fonticon-chart-bar-stacked';
	elem.label = '<i18n:message code="widget.plmacharts.menu.stacking" javaScriptEscape="${true}"/>';
	elem.action = function(e) {
		$(e.target).closest('.plmaCharts').data('widget').switchStacking();
	};
	buttonOptions.buttons.push(elem);
</c:if>
<c:if test="${displayDoc}">
	var docButton = {};
	docButton.icon = 'fonticon fonticon-info';
	docButton.label = '<i18n:message code="widget.plmacharts.menu.documentation" javaScriptEscape="${true}"/>';
	docButton.action = function(e) {
		var chart = $(e.target).closest('.plmaCharts');
		var docContainer = chart.find('.doc-container');
		docContainer.removeClass('hidden');
	};
	buttonOptions.buttons.push(docButton);
	$('.${uCssId}').find('.doc-container .close-doc').on('click', function(e) {
		var chart = $(e.target).closest('.plmaCharts');
		chart.find('.doc-container').addClass('hidden');
	});
</c:if>
<c:if test="${exportChart}">
    <config:getOption name="spaceUrl" var="spaceUrl" defaultValue="" />
    <config:getOption name="imageType" var="imageType" defaultValue="svg" />
    <config:getOption name="enableNewDoc" var="enableNewDoc" defaultValue="false" />
    <config:getOption name="enableBookmarkMode" var="enableBookmarkMode" defaultValue="true" />

	var exportButton = {};
	exportButton.icon = 'fonticon fonticon-picture';
	exportButton.label = '<i18n:message code="widget.plmacharts.menu.exportchart" javaScriptEscape="${true}"/>';
	exportButton.action = function(e) {
	    var plmaChart = $(e.target).closest('.plmaCharts').data('widget');

	    /* Call showPopup only when the data is available. if no data available then notify error.*/
        if (!plmaChart.getChart()) {
            $.notify(plmaChart.getMessage('widget.plmacharts.exportchart.err.nodata'), 'error');
        }else{
            /* Turn off the Pointer events to avoid unwanted fade/blur/click event.
            Events will be put back once the export is done. */
            let $chartWrapper = plmaChart.widget.find('.chart-wrapper');
            $chartWrapper.css('pointer-events','none');

            var previewHelper = window.exportTo3DSpace.startDocumentExport({
                id:"DocumentExport",
				title: '<i18n:message code="plma.exportto3dspace.title" javaScriptEscape="${true}"/>',
				fileMode: true,
				fileExtension: '${imageType}',
				spaceUrl: '${spaceUrl}',
                enableAddNewDocument: ${enableNewDoc},
                isBookmarkEnable: ${enableBookmarkMode}
            });
            plmaChart.exportChart({
                imageType: '${imageType}',
                container: previewHelper.container,
                doneCallback: previewHelper.doneCallback
            });

            $chartWrapper.css('pointer-events','auto');
        }
	};
	buttonOptions.buttons.push(exportButton);
</c:if>

<c:forEach items="${buttons}" var="button">
	var elem = {};
	elem.icon = '<string:escape value="${button.css}" escapeType="JAVASCRIPT"/>';
	elem.label = '<string:escape value="${button.label}" escapeType="JAVASCRIPT"/>';
	elem.action = ${button.onClick};
	<c:if test="${not empty button.onInit}">		
		elem.onInit = ${button.onInit};
	</c:if>
	buttonOptions.buttons.push(elem);
</c:forEach>

var plmaChartButtons = new PlmaChartButton('${uCssId}', buttonOptions, plmaChart2Options);
plmaChartButtons.init();
