<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds"/>
<render:import parameters="viewWidget,parentWidgetID"/>

<request:getParameterValue var="gridtableParam" name="gridtable" defaultValue=""/>

<%--If export is activated specificaly for this view, display export button in header--%>
<c:if test="${plma:getBooleanParam(viewWidget, 'enableExport', false)}">
    <render:template template="templates/exportDisplay.jsp" widget="plmaResultListCommons">
        <render:parameter name="columnsConfig" value="${plma:getStringParam(widget, 'resultListId', '')}"/>
        <render:parameter name="buttonWidgetCssId" value="${parentWidgetID}"/>
    </render:template>
    <span class="insert-after"></span>
</c:if>



