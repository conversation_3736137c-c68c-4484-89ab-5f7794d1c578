<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="displayHits" uri="http://www.exalead.com/displayHits-widget-helpers" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="experience" uri="http://www.exalead.com/jspapi/experience" %>

<render:import parameters="facet,widget,feeds,uCssId,facetConfig,isSuggestSearch,suggestApiAction,facetsSuggestContext,refinePanelCfg,suggestCfg,refinesPanelId,refinesPanelMode"/>

<%-- Manage pagination on UI side, absolutly not recommended since for a correct behavior, facte must be exhaustive
 		It is recommended to paginate on backend side using UI and Access facets management triggers
 		https://gitlab.paris.exalead.com/apps/plmaonpremise/plma-tools/-/wikis/Facets-management-triggers#ui-trigger --%>
<c:set var="isPaginated" value="${refinePanelCfg.paginated && refinePanelCfg.perPage > 0}"/>

<c:set var="facetTemplateDirectory" value="templates/default"/>

<c:set var="collapseValue" value="${refinePanelCfg.collapseFacet ? 'hidden' : ''}" />

<i18n:message code="plma.tools.search" var="searchI18n"/>
<i18n:message code="refines.noresult" var="noResultI18n"/>

<%-- Get pagination info from displayed facet, it is recommended to put in place facets UI and access trigger to manage correctly pagination --%>
<c:choose>
    <c:when test="${isPaginated}">
        <plma:getPaginationInfo var="paginationInfo" feeds="${feeds}" facetId="${facet.id}" uiPagination="${refinePanelCfg.perPage}"/>
    </c:when>
    <c:otherwise>
        <plma:getPaginationInfo var="paginationInfo" feeds="${feeds}" facetId="${facet.id}"/>
    </c:otherwise>
</c:choose>

<plma:isRefineFacetChartMode facet="${facet.id}" cookie="refine-panel-facet-option" var="defaultChartMode"/>
<plma:refinePanelSerie facet="${facet}" var="paginatedSerie" paginate="${isPaginated && paginationInfo.uiPagination}" perPage="${refinePanelCfg.perPage}"
                       refineConfig="${facetConfig}" feeds="${feeds}"/>

<div class="filter-list-facet-elem filter-list-facet-elem-facet filter-list-facet-elem-facet-${facet.id}" data-facetPath="${facet.path}" data-facetId="${facet.id}">
    <div class="facet-spinner-container">
        <div class="facet-spinner"></div>
    </div>
    <div class="facet-value-container">
        <plma:jspTemplatePathCheck var="categoryTemplatePath"
                                  defaultJspPath="${facetTemplateDirectory}/category.jsp"
                                  customJspPath="${facetTemplateDirectory}/${fn:toLowerCase(facet.path)}/category.jsp" widget="refinePanel"/>

        <render:template template="${facetTemplateDirectory}/${fn:toLowerCase(facet.path)}/facet.jsp"
                         defaultTemplate="${facetTemplateDirectory}/facet.jsp" widget="refinePanel">
            <render:parameter name="widget" value="${widget}"/>
            <render:parameter name="facet" value="${facet}"/>
            <render:parameter name="accessFeeds" value="${feeds}"/>
            <render:parameter name="uCssId" value="${uCssId}"/>
            <render:parameter name="facetConfig" value="${facetConfig}"/>
            <render:parameter name="collapseValue" value="${collapseValue}"/>
            <render:parameter name="isSuggestSearch" value="${isSuggestSearch}"/>
        </render:template>

        <c:if test="${refinePanelCfg.activateFilter}">
            <c:set var="showInput" value="${false}"/>
            <c:set var="categoryCount" value="0"/>
            <search:forEachCategory
                    var="category"
                    root="${facet}"
                    iterationMode="all"
                    end="${refinePanelCfg.filterMin+1}"
                    showEmptyCategories="false">
                <c:set var="categoryCount" value="${categoryCount + 1}"/>
            </search:forEachCategory>
            <c:set var="showInput" value="${categoryCount >= refinePanelCfg.filterMin}"/>
            <div class="inputFilterCategory table-list-facet-${facet.id}-input ${showInput ? '' : 'hidden'}">
                <div id="filter-list-facet-${facet.id}-noresult"
                     class=" filter-list-facet-noresult filter-list-facet-${facet.id}-noresult hidden">${noResultI18n}</div>
            </div>
        </c:if>
        <span class="view-container">
			<div class="facet-container-list ${refinePanelCfg.collapseFacet || (defaultChartMode && isSuggestSearch != 'true' && suggestApiAction == 'access') ? 'hidden' : ''}">
				<table class="facet table table-layout table-facets table-hover table-list-facet-${facet.id}"
                       data-page="${paginationInfo.page}">
					<render:template
                            template="${facetTemplateDirectory}/${fn:toLowerCase(facet.path)}/subCategory.jsp"
                            defaultTemplate="${facetTemplateDirectory}/subCategory.jsp" widget="refinePanel">
                        <render:parameter name="widget" value="${widget}"/>
                        <render:parameter name="facet" value="${facet}"/>
                        <render:parameter name="accessFeeds" value="${feeds}"/>
                        <render:parameter name="categoryTemplatePath" value="${categoryTemplatePath}"/>
                        <render:parameter name="paginationInfo" value="${paginationInfo}"/>
                        <render:parameter name="facetConfig" value="${facetConfig}"/>
                        <render:parameter name="uCssId" value="${uCssId}"/>
                        <render:parameter name="facetsSuggestContext" value="${facetsSuggestContext}"/>
                        <render:parameter name="refinePanelCfg" value="${refinePanelCfg}"/>
                        <render:parameter name="suggestCfg" value="${suggestCfg}"/>
                    </render:template>
				</table>
			</div>

			<c:if test="${not empty facetConfig && isSuggestSearch != 'true' || suggestApiAction != 'access'}">
                <div class="highcharts-facet-container ${refinePanelCfg.collapseFacet || !defaultChartMode ? 'hidden' : ''}"></div>
            </c:if>

			<c:set var="maxPagination" value="${paginationInfo.pages}"/>
			<config:getOption var="reloadCondition" name="reloadCondition" defaultValue="true"/>
			<string:eval var="reloadCondition" string="${reloadCondition}" facet="${facet}"/>
			<c:set var="reloadCondition" value="${refinePanelCfg.activateReload && reloadCondition}"/>
			<%-- Facet is paginated (on backend or UI side) --%>
			<c:if test="${paginationInfo.paginated && (not isSuggestSearch || suggestApiAction != 'access')}">
				<div class="view pagination ${collapseValue}">
					<span class="previous-page fonticon fonticon-left-open"></span>
					<span class="current-page">${paginationInfo.page}</span> / <span class="max-page">${paginationInfo.pages}</span>
					<span class="next-page fonticon fonticon-right-open"></span>
				</div>
            </c:if>
		</span>
    </div>
</div>

<%-- Don't execute facet script only in case of access mode (directly renders facets list)--%>
<c:if test="${not isSuggestSearch || suggestApiAction != 'access'}">
    <render:renderScript position="READY">
        var options = {};
        options.pageName = '<search:getPageName/>';
        options.feeds = ${plma:getKeys(feeds, '[]')};
        options.refinesPanelId = '${refinesPanelId}';
        options.baseAjaxUrl = '<c:url value="/plma/ajax/feed"/>/<search:getPageName/>/${plma:getFirstFeedID(feeds)}';
        options.baseAjaxReload = '<c:url value="/plma/ajax/refine"/>/<search:getPageName/>/${refinesPanelMode}';
        options.applicationId = '${plma:getApplicationId()}';
        options.pagination = ${plma:getJSON(paginationInfo, '{}')};
        options.paginatedSerie = ${plma:getJSON(paginatedSerie, '{}')};
        options.facetId = '${facet.id}';
        options.facetDisplayConfig = <plma:getFacetDisplayConfigAsJson facet="${facet.id}"/>;
        options.paramNameReload = '${refinePanelCfg.paramNameReload}';
        options.suggestApiAction = '${suggestApiAction}';
        <c:if test="${not empty facetConfig}">
            options.facetConfig = ${plma:getJSON(facetConfig, '{}')};
        </c:if>
        options.paramNameReloadValue = [];
        <request:getParameterValues name="${refinePanelCfg.paramNameReload}" var="paramNameReloads"/>
        <c:forEach items="${paramNameReloads}" var="paramName">
            options.paramNameReloadValue.push('${paramName}');
        </c:forEach>
        <request:getParameterValues name="plma_facet_sort_reload" var="paramNameReloadsSort"/>
        <c:forEach items="${paramNameReloadsSort}" var="paramName">
            options.paramNameReloadValue.push('${paramName}');
        </c:forEach>
        options.loadDateFacetParameter = '<config:getOption name="loadDateFacetParameter" defaultValue=""/>';
        options.loadFacetParameter = '<plma:getConstantValueFromNameTag
            constantName="CLEAN_FEED_PARAMETER"/>';
        options.facetPage = '<request:getParameterValue name="reloadFacetPage" defaultValue="1"/>';
        options.refineEventMode = false;
        <c:if test="${refinePanelCfg.mode.equals('Event')}">
            options.refineEventMode = true;
        </c:if>
        $('.aggregation-bar').empty();
        options.customDelimiter = "${suggestCfg.customDelimiter}";
        var facetRefine = new FacetRefine('${uCssId}', options);
        facetRefine.init();
    </render:renderScript>
</c:if>
