/* global Highcharts */
(function(){
	'use strict';
	if (typeof Highcharts !== 'undefined'){
		
		var plmaTheme = {
			colors: [
				'#009DDB','#FF8A2E','#9B2C98','#00AA86',
				'#F564A3','#8DDEE4','#CC092F','#E2D5CC',
				'#6FBC4B','#70036b','#002596','#C6C68B',
				'#7F7F7F','#4EA8B7','#007A4C','#ADADAD',
				'#127277','#6D2815','#000000','#4D62BA',
				'#FEE000'
			],
			xAxis: {
				labels: {
					style: {
						whiteSpace: 'nowrap',
						textOverflow: 'ellipsis',
						width: 200
					}
				}
			},
		};
		
		Highcharts.setOptions(plmaTheme);
		
		
	}else{
		if (console && console.warn){
			console.warn('Highcharts has not been loaded early enough.');	
		}
	}
	
	
})();