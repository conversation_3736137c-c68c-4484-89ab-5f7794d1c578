/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:33 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.refinePanel.templates.default_;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class numericalFilter_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(14);
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/i18n.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/list.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/url.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/highcharts.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/request.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005ffacet_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005ffacet_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody.release();
    _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.release();
    _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005ffacet_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.release();
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_plma_005fgetConstantValueFromNameTag_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_list_005fnew_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_search_005fforEachFeed_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_search_005fgetFacet_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fchoose_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fif_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(14,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setParameters("widget,feeds,facet,min,max,step,uCssId,refinePanelCfg");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetConstantValueFromNameTag_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getConstantValueFromNameTag
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag _jspx_th_plma_005fgetConstantValueFromNameTag_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag) _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag.class);
    boolean _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(16,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setVar("loadFacetParameter");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(16,0) name = constantName type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setConstantName("CLEAN_FEED_PARAMETER");
      int[] _jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetConstantValueFromNameTag_005f0 = _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doStartTag();
        if (_jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody.reuse(_jspx_th_plma_005fgetConstantValueFromNameTag_005f0);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetConstantValueFromNameTag_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fnew_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:new
    com.exalead.cv360.searchui.view.jspapi.list.NewTag _jspx_th_list_005fnew_005f0 = (com.exalead.cv360.searchui.view.jspapi.list.NewTag) _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.NewTag.class);
    boolean _jspx_th_list_005fnew_005f0_reused = false;
    try {
      _jspx_th_list_005fnew_005f0.setPageContext(_jspx_page_context);
      _jspx_th_list_005fnew_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(18,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fnew_005f0.setVar("feedList");
      int[] _jspx_push_body_count_list_005fnew_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fnew_005f0 = _jspx_th_list_005fnew_005f0.doStartTag();
        if (_jspx_th_list_005fnew_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fnew_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fnew_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fnew_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fnew_0026_005fvar_005fnobody.reuse(_jspx_th_list_005fnew_005f0);
      _jspx_th_list_005fnew_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fnew_005f0, _jsp_getInstanceManager(), _jspx_th_list_005fnew_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachFeed_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachFeed
    com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag _jspx_th_search_005fforEachFeed_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag) _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachFeedTag.class);
    boolean _jspx_th_search_005fforEachFeed_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachFeed_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachFeed_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(19,0) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(19,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFeed_005f0.setVar("feed");
      int[] _jspx_push_body_count_search_005fforEachFeed_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachFeed_005f0 = _jspx_th_search_005fforEachFeed_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachFeed_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write('\r');
            out.write('\n');
            out.write('	');
            if (_jspx_meth_list_005fadd_005f0(_jspx_th_search_005fforEachFeed_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFeed_005f0))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_search_005fforEachFeed_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachFeed_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachFeed_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachFeed_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachFeed_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachFeed_0026_005fvar_005ffeeds.reuse(_jspx_th_search_005fforEachFeed_005f0);
      _jspx_th_search_005fforEachFeed_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachFeed_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachFeed_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_list_005fadd_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFeed_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFeed_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  list:add
    com.exalead.cv360.searchui.view.jspapi.list.AddTag _jspx_th_list_005fadd_005f0 = (com.exalead.cv360.searchui.view.jspapi.list.AddTag) _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.list.AddTag.class);
    boolean _jspx_th_list_005fadd_005f0_reused = false;
    try {
      _jspx_th_list_005fadd_005f0.setPageContext(_jspx_page_context);
      _jspx_th_list_005fadd_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFeed_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(20,1) name = list type = java.util.Collection reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f0.setList((java.util.Collection) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedList}", java.util.Collection.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(20,1) name = value type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_list_005fadd_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("'${feed.id}'", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_list_005fadd_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_list_005fadd_005f0 = _jspx_th_list_005fadd_005f0.doStartTag();
        if (_jspx_th_list_005fadd_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_list_005fadd_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_list_005fadd_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_list_005fadd_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005flist_005fadd_0026_005fvalue_005flist_005fnobody.reuse(_jspx_th_list_005fadd_005f0);
      _jspx_th_list_005fadd_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_list_005fadd_005f0, _jsp_getInstanceManager(), _jspx_th_list_005fadd_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacet_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacet
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag _jspx_th_search_005fgetFacet_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag) _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetTag.class);
    boolean _jspx_th_search_005fgetFacet_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFacet_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacet_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(23,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f0.setVar("mergedFacet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(23,0) name = facetId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f0.setFacetId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(23,0) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacet_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacet_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacet_005f0 = _jspx_th_search_005fgetFacet_005f0.doStartTag();
        if (_jspx_th_search_005fgetFacet_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacet_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacet_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacet_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacet_0026_005fvar_005ffeeds_005ffacetId_005fnobody.reuse(_jspx_th_search_005fgetFacet_005f0);
      _jspx_th_search_005fgetFacet_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacet_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacet_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent(null);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(26,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.collapseFacet}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fset_005f0(_jspx_th_c_005fwhen_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f0_reused = false;
    try {
      _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(27,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setVar("collapseValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(27,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(27,8) 'hidden'",_jsp_getExpressionFactory().createValueExpression("hidden",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
      if (_jspx_th_c_005fset_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
      _jspx_th_c_005fset_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fset_005f1(_jspx_th_c_005fotherwise_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f1_reused = false;
    try {
      _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(30,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setVar("collapseValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(30,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(30,8) ''",_jsp_getExpressionFactory().createValueExpression("",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
      if (_jspx_th_c_005fset_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
      _jspx_th_c_005fset_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(34,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty mergedFacet}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("	<div class=\"filter-list-facet-elem filter-list-facet-elem-numerical filter-list-facet-elem-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("		<div class=\"facet-value-container\">\r\n");
          out.write("			<div class=\"facet-container-list\">\r\n");
          out.write("				<table class=\"facet table table-layout table-facets table-hover table-list-facet-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("					<!-- Title -->\r\n");
          out.write("					<h3 class=\"title-container sub-header-collapsible\">\r\n");
          out.write("						<span class=\"facet-name\"  id=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("							");
          if (_jspx_meth_search_005fgetFacetLabel_005f0(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("						</span>\r\n");
          out.write("						<span class=\"icon-collapse-button icon-collapsible-button fonticon fonticon-chevron-up\"></span>\r\n");
          out.write("						<span class=\"icon-collapse-button icon-collapsed-button fonticon fonticon-chevron-down hidden\"></span>\r\n");
          out.write("					</h3>\r\n");
          out.write("					<div class=\"facet-container-list facet-container-list-numerical-slider-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(' ');
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collapseValue}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("						<div class=\"facet-value\">\r\n");
          out.write("							");
          if (_jspx_meth_c_005fif_005f1(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("							");
          if (_jspx_meth_c_005fif_005f2(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("							");
          if (_jspx_meth_c_005fif_005f3(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("							<div class=\"refine-panel-numerical-slider refine-panel-numerical-slider-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"></div>\r\n");
          out.write("							<div class=\"slider-extreme-container\">\r\n");
          out.write("								<span class=\"slider-min-value\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${min}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</span>\r\n");
          out.write("								<span class=\"slider-max-value\">");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${max}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</span>\r\n");
          out.write("							</div>\r\n");
          out.write("\r\n");
          out.write("							<div class=\"slider-action-container\">\r\n");
          out.write("								<div class=\"slider-inputs\">\r\n");
          out.write("									<input type=\"number\" class=\"value min-value\" max=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${max}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" min=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${min}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"/>\r\n");
          out.write("									<span class=\"value\"> - </span>\r\n");
          out.write("									<input type=\"number\" class=\"value max-value\" max=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${max}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" min=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${min}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"/>\r\n");
          out.write("								</div>\r\n");
          out.write("								");
          if (_jspx_meth_i18n_005fmessage_005f0(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("								");
          if (_jspx_meth_i18n_005fmessage_005f1(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("								<span class=\"slider-button slider-exclude-button fonticon fonticon-block\"\r\n");
          out.write("									  title=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${excludeButton}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"></span>\r\n");
          out.write("								<span class=\"slider-button slider-refine-button fonticon fonticon-filter\"\r\n");
          out.write("									  title=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${filterButton}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"></span>\r\n");
          out.write("							</div>\r\n");
          out.write("\r\n");
          out.write("							<div class=\"slider-refine-container hidden\">\r\n");
          out.write("\r\n");
          out.write("							</div>\r\n");
          out.write("\r\n");
          out.write("						</div>\r\n");
          out.write("					</div>\r\n");
          out.write("				</table>\r\n");
          out.write("			</div>\r\n");
          out.write("		</div>\r\n");
          out.write("	</div>\r\n");
          out.write("\r\n");
          out.write("	");
          if (_jspx_meth_render_005frenderScript_005f0(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacetLabel_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacetLabel
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag _jspx_th_search_005fgetFacetLabel_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag) _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag.class);
    boolean _jspx_th_search_005fgetFacetLabel_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFacetLabel_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacetLabel_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(42,7) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetLabel_005f0.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${mergedFacet}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacetLabel_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacetLabel_005f0 = _jspx_th_search_005fgetFacetLabel_005f0.doStartTag();
        if (_jspx_th_search_005fgetFacetLabel_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacetLabel_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacetLabel_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacetLabel_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005ffacet_005fnobody.reuse(_jspx_th_search_005fgetFacetLabel_005f0);
      _jspx_th_search_005fgetFacetLabel_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacetLabel_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacetLabel_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(49,7) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${empty min}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("								");
          if (_jspx_meth_c_005fset_005f2(_jspx_th_c_005fif_005f1, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("							");
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f2_reused = false;
    try {
      _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(50,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setVar("min");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(50,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(50,8) '0'",_jsp_getExpressionFactory().createValueExpression("0",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
      if (_jspx_th_c_005fset_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
      _jspx_th_c_005fset_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(52,7) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${empty max}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("								");
          if (_jspx_meth_c_005fset_005f3(_jspx_th_c_005fif_005f2, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("							");
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f3_reused = false;
    try {
      _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(53,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setVar("max");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(53,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(53,8) '100'",_jsp_getExpressionFactory().createValueExpression("100",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
      if (_jspx_th_c_005fset_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
      _jspx_th_c_005fset_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(55,7) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${empty step}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("								");
          if (_jspx_meth_c_005fset_005f4(_jspx_th_c_005fif_005f3, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("							");
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f4_reused = false;
    try {
      _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(56,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setVar("step");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(56,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(56,8) '1'",_jsp_getExpressionFactory().createValueExpression("1",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
      if (_jspx_th_c_005fset_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
      _jspx_th_c_005fset_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f0 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f0_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f0.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(70,8) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setCode("widgets.refinePanel.numerical.filter");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(70,8) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setVar("filterButton");
      int[] _jspx_push_body_count_i18n_005fmessage_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f0 = _jspx_th_i18n_005fmessage_005f0.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f0);
      _jspx_th_i18n_005fmessage_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f0, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f1 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f1_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f1.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(71,8) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setCode("widgets.refinePanel.numerical.exclude");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(71,8) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setVar("excludeButton");
      int[] _jspx_push_body_count_i18n_005fmessage_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f1 = _jspx_th_i18n_005fmessage_005f1.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f1);
      _jspx_th_i18n_005fmessage_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f1, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f0_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(89,1) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f0.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f0 = _jspx_th_render_005frenderScript_005f0.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f0);
          }
          do {
            out.write("\r\n");
            out.write("		var options = {};\r\n");
            out.write("		options.uCssId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		options.facet = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		options.min = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${min}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		options.max = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${max}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		options.step = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${step}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		options.feedList = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedList}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(";\r\n");
            out.write("		options.url = '");
            if (_jspx_meth_c_005furl_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("		options.loadFacetParameter = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${loadFacetParameter}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("		var plmaNumericalRefine = new PLMANumericalRefine(options);\r\n");
            out.write("		plmaNumericalRefine.init();\r\n");
            out.write("	");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f0);
      _jspx_th_render_005frenderScript_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f0 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f0_reused = false;
    try {
      _jspx_th_c_005furl_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/numericalFilter.jsp(97,17) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f0.setValue("/data/range/facet");
      int _jspx_eval_c_005furl_005f0 = _jspx_th_c_005furl_005f0.doStartTag();
      if (_jspx_th_c_005furl_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f0);
      _jspx_th_c_005furl_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f0, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f0_reused);
    }
    return false;
  }
}
