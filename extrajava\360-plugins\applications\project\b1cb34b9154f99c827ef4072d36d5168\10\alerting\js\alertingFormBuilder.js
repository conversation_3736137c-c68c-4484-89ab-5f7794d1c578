function AlertingFormBuilder () {

	var url = new BuildUrl(window.location.href);
	var pageId = url.getParameter('pageId');
	pageId = pageId && pageId[0];

	var data = {
		title: '',
		condition: '',
		thresholds: [],
		// no date is ''
		startDate: '',
		endDate: '',
		subscriptions: [],
		facets: [],
		series: [],
		seriesAndFacets: [],
		type: AlertingForm.TYPE_CREATE,
		pageName: window.mashup.pageName,
		pageId: pageId,
		chartID: null,
		id: null,
		enabled: true,
		owner: {}
	};

	var options = {
		data: data,
		onSubmitSuccess: $.noop,
		onSubmitFail: $.noop,
		onCancel: $.noop
	};

	return {
		withTitle: function (title) {
			data.title = title;
			return this;
		},

		withCondition: function (condition) {
			data.condition = condition;
			return this;
		},

		withThreshold: function (threshold) {
			data.thresholds.push(threshold);
			return this;
		},

		withThresholds: function (thresholds) {
			data.thresholds = thresholds;
			return this;
		},

		withStartDate: function (startDate) {
			data.startDate = startDate;
			return this;
		},

		withEndDate: function (endDate) {
			data.endDate = endDate;
			return this;
		},

		withSubscription: function (subscription) {
			data.subscriptions.push(subscription);
			return this;
		},

		withSubscriptions: function (subscriptions) {
			data.subscriptions = subscriptions;
			return this;
		},

		withFacets: function (facets) {
			data.facets = facets;
			return this;
		},

        withSeriesAndFacets: function(seriesAndFacets){
		    data.seriesAndFacets = seriesAndFacets;
		    return this;
        },

		withSeries: function (series) {
			data.series = series;
			return this;
		},

		/**
 		 * @param {string} type - Either AlertingForm.TYPE_CREATE or AlertingForm.TYPE_EDIT
		 */
		withType: function (type) {
			data.type = type;
			return this;
		},

		withPageName: function (pageName) {
			data.pageName = pageName;
			return this;
		},

		withPageId: function (pageId) {
			data.pageId = pageId;
			return this;
		},

		withChartId: function (chartId) {
			data.chartID = chartId;
			return this;
		},

		withOnSubmitSuccess: function (onSubmitSuccess) {
			options.onSubmitSuccess = onSubmitSuccess;
			return this;
		},

		withOnSubmitFail: function (onSubmitFail) {
			options.onSubmitFail = onSubmitFail;
			return this;
		},

		withOnCancel: function (onCancel) {
			options.onCancel = onCancel;
			return this;
		},

		withId: function (id) {
			data.id = id;
			return this;
		},

		withEnabled: function (enabled) {
			data.enabled = enabled;
			return this;
		},

		withOwner: function (owner) {
			data.owner = owner;
			return this;
		},

		withUser: function (user) {
			options.user = user;
			return this;
		},

		build: function () {
			return new AlertingForm(options);
		}
	}
}