<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>


<render:import varWidget="widget" varFeeds="feeds"/>

<config:getOption var="dbKey" name="dbKey" defaultValue="" />
<config:getOption var="savedBookmarksWuid" name="savedBookmarksWuid" />
<config:getOptions var="additionalParameters" name="additionalParameters" defaultValue="" />

<widget:widget varCssId="cssId" varUcssId="uCssId" extraCss="addbookmarks">
        <span class="${widget.wuid} addbookmarks-star fonticon fonticon-star-empty">
        </span>
        <c:set var="popupContent">
            <div class="bookmark_div">
                <h3>Bookmark</h3>
                <div class="name_div">
                    <label>Name:</label>
                    <input class="bm_name" type="text">
                </div>
                <div class="desc_div">
                    <label>Description:</label>
                    <input class="bm_desc" type="text">
                </div>
                <span class="empty_span">The field name cannot be empty !</span>
                <div class="buttons_div">
                    <button class="edit_bm btn active-btn">Edit existing</button>
                    <button class="remove_bm btn active-btn">Remove</button>
                    <button class="add_bm btn active-btn">Add</button>
                </div>
            </div>
        </c:set>
</widget:widget>


<render:renderScript position="READY">
    var filtersList = [];
    var additionalParamsList = [];
    var filter = null;
    var categoryTitle;

    <search:forEachFacet var="facet" feeds="${feeds}">
        <search:getFacetType var="facetType" facet="${facet}"/>
        <c:if test="${(facetType != 'H2D' && facetType != 'MULTI')}">
            <search:forEachCategory root="${facet}" var="category" showState="REFINED" iterationMode="ALL">
                <plma:getCategoryLabel var="categoryTitle" category="${category}" feeds="${feeds}"/>
                filter = {
                    facetId : '${facet.id}', 
                	facetPath 
                	: '${facet.path}', 
                	facetType : '${facetType}', 
                	categoryPath : '${category.path}', 
                	showState : 'REFINED', 
                	categoryTitle: '<string:escape value="${categoryTitle}" escapeType="JAVASCRIPT"/>'};
                filtersList.push(filter);
            </search:forEachCategory>
        </c:if>
    </search:forEachFacet>

    <search:forEachFacet var="facet" feeds="${feeds}">
        <search:getFacetType var="facetType" facet="${facet}"/>
        <c:if test="${(facetType != 'H2D' && facetType != 'MULTI')}">
            <search:forEachCategory root="${facet}" var="category" showState="EXCLUDED" iterationMode="ALL">
                <plma:getCategoryLabel var="categoryTitle" category="${category}" feeds="${feeds}"/>
                filter = {
                    facetId : '${facet.id}',
                	facetPath : '${facet.path}',
                	facetType : '${facetType}',
                	categoryPath : '${category.path}',
                	showState : 'EXCLUDED',
                	categoryTitle: '<string:escape value="${categoryTitle}" escapeType="JAVASCRIPT"/>'};
                filtersList.push(filter);
            </search:forEachCategory>
        </c:if>
    </search:forEachFacet>

   
    <c:forEach items="${additionalParameters}" var="paramName">
         additionalParamsList.push('${paramName}');
    </c:forEach>


    $('#${cssId}').addbookmarks(
    	'${widget.wuid}',
		{ 
	        storageKey: '${dbKey}', 
	        scope: 'user', 
	        selector: '.${widget.wuid}', 
	        popupContent: '<string:escape value="${popupContent}" escapeType="JAVASCRIPT"/>',
	        action: '', 
	        savedBookmarksWuid:  '${savedBookmarksWuid}', 
	        filtersList : filtersList,
	        additionalParameters : additionalParamsList
        }
    );
</render:renderScript>
    