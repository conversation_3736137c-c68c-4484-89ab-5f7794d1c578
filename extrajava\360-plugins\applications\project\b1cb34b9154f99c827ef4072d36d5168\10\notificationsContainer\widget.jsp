<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import varWidget="widget"/>

<widget:widget extraCss="alert-notifications-container" varCssId="cssId" varUcssId="uCssId">
	<div class="widget-title"><config:getOption name="title" defaultValue=""/></div>
	<div class="widget-container">
		<h3 class="no-notification hidden"><i18n:message code="notification.empty" /></h3>
		<div class="list-container">
		</div>
	</div>
</widget:widget>

<render:renderScript position="READY">
	var options = {};
	options.user = '<string:eval string="\${security.username}" isJsEscape="true" feeds="${feeds}"/>';
	var notificationsContainer = new NotificationsContainer('${uCssId}', options);
</render:renderScript>

<render:renderOnce id="${widget.id}">
	<script id="notification-wrapper-template" type="text/x-lodash-template">
		<div class="notification-wrapper {{ seen ? 'seen' : 'unseen' }}">
			<div class="content"><div class="icon fonticon fonticon-bell-alt"></div> <i18n:message code="notification.content" /></div>
			<div class="delete-notification icon fonticon fonticon-wrong"></div>
		</div>
	</script>
</render:renderOnce>