var CompareHit = function (options) {
    this.options = options;
    this.countSelector = '.' + this.options.collection + '-counter';
    this.countAwareSelector = '.' + this.options.collection + '-count-aware';
    this.statusSelector = '.' + this.options.collection + '-header-status';
    this.init();
    return this;
};

CompareHit.prototype.init = function () {
    // Different divs can contain a collection button --> so, disable click event for all and re-init for all
    var escapedID = $.escapeSelector(this.options.hitID);
    var selectorId = `.hit-action-compare[data-id="${escapedID}"]`;
    $(selectorId).off('click').on('click', $.proxy(function (e) {
        // Disable parent div event
        e.stopPropagation();
        // Get hit ID to store in 'dataset-id' attribute
        var id = $(e.target).data('id');
        // Get favorite button state in 'dataset-status' attribute
        var status = $(e.target).data('status');

        var toggleClasses = 'fonticon-compare-on fonticon-compare-off';
        var collectionCount = 0;
        // If doc is in collection --> remove, else, add it
        if (status == 'on') {
            $.ajax({
                method: 'DELETE',
                url: this.options.url + '/delete/' + this.options.collection + '/' + encodeURIComponent(id),
                dataType: 'JSON',
                async: false,
                context: this,
                success: function (data) {
                    $(selectorId).toggleClass(toggleClasses);
                    $(selectorId).data('status', 'off');
                    collectionCount = data.answer.length;
                    if (this.options.callbackFunction) {
                        this.options.callbackFunction.call(this, data);
                    }
                },
                error: function (data) {
                    $.notify('Error removing hit from collection', "error");
                }
            });
        } else {
            $.ajax({
                method: 'POST',
                url: this.options.url + '/add',
                dataType: 'JSON',
                async: false,
                context: this,
                data: {
                    collection: this.options.collection,
                    id: id
                },
                success: function (data) {
                    $(selectorId).toggleClass(toggleClasses);
                    $(selectorId).data('status', 'on');
                    collectionCount = data.answer.length;
                    if (this.options.callbackFunction) {
                        this.options.callbackFunction.call(this, data);
                    }
                },
                error: function (data) {
                    $.notify('Error adding hit to collection (' + data.responseJSON.error + ')', "error");
                }
            });
        }
        this.refreshCount(collectionCount);
    }, this));
}

/**
 * Refresh div displaying collection count
 */
CompareHit.prototype.refreshCount = function (collectionCount) {
    $(this.countSelector).text(collectionCount);
    $(this.countAwareSelector).attr('data-count', collectionCount);
    // This JS lib is called by hit action button but minimum size to display header button is configured in header widget config
    // To not configure twice this parameter, let's get it from data HTML attribute
    var minSize = 0;
    var urlParams = new URLSearchParams(window.location.search);
    if ($(this.statusSelector).data('min-size') != '') {
        minSize = parseInt($(this.statusSelector).data('min-size'));
    }
    if ((collectionCount > 0 && $(this.statusSelector).hasClass('fonticon-compare-off')) ||
        (collectionCount == 0 && $(this.statusSelector).hasClass('fonticon-compare-on'))) {
        $(this.statusSelector).toggleClass('fonticon-compare-off fonticon-compare-on');
    }
    // If minimum size is not reached, hide header button, for example, we can consider hiding compare button if less than 2 hits are selected
    if (minSize > 0 && collectionCount < minSize) {
        $(this.statusSelector).addClass('hidden');
        urlParams.has('displayView', 'compare') ?  window.location.href = window.location.origin + window.location.pathname : '';
    } else if ($(this.statusSelector).hasClass('hidden')) {
        $(this.statusSelector).removeClass('hidden');
    }else if (urlParams.has('displayView', 'compare')){
        window.location.reload();
    }
}