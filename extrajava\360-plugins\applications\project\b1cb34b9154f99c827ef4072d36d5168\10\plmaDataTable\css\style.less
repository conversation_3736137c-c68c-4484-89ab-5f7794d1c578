@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/polyfills.less";

.mashup.mashup-style {

	.plmaDataTable {
		position: relative;

		&.full-size-active {
			position: fixed;
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
			padding: 0;
			z-index: 2000;
			background-color: rgba(0, 0, 0, 0.3);
		}

		/* TODO find a better way to fix this : when inside a Chartboard, the header is squeezed */

		.widgetHeader {
			.flex(0 0 auto);
		}

		.widgetContent {
			overflow: auto;
			position: relative;
			.flex-grow(2);
			.flex-basis(0);

		}

		&.withLinks td {
			cursor: pointer;
		}

		.menu-popup {
			background: @cblock-bg;
			margin-top: 0px;
			border: none;
			border-radius: 0;
			.box-shadow(0 1px 6px 0px @ctext-weak);
			position: absolute;
			top: 40px;
			right: 10px;
			z-index: 11;

			.appspopup-arrow {
				display: none;
			}
		}

		.widgetContent.table-wrapper {
			position: relative;
		}

		.plmadatatable-menu-container {
			text-align: left;
			&.toolbar-mode{
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-end;
                width: 205px;
				
				.widget-menu-item,
                .plmadatatable-menu-item{
                    padding-right: 0px;
                    .label{
                        display: none;
                    }
                }
			}
		}

		.plmadatatable-menu-item {
			cursor: pointer;
			font-family: Arial;
			color: @ctext;
			font-weight: normal;
			line-height: (3 * @line-height);
			border-bottom: 1px solid @cblock-border;
			padding-right: @line-height;
			white-space: nowrap;

			i {
				color: @ctext-weak;
				font-size: 1.35em;
				width: 44px;
			}

			&:hover {
				background-color: @cblock-bg-alt;
			}

			&.active {
				color: @clink-active;
			}
		}

		.export-data-form {
			display: none;
		}

		.settings-form {
			.display-flex();
			.flex-flow(row nowrap);
			background: @cblock-bg-alt;

			.tabs {
				border-right: 1px solid @cblock-border;
				padding: (@line-height /2) 0;
			}

			.section-tab {
				padding: (@line-height /2) @line-height;
				color: @clink;
				cursor: pointer;

				&.active {
					background: @cblock-bg;
				}
			}

			.options {
				padding: (@line-height /2) @line-height;
			}

			.section-options {
				padding: (@line-height / 2) @line-height;

				.table {
					display: grid;
                    grid-template-columns: repeat(2,1fr);
                    column-gap: 5px;

					.displayedColumn, .column-config-list {
                        white-space: nowrap;
                    }
					.column-1, .column-2 {
						border: 1px solid #e2e4e3;
					}
				}

				label {
					margin-right: @line-height;
				}

				&.section-fixedRows {
					.fixed-rows-action {
						margin-top: 15px;
						span {
							background-color: white;
							border: 1px solid @ctext-weaker;
							border-radius: 4px;
							cursor: pointer;
							padding: 6px 9px 5px 9px;
							&:hover {
								color: @clink;
								border-color: @clink;
							}
						}
					}
				}
			}

			.close {
				position: absolute;
				top: 0;
				right: 0;
				cursor: pointer;
				margin: (@line-height /2);
			}
		}

		.widgetContent.hidden {
			display: none;
		}

		table {

			tbody tr {
				&.odd {
					background-color: #FFFFFF;

					td.fixedColumn {
						background-color: #F9F9F9;
					}
				}

				&.even {
					background-color: #F2F5F7;

					td.fixedColumn {
						background-color: #F1F1F1;
					}
				}

				&:hover, &.hover {
					background-color: #E2E4E3;
				}

				&.selected {
					background-color: #D5E8F2;
				}
			}

		}

		&.not-paginated {
			.dataTables_info {
				display: none;
			}
		}

		/* YFAIRE implement search in Hits mode */

		&.modeHits {
			.plmadatatable-search {
				display: none;
			}

			.widgetContent .dataTables_filter {
				display: none;
			}

			&.infinite-scroll {
				.widgetContent {
					overflow: hidden;
					.dataTables_wrapper {
						height: 100%;
						display: flex;
						flex-direction: column;
						.dataTables_scroll {
							border-bottom: 1px solid black;
							overflow: hidden;
						}
						.dataTables_info {
							flex: 0 0 20px;
						}
					}
				}
			}
		}


		.dataTables_wrapper {

			.dataTables_length {
				padding-top: 4px;
				margin-right: 28px;
			}

			.dataTables_info {
				clear: none;
			}

			.fix-page-toolbar {
                position: sticky;
                background: white;
                display: inline-block;
                z-index: 3;
                width: 100%;
                &.fix-header {
                    top: 0px;
                }
                &.fix-footer {
                    bottom: 0px;
                }

            }

		}

		.dataTables_filter {
			margin: 0 @line-height (@line-height /2) @line-height;
		}

		.buttons-csv {
			display: none;
		}

		.overlay {
			position: absolute;
			top: 0;
			left: 0;
			height: 100%;
			width: 100%;
			background-color: @cblock-bg;
			opacity: 0.3;
		}
	}

	/* Overriding datatable theme */

	.dataTable {

		th {
			padding: (@line-height /2) (@line-height * 1.5) (@line-height /2) @line-height;
			text-align: left;
		}

		td {
			padding: 2px 3px;
			font-size: @xs-font;

			&.numerical-value {
				font-family: monospace;
			}
		}

		.numerical-value {
			text-align: right;
		}
	}

	div.dts div.dataTables_scrollBody {
		background: none;
	}

	/* Can affect other widgets */

	.relative {
		position: relative;
	}

	.hidden {
		display: none;
	}

	.dataTable.tableClone {
		position: absolute;
		table-layout: fixed;
		background: @cblock-bg;
		border-bottom: none;

		&.columnClone {
			z-index: 1;
		}

		&.headerClone, &.rowsClone {
			z-index: 2;
		}
		&.rowsClone {
			z-index: 2;
			border-bottom: 1px solid #111;
		}

		&.columnsHeaderClone {
			z-index: 3;
		}
	}

	.dataTable:not(.tableClone) {
		tbody:before {
			line-height: 0;
			content: "_";
			color: white;
			display: block;
		}
	}
}
