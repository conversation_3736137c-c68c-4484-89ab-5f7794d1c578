@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/polyfills.less";

.ie {
	.sideBarMenu .search-item-container .search-item-input {
		box-sizing: border-box;
	}
}

.sideBarMenu-hidden() {
	width: 0px;
	min-width: 0px;
	max-width: 0px;
	.display-mode-switch {
		height: @line-height;
		width: @line-height;
		position: absolute;
		top: 0;
		left: 0;
	}
	.display-mode-collapse, .display-mode-expand, .display-mode-hide {
		display: none;
	}
	.display-mode-show {
		display: block;
	}
	.main-items, .secondary-items {
		display: none;
	}
	.item-description {
		display: none;
	}
	.pinned {
		.item-wrapper::after {
			display: none;
		}
	}
}

.sideBarMenu-collapsed() {
	width: (@line-height *2);
	min-width: (@line-height *2);
	max-width: (@line-height *2);

	.transition(@transition-normal, "~min-width");

	.display-mode-collapse, .display-mode-show, .display-mode-hide {
		display: none;
	}
	.display-mode-expand {
		display: block;
	}
	.section-label {
		display: none;
	}
	.item-description {
		display: none;
	}
	.fold-icon {
		display: none;
	}
	.search-item-container {
		display: none;
	}
	.display-mode-edit {
		margin: 8px;
		display: none;
	}
	.display-mode-switch {
		width: 100%;
		padding: 0;
		height: 30px;
		.display-mode-expand {
			margin: 5px;
			font-size: 16px;
		}
	}
	.item-menu-container {
		flex: 1 0 0px;
		overflow: auto;
		&.edit-active {
			.new-section-input-container {
				display: none;
			}
			.user-items,.main-items,.shared-items {
				.menu-item {
					margin: 0;
					background-color: @cblock-border;
					border-radius: 0;
					&.active {
						background-color: #f1f1f1;
					}
				}
			}
			.drop-section {
				display: none;
			}
			.items.user-items-custom {
				.section-separation.user-section {
					display: none;
				}
			}
		}
		.items {
			.section-separation {
				display: none;
			}
		}
	}
	.menu-item {
		.item-wrapper {
			padding: 0 0 0 (@line-height / 2);
			line-height: 25px;
		}
		.item-icon {
			margin: 0;
		}
		.item-label {
			display: none;
		}
		.icon-shared {
			display: none;
		}
		.icon-favorite {
			display: none;
		}
		.drag-drop-icon {
			display: none;
		}
		&.indent-0 {
			padding-left: 0;
		}
		&.indent-1 {
			padding-left: 0;
		}
		&.indent-2 {
			padding-left: 0;
		}
		&.indent-3 {
			padding-left: 0;
		}
		&.indent-4 {
			padding-left: 0;
		}
		&.indent-5 {
			padding-left: 0;
		}
		&.indent-6 {
			padding-left: 0;
		}
		&.indent-7 {
			padding-left: 0;
		}
		&.indent-8 {
			padding-left: 0;
		}
	}
	.secondary-items {
		flex-direction: column;
		flex: 0 0 90px;
		.item-wrapper {
			padding: 3px;
			.item-icon {
				font-size: 20px;
			}
		}
	}
	.pinned {
		.item-wrapper::after {
			display: none;
		}
	}
}

.sideBarMenu-expanded() {
	width: auto;
	min-width: 210px;
	max-width: 210px;

	.transition(@transition-normal, "~max-width");
	.transition(@transition-normal, "~min-width");

	.display-mode-switch {
		width: auto;
		position: initial;
		display: inline-block;
		font-size: 13px;
		line-height: 9px;
		height: auto;
		.display-mode-collapse {
			font-size: 16px;
		}
	}
	.display-mode-expand, .display-mode-show {
		display: none;
	}
	.display-mode-collapse, .display-mode-hide {
		display: block;
	}
	.main-items, .secondary-items {
		//		display: block;
	}
	.section-label {
		display: block;
	}
	//.item-description {
	//	display: block;
	//}
	.fold-icon {
		display: inline;
	}
	.menu-item {
		.item-wrapper {
			padding: 0 0 0 @line-height;
			line-height: 25px;
		}
		.item-icon {
			margin: 0 (@line-height / 2) 0 0;
		}
		.item-label {
			display: inline-block;
		}
		&.indent-0 {
			padding-left: 0;
		}
		&.indent-1 {
			padding-left: 1 * @line-height;
		}
		&.indent-2 {
			padding-left: 2 * @line-height;
		}
		&.indent-3 {
			padding-left: 3 * @line-height;
		}
		&.indent-4 {
			padding-left: 4 * @line-height;
		}
		&.indent-5 {
			padding-left: 5 * @line-height;
		}
		&.indent-6 {
			padding-left: 6 * @line-height;
		}
		&.indent-7 {
			padding-left: 7 * @line-height;
		}
		&.indent-8 {
			padding-left: 8 * @line-height;
		}
	}

	.menu-item {
		.item-wrapper {
			position: relative;
			display: flex;
			.item-label {
				flex: 1 0 0px;
			}
			.icon-shared {
				color: transparent;
			}
		}
		&.pinned {
			.item-wrapper {
				.icon-favorite {
					color: #f1b71a;
				}
			}
		}
		&.isShared {
			.item-wrapper {
				.icon-shared {
					color: @ctext-weak;
				}
			}
		}
	}
	.item-menu-container {
		flex: 1 0 0px;
		overflow: auto;
		&.edit-active {
			.drop-section {
				margin: 14px 10px 14px 14px;
				border: 2px dashed @ctext-weak;
				border-radius: 3px;
				display: flex;
				flex-direction: column;
				&.drop-active {
					color: @clink-active;
					border-color: @clink-active;
				}
				.label {
					align-self: center;
					margin: 7px;
					font-size: 15px;
				}
				.drop-icon {
					margin: 7px;
					font-size: 20px;
				}
			}
			.items {
				&.user-items, &.shared-items {
					.menu-item {
						.item-wrapper {
							cursor: default;
						}
						&:hover {
							color: @ctext;
							.item-wrapper {
								color: @ctext;
							}
						}
						.drag-drop-icon {
							cursor: grab;
							font-size: 20px;
							position: relative;
							top: 5px;
							margin: 0;
						}

					}
				}
				.editable-item {
					.item-wrapper {
						.item-label {
							cursor: text;
							max-width: 115px;
							&:focus {
                                text-overflow: initial;
                            }
						}
					}
					.item-icon {
						cursor: pointer;
						&:hover {
							color: @clink;
						}
						&.active {
							color: @clink;
						}
					}
				}
				.user-section {
					&.hidden {
						display: block;
					}
					&.section-hidden {
						border-bottom: none;
					}
				}
				.section-separation {
					&.section-hidden {
						border-bottom: none;
					}
				}
				.menu-item {
					a {
						width: 100%;
					}
					&.hidden {
						display: flex;
						max-height: none;
					}
				}
				.menu-item {
					display: flex;
					margin: 7px 7px 7px 14px;
					background-color: #f1f1f1;
					border-radius: 4px;
					color: @ctext;
					a {
						flex: 1 0 0px;
						.item-wrapper {
							padding: 0 0 0 7px;
						}
					}
				}
			}
		}
	}
	.secondary-items {
		.menu-item {
			.item-label {
				width: 50px;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}
	}
}

.sideBarMenu {
	background-color: @cblock-border;
	border: 1px solid @cblock-border-alt;
	box-sizing: border-box;
	.display-flex();
	.flex-direction(column);
	.flex-wrap(nowrap);
	justify-content: flex-start;
	overflow-x: hidden;
	.search-item-container {
		position: relative;
		display: inline-block;
		margin: 0 0 0 @line-height;
		.search-item-input {
			height: 22px;
			border-radius: 4px;
			border: 1px solid @cblock-border-alt;
			line-height: 22px;
			padding: 0 20px 0 5px;
			width: 100%;
			color: @ctext;
			box-sizing: border-box;
		}
		i {
			position: absolute;
			right: 0;
			top: 5px;
			font-size: 15px;
			&.no-edit {
				right: 26px;
			}
		}
	}
	.side-bar-menu-header {
		display: flex;
		margin-top: 7px;
		.search-item-container {
			flex: 1 0 0px;
		}
		.display-mode-edit {
			display: inline-block;
			font-size: 16px;
			cursor: pointer;
			align-self: center;
			&:hover {
				color: @clink;
			}
			&.active {
				color: @clink;
			}
		}
		.display-mode-switch {
			align-self: center;
		}
	}
	.display-mode-switch {
		height: @line-height;
		line-height: @line-height;
		padding-right: (@line-height / 4);
		font-size: @xs-font;
		color: @clink;
		display: inline-block;
		.transition(@transition-quick, ~"font-size");
		> div {
			width: 100%;
			float: right;
			margin: 0px 4px;
			text-align: right;
			cursor: pointer;
		}
		&:hover {
			font-size: @m-font;
			color: @clink-hover;
			.transition(@transition-quick, ~"font-size");
		}
	}

	.main-items, .secondary-items {
		margin: (@line-height/2) 0;
	}

	.main-items {
		.flex-grow(5);
	}

	.item-menu-container {
		.menu-item {
			&.hidden {
				display: none;
			}
		}

	}

	.secondary-items {
		flex: 0 0 30px;
		.display-flex();
		.flex-wrap(nowrap);
		.justify-content(center);
		.align-items(flex-end);
		.menu-item {
			.flex-grow(1);
			.item-wrapper {
				.display-flex();
				.justify-content(center);
				.item-icon {
					font-size: 25px;
					overflow: visible;
				}
			}
			.item-label {
				display: none;
			}
		}
		.section-separation {
			display: none;
		}
	}

	.menu-item {
		font-size: @m-font;
		color: @clink;
		white-space: nowrap;
		-webkit-transition: max-height 1s;
		-moz-transition: max-height 1s;
		-ms-transition: max-height 1s;
		-o-transition: max-height 1s;
		transition: max-height 1s;
		max-height: 300px;

		a {
			display: block;
		}

		.item-wrapper {
			cursor: pointer;
			color: @ctext;
			.fold-icon {
				float: right;
				font-size: 20px;
				&.hidden {
					display: none;
				}
			}
		}

		&:hover, &:focus {

			.item-wrapper {
				color: @clink-hover;
			}
			a {
				text-decoration: none;
			}
		}
		&.active {
			background-color: @cblock-bg;
			.item-wrapper {
				color: @clink-active;
			}
			&.active-folding {
				background-color: @cblock;
				.item-wrapper {
					color: @ctext-inverted;
				}
				&:hover {
					background-color: @cblock-bg-alt;

					.item-wrapper {
						color: @ctext;
					}
					a {
						text-decoration: none;
					}
				}
			}
		}
		&.hidden {
			max-height: 0;
			overflow: hidden;
			-webkit-transition: max-height 0.5s;
			-moz-transition: max-height 0.5s;
			-ms-transition: max-height 0.5s;
			-o-transition: max-height 0.5s;
			transition: max-height 0.5s;
		}
	}

	.user-item-template {
		display: none;
	}

	.item-icon, .item-label {
		display: inline-block;
		max-width: 150px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.item-icon {
		font-size: @m-font;
	}

	.section-separation {
		color: @clink-active;
		font-size: @m-font;
		font-weight: bold;
		margin: @line-height (@line-height / 2) (@line-height / 4) (@line-height / 2);
		padding: (@line-height / 4) (@line-height / 2);
		white-space: nowrap;
		cursor: pointer;
		&.section-hidden {
			border-bottom: 2px solid @clink-active;
		}
		&.hidden {
			display: none;
		}
	}

	.item-description-container{
		height: 100px;
		overflow: auto;
		margin: 7px 0;
	}

	.item-description {
		position: relative;
		padding: (@line-height / 2) @line-height;
		margin-bottom: (4 * @line-height);
		//opacity: 1;
		.transition(@transition-normal, ~"opacity");

		.description-title {
			font-size: @m-font;
			font-weight: bold;
			padding: (@line-height / 2) 0;
			color: @ctext-bold;
			max-width: 150px;
		}
		.description-text {
			position: absolute;
			color: @ctext;
			font-size: @s-font;
			margin-right: (@line-height /2);
		}
		&.hidden {
			display: none;
			//opacity: 0;
			//.transition(@transition-long, ~"opacity");
		}
		&.transitive {
			display: none;
			//opacity: 0;
			//.transition(@transition-quick, ~"opacity");
		}
	}

	&.hidden {
		.sideBarMenu-hidden();
	}

	&.collapsed {
		.sideBarMenu-collapsed();
	}

	&.expanded {
		.sideBarMenu-expanded();
	}
	
	.icon-tooltip {
		display: flex;
		flex-direction: column;
		align-items: stretch;

		padding: 5px;

		.fonticon-container {
			margin-top: 10px;
			max-height: 200px;
			width: 100%;
			overflow-y: scroll;

			display: flex;
			flex-wrap: wrap;

			> span {
				margin: 5px;
			}
			
			.fonticon:hover {
				color: #368ec4;
				cursor: pointer;
			}
		}
	}

}

@import "responsive.less";
