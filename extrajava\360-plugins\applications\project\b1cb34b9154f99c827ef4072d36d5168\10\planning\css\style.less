@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/styles/colorPreferences.less";
@import "../../plmaResources/css/polyfills.less";

.mashup.mashup-style.modern.theme_3dxp .wuid.searchWidget{
	&.srcTimelinevis {
		.display-flex();
		overflow: hidden;
		flex-direction: column;
		height: 100%;
		.widgetContent{
			.flex(1 0 0);
			flex-grow: 1;
			.display-flex();
			flex-direction: column;
			position: relative;
			.main-widget{
				display: flex;
				flex-grow: 1;
				flex-basis: 0;
				overflow: auto;
				.timeline-vis {
					.flex(3 0 0);
					flex-grow: 3;
					flex-basis: 0;
				}
				.timeline-tooltip {
					.flex(1 0 0);
					overflow: auto;
					height: 100%;
					border-bottom: 1px solid @cblock-border;
					.facetTitle {
						margin: 7px;
						margin-top: 15px;
						font-size: 16px;
					    font-weight: bold;
					    color: @blue-3;
					    border-bottom: 1px solid @grey-3;
					}
					.elemTooltip {
						margin: 5px;
						.colorTooltip {
							border-radius: 50%;
							width: 10px;
							height: 10px;
							display: inline-block;
						}
						.catTooltip {
							font-size: 14px;
						}
					}
				}
				.timeline-info {
					.flex(0 0 0);
					flex-basis: 0;
					overflow: hidden;
					.transition(0.5s);
					display: flex;
					flex-flow: column nowrap;
					&.visible {
						.flex(1 0 0);
						flex-basis: 0;
						flex-grow: 1;
						border-bottom: 1px solid @grey-3;
					}
					.info-title {
						display: flex;
						.flex(0 0 auto);
						flex-basis: auto;
						flex-wrap: wrap;
						.title-value {
							font-size: 2em;
							font-weight: bold;
							color: @blue-3;
							position: relative;
							top: 6px;
							margin: 7px;
							.flex(1 0 0);
							word-break: break-word;
							line-height: 20px;
						}
						.close-button {
							cursor: pointer;
							font-size: 20px;
							margin-top: 13px;
						}
						.refine-button {
							cursor: pointer;
							font-size: 20px;
							margin-top: 13px;
						}
					}
					.info-main {
						margin: 15px;
						border-top: 1px solid @grey-3;
						.info-main-data {
							margin-top: 6px;
							font-size: 14px;
							.label {
								color: @grey-5;
							}
						}
					}
					.info-sub-item {
						display: flex;
						flex-grow: 1;
						flex-basis: 0;
						flex-wrap: wrap;
						overflow: auto;
						.info-sub-item-elem {
							border: 1px solid @grey-3;
							overflow: auto;
							min-width: 120px;
							padding: 7px;
							margin: 5px;
							.flex(1 0 0);
							flex-grow: 1;
							cursor: pointer;
							&.hidden {
								display: none;
							}
							&:hover {
								border-color: @blue-2;
							}
							.info-sub-item-title {
								font-size: 17px;
							    font-weight: bold;
							    color: @blue-3;
							    margin-bottom: 10px;
							}
							.info-sub-item-main {
								font-size: 14px;
								.label {
									color: @grey-5;
								}
							}
						}
					}
				}
			}
			.timeline-vis {
				.flex(1 0 0);
				flex-grow: 1;
				overflow: hidden;
				&.activeSpinner {
					opacity: 0.2;
				}
				.vis-panel.vis-left {
					width: 110px;
				}
				.vis-item {
					.vis-item-content{
						margin-top: 0px;
					}
					&.vis-estimated-actual {
						&.selected {
							.vis-background {
								&.actual {
									
										background-color: transparent !important;
										border: 2px solid @red-1;
								}
								&.subElem {
									&.selected {
										background-color: white !important;
										border: 2px solid @red-1;
									}
								}
							}
						}
					}
					&.subElem {
						width: 15px !important;
						height: 15px !important;
						top: 35px !important;
						background-color: black;
						border: 1px solid @blue-4;
						z-index: 5;
						&:before {
							content:'';
							height: 10px;
							width: 2px;
							background-color: @blue-4;
							position: absolute;
							top: -11px;
							left: 6px;
						}
						&.circle {
							border-radius: 10px;
						}
						&.diamond {
							width: 0 !important;
							height: 0 !important;
							background-color: transparent;
							border: 8px solid transparent;
							border-bottom: 12px solid black;
							position: relative;
							top: -8px !important;
							&:after {
								content: '';
								position: absolute;
								left: -20px; top: 12px;
								width: 0;
								height: 0;
								border: 8px solid transparent;
								border-top: 12px solid black;
							}
						}
						&.triangle {
							width: 0 !important;
							height: 0 !important;
							background-color: transparent;
							border-left: 8px solid transparent;
							border-right: 8px solid transparent;
							border-bottom: 15px solid black;
						}
					}
				}
			}
		}
		.timelineFooter {
			height: 35px;
			.buttonsContainer {
				display: inline-block;
			    float: right;
			    margin: 5px;
				.actionButton {
					font-size: 15px;
				    border: 1px solid @cblock-border;
				    padding: 3px;
				    cursor: pointer;
				}
			}
			.searchContainer {
				display: inline-block;
				height: 35px;
   		 		line-height: 20px;
				.searchTimelineButton {
					font-size: 20px;
					position: relative;
					top: 6px;
					cursor: pointer;
				}
				.searchTimelineInput {
					border: 1px solid @cblock-border;
				    border-radius: 4px;
				    color: #555;
				    height: 21px;
				    line-height: 21px;
				    vertical-align: middle;
				    margin: 5px 2px 5px 5px;
				    padding-left: 5px;
				    width: 200px;
				    &.hidden {
				    	display: none;
				    }
				}
				&.no-result {
					color: #EA4F37;
					.searchTimelineInput {
						color: #EA4F37;
					}
				}
			}
			.tooltip-container {
				display: inline-block;
    			height: 35px;
    			line-height: 20px;
    			.tooltip-button {
    			    font-size: 20px;
				    position: relative;
				    top: 6px;
				    cursor: pointer;
    			}
			}
			.loadAllData {
				display: inline-block;
				height: 35px;
				line-height: 20px;
				.loadAllDataButton {
					font-size: 20px;
					position: relative;
					top: 6px;
					cursor: pointer;
				}
			}
		}
		.loader {
		    border: 16px solid #f3f3f3; /* Light grey */
		    border-top: 16px solid #3498db; /* Blue */
		    border-radius: 50%;
		    width: 60px;
		    height: 60px;
		    animation: spin 2s linear infinite;
		    position: absolute;
		    left: 40%;
		    top: 33%;
		    z-index: 100;
		    display: none;
		    &.activeSpinner {
		    	display: block;
		    }
		}

		.widgetContent {
			position: relative;
		}
		
		@keyframes spin {
		    0% { transform: rotate(0deg); }
		    100% { transform: rotate(360deg); }
		}
		
		.loading-spinner{
			display: none;
		}
	}
}

.mashup .searchWidget.srcTimelinevis {
	&.full-size-active {
		position: fixed;
	    width: 100%;
	    height: 100% !important;
	    top: 0;
	    left: 0;
	    z-index: 20000;
	    .display-flex();
	    flex-direction: column;
	    padding: 50px;
	    background-color: rgba(0, 0, 0, 0.3);
	    .widgetHeader {
	    	flex-basis: 20px;
	    }
	    .widgetContent {
	    	flex: 1;
	    	padding: 0;
	    	.vis-timeline {
	    		height: 100% !important;
	    	}
	    }
	}
}