<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Header buttons decorator" group="PLM Analytics/Decoration" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>Adds buttons to a widget header.</Description>
	
	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/widgetHeaderButton.js" />
	</Includes>

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportWidgetsId arity="ONE" />

	<OptionsGroup name="Buttons">
		<OptionComposite name="Buttons" id="buttons" arity="ZERO_OR_MANY">
			<Option name="Icon CSS" id="css">
				<Description>CSS class(es) to define the button icon.</Description>
			</Option>
			<Option name="Tooltip" id="tooltip" isEvaluated="true">
				<Description>A short text is displayed when the user hovers over the button.</Description>
			</Option>
			<Option name="On init" id="onInit" isEvaluated="true">
				<Description>Javascript code executed to initialize the button's behavior.</Description>
				<Placeholder>function(){ }</Placeholder>
				<Values>
					<Value>function(button, widget, data){ data.myProperty = "hello"; }</Value>
				</Values>
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>
			<Option name="On click" id="onClick" isEvaluated="true">
				<Description>Javascript code executed when the button is clicked.</Description>
				<Placeholder>function(){ }</Placeholder>
				<Values>
					<Value>function(button, widget, data){ console.log(data.myProperty); }</Value>
				</Values>
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>
					
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="wrap">false</DefaultValue>
	</DefaultValues>
</Widget>
