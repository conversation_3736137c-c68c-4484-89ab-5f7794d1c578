/**
 * Behaviours shared by all widgets in PLMA applications
 */

/**
 * Truncating widget titles when there is not enough room
 */
// (function () {
//     function truncateWidgetTitle(widget) {
//         var untruncatedText = widget.data('untruncatedText');
//         if (untruncatedText === undefined) {
//             untruncatedText = widget.text();
//             widget.data('untruncatedText', untruncatedText);
//         } else {
//             widget.text(untruncatedText)
//         }
//         if (typeof truncate === "function") {
//             widget.truncate();
//         } else {
//             if (typeof console !== "undefined" && console.warn !== undefined) {
//                 console.warn("The method truncate is not available");
//             }
//         }
//         if (!widget.attr('title')) {
//             widget.attr('title', untruncatedText);
//         }
//     };
//
//     $(window).on('resize', function () {
//         echo('truncate-widget-titles', 200, function () {
//             $('.searchWidget > .widgetHeader > .widgetTitle').trigger('plma:resize');
//         });
//     });
//     $(document).on('plma:resize', '.widgetTitle', function (e) {
//         var widget = $(e.target);
//         truncateWidgetTitle(widget);
//     });
//
// })();


/* UTILS */

/**
 * Return an ID
 * @type {{newId: (function(): string)}}
 */
// PlmaWidgetUtils = {
//     newId: function () {
//         var S4 = function () {
//             return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
//         };
//         return (S4() + "-" + S4() + "-" + S4() + "-" + S4());
//     }
// };


/** Runs the provided function with the provided arguments
 * after the specified amount of time, unless it is called
 * again with the same id. */
// echo = (function () {
//     var echoes = {};
//
//     return function (id, time, func, args) {
//         if (!echoes.hasOwnProperty(id)) {
//             echoes[id] = 0;
//         }
//         echoes[id]++;
//         setTimeout(function () {
//             if (echoes[id] == 1) {
//                 func.apply(null, args);
//             }
//             echoes[id]--;
//         }, time);
//     }
// })();

/**
 * This methods allows to update HTML content of element calling ajax query to get new content
 *
 * Call example :
 * $('.preference-page-detail').updateHTML('/savedPages/admin/24c279ab-2398-40d1-973a-d9b40cbb1f89', {wuid: 'uyfdsgguiyfsiuysiudsf'})
 *
 * @param pUrl Ajax URL
 * @param pData Ajax data
 */
jQuery.fn.updateHTML = function (pUrl, pData, successCallback, errorCallback) {
    $.ajax({
        url: pUrl,
        type: 'GET',
        data: pData,
        context: this,
        async: false,
        success: function (data) {
            $(this).html(data);
            if (successCallback) {
                successCallback.call();
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            if (errorCallback) {
                errorCallback.call();
            }
        }
    });
};

/**
 * Simply open URL present in "data-url" attribute if present, this method adds event handler
 */
jQuery.fn.addURLEvent = function () {
    if ($(this).data('url')) {
        $(this).on('click', $.proxy(function (e) {
            window.location = $(this).data('url');
        }, this));
    }
}

/**
 * Returns week of year of given timestamp
 *
 * @param timestamp Timestamp
 * @returns {string} Week of year (String representation)
 */
function getWeekOfYear(timestamp) {
    const tsDate = new Date(timestamp);
    const januaryFirst = new Date(tsDate.getFullYear(), 0, 1);
    const daysToNextMonday = (januaryFirst.getDay() === 1) ? 0 :  (7 - januaryFirst.getDay()) % 7;
    const nextMonday = new Date(tsDate.getFullYear(), 0, januaryFirst.getDate() + daysToNextMonday);
    const weekNumber = (tsDate < nextMonday) ? 52 : (tsDate > nextMonday ? Math.ceil((tsDate - nextMonday) / (24 * 3600 * 1000) / 7) : 1);
    return `${tsDate.getFullYear()} week ${weekNumber}`;
}