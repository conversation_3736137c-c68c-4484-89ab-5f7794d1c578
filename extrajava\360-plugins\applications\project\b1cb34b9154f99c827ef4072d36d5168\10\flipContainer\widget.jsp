<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>


<config:getOption name="height" var="height"/>

<config:getOption name="firstButtonIcon" var="firstButtonIcon"/>
<config:getOption name="firstButtonTitle" var="firstButtonTitle"/>
<config:getOption name="firstButtonCssSelector" var="firstButtonCssSelector" defaultValue=".widgetHeader"/>
<config:getOption name="firstButtonCssType" var="firstButtonCssType" defaultValue="true"/>
<config:getOption name="secondButtonIcon" var="secondButtonIcon"/>
<config:getOption name="secondButtonTitle" var="secondButtonTitle"/>
<config:getOption name="secondButtonCssSelector" var="secondButtonCssSelector" defaultValue=".widgetHeader"/>
<config:getOption name="secondButtonCssType" var="secondButtonCssType" defaultValue="true"/>
<config:getOption name="enableFullScreen" var="enableFullScreen" defaultValue="${false}"/>
<config:getOption name="activateAsynch" var="activateAsynch"/>
<config:getOptionsComposite name="additionalParams" var="additionalParams" mapIndex="true"/>

<config:getOption var="minwidth" name="minwidth"/>
<c:if test="${minwidth != null}"><c:set var="minwidth" value="min-width:${minwidth}px;"/></c:if>
<c:set var="wuidSecondWidget" value=""/>

<widget:widget extraCss="flipContainer" varUcssId="uCssId" disableStyles="true" extraStyles=" ${minwidth}">
	<widget:content extraCss="content ${not empty height?'fixedHeight':''}"
					extraStyles="${not empty height?'height:':''}${height}${not empty height?'px':''}">
		<div class="content-wrapper">
			<c:choose>
				<c:when test="${activateAsynch == 'true'}">
					<widget:forEachSubWidget iteration="1">
						<render:widget/>
					</widget:forEachSubWidget>
					<widget:forEachSubWidget begin="1" iteration="1">
						<widget:getUcssId var="wuidSecondWidget"/>
						<div class="subWidgetHidden wuid ${wuidSecondWidget}"></div>
					</widget:forEachSubWidget>
				</c:when>
				<c:otherwise>
					<render:subWidgets/>
				</c:otherwise>
			</c:choose>
		</div>
	</widget:content>

	<render:renderScript position="READY">
		(function() {
		function addIfNotPresent(obj, key, value) {
		if (obj.hasOwnProperty(key)) {
		obj[key].push(value);
		} else {
		obj[key] = [value];
		}
		};

		var options = {};
		options.firstButtonIcon = '${firstButtonIcon}';
		options.firstButtonTitle = '${firstButtonTitle}';
		options.firstButtonCssSelector = '${firstButtonCssSelector}';
		options.firstButtonCssType = '${firstButtonCssType == 'true' ? 'menu' : 'normal'}';
		options.secondButtonIcon = '${secondButtonIcon}';
		options.secondButtonTitle = '${secondButtonTitle}';
		options.secondButtonCssSelector = '${secondButtonCssSelector}';
		options.secondButtonCssType = '${secondButtonCssType == 'true' ? 'menu' : 'normal'}';
		options.enableFullScreen = ${enableFullScreen == true};
		options.secondWidgetWuid = '${wuidSecondWidget}';
		options.ajaxParams = {};
		<c:forEach items="${additionalParams}" var="additionalParam">
			addIfNotPresent(options.ajaxParams, '${additionalParam.paramName}', '${additionalParam.paramValue}');
		</c:forEach>
		new Flip('${uCssId}',options);
		})();
	</render:renderScript>

</widget:widget>
