/*
 * Create tab in the global preferences object.
 * pass { prefObject } in options to immediately add the tab.
 * else it will wait for the window.Preferences.initCallBacks.
 */
var PlmaResultListPrefTab = function(options) {
    this.rlConfigList = [];
    this.options = $.extend({},options);

    this.$tab =
        $('<div class="preference-tab preference-resultlist-tab">' +
            '<span class="tab-icon fonticon fonticon-list "></span>' +
            '<span class="label">' + this.getMessage('plma.resultlist.tab.label') + '</span>' +
        '</div>');
    this.$tabContainer =
        $('<div class="preference-container preference-resultlist-container">' +
            '<div class="preference-title">' +
                '<div class="main">' +
                    '<span>' + this.getMessage('plma.resultlist.tab.title') + '</span>' +
                '</div>' +
                '<div class="description">' + this.getMessage('plma.resultlist.tab.description') + '</div>' +
            '</div>' +
            '<div class="preference-block-container">' +
                '<div class="resultlist_list wrapped"></div>' +
                '<div class="resultlist_details active"></div>' +
            '</div>' +
        '</div>');
    this.init();
    return this;
}

PlmaResultListPrefTab.prototype.init = function(){
	if(this.options.prefObject){
		this.displayResultListConfigs(this.options.prefObject);
	}else if(window.Preferences) {
		window.Preferences.getInitCallbacks()
			.add(function (preferences) {
				if(!preferences.hasTab('resultlist')){
					this.displayResultListConfigs(preferences);
				}
			}.bind(this))
	}
}

PlmaResultListPrefTab.prototype.displayResultListConfigs = function(prefObject){
	prefObject.addTab({
		id: 'resultlist',
		onInit: $.noop,
		tab: this.$tab,
		container: this.$tabContainer
	});
	//this.bindEvents(this.$container);
	this.options.appConfig = $.extend(false, {}, {
		configName: prefObject.configName()
	}, this.options.appConfig);

	$.ajax({
		type: 'GET',
		url: mashup.baseUrl + '/config/resultlist/getlist',
		data: {
			confName: this.options.appConfig.configName
		}
	}).then(function(data){
		this.rlConfigList = data.answer;
		this.rlConfigForms = {};
		
		var $ul = $('<ul></ul>');
		this.rlConfigList.forEach(function(rlConfig){
			var $li = 
				$('<li class="resultlist-config-item" data-id="' + rlConfig.id + '">' +
					'<div class="hitHeader">' + 
						'<span class="hit-name">' + rlConfig.label + '</span>' +
						'<span class="description">' + rlConfig.description + '</span>' +
					'</div>' +
				'</li>');
			$ul.append($li);
			$li.on('click', function(e){
				$ul.find('li').removeClass('selected');
				var $el = $(e.target).closest('li');
				this.$tabContainer.find('.resultlist_details .resultlist-preference-form:not(.hidden)').addClass('hidden');
				
				var $form = this.$tabContainer.find('.resultlist_details #form-' + $el.attr('data-id'));
				
				$el.addClass('selected');
				$form.removeClass('hidden');					
			}.bind(this));
			
			var $container = $('<div id="form-' + rlConfig.id + '" class="resultlist-preference-form hidden"></div>');
			this.$tabContainer.find('.resultlist_details').append($container);
			var options = $.extend({}, this.options, {
				showInPreferences: true,
				container: $container,
				appConfig: {
					resultListId: rlConfig.id,
					configName: this.options.appConfig.configName
				}
			});
			this.rlConfigForms[rlConfig.id] = new PlmaResultListPref(rlConfig.id, options);
		}.bind(this));
		this.$tabContainer.find('.resultlist_list').empty().append($ul);
		$ul.find('li:first').trigger('click');
	}.bind(this));
}

/*
 * Create Result list Preference form and manages the modifications.
 */
var PlmaResultListPref = function (uCssId, options, resultListConfig) {
    this.uCssId = uCssId;
    this.options = $.extend({
        groupConfigCssId: this.uCssId + '_sortable',
        showInPreferences: false
    },options);
    this.$widget = uCssId? $('.wuid.' + uCssId) : $();
    this.$elements = {};

    this.resultListConfig = resultListConfig;
    this.defaultAjaxData = {
        //confName: this.options.appConfig.configName,
        id: this.resultListConfig.id
    }

    if(this.options.showInPreferences == false){
        this.$elements.PREF_CONTAINER = $('<div class="resultlist-preference-form"></div>');
        this.initPreferences();
        this.initStandalone();
        this.initButtons(this.$elements.PREF_CONTAINER);
    }else{
        this.$elements.PREF_CONTAINER = this.options.container;
        // this.initPreferences().then(this.initGroups.bind(this));
        this.initPreferences();
        this.initGroups();
        this.initButtons(this.$elements.PREF_CONTAINER);
    }

	// On close , we reload widget and then user session configuration
	$(document).off('click', '.resultlist-preference .plmalightbox-close').on('click', '.resultlist-preference .plmalightbox-close', $.proxy(function (e) {
		var client = new PlmaAjaxClient();
		client.addWidget(this.uCssId);
		client.update();
	}, this));

    return this;
}

PlmaResultListPref.prototype.reset = function () {
	/*
	We get default config and init groups based on this config (but not user session one, it is not saved in database, if preferences are closed,
	it is reinitialized with user session config but if save button is clicked, default config is sent to controller)
	 */
	$.ajax({
		type: 'GET',
		url: mashup.baseUrl + '/config/resultlist/default',
		data: {
			//confName: this.options.appConfig.configName,
			id: this.resultListConfig.id
		},
		success: function (data) {
			this.resultListConfig = data;
			this.initGroups();
		}.bind(this)
	});
}

PlmaResultListPref.prototype.initPreferences = function () {
	this.$elements.PREF_CONTAINER.append($('<div class="header">' +
		'<span class="label"></span>' +
		'<span class="description"></span>' +
	'</div>'));
	
	var $topButtonContainer = $('<div class="button-container button-container-top"></div>');
	var $selectAll = $('<span class="button button-select-all">' + this.getMessage('plma.resultlist.button.selectall') + '</span>');
	var $deselectAll = $('<span class="button button-deselect-all" >' + this.getMessage('plma.resultlist.button.deselectall') + '</span>');
	
	$selectAll.on('click', function(e){
		this.$elements.GROUPS_CONTAINER.find('div.column:not(.edit-disabled)').each(function (i, el) {
			var $column = $(el);
			$column.find('.checkbox').removeClass('fonticon-checkbox-off fonticon-checkbox-on fonticon-stop').addClass('fonticon-checkbox-on');
			$column.attr('data-column-state', 'true');			
		});
		this.setModified();
	}.bind(this));
	$deselectAll.on('click', function(e){
		this.$elements.GROUPS_CONTAINER.find('div.column:not(.edit-disabled)').each(function (i, el) {
			var $column = $(el);
			$column.find('.checkbox').removeClass('fonticon-checkbox-off fonticon-checkbox-on fonticon-stop').addClass('fonticon-checkbox-off');
			$column.attr('data-column-state', 'false');			
		});
		this.setModified();
	}.bind(this));
	
	$topButtonContainer.append($selectAll);
	$topButtonContainer.append($deselectAll);
	
	$topButtonContainer.append($('<span class="legend">' +
		'<span> <i class="icon fonticon ' + PlmaResultListPref.getStateCss('false') + '"></i>' + this.getMessage('plma.resultlist.column.state.false') + '</span>' +
		'<span> <i class="icon fonticon ' + PlmaResultListPref.getStateCss('ifNotEmpty') + '"></i>' + this.getMessage('plma.resultlist.column.state.ifnotempty') + '</span>' +
		'<span> <i class="icon fonticon ' + PlmaResultListPref.getStateCss('true') + '"></i>' + this.getMessage('plma.resultlist.column.state.true') + '</span>' +
	'</span>'));
	
	this.$elements.PREF_CONTAINER.append($topButtonContainer);
	
	this.$elements.GROUPS_CONTAINER = $('<div id="' + this.options.groupConfigCssId +'" class="groups-config"></div>');
	this.$elements.PREF_CONTAINER.append(this.$elements.GROUPS_CONTAINER);
	this.$elements.PREF_CONTAINER.find('.header .label').text(this.resultListConfig.label);
	this.$elements.PREF_CONTAINER.find('.header .description').text(this.resultListConfig.description);
}

PlmaResultListPref.prototype.initStandalone = function () {	
	this.$elements.LIGHTBOX = new PLMALightbox({
		title: this.getMessage('plma.resultlist.tab.title'),
		content: this.$elements.PREF_CONTAINER,
		extraCss: 'resultlist-preference ' + this.uCssId + '_resultlist',
		onShow: (function(){
			this.initGroups();
		}).bind(this),
		onHide: (function(){

		}).bind(this),
		draggable : false
	});	
	// Enable lightbox show.
	this.$widget.find(this.options.buttonSelector).on('click', function (e) {
		this.$elements.LIGHTBOX.show();
	}.bind(this));		
}

PlmaResultListPref.getNextState = function (state) {
	/*State Order : false, ifNotEmpty, true*/
	switch(state){
		case 'false':
			return 'ifNotEmpty';
		case 'ifNotEmpty':
			return 'true';
		case 'true':
			return 'false'
	}
	throw "Unknown State [" + state + "]";
}
PlmaResultListPref.getStateCss = function (state) {
	/*State Order : false, ifNotEmpty, true*/
	switch(state){
		case 'false':
			return 'fonticon-checkbox-off';
		case 'ifNotEmpty':
			return 'fonticon-stop';
		case 'true':
		case 'titleConfig':
			return 'fonticon-checkbox-on'
	}
	throw "Unknown State [" + state + "]";
}

PlmaResultListPref.prototype.initGroups = function () {
	this.$elements.GROUPS_CONTAINER.empty();
	this.unsetModified();
	let singleGroup = this.resultListConfig.columns.length < 2;
	this.$elements.GROUPS_CONTAINER.addClass(singleGroup? 'single-group': 'multi-group');

	this.resultListConfig.columns.forEach(function(groupConfig){
		var $item = $('<div class="group" data-groupid="' + groupConfig.id + '">' +
			'<div class="group-header">' +
				'<span class="move icon fonticon fonticon-drag-grip"></span>' +
				'<span class="label">' + groupConfig.label + '</span>' +
				'<span class="description">' + groupConfig.description + '</span>' +	
			'</div>' +
		'</div>' );
		this.renderConditional($item.find('.group-header'), groupConfig.displayCondition);
		$item.data('groupData', groupConfig);
		
		var $colContainer = $('<div id="' + this.options.groupConfigCssId + '_' + groupConfig.id +'" class="columns-config"></div>');
		$item.append($colContainer);
		this.initColumns($colContainer, groupConfig.id, groupConfig.column);
		this.$elements.GROUPS_CONTAINER.append($item);
	}.bind(this));
	
	if(!singleGroup){
		this.$elements.GROUPS_CONTAINER.sortable({
		  items: 'div.group',
		  placeholder: 'group ui-state-highlight',
		  change: function(event, ui){
			  this.setModified();
		  }.bind(this)
		});
	}	
}

PlmaResultListPref.prototype.initColumns = function ($container, groupId, columnArray) {
	columnArray.filter((c) => c.configurable).forEach(function(columnConfig){
		var $item = $('<div class="column" data-column-state="' + columnConfig.isShown + '">' +
			'<span class="move icon fonticon fonticon-drag-grip"></span>' +
			'<span class="checkbox icon fonticon ' + PlmaResultListPref.getStateCss(columnConfig.isShown) + '"></span>' +
			'<span class="label">' + columnConfig.label + '</span>' +
		'</div>' );
		
		if(columnConfig.isShown == 'titleConfig'){
			$item.addClass('edit-disabled');
			$item.find('span.move').toggleClass('fonticon-drag-grip fonticon-block');
		}			
		$item.data('colData', columnConfig);
		$container.append($item);
	}.bind(this));
	
	$container.sortable({
	  helper: 'clone',
      items: 'div.column:not(.edit-disabled)',
	  placeholder: 'column ui-state-highlight',
	  change: function(event, ui){
		  this.setModified();
	  }.bind(this)
    });
	
	$container.find('div.column:not(.edit-disabled) ').on('click', function (e) {
		var $column = $(e.target).closest('.column');
		var newState = PlmaResultListPref.getNextState($column.attr('data-column-state'));
		
		$column.find('.checkbox').removeClass('fonticon-checkbox-off fonticon-checkbox-on fonticon-stop').addClass(PlmaResultListPref.getStateCss(newState));
		$column.attr('data-column-state', newState);
		this.setModified();
	}.bind(this));
}

PlmaResultListPref.prototype.renderConditional = function ($item, condition) {
	if(condition){
		$item.append($('<span class="condition fonticon fonticon-code">' +
			'<span class="condition-tooltip">' + 
				'<span class="description">' + (condition.description?condition.description:'') + '</span>' +
				'<span class="expr">' + (condition.expr?condition.expr:'') + '</span>' +
			'</span>' +
		'</span>'));
	}
}

PlmaResultListPref.prototype.editResultList = function(){
	var $container = this.$elements.PREF_CONTAINER.find('#'+this.options.groupConfigCssId);
	// Set orders correctly as per user modifications.
	$container.find('.group').each(function(groupOrder, group){
		let groupData = $(group).data('groupData');
		groupData.order = groupOrder;
	
		$(group).find('.columns-config .column').each(function(columnOrder, column){
			let colData = $(column).data('colData');
			colData.order = columnOrder;
			colData.isShown = $(column).attr('data-column-state');
		});
	}.bind(this));

	return $.ajax({
		type: 'POST',
		dataType: "json",
		contentType: "application/json; charset=utf-8",
		url: mashup.baseUrl + '/config/resultlist/edit',
		data: JSON.stringify(this.resultListConfig)
	});
}

PlmaResultListPref.prototype.initButtons = function ($container) {
	/* Add Save / cancel buttons */
	this.$elements.BUTTONS_CONTAINER = $('<div class="button-container"></div>');
	this.$elements.SAVE_BUTTTON = $('<span class="button save-button">' + this.getMessage('plma.resultlist.save') + '</span>');
	this.$elements.RESET_BUTTTON = $('<span class="button reset-button">' + this.getMessage('plma.resultlist.reset') + '</span>');
	
	this.$elements.BUTTONS_CONTAINER.append(this.$elements.SAVE_BUTTTON);
	this.$elements.BUTTONS_CONTAINER.append(this.$elements.RESET_BUTTTON);
	$container.append(this.$elements.BUTTONS_CONTAINER);

	this.$elements.SAVE_BUTTTON.on('click', function () {
		this.$elements.PREF_CONTAINER.showPLMASpinner({overlay: true});
		this.editResultList()
			.done(function(){
				this.unsetModified();
				if(window.Preferences){
					$.notify(Preferences.getMessage('plma.preference.save.success'), 'success');;
				}
				setTimeout($.proxy(function() {
					this.reload();
				},this),1000);
			}.bind(this))
			.fail(function(){
				this.$elements.PREF_CONTAINER.hidePLMASpinner();
				this.$elements.GROUPS_CONTAINER.notify('Error while saving preferences', 'error');
			}.bind(this));
	}.bind(this));
		
	this.$elements.RESET_BUTTTON.on('click', function () {
		this.reset();
	}.bind(this));
}
PlmaResultListPref.prototype.getMessage = function (code) {
	return mashupI18N.get("plmaResultListCommons", code);
}

PlmaResultListPref.prototype.setModified = function () {
	this.$elements.SAVE_BUTTTON.addClass('active');
}
PlmaResultListPref.prototype.unsetModified = function () {
	this.$elements.SAVE_BUTTTON.removeClass('active');
}

PlmaResultListPref.prototype.failureTemp = function () {
	$.notify(Preferences.getMessage('plma.preference.save.error'), 'error');
}

PlmaResultListPref.prototype.reload = function () {
	window.location.reload();
};