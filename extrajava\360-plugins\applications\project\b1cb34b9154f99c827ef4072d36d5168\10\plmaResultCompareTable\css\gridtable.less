@header-height: 45px;
@row-height: 50px;
@fixed-col-width: min-content; /*Including toolbar width*/
@toolbar-col-width: 100px;
@grid-col-width: 160px;

.mashup .result-panel {
	overflow: hidden;
}

.mashup .result-panel .plmaGridtableResultList .results-panel{
	.scroll-container.gridtable{
		overflow:auto;
		padding: 0px;
		&::-webkit-scrollbar-track {
			margin-left: calc(241px + @toolbar-col-width);
			margin-top: @row-height;
		}
		
		/*Fix thead which can have th or td(for fixed rows)*/
		.fix-header{
			position: sticky;
			top: 0;
			height: auto;
			width: min-content;
			z-index: 10;
			box-shadow: -6px 6px 10px -2px #b4b6ba;
			margin-bottom: 3px;		
			.table-header .th {
				background: #F1F1F1;
				font-size: 12px;
				color: #3D3D3D;
				padding: 0px 5px;
				height: @header-height;				
				
				&.table-column{
					position: relative;
					.label{
						position: inherit;
						display: block;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
						width: 95%;
						bottom: 14px;
						padding-left: 6px;
					}
				}

				&.table-column.sortable-column{
					.label{
						width: 85%;
					}
				}
				
				&> span{
					line-height: @header-height;
					
					&.fonticon.fonticon-expand-down,
					&.fonticon.fonticon-expand-up {
						line-height: 10px;
						position: absolute;
						right: 5px;
						top: 10px;
					}
					&.fonticon.fonticon-expand-down{
						bottom: 10px;
						top: auto;
					}
				}
				&.fixed-column{
					padding-left: 0px;
					padding-right: 5px;
				}
			}
			
			.grid-row.table-header{
				height: @header-height;
			}			
		}
		.grid-row{
			display: grid;
			grid-template-columns: @fixed-col-width;	/*Fixed Column*/
			grid-auto-flow: column;
			grid-auto-columns: @grid-col-width;		
			grid-template-rows: @row-height;
			border: 1px solid #E2E4E3;
			align-items: center;
			background: white;
			
			.fixed-column{
				line-height: 3px;
				position: sticky;
				left: 0;
				box-shadow: 7px 0px 7px -3px #b4b6ba;
				background: #ffffff;
				z-index: 9;
				width: 335px;
			}		
		}
		li.hit .hit-container.grid-row:hover{
			border-bottom:1px solid #77797c;
			border-top:1px solid #77797c;
			.mainColumn{
				border-right-color: #77797c;
			}
		}
		
		.hits.gridtable-layout{
			margin-top: 0px;
			padding: 0px;
			width: min-content;
		}
		/*Can presnet in both fix-header or hits*/
		li.hit{
			margin: 0px;
			border-width: 0px;
			.hit-container{
				overflow: inherit;
				.mainColumn{
					border-right: 5px solid transparent;
					height: @row-height;
					.hitContent { 
						display: none; 
					}
				}
			}	
			.subwidgets{
				height: 0px;
				div { padding: 0px; }
			}
			&.selected{
				border-width: 0px;
				.mainColumn{
					border-right-color: #42a2da;
				}
			}
		}	
				
		.grid-row{
			.fixed-column .title-container {
				display: grid;
				grid-auto-flow: row;
				grid-template-columns: min-content;
				height: 50px;
				align-items: center;
				
				.toolbarContainer{
					grid-row: 1;
					grid-column: 1;
					width: @toolbar-col-width;
					position:relative;
					border-right: 1px solid;
					height: 100%;
					.freezeButtonClass:hover{
                        color:#42a2da;
                    }
				}
				.hitThumbnail{
					grid-row: 1;
					grid-column: 2;					
				}
				.icon-container {
					grid-row: 1;
					grid-column: 2;
					padding: 0px 5px 0px 5px;
					width: 45px;
				}
				.hitTitle{
					grid-row: 1;
					grid-column: 3;
					width: 180px;
					line-height: 100%;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
					display: inline-block;
					color: #368ec4;
					font-size: 16px;					
				}
				
				&.with-thumbnail{
					.hitThumbnail{
						height: @row-height;
						width: @row-height;
						padding: 0px 5px;
					}
					.hitTitle .icon{
						width: 22px;
					}
				}
			}
			.th.fixed-column .title-container{
				height: 45px;
				.hitThumbnail, .toolbarContainer {
					height: 45px;
				}
				.hitTitle{
					width: 180px + 55px - 1px;	/* hitTitleWidth + (iconContainerWidth + (padding*2)) - border */
				}
				&.with-thumbnail .hitTitle{
					width: 180px - 1px;
				}
			}
		}
		
		.th, .td {
			border-right: 1px solid gray;
			word-break: break-word;
			overflow: hidden;
		}
		.td.hitFacet, .td.hitMeta, .td.hitType {
			padding: 3px;
			padding-left: 12px;
			height: @row-height - 6px;
			line-height: 40px;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
		.td.hitFacet{
        			.categoryLinksDecoration{
        			    text-decoration: underline 0.5px;
        			}
        }
	}
}