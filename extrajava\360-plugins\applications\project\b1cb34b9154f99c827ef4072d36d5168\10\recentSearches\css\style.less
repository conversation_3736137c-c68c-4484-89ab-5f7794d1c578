@import "../../plmaResources/css/styles/variables.less";

.plmaResultList ul{
	.plmaButton.favorite-button{
		display: inline-block;
		padding: 0px;
		
		.collection-modifier{
			position: absolute;
			right: 4px;
			top: 28px;
			font-size: 16px;
			cursor: pointer;
			&:hover{
				color: #42a2da;
			}
		}
	}
	.plmaButton.compare-button{
		display: inline-block;
		padding: 0px;
		
		.collection-modifier{
			position: absolute;
			right: 4px;
			top: 48px;
			font-size: 16px;
			cursor: pointer;
			&:hover{
				color: #42a2da;
			}
		}
	}

	&.tile-layout, &.article-layout{
		.plmaButton.favorite-button .collection-modifier{
			right: 27px;
			top: 7px;
		}
		.plmaButton.compare-button .collection-modifier{
			right: 50px;
			top: 7px;
		}
	}
}

.mashup.mashup-style .icons-div > div.recentSearches.compact-header{
	margin: 10px 5px;
	height: auto;
	overflow: visible;
}
.plmaResultList.no-header{
	.resultsTitle { display: none; }
}

.recentSearches {
	//background-color: white;
	overflow: auto;
	
	.collection-overlay {
		display:none;
	}
	
	&.collection-disabled{
	  	background-color:transparent;
		position: relative;
		//opacity: 25%;
        pointer-events: none;
	
// 		.collection-overlay{
// 		    position: absolute;
// 			top: 0px;
// 			left: 0px;
// 			width: 100%;
// 			height: 100%;
// 			background-color:#f9f2f2a6;
// 			z-index: 5;
// 			display: block;
// 		}
		.label-section{
			opacity: 25%;
		}
		.button-enabled{
			opacity: 1;
			pointer-events: all;
		}


		.action-clear{
			display: none;
		}
		.title{
			font-style:italic;
		}
	}
	
	&.collection-displayed{
		background-color:#f1f1f1;
		.collection-header{
			.label-section{
				.fonticon{
					color: #368EC4;
					background: white;
				}
				.counter{
					border-color:#368EC4;
				}
			}
			.fonticon-open-down{
				display: none;
			}
		}
	}
	&.toggle-activated{
		.collection-header{
			&:hover{
				background-color:#e2e4e3;
				.label-section{
					cursor: pointer;
					.title{ 
						color: black;
					}
					.fonticon{
						color:white;
						background-color:#77797c;
					}		
				}
			}
		}
		
	}
		
	&.compact-header{
		border-radius: 5px;
		&.collection-displayed{
			background-color:#e2e4e3;
			overflow: visible;
			position: relative;
			.collection-header {
				.button-enabled{
					opacity: 1;
					pointer-events: all;
				}
				.fonticon-open-down{
					display: none;
				}
			.action-clear{
				display:inline;
				top: 18px;
				right: -18px;
				z-index: 5;
			    width: 30px;
				height: 30px;
				border-radius: 20px;
				background-color: #e2e4e3;
			}
		}
			.collection-details{
				position:absolute !important; /*for overriding the changes by showSpinner*/
				right:0px;
				margin-top: 5px;
				background-color: #e2e4e3;
				min-width: 350px;
				.loading-overlay{ background-color: transparent; }
			}
		}
		.collection-header {
			padding: 0px 0px;
			.label-section{
				margin-right: 0px;
			}
			.label-section .title,
			.action-clear{
				display:none;
			}
		}
	}

	&.hit-details-popup{
		padding: 10px;
		min-width: 350px;
		.plmalightbox-header{
			margin-bottom: 7px;
			border-bottom: 2px solid #78befa;
		}
		.collection-header{
			display: inline-flex;
			width: 85%;
			padding-top: 0px;
			padding-left: 0px;
			.action-clear{ top: 0px; }
			.counter{ line-height: 12px; }
		}
		.collection-details{
			min-width: 350px;
		}
	}
	
	.collection-header {
		padding: 10px 20px;
		position: relative;
		display: flex;
		.label-section{
			display: block;
			margin-right: 30px;
			.title {
				font-size: 18px;
				color: @clink-active;
				vertical-align: middle;
			}
			.fonticon{
				position: relative;
				width: 30px;
				height: 30px;
				border: 1px solid;
				border-radius: 25px;
				background-color: #b4b6ba;
				color: white;
				margin-right: 30px;
		
				&:before{
					vertical-align: middle;
					line-height: 30px;
					font-size: 18px;
				}
				.counter{
					width:min-content;
					font-size: 10px;
					position: absolute;
					top: -5px;
					color: #3d3d3d;
					background-color: white;
					border: 1px solid;
					padding: 1px 6px;
					border-radius: 9px;
				}
			}
		}
		.fonticon-open-down{
			margin-left: -19px;
			position: relative;
			line-height: 33px;
			pointer-events: all;
			cursor: pointer;
			top: 5px;
		}

		.button-popup {

			background-color: #ffff;
			top: 47px;
			position: fixed;
			right: 25%;
			box-shadow: 0 1px 6px 0px #b4b6ba;
			border: 1px solid #e2e4e3;
			height: 81px;
			width: 232px;
			z-index: 55;
			.action-details-multi-select{
				margin-right: 0;
				pointer-events: all;
				border-bottom: 1px solid #e2e4e3;
				padding: 4px;
			}

			&.button-disabled{
				pointer-events: none;
			}
			.multiselect-fonticon{
				margin-right: 6px;

			}
			.multiselect-title{
				font-size: 15px;
			}
		}
		.action-clear {
			cursor: pointer;
			position: absolute;
			right: 5px;
			top: 10px;
			font-size: 18px;
			&:hover {
				color: @clink;
			}
			&:before{
				vertical-align: middle;
				line-height: 30px;
			}
		}
	}
	.collection-details{
		.collection-icons-div{
			display: flex;
			position: absolute;
			right: 5px;
			top: 5px;
			font-size: 18px;
			&.fonticon, .fonticon{ 
				color: #f1b71a;				
			}
			.fonticon:hover{
				color: #42a2da;
			}			
		}
	}
	
	.history {
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		margin-left: 20px;
		min-height: 188px;
		min-width: 500px;
		margin: 5px;
		margin-bottom:0px;
		background: white;
		&.hidden {
			display: none;
		}
		a {
			text-decoration: none;
			flex: 0 0 450px;
			margin-bottom: 10px;
			&.hidden {
				display: none;
			}
			.entry {
				margin: 0 10px 0 0;
				border: 1px solid @cblock-border;
				display: flex;
				flex-flow: column nowrap;
				white-space: nowrap;
				position: relative;
				.header {
					flex: 0 0 auto;
					display: flex;
					flex-direction:column;

					.header-container {
						.icon-container {
							display: inline-block;
							padding: 10px;
							padding-bottom: 0;
							height: 35px;
							.icon {
								border-radius: 35px;
								width: 35px;
								height: 35px;
								display: flex;
								align-items: center;
								justify-content: center;
								background-color: #d5e8f2;
								.fonticon {
									font-size: 17px;
									color: @clink;
								}
							}
						}
						.title {
							display: inline-block;
							word-wrap: break-word;
							color: @clink;
							line-height: 18px;
							font-size: 18px;
							margin: 0;
						}
					}

					.main-container {
						display: inline-block;
						flex-grow: 1;
						min-height: 10px;
						.metas {
							.type {
								font-size: 12px;
								color: @ctext-weak;
								margin: 0 0 0 56px;
								line-height: 24px;
								display: inline-block;
							}
							.meta-values {
								display: inline-block;
								float: right;
								flex: 0 0 auto;
								color: @ctext-weak;
								margin-right: 10px;
								margin-bottom: 0;
								line-height: 24px;
							}
						}
					}
				}
				.facets {
					flex: 1 0 auto;
					border-top: 1px solid @cblock-border;
					margin-left: 10px;
					margin-right: 10px;
					display: flex;
					padding-top: 7px;
					font-size: 11px;
					.facet {
						display: flex;
						margin-bottom: 7px;
						flex: 1 0 0px;
						margin-right: 14px;
						&:last-child {
							margin-right: 0;
							display: flex;
							justify-content: flex-end;
						}
						.categoryName {
							max-width: 100px;
							text-overflow: ellipsis;
							white-space: nowrap;
							overflow: hidden;
						}
					}
				}
			}
		}
	}

	.pagination {
		display: flex;
		justify-content: center;
		margin: 5px;
		margin-top: 0px;
		padding-top: 10px;
		background-color: white;    
		.page-button {
			background-color: @cblock-border;
			height: 10px;
			width: 10px;
			border-radius: 10px;
			margin: 5px;
			display: inline-block;
			cursor: pointer;
			&.selected {
				background-color: @ctext-weak;
			}
		}
	}

	.empty-history {
		margin: 20px;
		font-size: 15px;
		.fonticon {
			font-size: 25px;
		}
		&.hidden {
			display: none;
		}
	}
}
