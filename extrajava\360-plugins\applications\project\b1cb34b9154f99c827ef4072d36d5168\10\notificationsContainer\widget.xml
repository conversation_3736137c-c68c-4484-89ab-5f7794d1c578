<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Notifications Container" group="PLM Analytics/Results Rendering" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <Description>This widget contains the notifications of user's alerts</Description>
    <Preview>
        <![CDATA[
            <img src="images/preview.png" alt="Notification container" />
        ]]>
    </Preview>
    <Includes>
        <Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
        <Include type="js" path="../plmaResources/js/lodash.min.js"/>
        <Include type="js" path="js/notificationsContainer.js" />
        <Include type="js" path="../alerting/js/alertingClient.js" />
        <Include type="css" path="css/style.less" />
    </Includes>

    <SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
    <SupportWidgetsId arity="ZERO_OR_MANY" />
    <SupportI18N supported="true">
        <JsKeys>
            <JsKey>notification.confirmation.delete</JsKey>
        </JsKeys>
    </SupportI18N>

    <Dependencies>
        <Widget name="plmaResources" />
    </Dependencies>

    <OptionsGroup name="General">
        <Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
            <Description>Widget title. If empty, no name is displayed.</Description>
        </Option>
    </OptionsGroup>

    <DefaultValues>
    </DefaultValues>

</Widget>
