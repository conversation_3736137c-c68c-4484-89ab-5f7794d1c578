<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Chart board" group="PLM Analytics/Layout" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget creates a container in which you can add subwidgets. It also provides edit, refine, and visibility options that either have an effect on the subwidgets or on the whole page.</Description>


	<Preview>
		<![CDATA[
            <img src="/resources/widgets/chartboard/image/chartboard.PNG" alt="Chartboard" />
        ]]>
	</Preview>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="css" path="../plmaResources/lib/gridstack/gridstack.less"/>
		<Include type="css" path="../plmaResources/css/lightbox.less"/>
		<Include type="css" path="../plmaResources/css/styles/message.less"/>
		<Include type="css" path="../plmaResources/css/styles/spinner.less"/>
		
		<Include type="js" path="../plmaResources/js/lodash.min.js"/>
		<Include type="js" path="/resources/jquery-ui-1.11.4.custom/jquery-ui.min.js"/>
		<Include type="js" path="../plmaResources/lib/gridstack/gridstack.js"/>
		<Include type="js" path="../plmaResources/lib/gridstack/gridstack.jQueryUI.js"/>
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js"/>
		<Include type="js" path="../plmaResources/js/lightbox.js"/>
		<Include type="js" path="../plmaResources/js/message.js"/>
		<Include type="js" path="../plmaResources/js/plmaSpinner.js"/>
		<Include type="js" path="../plmaResources/js/i18nClient.js"/>
		<Include type="js" path="../plmaResources/js/rights.js"/>
		<Include type="js" path="js/chartboard.js" />
		<Include type="js" path="js/model.js" />
		<Include type="js" path="js/toolbar.js" />
		<Include type="js" path="js/richTextEditor.js" />
		<Include type="js" path="js/filters.js" />
		<Include type="js" path="js/share.js" />
	</Includes>

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportWidgetsId arity="ZERO_OR_MANY" displayType="LAYOUT"/>
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>chartboard.error.message.save</JsKey>
			<JsKey>chartboard.error.message.duplicate</JsKey>
			<JsKey>chartboard.error.message.delete</JsKey>
			<JsKey>chartboard.edit.tooltip</JsKey>
			<JsKey>chartboard.save.tooltip</JsKey>
			<JsKey>chartboard.reset.tooltip</JsKey>
			<JsKey>chartboard.cancel.tooltip</JsKey>
			<JsKey>chartboard.duplicate.tooltip</JsKey>
			<JsKey>chartboard.delete.tooltip</JsKey>
			<JsKey>chartboard.edit.label</JsKey>
			<JsKey>chartboard.duplicate.label</JsKey>
			<JsKey>chartboard.delete.label</JsKey>
			<JsKey>chartboard.reset.label</JsKey>
			<JsKey>chartboard.save.filters.label</JsKey>
			<JsKey>chartboard.remove.filters.label</JsKey>
			<JsKey>chartboard.share.label</JsKey>
			<JsKey>chartboard.save.filters.tooltip</JsKey>
			<JsKey>chartboard.remove.filters.tooltip</JsKey>
			<JsKey>chartboard.share.tooltip</JsKey>
			<JsKey>chartboard.reset.tooltip</JsKey>
			<JsKey>chartboard.reset.confirm</JsKey>
			<JsKey>yes</JsKey>
			<JsKey>no</JsKey>
			<JsKey>chartboard.date.on</JsKey>
			<JsKey>chartboard.background.tooltip</JsKey>
			<JsKey>chartboard.signature.tooltip</JsKey>
			<JsKey>chartboard.arrow.tooltip</JsKey>
			<JsKey>chartboard.arrow.left</JsKey>
			<JsKey>chartboard.arrow.top</JsKey>
			<JsKey>chartboard.arrow.right</JsKey>
			<JsKey>chartboard.arrow.bottom</JsKey>
			<JsKey>chartboard.toolbar.layout</JsKey>
			<JsKey>chartboard.toolbar.save</JsKey>
			<JsKey>chartboard.toolbar.cancel</JsKey>
			<JsKey>chartboard.toolbar.choose</JsKey>
			<JsKey>chartboard.error.message</JsKey>
			<JsKey>chartboard.toolbar.layout.default</JsKey>
			<JsKey>chartboard.toolbar.layout.label</JsKey>
			<JsKey>chartboard.toolbar.layout.description</JsKey>
			<JsKey>chartboard.toolbar.layout.icon</JsKey>
			<JsKey>chartboard.toolbar.layout.save</JsKey>
			<JsKey>chartboard.toolbar.page.noicon</JsKey>
			<JsKey>chartboard.toolbar.layout.saveas</JsKey>
			<JsKey>chartboard.dots.tooltip</JsKey>
			<JsKey>chartboard.page.tooltip</JsKey>
			<JsKey>chartboard.layout.plus.tooltip</JsKey>
			<JsKey>chartboard.layout.change.tooltip</JsKey>
			<JsKey>chartboard.share.all</JsKey>
			<JsKey>chartboard.share.title</JsKey>
			<JsKey>chartboard.share.write</JsKey>
			<JsKey>chartboard.share.read</JsKey>
			<JsKey>chartboard.share.apply</JsKey>
			<JsKey>chartboard.share.page.modified</JsKey>
			<JsKey>chartboard.share.page.edit</JsKey>
			<JsKey>chartboard.share.layout.modified</JsKey>
			<JsKey>chartboard.share.filter.modified</JsKey>
			<JsKey>chartboard.share.change.user</JsKey>
			<JsKey>chartboard.share.change.group</JsKey>
			<JsKey>chartboard.share.added.user</JsKey>
			<JsKey>chartboard.share.page.owner</JsKey>
			<JsKey>chartboard.reset.page.title</JsKey>
			<JsKey>chartboard.delete.page.title</JsKey>
			<JsKey>chartboard.delete.page.confirm</JsKey>
			<JsKey>chartboard.delete.page.cancel</JsKey>
			<JsKey>chartboard.share.add.tooltip</JsKey>
			<JsKey>chartboard.share.cancel.tooltip</JsKey>
			<JsKey>chartboard.share.remove.tooltip</JsKey>
		</JsKeys>
	</SupportI18N>
	
	<Dependencies>
		<Widget name="plmaResources"/>
	</Dependencies>
	
	<OptionsGroup name="General">
		<Option arity="ZERO_OR_ONE" name="Title" id="widgetTitle" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
		</Option>
		<Option id="cellHeight" name="Height of widgets">
			<Description>Height in pixel of a layout box. It will be rounded to the closest grid row. Provide multiplier of 80. e.g 160 will rounded to grid height data-gs-height=2</Description>
		</Option>
		<Option id="editButtonLocationSelector" name="Edit button">
			<Description>
				The CSS selector of the element into which the 'Edit' button' is placed.&lt;br/&gt;
				&lt;b&gt;Note:&lt;/b&gt; The Edit button will not appear if the user is not logged in, as edited layouts are stored in the Storage Service under the user scope.
			</Description>
		</Option>
		<Option id="editToolbarLocationSelector" name="Edit toolbar">
			<Description>
				The CSS selector of the element into which the 'Edit' toolbar is placed.&lt;br/&gt;
				&lt;b&gt;Note:&lt;/b&gt; The Edit toolbar will not appear if the user is not logged in, as edited layouts are stored in the Storage Service under the user scope.
			</Description>
		</Option>
		<Option id="allowEditPages" name="Allow edit pages">
			<Description>Allows the user to duplicate the page and modify its label, description, and icon.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="allowEditFilters" name="Allow edit filters">
			<Description>Allows the user to save filters.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="lockMashupPages" name="Lock edition on template pages">
			<Description>Prevents all users from editing the layout of any template page. (Default value is false)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="savePreview" name="Save preview for available charts">
			<Description>Saves a preview of the chart at the time it is removed from the chartboard. (Default is true)</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Advanced">
		<Option id="hideAllCells" name="Hide All Charts">
			<Description>Hides by default the subwidgets from the widget. If hidden, subwidgets appear in the Available widget panel.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		
	</OptionsGroup>
	<DefaultValues>
		<DefaultValue name="editButtonLocationSelector">.plmaTitleIconsDiv</DefaultValue>
		<!-- If this option is not checked, chartboard edition is not working, so, set it as true as default -->
		<DefaultValue name="cssDisableStyle">true</DefaultValue>
	</DefaultValues>

</Widget>
