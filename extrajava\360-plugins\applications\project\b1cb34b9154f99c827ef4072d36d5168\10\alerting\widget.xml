<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Alerting" group="Alerting" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <Description>A widget to create alerts for charts</Description>


    <SupportWidgetsId arity="ZERO" />
    <SupportFeedsId arity="ZERO" />
    <SupportI18N supported="true">
        <JsKeys>
            <JsKey>alerting.alert</JsKey>
            <JsKey>alerting.form.anyCategory</JsKey>
            <JsKey>alerting.form.actions.remove</JsKey>
            <JsKey>alerting.tab.alertTitle</JsKey>
            <JsKey>alerting.tab.alert.subscribe</JsKey>
            <JsKey>alerting.tab.alert.unsubscribe</JsKey>
        </JsKeys>
    </SupportI18N>

    <Includes>
        <Include type="css" path="../plmaResources/css/lightbox.less"/>
        <Include type="css" path="../plmaResources/lib/notify/notify-plma.less"/>
        <Include type="css" path="css/style.less" />
        <Include type="js" path="../plmaResources/js/lodash.min.js"/>
        <Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
        <Include type="js" path="../plmaResources/js/lightbox.js"/>
        <Include type="js" path="../plmaResources/lib/notify/notify.js"/>
        <Include type="js" path="../plmaResources/lib/notify/notify-plma.js"/>
        <Include type="js" path="../plmaResources/js/moment-with-locales.min.js" />
        <Include type="js" path="../plmaResources/js/daterangepicker.js"/>
        <Include type="js" path="js/alertingClient.js" />
        <Include type="js" path="js/alertingForm.js" />
        <Include type="js" path="js/alertingFormBuilder.js" />
        <Include type="js" path="js/alertingPopup.js" />
        <Include type="js" path="js/alerting.js" />
    </Includes>

    <Dependencies>
        <Trigger position="self" className="com.exalead.cv360.searchui.triggers.impl.RemoveIfNotLoggedIn" />
        <!--<Widget name="plmaResources" />-->
    </Dependencies>

    <OptionsGroup name="General">
        <Option id="dbKey" name="Storage database key" arity="ONE">
            <Description>Defines the key used in the storage service to store bookmarks</Description>
        </Option>
        <Option id="waitForTrigger" name="Create alerting button after trigger event">
            <Description>If option is set to 'true', alerting btn is set after a "plma:chart-is-loaded" trigger event
                has been sent.  &lt;br/&gt;
                &lt;b&gt;WARNING&lt;/b&gt;: Mandatory when plmaChart2 widget uses a plma Asynchronous
                ajax loading trigger.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>
    </OptionsGroup>

<!--    <OptionsGroup name="Alerting">-->
<!--        <Option id="" name="">-->
<!--        </Option>-->
<!--    </OptionsGroup>-->

    <DefaultValues>
        <DefaultValue name="dbKey">alerting[]</DefaultValue>
        <DefaultValue name="waitForTrigger">false</DefaultValue>
    </DefaultValues>

</Widget>
