var PLMAHitDetailsClose = function (uCssId) {
    'use strict';
    this.uCssId = uCssId;
    if (uCssId) {
        this.widget = $('.' + uCssId);
    } else {
        this.widget = $();
    }
    this.init();
};

PLMAHitDetailsClose.prototype.init = function (){
    $(this.widget).find(".closeHitDetailsButton").on('click', $.proxy(function (e) {
        // Trigger an event on any result list view (deselection must be implemented by view which
        // listens to this event since views can be customized)
        $('.resultlistView').trigger('hit-details:close');
        var helper = new DetailHitHelper();
        helper.closeDetailPanel();
        helper.pushState();
        helper.deselectHit();
        if($('.plmaTilesResultList').length > 0) {
            helper.displayTileView();
        } else {
            helper.displayRightView();
        }
        $(window).trigger('resize');
    }, this));
}