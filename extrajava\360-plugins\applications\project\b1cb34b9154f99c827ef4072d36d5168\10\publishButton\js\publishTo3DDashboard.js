var PublishButton = function(options){
	this.$widget = $('#' + options.cssId);
	this.$button = this.$widget.find('> .button-content');
	this.options = $.extend(null, {
		onInit: $.noop,
		onDone: $.noop,
	}, options);
	this.$widget.data('widget', this);
	this.init();
	return this;
}

PublishButton.prototype.init = function () {
    if(window.dashboardController) {
        this.$widget.removeClass('disabled');
        this.options.onInit.call(this.$button);
        this.$button.on('click', this.handleClick.bind(this));
    }else{
        this.$button.on('click', function(e) {
            $.notify("Publish Only Allowed inside 3DExperience", 'error');
        });
    }
}

PublishButton.prototype.handleClick = function () {
	this.$button.toggleClass('active');
	this.previewHelper = window.exportTo3DSpace.startDocumentExport({
		id: this.options.fileMode ? 'SnapshotExport' : 'ContextShare',
		title: this.options.title,
		fileMode: this.options.fileMode,
		fileExtension: 'pdf',
		enableAddNewDocument: true,
		isBookmarkEnable: this.options.isBookmarkEnable,
		isSwymEnable: this.options.isSwymEnable,
		securityContext: this.options.securityContext,
		$tabsContainer: this.$widget.find('.tabs-container')
	});
	this.MAIN_CONTAINER = this.previewHelper.container.closest('.container');
	if(this.options.enableDrop && this.DROP_AREA == undefined){
		this.DROP_AREA =
			$('<div class="drop-area" title="Drop Document Object to Reload Context">' +
				'<span class="fonticon fonticon-download"></span>' +
				'<span class="label">' + this.getPlmaMessage('plma.exportto3dspace.drop.label')  +' to reload Context</span>' +
				'<span class="disabled-overlay"></span>' +
			'</div>');
		this.MAIN_CONTAINER.closest('.export-to-3dspace').find('> .plmalightbox-header').append(this.DROP_AREA);
		this.enableDropContext();
	}
	this.MAIN_CONTAINER.find('.exp-context-container').removeClass('hidden');
	this.createContextLinkUI(this.previewHelper.container.find('.preview-header'));
	if(this.options.fileMode){
		this.$widget.find('.pdfCreator > .button-content').trigger('click');
	}else{
		this.previewHelper.doneCallback(
			function(){},
			'', 
			this.updateDocInfo.bind(null, this.previewHelper.container),
			this.updateSwymInfo.bind(null, this.previewHelper.container)
		);
	}
}

PublishButton.prototype.createContextLinkUI = function($container){
	let $contextLinkContainer = $(
		'<div class="context-link-container">'+
			'<div class="link-label">'+
				'<span class="fonticon label">' + this.getPlmaMessage("plma.publishto3ddashboard.preview.input.label")+ '</span>'+
				'<input type="text" class="label-input" size=70>'+
			'</div>'+
			'<div class="link copy-clipboard" >'+
				'<span title= "Copy 3DDashboard context link To ClipBoard" class="fonticon fonticon-copy"></span>'+
			'</div>' +
		'</div>');
	$container.append($contextLinkContainer);
	$contextLinkContainer.data('contextLink', this._contextLink());
	$contextLinkContainer.find('.label-input').val(this._getPageTitle());
	$contextLinkContainer.find('.link.copy-clipboard').on('click', function(){
		$contextLinkContainer.addClass("active");
		
		/*clipboard available only when running the app with HTTPS or localhost*/
		if(navigator.clipboard){
			navigator.clipboard.writeText($contextLinkContainer.data('contextLink'));
			$.notify(this.getPlmaMessage('plma.publishto3ddashboard.info.copiedtoclipboard'), 'info');
		}else{
			$.notify('Error: No ClipBoard available. Use HTTPS/localhost to load the page', 'error');
		}
	}.bind(this));
}

PublishButton.prototype.getPlmaMessage = function (code) {
    return mashupI18N.get('plmaResources', code);
}
PublishButton.prototype._getPageTitle = function() {
	return $('.pageTitleContainer .pageTitleWidget .pageTitle').text().trim();
}
/* Create the context link which can directly open the PLMAApp with exact page and query from 3DDashboard url.
 * Format: protocol=[https:]//host=[a.com:443]pathname=[/3DDashboard/]#app:[appId]/content:[pageName]Query=[queryString]
 * e.g. https://vdevpril635dsy.dsone.3ds.com:444/3DDashboard/#app:MAP-GODLMXYVP/content:overviewQuery=_context=3dxp&lang=en
 */
PublishButton.prototype._contextLink = function() {
	let dashBoardUrl = window.top.location;		
	return dashBoardUrl.protocol + '//' + dashBoardUrl.host + dashBoardUrl.pathname + 
		'#app:' + window.dashboardController.widgetInfos.appId + 
		/*Remove the empty strings after split it can happen that the path have '/' at the end.*/
		'/content:' + window.location.pathname.split('/').filter(function (c) { return c.length > 0;}).pop() +
		'Query=' + window.location.href.split('?')[1] ;
}

//Callback to update Document Info before data Published to Document/Bookmark
PublishButton.prototype.updateDocInfo = function($container, documentInfo, fileRefIndex){
	let timeStampStr = '' + moment().format('DD MMM, YYYY (hh:mm:ss A)');
	let contextLinkText = '\n$#$ ContextLink=[ ' + timeStampStr + ' ] [ ' + 
		$container.find('.context-link-container .label-input').val() + ' ] [ ' + 
		$container.find('.context-link-container').data('contextLink') + ' ]\n';
	documentInfo.dataelements.description = 
		documentInfo.dataelements.description + '\n' + contextLinkText;
		
	if(fileRefIndex != undefined){
		let file = documentInfo.relateddata.files[fileRefIndex];
		file.dataelements.comments = 
			file.dataelements.comments + '\n' + contextLinkText;
	}
	
	// In case if Document already exist
	if(documentInfo.updateAction == 'NONE') {
		documentInfo.updateAction = 'MODIFY';
	}
}

//Callback to update Post Info before data Published to Sywm
PublishButton.prototype.updateSwymInfo = function($container, postInfo){
	let timeStampStr = '' + moment().format('DD MMM, YYYY (hh:mm:ss A)');
	postInfo.contentDescription = postInfo.contentDescription + "<br>" +
	'<a href="' + $container.find('.context-link-container').data('contextLink') +'">'+
		$container.find('.context-link-container .label-input').val() +
		' [ ' + timeStampStr + ']' +
	'</a>';
}

PublishButton.getPreviewContainer = function(cssId){
	return $('#' + cssId).data('widget').previewHelper.container.find('.preview-content');
}

PublishButton.filePreviewHandler = function(cssId, $previewContainer, pdfDoc, fileName, id){
	// Show Preview
	CreatePDF.showPreview($previewContainer, pdfDoc, fileName, id);
	
	let $previewHeader = $previewContainer.prev();
	$previewContainer.find('.download-link').appendTo($previewHeader);
	$previewHeader.append($('<i class="fullscreen-preview fonticon fonticon-resize-full"></i>'));
	$previewHeader.find('.fullscreen-preview').on('click', function(){
		$(this).toggleClass('fonticon-resize-full fonticon-resize-small');
		$previewHeader.closest('.container').find('.exp-context-container').toggleClass('hidden');
		
		let $pdfPreviewContainer = $previewContainer.find('.pdf-preview-pane');
		if($pdfPreviewContainer.attr('style') == undefined || $pdfPreviewContainer.attr('style').search('height') == -1){
			$pdfPreviewContainer.css('height', '' + (0.9*window.innerHeight - 100) + 'px');
		}else{
			$pdfPreviewContainer.css('height', '');
		}
	});
	
	// Send Done Callback.
	let _instance = $('#' + cssId).data('widget');
	_instance.previewHelper.doneCallback(
		function(){ 
			return $previewContainer.find('embed').attr('src');
		}, 
		fileName,
		_instance.updateDocInfo.bind(null, _instance.previewHelper.container),
		_instance.updateSwymInfo.bind(null, _instance.previewHelper.container)
	);
}

/**
* enableDropContext is method helps to load the Page Context from the 3DSpace Object when its dropped in the exalead widget.
* The Dropped object contains object id,security context,title,objecttype so using these information need to call Document API that will get other
* information of the object and the method extracts description property that has the page Link to be loaded.
* Then modify the window location with the Page Link to redirect.
*
* */
PublishButton.prototype.enableDropContext = function(){
	let receiveOptions = {
        acceptedTypes: ['text/plain','Text'],
        enableSubscribe: true,
        subscribeChannel: 'com.ds.compass',
        subscribeMethod: 'PubSub'
    }
    let that = this;
    function trapEvent(event) {
        event.preventDefault();
        if(event.type !== "dragleave" ){
            that.DROP_AREA.css('background-color', '#e2e4e3');
        }
        if(event.type === "dragleave" ){
            that.DROP_AREA.css('background-color', '');
        }
    }
    function acceptedContextTypes(candidates){
        return  receiveOptions.acceptedTypes.filter(function(type) {
            return candidates.contains ? candidates.contains(type) : candidates.indexOf(type) > -1;
        });
    }
	
	var domElement = this.DROP_AREA.get(0);
	domElement.addEventListener("dragover", trapEvent);
	domElement.addEventListener("dragenter", trapEvent);
	domElement.addEventListener("dragleave", trapEvent);
	domElement.addEventListener("drop", (function(event) {
		if (event.preventDefault) {
			event.preventDefault();
		}
		if (event.stopPropagation) {
			event.stopPropagation();
		}
		if(this.DROP_AREA.hasClass('disabled')){
			this.DROP_AREA.css("background-color", "#EA4F37")
				.animate({ backgroundColor: "#FFFFFF"}, 2000);
			return;
		}
		this.DROP_AREA.css('background-color', '');
		var accepted = acceptedContextTypes(event.dataTransfer.types);
		if (accepted.length > 0) {
			var data = event.dataTransfer.getData(accepted[0]);
			var obj = JSON.parse(data);
			if(obj.data.items[0].objectType == "Document"){
				this._renderContext(obj);
			}else{
				$.notify('[' + obj.data.items[0].objectType + '] ' + this.getPlmaMessage('plma.exportto3dspace.error.unsupported.type'), 'error');
			}
		}
	}).bind(this));
}

PublishButton.prototype._renderContext = function(obj){
	var that = this;
	that.DROP_AREA.showPLMASpinner({overlay: true})
	EXPUtils.getWafData().then(function(API){
		// URL -> CSRF -> DocInfo -> Redirect to Page Context
		let data = {
			API: API,
			securityContext: obj.data.items[0].contextId,
			documentInfo: {
				id: obj.data.items[0].objectId
			},
			failureCallback: function(err){console.error("Error While Executing API's",err)},
			successCallback: function(data){
				// Received URL
				data.successCallback = function(data){
					// Received CSRF
					data.successCallback = function(data){
						that.DROP_AREA.hidePLMASpinner();
						// Received DocInfo
						const matches = data.result.dataelements.description.match(/(https?:\/\/[^\s]+)/g);
						if(matches.length > 0){
							if(matches.length > 1){
								$.notify('Found '+ matches.length + " URL's redirect to the first", 'info');
							}
							let urlParams = matches[0].split('/content:').pop().split("Query=");
							let newUrl = window.location.href.split(window.location.pathname.split("/").pop())[0];
							if(urlParams.length == 2 && newUrl && newUrl != ""){
								window.location.href = newUrl + urlParams[0] + "?" +urlParams[1];
							}else{
								$.notify('Malformed URL : Unable to redirect as URl in description' , 'error');
							}
						}
					}
					EXPUtils.fetchDocumentInfo(data);
				}
				EXPUtils.fetchCSRFToken(data);
			}
		};
		EXPUtils.fetch3DSpaceUrl(data)
	})
}
