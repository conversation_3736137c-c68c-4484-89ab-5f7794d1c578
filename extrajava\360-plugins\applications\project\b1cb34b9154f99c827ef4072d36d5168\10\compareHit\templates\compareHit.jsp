
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import parameters="feeds,entry,showCompareLabel,compareLabel,hitID,compareCollection" ignore="true"/>

<plma:hasCollectionItem var="isComparedHit" id="${hitID}" collection="${compareCollection}"/>

<div class="plmaButton compare-button compared-hit">
    <c:choose>
        <c:when test="${isComparedHit}">
            <i class="fonticon fonticon-compare-on hit-action-compare collection-modifier" data-status="on" data-id="${hitID}" title="Remove"></i>
        </c:when>
        <c:otherwise>
            <i class="fonticon fonticon-compare-off hit-action-compare collection-modifier" data-status="off" data-id="${hitID}" title="${compareLabel}"></i>
        </c:otherwise>
    </c:choose>

    <c:if test="${showLabel}">
        <span class="button-label">${label}</span>
    </c:if>
</div>

<render:renderScript position="READY">
    var options = {};
    options.url = '<c:url value="/collections"/>';
    options.page = '<search:getPageName/>';
    options.collection = '${compareCollection}';
    options.counterSelector = '${counterSelector}';
    options.hitID = '${hitID}';
    new CompareHit(options);
</render:renderScript>