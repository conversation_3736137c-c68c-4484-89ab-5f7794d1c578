<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<config:getOption name="mode" var="mode" />
<config:getOption name="titleSelector" var="titleSelector" defaultValue=".pageTitle" />
<config:getOption name="elemPerPage" var="elemPerPage" defaultValue="6" />
<config:getOptions name="queryStringFields" var="queryStringFields" />
<config:getOption name="displayLabel" var="displayLabel" defaultValue="false" />

<widget:widget varUcssId="uCssId" extraCss="pinnedPages ${mode == 'pin' ? 'button' : ''}">

	<c:choose>
		<c:when test="${mode == 'pin'}">
			<i class="fonticon fonticon-favorite-off"></i>
			<c:if test="${displayLabel == 'true'}">
				<span class="button-label"></span>
			</c:if>
		</c:when>
		<c:otherwise>
			<div class="favorite-header">
				<span class="title"><config:getOption name="title" defaultValue=""/></span>
				<span class="delete-button fonticon fonticon-trash" title="<i18n:message code='pinnedPages.unpinAll'/>"></span>
			</div>
			<div class="pages"></div>
			<div class="pagination"></div>
			<div class="empty-pages"><span class="fonticon fonticon-doc-delete"></span> <i18n:message code='pinnedPages.empty'/></div>
		</c:otherwise>
	</c:choose>

	<render:renderScript position="READY">
		var pinnedPages = new PinnedPages('${uCssId}', {
			queryStringFields: [
				<c:forEach items="${queryStringFields}" var="field">
					'<c:out value="${field}" />',
				</c:forEach>
			],
			displayLabel: ${displayLabel},
			elemPerPage: ${elemPerPage}
		});
		<c:choose>
			<c:when test="${mode == 'pin'}">
				pinnedPages.init();
			</c:when>
			<c:otherwise>
				<plma:menuConfig varMainItems="menuConfig"/>
				var menuConfig = [
				<c:forEach items="${menuConfig}" var="page">
					{
						iconCss: '<string:escape value="${page.iconCss}" escapeType="JAVASCRIPT"/>',
						description: '<string:escape value="${page.description}" escapeType="JAVASCRIPT"/>',
						target: '<string:escape value="${page.target}" escapeType="JAVASCRIPT"/>',
						label: '<string:escape value="${page.label}" escapeType="JAVASCRIPT"/>'
					},
				</c:forEach>
				];
				pinnedPages.getPinnedPages(menuConfig);
				pinnedPages.bind();
			</c:otherwise>
		</c:choose>
	</render:renderScript>
	
</widget:widget>
