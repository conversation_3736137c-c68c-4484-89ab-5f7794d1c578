@import "../polyfills.less";
@import "../styles/utils.less";

.flex-layout-wrapper {
	height: 100vh;
	.display-flex();
	.flex-direction(column);
	
	> .flex-layout-row {
		overflow: auto;
		height: 100%;
		.flex-grow(1);
		.flex-shrink(0);
		.flex-basis(auto);
		
		.display-flex();
		.flex-direction(row);
		.flex-wrap(nowrap);
		.align-items(stretch);
		
		
		>.flex-layout-item{
			height: 100%;
			overflow: auto;
				
			.flex-shrink(1);
			.flex-basis(0);
			
			&.menuPanel{ /* Menu sidebar */
				.flex(0 0 auto);
				
				/* Overriding the sidebar style to avoid weird scrolling behaviour */
				background: @cblock-border;
				border-right: 1px solid @cblock-border-alt;
				box-sizing: border-box;
				
			}
			
			&.collapsiblePanel{
				.transition(@transition-quick, "~flex-grow, flex-basis");
				.flex-shrink(0);
				.flex-basis(150px);
				border-right: 1px solid @cblock-border;
				padding: 0 (@line-height /2);
				white-space: nowrap;
				
				&.hidden{
					.transition(@transition-quick, "~flex-grow, flex-basis");
					.flex-grow(0);
					.flex-basis(0);
					border: none;
					padding: 0;
					/* Fix for IE */
					overflow: hidden;
				}
			}
			
			
			&.contentPanel{
				.display-flex();
				.flex-flow(column nowrap);
				.justify-content(flex-start);
				
				.pageTitleContainer{
					.flex(0 0 auto);
				}
				
				.flex-panel-container-hits{
					.flex(1 1 auto);
					position: relative;
				}
			}
		}
		
		&.wrapPanels {
			.flex-wrap(wrap);
			>.flex-layout-item{
				.flex-shrink(0);
			}
		}
		
		&.fixedHeight {
			.flex-grow(0);
		}
		
	}
	
	
	#panelsContainer{
		height: 100vh;
		> .flexPanelsContainer-row {
			height: 100%;
			
			
			>.flexPanelsContainer-item{
				height: 100%;
				overflow: auto;
				
				&:first-child{ /* Menu sidebar */
					.flex(0 0 auto);
					
					/* Overriding the sidebar style to avoid weird scrolling behaviour */
					background: @cblock-border;
					border-right: 1px solid @cblock-border-alt;
					box-sizing: border-box;
					
				}
				
				&.collapsiblePanel{
					.transition(@transition-quick, "~flex-grow, flex-basis");
					.flex-shrink(0);
					.flex-basis(150px);
					border-right: 1px solid @cblock-border;
					padding: 0 (@line-height /2);
					white-space: nowrap;
					
					&.hidden{
						.transition(@transition-quick, "~flex-grow, flex-basis");
						.flex-grow(0);
						.flex-basis(0);
						border: none;
						padding: 0;
						/* Fix for IE */
						overflow: hidden;
					}
				}
				
				&.refinesPanel{
				}
				&.bookmarksPanel{
					padding: 0;
					.widgetHeader {
						margin: @line-height (@line-height /2);
					}
					.savedbookmarks{
						max-width: 60em;  /* To avoid weird behaviour when bookmark title is too long */
					}
				}
				
				&.contentPanel{
					.display-flex();
					.flex-flow(column nowrap);
					.justify-content(flex-start);
					
					.pageTitleContainer{
						.flex(0 0 auto);
					}
					
					.flex-panel-container-hits{
						.flex(1 1 auto);
						position: relative;
					}
				}
				
				&.main-flex-container-home{
					height: inherit;
				}
			}
			
		}
		
	}
	
	
	
	/* Cannot use LESS loops because guards are only available in LESS 1.5.0 */
	.flex-layout-col-1   {.flex-grow(1);}
	.flex-layout-col-2   {.flex-grow(2);}
	.flex-layout-col-3   {.flex-grow(3);}
	.flex-layout-col-4   {.flex-grow(4);}
	.flex-layout-col-5   {.flex-grow(5);}
	.flex-layout-col-6   {.flex-grow(6);}
	.flex-layout-col-7   {.flex-grow(7);}
	.flex-layout-col-8   {.flex-grow(8);}
	.flex-layout-col-9   {.flex-grow(9);}
	.flex-layout-col-10  {.flex-grow(10);}
	.flex-layout-col-11  {.flex-grow(11);}
	.flex-layout-col-12  {.flex-grow(12);}
	.flex-layout-col-13  {.flex-grow(13);}
	.flex-layout-col-14  {.flex-grow(14);}
	.flex-layout-col-15  {.flex-grow(15);}
	.flex-layout-col-16  {.flex-grow(16);}
	.flex-layout-col-17  {.flex-grow(17);}
	.flex-layout-col-18  {.flex-grow(18);}
	.flex-layout-col-19  {.flex-grow(19);}
	.flex-layout-col-20  {.flex-grow(20);}
	.flex-layout-col-21  {.flex-grow(21);}
	.flex-layout-col-22  {.flex-grow(22);}
	.flex-layout-col-23  {.flex-grow(23);}
	.flex-layout-col-24  {.flex-grow(24);}
	.flex-layout-col-25  {.flex-grow(25);}
	.flex-layout-col-26  {.flex-grow(26);}
	.flex-layout-col-27  {.flex-grow(27);}
	.flex-layout-col-28  {.flex-grow(28);}
	.flex-layout-col-29  {.flex-grow(29);}
	.flex-layout-col-30  {.flex-grow(30);}
	.flex-layout-col-31  {.flex-grow(31);}
	.flex-layout-col-32  {.flex-grow(32);}
	.flex-layout-col-33  {.flex-grow(33);}
	.flex-layout-col-34  {.flex-grow(34);}
	.flex-layout-col-35  {.flex-grow(35);}
	.flex-layout-col-36  {.flex-grow(36);}
	.flex-layout-col-37  {.flex-grow(37);}
	.flex-layout-col-38  {.flex-grow(38);}
	.flex-layout-col-39  {.flex-grow(39);}
	.flex-layout-col-40  {.flex-grow(40);}
	.flex-layout-col-41  {.flex-grow(41);}
	.flex-layout-col-42  {.flex-grow(42);}
	.flex-layout-col-43  {.flex-grow(43);}
	.flex-layout-col-44  {.flex-grow(44);}
	.flex-layout-col-45  {.flex-grow(45);}
	.flex-layout-col-46  {.flex-grow(46);}
	.flex-layout-col-47  {.flex-grow(47);}
	.flex-layout-col-48  {.flex-grow(48);}
	.flex-layout-col-49  {.flex-grow(49);}
	.flex-layout-col-50  {.flex-grow(50);}
	.flex-layout-col-51  {.flex-grow(51);}
	.flex-layout-col-52  {.flex-grow(52);}
	.flex-layout-col-53  {.flex-grow(53);}
	.flex-layout-col-54  {.flex-grow(54);}
	.flex-layout-col-55  {.flex-grow(55);}
	.flex-layout-col-56  {.flex-grow(56);}
	.flex-layout-col-57  {.flex-grow(57);}
	.flex-layout-col-58  {.flex-grow(58);}
	.flex-layout-col-59  {.flex-grow(59);}
	.flex-layout-col-60  {.flex-grow(60);}
	.flex-layout-col-61  {.flex-grow(61);}
	.flex-layout-col-62  {.flex-grow(62);}
	.flex-layout-col-63  {.flex-grow(63);}
	.flex-layout-col-64  {.flex-grow(64);}
	.flex-layout-col-65  {.flex-grow(65);}
	.flex-layout-col-66  {.flex-grow(66);}
	.flex-layout-col-67  {.flex-grow(67);}
	.flex-layout-col-68  {.flex-grow(68);}
	.flex-layout-col-69  {.flex-grow(69);}
	.flex-layout-col-70  {.flex-grow(70);}
	.flex-layout-col-71  {.flex-grow(71);}
	.flex-layout-col-72  {.flex-grow(72);}
	.flex-layout-col-73  {.flex-grow(73);}
	.flex-layout-col-74  {.flex-grow(74);}
	.flex-layout-col-75  {.flex-grow(75);}
	.flex-layout-col-76  {.flex-grow(76);}
	.flex-layout-col-77  {.flex-grow(77);}
	.flex-layout-col-78  {.flex-grow(78);}
	.flex-layout-col-79  {.flex-grow(79);}
	.flex-layout-col-80  {.flex-grow(80);}
	.flex-layout-col-81  {.flex-grow(81);}
	.flex-layout-col-82  {.flex-grow(82);}
	.flex-layout-col-83  {.flex-grow(83);}
	.flex-layout-col-84  {.flex-grow(84);}
	.flex-layout-col-85  {.flex-grow(85);}
	.flex-layout-col-86  {.flex-grow(86);}
	.flex-layout-col-87  {.flex-grow(87);}
	.flex-layout-col-88  {.flex-grow(88);}
	.flex-layout-col-89  {.flex-grow(89);}
	.flex-layout-col-90  {.flex-grow(90);}
	.flex-layout-col-91  {.flex-grow(91);}
	.flex-layout-col-92  {.flex-grow(92);}
	.flex-layout-col-93  {.flex-grow(93);}
	.flex-layout-col-94  {.flex-grow(94);}
	.flex-layout-col-95  {.flex-grow(95);}
	.flex-layout-col-96  {.flex-grow(96);}
	.flex-layout-col-97  {.flex-grow(97);}
	.flex-layout-col-98  {.flex-grow(98);}
	.flex-layout-col-99  {.flex-grow(99);}
	.flex-layout-col-100 {.flex-grow(100);}
}
