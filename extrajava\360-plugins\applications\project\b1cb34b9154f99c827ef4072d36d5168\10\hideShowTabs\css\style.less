@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/variables.less";


@border-thickness: 3px;

.mashup {
.hideShowTabs{
	font-size: @m-font;
	
	&.no-tabs-row{
		.tabs{
			height: 0;
		    padding: 0;
		    border: 0;
		}
	}
	
	&.tab-title{
		.sectionTitle{
			margin-left: 10px;
			margin-top: 10px;
		}
	}
	
	.tabs{
		padding: ((@line-height /2) + @border-thickness + 1) 0;
		border-bottom: 1px solid @cblock-border;
		display: flex;
        .align-self(flex-start);
		
		.overview-tabs-tooltip{
			margin-left: 20px;
		    font-size: 17px;
			.appspopup{
				min-width: 500px;
				text-align: left;
				padding: 10px;
				li{
					list-style: initial;
					padding-left: 14px;
				}
			}
		}
	}
	
	.tab{
		display: inline;
		margin: 0 @line-height;
		padding-bottom: ((@line-height /2) + @border-thickness);
		cursor: pointer;
		
		.chartboard-edit-buttons {
			display: inline-block;
			.fonticon {
				font-size: 16px;
			}
		}
		
		&:hover{
			border-bottom: @border-thickness solid @ctext;
			padding-bottom: (@line-height /2);
			
			.transition(@transition-quick, "~padding-bottom");
		}
		
		&.active{
			border-bottom: @border-thickness solid @clink;
			padding-bottom: (@line-height /2);
			cursor: initial;
		}
		
	}
	
	.content.fixedHeight{
		position: relative;
		overflow: auto;
		.content-wrapper{
			position: absolute;
			width: 100%;
		}
	}
	
	.hideShowTabs-element{
		&.hideShowTabs-hidden{
			display: none;
		}
	} 
	.hideShowTabs-hidden{
		display: none;
	}

	.widgetContent.tabs .tab.hidden{
		display: none;
	}

}

}

