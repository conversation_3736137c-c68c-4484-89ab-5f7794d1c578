<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="security" uri="http://www.exalead.com/jspapi/security" %>
<%@ taglib prefix="searh" uri="http://www.exalead.com/jspapi/search" %>

<render:import varWidget="widget" varFeeds="feeds"/>

<config:getOption name="title" var="title" defaultValue=""/>
<config:getOption name="height" var="height" defaultValue="0"/>
<config:getOption name="resultListId" var="resultListId"/>

<config:getOption name="hitParamName" var="hitParamName" defaultValue="hit"/>
<config:getOption name="hitParamValue" var="hitParamValue" defaultValue="uri"/>

<config:getOption name="enableColsReorder" var="enableColsReorder" defaultValue="true"/>
<config:getOption name="saveState" var="saveState" defaultValue="true"/>
<config:getOption name="enableColsVisibility" var="enableColsVisibility" defaultValue="true"/>
<config:getOption name="enablePagination" var="enablePagination" defaultValue="true"/>
<config:getOption name="scrollY" var="scrollY" defaultValue="100%"/>
<config:getOption name="fixedHeader" var="fixedHeader" defaultValue="false"/>
<config:getOption name="infiniteScroll" var="infiniteScroll" defaultValue="false"/>
<config:getOption name="responsive" var="responsive" defaultValue="false"/>
<config:getOption name="enableHitDetails" var="enableHitDetails" defaultValue="false"/>
<config:getOption var="displayHeader" name="displayHeader" defaultValue="false"/>
<config:getOption var="buttonsIconSize" name="buttonsIconSize" defaultValue=""/>
<config:getOption var="enableExport" name="enableExport" defaultValue="false"/>
<config:getOptions var="channelNames" name="channels" />
<config:getOption var="enablePublication" name="enablePublication" defaultValue="false" />
<config:getOption var="enableSubscription" name="enableSubscription" defaultValue="false" />

<%--Get first feed--%>
<search:getFeed var="feed" feeds="${feeds}"/>
<search:getPageName var="page"/>
<widget:getUcssId var="uCssId"/>
<plma:resultListConfig var="columnsConfig" resultListId="${resultListId}" toMap="false"/>
<search:getPaginationInfos varStart="startIndex" varEnd="endIndex" varPerPage="perPage" varTotal="totalResults" feeds="${feeds}"/>

<url:url var="pageUrl" value="" keepQueryString="true"/>
<i18n:getLang var="lang"/>
<url:resource var="i18nFile" file="/resources/widgets/plmaSimpleDataTable/i18n/${lang}.json" testIfExists="false"/>

<plma:hitsDataTable pageURL="${pageUrl}" varDataTable="hitsDataTable" varData="hitsData" varColsDef="hitsColsDef" feed="${feed}" config="${columnsConfig}" withDefs="true" responsive="${responsive}"/>

<c:url value="/plma/ajax/datatable/hits/${page}/${feed.id}/${resultListId}?wuid=${widget.wuid}" var="ajaxURL">
    <plma:addRequestParams />
</c:url>

<widget:widget extraCss="plmaSimpleDataTable" varCssId="cssId" varUcssId="uCssId">
    <c:choose>
        <%-- If no feeds --%>
        <c:when test="${!search:hasFeeds(feeds)}">
            <widget:header>
                <config:getOption name="title" defaultValue=""/>
            </widget:header>
            <widget:content>
                <render:definition name="noFeeds">
                    <render:parameter name="widget" value="${widget}"/>
                    <render:parameter name="showSuggestion" value="true"/>
                </render:definition>
            </widget:content>
        </c:when>

        <%-- If no results --%>
        <c:when test="${plma:hasError(feed) || hitsDataTable.recordsTotal == 0}">
            <widget:content>
                <config:getOption var="customHTMLNoResultMessage" name="customHTMLNoResultMessage"/>
                <c:choose>
                    <c:when test="${not empty customHTMLNoResultMessage}">
                        <div class="noresult">
                                ${customHTMLNoResultMessage}
                        </div>
                    </c:when>
                    <c:otherwise>
                        <config:getOption var="noResultsJspPathHit" name="noResultsJspPathHit"
                                          defaultValue="/WEB-INF/jsp/commons/noResults.jsp"/>
                        <render:template template="${noResultsJspPathHit}">
                            <render:parameter name="accessFeeds" value="${feeds}"/>
                            <render:parameter name="showSuggestion" value="false"/>
                        </render:template>
                    </c:otherwise>
                </c:choose>
            </widget:content>
        </c:when>

        <%-- Feed has results --%>
        <c:otherwise>
            <div class="panels-container">
                <div class="results-panel">
                    <c:if test="${displayHeader == 'true'}">
                        <%-- Results --%>
                        <render:template template="templates/resultListHeader.jsp" widget="plmaResultListCommons">
                            <render:parameter name="feeds" value="${feeds}"/>
                            <render:parameter name="columnsConfig" value="${columnsConfig}"/>
                            <render:parameter name="wuid" value="${uCssId}"/>
                            <render:parameter name="layoutName" value="tile-layout"/>
                            <render:parameter name="disableResultListPrefTab" value="true"/>
                            <render:parameter name="recursiveButtonsWidgets" value=""/>
                            <%-- displayButtonsContainer and plmaButton widget are displayed in hit header, not result list  --%>
                            <render:parameter name="directButtonsWidgets" value="headerButtonsContainer"/>
                            <render:parameter name="buttonsIconSize" value="${buttonsIconSize}"/>
                            <%-- Parameters for JS creation  --%>
                            <render:parameter name="ajaxSort" value="false"/>
                            <render:parameter name="enableExport" value="${enableExport}"/>
                            <render:parameter name="channelNames" value="${channelNames}"/>
                            <render:parameter name="enablePublication" value="${enablePublication}"/>
                            <render:parameter name="enableSubscription" value="${enableSubscription}"/>
                        </render:template>
                    </c:if>

                    <table id="datatable_${uCssId}" class="plmaDataTable resultlistView dtTranspose"></table>
                    <c:set var="rowCallbacks" value="${plma:getParameterValues(widget, 'rowCallbackFunction')}"/>
                </div>
            </div>

            <render:renderScript position="READY">
                var plmaDataTable = new PlmaSimpleDataTable('${uCssId}', {
                ajaxURL: '${ajaxURL}',
                i18nFile: '${not empty i18nFile ? i18nFile : "" }',
                resultListId: '${resultListId}',
                data: ${plma:getJSON(hitsData, '[]')},
                colsReorder: ${enableColsReorder},
                saveState: ${saveState},
                colsDef: ${plma:getJSON(hitsColsDef, '[]')},
                totalResults: ${totalResults},
                perPage: ${perPage},
                pageName: '${page}',
                pageLengthButton: ${enablePagination && !infiniteScroll},
                colvisButton: ${enableColsVisibility},
                baseControllerURL: '<c:url value="/plma/ajax/datatable"/>',
                stateSaved: ${plma:hasSavedState(pageContext, resultListId)},
                scrollY: '${scrollY}',
                fixedHeader: ${fixedHeader},
                infiniteScroll: ${infiniteScroll},
                idAttribute: '${hitParamValue}',
                hitParamName: '${hitParamName}',
                enableHitDetails: ${enableHitDetails},
                responsive: ${responsive},
                wuid: '${widget.wuid}',
                <c:choose>
                    <c:when test="${plma:getBooleanParam(widget, 'enableDrawCallback', false )}">
                        drawCallback: ${plma:getStringParam(widget, 'drawCallbackFunction','function (settings) {}')}
                    </c:when>
                    <c:otherwise>
                        drawCallback: function (settings) {}
                    </c:otherwise>
                </c:choose>
                ,rowCallbacks: [${plma:join(rowCallbacks, ', ' )}]
                });
            </render:renderScript>
        </c:otherwise>
    </c:choose>
</widget:widget>



