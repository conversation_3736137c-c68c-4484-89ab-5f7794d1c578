var refineFromMetrics = function (newRefine) {
	var url = new BuildUrl(window.location.href);
	url.addParameter(newRefine.split('=')[0], newRefine.split('=')[1]);

	window.location.replace(url.toString());
};

var refinesFromMetrics = function (newRefines) {
	var url = new BuildUrl(window.location.href);
	var newRefinesTab = newRefines.split(',');
	for (var j = 0; j < newRefinesTab.length; j++) {
		url.addParameter(newRefinesTab[j].split('=')[0], newRefinesTab[j].split('=')[1]);
	}
	window.location.replace(url.toString());
};

var PLMAFacetHistoryMetrics = function (uCssId, data, options) {
	var defaults = {
		data: [],
		dataProcessor: function (data) {
			return data;
		},
		userOptions: {},
		defaultOptions: {},
		baseUrl: '',
		enableRefine: false
	};
	this.options = $.extend({}, defaults, options);
	this.uCssId = uCssId;
	this.data = JSON.parse(data);

	if (this.options.limit !== undefined  && this.options.limit !== ''){
        this.options.limit = parseInt(this.options.limit);
	}
	this.fillDataHoles();
	this.widget = $('.' + this.uCssId);
};

PLMAFacetHistoryMetrics.prototype.fillDataHoles = function () {
	if (this.data && this.data.length > 0 && this.data[0].series.length > 0) {
		var currentYear = moment().year();
		var currentWeek = moment().isoWeek();

		/* Fill holes in labelLinks */
		var tempLabel = 0;
		var tempValue = 0;
		var newLabelLinks = this.data[0].labelLinks;
		for (var label in this.data[0].labelLinks) {
			if (tempLabel !== 0 && tempLabel !== (parseInt(label) - 1) && (tempLabel.toString().substring(4, 6) !== '52' || label.substring(4, 6) !== '01')) {
				while (tempLabel !== (parseInt(label) - 1) && tempLabel !== parseInt(label)) {
					var firstTempLabel = tempLabel;
					if (parseInt(tempLabel.toString().substring(4, 6)) !== 52) {
						tempLabel++;
					} else {
						tempLabel = parseInt((tempLabel + 100).toString().substring(0, 4) + '01');
					}
					newLabelLinks[tempLabel] = tempValue.replace(firstTempLabel, tempLabel);
				}
			}
			tempLabel = parseInt(label);
			tempValue = this.data[0].labelLinks[label];
		}
		for (var i = parseInt(tempLabel.toString().substring(4, 6)) + 1; i <= currentWeek; i++) {
			var newLabel = currentYear + '' + i;
			newLabelLinks[newLabel] = tempValue.replace(tempLabel, newLabel);
		}
		this.data[0].labelLinks = newLabelLinks;

		/* Fill holes in series */
		var tempData = 0;
		var newDatas = this.data[0].series[0].data;
		for (var i = this.data[0].series[0].data.length - 1; i >= 0; i--) {
			var data = this.data[0].series[0].data[i];
			if (tempData !== 0 && tempData !== (parseInt(data.name) - 1) && (tempData.toString().substring(4, 6) !== '52' || data.name.substring(4, 6) !== '01')) {
				while (tempData !== (parseInt(data.name) - 1) && tempData !== parseInt(data.name) && tempData < parseInt(data.name)) {
					if (parseInt(tempData.toString().substring(4, 6)) !== 52) {
						tempData++;
					} else {
						tempData = parseInt((tempData + 100).toString().substring(0, 4) + '01');
					}
					var dataToAdd = jQuery.extend(true, {}, data);
					dataToAdd.name = tempData.toString();
					dataToAdd.y = 0;
					dataToAdd.id = dataToAdd.id.replace(data.name, tempData.toString());
					dataToAdd.refineParam = dataToAdd.refineParam.replace(data.name, tempData.toString());

					newDatas.unshift(dataToAdd);
				}
			}
			tempData = parseInt(data.name);
		}
		newDatas.sort(this.compare);
		var defaultData = this.data[0].series[0].data[this.data[0].series[0].data.length - 1];
		for (var i = parseInt((parseInt(defaultData.name) + 1).toString().substring(4, 6)); i <= currentWeek; i++) {
			var newData = jQuery.extend(true, {}, defaultData);
			newData.name = currentYear + '' + i;
			newData.y = 0;
			newData.id = newData.id.replace(defaultData.name, newData.name);
			newData.refineParam = newData.refineParam.replace(defaultData.name, newData.name);
			newDatas.push(jQuery.extend(true, {}, newData));
		}
		if (this.options.limit > 0 && newDatas.length > this.options.limit) {
			newDatas.splice(0, newDatas.length - this.options.limit);
		}
		newDatas.sort(this.compareDesc);
		this.data[0].series[0].data = newDatas;

		/* Fill holes in seriesCategories */
		var tempCat = 0;
		var newCats = this.data[0].seriesCategories;
		for (var i = this.data[0].seriesCategories.length - 1; i >= 0; i--) {
			var cat = this.data[0].seriesCategories[i];
			if (tempCat !== 0 && tempCat !== (parseInt(cat) - 1) && (tempCat.toString().substring(4, 6) !== '52' || cat.substring(4, 6) !== '01')) {
				while (tempCat !== (parseInt(cat) - 1) && tempCat !== parseInt(cat) && tempCat < parseInt(cat)) {
					if (parseInt(tempCat.toString().substring(4, 6)) !== 52) {
						tempCat++;
					} else {
						tempCat = parseInt((tempCat + 100).toString().substring(0, 4) + '01');
					}
					newCats.unshift(tempCat.toString());
				}
			}
			tempCat = parseInt(cat);
		}
		newCats.sort();
		for (var i = parseInt(this.data[0].seriesCategories[this.data[0].seriesCategories.length - 1].substring(4, 6)) + 1; i <= currentWeek; i++) {
			var newCat = currentYear + '' + i;
			newCats.push(newCat);
		}
		newCats.reverse();
		if (this.options.limit > 0 && newCats.length > this.options.limit) {
			newCats.splice(this.options.limit, newCats.length - this.options.limit);
		}
		this.data[0].seriesCategories = newCats;

		this.data[0].xAxis[0].categories = this.data[0].seriesCategories;
	}
};

PLMAFacetHistoryMetrics.prototype.compare = function (a, b) {
	if (a.name < b.name)
		return -1;
	if (a.name > b.name)
		return 1;
	return 0;
};

PLMAFacetHistoryMetrics.prototype.compareDesc = function (a, b) {
	if (a.name < b.name)
		return 1;
	if (a.name > b.name)
		return -1;
	return 0;
};

PLMAFacetHistoryMetrics.prototype.init = function () {
	if (this.options.reverse === 'true') {
		this.reverseData();
	}
	this.setMetric();
	this.setTrendColor();
	if (this.options.hideChart !== 'true') {
		this.createChart();
	}

	this.initDoc();
};

PLMAFacetHistoryMetrics.prototype.initDoc = function () {
	this.widget.find('.doc-button').on('click', $.proxy(function () {
		this.widget.find('.doc-container').removeClass('hidden');
	}, this));
	this.widget.find('.doc-container .close-doc').on('click', $.proxy(function () {
		this.widget.find('.doc-container').addClass('hidden');
	}, this));
};

PLMAFacetHistoryMetrics.prototype.reverseData = function () {
	if (this.data.length > 0 && this.data[0].series.length > 0 && this.data[0].series[0].data.length > 0) {
		//Reverse data in series
		var tempData = [];
		for (var i = this.data[0].series[0].data.length - 1; i >= 0; i--) {
			tempData.push(this.data[0].series[0].data[i]);
		}
		this.data[0].series[0].data = tempData;

		//Reverse xAxis
		var tempDataXAxis = [];
		for (var i = this.data[0].xAxis[0].categories.length - 1; i >= 0; i--) {
			tempDataXAxis.push(this.data[0].xAxis[0].categories[i]);
		}
		this.data[0].xAxis[0].categories = tempDataXAxis;

		//Reverse seriesCategories
		var tempDataSeries = [];
		for (var i = this.data[0].seriesCategories.length - 1; i >= 0; i--) {
			tempDataSeries.push(this.data[0].seriesCategories[i]);
		}
		this.data[0].seriesCategories = tempDataSeries;

	}
};

PLMAFacetHistoryMetrics.prototype.setMetric = function () {
	var lastValue = 0;
	if (this.data.length > 0 && this.data[0].series.length > 0 && this.data[0].series[0].data.length > 0) {
		lastValue = this.data[0].series[0].data[this.data[0].series[0].data.length - 1].y || 0;
	}
	if (Math.round(lastValue) !== lastValue) {
		lastValue = lastValue.toFixed(2);
	}
	this.widget.find('.metric')[0].innerHTML = lastValue;
	this.currentMetric = lastValue;
};

PLMAFacetHistoryMetrics.prototype.createChart = function () {
	var currentData = this.data[0];
	currentData.chart = {
		renderTo: this.widget.find('.chart-container')[0],
		plotBackgroundColor: this.currentColor,
		plotShadow: false,
		margin: [0, 0, 0, 0]
	};
	currentData.tooltip = this.options.tooltip;
	currentData.title = {text: null};
	currentData.credits = {enabled: false};
	currentData.yAxis = {
		visible: false,
		gridLineWidth: 0,
		minorGridLineWidth: 0,
		title: {
			text: null
		}
	};
	currentData.xAxis = {visible: false};
	currentData.legend = {enabled: false};

	if (this.data.length > 0 && this.data[0].series.length > 0 && this.data[0].series[0].data.length > 0) {
		currentData.series[0].color = 'white';
		currentData.series[0].borderWidth = 0;
		this.chart = highCharts.create({
			$widget: this.widget,
			$chartContainer: this.widget.find('.chart-container')[0],
			opts: currentData
		});
	}
};

PLMAFacetHistoryMetrics.prototype.setTrendColor = function () {
	var average = 0;
	if (this.data.length > 0 && this.data[0].series.length > 0 && this.data[0].series[0].data.length > 0) {
		for (var i = 0; i < this.data[0].series[0].data.length; i++) {
			average += this.data[0].series[0].data[i].y;
		}
		average = average / this.data[0].series[0].data.length;
	}

	this.currentColor = '#00B8DE';
	for (var j = 0; j < this.options.colors.length; j++) {
		if (average * Number(this.options.colors[j].start) <= this.currentMetric
			&& average * Number(this.options.colors[j].end) >= this.currentMetric) {
			this.currentColor = this.options.colors[j].value;
		}
	}

	this.widget.find('.metrics').css('background-color', this.currentColor);
	this.widget.find('.chart-container').css('border-bottom', '1px solid ' + this.currentColor);
};

(function () {

})(jQuery);
