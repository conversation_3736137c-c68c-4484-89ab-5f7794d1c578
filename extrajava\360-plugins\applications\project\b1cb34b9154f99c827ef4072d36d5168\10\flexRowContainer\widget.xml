<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Flex row container" group="PLM Analytics/Layout/Flex display" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget creates a CSS flexbox container. It allows you to display its subwidgets side-by-side.</Description>

	<Preview>
	<![CDATA[
		
	]]>
	</Preview>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Includes>
		<Include type="css" path="css/style.less" />
	</Includes>

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportWidgetsId arity="ZERO_OR_MANY"/>
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportI18N supported="true" />
	
	<OptionsGroup name="General">
		<Option id="widgetTags" name="Embed in widget tags">
			<Description>Renders the subwidgets in a wrapper widget. Otherwise, they are rendered inside mere divs.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="title" name="Title">
			<Description>Widget title. If empty or if subwidgets are not embedded in widget tags, no name is displayed.</Description>
		
		</Option>
		<Option id="minWidth" name="Min-width for Children widgets">
			<Description>Defines the minimum width of the subwidgets.</Description>
		
		</Option>

		<OptionComposite id="variables" name="Additional CSS variables" arity="ZERO_OR_MANY" glue="##" isEvaluated="true">
			<Description>Add variables to widget div, it can be useful to configure CSS using variables for example.</Description>
			<Option id="name" name="Name" arity="ONE" isEvaluated="true">
				<Description>Variable name to add.</Description>
				<Functions>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="value" name="Value" arity="ONE" isEvaluated="true">
				<Description>Aggregation function expression.</Description>
				<Functions>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
		</OptionComposite>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="widgetTags">false</DefaultValue>
	</DefaultValues>
</Widget>
