/**
 * ExportTo3DSpace is global object to manage the Export of Content to the 3DSpace.
 * Main purpose of this class is to create single instance of POPUP dialog and manage
 * its contents.
 * The object exposed to outside world provides only required methods.
 * */
(function(window){
    var ExportTo3DSpace = function(options) {
        this.i18nMap = {};
        this.lightbox = undefined;
        this.$elements = {};
        this.options = $.extend(null, {
			title: 'ExportTo3DSpace POPUP'
		}, options);
        this.init();
        return this;
    }
    ExportTo3DSpace.prototype.getMessage = function(code) {
        return mashupI18N.get('plmaResources', code);
    };
		
    /**
     * Initialize dialog box popup. Create 2 main containers as follows.
     * 1. EXPort Context Container : To get the definition of 3DExp Context Object
     *   where the exported contents will be attached.
     * 2. Preview Container: Container to hold the preview of the containts to be exported(img, pdf etc)
     * Also Initialize the 3DSpace Document Manager whose jobs is to handle all interactions with 3DSpace.
     * */
    ExportTo3DSpace.prototype.init = function (successCallback){
		let $exportContainer = $('<div id="export-container-global">' +
			'<div class="container">' +
				'<div class="exp-context-container"></div>'+
				'<div class="preview-container" width="100%">' +
					'<div class="preview-header">' +
					' </div>' +
					'<div class="preview-content"></div>' +
				'</div>'+
			'</div>'+
		'</div>');
		this.autoHideDelayDefault = $.notify.defaults().autoHideDelay;
        this.lightbox = new PLMALightbox({
            title: this.options.title,
            content: $exportContainer,
            extraCss: "export-to-3dspace",
            onShow: (function(){
                $.notify.defaults({autoHideDelay: 10000});
            }).bind(this),
            onHide: (function(){
                $.notify.defaults({autoHideDelay: this.autoHideDelayDefault});
                this.reset();
            }).bind(this),
            draggable : false
        });

		if(this.options.$tabsContainer){	
			this.options.$tabsContainer.appendTo($exportContainer.find('.exp-context-container'));
			this.options.$tabsContainer.tabs();
			
			this.$elements.EXP_CONTEXT_CONTAINER = $exportContainer.find('.exp-context-container .contents .tabs-publish-bookmark');
			this.$elements.SWYM_EXP_CONTEXT_CONTAINER = $exportContainer.find('.exp-context-container .contents .tabs-publish-swym');
			
			this.$elements.PREVIEW_CONTAINER = $exportContainer.find('.preview-container');			
		}else{
			this.$elements.EXP_CONTEXT_CONTAINER = $exportContainer.find('.exp-context-container');
			this.$elements.PREVIEW_CONTAINER = $exportContainer.find('.preview-container');			
		}
    }


    /**
     * Reset the Dialog box once the Export is done. This enables the reuse
     * of same POPUP Dialog for any exports.
     * */
    ExportTo3DSpace.prototype.reset = function(){
        this.$elements.EXP_CONTEXT_CONTAINER.empty();
        this.$elements.PREVIEW_CONTAINER.find('.preview-header').empty();
        this.$elements.PREVIEW_CONTAINER.find('.preview-content').empty();
		
		if(this.$elements.SWYM_EXP_CONTEXT_CONTAINER){
			this.$elements.SWYM_EXP_CONTEXT_CONTAINER.empty();
		}
    }

    // Singleton Object of the exportTo3DSpace per unique ID.
    var instances = new Map();
    ExportTo3DSpace.getInstance = function(options){
        if(instances[options.id] === undefined){
            instances[options.id] = new ExportTo3DSpace(options);
        }
        return instances[options.id];
    };

    /**
     * Expose the methods which will help in export.
     * */
    window.exportTo3DSpace = {
        /**
         * startDocumentExport method helps caller to open the POPUP and provide the Preview Helper.
         * @param {Object} options - Options for Exporting.
         *          fileExtension: Represents the content Type. Needed for Validation else the file
         *              written will be corrupt.
         *          spaceUrl : 3DSpace URL(if empty, we will call the 3DEXP API's to get the URL),
         *          enableAddNewDocument : Enable Creation of new Document(default false)
         * @return {Object} - PreviewHelper object which will have container and doneCallback.
         *          Once the Export Data is created and added to Preview Container(dataUrl), caller
         *          Must call the doneCallback method with the Appropriate Information about the format
         *          and how to find the DataURL. The DataUrl will be converted to Blob and then uploaded to
         *          3DSpace FCS Storage.
         * */
        startDocumentExport : function(options){
            var _instance = ExportTo3DSpace.getInstance(options);
			_instance.reset();
            if(options.fileMode && options.fileExtension == undefined){
                $.notify(_instance.getMessage('plma.exportto3dspace.error.configurations'), 'error');
                throw "Need Option 'fileExtention' to make sure the content type is correct.";
            }
            if( typeof options.spaceUrl == "string" && options.spaceUrl.length < 12){ // https://a.c
                options.spaceUrl = undefined;
            }
			_instance.$elements.PREVIEW_CONTAINER.closest('.export-to-3dspace').addClass(options.id);
            let documentManager = new DocumentManager3DSpace(_instance.$elements.EXP_CONTEXT_CONTAINER, options);
            _instance.lightbox.show();
			
			let swymManager = undefined;
			if(options.isSwymEnable){
				swymManager = new SwymPublishManager(_instance.$elements.SWYM_EXP_CONTEXT_CONTAINER, 
					$.extend({}, options , {enableAddNewDocument:true}));
			}
			
            var previewHelper = {
                container: _instance.$elements.PREVIEW_CONTAINER,
                /**
                 *  Need to provide function which returns the DataUrl to convert
                 *  to Blob/File to be uploaded to FCS
                 * */
                doneCallback: function(getDataUrl, fileName, updateDocInfo, updateSwymInfo){
                    documentManager.previewLoaded({
                        getDataUrl: getDataUrl,
                        fileName: fileName,
						updateDocInfo: updateDocInfo
                    });
					if(swymManager){
						swymManager.previewLoaded({
							getDataUrl: getDataUrl,
							fileName: fileName,
							updateSwymInfo: updateSwymInfo
						});
					}
                }
            }
            return previewHelper;
        }
    }
	return window.exportTo3DSpace;
})(window);