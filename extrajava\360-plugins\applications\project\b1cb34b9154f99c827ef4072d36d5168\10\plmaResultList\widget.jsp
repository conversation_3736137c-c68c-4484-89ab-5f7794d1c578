<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds"/>

<%-- Check if it is an infiniteScroll request --%>
<request:isAjax var="isAjax"/>
<request:getParameterValues var="infiniteScrollValues" name="infiniteScroll"/>
<request:getParameterValues var="paginationLoad" name="paginationLoad"/>
<request:getParameterValues var="isInfiniteScrollRequest" name="isInfiniteScrollRequest"/>
<c:set var="isAnInfiniteScrollRequest" value="false"/>
<c:forEach var="value" items="${infiniteScrollValues}">
	<c:if test="${value!=''}"><c:set var="isAnInfiniteScrollRequest" value="true"/></c:if>
</c:forEach>

<config:getOption name="enableInfiniteScroll" var="enableInfiniteScroll"/>
<config:getOption name="containerHeight" var="containerHeight"/>
<config:getOption name="defaultLayout" var="defaultLayout" defaultValue="Responsive"/>
<config:getOption var="pinCompareRef" name="pinCompareRef" defaultValue="false"/>
<config:getOption var="showThumbnail" name="showThumbnail" defaultValue="false"/>
<config:getOption var="enableExport" name="enableExport" defaultValue="false"/>
<config:getOption var="numHits" name="numHits" defaultValue="0"/>
<config:getOption var="customExportJSP" name="customExportJSP" defaultValue="templates/exportDisplay.jsp" />
<config:getOption name="enableResizeColumn" var="enableResizeColumn" defaultValue="false" />
<search:getPaginationInfos varStart="startIndex" varEnd="endIndex" varTotal="totalResults" feeds="${feeds}"/>
<c:set var="totalResultsDefault" value="${empty totalResults ? 0 : totalResults}" />

<config:getOption var="enablePublication" name="enablePublication" defaultValue="false" />
<config:getOption var="enableSubscription" name="enableSubscription" defaultValue="false" />
<config:getOptions var="channelNames" name="channels" />

<widget:getUcssId var="uCssId"/>
<c:set var="scrollContainerId" value="${uCssId}_scroll-container" />
<c:if test="${showThumbnail}">
	<config:setOption name="mainContainerId" value="${scrollContainerId}" component="${widget}"/>
</c:if>

<c:set var="isGridTable" value="${defaultLayout == 'GridTable'}"/>
<c:set var="isGridDefault" value="${defaultLayout == 'GridTable'}"/>
<config:getOption var="doTrasnspose" name="doTrasnspose" defaultValue="false"/>

<%-- Use config value if parameter is absent else choose from the param value --%>
<request:getParameterValue var="gridtableParam" name="gridtable" defaultValue=""/>
<c:if test="${gridtableParam != ''}">
	<c:set var="doTrasnspose" value="${gridtableParam == 'transpose'}"/>
	<config:setOption name="defaultLayout" value="GridTable" component="${widget}"/>
	<config:getOption name="defaultLayout" var="defaultLayout" defaultValue="Responsive"/>
	<c:set var="isGridTable" value="true"/>
</c:if>

<request:getParameterValue var="switchView" name="switchView" defaultValue=""/>
<c:if test="${isGridTable && switchView != ''}">
	<c:set var="doTrasnspose" value="${gridtableParam == 'transpose'}"/>
	<config:getOption name="switchLayout" var="switchLayout"/>
	<config:setOption name="defaultLayout" value="${switchLayout}" component="${widget}"/>
	<config:getOption name="defaultLayout" var="defaultLayout" defaultValue="Responsive"/>
	<c:set var="isGridTable" value="false"/>
</c:if>

<config:getOption var="iconTitleContainerWidth" name="iconTitleContainerWidth" defaultValue="false"/>
<c:if test="${iconTitleContainerWidth}">
    <c:set var="iconContainerWidth" value=""/>
    <c:set var="titleContainerWidth" value=""/>
    <config:getOption var="iconContainerSize" name="iconContainerWidth"/>
    <config:getOption var="titleContainerSize" name="titleContainerWidth"/>
    <c:if test="${iconContainerSize != null && defaultLayout == 'GridTable'}"><c:set var="iconContainerWidth" value="--icon-container-size:${iconContainerSize}px;"/></c:if>
    <c:if test="${titleContainerSize != null && defaultLayout == 'GridTable'}"><c:set var="titleContainerWidth" value="--title-container-size:${titleContainerSize}px;"/></c:if>
</c:if>

<config:getOption var="enableNewConfig" name="enableNewConfig" defaultValue="true"/>
<config:getOption var="useExternalisedConfig" name="useExternalisedConfig" defaultValue="false"/>
<config:getOption name="disableResultListPrefTab" var="disableResultListPrefTab" defaultValue="false" />
<c:choose>
	<c:when test="${enableNewConfig == 'true' && useExternalisedConfig == 'true'}">
	    <plma:getOptionComposite var="externalConfig" name="externalConfig" mapIndex="true"/>
		<plma:resultListConfig var="columnsConfig" configName="${externalConfig.configName}" resultListId="${externalConfig.resultListId}"/>
		<plma:resultListJSONConfig var="resultListJSON" configName="${externalConfig.configName}" resultListId="${externalConfig.resultListId}"/>
	</c:when>
	<c:when test="${enableNewConfig == 'true'}">
	    <config:getOptionsComposite var="columnsConfig" name="columnsConfig" mapIndex="true"/>
	</c:when>
</c:choose>

<c:choose>
	<c:when test="${isAjax && (isAnInfiniteScrollRequest || fn:length(paginationLoad) > 0)}">
		<render:template template="templates/hits.jsp">
			<render:parameter name="feeds" value="${feeds}"/>
			<render:parameter name="widget" value="${widget}"/>
			<render:parameter name="uCssId" value="${uCssId}"/>
			<render:parameter name="columnsConfig" value="${columnsConfig}"/>
			<render:parameter name="doTrasnspose" value="${doTrasnspose? 'transpose' : ''}"/>
		</render:template>
		<c:if test="${search:hasEntries(feeds) == true && !enableInfiniteScroll}">
			<div class="pagination">
				<render:template template="templates/pagination.jsp" widget="plmaPagination">
					<render:parameter name="accessFeeds" value="${feeds}"/>
					<%--<render:parameter name="cssId" value="${cssId}"/>--%>
					<render:parameter name="wuid" value="${uCssId}"/>
				</render:template>
			</div>
		</c:if>
	</c:when>

	<c:otherwise>
		<widget:widget extraCss="plmaResultList" varCssId="cssId" varUcssId="uCssId"
					   extraStyles="height: ${not empty containerHeight ? containerHeight.concat('px') : '100%'}">
			<c:choose>

				<%-- If widget has no Feed --%>
				<c:when test="${search:hasFeeds(feeds) == false}">
					<widget:header>
						<config:getOption name="title" defaultValue=""/>
					</widget:header>
					<widget:content>
						<render:definition name="noFeeds">
							<render:parameter name="widget" value="${widget}"/>
							<render:parameter name="showSuggestion" value="true"/>
						</render:definition>
						<render:template template="templates/select-api.jsp" />
					</widget:content>
				</c:when>

				<%-- If all feeds have no results --%>
				<c:when test="${search:hasEntries(feeds) == false}">
					<widget:header extraCss="empty-result">
						<config:getOption name="title" defaultValue=""/>
					</widget:header>
					<widget:content>
						<config:getOption var="customHTMLNoResultMessage" name="customHTMLNoResultMessage"/>
						<c:choose>
							<c:when test="${not empty customHTMLNoResultMessage}">
								<div class="noresult">
									${customHTMLNoResultMessage}
								</div>
							</c:when>
							<c:otherwise>
								<config:getOption var="noResultsJspPathHit" name="noResultsJspPathHit"
												  defaultValue="/WEB-INF/jsp/commons/noResults.jsp"/>
								<render:template template="${noResultsJspPathHit}">
									<render:parameter name="accessFeeds" value="${feeds}"/>
									<render:parameter name="showSuggestion" value="true"/>
								</render:template>
							</c:otherwise>
						</c:choose>
						<render:template template="templates/select-api.jsp" />
					</widget:content>
					<%-- /If all feeds have no results --%>
				</c:when>

				<c:otherwise>

					<div class="panels-container">
						<div class="results-panel">
								<%-- Results --%>
							<c:set var="responsiveLayoutCssClass" value="${defaultLayout == 'Article Responsive' ? ' article-view table':'table'}"/>
							<div class="resultsTitle ${defaultLayout == 'Responsive' || defaultLayout == 'Article Responsive' ? responsiveLayoutCssClass : fn:toLowerCase(defaultLayout)}-layout">

								<span class="widgetTitle">
									<config:getOption name="title" defaultValue=""/>
								</span>

								<span class="title">
                                    <%-- Num results --%>
                                    <search:getPaginationInfos varStart="startIndex" varEnd="endIndex"
															   varTotal="totalResults" feeds="${feeds}"/>
                                    <c:choose>
										<c:when test="${totalResults > 0}">
											<i18n:message code="hitcustom.title" arguments="${totalResults}"/>
										</c:when>
										<c:otherwise>
											<i18n:message code="hitcustom.no_result"/>
										</c:otherwise>
									</c:choose>
                                </span>

								<div class="global-toolbar">
									<render:template template="templates/sortDisplay.jsp">
										<render:parameter name="feeds" value="${feeds}"/>
										<render:parameter name="columnsConfig" value="${columnsConfig}"/>
									</render:template>
									<url:url var="gridUrl" keepQueryString="true" />
                                    <c:if test="${enableExport}">
										<config:getOption var="exportTabResultListId" name="resultListId" defaultValue="${externalConfig.resultListId}" />
                                        <plma:resultListConfig var="exportColumnsConfig" resultListId="${exportTabResultListId}"/>
										<render:template template="${customExportJSP}">
                                            <render:parameter name="feeds" value="${feeds}"/>
                                            <render:parameter name="columnsConfig" value="${exportColumnsConfig}"/>
                                            <render:parameter name="uCssId" value="${uCssId}"/>
                                        </render:template>
                                    </c:if>
									<span class="insert-after"></span>
									<span class="display-description-button">
										<config:getOption name="hitContentDisplay" var="hitContentDisplay" defaultValue="false"/>
										<request:getCookieValue name="description_result_list${uCssId}" var="displayDescription" defaultValue="${hitContentDisplay}"/>
										<span class="icon icon-visible fonticon fonticon-eye-off ${displayDescription == 'true' ? '' : 'hidden'}"
											title="<i18n:message code='hitcustom.column.description.hide'/>"
										></span>
										<span class="icon icon-hidden fonticon fonticon-eye ${displayDescription == 'true' ? 'hidden' : ''}"
											title="<i18n:message code='hitcustom.column.description.display'/>"
										></span>
										<span class="label label-visible ${displayDescription == 'true' ? '' : 'hidden'}"><i18n:message code="hitcustom.column.description.hide"/></span>
										<span class="label label-hidden ${displayDescription == 'true' ? 'hidden' : ''}"><i18n:message code="hitcustom.column.description.display"/></span>
									</span>									
									<span class="insert-before"></span>
									<c:if test="${enableNewConfig == 'true' && useExternalisedConfig == 'true' && disableResultListPrefTab == 'false'}">
										<span class="action-display-settings" title="<i18n:message code='plma.resultlist.tab.title'/>">
											<span class="fonticon fonticon-cog"></span>
										</span>
									</c:if>
								</div>
								<div class="select-all-container"></div>

								<c:if test="${!isGridTable}">
									<div class="table-header">
										<render:template template="templates/tableHeader.jsp">
											<render:parameter name="feeds" value="${feeds}"/>
											<render:parameter name="widget" value="${widget}"/>
											<render:parameter name="uCssId" value="${uCssId}"/>
											<render:parameter name="columnsConfig" value="${columnsConfig}"/>
											<render:parameter name="doTrasnspose" value="${doTrasnspose? 'transpose' : ''}"/>
										</render:template>
									</div>
								</c:if>
							</div>

							<c:set var="gridtableCSS" value=""/>
							<c:if test="${isGridTable}">
								<c:set var="gridtableCSS" value="${enableResizeColumn == 'true' ? 'grid-resize' : ''} ${fn:toLowerCase(defaultLayout)}-layout ${fn:toLowerCase(defaultLayout)}${doTrasnspose? '-transpose' : ''}"/>
							</c:if>
							<div id="${scrollContainerId}" data-default-gridtable="${isGridDefault}" class="scroll-container ${gridtableCSS}" style="height: 800px; ${titleContainerWidth} ${iconContainerWidth}">
								<c:if test="${isGridTable}">
									<ul class="fix-header hitCustomList">
										<li class="grid-row table-header">
											<render:template template="templates/tableHeader.jsp">
												<render:parameter name="feeds" value="${feeds}"/>
												<render:parameter name="widget" value="${widget}"/>
												<render:parameter name="uCssId" value="${uCssId}"/>
												<render:parameter name="columnsConfig" value="${columnsConfig}"/>
												<render:parameter name="isGridTable" value="${isGridTable}"/>
												<render:parameter name="doTrasnspose" value="${doTrasnspose? 'transpose' : ''}"/>
											</render:template>
										</li>
									</ul>
								</c:if>
								<c:set var="responsiveLayoutCssClass" value="${defaultLayout == 'Article Responsive' ? 'responsive-layout article-view table':'responsive-layout table'}"/>
								<ul class="hits hitCustomList ${defaultLayout == 'Responsive' || defaultLayout == 'Article Responsive' ? responsiveLayoutCssClass : fn:toLowerCase(defaultLayout)}-layout" ucssId="${uCssId}" data-pin-ref="${pinCompareRef}">
									<render:template template="templates/hits.jsp">
										<render:parameter name="feeds" value="${feeds}"/>
										<render:parameter name="widget" value="${widget}"/>
										<render:parameter name="uCssId" value="${uCssId}"/>
										<render:parameter name="columnsConfig" value="${columnsConfig}"/>
										<render:parameter name="doTrasnspose" value="${doTrasnspose? 'transpose' : ''}"/>
									</render:template>
								</ul>
								<c:if test="${search:hasEntries(feeds) == true && enableInfiniteScroll}">
                                    <div class="loading-indicator-block">
                                        <span class="label fonticon"><i18n:message code="plma.loading.results.label" text="Loading...."/></span>
                                        <span class="loading-spinner small"></span>
                                    </div>
                                </c:if>
							</div>
						</div>

						<c:if test="${search:hasEntries(feeds) == true && !enableInfiniteScroll}">
							<div class="pagination">
								<render:template template="templates/pagination.jsp" widget="plmaPagination">
									<render:parameter name="accessFeeds" value="${feeds}"/>
									<%--<render:parameter name="cssId" value="${cssId}"/>--%>
									<render:parameter name="wuid" value="${uCssId}"/>
								</render:template>
							</div>
						</c:if>
					</div>
				</c:otherwise>
			</c:choose>

			<config:getOption name="JSMode" var="JSMode" defaultValue="Custom JS"/>
            <c:choose>
                <c:when test="${JSMode == 'Custom JS'}">
                    <config:getOption name="onHitClick" var="onHitClick"/>
                    <config:getOption name="onInit" var="onInit"/>
                </c:when>
                <c:otherwise>
                    <c:set var="onInit">
                        function() {
                            new GridTableSetup(widget, {
                                buttonSelectors: [
                                '.plmaButton.freeze-row-button',
                                '.plmaButton.favorite-button',
                                '.plmaButton.compare-button'
                                ]
                            });

                            var helper = new DetailHitHelper($('.plmaResultList .hits').hasClass('responsive-layout'));
                            //helper.initResponsive();
                            /* if an anchor is present, select the right hit and open the detail panel */
                            if(window.location.href.split('#').length > 1) {
                                var anchor = window.location.href.split('#')[1];

                                helper.selectHitFromAnchor(anchor);
                                helper.loadDetailFromAnchor(anchor,'hit');
                            }
                        }
                    </c:set>
                    <c:set var="onHitClick">
                        function(e) {
                            if($(e.target).parent().is('.category-link')){
                              return;
                             }
                            /* Reload the detail widget */
                            var $hit = $(e.target).closest('.hit');
                            /* if an anchor is present, check if hit already selected and deselect it */
                            if (window.location.href.split('#').length > 1) {
                            var anchor = window.location.href.split('#')[1];
                            }
                            if ($hit.data('uri') != anchor ) {
                            var helper = new DetailHitHelper($('.plmaResultList .hits').hasClass('responsive-layout'));
                            helper.selectHit($hit);
                            var entryUri = $hit.data('uri');
                            var paramName = $hit.data('parameter');
                            $('.selection-panel').addClass('hidden');
                            helper.loadDetail(paramName,entryUri);
                            $(window).trigger('resize');
                            /* push state in url */
                            helper.pushState(entryUri);
                            } else {
                            // Close detail panel
                            var helper = new DetailHitHelper();
                            helper.closeDetailPanel();
                            helper.pushState();
                            helper.deselectHit();
                            helper.displayTileView();
                            $(window).trigger('resize');
                            }
                        }
                    </c:set>
                </c:otherwise>
            </c:choose>
			<config:getOption name="ajaxSort" var="ajaxSort" defaultValue="false"/>
            <config:getOption name="customAbsoluteBlock" var="customAbsoluteBlock" defaultValue="" />
			<render:renderScript position="READY">
				var feedsName = [<search:forEachFeed var="feed" varStatus="feedStatus" feeds="${feeds}">'${feed.id}'<c:if
				test="${!feedStatus.last}">,</c:if></search:forEachFeed>];
				<string:escape var="entryUri" value="${entryUri}" escapeType="JAVASCRIPT"/>
				var options = {};
				options['ajaxSorting'] = ${ajaxSort};
				options.feedsName = feedsName;
				options.enableExport = ${enableExport};
				options.numHits= ${numHits};
                options.totalResultsDefault = ${totalResultsDefault};
				options.selectionConfig = {
					topicNames: [<c:forEach items="${channelNames}" var="channelName">'${channelName}',</c:forEach>],
					enablePublication: ${enablePublication},
					enableSubscription: ${enableSubscription}
				}
				var plmaResultList = new PlmaResultList('${uCssId}', options);
				<search:getPaginationInfos feeds="${feeds}" varCurrentPage="currentPage" varLastPage="maxPages"/>

				<config:getOptionsComposite var="infiniteScrollExtraParameters" name="infiniteScrollExtraParameters" mapIndex="true"/>
				var extraParameters = {};
				<c:forEach items="${infiniteScrollExtraParameters}" var="infiniteScrollExtraParameter">
					extraParameters["${infiniteScrollExtraParameter.parameterName}"] = ["<string:escape value="${infiniteScrollExtraParameter.parameterValue}" escapeType="JAVASCRIPT"/>"];
				</c:forEach>
				<c:if test="${search:hasEntries(feeds) == true && enableInfiniteScroll}">
					new PlmaInfiniteScroll('${uCssId}', ${maxPages}, {
						feedsName: feedsName,
						scrollOrientation: '${doTrasnspose? 'x' : 'y' }',
						pageInWidgetSelector: '.results-panel > .scroll-container .hits',
						getRelativeBlock: function(){
							var relativeBlock = $('.${uCssId} .results-panel > .scroll-container .hits');
							return relativeBlock;
						},
						getAbsoluteBlock: function(){
							var absoluteBlock = $('.${uCssId} .results-panel > .scroll-container');
							var customAbsoluteBlock = $("${customAbsoluteBlock}");
							absoluteBlock = (customAbsoluteBlock.length>0?customAbsoluteBlock:absoluteBlock);
							return absoluteBlock;
						},
						pxBeforeEndToTrigger: '100',
						getNewPageParams: function(){
							var hasTableView = !$($('.${uCssId} .hit .hit-table')[0]).hasClass('hidden');
							var params = {};
							params.isTableView = hasTableView;
							params = $.extend(params, PlmaResultList._PARAMS, extraParameters);
							return params;
						},
						onLoadPageSuccessEnd: function() {
							$(window).trigger(PlmaInfiniteScroll.PAGE_LOADED);
						},
                        loadingBlockSelector: '.loading-indicator-block'
					});
				</c:if>
				<c:if test="${enableNewConfig == 'true' && useExternalisedConfig == 'true' && disableResultListPrefTab == 'false'}">
					<plma:getOptionComposite var="externalConfig" name="externalConfig" mapIndex="true"/>
					var options = {
						buttonSelector: '.global-toolbar .action-display-settings'
					};
<%--					options.appConfig = {--%>
<%--						configName: '${externalConfig.configName}',--%>
<%--						resultListId: '${externalConfig.resultListId}'--%>
<%--					};--%>
			
					new PlmaResultListPref('${uCssId}', options, ${resultListJSON});
<%--					new PlmaResultListPrefTab();--%>
				</c:if>
			</render:renderScript>

			<%-- render on init and on hit click events --%>
			<render:renderOnce id="${uCssId}">
				<render:renderScript>
					(function() {
					var widget = $('.${uCssId}');
					(${onInit}).call(null, widget);

					$('.${uCssId}').on('click', "li.hit", function(e, data) {
					if($(e.target).hasClass('fonticon-open-down')){
                        $(e.target).closest('.large-content').find('.large-content-tooltip').toggleClass('hidden');
                        return;
                    }
					(${onHitClick}).call(widget, e);
					document.querySelector('li[data-uri="' + this.attributes['data-uri'].value + '"]').scrollIntoView({ block: 'nearest' });
					//(document.querySelector('li[data-uri="' + this.attributes['data-uri'].value + '"]').scrollIntoView({ block: 'nearest' })).call(widget, e);
					<c:if test="${enablePublication}">
						if (!data || !data.preventPublish) {
							widget.data('widget').setSelectedHits([$(e.currentTarget).data('hitId')]);
						}
					</c:if>
					});
					})();
				</render:renderScript>
			</render:renderOnce>

		</widget:widget>
	</c:otherwise>
</c:choose>

