var AppsPopup = (function(){
	
	/* Constants and templates */
	var arrow = $('<div class="appspopup-arrow"></div>');
	var wrapper = $('<div class="appspopup-wrapper"></div>');
	var popup = $('<div class="appspopup"></div>');
	var directions = ["up", "down", "left", "right"];
	var modes = ["hover", "click"];

	/* Object constructor */
	return function(options){
		this.options = {
			button: null, /* button triggerring the popup */
			contentText: null, /* text to put in the popup */
			content: null, /* jQuery DOM object. overrides contentText */
			extraCss: null, /* appended to the popup's class attribute */
			mode: "hover", /* can be "hover" or "click" */
			delay: 400, /* delay waited before opening or closing the popup */
			delayClose: null, /* delay waited before closing the popup, if null 'delay' is used */
			direction: "up", /* where the popup should open. Can be "up", "down", "left", "right */
			appendTo: null, /* if the popup should not be appended to the button, use this option to specify the element to which to append the popup. The popup will have its position property set to fixed. Useful when playing with z-indexes or scroll:hidden.*/
			closeOnClickOutside: false, /* Close the popup if you click outside of it, instead of only clicking on the button. */
			onInit: $.noop,
			onOpen: $.noop,
			onClose: $.noop,
			autoposition: true, /* the position should be computed automatically */
		};
		
		this._wait = false;
		
		this.init = function(options){
			this.options = $.extend({}, this.options, options);
			this.button = $(this.options.button);
			if (this.options.content){
				this.content = $(this.options.content);	
			}else{
				this.content = $("<span><span/>").text(this.options.contentText);
			}
			if (directions.indexOf(this.options.direction) == -1){
				this.options.direction = directions[0];
			}
			if (modes.indexOf(this.options.mode) == -1){
				this.options.mode = modes[0];
			}
			this.arrow = arrow.clone();
			this.wrapper = wrapper.clone();
			this.popup = popup.clone();
			this.popup.addClass("direction-" + this.options.direction);
			if (this.options.extraCss){
				this.popup.addClass(this.options.extraCss);
			}
			
			
			this.button.addClass("appspopup-button");
			this.content.addClass("appspopup-content");
			this.popup.append(this.arrow).append(this.wrapper);
			this.wrapper.append(this.content);
			
			this.positionSet = !this.options.autoposition;
			
			var parent;
			if (this.options.appendTo){
				parent = this.options.appendTo;
				this.popup.addClass("appspopup-positionned");
			}else{
				parent = this.button;
			}
			
			parent.append(this.popup);
			this.button.data('popup', this);

			this.hasOpenCallback = $.isFunction(this.options.onOpen);
			this.hasCloseCallback = $.isFunction(this.options.onClose);
			
			this.initEventHandlers(this.options.mode);
			
			if ($.isFunction(this.options.onInit)){
				this.options.onInit.call(this, this.popup);
			}
		};
		
		this.init(options);
		return this;
	};
		
})();

AppsPopup.prototype.initEventHandlers = function(mode){
	switch(mode){
	case "click":
		this.button.on("click", $.proxy(function(e){
			if (!this.isOpen()){
				this.open();
			}else{
				if (!$.contains(this.content.get(0), e.target)){
					this.close();
				}
			}
		}, this));
		
		
		// Close the popup if click is on outside
		// if option is activated.
		if(this.options.closeOnClickOutside){
			
			var wholePage = $('html');
			wholePage.on("click", $.proxy(function(e){
				
				if(this.isOpen()){
					// Detect if click is outside the popup
					var $e = $(e.target); 
					var $popup = $e.closest(".appspopup-wrapper");
					
					if($popup.length == 0 || this.wrapper.get(0) != $popup.get(0)){
						this.close();
					}
				}
			}, this));	
		}
		
		break;
	case "hover":
		this.button.hover(
				$.proxy(function(){
					this.open();
					this.button.addClass("appspopup-hover");
				}, this), 
				$.proxy(function(){
					this.close();
					this.button.removeClass("appspopup-hover");
				}, this));
		if (this.options.appendTo){
			this.popup.on("mouseenter", $.proxy(function(){
				this.popup.addClass("appspopup-hover");
			}, this));
			this.popup.on("mouseleave", $.proxy(function(){
				this.close();
				this.popup.removeClass("appspopup-hover");
			}, this));
		}
		this.button.on("mousedown", $.proxy(function(e){
			if (!$.contains(this.content.get(0), e.target)){
				this.close();
			}
		}, this));
		break;
	}
	this.popup.on("open-container", $.proxy(function(e, options){
		this.open(options ? options.delay : undefined);
	}, this));
	this.popup.on("close-container", $.proxy(function(e, options){
		e.stopPropagation();
		this.close(options ? options.delay : undefined);
		return false;
	}, this));
	
	/* If the popup is position:fixed, try to follow the button when the user scrolls */
	if (this.options.appendTo){
		this.options.button.parents().filter(function(){
			return this.scrollHeight > $(this).height();
		}).on("scroll", $.proxy(function(e){
			if (!this._wait){
				this._wait = true;
				setTimeout($.proxy(function(){this._wait = false;}, this), 100);
				this.position();
			}
		}, this));
		
	}
};

AppsPopup.prototype.open = function(delay){
	var _delay = typeof delay != "undefined" ? delay : this.options.delay;
	setTimeout($.proxy(function(){
		if (this.options.mode == "click" || this.button.hasClass("appspopup-hover")){
			this.position();
			this.popup.fadeIn(200, $.proxy(function(){
				this.popup.addClass("active");
				this.button.addClass("appspopup-active active");
				this.checkWindowClipping();
				if (this.hasOpenCallback){
					this.options.onOpen.call(this, this.popup);
				}
			}, this));
		}
	}, this), _delay);
};

AppsPopup.prototype.close = function(delay){
	var _delay = typeof delay != "undefined" ? delay : (this.options.delayClose != null ? this.options.delayClose : this.options.delay);
	setTimeout($.proxy(function(){
		/* If the popup has not been appended to the button, only close the popup if the mouse is not on the popup anymore */
		if (this.options.mode != "hover" || !(this.options.appendTo && this.popup.hasClass("appspopup-hover"))){
			this.popup.fadeOut(200, $.proxy(function(){			
				this.popup.removeClass("active");
				this.button.removeClass("appspopup-active active");
				if (this.hasOpenCallback){
					this.options.onClose.call(this, this.popup);
				}
			}, this));	
		}
	}, this), _delay);
};
AppsPopup.prototype.isOpen = function(){
	return this.popup.hasClass("active");
};
AppsPopup.prototype.position = function(){
	this.setDirectionClass();
	if (this.options.appendTo){
		/* If the popup has not been appended to the button,
		 * it has been set to position:fixed. Therefore its
		 * position must be computed each time it is displayed. */
		var $window = $(window);
		var windowDim = {width: $window.outerWidth(false), height: $window.outerHeight(false)};
		var windowScroll = {top: $window.scrollTop(), left: $window.scrollLeft()};
		var buttonDim = {width: this.button.outerWidth(false), height: this.button.outerHeight(false)};
		var coords = this.button.offset();
		coords.right = windowDim.width - (coords.left + buttonDim.width);
		coords.bottom = windowDim.height - (coords.top + buttonDim.height);
		var offsetForArrow = {
			right: {
				top: buttonDim.height/2 - 25,
				left: 9 
			},
			left: {
				top: buttonDim.height/2 - 25,
				left: 9 
			},
			down: {
				top: 9,
				right: buttonDim.width/2 - 25
			},
			up: {
				top: 9,
				left: buttonDim.width/2 - 25
			}
		
				
		};
		switch(this.options.direction){
			case "up":
				this.popup.css({
					top: "auto",
					bottom: windowDim.height + windowScroll.top - coords.top + offsetForArrow.up.top, 
					left: coords.left - windowScroll.left + offsetForArrow.up.left,
					right: "auto"});
				break;
			case "down":
				this.popup.css({
					top: windowDim.height - windowScroll.top - coords.bottom + offsetForArrow.down.top, 
					bottom: "auto",
					left: "auto",
					right: coords.right + windowScroll.left + offsetForArrow.down.right});
				break;
			case "left":
				this.popup.css({
					top: coords.top - windowScroll.top + offsetForArrow.left.top, 
					bottom: "auto",
					left: "auto",
					right: windowDim.width + windowScroll.left - coords.left + offsetForArrow.left.left});
				break;
			case "right":
				this.popup.css({
					top: coords.top - windowScroll.top + offsetForArrow.right.top, 
					bottom: "auto",
					left: windowDim.width - windowScroll.left - coords.right + offsetForArrow.right.left,
					right: "auto"});
				break;
		}
		this.wrapper.append(this.content);
		this.popup.append(this.arrow).append(this.wrapper);
	}else{
		if (!this.positionSet){
			var buttonDim = {width: this.button.outerWidth(false), height: this.button.outerHeight(false)};
			switch(this.options.direction){
				case "up":
					this.popup.css({bottom: buttonDim.height + 18});
					this.popup.css({left: buttonDim.width/2 - 24});
					break;
				case "down":
					this.popup.css({top: buttonDim.height + 18});
					this.popup.css({right: buttonDim.width/2 -24});
					break;
				case "left":
					this.popup.css({right: buttonDim.width + 18});
					this.popup.css({top: buttonDim.height/2 -22});
					break;
				case "right":
					this.popup.css({left: buttonDim.width + 18});
					this.popup.css({top: buttonDim.height/2 -28});
					break;
			}
			this.positionSet = true;
		}
		
	}
};

AppsPopup.prototype.checkWindowClipping = function(){
	if (this.options.direction == "right" || this.options.direction == "left"){
		var $window = $(window);
		var windowDim = {width: $window.outerWidth(false), height: $window.outerHeight(false)};
		var windowScroll = {top: $window.scrollTop(), left: $window.scrollLeft()};
		
		var popupDim = {width: this.popup.outerWidth(false), height: this.popup.outerHeight(false)};
		var coords = this.popup.offset();
		coords.right = windowDim.width - (coords.left + popupDim.width);
		coords.bottom = windowDim.height - (coords.top + popupDim.height);
		
		if (coords.bottom < 0){
			var popupTop = parseInt(this.popup.css("top").replace('px', ''));
			this.popup.css("top", parseInt(this.popup.css("top").replace('px', '')) + coords.bottom - 2);
			this.arrow.css("top", parseInt(this.arrow.css("top").replace('px', '')) - coords.bottom + 2);
		}
	}
};

AppsPopup.prototype.setDirectionClass = (function(){
	var directionClasses = {up: "direction-up", down: "direction-down", right: "direction-right", left: "direction-left"};
	return function(){
		/* Checks if the direction option has changed */
		if (!this.popup.hasClass(directionClasses[this.options.direction])){
			for (direction in directionClasses){
				this.popup.removeClass(directionClasses[direction])
			}
			this.popup.addClass(directionClasses[this.options.direction]);
		}
	}
})();

/* jQuery/jQuery-UI plugin */
(function($){
	if ($.ui){
		/**
		 * jQuery-UI plugin.
		 * Usage : $("#myButton").popup({
		 * 	content: $("#myContent"),
		 * 	...
		 * })
		 * Change an option: $("#myButton").popup("option", optionName, optionValue)
		 */
		$.widget("apps.popup", {
			_create: function(){
				this.options.button = this.element
				this.instance = new AppsPopup(this.options);
			},
			option: function(optionName, optionValue){
				if (typeof optionValue !== "undefined"){
					this.instance.options[optionName] = optionValue;
					this.options[optionName] = optionValue;
				}else{
					return this.instance.options[optionName];
				}
			},
			open: function(delay){
				this.instance.open(delay);
			},
			close: function(delay){
				this.instance.close(delay);
			}
		});
	}else{
		/**
		 * jQuery plugin.
		 * Usage : $("#myButton").popup($("#myContent"), options)
		 * or : $("#myButton").popup({
		 * 	content: $("#myContent"),
		 * 	...
		 * })
		 */
		$.fn.popup = function(content, options){
			if (content instanceof $) {
				if (typeof options === "undefined"){
					options = {};
				}
				options.button = this;
				options.content = content;
				new AppsPopup(options);
			} else if($.isPlainObject(content)) {
			    var options = content;
			    options.button = this;
				new AppsPopup(options);
			} else if (typeof content === "string") {
			    var functionName = content;
			    if (typeof this.data('popup') != "undefined" 
			        && $.isFunction(this.data('popup')[functionName])) {
			        var args =  Array.prototype.slice.call(arguments, 1);
			        this.data('popup')[functionName].apply(this.data('popup'), args);
			    }
			}
			return this;
		};
	}
})(jQuery);