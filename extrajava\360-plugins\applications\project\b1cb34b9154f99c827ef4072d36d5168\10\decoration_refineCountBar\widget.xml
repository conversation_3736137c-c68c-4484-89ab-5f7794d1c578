<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Facet count bars" group="PLM Analytics/Facets" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>Drop this widget in your page to add bar visualizations to your facet widgets.</Description>
	
	<Includes>
    	<Include type="css" path="css/style.less" />
    	<Include type="js" path="js/refineCountBars.js" />
    </Includes>
	
	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO" />
	<SupportI18N supported="true" />
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<OptionsGroup name="General">
		<Option id="wuids" name="Widget WUIDs" arity="ZERO_OR_MANY">
			<Description>The WUID of refine widgets on which to add bar visualizations. If empty, all refine widgets are targeted.</Description>
			<Functions>
				<ContextMenu>WUIDS()</ContextMenu>
			</Functions>
		</Option>
		<Option id="selector" name="Custom selector" arity="ZERO_OR_ONE">
			<Description>You can use a custom CSS selector to target refine widgets. Overrides the 'Widget WUIDs' option.</Description>
		</Option>
		<Option id="color" name="Bar color" arity="ONE">
			<Description>The color of the bars.</Description>
		</Option>
		<Option id="position" name="Bar position" arity="ONE">
			<Description>Whether to position the bar under the category title or behind it.</Description>
			<Values>
				<Value>Under</Value>
				<Value>Behind</Value>
				<Value>Left</Value>
				<Value>Right</Value>
			</Values>
		</Option>
		
		
	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="color">#F4F5F6</DefaultValue>
	</DefaultValues>

</Widget>
