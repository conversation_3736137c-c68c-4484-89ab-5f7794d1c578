/**
 * Base class for widgets handling a selection event.
 */
function SelectionWidget() {
	this.selectionWidget = {
		topicNames: [],
		enablePublication: false,
		enableSubscription: false,
		selectedHits: []
	};
}

/**
 * Use the SelectAPI to publish the current selection.
 */
SelectionWidget.prototype.publishSelection = function () {
	if (window.SelectAPI) {
		if (this.selectionWidget.enablePublication) {
			SelectAPI.publish(this.selectionWidget.selectedHits, this.selectionWidget.topicNames);
		}
	} else {
		console.error('Can not publish the selection: SelectAPI is not defined');
	}
};

/**
 * Function to call when selection changes.
 * @param {String[]} selectedHits - Array of hit ids
 */
SelectionWidget.prototype.setSelectedHits = function (selectedHits) {
	this.selectionWidget.selectedHits = selectedHits;
	this.publishSelection();
};

/**
 * Function to call during the widget initialization.
 * @param {Object} config - Widget configuration for the SelectAPI
 * @param {String[]} [config.topicNames] - Topic names to subscribe to
 * @param {Boolean} [config.enablePublication] - Whether to enable publication
 * @param {Boolean} [config.enableSubscription] - Whether to enable subscription
 */
SelectionWidget.prototype.setSelectionConfig = function (config) {
	$.extend(this.selectionWidget, config);
	if (window.SelectAPI) {
		if (this.selectionWidget.enableSubscription) {
			SelectAPI.subscribeToTopics(this.selectionWidget.topicNames, this.onHitsSelect.bind(this));
		}
	} else {
		console.error('Can not subscribe to the topic: SelectAPI is not defined');
	}
};

/**
 * Function to call when hits are selected outside of PLMA apps.
 * It should be overridden by the child class.
 * @param {String[]} selectedHits - Array of hit ids
 */
SelectionWidget.prototype.onHitsSelect = function (selectedHits) {
	console.warn('onHitsSelect should be overridden');
};