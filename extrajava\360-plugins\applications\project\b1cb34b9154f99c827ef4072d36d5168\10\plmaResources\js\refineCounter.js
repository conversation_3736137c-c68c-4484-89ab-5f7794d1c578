/**
 * Provides simple API to memorize the number of refinements.
 */
var refineCounter = (function() {

	/**
	 * Local storage key.
	 */
	var REFINE_COUNTER_KEY = 'plma.refineCounter';

	/**
	 * Object that describes the current state.
	 * Uses (categoryId, category properties) pairs.
	 * 
	 * @private
	 * @example
	 * {
	 * 		'f/foo/bar': {
	 * 			label: 'Bar',
	 * 			count: 2
	 * 		},
	 * 		'f/foo/baz': {
	 * 			label: 'Baz',
	 * 			count: 1
	 * 		}
	 * }
	 */
	var state = {};

	/**
	 * Retrieves the state by reading the local storage.
	 */
	(function init() {
		var stateAsString = localStorage.getItem(REFINE_COUNTER_KEY);
		if (stateAsString) {
			state = JSON.parse(stateAsString);
		}
	})();

	/**
	 * Saves the current state to the local storage.
	 * 
	 * @private
	 */
	function save() {
		localStorage.setItem(REFINE_COUNTER_KEY, JSON.stringify(state))
	}
	
	return {

		/**
		 * Returns the current state.
		 * 
		 * @returns {Object} the current state
		 */
		getState: function() {
			return state;
		},

		/**
		 * Returns the count for a category.
		 * 
		 * @param {String} categoryId - Category id
		 * @returns {Integer} count for the category
		 */
		getCategoryCount: function(categoryId) {
			var properties = state[categoryId];
			return properties ? properties.count : 0;
		},

		/**
		 * Increments the count for a category.
		 * 
		 * @param {String} categoryId - Category id
		 * @returns {Object} the new state
		 */
		incrementCategory: function(categoryId) {
			var properties = state[categoryId];
			if (properties) {
				state[categoryId].count++;
			} else {
				state[categoryId] = {
					count: 1
				};
			}
			save();
			return this.getState();
		},

		/**
		 * Sets a label for a category.
		 * 
		 * @param {String} categoryId - Category id
		 * @param {String} label - Label
		 * @returns {Object} the new state
		 */
		setCategoryLabel: function(categoryId, label) {
			var properties = state[categoryId];
			if (properties) {
				state[categoryId].label = label;
			} else {
				state[categoryId] = {
					label: label
				};
			}
			save();
			return this.getState();
		},

		/**
		 * Removes the count for a category
		 * 
		 * @param {String} categoryId - Category id
		 * @returns {Object} the new state
		 */
		removeCategory: function(categoryId) {
			delete state[categoryId];
			save();
			return this.getState();
		},

		/**
		 * Removes all the count of the categories.
		 * @returns {Object} the new state
		 */
		removeAll: function() {
			state = {};
			save();
			return this.getState();
		}

	};
})();