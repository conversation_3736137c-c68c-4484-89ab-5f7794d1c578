<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="data" uri="http://www.exalead.com/jspapi/data" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varFeeds="feeds" parameters="chartContainerId" />

<config:getOption var="facet" name="facet" doEval="true" />
<config:getOption var="color" name="color" defaultValue="#368ec4" />
<config:getOption var="height" name="height" />
<config:getOption var="onClick" name="onClick" />
<config:getOption var="displayLabel" name="displayLabel" defaultValue="true" />
<config:getOptionsComposite name="displayLabelList" var="displayLabelList" mapIndex="true" />

<plma:getDataForTreeMap data="data" feedName="feedName" feeds="${feeds}" facetId="${facet}" color="${color}" />

if(jQuery) {
	jQuery(window).on("plma:resize", function() {
		var event = document.createEvent("Event");

		event.initEvent("iichart:resize", true, true);
		setTimeout(function() {window.dispatchEvent(event)}, 400);
	})
}

/* Chart Options */
var data = (<config:getOption name="dataProcessor" defaultValue="function(data) { return data; }" />)(${data});
var highlight = (<config:getOption name="highlight" />);
var customColors = (<config:getOption name="customColors" />);

var displayLabelList = [];
<c:forEach items="${displayLabelList}" var="label">
    displayLabelList.push({name: '${label.labelName}', alias : '${label.labelAlias}'});
</c:forEach>

var props = {
	"height": ${height},
	"data": data,
	"onClick": ${onClick},
    "paddingInner": 4,
	"scaleLinearColor": true,
    "hideLabel": ${displayLabel},
    "displayLabels": displayLabelList
};

if (highlight) {
	props.highlight = highlight;
}

if (customColors) {
	props.customColors = customColors;
}

ReactDOM.render(
	React.createElement(WidgetTreemap, props),
    document.getElementById('${chartContainerId}')
);

