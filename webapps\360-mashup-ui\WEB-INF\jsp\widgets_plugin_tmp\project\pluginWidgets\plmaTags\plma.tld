<?xml version="1.0" encoding="UTF-8" ?>
<taglib xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd"
        version="2.0">

    <description>plma Mashup Widget Helpers</description>
    <tlib-version>1.0</tlib-version>
    <short-name>plma</short-name>
    <uri>http://www.exalead.com/jspapi/plma</uri>

    <tag>
        <description>Crypto utility tag, used to sign string value, returns base64 encoded signature</description>
        <name>signature</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.CryptoTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description>Value to sign</description>
            <name>value</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Output variable</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns facets which have only one value on the same hierarchical level</description>
        <name>getFacetFilter</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.FacettingFilterTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description></description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description></description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description></description>
            <name>varStatus</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns facets which have only one value on the same hierarchical level</description>
        <name>getFacetDimensions</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetDimensionsFromFacetTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description></description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description></description>
            <name>id1</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description></description>
            <name>id2</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description></description>
            <name>facetId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the url to refine on a category</description>
        <name>buildUrl</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.BuildUrlFromFacetTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description></description>
            <name>url</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>catLabel</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description></description>
            <name>refineAxis</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <description></description>
            <name>labelAxis</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <description>The ResultFeed to use for the URL creation. Object is of type Map(String, ResultFeed).
            </description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <name>category</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.AbstractCategory</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the url of the used search server</description>
        <name>getSearchServer</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetSearchServerTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description></description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The ResultFeed to use for the URL creation. Object is of type Map(String, ResultFeed).
            </description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the content of the FacetDisplays config (color and icon for categories) as a JSON string.
        </description>
        <name>getFacetDisplayConfigAsJson</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetFacetDisplayConfigAsJsonTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The output variable to store the color.</description>
            <name>var</name>
            <rtexprvalue>false</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Facet id</description>
            <name>facet</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns a map of categories with their associated color in preference for a given facet.
        </description>
        <name>getColorsFromFacetConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetColorsFromFacetConfigTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The output variable to store the color.</description>
            <name>var</name>
            <rtexprvalue>false</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the facet.</description>
            <name>facetName</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns a string value of the searched variable (stored in an enum in ConstantUtils).
        </description>
        <name>getConstantValueFromNameTag</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The output variable to store the color.</description>
            <name>var</name>
            <rtexprvalue>false</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the constant.</description>
            <name>constantName</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the content of the config.</description>
        <name>getApplicationConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetApplicationConfigTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The output variable to store the color.</description>
            <name>var</name>
            <rtexprvalue>false</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the display color for a given category</description>
        <name>getCategoryColor</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryColorTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The output variable to store the color.</description>
            <name>var</name>
            <rtexprvalue>false</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the config from which to retrieve the color. By default, the appName attribute is
                used.
            </description>
            <name>configName</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the app from which to retrieve the color.</description>
            <name>appName</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the category for which to retrieve the color.</description>
            <name>categoryName</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The category for which to retrieve the color.</description>
            <name>category</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Category</type>
        </attribute>
        <attribute>
            <description>The Id of the facet of the category for which to retrieve the color. 'entry' must be
                specified.
            </description>
            <name>facetId</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The entry from which to get the category color. 'facetId' must be specified.</description>
            <name>entry</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Entry</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the display icon name for a given facet or category. You can use it as a CSS class. e.g. :
            'fonticon fonticon-search'
        </description>
        <name>getIconName</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryIconTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The output variable to store the icon name.</description>
            <name>var</name>
            <rtexprvalue>false</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the config from which to retrieve the icon name. By default, the appName attribute
                is used.
            </description>
            <name>configName</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the app from which to retrieve the icon name.</description>
            <name>appName</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the category for which to retrieve the icon name.</description>
            <name>value</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The category for which to retrieve the icon name.</description>
            <name>category</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Category</type>
        </attribute>
        <attribute>
            <description>The Id of the facet of the category for which to retrieve the icon name. 'entry' must be
                specified.
            </description>
            <name>facetId</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The entry from which to get the category icon name. 'facetId' must be specified.</description>
            <name>entry</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Entry</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns only needed metas for Timeline Widget</description>
        <name>getTimelineData</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetTimelineTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description></description>
            <name>itemConfig</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The output variable</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The output variable Min Date</description>
            <name>varMinDate</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The output variable Max Date</description>
            <name>varMaxDate</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description></description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description></description>
            <name>groupBy</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the refinements for a 2D facet</description>
        <name>getRefinements2DFacet</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetRefinements2DFacetTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>feeds</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <name>facetId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the date ranges from the application configuration</description>
        <name>getDateRangeConfigTag</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetDateRangeConfigTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>ranges</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>configName</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>appName</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the date range label for a specific date range id from the application configuration</description>
        <name>getDateRangeLabelByIdTag</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetDateRangeLabelByIdTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <name>rangeLabel</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>rangeId</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns information about the product</description>
        <name>productInfo</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.ProductInfoTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The variable to store the ProductInfo object.</description>
            <name>var</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The variable to store the display name of the application.</description>
            <name>varName</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The variable to store the version of the application.</description>
            <name>varVersion</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The variable to store the revision of the application.</description>
            <name>varRevision</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The variable to store the build date of the application.</description>
            <name>varDate</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the application (used to find the configuration repository).</description>
            <name>appName</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the menu configuration as defined in the applicative configuration.</description>
        <name>menuConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetMenuConfigTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The variable to store the list of main menu items.</description>
            <name>varMainItems</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The variable to store the list of secondary menu items.</description>
            <name>varSecondaryItems</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the app from which to retrieve the menu configuration.</description>
            <name>appName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the app configuration from which to retrieve the menu configuration.</description>
            <name>configName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the icon list</description>
        <name>iconList</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetIconsTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The variable to store the icon list.</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The path of the less file containing the icons.</description>
            <name>filePath</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the app from which to retrieve the color.</description>
            <name>appName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the data for the highchart treeMap</description>
        <name>getDataForTreeMap</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetDataForTreeMapTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The variable to store the data object.</description>
            <name>data</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The variable to store the feed name.</description>
            <name>feedName</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>feeds</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <name>color</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>facetId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Corrects an url by skipping a parameter</description>
        <name>correctUrl</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.CorrectUrlTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>url</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>A list of additional feeds name to refine.</description>
            <name>forceRefineOn</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <name>pageUrl</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>keep</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <name>skip</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <name>skipProfiles</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <name>delete2DFacet</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <name>keepExtraRefinements</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <description></description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the localized label for the MergedCategory or the given MergedCategory path. Useful to get
            the description for 'EXCLUDED' MergedCategory objects.
        </description>
        <name>getCategoryLabel</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryLabelTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for the retrieved MergedCategory label.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>This feed will be used to retrieve the category title through the Search API.</description>
            <name>feed</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>One of these feeds will be used to retrieve the category title through the Search API.
            </description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>The MergedCategory to retrieve label from.</description>
            <name>category</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.AbstractCategory</type>
        </attribute>
        <attribute>
            <description>The MergedCategory path (Facet/Category) to use for the label.</description>
            <name>categoryPath</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The MergedCategory id to use for the label.</description>
            <name>categoryId</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Facets suggest query context.</description>
            <name>suggestContext</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.dto.FacetsSuggestContext</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns category titles for full category path</description>
        <name>getCategoryPathTitle</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetCategoryPathTitle</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for the retrieved MergedCategory label.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>This feed will be used to retrieve the category title through the Search API.</description>
            <name>feed</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>One of these feeds will be used to retrieve the category title through the Search API.</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>The MergedCategory to retrieve label from.</description>
            <name>category</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.AbstractCategory</type>
        </attribute>
        <attribute>
            <description>Cache name used in page context used to store computed paths.</description>
            <name>cacheName</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Prepare facet history metrics data.</description>
        <name>facetHistoryMetrics</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.PrepareHistoryMetrics</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Target serie variable name.</description>
            <name>varSerie</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Target color variable name.</description>
            <name>varColor</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Target status variable name.</description>
            <name>varStatus</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Facet name.</description>
            <name>facet</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate feeds expressions by specifying a ResultFeed.</description>
            <name>feed</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate feeds expressions by specifying a Map(String, ResultFeed).</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>Max number of categories to keep.</description>
            <name>nbCategories</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Integer</type>
        </attribute>
        <attribute>
            <description>Colors mappings (based on ratio between last value and average).</description>
            <name>colorMappings</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <description>Aggregation name.</description>
            <name>aggregation</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Default chart color.</description>
            <name>defaultColor</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Last value variable name</description>
            <name>varLastValue</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Target facet variable name</description>
            <name>varFacet</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the category along with its parent.</description>
        <name>getCategoriesWithParentTag</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetCategoriesWithParentTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for the retrieved CategoryWithParent.</description>
            <name>var</name>
            <required>true</required>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <description>The MergedFacet to retrieve Categories from.</description>
            <name>facet</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.MergedFacet</type>
        </attribute>
        <attribute>
            <description>Possible values are: 'ALL' (hierarchical view), 'LEAVES' (flat view of leaf nodes), 'FLAT' (flat view of the first level)</description>
            <name>iterationMode</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the localized label for the given MergedFacet, MergedFacet path or facet ID.</description>
        <name>getFacetLabel</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetFacetLabelTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for the retrieved MergedFacet label.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>This feed will be used to retrieve the facet label through the Search API, if needed.
            </description>
            <name>feed</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>One of these feeds will be used to retrieve the facet label through the Search API, if
                needed.
            </description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>The MergedFacet to retrieve label from.</description>
            <name>facet</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.AbstractCategory</type>
        </attribute>
        <attribute>
            <description>The MergedFacet path to use for the label.</description>
            <name>facetPath</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The facet ID to use for the label.</description>
            <name>facetId</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the list of hit metas from a feed in JSON format.</description>
        <name>getHitMetasAsJson</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetHitMetasAsJsonTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for the list of hit metas.</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Feed from which the metas are extracted.</description>
            <name>feed</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>List of meta names.</description>
            <name>metaNames</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <description>Description of the hit. Can contain MEL. Scopes are: feed, entry.</description>
            <name>description</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Desired number of hits.</description>
            <name>length</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Integer</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the list of applied refinements on the given feed or page, even on an empty feed.
        </description>
        <name>getRefines</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetRefinesTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for the retrieved list of refines.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Name of the exported variable for the retrieved list of inactive refines (refines that do not
                applied to the widget's feeds).
            </description>
            <name>varInactive</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Feed from which applied refinements are listed. If empty and if the 'feeds' attribute is empty
                too, all refinements on the page are listed.
            </description>
            <name>feed</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>Feeds from which applied refinements are listed. If empty and if the 'feed' attribute is empty
                too, all refinements on the page are listed.
            </description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>Whether to keep only refinements applied on simple facet (true) or to keep all refinements
                including those on 2D and multidim facets (false). Defaults to true.
            </description>
            <name>only1d</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
    </tag>

    <tag>
        <description>highchartsSerieName tag</description>
        <name>highchartsSerieName</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsSerieNameTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for the list of hit metas.</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The feeds providing the data displayed in this chart.</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>The configuration of the serie as returned by the config:getOptionComposite tag with mapIndex
                set to true.
            </description>
            <name>serieConfig</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the JSON definition of a Highcharts chart. Series can be added to this chart with the
            highchartsSerie tag.
        </description>
        <name>highchartsJSON</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsJsonTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The feeds providing the data displayed in this chart.</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>A legend displayed below the X axis, indicating what it displays and/or the unit.</description>
            <name>axisLabelX</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>A legend displayed next to the corresponding Y axis, indicating what is measured and/or the
                unit.
            </description>
            <name>axisLabelsY</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <description>The explicit order of categories on the X axis (comma-separated list).</description>
            <name>categoriesOrder</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <description>The max number of categories to display.</description>
            <name>maxCategories</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Take first or last values of facet.</description>
            <name>takeFirstValues</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>What to do with categories after 'maxCategories' is reached. Possible values are 'HIDE',
                'OTHERS', 'PAGINATE'.
            </description>
            <name>additionalCategories</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Base URL for refine links.</description>
            <name>baseUrl</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Additional feeds on which to force refines.</description>
            <name>forceRefineOnFeeds</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Additional facets on which to force refines.</description>
            <name>forceRefineOnFacets</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Refine URL will contain parameters for both the multidimension category (if the facet is
                multidim) and the 1D category.
            </description>
            <name>forceRefineOnMulti</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Adds a serie to a Highcharts chart created with the hichartsJSON tag.</description>
        <name>highchartsSerie</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.HighchartsSerieTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The configuration of the serie as returned by the config:getOptionComposite tag with mapIndex
                set to true.
            </description>
            <name>serieConfig</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>The feeds providing the data displayed in this serie.</description>
            <name>feeds</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>Custom series generation.</description>
            <name>customSeries</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <description>Custom series generation class.</description>
            <name>customSeriesClass</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Facet object (can be null).</description>
            <name>facet</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Facet</type>
        </attribute>
    </tag>

    <tag>
        <description>Get paginated facet categories for refine panel chart.</description>
        <name>refinePanelSerie</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.RefinePanelSerieTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The facet providing the data displayed in this serie.</description>
            <name>facet</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.MergedFacet</type>
        </attribute>
        <attribute>
            <description>Paginate categories.</description>
            <name>paginate</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <description>Output variable.</description>
            <name>var</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Categories per page.</description>
            <name>perPage</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Long</type>
        </attribute>
        <attribute>
            <description>Refine panel facet config.</description>
            <name>refineConfig</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.apps.plma_tools.commons.config.RefineBy</type>
        </attribute>
        <attribute>
            <description>The feeds providing the data displayed in this serie.</description>
            <name>feeds</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
    </tag>

    <tag>
        <description>Get cookie facet display mode (in refine panel, default value is false).</description>
        <name>isRefineFacetChartMode</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.ChartDisplayModeTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Facet name.</description>
            <name>facet</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Output variable.</description>
            <name>var</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Cookie name.</description>
            <name>cookie</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Default value.</description>
            <name>defaultValue</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Object</type>
        </attribute>
        <attribute>
            <description>URL decode cookie value.</description>
            <name>urlDecode</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the configuration of the specified chartboard.</description>
        <name>getChartboardConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.chartboard.GetCharboardFromStorageTag
        </tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Name of the exported variable for the map containing the cells. This maps allows to retrieve a
                Mashup Cell based on an ID generated in each tile (tile.cellId).
            </description>
            <name>varCells</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The widget layout coming from the Mashup Builder configuraiton. Will be used if nothing is
                found with the given storage key.
            </description>
            <name>layout</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.cv360.searchui.configuration.v10.Layout</type>
        </attribute>
        <attribute>
            <description>Number of columns in the grid layout. Defaults to 12.</description>
            <name>nbColumns</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Integer</type>
        </attribute>
        <attribute>
            <description>The ID of this chart board instance. If not specified in the config, it will typically be the widget uCssId.</description>
            <name>chartBoardId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The ID of this page instance. If not specified in the config, it will typically be the widget uCssId.</description>
            <name>pageId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Whether the page is a duplicate one or a basic mashup page.</description>
            <name>mashupPage</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <description>The approximate height of each layout cell. It will be rounded to the closest number of grid lines.</description>
            <name>cellHeight</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>The height of a grid row. Cells height will be a multiple of this number.</description>
            <name>baseY</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Are all cells hidden by default.</description>
            <name>hideAllCells</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the security of a chartboard page.</description>
        <name>GetChartboardPageSecurity</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.chartboard.GetChartboardPageSecurity
        </tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for write rights.</description>
            <name>writeRights</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Name of the exported variable for read rights.</description>
            <name>readRights</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The ID of this page instance. If not specified in the config, it will typically be the widget
                uCssId.
            </description>
            <name>pageId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns access lists of a chartboard page.</description>
        <name>GetChartboardPageRights</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.chartboard.GetChartboardPageRightsTag
        </tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for user write rights.</description>
            <name>userWrite</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Name of the exported variable for user read rights.</description>
            <name>userRead</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Name of the exported variable for group write rights.</description>
            <name>groupWrite</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Name of the exported variable for group read rights.</description>
            <name>groupRead</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The ID of this page instance. If not specified in the config, it will typically be the widget
                uCssId.
            </description>
            <name>pageId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the configuration of the specified chartboard.</description>
        <name>getNoteConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.chartboard.GetNoteFromStorageTag
        </tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The storage key from which to retrieve the config. Defaults to 'chartboardConfig'</description>
            <name>storageKey</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Number of columns in the grid layout. Defaults to 12.</description>
            <name>nbColumns</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Integer</type>
        </attribute>
        <attribute>
            <description>The approximate height of each layout cell. It will be rounded to the closest number of grid
                lines.
            </description>
            <name>cellHeight</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>The height of a grid row. Cells height will be a multiple of this number.</description>
            <name>baseY</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Returns the stored value of a saved parameter of the specified page.</description>
        <name>getParamFromSavedFilters</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetParamFromSavedFiltersTag
        </tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The id of the page.</description>
            <name>pageId</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the page.</description>
            <name>pageName</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Name of searched parameter.
            </description>
            <name>param</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns a list of categories which have a refine as saved filter and this refine is zapped in the page.</description>
        <name>getZapRefineFromSavedFilters</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetZapRefineFromSavedFiltersTag
        </tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The id of the page.</description>
            <name>pageId</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The name of the page.</description>
            <name>pageName</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Page query.
            </description>
            <name>query</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns a JSON string containing the table header and rows. Data can be added using the tableData
            tag.
        </description>
        <name>tableJSON</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.datatable.TableJsonTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Name of the variable to indicate whether there are records in table or not.</description>
            <name>varHasRecords</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Draw counter of the request. Will be forwarded in the response.</description>
            <name>draw</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Integer</type>
        </attribute>
    </tag>

    <tag>
        <description>Adds data to a table. Uses hit metas as columns.</description>
        <name>tableHitData</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.datatable.TableHitDataTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Feeds</description>
            <name>feeds</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>The configuration of the serie based on hits, as returned by the config:getOptionsComposite tag
                with mapIndex set to true. See
                com.exalead.cv360.searchui.view.jspapi.custom.taglib.datatable.ColumnConfig.
            </description>
            <name>hitConfig</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <description>URL of the page each hit should lead to</description>
            <name>hitUrl</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Name of the meta to get in order to set the row's id</description>
            <name>rowId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>CSS class to set on all cells of all columns containing values</description>
            <name>columnClassName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Property name and value of technical data with mapIndex set to true</description>
            <name>technicalData</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
    </tag>

    <tag>
        <description>Get data table object from result feed and result list config.</description>
        <name>hitsDataTable</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.datatable.HitsDataTableTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Feed</description>
            <name>feed</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>Result list config object (coming from external config XML file).</description>
            <name>config</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.apps.plma_tools.commons.config.ResultList</type>
        </attribute>
        <attribute>
            <description>Return columns definition in result JSON object</description>
            <name>withDefs</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <description>Responsive display (generates a control column in this case)</description>
            <name>responsive</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <description>Target variable for dataTable object, can be empty, if empty, JSON object is serialized in page result</description>
            <name>varDataTable</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Target variable for dataTable entries (without columns definition)</description>
            <name>varData</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Target variable for dataTable columns definition (without table entries)</description>
            <name>varColsDef</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Page base URL</description>
            <name>pageURL</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Adds data to a table. Can use aggregations as columns on 1D facets, and can use multidim facets
            too.
        </description>
        <name>tableSynthesisData</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.datatable.TableSynthesisDataTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Feeds</description>
            <name>feeds</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>The configuration of the serie based on facets, as in the list returned by the
                config:getOptionsComposite tag with mapIndex set to true. See
                com.exalead.cv360.searchui.view.jspapi.custom.taglib.datatable.DataSerieConfig.
            </description>
            <name>facetSerieConfig</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>Base URL for refine links</description>
            <name>baseUrl</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Additional feeds on which to force refines.</description>
            <name>forceRefineOnFeeds</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Additional facets on which to force refines.</description>
            <name>forceRefineOnFacets</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>Refine URL will contain parameters for both the multidimension category (if the facet is
                multidim) and the 1D category.
            </description>
            <name>forceRefineOnMulti</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>CSS class to set on all cells of all columns containing values</description>
            <name>columnClassName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Default value to set in a cell when the aggregation has no value on the given category. Defaults to null.</description>
            <name>defaultValue</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>
            get value from a json string key.
        </description>
        <name>getValueFromJsonKey</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetValueFromJsonKeyTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>json key</description>
            <name>key</name>
            <required>true</required>
            <rtexprvalue>false</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>json string object</description>
            <name>jsonObject</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>
            get a json array from a stringified json
        </description>
        <name>getJsonArrayFromString</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJsonArrayFromString</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>org.json.JSONArray</type>
        </attribute>
        <attribute>
            <description>json string array</description>
            <name>jsonArray</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>
            get a json object from a json array
        </description>
        <name>getJsonObjectFromJsonArray</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJsonObjectFromJsonArray</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>index</description>
            <name>index</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Integer</type>
        </attribute>
        <attribute>
            <description>json array</description>
            <name>jsonArray</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>org.json.JSONArray</type>
        </attribute>
    </tag>

    <tag>
        <description>Get JSON representation of object (using Jackson databind)</description>
        <name>toJSON</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Object</description>
            <name>object</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Object</type>
        </attribute>
        <attribute>
            <description>Object</description>
            <name>defaultValue</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Ignore fields from serialisation (List)</description>
            <name>ignoreFields</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
    </tag>

    <tag>
        <description>Debug object information</description>
        <name>debugObject</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.debug.DebugTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Log line prefix.</description>
            <name>prefix</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Object</description>
            <name>object</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Object</type>
        </attribute>
        <attribute>
            <description>Expose Json in variable.</description>
            <name>print</name>
            <type>java.lang.Boolean</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the last index time.</description>
        <name>getLastIndexTime</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetLastIndexTimeTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Names of the build groups.</description>
            <name>buildGroupNames</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
    </tag>

    <tag>
        <description>Get the pagination information for a specified facet (Number of categories per page and number of not loaded categories)
        </description>
        <name>getPaginationInfo</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetPaginationInfoTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Facet name.</description>
            <name>facetId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Result feeds</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>UI pagination.</description>
            <name>uiPagination</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Integer</type>
        </attribute>
    </tag>

    <tag>
        <description>Get facets refine panel suggest information</description>
        <name>getFacetSuggestContext</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetFacetSuggestContextTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Result feeds</description>
            <name>feeds</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>Output suggest context variable.</description>
            <name>var</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>get value from a 'Document' storage key</description>
        <name>getDocumentStorageValueTag</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetDocumentStorageValueTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>key</description>
            <name>key</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>uniqueKey</description>
            <name>uniqueKey</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>buildGroup</description>
            <name>buildGroup</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>source</description>
            <name>source</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>url</description>
            <name>url</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>format data to json for the heatmap</description>
        <name>heatmapJSON</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.heatmap.HeatmapJSONTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The configuration of the serie based on facets, as in the list returned by the
                config:getOptionsComposite tag with mapIndex set to true. See
                com.exalead.cv360.searchui.view.jspapi.custom.taglib.datatable.DataSerieConfig.
            </description>
            <name>facetSerieConfig</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>feeds</description>
            <name>feeds</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
    </tag>

    <tag>
        <description>Get channels configuration from the applicative configuration</description>
        <name>getChannelsConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetChannelsConfigTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>feeds</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <name>entry</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Entry</type>
        </attribute>
        <attribute>
            <name>channelNames</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
    </tag>

    <tag>
        <description>Get 1D facets from a multidimension facet</description>
        <name>get1DFacetsFromMultiDimensionFacet</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.Get1DFacetsFromMultiDimensionFacetTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>feeds</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <name>multiDimensionFacetName</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Hide flex panel container with css class</description>
        <name>getFlexPanelCSSClassesTag</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetFlexPanelCSSClassesTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Css classes of the current flexContainer</description>
            <name>flexContainerCssClasses</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Cookie value</description>
            <name>cookie</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the JSON definition of a Highcharts chart. Series can be added to this chart with the timeSerie tag.</description>
        <name>timeSeriesJSON</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.timeseries.TimeSeriesJSONTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description>Name of the exported variable.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The feeds providing the data displayed in this chart.</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>Base URL for refine links.</description>
            <name>baseUrl</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
        <attribute>
            <description>A legend displayed below the X axis, indicating what it displays and/or the unit.</description>
            <name>axisLabelX</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>A legend displayed next to the corresponding Y axis, indicating what is measured and/or the
                unit.
            </description>
            <name>axisLabelsY</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <description>Additional facets on which to force refines.</description>
            <name>forceRefineOnFacets</name>
            <rtexprvalue>true</rtexprvalue>
        </attribute>
    </tag>

    <tag>
        <description>Adds a serie to a Highcharts chart created with the TimeSeriesJSONTag.</description>
        <name>timeSerie</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.timeseries.TimeSerieTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The configuration of the serie as returned by the config:getOptionComposite tag with mapIndex
                set to true.
            </description>
            <name>serieConfig</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>The feeds providing the data displayed in this serie.</description>
            <name>feeds</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the ResultList configuration as defined in the applicative configuration.</description>
        <name>resultListConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetResultListConfigTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The variable to store the ResultList Configuration Items. Returns List(Map(String, String))</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>ResultTable Configuration Id.</description>
            <name>resultListId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The id of the app configuration from which to retrieve the ResultList configuration.</description>
            <name>configName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Transform result list columns object to list of maps.</description>
            <name>toMap</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the ResultList configuration as defined in the applicative configuration (or modified by user).</description>
        <name>resultListJSONConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetResultListJSON</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The variable to store the ResultList Configuration Items. Returns List(Map(String, String))</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>ResultTable Configuration Id.</description>
            <name>resultListId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The id of the app configuration from which to retrieve the ResultList configuration.</description>
            <name>configName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the Refines panel configuration as defined in the applicative configuration.</description>
        <name>refinesPanelConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetRefinesPanelConfigTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The variable to store the Refines panel Configuration Items. Returns List(Map(String, String))</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Refinespanel Configuration Id.</description>
            <name>refinesPanelId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Returns the HitDetails configuration as defined in the applicative configuration.</description>
        <name>hitDetailsConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetHitDetailsConfigTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The variable to store the FieldGroups Configuration Items. Returns List(FieldGroup)</description>
            <name>varGroupList</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The variable to store hit details configuration</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>HitDetails Configuration ID.</description>
            <name>hitDetailsId</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The Name of the application. if empty, used current mashup application name.</description>
            <name>appName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The id of the app configuration from which to retrieve the ResultList configuration.</description>
            <name>configName</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Get application config menu items</description>
        <name>getMenuItems</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.PLMAMenuItems</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Main menu items target variable</description>
            <name>varMainItems</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Secondary menu items target variable</description>
            <name>varSecondaryItems</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Convert menu map to menu items</description>
        <name>menuMapToMenuItem</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.menu.MenuMapToMenuItemTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Main menu items list</description>
            <name>maps</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
        <attribute>
            <description>Output variable</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
    </tag>


    <tag>
        <description>Get page title</description>
        <name>getPageTitle</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.GetPageTitle</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Target variable</description>
            <name>var</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Default value</description>
            <name>defaultValue</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Get configuration object</description>
        <name>getConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.GetConfigObject</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Target variable</description>
            <name>var</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Configuration handler ID</description>
            <name>id</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Widget object</description>
            <name>widget</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Get layout configuration</description>
        <name>getLayoutConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.GetLayoutConfig</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Target variable</description>
            <name>var</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Layout ID (can override one applicable for layout widget)</description>
            <name>id</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Widget object</description>
            <name>widget</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Get page bookmarks (for connected user)</description>
        <name>getBookmarks</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.storage.GetBookmarks</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for bookmarks</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Get saved pages (for connected user)</description>
        <name>getSavedPages</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.storage.GetSavedPages</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for saved pages</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Get saved pages (for connected user)</description>
        <name>getSharedPages</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.storage.GetSharedPages</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for shared pages</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Check if hit is marked as favorite (for connected user)</description>
        <name>isPreferredHit</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.storage.HasPreferredHit</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for bookmarks</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Hit id</description>
            <name>id</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Favorites collection name</description>
            <name>collection</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Check if collection contains given item (by ID)</description>
        <name>hasCollectionItem</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.request.HasCollectionItem</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for bookmarks</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Item id</description>
            <name>id</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Favorites collection name</description>
            <name>collection</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Get PLMA user collection information (items and count)</description>
        <name>getCollection</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.request.GetCollectionTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Target collection items variable name</description>
            <name>varItems</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Collection count variable name</description>
            <name>varCount</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Collection name</description>
            <name>collection</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Get current URL with filtered parameters</description>
        <name>getURL</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.request.GetURLTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Target URL variable</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Include patterns (comma separated)</description>
            <name>include</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Exclude patterns (comma separated)</description>
            <name>exclude</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Profiles (comma separated) (set of exclude patterns)</description>
            <name>profiles</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Add URL filtered request parameters</description>
        <name>addRequestParams</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.request.AddRequestParams</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Include patterns (comma separated)</description>
            <name>include</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Exclude patterns (comma separated)</description>
            <name>exclude</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Profiles (comma separated) (set of exclude patterns)</description>
            <name>profiles</name>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Check if page is marked as favorite (for connected user)</description>
        <name>isPreferredPage</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.storage.HasPreferredPage</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for bookmarks</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Hit id</description>
            <name>id</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Get preferred hits size for given collection</description>
        <name>countPreferredHits</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.storage.PreferredHitsCount</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for size</description>
            <name>var</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Preferred hits collection name</description>
            <name>collection</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>


    <tag>
        <description>Gets an option composite value. The returned value is of type String[] or Map(String, String).</description>
        <name>getOptionComposite</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.PLMAGetOptionCompositeTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>The separator used for the composite option, by default it will retrieve the one from the configuration.</description>
            <name>separator</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Specifies whether to map or not indexes with the option name.</description>
            <name>mapIndex</name>
            <type>boolean</type>
        </attribute>
        <attribute>
            <description>Name of the exported variable for the option value.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Name of the option to retrieve.</description>
            <name>name</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>If the option value is not set, the given default value will be returned.</description>
            <name>defaultValue</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Composite option key to be returned.</description>
            <name>key</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Default composite option value (with separators).</description>
            <name>defaultComposite</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The component we want to retrieve the option from. Object is of type MashupPage, Row, Cell or Widget. By defaults the widget
                in the current context is used.
            </description>
            <name>component</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.cv360.config.elements.CustomComponentParameterContainer</type>
        </attribute>
        <attribute>
            <description>Specifies whether to eval or not the option. Defaults to the option configuration.</description>
            <name>doEval</name>
            <type>boolean</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate feeds expressions by specifying a ResultFeed.</description>
            <name>feed</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate feeds expressions by specifying a Map(String, ResultFeed).</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate entry expressions by specifying an Entry.</description>
            <name>entry</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Entry</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate CATEGORY expressions by specifying a MergedCategory.</description>
            <name>category</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.AbstractCategory</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can specifies on which aggregation the given category is based. Defaults to 'count'.</description>
            <name>aggregation</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate FACET expressions by specifying a MergedFacet.</description>
            <name>facet</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.MergedFacet</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate META expressions by specifying a Meta.</description>
            <name>meta</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Meta</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can enable/disable the highlighting.</description>
            <name>isHighlighted</name>
            <type>boolean</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can enable/disable the XML escaping.</description>
            <name>isXmlEscaped</name>
            <type>boolean</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can enable/disable the JS escaping.</description>
            <name>isJsEscaped</name>
            <type>boolean</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can enable/disable the HTML escaping.</description>
            <name>isHtmlEscaped</name>
            <type>boolean</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can enable/disable the URL encoding.</description>
            <name>isUrlEncoded</name>
            <type>boolean</type>
        </attribute>
    </tag>

    <tag>
        <description>Iterates over the sub-Widgets of given types and changes the scope of the JSP to the widget we iterate on.</description>
        <name>forEachSubWidget</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.widget.ForEachSubWidgetTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description>Name of the exported scoped variable for the status of the iteration. This scoped variable has nested visibility.
            </description>
            <name>varStatus</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Widget types to include.</description>
            <name>includes</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Widget types to exclude.</description>
            <name>excludes</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The DataWidgetWrapper that contains the widgets to iterate over. Defaults to the widget in the current context.</description>
            <name>dataWidgetWrapper</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.cv360.searchui.widgets.DataWidgetWrapper</type>
        </attribute>
        <attribute>
            <description>The widget container that contains the widgets to iterate over. If specified, you must set the feed and entry attributes.
            </description>
            <name>widgetContainer</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.cv360.searchui.configuration.v10.WidgetContainer</type>
        </attribute>
        <attribute>
            <description>The parent ResultFeed for the specified widget.</description>
            <name>feed</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>The parent Entry for the specified widget.</description>
            <name>entry</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Entry</type>
        </attribute>
        <attribute>
            <description>Specifies whether the tag should evaluate or not the widget triggers. Defaults to 'true'.</description>
            <name>evalTriggers</name>
            <type>boolean</type>
        </attribute>
        <attribute>
            <description>The index on which to begin the iteration. Defaults to '0'.</description>
            <name>begin</name>
            <rtexprvalue>true</rtexprvalue>
            <type>int</type>
        </attribute>
        <attribute>
            <description>The number of iteration to perform, replace the 'end' attribute. 0 is interpreted as 'unbounded'. Defaults to '0'.
            </description>
            <name>iteration</name>
            <rtexprvalue>true</rtexprvalue>
            <type>int</type>
        </attribute>
        <attribute>
            <description>Get matching widgets recursively (can be dangerous if parent and children are of same type).</description>
            <name>recurse</name>
            <required>false</required>
            <type>boolean</type>
        </attribute>
    </tag>

    <tag>
        <description>Create new data widget wrapper.</description>
        <name>createDataWidgetWrapper</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.widget.CreateDataWidgetWrapper</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description>Target variable for data widget wrapper.</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Configuration object (will be serialized to JSON in target widget configuration).</description>
            <name>config</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Object</type>
        </attribute>
        <attribute>
            <description>Target widget ID (widget type).</description>
            <name>widgetId</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Target widget UUID (to be generated because dynamic and not initialized in Mashup UI).</description>
            <name>widgetUUID</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The parent ResultFeed for the specified widget.</description>
            <name>parentFeed</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>The parent Entry for the specified widget.</description>
            <name>parentEntry</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Entry</type>
        </attribute>
        <attribute>
            <description>Parent data widget wrapper (used to get used feeds configuration).</description>
            <name>parentWidget</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.cv360.searchui.configuration.v10.Widget</type>
        </attribute>
    </tag>

    <tag>
        <description>Add parameter value to data widget wrapper.</description>
        <name>addWidgetParameter</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.widget.AddWidgetParameter</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description>Data widget wrapper.</description>
            <name>dataWidgetWrapper</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.cv360.searchui.widgets.DataWidgetWrapper</type>
        </attribute>
        <attribute>
            <description>Parameter name.</description>
            <name>name</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Parameter value.</description>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <name>jspTemplatePathCheck</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.widget.JSPTemplatePathCheck</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>customJspPath</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>defaultJspPath</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>required</name>
            <required>false</required>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <name>widget</name>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Iterates over sub-Widgets matching parameter value (ex. resultListView = tiles).</description>
        <name>forEachSubWidgetWithParam</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.widget.ForEachSubWidgetWithParamTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
            <description>Name of the exported scoped variable for the status of the iteration. This scoped variable has nested visibility.
            </description>
            <name>varStatus</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Filter parameter name.</description>
            <name>param</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Filter parameter value.</description>
            <name>value</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The DataWidgetWrapper that contains the widgets to iterate over. Defaults to the widget in the current context.</description>
            <name>dataWidgetWrapper</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.cv360.searchui.widgets.DataWidgetWrapper</type>
        </attribute>
        <attribute>
            <description>The widget container that contains the widgets to iterate over. If specified, you must set the feed and entry attributes.
            </description>
            <name>widgetContainer</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.cv360.searchui.configuration.v10.WidgetContainer</type>
        </attribute>
        <attribute>
            <description>The parent ResultFeed for the specified widget.</description>
            <name>feed</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>The parent Entry for the specified widget.</description>
            <name>entry</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Entry</type>
        </attribute>
        <attribute>
            <description>Specifies whether the tag should evaluate or not the widget triggers. Defaults to 'true'.</description>
            <name>evalTriggers</name>
            <type>boolean</type>
        </attribute>
        <attribute>
            <description>The index on which to begin the iteration. Defaults to '0'.</description>
            <name>begin</name>
            <rtexprvalue>true</rtexprvalue>
            <type>int</type>
        </attribute>
        <attribute>
            <description>The number of iteration to perform, replace the 'end' attribute. 0 is interpreted as 'unbounded'. Defaults to '0'.
            </description>
            <name>iteration</name>
            <rtexprvalue>true</rtexprvalue>
            <type>int</type>
        </attribute>
        <attribute>
            <description>Get matching widgets recursively (can be dangerous if parent and children are of same type).</description>
            <name>recurse</name>
            <required>false</required>
            <type>boolean</type>
        </attribute>
    </tag>

    <tag>
        <description>Checks if data is present for any facet used for timeserie.</description>
        <name>hasSeriesData</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.serie.HasSeriesDataTag</tag-class>
        <tei-class>com.exalead.cv360.searchui.view.jspapi.search.GetFacetTEI</tei-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for result.</description>
            <name>var</name>
            <required>true</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>The ResultFeed containing the facet to retrieve. Object is of type Map(String, ResultFeed).</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>The ResultFeed containing the facet to retrieve. Object is of type ResultFeed.</description>
            <name>feed</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>Series configuration</description>
            <name>seriesConfig</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.List</type>
        </attribute>
    </tag>

    <tag>
        <description>Get maximum count from the categories of specfied facet.</description>
        <name>getMaxCount</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetMaxCount</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for max count</description>
            <name>var</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Facet Object</description>
            <name>facet</name>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.MergedFacet</type>
        </attribute>
    </tag>

    <tag>
        <description>Get current displayed page ID.</description>
        <name>currentPageId</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.menu.CurrentPageIdTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable current page ID</description>
            <name>var</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Name of current page variable</description>
            <name>varPageName</name>
            <required>false</required>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Execute Freemarker template.</description>
        <name>freemarker</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.freemarker.TemplateRenderer</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for the option value.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Freemarker template path.</description>
            <name>template</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate feeds expressions by specifying a ResultFeed.</description>
            <name>feed</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.ResultFeed</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate feeds expressions by specifying a Map(String, ResultFeed).</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate entry expressions by specifying an Entry.</description>
            <name>entry</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Entry</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate CATEGORY expressions by specifying a MergedCategory.</description>
            <name>category</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.AbstractCategory</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate FACET expressions by specifying a MergedFacet.</description>
            <name>facet</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.MergedFacet</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate META expressions by specifying a Meta.</description>
            <name>meta</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.access.feedapi.Meta</type>
        </attribute>
    </tag>

    <tag>
        <description>Get menu item URL.</description>
        <name>menuItemUrl</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.MenuItemUrlTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for URL.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>Freemarker template path.</description>
            <name>ignoreParams</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate feeds expressions by specifying a Map(String, ResultFeed).</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
            <description>Menu item config.</description>
            <name>config</name>
            <rtexprvalue>true</rtexprvalue>
            <type>com.exalead.apps.plma_tools.commons.config.MenuItem</type>
        </attribute>
    </tag>

    <tag>
        <description>Get refine panel context.</description>
        <name>refinePanelContext</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetRefinePanelContext</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for refine context.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate feeds expressions by specifying a Map(String, ResultFeed).</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
    </tag>

    <tag>
        <description>Get widget used feeds.</description>
        <name>getUsedFeeds</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.PLMAGetUsedFeeds</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for refine context.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
    </tag>

    <tag>
        <description>Get refine panel facets list from old config (configured in Mashup Builder)</description>
        <name>refineFacetsOldConfig</name>
        <tag-class>com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetRefineFacetsFromOldConfig</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <description>Name of the exported variable for refine context.</description>
            <name>var</name>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <description>For evaluated options, you can evaluate feeds expressions by specifying a Map(String, ResultFeed).</description>
            <name>feeds</name>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
    </tag>

    <function>
        <description>Returns whether the given Widget has any subwidget of given type(s).</description>
        <name>hasSubWidget</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean anySubWidget(com.exalead.cv360.searchui.configuration.v10.WidgetContainer, java.lang.String)</function-signature>
        <example>
            &lt;c:if test="${plma:hasSubWidget(widget, 'type1,type2')}"&gt;
        </example>
    </function>

    <function>
        <description>Returns whether the given widget layout has any subwidget of given type(s).</description>
        <name>hasLayoutSubWidget</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean anyLayoutSubWidget(com.exalead.cv360.searchui.configuration.v10.Widget,java.lang.String)</function-signature>
        <example>
            &lt;c:if test="${plma:anyLayoutSubWidget(widget, 'type1,type2')}"&gt;
        </example>
    </function>

    <function>
        <description>Returns whether the given Widget has any subwidget matching parameter value.</description>
        <name>hasSubWidgetWithParam</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean anySubWidget(com.exalead.cv360.searchui.configuration.v10.WidgetContainer,java.lang.String,java.lang.String)</function-signature>
        <example>
            &lt;c:if test="${plma:hasSubWidgetWithParam(widget, 'displayViewId', 'tile')}"&gt;
        </example>
    </function>

    <function>
        <description>Returns whether the given Widget has any subwidget of given type(s) recursively.</description>
        <name>hasSubWidgetRecurse</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean anySubWidgetRecurse(com.exalead.cv360.searchui.configuration.v10.Widget, java.lang.String)</function-signature>
        <example>
            &lt;c:if test="${plma:hasSubWidgetRecurse(widget, 'type1,type2')}"&gt;
        </example>
    </function>

    <function>
        <description>Returns whether the given Widget does not have any subwidget of given type(s).</description>
        <name>hasExceptSubWidget</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean anyExceptSubWidget(com.exalead.cv360.searchui.configuration.v10.WidgetContainer, java.lang.String)
        </function-signature>
        <example>
            &lt;c:if test="${plma:hasExceptSubWidget(widget, 'type1,type2')}"&gt;
        </example>
    </function>

    <function>
        <description>Get RGB representation of color code (with opacity).</description>
        <name>toRGB</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String toRGB(java.lang.String,java.lang.String)</function-signature>
        <example>
            &lt;c:if test="${plma:toRGB('#6A314A', '.3')}"&gt;
        </example>
    </function>

    <function>
        <description>Get Javascript array from String list.</description>
        <name>toJSArray</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String toJSArray(java.util.List)</function-signature>
        <example>
            &lt;c:if test="${plma:toJSArray(optionValues)}"&gt;
        </example>
    </function>

    <function>
        <description>Get flex grow for given column index.</description>
        <name>getFexGrow</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.Integer getFexGrow(com.exalead.cv360.searchui.configuration.v10.Table,java.lang.Integer)</function-signature>
        <example>
            ${plma:getFexGrow(table, statusCell.index)}
        </example>
    </function>

    <function>
        <description>Is shared page editable.</description>
        <name>isEditable</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean isEditable(jakarta.servlet.http.HttpServletRequest,java.lang.String)</function-signature>
        <example>
            ${plma:isEditable(request, 'ugfuyisdyfuy')}
        </example>
    </function>

    <function>
        <description>Is page owner.</description>
        <name>isPageOwner</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean isPageOwner(jakarta.servlet.http.HttpServletRequest,java.lang.String)</function-signature>
        <example>
            ${plma:isPageOwner(request, 'ugfuyisdyfuy')}
        </example>
    </function>

    <function>
        <description>Get category page index.</description>
        <name>getPage</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.Long getPage(java.lang.Long, java.lang.Long)</function-signature>
        <example>
            ${plma:getPage(27, 10)}
        </example>
    </function>

    <function>
        <description>Get application ID.</description>
        <name>getApplicationId</name>
        <function-class>com.exalead.cv360.searchui.services.context.PLMAConfigContext</function-class>
        <function-signature>java.lang.String getApplicationName()</function-signature>
        <example>
            ${plma:getApplicationId()}
        </example>
    </function>

    <function>
        <description>Get object JSON representation.</description>
        <name>getJSON</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getJSON(java.lang.Object, java.lang.String)</function-signature>
        <example>
            ${plma:getJSON(object, '{}')}
        </example>
    </function>

    <function>
        <description>Get first resultfeed ID.</description>
        <name>getFirstFeedID</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getFirstFeedID(java.util.Map)</function-signature>
        <example>
            ${plma:getFirstFeedID(feeds)}
        </example>
    </function>

    <function>
        <description>Get map keys as JSON array.</description>
        <name>getKeys</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getKeys(java.util.Map,java.lang.String)</function-signature>
        <example>
            ${plma:getKeys(feeds, '[]')}
        </example>
    </function>

    <function>
        <description>Get feed(s) info (first value).</description>
        <name>getFeedsInfo</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getFeedsInfo(java.util.Map,java.lang.String,java.lang.String)</function-signature>
        <example>
            ${plma:getFeedsInfo(feeds, 'facets_suggest_query', '')}
        </example>
    </function>

    <function>
        <description>Get suggest facets (comma separated values).</description>
        <name>getSuggestFacets</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getSuggestFacets(java.util.List)</function-signature>
        <example>
            &lt;c:if test="${plma:getSuggestFacets(refines)}"&gt;
        </example>
    </function>

    <function>
        <description>Get JSP template path from configured JSP template.</description>
        <name>getJSPTemplatePath</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getJSPTemplatePath(java.lang.String)</function-signature>
        <example>
            &lt;c:if test="${plma:getJSPTemplatePath(displayTemplate)}"&gt;
        </example>
    </function>

    <function>
        <description>Get JSP template widget name from configured JSP template.</description>
        <name>geTemplateWidget</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getTemplateWidget(java.lang.String)</function-signature>
        <example>
            &lt;c:if test="${plma:getTemplateWidget(displayTemplate)}"&gt;
        </example>
    </function>

    <function>
        <description>Log object in log file and return value.</description>
        <name>logObject</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String debug(java.lang.String,java.lang.Object)</function-signature>
        <example>
            ${plma:logObject('feeds_listJSP', feeds)}
        </example>
    </function>

    <function>
        <description>Format string.</description>
        <name>format</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String format(java.lang.String,java.lang.String,java.lang.String)</function-signature>
        <example>
            ${plma:format(' style=font-size: %spx;', fontSize)}
        </example>
    </function>

    <function>
        <description>Get widget boolean parameter.</description>
        <name>getBooleanParam</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean getBooleanParam(com.exalead.cv360.searchui.configuration.v10.Widget,java.lang.String,boolean)</function-signature>
        <example>
            ${plma:getBooleanParam(widget, 'activate', false)}
        </example>
    </function>

    <function>
        <description>Get widget string parameter.</description>
        <name>getStringParam</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getStringParam(com.exalead.cv360.searchui.configuration.v10.Widget,java.lang.String,java.lang.String)
        </function-signature>
        <example>
            ${plma:getStringParam(widget, 'id', 'none')}
        </example>
    </function>

    <function>
        <description>Widhet has parameter.</description>
        <name>hasParam</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean hasParam(com.exalead.cv360.searchui.configuration.v10.Widget,java.lang.String)</function-signature>
        <example>
            ${plma:hasParam(widget, 'id')}
        </example>
    </function>

    <function>
        <description>Get widget integer parameter.</description>
        <name>getIntegerParam</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>int getIntegerParam(com.exalead.cv360.searchui.configuration.v10.Widget,java.lang.String,int)</function-signature>
        <example>
            ${plma:getIntegerParam(widget, 'maxValues', 10)}
        </example>
    </function>

    <function>
        <description>Get widget parameter values.</description>
        <name>getParameterValues</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.util.List getParamValues(com.exalead.cv360.searchui.configuration.v10.Widget,java.lang.String)</function-signature>
        <example>
            ${plma:getParamValues(widget, 'channels')}
        </example>
    </function>

    <function>
        <description>Add value to list.</description>
        <name>addValue</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.util.List addValue(java.util.List,java.lang.String)</function-signature>
        <example>
            ${plma:addValue(params, 'val')}
        </example>
    </function>

    <function>
        <description>Get widget parameter values with seperator.</description>
        <name>getParameterValuesWithSeparator</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.util.List getParamValuesWithSeparator(com.exalead.cv360.searchui.configuration.v10.Widget,java.lang.String,java.lang.String)</function-signature>
        <example>
            ${plma:getParameterValuesWithSeparator(widget, 'facets', ',')}
        </example>
    </function>

    <function>
        <description>Capitalize string value.</description>
        <name>capitalize</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String capitalize(java.lang.String)</function-signature>
        <example>
            ${plma:capitalize('compare')}
        </example>
    </function>

    <function>
        <description>Widget has template.</description>
        <name>hasTemplate</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean hasTemplate(jakarta.servlet.jsp.PageContext,com.exalead.cv360.searchui.configuration.v10.Widget,java.lang.String)</function-signature>
        <example>
            ${plma:hasTemplate(pageContext, widget, 'templates/header.jsp')}
        </example>
    </function>

    <function>
        <description>Get widget ID.</description>
        <name>getWidgetID</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getWidgetID(com.exalead.cv360.searchui.configuration.v10.Widget)</function-signature>
        <example>
            ${plma:getWidgetID(widget)}
        </example>
    </function>

    <function>
        <description>Get widget unique ID.</description>
        <name>getWidgetUID</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getWidgetUID(com.exalead.cv360.searchui.configuration.v10.Widget)</function-signature>
        <example>
            ${plma:getWidgetUID(widget)}
        </example>
    </function>

    <function>
        <description>Get object class.</description>
        <name>getObjectClass</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getObjectClass(java.lang.Object)</function-signature>
        <example>
            ${plma:getObjectClass(var)}
        </example>
    </function>

    <function>
        <description>Check if config folder contains file path (relative)</description>
        <name>hasConfiguration</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean hasConfiguration(java.lang.String)</function-signature>
        <example>
            ${plma:hasConfiguration(var)}
        </example>
    </function>

    <function>
        <description>Check if config application folder contains file path (relative)</description>
        <name>hasApplicationConfig</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean hasApplicationConfig(java.lang.String)</function-signature>
        <example>
            ${plma:hasApplicationConfig('ProductInfo')}
        </example>
    </function>

    <function>
        <description>Get widget boolean option.</description>
        <name>getBooleanOption</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean getBooleanOption(com.exalead.cv360.searchui.config.WidgetOptions,java.lang.String,boolean)</function-signature>
        <example>
            ${plma:getBooleanOption(widgetOptions, 'activate', false)}
        </example>
    </function>

    <function>
        <description>Get widget string option.</description>
        <name>getStringOption</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getStringOption(com.exalead.cv360.searchui.config.WidgetOptions,java.lang.String,java.lang.String)</function-signature>
        <example>
            ${plma:getStringOption(widgetOptions, 'id', 'none')}
        </example>
    </function>

    <function>
        <description>Get widget integer option.</description>
        <name>getIntegerOption</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>int getIntegerOption(com.exalead.cv360.searchui.config.WidgetOptions,java.lang.String,int)</function-signature>
        <example>
            ${plma:getIntegerOption(widgetOptions, 'maxValues', 10)}
        </example>
    </function>

    <function>
        <description>Get composite options.</description>
        <name>getCompositeOptions</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.util.List getCompositeOptions(com.exalead.cv360.searchui.config.WidgetOptions,java.lang.String)</function-signature>
        <example>
            ${plma:getCompositeOptions(widgetOptions, 'id')}
        </example>
    </function>

    <function>
        <description>Get composite option.</description>
        <name>getCompositeOption</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>com.exalead.cv360.searchui.config.CompositeOption getCompositeOption(com.exalead.cv360.searchui.config.WidgetOptions,java.lang.String)</function-signature>
        <example>
            ${plma:getCompositeOption(widgetOptions, 'id')}
        </example>
    </function>

    <function>
        <description>Create a new data widget wrapper from widget id.</description>
        <name>dataWidgetWrapper</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>com.exalead.cv360.searchui.widgets.DataWidgetWrapper getDataWidgetWrapper(java.lang.String,java.lang.String,com.exalead.access.feedapi.ResultFeed,com.exalead.access.feedapi.Entry,java.lang.Object)</function-signature>
        <example>
            ${plma:dataWidgetWrapper('refinePanel', wuid, feed, config)}
        </example>
    </function>

    <function>
        <description>Get used feeds with parent feed ID if use parent entry.</description>
        <name>getUsedFeeds</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.util.List getUsedFeeds(com.exalead.access.feedapi.ResultFeed, com.exalead.cv360.searchui.configuration.v10.Widget)</function-signature>
        <example>
            ${plma:getUsedFeeds(parentEntry, widget)}
        </example>
    </function>

    <function>
        <description>Get subwidget matching parameter value (pattern).</description>
        <name>getSubWidgetWithParam</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>com.exalead.cv360.searchui.configuration.v10.Widget getSubWidgetWithParam(com.exalead.cv360.searchui.configuration.v10.Widget,java.lang.String,java.lang.String,com.exalead.cv360.searchui.configuration.v10.Widget)</function-signature>
        <example>
            ${plma:getSubWidgetWithParam(widget, 'displayViewId', 'tiles', widget)}
        </example>
    </function>

    <function>
        <description>Check if application is running in 3D dashboard.</description>
        <name>in3DXPContext</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean in3DXPContext(jakarta.servlet.jsp.PageContext)</function-signature>
        <example>
            ${plma:in3DXPContext(pageContext)}
        </example>
    </function>

    <function>
        <description>Check if widget parameter (multivalued) values contains given value.</description>
        <name>containsParameterValue</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean containsParameterValue(com.exalead.cv360.searchui.configuration.v10.Widget,java.lang.String,java.lang.String)</function-signature>
        <example>
            ${plma:containsParameterValue(widget, 'views', 'compare')}
        </example>
    </function>

    <function>
        <description>Convert string to list (comma separator split)</description>
        <name>toList</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.util.List toList(java.lang.String)</function-signature>
        <example>
            ${plma:toList('a,b,c')}
        </example>
    </function>

    <function>
        <description>Convert string to list (with given separator)</description>
        <name>toListWithSeparator</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.util.List toListWithSeparator(java.lang.String,java.lang.String)</function-signature>
        <example>
            ${plma:toListWithSeparator('a|b|c', '|')}
        </example>
    </function>

    <function>
        <description>Format metadata value with field config (inputFormat and outputFormat attributes)</description>
        <name>formatField</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String formatField(java.lang.String,com.exalead.apps.plma_tools.commons.config.FieldDef)</function-signature>
        <example>
            ${plma:formatField(metaValue, colCfg)}
        </example>
    </function>

    <function>
        <description>Check if category is applicable for suggests</description>
        <name>suggestApplicable</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean suggestApplicable(com.exalead.access.feedapi.AbstractCategory,java.lang.String[])</function-signature>
        <example>
            ${plma:suggestApplicable(category, 'path1,path2')}
        </example>
    </function>

    <function>
        <description>Get hierarchical category title</description>
        <name>getHierarchicalDescription</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getHierarchicalDescription(com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.dto.CategoryWithParent,jakarta.servlet.jsp.PageContext,java.lang.String)</function-signature>
        <example>
            ${plma:getHierarchicalDescription(category, pagecontext, ' > ')}
        </example>
    </function>

    <function>
        <description>Get entry ID from meta value or URI</description>
        <name>getEntryID</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getEntryID(com.exalead.access.feedapi.Entry,java.lang.String)</function-signature>
        <example>
            ${plma:getEntryID(entry, 'id')}
        </example>
    </function>

    <function>
        <description>HTML escape string</description>
        <name>escapeHTML</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String escapeHTML(java.lang.String)</function-signature>
        <example>
            ${plma:escapeHTML(title)}
        </example>
    </function>

    <function>
        <description>JS escape string</description>
        <name>escapeJS</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String escapeJS(java.lang.String)</function-signature>
        <example>
            ${plma:escapeJS(jsCode)}
        </example>
    </function>

    <function>
        <description>XML escape string</description>
        <name>escapeXML</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String escapeXML(java.lang.String)</function-signature>
        <example>
            ${plma:escapeXML(title)}
        </example>
    </function>

    <function>
        <description>Get highlighted metadata value(s)</description>
        <name>getHighlightedValues</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getHighlightedValues(com.exalead.access.feedapi.Meta,boolean,java.lang.String)</function-signature>
        <example>
            ${plma:getHighlightedValues(meta, true, ', ')}
        </example>
    </function>

    <function>
        <description>Get RAW metadata value(s)</description>
        <name>getRawValues</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getRawValues(com.exalead.access.feedapi.Meta,boolean,java.lang.String)</function-signature>
        <example>
            ${plma:getRawValues(meta, true, ', ')}
        </example>
    </function>

    <function>
        <description>Get metadata values as array</description>
        <name>getMetaValuesArray</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getMetaValuesArray(com.exalead.access.feedapi.Meta,boolean)</function-signature>
        <example>
            ${plma:getMetaValuesArray(meta, false)}
        </example>
    </function>

    <function>
        <description>Get formatted metadata value(s)</description>
        <name>getFormattedValues</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getFormattedValues(com.exalead.access.feedapi.Meta,boolean,com.exalead.apps.plma_tools.commons.config.FieldDef,java.lang.String)</function-signature>
        <example>
            ${plma:getFormattedValues(meta, true fieldDef, ', ')}
        </example>
    </function>

    <function>
        <description>Concatenate string values (without adding quotes)</description>
        <name>join</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String join(java.util.List,java.lang.String)</function-signature>
        <example>
            ${plma:join(params, ', ')}
        </example>
    </function>

    <function>
        <description>Format string using Java string formatter</description>
        <name>formatString</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String formatString(java.lang.String,java.util.List)</function-signature>
        <example>
            ${plma:formatString('A %s, B %s', [var_a, var_b])}
        </example>
    </function>

    <function>
        <description>Check feed execution error presence</description>
        <name>hasError</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean hasError(com.exalead.access.feedapi.ResultFeed)</function-signature>
        <example>
            ${plma:hasError(feed)}
        </example>
    </function>

    <function>
        <description>Get feed execution error message</description>
        <name>getErrorMessage</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getErrorMessage(com.exalead.access.feedapi.ResultFeed)</function-signature>
        <example>
            ${plma:getErrorMessage(feed)}
        </example>
    </function>

    <function>
        <description>Get CSS styles from list of map (key='name', value='value')</description>
        <name>getCssStyles</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>java.lang.String getCssStyles(java.util.List)</function-signature>
        <example>
            ${plma:getCssStyles(varOptions)}
        </example>
    </function>

    <function>
        <description>Check if user has saved datatable state</description>
        <name>hasSavedState</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean hasSavedState(jakarta.servlet.jsp.PageContext,java.lang.String)</function-signature>
        <example>
            ${plma:hasSavedState(pagecontext, 'rl-001')}
        </example>
    </function>

    <function>
        <description>Check if result list config contains at least one sortable column</description>
        <name>hasSortableColumn</name>
        <function-class>com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions</function-class>
        <function-signature>boolean hasSortableColumn(com.exalead.apps.plma_tools.commons.config.ResultList)</function-signature>
        <example>
            ${plma:hasSortableColumn(resultListCfg)}
        </example>
    </function>
</taglib>
