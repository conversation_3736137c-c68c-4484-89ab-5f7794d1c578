<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Active filters" group="PLM Analytics/Facets" premium="true"
		xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget displays the filters currently applied to the feeds.</Description>

	<Preview>
		<![CDATA[
			<img src="/resources/widgets/activeFilters/images/preview.PNG" alt="Active Filters" />
		]]>
	</Preview>

	<Includes>
		<Include type="css" path="../plmaResources/lib/notify/notify-plma.less" />
		<Include type="css" path="css/style.less"/>
		<Include type="js" path="/resources/jquery-ui-1.11.4.custom/jquery-ui.min.js"/>
		<Include type="js" path="../chartboard/js/model.js"/>
		<Include type="js" path="../plmaResources/js/lodash.min.js" />
		<Include type="js" path="../plmaResources/js/jquery.plmaSuggest.js" />
		<Include type="js" path="../plmaResources/lib/notify/notify-plma.js" />
		<Include type="js" path="../plmaResources/js/refineCounter.js" />
		<Include type="js" path="../plmaResources/js/rights.js"/>
		<Include type="js" path="js/activeFilters.js"/>
		<Include type="js" path="js/savedFilters.js"/>
		<Include type="js" path="js/quickFilters.js"/>
	</Includes>

	<SupportWidgetsId arity="ZERO"/>
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>widgets.activeFilters.tooltip.cancel</JsKey>
			<JsKey>widgets.activeFilters.savedFilters.main</JsKey>
			<JsKey>widgets.activeFilters.savedFilters.save</JsKey>
			<JsKey>widgets.activeFilters.savedFilters.drop</JsKey>
			<JsKey>widgets.activeFilters.savedFilters.unlock</JsKey>
			<JsKey>widgets.activeFilters.savedFilters.title.unlock</JsKey>
			<JsKey>widgets.activeFilters.savedFilters.title.removeAll</JsKey>
			<JsKey>widgets.activeFilters.savedFilters.title.save</JsKey>
			<JsKey>widgets.activeFilters.quickFilters.mostUsedFilters</JsKey>
			<JsKey>widgets.activeFilters.quickFilters.mostUsedFilters.clear</JsKey>
			<JsKey>widgets.activeFilters.quickFilters.noResults</JsKey>
			<JsKey>widgets.activeFilters.quickFilters.suggests.error</JsKey>
		</JsKeys>
	</SupportI18N>

	<Dependencies>
		<Trigger position="self" className="com.exalead.cv360.searchui.view.widgets.triggers.Remove2DRefinesTrigger"/>
		<Widget name="plmaResources"/>
	</Dependencies>

	<OptionsGroup name="General">
		<Option id="title" name="Title" arity="ONE" isEvaluated="true">
			<Description>Widget title.</Description>
		</Option>
		<Option id="hideWidget" name="Hide when no filters" arity="ONE">
			<Description>Hides the widget when no filters are applied to the feeds.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="showReset" name="Show reset button" arity="ONE">
			<Description>Displays the reset button, which allows removing all filters at once.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="displayMode" name="Display mode" arity="ONE">
			<Description>Whether to display this widget with the standard widget style or in a more compact way.
			</Description>
			<Values>
				<Value>Compact</Value>
				<Value>Standard</Value>
			</Values>
		</Option>
		<Option id="display2D" name="Display 2D facets" arity="ONE">
			<Description>In PLMA, 2D facets are automatically refined when one of their facets is. You may not want to
				display them to avoid redundancy.
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="hideInactive" name="Hide inactive filters" arity="ONE">
			<Description>Hides applied filters that have no effect on the selected feeds.
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="displaySavedFilter" name="Display chartboard's saved filters" arity="ONE">
			<Description>Displays and allows edition of saved filters separately from the other filters.
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Advanced">
		<Option id="searchParameter" name="Search parameter">
			<Description>The URL parameter used to send the user Query to the feed.</Description>
		</Option>
		<Option id="separateFeeds" name="Separate feeds" arity="ONE">
			<Description>Displays filters grouped feed by feed.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('false', ['feedDisplayNames'], [], true, false)</Display>
			</Functions>
		</Option>
		<Option id="feedsList" name="Link Feeds list">
			<Description>Comma-separated list of feeds to which filters are applied.</Description>
			<Functions>
				<ContextMenu>COMMON_appendOnChange(', ')</ContextMenu>
			</Functions>
		</Option>
		<OptionComposite id="feedDisplayNames" name="Feed display names" arity="ZERO_OR_MANY">
			<Description>As feed IDs are not always user-friendly, you should rename them, e.g. with the type of
				business object they return.
			</Description>
			<Option id="feedId" name="Feed ID" arity="ONE">
				<Functions>
					<ContextMenu>Feeds()</ContextMenu>
				</Functions>
			</Option>
			<Option id="feedDisplayName" name="Display name" isEvaluated="true">
				<Description>The name to display instead of the feed ID when displaying this feed's filters.
				</Description>
			</Option>
		</OptionComposite>
		<OptionComposite id="customLinks" name="Custom links" arity="ZERO_OR_MANY">
			<Description>Adds custom links to the widget.</Description>
			<Option id="iconCss" name="Icon" isEvaluated="true">
				<Description>The class attribute of the icon displayed next to the label.</Description>
			</Option>
			<Option id="label" name="Label" isEvaluated="true">
				<Description>The label for this link.</Description>
			</Option>
			<Option id="url" name="Link" isEvaluated="true">
				<Description>Page that opens when the user clicks the link.</Description>
			</Option>
			<Option id="keepQueryParams" name="Keep parameters" arity="ONE">
				<Description>Keeps the current URL query string (including all filters) in the destination URL. It allows the user to keep filters throughout the navigation.</Description>
				<Values>
					<Value>true</Value>
					<Value>false</Value>
				</Values>
			</Option>
		</OptionComposite>
		<OptionComposite id="customButtons" name="Custom buttons" arity="ZERO_OR_MANY">
			<Description>Adds custom buttons to the widget.</Description>
			<Option id="iconCss" name="Icon" isEvaluated="true">
				<Description>The class attribute of the icon displayed next to the label.</Description>
			</Option>
			<Option id="title" name="Title" isEvaluated="true">
				<Description>The displayed name of this button.</Description>
			</Option>
			<Option id="onInit" name="On init" isEvaluated="true">
				<Description>JavaScript code that is executed once when the button is loaded on the page. The "this" keyword
					points to the button as a jQuery object.
				</Description>
				<Placeholder>function() {}</Placeholder>
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>
			<Option id="onClick" name="On click" isEvaluated="true">
				<Description>JavaScript code that is executed when clicking the button. The jQuery click event is passed as
					a parameter to the function. The "this" keyword points to the button as a jQuery object.
				</Description>
				<Placeholder>function(e) {}</Placeholder>
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>
			<Option id="positioning" name="Position" arity="ONE">
				<Description>Specifies the button position on the widget.</Description>
				<Values>
					<Value>right</Value>
					<Value>left</Value>
				</Values>
			</Option>
		</OptionComposite>
		<OptionComposite arity="ZERO_OR_MANY" name="Custom date refine Parameter name" id="customDateRefine">
			<Description>Allows to add date filters in the active filters bar.</Description>
			<Option id="label" name="Label" isEvaluated="true">
				<Description>The label to display in the active filters bar.</Description>
			</Option>
			<Option id="paramName" name="Parameter name" isEvaluated="true">
				<Description>The name of the observed start date.</Description>
			</Option>
		</OptionComposite>
		<OptionComposite arity="ZERO_OR_MANY" name="Additional Custom Parameters" id="customParams">
			<Description>Allows to additional parameters.</Description>
			<Option id="label" name="Label" isEvaluated="true">
				<Description>The label to display in the active filters bar.</Description>
			</Option>
			<Option id="paramName" name="Parameter name" isEvaluated="true">
				<Description>The name of the observed start date.</Description>
			</Option>
		</OptionComposite>
	</OptionsGroup>
	<OptionsGroup name="Quick filters">
		<Description>
			<![CDATA[
				It only works with suggest dispatcher with prefix handlers using the following pattern:
				facet__&lt;facet_id&gt;. Moreover, the fields in the index have to be normalized.<br />
				<b>Caution:</b> For index field suggests: "Subexpr matching" have to be enabled and "Sanitize entry" have to be disabled.
				"Compress content" for the corresponding fields in the Data Model have to be disabled.
			]]>
		</Description>
		<Option id="enableSuggest" name="Enable quick filters">
			<Description>Whether to enable quick filters.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('false',['suggestNames', 'suggestApiAction', 'suggestApiConfig', 'suggestCustomSearchAPIs', 'suggestApiCommand', 'draggableFiltersSelector'],[], true, false)</Display>
				<Display>ToggleReadOnly({ valueToMatch: 'false', options: ['suggestApiAction', 'suggestApiCommand'] })</Display>			
			</Functions>
		</Option>
		<Option id="suggestNames" name="Suggest Name" arity="MANY">
			<Description>Suggest service name.</Description>
			<Functions>
				<ContextMenu>SuggestNames('suggestApiAction')</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="suggestApiAction" name="Action" arity="ONE">
			<Description>Specifies whether the suggest should use a simple service or a suggest dispatcher which varies depending on the query input. </Description>			
			<Values>
				<Value>dispatcher</Value>
			</Values>
		</Option>
		<Option id="suggestApiConfig" name="Config" arity="ZERO_OR_ONE">
			<Description>
				<![CDATA[
					Indicates the name of the default Search API, for example, <code>sapi0</code>.
				]]>
			</Description>
			<Placeholder>sapi0</Placeholder>
			<Functions>
				<ContextMenu>ApiConfig()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				<Check>isSpecified('ONE', 'suggestCustomSearchAPIs')</Check>
			</Functions>
		</Option>
		<Option id="suggestCustomSearchAPIs" name="Search API URL" arity="ZERO_OR_MANY">
			<Description>Defines the URL that will be used by the Search API.</Description>
			<Placeholder>http://HOST:PORT/</Placeholder>
			<Functions>
				<Check>isSpecified('ONE', 'suggestApiConfig')</Check>
			</Functions>
		</Option>
		<Option id="suggestApiCommand" name="API command" arity="ONE">
			<Description>Specifies the suggest API command name that will be appended to the 'Search API URL'.</Description>
			<Placeholder>suggest</Placeholder>
			<Values>
				<Value>suggest</Value>
			</Values>
			<Functions>
				<ContextMenu>ApiCommand()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="draggableFiltersSelector" name="Draggable filters selector">
			<Description>CSS selector of the draggable filters (references for example refine panel category selector ('.refine-link')).</Description>
		</Option>
	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="hideWidget">true</DefaultValue>
		<DefaultValue name="showReset">true</DefaultValue>
		<DefaultValue name="displayMode">Compact</DefaultValue>
		<DefaultValue name="display2D">false</DefaultValue>
		<DefaultValue name="keepQueryParams">true</DefaultValue>
		<DefaultValue name="searchParameter">q</DefaultValue>
		<DefaultValue name="title">${i18n['widgets.activeFilters.title']}</DefaultValue>
		<DefaultValue name="separateFeeds">false</DefaultValue>
		<DefaultValue name="draggableFiltersSelector">.refine-link</DefaultValue>
	</DefaultValues>

</Widget>
