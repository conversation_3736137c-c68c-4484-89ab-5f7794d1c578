<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<render:import parameters="feed,iconCss,collectionId" />

<config:getOption name="elemPerPage" var="elemPerPage" defaultValue="4" />
<config:getOption name="metaForId" var="metaForId" />
<config:getOption var="facetId" name="facetId" />

<div class="history hidden">
	<c:set var="entriesCount" value="${0}" />
	<search:forEachEntry var="entry" feed="${feed}">
		<c:set var="entriesCount" value="${entriesCount + 1}" />
		<config:getOption var="description" name="description" entry="${entry}" feeds="${feeds}" isHtmlEscaped="true"/>
		<search:getMetaValue var="currentEntryId" entry="${entry}" metaName="${metaForId}" />
		<search:getFacet var="typeFacet" facetId="${facetId}" entry="${entry}"/>
		<search:forEachCategory var="typeCategory" root="${typeFacet}">
			<plma:getIconName var="typeCategoryIcon" category="${typeCategory}" entry="${entry}"/>
			<string:escape var="typeCategoryIcon" value="${typeCategoryIcon}" escapeType="HTML" />
			<plma:getCategoryColor var="typeCategoryColor" category="${typeCategory}"/>
			<string:escape var="typeCategoryColor" value="${typeCategoryColor}" escapeType="HTML" />
		</search:forEachCategory>
		<a class="hidden" href="#" title="${description}">
			<div data-entry-id="${currentEntryId}" class="entry">
				<div class="header">
					<div class="header-container">
						<div class="icon-container">
							<div class="icon" data-color="${typeCategoryColor}">
								<i class="${typeCategoryIcon}"></i>
							</div>
						</div>
						<div class="title">
							<string:escape escapeType="HTML">
								<config:getOption name="content" entry="${entry}" feeds="${feeds}" />
							</string:escape>
						</div>
					</div>
					<div class="main-container">
						<config:getOption var="metasList" name="metas"/>
						<search:forEachMeta var="meta" entry="${entry}" filterMode="INCLUDE" metasList="${metasList}" showEmptyMetas="true" >
							<search:getMetaLabel var="metaLabel" meta="${meta}"/>
							<c:set var="metaValue" value=""/>
							<%-- Value with no highlight --%>
							<c:set var="rawMetaValue" value=""/>
							<search:forEachMetaValue meta="${meta}" entry="${entry}" var="value" varRaw="rawValue" varStatus="loop" >
								<c:choose>
									<c:when test="${fn:length(fn:split(value,'#')) > 1}">
										<c:set var="metaValue" value="${metaValue}${fn:split(value,'#')[1]}"/>
										<c:set var="rawMetaValue" value="${rawMetaValue}${fn:split(rawValue,'#')[1]}"/>
									</c:when>
									<c:otherwise>
										<c:set var="metaValue" value="${metaValue}${value}"/>
										<c:set var="rawMetaValue" value="${rawMetaValue}${rawValue}"/>
									</c:otherwise>
								</c:choose>
								<c:if test="${not loop.last}"><c:set var="metaValue" value="${metaValue}, "/></c:if>
								<c:if test="${not loop.last}"><c:set var="rawMetaValue" value="${rawMetaValue}, "/></c:if>
							</search:forEachMetaValue>
							<div class="metas">
								<div class="type"><string:escape escapeType="HTML">${metaLabel}</string:escape></div>
								<div class="meta-values"><string:escape escapeType="HTML">${metaValue}</string:escape></div>
							</div>
						</search:forEachMeta>
					</div>
					<div class="facets">
						<config:getOption var="hitFacetsList" name="hitFacetsList" defaultValue=""/>
						<search:forEachFacet var="facet" entry="${entry}" filterMode="INCLUDE" facetsList="${hitFacetsList}" showEmptyFacets="true" varStatus="loop" >
							<search:getFacetLabel var="facetLabel" facet="${facet}"/>
							<string:escape var="facetLabel" value="${facetLabel}" escapeType="HTML" />
							<plma:getIconName var="facetIcon" facetId="${facet.id}"/>
							<string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML" />
							<search:forEachCategory root="${facet}" var="category" iterationMode="LEAVES">
								<plma:getIconName var="categoryIcon" category="${category}" entry="${entry}"/>
								<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML" />
								<plma:getCategoryColor var="categoryColor" category="${category}"/>
								<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML" />
								<search:getCategoryLabel var="categoryLabel" category="${category}"/>
								<string:escape var="categoryLabel" value="${categoryLabel}" escapeType="HTML" />
								<div class="facet">
									<span class="categoryIcon fonticon ${not empty categoryIcon ? categoryIcon : facetIcon}" style="color: ${categoryColor};"></span>
									<span class="categoryName" title="${facetLabel}: ${categoryLabel}">${categoryLabel}</span>
								</div>
							</search:forEachCategory>
						</search:forEachFacet>
					</div>
				</div>
				<span class="collection-icons-div fonticon ${iconCss}">				
			</div>
		</a>
	</search:forEachEntry>
</div>
<div class="pagination">
	<c:if test="${elemPerPage > 0 && entriesCount > elemPerPage}">
		<%-- equivalent of Math.ceil --%>
		<fmt:formatNumber
			var="nbPage"
			value="${entriesCount/elemPerPage + (entriesCount/elemPerPage % 1 == 0 ? 0 : 0.5)}"
			type="number"
			pattern="#"
		/>
		<c:forEach begin="${0}" end="${nbPage - 1}" varStatus="loop">
			<span data-page-nb="${loop.index}" class="page-button ${loop.index == 0 ? "selected" : ""}"></span>
		</c:forEach>
	</c:if>
</div>