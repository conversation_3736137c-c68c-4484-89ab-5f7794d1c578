<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA Chart 2" group="PLM Analytics/Visualizations/Charts" premium="true"
		xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget displays a custom PLMA chart.</Description>

	<Preview>
		<![CDATA[
		<img src="/resources/highcharts/images/preview_linechart.png" alt="Line Chart" />
	]]>
	</Preview>

	<Platforms>
		<Platform type="web" supported="true"/>
		<Platform type="mobile" supported="true"/>
	</Platforms>

	<Includes>
		<Include type="css" path="/resources/highcharts/css/style.css"/>
		<Include type="css" path="../plmaResources/css/lightbox.less"/>
		<Include type="css" path="css/style.less"/>
		<Include type="js" path="../plmaResources/js/lodash.min.js"/>
		<Include type="js" path="js/plmaCharts.js"/>
		<Include type="js" path="js/plmaChartButtons.js"/>
		<Include type="js" path="/resources/highcharts/js/highcharts.src.js"/>
		<Include type="js" path="/resources/highcharts/js/highcharts.js"/>
		<Include type="js" path="/resources/highcharts/js/lineChart.js"/>
		<Include type="js" path="/resources/highcharts/js/highcharts-more.js"/>
		<Include type="js" path="../plmaResources/js/plmaHighcharts.js"/>
		<Include type="js" path="../plmaResources/js/popupLib.js"/>
		<Include type="js" path="../plmaResources/js/lightbox.js"/>
		<Include type="js" path="../plmaResources/js/moment-with-locales.min.js" />
		<Include type="js" path="../plmaResources/js/widgetButtonManager.js"/>
		<Include type="js" path="../plmaResources/js/fullScreenWidget.js"/>

		<Include type="js" path="/resources/widgets/plmaResources/lib/notify/notify.js" />
		<Include type="js" path="/resources/widgets/plmaResources/lib/notify/notify-plma.js" />

		<Include type="js" path="../plmaResources/js/exportTo3DSpace/EXPUtils.js"/>
		<Include type="js" path="../plmaResources/js/exportTo3DSpace/documentManager3DSpace.js"/>
		<Include type="js" path="../plmaResources/js/exportTo3DSpace/exportTo3DSpace.js"/>

		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="../3dxpSearchAndRefine/js/message-handler.js"/>
		<Include type="js" path="../plmaResources/js/experienceAPIs/Session3DContent.js"/>
		<Include type="js" path="../plmaResources/js/colorpicker.js"/>
		<Include type="js" path="../plmaResources/js/experienceAPIs/Enrich3DAPI.js"/>
	</Includes>

	<Dependencies>
		<Widget name="formInput"/>
		<Widget name="plmaResources"/>
	</Dependencies>

	<SupportWidgetsId arity="ZERO"/>
	<SupportFeedsId arity="MANY"/>
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>charts.nodata</JsKey>
			<JsKey>widget.plmacharts.menu.fullsize</JsKey>
			<JsKey>widget.plmacharts.legend.hide</JsKey>
			<JsKey>widget.plmacharts.legend.display</JsKey>
			<JsKey>widget.plmacharts.exportchart.err.nodata</JsKey>
			<JsKey>widget.plmacharts.exportchart.err.convert</JsKey>
		</JsKeys>
	</SupportI18N>

	<DefaultValues displayName="Normal" previewUrl="">
		<DefaultValue name="sortMode">default</DefaultValue>
		<DefaultValue name="basedOn">####count##column##0##Disabled####${facet.description}##${category.description}
		</DefaultValue>
		<DefaultValue name="maxCategories">0</DefaultValue>
		<DefaultValue name="height">350</DefaultValue>
		<DefaultValue name="dataProcessor"><![CDATA[
function(data) {
	return data;
}
]]></DefaultValue>
		<DefaultValue name="exportChart">false</DefaultValue>
		<DefaultValue name="imageType">svg</DefaultValue>
		<DefaultValue name="enableNewDoc">false</DefaultValue>
		<DefaultValue name="enableBookmarkMode">true</DefaultValue>
	</DefaultValues>

	<OptionsGroup name="General">
		<Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
		</Option>
		<OptionComposite id="basedOn" name="Series" arity="ZERO_OR_MANY" glue="##">
			<Option id="feedId" name="Feed" arity="ZERO_OR_ONE">
				<Description>Specifies the feed to use for this series. Can be empty if the widget is based on only one
					feed.
				</Description>
				<Functions>
					<ContextMenu>Feeds()</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="facetId" name="Facet" arity="ONE">
				<Description>
					Specifies the facet to use for this series. &lt;br/&gt;
					&lt;b&gt;Warning:&lt;/b&gt; using a facet with a large number of categories
					(several thousands), without limiting the number of returned categories, can
					cause performance issues. Use the Search Logic configuration or the
					&lt;b&gt;Limit facet categories&lt;/b&gt; feed trigger to reduce the number of
					returned categories.
				</Description>
				<Functions>
					<ContextMenu>Facets()</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="aggregation" name="Aggregation" arity="ZERO_OR_ONE">
				<Description>The aggregation (calculated on the specified facet) to display for this series. Defaults to
					'count'.
				</Description>
				<Functions>
					<ContextMenu>Aggregations('facetId')</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="representation" name="Representation" arity="ONE">
				<Description>The chart type used to display the series.</Description>
				<Values>
					<Value>column</Value>
					<Value>area</Value>
					<Value>areaspline</Value>
					<Value>bar</Value>
					<Value>line</Value>
					<Value>spline</Value>
					<Value>pie</Value>
					<Value>waterfall</Value>
				</Values>
			</Option>
			<Option id="axis" name="Axis" arity="ZERO_OR_ONE">
				<Description>The y-axis to use for this series.</Description>
				<Functions>
					<Display>SetType('number')</Display>
					<Check>isInteger</Check>
				</Functions>
			</Option>
			<Option id="stacking" name="Stacking" arity="ONE">
				<Description>
					Allows to add up series between them instead of displaying them independently. &lt;br/&gt;
					'Disabled' displays series independently, 'Normal' stacks them by adding their values, 'Percent'
					stacks them with equal sizes to show relative values.&lt;br/&gt;
					If set to 'Normal' or 'Percent', the user can switch to the other mode.
				</Description>
				<Values>
					<Value>Disabled</Value>
					<Value>Normal</Value>
					<Value>Percent</Value>
				</Values>
			</Option>
			<Option id="stack" name="Stack index" arity="ZERO_OR_ONE">
				<Description>The stack on which this serie should be stacked. This allows stacking several series
					together or on different stacks. Defaults to 0.
				</Description>
				<Functions>
					<Display>SetType('number')</Display>
					<Check>isInteger</Check>
				</Functions>
			</Option>
			<Option id="legend" name="Legend" arity="ZERO_OR_ONE" isEvaluated="false">
				<Description>A short description of what the series represents.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<ContextMenu>WIDGET_EvalFacet()</ContextMenu>
					<ContextMenu>WIDGET_EvalCategory()</ContextMenu>
				</Functions>
			</Option>
			<Option id="pointLegend" name="Point legend" arity="ZERO_OR_ONE" isEvaluated="false">
				<Description>A short description of what each point represents.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
				</Functions>
			</Option>
			<Option id="colorConfig" name="Color config" arity="ZERO_OR_ONE">
				<Description>You can specify the name of a facet to use its color configuration (from the applicative
					configuration) for this chart. By default, the color configuration is inferred from the charted
					facet.
				</Description>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>
	<OptionsGroup name="Axes">
		<Option id="xAxisLabel" name="X axis label" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>The legend displayed below the x-axis to indicate what is displayed and/or the unit.
			</Description>
		</Option>
		<Option id="yAxisLabels" name="Y axis label" arity="ZERO_OR_MANY" isEvaluated="true">
			<Description>The legend displayed next to the corresponding y-axis to indicate what is displayed and/or the
				unit.
			</Description>
		</Option>
		<Option id="categoriesOrder" name="Categories order" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Optional comma-separated list of facet categories to specify their display order on the
				x-axis.
			</Description>
		</Option>
		<Option id="maxCategories" name="Max number of categories">
			<Description>Specifies the maximum number of facet categories to display. Enter 0 to display all
				categories. &lt;br/&gt;
				&lt;b&gt;Warning:&lt;/b&gt; facets with high cardinality will still be costly to
				compute, even with a limit set here. Use the Search Logic configuration or the
				&lt;b&gt;Limit facet categories&lt;/b&gt; feed trigger to reduce the number of
				returned categories.
			</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Display>ToggleDisplay({valueToMatch: ['0'], hideOptions: ['additionalCategories']})</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="additionalCategories" name="Additional categories" arity="ONE">
			<Description>
				What to do with additional categories when the max number of categories has been reached:&lt;ul&gt;
				&lt;li&gt;Hide: Additional categories are not displayed.&lt;/li&gt;
				&lt;li&gt;Others: Additional categories are merged into a single 'Others' category. &lt;b&gt;Warning:&lt;/b&gt;
				This will only work with aggregations based on SUM.&lt;/li&gt;
				&lt;li&gt;Paginate: Categories are paginated, with 'maxCategories' categories per page.&lt;/li&gt;
				&lt;/ul&gt;
			</Description>
			<Values>
				<Value>Hide</Value>
				<Value>Others</Value>
				<Value>Paginate</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['Paginate'], showOptions:['paginateCondition']})</Display>
			</Functions>
		</Option>
		<Option arity="ZERO_OR_ONE" name="Paginate Condition" id="paginateCondition" isEvaluated="true">
			<Description>Condition to display the pagination (default is true).</Description>
			<Functions>
				<Display>SetType('code', 'mel')</Display>
			</Functions>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Filters">
		<Option id="enableRefines" name="Enable refine" arity="ONE">
			<Description>Allows the user to refine on a category by clicking the corresponding point or label on the
				chart.
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['target', 'forceRefineOnFeeds',
					'forceRefineOnFacets']})
				</Display>
			</Functions>
		</Option>
		<Option id="target" name="Destination page on click" isUrlEncoded="true" isEvaluated="true">
			<Description>Specifies the URL that opens when clicking on a point or label of the chart. If empty, the user
				will stay on the current page.
			</Description>
			<Functions>
				<ContextMenu>Pages()</ContextMenu>
			</Functions>
		</Option>
		<Option id="forceRefineOnFeeds" name="Force refinement for feeds" arity="ZERO_OR_MANY">
			<Description>Forces refinements on the specified feeds.</Description>
			<Functions>
				<ContextMenu>Feeds()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="forceRefineOnFacets" name="Force refinement for facets" arity="ZERO_OR_MANY">
			<Description>Applies refinements on the specified facets instead of the original one. Only works for 1D
				facets.
			</Description>
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="forceRefineOnMulti" name="Force refinement for multidimension facet" arity="ZERO_OR_ONE">
			<Description>
				If the displayed facet is an AxB multidimension facet, you can check this option to apply refinements to
				both A and AxB.
				This will enable drilling down on the corresponding dimension. &lt;br/&gt;
				By default, this option is deactivated and refinements only apply to the 1-dimension facet.
				In PLM Analytics app, propagating these refinements to multidim facets is handled by a trigger.
				Only check this option if you specifically want refinements to add the multidim refine parameter to the
				URL.
			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="hideEmpty" name="Hide Empty Series" arity="ONE">
			<Description>Hides series with no values.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Advanced">
		<Option id="activateReloadChart" name="Activate Reload Chart" arity="ONE">
			<Description>Activates a button to reload chart with additionnal parameters.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'],
					showOptions:['labelReload','iconReload','additionalParams','reloadCondition']})
				</Display>
			</Functions>
		</Option>
		<Option id="labelReload" name="Button label" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Specifies the label of the button. If empty, no label is displayed.</Description>
		</Option>
		<Option id="iconReload" name="Button icon" arity="ZERO_OR_ONE">
			<Description>Specifies the CSS class name of the icon added to the button. If empty, no icon is displayed.
			</Description>
		</Option>
		<OptionComposite id="additionalParams" name="Additional parameters" arity="ZERO_OR_MANY">
			<Description>Additionnal URL parameters to be sent when the hidden widget is called through Ajax.
			</Description>
			<Option id="paramName" name="Name" arity="ONE">
				<Description>The name of the parameter.</Description>
				<Functions>
					<ContextMenu>PageParameters()</ContextMenu>
				</Functions>
			</Option>
			<Option id="paramValue" name="Value" arity="ZERO_OR_ONE" isEvaluated="true">
				<Description>The value to send for this parameter. Can be a MEL expression.</Description>
			</Option>
		</OptionComposite>
		<Option arity="ZERO_OR_ONE" name="Reload Button Condition" id="reloadCondition" isEvaluated="true">
			<Description>Condition to display the reload button (default is true).</Description>
			<Functions>
				<Display>SetType('code', 'mel')</Display>
			</Functions>
		</Option>
		<Option id="grouping" name="Data grouping">
			<Description>Groups the data.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['dataGroups']})</Display>
			</Functions>
		</Option>
		<OptionComposite id="dataGroups" name="Groups" arity="ZERO_OR_MANY" glue="##">
			<Option id="begin" name="Interval Start">
				<Description>Specifies the start of the interval.</Description>
				<Functions>
					<Check>isInteger</Check>
				</Functions>
			</Option>
			<Option id="end" name="Interval End">
				<Description>Specifies the end of the interval.</Description>
				<Functions>
					<Check>isInteger</Check>
				</Functions>
			</Option>
			<Option id="label" name="Label" isEvaluated="true">
				<Description>Specifies the label of the interval.</Description>
			</Option>
		</OptionComposite>
		<Option id="stackingButton" name="Display a button to modify stacking">
			<Description>Display a button to modify stacking. In the chart menu, you will be able to click to pass to stacking percent and stacking normal modes.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="activateFilter" name="Activate filter on categories">
			<Description>Activates filters on category names.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('false',['widgetsId'],[], true, false)</Display>
			</Functions>
		</Option>
		<OptionComposite id="widgetsId" name="Wuid from widget to update them in ajax at each search"
						 arity="ZERO_OR_MANY" glue="##">
			<Description>The list of widget wuids to update with a search.</Description>
			<Option id="idWidget" name="Widget Id" arity="ONE">
			</Option>
		</OptionComposite>
		<Option arity="ONE" name="Show options for Waterfall chart" id="optionsWaterfall">
			<Description>Displays the options for a waterfall chart.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'],
					showOptions:['upColor','downColor','finalSumWaterfall','finalSumDisplayName','finalSumDisplayColor','intermediateSumWaterfall','intermediateSum']})
				</Display>
			</Functions>
		</Option>
		<!--		 <Option id="upColor" name="Color for positive values in Waterfall chart" isEvaluated="true"> -->
		<!--		 </Option> -->
		<!--		 <Option id="downColor" name="Color for negative values in Waterfall chart" isEvaluated="true"> -->
		<!--		 </Option> -->
		<Option arity="ONE" name="Display final sum for Waterfall chart" id="finalSumWaterfall">
			<Description>On a waterfall chart, displays a bar with the final sum.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'],
					showOptions:['finalSumDisplayName','finalSumDisplayColor']})
				</Display>
			</Functions>
		</Option>
		<Option id="finalSumDisplayName" name="Final sum display name" isEvaluated="true">
		</Option>
		<Option id="finalSumDisplayColor" name="Final sum color">
		</Option>
		<Option arity="ONE" name="Display intermediate sum for Waterfall chart" id="intermediateSumWaterfall">
			<Description>On a waterfall chart, displays the intermediate sum.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['intermediateSum']})</Display>
			</Functions>
		</Option>
		<OptionComposite arity="ZERO_OR_ONE" id="intermediateSum" name="Intermediate sum" glue="##">
			<Option id="intermediateSumType" name="Type">
				<Values>
					<Value>Fixed number of sums</Value>
					<Value>Sum to fixed number of values</Value>
					<Value>List of positions (2,4,6 etc...)</Value>
				</Values>
			</Option>
			<Option id="intermediateSumNb" name="Number"></Option>
			<Option id="isIntermediate" name="Intermediate or total">
				<Values>
					<Value>Total</Value>
					<Value>Intermediate</Value>
				</Values>
			</Option>
			<Option id="intermediateSumName" name="Display name" isEvaluated="true"></Option>
			<Option id="intermediateSumColor" name="Color"></Option>
		</OptionComposite>
		<Option id="customSeries" name="Custom series class">
			<Description>Activate custom series generation. If true, a custom class can be specified for Highcharts series generation.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['customSeriesClass']})</Display>
			</Functions>
		</Option>
		<Option id="customSeriesClass" name="Custom series class">
			<Description>Custom class for series generation (implements
				'com.exalead.cv360.searchui.view.jspapi.custom.taglib.highcharts.factories.ISeriesFactory).
				Implementing this interface, it is possible to use widget resultfeed(s) to generate series based on entries,
				categories ... in a different way that the OOTB components.</Description>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Buttons">
		<Option id="exportChart" name="Export Chart to 3DSpace">
			<Description>Enables Chart Share to platform. This functionality captures the Image of the chart
				This image will be uploaded to 3DSpace. To upload image you can create new document by enabling option
				here, or drop the 3DSpace Object to modify. For Existing Object, you can either choose to add new file or
				revise existing file. if you enable this option, make sure you have required JS files at page or app level.
				All JS files inside 'plmaResources/js/exportTo3DSpace' will be required.
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['spaceUrl', 'imageType','enableNewDoc', 'enableBookmarkMode']})</Display>
			</Functions>
		</Option>
		<Option id="spaceUrl" name="Url of 3DSpace" arity="ZERO_OR_ONE">
			<Description>Used to call 3DSpace API's for uploading the image to 3DSpace Object. If not provided internally queried to PlatformServices.</Description>
			<Functions>
				<Display></Display>
			</Functions>
		</Option>
		<Option id="imageType" name="Type of Image" arity="ONE">
			<Description>Image type to export.</Description>
			<Values>
				<Value>svg</Value>
				<Value>png</Value>
			</Values>
		</Option>
		<Option id="enableNewDoc" name="Enable New Document Creation">
			<Description><![CDATA[ If true, New Document creation option will be enabled for Share to platform Functionality.
				(Default value is false).
				<span style="color:red;"><b>CAUTION</b>: Use this option carefully as it can create many object in 3DSpace/FCS.</span>
			]]></Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="enableBookmarkMode" name="Enable Bookmark Workflow">
			<Description><![CDATA[ If true, the dropdowns of bookmark and document selection are provided instead of
				Drop Box. This is better way to handle documents instead of created floating documents.<br/>
				This will be default = true. If you want old functionality turn off the option explicitly.<br/>
				<i>Note we may deprecate Drop functionality in future releases </i>
			]]></Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>

		<OptionComposite name="Buttons" id="buttons" arity="ZERO_OR_MANY">
			<Option name="Icon CSS" id="css">
				<Description>Specifies the CSS class name of the icon added to the button, which is displayed on the
					widget header.
				</Description>
			</Option>
			<Option name="Label" id="label" isEvaluated="true">
				<Description>A short text displayed in the button.</Description>
			</Option>
			<Option name="On click" id="onClick" isEvaluated="true">
				<Description><![CDATA[ Javascript code that is executed when clicking the button. <br/>
					function(event, chartData){}
				]]></Description>
				<Placeholder>function(event, chartData){ }</Placeholder>
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>
			<Option name="On Init" id="onInit" isEvaluated="true">
				<Description><![CDATA[ Javascript code that is executed when the button is initialized. <br/>
					function($button){ /*if(!window.dashboardController){ $button.hide(); }*/}
				]]></Description>
				<Placeholder>function($button){ if(!window.dashboardController){ $button.hide(); } }</Placeholder>
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>

	<OptionsGroup name="JavaScript">
		<Option id="opts" name="JavaScript options" arity="ONE" isEvaluated="true">
			<Description>Lets you customize some javascript options used by the Highcharts library. Your javascript
				options will overwrite the default ones. For example, it can be useful to redirect clicks on pages.
			</Description>
			<Values>
				<Value><![CDATA[

{
	/*colors: ['#4572A7', '#AA4643', '#89A54E', '#80699B', '#3D96AE', '#DB843D', '#92A8CD', '#A47D7C', '#B5CA92'],*/
	/*colors: ['#058DC7', '#50B432', '#ED561B', '#DDDF00', '#24CBE5'],*/
	chart: {
		defaultSeriesType: 'column',
		zoomType: 'xy'
	},
	title: {
		text: null
	},
	credits: {
		enabled: false
	},
	tooltip: {
		backgroundColor: '#222',
		borderWidth: 0,
		style: {
			color: '#fff'
		},
		useHTML: true,
		headerFormat: '<div style="display: inline-block; margin-right: 4px;margin-bottom: 2px;width:6px;height:6px;border-radius:3px;background-color:{series.color};"></div><b>{series.name}</b><br />{point.key}',
		pointFormat: ':{point.y}<br />',
		footerFormat:'',
		valueDecimals: 0
	},
	legend: {
		margin: 20,
		borderWidth: 0,
		layout: 'horizontal',
		verticalAlign: 'top',
		itemWidth: 150
	},
	xAxis: [{
		labels: {
			rotation: -45,
			align: 'right'
		},
		tickInterval: (
			(data != null && data.length > 0 && data[0].xAxis != null && data[0].xAxis.length > 0 && data[0].xAxis[0].categories != null) ? 
				Math.floor(data[0].xAxis[0].categories.length / 15, 0) + 1 : null),
		events: {
			setExtremes: function(event){
				PLMAChart2.utils.setXTickInterval(event);
			}
		}
	}],
	yAxis: [{
		/* title: {text: null} */
	
	}],
	plotOptions: {
		series: {
			// Configure color threshold
			//threshold:0,
			//negativeColor:"#FF0000",
			turboThreshold: 0,
			cursor: 'pointer',
				followPointer: true,
			stickyTracking: true,
			shadow: false,
			marker: {
				enabled: false
			},
			point: {
				events: {
					click: function(e) {
						if (e.point.r) {
							hideOtherSeries(e.point);
						}
					}
				}
			}
		}
	}
}

]]></Value>
			</Values>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Data processor">
		<Description>The Data Processor allows you to manipulate the json before its execution. Be careful, some
			properties of the data object may be overridden by the ones set in the Javascript tab.
		</Description>
		<Option id="dataProcessor" name="Javascript function">
			<Description>Lets you define the Javascript function that will be executed on data before the Highcharts
				processing.
			</Description>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Documentation">
		<Option id="displayDoc" name="Enable Documentation">
			<Description>If true, adds a button to display a HTML documentation to give the user information about the chart. (Default value is false)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['doc']})</Display>
			</Functions>
		</Option>
		<Option id="doc" name="User Reference" isEvaluated="true">
			<Description>This is an HTML code to describe the chart.</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
		<Option id="techDoc" name="Technical Reference" isEvaluated="true">
			<Description>This is the chart's technical reference (it won't be displayed in the application).</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Style">
		<Option id="width" name="Width">
			<Description>Specifies the widget width (pixels). You must enter an integer.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="height" name="Height">
			<Description>Specifies the widget height (pixels). If no integer is given, the chart will take 100% of the
				available height.
                &lt;b&gt;WARNING&lt;/b&gt;:
                If widget is used as a sub-widget of a Chart board cell widget, height must not be set to take all available height.
			</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="minwidth" name="Min Width">
			<Description>Specifies the minimum width of the widget (pixels). You must enter an integer.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="enableFullScreen" name="Enable full screen" arity="ONE">
			<Description>Allows the user to resize the chart to full screen.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<!-- <Option id="enableFullScreen" name="Enable full screen" arity="ONE">
			<Description>

			</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>-->
	</OptionsGroup>
</Widget>
