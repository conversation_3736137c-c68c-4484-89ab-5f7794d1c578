(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('react'), require('d3')) :
        typeof define === 'function' && define.amd ? define(['exports', 'react', 'd3'], factory) :
            (global = global || self, factory(global.iiChart = {}, global.React, global.d3));
}(this, function (exports, React, d3) { 'use strict';

    var React__default = 'default' in React ? React['default'] : React;

    function _extends() {
        _extends = Object.assign || function (target) {
            for (var i = 1; i < arguments.length; i++) {
                var source = arguments[i];

                for (var key in source) {
                    if (Object.prototype.hasOwnProperty.call(source, key)) {
                        target[key] = source[key];
                    }
                }
            }

            return target;
        };

        return _extends.apply(this, arguments);
    }

    function _inheritsLoose(subClass, superClass) {
        subClass.prototype = Object.create(superClass.prototype);
        subClass.prototype.constructor = subClass;
        subClass.__proto__ = superClass;
    }

    function _objectWithoutPropertiesLoose(source, excluded) {
        if (source == null) return {};
        var target = {};
        var sourceKeys = Object.keys(source);
        var key, i;

        for (i = 0; i < sourceKeys.length; i++) {
            key = sourceKeys[i];
            if (excluded.indexOf(key) >= 0) continue;
            target[key] = source[key];
        }

        return target;
    }

    function _assertThisInitialized(self) {
        if (self === void 0) {
            throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
        }

        return self;
    }

    function unwrapExports (x) {
        return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
    }

    function createCommonjsModule(fn, module) {
        return module = { exports: {} }, fn(module, module.exports), module.exports;
    }

    var reactIs_production_min = createCommonjsModule(function (module, exports) {

        Object.defineProperty(exports, "__esModule", {
            value: !0
        });
        var b = "function" === typeof Symbol && Symbol.for,
            c = b ? Symbol.for("react.element") : 60103,
            d = b ? Symbol.for("react.portal") : 60106,
            e = b ? Symbol.for("react.fragment") : 60107,
            f = b ? Symbol.for("react.strict_mode") : 60108,
            g = b ? Symbol.for("react.profiler") : 60114,
            h = b ? Symbol.for("react.provider") : 60109,
            k = b ? Symbol.for("react.context") : 60110,
            l = b ? Symbol.for("react.async_mode") : 60111,
            m = b ? Symbol.for("react.concurrent_mode") : 60111,
            n = b ? Symbol.for("react.forward_ref") : 60112,
            p = b ? Symbol.for("react.suspense") : 60113,
            q = b ? Symbol.for("react.memo") : 60115,
            r = b ? Symbol.for("react.lazy") : 60116;

        function t(a) {
            if ("object" === typeof a && null !== a) {
                var u = a.$$typeof;

                switch (u) {
                    case c:
                        switch (a = a.type, a) {
                            case l:
                            case m:
                            case e:
                            case g:
                            case f:
                            case p:
                                return a;

                            default:
                                switch (a = a && a.$$typeof, a) {
                                    case k:
                                    case n:
                                    case h:
                                        return a;

                                    default:
                                        return u;
                                }

                        }

                    case r:
                    case q:
                    case d:
                        return u;
                }
            }
        }

        function v(a) {
            return t(a) === m;
        }

        exports.typeOf = t;
        exports.AsyncMode = l;
        exports.ConcurrentMode = m;
        exports.ContextConsumer = k;
        exports.ContextProvider = h;
        exports.Element = c;
        exports.ForwardRef = n;
        exports.Fragment = e;
        exports.Lazy = r;
        exports.Memo = q;
        exports.Portal = d;
        exports.Profiler = g;
        exports.StrictMode = f;
        exports.Suspense = p;

        exports.isValidElementType = function (a) {
            return "string" === typeof a || "function" === typeof a || a === e || a === m || a === g || a === f || a === p || "object" === typeof a && null !== a && (a.$$typeof === r || a.$$typeof === q || a.$$typeof === h || a.$$typeof === k || a.$$typeof === n);
        };

        exports.isAsyncMode = function (a) {
            return v(a) || t(a) === l;
        };

        exports.isConcurrentMode = v;

        exports.isContextConsumer = function (a) {
            return t(a) === k;
        };

        exports.isContextProvider = function (a) {
            return t(a) === h;
        };

        exports.isElement = function (a) {
            return "object" === typeof a && null !== a && a.$$typeof === c;
        };

        exports.isForwardRef = function (a) {
            return t(a) === n;
        };

        exports.isFragment = function (a) {
            return t(a) === e;
        };

        exports.isLazy = function (a) {
            return t(a) === r;
        };

        exports.isMemo = function (a) {
            return t(a) === q;
        };

        exports.isPortal = function (a) {
            return t(a) === d;
        };

        exports.isProfiler = function (a) {
            return t(a) === g;
        };

        exports.isStrictMode = function (a) {
            return t(a) === f;
        };

        exports.isSuspense = function (a) {
            return t(a) === p;
        };
    });
    unwrapExports(reactIs_production_min);
    var reactIs_production_min_1 = reactIs_production_min.typeOf;
    var reactIs_production_min_2 = reactIs_production_min.AsyncMode;
    var reactIs_production_min_3 = reactIs_production_min.ConcurrentMode;
    var reactIs_production_min_4 = reactIs_production_min.ContextConsumer;
    var reactIs_production_min_5 = reactIs_production_min.ContextProvider;
    var reactIs_production_min_6 = reactIs_production_min.Element;
    var reactIs_production_min_7 = reactIs_production_min.ForwardRef;
    var reactIs_production_min_8 = reactIs_production_min.Fragment;
    var reactIs_production_min_9 = reactIs_production_min.Lazy;
    var reactIs_production_min_10 = reactIs_production_min.Memo;
    var reactIs_production_min_11 = reactIs_production_min.Portal;
    var reactIs_production_min_12 = reactIs_production_min.Profiler;
    var reactIs_production_min_13 = reactIs_production_min.StrictMode;
    var reactIs_production_min_14 = reactIs_production_min.Suspense;
    var reactIs_production_min_15 = reactIs_production_min.isValidElementType;
    var reactIs_production_min_16 = reactIs_production_min.isAsyncMode;
    var reactIs_production_min_17 = reactIs_production_min.isConcurrentMode;
    var reactIs_production_min_18 = reactIs_production_min.isContextConsumer;
    var reactIs_production_min_19 = reactIs_production_min.isContextProvider;
    var reactIs_production_min_20 = reactIs_production_min.isElement;
    var reactIs_production_min_21 = reactIs_production_min.isForwardRef;
    var reactIs_production_min_22 = reactIs_production_min.isFragment;
    var reactIs_production_min_23 = reactIs_production_min.isLazy;
    var reactIs_production_min_24 = reactIs_production_min.isMemo;
    var reactIs_production_min_25 = reactIs_production_min.isPortal;
    var reactIs_production_min_26 = reactIs_production_min.isProfiler;
    var reactIs_production_min_27 = reactIs_production_min.isStrictMode;
    var reactIs_production_min_28 = reactIs_production_min.isSuspense;

    var reactIs_development = createCommonjsModule(function (module, exports) {
    });
    unwrapExports(reactIs_development);
    var reactIs_development_1 = reactIs_development.typeOf;
    var reactIs_development_2 = reactIs_development.AsyncMode;
    var reactIs_development_3 = reactIs_development.ConcurrentMode;
    var reactIs_development_4 = reactIs_development.ContextConsumer;
    var reactIs_development_5 = reactIs_development.ContextProvider;
    var reactIs_development_6 = reactIs_development.Element;
    var reactIs_development_7 = reactIs_development.ForwardRef;
    var reactIs_development_8 = reactIs_development.Fragment;
    var reactIs_development_9 = reactIs_development.Lazy;
    var reactIs_development_10 = reactIs_development.Memo;
    var reactIs_development_11 = reactIs_development.Portal;
    var reactIs_development_12 = reactIs_development.Profiler;
    var reactIs_development_13 = reactIs_development.StrictMode;
    var reactIs_development_14 = reactIs_development.Suspense;
    var reactIs_development_15 = reactIs_development.isValidElementType;
    var reactIs_development_16 = reactIs_development.isAsyncMode;
    var reactIs_development_17 = reactIs_development.isConcurrentMode;
    var reactIs_development_18 = reactIs_development.isContextConsumer;
    var reactIs_development_19 = reactIs_development.isContextProvider;
    var reactIs_development_20 = reactIs_development.isElement;
    var reactIs_development_21 = reactIs_development.isForwardRef;
    var reactIs_development_22 = reactIs_development.isFragment;
    var reactIs_development_23 = reactIs_development.isLazy;
    var reactIs_development_24 = reactIs_development.isMemo;
    var reactIs_development_25 = reactIs_development.isPortal;
    var reactIs_development_26 = reactIs_development.isProfiler;
    var reactIs_development_27 = reactIs_development.isStrictMode;
    var reactIs_development_28 = reactIs_development.isSuspense;

    var reactIs = createCommonjsModule(function (module) {

        {
            module.exports = reactIs_production_min;
        }
    });

    /*
    object-assign
    (c) Sindre Sorhus
    @license MIT
    */
    /* eslint-disable no-unused-vars */

    var getOwnPropertySymbols = Object.getOwnPropertySymbols;
    var hasOwnProperty = Object.prototype.hasOwnProperty;
    var propIsEnumerable = Object.prototype.propertyIsEnumerable;

    function toObject(val) {
        if (val === null || val === undefined) {
            throw new TypeError('Object.assign cannot be called with null or undefined');
        }

        return Object(val);
    }

    function shouldUseNative() {
        try {
            if (!Object.assign) {
                return false;
            } // Detect buggy property enumeration order in older V8 versions.
            // https://bugs.chromium.org/p/v8/issues/detail?id=4118


            var test1 = new String('abc'); // eslint-disable-line no-new-wrappers

            test1[5] = 'de';

            if (Object.getOwnPropertyNames(test1)[0] === '5') {
                return false;
            } // https://bugs.chromium.org/p/v8/issues/detail?id=3056


            var test2 = {};

            for (var i = 0; i < 10; i++) {
                test2['_' + String.fromCharCode(i)] = i;
            }

            var order2 = Object.getOwnPropertyNames(test2).map(function (n) {
                return test2[n];
            });

            if (order2.join('') !== '0123456789') {
                return false;
            } // https://bugs.chromium.org/p/v8/issues/detail?id=3056


            var test3 = {};
            'abcdefghijklmnopqrst'.split('').forEach(function (letter) {
                test3[letter] = letter;
            });

            if (Object.keys(Object.assign({}, test3)).join('') !== 'abcdefghijklmnopqrst') {
                return false;
            }

            return true;
        } catch (err) {
            // We don't expect any of the above to throw, but better to be safe.
            return false;
        }
    }

    var objectAssign = shouldUseNative() ? Object.assign : function (target, source) {
        var from;
        var to = toObject(target);
        var symbols;

        for (var s = 1; s < arguments.length; s++) {
            from = Object(arguments[s]);

            for (var key in from) {
                if (hasOwnProperty.call(from, key)) {
                    to[key] = from[key];
                }
            }

            if (getOwnPropertySymbols) {
                symbols = getOwnPropertySymbols(from);

                for (var i = 0; i < symbols.length; i++) {
                    if (propIsEnumerable.call(from, symbols[i])) {
                        to[symbols[i]] = from[symbols[i]];
                    }
                }
            }
        }

        return to;
    };

    /**
     * Copyright (c) 2013-present, Facebook, Inc.
     *
     * This source code is licensed under the MIT license found in the
     * LICENSE file in the root directory of this source tree.
     */

    var ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';
    var ReactPropTypesSecret_1 = ReactPropTypesSecret;

    var has = Function.call.bind(Object.prototype.hasOwnProperty);

    function emptyFunction() {}

    function emptyFunctionWithReset() {}

    emptyFunctionWithReset.resetWarningCache = emptyFunction;

    var factoryWithThrowingShims = function factoryWithThrowingShims() {
        function shim(props, propName, componentName, location, propFullName, secret) {
            if (secret === ReactPropTypesSecret_1) {
                // It is still safe when called from React.
                return;
            }

            var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');
            err.name = 'Invariant Violation';
            throw err;
        }
        shim.isRequired = shim;

        function getShim() {
            return shim;
        }
        // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.

        var ReactPropTypes = {
            array: shim,
            bool: shim,
            func: shim,
            number: shim,
            object: shim,
            string: shim,
            symbol: shim,
            any: shim,
            arrayOf: getShim,
            element: shim,
            elementType: shim,
            instanceOf: getShim,
            node: shim,
            objectOf: getShim,
            oneOf: getShim,
            oneOfType: getShim,
            shape: getShim,
            exact: getShim,
            checkPropTypes: emptyFunctionWithReset,
            resetWarningCache: emptyFunction
        };
        ReactPropTypes.PropTypes = ReactPropTypes;
        return ReactPropTypes;
    };

    var propTypes = createCommonjsModule(function (module) {
        /**
         * Copyright (c) 2013-present, Facebook, Inc.
         *
         * This source code is licensed under the MIT license found in the
         * LICENSE file in the root directory of this source tree.
         */
        {
            // By explicitly using `prop-types` you are opting into new production behavior.
            // http://fb.me/prop-types-in-prod
            module.exports = factoryWithThrowingShims();
        }
    });

    function renderText(props) {
        switch (props.type) {
            case 'treemap':
                return React__default.createElement("div", null, props.text);

            default:
                return props.text;
        }
    }

    var Tooltip = function Tooltip(props) {
        var className = props.className,
            x = props.x,
            y = props.y,
            spacing = props.spacing,
            width = props.width,
            height = props.height,
            type = props.type,
            svgClientRect = props.svgClientRect;
        var styleElt = {
            left: x + spacing.x - svgClientRect.x,
            top: y + spacing.y
        };

        if (width) {
            styleElt.width = width;
        }

        if (height) {
            styleElt.height = height;
        }

        return React__default.createElement("div", {
            className: "chart-tooltip " + className + " " + type,
            style: styleElt
        }, renderText(props));
    };

    Tooltip.propTypes = {
        x: propTypes.number,
        y: propTypes.number,
        height: propTypes.number,
        width: propTypes.number,
        spacing: propTypes.object,
        text: propTypes.oneOfType([propTypes.string, propTypes.number]),
        className: propTypes.string,
        type: propTypes.string,
        svgClientRect: propTypes.exact({
            x: propTypes.number,
            y: propTypes.number
        })
    };
    Tooltip.defaultProps = {
        x: 0,
        y: 0,
        spacing: {
            x: 0,
            y: 0
        },
        className: '',
        svgClientRect: {
            x: 0,
            y: 0
        }
    };
    Tooltip.requiresSVG = false;
    Tooltip.displayName = 'Tooltip';

    function computeSize(props) {
        if (!props) {
            return;
        }

        var className = props.className,
            type = props.type,
            text = props.text,
            width = props.width,
            height = props.height;
        var elt = document.createElement('div');
        var textWrapper;
        elt.classList.add('chart-tooltip');
        elt.style.overflow = 'auto'; //    elt.style.zIndex = -1;
        //    elt.style.opacity = 0;

        if (className) {
            elt.classList.add(className);
        }

        if (type) {
            elt.classList.add(type);
        }

        if (width) {
            elt.style.width = width + 'px';
        }

        if (height) {
            elt.style.height = height + 'px';
        }

        switch (type) {
            case 'treemap':
                var innerElt = document.createElement('div');
                innerElt.innerText = text;
                textWrapper = innerElt;
                elt.appendChild(innerElt);
                break;

            default:
                elt.innerText = text;
                textWrapper = elt;
                break;
        }

        document.body.appendChild(elt);
        var clientRect = textWrapper.getBoundingClientRect();
        clientRect.clientWidth = textWrapper.clientWidth;
        clientRect.clientHeight = textWrapper.clientHeight;
        clientRect.scrollWidth = textWrapper.scrollWidth;
        clientRect.scrollHeight = textWrapper.scrollHeight;
        textWrapper.style.display = 'inline-block';
        textWrapper.style.position = 'static';
        elt.style.width = 'auto';
        elt.style.height = 'auto';
        clientRect.innerWidth = Math.ceil(textWrapper.clientWidth);
        clientRect.innerHeight = Math.ceil(textWrapper.clientHeight);
        elt.parentNode.removeChild(elt);
        return clientRect;
    }

    var Tooltip$1 =
        /*#__PURE__*/
        function () {
            function Tooltip(text, x, y, spacing) {
                this.setTooltipArea = this.setTooltipArea.bind(this);
                this.setParentSize = this.setParentSize.bind(this);
                this.setType = this.setType.bind(this);
                this.isTooLarge = this.isTooLarge.bind(this);
                this.computeSize = this.computeSize.bind(this);
                this.getCoordInArea = this.getCoordInArea.bind(this);
                this.getData = this.getData.bind(this);
                this.text = text;
                this.x = x;
                this.y = y;
                this.spacing = spacing || {
                    x: 0,
                    y: 0
                };
                this.type = 'tooltip';
                this.parentSize = null;
                this.size = null;
                this.tooltipArea = null;
            }

            var _proto = Tooltip.prototype;

            _proto.setTooltipArea = function setTooltipArea(area) {
                this.tooltipArea = area;
                return this;
            };

            _proto.setParentSize = function setParentSize(parentSize) {
                this.parentSize = parentSize;
                return this;
            };

            _proto.setType = function setType(type) {
                this.type = type;
                return this;
            };

            _proto.isTooLarge = function isTooLarge() {
                var _this$size = this.size,
                    scrollWidth = _this$size.scrollWidth,
                    scrollHeight = _this$size.scrollHeight;
                return Math.ceil(this.parentSize.width) < scrollWidth || Math.ceil(this.parentSize.height) < scrollHeight;
            };

            _proto.computeSize = function computeSize$1(config) {
                this.size = computeSize(config);
            };

            _proto.getCoordInArea = function getCoordInArea(tooltip) {
                var areaRect = this.tooltipArea.getBoundingClientRect();

                if (tooltip.x + this.size.width + this.spacing.x > areaRect.width) {
                    tooltip.x = areaRect.width - this.size.innerWidth - this.spacing.x - 4;
                }

                if (tooltip.y + this.size.height + this.spacing.y > areaRect.height) {
                    tooltip.y = areaRect.height - this.size.innerHeight - this.spacing.y - 4;
                }

                return tooltip;
            };

            _proto.getData = function getData() {
                var x = this.x,
                    y = this.y,
                    text = this.text,
                    type = this.type,
                    parentSize = this.parentSize;
                var tooltip = {
                    x: x,
                    y: y,
                    text: text,
                    type: type
                };

                switch (type) {
                    case 'treemap':
                        this.computeSize(_extends({}, parentSize, {
                            text: text,
                            type: type
                        }));

                        if (this.isTooLarge()) {
                            delete tooltip.type;
                            return this.getCoordInArea(tooltip);
                        }

                        tooltip.x = this.parentSize.x;
                        tooltip.y = this.parentSize.y;
                        tooltip.height = this.parentSize.height;
                        tooltip.width = this.parentSize.width;
                        return tooltip;

                    default:
                        this.computeSize({
                            text: text,
                            type: type
                        });
                        return this.getCoordInArea(tooltip);
                }
            };

            return Tooltip;
        }();

    var NODE_WIDTH = 20;
    var LEAF_PADDING = 13;

    var Circle = function Circle(props) {
        var collapsable = props.collapsable,
            onCollapse = props.onCollapse,
            _onClick = props.onClick,
            node = props.node,
            className = props.className,
            onMouseMove = props.onMouseMove,
            onMouseLeave = props.onMouseLeave,
            name = props.name,
            r = props.r;
        var isClickable = _onClick || collapsable && onCollapse;
        var newProps = {
            r: r || 5,
            'data-name': name,
            className: ['dendrogram-node', className, isClickable ? 'cursor' : null].filter(function (name) {
                return name;
            }).join(' '),
            onMouseMove: onMouseMove,
            onMouseLeave: onMouseLeave,
            onClick: function onClick(event) {
                if (_onClick) {
                    _onClick(event);
                }

                if (collapsable && onCollapse) {
                    onCollapse(event, node);
                }
            }
        };
        return React__default.createElement("circle", newProps);
    };

    function initTreemap(height, width) {
        return d3.cluster().size([height, width]).separation(function () {
            return 1;
        });
    }

    function initHierarchy(data) {
        return d3.hierarchy(data, function (d) {
            return d.children;
        });
    }

    var Dendrogram =
        /*#__PURE__*/
        function (_Component) {
            _inheritsLoose(Dendrogram, _Component);

            function Dendrogram(props) {
                var _this;

                _this = _Component.call(this, props) || this;

                _this.computeInnerSize();

                _this.computeRootData();

                _this.state = _this.computeState();
                _this.renderLinks = _this.renderLinks.bind(_assertThisInitialized(_this));
                _this.renderNodes = _this.renderNodes.bind(_assertThisInitialized(_this));
                _this.computeState = _this.computeState.bind(_assertThisInitialized(_this));
                _this.computeInnerSize = _this.computeInnerSize.bind(_assertThisInitialized(_this));
                _this.computeRootData = _this.computeRootData.bind(_assertThisInitialized(_this));
                _this.updateZoom = _this.updateZoom.bind(_assertThisInitialized(_this));
                _this.toggleNode = _this.toggleNode.bind(_assertThisInitialized(_this));
                return _this;
            }

            var _proto = Dendrogram.prototype;

            _proto.componentDidUpdate = function componentDidUpdate(prevProps) {
                var _this$props = this.props,
                    deep = _this$props.deep,
                    data = _this$props.data,
                    width = _this$props.width,
                    height = _this$props.height;
                var isUpdated = false;

                if (prevProps.data !== data) {
                    this.computeRootData();
                    isUpdated = true;
                }

                if (prevProps.width !== width || prevProps.height !== height) {
                    this.computeInnerSize();
                    isUpdated = true;
                }

                if (prevProps.deep !== deep) {
                    this.updateZoom(this.rootData);
                    isUpdated = true;
                }

                if (isUpdated) {
                    this.setState(this.computeState());
                }
            };

            _proto.computeRootData = function computeRootData() {
                this.rootData = initHierarchy(this.props.data);
                this.rootData.x0 = this.height / 2;
                this.rootData.y0 = 0;
            };

            _proto.computeInnerSize = function computeInnerSize() {
                var _this$props2 = this.props,
                    margin = _this$props2.margin,
                    width = _this$props2.width,
                    height = _this$props2.height;
                this.innerHeight = height - margin.top - margin.bottom;
                this.innerWidth = width - margin.left - margin.right;
                this.treemap = initTreemap(this.innerHeight, this.innerWidth);
            };

            _proto.updateZoom = function updateZoom(d) {
                var deep = this.props.deep;

                if (!d.children && !d._children) {
                    return;
                }

                if (d.depth >= deep && d.children) {
                    d._children = d.children;
                    d.children = null;
                } else if (d.depth < deep && d._children) {
                    d.children = d._children;
                    delete d._children;
                }

                (d.children || d._children).forEach(this.updateZoom, this);
            };

            _proto.collapse = function collapse(d) {
                if (d.children) {
                    d._children = d.children;
                    d.children = null;

                    d._children.forEach(this.collapse, this);
                }
            };

            _proto.toggleNode = function toggleNode(e, node) {
                if (!node || !node.children && !node._children) {
                    return;
                }

                if (node.children) {
                    this.collapse(node);
                } else {
                    node.children = node._children;
                    delete node._children;
                }

                this.setState(this.computeState());
            };

            _proto.computeState = function computeState(dispatch) {
                var _this2 = this;

                var onUpdate = this.props.onUpdate;
                var nodes;
                var links;
                var nodeWidth = NODE_WIDTH;
                var treeData = this.treemap(this.rootData); // Compute the new tree layout.

                nodes = treeData.descendants();
                links = treeData.descendants().slice(1);
                var rootLabel = nodes[0].data;
                rootLabel.computedSize = computeLabelSize([rootLabel]); // Normalize for fixed-depth. Align nodes on right if node is the last child

                nodes.forEach(function (d) {
                    d.y = (d.children ? d.depth : _this2.rootData.height) * nodeWidth;
                });

                if (dispatch !== false && onUpdate) {
                    onUpdate({
                        nodes: nodes,
                        links: links
                    });
                }

                return {
                    nodes: nodes,
                    links: links,
                    rootLabel: rootLabel
                };
            };

            _proto.renderNodes = function renderNodes() {
                var _this3 = this;

                var _this$props3 = this.props,
                    showLeavesNode = _this$props3.showLeavesNode,
                    collapsable = _this$props3.collapsable,
                    onNodeMouseover = _this$props3.onNodeMouseover,
                    onNodeMouseleave = _this$props3.onNodeMouseleave,
                    hideRootLabel = _this$props3.hideRootLabel,
                    getLabel = _this$props3.getLabel;

                function showLabel(node, index) {
                    if (!node.children && !node._children) {
                        return true;
                    }

                    return (!node.children || !node.parent) && (index > 0 || index === 0 && (!hideRootLabel || node._children));
                }

                return this.state.nodes.map(function (node, index) {
                    var label = getLabel ? getLabel(node.data) : node.data.id;
                    var labelProps = {
                        className: (node._children ? 'has-child ' : ' ') + 'dendrogram-label',
                        textAnchor: node.children ? 'end' : 'start',
                        x: node.children ? -LEAF_PADDING : LEAF_PADDING
                    };

                    if ((node.children || node._children) && collapsable) {
                        labelProps.className += ' cursor';

                        labelProps.onClick = function (e) {
                            _this3.toggleNode(e, node);
                        };
                    }

                    return React__default.createElement("g", {
                        key: "" + node.data.name + index,
                        transform: "translate(" + node.y + "," + node.x + ")"
                    }, showLabel(node, index) && React__default.createElement("text", _extends({
                        alignmentBaseline: "middle"
                    }, labelProps), label), showLeavesNode && !node.children && !node._children && React__default.createElement(Circle, {
                        r: "4",
                        className: "dendrogram-leaf"
                    }), (node.children || node._children) && React__default.createElement(Circle, _extends({
                        name: label,
                        onMouseMove: onNodeMouseover,
                        onMouseLeave: onNodeMouseleave,
                        onCollapse: _this3.toggleNode,
                        node: node
                    }, _this3.props)));
                });
            };

            _proto.diagonal = function diagonal(s, d) {
                return "M " + s.y + "," + s.x + " L " + d.y + "," + s.x + " L " + d.y + "," + d.x;
            };

            _proto.renderLinks = function renderLinks() {
                var _this4 = this;

                return this.state.links.map(function (node, index) {
                    return React__default.createElement("path", {
                        key: "" + index,
                        className: "chart-link",
                        d: _this4.diagonal(node, node.parent)
                    });
                });
            };

            _proto.render = function render() {
                var _this5 = this;

                var _this$props4 = this.props,
                    margin = _this$props4.margin,
                    width = _this$props4.width,
                    height = _this$props4.height,
                    className = _this$props4.className,
                    rest = _objectWithoutPropertiesLoose(_this$props4, ["margin", "width", "height", "className"]);

                var rootLabel = this.state.rootLabel;
                var paddingLeft = rest.hideRootLabel ? 10 : rootLabel.computedSize.width;
                delete rest.onUpdate;
                delete rest.collapsable;
                delete rest.showLeavesNode;
                delete rest.onNodeMouseover;
                delete rest.onNodeMouseleave;
                delete rest.hideRootLabel;
                delete rest.getLabel;
                return React__default.createElement("svg", _extends({
                    ref: function ref(node) {
                        return _this5.node = node;
                    },
                    width: width,
                    height: height,
                    shapeRendering: "crispEdges",
                    className: "industry-chart " + className
                }, rest), React__default.createElement("g", {
                    transform: "translate(" + paddingLeft + ", " + margin.top + ")"
                }, this.renderLinks(), this.renderNodes()));
            };

            return Dendrogram;
        }(React.Component);
    /**
     *
     * @param {Array} labels Array of oject that contain label informations
     * @param {function} getLabel function that return the label from a node label
     * return an object {width, height}
     */


    function computeLabelSize(labels, getLabel) {
        var svg = d3.select('body').append('svg').attr('class', 'industry-chart').attr('style', 'visibility:hidden; position: absolute; z-index:-1');
        svg.append('g').attr('transform', 'translate(0,0)');
        svg.select('g').selectAll('text').data(labels).enter().append('g').each(function () {
            d3.select(this).append('text').attr('class', 'dendrogram-label').attr('x', LEAF_PADDING).text(function (d) {
                return getLabel ? getLabel(d.data) : d.id;
            });
            d3.select(this).append('circle').attr('class', 'dendrogram-node dendrogram-leaf').attr('r', 4);
        });
        var box = svg.select('g').node().getBBox();
        svg.node().parentNode.removeChild(svg.node());
        return box;
    }
    /**
     * @param {*} props that receive the dendrogram with the necessary data to compute the size of the box
     * return the width
     */


    function computeWidth(props) {
        var vTreemap = initTreemap(100, 100);
        var dendrogram = initHierarchy(props.data);
        var treeData = vTreemap(dendrogram);
        var nodes = treeData.descendants();
        var labels = treeData.descendants().slice(1).map(function (n) {
            return n.data;
        });
        var rootLabel = nodes[0].data;
        var box = computeLabelSize(labels, props.getLabel);
        return box.width + dendrogram.height * NODE_WIDTH + (props.hideRootLabel && labels.length > 0 ? 10 : computeLabelSize([rootLabel]).width);
    }

    Dendrogram.propTypes = {
        /** This should be the description */
        margin: propTypes.object,
        width: propTypes.number,
        height: propTypes.number,
        className: propTypes.string,

        /**
         * Boolean indicating whether the button should render as disabled
         */
        collapsable: propTypes.bool,
        hideRootLabel: propTypes.bool,
        orientation: propTypes.string
    };
    Dendrogram.defaultProps = {
        width: 300,
        height: 300,
        orientation: 'ltr'
    };

    var min = d3.min,
        max = d3.max;

    var Label =
        /*#__PURE__*/
        function () {
            function Label(label, config) {
                this.getId = this.getId.bind(this);
                this.getLabel = this.getLabel.bind(this);
                this.label = label;
                this.config = config;
            }

            var _proto = Label.prototype;

            _proto.getId = function getId() {
                return this.config && this.config.id ? this.label[this.config.id] : this.label;
            };

            _proto.getLabel = function getLabel() {
                return this.config && this.config.getLabel ? this.config.getLabel(this.label) : this.label;
            };

            return Label;
        }();

    /**
     * Collapse a node; save children in a hidden property
     * @param {*} d node to collapse
     */
    var collapse = function collapse(d) {
        if (d.children) {
            d._children = d.children;
            d.children = null;

            d._children.forEach(collapse);
        }
    };
    /**
     * Hide node over the given level
     * @param {*} d
     * @param {*} level max visible
     */


    var zoom = function zoom(d, level) {
        if (d.children) {
            if (d.depth >= level) {
                collapse(d);
            } else {
                d.children.forEach(function (node) {
                    zoom(node, level);
                });
            }
        }
    };

    var getNodesWithChildren = function getNodesWithChildren(node) {
        return node.descendants().filter(function (child) {
            return child.children;
        });
    };

    var filter = function filter(d, cb) {
        if (d.children) {
            d.children.forEach(function (node) {
                if (cb(node)) {
                    collapse(node);
                } else {
                    filter(node, cb);
                }
            });
        }
    };

    var hierarchyUtils = /*#__PURE__*/Object.freeze({
        collapse: collapse,
        zoom: zoom,
        getNodesWithChildren: getNodesWithChildren,
        filter: filter
    });

    /**
     *
     * @param {*} color
     * @param {*} value percent 0 -> 100
     */

    function lighten(color, value) {
        var scale = getQuantileColor(color, 100);
        return scale[value * 100 - 1];
    }
    /**
     * compute a scale of colors from white to the given color
     * @param {string} color
     * @param {number} length of th scale
     * @return {array} of colors
     */


    function getQuantileColor(color, length) {
        var colors = [];
        var scale = d3.scaleLinear().domain([0, length]).range(["white", color]);

        for (var i = 0; i < length + 1; i++) {
            colors.push(scale(i));
        }

        colors.shift();
        return colors;
    }

    function Id(id) {
        this.id = id;
        this.href = window.location.href + "#" + id;
    }

    Id.prototype.toString = function () {
        return "url(" + this.href + ")";
    };

    var count = 0;
    var uid = function uid(name) {
        return new Id("O-" + (name == null ? "" : name + "-") + ++count);
    };

    var Heatmap =
        /*#__PURE__*/
        function (_Component) {
            _inheritsLoose(Heatmap, _Component);

            function Heatmap(props) {
                var _this;

                _this = _Component.call(this, props) || this;
                _this.renderLines = _this.renderLines.bind(_assertThisInitialized(_this));
                _this.computeData = _this.computeData.bind(_assertThisInitialized(_this));
                _this.handleClick = _this.handleClick.bind(_assertThisInitialized(_this));
                _this.handleMouseMove = _this.handleMouseMove.bind(_assertThisInitialized(_this));
                _this.handleMouseEnter = _this.handleMouseEnter.bind(_assertThisInitialized(_this));
                _this.handleMouseLeave = _this.handleMouseLeave.bind(_assertThisInitialized(_this));

                var _this$computeData = _this.computeData(),
                    lines = _this$computeData.lines,
                    data = _this$computeData.data,
                    min = _this$computeData.min,
                    max = _this$computeData.max;

                _this.state = {
                    lines: lines,
                    data: data,
                    min: min,
                    max: max
                };
                return _this;
            }

            var _proto = Heatmap.prototype;

            _proto._findCell = function _findCell(e) {
                return e.target.getAttribute('data-type') === 'background' ? null : e.target.parentElement;
            };

            _proto.handleClick = function handleClick(e) {
                e.preventDefault();

                var cell = this._findCell(e);

                var id = cell.getAttribute('data-id');

                var data = _extends({}, this.state.data[id]);

                delete data.id;
                e.data = data;
                this.props.onClick(e);
            };

            _proto.handleMouseMove = function handleMouseMove(e) {
                e.preventDefault();

                var cell = this._findCell(e);

                if (cell) {
                    var id = cell.getAttribute('data-id');

                    var data = _extends({}, this.state.data[id]);

                    delete data.id; //remove generated id

                    e.data = data;
                } else {
                    delete e.data;
                }

                this.props.onMouseMove(e);
            };

            _proto.handleMouseEnter = function handleMouseEnter(e) {
                e.preventDefault();

                var cell = this._findCell(e);

                if (cell) {
                    var id = cell.getAttribute('data-id');

                    var data = _extends({}, this.state.data[id]);

                    delete data.id; //remove generated id

                    e.data = data;
                } else {
                    delete e.data;
                }

                this.props.onMouseEnter(e);
            };

            _proto.handleMouseLeave = function handleMouseLeave(e) {
                e.data = null;
                this.props.onMouseLeave(e);
            };

            _proto.componentDidUpdate = function componentDidUpdate(prevProps) {
                if (prevProps.yLabels !== this.props.yLabels || prevProps.xSort !== this.props.xSort || prevProps.ySort !== this.props.ySort || prevProps.data !== this.props.data || prevProps.svgClientRect !== this.props.svgClientRect) {
                    var _this$computeData2 = this.computeData(),
                        lines = _this$computeData2.lines,
                        data = _this$computeData2.data,
                        min = _this$computeData2.min,
                        max = _this$computeData2.max;

                    this.setState({
                        lines: lines,
                        data: data,
                        max: max,
                        min: min
                    });
                }
            };

            _proto.computeDataInfos = function computeDataInfos(data) {
                return {
                    min: min(data, function (elt) {
                        return typeof elt.value === 'string' ? parseInt(elt.value) : elt.value;
                    }),
                    max: max(data, function (elt) {
                        return typeof elt.value === 'string' ? parseInt(elt.value) : elt.value;
                    })
                };
            };

            _proto.computeData = function computeData() {
                var _this2 = this;

                var _this$props = this.props,
                    data = _this$props.data,
                    yLabels = _this$props.yLabels,
                    xLabels = _this$props.xLabels,
                    xSort = _this$props.xSort,
                    ySort = _this$props.ySort,
                    svgClientRect = _this$props.svgClientRect,
                    margin = _this$props.margin;
                var lines = {};
                var computedData = {};
                var xDomain = xSort || xLabels;
                var yDomain = ySort || yLabels;
                var scaleBandHeight = d3.scaleBand().range([0, svgClientRect.height - margin.top - margin.bottom]).domain(yLabels);
                var scaleBandWidth = d3.scaleBand().range([0, svgClientRect.width - margin.left - margin.right]).domain(xLabels);
                this.scaleBandHeight = scaleBandHeight;
                this.scaleBandWidth = scaleBandWidth;

                function addData(data) {
                    var id = data.y;
                    var lineData = lines[id] && lines[id].data || [];
                    data.position = scaleBandWidth(data.x);
                    lineData.push(data);
                    return lineData;
                }

                data.forEach(function (_ref) {
                    var x = _ref.x,
                        y = _ref.y,
                        value = _ref.value,
                        id = _ref.id;

                    if (yDomain.indexOf(y) >= 0 && xDomain.indexOf(x) >= 0) {
                        id = id || Math.random().toString(36).substr(2, 9);
                        computedData[id] = {
                            x: x,
                            y: y,
                            value: value,
                            id: id
                        };
                        lines[y] = {
                            position: _this2.scaleBandHeight(y),
                            data: addData({
                                x: x,
                                y: y,
                                value: value,
                                id: id
                            }),
                            key: y
                        };
                    }
                });

                var _this$computeDataInfo = this.computeDataInfos(Object.keys(computedData).map(function (key) {
                        return computedData[key];
                    })),
                    min = _this$computeDataInfo.min,
                    max = _this$computeDataInfo.max;

                return {
                    lines: lines,
                    data: computedData,
                    min: min,
                    max: max
                };
            };

            _proto.renderLines = function renderLines() {
                var _this$props2 = this.props,
                    getColor = _this$props2.getColor,
                    transition = _this$props2.transition,
                    colors = _this$props2.colors;
                var _this$state = this.state,
                    lines = _this$state.lines,
                    max = _this$state.max; //const scaleHeight = (height - margin.top - margin.bottom) / yLabels.length;

                var colorScale = colors.map(function (c) {
                    return d3.scaleLinear().domain([0, max]).range(["white", c]);
                });
                var scaleHeight = this.scaleBandHeight.bandwidth(); //const scaleWidth = (width - margin.left - margin.right) / xLabels.length;

                var scaleWidth = this.scaleBandWidth.bandwidth();
                return Object.keys(lines).map(function (key, index) {
                    var line = lines[key];
                    return React__default.createElement("g", {
                        className: "chart-heatmap-row",
                        key: key,
                        style: transition && {
                            transitionDelay: index + "0ms"
                        },
                        transform: "translate(0, " + line.position + ")"
                    }, line.data.map(function (cell) {
                        return React__default.createElement("g", {
                            className: "chart-heatmap-cell",
                            key: cell.x,
                            style: transition && {
                                transitionDelay: cell.position + "0ms"
                            },
                            transform: "translate(" + cell.position + ", 0)",
                            "data-id": cell.id
                        }, React__default.createElement("rect", {
                            x: "0",
                            y: "0",
                            width: scaleWidth,
                            height: scaleHeight,
                            fill: getColor(cell, colorScale)
                        }));
                    }));
                });
            };

            _proto.render = function render() {
                var _this$props3 = this.props,
                    svgClientRect = _this$props3.svgClientRect,
                    onClick = _this$props3.onClick,
                    onMouseEnter = _this$props3.onMouseEnter,
                    onMouseMove = _this$props3.onMouseMove,
                    onMouseLeave = _this$props3.onMouseLeave,
                    margin = _this$props3.margin,
                    transition = _this$props3.transition;
                var events = {
                    onClick: onClick ? this.handleClick : null,
                    onMouseMove: onMouseMove ? this.handleMouseMove : null,
                    onMouseOver: onMouseEnter ? this.handleMouseEnter : null,
                    onMouseLeave: onMouseLeave ? this.handleMouseLeave : null
                };
                var className = 'chart-heatmap' + (transition ? ' transition' : '');
                return React__default.createElement("g", _extends({
                    transform: "translate(0, 0)",
                    className: className
                }, events), React__default.createElement("rect", {
                    "data-type": "background",
                    className: "chart-background",
                    width: svgClientRect.width - margin.left - margin.right,
                    height: svgClientRect.height - margin.top - margin.bottom,
                    style: {
                        opacity: 0.5
                    }
                }), this.renderLines());
            };

            return Heatmap;
        }(React.Component);

    Heatmap.requiresSVG = true;
    Heatmap.propTypes = {
        /** Data of the heatmap */
        data: propTypes.array,

        /** Enable transition on rows and cells */
        transition: propTypes.bool,
        colors: propTypes.array,
        getColor: propTypes.func,
        onClick: propTypes.func,
        onMouseMove: propTypes.func,
        onMouseOver: propTypes.func,
        onMouseLeave: propTypes.func
    };
    Heatmap.defaultProps = {
        colors: ['OrangeRed'],
        getColor: function getColor(cell, colorScale) {
            return colorScale[0](cell.value);
        }
    };

    var Label$1 = function Label(props) {
        var className = props.className,
            children = props.children,
            alignmentBaseline = props.alignmentBaseline,
            textAnchor = props.textAnchor,
            transform = props.transform;
        var propsEl = {
            alignmentBaseline: alignmentBaseline,
            textAnchor: textAnchor,
            transform: transform
        };
        return React__default.createElement("text", _extends({
            className: "chart-label " + className
        }, propsEl), children);
    };

    Label$1.requiresSVG = true;
    Label$1.propTypes = {
        alignmentBaseline: propTypes.string,
        textAnchor: propTypes.string,
        transform: propTypes.string,
        className: propTypes.string,
        svgClientRect: propTypes.object,
        children: propTypes.oneOfType([propTypes.arrayOf(propTypes.node), propTypes.node])
    };

    var SVGProps = ['accentHeight', 'accumulate', 'additive', 'alignmentBaseline', 'allowReorder', 'alphabetic', 'amplitude', 'arabicForm', 'ascent', 'attributeName', 'attributeType', 'autoReverse', 'azimuth', 'baseFrequency', 'baseProfile', 'baselineShift', 'bbox', 'begin', 'bias', 'by', 'calcMode', 'capHeight', 'clip', 'clipPath', 'clipPathUnits', 'clipRule', 'colorInterpolation', 'colorInterpolationFilters', 'colorProfile', 'colorRendering', 'contentScriptType', 'contentStyleType', 'cursor', 'cx', 'cy', 'd', 'decelerate', 'descent', 'diffuseConstant', 'direction', 'display', 'divisor', 'dominantBaseline', 'dur', 'dx', 'dy', 'edgeMode', 'elevation', 'enableBackground', 'end', 'exponent', 'externalResourcesRequired', 'fill', 'fillOpacity', 'fillRule', 'filter', 'filterRes', 'filterUnits', 'floodColor', 'floodOpacity', 'focusable', 'fontFamily', 'fontSize', 'fontSizeAdjust', 'fontStretch', 'fontStyle', 'fontVariant', 'fontWeight', 'format', 'from', 'fx', 'fy', 'g1', 'g2', 'glyphName', 'glyphOrientationHorizontal', 'glyphOrientationVertical', 'glyphRef', 'gradientTransform', 'gradientUnits', 'hanging', 'horizAdvX', 'horizOriginX', 'ideographic', 'imageRendering', 'in', 'in2', 'intercept', 'k', 'k1', 'k2', 'k3', 'k4', 'kernelMatrix', 'kernelUnitLength', 'kerning', 'keyPoints', 'keySplines', 'keyTimes', 'lengthAdjust', 'letterSpacing', 'lightingColor', 'limitingConeAngle', 'local', 'markerEnd', 'markerHeight', 'markerMid', 'markerStart', 'markerUnits', 'markerWidth', 'mask', 'maskContentUnits', 'maskUnits', 'mathematical', 'mode', 'numOctaves', 'offset', 'opacity', 'operator', 'order', 'orient', 'orientation', 'origin', 'overflow', 'overlinePosition', 'overlineThickness', 'paintOrder', 'panose1', 'pathLength', 'patternContentUnits', 'patternTransform', 'patternUnits', 'pointerEvents', 'points', 'pointsAtX', 'pointsAtY', 'pointsAtZ', 'preserveAlpha', 'preserveAspectRatio', 'primitiveUnits', 'r', 'radius', 'refX', 'refY', 'renderingIntent', 'repeatCount', 'repeatDur', 'requiredExtensions', 'requiredFeatures', 'restart', 'result', 'rotate', 'rx', 'ry', 'scale', 'seed', 'shapeRendering', 'slope', 'spacing', 'specularConstant', 'specularExponent', 'speed', 'spreadMethod', 'startOffset', 'stdDeviation', 'stemh', 'stemv', 'stitchTiles', 'stopColor', 'stopOpacity', 'strikethroughPosition', 'strikethroughThickness', 'string', 'stroke', 'strokeDasharray', 'strokeDashoffset', 'strokeLinecap', 'strokeLinejoin', 'strokeMiterlimit', 'strokeOpacity', 'strokeWidth', 'surfaceScale', 'systemLanguage', 'tableValues', 'targetX', 'targetY', 'textAnchor', 'textDecoration', 'textLength', 'textRendering', 'to', 'transform', 'u1', 'u2', 'underlinePosition', 'underlineThickness', 'unicode', 'unicodeBidi', 'unicodeRange', 'unitsPerEm', 'vAlphabetic', 'vHanging', 'vIdeographic', 'vMathematical', 'values', 'vectorEffect', 'version', 'vertAdvY', 'vertOriginX', 'vertOriginY', 'viewBox', 'viewTarget', 'visibility', 'widths', 'wordSpacing', 'writingMode', 'x', 'x1', 'x2', 'xChannelSelector', 'xHeight', 'xlinkActuate', 'xlinkArcrole', 'xlinkHref', 'xlinkRole', 'xlinkShow', 'xlinkTitle', 'xlinkType', 'xmlns', 'xmlnsXlink', 'xmlBase', 'xmlLang', 'xmlSpace', 'y', 'y1', 'y2', 'yChannelSelector', 'z', 'zoomAndPan'];

    /**
     * Return an object containing the valid props to apply to an SVGElement.
     *
     * @param {Object} props
     * @param {Object}
     */


    var cleanSVGProps = function cleanSVGProps(props, excludes) {
        if (props === void 0) {
            props = {};
        }

        if (excludes === void 0) {
            excludes = [];
        }

        var returnProps = Object.assign({}, props);
        var validProps = Object.keys(returnProps).filter(function (key) {
            if (excludes.indexOf(key) !== -1) {
                return false;
            }

            if (SVGProps.indexOf(key) !== -1) {
                return true;
            }

            return false;
        });
        Object.keys(returnProps).forEach(function (key) {
            if (validProps.indexOf(key) === -1) {
                delete returnProps[key];
            }
        });
        return returnProps;
    }; // Exports ___________________________________________________________________

    /**
     * Default function to calculate the size a node component
     * Using by Organization init and create a flexible tree
     * @param {object} item
     * @returns {array} of two elements: [width, height]
     */

    var getNodeComponentSize = function getNodeComponentSize(n) {
        var getLabel = this.props.getLabel;
        var results = {};
        var nodes = !Array.isArray(n) ? [n] : n;
        var svg = d3.select('body').append('svg').attr('class', 'industry-chart');
        svg.append('g').attr('transform', 'translate(0,0)').attr('class', this.props.className);
        svg.select('g').append('text').attr('class', 'organiszation-node-label');
        var label = svg.select('text.organiszation-node-label');
        nodes.forEach(function (node) {
            var item = node.data;
            var labelTxt = getLabel(item);
            label.text(labelTxt);
            var box = svg.select('g').node().getBBox();
            box.width = box.width + 1;
            results[labelTxt] = [Math.ceil(box.width) + 20, Math.ceil(box.height) + 10];
        });
        svg.node().parentNode.removeChild(svg.node());
        return results;
    };
    /**
     * Default Node Component
     * @param {*} props
     */


    var Node = function Node(props) {
        var node = props.node,
            getLabel = props.getLabel,
            collapsable = props.collapsable,
            toggleNode = props.toggleNode,
            className = props.className;
        var _node$data$__size = node.data.__size,
            width = _node$data$__size[0];
        var height = node.data.__size[1];
        var isCollapsable = collapsable && toggleNode && (node.children || node._children);
        var containerProps = {
            className: className ? className : ''
        };

        if (isCollapsable) {
            containerProps.onClick = function (e) {
                toggleNode(e, node);
            };

            containerProps.className += ' cursor';
        }

        return React__default.createElement("g", _extends({
            transform: "translate(" + (node.x - node.data.__size[0] / 2) + "," + node.y + ")"
        }, containerProps), React__default.createElement("rect", {
            className: "organiszation-node-box",
            width: width,
            height: height,
            x: 0,
            y: 0
        }), React__default.createElement("text", {
            textAnchor: "left",
            className: "organiszation-node-label",
            alignmentBaseline: "text-before-edge",
            x: 10,
            y: 5
        }, getLabel(node.data)));
    };

    Node.displayName = 'Node';
    Node.requiresSVG = true;
    Node.propTypes = {
        className: propTypes.string,
        node: propTypes.object,
        getLabel: propTypes.func,
        collapsable: propTypes.bool,
        toggleNode: propTypes.func
    };

    var Link = function Link(props) {
        var pathProps = cleanSVGProps(props);
        return React__default.createElement("path", _extends({}, pathProps, {
            className: "organization-link"
        }));
    };

    Link.displayName = 'Link';
    Link.requiresSVG = true;

    var Organization =
        /*#__PURE__*/
        function (_Component) {
            _inheritsLoose(Organization, _Component);

            function Organization(props) {
                var _this;

                _this = _Component.call(this, props) || this;
                _this.computeDataTree = _this.computeDataTree.bind(_assertThisInitialized(_this));
                _this.computeDataVerticalTree = _this.computeDataVerticalTree.bind(_assertThisInitialized(_this));
                _this.computeDataHorizontalTree = _this.computeDataHorizontalTree.bind(_assertThisInitialized(_this));
                _this.computeState = _this.computeState.bind(_assertThisInitialized(_this));
                _this.initTreemap = _this.initTreemap.bind(_assertThisInitialized(_this));
                _this.toggleNode = _this.toggleNode.bind(_assertThisInitialized(_this));
                _this.collapse = _this.collapse.bind(_assertThisInitialized(_this));
                _this.ref = React__default.createRef();

                _this.initTreemap();

                _this.computeDataTree();

                _this.state = _extends({}, _this.computeState());
                return _this;
            }

            var _proto = Organization.prototype;

            _proto.shouldComponentUpdate = function shouldComponentUpdate(nextProps, nextState) {
                return this.props.data !== nextProps.data || this.props.direction !== nextProps.direction || this.props.svgTransform !== nextProps.svgTransform || this.props.nodes !== nextState.nodes;
            };

            _proto.componentDidUpdate = function componentDidUpdate(prevProps) {
                var isUpdated = false;

                if (this.props.data !== prevProps.data || this.props.width !== prevProps.width || this.props.height !== prevProps.height || this.props.direction !== prevProps.direction) {
                    isUpdated = true;
                    this.initTreemap();
                }

                if (this.props.data !== prevProps.data || this.props.xSpacing !== prevProps.xSpacing || this.props.ySpacing !== prevProps.ySpacing || this.props.direction !== prevProps.direction) {
                    isUpdated = true;
                    this.computeDataTree();
                }

                if (isUpdated) {
                    this.setState(this.computeState());
                }

                return isUpdated;
            };

            _proto.componentDidMount = function componentDidMount() {
                var onUpdate = this.props.onUpdate;

                if (onUpdate) {
                    onUpdate(this.state.meta);
                }
            }
            /**
             * Initialize the D3 function with the size of chart: width/height
             */
            ;

            _proto.initTreemap = function initTreemap() {
                var _this$props = this.props,
                    margin = _this$props.margin,
                    width = _this$props.width,
                    height = _this$props.height,
                    getNodeComponentSize = _this$props.getNodeComponentSize,
                    getLabel = _this$props.getLabel,
                    xSpacing = _this$props.xSpacing;
                this.innerHeight = height - margin.top - margin.bottom;
                this.innerWidth = width - margin.left - margin.right; //this.treemap = initTreemap(this.innerHeight, this.innerWidth);

                if (Array.isArray(this.props.data)) {
                    this.dataTree = d3.stratify().id(function (d) {
                        return d.id;
                    }).parentId(function (d) {
                        return d.parent;
                    })(this.props.data);
                } else {
                    this.dataTree = d3.hierarchy(this.props.data);
                }

                var nodes = this.dataTree.descendants().sort(function (a, b) {
                    if (a.depth < b.depth) {
                        return 1;
                    } else if (a.depth > b.depth) {
                        return -1;
                    }

                    return 0;
                });
                var nodeSize = getNodeComponentSize.call(this, nodes);

                function calculateNodeSize(node) {
                    node.size = [].concat(nodeSize[getLabel(node.data)]);
                    node.data.__size = [].concat(node.size);

                    if (node.children) {
                        var childArea = {
                            width: 0,
                            height: 0
                        };
                        node.children.forEach(function (child, index) {
                            childArea.width = childArea.width + child.size[0] + (index > 0 ? xSpacing : 0);

                            if (node.data.__size[0] < childArea.width) {
                                node.size[0] = childArea.width;
                            }
                        });
                    }
                }

                nodes.forEach(calculateNodeSize);
            };

            _proto.computeDataTree = function computeDataTree() {
                var direction = this.props.direction;
                this[direction === 'vertical' ? 'computeDataVerticalTree' : 'computeDataHorizontalTree']();
            };

            _proto.computeDataHorizontalTree = function computeDataHorizontalTree() {
                var height = this.dataTree.leaves().length * 50;
                var width = this.dataTree.height * 200;
                console.log(height, width);
                this.dataTree = d3.cluster().size([height, width]).separation(function () {
                    return 1;
                })(this.dataTree); // reverse coord

                this.dataTree.descendants().forEach(function (node) {
                    var x = node.y;
                    var y = node.x;
                    node.x = x;
                    node.y = y;
                });
            }
            /**
             * Compute the D3 tree hierachy
             * - compute the size of node tree
             */
            ;

            _proto.computeDataVerticalTree = function computeDataVerticalTree() {
                var xSpacing = this.props.xSpacing;

                function calculateNodePosition(node, prevNode) {
                    node.x = node.size[0] / 2;

                    if (prevNode) {
                        node.x = node.x + xSpacing + prevNode.x + prevNode.size[0] / 2;
                    } else if (node.parent) {
                        node.x = node.x + node.parent.x - node.parent.size[0] / 2;
                    }

                    node.y = node.depth * 100;
                }

                function browseTree(node) {
                    if (node.children) {
                        node.children.forEach(function (child, index) {
                            if (index === 0) {
                                calculateNodePosition(child);
                            } else {
                                calculateNodePosition(child, node.children[index - 1]);
                            }

                            browseTree(child);
                        });
                    }
                }

                this.dataTree.x = this.dataTree.size[0] / 2;
                this.dataTree.y = 0;
                browseTree(this.dataTree);
            };

            _proto.computeState = function computeState() {
                var data = this.dataTree;
                var meta = {
                    width: 0,
                    height: 0
                }; // Compute the new tree layout.

                var nodes = data.descendants();
                var links = data.descendants().slice(1);
                nodes.forEach(function (node) {
                    var w = node.x + node.data.__size[0] / 2;
                    var h = node.y + node.data.__size[1];

                    if (meta.width < w) {
                        meta.width = w;
                    }

                    if (meta.height < h) {
                        meta.height = h;
                    }
                });
                var rootLabel = nodes[0].data;
                return {
                    nodes: nodes,
                    links: links,
                    rootLabel: rootLabel,
                    meta: meta
                };
            };

            _proto.collapse = function collapse(d) {
                if (d.children) {
                    d._children = d.children;
                    d.children = null;

                    d._children.forEach(this.collapse, this);
                }
            };

            _proto.toggleNode = function toggleNode(e, node) {
                if (!node || !node.children && !node._children) {
                    return;
                }

                if (node.children) {
                    this.collapse(node);
                } else {
                    node.children = node._children;
                    delete node._children;
                }

                this.computeDataTree();
                this.setState(this.computeState());
            };

            _proto.renderNodes = function renderNodes() {
                var _this2 = this;

                var _this$props2 = this.props,
                    NodeComponent = _this$props2.NodeComponent,
                    nodeProps = _this$props2.nodeProps,
                    getLabel = _this$props2.getLabel,
                    getId = _this$props2.getId,
                    ySpacing = _this$props2.ySpacing,
                    collapsable = _this$props2.collapsable,
                    svgClientRect = _this$props2.svgClientRect,
                    svgTransform = _this$props2.svgTransform,
                    direction = _this$props2.direction;
                var width = svgClientRect.width,
                    height = svgClientRect.height;
                var deltaX = width * 0.125;
                var deltaY = height * 0.125;
                var translate = svgTransform.translate,
                    scale = svgTransform.scale;
                return this.state.nodes.filter(function (node) {
                    return node.x * scale + translate.x >= -deltaX && node.x * scale + translate.x < width + deltaX && node.y * scale + translate.y >= -deltaY && node.y * scale + translate.y < height + deltaY;
                }).map(function (node) {
                    return React__default.createElement(NodeComponent, _extends({
                        className: "organization-node",
                        key: "node-" + getId(node.data),
                        node: node,
                        getLabel: getLabel,
                        ySpacing: ySpacing,
                        collapsable: collapsable,
                        toggleNode: _this2.toggleNode,
                        direction: direction
                    }, nodeProps));
                });
            };

            _proto.renderLinks = function renderLinks() {
                var _this$props3 = this.props,
                    getId = _this$props3.getId,
                    svgTransform = _this$props3.svgTransform,
                    svgClientRect = _this$props3.svgClientRect,
                    direction = _this$props3.direction;
                var translate = svgTransform.translate,
                    scale = svgTransform.scale;
                var width = svgClientRect.width,
                    height = svgClientRect.height;
                var deltaX = width * 0.125;
                var deltaY = height * 0.125;

                function getPosition(node) {
                    if (node.x * scale + translate.x < -deltaX) {
                        return 'left';
                    } else if (node.x * scale + translate.x > width + deltaX) {
                        return 'right';
                    } else if (node.y * scale + translate.y < -deltaY) {
                        return 'top';
                    } else if (node.y * scale + translate.y > height + deltaY) {
                        return 'bottom';
                    }

                    return 'window';
                }

                function getSourceCoords(node) {
                    return {
                        x: node.parent.x,
                        y: node.parent.y // + node.parent.data.__size[1]

                    };
                }

                function getTargetCoords(node) {
                    return {
                        x: node.x,
                        y: node.y
                    };
                }

                return this.state.links.filter(function (link) {
                    var source = getSourceCoords(link);
                    var target = getTargetCoords(link);
                    var sourcePos = getPosition(source);
                    var targetPos = getPosition(target);
                    return sourcePos !== targetPos || sourcePos === 'window';
                }).map(function (link) {
                    var source = getSourceCoords(link);
                    var target = getTargetCoords(link);
                    var delta = {
                        x: target.x - source.x,
                        y: target.y - source.y
                    };
                    var dir = {
                        x: delta.x < 0 ? -1 : 1,
                        y: delta.y < 0 ? -1 : 1
                    };
                    var r = 30;
                    var ry = Math.abs(delta.y) / 2 < r ? Math.abs(delta.y) / 2 : r;
                    var rx = Math.abs(delta.x) / 2 < r ? Math.abs(delta.x) / 2 : r;
                    var p1 = {
                        x: direction === 'vertical' ? source.x : source.x + rx * dir.x,
                        y: direction === 'vertical' ? source.y + ry * dir.y : source.y
                    };
                    var p2 = {
                        x: direction === 'vertical' ? p1.x + rx * dir.x : p1.x,
                        y: direction === 'vertical' ? p1.y : p1.y + ry * dir.y
                    };
                    var p3 = {
                        x: direction === 'vertical' ? target.x - rx * dir.x : p2.x,
                        y: direction === 'vertical' ? p2.y : target.y - ry * dir.y
                    };
                    var p4 = {
                        x: direction === 'vertical' ? p3.x + rx * dir.x : p3.x,
                        y: direction === 'vertical' ? p3.y : p3.y + ry * dir.y
                    };
                    var p5 = {
                        x: direction === 'vertical' ? p4.x : p4.x + rx * dir.x,
                        y: direction === 'vertical' ? p4.y + ry * dir.y : p4.y
                    };
                    return React__default.createElement(Link, {
                        key: "link-" + getId(link.data),
                        d: "M" + source.x + "," + source.y + " Q" + p1.x + "," + p1.y + " " + p2.x + "," + p2.y + " L" + p3.x + "," + p3.y + " Q" + p4.x + "," + p4.y + " " + p5.x + "," + p5.y + " L" + target.x + "," + target.y
                    });
                });
            };

            _proto.render = function render() {
                var _this$state = this.state,
                    links = _this$state.links,
                    meta = _this$state.meta;
                return React__default.createElement("g", {
                    className: "organization-chart"
                }, links.length > 0 && meta && this.renderLinks(), this.renderNodes());
            };

            return Organization;
        }(React.Component);

    Organization.displayName = 'Organization';
    Organization.requiresSVG = true;
    Organization.propTypes = {
        id: propTypes.string,

        /** This should be the description */
        data: propTypes.oneOfType([propTypes.object, propTypes.arrayOf(propTypes.object)]),
        margin: propTypes.object,
        width: propTypes.number,
        height: propTypes.number,
        collapsable: propTypes.bool,
        className: propTypes.string,

        /**
         * Vertical spacing between node
         */
        ySpacing: propTypes.number,

        /**
         * Horizontal spacing between node
         */
        xSpacing: propTypes.number,
        initialScale: propTypes.number,
        style: propTypes.object,
        direction: propTypes.oneOf(['horizontal', 'vertical']),

        /**
         * Function that return the id of a data element
         */
        getId: propTypes.func,

        /**
         * Function that return the Parent of a data element
         */
        getParent: propTypes.func,
        getLabel: propTypes.func,
        NodeComponent: propTypes.elementType,
        nodeProps: propTypes.object,
        getNodeComponentSize: propTypes.func
    };
    Organization.defaultProps = {
        xSpacing: 10,
        ySpacing: 10,
        initialScale: 1,
        direction: 'vertical',
        getId: function getId(item) {
            return item.id;
        },
        getParent: function getParent(item) {
            return item.parent;
        },
        getLabel: function getLabel(item) {
            return item.name;
        },
        NodeComponent: Node,
        getNodeComponentSize: getNodeComponentSize
    };

    var Svg =
        /*#__PURE__*/
        function (_Component) {
            _inheritsLoose(Svg, _Component);

            function Svg(props) {
                var _this;

                _this = _Component.call(this, props) || this;
                _this.handleResize = _this.handleResize.bind(_assertThisInitialized(_this));
                _this.cloneElement = _this.cloneElement.bind(_assertThisInitialized(_this));
                _this.id = _this.props.id || uid("svg").id;
                _this.svgRef = null;
                _this.applyScaleTranslateEvents = _this.applyScaleTranslateEvents.bind(_assertThisInitialized(_this));
                _this.handleZoom = _this.handleZoom.bind(_assertThisInitialized(_this));
                _this.handleDragStart = _this.handleDragStart.bind(_assertThisInitialized(_this));
                _this.handleDragMove = _this.handleDragMove.bind(_assertThisInitialized(_this));
                _this.handleDragEnd = _this.handleDragEnd.bind(_assertThisInitialized(_this));
                _this.setSvgRef = _this.setSvgRef.bind(_assertThisInitialized(_this));
                var initialScale = _this.props.initialScale;
                _this.state = {
                    svgRect: null,
                    scale: initialScale,
                    translate: {
                        x: 0,
                        y: 0
                    }
                };
                return _this;
            }

            var _proto = Svg.prototype;

            _proto.componentDidMount = function componentDidMount() {
                // Listening a resize window or parent container size
                window.addEventListener('resize', this.handleResize);
                window.addEventListener('iichart:resize', this.handleResize);
                this.setState({
                    svgRect: this.svgRef.getBoundingClientRect()
                }); // Enable or remove scale/translate event

                this.applyScaleTranslateEvents();
            };

            _proto.componentWillUnmount = function componentWillUnmount() {
                window.removeEventListener('resize', this.handleResize);
                window.removeEventListener('iichart:resize', this.handleResize);
                this.svgRef.removeEventListener('mousedown', this.handleDragStart);
            };

            _proto.componentDidUpdate = function componentDidUpdate(prevProps) {
                if (this.props.enableScale !== prevProps.enableScale || this.props.enableTranslate !== prevProps.enableTranslate) {
                    this.applyScaleTranslateEvents();
                }
            };

            _proto.setSvgRef = function setSvgRef(element) {
                this.svgRef = element;
            };

            _proto.applyScaleTranslateEvents = function applyScaleTranslateEvents() {
                if (this.svgRef) {
                    var _this$props = this.props,
                        enableScale = _this$props.enableScale,
                        enableTranslate = _this$props.enableTranslate;
                    this.svgRef.onwheel = enableScale ? this.handleZoom : null;
                    this.svgRef[enableTranslate ? 'addEventListener' : 'removeEventListener']("mousedown", this.handleDragStart);
                }
            };

            _proto.handleResize = function handleResize() {
                this.setState({
                    svgRect: this.svgRef.getBoundingClientRect()
                });
            };

            _proto.handleZoom = function handleZoom(e) {
                e.preventDefault();
                e.stopPropagation();
                var _this$props2 = this.props,
                    width = _this$props2.width,
                    height = _this$props2.height;
                var _this$state = this.state,
                    scale = _this$state.scale,
                    translate = _this$state.translate;
                var onScale = this.props.onScale;
                var newScale = Math.min(Math.max(.35, scale + e.deltaY * -0.005), 2);
                var ratio = scale / newScale;
                var deltaX = (width / ratio - width) / 2;
                var deltaY = (height / ratio - height) / 2;
                this.setState({
                    translate: {
                        x: Number.parseFloat((translate.x / ratio - deltaX).toFixed(4)),
                        y: Number.parseFloat((translate.y / ratio - deltaY).toFixed(4))
                    },
                    scale: Number.parseFloat(newScale.toFixed(4))
                });

                if (onScale) {
                    onScale(newScale);
                }
            };

            _proto.handleDragStart = function handleDragStart(e) {
                this.dragStartMousePosition = {
                    x: e.screenX,
                    y: e.screenY
                };
                this.dragStartStateTransition = this.state.translate; // add mouse move listener

                document.addEventListener('mousemove', this.handleDragMove); // add mouse up listener

                document.addEventListener('mouseup', this.handleDragEnd);
            };

            _proto.handleDragMove = function handleDragMove(e) {
                var onTranslate = this.props.onTranslate;
                var translate = {
                    x: this.dragStartStateTransition.x - (this.dragStartMousePosition.x - e.screenX),
                    y: this.dragStartStateTransition.y - (this.dragStartMousePosition.y - e.screenY)
                };
                this.setState({
                    translate: translate
                });

                if (onTranslate) {
                    onTranslate(translate);
                }
            };

            _proto.handleDragEnd = function handleDragEnd() {
                //remove mouse move listener
                document.removeEventListener('mousemove', this.handleDragMove); //remove mouse up listener

                document.removeEventListener('mouseup', this.handleDragEnd);
            };

            _proto.cloneElement = function cloneElement(children, props) {
                var _this2 = this;

                var _this$state2 = this.state,
                    svgRect = _this$state2.svgRect,
                    translate = _this$state2.translate,
                    scale = _this$state2.scale;
                return React__default.Children.toArray(children).map(function (child, index) {
                    return React__default.cloneElement(child, _extends({}, props, {
                        svgTransform: {
                            scale: scale,
                            translate: translate
                        },
                        key: index,
                        svgId: _this2.id,
                        svgClientRect: svgRect
                    }));
                });
            };

            _proto.requiresSVG = function requiresSVG(child) {
                var svgTags = ['g', 'text', 'svg', 'path', 'foreignObject'];

                if (svgTags.indexOf(child.type) !== -1) {
                    return true;
                }

                return child.type && child.type.requiresSVG === true;
            };

            _proto.render = function render() {
                var _this3 = this;

                var _this$props3 = this.props,
                    width = _this$props3.width,
                    height = _this$props3.height,
                    className = _this$props3.className,
                    style = _this$props3.style,
                    rest = _objectWithoutPropertiesLoose(_this$props3, ["width", "height", "className", "style"]);

                var _this$state3 = this.state,
                    svgRect = _this$state3.svgRect,
                    scale = _this$state3.scale,
                    translate = _this$state3.translate;
                delete rest.children;
                var c = svgRect ? this.cloneElement(this.props.children, rest) : [];
                return React__default.createElement("div", null, React__default.createElement("svg", {
                    id: this.id,
                    ref: this.setSvgRef,
                    width: width,
                    height: height,
                    className: "industry-chart " + className,
                    style: style
                }, React__default.createElement("g", {
                    transform: "translate(" + translate.x + ", " + translate.y + ") scale(" + scale + ")"
                }, svgRect && React__default.Children.toArray(c).filter(this.requiresSVG))), React__default.Children.toArray(c).filter(function (child) {
                    return !_this3.requiresSVG(child);
                }));
            };

            return Svg;
        }(React.Component);

    Svg.displayName = 'Svg';
    Svg.propTypes = {
        id: propTypes.string,
        margin: propTypes.object,
        width: propTypes.oneOfType([propTypes.number, propTypes.string]),
        height: propTypes.oneOfType([propTypes.number, propTypes.string]),
        className: propTypes.string,
        style: propTypes.object,
        enableScale: propTypes.bool,
        enableTranslate: propTypes.bool,
        initialScale: propTypes.number,
        children: propTypes.oneOfType([propTypes.arrayOf(propTypes.node), propTypes.node]),
        onScale: propTypes.func,
        onTranslate: propTypes.func
    };
    Svg.defaultProps = {
        width: 300,
        height: 300,
        initialScale: 1
    };

    var Treemap =
        /*#__PURE__*/
        function (_Component) {
            _inheritsLoose(Treemap, _Component);

            function Treemap(props) {
                var _this;

                _this = _Component.call(this, props) || this;
                _this.initTreemap = _this.initTreemap.bind(_assertThisInitialized(_this));
                _this.computeTreemap = _this.computeTreemap.bind(_assertThisInitialized(_this));
                _this.renderLeaves = _this.renderLeaves.bind(_assertThisInitialized(_this));
                _this.handleClick = _this.handleClick.bind(_assertThisInitialized(_this));
                _this.handleMouseMove = _this.handleMouseMove.bind(_assertThisInitialized(_this));
                _this.handleMouseLeave = _this.handleMouseLeave.bind(_assertThisInitialized(_this));
                _this.computeColor = _this.computeColor.bind(_assertThisInitialized(_this));

                _this.initTreemap();

                var leaves = _this.computeTreemap();

                var leavesById = _this.leavesById(leaves);

                _this.state = {
                    leaves: leaves,
                    leavesById: leavesById
                };
                return _this;
            }

            var _proto = Treemap.prototype;

            _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {
                var _this$props = this.props,
                    data = _this$props.data,
                    svgClientRect = _this$props.svgClientRect,
                    paddingInner = _this$props.paddingInner,
                    paddingOuter = _this$props.paddingOuter,
                    colorScaleLinear = _this$props.colorScaleLinear,
                    highlight = _this$props.highlight;
                var isUpdated = false;

                if (prevProps.colorScaleLinear !== colorScaleLinear) {
                    isUpdated = true;
                }

                if (prevProps.data !== data || prevProps.svgClientRect.width !== svgClientRect.width || prevProps.svgClientRect.height !== svgClientRect.height || prevProps.paddingInner !== paddingInner || prevProps.paddingOuter !== paddingOuter || prevProps.highlight !== highlight) {
                    this.initTreemap();
                    isUpdated = true;
                }

                if (isUpdated) {
                    var leaves = this.computeTreemap();
                    this.setState({
                        leaves: leaves,
                        leavesById: this.leavesById(leaves)
                    });
                }
            };

            _proto.findCell = function findCell(target) {
                var isCell = function isCell(elt) {
                    return elt && elt.getAttribute("data-type") === 'cell';
                };

                while (target.tagName !== 'svg' && target.parentNode.tagName !== 'svg' && !isCell(target)) {
                    target = target.parentNode;
                }

                return isCell(target) ? target : null;
            }
            /**
             * Return informations of the cell: position and size
             */
            ;

            _proto.computeCellOffset = function computeCellOffset(cell) {
                var offset = cell.getAttribute('transform').match(/\((.*)[,|\s](.*)\)/);
                var rect = cell.getElementsByTagName('rect')[0];
                return {
                    x: parseFloat(offset[1] || 0),
                    y: parseFloat(offset[2] || 0),
                    width: parseFloat(rect.getAttribute('width')),
                    height: parseFloat(rect.getAttribute('height'))
                };
            }
            /**
             * Throw the data of the cell to the callback when a click is occur
             */
            ;

            _proto.handleClick = function handleClick(e) {
                e.preventDefault();
                var cell = this.findCell(e.target);
                var data;

                if (cell) {
                    e.data = {
                        target: cell,
                        meta: this.computeCellOffset(cell)
                    };
                    var cellId = cell.getAttribute('data-id');
                    data = this.state.leavesById[cellId];
                } else {
                    delete e.data;
                }

                this.props.onClick(e, data);
            }
            /**
             * Throw the data of the cell to the callback when the mouse is over an element
             */
            ;

            _proto.handleMouseMove = function handleMouseMove(e) {
                e.preventDefault();
                var cell = this.findCell(e.target);
                var data;

                if (cell) {
                    e.data = {
                        target: cell,
                        meta: this.computeCellOffset(cell)
                    };
                    var cellId = cell.getAttribute('data-id');
                    data = this.state.leavesById[cellId];
                } else {
                    delete e.data;
                }

                this.props.onMouseMove(e, data);
            };

            _proto.handleMouseLeave = function handleMouseLeave(e) {
                e.data = null;
                this.props.onMouseLeave(e);
            }
            /**
             * Init the D3 function
             */
            ;

            _proto.initTreemap = function initTreemap() {
                var _this$props2 = this.props,
                    paddingInner = _this$props2.paddingInner,
                    paddingOuter = _this$props2.paddingOuter,
                    highlight = _this$props2.highlight,
                    data = _this$props2.data,
                    _this$props2$svgClien = _this$props2.svgClientRect,
                    width = _this$props2$svgClien.width,
                    height = _this$props2$svgClien.height;

                if (!data || data && data.length === 0) {
                    return;
                }

                this.hierarchyTreemap = d3.stratify().parentId(function (d) {
                    return d.parent;
                })(data);
                var nbChildren = this.hierarchyTreemap.children.filter(function (child) {
                    return child.height > 0;
                });
                this.isHighlight = highlight && nbChildren.length > 1;
                var treemapWidth = width;
                var highlightTreemapWidth = 0;

                if (this.isHighlight) {
                    var ratio = highlight.size || 50;
                    highlightTreemapWidth = width * ratio / 100;
                    treemapWidth -= highlightTreemapWidth;
                    this.highlightTreemap = d3.treemap().paddingInner(paddingInner).paddingOuter(paddingOuter).size([highlightTreemapWidth, height]);
                } else {
                    this.highlightTreemap = null;
                }

                this.treemap = d3.treemap().paddingInner(paddingInner).paddingOuter(paddingOuter).size([treemapWidth, height]);
            };

            _proto.leavesById = function leavesById(leaves) {
                var leavesById = {};
                leaves && leaves.forEach(function (leaf) {
                    leavesById[leaf.leafUid.id] = leaf.data;
                });
                return leavesById;
            };

            _proto.computeColor = function computeColor(d) {
                var customColors = this.props.customColors;
                var custom;

                while (d.depth > 1) {
                    d = d.parent;
                }

                customColors && customColors.forEach(function (c) {
                    if (c.nodes.includes(d.data[c.property])) {
                        custom = c.color;
                    }
                });

                if (custom) {
                    return custom;
                }

                return this.scaleOrdinal(d.data.name);
            }
            /**
             * Compute the treemap
             * -> if a node
             */
            ;

            _proto.computeTreemap = function computeTreemap() {
                var _this2 = this;

                var data = this.props.data;

                if (!data || data && data.length === 0) {
                    return;
                }

                var _this$props3 = this.props,
                    colorScaleLinear = _this$props3.colorScaleLinear,
                    highlight = _this$props3.highlight,
                    width = _this$props3.svgClientRect.width;
                var hierarchyTreemap = this.hierarchyTreemap;
                this.scaleOrdinal = d3.scaleOrdinal(d3.schemeCategory10);
                var offset = 0;
                var computeScaleColor;
                var leavesHighlight = [];

                if (this.isHighlight) {
                    var ratio = highlight.size || 50;
                    offset = width * ratio / 100;
                    var highlightNodes = [hierarchyTreemap.data];
                    var hierarchyHighlight = d3.stratify().parentId(function (d) {
                        return d.parent;
                    })(data);
                    hierarchyHighlight.links().forEach(function (link) {
                        var node = link.target;
                        var highlightNode = highlight.nodes.map(function (value) {
                            return value.toString();
                        }).includes(node[highlight.property]);
                        var parents = node.ancestors();
                        !highlightNode && parents.map(function (d) {
                            if (!d.parent) {
                                return null;
                            }

                            return d.parent[highlight.property];
                        }).forEach(function (id) {
                            if (id && highlight.nodes.map(function (value) {
                                return value.toString();
                            }).includes(id)) {
                                highlightNode = true;
                            }
                        });

                        if (highlightNode) {
                            highlightNodes.push(node.data);
                        }
                    });
                    leavesHighlight = this.highlightTreemap(d3.stratify().parentId(function (d) {
                        return d.parent;
                    })(highlightNodes).sum(function (d) {
                        return d.value;
                    }).sort(function (a, b) {
                        return b.value - a.value;
                    })).leaves();
                    filter(hierarchyTreemap, function (node) {
                        return node.data[highlight.property] && highlight.nodes.includes(node.data[highlight.property]);
                    });
                }

                var leaves = this.treemap(hierarchyTreemap.sum(function (d) {
                    return d.value;
                }).sort(function (a, b) {
                    return b.value - a.value;
                })).leaves();

                if (colorScaleLinear) {
                    //compute min and max for each parent
                    var dataByParent = {};
                    leavesHighlight.forEach(function (leaf) {
                        var parent = leaf;

                        while (parent.depth > 1) {
                            parent = parent.parent;
                        }

                        if (!dataByParent[parent.data.name]) {
                            dataByParent[parent.data.name] = {
                                data: [],
                                node: parent
                            };
                        }

                        dataByParent[parent.data.name].data.push(leaf.data.value);
                    });
                    leaves.forEach(function (leaf) {
                        var parent = leaf;

                        while (parent.depth > 1) {
                            parent = parent.parent;
                        }

                        if (!dataByParent[parent.data.name]) {
                            dataByParent[parent.data.name] = {
                                data: [],
                                node: parent
                            };
                        }

                        dataByParent[parent.data.name].data.push(leaf.data.value);
                    }); //compute max and color linear scale

                    Object.keys(dataByParent).forEach(function (parent) {
                        dataByParent[parent].max = max(dataByParent[parent].data, function (value) {
                            return parseInt(value);
                        });

                        var parentColor = _this2.computeColor(dataByParent[parent].node);

                        dataByParent[parent].scale = d3.scaleLinear().domain([0, dataByParent[parent].max]).range([lighten(parentColor, 0.3), parentColor]);
                    });

                    computeScaleColor = function computeScaleColor(d) {
                        var parent = d;

                        while (parent.depth > 1) {
                            parent = parent.parent;
                        }

                        return dataByParent[parent.data.name].scale(d.value);
                    };
                }

                var colorFunction = colorScaleLinear ? computeScaleColor : this.computeColor;
                var computedLeaves = this.computeLeaves(leaves, offset, colorFunction);

                if (leavesHighlight.length > 0) {
                    computedLeaves = computedLeaves.concat(this.computeLeaves(leavesHighlight, 0, colorFunction));
                }

                return computedLeaves;
            };

            _proto.computeLeaves = function computeLeaves(leaves, offset, color) {
                return leaves.map(function (leaf) {
                    var x0 = leaf.x0,
                        y0 = leaf.y0,
                        x1 = leaf.x1,
                        y1 = leaf.y1;
                    return {
                        x: x0 + offset,
                        y: y0,
                        width: x1 - x0,
                        height: y1 - y0,
                        color: color(leaf),
                        data: leaf,
                        leafUid: uid("leaf"),
                        clipUid: uid("clip")
                    };
                });
            };

            _proto.renderLeaves = function renderLeaves() {
                var leaves = this.state.leaves;
                var _this$props4 = this.props,
                    hideLabel = _this$props4.hideLabel,
                    displayLabels = _this$props4.displayLabels;

                if (!leaves) {
                    return React__default.createElement("text", {
                        alignmentBaseline: "hanging"
                    }, "No data");
                }

                return React__default.createElement(React__default.Fragment, null, leaves.map(function (leaf) {
                    var x = leaf.x,
                        y = leaf.y,
                        width = leaf.width,
                        height = leaf.height,
                        color = leaf.color,
                        data = leaf.data,
                        leafUid = leaf.leafUid,
                        clipUid = leaf.clipUid;
                    var label = displayLabels.find(function (label) {
                        return label.name === data.data.name;
                    });
                    return React__default.createElement("g", {
                        key: leafUid.id,
                        transform: "translate(" + x + "," + y + ")",
                        "data-type": "cell",
                        "data-id": leafUid.id,
                        clipPath: clipUid
                    }, React__default.createElement("rect", {
                        className: "treemap-cell",
                        id: leafUid.id,
                        width: width,
                        height: height,
                        fill: color
                    }), React__default.createElement("clipPath", {
                        id: clipUid.id
                    }, React__default.createElement("use", {
                        xlinkHref: leafUid.href
                    })), (label || hideLabel !== true) && React__default.createElement("g", {
                        transform: "translate(0," + (height - 20) + ")"
                    }, React__default.createElement("text", {
                        className: "treemap-label always-displayed",
                        alignmentBaseline: "hanging",
                        x: "5",
                        y: "0"
                    }, label ? label.alias : data.data.name)));
                }));
            };

            _proto.render = function render() {
                var _this$props5 = this.props,
                    margin = _this$props5.margin,
                    className = _this$props5.className,
                    data = _this$props5.data,
                    onClick = _this$props5.onClick,
                    onMouseMove = _this$props5.onMouseMove,
                    onMouseLeave = _this$props5.onMouseLeave,
                    rest = _objectWithoutPropertiesLoose(_this$props5, ["margin", "className", "data", "onClick", "onMouseMove", "onMouseLeave"]);

                var events = {
                    onClick: onClick ? this.handleClick : null,
                    onMouseMove: onMouseMove ? this.handleMouseMove : null,
                    onMouseLeave: onMouseLeave ? this.handleMouseLeave : null
                };
                delete rest.paddingInner;
                delete rest.paddingOuter;
                delete rest.hideLabel;
                delete rest.displayLabels;
                delete rest.colorScaleLinear;
                delete rest.customColors;
                delete rest.svgClientRect;
                delete rest.initialScale;
                return !data ? React__default.createElement("text", {
                    alignmentBaseline: "hanging"
                }, "No data") : React__default.createElement("g", _extends({}, events, rest), data && this.renderLeaves());
            };

            return Treemap;
        }(React.Component);

    Treemap.displayName = 'Treemap';
    Treemap.requiresSVG = true;
    Treemap.propTypes = {
        margin: propTypes.object,
        onClick: propTypes.func,
        onMouseMove: propTypes.func,
        onMouseOver: propTypes.func,
        onMouseLeave: propTypes.func,

        /**
         * Treemap options
         */
        paddingInner: propTypes.number,
        paddingOuter: propTypes.number,
        hideLabel: propTypes.bool,
        displayLabels: propTypes.arrayOf(propTypes.exact({
            name: propTypes.string,
            alias: propTypes.string
        })),
        colorScaleLinear: propTypes.bool,
        highlight: propTypes.object,
        customColors: propTypes.arrayOf(propTypes.object)
    };
    Treemap.defaultProps = {
        paddingInner: 1,
        paddingOuter: 1
    };

    function styleInject(css, ref) {
        if (ref === void 0) ref = {};
        var insertAt = ref.insertAt;

        if (!css || typeof document === 'undefined') {
            return;
        }

        var head = document.head || document.getElementsByTagName('head')[0];
        var style = document.createElement('style');
        style.type = 'text/css';

        if (insertAt === 'top') {
            if (head.firstChild) {
                head.insertBefore(style, head.firstChild);
            } else {
                head.appendChild(style);
            }
        } else {
            head.appendChild(style);
        }

        if (style.styleSheet) {
            style.styleSheet.cssText = css;
        } else {
            style.appendChild(document.createTextNode(css));
        }
    }

    var css = ".industry-chart {\r\n    background-color: #fff;\r\n    font-family: sans-serif;\r\n    overflow: hidden;\r\n    display: block;\r\n}\r\n\r\n.industry-chart .cursor {\r\n    cursor: pointer\r\n}\r\n\r\n.industry-chart .axis-label {\r\n    transition: transform ease 200ms;\r\n}\r\n\r\n.industry-chart .chart-background {\r\n    fill:#fff;\r\n}\r\n\r\n.industry-chart .chart-axis line {\r\n    stroke: #000;\r\n    stroke-width: 1px;\r\n}\r\n\r\n.industry-chart .chart-label {\r\n    fill:#777;\r\n    font-size: 10px;\r\n}\r\n\r\n.industry-chart.light .chart-label {\r\n    fill:#ddd;\r\n}\r\n\r\n.industry-chart.light .chart-axis line {\r\n    stroke: #ddd;\r\n}\r\n\r\n/* Tooltip */\r\n.chart-tooltip {\r\n    font-family: Arial, Helvetica, sans-serif;\r\n    position: absolute;\r\n    color: #fff;\r\n    font-size: 14px;\r\n    pointer-events: none;\r\n    box-sizing: border-box;\r\n    z-index: 100;\r\n    border-left: 2px solid transparent;\r\n    border-right: 2px solid transparent;\r\n    border-bottom: 2px solid transparent;\r\n}\r\n\r\n.chart-tooltip.treemap {\r\n    background-color: transparent;\r\n    position: absolute;\r\n    overflow: hidden;\r\n}\r\n\r\n.chart-tooltip, .chart-tooltip.treemap div {\r\n    background-color: #80aac2;\r\n    padding: 1.2em 0.5em;\r\n    color: #454a4c;\r\n    font-weight: bold;\r\n}\r\n\r\n.chart-tooltip.treemap div {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n}\r\n\r\n/* Heatmap */\r\n.industry-chart .chart-heatmap-row {\r\n    stroke: #fff;\r\n}\r\n/* --> with transition */\r\n.industry-chart.transition .chart-heatmap-row,\r\n.industry-chart.transition .chart-heatmap-cell  {\r\n    transition: all 1000ms linear;\r\n}\r\n\r\n/* Dendrogram */\r\n.industry-chart .chart-link {\r\n    fill: none;\r\n    stroke: #cdcdcd;\r\n}\r\n\r\n.industry-chart text.dendrogram-label {\r\n    font-size: 11px;\r\n    fill: #777;\r\n}\r\n\r\n.industry-chart text.dendrogram-label.has-child {\r\n    font-weight: 700;\r\n}\r\n\r\n.industry-chart circle.dendrogram-node {\r\n    fill: #fff;\r\n    stroke: #368ec4;\r\n    stroke-width: 2px;\r\n    shape-rendering: auto;\r\n}\r\n\r\n.industry-chart circle.dendrogram-node.cursor {\r\n    cursor: pointer\r\n}\r\n\r\n.industry-chart circle.dendrogram-leaf {\r\n    fill: #cdcdcd;\r\n    stroke-width: 0;\r\n}\r\n\r\n/* Treemap */\r\n.treemap-label {\r\n    font-size: 0.8em;\r\n    fill: #000;\r\n}\r\n\r\n.treemap-cell:hover {\r\n    stroke:black;\r\n    stroke-width: 3px;\r\n}\r\n\r\n.treemap-label.always-displayed{\r\n    fill: white;\r\n    font-size: 15px;\r\n}\r\n\r\n/* Organization */\r\n.organiszation-node-box {\r\n    stroke: #000;\r\n    stroke-width: 1px;\r\n    fill: #ddd;\r\n}\r\n\r\n.organization-link {\r\n    fill: none;\r\n    stroke: #000;\r\n    stroke-width: 2px;\r\n}\r\n\r\nforeignObject {\r\n    user-select: none;\r\n}\r\n";
    styleInject(css);

    var XYPlot =
        /*#__PURE__*/
        function (_Component) {
            _inheritsLoose(XYPlot, _Component);

            function XYPlot() {
                return _Component.apply(this, arguments) || this;
            }

            var _proto = XYPlot.prototype;

            _proto.cloneElement = function cloneElement(children, props) {
                var _this = this;

                return React__default.Children.toArray(children).map(function (child) {
                    return React__default.cloneElement(child, props, _this.cloneElement(child.props.children, props));
                });
            };

            _proto.render = function render() {
                var margin = this.props.margin;
                var children = this.cloneElement(this.props.children, this.props);
                return React__default.createElement("g", {
                    transform: "translate(" + margin.left + ", " + margin.top + ")"
                }, children.filter(function (child) {
                    return child && child.type.requiresSVG;
                }));
            };

            return XYPlot;
        }(React.Component);

    XYPlot.displayName = 'XYPlot';
    XYPlot.requiresSVG = true;
    XYPlot.propTypes = {
        margin: propTypes.object,
        className: propTypes.string,
        xLabels: propTypes.array.isRequired,
        yLabels: propTypes.array.isRequired
    };

    var AxisLabels =
        /*#__PURE__*/
        function (_Component) {
            _inheritsLoose(AxisLabels, _Component);

            function AxisLabels(props) {
                var _this;

                _this = _Component.call(this, props) || this;
                var isHorizontal = _this.props.isHorizontal;
                _this.renderLabels = _this.renderLabels.bind(_assertThisInitialized(_this));
                _this.computeLabels = _this.computeLabels.bind(_assertThisInitialized(_this));
                _this.initBandwidth = _this.initBandwidth.bind(_assertThisInitialized(_this));
                _this.reorderLabels = _this.reorderLabels.bind(_assertThisInitialized(_this));
                _this.domain = isHorizontal ? 'x' : 'y';

                _this.initBandwidth();

                _this.state = {
                    labels: _this.computeLabels()
                };
                _this.ref = React__default.createRef();
                return _this;
            }

            var _proto = AxisLabels.prototype;

            _proto.componentDidUpdate = function componentDidUpdate(prevProps) {
                var _this$props = this.props,
                    width = _this$props.width,
                    height = _this$props.height;
                var sortKey = this.domain + "Sort";
                var labelKey = this.domain + "Labels";

                if (prevProps.width !== width || prevProps.height !== height) {
                    this.initBandwidth();
                    this.setState({
                        labels: this.computeLabels()
                    });
                }

                if (prevProps[labelKey] !== this.props[labelKey]) {
                    this.setState({
                        labels: this.computeLabels()
                    });
                }

                if (prevProps[sortKey] !== this.props[sortKey]) {
                    this.reorderLabels();
                }
            };

            _proto.initBandwidth = function initBandwidth() {
                var _this$props2 = this.props,
                    isHorizontal = _this$props2.isHorizontal,
                    width = _this$props2.width,
                    height = _this$props2.height;
                var size = isHorizontal ? width : height;
                var labels = this.props[this.domain + "Labels"];
                this.scaleband = d3.scaleBand().range([0, size]).domain(labels);
            };

            _proto.computeLabels = function computeLabels() {
                var labels = this.props[this.domain + "Labels"];
                var isHorizontal = this.props.isHorizontal;
                var bandWidth = this.scaleband.domain(labels).bandwidth();
                return labels.map(function (label, index) {
                    var bandPosition = index * bandWidth;
                    return {
                        id: index,
                        label: label,
                        x: (isHorizontal && bandPosition) + 0,
                        y: (!isHorizontal && bandPosition) + 0
                    };
                });
            };

            _proto.reorderLabels = function reorderLabels() {
                var _this2 = this;

                var map = {};
                this.state.labels.forEach(function (label) {
                    map[label.label] = label;
                });
                var labels = this.props[this.domain + "Sort"];
                this.setState({
                    labels: labels.map(function (key) {
                        return map[key];
                    })
                }, function () {
                    var labels = _this2.state.labels;

                    var bandWidth = _this2.scaleband.bandwidth();

                    var isHorizontal = _this2.props.isHorizontal;

                    _this2.ref.current.getBoundingClientRect();

                    _this2.setState({
                        labels: labels.map(function (label, index) {
                            var bandPosition = index * bandWidth;
                            return _extends({}, label, {
                                x: isHorizontal && bandPosition,
                                y: !isHorizontal && bandPosition
                            });
                        })
                    });
                });
            };

            _proto.renderLabels = function renderLabels() {
                var bandWidth = this.scaleband.bandwidth();
                var _this$props3 = this.props,
                    padding = _this$props3.padding,
                    tickFormat = _this$props3.tickFormat;
                var rotation = parseInt(this.props.rotation || 0);
                var position = this.props.position || 'left';
                var labels = this.state.labels;
                var BASELINE_BASELINE = 'baseline';
                var BASELINE_MIDDLE = 'middle';
                var BASELINE_HANGING = 'hanging';
                var ANCHOR_START = 'start';
                var ANCHOR_MIDDLE = 'middle';
                var ANCHOR_END = 'end';
                var defaultProps = {
                    textAnchor: 'start'
                };
                var ajustX = position === 'top' || position === 'bottom' ? bandWidth / 2 : position === 'left' ? -padding : padding;
                var ajustY = position === 'left' || position === 'right' ? bandWidth / 2 : position === 'top' ? -padding : padding;

                if (position === 'bottom') {
                    defaultProps.alignmentBaseline = BASELINE_HANGING;

                    if (rotation === 90 || rotation === -90) {
                        defaultProps.alignmentBaseline = BASELINE_MIDDLE;
                    }

                    if (rotation === 0) {
                        defaultProps.textAnchor = ANCHOR_MIDDLE;
                    } else if (rotation < 0) {
                        defaultProps.textAnchor = ANCHOR_END;
                    }
                }

                if (position === 'left') {
                    if (rotation === 90 || rotation === -90) {
                        defaultProps.textAnchor = BASELINE_MIDDLE;

                        if (rotation === 90) {
                            defaultProps.alignmentBaseline = BASELINE_HANGING;
                        } else {
                            defaultProps.alignmentBaseline = BASELINE_BASELINE;
                        }
                    } else {
                        defaultProps.textAnchor = ANCHOR_END;
                    }
                }

                if (position === 'top' || position === 'right') {
                    defaultProps.textAnchor = ANCHOR_START;
                }

                return labels.map(function (column, index) {
                    var x = column.x,
                        y = column.y;
                    var labelFormat = tickFormat && tickFormat(column);
                    var props = Object.assign({
                        transform: "" + (rotation ? " rotate(" + rotation + ")" : '')
                    }, defaultProps);
                    props.style = _extends({}, props.style);
                    return React__default.createElement("g", {
                        className: "axis-label",
                        key: column.label,
                        transform: "translate(" + (x + ajustX) + ", " + (y + ajustY) + ")",
                        style: {
                            transitionDelay: index + "0ms"
                        }
                    }, tickFormat && typeof labelFormat !== 'string' ? React__default.createElement("g", props, labelFormat) : React__default.createElement(Label$1, _extends({
                        key: column.label
                    }, props), typeof labelFormat === 'string' ? labelFormat : column.label));
                });
            };

            _proto.render = function render() {
                var style = this.props.style;
                return React__default.createElement("g", _extends({}, style, {
                    ref: this.ref
                }), this.renderLabels());
            };

            return AxisLabels;
        }(React.Component);

    AxisLabels.defaultProps = {
        padding: 10
    };
    AxisLabels.requiresSVG = true;

    var Axis =
        /*#__PURE__*/
        function (_Component) {
            _inheritsLoose(Axis, _Component);

            function Axis(props) {
                var _this;

                _this = _Component.call(this, props) || this;
                _this.renderLine = _this.renderLine.bind(_assertThisInitialized(_this));
                return _this;
            }

            var _proto = Axis.prototype;

            _proto.isHorizontal = function isHorizontal(position) {
                return position === 'top' || position === 'bottom';
            }
            /**
             * TODO add option to display the line
             */
            ;

            _proto.renderLine = function renderLine() {
                var _this$props = this.props,
                    height = _this$props.height,
                    width = _this$props.width,
                    position = _this$props.position,
                    margin = _this$props.margin;
                var isHorizontal = this.isHorizontal(position);
                var size = isHorizontal ? width - margin.left - margin.right : height - margin.top - margin.bottom;
                return React__default.createElement("line", {
                    x1: 0,
                    y1: 0,
                    x2: (isHorizontal && size) + 0,
                    y2: (!isHorizontal && size) + 0
                });
            };

            _proto.render = function render() {
                var _this$props2 = this.props,
                    svgClientRect = _this$props2.svgClientRect,
                    position = _this$props2.position,
                    style = _this$props2.style,
                    margin = _this$props2.margin,
                    render = _this$props2.render;
                var isHorizontal = position === 'top' || position === 'bottom';
                var x = position === 'right' ? svgClientRect.width - margin.left - margin.right : 0;
                var y = position === 'bottom' ? svgClientRect.height - margin.top - margin.bottom : 0;
                return React__default.createElement("g", _extends({
                    className: "chart-axis " + (position || 'left'),
                    fill: "black",
                    transform: "translate(" + x + ", " + y + ")"
                }, style), render ? render() : React__default.createElement(AxisLabels, _extends({}, this.props, {
                    width: svgClientRect.width - margin.left - margin.right,
                    height: svgClientRect.height - margin.top - margin.bottom,
                    isHorizontal: isHorizontal
                })));
            };

            return Axis;
        }(React.Component);

    Axis.displayName = 'Axis';
    Axis.requiresSVG = true;
    Axis.propTypes = {
        width: propTypes.oneOfType([propTypes.number, propTypes.string]),
        height: propTypes.oneOfType([propTypes.number, propTypes.string]),

        /** Data of the heatmap */
        position: propTypes.string
    };
    Axis.defaultProps = {
        position: 'left'
    };

    var Utils = {
        DOM: {
            uid: uid
        },
        min: min,
        max: max,
        getQuantileColor: getQuantileColor,
        lighten: lighten,
        hierarchy: hierarchyUtils,
        Tooltip: Tooltip$1,
        Dendrogram: {
            computeWidth: computeWidth
        },
        Label: Label
    };

    exports.Axis = Axis;
    exports.Dendrogram = Dendrogram;
    exports.Heatmap = Heatmap;
    exports.Label = Label$1;
    exports.Organization = Organization;
    exports.Svg = Svg;
    exports.Tooltip = Tooltip;
    exports.Treemap = Treemap;
    exports.Utils = Utils;
    exports.XYPlot = XYPlot;

    Object.defineProperty(exports, '__esModule', { value: true });

}));
