@import "../../plmaResources/css/polyfills.less";

.main-container{
	overflow: hidden;
    .display-flex();
    .flex-direction(column);
}

.stickyContainer {
	.display-flex();
	flex-direction: column;
	.flex(0 0 auto);
	z-index: 1;
}

.flexbox-container{
	
	&.full-width{
		width: 100%;
	}
	
	.row{
		.display-flex();
		.flex-flow(row wrap);
		.justify-content(flex-start);
		
	}
	
	.item {
		.flex(1 1 auto);
	}
	
}

.mashup .flexRowContainer{
		
	&.flexbox-container.searchWidget{
		margin: 0 auto;
	}
	
}