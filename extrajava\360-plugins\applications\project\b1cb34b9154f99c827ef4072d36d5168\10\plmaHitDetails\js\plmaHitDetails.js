var HitDetail = function (uCssId, options) {
    this.uCssId = uCssId;
    this.widget = $('.' + uCssId);
    this.options = options;

    /* reload widgets */
    $(window).off('plma:panel-detail-reload');
    $(window).on('plma:panel-detail-reload', $.proxy(function (event, parameterName, parameter) {
        if (parameterName && parameter) {
            var client = new PlmaAjaxClient(this.widget);

            if (this.options.feeds) {
                client.addParameter("use_page_feeds", this.options.feeds.join(","));
            }

            client.addParameter(parameterName, parameter);
            client.addWidget(this.uCssId);
            client.update();
        }
    }, this));

    if (this.options.scrollTop == 'true' && this.widget.parent().scrollTop() > 0) {
        this.widget.parent().scrollTop(0);
    }
};