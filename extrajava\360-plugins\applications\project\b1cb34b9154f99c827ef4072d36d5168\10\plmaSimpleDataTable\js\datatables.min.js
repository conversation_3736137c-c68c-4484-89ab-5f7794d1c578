/*
 * This combined file was created by the DataTables downloader builder:
 *   https://datatables.net/download
 *
 * To rebuild or modify this file with the latest versions of the included
 * software please visit:
 *   https://datatables.net/download/#dt/dt-2.3.0/b-3.2.3/cr-2.1.0/fc-5.0.4/fh-4.0.1/kt-2.12.1/r-3.0.4/sc-2.4.3/sl-3.0.0
 *
 * Included libraries:
 *   DataTables 2.3.0, Buttons 3.2.3, ColReorder 2.1.0, FixedColumns 5.0.4, FixedHeader 4.0.1, KeyTable 2.12.1, Responsive 3.0.4, Scroller 2.4.3, Select 3.0.0
 */

/*! DataTables 2.3.0
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){"use strict";var r;"function"==typeof define&&define.amd?define(["jquery"],function(e){return n(e,window,document)}):"object"==typeof exports?(r=require("jquery"),"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||r(e),n(t,e,e.document)}:module.exports=n(r,window,window.document)):window.DataTable=n(jQuery,window,document)}(function(H,W,S){"use strict";function f(e){var t=parseInt(e,10);return!isNaN(t)&&isFinite(e)?t:null}function c(e,t,n,r){var a=typeof e,o="string"==a;return"number"==a||"bigint"==a||!(!r||!_(e))||(t&&o&&(e=E(e,t)),n&&o&&(e=e.replace(P,"")),!isNaN(parseFloat(e))&&isFinite(e))}function n(e,t,n,r){var a;return!(!r||!_(e))||("string"!=typeof e||!e.match(/<(input|select)/i))&&(_(a=e)||"string"==typeof a)&&!!c(w(e),t,n,r)||null}function v(e,t,n,r){var a=[],o=0,i=t.length;if(void 0!==r)for(;o<i;o++)e[t[o]]&&e[t[o]][n]&&a.push(e[t[o]][n][r]);else for(;o<i;o++)e[t[o]]&&a.push(e[t[o]][n]);return a}function h(e,t){var n,r=[];void 0===t?(t=0,n=e):(n=t,t=e);for(var a=t;a<n;a++)r.push(a);return r}function A(e){for(var t=[],n=0,r=e.length;n<r;n++)e[n]&&t.push(e[n]);return t}var T,X,r,e,V=function(e,P){var E,k,M;return V.factory(e,P)?V:this instanceof V?H(e).DataTable(P):(k=void 0===(P=e),M=(E=this).length,k&&(P={}),this.api=function(){return new X(this)},this.each(function(){var t=1<M?tt({},P,!0):P,e=0,n=this.getAttribute("id"),r=V.defaults,a=H(this);if("table"!=this.nodeName.toLowerCase())z(null,0,"Non-table node initialisation ("+this.nodeName+")",2);else{t.on&&t.on.options&&lt(a,"options",t.on.options),a.trigger("options.dt",t),Q(r),K(r.column),B(r,r,!0),B(r.column,r.column,!0),B(r,H.extend(t,a.data()),!0);var o=V.settings;for(e=0,j=o.length;e<j;e++){var i=o[e];if(i.nTable==this||i.nTHead&&i.nTHead.parentNode==this||i.nTFoot&&i.nTFoot.parentNode==this){var l=(void 0!==t.bRetrieve?t:r).bRetrieve,s=(void 0!==t.bDestroy?t:r).bDestroy;if(k||l)return i.oInstance;if(s){new V.Api(i).destroy();break}return void z(i,0,"Cannot reinitialise DataTable",3)}if(i.sTableId==this.id){o.splice(e,1);break}}null!==n&&""!==n||(n="DataTables_Table_"+V.ext._unique++,this.id=n);var u,c=H.extend(!0,{},V.models.oSettings,{sDestroyWidth:a[0].style.width,sInstance:n,sTableId:n,colgroup:H("<colgroup>").prependTo(this),fastData:function(e,t,n){return q(c,e,t,n)}}),n=(c.nTable=this,c.oInit=t,o.push(c),c.api=new X(c),c.oInstance=1===E.length?E:a.dataTable(),Q(t),t.aLengthMenu&&!t.iDisplayLength&&(t.iDisplayLength=Array.isArray(t.aLengthMenu[0])?t.aLengthMenu[0][0]:H.isPlainObject(t.aLengthMenu[0])?t.aLengthMenu[0].value:t.aLengthMenu[0]),t=tt(H.extend(!0,{},r),t),$(c.oFeatures,t,["bPaginate","bLengthChange","bFilter","bSort","bSortMulti","bInfo","bProcessing","bAutoWidth","bSortClasses","bServerSide","bDeferRender"]),$(c,t,["ajax","fnFormatNumber","sServerMethod","aaSorting","aaSortingFixed","aLengthMenu","sPaginationType","iStateDuration","bSortCellsTop","iTabIndex","sDom","fnStateLoadCallback","fnStateSaveCallback","renderer","searchDelay","rowId","caption","layout","orderDescReverse","orderIndicators","orderHandler","titleRow","typeDetect",["iCookieDuration","iStateDuration"],["oSearch","oPreviousSearch"],["aoSearchCols","aoPreSearchCols"],["iDisplayLength","_iDisplayLength"]]),$(c.oScroll,t,[["sScrollX","sX"],["sScrollXInner","sXInner"],["sScrollY","sY"],["bScrollCollapse","bCollapse"]]),$(c.oLanguage,t,"fnInfoCallback"),Y(c,"aoDrawCallback",t.fnDrawCallback),Y(c,"aoStateSaveParams",t.fnStateSaveParams),Y(c,"aoStateLoadParams",t.fnStateLoadParams),Y(c,"aoStateLoaded",t.fnStateLoaded),Y(c,"aoRowCallback",t.fnRowCallback),Y(c,"aoRowCreatedCallback",t.fnCreatedRow),Y(c,"aoHeaderCallback",t.fnHeaderCallback),Y(c,"aoFooterCallback",t.fnFooterCallback),Y(c,"aoInitComplete",t.fnInitComplete),Y(c,"aoPreDrawCallback",t.fnPreDrawCallback),c.rowIdFn=U(t.rowId),t.on&&Object.keys(t.on).forEach(function(e){lt(a,e,t.on[e])}),c),d=(V.__browser||(f={},V.__browser=f,p=H("<div/>").css({position:"fixed",top:0,left:-1*W.pageXOffset,height:1,width:1,overflow:"hidden"}).append(H("<div/>").css({position:"absolute",top:1,left:1,width:100,overflow:"scroll"}).append(H("<div/>").css({width:"100%",height:10}))).appendTo("body"),d=p.children(),u=d.children(),f.barWidth=d[0].offsetWidth-d[0].clientWidth,f.bScrollbarLeft=1!==Math.round(u.offset().left),p.remove()),H.extend(n.oBrowser,V.__browser),n.oScroll.iBarWidth=V.__browser.barWidth,c.oClasses),f=(H.extend(d,V.ext.classes,t.oClasses),a.addClass(d.table),c.oFeatures.bPaginate||(t.iDisplayStart=0),void 0===c.iInitDisplayStart&&(c.iInitDisplayStart=t.iDisplayStart,c._iDisplayStart=t.iDisplayStart),t.iDeferLoading),h=(null!==f&&(c.deferLoading=!0,u=Array.isArray(f),c._iRecordsDisplay=u?f[0]:f,c._iRecordsTotal=u?f[1]:f),[]),p=this.getElementsByTagName("thead"),n=Ne(c,p[0]);if(t.aoColumns)h=t.aoColumns;else if(n.length)for(j=n[e=0].length;e<j;e++)h.push(null);for(e=0,j=h.length;e<j;e++)ee(c);var g,m,v,b,y,D,x,S=c,w=t.aoColumnDefs,T=h,_=n,C=function(e,t){te(c,e,t)},I=S.aoColumns;if(T)for(g=0,m=T.length;g<m;g++)T[g]&&T[g].name&&(I[g].sName=T[g].name);if(w)for(g=w.length-1;0<=g;g--){var L=void 0!==(x=w[g]).target?x.target:void 0!==x.targets?x.targets:x.aTargets;for(Array.isArray(L)||(L=[L]),v=0,b=L.length;v<b;v++){var A=L[v];if("number"==typeof A&&0<=A){for(;I.length<=A;)ee(S);C(A,x)}else if("number"==typeof A&&A<0)C(I.length+A,x);else if("string"==typeof A)for(y=0,D=I.length;y<D;y++)"_all"===A?C(y,x):-1!==A.indexOf(":name")?I[y].sName===A.replace(":name","")&&C(y,x):_.forEach(function(e){e[y]&&(e=H(e[y].cell),A.match(/^[a-z][\w-]*$/i)&&(A="."+A),e.is(A))&&C(y,x)})}}if(T)for(g=0,m=T.length;g<m;g++)C(g,T[g]);var N,n=a.children("tbody").find("tr:first-child").eq(0),F=(n.length&&(N=function(e,t){return null!==e.getAttribute("data-"+t)?t:null},H(n[0]).children("th, td").each(function(e,t){var n,r=c.aoColumns[e];r||z(c,0,"Incorrect column count",18),r.mData===e&&(n=N(t,"sort")||N(t,"order"),t=N(t,"filter")||N(t,"search"),null===n&&null===t||(r.mData={_:e+".display",sort:null!==n?e+".@data-"+n:void 0,type:null!==n?e+".@data-"+n:void 0,filter:null!==t?e+".@data-"+t:void 0},r._isArrayHost=!0,te(c,e)))})),Y(c,"aoDrawCallback",Ke),c.oFeatures);if(t.bStateSave&&(F.bStateSave=!0),void 0===t.aaSorting)for(var O=c.aaSorting,e=0,j=O.length;e<j;e++)O[e][1]=c.aoColumns[e].asSorting[0];Qe(c),Y(c,"aoDrawCallback",function(){(c.bSorted||"ssp"===J(c)||F.bDeferRender)&&Qe(c)});var n=a.children("caption"),n=(c.caption&&(n=0===n.length?H("<caption/>").appendTo(a):n).html(c.caption),n.length&&(n[0]._captionSide=n.css("caption-side"),c.captionNode=n[0]),0===p.length&&(p=H("<thead/>").appendTo(a)),c.nTHead=p[0],a.children("tbody")),n=(0===n.length&&(n=H("<tbody/>").insertAfter(p)),c.nTBody=n[0],a.children("tfoot")),R=(0===n.length&&(n=H("<tfoot/>").appendTo(a)),c.nTFoot=n[0],c.aiDisplay=c.aiDisplayMaster.slice(),c.bInitialised=!0,c.oLanguage);H.extend(!0,R,t.oLanguage),R.sUrl?H.ajax({dataType:"json",url:R.sUrl,success:function(e){B(r.oLanguage,e),H.extend(!0,R,e,c.oInit.oLanguage),G(c,null,"i18n",[c],!0),He(c)},error:function(){z(c,0,"i18n file loading error",21),He(c)}}):(G(c,null,"i18n",[c],!0),He(c))}}),E=null,this)},d=(V.ext=T={builder:"dt/dt-2.3.0/b-3.2.3/cr-2.1.0/fc-5.0.4/fh-4.0.1/kt-2.12.1/r-3.0.4/sc-2.4.3/sl-3.0.0",buttons:{},ccContent:{},classes:{},errMode:"alert",feature:[],features:{},search:[],selector:{cell:[],column:[],row:[]},legacy:{ajax:null},pager:{},renderer:{pageButton:{},header:{}},order:{},type:{className:{},detect:[],render:{},search:{},order:{}},_unique:0,fnVersionCheck:V.fnVersionCheck,iApiIndex:0,sVersion:V.version},H.extend(T,{afnFiltering:T.search,aTypes:T.type.detect,ofnSearch:T.type.search,oSort:T.type.order,afnSortData:T.order,aoFeatures:T.feature,oStdClasses:T.classes,oPagination:T.pager}),H.extend(V.ext.classes,{container:"dt-container",empty:{row:"dt-empty"},info:{container:"dt-info"},layout:{row:"dt-layout-row",cell:"dt-layout-cell",tableRow:"dt-layout-table",tableCell:"",start:"dt-layout-start",end:"dt-layout-end",full:"dt-layout-full"},length:{container:"dt-length",select:"dt-input"},order:{canAsc:"dt-orderable-asc",canDesc:"dt-orderable-desc",isAsc:"dt-ordering-asc",isDesc:"dt-ordering-desc",none:"dt-orderable-none",position:"sorting_"},processing:{container:"dt-processing"},scrolling:{body:"dt-scroll-body",container:"dt-scroll",footer:{self:"dt-scroll-foot",inner:"dt-scroll-footInner"},header:{self:"dt-scroll-head",inner:"dt-scroll-headInner"}},search:{container:"dt-search",input:"dt-input"},table:"dataTable",tbody:{cell:"",row:""},thead:{cell:"",row:""},tfoot:{cell:"",row:""},paging:{active:"current",button:"dt-paging-button",container:"dt-paging",disabled:"disabled",nav:""}}),{}),N=/[\r\n\u2028]/g,F=/<([^>]*>)/g,O=Math.pow(2,28),j=/^\d{2,4}[./-]\d{1,2}[./-]\d{1,2}([T ]{1}\d{1,2}[:.]\d{2}([.:]\d{2})?)?$/,R=new RegExp("(\\"+["/",".","*","+","?","|","(",")","[","]","{","}","\\","$","^","-"].join("|\\")+")","g"),P=/['\u00A0,$£€¥%\u2009\u202F\u20BD\u20a9\u20BArfkɃΞ]/gi,_=function(e){return!e||!0===e||"-"===e},E=function(e,t){return d[t]||(d[t]=new RegExp(Ee(t),"g")),"string"==typeof e&&"."!==t?e.replace(/\./g,"").replace(d[t],"."):e},b=function(e,t,n){var r=[],a=0,o=e.length;if(void 0!==n)for(;a<o;a++)e[a]&&e[a][t]&&r.push(e[a][t][n]);else for(;a<o;a++)e[a]&&r.push(e[a][t]);return r},w=function(e){if(!e||"string"!=typeof e)return e;if(e.length>O)throw new Error("Exceeded max str len");var t;for(e=e.replace(F,"");(e=(t=e).replace(/<script/i,""))!==t;);return t},u=function(e){return"string"==typeof(e=Array.isArray(e)?e.join(","):e)?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"):e},k=function(e,t){var n;return"string"!=typeof e?e:(n=e.normalize?e.normalize("NFD"):e).length!==e.length?(!0===t?e+" ":"")+n.replace(/[\u0300-\u036f]/g,""):n},C=function(e){if(Array.from&&Set)return Array.from(new Set(e));if(function(e){if(!(e.length<2))for(var t=e.slice().sort(),n=t[0],r=1,a=t.length;r<a;r++){if(t[r]===n)return!1;n=t[r]}return!0}(e))return e.slice();var t,n,r,a=[],o=e.length,i=0;e:for(n=0;n<o;n++){for(t=e[n],r=0;r<i;r++)if(a[r]===t)continue e;a.push(t),i++}return a},M=function(e,t){if(Array.isArray(t))for(var n=0;n<t.length;n++)M(e,t[n]);else e.push(t);return e};function y(t,e){e&&e.split(" ").forEach(function(e){e&&t.classList.add(e)})}function Z(t){var n,r,a={};H.each(t,function(e){(n=e.match(/^([^A-Z]+?)([A-Z])/))&&-1!=="a aa ai ao as b fn i m o s ".indexOf(n[1]+" ")&&(r=e.replace(n[0],n[2].toLowerCase()),a[r]=e,"o"===n[1])&&Z(t[e])}),t._hungarianMap=a}function B(t,n,r){var a;t._hungarianMap||Z(t),H.each(n,function(e){void 0===(a=t._hungarianMap[e])||!r&&void 0!==n[a]||("o"===a.charAt(0)?(n[a]||(n[a]={}),H.extend(!0,n[a],n[e]),B(t[a],n[a],r)):n[a]=n[e])})}V.util={diacritics:function(e,t){if("function"!=typeof e)return k(e,t);k=e},debounce:function(n,r){var a;return function(){var e=this,t=arguments;clearTimeout(a),a=setTimeout(function(){n.apply(e,t)},r||250)}},throttle:function(r,e){var a,o,i=void 0!==e?e:200;return function(){var e=this,t=+new Date,n=arguments;a&&t<a+i?(clearTimeout(o),o=setTimeout(function(){a=void 0,r.apply(e,n)},i)):(a=t,r.apply(e,n))}},escapeRegex:function(e){return e.replace(R,"\\$1")},set:function(r){var f;return H.isPlainObject(r)?V.util.set(r._):null===r?function(){}:"function"==typeof r?function(e,t,n){r(e,"set",t,n)}:"string"!=typeof r||-1===r.indexOf(".")&&-1===r.indexOf("[")&&-1===r.indexOf("(")?function(e,t){e[r]=t}:(f=function(e,t,n){for(var r,a,o,i,l=ge(n),n=l[l.length-1],s=0,u=l.length-1;s<u;s++){if("__proto__"===l[s]||"constructor"===l[s])throw new Error("Cannot set prototype values");if(r=l[s].match(pe),a=l[s].match(p),r){if(l[s]=l[s].replace(pe,""),e[l[s]]=[],(r=l.slice()).splice(0,s+1),i=r.join("."),Array.isArray(t))for(var c=0,d=t.length;c<d;c++)f(o={},t[c],i),e[l[s]].push(o);else e[l[s]]=t;return}a&&(l[s]=l[s].replace(p,""),e=e[l[s]](t)),null!==e[l[s]]&&void 0!==e[l[s]]||(e[l[s]]={}),e=e[l[s]]}n.match(p)?e[n.replace(p,"")](t):e[n.replace(pe,"")]=t},function(e,t){return f(e,t,r)})},get:function(a){var o,f;return H.isPlainObject(a)?(o={},H.each(a,function(e,t){t&&(o[e]=V.util.get(t))}),function(e,t,n,r){var a=o[t]||o._;return void 0!==a?a(e,t,n,r):e}):null===a?function(e){return e}:"function"==typeof a?function(e,t,n,r){return a(e,t,n,r)}:"string"!=typeof a||-1===a.indexOf(".")&&-1===a.indexOf("[")&&-1===a.indexOf("(")?function(e){return e[a]}:(f=function(e,t,n){var r,a,o;if(""!==n)for(var i=ge(n),l=0,s=i.length;l<s;l++){if(d=i[l].match(pe),r=i[l].match(p),d){if(i[l]=i[l].replace(pe,""),""!==i[l]&&(e=e[i[l]]),a=[],i.splice(0,l+1),o=i.join("."),Array.isArray(e))for(var u=0,c=e.length;u<c;u++)a.push(f(e[u],t,o));var d=d[0].substring(1,d[0].length-1);e=""===d?a:a.join(d);break}if(r)i[l]=i[l].replace(p,""),e=e[i[l]]();else{if(null===e||null===e[i[l]])return null;if(void 0===e||void 0===e[i[l]])return;e=e[i[l]]}}return e},function(e,t){return f(e,t,a)})},stripHtml:function(e){var t=typeof e;if("function"!=t)return"string"==t?w(e):e;w=e},escapeHtml:function(e){var t=typeof e;if("function"!=t)return"string"==t||Array.isArray(e)?u(e):e;u=e},unique:C};var a=function(e,t,n){void 0!==e[t]&&(e[n]=e[t])};function Q(e){a(e,"ordering","bSort"),a(e,"orderMulti","bSortMulti"),a(e,"orderClasses","bSortClasses"),a(e,"orderCellsTop","bSortCellsTop"),a(e,"order","aaSorting"),a(e,"orderFixed","aaSortingFixed"),a(e,"paging","bPaginate"),a(e,"pagingType","sPaginationType"),a(e,"pageLength","iDisplayLength"),a(e,"searching","bFilter"),"boolean"==typeof e.sScrollX&&(e.sScrollX=e.sScrollX?"100%":""),"boolean"==typeof e.scrollX&&(e.scrollX=e.scrollX?"100%":""),"object"==typeof e.bSort?(e.orderIndicators=void 0===e.bSort.indicators||e.bSort.indicators,e.orderHandler=void 0===e.bSort.handler||e.bSort.handler,e.bSort=!0):!1===e.bSort&&(e.orderIndicators=!1,e.orderHandler=!1),"boolean"==typeof e.bSortCellsTop&&(e.titleRow=e.bSortCellsTop);var t=e.aoSearchCols;if(t)for(var n=0,r=t.length;n<r;n++)t[n]&&B(V.models.oSearch,t[n]);e.serverSide&&!e.searchDelay&&(e.searchDelay=400)}function K(e){a(e,"orderable","bSortable"),a(e,"orderData","aDataSort"),a(e,"orderSequence","asSorting"),a(e,"orderDataType","sortDataType");var t=e.aDataSort;"number"!=typeof t||Array.isArray(t)||(e.aDataSort=[t])}function ee(e){var t=V.defaults.column,n=e.aoColumns.length,t=H.extend({},V.models.oColumn,t,{aDataSort:t.aDataSort||[n],mData:t.mData||n,idx:n,searchFixed:{},colEl:H("<col>").attr("data-dt-column",n)}),t=(e.aoColumns.push(t),e.aoPreSearchCols);t[n]=H.extend({},V.models.oSearch,t[n])}function te(e,t,n){function r(e){return"string"==typeof e&&-1!==e.indexOf("@")}var a=e.aoColumns[t],o=(null!=n&&(K(n),B(V.defaults.column,n,!0),void 0===n.mDataProp||n.mData||(n.mData=n.mDataProp),n.sType&&(a._sManualType=n.sType),n.className&&!n.sClass&&(n.sClass=n.className),t=a.sClass,H.extend(a,n),$(a,n,"sWidth","sWidthOrig"),t!==a.sClass&&(a.sClass=t+" "+a.sClass),void 0!==n.iDataSort&&(a.aDataSort=[n.iDataSort]),$(a,n,"aDataSort")),a.mData),i=U(o);a.mRender&&Array.isArray(a.mRender)&&(n=(t=a.mRender.slice()).shift(),a.mRender=V.render[n].apply(W,t)),a._render=a.mRender?U(a.mRender):null;a._bAttrSrc=H.isPlainObject(o)&&(r(o.sort)||r(o.type)||r(o.filter)),a._setter=null,a.fnGetData=function(e,t,n){var r=i(e,t,void 0,n);return a._render&&t?a._render(r,t,e,n):r},a.fnSetData=function(e,t,n){return m(o)(e,t,n)},"number"==typeof o||a._isArrayHost||(e._rowReadObject=!0),e.oFeatures.bSort||(a.bSortable=!1)}function ne(e){!function(t){if(t.oFeatures.bAutoWidth){var e,n,r=t.nTable,a=t.aoColumns,o=t.oScroll,i=o.sY,l=o.sX,o=o.sXInner,s=ie(t,"bVisible"),u=r.getAttribute("width"),c=r.parentNode,d=r.style.width,f=ze(t);if(f===t.containerWidth)return;t.containerWidth=f,d||u||(r.style.width="100%",d="100%"),d&&-1!==d.indexOf("%")&&(u=d),G(t,null,"column-calc",{visible:s},!1);var f=H(r.cloneNode()).css("visibility","hidden").removeAttr("id"),h=(f.append("<tbody>"),H("<tr/>").appendTo(f.find("tbody")));for(f.append(H(t.nTHead).clone()).append(H(t.nTFoot).clone()),f.find("tfoot th, tfoot td").css("width",""),f.find("thead th, thead td").each(function(){var e=ce(t,this,!0,!1);e?(this.style.width=e,l&&(this.style.minWidth=e,H(this).append(H("<div/>").css({width:e,margin:0,padding:0,border:0,height:1})))):this.style.width=""}),e=0;e<s.length;e++){p=s[e],n=a[p];var p=function(e,t){var n=e.aoColumns[t];if(!n.maxLenString){for(var r,a="",o=-1,i=0,l=e.aiDisplayMaster.length;i<l;i++){var s=e.aiDisplayMaster[i],s=De(e,s)[t],s=s&&"object"==typeof s&&s.nodeType?s.innerHTML:s+"";s=s.replace(/id=".*?"/g,"").replace(/name=".*?"/g,""),(r=w(s).replace(/&nbsp;/g," ")).length>o&&(a=s,o=r.length)}n.maxLenString=a}return n.maxLenString}(t,p),g=T.type.className[n.sType],m=p+n.sContentPadding,p=-1===p.indexOf("<")?S.createTextNode(m):m;H("<td/>").addClass(g).addClass(n.sClass).append(p).appendTo(h)}H("[name]",f).removeAttr("name");var v,b,d=H("<div/>").css(l||i?{position:"absolute",top:0,left:0,height:1,right:0,overflow:"hidden"}:{}).append(f).appendTo(c),y=(l&&o?f.width(o):l?(f.css("width","auto"),f.removeAttr("width"),f.outerWidth()<c.clientWidth&&u&&f.outerWidth(c.clientWidth)):i?f.outerWidth(c.clientWidth):u&&f.outerWidth(u),0),D=f.find("tbody tr").eq(0).children();for(e=0;e<s.length;e++){var x=D[e].getBoundingClientRect().width;y+=x,a[s[e]].sWidth=L(x)}r.style.width=L(y),d.remove(),u&&(r.style.width=L(u)),!u&&!l||t._reszEvt||(v=V.util.throttle(function(){var e=ze(t);t.bDestroying||0===e||ne(t)}),W.ResizeObserver?(b=H(t.nTableWrapper).is(":visible"),o=H("<div>").css({width:"100%",height:0}).addClass("dt-autosize").appendTo(t.nTableWrapper),t.resizeObserver=new ResizeObserver(function(e){b?b=!1:v()}),t.resizeObserver.observe(o[0])):H(W).on("resize.DT-"+t.sInstance,v),t._reszEvt=!0)}}(e);for(var t=e,n=t.aoColumns,r=0;r<n.length;r++){var a=ce(t,[r],!1,!1);n[r].colEl.css("width",a),t.oScroll.sX&&n[r].colEl.css("min-width",a)}var o=e.oScroll;""===o.sY&&""===o.sX||Ue(e),G(e,null,"column-sizing",[e])}function re(e,t){e=ie(e,"bVisible");return"number"==typeof e[t]?e[t]:null}function ae(e,t){e=ie(e,"bVisible").indexOf(t);return-1!==e?e:null}function oe(e){var t=e.aoHeader,n=e.aoColumns,r=0;if(t.length)for(var a=0,o=t[0].length;a<o;a++)n[a].bVisible&&"none"!==H(t[0][a].cell).css("display")&&r++;return r}function ie(e,n){var r=[];return e.aoColumns.map(function(e,t){e[n]&&r.push(t)}),r}function le(e,t){return!0===t?e._name:t}function se(e){for(var t,n,r,a,o,i,l=e.aoColumns,s=e.aoData,u=V.ext.type.detect,c=0,d=l.length;c<d;c++){if(i=[],!(o=l[c]).sType&&o._sManualType)o.sType=o._sManualType;else if(!o.sType){if(!e.typeDetect)return;for(t=0,n=u.length;t<n;t++){var f=u[t],h=f.oneOf,p=f.allOf||f,g=f.init,m=!1,v=null;if(g&&(v=le(f,g(e,o,c)))){o.sType=v;break}for(r=0,a=s.length;r<a;r++)if(s[r]){if(void 0===i[r]&&(i[r]=q(e,r,c,"type")),h&&!m&&(m=le(f,h(i[r],e))),!(v=le(f,p(i[r],e)))&&t!==u.length-3)break;if("html"===v&&!_(i[r]))break}if(h&&m&&v||!h&&v){o.sType=v;break}}o.sType||(o.sType="string")}var b=T.type.className[o.sType],b=(b&&(ue(e.aoHeader,c,b),ue(e.aoFooter,c,b)),T.type.render[o.sType]);if(b&&!o._render){o._render=V.util.get(b),y=w=S=x=D=void 0;for(var y,D=e,x=c,S=D.aoData,w=0;w<S.length;w++)S[w].nTr&&(y=q(D,w,x,"display"),S[w].displayData[x]=y,he(S[w].anCells[x],y))}}}function ue(e,t,n){e.forEach(function(e){e[t]&&e[t].unique&&y(e[t].cell,n)})}function ce(e,t,n,r){Array.isArray(t)||(t=de(t));for(var a,o=0,i=e.aoColumns,l=0,s=t.length;l<s;l++){var u=i[t[l]],c=n?u.sWidthOrig:u.sWidth;if(r||!1!==u.bVisible){if(null==c)return null;"number"==typeof c?(a="px",o+=c):(u=c.match(/([\d\.]+)([^\d]*)/))&&(o+=+u[1],a=3===u.length?u[2]:"px")}}return o+a}function de(e){e=H(e).closest("[data-dt-column]").attr("data-dt-column");return e?e.split(",").map(function(e){return+e}):[]}function D(e,t,n,r){for(var a=e.aoData.length,o=H.extend(!0,{},V.models.oRow,{src:n?"dom":"data",idx:a}),i=(o._aData=t,e.aoData.push(o),e.aoColumns),l=0,s=i.length;l<s;l++)i[l].sType=null;e.aiDisplayMaster.push(a);t=e.rowIdFn(t);return void 0!==t&&(e.aIds[t]=o),!n&&e.oFeatures.bDeferRender||xe(e,a,n,r),a}function fe(n,e){var r;return(e=e instanceof H?e:H(e)).map(function(e,t){return r=ye(n,t),D(n,r.data,t,r.cells)})}function q(e,t,n,r){"search"===r?r="filter":"order"===r&&(r="sort");var a=e.aoData[t];if(a){var o=e.iDraw,i=e.aoColumns[n],a=a._aData,l=i.sDefaultContent,s=i.fnGetData(a,r,{settings:e,row:t,col:n});if(void 0===(s="display"!==r&&s&&"object"==typeof s&&s.nodeName?s.innerHTML:s))return e.iDrawError!=o&&null===l&&(z(e,0,"Requested unknown parameter "+("function"==typeof i.mData?"{function}":"'"+i.mData+"'")+" for row "+t+", column "+n,4),e.iDrawError=o),l;if(s!==a&&null!==s||null===l||void 0===r){if("function"==typeof s)return s.call(a)}else s=l;return null===s&&"display"===r?"":s="filter"===r&&(t=V.ext.type.search)[i.sType]?t[i.sType](s):s}}function he(e,t){t&&"object"==typeof t&&t.nodeName?H(e).empty().append(t):e.innerHTML=t}var pe=/\[.*?\]$/,p=/\(\)$/;function ge(e){return(e.match(/(\\.|[^.])+/g)||[""]).map(function(e){return e.replace(/\\\./g,".")})}var U=V.util.get,m=V.util.set;function me(e){return b(e.aoData,"_aData")}function ve(e){e.aoData.length=0,e.aiDisplayMaster.length=0,e.aiDisplay.length=0,e.aIds={}}function be(e,t,n,r){var a,o,i=e.aoData[t];if(i._aSortData=null,i._aFilterData=null,i.displayData=null,"dom"!==n&&(n&&"auto"!==n||"dom"!==i.src)){var l=i.anCells,s=De(e,t);if(l)if(void 0!==r)he(l[r],s[r]);else for(a=0,o=l.length;a<o;a++)he(l[a],s[a])}else i._aData=ye(e,i,r,void 0===r?void 0:i._aData).data;var u=e.aoColumns;if(void 0!==r)u[r].sType=null,u[r].maxLenString=null;else{for(a=0,o=u.length;a<o;a++)u[a].sType=null,u[a].maxLenString=null;Se(e,i)}}function ye(e,t,n,r){function a(e,t){var n;"string"==typeof e&&-1!==(n=e.indexOf("@"))&&(n=e.substring(n+1),m(e)(r,t.getAttribute(n)))}function o(e){void 0!==n&&n!==d||(l=f[d],s=e.innerHTML.trim(),l&&l._bAttrSrc?(m(l.mData._)(r,s),a(l.mData.sort,e),a(l.mData.type,e),a(l.mData.filter,e)):h?(l._setter||(l._setter=m(l.mData)),l._setter(r,s)):r[d]=s),d++}var i,l,s,u=[],c=t.firstChild,d=0,f=e.aoColumns,h=e._rowReadObject;r=void 0!==r?r:h?{}:[];if(c)for(;c;)"TD"!=(i=c.nodeName.toUpperCase())&&"TH"!=i||(o(c),u.push(c)),c=c.nextSibling;else for(var p=0,g=(u=t.anCells).length;p<g;p++)o(u[p]);var t=t.firstChild?t:t.nTr;return t&&(t=t.getAttribute("id"))&&m(e.rowId)(r,t),{data:r,cells:u}}function De(e,t){var n=e.aoData[t],r=e.aoColumns;if(!n.displayData){n.displayData=[];for(var a=0,o=r.length;a<o;a++)n.displayData.push(q(e,t,a,"display"))}return n.displayData}function xe(e,t,n,r){var a,o,i,l,s,u,c=e.aoData[t],d=c._aData,f=[],h=e.oClasses.tbody.row;if(null===c.nTr){for(a=n||S.createElement("tr"),c.nTr=a,c.anCells=f,y(a,h),a._DT_RowIndex=t,Se(e,c),l=0,s=e.aoColumns.length;l<s;l++){i=e.aoColumns[l],(o=(u=!n||!r[l])?S.createElement(i.sCellType):r[l])||z(e,0,"Incorrect column count",18),o._DT_CellIndex={row:t,column:l},f.push(o);var p=De(e,t);!u&&(!i.mRender&&i.mData===l||H.isPlainObject(i.mData)&&i.mData._===l+".display")||he(o,p[l]),y(o,i.sClass),i.bVisible&&u?a.appendChild(o):i.bVisible||u||o.parentNode.removeChild(o),i.fnCreatedCell&&i.fnCreatedCell.call(e.oInstance,o,q(e,t,l),d,t,l)}G(e,"aoRowCreatedCallback","row-created",[a,d,t,f])}else y(c.nTr,h)}function Se(e,t){var n=t.nTr,r=t._aData;n&&((e=e.rowIdFn(r))&&(n.id=e),r.DT_RowClass&&(e=r.DT_RowClass.split(" "),t.__rowc=t.__rowc?C(t.__rowc.concat(e)):e,H(n).removeClass(t.__rowc.join(" ")).addClass(r.DT_RowClass)),r.DT_RowAttr&&H(n).attr(r.DT_RowAttr),r.DT_RowData)&&H(n).data(r.DT_RowData)}function we(e,t){var n,r,a,o=e.oClasses,i=e.aoColumns,l="header"===t?e.nTHead:e.nTFoot,s="header"===t?"sTitle":t;if(l){if(("header"===t||b(e.aoColumns,s).join(""))&&1===(a=(a=H("tr",l)).length?a:H("<tr/>").appendTo(l)).length){var u=0;for(H("td, th",a).each(function(){u+=this.colSpan}),n=u,r=i.length;n<r;n++)H("<th/>").html(i[n][s]||"").appendTo(a)}var c=Ne(e,l,!0);"header"===t?(e.aoHeader=c,H("tr",l).addClass(o.thead.row)):(e.aoFooter=c,H("tr",l).addClass(o.tfoot.row)),H(l).children("tr").children("th, td").each(function(){at(e,t)(e,H(this),o)})}}function Te(e,t,n){var r,a,o,i,l,s=[],u=[],c=e.aoColumns,e=c.length;if(t){for(n=n||h(e).filter(function(e){return c[e].bVisible}),r=0;r<t.length;r++)s[r]=t[r].slice().filter(function(e,t){return n.includes(t)}),u.push([]);for(r=0;r<s.length;r++)for(a=0;a<s[r].length;a++)if(l=i=1,void 0===u[r][a]){for(o=s[r][a].cell;void 0!==s[r+i]&&s[r][a].cell==s[r+i][a].cell;)u[r+i][a]=null,i++;for(;void 0!==s[r][a+l]&&s[r][a].cell==s[r][a+l].cell;){for(var d=0;d<i;d++)u[r+d][a+l]=null;l++}var f=H("span.dt-column-title",o);u[r][a]={cell:o,colspan:l,rowspan:i,title:(f.length?f:H(o)).html()}}return u}}function _e(e,t){for(var n,r,a=Te(e,t),o=0;o<t.length;o++){if(n=t[o].row)for(;r=n.firstChild;)n.removeChild(r);for(var i=0;i<a[o].length;i++){var l=a[o][i];l&&H(l.cell).appendTo(n).attr("rowspan",l.rowspan).attr("colspan",l.colspan)}}}function x(e,t){if(a="ssp"==J(s=e),void 0!==(i=s.iInitDisplayStart)&&-1!==i&&(s._iDisplayStart=!a&&i>=s.fnRecordsDisplay()?0:i,s.iInitDisplayStart=-1),-1!==G(e,"aoPreDrawCallback","preDraw",[e]).indexOf(!1))I(e,!1);else{var l,n=[],r=0,a="ssp"==J(e),o=e.aiDisplay,i=e._iDisplayStart,s=e.fnDisplayEnd(),u=e.aoColumns,c=H(e.nTBody);if(e.bDrawing=!0,e.deferLoading)e.deferLoading=!1,e.iDraw++,I(e,!1);else if(a){if(!e.bDestroying&&!t)return 0===e.iDraw&&c.empty().append(Ie(e)),(l=e).iDraw++,I(l,!0),void Fe(l,function(t){function n(e,t){return"function"==typeof r[e][t]?"function":r[e][t]}var r=t.aoColumns,e=t.oFeatures,a=t.oPreviousSearch,o=t.aoPreSearchCols;return{draw:t.iDraw,columns:r.map(function(t,e){return{data:n(e,"mData"),name:t.sName,searchable:t.bSearchable,orderable:t.bSortable,search:{value:o[e].search,regex:o[e].regex,fixed:Object.keys(t.searchFixed).map(function(e){return{name:e,term:t.searchFixed[e].toString()}})}}}),order:Je(t).map(function(e){return{column:e.col,dir:e.dir,name:n(e.col,"sName")}}),start:t._iDisplayStart,length:e.bPaginate?t._iDisplayLength:-1,search:{value:a.search,regex:a.regex,fixed:Object.keys(t.searchFixed).map(function(e){return{name:e,term:t.searchFixed[e].toString()}})}}}(l),function(e){var t=l,n=Oe(t,e=e),r=je(t,"draw",e),a=je(t,"recordsTotal",e),e=je(t,"recordsFiltered",e);if(void 0!==r){if(+r<t.iDraw)return;t.iDraw=+r}n=n||[],ve(t),t._iRecordsTotal=parseInt(a,10),t._iRecordsDisplay=parseInt(e,10);for(var o=0,i=n.length;o<i;o++)D(t,n[o]);t.aiDisplay=t.aiDisplayMaster.slice(),se(t),x(t,!0),We(t),I(t,!1)})}else e.iDraw++;if(0!==o.length)for(var d=a?e.aoData.length:s,f=a?0:i;f<d;f++){for(var h=o[f],p=e.aoData[h],g=(null===p.nTr&&xe(e,h),p.nTr),m=0;m<u.length;m++){var v=u[m],b=p.anCells[m];y(b,T.type.className[v.sType]),y(b,e.oClasses.tbody.cell)}G(e,"aoRowCallback",null,[g,p._aData,r,f,h]),n.push(g),r++}else n[0]=Ie(e);G(e,"aoHeaderCallback","header",[H(e.nTHead).children("tr")[0],me(e),i,s,o]),G(e,"aoFooterCallback","footer",[H(e.nTFoot).children("tr")[0],me(e),i,s,o]),c[0].replaceChildren?c[0].replaceChildren.apply(c[0],n):(c.children().detach(),c.append(H(n))),H(e.nTableWrapper).toggleClass("dt-empty-footer",0===H("tr",e.nTFoot).length),G(e,"aoDrawCallback","draw",[e],!0),e.bSorted=!1,e.bFiltered=!1,e.bDrawing=!1}}function Ce(e,t,n){var r=e.oFeatures,a=r.bSort,r=r.bFilter;void 0!==n&&!0!==n||(se(e),a&&Ze(e),r?Re(e,e.oPreviousSearch):e.aiDisplay=e.aiDisplayMaster.slice()),!0!==t&&(e._iDisplayStart=0),e._drawHold=t,x(e),e._drawHold=!1}function Ie(e){var t=e.oLanguage,n=t.sZeroRecords,r=J(e);return"ssp"!==r&&"ajax"!==r||e.json?t.sEmptyTable&&0===e.fnRecordsTotal()&&(n=t.sEmptyTable):n=t.sLoadingRecords,H("<tr/>").append(H("<td />",{colSpan:oe(e),class:e.oClasses.empty.row}).html(n))[0]}function Le(e,t,a){var o=[];H.each(t,function(e,t){var n,r;null!==t&&(n=(e=e.match(/^([a-z]+)([0-9]*)([A-Za-z]*)$/))[2]?+e[2]:0,r=e[3]?e[3].toLowerCase():"full",e[1]===a)&&function e(t,n,r){if(Array.isArray(r))for(var a=0;a<r.length;a++)e(t,n,r[a]);else{var o=t[n];H.isPlainObject(r)?r.features?(r.rowId&&(t.id=r.rowId),r.rowClass&&(t.className=r.rowClass),o.id=r.id,o.className=r.className,e(t,n,r.features)):Object.keys(r).map(function(e){o.contents.push({feature:e,opts:r[e]})}):o.contents.push(r)}}(function(e,t,n){for(var r,a=0;a<e.length;a++)if((r=e[a]).rowNum===t&&("full"===n&&r.full||("start"===n||"end"===n)&&(r.start||r.end)))return r[n]||(r[n]={contents:[]}),r;return(r={rowNum:t})[n]={contents:[]},e.push(r),r}(o,n,r),r,t)}),o.sort(function(e,t){var n=e.rowNum,r=t.rowNum;return n===r?(e=e.full&&!t.full?-1:1,"bottom"===a?-1*e:e):r-n}),"bottom"===a&&o.reverse();for(var n=0;n<o.length;n++)delete o[n].rowNum,!function(o,i){function l(e,t){return T.features[e]||z(o,0,"Unknown feature: "+e),T.features[e].apply(this,[o,t])}function e(e){if(i[e])for(var t,n=i[e].contents,r=0,a=n.length;r<a;r++)n[r]&&("string"==typeof n[r]?n[r]=l(n[r],null):H.isPlainObject(n[r])?n[r]=l(n[r].feature,n[r].opts):"function"==typeof n[r].node?n[r]=n[r].node(o):"function"==typeof n[r]&&(t=n[r](o),n[r]="function"==typeof t.node?t.node():t))}e("start"),e("end"),e("full")}(e,o[n]);return o}function Ae(t){var r,e=t.oClasses,n=H(t.nTable),a=H("<div/>").attr({id:t.sTableId+"_wrapper",class:e.container}).insertBefore(n);if(t.nTableWrapper=a[0],t.sDom)for(var o,i,l,s,u,c,d=t,e=t.sDom,f=a,h=e.match(/(".*?")|('.*?')|./g),p=0;p<h.length;p++)o=null,"<"==(i=h[p])?(l=H("<div/>"),"'"!=(s=h[p+1])[0]&&'"'!=s[0]||(s=s.replace(/['"]/g,""),u="",-1!=s.indexOf(".")?(c=s.split("."),u=c[0],c=c[1]):"#"==s[0]?u=s:c=s,l.attr("id",u.substring(1)).addClass(c),p++),f.append(l),f=l):">"==i?f=f.parent():"t"==i?o=qe(d):V.ext.feature.forEach(function(e){i==e.cFeature&&(o=e.fnInit(d))}),o&&f.append(o);else{var n=Le(t,t.layout,"top"),e=Le(t,t.layout,"bottom"),g=at(t,"layout");n.forEach(function(e){g(t,a,e)}),g(t,a,{full:{table:!0,contents:[qe(t)]}}),e.forEach(function(e){g(t,a,e)})}var n=t,e=n.nTable,m=""!==n.oScroll.sX||""!==n.oScroll.sY;n.oFeatures.bProcessing&&(r=H("<div/>",{id:n.sTableId+"_processing",class:n.oClasses.processing.container,role:"status"}).html(n.oLanguage.sProcessing).append("<div><div></div><div></div><div></div><div></div></div>"),m?r.prependTo(H("div.dt-scroll",n.nTableWrapper)):r.insertBefore(e),H(e).on("processing.dt.DT",function(e,t,n){r.css("display",n?"block":"none")}))}function Ne(e,t,n){for(var r,a,o,i,l,s,u=e.aoColumns,c=H(t).children("tr"),d=e.titleRow,f=t&&"thead"===t.nodeName.toLowerCase(),h=[],p=0,g=c.length;p<g;p++)h.push([]);for(p=0,g=c.length;p<g;p++)for(a=(r=c[p]).firstChild;a;){if("TD"==a.nodeName.toUpperCase()||"TH"==a.nodeName.toUpperCase()){var m,v,b,y,D,x=[],S=H(a);for(y=(y=+a.getAttribute("colspan"))&&0!=y&&1!=y?y:1,D=(D=+a.getAttribute("rowspan"))&&0!=D&&1!=D?D:1,l=function(e,t,n){for(var r=e[t];r[n];)n++;return n}(h,p,0),s=1==y,n&&(s&&(te(e,l,S.data()),m=u[l],v=a.getAttribute("width")||null,(b=a.style.width.match(/width:\s*(\d+[pxem%]+)/))&&(v=b[1]),m.sWidthOrig=m.sWidth||v,f?(null===m.sTitle||m.autoTitle||(!0===d&&0===p||!1===d&&p===c.length-1||d===p||null===d)&&(a.innerHTML=m.sTitle),!m.sTitle&&s&&(m.sTitle=w(a.innerHTML),m.autoTitle=!0)):m.footer&&(a.innerHTML=m.footer),m.ariaTitle||(m.ariaTitle=S.attr("aria-label")||m.sTitle),m.className)&&S.addClass(m.className),0===H("span.dt-column-title",a).length&&H("<span>").addClass("dt-column-title").append(a.childNodes).appendTo(a),e.orderIndicators&&f&&0!==S.filter(":not([data-dt-order=disable])").length&&0!==S.parent(":not([data-dt-order=disable])").length&&0===H("span.dt-column-order",a).length&&H("<span>").addClass("dt-column-order").appendTo(a),0===H("span.dt-column-"+(b=f?"header":"footer"),a).length)&&H("<div>").addClass("dt-column-"+b).append(a.childNodes).appendTo(a),i=0;i<y;i++){for(o=0;o<D;o++)h[p+o][l+i]={cell:a,unique:s},h[p+o].row=r;x.push(l+i)}a.setAttribute("data-dt-column",C(x).join(","))}a=a.nextSibling}return h}function Fe(n,e,r){function t(e){var t=n.jqXHR?n.jqXHR.status:null;if((null===e||"number"==typeof t&&204==t)&&Oe(n,e={},[]),(t=e.error||e.sError)&&z(n,0,t),e.d&&"string"==typeof e.d)try{e=JSON.parse(e.d)}catch(e){}n.json=e,G(n,null,"xhr",[n,e,n.jqXHR],!0),r(e)}var a,o=n.ajax,i=n.oInstance,l=(H.isPlainObject(o)&&o.data&&(l="function"==typeof(a=o.data)?a(e,n):a,e="function"==typeof a&&l?l:H.extend(!0,e,l),delete o.data),{url:"string"==typeof o?o:"",data:e,success:t,dataType:"json",cache:!1,type:n.sServerMethod,error:function(e,t){-1===G(n,null,"xhr",[n,null,n.jqXHR],!0).indexOf(!0)&&("parsererror"==t?z(n,0,"Invalid JSON response",1):4===e.readyState&&z(n,0,"Ajax error",7)),I(n,!1)}});H.isPlainObject(o)&&H.extend(l,o),n.oAjaxData=e,G(n,null,"preXhr",[n,e,l],!0),"json"===l.submitAs&&"object"==typeof e&&(l.data=JSON.stringify(e)),"function"==typeof o?n.jqXHR=o.call(i,e,t,n):""===o.url?(i={},V.util.set(o.dataSrc)(i,[]),t(i)):n.jqXHR=H.ajax(l),a&&(o.data=a)}function Oe(e,t,n){var r="data";if(H.isPlainObject(e.ajax)&&void 0!==e.ajax.dataSrc&&("string"==typeof(e=e.ajax.dataSrc)||"function"==typeof e?r=e:void 0!==e.data&&(r=e.data)),!n)return"data"===r?t.aaData||t[r]:""!==r?U(r)(t):t;m(r)(t,n)}function je(e,t,n){var e=H.isPlainObject(e.ajax)?e.ajax.dataSrc:null;return e&&e[t]?U(e[t])(n):(e="","draw"===t?e="sEcho":"recordsTotal"===t?e="iTotalRecords":"recordsFiltered"===t&&(e="iTotalDisplayRecords"),void 0!==n[e]?n[e]:n[t])}function Re(n,e){var t=n.aoPreSearchCols;if("ssp"!=J(n)){for(var r,a,o,i,l,s=n,u=s.aoColumns,c=s.aoData,d=0;d<c.length;d++)if(c[d]&&!(l=c[d])._aFilterData){for(o=[],r=0,a=u.length;r<a;r++)u[r].bSearchable?"string"!=typeof(i=null===(i=q(s,d,r,"filter"))?"":i)&&i.toString&&(i=i.toString()):i="",i.indexOf&&-1!==i.indexOf("&")&&(ke.innerHTML=i,i=Me?ke.textContent:ke.innerText),i.replace&&(i=i.replace(/[\r\n\u2028]/g,"")),o.push(i);l._aFilterData=o,l._sFilterRow=o.join("  "),0}n.aiDisplay=n.aiDisplayMaster.slice(),Pe(n.aiDisplay,n,e.search,e),H.each(n.searchFixed,function(e,t){Pe(n.aiDisplay,n,t,{})});for(var f=0;f<t.length;f++){var h=t[f];Pe(n.aiDisplay,n,h.search,h,f),H.each(n.aoColumns[f].searchFixed,function(e,t){Pe(n.aiDisplay,n,t,{},f)})}for(var p,g,m=n,v=V.ext.search,b=m.aiDisplay,y=0,D=v.length;y<D;y++){for(var x=[],S=0,w=b.length;S<w;S++)g=b[S],p=m.aoData[g],v[y](m,p._aFilterData,g,p._aData,S)&&x.push(g);b.length=0,it(b,x)}}n.bFiltered=!0,G(n,null,"search",[n])}function Pe(e,t,n,r,a){if(""!==n){for(var o=0,i=[],l="function"==typeof n?n:null,s=n instanceof RegExp?n:l?null:function(e,t){var r=[],t=H.extend({},{boundary:!1,caseInsensitive:!0,exact:!1,regex:!1,smart:!0},t);"string"!=typeof e&&(e=e.toString());if(e=k(e),t.exact)return new RegExp("^"+Ee(e)+"$",t.caseInsensitive?"i":"");{var n,a,o;e=t.regex?e:Ee(e),t.smart&&(n=(e.match(/!?["\u201C][^"\u201D]+["\u201D]|[^ ]+/g)||[""]).map(function(e){var t,n=!1;return"!"===e.charAt(0)&&(n=!0,e=e.substring(1)),'"'===e.charAt(0)?e=(t=e.match(/^"(.*)"$/))?t[1]:e:"“"===e.charAt(0)&&(e=(t=e.match(/^\u201C(.*)\u201D$/))?t[1]:e),n&&(1<e.length&&r.push("(?!"+e+")"),e=""),e.replace(/"/g,"")}),a=r.length?r.join(""):"",o=t.boundary?"\\b":"",e="^(?=.*?"+o+n.join(")(?=.*?"+o)+")("+a+".)*$")}return new RegExp(e,t.caseInsensitive?"i":"")}(n,r),o=0;o<e.length;o++){var u=t.aoData[e[o]],c=void 0===a?u._sFilterRow:u._aFilterData[a];(l&&l(c,u._aData,e[o],a)||s&&s.test(c))&&i.push(e[o])}for(e.length=i.length,o=0;o<i.length;o++)e[o]=i[o]}}var Ee=V.util.escapeRegex,ke=H("<div>")[0],Me=void 0!==ke.textContent;function He(i){var l,t,n,e,s=i.oInit,u=i.deferLoading,c=J(i);i.bInitialised?(we(i,"header"),we(i,"footer"),n=function(){_e(i,i.aoHeader),_e(i,i.aoFooter);var n=i.iInitDisplayStart;if(s.aaData)for(l=0;l<s.aaData.length;l++)D(i,s.aaData[l]);else!u&&"dom"!=c||fe(i,H(i.nTBody).children("tr"));i.aiDisplay=i.aiDisplayMaster.slice(),Ae(i);var e=i,t=e.nTHead,r=t.querySelectorAll("tr"),a=e.titleRow,o=':not([data-dt-order="disable"]):not([data-dt-order="icon-only"])';!0===a?t=r[0]:!1===a?t=r[r.length-1]:null!==a&&(t=r[a]),e.orderHandler&&Ye(e,t,t===e.nTHead?"tr"+o+" th"+o+", tr"+o+" td"+o:"th"+o+", td"+o),g(e,r=[],e.aaSorting),e.aaSorting=r,$e(i),I(i,!0),G(i,null,"preInit",[i],!0),Ce(i),"ssp"==c&&!u||("ajax"==c?Fe(i,{},function(e){var t=Oe(i,e);for(l=0;l<t.length;l++)D(i,t[l]);i.iInitDisplayStart=n,Ce(i),I(i,!1),We(i)}):(We(i),I(i,!1)))},(t=i).oFeatures.bStateSave?void 0!==(e=t.fnStateLoadCallback.call(t.oInstance,t,function(e){et(t,e,n)}))&&et(t,e,n):n()):setTimeout(function(){He(i)},200)}function We(e){var t;e._bInitComplete||(t=[e,e.json],e._bInitComplete=!0,ne(e),G(e,null,"plugin-init",t,!0),G(e,"aoInitComplete","init",t,!0))}function Xe(e,t){t=parseInt(t,10);e._iDisplayLength=t,rt(e),G(e,null,"length",[e,t])}function Ve(e,t,n){var r=e._iDisplayStart,a=e._iDisplayLength,o=e.fnRecordsDisplay();if(0===o||-1===a)r=0;else if("number"==typeof t)o<(r=t*a)&&(r=0);else if("first"==t)r=0;else if("previous"==t)(r=0<=a?r-a:0)<0&&(r=0);else if("next"==t)r+a<o&&(r+=a);else if("last"==t)r=Math.floor((o-1)/a)*a;else{if("ellipsis"===t)return;z(e,0,"Unknown paging action: "+t,5)}o=e._iDisplayStart!==r;e._iDisplayStart=r,G(e,null,o?"page":"page-nc",[e]),o&&n&&x(e)}function I(e,t){e.bDrawing&&!1===t||G(e,null,"processing",[e,t])}function Be(e,t,n){t?(I(e,!0),setTimeout(function(){n(),I(e,!1)},0)):n()}function qe(e){var t,n,r,a,o,i,l,s,u,c,d,f,h,p=H(e.nTable),g=e.oScroll;return""===g.sX&&""===g.sY?e.nTable:(t=g.sX,n=g.sY,r=e.oClasses.scrolling,o=(a=e.captionNode)?a._captionSide:null,u=H(p[0].cloneNode(!1)),i=H(p[0].cloneNode(!1)),c=function(e){return e?L(e):null},(l=p.children("tfoot")).length||(l=null),u=H(s="<div/>",{class:r.container}).append(H(s,{class:r.header.self}).css({overflow:"hidden",position:"relative",border:0,width:t?c(t):"100%"}).append(H(s,{class:r.header.inner}).css({"box-sizing":"content-box",width:g.sXInner||"100%"}).append(u.removeAttr("id").css("margin-left",0).append("top"===o?a:null).append(p.children("thead"))))).append(H(s,{class:r.body}).css({position:"relative",overflow:"auto",width:c(t)}).append(p)),l&&u.append(H(s,{class:r.footer.self}).css({overflow:"hidden",border:0,width:t?c(t):"100%"}).append(H(s,{class:r.footer.inner}).append(i.removeAttr("id").css("margin-left",0).append("bottom"===o?a:null).append(p.children("tfoot"))))),c=u.children(),d=c[0],f=c[1],h=l?c[2]:null,H(f).on("scroll.DT",function(){var e=this.scrollLeft;d.scrollLeft=e,l&&(h.scrollLeft=e)}),H("th, td",d).on("focus",function(){var e=d.scrollLeft;f.scrollLeft=e,l&&(f.scrollLeft=e)}),H(f).css("max-height",n),g.bCollapse||H(f).css("height",n),e.nScrollHead=d,e.nScrollBody=f,e.nScrollFoot=h,e.aoDrawCallback.push(Ue),u[0])}function Ue(t){var e=t.oScroll,n=e.iBarWidth,r=H(t.nScrollHead).children("div"),a=r.children("table"),o=t.nScrollBody,i=H(o),l=H(t.nScrollFoot).children("div"),s=l.children("table"),u=H(t.nTHead),c=H(t.nTable),d=t.nTFoot&&H("th, td",t.nTFoot).length?H(t.nTFoot):null,f=t.oBrowser,h=o.scrollHeight>o.clientHeight;if(t.scrollBarVis!==h&&void 0!==t.scrollBarVis)t.scrollBarVis=h,ne(t);else{if(t.scrollBarVis=h,c.children("thead, tfoot").remove(),(h=u.clone().prependTo(c)).find("th, td").removeAttr("tabindex"),h.find("[id]").removeAttr("id"),d&&(D=d.clone().prependTo(c)).find("[id]").removeAttr("id"),t.aiDisplay.length){for(var p=null,g="ssp"!==J(t)?t._iDisplayStart:0,m=g;m<g+t.aiDisplay.length;m++){var v=t.aiDisplay[m],v=t.aoData[v].nTr;if(v){p=v;break}}if(p)for(var b=H(p).children("th, td").map(function(e){return{idx:re(t,e),width:H(this).outerWidth()}}),m=0;m<b.length;m++){var y=t.aoColumns[b[m].idx].colEl[0];y.style.width.replace("px","")!==b[m].width&&(y.style.width=b[m].width+"px",e.sX)&&(y.style.minWidth=b[m].width+"px")}}a.find("colgroup").remove(),a.append(t.colgroup.clone()),d&&(s.find("colgroup").remove(),s.append(t.colgroup.clone())),H("th, td",h).each(function(){H(this.childNodes).wrapAll('<div class="dt-scroll-sizing">')}),d&&H("th, td",D).each(function(){H(this.childNodes).wrapAll('<div class="dt-scroll-sizing">')});var u=Math.floor(c.height())>o.clientHeight||"scroll"==i.css("overflow-y"),h="padding"+(f.bScrollbarLeft?"Left":"Right"),D=c.outerWidth();a.css("width",L(D)),r.css("width",L(D)).css(h,u?n+"px":"0px"),d&&(s.css("width",L(D)),l.css("width",L(D)).css(h,u?n+"px":"0px")),c.children("colgroup").prependTo(c),i.trigger("scroll"),!t.bSorted&&!t.bFiltered||t._drawHold||(o.scrollTop=0)}}function ze(e){return H(e.nTableWrapper).is(":visible")?H(e.nTableWrapper).width():0}function L(e){return null===e?"0px":"number"==typeof e?e<0?"0px":e+"px":e.match(/\d$/)?e+"px":e}function $e(e){var t=e.aoColumns;for(e.colgroup.empty(),l=0;l<t.length;l++)t[l].bVisible&&e.colgroup.append(t[l].colEl)}function Ye(o,e,t,i,l){nt(e,t,function(e){var t=!1,n=void 0===i?de(e.target):Array.isArray(i)?i:[i];if(n.length){for(var r=0,a=n.length;r<a;r++)if(!1!==function(e,t,n,r){function a(e,t){var n=e._idx;return(n=void 0===n?s.indexOf(e[1]):n)+1<s.length?n+1:t?null:0}var o,i=e.aoColumns[t],l=e.aaSorting,s=i.asSorting;if(!i.bSortable)return!1;"number"==typeof l[0]&&(l=e.aaSorting=[l]);(r||n)&&e.oFeatures.bSortMulti?-1!==(i=b(l,"0").indexOf(t))?null===(o=null===(o=a(l[i],!0))&&1===l.length?0:o)?l.splice(i,1):(l[i][1]=s[o],l[i]._idx=o):(r?l.push([t,s[0],0]):l.push([t,l[0][1],0]),l[l.length-1]._idx=0):l.length&&l[0][0]==t?(o=a(l[0]),l.length=1,l[0][1]=s[o],l[0]._idx=o):(l.length=0,l.push([t,s[0]]),l[0]._idx=0)}(o,n[r],r,e.shiftKey)&&(t=!0),1===o.aaSorting.length&&""===o.aaSorting[0][1])break;t&&Be(o,!0,function(){Ze(o),Ge(o,o.aiDisplay),Ce(o,!1,!1),l&&l()})}})}function Ge(e,t){if(!(t.length<2)){for(var n=e.aiDisplayMaster,r={},a={},o=0;o<n.length;o++)r[n[o]]=o;for(o=0;o<t.length;o++)a[t[o]]=r[t[o]];t.sort(function(e,t){return a[e]-a[t]})}}function g(n,r,e){function t(e){var t;H.isPlainObject(e)?void 0!==e.idx?r.push([e.idx,e.dir]):e.name&&-1!==(t=b(n.aoColumns,"sName").indexOf(e.name))&&r.push([t,e.dir]):r.push(e)}if(H.isPlainObject(e))t(e);else if(e.length&&"number"==typeof e[0])t(e);else if(e.length)for(var a=0;a<e.length;a++)t(e[a])}function Je(e){var t,n,r,a,o,i,l,s=[],u=V.ext.type.order,c=e.aoColumns,d=e.aaSortingFixed,f=H.isPlainObject(d),h=[];if(e.oFeatures.bSort)for(Array.isArray(d)&&g(e,h,d),f&&d.pre&&g(e,h,d.pre),g(e,h,e.aaSorting),f&&d.post&&g(e,h,d.post),t=0;t<h.length;t++)if(c[l=h[t][0]])for(n=0,r=(a=c[l].aDataSort).length;n<r;n++)i=c[o=a[n]].sType||"string",void 0===h[t]._idx&&(h[t]._idx=c[o].asSorting.indexOf(h[t][1])),h[t][1]&&s.push({src:l,col:o,dir:h[t][1],index:h[t]._idx,type:i,formatter:u[i+"-pre"],sorter:u[i+"-"+h[t][1]]});return s}function Ze(e,t,n){var r,a,o,i,l,c,d=[],s=V.ext.type.order,f=e.aoData,u=e.aiDisplayMaster;for(se(e),void 0!==t?(l=e.aoColumns[t],c=[{src:t,col:t,dir:n,index:0,type:l.sType,formatter:s[l.sType+"-pre"],sorter:s[l.sType+"-"+n]}],u=u.slice()):c=Je(e),r=0,a=c.length;r<a;r++){i=c[r],S=x=D=g=p=h=y=b=v=m=void 0;var h,p,g,m=e,v=i.col,b=m.aoColumns[v],y=V.ext.order[b.sSortDataType];y&&(h=y.call(m.oInstance,m,v,ae(m,v)));for(var D=V.ext.type.order[b.sType+"-pre"],x=m.aoData,S=0;S<x.length;S++)x[S]&&((p=x[S])._aSortData||(p._aSortData=[]),p._aSortData[v]&&!y||(g=y?h[S]:q(m,S,v,"sort"),p._aSortData[v]=D?D(g,m):g))}if("ssp"!=J(e)&&0!==c.length){for(r=0,o=u.length;r<o;r++)d[r]=r;c.length&&"desc"===c[0].dir&&e.orderDescReverse&&d.reverse(),u.sort(function(e,t){for(var n,r,a,o,i=c.length,l=f[e]._aSortData,s=f[t]._aSortData,u=0;u<i;u++)if(n=l[(o=c[u]).col],r=s[o.col],o.sorter){if(0!==(a=o.sorter(n,r)))return a}else if(0!==(a=n<r?-1:r<n?1:0))return"asc"===o.dir?a:-a;return(n=d[e])<(r=d[t])?-1:r<n?1:0})}else 0===c.length&&u.sort(function(e,t){return e<t?-1:t<e?1:0});return void 0===t&&(e.bSorted=!0,e.sortDetails=c,G(e,null,"order",[e,c])),u}function Qe(e){var t,n,r,a=e.aLastSort,o=e.oClasses.order.position,i=Je(e),l=e.oFeatures;if(l.bSort&&l.bSortClasses){for(t=0,n=a.length;t<n;t++)r=a[t].src,H(b(e.aoData,"anCells",r)).removeClass(o+(t<2?t+1:3));for(t=0,n=i.length;t<n;t++)r=i[t].src,H(b(e.aoData,"anCells",r)).addClass(o+(t<2?t+1:3))}e.aLastSort=i}function Ke(n){var t,e;n._bLoadingState||(g(n,e=[],n.aaSorting),t=n.aoColumns,e={time:+new Date,start:n._iDisplayStart,length:n._iDisplayLength,order:e.map(function(e){return t[e[0]]&&t[e[0]].sName?[t[e[0]].sName,e[1]]:e.slice()}),search:H.extend({},n.oPreviousSearch),columns:n.aoColumns.map(function(e,t){return{name:e.sName,visible:e.bVisible,search:H.extend({},n.aoPreSearchCols[t])}})},n.oSavedState=e,G(n,"aoStateSaveParams","stateSaveParams",[n,e]),n.oFeatures.bStateSave&&!n.bDestroying&&n.fnStateSaveCallback.call(n.oInstance,n,e))}function et(r,e,t){var n,a,o=r.aoColumns,i=b(r.aoColumns,"sName"),l=(r._bLoadingState=!0,r._bInitComplete?new V.Api(r):null);if(e&&e.time){var s=r.iStateDuration;if(0<s&&e.time<+new Date-1e3*s)r._bLoadingState=!1;else if(-1!==G(r,"aoStateLoadParams","stateLoadParams",[r,e]).indexOf(!1))r._bLoadingState=!1;else{if(r.oLoadedState=H.extend(!0,{},e),G(r,null,"stateLoadInit",[r,e],!0),void 0!==e.length&&(l?l.page.len(e.length):r._iDisplayLength=e.length),void 0!==e.start&&(null===l?(r._iDisplayStart=e.start,r.iInitDisplayStart=e.start):Ve(r,e.start/r._iDisplayLength)),void 0!==e.order&&(r.aaSorting=[],H.each(e.order,function(e,t){var n=[t[0],t[1]];if("string"==typeof t[0]){t=i.indexOf(t[0]);if(t<0)return;n[0]=t}else if(n[0]>=o.length)return;r.aaSorting.push(n)})),void 0!==e.search&&H.extend(r.oPreviousSearch,e.search),e.columns){var u,c=e.columns,d=b(e.columns,"name");if(d.join("").length&&d.join("")!==i.join(""))for(c=[],n=0;n<i.length;n++)""!=i[n]&&0<=(u=d.indexOf(i[n]))?c.push(e.columns[u]):c.push({});if(c.length===o.length){for(n=0,a=c.length;n<a;n++){var f=c[n];void 0!==f.visible&&(l?l.column(n).visible(f.visible,!1):o[n].bVisible=f.visible),void 0!==f.search&&H.extend(r.aoPreSearchCols[n],f.search)}l&&l.columns.adjust()}}r._bLoadingState=!1,G(r,"aoStateLoaded","stateLoaded",[r,e])}}else r._bLoadingState=!1;t()}function z(e,t,n,r){if(n="DataTables warning: "+(e?"table id="+e.sTableId+" - ":"")+n,r&&(n+=". For more information about this error, please see https://datatables.net/tn/"+r),t)W.console&&console.log&&console.log(n);else{t=V.ext,t=t.sErrMode||t.errMode;if(e&&G(e,null,"dt-error",[e,r,n],!0),"alert"==t)alert(n);else{if("throw"==t)throw new Error(n);"function"==typeof t&&t(e,r,n)}}}function $(n,r,e,t){Array.isArray(e)?H.each(e,function(e,t){Array.isArray(t)?$(n,r,t[0],t[1]):$(n,r,t)}):(void 0===t&&(t=e),void 0!==r[e]&&(n[t]=r[e]))}function tt(e,t,n){var r,a;for(a in t)Object.prototype.hasOwnProperty.call(t,a)&&(r=t[a],H.isPlainObject(r)?(H.isPlainObject(e[a])||(e[a]={}),H.extend(!0,e[a],r)):n&&"data"!==a&&"aaData"!==a&&Array.isArray(r)?e[a]=r.slice():e[a]=r);return e}function nt(e,t,n){H(e).on("click.DT",t,function(e){n(e)}).on("keypress.DT",t,function(e){13===e.which&&(e.preventDefault(),n(e))}).on("selectstart.DT",t,function(){return!1})}function Y(e,t,n){n&&e[t].push(n)}function G(t,e,n,r,a){var o=[];return e&&(o=t[e].slice().reverse().map(function(e){return e.apply(t.oInstance,r)})),null!==n&&(e=H.Event(n+".dt"),n=H(t.nTable),e.dt=t.api,n[a?"trigger":"triggerHandler"](e,r),a&&0===n.parents("body").length&&H("body").trigger(e,r),o.push(e.result)),o}function rt(e){var t=e._iDisplayStart,n=e.fnDisplayEnd(),r=e._iDisplayLength;n<=t&&(t=n-r),t-=t%r,e._iDisplayStart=t=-1===r||t<0?0:t}function at(e,t){var e=e.renderer,n=V.ext.renderer[t];return H.isPlainObject(e)&&e[t]?n[e[t]]||n._:"string"==typeof e&&n[e]||n._}function J(e){return e.oFeatures.bServerSide?"ssp":e.ajax?"ajax":"dom"}function ot(e,t,n){var r=e.fnFormatNumber,a=e._iDisplayStart+1,o=e._iDisplayLength,i=e.fnRecordsDisplay(),l=e.fnRecordsTotal(),s=-1===o;return t.replace(/_START_/g,r.call(e,a)).replace(/_END_/g,r.call(e,e.fnDisplayEnd())).replace(/_MAX_/g,r.call(e,l)).replace(/_TOTAL_/g,r.call(e,i)).replace(/_PAGE_/g,r.call(e,s?1:Math.ceil(a/o))).replace(/_PAGES_/g,r.call(e,s?1:Math.ceil(i/o))).replace(/_ENTRIES_/g,e.api.i18n("entries","",n)).replace(/_ENTRIES-MAX_/g,e.api.i18n("entries","",l)).replace(/_ENTRIES-TOTAL_/g,e.api.i18n("entries","",i))}function it(e,t){if(t)if(t.length<1e4)e.push.apply(e,t);else for(l=0;l<t.length;l++)e.push(t[l])}function lt(e,t,n){for(Array.isArray(n)||(n=[n]),l=0;l<n.length;l++)e.on(t+".dt",n[l])}var st=[],t=Array.prototype;X=function(e,t){if(!(this instanceof X))return new X(e,t);function n(e){e=e,t=V.settings,r=b(t,"nTable");var n,t,r,a=e?e.nTable&&e.oFeatures?[e]:e.nodeName&&"table"===e.nodeName.toLowerCase()?-1!==(a=r.indexOf(e))?[t[a]]:null:e&&"function"==typeof e.settings?e.settings().toArray():("string"==typeof e?n=H(e).get():e instanceof H&&(n=e.get()),n?t.filter(function(e,t){return n.includes(r[t])}):void 0):[];a&&o.push.apply(o,a)}var r,o=[];if(Array.isArray(e))for(r=0;r<e.length;r++)n(e[r]);else n(e);this.context=1<o.length?C(o):o,it(this,t),this.selector={rows:null,cols:null,opts:null},X.extend(this,this,st)},V.Api=X,H.extend(X.prototype,{any:function(){return 0!==this.count()},context:[],count:function(){return this.flatten().length},each:function(e){for(var t=0,n=this.length;t<n;t++)e.call(this,this[t],t,this);return this},eq:function(e){var t=this.context;return t.length>e?new X(t[e],this[e]):null},filter:function(e){e=t.filter.call(this,e,this);return new X(this.context,e)},flatten:function(){var e=[];return new X(this.context,e.concat.apply(e,this.toArray()))},get:function(e){return this[e]},join:t.join,includes:function(e){return-1!==this.indexOf(e)},indexOf:t.indexOf,iterator:function(e,t,n,r){var a,o,i,l,s,u,c,d,f=[],h=this.context,p=this.selector;for("string"==typeof e&&(r=n,n=t,t=e,e=!1),o=0,i=h.length;o<i;o++){var g=new X(h[o]);if("table"===t)void 0!==(a=n.call(g,h[o],o))&&f.push(a);else if("columns"===t||"rows"===t)void 0!==(a=n.call(g,h[o],this[o],o))&&f.push(a);else if("every"===t||"column"===t||"column-rows"===t||"row"===t||"cell"===t)for(c=this[o],"column-rows"===t&&(u=Dt(h[o],p.opts)),l=0,s=c.length;l<s;l++)d=c[l],void 0!==(a="cell"===t?n.call(g,h[o],d.row,d.column,o,l):n.call(g,h[o],d,o,l,u))&&f.push(a)}return f.length||r?((e=(r=new X(h,e?f.concat.apply([],f):f)).selector).rows=p.rows,e.cols=p.cols,e.opts=p.opts,r):this},lastIndexOf:t.lastIndexOf,length:0,map:function(e){e=t.map.call(this,e,this);return new X(this.context,e)},pluck:function(e){var t=V.util.get(e);return this.map(function(e){return t(e)})},pop:t.pop,push:t.push,reduce:t.reduce,reduceRight:t.reduceRight,reverse:t.reverse,selector:null,shift:t.shift,slice:function(){return new X(this.context,this)},sort:t.sort,splice:t.splice,toArray:function(){return t.slice.call(this)},to$:function(){return H(this)},toJQuery:function(){return H(this)},unique:function(){return new X(this.context,C(this.toArray()))},unshift:t.unshift}),W.__apiStruct=st,X.extend=function(e,t,n){if(n.length&&t&&(t instanceof X||t.__dt_wrapper))for(var r,a=0,o=n.length;a<o;a++)"__proto__"!==(r=n[a]).name&&(t[r.name]="function"===r.type?function(t,n,r){return function(){var e=n.apply(t||this,arguments);return X.extend(e,e,r.methodExt),e}}(e,r.val,r):"object"===r.type?{}:r.val,t[r.name].__dt_wrapper=!0,X.extend(e,t[r.name],r.propExt))},X.register=r=function(e,t){if(Array.isArray(e))for(var n=0,r=e.length;n<r;n++)X.register(e[n],t);else for(var a=e.split("."),o=st,i=0,l=a.length;i<l;i++){var s,u,c=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n].name===t)return e[n];return null}(o,u=(s=-1!==a[i].indexOf("()"))?a[i].replace("()",""):a[i]);c||o.push(c={name:u,val:{},methodExt:[],propExt:[],type:"object"}),i===l-1?(c.val=t,c.type="function"==typeof t?"function":H.isPlainObject(t)?"object":"other"):o=s?c.methodExt:c.propExt}},X.registerPlural=e=function(e,t,n){X.register(e,n),X.register(t,function(){var e=n.apply(this,arguments);return e===this?this:e instanceof X?e.length?Array.isArray(e[0])?new X(e.context,e[0]):e[0]:void 0:e})};function ut(e,t){var n,r;return Array.isArray(e)?(n=[],e.forEach(function(e){e=ut(e,t);it(n,e)}),n.filter(function(e){return e})):"number"==typeof e?[t[e]]:(r=t.map(function(e){return e.nTable}),H(r).filter(e).map(function(){var e=r.indexOf(this);return t[e]}).toArray())}function ct(a,o,e){var t,n;e&&(t=new X(a)).one("draw",function(){e(t.ajax.json())}),"ssp"==J(a)?Ce(a,o):(I(a,!0),(n=a.jqXHR)&&4!==n.readyState&&n.abort(),Fe(a,{},function(e){ve(a);for(var t=Oe(a,e),n=0,r=t.length;n<r;n++)D(a,t[n]);Ce(a,o),We(a),I(a,!1)}))}function dt(e,t,n,r,a){for(var o,i=[],l=typeof t,s=0,u=(t=t&&"string"!=l&&"function"!=l&&void 0!==t.length?t:[t]).length;s<u;s++)(o=(o=n("string"==typeof t[s]?t[s].trim():t[s])).filter(function(e){return null!=e}))&&o.length&&(i=i.concat(o));var c=T.selector[e];if(c.length)for(s=0,u=c.length;s<u;s++)i=c[s](r,a,i);return C(i)}function ft(e){return(e=e||{}).filter&&void 0===e.search&&(e.search=e.filter),H.extend({columnOrder:"implied",search:"none",order:"current",page:"all"},e)}function ht(e){var t=new X(e.context[0]);return e.length&&t.push(e[0]),t.selector=e.selector,t.length&&1<t[0].length&&t[0].splice(1),t}r("tables()",function(e){return null!=e?new X(ut(e,this.context)):this}),r("table()",function(e){var e=this.tables(e),t=e.context;return t.length?new X(t[0]):e}),[["nodes","node","nTable"],["body","body","nTBody"],["header","header","nTHead"],["footer","footer","nTFoot"]].forEach(function(t){e("tables()."+t[0]+"()","table()."+t[1]+"()",function(){return this.iterator("table",function(e){return e[t[2]]},1)})}),[["header","aoHeader"],["footer","aoFooter"]].forEach(function(t){r("table()."+t[0]+".structure()",function(e){var n=this.columns(e).indexes().flatten().toArray(),e=this.context[0],e=Te(e,e[t[1]],n),r=n.slice().sort(function(e,t){return e-t});return e.map(function(t){return n.map(function(e){return t[r.indexOf(e)]})})})}),e("tables().containers()","table().container()",function(){return this.iterator("table",function(e){return e.nTableWrapper},1)}),r("tables().every()",function(n){var r=this;return this.iterator("table",function(e,t){n.call(r.table(t),t)})}),r("caption()",function(a,o){var e,t=this.context;return void 0===a?(e=t[0].captionNode)&&t.length?e.innerHTML:null:this.iterator("table",function(e){var t=H(e.nTable),n=H(e.captionNode),r=H(e.nTableWrapper);n.length||(n=H("<caption/>").html(a),e.captionNode=n[0],o)||(t.prepend(n),o=n.css("caption-side")),n.html(a),o&&(n.css("caption-side",o),n[0]._captionSide=o),(r.find("div.dataTables_scroll").length?(e="top"===o?"Head":"Foot",r.find("div.dataTables_scroll"+e+" table")):t).prepend(n)},1)}),r("caption.node()",function(){var e=this.context;return e.length?e[0].captionNode:null}),r("draw()",function(t){return this.iterator("table",function(e){"page"===t?x(e):Ce(e,!1===(t="string"==typeof t?"full-hold"!==t:t))})}),r("page()",function(t){return void 0===t?this.page.info().page:this.iterator("table",function(e){Ve(e,t)})}),r("page.info()",function(){var e,t,n,r,a;if(0!==this.context.length)return t=(e=this.context[0])._iDisplayStart,n=e.oFeatures.bPaginate?e._iDisplayLength:-1,r=e.fnRecordsDisplay(),{page:(a=-1===n)?0:Math.floor(t/n),pages:a?1:Math.ceil(r/n),start:t,end:e.fnDisplayEnd(),length:n,recordsTotal:e.fnRecordsTotal(),recordsDisplay:r,serverSide:"ssp"===J(e)}}),r("page.len()",function(t){return void 0===t?0!==this.context.length?this.context[0]._iDisplayLength:void 0:this.iterator("table",function(e){Xe(e,t)})}),r("ajax.json()",function(){var e=this.context;if(0<e.length)return e[0].json}),r("ajax.params()",function(){var e=this.context;if(0<e.length)return e[0].oAjaxData}),r("ajax.reload()",function(t,n){return this.iterator("table",function(e){ct(e,!1===n,t)})}),r("ajax.url()",function(t){var e=this.context;return void 0===t?0===e.length?void 0:(e=e[0],H.isPlainObject(e.ajax)?e.ajax.url:e.ajax):this.iterator("table",function(e){H.isPlainObject(e.ajax)?e.ajax.url=t:e.ajax=t})}),r("ajax.url().load()",function(t,n){return this.iterator("table",function(e){ct(e,!1===n,t)})});function pt(o,i,e,t){function l(e,t){var n;if(Array.isArray(e)||e instanceof H)for(var r=0,a=e.length;r<a;r++)l(e[r],t);else e.nodeName&&"tr"===e.nodeName.toLowerCase()?(e.setAttribute("data-dt-row",i.idx),s.push(e)):(n=H("<tr><td></td></tr>").attr("data-dt-row",i.idx).addClass(t),H("td",n).addClass(t).html(e)[0].colSpan=oe(o),s.push(n[0]))}var s=[];l(e,t),i._details&&i._details.detach(),i._details=H(s),i._detailsShow&&i._details.insertAfter(i.nTr)}function gt(e,t){var n=e.context;if(n.length&&e.length){var r=n[0].aoData[e[0]];if(r._details){(r._detailsShow=t)?(r._details.insertAfter(r.nTr),H(r.nTr).addClass("dt-hasChild")):(r._details.detach(),H(r.nTr).removeClass("dt-hasChild")),G(n[0],null,"childRow",[t,e.row(e[0])]);var i=n[0],a=new X(i),r=".dt.DT_details",t="draw"+r,e="column-sizing"+r,r="destroy"+r,l=i.aoData;if(a.off(t+" "+e+" "+r),b(l,"_details").length>0){a.on(t,function(e,t){if(i!==t)return;a.rows({page:"current"}).eq(0).each(function(e){var t=l[e];if(t._detailsShow)t._details.insertAfter(t.nTr)})});a.on(e,function(e,t){if(i!==t)return;var n,r=oe(t);for(var a=0,o=l.length;a<o;a++){n=l[a];if(n&&n._details)n._details.each(function(){var e=H(this).children("td");if(e.length==1)e.attr("colspan",r)})}});a.on(r,function(e,t){if(i!==t)return;for(var n=0,r=l.length;n<r;n++)if(l[n]&&l[n]._details)wt(a,n)})}St(n)}}}function mt(e,t,n,r,a,o){for(var i=[],l=0,s=a.length;l<s;l++)i.push(q(e,a[l],t,o));return i}function vt(e,t,n){var r=e.aoHeader,e=e.titleRow,a=null;if(void 0!==n)a=n;else if(!0===e)a=0;else if(!1===e)a=r.length-1;else if(null!==e)a=e;else{for(var o=0;o<r.length;o++)r[o][t].unique&&H("span.dt-column-title",r[o][t].cell).text()&&(a=o);null===a&&(a=0)}return r[a][t].cell}var bt,yt,Dt=function(e,t){var n,r=[],a=e.aiDisplay,o=e.aiDisplayMaster,i=t.search,l=t.order,t=t.page;if("ssp"==J(e))return"removed"===i?[]:h(0,o.length);if("current"==t)for(u=e._iDisplayStart,c=e.fnDisplayEnd();u<c;u++)r.push(a[u]);else if("current"==l||"applied"==l){if("none"==i)r=o.slice();else if("applied"==i)r=a.slice();else if("removed"==i){for(var s={},u=0,c=a.length;u<c;u++)s[a[u]]=null;o.forEach(function(e){Object.prototype.hasOwnProperty.call(s,e)||r.push(e)})}}else if("index"==l||"original"==l)for(u=0,c=e.aoData.length;u<c;u++)e.aoData[u]&&("none"==i||-1===(n=a.indexOf(u))&&"removed"==i||0<=n&&"applied"==i)&&r.push(u);else if("number"==typeof l){var d=Ze(e,l,"asc");if("none"===i)r=d;else for(u=0;u<d.length;u++)(-1===(n=a.indexOf(d[u]))&&"removed"==i||0<=n&&"applied"==i)&&r.push(d[u])}return r},xt=(r("rows()",function(n,r){void 0===n?n="":H.isPlainObject(n)&&(r=n,n=""),r=ft(r);var e=this.iterator("table",function(e){return t=dt("row",t=n,function(n){var e=f(n),r=a.aoData;if(null!==e&&!o)return[e];if(i=i||Dt(a,o),null!==e&&-1!==i.indexOf(e))return[e];if(null==n||""===n)return i;if("function"==typeof n)return i.map(function(e){var t=r[e];return n(e,t._aData,t.nTr)?e:null});if(n.nodeName)return e=n._DT_RowIndex,t=n._DT_CellIndex,void 0!==e?r[e]&&r[e].nTr===n?[e]:[]:t?r[t.row]&&r[t.row].nTr===n.parentNode?[t.row]:[]:(e=H(n).closest("*[data-dt-row]")).length?[e.data("dt-row")]:[];if("string"==typeof n&&"#"===n.charAt(0)){var t=a.aIds[n.replace(/^#/,"")];if(void 0!==t)return[t.idx]}e=A(v(a.aoData,i,"nTr"));return H(e).filter(n).map(function(){return this._DT_RowIndex}).toArray()},a=e,o=r),"current"!==o.order&&"applied"!==o.order||Ge(a,t),t;var a,t,o,i},1);return e.selector.rows=n,e.selector.opts=r,e}),r("rows().nodes()",function(){return this.iterator("row",function(e,t){return e.aoData[t].nTr||void 0},1)}),r("rows().data()",function(){return this.iterator(!0,"rows",function(e,t){return v(e.aoData,t,"_aData")},1)}),e("rows().cache()","row().cache()",function(n){return this.iterator("row",function(e,t){e=e.aoData[t];return"search"===n?e._aFilterData:e._aSortData},1)}),e("rows().invalidate()","row().invalidate()",function(n){return this.iterator("row",function(e,t){be(e,t,n)})}),e("rows().indexes()","row().index()",function(){return this.iterator("row",function(e,t){return t},1)}),e("rows().ids()","row().id()",function(e){for(var t=[],n=this.context,r=0,a=n.length;r<a;r++)for(var o=0,i=this[r].length;o<i;o++){var l=n[r].rowIdFn(n[r].aoData[this[r][o]]._aData);t.push((!0===e?"#":"")+l)}return new X(n,t)}),e("rows().remove()","row().remove()",function(){return this.iterator("row",function(e,t){var n=e.aoData,r=n[t],a=e.aiDisplayMaster.indexOf(t),a=(-1!==a&&e.aiDisplayMaster.splice(a,1),0<e._iRecordsDisplay&&e._iRecordsDisplay--,rt(e),e.rowIdFn(r._aData));void 0!==a&&delete e.aIds[a],n[t]=null}),this}),r("rows.add()",function(o){var e=this.iterator("table",function(e){for(var t,n=[],r=0,a=o.length;r<a;r++)(t=o[r]).nodeName&&"TR"===t.nodeName.toUpperCase()?n.push(fe(e,t)[0]):n.push(D(e,t));return n},1),t=this.rows(-1);return t.pop(),it(t,e),t}),r("row()",function(e,t){return ht(this.rows(e,t))}),r("row().data()",function(e){var t,n=this.context;return void 0===e?n.length&&this.length&&this[0].length?n[0].aoData[this[0]]._aData:void 0:((t=n[0].aoData[this[0]])._aData=e,Array.isArray(e)&&t.nTr&&t.nTr.id&&m(n[0].rowId)(e,t.nTr.id),be(n[0],this[0],"data"),this)}),r("row().node()",function(){var e=this.context;if(e.length&&this.length&&this[0].length){e=e[0].aoData[this[0]];if(e&&e.nTr)return e.nTr}return null}),r("row.add()",function(t){t instanceof H&&t.length&&(t=t[0]);var e=this.iterator("table",function(e){return t.nodeName&&"TR"===t.nodeName.toUpperCase()?fe(e,t)[0]:D(e,t)});return this.row(e[0])}),H(S).on("plugin-init.dt",function(e,t){var r=new X(t);r.on("stateSaveParams.DT",function(e,t,n){for(var r=t.rowIdFn,a=t.aiDisplayMaster,o=[],i=0;i<a.length;i++){var l=a[i],l=t.aoData[l];l._detailsShow&&o.push("#"+r(l._aData))}n.childRows=o}),r.on("stateLoaded.DT",function(e,t,n){xt(r,n)}),xt(r,r.state.loaded())}),function(e,t){t&&t.childRows&&e.rows(t.childRows.map(function(e){return e.replace(/([^:\\]*(?:\\.[^:\\]*)*):/g,"$1\\:")})).every(function(){G(e.settings()[0],null,"requestChild",[this])})}),St=V.util.throttle(function(e){Ke(e[0])},500),wt=function(e,t){var n=e.context;n.length&&(t=n[0].aoData[void 0!==t?t:e[0]])&&t._details&&(t._details.remove(),t._detailsShow=void 0,t._details=void 0,H(t.nTr).removeClass("dt-hasChild"),St(n))},Tt="row().child",_t=Tt+"()",Ct=(r(_t,function(e,t){var n=this.context;return void 0===e?n.length&&this.length&&n[0].aoData[this[0]]?n[0].aoData[this[0]]._details:void 0:(!0===e?this.child.show():!1===e?wt(this):n.length&&this.length&&pt(n[0],n[0].aoData[this[0]],e,t),this)}),r([Tt+".show()",_t+".show()"],function(){return gt(this,!0),this}),r([Tt+".hide()",_t+".hide()"],function(){return gt(this,!1),this}),r([Tt+".remove()",_t+".remove()"],function(){return wt(this),this}),r(Tt+".isShown()",function(){var e=this.context;return e.length&&this.length&&e[0].aoData[this[0]]&&e[0].aoData[this[0]]._detailsShow||!1}),/^([^:]+)?:(name|title|visIdx|visible)$/);r("columns()",function(n,r){void 0===n?n="":H.isPlainObject(n)&&(r=n,n=""),r=ft(r);var e=this.iterator("table",function(e){return t=n,l=r,c=(i=e).aoColumns,d=function(e){for(var t=[],n=0;n<e.length;n++)for(var r=0;r<e[n].length;r++){var a=e[n][r].cell;t.includes(a)||t.push(a)}return t}(i.aoHeader),t=dt("column",t,function(n){var r,e=f(n);if(""===n)return h(c.length);if(null!==e)return[0<=e?e:c.length+e];if("function"==typeof n)return r=Dt(i,l),c.map(function(e,t){return n(t,mt(i,t,0,0,r),vt(i,t))?t:null});var t,a,o="string"==typeof n?n.match(Ct):"";if(o)switch(o[2]){case"visIdx":case"visible":return o[1]&&o[1].match(/^\d+$/)?(t=parseInt(o[1],10))<0?[(a=c.map(function(e,t){return e.bVisible?t:null}))[a.length+t]]:[re(i,t)]:c.map(function(e,t){return e.bVisible&&(!o[1]||0<H(d[t]).filter(o[1]).length)?t:null});case"name":return(s=s||b(c,"sName")).map(function(e,t){return e===o[1]?t:null});case"title":return(u=u||b(c,"sTitle")).map(function(e,t){return e===o[1]?t:null});default:return[]}return n.nodeName&&n._DT_CellIndex?[n._DT_CellIndex.column]:(e=H(d).filter(n).map(function(){return de(this)}).toArray().sort(function(e,t){return e-t})).length||!n.nodeName?e:(e=H(n).closest("*[data-dt-column]")).length?[e.data("dt-column")]:[]},i,l),l.columnOrder&&"index"===l.columnOrder?t.sort(function(e,t){return e-t}):t;var i,t,l,s,u,c,d},1);return e.selector.cols=n,e.selector.opts=r,e}),e("columns().header()","column().header()",function(n){return this.iterator("column",function(e,t){return vt(e,t,n)},1)}),e("columns().footer()","column().footer()",function(n){return this.iterator("column",function(e,t){return e.aoFooter.length?e.aoFooter[void 0!==n?n:0][t].cell:null},1)}),e("columns().data()","column().data()",function(){return this.iterator("column-rows",mt,1)}),e("columns().render()","column().render()",function(o){return this.iterator("column-rows",function(e,t,n,r,a){return mt(e,t,0,0,a,o)},1)}),e("columns().dataSrc()","column().dataSrc()",function(){return this.iterator("column",function(e,t){return e.aoColumns[t].mData},1)}),e("columns().cache()","column().cache()",function(o){return this.iterator("column-rows",function(e,t,n,r,a){return v(e.aoData,a,"search"===o?"_aFilterData":"_aSortData",t)},1)}),e("columns().init()","column().init()",function(){return this.iterator("column",function(e,t){return e.aoColumns[t]},1)}),e("columns().names()","column().name()",function(){return this.iterator("column",function(e,t){return e.aoColumns[t].sName},1)}),e("columns().nodes()","column().nodes()",function(){return this.iterator("column-rows",function(e,t,n,r,a){return v(e.aoData,a,"anCells",t)},1)}),e("columns().titles()","column().title()",function(n,r){return this.iterator("column",function(e,t){"number"==typeof n&&(r=n,n=void 0);t=H("span.dt-column-title",this.column(t).header(r));return void 0!==n?(t.html(n),this):t.html()},1)}),e("columns().types()","column().type()",function(){return this.iterator("column",function(e,t){t=e.aoColumns[t].sType;return t||se(e),t},1)}),e("columns().visible()","column().visible()",function(n,r){var t=this,a=[],e=this.iterator("column",function(e,t){if(void 0===n)return e.aoColumns[t].bVisible;!function(e,t,n){var r,a,o=e.aoColumns,i=o[t],l=e.aoData;if(void 0===n)return i.bVisible;if(i.bVisible===n)return!1;if(n)for(var s=b(o,"bVisible").indexOf(!0,t+1),u=0,c=l.length;u<c;u++)l[u]&&(a=l[u].nTr,r=l[u].anCells,a)&&a.insertBefore(r[t],r[s]||null);else H(b(e.aoData,"anCells",t)).detach();return i.bVisible=n,$e(e),!0}(e,t,n)||a.push(t)});return void 0!==n&&this.iterator("table",function(e){_e(e,e.aoHeader),_e(e,e.aoFooter),e.aiDisplay.length||H(e.nTBody).find("td[colspan]").attr("colspan",oe(e)),Ke(e),t.iterator("column",function(e,t){a.includes(t)&&G(e,null,"column-visibility",[e,t,n,r])}),a.length&&(void 0===r||r)&&t.columns.adjust()}),e}),e("columns().widths()","column().width()",function(){var e=this.columns(":visible").count(),e=H("<tr>").html("<td>"+Array(e).join("</td><td>")+"</td>"),n=(H(this.table().body()).append(e),e.children().map(function(){return H(this).outerWidth()}));return e.remove(),this.iterator("column",function(e,t){e=ae(e,t);return null!==e?n[e]:0},1)}),e("columns().indexes()","column().index()",function(n){return this.iterator("column",function(e,t){return"visible"===n?ae(e,t):t},1)}),r("columns.adjust()",function(){return this.iterator("table",function(e){e.containerWidth=-1,ne(e)},1)}),r("column.index()",function(e,t){var n;if(0!==this.context.length)return n=this.context[0],"fromVisible"===e||"toData"===e?re(n,t):"fromData"===e||"toVisible"===e?ae(n,t):void 0}),r("column()",function(e,t){return ht(this.columns(e,t))});function It(e,t){H(e).find("span.dt-column-order").remove(),H(e).find("span.dt-column-title").each(function(){var e=H(this).html();H(this).parent().parent().append(e),H(this).remove()}),H(e).find("div.dt-column-"+t).remove(),H("th, td",e).removeAttr("data-dt-column")}r("cells()",function(g,e,m){var r,a,o,i,l,s,t;return H.isPlainObject(g)&&(void 0===g.row?(m=g,g=null):(m=e,e=null)),H.isPlainObject(e)&&(m=e,e=null),null==e?this.iterator("table",function(e){return r=e,e=g,t=ft(m),d=r.aoData,f=Dt(r,t),n=A(v(d,f,"anCells")),h=H(M([],n)),p=r.aoColumns.length,dt("cell",e,function(e){var t,n="function"==typeof e;if(null==e||n){for(o=[],i=0,l=f.length;i<l;i++)for(a=f[i],s=0;s<p;s++)u={row:a,column:s},(!n||(c=d[a],e(u,q(r,a,s),c.anCells?c.anCells[s]:null)))&&o.push(u);return o}return H.isPlainObject(e)?void 0!==e.column&&void 0!==e.row&&-1!==f.indexOf(e.row)?[e]:[]:(t=h.filter(e).map(function(e,t){return{row:t._DT_CellIndex.row,column:t._DT_CellIndex.column}}).toArray()).length||!e.nodeName?t:(c=H(e).closest("*[data-dt-row]")).length?[{row:c.data("dt-row"),column:c.data("dt-column")}]:[]},r,t);var r,t,a,o,i,l,s,u,c,d,f,n,h,p}):(t=m?{page:m.page,order:m.order,search:m.search}:{},r=this.columns(e,t),a=this.rows(g,t),t=this.iterator("table",function(e,t){var n=[];for(o=0,i=a[t].length;o<i;o++)for(l=0,s=r[t].length;l<s;l++)n.push({row:a[t][o],column:r[t][l]});return n},1),t=m&&m.selected?this.cells(t,m):t,H.extend(t.selector,{cols:e,rows:g,opts:m}),t)}),e("cells().nodes()","cell().node()",function(){return this.iterator("cell",function(e,t,n){e=e.aoData[t];return e&&e.anCells?e.anCells[n]:void 0},1)}),r("cells().data()",function(){return this.iterator("cell",function(e,t,n){return q(e,t,n)},1)}),e("cells().cache()","cell().cache()",function(r){return r="search"===r?"_aFilterData":"_aSortData",this.iterator("cell",function(e,t,n){return e.aoData[t][r][n]},1)}),e("cells().render()","cell().render()",function(r){return this.iterator("cell",function(e,t,n){return q(e,t,n,r)},1)}),e("cells().indexes()","cell().index()",function(){return this.iterator("cell",function(e,t,n){return{row:t,column:n,columnVisible:ae(e,n)}},1)}),e("cells().invalidate()","cell().invalidate()",function(r){return this.iterator("cell",function(e,t,n){be(e,t,r,n)})}),r("cell()",function(e,t,n){return ht(this.cells(e,t,n))}),r("cell().data()",function(e){var t,n,r,a,o,i=this.context,l=this[0];return void 0===e?i.length&&l.length?q(i[0],l[0].row,l[0].column):void 0:(t=i[0],n=l[0].row,r=l[0].column,a=t.aoColumns[r],o=t.aoData[n]._aData,a.fnSetData(o,e,{settings:t,row:n,col:r}),be(i[0],l[0].row,"data",l[0].column),this)}),r("order()",function(n,e){var t=this.context,r=Array.prototype.slice.call(arguments);return void 0===n?0!==t.length?t[0].aaSorting:void 0:("number"==typeof n?n=[[n,e]]:1<r.length&&(n=r),this.iterator("table",function(e){var t=[];g(e,t,n),e.aaSorting=t}))}),r("order.listener()",function(t,n,r){return this.iterator("table",function(e){Ye(e,t,{},n,r)})}),r("order.fixed()",function(t){var e;return t?this.iterator("table",function(e){e.aaSortingFixed=H.extend(!0,{},t)}):(e=(e=this.context).length?e[0].aaSortingFixed:void 0,Array.isArray(e)?{pre:e}:e)}),r(["columns().order()","column().order()"],function(n){var r=this;return n?this.iterator("table",function(e,t){e.aaSorting=r[t].map(function(e){return[e,n]})}):this.iterator("column",function(e,t){for(var n=Je(e),r=0,a=n.length;r<a;r++)if(n[r].col===t)return n[r].dir;return null},1)}),e("columns().orderable()","column().orderable()",function(n){return this.iterator("column",function(e,t){e=e.aoColumns[t];return n?e.asSorting:e.bSortable},1)}),r("processing()",function(t){return this.iterator("table",function(e){I(e,t)})}),r("search()",function(t,n,r,a){var e=this.context;return void 0===t?0!==e.length?e[0].oPreviousSearch.search:void 0:this.iterator("table",function(e){e.oFeatures.bFilter&&Re(e,"object"==typeof n?H.extend(e.oPreviousSearch,n,{search:t}):H.extend(e.oPreviousSearch,{search:t,regex:null!==n&&n,smart:null===r||r,caseInsensitive:null===a||a}))})}),r("search.fixed()",function(t,n){var e=this.iterator(!0,"table",function(e){e=e.searchFixed;return t?void 0===n?e[t]:(null===n?delete e[t]:e[t]=n,this):Object.keys(e)});return void 0!==t&&void 0===n?e[0]:e}),e("columns().search()","column().search()",function(r,a,o,i){return this.iterator("column",function(e,t){var n=e.aoPreSearchCols;if(void 0===r)return n[t].search;e.oFeatures.bFilter&&("object"==typeof a?H.extend(n[t],a,{search:r}):H.extend(n[t],{search:r,regex:null!==a&&a,smart:null===o||o,caseInsensitive:null===i||i}),Re(e,e.oPreviousSearch))})}),r(["columns().search.fixed()","column().search.fixed()"],function(n,r){var e=this.iterator(!0,"column",function(e,t){e=e.aoColumns[t].searchFixed;return n?void 0===r?e[n]||null:(null===r?delete e[n]:e[n]=r,this):Object.keys(e)});return void 0!==n&&void 0===r?e[0]:e}),r("state()",function(e,t){var n;return e?(n=H.extend(!0,{},e),this.iterator("table",function(e){!1!==t&&(n.time=+new Date+100),et(e,n,function(){})})):this.context.length?this.context[0].oSavedState:null}),r("state.clear()",function(){return this.iterator("table",function(e){e.fnStateSaveCallback.call(e.oInstance,e,{})})}),r("state.loaded()",function(){return this.context.length?this.context[0].oLoadedState:null}),r("state.save()",function(){return this.iterator("table",function(e){Ke(e)})}),V.use=function(e,t){var n="string"==typeof e?t:e,t="string"==typeof t?t:e;if(void 0===n&&"string"==typeof t)switch(t){case"lib":case"jq":return H;case"win":return W;case"datetime":return V.DateTime;case"luxon":return o;case"moment":return i;case"bootstrap":return bt||W.bootstrap;case"foundation":return yt||W.Foundation;default:return null}"lib"===t||"jq"===t||n&&n.fn&&n.fn.jquery?H=n:"win"===t||n&&n.document?S=(W=n).document:"datetime"===t||n&&"DateTime"===n.type?V.DateTime=n:"luxon"===t||n&&n.FixedOffsetZone?o=n:"moment"===t||n&&n.isMoment?i=n:"bootstrap"===t||n&&n.Modal&&"modal"===n.Modal.NAME?bt=n:("foundation"===t||n&&n.Reveal)&&(yt=n)},V.factory=function(e,t){var n=!1;return e&&e.document&&(S=(W=e).document),t&&t.fn&&t.fn.jquery&&(H=t,n=!0),n},V.versionCheck=function(e,t){for(var n,r,a=(t||V.version).split("."),o=e.split("."),i=0,l=o.length;i<l;i++)if((n=parseInt(a[i],10)||0)!==(r=parseInt(o[i],10)||0))return r<n;return!0},V.isDataTable=function(e){var a=H(e).get(0),o=!1;return e instanceof V.Api||(H.each(V.settings,function(e,t){var n=t.nScrollHead?H("table",t.nScrollHead)[0]:null,r=t.nScrollFoot?H("table",t.nScrollFoot)[0]:null;t.nTable!==a&&n!==a&&r!==a||(o=!0)}),o)},V.tables=function(t){var e=!1,n=(H.isPlainObject(t)&&(e=t.api,t=t.visible),V.settings.filter(function(e){return!(t&&!H(e.nTable).is(":visible"))}).map(function(e){return e.nTable}));return e?new X(n):n},V.camelToHungarian=B,r("$()",function(e,t){t=this.rows(t).nodes(),t=H(t);return H([].concat(t.filter(e).toArray(),t.find(e).toArray()))}),H.each(["on","one","off"],function(e,n){r(n+"()",function(){var e=Array.prototype.slice.call(arguments),t=(e[0]=e[0].split(/\s/).map(function(e){return e.match(/\.dt\b/)?e:e+".dt"}).join(" "),H(this.tables().nodes()));return t[n].apply(t,e),this})}),r("clear()",function(){return this.iterator("table",function(e){ve(e)})}),r("error()",function(t){return this.iterator("table",function(e){z(e,0,t)})}),r("settings()",function(){return new X(this.context,this.context)}),r("init()",function(){var e=this.context;return e.length?e[0].oInit:null}),r("data()",function(){return this.iterator("table",function(e){return b(e.aoData,"_aData")}).flatten()}),r("trigger()",function(t,n,r){return this.iterator("table",function(e){return G(e,null,t,n,r)}).flatten()}),r("ready()",function(t){var e=this.context;return t?this.tables().every(function(){var e=this;this.context[0]._bInitComplete?t.call(e):this.on("init.dt.DT",function(){t.call(e)})}):e.length?e[0]._bInitComplete||!1:null}),r("destroy()",function(c){return c=c||!1,this.iterator("table",function(e){var t=e.oClasses,n=e.nTable,r=e.nTBody,a=e.nTHead,o=e.nTFoot,i=H(n),r=H(r),l=H(e.nTableWrapper),s=e.aoData.map(function(e){return e?e.nTr:null}),u=t.order,o=(e.bDestroying=!0,G(e,"aoDestroyCallback","destroy",[e],!0),c||new X(e).columns().visible(!0),e.resizeObserver&&e.resizeObserver.disconnect(),l.off(".DT").find(":not(tbody *)").off(".DT"),H(W).off(".DT-"+e.sInstance),n!=a.parentNode&&(i.children("thead").detach(),i.append(a)),o&&n!=o.parentNode&&(i.children("tfoot").detach(),i.append(o)),It(a,"header"),It(o,"footer"),e.colgroup.remove(),e.aaSorting=[],e.aaSortingFixed=[],Qe(e),H(i).find("th, td").removeClass(H.map(V.ext.type.className,function(e){return e}).join(" ")),H("th, td",a).removeClass(u.none+" "+u.canAsc+" "+u.canDesc+" "+u.isAsc+" "+u.isDesc).css("width","").removeAttr("aria-sort"),r.children().detach(),r.append(s),e.nTableWrapper.parentNode),a=e.nTableWrapper.nextSibling,u=c?"remove":"detach",r=(i[u](),l[u](),!c&&o&&(o.insertBefore(n,a),i.css("width",e.sDestroyWidth).removeClass(t.table)),V.settings.indexOf(e));-1!==r&&V.settings.splice(r,1)})}),H.each(["column","row","cell"],function(e,s){r(s+"s().every()",function(r){var a,o=this.selector.opts,i=this,l=0;return this.iterator("every",function(e,t,n){a=i[s](t,o),"cell"===s?r.call(a,a[0][0].row,a[0][0].column,n,l):r.call(a,t,n,l),l++})})}),r("i18n()",function(e,t,n){var r=this.context[0],e=U(e)(r.oLanguage);return"string"==typeof(e=H.isPlainObject(e=void 0===e?t:e)?void 0!==n&&void 0!==e[n]?e[n]:e._:e)?e.replace("%d",n):e}),V.version="2.3.0",V.settings=[],V.models={},V.models.oSearch={caseInsensitive:!0,search:"",regex:!1,smart:!0,return:!1},V.models.oRow={nTr:null,anCells:null,_aData:[],_aSortData:null,_aFilterData:null,_sFilterRow:null,src:null,idx:-1,displayData:null},V.models.oColumn={idx:null,aDataSort:null,asSorting:null,bSearchable:null,bSortable:null,bVisible:null,_sManualType:null,_bAttrSrc:!1,fnCreatedCell:null,fnGetData:null,fnSetData:null,mData:null,mRender:null,sClass:null,sContentPadding:null,sDefaultContent:null,sName:null,sSortDataType:"std",sSortingClass:null,sTitle:null,sType:null,sWidth:null,sWidthOrig:null,maxLenString:null,searchFixed:null},V.defaults={aaData:null,aaSorting:[[0,"asc"]],aaSortingFixed:[],ajax:null,aLengthMenu:[10,25,50,100],aoColumns:null,aoColumnDefs:null,aoSearchCols:[],bAutoWidth:!0,bDeferRender:!0,bDestroy:!1,bFilter:!0,bInfo:!0,bLengthChange:!0,bPaginate:!0,bProcessing:!1,bRetrieve:!1,bScrollCollapse:!1,bServerSide:!1,bSort:!0,bSortMulti:!0,bSortCellsTop:null,titleRow:null,bSortClasses:!0,bStateSave:!1,fnCreatedRow:null,fnDrawCallback:null,fnFooterCallback:null,fnFormatNumber:function(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,this.oLanguage.sThousands)},fnHeaderCallback:null,fnInfoCallback:null,fnInitComplete:null,fnPreDrawCallback:null,fnRowCallback:null,fnStateLoadCallback:function(e){try{return JSON.parse((-1===e.iStateDuration?sessionStorage:localStorage).getItem("DataTables_"+e.sInstance+"_"+location.pathname))}catch(e){return{}}},fnStateLoadParams:null,fnStateLoaded:null,fnStateSaveCallback:function(e,t){try{(-1===e.iStateDuration?sessionStorage:localStorage).setItem("DataTables_"+e.sInstance+"_"+location.pathname,JSON.stringify(t))}catch(e){}},fnStateSaveParams:null,iStateDuration:7200,iDisplayLength:10,iDisplayStart:0,iTabIndex:0,oClasses:{},oLanguage:{oAria:{orderable:": Activate to sort",orderableReverse:": Activate to invert sorting",orderableRemove:": Activate to remove sorting",paginate:{first:"First",last:"Last",next:"Next",previous:"Previous",number:""}},oPaginate:{sFirst:"«",sLast:"»",sNext:"›",sPrevious:"‹"},entries:{_:"entries",1:"entry"},lengthLabels:{"-1":"All"},sEmptyTable:"No data available in table",sInfo:"Showing _START_ to _END_ of _TOTAL_ _ENTRIES-TOTAL_",sInfoEmpty:"Showing 0 to 0 of 0 _ENTRIES-TOTAL_",sInfoFiltered:"(filtered from _MAX_ total _ENTRIES-MAX_)",sInfoPostFix:"",sDecimal:"",sThousands:",",sLengthMenu:"_MENU_ _ENTRIES_ per page",sLoadingRecords:"Loading...",sProcessing:"",sSearch:"Search:",sSearchPlaceholder:"",sUrl:"",sZeroRecords:"No matching records found"},orderDescReverse:!0,oSearch:H.extend({},V.models.oSearch),layout:{topStart:"pageLength",topEnd:"search",bottomStart:"info",bottomEnd:"paging"},sDom:null,searchDelay:null,sPaginationType:"",sScrollX:"",sScrollXInner:"",sScrollY:"",sServerMethod:"GET",renderer:null,rowId:"DT_RowId",caption:null,iDeferLoading:null,on:null},Z(V.defaults),V.defaults.column={aDataSort:null,iDataSort:-1,ariaTitle:"",asSorting:["asc","desc",""],bSearchable:!0,bSortable:!0,bVisible:!0,fnCreatedCell:null,mData:null,mRender:null,sCellType:"td",sClass:"",sContentPadding:"",sDefaultContent:null,sName:"",sSortDataType:"std",sTitle:null,sType:null,sWidth:null},Z(V.defaults.column),V.models.oSettings={oFeatures:{bAutoWidth:null,bDeferRender:null,bFilter:null,bInfo:!0,bLengthChange:!0,bPaginate:null,bProcessing:null,bServerSide:null,bSort:null,bSortMulti:null,bSortClasses:null,bStateSave:null},oScroll:{bCollapse:null,iBarWidth:0,sX:null,sXInner:null,sY:null},oLanguage:{fnInfoCallback:null},oBrowser:{bScrollbarLeft:!1,barWidth:0},ajax:null,aanFeatures:[],aoData:[],aiDisplay:[],aiDisplayMaster:[],aIds:{},aoColumns:[],aoHeader:[],aoFooter:[],oPreviousSearch:{},searchFixed:{},aoPreSearchCols:[],aaSorting:null,aaSortingFixed:[],sDestroyWidth:0,aoRowCallback:[],aoHeaderCallback:[],aoFooterCallback:[],aoDrawCallback:[],aoRowCreatedCallback:[],aoPreDrawCallback:[],aoInitComplete:[],aoStateSaveParams:[],aoStateLoadParams:[],aoStateLoaded:[],sTableId:"",nTable:null,nTHead:null,nTFoot:null,nTBody:null,nTableWrapper:null,bInitialised:!1,aoOpenRows:[],sDom:null,searchDelay:null,sPaginationType:"two_button",pagingControls:0,iStateDuration:0,aoStateSave:[],aoStateLoad:[],oSavedState:null,oLoadedState:null,bAjaxDataGet:!0,jqXHR:null,json:void 0,oAjaxData:void 0,sServerMethod:null,fnFormatNumber:null,aLengthMenu:null,iDraw:0,bDrawing:!1,iDrawError:-1,_iDisplayLength:10,_iDisplayStart:0,_iRecordsTotal:0,_iRecordsDisplay:0,oClasses:{},bFiltered:!1,bSorted:!1,bSortCellsTop:null,oInit:null,aoDestroyCallback:[],fnRecordsTotal:function(){return"ssp"==J(this)?+this._iRecordsTotal:this.aiDisplayMaster.length},fnRecordsDisplay:function(){return"ssp"==J(this)?+this._iRecordsDisplay:this.aiDisplay.length},fnDisplayEnd:function(){var e=this._iDisplayLength,t=this._iDisplayStart,n=t+e,r=this.aiDisplay.length,a=this.oFeatures,o=a.bPaginate;return a.bServerSide?!1===o||-1===e?t+r:Math.min(t+e,this._iRecordsDisplay):!o||r<n||-1===e?r:n},oInstance:null,sInstance:null,iTabIndex:0,nScrollHead:null,nScrollFoot:null,aLastSort:[],oPlugins:{},rowIdFn:null,rowId:null,caption:"",captionNode:null,colgroup:null,deferLoading:null,typeDetect:!0,resizeObserver:null,containerWidth:-1,orderDescReverse:null,orderIndicators:!0,orderHandler:!0,titleRow:null};function Lt(t,n){return function(e){return _(e)||"string"!=typeof e||(e=e.replace(N," "),t&&(e=w(e)),n&&(e=k(e,!1))),e}}_t=V.ext.pager;H.extend(_t,{simple:function(){return["previous","next"]},full:function(){return["first","previous","next","last"]},numbers:function(){return["numbers"]},simple_numbers:function(){return["previous","numbers","next"]},full_numbers:function(){return["first","previous","numbers","next","last"]},first_last:function(){return["first","last"]},first_last_numbers:function(){return["first","numbers","last"]},_numbers:Bt,numbers_length:7}),H.extend(!0,V.ext.renderer,{pagingButton:{_:function(e,t,n,r,a){var e=e.oClasses.paging,o=[e.button];return r&&o.push(e.active),a&&o.push(e.disabled),{display:r="ellipsis"===t?H('<span class="ellipsis"></span>').html(n)[0]:H("<button>",{class:o.join(" "),role:"link",type:"button"}).html(n),clicker:r}}},pagingContainer:{_:function(e,t){return t}}});function At(e,t,n,r,a){return i?e[t](a):o?e[n](a):r?e[r](a):e}var o,i,Nt=!1;function Ft(e,t,n){var r;if(W.luxon&&!o&&(o=W.luxon),i=W.moment&&!i?W.moment:i){if(!(r=i.utc(e,t,n,!0)).isValid())return null}else if(o){if(!(r=t&&"string"==typeof e?o.DateTime.fromFormat(e,t):o.DateTime.fromISO(e)).isValid)return null;r=r.setLocale(n)}else t?(Nt||alert("DataTables warning: Formatted date without Moment.js or Luxon - https://datatables.net/tn/17"),Nt=!0):r=new Date(e);return r}function Ot(s){return function(r,a,o,i){0===arguments.length?(o="en",r=a=null):1===arguments.length?(o="en",a=r,r=null):2===arguments.length&&(o=a,a=r,r=null);var l="datetime"+(a?"-"+a:"");return V.ext.type.order[l+"-pre"]||V.type(l,{detect:function(e){return e===l&&l},order:{pre:function(e){return e.valueOf()}},className:"dt-right"}),function(e,t){var n;return null==e&&(e="--now"===i?(n=new Date,new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds()))):""),"type"===t?l:""===e?"sort"!==t?"":Ft("0000-01-01 00:00:00",null,o):!(null===a||r!==a||"sort"===t||"type"===t||e instanceof Date)||null===(n=Ft(e,r,o))?e:"sort"===t?n:(e=null===a?At(n,"toDate","toJSDate","")[s]():At(n,"format","toFormat","toISOString",a),"display"===t?u(e):e)}}}var jt=",",Rt=".";if(void 0!==W.Intl)try{for(var Pt=(new Intl.NumberFormat).formatToParts(100000.1),l=0;l<Pt.length;l++)"group"===Pt[l].type?jt=Pt[l].value:"decimal"===Pt[l].type&&(Rt=Pt[l].value)}catch(e){}V.datetime=function(n,r){var a="datetime-"+n;r=r||"en",V.ext.type.order[a]||V.type(a,{detect:function(e){var t=Ft(e,n,r);return!(""!==e&&!t)&&a},order:{pre:function(e){return Ft(e,n,r)||0}},className:"dt-right"})},V.render={date:Ot("toLocaleDateString"),datetime:Ot("toLocaleString"),time:Ot("toLocaleTimeString"),number:function(a,o,i,l,s){return null==a&&(a=jt),null==o&&(o=Rt),{display:function(e){if("number"!=typeof e&&"string"!=typeof e)return e;if(""===e||null===e)return e;var t=e<0?"-":"",n=parseFloat(e),r=Math.abs(n);if(1e11<=r||r<1e-4&&0!==r)return(r=n.toExponential(i).split(/e\+?/))[0]+" x 10<sup>"+r[1]+"</sup>";if(isNaN(n))return u(e);n=n.toFixed(i),e=Math.abs(n);r=parseInt(e,10),n=i?o+(e-r).toFixed(i).substring(2):"";return(t=0===r&&0===parseFloat(n)?"":t)+(l||"")+r.toString().replace(/\B(?=(\d{3})+(?!\d))/g,a)+n+(s||"")}}},text:function(){return{display:u,filter:u}}};function Et(e,t){return e=null!=e?e.toString().toLowerCase():"",t=null!=t?t.toString().toLowerCase():"",e.localeCompare(t,navigator.languages[0]||navigator.language,{numeric:!0,ignorePunctuation:!0})}function kt(e,t){return e=w(e),t=w(t),Et(e,t)}var s=V.ext.type,Mt=(V.type=function(n,e,t){if(!e)return{className:s.className[n],detect:s.detect.find(function(e){return e._name===n}),order:{pre:s.order[n+"-pre"],asc:s.order[n+"-asc"],desc:s.order[n+"-desc"]},render:s.render[n],search:s.search[n]};function r(e,t){s[e][n]=t}function a(e){Object.defineProperty(e,"_name",{value:n});var t=s.detect.findIndex(function(e){return e._name===n});-1===t?s.detect.unshift(e):s.detect.splice(t,1,e)}function o(e){s.order[n+"-pre"]=e.pre,s.order[n+"-asc"]=e.asc,s.order[n+"-desc"]=e.desc}void 0===t&&(t=e,e=null),"className"===e?r("className",t):"detect"===e?a(t):"order"===e?o(t):"render"===e?r("render",t):"search"===e?r("search",t):e||(t.className&&r("className",t.className),void 0!==t.detect&&a(t.detect),t.order&&o(t.order),void 0!==t.render&&r("render",t.render),void 0!==t.search&&r("search",t.search))},V.types=function(){return s.detect.map(function(e){return e._name})},V.type("string",{detect:function(){return"string"},order:{pre:function(e){return _(e)&&"boolean"!=typeof e?"":"string"==typeof e?e.toLowerCase():e.toString?e.toString():""}},search:Lt(!1,!0)}),V.type("string-utf8",{detect:{allOf:function(e){return!0},oneOf:function(e){return!_(e)&&navigator.languages&&"string"==typeof e&&e.match(/[^\x00-\x7F]/)}},order:{asc:Et,desc:function(e,t){return-1*Et(e,t)}},search:Lt(!1,!0)}),V.type("html",{detect:{allOf:function(e){return _(e)||"string"==typeof e&&-1!==e.indexOf("<")},oneOf:function(e){return!_(e)&&"string"==typeof e&&-1!==e.indexOf("<")}},order:{pre:function(e){return _(e)?"":e.replace?w(e).trim().toLowerCase():e+""}},search:Lt(!0,!0)}),V.type("html-utf8",{detect:{allOf:function(e){return _(e)||"string"==typeof e&&-1!==e.indexOf("<")},oneOf:function(e){return navigator.languages&&!_(e)&&"string"==typeof e&&-1!==e.indexOf("<")&&"string"==typeof e&&e.match(/[^\x00-\x7F]/)}},order:{asc:kt,desc:function(e,t){return-1*kt(e,t)}},search:Lt(!0,!0)}),V.type("date",{className:"dt-type-date",detect:{allOf:function(e){var t;return!e||e instanceof Date||j.test(e)?null!==(t=Date.parse(e))&&!isNaN(t)||_(e):null},oneOf:function(e){return e instanceof Date||"string"==typeof e&&j.test(e)}},order:{pre:function(e){e=Date.parse(e);return isNaN(e)?-1/0:e}}}),V.type("html-num-fmt",{className:"dt-type-numeric",detect:{allOf:function(e,t){t=t.oLanguage.sDecimal;return n(e,t,!0,!1)},oneOf:function(e,t){t=t.oLanguage.sDecimal;return n(e,t,!0,!1)}},order:{pre:function(e,t){t=t.oLanguage.sDecimal;return Mt(e,t,F,P)}},search:Lt(!0,!0)}),V.type("html-num",{className:"dt-type-numeric",detect:{allOf:function(e,t){t=t.oLanguage.sDecimal;return n(e,t,!1,!0)},oneOf:function(e,t){t=t.oLanguage.sDecimal;return n(e,t,!1,!1)}},order:{pre:function(e,t){t=t.oLanguage.sDecimal;return Mt(e,t,F)}},search:Lt(!0,!0)}),V.type("num-fmt",{className:"dt-type-numeric",detect:{allOf:function(e,t){t=t.oLanguage.sDecimal;return c(e,t,!0,!0)},oneOf:function(e,t){t=t.oLanguage.sDecimal;return c(e,t,!0,!1)}},order:{pre:function(e,t){t=t.oLanguage.sDecimal;return Mt(e,t,P)}}}),V.type("num",{className:"dt-type-numeric",detect:{allOf:function(e,t){t=t.oLanguage.sDecimal;return c(e,t,!1,!0)},oneOf:function(e,t){t=t.oLanguage.sDecimal;return c(e,t,!1,!1)}},order:{pre:function(e,t){t=t.oLanguage.sDecimal;return Mt(e,t)}}}),function(e,t,n,r){var a;return 0===e||e&&"-"!==e?"number"==(a=typeof e)||"bigint"==a?e:+(e=(e=t?E(e,t):e).replace&&(n&&(e=e.replace(n,"")),r)?e.replace(r,""):e):-1/0});function Ht(e,t,n){n&&(e[t]=n)}H.extend(!0,V.ext.renderer,{footer:{_:function(e,t,n){t.addClass(n.tfoot.cell)}},header:{_:function(g,m,v){m.addClass(v.thead.cell),g.oFeatures.bSort||m.addClass(v.order.none);var e=g.titleRow,t=m.closest("thead").find("tr"),n=m.parent().index();"disable"===m.attr("data-dt-order")||"disable"===m.parent().attr("data-dt-order")||!0===e&&0!==n||!1===e&&n!==t.length-1||"number"==typeof e&&n!==e||H(g.nTable).on("order.dt.DT column-visibility.dt.DT",function(e,t,n){if(g===t){var r=t.sortDetails;if(r){var a=b(r,"col");if("column-visibility"!==e.type||a.includes(n)){for(var e=v.order,n=t.api.columns(m),o=g.aoColumns[n.flatten()[0]],i=n.orderable().includes(!0),l="",s=n.indexes(),u=n.orderable(!0).flatten(),c=g.iTabIndex,d=t.orderHandler&&i,f=(m.removeClass(e.isAsc+" "+e.isDesc).toggleClass(e.none,!i).toggleClass(e.canAsc,d&&u.includes("asc")).toggleClass(e.canDesc,d&&u.includes("desc")),!0),h=0;h<s.length;h++)a.includes(s[h])||(f=!1);f&&(d=n.order(),m.addClass(d.includes("asc")?e.isAsc:""+d.includes("desc")?e.isDesc:""));var p=-1;for(h=0;h<a.length;h++)if(g.aoColumns[a[h]].bVisible){p=a[h];break}s[0]==p?(u=r[0],n=o.asSorting,m.attr("aria-sort","asc"===u.dir?"ascending":"descending"),l=n[u.index+1]?"Reverse":"Remove"):m.removeAttr("aria-sort"),i&&((d=m.find(".dt-column-order")).attr("role","button").attr("aria-label",i?o.ariaTitle+t.api.i18n("oAria.orderable"+l):o.ariaTitle),-1!==c)&&d.attr("tabindex",c)}}}})}},layout:{_:function(e,t,n){var r=e.oClasses.layout,a=H("<div/>").attr("id",n.id||null).addClass(n.className||r.row).appendTo(t);V.ext.renderer.layout._forLayoutRow(n,function(e,t){var n;"id"!==e&&"className"!==e&&(n="",t.table&&(a.addClass(r.tableRow),n+=r.tableCell+" "),n+="start"===e?r.start:"end"===e?r.end:r.full,H("<div/>").attr({id:t.id||null,class:t.className||r.cell+" "+n}).append(t.contents).appendTo(a))})},_forLayoutRow:function(t,n){function r(e){switch(e){case"":return 0;case"start":return 1;case"end":return 2;default:return 3}}Object.keys(t).sort(function(e,t){return r(e)-r(t)}).forEach(function(e){n(e,t[e])})}}}),V.feature={},V.feature.register=function(e,t,n){V.ext.features[e]=t,n&&T.feature.push({cFeature:n,fnInit:t})},V.feature.register("div",function(e,t){var n=H("<div>")[0];return t&&(Ht(n,"className",t.className),Ht(n,"id",t.id),Ht(n,"innerHTML",t.html),Ht(n,"textContent",t.text)),n}),V.feature.register("info",function(e,s){var t,n,u;return e.oFeatures.bInfo?(t=e.oLanguage,n=e.sTableId,u=H("<div/>",{class:e.oClasses.info.container}),s=H.extend({callback:t.fnInfoCallback,empty:t.sInfoEmpty,postfix:t.sInfoPostFix,search:t.sInfoFiltered,text:t.sInfo},s),e.aoDrawCallback.push(function(e){var t=s,n=u,r=e._iDisplayStart+1,a=e.fnDisplayEnd(),o=e.fnRecordsTotal(),i=e.fnRecordsDisplay(),l=i?t.text:t.empty;i!==o&&(l+=" "+t.search),l+=t.postfix,l=ot(e,l),t.callback&&(l=t.callback.call(e.oInstance,e,r,a,o,i,l)),n.html(l),G(e,null,"info",[e,n[0],l])}),e._infoEl||(u.attr({"aria-live":"polite",id:n+"_info",role:"status"}),H(e.nTable).attr("aria-describedby",n+"_info"),e._infoEl=u),u):null},"i");var Wt=0;function Xt(e){var t=[];return e.numbers&&t.push("numbers"),e.previousNext&&(t.unshift("previous"),t.push("next")),e.firstLast&&(t.unshift("first"),t.push("last")),t}function Vt(e,t,n,r){var a=e.oLanguage.oPaginate,o={display:"",active:!1,disabled:!1};switch(t){case"ellipsis":o.display="&#x2026;";break;case"first":o.display=a.sFirst,0===n&&(o.disabled=!0);break;case"previous":o.display=a.sPrevious,0===n&&(o.disabled=!0);break;case"next":o.display=a.sNext,0!==r&&n!==r-1||(o.disabled=!0);break;case"last":o.display=a.sLast,0!==r&&n!==r-1||(o.disabled=!0);break;default:"number"==typeof t&&(o.display=e.fnFormatNumber(t+1),n===t)&&(o.active=!0)}return o}function Bt(e,t,n,r){var a=[],o=Math.floor(n/2),i=r?2:1,l=r?1:0;return t<=n?a=h(0,t):1===n?a=[e]:3===n?e<=1?a=[0,1,"ellipsis"]:t-2<=e?(a=h(t-2,t)).unshift("ellipsis"):a=["ellipsis",e,"ellipsis"]:e<=o?((a=h(0,n-i)).push("ellipsis"),r&&a.push(t-1)):t-1-o<=e?((a=h(t-(n-i),t)).unshift("ellipsis"),r&&a.unshift(0)):((a=h(e-o+i,e+o-l)).push("ellipsis"),a.unshift("ellipsis"),r&&(a.push(t-1),a.unshift(0))),a}V.feature.register("search",function(n,r){var e,t,a,o,i,l,s,u,c,d;return n.oFeatures.bFilter?(e=n.oClasses.search,t=n.sTableId,c=n.oLanguage,a=n.oPreviousSearch,o='<input type="search" class="'+e.input+'"/>',-1===(r=H.extend({placeholder:c.sSearchPlaceholder,processing:!1,text:c.sSearch},r)).text.indexOf("_INPUT_")&&(r.text+="_INPUT_"),r.text=ot(n,r.text),c=r.text.match(/_INPUT_$/),s=r.text.match(/^_INPUT_/),i=r.text.replace(/_INPUT_/,""),l="<label>"+r.text+"</label>",s?l="_INPUT_<label>"+i+"</label>":c&&(l="<label>"+i+"</label>_INPUT_"),(s=H("<div>").addClass(e.container).append(l.replace(/_INPUT_/,o))).find("label").attr("for","dt-search-"+Wt),s.find("input").attr("id","dt-search-"+Wt),Wt++,u=function(e){var t=this.value;a.return&&"Enter"!==e.key||t!=a.search&&Be(n,r.processing,function(){a.search=t,Re(n,a),n._iDisplayStart=0,x(n)})},c=null!==n.searchDelay?n.searchDelay:0,d=H("input",s).val(a.search).attr("placeholder",r.placeholder).on("keyup.DT search.DT input.DT paste.DT cut.DT",c?V.util.debounce(u,c):u).on("mouseup.DT",function(e){setTimeout(function(){u.call(d[0],e)},10)}).on("keypress.DT",function(e){if(13==e.keyCode)return!1}).attr("aria-controls",t),H(n.nTable).on("search.dt.DT",function(e,t){n===t&&d[0]!==S.activeElement&&d.val("function"!=typeof a.search?a.search:"")}),s):null},"f"),V.feature.register("paging",function(e,t){if(!e.oFeatures.bPaginate)return null;t=H.extend({buttons:V.ext.pager.numbers_length,type:e.sPaginationType,boundaryNumbers:!0,firstLast:!0,previousNext:!0,numbers:!0},t);function n(){!function e(t,n,r){if(!t._bInitComplete)return;var a=r.type?V.ext.pager[r.type]:Xt,o=t.oLanguage.oAria.paginate||{},i=t._iDisplayStart,l=t._iDisplayLength,s=t.fnRecordsDisplay(),u=-1===l,c=u?0:Math.ceil(i/l),d=u?1:Math.ceil(s/l),f=[],h=[],i=a(r).map(function(e){return"numbers"===e?Bt(c,d,r.buttons,r.boundaryNumbers):e});f=f.concat.apply(f,i);for(var p=0;p<f.length;p++){var g=f[p],m=Vt(t,g,c,d),v=at(t,"pagingButton")(t,g,m.display,m.active,m.disabled),b="string"==typeof g?o[g]:o.number?o.number+(g+1):null;H(v.clicker).attr({"aria-controls":t.sTableId,"aria-disabled":m.disabled?"true":null,"aria-current":m.active?"page":null,"aria-label":b,"data-dt-idx":g,tabIndex:m.disabled?-1:t.iTabIndex&&"span"!==v.clicker[0].nodeName.toLowerCase()?t.iTabIndex:null}),"number"!=typeof g&&H(v.clicker).addClass(g),nt(v.clicker,{action:g},function(e){e.preventDefault(),Ve(t,e.data.action,!0)}),h.push(v.display)}u=at(t,"pagingContainer")(t,h);s=n.find(S.activeElement).data("dt-idx");n.empty().append(u);void 0!==s&&n.find("[data-dt-idx="+s+"]").trigger("focus");h.length&&(l=H(h[0]).outerHeight(),1<r.buttons)&&0<l&&H(n).height()>=2*l-10&&e(t,n,H.extend({},r,{buttons:r.buttons-2}))}(e,r.children(),t)}var r=H("<div/>").addClass(e.oClasses.paging.container+(t.type?" paging_"+t.type:"")).append(H("<nav>").attr("aria-label","pagination").addClass(e.oClasses.paging.nav));return e.aoDrawCallback.push(n),H(e.nTable).on("column-sizing.dt.DT",n),r},"p");var qt=0;return V.feature.register("pageLength",function(r,e){var t=r.oFeatures;if(!t.bPaginate||!t.bLengthChange)return null;e=H.extend({menu:r.aLengthMenu,text:r.oLanguage.sLengthMenu},e);var t=r.oClasses.length,n=r.sTableId,a=e.menu,o=[],i=[];if(Array.isArray(a[0]))o=a[0],i=a[1];else for(p=0;p<a.length;p++)H.isPlainObject(a[p])?(o.push(a[p].value),i.push(a[p].label)):(o.push(a[p]),i.push(a[p]));for(var l=e.text.match(/_MENU_$/),s=e.text.match(/^_MENU_/),u=e.text.replace(/_MENU_/,""),e="<label>"+e.text+"</label>",s=(s?e="_MENU_<label>"+u+"</label>":l&&(e="<label>"+u+"</label>_MENU_"),"tmp-"+ +new Date),c=H("<div/>").addClass(t.container).append(e.replace("_MENU_",'<span id="'+s+'"></span>')),d=[],f=(Array.prototype.slice.call(c.find("label")[0].childNodes).forEach(function(e){e.nodeType===Node.TEXT_NODE&&d.push({el:e,text:e.textContent})}),function(t){d.forEach(function(e){e.el.textContent=ot(r,e.text,t)})}),h=H("<select/>",{"aria-controls":n,class:t.select}),p=0;p<o.length;p++){var g=r.api.i18n("lengthLabels."+o[p],null);null===g&&(g="number"==typeof i[p]?r.fnFormatNumber(i[p]):i[p]),h[0][p]=new Option(g,o[p])}return c.find("label").attr("for","dt-length-"+qt),h.attr("id","dt-length-"+qt),qt++,c.find("#"+s).replaceWith(h),H("select",c).val(r._iDisplayLength).on("change.DT",function(){Xe(r,H(this).val()),x(r)}),H(r.nTable).on("length.dt.DT",function(e,t,n){r===t&&(H("select",c).val(n),f(n))}),f(r._iDisplayLength),c},"l"),((H.fn.dataTable=V).$=H).fn.dataTableSettings=V.settings,H.fn.dataTableExt=V.ext,H.fn.DataTable=function(e){return H(this).dataTable(e).api()},H.each(V,function(e,t){H.fn.DataTable[e]=t}),V});

/*! DataTables styling integration
 * © SpryMedia Ltd - datatables.net/license
 */
!function(t){var o,d;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return t(e,window,document)}):"object"==typeof exports?(o=require("jquery"),d=function(e,n){n.fn.dataTable||require("datatables.net")(e,n)},"undefined"==typeof window?module.exports=function(e,n){return e=e||window,n=n||o(e),d(e,n),t(n,0,e.document)}:(d(window,o),module.exports=t(o,window,window.document))):t(jQuery,window,document)}(function(e,n,t){"use strict";return e.fn.dataTable});

/*! Buttons for DataTables 3.2.3
 * © SpryMedia Ltd - datatables.net/license
 */
!function(e){var o,i;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return e(t,window,document)}):"object"==typeof exports?(o=require("jquery"),i=function(t,n){n.fn.dataTable||require("datatables.net")(t,n)},"undefined"==typeof window?module.exports=function(t,n){return t=t||window,n=n||o(t),i(t,n),e(n,t,t.document)}:(i(window,o),module.exports=e(o,window,window.document))):e(jQuery,window,document)}(function(x,m,v){"use strict";var e=x.fn.dataTable,o=0,C=0,_=e.ext.buttons,i=null;function y(t,n,e){x.fn.animate?t.stop().fadeIn(n,e):(t.css("display","block"),e&&e.call(t))}function w(t,n,e){x.fn.animate?t.stop().fadeOut(n,e):(t.css("display","none"),e&&e.call(t))}function A(n,t){if(!e.versionCheck("2"))throw"Warning: Buttons requires DataTables 2 or newer";if(!(this instanceof A))return function(t){return new A(t,n).container()};!0===(t=void 0===t?{}:t)&&(t={}),Array.isArray(t)&&(t={buttons:t}),this.c=x.extend(!0,{},A.defaults,t),t.buttons&&(this.c.buttons=t.buttons),this.s={dt:new e.Api(n),buttons:[],listenKeys:"",namespace:"dtb"+o++},this.dom={container:x("<"+this.c.dom.container.tag+"/>").addClass(this.c.dom.container.className)},this._constructor()}x.extend(A.prototype,{action:function(t,n){t=this._nodeToButton(t);return void 0===n?t.conf.action:(t.conf.action=n,this)},active:function(t,n){var t=this._nodeToButton(t),e=this.c.dom.button.active,o=x(t.node);return t.inCollection&&this.c.dom.collection.button&&void 0!==this.c.dom.collection.button.active&&(e=this.c.dom.collection.button.active),void 0===n?o.hasClass(e):(o.toggleClass(e,void 0===n||n),this)},add:function(t,n,e){var o=this.s.buttons;if("string"==typeof n){for(var i=n.split("-"),s=this.s,r=0,a=i.length-1;r<a;r++)s=s.buttons[+i[r]];o=s.buttons,n=+i[i.length-1]}return this._expandButton(o,t,void 0!==t?t.split:void 0,(void 0===t||void 0===t.split||0===t.split.length)&&void 0!==s,!1,n),void 0!==e&&!0!==e||this._draw(),this},collectionRebuild:function(t,n){var e=this._nodeToButton(t);if(void 0!==n){for(var o=e.buttons.length-1;0<=o;o--)this.remove(e.buttons[o].node);for(e.conf.prefixButtons&&n.unshift.apply(n,e.conf.prefixButtons),e.conf.postfixButtons&&n.push.apply(n,e.conf.postfixButtons),o=0;o<n.length;o++){var i=n[o];this._expandButton(e.buttons,i,void 0!==i&&void 0!==i.config&&void 0!==i.config.split,!0,void 0!==i.parentConf&&void 0!==i.parentConf.split,null,i.parentConf)}}this._draw(e.collection,e.buttons)},container:function(){return this.dom.container},disable:function(t){t=this._nodeToButton(t);return(t.isSplit?x(t.node.childNodes[0]):x(t.node)).addClass(this.c.dom.button.disabled).prop("disabled",!0),t.disabled=!0,this._checkSplitEnable(),this},destroy:function(){x("body").off("keyup."+this.s.namespace);for(var t=this.s.buttons.slice(),n=0,e=t.length;n<e;n++)this.remove(t[n].node);this.dom.container.remove();var o=this.s.dt.settings()[0];for(n=0,e=o.length;n<e;n++)if(o.inst===this){o.splice(n,1);break}return this},enable:function(t,n){return!1===n?this.disable(t):(((n=this._nodeToButton(t)).isSplit?x(n.node.childNodes[0]):x(n.node)).removeClass(this.c.dom.button.disabled).prop("disabled",!1),n.disabled=!1,this._checkSplitEnable(),this)},index:function(t,n,e){n||(n="",e=this.s.buttons);for(var o=0,i=e.length;o<i;o++){var s=e[o].buttons;if(e[o].node===t)return n+o;if(s&&s.length){s=this.index(t,o+"-",s);if(null!==s)return s}}return null},name:function(){return this.c.name},node:function(t){return t?(t=this._nodeToButton(t),x(t.node)):this.dom.container},processing:function(t,n){var e=this.s.dt,o=this._nodeToButton(t);return void 0===n?x(o.node).hasClass("processing"):(x(o.node).toggleClass("processing",n),x(e.table().node()).triggerHandler("buttons-processing.dt",[n,e.button(t),e,x(t),o.conf]),this)},remove:function(t){var n=this._nodeToButton(t),e=this._nodeToHost(t),o=this.s.dt;if(n.buttons.length)for(var i=n.buttons.length-1;0<=i;i--)this.remove(n.buttons[i].node);n.conf.destroying=!0,n.conf.destroy&&n.conf.destroy.call(o.button(t),o,x(t),n.conf),this._removeKey(n.conf),x(n.node).remove(),n.inserter&&x(n.inserter).remove();o=x.inArray(n,e);return e.splice(o,1),this},text:function(t,n){function e(t){return"function"==typeof t?t(i,s,o.conf):t}var o=this._nodeToButton(t),t=o.textNode,i=this.s.dt,s=x(o.node);return void 0===n?e(o.conf.text):(o.conf.text=n,t.html(e(n)),this)},_constructor:function(){var e=this,t=this.s.dt,o=t.settings()[0],n=this.c.buttons;o._buttons||(o._buttons=[]),o._buttons.push({inst:this,name:this.c.name});for(var i=0,s=n.length;i<s;i++)this.add(n[i]);t.on("destroy",function(t,n){n===o&&e.destroy()}),x("body").on("keyup."+this.s.namespace,function(t){var n;v.activeElement&&v.activeElement!==v.body||(n=String.fromCharCode(t.keyCode).toLowerCase(),-1!==e.s.listenKeys.toLowerCase().indexOf(n)&&e._keypress(n,t))})},_addKey:function(t){t.key&&(this.s.listenKeys+=(x.isPlainObject(t.key)?t.key:t).key)},_draw:function(t,n){t||(t=this.dom.container,n=this.s.buttons),t.children().detach();for(var e=0,o=n.length;e<o;e++)t.append(n[e].inserter),t.append(" "),n[e].buttons&&n[e].buttons.length&&this._draw(n[e].collection,n[e].buttons)},_expandButton:function(t,n,e,o,i,s,r){for(var a,l=this.s.dt,c=this.c.dom.collection,u=Array.isArray(n)?n:[n],d=0,f=(u=void 0===n?Array.isArray(e)?e:[e]:u).length;d<f;d++){var p=this._resolveExtends(u[d]);if(p)if(a=!(!p.config||!p.config.split),Array.isArray(p))this._expandButton(t,p,void 0!==h&&void 0!==h.conf?h.conf.split:void 0,o,void 0!==r&&void 0!==r.split,s,r);else{var h=this._buildButton(p,o,void 0!==p.split||void 0!==p.config&&void 0!==p.config.split,i);if(h){if(null!=s?(t.splice(s,0,h),s++):t.push(h),h.conf.dropIcon&&!h.conf.split&&x(h.node).addClass(this.c.dom.button.dropClass).append(this.c.dom.button.dropHtml),h.conf.buttons&&(h.collection=x("<"+c.container.content.tag+"/>"),h.conf._collection=h.collection,this._expandButton(h.buttons,h.conf.buttons,h.conf.split,!a,a,s,h.conf)),h.conf.split){h.collection=x("<"+c.container.tag+"/>"),h.conf._collection=h.collection;for(var b=0;b<h.conf.split.length;b++){var g=h.conf.split[b];"object"==typeof g&&(g.parent=r,void 0===g.collectionLayout&&(g.collectionLayout=h.conf.collectionLayout),void 0===g.dropup&&(g.dropup=h.conf.dropup),void 0===g.fade)&&(g.fade=h.conf.fade)}this._expandButton(h.buttons,h.conf.buttons,h.conf.split,!a,a,s,h.conf)}h.conf.parent=r,p.init&&p.init.call(l.button(h.node),l,x(h.node),p)}}}},_buildButton:function(n,t,e,o){function i(t){return"function"==typeof t?t(f,c,n):t}var s,r,a,l,c,u=this,d=this.c.dom,f=this.s.dt,p=x.extend(!0,{},d.button);if(t&&e&&d.collection.split?x.extend(!0,p,d.collection.split.action):o||t?x.extend(!0,p,d.collection.button):e&&x.extend(!0,p,d.split.button),n.spacer)return d=x("<"+p.spacer.tag+"/>").addClass("dt-button-spacer "+n.style+" "+p.spacer.className).html(i(n.text)),{conf:n,node:d,nodeChild:null,inserter:d,buttons:[],inCollection:t,isSplit:e,collection:null,textNode:d};if(n.available&&!n.available(f,n)&&!n.html)return!1;n.html?c=x(n.html):(r=function(t,n,e,o,i){o.action.call(n.button(e),t,n,e,o,i),x(n.table().node()).triggerHandler("buttons-action.dt",[n.button(e),n,e,o])},a=function(t,n,e,o){o.async?(u.processing(e[0],!0),setTimeout(function(){r(t,n,e,o,function(){u.processing(e[0],!1)})},o.async)):r(t,n,e,o,function(){})},d=n.tag||p.tag,l=void 0===n.clickBlurs||n.clickBlurs,c=x("<"+d+"/>").addClass(p.className).attr("tabindex",this.s.dt.settings()[0].iTabIndex).attr("aria-controls",this.s.dt.table().node().id).on("click.dtb",function(t){t.preventDefault(),!c.hasClass(p.disabled)&&n.action&&a(t,f,c,n),l&&c.trigger("blur")}).on("keypress.dtb",function(t){13===t.keyCode&&(t.preventDefault(),!c.hasClass(p.disabled))&&n.action&&a(t,f,c,n)}),"a"===d.toLowerCase()&&c.attr("href","#"),"button"===d.toLowerCase()&&c.attr("type","button"),s=p.liner.tag?(d=x("<"+p.liner.tag+"/>").html(i(n.text)).addClass(p.liner.className),"a"===p.liner.tag.toLowerCase()&&d.attr("href","#"),c.append(d),d):(c.html(i(n.text)),c),!1===n.enabled&&c.addClass(p.disabled),n.className&&c.addClass(n.className),n.titleAttr&&c.attr("title",i(n.titleAttr)),n.attr&&c.attr(n.attr),n.namespace||(n.namespace=".dt-button-"+C++),void 0!==n.config&&n.config.split&&(n.split=n.config.split));var h,b,g,m,v,d=this.c.dom.buttonContainer,d=d&&d.tag?x("<"+d.tag+"/>").addClass(d.className).append(c):c,y=(this._addKey(n),this.c.buttonCreated&&(d=this.c.buttonCreated(n,d)),e&&(y=(h=t?x.extend(!0,this.c.dom.split,this.c.dom.collection.split):this.c.dom.split).wrapper,b=x("<"+y.tag+"/>").addClass(y.className).append(c),g=x.extend(n,{autoClose:!0,align:h.dropdown.align,attr:{"aria-haspopup":"dialog","aria-expanded":!1},className:h.dropdown.className,closeButton:!1,splitAlignClass:h.dropdown.splitAlignClass,text:h.dropdown.text}),this._addKey(g),m=function(t,n,e,o){_.split.action.call(n.button(b),t,n,e,o),x(n.table().node()).triggerHandler("buttons-action.dt",[n.button(e),n,e,o]),e.attr("aria-expanded",!0)},v=x('<button class="'+h.dropdown.className+' dt-button"></button>').html(this.c.dom.button.dropHtml).addClass(this.c.dom.button.dropClass).on("click.dtb",function(t){t.preventDefault(),t.stopPropagation(),v.hasClass(p.disabled)||m(t,f,v,g),l&&v.trigger("blur")}).on("keypress.dtb",function(t){13===t.keyCode&&(t.preventDefault(),v.hasClass(p.disabled)||m(t,f,v,g))}),0===n.split.length&&v.addClass("dtb-hide-drop"),b.append(v).attr(g.attr)),(e?b:c).get(0));return{conf:n,node:y,nodeChild:y&&y.children&&y.children.length?y.children[0]:null,inserter:e?b:d,buttons:[],inCollection:t,isSplit:e,inSplit:o,collection:null,textNode:s}},_checkSplitEnable:function(t){t=t||this.s.buttons;for(var n=0;n<t.length;n++){var e,o=t[n];o.isSplit?(e=o.node.childNodes[1],(this._checkAnyEnabled(o.buttons)?x(e).removeClass(this.c.dom.button.disabled):x(e).addClass(this.c.dom.button.disabled)).prop("disabled",!1)):o.isCollection&&this._checkSplitEnable(o.buttons)}},_checkAnyEnabled:function(t){for(var n=0;n<t.length;n++)if(!t[n].disabled)return!0;return!1},_nodeToButton:function(t,n){for(var e=0,o=(n=n||this.s.buttons).length;e<o;e++){if(n[e].node===t||n[e].nodeChild===t)return n[e];if(n[e].buttons.length){var i=this._nodeToButton(t,n[e].buttons);if(i)return i}}},_nodeToHost:function(t,n){for(var e=0,o=(n=n||this.s.buttons).length;e<o;e++){if(n[e].node===t)return n;if(n[e].buttons.length){var i=this._nodeToHost(t,n[e].buttons);if(i)return i}}},_keypress:function(s,r){var a;r._buttonsHandled||(a=function(t){for(var n,e,o=0,i=t.length;o<i;o++)n=t[o].conf,e=t[o].node,!n.key||n.key!==s&&(!x.isPlainObject(n.key)||n.key.key!==s||n.key.shiftKey&&!r.shiftKey||n.key.altKey&&!r.altKey||n.key.ctrlKey&&!r.ctrlKey||n.key.metaKey&&!r.metaKey)||(r._buttonsHandled=!0,x(e).click()),t[o].buttons.length&&a(t[o].buttons)})(this.s.buttons)},_removeKey:function(t){var n;t.key&&(t=(x.isPlainObject(t.key)?t.key:t).key,n=this.s.listenKeys.split(""),t=x.inArray(t,n),n.splice(t,1),this.s.listenKeys=n.join(""))},_resolveExtends:function(e){function t(t){for(var n=0;!x.isPlainObject(t)&&!Array.isArray(t);){if(void 0===t)return;if("function"==typeof t){if(!(t=t.call(i,s,e)))return!1}else if("string"==typeof t){if(!_[t])return{html:t};t=_[t]}if(30<++n)throw"Buttons: Too many iterations"}return Array.isArray(t)?t:x.extend({},t)}var n,o,i=this,s=this.s.dt;for(e=t(e);e&&e.extend;){if(!_[e.extend])throw"Cannot extend unknown button type: "+e.extend;var r=t(_[e.extend]);if(Array.isArray(r))return r;if(!r)return!1;var a=r.className;void 0!==e.config&&void 0!==r.config&&(e.config=x.extend({},r.config,e.config)),e=x.extend({},r,e),a&&e.className!==a&&(e.className=a+" "+e.className),e.extend=r.extend}var l=e.postfixButtons;if(l)for(e.buttons||(e.buttons=[]),n=0,o=l.length;n<o;n++)e.buttons.push(l[n]);var c=e.prefixButtons;if(c)for(e.buttons||(e.buttons=[]),n=0,o=c.length;n<o;n++)e.buttons.splice(n,0,c[n]);return e},_popover:function(o,t,n){function i(){p=!0,w(x(b),h.fade,function(){x(this).detach()}),x(d.buttons('[aria-haspopup="dialog"][aria-expanded="true"]').nodes()).attr("aria-expanded","false"),x("div.dt-button-background").off("click.dtb-collection"),A.background(!1,h.backgroundClassName,h.fade,g),x(m).off("resize.resize.dtb-collection"),x("body").off(".dtb-collection"),d.off("buttons-action.b-internal"),d.off("destroy"),x("body").trigger("buttons-popover-hide.dt")}var e,s,r,a,l,c,u,d=t,f=this.c,p=!1,h=x.extend({align:"button-left",autoClose:!1,background:!0,backgroundClassName:"dt-button-background",closeButton:!0,containerClassName:f.dom.collection.container.className,contentClassName:f.dom.collection.container.content.className,collectionLayout:"",collectionTitle:"",dropup:!1,fade:400,popoverTitle:"",rightAlignClassName:"dt-button-right",tag:f.dom.collection.container.tag},n),b=h.tag+"."+h.containerClassName.replace(/ /g,"."),f=t.node(),g=h.collectionLayout.includes("fixed")?x("body"):t.node();!1===o?i():((n=x(d.buttons('[aria-haspopup="dialog"][aria-expanded="true"]').nodes())).length&&(g.closest(b).length&&(g=n.eq(0)),i()),h.sort&&((n=x("button",o).map(function(t,n){return{text:x(n).text(),el:n}}).toArray()).sort(function(t,n){return t.text.localeCompare(n.text)}),x(o).append(n.map(function(t){return t.el}))),n=x(".dt-button",o).length,a="",3===n?a="dtb-b3":2===n?a="dtb-b2":1===n&&(a="dtb-b1"),e=x("<"+h.tag+"/>").addClass(h.containerClassName).addClass(h.collectionLayout).addClass(h.splitAlignClass).addClass(a).css("display","none").attr({"aria-modal":!0,role:"dialog"}),o=x(o).addClass(h.contentClassName).attr("role","menu").appendTo(e),f.attr("aria-expanded","true"),g.parents("body")[0]!==v.body&&(g=x(v.body).children("div, section, p").last()),h.popoverTitle?e.prepend('<div class="dt-button-collection-title">'+h.popoverTitle+"</div>"):h.collectionTitle&&e.prepend('<div class="dt-button-collection-title">'+h.collectionTitle+"</div>"),h.closeButton&&e.prepend('<div class="dtb-popover-close">&times;</div>').addClass("dtb-collection-closeable"),y(e.insertAfter(g),h.fade),n=x(t.table().container()),a=e.css("position"),"container"!==h.span&&"dt-container"!==h.align||(g=g.parent(),e.css("width",n.width())),"absolute"===a?(f=x(g[0].offsetParent),t=g.position(),n=g.offset(),a=f.offset(),s=f.position(),r=m.getComputedStyle(f[0]),a.height=f.outerHeight(),a.width=f.width()+parseFloat(r.paddingLeft),a.right=a.left+a.width,a.bottom=a.top+a.height,a=t.top+g.outerHeight(),l=t.left,e.css({top:a,left:l}),r=m.getComputedStyle(e[0]),(c=e.offset()).height=e.outerHeight(),c.width=e.outerWidth(),c.right=c.left+c.width,c.bottom=c.top+c.height,c.marginTop=parseFloat(r.marginTop),c.marginBottom=parseFloat(r.marginBottom),h.dropup&&(a=t.top-c.height-c.marginTop-c.marginBottom),"button-right"!==h.align&&!e.hasClass(h.rightAlignClassName)||(l=t.left-c.width+g.outerWidth()),"dt-container"!==h.align&&"container"!==h.align||l<t.left&&(l=-t.left),s.left+l+c.width>x(m).width()&&(l=x(m).width()-c.width-s.left),n.left+l<0&&(l=-n.left),s.top+a+c.height>x(m).height()+x(m).scrollTop()&&(a=t.top-c.height-c.marginTop-c.marginBottom),f.offset().top+a<x(m).scrollTop()&&(a=t.top+g.outerHeight()),e.css({top:a,left:l})):((u=function(){var t=x(m).height()/2,n=e.height()/2;e.css("marginTop",-1*(n=t<n?t:n))})(),x(m).on("resize.dtb-collection",function(){u()})),h.background&&A.background(!0,h.backgroundClassName,h.fade,h.backgroundHost||g),x("div.dt-button-background").on("click.dtb-collection",function(){}),h.autoClose&&setTimeout(function(){d.on("buttons-action.b-internal",function(t,n,e,o){o[0]!==g[0]&&i()})},0),x(e).trigger("buttons-popover.dt"),d.on("destroy",i),setTimeout(function(){p=!1,x("body").on("click.dtb-collection",function(t){var n,e;!p&&(n=x.fn.addBack?"addBack":"andSelf",e=x(t.target).parent()[0],!x(t.target).parents()[n]().filter(o).length&&!x(e).hasClass("dt-buttons")||x(t.target).hasClass("dt-button-background"))&&i()}).on("keyup.dtb-collection",function(t){27===t.keyCode&&i()}).on("keydown.dtb-collection",function(t){var n=x("a, button",o),e=v.activeElement;9===t.keyCode&&(-1===n.index(e)?(n.first().focus(),t.preventDefault()):t.shiftKey?e===n[0]&&(n.last().focus(),t.preventDefault()):e===n.last()[0]&&(n.first().focus(),t.preventDefault()))})},0))}}),A.background=function(t,n,e,o){void 0===e&&(e=400),o=o||v.body,t?y(x("<div/>").addClass(n).css("display","none").insertAfter(o),e):w(x("div."+n),e,function(){x(this).removeClass(n).remove()})},A.instanceSelector=function(t,s){var r,a,l;return null==t?x.map(s,function(t){return t.inst}):(r=[],a=x.map(s,function(t){return t.name}),(l=function(t){var n;if(Array.isArray(t))for(var e=0,o=t.length;e<o;e++)l(t[e]);else if("string"==typeof t)-1!==t.indexOf(",")?l(t.split(",")):-1!==(n=x.inArray(t.trim(),a))&&r.push(s[n].inst);else if("number"==typeof t)r.push(s[t].inst);else if("object"==typeof t&&t.nodeName)for(var i=0;i<s.length;i++)s[i].inst.dom.container[0]===t&&r.push(s[i].inst);else"object"==typeof t&&r.push(t)})(t),r)},A.buttonSelector=function(t,n){for(var c=[],u=function(t,n,e){for(var o,i,s=0,r=n.length;s<r;s++)(o=n[s])&&(t.push({node:o.node,name:o.conf.name,idx:i=void 0!==e?e+s:s+""}),o.buttons)&&u(t,o.buttons,i+"-")},d=function(t,n){var e=[],o=(u(e,n.s.buttons),x.map(e,function(t){return t.node}));if(Array.isArray(t)||t instanceof x)for(s=0,r=t.length;s<r;s++)d(t[s],n);else if(null==t||"*"===t)for(s=0,r=e.length;s<r;s++)c.push({inst:n,node:e[s].node});else if("number"==typeof t)n.s.buttons[t]&&c.push({inst:n,node:n.s.buttons[t].node});else if("string"==typeof t)if(-1!==t.indexOf(","))for(var i=t.split(","),s=0,r=i.length;s<r;s++)d(i[s].trim(),n);else if(t.match(/^\d+(\-\d+)*$/)){var a=x.map(e,function(t){return t.idx});c.push({inst:n,node:e[x.inArray(t,a)].node})}else if(-1!==t.indexOf(":name")){var l=t.replace(":name","");for(s=0,r=e.length;s<r;s++)e[s].name===l&&c.push({inst:n,node:e[s].node})}else x(o).filter(t).each(function(){c.push({inst:n,node:this})});else"object"==typeof t&&t.nodeName&&-1!==(a=x.inArray(t,o))&&c.push({inst:n,node:o[a]})},e=0,o=t.length;e<o;e++){var i=t[e];d(n,i)}return c},A.stripData=function(t,n){return"string"==typeof(t=null!==t&&"object"==typeof t&&t.nodeName&&t.nodeType?t.innerHTML:t)&&(t=A.stripHtmlScript(t),t=A.stripHtmlComments(t),n&&!n.stripHtml||(t=e.util.stripHtml(t)),n&&!n.trim||(t=t.trim()),n&&!n.stripNewlines||(t=t.replace(/\n/g," ")),n&&!n.decodeEntities||(t=i?i(t):(c.innerHTML=t,c.value)),!n||n.escapeExcelFormula)&&t.match(/^[=+\-@\t\r]/)&&(console.log("matching and updateing"),t="'"+t),t},A.entityDecoder=function(t){i=t},A.stripHtmlComments=function(t){for(var n;(t=(n=t).replace(/(<!--.*?--!?>)|(<!--[\S\s]+?--!?>)|(<!--[\S\s]*?$)/g,""))!==n;);return t},A.stripHtmlScript=function(t){for(var n;(t=(n=t).replace(/<script\b[^<]*(?:(?!<\/script[^>]*>)<[^<]*)*<\/script[^>]*>/gi,""))!==n;);return t},A.defaults={buttons:["copy","excel","csv","pdf","print"],name:"main",tabIndex:0,dom:{container:{tag:"div",className:"dt-buttons"},collection:{container:{className:"dt-button-collection",content:{className:"",tag:"div"},tag:"div"}},button:{tag:"button",className:"dt-button",active:"dt-button-active",disabled:"disabled",spacer:{className:"dt-button-spacer",tag:"span"},liner:{tag:"span",className:""},dropClass:"",dropHtml:'<span class="dt-button-down-arrow">&#x25BC;</span>'},split:{action:{className:"dt-button-split-drop-button dt-button",tag:"button"},dropdown:{align:"split-right",className:"dt-button-split-drop",splitAlignClass:"dt-button-split-left",tag:"button"},wrapper:{className:"dt-button-split",tag:"div"}}}},x.extend(_,{collection:{text:function(t){return t.i18n("buttons.collection","Collection")},className:"buttons-collection",closeButton:!(A.version="3.2.3"),dropIcon:!0,init:function(t,n){n.attr("aria-expanded",!1)},action:function(t,n,e,o){o._collection.parents("body").length?this.popover(!1,o):this.popover(o._collection,o),"keypress"===t.type&&x("a, button",o._collection).eq(0).focus()},attr:{"aria-haspopup":"dialog"}},split:{text:function(t){return t.i18n("buttons.split","Split")},className:"buttons-split",closeButton:!1,init:function(t,n){return n.attr("aria-expanded",!1)},action:function(t,n,e,o){this.popover(o._collection,o)},attr:{"aria-haspopup":"dialog"}},copy:function(){if(_.copyHtml5)return"copyHtml5"},csv:function(t,n){if(_.csvHtml5&&_.csvHtml5.available(t,n))return"csvHtml5"},excel:function(t,n){if(_.excelHtml5&&_.excelHtml5.available(t,n))return"excelHtml5"},pdf:function(t,n){if(_.pdfHtml5&&_.pdfHtml5.available(t,n))return"pdfHtml5"},pageLength:function(t){var n=t.settings()[0].aLengthMenu,e=[],o=[];if(Array.isArray(n[0]))e=n[0],o=n[1];else for(var i=0;i<n.length;i++){var s=n[i];x.isPlainObject(s)?(e.push(s.value),o.push(s.label)):(e.push(s),o.push(s))}return{extend:"collection",text:function(t){return t.i18n("buttons.pageLength",{"-1":"Show all rows",_:"Show %d rows"},t.page.len())},className:"buttons-page-length",autoClose:!0,buttons:x.map(e,function(s,t){return{text:o[t],className:"button-page-length",action:function(t,n){n.page.len(s).draw()},init:function(t,n,e){function o(){i.active(t.page.len()===s)}var i=this;t.on("length.dt"+e.namespace,o),o()},destroy:function(t,n,e){t.off("length.dt"+e.namespace)}}}),init:function(t,n,e){var o=this;t.on("length.dt"+e.namespace,function(){o.text(e.text)})},destroy:function(t,n,e){t.off("length.dt"+e.namespace)}}},spacer:{style:"empty",spacer:!0,text:function(t){return t.i18n("buttons.spacer","")}}}),e.Api.register("buttons()",function(n,e){void 0===e&&(e=n,n=void 0),this.selector.buttonGroup=n;var t=this.iterator(!0,"table",function(t){if(t._buttons)return A.buttonSelector(A.instanceSelector(n,t._buttons),e)},!0);return t._groupSelector=n,t}),e.Api.register("button()",function(t,n){t=this.buttons(t,n);return 1<t.length&&t.splice(1,t.length),t}),e.Api.registerPlural("buttons().active()","button().active()",function(n){return void 0===n?this.map(function(t){return t.inst.active(t.node)}):this.each(function(t){t.inst.active(t.node,n)})}),e.Api.registerPlural("buttons().action()","button().action()",function(n){return void 0===n?this.map(function(t){return t.inst.action(t.node)}):this.each(function(t){t.inst.action(t.node,n)})}),e.Api.registerPlural("buttons().collectionRebuild()","button().collectionRebuild()",function(e){return this.each(function(t){for(var n=0;n<e.length;n++)"object"==typeof e[n]&&(e[n].parentConf=t);t.inst.collectionRebuild(t.node,e)})}),e.Api.register(["buttons().enable()","button().enable()"],function(n){return this.each(function(t){t.inst.enable(t.node,n)})}),e.Api.register(["buttons().disable()","button().disable()"],function(){return this.each(function(t){t.inst.disable(t.node)})}),e.Api.register("button().index()",function(){var n=null;return this.each(function(t){t=t.inst.index(t.node);null!==t&&(n=t)}),n}),e.Api.registerPlural("buttons().nodes()","button().node()",function(){var n=x();return x(this.each(function(t){n=n.add(t.inst.node(t.node))})),n}),e.Api.registerPlural("buttons().processing()","button().processing()",function(n){return void 0===n?this.map(function(t){return t.inst.processing(t.node)}):this.each(function(t){t.inst.processing(t.node,n)})}),e.Api.registerPlural("buttons().text()","button().text()",function(n){return void 0===n?this.map(function(t){return t.inst.text(t.node)}):this.each(function(t){t.inst.text(t.node,n)})}),e.Api.registerPlural("buttons().trigger()","button().trigger()",function(){return this.each(function(t){t.inst.node(t.node).trigger("click")})}),e.Api.register("button().popover()",function(n,e){return this.map(function(t){return t.inst._popover(n,this.button(this[0].node),e)})}),e.Api.register("buttons().containers()",function(){var i=x(),s=this._groupSelector;return this.iterator(!0,"table",function(t){if(t._buttons)for(var n=A.instanceSelector(s,t._buttons),e=0,o=n.length;e<o;e++)i=i.add(n[e].container())}),i}),e.Api.register("buttons().container()",function(){return this.containers().eq(0)}),e.Api.register("button().add()",function(t,n,e){var o=this.context;return o.length&&(o=A.instanceSelector(this._groupSelector,o[0]._buttons)).length&&o[0].add(n,t,e),this.button(this._groupSelector,t)}),e.Api.register("buttons().destroy()",function(){return this.pluck("inst").unique().each(function(t){t.destroy()}),this}),e.Api.registerPlural("buttons().remove()","buttons().remove()",function(){return this.each(function(t){t.inst.remove(t.node)}),this}),e.Api.register("buttons.info()",function(t,n,e){var o=this;return!1===t?(this.off("destroy.btn-info"),w(x("#datatables_buttons_info"),400,function(){x(this).remove()}),clearTimeout(s),s=null):(s&&clearTimeout(s),x("#datatables_buttons_info").length&&x("#datatables_buttons_info").remove(),t=t?"<h2>"+t+"</h2>":"",y(x('<div id="datatables_buttons_info" class="dt-button-info"/>').html(t).append(x("<div/>")["string"==typeof n?"html":"append"](n)).css("display","none").appendTo("body")),void 0!==e&&0!==e&&(s=setTimeout(function(){o.buttons.info(!1)},e)),this.on("destroy.btn-info",function(){o.buttons.info(!1)})),this}),e.Api.register("buttons.exportData()",function(t){if(this.context.length)return u(new e.Api(this.context[0]),t)}),e.Api.register("buttons.exportInfo()",function(t){return{filename:n(t=t||{},this),title:a(t,this),messageTop:l(this,t,t.message||t.messageTop,"top"),messageBottom:l(this,t,t.messageBottom,"bottom")}});var s,n=function(t,n){var e;return null==(e="function"==typeof(e="*"===t.filename&&"*"!==t.title&&void 0!==t.title&&null!==t.title&&""!==t.title?t.title:t.filename)?e(t,n):e)?null:(e=(e=-1!==e.indexOf("*")?e.replace(/\*/g,x("head > title").text()).trim():e).replace(/[^a-zA-Z0-9_\u00A1-\uFFFF\.,\-_ !\(\)]/g,""))+(r(t.extension,t,n)||"")},r=function(t,n,e){return null==t?null:"function"==typeof t?t(n,e):t},a=function(t,n){t=r(t.title,t,n);return null===t?null:-1!==t.indexOf("*")?t.replace(/\*/g,x("head > title").text()||"Exported data"):t},l=function(t,n,e,o){e=r(e,n,t);return null===e?null:(n=x("caption",t.table().container()).eq(0),"*"===e?n.css("caption-side")!==o?null:n.length?n.text():"":e)},c=x("<textarea/>")[0],u=function(i,t){for(var s=x.extend(!0,{},{rows:null,columns:"",modifier:{search:"applied",order:"applied"},orthogonal:"display",stripHtml:!0,stripNewlines:!0,decodeEntities:!0,escapeExcelFormula:!1,trim:!0,format:{header:function(t){return A.stripData(t,s)},footer:function(t){return A.stripData(t,s)},body:function(t){return A.stripData(t,s)}},customizeData:null,customizeZip:null},t),t=i.columns(s.columns).indexes().map(function(t){var n=i.column(t);return s.format.header(n.title(),t,n.header())}).toArray(),n=i.table().footer()?i.columns(s.columns).indexes().map(function(t){var n,e=i.column(t).footer(),o="";return e&&(o=((n=x(".dt-column-title",e)).length?n:x(e)).html()),s.format.footer(o,t,e)}).toArray():null,e=x.extend({},s.modifier),o=(i.select&&"function"==typeof i.select.info&&void 0===e.selected&&i.rows(s.rows,x.extend({selected:!0},e)).any()&&x.extend(e,{selected:!0}),i.rows(s.rows,e).indexes().toArray()),o=i.cells(o,s.columns,{order:e.order}),r=o.render(s.orthogonal).toArray(),a=o.nodes().toArray(),l=o.indexes().toArray(),c=i.columns(s.columns).count(),u=[],d=0,f=0,p=0<c?r.length/c:0;f<p;f++){for(var h=[c],b=0;b<c;b++)h[b]=s.format.body(r[d],l[d].row,l[d].column,a[d]),d++;u[f]=h}e={header:t,headerStructure:g(s.format.header,i.table().header.structure(s.columns)),footer:n,footerStructure:g(s.format.footer,i.table().footer.structure(s.columns)),body:u};return s.customizeData&&s.customizeData(e),e};function g(t,n){for(var e=0;e<n.length;e++)for(var o=0;o<n[e].length;o++){var i=n[e][o];i&&(i.title=t(i.title,o,i.cell))}return n}function t(t,n){t=new e.Api(t),n=n||t.init().buttons||e.defaults.buttons;return new A(t,n).container()}return x.fn.dataTable.Buttons=A,x.fn.DataTable.Buttons=A,x(v).on("init.dt plugin-init.dt",function(t,n){"dt"===t.namespace&&(t=n.oInit.buttons||e.defaults.buttons)&&!n._buttons&&new A(n,t).container()}),e.ext.feature.push({fnInit:t,cFeature:"B"}),e.feature&&e.feature.register("buttons",t),e});

/*! DataTables styling wrapper for Buttons
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var o,d;"function"==typeof define&&define.amd?define(["jquery","datatables.net-dt","datatables.net-buttons"],function(e){return n(e,window,document)}):"object"==typeof exports?(o=require("jquery"),d=function(e,t){t.fn.dataTable||require("datatables.net-dt")(e,t),t.fn.dataTable.Buttons||require("datatables.net-buttons")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||o(e),d(e,t),n(t,0,e.document)}:(d(window,o),module.exports=n(o,window,window.document))):n(jQuery,window,document)}(function(e,t,n){"use strict";return e.fn.dataTable});

/*! ColReorder 2.1.0
 * © SpryMedia Ltd - datatables.net/license
 */
!function(r){var o,n;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return r(t,window,document)}):"object"==typeof exports?(o=require("jquery"),n=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||o(t),n(t,e),r(e,0,t.document)}:(n(window,o),module.exports=r(o,window,window.document))):r(jQuery,window,document)}(function(d,t,h){"use strict";var n=d.fn.dataTable;function f(t,e,r,o){var n=t.splice(e,r);n.unshift(0),n.unshift(o<e?o:o-r+1),t.splice.apply(t,n)}function a(t){t.rows().invalidate("data"),t.column(0).visible(t.column(0).visible()),t.columns.adjust();var e=t.colReorder.order();t.trigger("columns-reordered",[{order:e,mapping:p(e)}])}function s(t){return t.settings()[0].aoColumns.map(function(t){return t._crOriginalIdx})}function g(t,e,r,o){for(var n=[],i=0;i<t.length;i++){var s=t[i];f(s,r[0],r.length,o);for(var a=0;a<s.length;a++){var l,c=s[a].cell;n.includes(c)||(l=c.getAttribute("data-dt-column").split(",").map(function(t){return e[t]}).join(","),c.setAttribute("data-dt-column",l),n.push(c))}}}function r(t){t.columns().iterator("column",function(t,e){t=t.aoColumns;void 0===t[e]._crOriginalIdx&&(t[e]._crOriginalIdx=e)})}function p(t){for(var e=[],r=0;r<t.length;r++)e[t[r]]=r;return e}function l(t,e,r){var o,n=t.settings()[0],i=n.aoColumns,s=i.map(function(t,e){return e});if(!e.includes(r)){f(s,e[0],e.length,r);var a=p(s);for(f(i,e[0],e.length,r),o=0;o<n.aoData.length;o++){var l=n.aoData[o];if(l){var c=l.anCells;if(c)for(f(c,e[0],e.length,r),d=0;d<c.length;d++)l.nTr&&c[d]&&i[d].bVisible&&l.nTr.appendChild(c[d]),c[d]&&c[d]._DT_CellIndex&&(c[d]._DT_CellIndex.column=d)}}for(o=0;o<i.length;o++){for(var u=i[o],d=0;d<u.aDataSort.length;d++)u.aDataSort[d]=a[u.aDataSort[d]];u.idx=a[u.idx],u.bVisible&&n.colgroup.append(u.colEl)}g(n.aoHeader,a,e,r),g(n.aoFooter,a,e,r),f(n.aoPreSearchCols,e[0],e.length,r),m(a,n.aaSorting),Array.isArray(n.aaSortingFixed)?m(a,n.aaSortingFixed):(n.aaSortingFixed.pre||n.aaSortingFixed.post)&&m(a,n.aaSortingFixed.pre),n.aLastSort.forEach(function(t){t.src=a[t.src]}),t.trigger("column-reorder",[t.settings()[0],{from:e,to:r,mapping:a}])}}function m(t,e){if(e)for(var r=0;r<e.length;r++){var o=e[r];"number"==typeof o?e[r]=t[o]:d.isPlainObject(o)&&void 0!==o.idx?o.idx=t[o.idx]:Array.isArray(o)&&"number"==typeof o[0]&&(o[0]=t[o[0]])}}function c(t,e,r){var o=!1;if(e.length!==t.columns().count())t.error("ColReorder - column count mismatch");else{for(var n=p(e=r?u(t,e,"toCurrent"):e),i=0;i<n.length;i++){var s=n.indexOf(i);i!==s&&(f(n,s,1,i),l(t,[s],i),o=!0)}o&&a(t)}}function u(t,e,r){var o=t.colReorder.order(),n=t.settings()[0].aoColumns;return"toCurrent"===r||"fromOriginal"===r?Array.isArray(e)?e.map(function(t){return o.indexOf(t)}):o.indexOf(e):Array.isArray(e)?e.map(function(t){return n[t]._crOriginalIdx}):n[e]._crOriginalIdx}function v(t,e,r){var o=t.columns().count();return!(e[0]<r&&r<e[e.length]||e[0]<0&&e[e.length-1]>o||r<0&&o<r||!e.includes(r)&&(!i(t.table().header.structure(),e,r)||!i(t.table().footer.structure(),e,r)))}function i(t,e,r){for(var o=function(t){for(var e=[],r=0;r<t.length;r++){e.push([]);for(var o=0;o<t[r].length;o++){var n=t[r][o];if(n)for(var i=0;i<n.rowspan;i++){e[r+i]||(e[r+i]=[]);for(var s=0;s<n.colspan;s++)e[r+i][o+s]=n.cell}}}return e}(t),n=0;n<o.length;n++)f(o[n],e[0],e.length,r);for(n=0;n<o.length;n++)for(var i=[],s=0;s<o[n].length;s++){var a=o[n][s];if(i.includes(a)){if(i[i.length-1]!==a)return}else i.push(a)}return 1}y.prototype.disable=function(){return this.c.enable=!1,this},y.prototype.enable=function(t){return!1===(t=void 0===t?!0:t)?this.disable():(this.c.enable=!0,this)},y.prototype._addListener=function(t){var r=this;d(t).on("selectstart.colReorder",function(){return!1}).on("mousedown.colReorder touchstart.colReorder",function(t){var e;"mousedown"===t.type&&1!==t.which||!r.c.enable||(e=d("button.dtcc-button_reorder",this)).length&&t.target!==e[0]&&0===e.find(t.target).length||r._mouseDown(t,this)})},y.prototype._createDragNode=function(){var t=this.s.mouse.target,e=t.parent(),r=e.parent(),o=r.parent(),n=t.clone();this.dom.drag=d(o[0].cloneNode(!1)).addClass("dtcr-cloned").append(d(r[0].cloneNode(!1)).append(d(e[0].cloneNode(!1)).append(n[0]))).css({position:"absolute",top:0,left:0,width:d(t).outerWidth(),height:d(t).outerHeight()}).appendTo("body")},y.prototype._cursorPosition=function(t,e){return(-1!==t.type.indexOf("touch")?t.originalEvent.touches[0]:t)[e]},y.prototype._mouseDown=function(t,e){for(var r=this,o=d(t.target).closest("th, td"),n=o.offset(),i=this.dt.columns(this.c.columns).indexes().toArray(),s=d(e).attr("data-dt-column").split(",").map(function(t){return parseInt(t,10)}),a=0;a<s.length;a++)if(!i.includes(s[a]))return!1;this.s.mouse.start.x=this._cursorPosition(t,"pageX"),this.s.mouse.start.y=this._cursorPosition(t,"pageY"),this.s.mouse.offset.x=this._cursorPosition(t,"pageX")-n.left,this.s.mouse.offset.y=this._cursorPosition(t,"pageY")-n.top,this.s.mouse.target=o,this.s.mouse.targets=s;for(var l=0;l<s.length;l++){var c=this.dt.cells(null,s[l],{page:"current"}).nodes().to$(),u="dtcr-moving";0===l&&(u+=" dtcr-moving-first"),l===s.length-1&&(u+=" dtcr-moving-last"),c.addClass(u)}this._regions(s),this._scrollRegions(),d(h).on("mousemove.colReorder touchmove.colReorder",function(t){r._mouseMove(t)}).on("mouseup.colReorder touchend.colReorder",function(t){r._mouseUp(t)})},y.prototype._mouseMove=function(t){if(null===this.dom.drag){if(Math.pow(Math.pow(this._cursorPosition(t,"pageX")-this.s.mouse.start.x,2)+Math.pow(this._cursorPosition(t,"pageY")-this.s.mouse.start.y,2),.5)<5)return;d(h.body).addClass("dtcr-dragging"),this._createDragNode()}this.dom.drag.css({left:this._cursorPosition(t,"pageX")-this.s.mouse.offset.x,top:this._cursorPosition(t,"pageY")-this.s.mouse.offset.y});var e,r=this.dt.table().node(),o=d(r).offset().left,o=this._cursorPosition(t,"pageX")-o,r=(e=this._isRtl()?r.clientWidth-o:o,this.s.dropZones.find(function(t){return t.inlineStart<=e&&e<=t.inlineStart+t.width}));this.s.mouse.absLeft=this._cursorPosition(t,"pageX"),r&&!r.self&&this._move(r,e)},y.prototype._mouseUp=function(t){var e=this;d(h).off(".colReorder"),d(h.body).removeClass("dtcr-dragging"),this.dom.drag&&(this.dom.drag.remove(),this.dom.drag=null,this.s.mouse.target.on("click.dtcr",function(t){return!1}),setTimeout(function(){e.s.mouse.target.off(".dtcr")},10)),this.s.scrollInterval&&clearInterval(this.s.scrollInterval),this.dt.cells(".dtcr-moving").nodes().to$().removeClass("dtcr-moving dtcr-moving-first dtcr-moving-last")},y.prototype._move=function(t,e){var r,o,n=this,i=(this.dt.colReorder.move(this.s.mouse.targets,t.colIdx),this.s.mouse.targets=d(this.s.mouse.target).attr("data-dt-column").split(",").map(function(t){return parseInt(t,10)}),this._regions(this.s.mouse.targets),this.s.mouse.targets.filter(function(t){return n.dt.column(t).visible()})),t=this.s.dropZones.find(function(t){return t.colIdx===i[0]}),s=this.s.dropZones.indexOf(t);t.inlineStart>e&&(o=t.inlineStart-e,r=this.s.dropZones[s-1],t.inlineStart-=o,t.width+=o,r)&&(r.width-=o),(t=this.s.dropZones.find(function(t){return t.colIdx===i[i.length-1]})).inlineStart+t.width<e&&(r=e-(t.inlineStart+t.width),o=this.s.dropZones[s+1],t.width+=r,o)&&(o.inlineStart+=r,o.width-=r)},y.prototype._regions=function(n){var i=this,s=[],a=0,l=0,c=this.dt.columns(this.c.columns).indexes().toArray(),u=this.dt.columns().widths();this.dt.columns().every(function(t,e,r){var o;this.visible()&&(o=u[t],c.includes(t)&&(v(i.dt,n,t)?s.push({colIdx:t,inlineStart:a-l,self:n[0]<=t&&t<=n[n.length-1],width:o+l}):t<n[0]?s.length&&(s[s.length-1].width+=o):t>n[n.length-1]&&(l+=o)),a+=o)}),this.s.dropZones=s},y.prototype._isScrolling=function(){return this.dt.table().body().parentNode!==this.dt.table().header().parentNode},y.prototype._scrollRegions=function(){var e,r,o,n;this._isScrolling()&&(r=d((e=this).dt.table().container()).offset().left,o=d(this.dt.table().container()).outerWidth(),n=this.dt.table().body().parentElement.parentElement,this.s.scrollInterval=setInterval(function(){var t=e.s.mouse.absLeft;-1!==t&&(t<r+75&&n.scrollLeft?n.scrollLeft-=5:r+o-75<t&&n.scrollLeft<n.scrollWidth&&(n.scrollLeft+=5))},25))},y.prototype._isRtl=function(){return"rtl"===d(this.dt.table()).css("direction")},y.defaults={columns:"",enable:!0,headerRows:null,order:null},y.version="2.1.0";
/*! ColReorder 2.1.0
 * © SpryMedia Ltd - datatables.net/license
 */var b=y;function y(o,n){this.dom={drag:null},this.c={columns:null,enable:null,headerRows:null,order:null},this.s={dropZones:[],mouse:{absLeft:-1,offset:{x:-1,y:-1},start:{x:-1,y:-1},target:null,targets:[]},scrollInterval:null};var t,e,i=this;o.settings()[0]._colReorder||((o.settings()[0]._colReorder=this).dt=o,d.extend(this.c,y.defaults,n),r(o),o.on("stateSaveParams",function(t,e,r){r.colReorder=s(o)}),o.on("destroy",function(){o.off(".colReorder"),o.colReorder.reset()}),t=o.state.loaded(),e=this.c.order,(e=t&&t.colReorder?t.colReorder:e)&&o.ready(function(){c(o,e,!0)}),o.table().header.structure().forEach(function(t,e){for(var r=n.headerRows,o=0;o<t.length;o++)r&&!r.includes(e)||t[o]&&t[o].cell&&i._addListener(t[o].cell)}))}return n.Api.register("colReorder.enable()",function(e){return this.iterator("table",function(t){t._colReorder&&t._colReorder.enable(e)})}),n.Api.register("colReorder.disable()",function(){return this.iterator("table",function(t){t._colReorder&&t._colReorder.disable()})}),n.Api.register("colReorder.move()",function(t,e){return r(this),v(this,t=Array.isArray(t)?t:[t],e)?this.tables().every(function(){l(this,t,e),a(this)}):(this.error("ColReorder - invalid move"),this)}),n.Api.register("colReorder.order()",function(t,e){return r(this),t?this.tables().every(function(){c(this,t,e)}):this.context.length?s(this):null}),n.Api.register("colReorder.reset()",function(){return r(this),this.tables().every(function(){var t=this.columns().every(function(t){return t}).flatten().toArray();c(this,t,!0)})}),n.Api.register("colReorder.transpose()",function(t,e){return r(this),u(this,t,e=e||"toCurrent")}),n.ColReorder=b,d(h).on("stateLoadInit.dt",function(t,e,r){if("dt"===t.namespace){t=new n.Api(e);if(r.colReorder)if(t.ready())c(t,r.colReorder,!0);else if(m(r.colReorder,r.order),r.columns){for(var o=0;o<r.columns.length;o++)r.columns[o]._cr_sort=r.colReorder[o];r.columns.sort(function(t,e){return t._cr_sort-e._cr_sort})}}}),d(h).on("preInit.dt",function(t,e){var r,o;"dt"===t.namespace&&(t=e.oInit.colReorder,o=n.defaults.colReorder,t||o)&&(r=d.extend({},o,t),!1!==t)&&(o=new n.Api(e),new b(o,r))}),n});

/*! FixedColumns 5.0.4
 * © SpryMedia Ltd - datatables.net/license
 */
!function(s){var i,o;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return s(t,window,document)}):"object"==typeof exports?(i=require("jquery"),o=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||i(t),o(t,e),s(e,0,t.document)}:(o(window,i),module.exports=s(i,window,window.document))):s(jQuery,window,document)}(function(o,t,e){"use strict";var h,l,s,i,n=o.fn.dataTable;function d(t,e){var s,i=this;if(l&&l.versionCheck&&l.versionCheck("2"))return t=new l.Api(t),this.classes=h.extend(!0,{},d.classes),this.c=h.extend(!0,{},d.defaults,e),this.s={dt:t,rtl:"rtl"===h(t.table().node()).css("direction")},e&&void 0!==e.leftColumns&&(e.left=e.leftColumns),e&&void 0!==e.left&&(this.c[this.s.rtl?"end":"start"]=e.left),e&&void 0!==e.rightColumns&&(e.right=e.rightColumns),e&&void 0!==e.right&&(this.c[this.s.rtl?"start":"end"]=e.right),this.dom={bottomBlocker:h("<div>").addClass(this.classes.bottomBlocker),topBlocker:h("<div>").addClass(this.classes.topBlocker),scroller:h("div.dt-scroll-body",this.s.dt.table().container())},this.s.dt.settings()[0]._bInitComplete?(this._addStyles(),this._setKeyTableListener()):t.one("init.dt.dtfc",function(){i._addStyles(),i._setKeyTableListener()}),t.on("column-sizing.dt.dtfc column-reorder.dt.dtfc draw.dt.dtfc",function(){return i._addStyles()}),s=l.util.debounce(function(){i._addStyles()},50),t.on("column-visibility.dt.dtfc",function(){s()}),this.dom.scroller.on("scroll.dtfc",function(){return i._scroll()}),this._scroll(),t.settings()[0]._fixedColumns=this,t.on("destroy",function(){return i._destroy()}),this;throw new Error("FixedColumns requires DataTables 2 or newer")}function r(t,e){void 0===e&&(e=null);t=new n.Api(t),e=e||t.init().fixedColumns||n.defaults.fixedColumns;new s(t,e)}return d.prototype.end=function(t){return void 0!==t?(0<=t&&t<=this.s.dt.columns().count()&&(this.c.end=t,this._addStyles()),this):this.c.end},d.prototype.left=function(t){return this.s.rtl?this.end(t):this.start(t)},d.prototype.right=function(t){return this.s.rtl?this.start(t):this.end(t)},d.prototype.start=function(t){return void 0!==t?(0<=t&&t<=this.s.dt.columns().count()&&(this.c.start=t,this._addStyles()),this):this.c.start},d.prototype._addStyles=function(){var s=this.s.dt,i=this,o=this.s.dt.columns(":visible").count(),l=s.table().header.structure(":visible"),n=s.table().footer.structure(":visible"),d=s.columns(":visible").widths().toArray(),t=h(s.table().node()).closest("div.dt-scroll"),e=h(s.table().node()).closest("div.dt-scroll-body")[0],r=this.s.rtl,c=this.c.start,a=this.c.end,f=r?a:c,r=r?c:a,u=s.settings()[0].oBrowser.barWidth;if(0===t.length)return this;e.offsetWidth===e.clientWidth&&(u=0),s.columns().every(function(t){var e,t=s.column.index("toVisible",t);null!==t&&(t<c?(e=i._sum(d,t),i._fixColumn(t,e,"start",l,n,u)):o-a<=t?(e=i._sum(d,o-t-1,!0),i._fixColumn(t,e,"end",l,n,u)):i._fixColumn(t,0,"none",l,n,u))}),h(s.table().node()).toggleClass(i.classes.tableFixedStart,0<c).toggleClass(i.classes.tableFixedEnd,0<a).toggleClass(i.classes.tableFixedLeft,0<f).toggleClass(i.classes.tableFixedRight,0<r);e=s.table().header(),f=s.table().footer(),r=h(e).outerHeight(),e=h(f).outerHeight();this.dom.topBlocker.appendTo(t).css("top",0).css(this.s.rtl?"left":"right",0).css("height",r).css("width",u+1).css("display",u?"block":"none"),f&&this.dom.bottomBlocker.appendTo(t).css("bottom",0).css(this.s.rtl?"left":"right",0).css("height",e).css("width",u+1).css("display",u?"block":"none")},d.prototype._destroy=function(){this.s.dt.off(".dtfc"),this.dom.scroller.off(".dtfc"),h(this.s.dt.table().node()).removeClass(this.classes.tableScrollingEnd+" "+this.classes.tableScrollingLeft+" "+this.classes.tableScrollingStart+" "+this.classes.tableScrollingRight),this.dom.bottomBlocker.remove(),this.dom.topBlocker.remove()},d.prototype._fixColumn=function(e,o,l,t,s,n){function i(t,e){var s,i;"none"===l?t.css("position","").css("left","").css("right","").removeClass(d.classes.fixedEnd+" "+d.classes.fixedLeft+" "+d.classes.fixedRight+" "+d.classes.fixedStart):(s="start"===l?"left":"right",d.s.rtl&&(s="start"===l?"right":"left"),i=o,"end"!==l||"header"!==e&&"footer"!==e||(i+=n),t.css("position","sticky").css(s,i).addClass("start"===l?d.classes.fixedStart:d.classes.fixedEnd).addClass("left"===s?d.classes.fixedLeft:d.classes.fixedRight))}var d=this,r=this.s.dt;t.forEach(function(t){t[e]&&i(h(t[e].cell),"header")}),i(r.column(e+":visible",{page:"current"}).nodes().to$(),"body"),s&&s.forEach(function(t){t[e]&&i(h(t[e].cell),"footer")})},d.prototype._scroll=function(){var t,e,s,i,o=this.dom.scroller[0];o&&(t=h(this.s.dt.table().node()).add(this.s.dt.table().header().parentNode).add(this.s.dt.table().footer().parentNode).add("div.dt-scroll-headInner table",this.s.dt.table().container()).add("div.dt-scroll-footInner table",this.s.dt.table().container()),e=o.scrollLeft,s=!this.s.rtl,i=0!==e,o=o.scrollWidth>o.clientWidth+Math.abs(e)+1,t.toggleClass(this.classes.tableScrollingStart,i),t.toggleClass(this.classes.tableScrollingEnd,o),t.toggleClass(this.classes.tableScrollingLeft,i&&s||o&&!s),t.toggleClass(this.classes.tableScrollingRight,o&&s||i&&!s))},d.prototype._setKeyTableListener=function(){var c=this;this.s.dt.on("key-focus.dt.dtfc",function(t,e,s){var i,o,l,n=h(s.node()).offset(),d=c.dom.scroller[0],r=h(h(c.s.dt.table().node()).closest("div.dt-scroll-body"));0<c.c.start&&(l=(o=h(c.s.dt.column(c.c.start-1).header())).offset(),o=o.outerWidth(),h(s.node()).hasClass(c.classes.fixedLeft)?r.scrollLeft(0):n.left<l.left+o&&(i=r.scrollLeft(),r.scrollLeft(i-(l.left+o-n.left)))),0<c.c.end&&(l=c.s.dt.columns().data().toArray().length,o=h(s.node()).outerWidth(),l=h(c.s.dt.column(l-c.c.end).header()).offset(),h(s.node()).hasClass(c.classes.fixedRight)?r.scrollLeft(d.scrollWidth-d.clientWidth):n.left+o>l.left&&(i=r.scrollLeft(),r.scrollLeft(i-(l.left-(n.left+o)))))})},d.prototype._sum=function(t,e,s){return(t=(s=void 0===s?!1:s)?t.slice().reverse():t).slice(0,e).reduce(function(t,e){return t+e},0)},d.version="5.0.4",d.classes={bottomBlocker:"dtfc-bottom-blocker",fixedEnd:"dtfc-fixed-end",fixedLeft:"dtfc-fixed-left",fixedRight:"dtfc-fixed-right",fixedStart:"dtfc-fixed-start",tableFixedEnd:"dtfc-has-end",tableFixedLeft:"dtfc-has-left",tableFixedRight:"dtfc-has-right",tableFixedStart:"dtfc-has-start",tableScrollingEnd:"dtfc-scrolling-end",tableScrollingLeft:"dtfc-scrolling-left",tableScrollingRight:"dtfc-scrolling-right",tableScrollingStart:"dtfc-scrolling-start",topBlocker:"dtfc-top-blocker"},d.defaults={i18n:{button:"FixedColumns"},start:1,end:0},s=d,l=(h=o).fn.dataTable,o.fn.dataTable.FixedColumns=s,o.fn.DataTable.FixedColumns=s,(i=n.Api.register)("fixedColumns()",function(){return this}),i("fixedColumns().start()",function(t){var e=this.context[0];return void 0!==t?(e._fixedColumns.start(t),this):e._fixedColumns.start()}),i("fixedColumns().end()",function(t){var e=this.context[0];return void 0!==t?(e._fixedColumns.end(t),this):e._fixedColumns.end()}),i("fixedColumns().left()",function(t){var e=this.context[0];return void 0!==t?(e._fixedColumns.left(t),this):e._fixedColumns.left()}),i("fixedColumns().right()",function(t){var e=this.context[0];return void 0!==t?(e._fixedColumns.right(t),this):e._fixedColumns.right()}),n.ext.buttons.fixedColumns={action:function(t,e,s,i){o(s).attr("active")?(o(s).removeAttr("active").removeClass("active"),e.fixedColumns().start(0),e.fixedColumns().end(0)):(o(s).attr("active","true").addClass("active"),e.fixedColumns().start(i.config.start),e.fixedColumns().end(i.config.end))},config:{start:1,end:0},init:function(t,e,s){void 0===t.settings()[0]._fixedColumns&&r(t.settings(),s),o(e).attr("active","true").addClass("active"),t.button(e).text(s.text||t.i18n("buttons.fixedColumns",t.settings()[0]._fixedColumns.c.i18n.button))},text:null},o(e).on("plugin-init.dt",function(t,e){"dt"!==t.namespace||!e.oInit.fixedColumns&&!n.defaults.fixedColumns||e._fixedColumns||r(e,null)}),n});

/*! FixedHeader 4.0.1
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var i,s;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return o(t,window,document)}):"object"==typeof exports?(i=require("jquery"),s=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"==typeof window?module.exports=function(t,e){return t=t||window,e=e||i(t),s(t,e),o(e,t,t.document)}:(s(window,i),module.exports=o(i,window,window.document))):o(jQuery,window,document)}(function(b,m,v){"use strict";function s(t,e){if(!d.versionCheck("2"))throw"Warning: FixedHeader requires DataTables 2 or newer";if(!(this instanceof s))throw"FixedHeader must be initialised with the 'new' keyword.";if(!0===e&&(e={}),t=new d.Api(t),this.c=b.extend(!0,{},s.defaults,e),this.s={dt:t,position:{theadTop:0,tbodyTop:0,tfootTop:0,tfootBottom:0,width:0,left:0,tfootHeight:0,theadHeight:0,windowHeight:b(m).height(),visible:!0},headerMode:null,footerMode:null,autoWidth:t.settings()[0].oFeatures.bAutoWidth,namespace:".dtfc"+o++,scrollLeft:{header:-1,footer:-1},enable:!0,autoDisable:!1},this.dom={floatingHeader:null,thead:b(t.table().header()),tbody:b(t.table().body()),tfoot:b(t.table().footer()),header:{host:null,floating:null,floatingParent:b('<div class="dtfh-floatingparent"><div></div></div>'),placeholder:null},footer:{host:null,floating:null,floatingParent:b('<div class="dtfh-floatingparent"><div></div></div>'),placeholder:null}},this.dom.header.host=this.dom.thead.parent(),this.dom.footer.host=this.dom.tfoot.parent(),(e=t.settings()[0])._fixedHeader)throw"FixedHeader already initialised on table "+e.nTable.id;(e._fixedHeader=this)._constructor()}var d=b.fn.dataTable,o=0;return b.extend(s.prototype,{destroy:function(){var t=this.dom;this.s.dt.off(".dtfc"),b(m).off(this.s.namespace),t.header.rightBlocker&&t.header.rightBlocker.remove(),t.header.leftBlocker&&t.header.leftBlocker.remove(),t.footer.rightBlocker&&t.footer.rightBlocker.remove(),t.footer.leftBlocker&&t.footer.leftBlocker.remove(),this.c.header&&this._modeChange("in-place","header",!0),this.c.footer&&t.tfoot.length&&this._modeChange("in-place","footer",!0)},enable:function(t,e,o){this.s.enable=t,this.s.enableType=o,!e&&void 0!==e||(this._positions(),this._scroll(!0))},enabled:function(){return this.s.enable},headerOffset:function(t){return void 0!==t&&(this.c.headerOffset=t,this.update()),this.c.headerOffset},footerOffset:function(t){return void 0!==t&&(this.c.footerOffset=t,this.update()),this.c.footerOffset},update:function(t){var e=this.s.dt.table().node();(this.s.enable||this.s.autoDisable)&&(b(e).is(":visible")?(this.s.autoDisable=!1,this.enable(!0,!1)):(this.s.autoDisable=!0,this.enable(!1,!1)),0!==b(e).children("thead").length)&&(this._positions(),this._scroll(void 0===t||t),this._widths(this.dom.header),this._widths(this.dom.footer))},_constructor:function(){var o=this,i=this.s.dt,t=(b(m).on("scroll"+this.s.namespace,function(){o._scroll()}).on("resize"+this.s.namespace,d.util.throttle(function(){o.s.position.windowHeight=b(m).height(),o.update()},50)),b(".fh-fixedHeader")),t=(!this.c.headerOffset&&t.length&&(this.c.headerOffset=t.outerHeight()),b(".fh-fixedFooter"));!this.c.footerOffset&&t.length&&(this.c.footerOffset=t.outerHeight()),i.on("column-reorder.dt.dtfc column-visibility.dt.dtfc column-sizing.dt.dtfc responsive-display.dt.dtfc",function(t,e){o.update()}).on("draw.dt.dtfc",function(t,e){o.update(e!==i.settings()[0])}),i.on("destroy.dtfc",function(){o.destroy()}),this._positions(),this._scroll()},_clone:function(t,e){var o,i,s=this,d=this.s.dt,n=this.dom[t],r="header"===t?this.dom.thead:this.dom.tfoot;"footer"===t&&this._scrollEnabled()||(!e&&n.floating?n.floating.removeClass("fixedHeader-floating fixedHeader-locked"):(n.floating&&(null!==n.placeholder&&n.placeholder.remove(),n.floating.children().detach(),n.floating.remove()),e=b(d.table().node()),o=b(e.parent()),i=this._scrollEnabled(),n.floating=b(d.table().node().cloneNode(!1)).attr("aria-hidden","true").css({top:0,left:0}).removeAttr("id"),n.floatingParent.css({width:o[0].offsetWidth,overflow:"hidden",height:"fit-content",position:"fixed",left:i?e.offset().left+o.scrollLeft():0}).css("header"===t?{top:this.c.headerOffset,bottom:""}:{top:"",bottom:this.c.footerOffset}).addClass("footer"===t?"dtfh-floatingparent-foot":"dtfh-floatingparent-head").appendTo("body").children().eq(0).append(n.floating),this._stickyPosition(n.floating,"-"),(i=function(){var t=o.scrollLeft();s.s.scrollLeft={footer:t,header:t},n.floatingParent.scrollLeft(s.s.scrollLeft.header)})(),o.off("scroll.dtfh").on("scroll.dtfh",i),n.floatingParent.children().css({width:"fit-content",paddingRight:s.s.dt.settings()[0].oBrowser.barWidth}),(e=b("footer"===t?"div.dtfc-bottom-blocker":"div.dtfc-top-blocker",d.table().container())).length&&e.clone().appendTo(n.floatingParent).css({position:"fixed",right:e.width()}),n.placeholder=r.clone(!1),n.placeholder.find("*[id]").removeAttr("id"),n.host.prepend(n.placeholder),n.floating.append(r),this._widths(n)))},_stickyPosition:function(t,e){var i;this._scrollEnabled()&&(i="rtl"===b(this.s.dt.table().node()).css("direction"),t.find("th").each(function(){var t,e,o;"sticky"===b(this).css("position")&&(t=b(this).css("right"),e=b(this).css("left"),"auto"===t||i?"auto"!==e&&i&&(o=+e.replace(/px/g,""),b(this).css("left",0<o?o:0)):(o=+t.replace(/px/g,""),b(this).css("right",0<o?o:0)))}))},_horizontal:function(t,e){var o,i=this.dom[t],s=this.s.scrollLeft;i.floating&&s[t]!==e&&(this._scrollEnabled()&&(o=b(b(this.s.dt.table().node()).parent()).scrollLeft(),i.floating.scrollLeft(o),i.floatingParent.scrollLeft(o)),s[t]=e)},_modeChange:function(t,e,o){var i,s,d,n,r,a,f,h=this.dom[e],l=this.s.position,c=this._scrollEnabled();"footer"===e&&c||(i=function(t){h.floating[0].style.setProperty("width",t+"px","important"),c||h.floatingParent[0].style.setProperty("width",t+"px","important")},n=this.dom["footer"===e?"tfoot":"thead"],s=b.contains(n[0],v.activeElement)?v.activeElement:null,r=b(b(this.s.dt.table().node()).parent()),"in-place"===t?(h.placeholder&&(h.placeholder.remove(),h.placeholder=null),"header"===e?h.host.prepend(n):h.host.append(n),h.floating&&(h.floating.remove(),h.floating=null,this._stickyPosition(h.host,"+")),h.floatingParent&&(h.floatingParent.find("div.dtfc-top-blocker").remove(),h.floatingParent.remove()),b(b(h.host.parent()).parent()).scrollLeft(r.scrollLeft())):"in"===t?(this._clone(e,o),n=r.offset(),f=(d=b(v).scrollTop())+b(m).height(),a=c?n.top:l.tbodyTop,n=c?n.top+r.outerHeight():l.tfootTop,r="footer"===e?f<a?l.tfootHeight:a+l.tfootHeight-f:d+this.c.headerOffset+l.theadHeight-n,a="header"===e?"top":"bottom",f=this.c[e+"Offset"]-(0<r?r:0),h.floating.addClass("fixedHeader-floating"),h.floatingParent.css(a,f).css({left:l.left,"z-index":3}),i(l.width),"footer"===e&&h.floating.css("top","")):"below"===t?(this._clone(e,o),h.floating.addClass("fixedHeader-locked"),h.floatingParent.css({position:"absolute",top:l.tfootTop-l.theadHeight,left:l.left+"px"}),i(l.width)):"above"===t&&(this._clone(e,o),h.floating.addClass("fixedHeader-locked"),h.floatingParent.css({position:"absolute",top:l.tbodyTop,left:l.left+"px"}),i(l.width)),s&&s!==v.activeElement&&setTimeout(function(){s.focus()},10),this.s.scrollLeft.header=-1,this.s.scrollLeft.footer=-1,this.s[e+"Mode"]=t)},_positions:function(){var t=this.s.dt,e=t.table(),o=this.s.position,i=this.dom,e=b(e.node()),s=this._scrollEnabled(),d=b(t.table().header()),t=b(t.table().footer()),i=i.tbody,n=e.parent();o.visible=e.is(":visible"),o.width=e.outerWidth(),o.left=e.offset().left,o.theadTop=d.offset().top,o.tbodyTop=(s?n:i).offset().top,o.tbodyHeight=(s?n:i).outerHeight(),o.theadHeight=d.outerHeight(),o.theadBottom=o.theadTop+o.theadHeight,o.tfootTop=o.tbodyTop+o.tbodyHeight,t.length?(o.tfootBottom=o.tfootTop+t.outerHeight(),o.tfootHeight=t.outerHeight()):(o.tfootBottom=o.tfootTop,o.tfootHeight=0)},_scroll:function(t){var e,o,i,s,d,n,r,a,f,h,l,c,p,g,u;this.s.dt.settings()[0].bDestroying||(e=this._scrollEnabled(),i=(o=b(this.s.dt.table().node()).parent()).offset(),h=o.outerHeight(),s=b(v).scrollLeft(),d=b(v).scrollTop(),n=b(m).height()+d,l=this.s.position,c=e?i.top:l.tbodyTop,a=(e?i:l).left,h=e?i.top+h:l.tfootTop,f=e?o.outerWidth():l.tbodyWidth,this.c.header&&(!this.s.enable||!l.visible||d+this.c.headerOffset+l.theadHeight<=c?r="in-place":d+this.c.headerOffset+l.theadHeight>c&&d+this.c.headerOffset+l.theadHeight<h?(r="in",d+this.c.headerOffset+l.theadHeight>h||void 0===this.dom.header.floatingParent?t=!0:this.dom.header.floatingParent.css({top:this.c.headerOffset,position:"fixed"}).children().eq(0).append(this.dom.header.floating)):r="below",!t&&r===this.s.headerMode||this._modeChange(r,"header",t),this._horizontal("header",s)),p={offset:{top:0,left:0},height:0},g={offset:{top:0,left:0},height:0},this.c.footer&&this.dom.tfoot.length&&this.dom.tfoot.find("th, td").length&&(!this.s.enable||!l.visible||l.tfootBottom+this.c.footerOffset<=n?u="in-place":h+l.tfootHeight+this.c.footerOffset>n&&c+this.c.footerOffset<n?(u="in",t=!0):u="above",!t&&u===this.s.footerMode||this._modeChange(u,"footer",t),this._horizontal("footer",s),h=function(t){return{offset:t.offset(),height:t.outerHeight()}},p=this.dom.header.floating?h(this.dom.header.floating):h(this.dom.thead),g=this.dom.footer.floating?h(this.dom.footer.floating):h(this.dom.tfoot),e)&&g.offset.top>d&&(c=n+((l=d-i.top)>-p.height?l:0)-(p.offset.top+(l<-p.height?p.height:0)+g.height),o.outerHeight(c=c<0?0:c),Math.round(o.outerHeight())>=Math.round(c)?b(this.dom.tfoot.parent()).addClass("fixedHeader-floating"):b(this.dom.tfoot.parent()).removeClass("fixedHeader-floating")),this.dom.header.floating&&this.dom.header.floatingParent.css("left",a-s),this.dom.footer.floating&&this.dom.footer.floatingParent.css("left",a-s),void 0!==this.s.dt.settings()[0]._fixedColumns&&(this.dom.header.rightBlocker=(u=function(t,e,o){var i;return null!==(o=void 0===o?0===(i=b("div.dtfc-"+t+"-"+e+"-blocker")).length?null:i.clone().css("z-index",1):o)&&("in"===r||"below"===r?o.appendTo("body").css({top:("top"===e?p:g).offset.top,left:"right"===t?a+f-o.width():a}):o.detach()),o})("right","top",this.dom.header.rightBlocker),this.dom.header.leftBlocker=u("left","top",this.dom.header.leftBlocker),this.dom.footer.rightBlocker=u("right","bottom",this.dom.footer.rightBlocker),this.dom.footer.leftBlocker=u("left","bottom",this.dom.footer.leftBlocker)))},_scrollEnabled:function(){var t=this.s.dt.settings()[0].oScroll;return""!==t.sY||""!==t.sX},_widths:function(t){if(t&&t.placeholder)for(var e=b(this.s.dt.table().node()),o=b(e.parent()),i=(t.floatingParent.css("width",o[0].offsetWidth),t.floating.css("width",e[0].offsetWidth),b("colgroup",t.floating).remove(),t.placeholder.parent().find("colgroup").clone().appendTo(t.floating).find("col")),s=this.s.dt.columns(":visible").widths(),d=0;d<s.length;d++)i.eq(d).css("width",s[d])}}),s.version="4.0.1",s.defaults={header:!0,footer:!1,headerOffset:0,footerOffset:0},b.fn.dataTable.FixedHeader=s,b.fn.DataTable.FixedHeader=s,b(v).on("init.dt.dtfh",function(t,e,o){var i;"dt"===t.namespace&&(t=e.oInit.fixedHeader,i=d.defaults.fixedHeader,t||i)&&!e._fixedHeader&&(i=b.extend({},i,t),!1!==t)&&new s(e,i)}),d.Api.register("fixedHeader()",function(){}),d.Api.register("fixedHeader.adjust()",function(){return this.iterator("table",function(t){t=t._fixedHeader;t&&t.update()})}),d.Api.register("fixedHeader.enable()",function(e){return this.iterator("table",function(t){t=t._fixedHeader;e=void 0===e||e,t&&e!==t.enabled()&&t.enable(e)})}),d.Api.register("fixedHeader.enabled()",function(){if(this.context.length){var t=this.context[0]._fixedHeader;if(t)return t.enabled()}return!1}),d.Api.register("fixedHeader.disable()",function(){return this.iterator("table",function(t){t=t._fixedHeader;t&&t.enabled()&&t.enable(!1)})}),b.each(["header","footer"],function(t,o){d.Api.register("fixedHeader."+o+"Offset()",function(e){var t=this.context;return void 0===e?t.length&&t[0]._fixedHeader?t[0]._fixedHeader[o+"Offset"]():void 0:this.iterator("table",function(t){t=t._fixedHeader;t&&t[o+"Offset"](e)})})}),d});

/*! KeyTable 2.12.1
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var i,s;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return n(e,window,document)}):"object"==typeof exports?(i=require("jquery"),s=function(e,t){t.fn.dataTable||require("datatables.net")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||i(e),s(e,t),n(t,e,e.document)}:(s(window,i),module.exports=n(i,window,window.document))):n(jQuery,window,document)}(function(c,u,d){"use strict";function o(e,t){if(!l.versionCheck||!l.versionCheck("1.10.8"))throw"KeyTable requires DataTables 1.10.8 or newer";if(this.c=c.extend(!0,{},l.defaults.keyTable,o.defaults,t),this.s={dt:new l.Api(e),dtDrawing:!1,enable:!0,focusDraw:!1,waitingForDraw:!1,lastFocus:null,namespace:".keyTable-"+n++,tabInput:null},this.dom={},t=this.s.dt.settings()[0],e=t.keytable)return e;(t.keytable=this)._constructor()}var l=c.fn.dataTable,n=0,f=0;return c.extend(o.prototype,{blur:function(){this._blur()},enable:function(e){this.s.enable=e},enabled:function(){return this.s.enable},focus:function(e,t){this._focus(this.s.dt.cell(e,t))},focused:function(e){var t;return!!this.s.lastFocus&&(t=this.s.lastFocus.cell.index(),e.row===t.row)&&e.column===t.column},_constructor:function(){this._tabInput();var i,o=this,s=this.s.dt,e=c(s.table().node()),l=this.s.namespace,t=!1,n=("static"===e.css("position")&&e.css("position","relative"),c(s.table().body()).on("click"+l,"th, td",function(e){var t;!1!==o.s.enable&&(t=s.cell(this)).any()&&o._focus(t,null,!1,e)}),c(d).on("keydown"+l,function(e){t||o.s.dtDrawing?e.preventDefault():o._key(e)}),this.c.blurable&&c(d).on("mousedown"+l,function(e){c(e.target).parents(".dataTables_filter, .dt-search").length&&o._blur(),c(e.target).parents().filter(s.table().container()).length||c(e.target).parents("div.DTE").length||c(e.target).parents("div.editor-datetime").length||c(e.target).parents("div.dt-datetime").length||c(e.target).parents().filter(".DTFC_Cloned").length||o._blur()}),this.c.editor&&((i=this.c.editor).on("open.keyTableMain",function(e,t,n){"inline"!==t&&o.s.enable&&(o.enable(!1),i.one("close"+l,function(){o.enable(!0)}))}),this.c.editOnFocus&&s.on("key-focus"+l+" key-refocus"+l,function(e,t,n,i){o._editor(null,i,!0)}),s.on("key"+l,function(e,t,n,i,s){o._editor(n,s,!1)}),c(s.table().body()).on("dblclick"+l,"th, td",function(e){!1===o.s.enable||!s.cell(this).any()||o.s.lastFocus&&this!==o.s.lastFocus.cell.node()||o._editor(null,e,!0)}),i.on("preSubmit",function(){t=!0}).on("preSubmitCancelled",function(){t=!1}).on("submitComplete",function(){t=!1})),s.on("stateSaveParams"+l,function(e,t,n){n.keyTable=o.s.lastFocus?o.s.lastFocus.cell.index():null}),s.on("column-visibility"+l,function(e){o._tabInput()}),s.on("column-reorder"+l,function(e,t,n){var i,s=o.s.lastFocus;s&&s.cell&&(i=s.relative.column,s.cell[0][0].column=n.mapping.indexOf(i),s.relative.column=n.mapping.indexOf(i))}),s.on("preDraw"+l+" scroller-will-draw"+l,function(e){o.s.dtDrawing=!0}),s.on("draw"+l,function(e){var t,n,i;o.s.dtDrawing=!1,o._tabInput(),o.s.focusDraw||o.s.lastFocus&&(t=o.s.lastFocus.relative,n=s.page.info(),i=t.row,0===n.recordsDisplay||i<n.start||i>n.start+n.length||(i>=n.recordsDisplay&&(i=n.recordsDisplay-1),o._focus(i,t.column,!0,e)))}),this.c.clipboard&&this._clipboard(),s.on("destroy"+l,function(){o._blur(!0),s.off(l),c(s.table().body()).off("click"+l,"th, td").off("dblclick"+l,"th, td"),c(d).off("mousedown"+l).off("keydown"+l).off("copy"+l).off("paste"+l)}),s.state.loaded());n&&n.keyTable?s.one("init",function(){var e=s.cell(n.keyTable);e.any()&&e.focus()}):this.c.focus&&s.cell(this.c.focus).focus()},_blur:function(e){var t;this.s.enable&&this.s.lastFocus&&(t=this.s.lastFocus.cell,c(t.node()).removeClass(this.c.className),this.s.lastFocus=null,e||(this._updateFixedColumns(t.index().column),this._emitEvent("key-blur",[this.s.dt,t])))},_clipboard:function(){var o=this.s.dt,l=this,e=this.s.namespace,t=this.c.clipboard;u.getSelection&&(!0!==t&&!t.copy||c(d).on("copy"+e,function(e){var e=e.originalEvent,t=u.getSelection().toString(),n=l.s.lastFocus;!t&&n&&(e.clipboardData.setData("text/plain",n.cell.render(l.c.clipboardOrthogonal)),e.preventDefault())}),!0!==t&&!t.paste||c(d).on("paste"+e,function(e){var t,e=e.originalEvent,n=l.s.lastFocus,i=d.activeElement,s=l.c.editor;!n||i&&"body"!==i.nodeName.toLowerCase()||(e.preventDefault(),u.clipboardData&&u.clipboardData.getData?t=u.clipboardData.getData("Text"):e.clipboardData&&e.clipboardData.getData&&(t=e.clipboardData.getData("text/plain")),s?(i=l._inlineOptions(n.cell.index()),s.inline(i.cell,i.field,i.options).set(s.displayed()[0],t).submit()):(n.cell.data(t),o.draw(!1)))}))},_columns:function(){var e=this.s.dt,t=e.columns(this.c.columns).indexes(),n=[];return e.columns(":visible").every(function(e){-1!==t.indexOf(e)&&n.push(e)}),n},_editor:function(e,t,n){var i,s,o,l,a,r;!this.s.lastFocus||t&&"draw"===t.type||(s=(i=this).s.dt,o=this.c.editor,l=this.s.lastFocus.cell,a=this.s.namespace+"e"+f++,c("div.DTE",l.node()).length)||null!==e&&(0<=e&&e<=9||11===e||12===e||14<=e&&e<=31||112<=e&&e<=123||127<=e&&e<=159)||(t&&(t.stopPropagation(),13===e)&&t.preventDefault(),r=function(){var e=i._inlineOptions(l.index());o.one("open"+a,function(){o.off("cancelOpen"+a),n||c("div.DTE_Field_InputControl input, div.DTE_Field_InputControl textarea").select(),s.keys.enable(n?"tab-only":"navigation-only"),s.on("key-blur.editor",function(e,t,n){"submit"!==o.s.editOpts.onBlur&&o.displayed()&&n.node()===l.node()&&o.submit()}),n&&c(s.table().container()).addClass("dtk-focus-alt"),o.on("preSubmitCancelled"+a,function(){setTimeout(function(){i._focus(l,null,!1)},50)}),o.on("submitUnsuccessful"+a,function(){i._focus(l,null,!1)}),o.one("close"+a,function(){s.keys.enable(!0),s.off("key-blur.editor"),o.off(a),c(s.table().container()).removeClass("dtk-focus-alt"),i.s.returnSubmit&&(i.s.returnSubmit=!1,i._emitEvent("key-return-submit",[s,l]))})}).one("cancelOpen"+a,function(){o.off(a)}).inline(e.cell,e.field,e.options)},13===e?(n=!0,c(d).one("keyup",function(){r()})):r())},_inlineOptions:function(e){return this.c.editorOptions?this.c.editorOptions(e):{cell:e,field:void 0,options:void 0}},_emitEvent:function(n,i){return this.s.dt.iterator("table",function(e,t){return c(e.nTable).triggerHandler(n,i)})},_focus:function(e,t,n,i){var s=this,o=this.s.dt,l=o.page.info(),a=this.s.lastFocus;if(i=i||null,this.s.enable){if("number"!=typeof e){if(!e.any())return;var r=e.index();if(t=r.column,(e=o.rows({filter:"applied",order:"applied"}).indexes().indexOf(r.row))<0)return;l.serverSide&&(e+=l.start)}if(-1!==l.length&&(e<l.start||e>=l.start+l.length))this.s.focusDraw=!0,this.s.waitingForDraw=!0,o.one("draw",function(){s.s.focusDraw=!1,s.s.waitingForDraw=!1,s._focus(e,t,void 0,i)}).page(Math.floor(e/l.length)).draw(!1);else if(-1!==c.inArray(t,this._columns())){l.serverSide&&(e-=l.start);r=o.cells(null,t,{search:"applied",order:"applied"}).flatten(),l=o.cell(r[e]),r=this._emitEvent("key-prefocus",[this.s.dt,l,i||null]);if(-1===r.indexOf(!1)){if(a){if(a.node===l.node())return void this._emitEvent("key-refocus",[this.s.dt,l,i||null]);this._blur()}this._removeOtherFocus();r=c(l.node()),a=(r.addClass(this.c.className),this._updateFixedColumns(t),void 0!==n&&!0!==n||(this._scroll(c(u),c(d.body),r,"offset"),(a=o.table().body().parentNode)!==o.table().header().parentNode&&(n=c(a.parentNode),this._scroll(n,n,r,"position"))),o.page.info());this.s.lastFocus={cell:l,node:l.node(),relative:{row:a.start+o.rows({page:"current"}).indexes().indexOf(l.index().row),column:l.index().column}},this._emitEvent("key-focus",[this.s.dt,l,i||null]),o.state.save()}}}},_key:function(n){if(this.s.waitingForDraw)n.preventDefault();else if(!c(n.target).closest(".dte-inlineAdd").length){var e=this.s.enable,t=(this.s.returnSubmit=("navigation-only"===e||"tab-only"===e)&&13===n.keyCode,!0===e||"navigation-only"===e);if(e&&(!(0===n.keyCode||n.ctrlKey||n.metaKey||n.altKey)||n.ctrlKey&&n.altKey)){var i=this.s.lastFocus;if(i)if(this.s.dt.cell(i.node).any()){var s=this,o=this.s.dt,l=!!this.s.dt.settings()[0].oScroll.sY;if(!this.c.keys||-1!==c.inArray(n.keyCode,this.c.keys))switch(n.keyCode){case 9:n.preventDefault(),this._keyAction(function(){s._shift(n,n.shiftKey?"left":"right",!0)});break;case 27:c(i.node).find("div.DTE").length||this.c.blurable&&!0===e&&this._blur();break;case 33:case 34:t&&!l&&(n.preventDefault(),this._keyAction(function(){o.page(33===n.keyCode?"previous":"next").draw(!1)}));break;case 35:case 36:t&&(n.preventDefault(),this._keyAction(function(){var e=o.cells({page:"current"}).indexes(),t=s._columns();s._focus(o.cell(e[35===n.keyCode?e.length-1:t[0]]),null,!0,n)}));break;case 37:t&&this._keyAction(function(){s._shift(n,"left")});break;case 38:t&&this._keyAction(function(){s._shift(n,"up")});break;case 39:t&&this._keyAction(function(){s._shift(n,"right")});break;case 40:t&&this._keyAction(function(){s._shift(n,"down")});break;case 113:if(this.c.editor){this._editor(null,n,!0);break}default:!0===e&&this._emitEvent("key",[o,n.keyCode,this.s.lastFocus.cell,n])}}else this.s.lastFocus=null}}},_keyAction:function(e){var t=this.c.editor;t&&t.mode()&&t.display()?t.submit(e):e()},_removeOtherFocus:function(){var t=this.s.dt.table().node();c.fn.dataTable.tables({api:!0}).iterator("table",function(e){this.table().node()!==t&&this.cell.blur()})},_scroll:function(e,t,n,i){var s=n[i](),o=n.outerHeight(),l=n.outerWidth(),a=t.scrollTop(),r=t.scrollLeft(),c=e.height(),e=e.width();"position"===i&&(s.top+=parseInt(n.closest("table").css("top"),10)),s.top<a&&s.top+o>a-5&&t.scrollTop(s.top),s.left<r&&t.scrollLeft(s.left),s.top+o>a+c&&s.top<a+c+5&&o<c&&t.scrollTop(s.top+o-c),s.left+l>r+e&&l<e&&t.scrollLeft(s.left+l-e)},_shift:function(e,t,n){var i,s=this.s.dt,o=s.page.info(),l=o.recordsDisplay,a=this._columns(),r=this.s.lastFocus;r&&(r=r.cell)&&(i=s.rows({filter:"applied",order:"applied"}).indexes().indexOf(r.index().row),o.serverSide&&(i+=o.start),o=i,r=a[i=s.columns(a).indexes().indexOf(r.index().column)],"rtl"===c(s.table().node()).css("direction")&&("right"===t?t="left":"left"===t&&(t="right")),"right"===t?r=i>=a.length-1?(o++,a[0]):a[i+1]:"left"===t?r=0===i?(o--,a[a.length-1]):a[i-1]:"up"===t?o--:"down"===t&&o++,0<=o&&o<l&&-1!==c.inArray(r,a)?(e&&e.preventDefault(),this._focus(o,r,!0,e)):n&&this.c.blurable?this._blur():e&&e.preventDefault())},_tabInput:function(){var n=this,i=this.s.dt,e=null!==this.c.tabIndex?this.c.tabIndex:i.settings()[0].iTabIndex;-1!=e&&(this.s.tabInput||((e=c('<div><input type="text" tabindex="'+e+'"/></div>').css({position:"absolute",height:1,width:0,overflow:"hidden"})).children().on("focus",function(e){var t=i.cell(":eq(0)",n._columns(),{page:"current"});t.any()&&n._focus(t,null,!0,e)}),this.s.tabInput=e),e=this.s.dt.cell(":eq(0)","0:visible",{page:"current",order:"current"}).node())&&c(e).prepend(this.s.tabInput)},_updateFixedColumns:function(e){var t,n=this.s.dt,i=n.settings()[0];i._oFixedColumns&&(t=i._oFixedColumns.s.iLeftColumns,i=i.aoColumns.length-i._oFixedColumns.s.iRightColumns,e<t||i<=e)&&n.fixedColumns().update()}}),o.defaults={blurable:!0,className:"focus",clipboard:!0,clipboardOrthogonal:"display",columns:"",editor:null,editOnFocus:!1,editorOptions:null,focus:null,keys:null,tabIndex:null},o.version="2.12.1",c.fn.dataTable.KeyTable=o,c.fn.DataTable.KeyTable=o,l.Api.register("cell.blur()",function(){return this.iterator("table",function(e){e.keytable&&e.keytable.blur()})}),l.Api.register("cell().focus()",function(){return this.iterator("cell",function(e,t,n){e.keytable&&e.keytable.focus(t,n)})}),l.Api.register("keys.disable()",function(){return this.iterator("table",function(e){e.keytable&&e.keytable.enable(!1)})}),l.Api.register("keys.enable()",function(t){return this.iterator("table",function(e){e.keytable&&e.keytable.enable(void 0===t||t)})}),l.Api.register("keys.enabled()",function(e){var t=this.context;return!!t.length&&!!t[0].keytable&&t[0].keytable.enabled()}),l.Api.register("keys.move()",function(t){return this.iterator("table",function(e){e.keytable&&e.keytable._shift(null,t,!1)})}),l.ext.selector.cell.push(function(e,t,n){var i=t.focused,s=e.keytable,o=[];if(!s||void 0===i)return n;for(var l=0,a=n.length;l<a;l++)(!0===i&&s.focused(n[l])||!1===i&&!s.focused(n[l]))&&o.push(n[l]);return o}),c(d).on("preInit.dt.dtk",function(e,t,n){var i,s;"dt"===e.namespace&&(e=t.oInit.keys,i=l.defaults.keys,e||i)&&(s=c.extend({},i,e),!1!==e)&&new o(t,s)}),l});

/*! Responsive 3.0.4
 * © SpryMedia Ltd - datatables.net/license
 */
!function(n){var i,r;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return n(e,window,document)}):"object"==typeof exports?(i=require("jquery"),r=function(e,t){t.fn.dataTable||require("datatables.net")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||i(e),r(e,t),n(t,e,e.document)}:(r(window,i),module.exports=n(i,window,window.document))):n(jQuery,window,document)}(function(b,y,d){"use strict";function a(e,t){if(!i.versionCheck||!i.versionCheck("2"))throw"DataTables Responsive requires DataTables 2 or newer";this.s={childNodeStore:{},columns:[],current:[],dt:new i.Api(e)},this.s.dt.settings()[0].responsive||(t&&"string"==typeof t.details?t.details={type:t.details}:t&&!1===t.details?t.details={type:!1}:t&&!0===t.details&&(t.details={type:"inline"}),this.c=b.extend(!0,{},a.defaults,i.defaults.responsive,t),(e.responsive=this)._constructor())}var i=b.fn.dataTable,e=(b.extend(a.prototype,{_constructor:function(){var o=this,r=this.s.dt,t=b(y).innerWidth(),e=(r.settings()[0]._responsive=this,b(y).on("orientationchange.dtr",i.util.throttle(function(){var e=b(y).innerWidth();e!==t&&(o._resize(),t=e)})),r.on("row-created.dtr",function(e,t,n,i){-1!==b.inArray(!1,o.s.current)&&b(">td, >th",t).each(function(e){e=r.column.index("toData",e);!1===o.s.current[e]&&b(this).css("display","none").addClass("dtr-hidden")})}),r.on("destroy.dtr",function(){r.off(".dtr"),b(r.table().body()).off(".dtr"),b(y).off("resize.dtr orientationchange.dtr"),r.cells(".dtr-control").nodes().to$().removeClass("dtr-control"),b(r.table().node()).removeClass("dtr-inline collapsed"),b.each(o.s.current,function(e,t){!1===t&&o._setColumnVis(e,!0)})}),this.c.breakpoints.sort(function(e,t){return e.width<t.width?1:e.width>t.width?-1:0}),this._classLogic(),this.c.details);!1!==e.type&&(o._detailsInit(),r.on("column-visibility.dtr",function(){o._timer&&clearTimeout(o._timer),o._timer=setTimeout(function(){o._timer=null,o._classLogic(),o._resizeAuto(),o._resize(!0),o._redrawChildren()},100)}),r.on("draw.dtr",function(){o._redrawChildren()}),b(r.table().node()).addClass("dtr-"+e.type)),r.on("column-calc.dt",function(e,t){for(var n=o.s.current,i=0;i<n.length;i++){var r=t.visible.indexOf(i);!1===n[i]&&0<=r&&t.visible.splice(r,1)}}),r.on("preXhr.dtr",function(){var e=[];r.rows().every(function(){this.child.isShown()&&e.push(this.id(!0))}),r.one("draw.dtr",function(){o._resizeAuto(),o._resize(),r.rows(e).every(function(){o._detailsDisplay(this,!1)})})}),r.on("draw.dtr",function(){o._controlClass()}).ready(function(){o._resizeAuto(),o._resize(),r.on("column-reorder.dtr",function(e,t,n){o._classLogic(),o._resizeAuto(),o._resize(!0)}),r.on("column-sizing.dtr",function(){o._resizeAuto(),o._resize()})})},_colGroupAttach:function(e,t,n){var i=null;if(t[n].get(0).parentNode!==e[0]){for(var r=n+1;r<t.length;r++)if(e[0]===t[r].get(0).parentNode){i=r;break}null!==i?t[n].insertBefore(t[i][0]):e.append(t[n])}},_childNodes:function(e,t,n){var i=t+"-"+n;if(this.s.childNodeStore[i])return this.s.childNodeStore[i];for(var r=[],o=e.cell(t,n).node().childNodes,s=0,d=o.length;s<d;s++)r.push(o[s]);return this.s.childNodeStore[i]=r},_childNodesRestore:function(e,t,n){var i=t+"-"+n;if(this.s.childNodeStore[i]){var r=e.cell(t,n).node(),e=this.s.childNodeStore[i];if(0<e.length){for(var o=e[0].parentNode.childNodes,s=[],d=0,a=o.length;d<a;d++)s.push(o[d]);for(var l=0,c=s.length;l<c;l++)r.appendChild(s[l])}this.s.childNodeStore[i]=void 0}},_columnsVisiblity:function(n){for(var i=this.s.dt,e=this.s.columns,t=e.map(function(e,t){return{columnIdx:t,priority:e.priority}}).sort(function(e,t){return e.priority!==t.priority?e.priority-t.priority:e.columnIdx-t.columnIdx}),r=b.map(e,function(e,t){return!1===i.column(t).visible()?"not-visible":(!e.auto||null!==e.minWidth)&&(!0===e.auto?"-":-1!==b.inArray(n,e.includeIn))}),o=0,s=0,d=r.length;s<d;s++)!0===r[s]&&(o+=e[s].minWidth);var a=i.settings()[0].oScroll,a=a.sY||a.sX?a.iBarWidth:0,l=i.table().container().offsetWidth-a-o;for(s=0,d=r.length;s<d;s++)e[s].control&&(l-=e[s].minWidth);var c=!1;for(s=0,d=t.length;s<d;s++){var u=t[s].columnIdx;"-"===r[u]&&!e[u].control&&e[u].minWidth&&(c||l-e[u].minWidth<0?r[u]=!(c=!0):r[u]=!0,l-=e[u].minWidth)}var h=!1;for(s=0,d=e.length;s<d;s++)if(!e[s].control&&!e[s].never&&!1===r[s]){h=!0;break}for(s=0,d=e.length;s<d;s++)e[s].control&&(r[s]=h),"not-visible"===r[s]&&(r[s]=!1);return-1===b.inArray(!0,r)&&(r[0]=!0),r},_classLogic:function(){function d(e,t,n,i){var r,o,s;if(n){if("max-"===n)for(r=a._find(t).width,o=0,s=l.length;o<s;o++)l[o].width<=r&&u(e,l[o].name);else if("min-"===n)for(r=a._find(t).width,o=0,s=l.length;o<s;o++)l[o].width>=r&&u(e,l[o].name);else if("not-"===n)for(o=0,s=l.length;o<s;o++)-1===l[o].name.indexOf(i)&&u(e,l[o].name)}else c[e].includeIn.push(t)}var a=this,l=this.c.breakpoints,c=this.s.dt.columns().eq(0).map(function(e){var e=this.column(e),t=e.header().className,n=e.init().responsivePriority,e=e.header().getAttribute("data-priority");return void 0===n&&(n=null==e?1e4:+e),{className:t,includeIn:[],auto:!1,control:!1,never:!!t.match(/\b(dtr\-)?never\b/),priority:n}}),u=function(e,t){e=c[e].includeIn;-1===b.inArray(t,e)&&e.push(t)};c.each(function(e,r){for(var t=e.className.split(" "),o=!1,n=0,i=t.length;n<i;n++){var s=t[n].trim();if("all"===s||"dtr-all"===s)return o=!0,void(e.includeIn=b.map(l,function(e){return e.name}));if("none"===s||"dtr-none"===s||e.never)return void(o=!0);if("control"===s||"dtr-control"===s)return o=!0,void(e.control=!0);b.each(l,function(e,t){var n=t.name.split("-"),i=new RegExp("(min\\-|max\\-|not\\-)?("+n[0]+")(\\-[_a-zA-Z0-9])?"),i=s.match(i);i&&(o=!0,i[2]===n[0]&&i[3]==="-"+n[1]?d(r,t.name,i[1],i[2]+i[3]):i[2]!==n[0]||i[3]||d(r,t.name,i[1],i[2]))})}o||(e.auto=!0)}),this.s.columns=c},_controlClass:function(){var e,t,n;"inline"===this.c.details.type&&(e=this.s.dt,t=this.s.current,n=b.inArray(!0,t),e.cells(null,function(e){return e!==n},{page:"current"}).nodes().to$().filter(".dtr-control").removeClass("dtr-control"),0<=n)&&e.cells(null,n,{page:"current"}).nodes().to$().addClass("dtr-control"),this._tabIndexes()},_detailsDisplay:function(t,n){function e(e){b(t.node()).toggleClass("dtr-expanded",!1!==e),b(o.table().node()).triggerHandler("responsive-display.dt",[o,t,e,n])}var i,r=this,o=this.s.dt,s=this.c.details;s&&!1!==s.type&&(i="string"==typeof s.renderer?a.renderer[s.renderer]():s.renderer,"boolean"==typeof(s=s.display(t,n,function(){return i.call(r,o,t[0][0],r._detailsObj(t[0]))},function(){e(!1)})))&&e(s)},_detailsInit:function(){var n=this,i=this.s.dt,e=this.c.details,r=("inline"===e.type&&(e.target="td.dtr-control, th.dtr-control"),b(i.table().body()).on("keyup.dtr","td, th",function(e){13===e.keyCode&&b(this).data("dtr-keyboard")&&b(this).click()}),e.target),e="string"==typeof r?r:"td, th";void 0===r&&null===r||b(i.table().body()).on("click.dtr mousedown.dtr mouseup.dtr",e,function(e){if(b(i.table().node()).hasClass("collapsed")&&-1!==b.inArray(b(this).closest("tr").get(0),i.rows().nodes().toArray())){if("number"==typeof r){var t=r<0?i.columns().eq(0).length+r:r;if(i.cell(this).index().column!==t)return}t=i.row(b(this).closest("tr"));"click"===e.type?n._detailsDisplay(t,!1):"mousedown"===e.type?b(this).css("outline","none"):"mouseup"===e.type&&b(this).trigger("blur").css("outline","")}})},_detailsObj:function(n){var i=this,r=this.s.dt;return b.map(this.s.columns,function(e,t){if(!e.never&&!e.control)return{className:r.settings()[0].aoColumns[t].sClass,columnIndex:t,data:r.cell(n,t).render(i.c.orthogonal),hidden:r.column(t).visible()&&!i.s.current[t],rowIndex:n,title:r.column(t).title()}})},_find:function(e){for(var t=this.c.breakpoints,n=0,i=t.length;n<i;n++)if(t[n].name===e)return t[n]},_redrawChildren:function(){var n=this,i=this.s.dt;i.rows({page:"current"}).iterator("row",function(e,t){n._detailsDisplay(i.row(t),!0)})},_resize:function(n){for(var e,i=this,r=this.s.dt,t=b(y).innerWidth(),o=this.c.breakpoints,s=o[0].name,d=this.s.columns,a=this.s.current.slice(),l=o.length-1;0<=l;l--)if(t<=o[l].width){s=o[l].name;break}var c=this._columnsVisiblity(s),u=(this.s.current=c,!1);for(l=0,e=d.length;l<e;l++)if(!1===c[l]&&!d[l].never&&!d[l].control&&!1==!r.column(l).visible()){u=!0;break}b(r.table().node()).toggleClass("collapsed",u);var h=!1,p=0,f=r.settings()[0],m=b(r.table().node()).children("colgroup"),v=f.aoColumns.map(function(e){return e.colEl});r.columns().eq(0).each(function(e,t){r.column(e).visible()&&(!0===c[t]&&p++,!n&&c[t]===a[t]||(h=!0,i._setColumnVis(e,c[t])),c[t]?i._colGroupAttach(m,v,t):v[t].detach())}),h&&(r.columns.adjust(),this._redrawChildren(),b(r.table().node()).trigger("responsive-resize.dt",[r,this._responsiveOnlyHidden()]),0===r.page.info().recordsDisplay)&&b("td",r.table().body()).eq(0).attr("colspan",p),i._controlClass()},_resizeAuto:function(){var t=this.s.dt,n=this.s.columns,r=this,o=t.columns().indexes().filter(function(e){return t.column(e).visible()});if(this.c.auto&&-1!==b.inArray(!0,b.map(n,function(e){return e.auto}))){for(var e=t.table().node().cloneNode(!1),i=b(t.table().header().cloneNode(!1)).appendTo(e),s=b(t.table().footer().cloneNode(!1)).appendTo(e),d=b(t.table().body()).clone(!1,!1).empty().appendTo(e),a=(e.style.width="auto",t.table().header.structure(o).forEach(e=>{e=e.filter(function(e){return!!e}).map(function(e){return b(e.cell).clone(!1).css("display","table-cell").css("width","auto").css("min-width",0)});b("<tr/>").append(e).appendTo(i)}),b("<tr/>").appendTo(d)),l=0;l<o.count();l++)a.append("<td/>");this.c.details.renderer._responsiveMovesNodes?t.rows({page:"current"}).every(function(n){var i,e=this.node();e&&(i=e.cloneNode(!1),t.cells(n,o).every(function(e,t){t=r.s.childNodeStore[n+"-"+t];(t?b(this.node().cloneNode(!1)).append(b(t).clone()):b(this.node()).clone(!1)).appendTo(i)}),d.append(i))}):b(d).append(b(t.rows({page:"current"}).nodes()).clone(!1)).find("th, td").css("display",""),d.find("th, td").css("display",""),t.table().footer.structure(o).forEach(e=>{e=e.filter(function(e){return!!e}).map(function(e){return b(e.cell).clone(!1).css("display","table-cell").css("width","auto").css("min-width",0)});b("<tr/>").append(e).appendTo(s)}),"inline"===this.c.details.type&&b(e).addClass("dtr-inline collapsed"),b(e).find("[name]").removeAttr("name"),b(e).css("position","relative");e=b("<div/>").css({width:1,height:1,overflow:"hidden",clear:"both"}).append(e);e.insertBefore(t.table().node()),a.children().each(function(e){e=t.column.index("fromVisible",e);n[e].minWidth=this.offsetWidth||0}),e.remove()}},_responsiveOnlyHidden:function(){var n=this.s.dt;return b.map(this.s.current,function(e,t){return!1===n.column(t).visible()||e})},_setColumnVis:function(e,t){var n=this,i=this.s.dt,r=t?"":"none";this._setHeaderVis(e,t,i.table().header.structure()),this._setHeaderVis(e,t,i.table().footer.structure()),i.column(e).nodes().to$().css("display",r).toggleClass("dtr-hidden",!t),b.isEmptyObject(this.s.childNodeStore)||i.cells(null,e).indexes().each(function(e){n._childNodesRestore(i,e.row,e.column)})},_setHeaderVis:function(n,i,o){var r=this,s=i?"":"none";o.forEach(function(e,t){for(var n=0;n<e.length;n++)if(e[n]&&1<e[n].rowspan)for(var i=e[n].rowspan,r=1;r<i;r++)o[t+r][n]={}}),o.forEach(function(e){if(e[n]&&e[n].cell)b(e[n].cell).css("display",s).toggleClass("dtr-hidden",!i);else for(var t=n;0<=t;){if(e[t]&&e[t].cell){e[t].cell.colSpan=r._colspan(e,t);break}t--}})},_colspan:function(e,t){for(var n=1,i=t+1;i<e.length;i++)if(null===e[i]&&this.s.current[i])n++;else if(e[i])break;return n},_tabIndexes:function(){var e=this.s.dt,t=e.cells({page:"current"}).nodes().to$(),n=e.settings()[0],i=this.c.details.target;t.filter("[data-dtr-keyboard]").removeData("[data-dtr-keyboard]"),("number"==typeof i?e.cells(null,i,{page:"current"}).nodes().to$():("td:first-child, th:first-child"===i&&(i=">td:first-child, >th:first-child"),t=e.rows({page:"current"}).nodes(),"tr"===i?b(t):b(i,t))).attr("tabIndex",n.iTabIndex).data("dtr-keyboard",1)}}),a.defaults={breakpoints:a.breakpoints=[{name:"desktop",width:1/0},{name:"tablet-l",width:1024},{name:"tablet-p",width:768},{name:"mobile-l",width:480},{name:"mobile-p",width:320}],auto:!0,details:{display:(a.display={childRow:function(e,t,n){var i=b(e.node());return t?i.hasClass("dtr-expanded")?(e.child(n(),"child").show(),!0):void 0:i.hasClass("dtr-expanded")?(e.child(!1),!1):!1!==(t=n())&&(e.child(t,"child").show(),!0)},childRowImmediate:function(e,t,n){var i=b(e.node());return!t&&i.hasClass("dtr-expanded")||!e.responsive.hasHidden()?(e.child(!1),!1):!1!==(t=n())&&(e.child(t,"child").show(),!0)},modal:function(s){return function(e,t,n,i){n=n();if(!1===n)return!1;if(t){if(!(o=b("div.dtr-modal-content")).length||e.index()!==o.data("dtr-row-idx"))return null;o.empty().append(n)}else{var r=function(){o.remove(),b(d).off("keypress.dtr"),b(e.node()).removeClass("dtr-expanded"),i()},o=b('<div class="dtr-modal"/>').append(b('<div class="dtr-modal-display"/>').append(b('<div class="dtr-modal-content"/>').data("dtr-row-idx",e.index()).append(n)).append(b('<div class="dtr-modal-close">&times;</div>').click(function(){r()}))).append(b('<div class="dtr-modal-background"/>').click(function(){r()})).appendTo("body");b(e.node()).addClass("dtr-expanded"),b(d).on("keyup.dtr",function(e){27===e.keyCode&&(e.stopPropagation(),r())})}return s&&s.header&&b("div.dtr-modal-content").prepend("<h2>"+s.header(e)+"</h2>"),!0}}}).childRow,renderer:(a.renderer={listHiddenNodes:function(){function e(i,e,t){var r=this,o=b('<ul data-dtr-index="'+e+'" class="dtr-details"/>'),s=!1;return b.each(t,function(e,t){var n;t.hidden&&(n=t.className?'class="'+t.className+'"':"",b("<li "+n+' data-dtr-index="'+t.columnIndex+'" data-dt-row="'+t.rowIndex+'" data-dt-column="'+t.columnIndex+'"><span class="dtr-title">'+t.title+"</span> </li>").append(b('<span class="dtr-data"/>').append(r._childNodes(i,t.rowIndex,t.columnIndex))).appendTo(o),s=!0)}),!!s&&o}return e._responsiveMovesNodes=!0,e},listHidden:function(){return function(e,t,n){n=b.map(n,function(e){var t=e.className?'class="'+e.className+'"':"";return e.hidden?"<li "+t+' data-dtr-index="'+e.columnIndex+'" data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><span class="dtr-title">'+e.title+'</span> <span class="dtr-data">'+e.data+"</span></li>":""}).join("");return!!n&&b('<ul data-dtr-index="'+t+'" class="dtr-details"/>').append(n)}},tableAll:function(i){return i=b.extend({tableClass:""},i),function(e,t,n){n=b.map(n,function(e){return"<tr "+(e.className?'class="'+e.className+'"':"")+' data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+(""!==e.title?e.title+":":"")+"</td> <td>"+e.data+"</td></tr>"}).join("");return b('<table class="'+i.tableClass+' dtr-details" width="100%"/>').append(n)}}}).listHidden(),target:0,type:"inline"},orthogonal:"display"},b.fn.dataTable.Api);return e.register("responsive()",function(){return this}),e.register("responsive.index()",function(e){return{column:(e=b(e)).data("dtr-index"),row:e.parent().data("dtr-index")}}),e.register("responsive.rebuild()",function(){return this.iterator("table",function(e){e._responsive&&e._responsive._classLogic()})}),e.register("responsive.recalc()",function(){return this.iterator("table",function(e){e._responsive&&(e._responsive._resizeAuto(),e._responsive._resize())})}),e.register("responsive.hasHidden()",function(){var e=this.context[0];return!!e._responsive&&-1!==b.inArray(!1,e._responsive._responsiveOnlyHidden())}),e.registerPlural("columns().responsiveHidden()","column().responsiveHidden()",function(){return this.iterator("column",function(e,t){return!!e._responsive&&e._responsive._responsiveOnlyHidden()[t]},1)}),a.version="3.0.4",b.fn.dataTable.Responsive=a,b.fn.DataTable.Responsive=a,b(d).on("preInit.dt.dtr",function(e,t,n){"dt"===e.namespace&&(b(t.nTable).hasClass("responsive")||b(t.nTable).hasClass("dt-responsive")||t.oInit.responsive||i.defaults.responsive)&&!1!==(e=t.oInit.responsive)&&new a(t,b.isPlainObject(e)?e:{})}),i});

/*! DataTables styling wrapper for Responsive
 * © SpryMedia Ltd - datatables.net/license
 */

(function( factory ){
	if ( typeof define === 'function' && define.amd ) {
		// AMD
		define( ['jquery', 'datatables.net-dt', 'datatables.net-responsive'], function ( $ ) {
			return factory( $, window, document );
		} );
	}
	else if ( typeof exports === 'object' ) {
		// CommonJS
		var jq = require('jquery');
		var cjsRequires = function (root, $) {
			if ( ! $.fn.dataTable ) {
				require('datatables.net-dt')(root, $);
			}

			if ( ! $.fn.dataTable.Responsive ) {
				require('datatables.net-responsive')(root, $);
			}
		};

		if (typeof window === 'undefined') {
			module.exports = function (root, $) {
				if ( ! root ) {
					// CommonJS environments without a window global must pass a
					// root. This will give an error otherwise
					root = window;
				}

				if ( ! $ ) {
					$ = jq( root );
				}

				cjsRequires( root, $ );
				return factory( $, root, root.document );
			};
		}
		else {
			cjsRequires( window, jq );
			module.exports = factory( jq, window, window.document );
		}
	}
	else {
		// Browser
		factory( jQuery, window, document );
	}
}(function( $, window, document ) {
'use strict';
var DataTable = $.fn.dataTable;




return DataTable;
}));


/*! Scroller 2.4.3
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var e,i;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return o(t,window,document)}):"object"==typeof exports?(e=require("jquery"),i=function(t,s){s.fn.dataTable||require("datatables.net")(t,s)},"undefined"==typeof window?module.exports=function(t,s){return t=t||window,s=s||e(t),i(t,s),o(s,t,t.document)}:(i(window,e),module.exports=o(e,window,window.document))):o(jQuery,window,document)}(function(d,l,o){"use strict";function i(t,s){this instanceof i?(void 0===s&&(s={}),t=d.fn.dataTable.Api(t),this.s={dt:t.settings()[0],dtApi:t,tableTop:0,tableBottom:0,redrawTop:0,redrawBottom:0,autoHeight:!0,viewportRows:0,stateTO:null,stateSaveThrottle:function(){},drawTO:null,heights:{jump:null,page:null,virtual:null,scroll:null,row:null,viewport:null,labelHeight:0,xbar:0},topRowFloat:0,scrollDrawDiff:null,loaderVisible:!1,forceReposition:!1,baseRowTop:0,baseScrollTop:0,mousedown:!1,lastScrollTop:0},this.s=d.extend(this.s,i.oDefaults,s),this.s.heights.row=this.s.rowHeight,this.dom={force:o.createElement("div"),label:d('<div class="dts_label">0</div>'),scroller:null,table:null,loader:null},this.s.dt.oScroller||(this.s.dt.oScroller=this).construct()):alert("Scroller warning: Scroller must be initialised with the 'new' keyword.")}var a=d.fn.dataTable,t=(d.extend(i.prototype,{measure:function(t){this.s.autoHeight&&this._calcRowHeight();var s=this.s.heights,o=(s.row&&(s.viewport=this._parseHeight(d(this.dom.scroller).css("max-height")),this.s.viewportRows=parseInt(s.viewport/s.row,10)+1,this.s.dt._iDisplayLength=this.s.viewportRows*this.s.displayBuffer),this.dom.label.outerHeight());s.xbar=this.dom.scroller.offsetHeight-this.dom.scroller.clientHeight,s.labelHeight=o,void 0!==t&&!t||this.s.dtApi.draw(!1)},pageInfo:function(){var t=this.s.dt,s=this.dom.scroller.scrollTop,t=t.fnRecordsDisplay(),o=Math.ceil(this.pixelsToRow(s+this.s.heights.viewport,!1,this.s.ani));return{start:Math.floor(this.pixelsToRow(s,!1,this.s.ani)),end:t<o?t-1:o-1}},pixelsToRow:function(t,s,o){t-=this.s.baseScrollTop,o=o?(this._domain("physicalToVirtual",this.s.baseScrollTop)+t)/this.s.heights.row:t/this.s.heights.row+this.s.baseRowTop;return s||void 0===s?parseInt(o,10):o},rowToPixels:function(t,s,o){t-=this.s.baseRowTop,o=o?this._domain("virtualToPhysical",this.s.baseScrollTop):this.s.baseScrollTop;return o+=t*this.s.heights.row,s||void 0===s?parseInt(o,10):o},scrollToRow:function(t,s){var o=this,e=!1,i=this.rowToPixels(t),r=t-(this.s.displayBuffer-1)/2*this.s.viewportRows;r<0&&(r=0),void 0===(s=(i>this.s.redrawBottom||i<this.s.redrawTop)&&this.s.dt._iDisplayStart!==r&&(e=!0,i=this._domain("virtualToPhysical",t*this.s.heights.row),this.s.redrawTop<i)&&i<this.s.redrawBottom?!(this.s.forceReposition=!0):s)||s?(this.s.ani=e,d(this.dom.scroller).animate({scrollTop:i},function(){setTimeout(function(){o.s.ani=!1},250)})):d(this.dom.scroller).scrollTop(i)},construct:function(){var e=this,t=this.s.dtApi;if(!this.s.dt.oFeatures.bPaginate)throw new Error("Pagination must be enabled for Scroller to operate");this.dom.force.style.position="relative",this.dom.force.style.top="0px",this.dom.force.style.left="0px",this.dom.force.style.width="1px",this.dom.scroller=t.table().node().parentNode,this.dom.scroller.appendChild(this.dom.force),this.dom.scroller.style.position="relative",this.dom.table=d(">table",this.dom.scroller)[0],this.dom.table.style.position="absolute",this.dom.table.style.top="0px",this.dom.table.style.left="0px",d(t.table().container()).addClass("dts DTS"),this.dom.label.appendTo(this.dom.scroller),this.s.heights.row&&"auto"!=this.s.heights.row&&(this.s.autoHeight=!1),this.s.ingnoreScroll=!0,d(this.dom.scroller).on("scroll.dt-scroller",function(t){e._scroll.call(e)}),d(this.dom.scroller).on("touchstart.dt-scroller",function(){e._scroll.call(e)}),d(this.dom.scroller).on("mousedown.dt-scroller",function(){e.s.mousedown=!0}).on("mouseup.dt-scroller",function(){e.s.labelVisible=!1,e.s.mousedown=!1,e.dom.label.css("display","none")}),d(l).on("resize.dt-scroller",function(){e.measure(!1),e._info()});var i=!0,r=t.state.loaded();t.on("stateSaveParams.scroller",function(t,s,o){i&&r?(o.scroller=r.scroller,i=!1,o.scroller&&(e.s.lastScrollTop=o.scroller.scrollTop)):o.scroller={topRow:e.s.topRowFloat,baseRowTop:e.s.baseRowTop}}),t.on("stateLoadParams.scroller",function(t,s,o){void 0!==o.scroller&&e.scrollToRow(o.scroller.topRow)}),this.measure(!1),r&&r.scroller&&(this.s.topRowFloat=r.scroller.topRow,this.s.baseRowTop=r.scroller.baseRowTop,this.s.baseScrollTop=this.s.baseRowTop*this.s.heights.row,r.scroller.scrollTop=this._domain("physicalToVirtual",this.s.topRowFloat*this.s.heights.row)),e.s.stateSaveThrottle=a.util.throttle(function(){e.s.dtApi.state.save()},500),t.on("init.scroller",function(){e.measure(!1),e.s.scrollType="jump",e._draw(),t.on("draw.scroller",function(){e._draw()})}),t.on("preDraw.dt.scroller",function(){e._scrollForce()}),t.on("destroy.scroller",function(){d(l).off("resize.dt-scroller"),d(e.dom.scroller).off(".dt-scroller"),d(e.s.dt.nTable).off(".scroller"),d(e.s.dt.nTableWrapper).removeClass("DTS"),d("div.DTS_Loading",e.dom.scroller.parentNode).remove(),e.dom.table.style.position="",e.dom.table.style.top="",e.dom.table.style.left=""})},_calcRowHeight:function(){var t=this.s.dt,s=t.nTable,o=s.cloneNode(!1),e=d("<tbody/>").appendTo(o),t=t.oClasses,t=a.versionCheck("2")?{container:t.container,scroller:t.scrolling.container,body:t.scrolling.body}:{container:t.sWrapper,scroller:t.sScrollWrapper,body:t.sScrollBody},i=d('<div class="'+t.container+' DTS"><div class="'+t.scroller+'"><div class="'+t.body+'"></div></div></div>'),r=(d("tbody tr:lt(4)",s).clone().appendTo(e),d("tr",e).length);if(1===r)e.prepend("<tr><td>&#160;</td></tr>"),e.append("<tr><td>&#160;</td></tr>");else for(;r<3;r++)e.append("<tr><td>&#160;</td></tr>");d("div."+t.body,i).append(o);t=this.s.dt.nHolding||s.parentNode;d(t).is(":visible")||(t="body"),i.find("input").removeAttr("name"),i.appendTo(t),this.s.heights.row=d("tr",e).eq(1).outerHeight(),i.remove()},_draw:function(){var t=this,s=this.s.heights,o=this.dom.scroller.scrollTop,e=d(this.s.dt.nTable).height(),i=this.s.dt._iDisplayStart,r=this.s.dt._iDisplayLength,l=this.s.dt.fnRecordsDisplay(),a=o+s.viewport,n=(this.s.skip=!0,!this.s.dt.bSorted&&!this.s.dt.bFiltered||0!==i||this.s.dt._drawHold||(this.s.topRowFloat=0),o="jump"===this.s.scrollType?this._domain("virtualToPhysical",this.s.topRowFloat*s.row):o,this.s.baseScrollTop=o,this.s.baseRowTop=this.s.topRowFloat,o-(this.s.topRowFloat-i)*s.row),i=(0===i?n=0:l<=i+r?n=s.scroll-e:n+e<a&&(this.s.baseScrollTop+=1+((l=a-e)-n),n=l),this.dom.table.style.top=n+"px",this.s.tableTop=n,this.s.tableBottom=e+this.s.tableTop,(o-this.s.tableTop)*this.s.boundaryScale);this.s.redrawTop=o-i,this.s.redrawBottom=o+i>s.scroll-s.viewport-s.row?s.scroll-s.viewport-s.row:o+i,this.s.skip=!1,t.s.ingnoreScroll&&(this.s.dt.oFeatures.bStateSave&&null!==this.s.dt.oLoadedState&&void 0!==this.s.dt.oLoadedState.scroller?((r=!(!this.s.dt.sAjaxSource&&!t.s.dt.ajax||this.s.dt.oFeatures.bServerSide))&&2<=this.s.dt.iDraw||!r&&1<=this.s.dt.iDraw)&&setTimeout(function(){d(t.dom.scroller).scrollTop(t.s.dt.oLoadedState.scroller.scrollTop),setTimeout(function(){t.s.ingnoreScroll=!1},0)},0):t.s.ingnoreScroll=!1),this.s.dt.oFeatures.bInfo&&setTimeout(function(){t._info.call(t)},0),d(this.s.dt.nTable).triggerHandler("position.dts.dt",n)},_domain:function(t,s){var o,e=this.s.heights,i=1e4;return e.virtual===e.scroll||s<i?s:"virtualToPhysical"===t&&s>=e.virtual-i?(o=e.virtual-s,e.scroll-o):"physicalToVirtual"===t&&s>=e.scroll-i?(o=e.scroll-s,e.virtual-o):(e=i-(o=(e.virtual-i-i)/(e.scroll-i-i))*i,"virtualToPhysical"===t?(s-e)/o:o*s+e)},_info:function(){if(this.s.dt.oFeatures.bInfo){var t=this.s.dt,s=this.s.dtApi,o=t.oLanguage,e=s.page.info(),i=e.recordsDisplay,e=e.recordsTotal,r=(this.s.lastScrollTop-this.s.baseScrollTop)/this.s.heights.row,r=Math.floor(this.s.baseRowTop+r)+1,l=(r="jump"===this.s.scrollType?Math.floor(this.s.topRowFloat)+1:r)+Math.floor(this.s.heights.viewport/this.s.heights.row),l=i<l?i:l,a=0===i&&i==e?o.sInfoEmpty+o.sInfoPostFix:0===i?o.sInfoEmpty+" "+o.sInfoFiltered+o.sInfoPostFix:i==e?o.sInfo+o.sInfoPostFix:o.sInfo+" "+o.sInfoFiltered+o.sInfoPostFix,o=(a=this._macros(a,r,l,e,i),o.fnInfoCallback),n=(o&&(a=o.call(t.oInstance,t,r,l,e,i,a)),t.aanFeatures.i);if(void 0!==n){for(var h=0,c=n.length;h<c;h++)d(n[h]).html(a);d(t.nTable).triggerHandler("info.dt")}d("div.dt-info",s.table().container()).each(function(){d(this).html(a),s.trigger("info",[s.settings()[0],this,a])})}},_macros:function(t,s,o,e,i){var r=this.s.dtApi,l=this.s.dt,a=l.fnFormatNumber;return t.replace(/_START_/g,a.call(l,s)).replace(/_END_/g,a.call(l,o)).replace(/_MAX_/g,a.call(l,e)).replace(/_TOTAL_/g,a.call(l,i)).replace(/_ENTRIES_/g,r.i18n("entries","")).replace(/_ENTRIES-MAX_/g,r.i18n("entries","",e)).replace(/_ENTRIES-TOTAL_/g,r.i18n("entries","",i))},_parseHeight:function(t){var s,o,t=/^([+-]?(?:\d+(?:\.\d+)?|\.\d+))(px|em|rem|vh)$/.exec(t);return null!==t&&(o=parseFloat(t[1]),"px"===(t=t[2])?s=o:"vh"===t?s=o/100*d(l).height():"rem"===t?s=o*parseFloat(d(":root").css("font-size")):"em"===t&&(s=o*parseFloat(d("body").css("font-size"))),s)||0},_scroll:function(){var t,s=this,o=this.s.heights,e=this.dom.scroller.scrollTop;this.s.skip||this.s.ingnoreScroll||e!==this.s.lastScrollTop&&(this.s.dt.bFiltered||this.s.dt.bSorted?this.s.lastScrollTop=0:(clearTimeout(this.s.stateTO),this.s.stateTO=setTimeout(function(){s.s.dtApi.state.save(),s._info()},250),this.s.scrollType=Math.abs(e-this.s.lastScrollTop)>o.viewport?"jump":"cont",this.s.topRowFloat="cont"===this.s.scrollType?this.pixelsToRow(e,!1,!1):this._domain("physicalToVirtual",e)/o.row,this.s.topRowFloat<0&&(this.s.topRowFloat=0),this.s.forceReposition||e<this.s.redrawTop||e>this.s.redrawBottom?(t=Math.ceil((this.s.displayBuffer-1)/2*this.s.viewportRows),t=parseInt(this.s.topRowFloat,10)-t,this.s.forceReposition=!1,t<=0?t=0:t+this.s.dt._iDisplayLength>this.s.dt.fnRecordsDisplay()?(t=this.s.dt.fnRecordsDisplay()-this.s.dt._iDisplayLength)<0&&(t=0):t%2!=0&&t++,(this.s.targetTop=t)!=this.s.dt._iDisplayStart&&(this.s.tableTop=d(this.s.dt.nTable).offset().top,this.s.tableBottom=d(this.s.dt.nTable).height()+this.s.tableTop,t=function(){s.s.dt._iDisplayStart=s.s.targetTop,s.s.dtApi.draw("page")},this.s.dt.oFeatures.bServerSide?(this.s.forceReposition=!0,d(this.s.dt.nTable).triggerHandler("scroller-will-draw.dt"),a.versionCheck("2")?s.s.dtApi.processing(!0):this.s.dt.oApi._fnProcessingDisplay(this.s.dt,!0),clearTimeout(this.s.drawTO),this.s.drawTO=setTimeout(t,this.s.serverWait)):t())):this.s.topRowFloat=this.pixelsToRow(e,!1,!0),this._info(),this.s.lastScrollTop=e,this.s.stateSaveThrottle(),"jump"===this.s.scrollType&&this.s.mousedown&&(this.s.labelVisible=!0),this.s.labelVisible&&(t=(o.viewport-o.labelHeight-o.xbar)/o.scroll,this.dom.label.html(this.s.dt.fnFormatNumber(parseInt(this.s.topRowFloat,10)+1)).css("top",e+e*t).css("display","block"))))},_scrollForce:function(){var t=this.s.heights;t.virtual=t.row*this.s.dt.fnRecordsDisplay(),t.scroll=t.virtual,1e6<t.scroll&&(t.scroll=1e6),this.dom.force.style.height=t.scroll>this.s.heights.row?t.scroll+"px":this.s.heights.row+"px"}}),i.oDefaults=i.defaults={boundaryScale:.5,displayBuffer:9,rowHeight:"auto",serverWait:200},i.version="2.4.3",d(o).on("preInit.dt.dtscroller",function(t,s){var o,e;"dt"===t.namespace&&(t=s.oInit.scroller,o=a.defaults.scroller,t||o)&&(e=d.extend({},t,o),!1!==t)&&new i(s,e)}),d.fn.dataTable.Scroller=i,d.fn.DataTable.Scroller=i,d.fn.dataTable.Api);return t.register("scroller()",function(){return this}),t.register("scroller().rowToPixels()",function(t,s,o){var e=this.context;if(e.length&&e[0].oScroller)return e[0].oScroller.rowToPixels(t,s,o)}),t.register("scroller().pixelsToRow()",function(t,s,o){var e=this.context;if(e.length&&e[0].oScroller)return e[0].oScroller.pixelsToRow(t,s,o)}),t.register(["scroller().scrollToRow()","scroller.toPosition()"],function(s,o){return this.iterator("table",function(t){t.oScroller&&t.oScroller.scrollToRow(s,o)}),this}),t.register("row().scrollTo()",function(o){var e=this;return this.iterator("row",function(t,s){t.oScroller&&(s=e.rows({order:"applied",search:"applied"}).indexes().indexOf(s),t.oScroller.scrollToRow(s,o))}),this}),t.register("scroller.measure()",function(s){return this.iterator("table",function(t){t.oScroller&&t.oScroller.measure(s)}),this}),t.register("scroller.page()",function(){var t=this.context;if(t.length&&t[0].oScroller)return t[0].oScroller.pageInfo()}),a});

/*! Select for DataTables 3.0.0
 * © SpryMedia Ltd - datatables.net/license/mit
 */
!function(s){var l,c;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return s(e,window,document)}):"object"==typeof exports?(l=require("jquery"),c=function(e,t){t.fn.dataTable||require("datatables.net")(e,t)},"undefined"==typeof window?module.exports=function(e,t){return e=e||window,t=t||l(e),c(e,t),s(t,e,e.document)}:(c(window,l),module.exports=s(l,window,window.document))):s(jQuery,window,document)}(function(m,i,a){"use strict";var v=m.fn.dataTable;function r(n,e,t){function s(t,s){s<t&&(e=s,s=t,t=e);var e,l=!1;return n.columns(":visible").indexes().filter(function(e){return e===t&&(l=!0),e===s?!(l=!1):l})}function l(t,s){var e,l=n.rows({search:"applied"}).indexes(),c=(l.indexOf(t)>l.indexOf(s)&&(e=s,s=t,t=e),!1);return l.filter(function(e){return e===t&&(c=!0),e===s?!(c=!1):c})}var c,t=n.cells({selected:!0}).any()||t?(c=s(t.column,e.column),l(t.row,e.row)):(c=s(0,e.column),l(0,e.row)),t=n.cells(t,c).flatten();n.cells(e,{selected:!0}).any()?n.cells(t).deselect():n.cells(t).select()}function w(e){var t=v.select.classes.checkbox;return e?t.replace(/ /g,"."):t}function n(e){var t=e.settings()[0]._select.selector;m(e.table().container()).off("mousedown.dtSelect",t).off("mouseup.dtSelect",t).off("click.dtSelect",t),m("body").off("click.dtSelect"+b(e.table().node()))}function c(o){var a,t=m(o.table().container()),s=o.settings()[0],l=s._select.selector;t.on("mousedown.dtSelect",l,function(e){(e.shiftKey||e.metaKey||e.ctrlKey)&&t.css("-moz-user-select","none").one("selectstart.dtSelect",l,function(){return!1}),i.getSelection&&(a=i.getSelection())}).on("mouseup.dtSelect",l,function(){t.css("-moz-user-select","")}).on("click.dtSelect",l,function(e){var t,s=o.select.items();if(a){var l=i.getSelection();if((!l.anchorNode||m(l.anchorNode).closest("table")[0]===o.table().node())&&l!==a)return}var c,l=o.settings()[0],n=o.table().container();m(e.target).closest("div.dt-container")[0]==n&&(n=o.cell(m(e.target).closest("td, th"))).any()&&(c=m.Event("user-select.dt"),u(o,c,[s,n,e]),c.isDefaultPrevented()||(c=n.index(),"row"===s?(t=c.row,p(e,o,l,"row",t)):"column"===s?(t=n.index().column,p(e,o,l,"column",t)):"cell"===s&&(t=n.index(),p(e,o,l,"cell",t)),l._select_lastCell=c))}),m("body").on("click.dtSelect"+b(o.table().node()),function(e){var t;!s._select.blurable||m(e.target).parents().filter(o.table().container()).length||0===m(e.target).parents("html").length||m(e.target).parents("div.DTE").length||(t=m.Event("select-blur.dt"),u(o,t,[e.target,e]),t.isDefaultPrevented())||_(s,!0)})}function u(e,t,s,l){l&&!e.flatten().length||("string"==typeof t&&(t+=".dt"),s.unshift(e),m(e.table().node()).trigger(t,s))}function g(e){return e.mRender&&"selectCheckbox"===e.mRender._name}function l(l,e){var t,s,c,n,o;"api"!==l.select.style()&&!1!==l.select.info()&&(o=(n=(c=l.settings()[0])._select_set.length)||l.rows({selected:!0}).count(),t=l.columns({selected:!0}).count(),s=l.cells({selected:!0}).count(),"subtractive"===c._select_mode&&(o=l.page.info().recordsDisplay-n),c=function(e,t,s){e.append(m('<span class="select-item"/>').append(l.i18n("select."+t+"s",{_:"%d "+t+"s selected",0:"",1:"1 "+t+" selected"},s)))},n=m(e),c(e=m('<span class="select-info"/>'),"row",o),c(e,"column",t),c(e,"cell",s),(o=n.children("span.select-info")).length&&o.remove(),""!==e.text())&&n.append(e)}function d(e){var t=e.page.info();t.page<t.pages-1&&e.one("draw",function(){e.row(":first-child").node().focus()}).page("next").draw(!1)}function f(e){0<e.page.info().page&&e.one("draw",function(){e.row(":last-child").node().focus()}).page("previous").draw(!1)}function o(o){var c,a=new v.Api(o);o._select_init=!0,o._select_mode="additive",o._select_set=[],o.aoRowCreatedCallback.push(function(e,t,s){var l,c,n=o.aoData[s],s=a.row(s).id();for((n._select_selected||"additive"===o._select_mode&&o._select_set.includes(s)||"subtractive"===o._select_mode&&!o._select_set.includes(s))&&(n._select_selected=!0,m(e).addClass(o._select.className).find("input."+w(!0)).prop("checked",!0)),l=0,c=o.aoColumns.length;l<c;l++)(o.aoColumns[l]._select_selected||n._selected_cells&&n._selected_cells[l])&&m(n.anCells[l]).addClass(o._select.className)}),(c=a).on("select",function(e,t,s,l){"row"===s&&("additive"===(s=c.settings()[0])._select_mode?y:x)(c,s._select_set,l)}),c.on("deselect",function(e,t,s,l){"row"===s&&("additive"===(s=c.settings()[0])._select_mode?x:y)(c,s._select_set,l)}),a.on("info.dt",function(e,t,s){t._select.infoEls.includes(s)||t._select.infoEls.push(s),l(a,s)}),a.on("select.dtSelect.dt deselect.dtSelect.dt",function(){o._select.infoEls.forEach(function(e){l(a,e)}),a.state.save()}),a.on("destroy.dtSelect",function(){m(a.rows({selected:!0}).nodes()).removeClass(a.settings()[0]._select.className),m("input."+w(!0),a.table().header()).remove(),n(a),a.off(".dtSelect"),m("body").off(".dtSelect"+b(a.table().node()))})}function h(e,t,s,l){var c,n=e[t+"s"]({search:"applied"}).indexes(),l=n.indexOf(l),o=n.indexOf(s);e[t+"s"]({selected:!0}).any()||-1!==l?(o<l&&(c=o,o=l,l=c),n.splice(o+1,n.length),n.splice(0,l)):n.splice(n.indexOf(s)+1,n.length),e[t](s,{selected:!0}).any()?(n.splice(n.indexOf(s),1),e[t+"s"](n).deselect()):e[t+"s"](n).select()}function _(e,t){!t&&"single"!==e._select.style||((t=new v.Api(e)).rows({selected:!0}).deselect(),t.columns({selected:!0}).deselect(),t.cells({selected:!0}).deselect())}function p(e,t,s,l,c){var n=t.select.style(),o=t.select.toggleable(),a=t[l](c,{selected:!0}).any();a&&!o||("os"===n?e.ctrlKey||e.metaKey?t[l](c).select(!a):e.shiftKey?"cell"===l?r(t,c,s._select_lastCell||null):h(t,l,c,s._select_lastCell?s._select_lastCell[l]:null):(o=t[l+"s"]({selected:!0}),a&&1===o.flatten().length?t[l](c).deselect():(o.deselect(),t[l](c).select())):"multi+shift"==n&&e.shiftKey?"cell"===l?r(t,c,s._select_lastCell||null):h(t,l,c,s._select_lastCell?s._select_lastCell[l]:null):t[l](c).select(!a))}function b(e){return e.id.replace(/[^a-zA-Z0-9\-\_]/g,"-")}function y(e,t,s){for(var l=0;l<s.length;l++){var c=e.row(s[l]).id();c&&"undefined"!==c&&!t.includes(c)&&t.push(c)}}function x(e,t,s){for(var l=0;l<s.length;l++){var c=e.row(s[l]).id(),c=t.indexOf(c);-1!==c&&t.splice(c,1)}}v.select={},v.select.classes={checkbox:"dt-select-checkbox"},v.select.version="3.0.0",v.select.init=function(o){var e,t,s,l,c,n,a,i,r,u,d,f,h,_,p=o.settings()[0];if(!v.versionCheck("2"))throw"Warning: Select requires DataTables 2 or newer";!p._select&&(e=o.state.loaded(),t=function(e,t,s){if(null!==s&&void 0!==s.select){if(o.rows({selected:!0}).any()&&o.rows().deselect(),void 0!==s.select.rows&&o.rows(s.select.rows).select(),o.columns({selected:!0}).any()&&o.columns().deselect(),void 0!==s.select.columns&&o.columns(s.select.columns).select(),o.cells({selected:!0}).any()&&o.cells().deselect(),void 0!==s.select.cells)for(var l=0;l<s.select.cells.length;l++)o.cell(s.select.cells[l].row,s.select.cells[l].column).select();o.state.save()}},o.on("stateSaveParams",function(e,t,s){s.select={},s.select.rows=o.rows({selected:!0}).ids(!0).toArray(),s.select.columns=o.columns({selected:!0})[0],s.select.cells=o.cells({selected:!0})[0].map(function(e){return{row:o.row(e.row).id(!0),column:e.column}})}).on("stateLoadParams",t).one("init",function(){t(0,0,e)}),l=p.oInit.select,s=v.defaults.select,s=void 0===l?s:l,l="row",a=!(n=!(c="api")),u="td, th",d="selected",_=h=!(f=r=!(i=null)),p._select={infoEls:[]},!0===s?(c="os",h=!0):"string"==typeof s?(c=s,h=!0):m.isPlainObject(s)&&(void 0!==s.blurable&&(n=s.blurable),void 0!==s.toggleable&&(a=s.toggleable),void 0!==s.info&&(r=s.info),void 0!==s.items&&(l=s.items),h=(c=void 0!==s.style?s.style:"os",!0),void 0!==s.selector&&(u=s.selector),void 0!==s.className&&(d=s.className),void 0!==s.headerCheckbox&&(f=s.headerCheckbox),void 0!==s.selectable&&(i=s.selectable),void 0!==s.keys)&&(_=s.keys),o.select.selector(u),o.select.items(l),o.select.style(c),o.select.blurable(n),o.select.toggleable(a),o.select.info(r),o.select.keys(_),o.select.selectable(i),p._select.className=d,!h&&m(o.table().node()).hasClass("selectable")&&o.select.style("os"),f||"select-page"===f||"select-all"===f)&&o.ready(function(){var c,n,s;n=f,s=(c=o).settings()[0].aoColumns,c.columns().iterator("column",function(e,t){var l;g(s[t])&&(t=c.column(t).header(),m("input",t).length||(l=m("<input>").attr({class:w(!0),type:"checkbox","aria-label":c.i18n("select.aria.headerCheckbox")||"Select all rows"}).appendTo(t).on("change",function(){this.checked?("select-page"==n?c.rows({page:"current"}):c.rows({search:"applied"})).select():("select-page"==n?c.rows({page:"current",selected:!0}):c.rows({selected:!0})).deselect()}).on("click",function(e){e.stopPropagation()}),c.on("draw select deselect",function(e,t,s){"row"!==s&&s||((s=function(e,t){var s=e.settings()[0],l=s._select.selectable,c=0,n=("select-page"==t?e.rows({page:"current",selected:!0}):e.rows({selected:!0})).count(),o=("select-page"==t?e.rows({page:"current",selected:!0}):e.rows({search:"applied",selected:!0})).count();if(l)for(var a=("select-page"==t?e.rows({page:"current"}):e.rows({search:"applied"})).indexes(),i=0;i<a.length;i++){var r=s.aoData[a[i]];l(r._aData,r.nTr,a[i])&&c++}else c=("select-page"==t?e.rows({page:"current"}):e.rows({search:"applied"})).count();return{available:c,count:n,search:o}}(c,n)).search&&s.search<=s.count&&s.search===s.available?l.prop("checked",!0).prop("indeterminate",!1):0===s.search&&0===s.count?l.prop("checked",!1).prop("indeterminate",!1):l.prop("checked",!1).prop("indeterminate",!0))})))})})},m.each([{type:"row",prop:"aoData"},{type:"column",prop:"aoColumns"}],function(e,i){v.ext.selector[i.type].push(function(e,t,s){var l,c=t.selected,n=[];if(!0!==c&&!1!==c)return s;for(var o=0,a=s.length;o<a;o++)(l=e[i.prop][s[o]])&&(!0===c&&!0===l._select_selected||!1===c&&!l._select_selected)&&n.push(s[o]);return n})}),v.ext.selector.cell.push(function(e,t,s){var l,c=t.selected,n=[];if(void 0===c)return s;for(var o=0,a=s.length;o<a;o++)(l=e.aoData[s[o].row])&&(!0===c&&l._selected_cells&&!0===l._selected_cells[s[o].column]||!1===c&&(!l._selected_cells||!l._selected_cells[s[o].column]))&&n.push(s[o]);return n});var e=v.Api.register,t=v.Api.registerPlural;function s(t,s){return function(e){return e.i18n("buttons."+t,s)}}function C(e){e=e._eventNamespace;return"draw.dt.DT"+e+" select.dt.DT"+e+" deselect.dt.DT"+e}e("select()",function(){return this.iterator("table",function(e){v.select.init(new v.Api(e))})}),e("select.blurable()",function(t){return void 0===t?this.context[0]._select.blurable:this.iterator("table",function(e){e._select.blurable=t})}),e("select.toggleable()",function(t){return void 0===t?this.context[0]._select.toggleable:this.iterator("table",function(e){e._select.toggleable=t})}),e("select.info()",function(t){return void 0===t?this.context[0]._select.info:this.iterator("table",function(e){e._select.info=t})}),e("select.items()",function(t){return void 0===t?this.context[0]._select.items:this.iterator("table",function(e){e._select.items=t,u(new v.Api(e),"selectItems",[t])})}),e("select.keys()",function(s){return void 0===s?this.context[0]._select.keys:this.iterator("table",function(e){var o,t;e._select||v.select.init(new v.Api(e)),e._select.keys=s,o=new v.Api(e),t=(e=o.settings()[0])._select.keys,e="dts-keys-"+e.sTableId,t?(m(o.rows({page:"current"}).nodes()).attr("tabindex",0),o.on("draw."+e,function(){m(o.rows({page:"current"}).nodes()).attr("tabindex",0)}),m(a).on("keydown."+e,function(e){var t,s,l,c=e.keyCode,n=a.activeElement;[9,13,32,38,40].includes(c)&&(l=!0,-1!==(s=(t=o.rows({page:"current"}).nodes().toArray()).indexOf(n)))&&(9===c?!1===e.shift&&s===t.length-1?d(o):!0===e.shift&&0===s?f(o):l=!1:13===c||32===c?(n=o.row(n)).selected()?n.deselect():n.select():38===c?0<s?t[s-1].focus():f(o):s<t.length-1?t[s+1].focus():d(o),l)&&(e.stopPropagation(),e.preventDefault())})):(m(o.rows().nodes()).removeAttr("tabindex"),o.off("draw."+e),m(a).off("keydown."+e))})}),e("select.style()",function(s){return void 0===s?this.context[0]._select.style:this.iterator("table",function(e){e._select||v.select.init(new v.Api(e)),e._select_init||o(e),e._select.style=s;var t=new v.Api(e);"api"!==s?t.ready(function(){n(t),c(t)}):n(t),u(new v.Api(e),"selectStyle",[s])})}),e("select.selector()",function(l){return void 0===l?this.context[0]._select.selector:this.iterator("table",function(e){var t=new v.Api(e),s=e._select.style;n(t),e._select.selector=l,s&&"api"!==s?t.ready(function(){n(t),c(t)}):n(t)})}),e("select.selectable()",function(e){var t=this.context[0];return e?(t._select.selectable=e,this):t._select.selectable}),e("select.last()",function(e){var t=this.context[0];return e?(t._select_lastCell=e,this):t._select_lastCell}),e("select.cumulative()",function(l){var e;return l?this.iterator("table",function(e){var t,s;e._select_mode!==l&&(t=new v.Api(e),"subtractive"===l?(s=t.rows({selected:!1}).ids().toArray(),e._select_mode=l,e._select_set.length=0,e._select_set.push.apply(e._select_set,s)):(s=t.rows({selected:!0}).ids().toArray(),e._select_mode=l,e._select_set.length=0,e._select_set.push.apply(e._select_set,s)))}).draw(!1):(e=this.context[0])&&e._select_set?{mode:e._select_mode,rows:e._select_set}:null}),t("rows().select()","row().select()",function(e){var o=this,a=[];return!1===e?this.deselect():(this.iterator("row",function(e,t){_(e);var s=e.aoData[t],l=e.aoColumns;if(e._select.selectable&&!1===e._select.selectable(s._aData,s.nTr,t))return;m(s.nTr).addClass(e._select.className),s._select_selected=!0,a.push(t);for(var c=0;c<l.length;c++){var n=l[c];null===n.sType&&o.columns().types(),g(n)&&((n=s.anCells)&&n[c]&&m("input."+w(!0),n[c]).prop("checked",!0),null!==s._aSortData)&&(s._aSortData[c]=null)}}),this.iterator("table",function(e){u(o,"select",["row",a],!0)}),this)}),e("row().selected()",function(){var e=this.context[0];return!!(e&&this.length&&e.aoData[this[0]]&&e.aoData[this[0]]._select_selected)}),e("row().focus()",function(){var e=this.context[0];e&&this.length&&e.aoData[this[0]]&&e.aoData[this[0]].nTr&&e.aoData[this[0]].nTr.focus()}),e("row().blur()",function(){var e=this.context[0];e&&this.length&&e.aoData[this[0]]&&e.aoData[this[0]].nTr&&e.aoData[this[0]].nTr.blur()}),t("columns().select()","column().select()",function(e){var s=this;return!1===e?this.deselect():(this.iterator("column",function(e,t){_(e),e.aoColumns[t]._select_selected=!0;t=new v.Api(e).column(t);m(t.header()).addClass(e._select.className),m(t.footer()).addClass(e._select.className),t.nodes().to$().addClass(e._select.className)}),this.iterator("table",function(e,t){u(s,"select",["column",s[t]],!0)}),this)}),e("column().selected()",function(){var e=this.context[0];return!!(e&&this.length&&e.aoColumns[this[0]]&&e.aoColumns[this[0]]._select_selected)}),t("cells().select()","cell().select()",function(e){var s=this;return!1===e?this.deselect():(this.iterator("cell",function(e,t,s){_(e);t=e.aoData[t];void 0===t._selected_cells&&(t._selected_cells=[]),t._selected_cells[s]=!0,t.anCells&&m(t.anCells[s]).addClass(e._select.className)}),this.iterator("table",function(e,t){u(s,"select",["cell",s.cells(s[t]).indexes().toArray()],!0)}),this)}),e("cell().selected()",function(){var e=this.context[0];if(e&&this.length){e=e.aoData[this[0][0].row];if(e&&e._selected_cells&&e._selected_cells[this[0][0].column])return!0}return!1}),t("rows().deselect()","row().deselect()",function(){var o=this;return this.iterator("row",function(e,t){var s=e.aoData[t],l=e.aoColumns;m(s.nTr).removeClass(e._select.className),s._select_selected=!1,e._select_lastCell=null;for(var c=0;c<l.length;c++){var n=l[c];null===n.sType&&o.columns().types(),g(n)&&((n=s.anCells)&&n[c]&&m("input."+w(!0),s.anCells[c]).prop("checked",!1),null!==s._aSortData)&&(s._aSortData[c]=null)}}),this.iterator("table",function(e,t){u(o,"deselect",["row",o[t]],!0)}),this}),t("columns().deselect()","column().deselect()",function(){var s=this;return this.iterator("column",function(l,e){l.aoColumns[e]._select_selected=!1;var t=new v.Api(l),s=t.column(e);m(s.header()).removeClass(l._select.className),m(s.footer()).removeClass(l._select.className),t.cells(null,e).indexes().each(function(e){var t=l.aoData[e.row],s=t._selected_cells;!t.anCells||s&&s[e.column]||m(t.anCells[e.column]).removeClass(l._select.className)})}),this.iterator("table",function(e,t){u(s,"deselect",["column",s[t]],!0)}),this}),t("cells().deselect()","cell().deselect()",function(){var s=this;return this.iterator("cell",function(e,t,s){t=e.aoData[t];void 0!==t._selected_cells&&(t._selected_cells[s]=!1),t.anCells&&!e.aoColumns[s]._select_selected&&m(t.anCells[s]).removeClass(e._select.className)}),this.iterator("table",function(e,t){u(s,"deselect",["cell",s[t]],!0)}),this});var k=0;return m.extend(v.ext.buttons,{selected:{text:s("selected","Selected"),className:"buttons-selected",limitTo:["rows","columns","cells"],init:function(s,e,l){var c=this;l._eventNamespace=".select"+k++,s.on(C(l),function(){var e,t;c.enable((e=s,!(-1===(t=l).limitTo.indexOf("rows")||!e.rows({selected:!0}).any())||!(-1===t.limitTo.indexOf("columns")||!e.columns({selected:!0}).any())||!(-1===t.limitTo.indexOf("cells")||!e.cells({selected:!0}).any())))}),this.disable()},destroy:function(e,t,s){e.off(s._eventNamespace)}},selectedSingle:{text:s("selectedSingle","Selected single"),className:"buttons-selected-single",init:function(t,e,s){var l=this;s._eventNamespace=".select"+k++,t.on(C(s),function(){var e=t.rows({selected:!0}).flatten().length+t.columns({selected:!0}).flatten().length+t.cells({selected:!0}).flatten().length;l.enable(1===e)}),this.disable()},destroy:function(e,t,s){e.off(s._eventNamespace)}},selectAll:{text:s("selectAll","Select all"),className:"buttons-select-all",action:function(e,t,s,l){var c=this.select.items(),n=l.selectorModifier;(n?("function"==typeof n&&(n=n.call(t,e,t,s,l)),this[c+"s"](n)):this[c+"s"]()).select()}},selectNone:{text:s("selectNone","Deselect all"),className:"buttons-select-none",action:function(){_(this.settings()[0],!0)},init:function(t,e,s){var l=this;s._eventNamespace=".select"+k++,t.on(C(s),function(){var e=t.rows({selected:!0}).flatten().length+t.columns({selected:!0}).flatten().length+t.cells({selected:!0}).flatten().length;l.enable(0<e)}),this.disable()},destroy:function(e,t,s){e.off(s._eventNamespace)}},showSelected:{text:s("showSelected","Show only selected"),className:"buttons-show-selected",action:function(e,t){var l;t.search.fixed("dt-select")?(t.search.fixed("dt-select",null),this.active(!1)):(l=t.settings()[0].aoData,t.search.fixed("dt-select",function(e,t,s){return l[s]._select_selected}),this.active(!0)),t.draw()}}}),m.each(["Row","Column","Cell"],function(e,t){var c=t.toLowerCase();v.ext.buttons["select"+t+"s"]={text:s("select"+t+"s","Select "+c+"s"),className:"buttons-select-"+c+"s",action:function(){this.select.items(c)},init:function(e){var l=this;this.active(e.select.items()===c),e.on("selectItems.dt.DT",function(e,t,s){l.active(s===c)})}}}),v.type("select-checkbox",{className:"dt-select",detect:v.versionCheck("2.1")?{oneOf:function(){return!1},allOf:function(){return!1},init:function(e,t,s){return g(t)}}:function(e){return"select-checkbox"===e&&e},order:{pre:function(e){return"X"===e?-1:0}}}),m.extend(!0,v.defaults.oLanguage,{select:{aria:{rowCheckbox:"Select row"}}}),v.render.select=function(e,t){function s(e,t,s,l){var c=l.settings.aoData[l.row],n=c._select_selected,o=l.settings.oLanguage.select.aria.rowCheckbox,a=l.settings._select.selectable;return"display"!==t?"type"===t?"select-checkbox":"filter"!==t&&n?"X":"":a&&!1===a(s,c.nTr,l.row)?"":m("<input>").attr({"aria-label":o,class:w(),name:r?r(s):null,type:"checkbox",value:i?i(s):null,checked:n}).on("input",function(e){e.preventDefault(),this.checked=m(this).closest("tr").hasClass("selected")})[0]}var i=e?v.util.get(e):null,r=t?v.util.get(t):null;return s._name="selectCheckbox",s},v.ext.order["select-checkbox"]=function(t,e){return this.api().column(e,{order:"index"}).nodes().map(function(e){return"row"===t._select.items?m(e).parent().hasClass(t._select.className).toString():"cell"===t._select.items&&m(e).hasClass(t._select.className).toString()})},m.fn.DataTable.select=v.select,m(a).on("i18n.dt.dtSelect preInit.dt.dtSelect",function(e,t){"dt"===e.namespace&&v.select.init(new v.Api(t))}),v});

