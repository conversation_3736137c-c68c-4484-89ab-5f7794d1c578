.mashup.mashup-style {
	.loading-spinner{
		background: none;
		border: 16px solid #f3f3f3; /* Light grey */
		border-top: 16px solid #3498db; /* Blue */
		border-radius: 50%;
		width: 60px;
		height: 60px;
		animation: spin 2s linear infinite;
		top: 50%;
		left: 50%;

		&.small{
			border: 4px solid #f3f3f3;
			border-top: 4px solid #3498db;
			border-radius: 50%;
			width: 10px;
			height: 10px;
		}

		&.no-margin{
			margin:0;
		}
	}
	.loading-overlay{
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	.hide-spinner {
		.loading-spinner {
			border: none;
		}
	}
}
