<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>


<render:import parameters="content,extraCss,direction,buttonId" />

<span id="${buttonId}" class="fonticon fonticon-info-circled ${extraCss}"></span>


<render:renderScript position="READY">
	var optionsPop = {};
	optionsPop.button = document.getElementById('${buttonId}');
	optionsPop.content = "<div>${content}</div>";
	optionsPop.direction = '${direction}';
	var popup = new AppsPopup(optionsPop);
</render:renderScript>