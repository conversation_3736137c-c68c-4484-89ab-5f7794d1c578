/**
 * Preferred hit library
 *
 * @param options Library options
 * @constructor Constructor
 */
var PreferredHit = function (options) {
    this.options = options;
    this.statusSelector = '.favorites-actions';
    this.init();
};

/**
 * Init button event handler
 */
PreferredHit.prototype.init = function () {
    // Different divs can contain a favorite widget for same hit --> so, disable click event for all and re-init for all
    var selectorId = '.preferred-hit[data-id="' + this.options.docId + '"]';
    $(selectorId).off('click').on('click', $.proxy(function (e) {
        // Disable parent div event
        e.stopPropagation();
        // Get hit ID to store in 'dataset-id' attribute
        var id = $(e.target).data('id');
        // Get favorite button state in 'dataset-status' attribute
        var status = $(e.target).data('status');

        // If doc is in favorite --> remove, else, add it
        if (status == 'on') {
            $.ajax({
                method: 'DELETE',
                url: this.options.url + '/delete/' + this.options.collection + '/' + encodeURIComponent(id),
                dataType: 'JSON',
                async: false,
                success: function (data) {
                    $(selectorId).toggleClass('fonticon-favorite-on fonticon-favorite-off');
                    $(selectorId).data('status', 'off');
                    $.notify('Hit successfully removed from favorites', "success");
                },
                error: function (data) {
                    $.notify('Error removing hit from favorites', "error");
                }
            });
        } else {
            $.ajax({
                method: 'POST',
                url: this.options.url + '/save',
                dataType: 'JSON',
                async: false,
                data: {
                    collection: this.options.collection,
                    hit: id
                },
                success: function (data) {
                    $(selectorId).toggleClass('fonticon-favorite-on fonticon-favorite-off');
                    $(selectorId).data('status', 'on');
                    $.notify('Hit successfully added to favorites', "success");
                },
                error: function (data) {
                    $.notify('Error adding hit to favorites (' + data.responseJSON.error + ')', "error");
                }
            });
        }
        this.refreshCount();
    }, this));
}

/**
 * Refresh div displaying favorites count
 */
PreferredHit.prototype.refreshCount = function () {
    var countSelector = this.options.counterSelector;
    var collectionCount = 0;
    $.ajax({
        method: 'GET',
        url: this.options.url + '/count/' + this.options.collection,
        dataType: 'JSON',
        async: false,
        success: function (data) {
            collectionCount = data.answer;
        },
        error: function (data) {
            console.error("Error retrieving favorites count")
        }
    });

    // Set counter div text
    $(countSelector).each(function () {
        $(this).find('.counter').text(collectionCount);
        $(this).find('.counter').attr('data-count', collectionCount);
        $("#clear-preferred-hits").attr( "data-count", collectionCount );
    });
    // Set data attribute for every 'count-aware' icons (css are based on that and applicable to multiple icons)

    var minSize = 0;
    if ($(this.statusSelector).data('min-size') != '') {
        minSize = parseInt($(this.statusSelector).data('min-size'));
    }
    if ((collectionCount > 0 && $(this.statusSelector).hasClass('fonticon-' + this.options.buttonPrefix + '-off')) ||
        (collectionCount == 0 && $(this.statusSelector).hasClass('fonticon-' + this.options.buttonPrefix + '-on'))) {
        $(this.statusSelector).toggleClass('fonticon-' + this.options.buttonPrefix + '-off fonticon-' + this.options.buttonPrefix + '-on');
    }

    if (minSize > 0 && collectionCount < minSize) {
        $(this.statusSelector).addClass('hidden');
    } else if ($(this.statusSelector).hasClass('hidden')) {
        $(this.statusSelector).removeClass('hidden');
    }
}