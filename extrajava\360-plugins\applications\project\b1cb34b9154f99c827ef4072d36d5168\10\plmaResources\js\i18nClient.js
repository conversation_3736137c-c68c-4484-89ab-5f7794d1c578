function I18nClient(_this, options) {
	this.options = $.extend({
		spinner: true
	}, options);

	this.ajaxRequest = null;
	this.ajaxResult = null;
	this.paths = {};
	this.url = mashup.baseUrl + "/c/i18n/";
};


I18nClient.prototype.getI18n = function (key, success, fail) {
	var s = success || $.noop;
	var f = fail || $.noop;
	this.ajaxRequest = $.ajax({
		type: 'GET',
		url: this.url + 'get',
		data: {
			key: key
		},
		cache: false,
		success: function (data, textStatus) {
			s(data);
		},
		error: function (XMLHttpRequest, textStatus, errorThrown) {
			f(errorThrown);
		}
	});
};

I18nClient.prototype.getI18nList = function (keys, success, fail) {
	var s = success || $.noop;
	var f = fail || $.noop;
	var Url = this.url + 'getList';
	this.ajaxRequest = $.ajax({
		type: 'POST',
		url: Url,
		data: {
		    keys: JSON.stringify(keys)
		},
		cache: false,
		success: function (data, textStatus) {
			s(data);
		},
		error: function (XMLHttpRequest, textStatus, errorThrown) {
			f(errorThrown);
		}
	});
};


