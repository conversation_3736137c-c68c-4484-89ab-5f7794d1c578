.transition (@transition) {
	-webkit-transition: @transition;
	-moz-transition: @transition;
	-ms-transition: @transition;
	-o-transition: @transition;
	transition: @transition;
}

.transition (@transition , @property) {
	.transition (@transition);
	-webkit-transition-property: @property;
	-moz-transition-property: @property;
	-ms-transition-property: @property;
	-o-transition-property: @property;
	transition-property: @property;
}

.transition-rotate(){
	-moz-transform: rotate(360deg);
	-ms-transform: rotate(360deg);
	-webkit-transform: rotate(360deg);
	transform: rotate(360deg);
}

.transition-rotateY(){
	-moz-transform: rotateY(360deg);
	-ms-transform: rotateY(360deg);
	-webkit-transform: rotateY(360deg);
	transform: rotateY(360deg);
}

.transition-rotateX(){
	-moz-transform: rotateX(360deg);
	-ms-transform: rotateX(360deg);
	-webkit-transform: rotateX(360deg);
	transform: rotateX(360deg);
}

.transition-skewX(){
	-moz-transform: skewX(180deg);
	-ms-transform: skewX(180deg);
	-webkit-transform: skewX(180deg);
	transform: skewX(180deg);
}

.transition-scale(@scale){
	-moz-transform: scale(@scale);
	-ms-transform: scale(@scale);
	-webkit-transform: scale(@scale);
	transform: scale(@scale);
}

.box-shadow(...) {
	-moz-box-shadow: @arguments; 
	-webkit-box-shadow: @arguments;
	box-shadow: @arguments;
}


@scale-smaller: 		0.7;
@scale-bigger: 			1.3;

@transition-simple: 	ease-out 0.5s;

@transition-quick: 		200ms ease-in-out 0ms;
@transition-normal: 	400ms ease-in-out 0ms;
@transition-long: 		600ms ease-in-out 200ms;

@delay-quick:			50ms ease-in-out 150ms;
@delay-normal:			200ms ease-in-out 300ms;
@delay-long:			200ms ease-in-out 400ms;