var Enrich3DAPI = function(cssId, dataSeriesConf, options){
	this.chartData = undefined;
	this.options = $.extend({}, Enrich3DAPI.DEFAULT_OPTIONS, options);
	if( options.logicPathMetaName == undefined || 
		options.pathMetaName == undefined || 
		options.indexHasPaths == undefined){
		$.notify('Missing mandatory options ["logicPathMetaName", "pathMetaName", "indexHasPaths"]', 'error');
		throw 'Missing mandatory options ["logicPathMetaName", "pathMetaName", "indexHasPaths"]';
	}
	
	let $widget = $('#' + cssId);
	this.chartData = {
		wuid: $widget.attr('class').split(' ')[1],
		index: $widget.find('.chart-wrapper .chart-inner .highChartsSVGWrapper').data('highchartsChart'),
		configs: dataSeriesConf.dataSeriesConfiguration? dataSeriesConf.dataSeriesConfiguration : dataSeriesConf
	}
	Enrich3DAPILayerBuilder.init();
	return this;
};

Enrich3DAPI.POI = 'poi';
Enrich3DAPI.COLORIZE = 'colorize';
Enrich3DAPI.GET_PATHS_DATA = '/enrich3d/getPathsData';
Enrich3DAPI.DEFAULT_OPTIONS = {
	/*Mandatory*/
	logicPathMetaName: undefined,
	pathMetaName: undefined,
	indexHasPaths: undefined,
	
	/*Optional/Defaults*/
	verbose: true,
	maxHitsToFetch: 100,
    transpLayerStyle: { color: 'white', opacity: 0.1 }, /*For colorization*/
    pathTitleFormat: '{name}', /*For POI format can be 'mytext-{meta2}-{meta2}'*/
    pathTitleMetas: 'name', /* meta name as per search logics. it can be comma separated list meta1,meta2 */

    /* Function to modify/define the query for provided layerDef object. This will help
     * to filter and bring the relevant results in access request fired by controller to
     * build the path data. Parameters supplied to fuction are
     * @param {string} Default layer query
     * @param {object} Layerdef object with all the details like categoryId, seriesName, catName etc.
     * @param {object} Data Series Configuration object for current layer(as comming from chartData.
     * */
	modifyLayerQuery: $.noop,
	/* Function to modify the layerDef data before calling the controller.
     * Some times needed to tweak the facet names to make sure the entries can be matched correctly.
     * @param {object} layersDefData Json object.
     * */
	modifyLayerDefData: $.noop,
	
	/* Function to modify the data just before calling publish evnet.
	 * Like tweaking some options for clustering in POI data etc.
	 * */
	modifyEventData: $.noop
}

/* Entry point function to start the publish of event.
 * */
Enrich3DAPI.prototype.publish = function(queryString) {
	if(!Enrich3DAPIHelper.enabled()){
		$.notify(Enrich3DAPIHelper._getMessage('plma.error.outside.3dexperience'), 'error');
		return;
	}

	let loadedRootIds = SessionContent.getContent();
	if(!Enrich3DAPI.isDebug && loadedRootIds.length == 0){
		$.notify(Enrich3DAPIHelper._getMessage('plma.error.no.3dcontent.incontext'), 'error');
		return;
	}else if(this.options.indexHasPaths){
		// Only when path based, we will be able to filter the parts using root ids.
		queryString = Enrich3DAPIHelper._addRootIdsInQuery(loadedRootIds, queryString, this.options.logicPathMetaName);
	}

	let layerBuilderOptions = {
		modifyLayerQuery: this.options.modifyLayerQuery
	}

	Enrich3DAPILayerBuilder.show(this.chartData, layerBuilderOptions, function(layersDefData, apiName, $notifyContainer){
		if(layersDefData == undefined || layersDefData.layers.length == 0){
			$notifyContainer.notify(Enrich3DAPIHelper._getMessage('plma.enrich3d.error.no.data.from.chart'), 'error');
			return;
		}

		if($.isFunction(this.options.modifyLayerDefData) && this.options.modifyLayerDefData != $.noop){
			layersDefData = this.options.modifyLayerDefData(layersDefData);
		}

		if(this.options.verbose){
			console.log('Loaded RootIds', loadedRootIds);
			console.log('layersDefData', layersDefData);
			console.log('QueryString', queryString);
		}

		let optionsData = {
			pageName: mashup.pageName,
			hitCount: this.options.maxHitsToFetch,
			logicPathMetaName: this.options.logicPathMetaName,
			pathMetaName: this.options.pathMetaName,
			loadedRootIds: this.options.indexHasPaths? loadedRootIds : undefined
		}
		if(apiName == Enrich3DAPI.POI){
			optionsData.pathTitleFormat = this.options.pathTitleFormat;
			optionsData.pathTitleMetas = this.options.pathTitleMetas;
		}
		Enrich3DAPILayerBuilder.getSpinnerContainer().showPLMASpinner({overlay: true});
		$.ajax({
			type: 'POST',
			url: mashup.baseUrl + Enrich3DAPI.GET_PATHS_DATA + (queryString?queryString:''),
			data: {
				layersDefData: JSON.stringify(layersDefData),
				optionsData: JSON.stringify(optionsData)
			}
		})
		.done(function(response){
			Enrich3DAPILayerBuilder.getSpinnerContainer().hidePLMASpinner();
			if(this.options.verbose){ console.log("LayerData from Controller", response.data); }

			let foundProblem = this._analyzeResponse(response, $notifyContainer);
			if(foundProblem){
				return;
			}

			// Build event data and publish
			let eventData = undefined;
			if(apiName == Enrich3DAPI.POI){
				eventData = Enrich3DAPI.EventHelpers.toPOIData(response.data, $notifyContainer);
			}else{
				eventData = Enrich3DAPI.EventHelpers.toColorizeData(response.data, $notifyContainer, loadedRootIds, this.options.transpLayerStyle);
			}

			if($.isFunction(this.options.modifyEventData) && this.options.modifyEventData != $.noop){
				this.options.modifyEventData.call(null, eventData.data);
			}

			if(this.options.verbose){ console.log('Event Data', eventData); }
			Enrich3DAPI.EventHelpers.publishEvent(eventData);
		}.bind(this))
		.fail(function(jqXHR, textStatus){
			$notifyContainer.notify(Enrich3DAPIHelper._getMessage('plma.enrich3d.error.controller.failed'), 'error');
			console.error("Status=" + jqXHR.responseJSON.status);
			let $disaplyBoard = Enrich3DAPILayerBuilder.getDisplayBoard();
			$disaplyBoard.append($('<span class="badge badge-failure">' +
				jqXHR.responseJSON.status +
			'</span>'));
			$disaplyBoard.parent().removeClass('hidden');
			Enrich3DAPILayerBuilder.getSpinnerContainer().hidePLMASpinner();
		});
	}.bind(this));
}

/**
 * Analyze the status from the controller response. Create disaply, console log and notify issues if any.
 */
Enrich3DAPI.prototype._analyzeResponse = function(response, $notifyContainer) {
	let foundProblem = false;
	let status = response.status;

	let $disaplyBoard = Enrich3DAPILayerBuilder.getDisplayBoard();
	let $layersInfoContainer = $('<ul class="layers-received"></ul>');
	$layersInfoContainer.append($('<li class="header">' +
		'<span class="layer-label">Layer Label</span>' +
		'<span class="layer-entry-count">Matched Entries</span>' +
	'</li>'));
	$disaplyBoard.append($layersInfoContainer);
	response.data.layersData.forEach(function(layerData){
		$layersInfoContainer.append($('<li class="row">' +
			'<span class="layer-label">'+ layerData.label +'</span>' +
			'<span class="layer-entry-count">' + layerData.entryUrls.length + '</span>' +
		'</li>'));
	});

	let $feedStatusContainer = $('<div class="feed-status-all"></div>');
	$disaplyBoard.append($feedStatusContainer);
	Object.keys(status).forEach(function(feedId){
		let feedStatus = status[feedId];
		let feedStatusMsg = '<span><b>Status of feed [{0}] </b></span>'.replace('{0}', feedId) +
			'<span>[ Total entries: <b>{0}</b> ]</span>'.replace('{0}', feedStatus.totalEntries) +
			'<span>[ Unmatched entries: <b>{0}</b> ]</span>'.replace('{0}', feedStatus.unmatchedEntries) +
			'<span>[ Entries with missing paths: <b>{0}</b> ]</span>'.replace('{0}', feedStatus.missingPaths);
		$feedStatusContainer.append($('<div class="feed-status">' +
			feedStatusMsg +
		'</div>'));

		if(feedStatus.totalEntries == 0){
			$notifyContainer.notify(Enrich3DAPIHelper._getMessage('plma.entrich3d.error.no.entries'), 'error');
			foundProblem = true;
		}else if(feedStatus.unmatchedEntries == feedStatus.totalEntries){
			$notifyContainer.notify(Enrich3DAPIHelper._getMessage('plma.entrich3d.error.no.matches'), 'error');
			foundProblem = true;
		}else if(feedStatus.unmatchedEntries > 0){
			let msg = Enrich3DAPIHelper._getMessage('plma.entrich3d.warn.unmatched.entries')
							.replace('{0}', feedStatus.unmatchedEntries)
							.replace('{1}', feedStatus.totalEntries);
			$notifyContainer.notify(msg, 'warn');
		}
		if( (feedStatus.totalEntries - feedStatus.unmatchedEntries) > 0 && // We can only tell it when we found entries to check the path.
			feedStatus.hasAtLeastOnePath != this.options.indexHasPaths){
			$notifyContainer.notify(Enrich3DAPIHelper._getMessage('plma.entrich3d.warn.index.has.paths'), 'warn');
		}

		if(this.options.verbose){
			let missingFacets = '';
			Object.keys(feedStatus.missingFacets).forEach(function(facetId){
				missingFacets += (missingFacets.length > 0? ', (': '(') + facetId + '=' + feedStatus.missingFacets[facetId] + ')';
			});
			console.log('Status of feed [%s] =>\n \t[ Total entries : %d ],\n \t[ Unmatched entries : %d ],\n ' +
				'\t[ Entries with missing paths : %d ],\n \t[ Missing Facets : %s ],\n \t[ Feed Query : %s ]',
				feedId, feedStatus.totalEntries, feedStatus.unmatchedEntries,
				feedStatus.missingPaths, missingFacets, feedStatus.feedQuery);
			console.log("Returned: feedStatus",  feedStatus);
		}
	}.bind(this));
	$disaplyBoard.parent().removeClass('hidden');
	return foundProblem;
}

Enrich3DAPIHelper = (function(){
	return {
		/**
		 * One entrypoint to check if this functionality is enabled or not.
		 */
		enabled: function(){
			return window.dashboardController != undefined;
		},

		/**
		 * Generic function to modify the query string.
		 * e.g. buildQueryString('${request.queryString}', [ { param: 'a', value: 'b', override: true }...]);
		 *   override default is false.
		 * @param {string} baseQueryString must start with '?'
		 * @param {array} additionalParams is array of objects of format
		 * 				  { param:'string', value:'string', override: 'boolean'(default false)}
		 */
		buildQueryString: function(baseQueryString, additionalParams){
			if(baseQueryString.startsWith('?') == false){
				throw 'baseQueryString should startsWith "?". Remove the context path from the baseQueryString';
			}
			let url = new BuildUrl(baseQueryString);
			if(url.getPath()){
				throw 'Only QueryString Expected. Remove the context path from the baseQueryString';
			}
			additionalParams.forEach(function(paramKV){
				if(paramKV.param == 'q' && url.getParameter('q')){
					// Need to merge both 'q' params.
					let urlQ = '(' + url.getParameter('q')[0] + ') AND (' + paramKV.value + ')';
					url.addParameter('q', urlQ, true);
				}else{
					let override = paramKV.override? true : false;
					url.addParameter(paramKV.param, paramKV.value, override);
				}
			});
			return url.toString();
		},

		/**
		 * For internal use
		 * If the facet used for the series is DYNDATE then it is not supported. As DYNDATE is not available in entries.
		 * Hence resulting in inability to match entries.
		 */
		_checkSupportedSerie: function(chartConf){
			let notSupported = (chartConf.facetType == 'DYNDATE') ||
				(chartConf.isMultiDimensionFacet &&
					(chartConf.multiDimensionFacetTypes[0] == 'DYNDATE' || chartConf.multiDimensionFacetTypes[1] == 'DYNDATE'));
			return !notSupported;
		},

		/**
		 * For internal use
		 * This added rootIds in query which will filter the result to bring only the Documents
		 * where pathMeta contains one of the given root Ids. To make this work the index must contains
		 * paths and the data model field must be searchable. Filtering helps to retirve the hits only
		 * applicable in current context maximizing the output.
		 */
		_addRootIdsInQuery: function(rootIds, queryString, queryPrefix){
			if(rootIds.length == 0){ return queryString; }
			if(!queryString) { queryString = '?'; }

			let q = queryPrefix + ':(';
				rootIds.forEach(function(root, index){
					q += (index > 0 ? ' OR ' : '') + '"' + root + '"';
				});
			q += ')';
			return Enrich3DAPIHelper.buildQueryString(queryString, [ { param: 'q', value: q } ]);
		},

		/**
		 * For internal use
		 */
		_getMessage: function(code){
		    return mashupI18N.get('plmaResources', code);
		}
	}
})();

Enrich3DAPI.EventHelpers = {
	/**
	 * Convert data returned by controller to Colorize event data.
	 */
	toColorizeData: function(pathsData, $notifyContainer, loadedRootIds, transpLayerStyle){
		var eventData = Enrich3DAPI.EventHelpers._getEventDataStub();
		eventData.data = {
			graphicProperties: {
				layers: []
			}
		}
		if(pathsData == undefined){
			eventData.empty = true;
			return eventData;
		}
		Enrich3DAPI.EventHelpers._converColors(pathsData.styles);

		let transpStyleId = pathsData.styles.length;
		transpLayerStyle.forceInherit = false;
		pathsData.styles.push(transpLayerStyle);

		let fnLayerStub = function(layerLabel){ return { label: layerLabel, colorMap: [], styles: pathsData.styles }; };

		// We can get the same layer(with same label) data splitted by colors in different arrays.
		let layerMap = {};
		pathsData.layersData.forEach(function(layerData){
			if(layerData.pathTitles.length > 0){
				let layerLabel = layerData.label;
				let layer = layerMap[ layerLabel ];
				if(layer == undefined){
					layer = fnLayerStub(layerLabel);

					//Add transperancy to rootIds so that the other parts can be seen easily...
					loadedRootIds.forEach(function(rootId){
						layer.colorMap.push({
							resourceid: rootId,
							style: transpStyleId
						});
					});
					eventData.data.graphicProperties.layers.push(layer);
					layerMap[layerLabel] = layer;
				}

				// Path Titles will only contain paths...titles won;t be there as we are not passing
				// pathTitleFormat option while calling controller in case of colorize.
				layerData.pathTitles.forEach(function(pathOrRef){
					if(pathOrRef.search('/') != -1){
						layer.colorMap.push({
							path: pathOrRef.split('/'),
							style: layerData.style
						});
					}else{
						layer.colorMap.push({
							resourceid: pathOrRef,
							style: layerData.style
						});
					}
				});

			}
		});
		if(eventData.data.graphicProperties.layers.length == 0){
			$notifyContainer.notify(Enrich3DAPIHelper._getMessage('plma.enrich3d.error.controller.failed'), 'error');
			return undefined;
		}
		return eventData;
	},
	/**
	 * Convert data returned by controller to POI event data.
	 */
	toPOIData: function(pathsData, $notifyContainer){
		var eventData = Enrich3DAPI.EventHelpers._getEventDataStub();
		eventData.data = {
			pointOfInterest: {
				enableClustering: true,
				enableProximityMerging: false,
				layerColorEditable: false,
				layers: []
			}
		}
		if(pathsData == undefined){
			eventData.empty = true;
			return eventData;
		}

		Enrich3DAPI.EventHelpers._converColors(pathsData.styles);
		let fnLayerStub = function(layerLabel){
			return {
				label: layerLabel,
				visibility: true,
				rootOpacity: 0.5,
				pointMap: [],
				styles: pathsData.styles
			};
		};

		let layerMap = {};
		let pointId = 0;
		pathsData.layersData.forEach(function(layerData){
			if(layerData.pathTitles.length > 0){
				let layerLabel = layerData.label;
				let layer = layerMap[ layerLabel ];
				if(layer == undefined){
					layer = fnLayerStub(layerLabel);
					layer.color = pathsData.styles[layerData.style].color; //TODO: WHY?
					eventData.data.pointOfInterest.layers.push(layer);
					layerMap[layerLabel] = layer;
				}

				// Here we will have paths and for each path we will have titles of matched entries.
				layerData.pathTitles.forEach(function(pathData){
					let pointData = {
						id: 'point-' + pointId,
						label: pathData.titles.join(", "),
						style: layerData.style
					};

					if(pathData.path.search('/') != -1){
						pointData.path = pathData.path.split('/');
					}else{
						pointData.resourceid = pathData.path;
					}
					layer.pointMap.push(pointData);
					pointId++;
				});
			}
		});
		if(eventData.data.pointOfInterest.layers.length == 0){
			$notifyContainer.notify(Enrich3DAPIHelper._getMessage('plma.enrich3d.error.controller.failed'), 'error');
			return undefined;
		}
		return eventData;
	},
	/**
	 * Publish the enrich3D event with provided event data
	 */
	publishEvent: function(eventData){
		if(Enrich3DAPIHelper.enabled()){
			window.dashboardController.publishTopic('DS/PADUtils/PADCommandProxy/enrich3D', eventData);
		}
	},

	/**
	 * For internal use
	 */
	_getEventDataStub: function(){
		var widgetInfos = window.dashboardController.widgetInfos;
		return {
			metadata: {
				uid: Math.round((Math.pow(36, length + 1) - Math.random() * Math.pow(36, length))).toString(36).slice(1),
				originUid: widgetInfos.widgetId,
				timestamp: Date.now(),
				appid: widgetInfos.appId,
				originWidgetId: widgetInfos.widgetId
			},
			data: {}
		};
	},

	_converColors: function(styles){
		styles.forEach(function(style){
			let rgb = ColorPicker.parseRgb(style.color);
			style.color = ColorPicker.rgb2hex(rgb);
		});
	}
}

/* This objects is a sigleton object which defines Layer Builder UI and helps
 * Users to define/modify/store the layers definition.
 */
Enrich3DAPILayerBuilder = (function() {
	const SAVE_ACTIONS = { CHANGES:'save_changes', LAST_API:'save_lastapi', DELETE:'delete' };
	let lightbox = undefined;
	let colorPicker = undefined;
	let $elements = {};
	let LAYER_DEF_STORAGE_KEY = "enrich3d_layerdef_" + mashup.pageName;
	let pageEnrich3DLayerDef = {};
	let _ctx = {};

	$elements.MAIN_CONTAINER = $(
		'<div class="container">'+
			'<div class="icons-container">' +
				'<span class="plma-checkbox-container action-enable-layer-query">' +
					'<input class="decorate plma-checkbox add-layer-query" type="checkbox"' +
						'name="layer-checkbox" id="add-layer-query" value="layer" checked>' +
					'<label class="truncate" for="add-layer-query"><span class="fonticon">' +
					    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.layerquery.add') +
					'</span></label>' +
				'</span>' +
				'<span class="fonticon fonticon-search-filter action-query-view" title="' +
				    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.layerquery.show') +
				'"></span>' +
				'<span class="fonticon data-invalidate-menu disabled">' +
					'<span class="fonticon fonticon-floppy action-save-layers" title="' +
					    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.changes.save') +
					'"></span>' +
					'<span class="fonticon fonticon-undo action-undo-layers" title="' +
					    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.changes.undo') +
					'"></span>' +
				'</span>' +
				'<span class="fonticon fonticon-database-delete action-reset-layers" title="' +
				    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.restore.default') +
				'"></span>' +
				'<span class="fonticon fonticon-publish action-publish" title="' +
				    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.publish.event') +
				'"></span>' +
				'<span class="action-api-select">' +
					'<span class="plma-checkbox-container">' +
						'<input class="decorate plma-radiobutton" type="radio"' +
							'name="enrich-api-radio" id="enrich-poi" value="' + Enrich3DAPI.POI + '">' +
						'<label class="truncate" for="enrich-poi">' +
							'<span class="fonticon fonticon-3ds-where">' +
							    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.api.poi') +
							'</span>' +
						'</label>' +
					'</span>' +
					'<span class="plma-checkbox-container">' +
						'<input class="decorate plma-radiobutton" type="radio"' +
							'name="enrich-api-radio" id="enrich-colorize" value="' + Enrich3DAPI.COLORIZE +'">' +
						'<label class="truncate" for="enrich-colorize">' +
							'<span class="fonticon fonticon-palette">' +
							    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.api.colorize') +
							'</span>' +
						'</label>' +
					'</span>' +
				'</span>' +
			'</div>' +
			'<div class="display-container hidden">' +
				'<div class="fonticon board"></div>' +
			'</div>' +
			'<div class="layers-container">' +
				'<div class="label-container"> ' +
					'<div class="column-label column-label-visibility plma-checkbox-container" title="' +
					    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.select.all') +
					'">' +
						'<input class="decorate plma-checkbox display-layer" type="checkbox"' +
							'name="layer-checkbox" id="layers-all-checkbox" value="layer" checked>' +
						'<label class="pull-left truncate" for="layers-all-checkbox"></label>' +
					'</div>' +
					'<div class="column-label column-label-series sortable" data-selector="serie-label">' +
						'<span class="fonticon fonticon-sorting"/>' + Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.column.series') +
					'</div>' +
					'<div class="column-label column-label-categories sortable" data-selector="category-label">' +
						'<span class="fonticon fonticon-sorting"/>' + Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.column.categories') +
					'</div>' +
					'<div class="column-label column-label-agg-value sortable" data-selector="agg-value">' +
						'<span class="fonticon fonticon-sorting"/>' + Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.column.aggvalue') +
					'</div>' +
					'<div class="column-label column-label-layers">' +
					    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.column.layerlabel') +
                    '</div>' +
					'<div class="column-label column-label-color">' +
					    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.column.color') +
					'</div>' +
				'</div>' +
				'<ul class="layer-list edit-enabled">' +
				'</ul>' +
				'<ul class="not-supported-layer-list hidden" title="' +
				    Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.series.notsupported') +
				'">' +
					'<label class="title fonticon">' + Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.series.notsupported') + '</label>'+
				'</ul>' +
			'</div>' +
		'</div>');

	$elements.LAYER_TEMPLATE = $(
		'<li class="layer">' +
			'<div class="plma-checkbox-container">' +
				'<input class="decorate plma-checkbox display-layer" type="checkbox"' +
					'name="layer-checkbox" id="" value="layer" checked>' +
				'<label class="pull-left truncate" for=""></label>' +
			'</div>' +
			'<div class="serie-label"></div>' +
			'<div class="category-label"></div>' +
			'<div class="agg-value"></div>' +
			'<div class="layer-label">' +
				'<input class="layer-label-input" type="text" value="">' +
			'</div>' +
			'<div class="layer-color action-edit-color"><span class="fonticon fonticon-cog"></span></div>' +
		'</li>');

	$elements.LAYER_LIST = $elements.MAIN_CONTAINER.find('ul.layer-list');
	$elements.NOT_SUPP_LAYER_LIST = $elements.MAIN_CONTAINER.find('ul.not-supported-layer-list');
	$elements.DATA_INVALIDATED_MENU = $elements.MAIN_CONTAINER.find('.data-invalidate-menu');
	$elements.COLOR_PICKER_CONTAINER = $();
	$elements.NOTIFY_CONTAINER = $elements.MAIN_CONTAINER;

	/**
     * Initialize the sigleton object.
     */
	let _init = function(){
		// Get layer definitions from storage.
		$elements.MAIN_CONTAINER.closest('.plmalightbox-box.layers-builder').showPLMASpinner({overlay: true});
		var storage = new StorageClient("user");
		storage.get(LAYER_DEF_STORAGE_KEY, function (items) {
			if (items.length > 0) {
				pageEnrich3DLayerDef = JSON.parse(items[0].value);
			}
			$elements.MAIN_CONTAINER.closest('.plmalightbox-box.layers-builder').hidePLMASpinner();
		});
	}

	/**
     * Saves the current pageEnrich3DLayerDef to storage.
     * @param {function} funtion to call after the db is updated
     */
	let _saveConfigurations = function(success) {
		$elements.MAIN_CONTAINER.closest('.plmalightbox-box.layers-builder').showPLMASpinner({overlay: true});
		var storage = new StorageClient("user");
		storage.set(LAYER_DEF_STORAGE_KEY, JSON.stringify(pageEnrich3DLayerDef), function(res){
			success();
			$elements.MAIN_CONTAINER.closest('.plmalightbox-box.layers-builder').hidePLMASpinner();
		});
	}

	/**
     * Layer id is defined by category path(categoryId) + aggregation name.
     * @param {object} layer data build from serie config and definition.
     */
	let _getLayerId = function(data){
		return data.categoryId + '#' + _ctx.layersDefData.configList[data.configIndex].aggregation;
	}

	/**
     * Create/Update the ui for Layers List based on layers from _ctx object.
     * @param {boolean} onlyUpdate the data, do not create. This will be called from undo & reset data.
     */
	let _renderLayers = function(onlyUpdate){
		_ctx.renderLayers = {};
		_ctx.layersDefData.layers.forEach(function(data, index){
			let $layerDef = data.uiElement;
			if(onlyUpdate != true){
				$layerDef = $elements.LAYER_TEMPLATE.clone();
				$layerDef.find('input.plma-checkbox.display-layer').attr('id', 'layer-checkbox_' + index)
				$layerDef.find('label').attr('for', 'layer-checkbox_' + index);

				$layerDef.find('div.serie-label').text(data.serieName);
				$layerDef.find('div.category-label').text(data.catName);
				let aggVal = data.aggVal? data.aggVal : 0;
				$layerDef.find('div.agg-value')
					.data('floatvalue', aggVal)
					.text(Number.isInteger(aggVal)?aggVal: aggVal.toFixed(3));
				$layerDef.find('div.layer-color').css('background-color', data.color);
				/*Set the color to rgb() format so as to ease compare during save*/
				data.color = $layerDef.find('div.layer-color').css('background-color');
				data.uiElement = $layerDef;

				if(data.supported){
					$elements.LAYER_LIST.append($layerDef);
				}else{
					$elements.NOT_SUPP_LAYER_LIST.append($layerDef);
				}
			}
			// Get the default layerDef and layerDef from storage(if any). and update ui accordingly.
			let layerId = _getLayerId(data);
			let layerData = $.extend(true, {
				enabled: true,
				label: data.serieName + ':' + data.catName,
				color: data.color
			}, _ctx.storedLayerDef[layerId]);
			if(_ctx.storedLayerDef[layerId] != undefined){
				_ctx.renderLayers[layerId] = true;
			}
			$layerDef.find('input.plma-checkbox.display-layer').prop('checked', data.supported && layerData.enabled).prop('disabled', !data.supported);
			$layerDef.find('div.layer-label input').val(layerData.label).prop('disabled', !data.supported);
			$layerDef.find('div.layer-color').css('background-color', layerData.color);
		});

		$elements.MAIN_CONTAINER.find('.action-enable-layer-query.plma-checkbox-container .plma-checkbox')
			.prop('checked', _ctx.storedLayerDef.enableQuery == undefined? true : _ctx.storedLayerDef.enableQuery);

		$elements.MAIN_CONTAINER.find('.action-api-select .plma-radiobutton[id=enrich-' + _ctx.lastSelectedApi + ']')
			.prop('checked', true);

		_updateSelectAllCheckBox();
		_onDataChange(false);

		if($elements.NOT_SUPP_LAYER_LIST.find('li.layer').length > 0 ){
			$elements.NOT_SUPP_LAYER_LIST.removeClass('hidden');
		}else{
			$elements.NOT_SUPP_LAYER_LIST.addClass('hidden');
		}

		if(onlyUpdate){ // No need to change any events....as we just updated the UI data values.
			_hideColorPicker($elements.COLOR_PICKER_CONTAINER.data('colorelement'));
			return;
		}else{	// handle All events.
		    // Turn off events when the layers count is zero and return
            if($elements.LAYER_LIST.find('li.layer').length == 0){
                $elements.MAIN_CONTAINER.find('.action-api-select .plma-radiobutton').off('click');
                $elements.MAIN_CONTAINER.find('.action-publish').off('click');
                $elements.MAIN_CONTAINER.find('.label-container .plma-checkbox-container .plma-checkbox').off('click');
                $elements.MAIN_CONTAINER.find('.action-enable-layer-query.plma-checkbox-container .plma-checkbox').off('click');
                $elements.MAIN_CONTAINER.find('.action-reset-layers').off('click');
                $elements.MAIN_CONTAINER.find('.label-container .column-label.sortable').off('click');
                $elements.MAIN_CONTAINER.find('.action-query-view').off('click');
                return;
            }
            // Handle API Change.
			$elements.MAIN_CONTAINER.find('.action-api-select .plma-radiobutton')
				.off('click')
				.on('click', function(){
					if($elements.DATA_INVALIDATED_MENU.hasClass('disabled') == false){
						$elements.NOTIFY_CONTAINER.notify(Enrich3DAPIHelper._getMessage('plma.entrich3d.error.unsaved.changes'), 'error');
						return false; // Prevents change of the radio button state.
					}
					_ctx.lastSelectedApi = $(this).val();
					if(pageEnrich3DLayerDef[_ctx.chartData.wuid] && pageEnrich3DLayerDef[_ctx.chartData.wuid][_ctx.lastSelectedApi]){
						_ctx.storedLayerDef = pageEnrich3DLayerDef[_ctx.chartData.wuid][_ctx.lastSelectedApi];
					}else{
						_ctx.storedLayerDef = {};
					}
					_renderLayers(true);
					return true;
				});

			// Handle Publish of the even.
			$elements.MAIN_CONTAINER.find('.action-publish')
				.off('click')
				.on('click', function(){
					_callPublishHandler();
				});

			// Handle select all checkbox click
			$elements.MAIN_CONTAINER.find('.label-container .plma-checkbox-container .plma-checkbox')
				.off('click')
				.on('click', function(e){
					var enabled = $(this).is(':checked');
					$(this).removeClass('partial');
					$elements.LAYER_LIST.find('li.layer .plma-checkbox-container .plma-checkbox').prop('checked', enabled);
					_updateLayerQuery();
					_onDataChange(true);
				});

			// Handle Restore Default Data Event
			$elements.MAIN_CONTAINER.find('.action-enable-layer-query.plma-checkbox-container .plma-checkbox')
				.off('click')
				.on('click', function(){
					_onDataChange(true);
				});

			// Delete LayerDef modification from storage.
			$elements.MAIN_CONTAINER.find('.action-reset-layers')
				.off('click')
				.on('click', function(){
					_saveLayerDef(SAVE_ACTIONS.DELETE);
				});

			// Handle sortable column click;
			$elements.MAIN_CONTAINER.find('.label-container .column-label.sortable')
				.off('click')
				.on('click', function(){
					let currOrder = $(this).data('order');
					let colSelector = '.' + $(this).data('selector');
					$elements.LAYER_LIST.find('li.layer').sort(function(a, b) {
						if(colSelector == '.agg-value'){
							let v1 = $(a).find(colSelector).data('floatvalue');
							let v2 = $(b).find(colSelector).data('floatvalue');
							return (v1 < v2 ? -1 : (v1 > v2 ? 1 : 0));
						}
						// Text sort
						return $(a).find(colSelector).text().localeCompare($(b).find(colSelector).text());
					}).each(function(index, el) {
						if(currOrder == true){
							$elements.LAYER_LIST.prepend(el);
						}else{
							$elements.LAYER_LIST.append(el);
						}
					});
					$(this).data('order', !currOrder);
				});

			// Handle Query view event
			$elements.MAIN_CONTAINER.find('.action-query-view')
				.off('click')
				.on('click', function(){
					$(this).toggleClass('active');
					if($(this).hasClass('active')){
						_updateLayerQuery();
						$elements.MAIN_CONTAINER.find('.display-container').removeClass('hidden');
					}else{
						$elements.MAIN_CONTAINER.find('.display-container').addClass('hidden');
					}
				});


			// As layers are created at the start of the function no event trunoff is required.
			// Handle User Changes events for visibility, color, label of layer.
			$elements.LAYER_LIST.find('li.layer .plma-checkbox-container .plma-checkbox')
				.on('click', function(){
					_onDataChange(true);
					_updateSelectAllCheckBox();
				});
			$elements.LAYER_LIST.find('li.layer div.layer-color')
				.on('click', _handleColorPickerDisplay);
			$elements.LAYER_LIST.find('li.layer div.layer-label input')
				.on('keyup', function(){
					_onDataChange(true);
				});
		}
	}

	/**
     * Set the state of selectAll checkbox based on the the current layers selection.
     */
	let _updateSelectAllCheckBox = function(){
		let enabledLength = $elements.LAYER_LIST.find('li.layer .plma-checkbox-container .plma-checkbox:is(:checked)').length;
		let layerLength = $elements.LAYER_LIST.find('li.layer').length;

		let $selectAllCheckbox = $elements.LAYER_LIST.parent().find('.label-container .plma-checkbox-container .plma-checkbox');
		if(enabledLength > 0 && enabledLength != layerLength){
			$selectAllCheckbox.addClass('partial');
		}else{
			/*When all enabled or all disabled*/
			$selectAllCheckbox.prop('checked', enabledLength > 0);
			$selectAllCheckbox.removeClass('partial');
		}
		_updateLayerQuery();
	}

	/**
     * Build layersDefData based on user modifications(enabled layers only), and calls the publishHandler.
     */
	let _callPublishHandler = function(){
		// Check if ready to publish
		let enabledLength = $elements.LAYER_LIST.find('li.layer .plma-checkbox-container .plma-checkbox:is(:checked)').length;
		if(enabledLength == 0){
			$elements.NOTIFY_CONTAINER.notify(Enrich3DAPIHelper._getMessage('plma.entrich3d.error.no.layer.selected'), 'error');
			return;
		}
		if($elements.DATA_INVALIDATED_MENU.hasClass('disabled') == false){
			$elements.NOTIFY_CONTAINER.notify(Enrich3DAPIHelper._getMessage('plma.entrich3d.error.unsaved.changes'), 'error');
			return;
		}

		let layersDefData = {
			configList: [],
			layers: [],
			feedsQuery: _ctx.storedLayerDef.enableQuery == false ? undefined : _ctx.layersDefData.feedsQuery
		};

		_ctx.layersDefData.configList.forEach(function(config){
			layersDefData.configList.push({
				feed: config.feed,
				facet: config.facet,
				multiDimensionFacetIds: config.multiDimensionFacetIds
			});
		});

		// Use info from stored Data only...Unsaved changes are ignored.
		_ctx.layersDefData.layers.forEach(function(data, index){
			if(data.supported){
				let layerId = _getLayerId(data);
				let layerData = $.extend(true, {
					enabled: true,
					label: data.serieName + ':' + data.catName,
					color: data.color
				}, _ctx.storedLayerDef[layerId]);

				if(layerData.enabled){
					layersDefData.layers.push({
						categoryId: data.categoryId,
						label: layerData.label,
						color: layerData.color,
						configIndex: data.configIndex
					});
				}
			}
		});
		_ctx.publishHandler(layersDefData, _ctx.lastSelectedApi, $elements.NOTIFY_CONTAINER);
	}

	/**
     * This function will be called from places where data will be modified. It manages the state
	 * of save and undo buttons.
     * @param {boolean} Data changed from stored values. When reseted/undone, the changed = false.
     */
	let _onDataChange = function(changed){
		if(changed){
			if($elements.DATA_INVALIDATED_MENU.hasClass('disabled') == true){
				$elements.DATA_INVALIDATED_MENU.removeClass('disabled');
				$elements.DATA_INVALIDATED_MENU.find('.action-save-layers')
					.on('click', function(){
						_saveLayerDef(SAVE_ACTIONS.CHANGES);
					});
				$elements.DATA_INVALIDATED_MENU.find('.action-undo-layers')
					.on('click', function(){
						_renderLayers(true); // Only Update the layer display to match the storage values.
					});
			}
		}else{
			$elements.DATA_INVALIDATED_MENU.addClass('disabled');
			$elements.DATA_INVALIDATED_MENU.find('.action-save-layers').off('click');
			$elements.DATA_INVALIDATED_MENU.find('.action-undo-layers').off('click');
		}
	}

	/**
     * This function will be build the query based on enabled layers only.
     * @param {boolean} Optionally update the query UI.
     */
	let _updateLayerQuery = function(){
		_ctx.layersDefData.feedsQuery = {};

		// Build Query for enabled layer list....
		let feedQueries = {};
		// Use info from stored Data only...Unsaved changes are ignored.
		_ctx.layersDefData.layers.forEach(function(data, index){
			if(data.supported){
				let $layer = data.uiElement;
				if($layer.find('.plma-checkbox').is(':checked')){
					let config = _ctx.layersDefData.configList[data.configIndex];
					let layerQuery = undefined;
					if(config.multiDimensionFacetIds){
						layerQuery = '(' +
							config.multiDimensionFacetIds[1] + ': "' + data.serieName + '" AND ' +
							config.multiDimensionFacetIds[0] + ': "' + data.catName + '")';
					}else{
						layerQuery = '(' + config.facet + ': "' + data.catName + '")';
					}
					if($.isFunction(_ctx.options.modifyLayerQuery) && _ctx.options.modifyLayerQuery != $.noop){
						layerQuery = _ctx.options.modifyLayerQuery(layerQuery, data, config);
					}
					if(layerQuery){
						if(feedQueries[config.feed] == undefined){
							feedQueries[config.feed] = [];
						}
						feedQueries[config.feed].push(layerQuery);
					}
				}
			}
		});

		let $disaplyBoard = $elements.MAIN_CONTAINER.find('.display-container .board').empty();
		let $feedQueryContainer = $('<div class="feed-query-all"></div>');
		$disaplyBoard.append($feedQueryContainer);
		let feedIds = Object.keys(feedQueries);
		if(feedIds.length == 0){
			$feedQueryContainer.append($('<div class="feed-query">' +
				'<span><b>{0}</b></span>'.replace('{0}', 'NO QUERY') +
			'</div>'))
			return;
		}
		feedIds.forEach(function(feedId){
			_ctx.layersDefData.feedsQuery[feedId] = _.uniq(feedQueries[feedId]).join(' OR ');
				
			let feedQueryMsg = '<span><b>Query for feed [{0}] </b></span>'.replace('{0}', feedId) +
				'<span>{0}</span>'.replace('{0}', _ctx.layersDefData.feedsQuery[feedId]);
			$feedQueryContainer.append($('<div class="feed-query">' +
				feedQueryMsg +
			'</div>'))
		});
	}
	
	/**
     * This function builds the layerDef considering user changes and saves it to storage. 
     * @param {string} what to save 'delete', 'changes', 'lastapi'.
     */
	let _saveLayerDef = function(action){
		let newLayersDef = {};
		let foundChanges = false;
		if(action == SAVE_ACTIONS.CHANGES){
			_ctx.layersDefData.layers.forEach(function(data, index){
				if(data.supported){
					let layerId = _getLayerId(data);
					/*Add only changes which are not matching default*/
					let layerDataDefault = {
						enabled: true,
						label: data.serieName + ':' + data.catName,
						color: data.color
					};
					
					let currLayerData = {
						enabled: data.uiElement.find('.plma-checkbox-container .plma-checkbox').is(':checked'),
						label: data.uiElement.find('div.layer-label input').val().trim(),
						color: data.uiElement.find('div.layer-color').css('background-color')
					}
					
					let finalData = {};
					let hasChanges =  false;
					if(layerDataDefault.enabled != currLayerData.enabled){
						finalData.enabled = currLayerData.enabled;
						hasChanges = true;
					}
					if(layerDataDefault.color != currLayerData.color){
						finalData.color = currLayerData.color;
						hasChanges = true;
					}
					if(layerDataDefault.label != currLayerData.label){
						finalData.label = currLayerData.label;
						hasChanges = true;
					}				
					if(hasChanges){
						newLayersDef[layerId] = finalData;
						foundChanges = true;
					}
				}
			});
			// Default value for add query is true, if the value is false then update in db.
			if($elements.MAIN_CONTAINER.find('.action-enable-layer-query.plma-checkbox-container .plma-checkbox').is(':checked') == false){
				newLayersDef.enableQuery = false;
				foundChanges = true;
			}else{
				delete newLayersDef.enableQuery;				
			}
			
			// If any layers not rendered currently exists add them as is so that we don''t loose them. 
			// This happens when the data is saved when no filters but now opended with filters hence having
			// only few layers.
			Object.keys(_ctx.storedLayerDef).forEach(function(layerId){
				if(layerId != 'enableQuery' && _ctx.renderLayers[layerId] == undefined){
					newLayersDef[layerId] = _ctx.storedLayerDef[layerId];
					foundChanges = true;
				}
			});
		}
		if(pageEnrich3DLayerDef[_ctx.chartData.wuid] == undefined){
			pageEnrich3DLayerDef[_ctx.chartData.wuid] = {};
		}
		if(action != SAVE_ACTIONS.LAST_API){
			if(foundChanges){
				pageEnrich3DLayerDef[_ctx.chartData.wuid][_ctx.lastSelectedApi] = newLayersDef;
			}else{
				delete pageEnrich3DLayerDef[_ctx.chartData.wuid][_ctx.lastSelectedApi];
			}
		}
		
		pageEnrich3DLayerDef[_ctx.chartData.wuid].lastSelectedApi = 
			$elements.MAIN_CONTAINER.find('.action-api-select .plma-radiobutton:checked').val();
			
		_saveConfigurations(function(){
			if(action != SAVE_ACTIONS.LAST_API){
				_ctx.storedLayerDef = newLayersDef; // Save the stored layersDef in context
				_renderLayers(true);  // Reset the layer data as per saved config.
			}
		});
	}
	
	/**
     * Internal main entrypoint to show the LayerDef builder UI 
     * @param {function} Calls the function once user finalizes the inputs, and finalized layerDef will be passed to this function.
     */	
	let _onShow = function() { 
		// Get matching conf to process serieData;
		let chartSeries = Highcharts.charts[_ctx.chartData.index].series;
				
		// Build layerDef data from the chart series and configurations. Once done, call renderLayers to show the data.
		_ctx.chartData.configs.every(function(chartConf, configIndex){
			let regExp = undefined;
			if(chartConf.isMultiDimensionFacet){
				regExp = new RegExp('.*' + chartConf.multiDimensionFacetIds[1].toLowerCase().replaceAll('_', '[_/]') + '/.*#' + chartConf.aggregation + '$');
			}else{
				regExp = new RegExp('.*' + chartConf.facet.toLowerCase().replaceAll('_', '[_/]') + '#' + chartConf.aggregation + '$');						
			}
			_ctx.layersDefData.configList.push({
				feed: chartConf.feed,
				facet: chartConf.facet,
				aggregation: chartConf.aggregation,
				multiDimensionFacetIds: chartConf.isMultiDimensionFacet? chartConf.multiDimensionFacetIds : undefined,
			})
			chartSeries.forEach(function(serie, serieIndex){
				if(regExp.test(serie.options.id)){
					serie.data.forEach(function(data, catIndex){
						let empty = (!data.y || data.y == 0);									
						let layerData = {
							categoryId: data.id.substring( 0 , data.id.lastIndexOf( "#" ) ),
							color: data.color,
							aggVal: data.y, /*If color values needs to be calculated from aggvalue, adding the value.*/
							serieName: serie.name.trim(),
							catName: data.name.trim(),
							configIndex: configIndex,
							supported: Enrich3DAPIHelper._checkSupportedSerie(chartConf)
						};
						_ctx.layersDefData.layers.push(layerData);						
					});
				}
			});
			return true;
		});		
		_renderLayers();
	}
	
	/**
     * When the popup is hidden make sure all the ui and data elements are reseted. as the same popup
	 * will be reused by all the charts.
     */	
	let _onHide = function() { 
		_saveLayerDef(SAVE_ACTIONS.LAST_API);
		$elements.LAYER_LIST.find('li.layer').remove();
		$elements.NOT_SUPP_LAYER_LIST.find('li.layer').remove();
		$elements.COLOR_PICKER_CONTAINER.addClass('hidden');
		$elements.MAIN_CONTAINER.find('.action-query-view').removeClass('active');
		$elements.MAIN_CONTAINER.find('.display-container').addClass('hidden');
		$elements.MAIN_CONTAINER.find('.label-container .plma-checkbox-container .plma-checkbox').off();
		_onDataChange(false);
		_ctx = {};		
	}
	
	/**
     * Show color picker. Create only once and then keep on changing position & colorElement info.
     */	 
	// for Ignoring updateCallback when ColorPicker.setRgb is called first time.
	let _isShowingColorPicker = undefined; 
	let _showColorPicker = function ($colorElement) {
		_isShowingColorPicker = true;
		if(colorPicker == undefined){ // If not already present create the new one.
			let pickerWrapper = $('<div class="picker-wrapper"></div>');
			let picker = $('<div class="picker"></div>');
			let pickerIndicator = $('<div class="picker-indicator"></div>');
			pickerWrapper.append(picker).append(pickerIndicator);

			let sliderWrapper = $('<div class="slider-wrapper"></div>');
			let slider = $('<div class="slider"></div>');
			let sliderIndicator = $('<div class="slider-indicator"></div>');
			sliderWrapper.append(slider).append(sliderIndicator);
						
			$elements.COLOR_PICKER_CONTAINER = $('<div class="color-picker-container"></div>');
			$elements.COLOR_PICKER_CONTAINER.append(sliderWrapper).append(pickerWrapper);
			
			_addExtraColorPallets($elements.COLOR_PICKER_CONTAINER);
			
			let colorpickerOverlay = $('<div class="colorpicker-overlay" title="Close Color Picker"></div>'); 
			$elements.LAYER_LIST.prepend(colorpickerOverlay).prepend($elements.COLOR_PICKER_CONTAINER);
			colorpickerOverlay.on('click', _handleColorPickerDisplay);
			
			ColorPicker.fixIndicators(sliderIndicator[0], pickerIndicator[0]);			
			colorPicker = ColorPicker(slider[0], picker[0], function (hex, hsv, rgb, pickerCoordinate, sliderCoordinate) {
				ColorPicker.positionIndicators(sliderIndicator[0], pickerIndicator[0], sliderCoordinate, pickerCoordinate);
				if (pickerCoordinate != undefined && !isNaN(pickerCoordinate.x)) {
					$elements.COLOR_PICKER_CONTAINER.data('colorelement').css('background-color', hex);
					if(_isShowingColorPicker == false){
						_onDataChange(true);
					}
				}
			});
		}
		
		// Position the color picker based on current colorElement position.
		$elements.COLOR_PICKER_CONTAINER.removeClass('hidden');
		let elPosition = $colorElement.position();
		let layerListPosition = $elements.LAYER_LIST.position();
		let layerRightX = layerListPosition.left + $elements.LAYER_LIST.width();
		$elements.COLOR_PICKER_CONTAINER.css('top', elPosition.top -1);
		$elements.COLOR_PICKER_CONTAINER.css('right', layerRightX - elPosition.left - 13);
		
		if($elements.COLOR_PICKER_CONTAINER.data('colorelement') != undefined){
			// Remove last active element class;
			$elements.COLOR_PICKER_CONTAINER.data('colorelement').removeClass('active');
		}
		$elements.COLOR_PICKER_CONTAINER.data('colorelement', $colorElement);
		
		let rgb = ColorPicker.parseRgb($colorElement.css('background-color'));
		colorPicker.setRgb(rgb);
		$colorElement.addClass('active');
		_isShowingColorPicker = false;
	}

	let _hideColorPicker = function($colorElement) {
		// Remove last active element class and hide the color picker.
		if($colorElement){ $colorElement.removeClass('active'); }
		$elements.COLOR_PICKER_CONTAINER.addClass('hidden');
	}
		
	let _handleColorPickerDisplay = function(e){
		let $colorElement = $(this);
		if($colorElement.hasClass('colorpicker-overlay')){
			$colorElement = $elements.COLOR_PICKER_CONTAINER.data('colorelement');
		}
		if($colorElement.hasClass('active')){
			_hideColorPicker($colorElement);
		}else{
			_showColorPicker($colorElement);
		}
	}
	
	let _addExtraColorPallets = function($container){
		/* Generated from UIKit web page */		
		let palletArray = {
			'Green palette': [ "#EDF6EB", "#57B847", "#477738" ],
			'Red palette': [ "#FFF0EE", "#EA4F37", "#844138" ],
			'Orange palette': [ "#FFF3E9", "#E87B00", "#8F4C00" ],
			'Cyan palette': [ "#F2F5F7", "#00B8DE", "#0087A3" ],
			'Blue palette': [ "#d5e8f2", "#78befa", "#42a2da", "#368ec4", "#005686", "#003c5a" ],
			'Grey palette': [ "#f9f9f9", "#f4f5f6", "#f1f1f1", "#e2e4e3", "#d1d4d4", "#b4b6ba", "#77797c", "#3d3d3d" ],
			'General': [ "#FEE000", "#009DDB", "#FF8A2E", "#9B2C98", "#00AA86", "#F564A3", "#8DDEE4", "#CC092F", "#E2D5CC", 
					"#6FBC4B", "#70036b", "#002596", "#C6C68B", "#7F7F7F", "#4EA8B7", "#007A4C", "#ADADAD", "#127277", 
					"#6D2815", "#000000", "#4D62BA" ]
		};
		
		let $colorPalletWrapper = $('<div class="pallet-wrapper"></div>');		
		Object.keys(palletArray).forEach(function(key, i){
			let cssClass = i < 4 ? 'col-50' : 'col-100';
			let $pallet = $(
				'<div class="pallet ' + cssClass + '">' +
					'<span class="title">' + key + '</span>' +
					'<span class="colors"></span>' +
				'</div>');
			$colorPalletWrapper.append($pallet);
			let colorsContainer = $pallet.find('.colors');
			palletArray[key].forEach(function(color){
				colorsContainer.append( $('<span class="color"/>').css('background-color', color));
			});
		});
		
		$container.append($colorPalletWrapper);
		$colorPalletWrapper.find('.color').on('click', function(){
			let rgb = ColorPicker.parseRgb($(this).css('background-color'));
			colorPicker.setRgb(rgb);
		});
	}
	
	let _getLightbox = function(){
		if(lightbox == undefined){
			lightbox = new PLMALightbox({
				title: Enrich3DAPIHelper._getMessage('plma.entrich3d.popup.header'),
				content: $elements.MAIN_CONTAINER,
				extraCss: "layers-builder",
				onHide: _onHide,
				onShow: function(){
					let supportedLayersCount = $elements.LAYER_LIST.find('li.layer').length;
					if(supportedLayersCount == 0){
						$elements.NOTIFY_CONTAINER.notify(Enrich3DAPIHelper._getMessage('plma.entrich3d.error.no.layers.found'), 'error');
					}
				},
				draggable : false
			});
		}
		return lightbox;
	}
	
	let _initContextObject = function(){
		_ctx = {
			storedLayerDef: {},
			renderLayers: {},
			layersDefData: {
				configList: [],
				layers:[]
			},
			chartData: {
				wuid: undefined,
				index: undefined,
				configs: undefined 
			},
			publishHandler: undefined,
			lastSelectedApi: undefined
		};
	}
	
	return {
	    init: function(){
	        _init();
	    },

		/**
		 * Main entrypoint to show the LayerDef builder UI 
		 * @param {object}   Chart configs object 'dataSeriesConfiguration'.
		 * @param {function} Calls the function once user finalizes the inputs, and finalized layerDef will be passed to this function.		 
		 */	
		show: function(chartData, options, publishHandler){
			_initContextObject();			
			_ctx.chartData.wuid = chartData.wuid;
			_ctx.chartData.index = chartData.index;
			_ctx.chartData.configs = chartData.configs;	
			_ctx.options = options;	
			_ctx.publishHandler = publishHandler;
			_ctx.storedLayerDef = {};
			if(pageEnrich3DLayerDef[_ctx.chartData.wuid]){
				let widgetStoredData = pageEnrich3DLayerDef[_ctx.chartData.wuid];
				_ctx.lastSelectedApi = widgetStoredData.lastSelectedApi? widgetStoredData.lastSelectedApi : Enrich3DAPI.POI;
				if(widgetStoredData[_ctx.lastSelectedApi]){
					_ctx.storedLayerDef = widgetStoredData[_ctx.lastSelectedApi];
				}
			}else{
				_ctx.lastSelectedApi = Enrich3DAPI.POI;
			}
			
			if(_ctx.chartData.index == undefined){
				publishHandler(undefined, _ctx.lastSelectedApi, $elements.NOTIFY_CONTAINER);
			}else{				
				_onShow();
				_getLightbox().show();
			}
		},
		/**
		 * Provides the display board to print some information for user.
		 */	
		getDisplayBoard: function(){
			return $elements.MAIN_CONTAINER.find('.display-container .board').empty();
		},
		/**
		 * Provides the display board to print some information for user.
		 */	
		getSpinnerContainer: function(){
			return $elements.MAIN_CONTAINER.closest('.plmalightbox-box.layers-builder');
		},
		
		/**
		 * For debugging purpose, you can get the current context data to analyze.
		 */	
		getCtx: function(){
			return _ctx;
		}		
	}
})();

/* This function helps debug the functionality outside the 3dexp platform.
 * Just add the enrich3d=debug param in url.
 * */
Enrich3DAPI.isDebug = (function(){
	if(window.dashboardController == undefined &&
		window.location.href.indexOf('enrich3d=debug') > -1){
		window.dashboardController = {
			widgetInfos: {
				widgetId: 'standalone-plma',
				appId: 'standalone-plma'
			},
			publishTopic: function(event, data){
				console.log('dummy event, do not forget to remove url param "enrich3d=debug", to disable');
			},
			subscribeTopic:function(event, data){
				console.log('dummy event, do not forget to remove url param "enrich3d=debug", to disable');
			},
			subscribe:function(event, data){
                console.log('dummy event, do not forget to remove url param "enrich3d=debug", to disable');
            }
		}
		return true;
	}
	return false;
})();