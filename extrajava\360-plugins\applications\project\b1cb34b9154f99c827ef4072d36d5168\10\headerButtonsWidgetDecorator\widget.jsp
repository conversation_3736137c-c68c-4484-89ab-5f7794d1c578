<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import varWidget="widget" varFeeds="feeds" />

<config:getOptionsComposite var="buttons" name="buttons" mapIndex="true"/>

<widget:widget disableStyles="true" varUcssId="uCssId">
	<render:subWidgets/>
</widget:widget>


<render:renderScript position="READY">

	<c:forEach items="${buttons}" var="button">
		<string:escape var="buttonCss" value="${button.css}" escapeType="JAVASCRIPT"/>
		<string:escape var="buttonTooltip" value="${button.tooltip}" escapeType="JAVASCRIPT"/>
	
		new WidgetHeaderButton("${uCssId}", {
			buttonHtml: '<span class="widgetHeaderButton ${buttonCss}" title="${buttonTooltip}"></span>',
			onInit: ${button.onInit},
			onClick: ${button.onClick}
		});
	</c:forEach>


</render:renderScript>