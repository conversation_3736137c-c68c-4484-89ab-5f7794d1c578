(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory(require('react'), require('d3'), require('charts')) :
        typeof define === 'function' && define.amd ? define(['react', 'd3', 'charts'], factory) :
            (global = global || self, global.WidgetHeatmap = factory(global.React, global.d3, global.iiChart));
}(this, function (React, d3, charts) { 'use strict';

    var React__default = 'default' in React ? React['default'] : React;

    function _extends() {
        _extends = Object.assign || function (target) {
            for (var i = 1; i < arguments.length; i++) {
                var source = arguments[i];

                for (var key in source) {
                    if (Object.prototype.hasOwnProperty.call(source, key)) {
                        target[key] = source[key];
                    }
                }
            }

            return target;
        };

        return _extends.apply(this, arguments);
    }

    function _inheritsLoose(subClass, superClass) {
        subClass.prototype = Object.create(superClass.prototype);
        subClass.prototype.constructor = subClass;
        subClass.__proto__ = superClass;
    }

    function _assertThisInitialized(self) {
        if (self === void 0) {
            throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
        }

        return self;
    }

    function unwrapExports (x) {
        return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
    }

    function createCommonjsModule(fn, module) {
        return module = { exports: {} }, fn(module, module.exports), module.exports;
    }

    var reactIs_production_min = createCommonjsModule(function (module, exports) {

        Object.defineProperty(exports, "__esModule", {
            value: !0
        });
        var b = "function" === typeof Symbol && Symbol.for,
            c = b ? Symbol.for("react.element") : 60103,
            d = b ? Symbol.for("react.portal") : 60106,
            e = b ? Symbol.for("react.fragment") : 60107,
            f = b ? Symbol.for("react.strict_mode") : 60108,
            g = b ? Symbol.for("react.profiler") : 60114,
            h = b ? Symbol.for("react.provider") : 60109,
            k = b ? Symbol.for("react.context") : 60110,
            l = b ? Symbol.for("react.async_mode") : 60111,
            m = b ? Symbol.for("react.concurrent_mode") : 60111,
            n = b ? Symbol.for("react.forward_ref") : 60112,
            p = b ? Symbol.for("react.suspense") : 60113,
            q = b ? Symbol.for("react.memo") : 60115,
            r = b ? Symbol.for("react.lazy") : 60116;

        function t(a) {
            if ("object" === typeof a && null !== a) {
                var u = a.$$typeof;

                switch (u) {
                    case c:
                        switch (a = a.type, a) {
                            case l:
                            case m:
                            case e:
                            case g:
                            case f:
                            case p:
                                return a;

                            default:
                                switch (a = a && a.$$typeof, a) {
                                    case k:
                                    case n:
                                    case h:
                                        return a;

                                    default:
                                        return u;
                                }

                        }

                    case r:
                    case q:
                    case d:
                        return u;
                }
            }
        }

        function v(a) {
            return t(a) === m;
        }

        exports.typeOf = t;
        exports.AsyncMode = l;
        exports.ConcurrentMode = m;
        exports.ContextConsumer = k;
        exports.ContextProvider = h;
        exports.Element = c;
        exports.ForwardRef = n;
        exports.Fragment = e;
        exports.Lazy = r;
        exports.Memo = q;
        exports.Portal = d;
        exports.Profiler = g;
        exports.StrictMode = f;
        exports.Suspense = p;

        exports.isValidElementType = function (a) {
            return "string" === typeof a || "function" === typeof a || a === e || a === m || a === g || a === f || a === p || "object" === typeof a && null !== a && (a.$$typeof === r || a.$$typeof === q || a.$$typeof === h || a.$$typeof === k || a.$$typeof === n);
        };

        exports.isAsyncMode = function (a) {
            return v(a) || t(a) === l;
        };

        exports.isConcurrentMode = v;

        exports.isContextConsumer = function (a) {
            return t(a) === k;
        };

        exports.isContextProvider = function (a) {
            return t(a) === h;
        };

        exports.isElement = function (a) {
            return "object" === typeof a && null !== a && a.$$typeof === c;
        };

        exports.isForwardRef = function (a) {
            return t(a) === n;
        };

        exports.isFragment = function (a) {
            return t(a) === e;
        };

        exports.isLazy = function (a) {
            return t(a) === r;
        };

        exports.isMemo = function (a) {
            return t(a) === q;
        };

        exports.isPortal = function (a) {
            return t(a) === d;
        };

        exports.isProfiler = function (a) {
            return t(a) === g;
        };

        exports.isStrictMode = function (a) {
            return t(a) === f;
        };

        exports.isSuspense = function (a) {
            return t(a) === p;
        };
    });
    unwrapExports(reactIs_production_min);
    var reactIs_production_min_1 = reactIs_production_min.typeOf;
    var reactIs_production_min_2 = reactIs_production_min.AsyncMode;
    var reactIs_production_min_3 = reactIs_production_min.ConcurrentMode;
    var reactIs_production_min_4 = reactIs_production_min.ContextConsumer;
    var reactIs_production_min_5 = reactIs_production_min.ContextProvider;
    var reactIs_production_min_6 = reactIs_production_min.Element;
    var reactIs_production_min_7 = reactIs_production_min.ForwardRef;
    var reactIs_production_min_8 = reactIs_production_min.Fragment;
    var reactIs_production_min_9 = reactIs_production_min.Lazy;
    var reactIs_production_min_10 = reactIs_production_min.Memo;
    var reactIs_production_min_11 = reactIs_production_min.Portal;
    var reactIs_production_min_12 = reactIs_production_min.Profiler;
    var reactIs_production_min_13 = reactIs_production_min.StrictMode;
    var reactIs_production_min_14 = reactIs_production_min.Suspense;
    var reactIs_production_min_15 = reactIs_production_min.isValidElementType;
    var reactIs_production_min_16 = reactIs_production_min.isAsyncMode;
    var reactIs_production_min_17 = reactIs_production_min.isConcurrentMode;
    var reactIs_production_min_18 = reactIs_production_min.isContextConsumer;
    var reactIs_production_min_19 = reactIs_production_min.isContextProvider;
    var reactIs_production_min_20 = reactIs_production_min.isElement;
    var reactIs_production_min_21 = reactIs_production_min.isForwardRef;
    var reactIs_production_min_22 = reactIs_production_min.isFragment;
    var reactIs_production_min_23 = reactIs_production_min.isLazy;
    var reactIs_production_min_24 = reactIs_production_min.isMemo;
    var reactIs_production_min_25 = reactIs_production_min.isPortal;
    var reactIs_production_min_26 = reactIs_production_min.isProfiler;
    var reactIs_production_min_27 = reactIs_production_min.isStrictMode;
    var reactIs_production_min_28 = reactIs_production_min.isSuspense;

    var reactIs_development = createCommonjsModule(function (module, exports) {
    });
    unwrapExports(reactIs_development);
    var reactIs_development_1 = reactIs_development.typeOf;
    var reactIs_development_2 = reactIs_development.AsyncMode;
    var reactIs_development_3 = reactIs_development.ConcurrentMode;
    var reactIs_development_4 = reactIs_development.ContextConsumer;
    var reactIs_development_5 = reactIs_development.ContextProvider;
    var reactIs_development_6 = reactIs_development.Element;
    var reactIs_development_7 = reactIs_development.ForwardRef;
    var reactIs_development_8 = reactIs_development.Fragment;
    var reactIs_development_9 = reactIs_development.Lazy;
    var reactIs_development_10 = reactIs_development.Memo;
    var reactIs_development_11 = reactIs_development.Portal;
    var reactIs_development_12 = reactIs_development.Profiler;
    var reactIs_development_13 = reactIs_development.StrictMode;
    var reactIs_development_14 = reactIs_development.Suspense;
    var reactIs_development_15 = reactIs_development.isValidElementType;
    var reactIs_development_16 = reactIs_development.isAsyncMode;
    var reactIs_development_17 = reactIs_development.isConcurrentMode;
    var reactIs_development_18 = reactIs_development.isContextConsumer;
    var reactIs_development_19 = reactIs_development.isContextProvider;
    var reactIs_development_20 = reactIs_development.isElement;
    var reactIs_development_21 = reactIs_development.isForwardRef;
    var reactIs_development_22 = reactIs_development.isFragment;
    var reactIs_development_23 = reactIs_development.isLazy;
    var reactIs_development_24 = reactIs_development.isMemo;
    var reactIs_development_25 = reactIs_development.isPortal;
    var reactIs_development_26 = reactIs_development.isProfiler;
    var reactIs_development_27 = reactIs_development.isStrictMode;
    var reactIs_development_28 = reactIs_development.isSuspense;

    var reactIs = createCommonjsModule(function (module) {

        {
            module.exports = reactIs_production_min;
        }
    });

    /*
    object-assign
    (c) Sindre Sorhus
    @license MIT
    */
    /* eslint-disable no-unused-vars */

    var getOwnPropertySymbols = Object.getOwnPropertySymbols;
    var hasOwnProperty = Object.prototype.hasOwnProperty;
    var propIsEnumerable = Object.prototype.propertyIsEnumerable;

    function toObject(val) {
        if (val === null || val === undefined) {
            throw new TypeError('Object.assign cannot be called with null or undefined');
        }

        return Object(val);
    }

    function shouldUseNative() {
        try {
            if (!Object.assign) {
                return false;
            } // Detect buggy property enumeration order in older V8 versions.
            // https://bugs.chromium.org/p/v8/issues/detail?id=4118


            var test1 = new String('abc'); // eslint-disable-line no-new-wrappers

            test1[5] = 'de';

            if (Object.getOwnPropertyNames(test1)[0] === '5') {
                return false;
            } // https://bugs.chromium.org/p/v8/issues/detail?id=3056


            var test2 = {};

            for (var i = 0; i < 10; i++) {
                test2['_' + String.fromCharCode(i)] = i;
            }

            var order2 = Object.getOwnPropertyNames(test2).map(function (n) {
                return test2[n];
            });

            if (order2.join('') !== '0123456789') {
                return false;
            } // https://bugs.chromium.org/p/v8/issues/detail?id=3056


            var test3 = {};
            'abcdefghijklmnopqrst'.split('').forEach(function (letter) {
                test3[letter] = letter;
            });

            if (Object.keys(Object.assign({}, test3)).join('') !== 'abcdefghijklmnopqrst') {
                return false;
            }

            return true;
        } catch (err) {
            // We don't expect any of the above to throw, but better to be safe.
            return false;
        }
    }

    var objectAssign = shouldUseNative() ? Object.assign : function (target, source) {
        var from;
        var to = toObject(target);
        var symbols;

        for (var s = 1; s < arguments.length; s++) {
            from = Object(arguments[s]);

            for (var key in from) {
                if (hasOwnProperty.call(from, key)) {
                    to[key] = from[key];
                }
            }

            if (getOwnPropertySymbols) {
                symbols = getOwnPropertySymbols(from);

                for (var i = 0; i < symbols.length; i++) {
                    if (propIsEnumerable.call(from, symbols[i])) {
                        to[symbols[i]] = from[symbols[i]];
                    }
                }
            }
        }

        return to;
    };

    /**
     * Copyright (c) 2013-present, Facebook, Inc.
     *
     * This source code is licensed under the MIT license found in the
     * LICENSE file in the root directory of this source tree.
     */

    var ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';
    var ReactPropTypesSecret_1 = ReactPropTypesSecret;

    var has = Function.call.bind(Object.prototype.hasOwnProperty);

    function emptyFunction() {}

    function emptyFunctionWithReset() {}

    emptyFunctionWithReset.resetWarningCache = emptyFunction;

    var factoryWithThrowingShims = function factoryWithThrowingShims() {
        function shim(props, propName, componentName, location, propFullName, secret) {
            if (secret === ReactPropTypesSecret_1) {
                // It is still safe when called from React.
                return;
            }

            var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');
            err.name = 'Invariant Violation';
            throw err;
        }
        shim.isRequired = shim;

        function getShim() {
            return shim;
        }
        // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.

        var ReactPropTypes = {
            array: shim,
            bool: shim,
            func: shim,
            number: shim,
            object: shim,
            string: shim,
            symbol: shim,
            any: shim,
            arrayOf: getShim,
            element: shim,
            elementType: shim,
            instanceOf: getShim,
            node: shim,
            objectOf: getShim,
            oneOf: getShim,
            oneOfType: getShim,
            shape: getShim,
            exact: getShim,
            checkPropTypes: emptyFunctionWithReset,
            resetWarningCache: emptyFunction
        };
        ReactPropTypes.PropTypes = ReactPropTypes;
        return ReactPropTypes;
    };

    var propTypes = createCommonjsModule(function (module) {
        /**
         * Copyright (c) 2013-present, Facebook, Inc.
         *
         * This source code is licensed under the MIT license found in the
         * LICENSE file in the root directory of this source tree.
         */
        {
            // By explicitly using `prop-types` you are opting into new production behavior.
            // http://fb.me/prop-types-in-prod
            module.exports = factoryWithThrowingShims();
        }
    });

    var bind = function bind(fn, thisArg) {
        return function wrap() {
            var args = new Array(arguments.length);

            for (var i = 0; i < args.length; i++) {
                args[i] = arguments[i];
            }

            return fn.apply(thisArg, args);
        };
    };

    /*!
     * Determine if an object is a Buffer
     *
     * <AUTHOR> Aboukhadijeh <https://feross.org>
     * @license  MIT
     */
    // The _isBuffer check is for Safari 5-7 support, because it's missing
    // Object.prototype.constructor. Remove this eventually
    var isBuffer_1 = function isBuffer_1(obj) {
        return obj != null && (isBuffer(obj) || isSlowBuffer(obj) || !!obj._isBuffer);
    };

    function isBuffer(obj) {
        return !!obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj);
    } // For Node v0.10 support. Remove this eventually.


    function isSlowBuffer(obj) {
        return typeof obj.readFloatLE === 'function' && typeof obj.slice === 'function' && isBuffer(obj.slice(0, 0));
    }

    /*global toString:true*/
    // utils is a library of generic helper functions non-specific to axios


    var toString = Object.prototype.toString;
    /**
     * Determine if a value is an Array
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is an Array, otherwise false
     */

    function isArray(val) {
        return toString.call(val) === '[object Array]';
    }
    /**
     * Determine if a value is an ArrayBuffer
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is an ArrayBuffer, otherwise false
     */


    function isArrayBuffer(val) {
        return toString.call(val) === '[object ArrayBuffer]';
    }
    /**
     * Determine if a value is a FormData
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is an FormData, otherwise false
     */


    function isFormData(val) {
        return typeof FormData !== 'undefined' && val instanceof FormData;
    }
    /**
     * Determine if a value is a view on an ArrayBuffer
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false
     */


    function isArrayBufferView(val) {
        var result;

        if (typeof ArrayBuffer !== 'undefined' && ArrayBuffer.isView) {
            result = ArrayBuffer.isView(val);
        } else {
            result = val && val.buffer && val.buffer instanceof ArrayBuffer;
        }

        return result;
    }
    /**
     * Determine if a value is a String
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is a String, otherwise false
     */


    function isString(val) {
        return typeof val === 'string';
    }
    /**
     * Determine if a value is a Number
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is a Number, otherwise false
     */


    function isNumber(val) {
        return typeof val === 'number';
    }
    /**
     * Determine if a value is undefined
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if the value is undefined, otherwise false
     */


    function isUndefined(val) {
        return typeof val === 'undefined';
    }
    /**
     * Determine if a value is an Object
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is an Object, otherwise false
     */


    function isObject(val) {
        return val !== null && typeof val === 'object';
    }
    /**
     * Determine if a value is a Date
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is a Date, otherwise false
     */


    function isDate(val) {
        return toString.call(val) === '[object Date]';
    }
    /**
     * Determine if a value is a File
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is a File, otherwise false
     */


    function isFile(val) {
        return toString.call(val) === '[object File]';
    }
    /**
     * Determine if a value is a Blob
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is a Blob, otherwise false
     */


    function isBlob(val) {
        return toString.call(val) === '[object Blob]';
    }
    /**
     * Determine if a value is a Function
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is a Function, otherwise false
     */


    function isFunction(val) {
        return toString.call(val) === '[object Function]';
    }
    /**
     * Determine if a value is a Stream
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is a Stream, otherwise false
     */


    function isStream(val) {
        return isObject(val) && isFunction(val.pipe);
    }
    /**
     * Determine if a value is a URLSearchParams object
     *
     * @param {Object} val The value to test
     * @returns {boolean} True if value is a URLSearchParams object, otherwise false
     */


    function isURLSearchParams(val) {
        return typeof URLSearchParams !== 'undefined' && val instanceof URLSearchParams;
    }
    /**
     * Trim excess whitespace off the beginning and end of a string
     *
     * @param {String} str The String to trim
     * @returns {String} The String freed of excess whitespace
     */


    function trim(str) {
        return str.replace(/^\s*/, '').replace(/\s*$/, '');
    }
    /**
     * Determine if we're running in a standard browser environment
     *
     * This allows axios to run in a web worker, and react-native.
     * Both environments support XMLHttpRequest, but not fully standard globals.
     *
     * web workers:
     *  typeof window -> undefined
     *  typeof document -> undefined
     *
     * react-native:
     *  navigator.product -> 'ReactNative'
     */


    function isStandardBrowserEnv() {
        if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {
            return false;
        }

        return typeof window !== 'undefined' && typeof document !== 'undefined';
    }
    /**
     * Iterate over an Array or an Object invoking a function for each item.
     *
     * If `obj` is an Array callback will be called passing
     * the value, index, and complete array for each item.
     *
     * If 'obj' is an Object callback will be called passing
     * the value, key, and complete object for each property.
     *
     * @param {Object|Array} obj The object to iterate
     * @param {Function} fn The callback to invoke for each item
     */


    function forEach(obj, fn) {
        // Don't bother if no value provided
        if (obj === null || typeof obj === 'undefined') {
            return;
        } // Force an array if not already something iterable


        if (typeof obj !== 'object') {
            /*eslint no-param-reassign:0*/
            obj = [obj];
        }

        if (isArray(obj)) {
            // Iterate over array values
            for (var i = 0, l = obj.length; i < l; i++) {
                fn.call(null, obj[i], i, obj);
            }
        } else {
            // Iterate over object keys
            for (var key in obj) {
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    fn.call(null, obj[key], key, obj);
                }
            }
        }
    }
    /**
     * Accepts varargs expecting each argument to be an object, then
     * immutably merges the properties of each object and returns result.
     *
     * When multiple objects contain the same key the later object in
     * the arguments list will take precedence.
     *
     * Example:
     *
     * ```js
     * var result = merge({foo: 123}, {foo: 456});
     * console.log(result.foo); // outputs 456
     * ```
     *
     * @param {Object} obj1 Object to merge
     * @returns {Object} Result of all merge properties
     */


    function merge()
    /* obj1, obj2, obj3, ... */
    {
        var result = {};

        function assignValue(val, key) {
            if (typeof result[key] === 'object' && typeof val === 'object') {
                result[key] = merge(result[key], val);
            } else {
                result[key] = val;
            }
        }

        for (var i = 0, l = arguments.length; i < l; i++) {
            forEach(arguments[i], assignValue);
        }

        return result;
    }
    /**
     * Extends object a by mutably adding to it the properties of object b.
     *
     * @param {Object} a The object to be extended
     * @param {Object} b The object to copy properties from
     * @param {Object} thisArg The object to bind function to
     * @return {Object} The resulting value of object a
     */


    function extend(a, b, thisArg) {
        forEach(b, function assignValue(val, key) {
            if (thisArg && typeof val === 'function') {
                a[key] = bind(val, thisArg);
            } else {
                a[key] = val;
            }
        });
        return a;
    }

    var utils = {
        isArray: isArray,
        isArrayBuffer: isArrayBuffer,
        isBuffer: isBuffer_1,
        isFormData: isFormData,
        isArrayBufferView: isArrayBufferView,
        isString: isString,
        isNumber: isNumber,
        isObject: isObject,
        isUndefined: isUndefined,
        isDate: isDate,
        isFile: isFile,
        isBlob: isBlob,
        isFunction: isFunction,
        isStream: isStream,
        isURLSearchParams: isURLSearchParams,
        isStandardBrowserEnv: isStandardBrowserEnv,
        forEach: forEach,
        merge: merge,
        extend: extend,
        trim: trim
    };

    var normalizeHeaderName = function normalizeHeaderName(headers, normalizedName) {
        utils.forEach(headers, function processHeader(value, name) {
            if (name !== normalizedName && name.toUpperCase() === normalizedName.toUpperCase()) {
                headers[normalizedName] = value;
                delete headers[name];
            }
        });
    };

    /**
     * Update an Error with the specified config, error code, and response.
     *
     * @param {Error} error The error to update.
     * @param {Object} config The config.
     * @param {string} [code] The error code (for example, 'ECONNABORTED').
     * @param {Object} [request] The request.
     * @param {Object} [response] The response.
     * @returns {Error} The error.
     */

    var enhanceError = function enhanceError(error, config, code, request, response) {
        error.config = config;

        if (code) {
            error.code = code;
        }

        error.request = request;
        error.response = response;
        return error;
    };

    /**
     * Create an Error with the specified message, config, error code, request and response.
     *
     * @param {string} message The error message.
     * @param {Object} config The config.
     * @param {string} [code] The error code (for example, 'ECONNABORTED').
     * @param {Object} [request] The request.
     * @param {Object} [response] The response.
     * @returns {Error} The created error.
     */


    var createError = function createError(message, config, code, request, response) {
        var error = new Error(message);
        return enhanceError(error, config, code, request, response);
    };

    /**
     * Resolve or reject a Promise based on response status.
     *
     * @param {Function} resolve A function that resolves the promise.
     * @param {Function} reject A function that rejects the promise.
     * @param {object} response The response.
     */


    var settle = function settle(resolve, reject, response) {
        var validateStatus = response.config.validateStatus; // Note: status is not exposed by XDomainRequest

        if (!response.status || !validateStatus || validateStatus(response.status)) {
            resolve(response);
        } else {
            reject(createError('Request failed with status code ' + response.status, response.config, null, response.request, response));
        }
    };

    function encode(val) {
        return encodeURIComponent(val).replace(/%40/gi, '@').replace(/%3A/gi, ':').replace(/%24/g, '$').replace(/%2C/gi, ',').replace(/%20/g, '+').replace(/%5B/gi, '[').replace(/%5D/gi, ']');
    }
    /**
     * Build a URL by appending params to the end
     *
     * @param {string} url The base of the url (e.g., http://www.google.com)
     * @param {object} [params] The params to be appended
     * @returns {string} The formatted url
     */


    var buildURL = function buildURL(url, params, paramsSerializer) {
        /*eslint no-param-reassign:0*/
        if (!params) {
            return url;
        }

        var serializedParams;

        if (paramsSerializer) {
            serializedParams = paramsSerializer(params);
        } else if (utils.isURLSearchParams(params)) {
            serializedParams = params.toString();
        } else {
            var parts = [];
            utils.forEach(params, function serialize(val, key) {
                if (val === null || typeof val === 'undefined') {
                    return;
                }

                if (utils.isArray(val)) {
                    key = key + '[]';
                } else {
                    val = [val];
                }

                utils.forEach(val, function parseValue(v) {
                    if (utils.isDate(v)) {
                        v = v.toISOString();
                    } else if (utils.isObject(v)) {
                        v = JSON.stringify(v);
                    }

                    parts.push(encode(key) + '=' + encode(v));
                });
            });
            serializedParams = parts.join('&');
        }

        if (serializedParams) {
            url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;
        }

        return url;
    };

    // c.f. https://nodejs.org/api/http.html#http_message_headers


    var ignoreDuplicateOf = ['age', 'authorization', 'content-length', 'content-type', 'etag', 'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since', 'last-modified', 'location', 'max-forwards', 'proxy-authorization', 'referer', 'retry-after', 'user-agent'];
    /**
     * Parse headers into an object
     *
     * ```
     * Date: Wed, 27 Aug 2014 08:58:49 GMT
     * Content-Type: application/json
     * Connection: keep-alive
     * Transfer-Encoding: chunked
     * ```
     *
     * @param {String} headers Headers needing to be parsed
     * @returns {Object} Headers parsed into an object
     */

    var parseHeaders = function parseHeaders(headers) {
        var parsed = {};
        var key;
        var val;
        var i;

        if (!headers) {
            return parsed;
        }

        utils.forEach(headers.split('\n'), function parser(line) {
            i = line.indexOf(':');
            key = utils.trim(line.substr(0, i)).toLowerCase();
            val = utils.trim(line.substr(i + 1));

            if (key) {
                if (parsed[key] && ignoreDuplicateOf.indexOf(key) >= 0) {
                    return;
                }

                if (key === 'set-cookie') {
                    parsed[key] = (parsed[key] ? parsed[key] : []).concat([val]);
                } else {
                    parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;
                }
            }
        });
        return parsed;
    };

    var isURLSameOrigin = utils.isStandardBrowserEnv() ? // Standard browser envs have full support of the APIs needed to test
        // whether the request URL is of the same origin as current location.
        function standardBrowserEnv() {
            var msie = /(msie|trident)/i.test(navigator.userAgent);
            var urlParsingNode = document.createElement('a');
            var originURL;
            /**
             * Parse a URL to discover it's components
             *
             * @param {String} url The URL to be parsed
             * @returns {Object}
             */

            function resolveURL(url) {
                var href = url;

                if (msie) {
                    // IE needs attribute set twice to normalize properties
                    urlParsingNode.setAttribute('href', href);
                    href = urlParsingNode.href;
                }

                urlParsingNode.setAttribute('href', href); // urlParsingNode provides the UrlUtils interface - http://url.spec.whatwg.org/#urlutils

                return {
                    href: urlParsingNode.href,
                    protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, '') : '',
                    host: urlParsingNode.host,
                    search: urlParsingNode.search ? urlParsingNode.search.replace(/^\?/, '') : '',
                    hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, '') : '',
                    hostname: urlParsingNode.hostname,
                    port: urlParsingNode.port,
                    pathname: urlParsingNode.pathname.charAt(0) === '/' ? urlParsingNode.pathname : '/' + urlParsingNode.pathname
                };
            }

            originURL = resolveURL(window.location.href);
            /**
             * Determine if a URL shares the same origin as the current location
             *
             * @param {String} requestURL The URL to test
             * @returns {boolean} True if URL shares the same origin, otherwise false
             */

            return function isURLSameOrigin(requestURL) {
                var parsed = utils.isString(requestURL) ? resolveURL(requestURL) : requestURL;
                return parsed.protocol === originURL.protocol && parsed.host === originURL.host;
            };
        }() : // Non standard browser envs (web workers, react-native) lack needed support.
        function nonStandardBrowserEnv() {
            return function isURLSameOrigin() {
                return true;
            };
        }();

    var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';

    function E() {
        this.message = 'String contains an invalid character';
    }

    E.prototype = new Error();
    E.prototype.code = 5;
    E.prototype.name = 'InvalidCharacterError';

    function btoa(input) {
        var str = String(input);
        var output = '';

        for ( // initialize result and counter
            var block, charCode, idx = 0, map = chars; // if the next str index does not exist:
            //   change the mapping table to "="
            //   check if d has no fractional digits
            str.charAt(idx | 0) || (map = '=', idx % 1); // "8 - idx % 1 * 8" generates the sequence 2, 4, 6, 8
            output += map.charAt(63 & block >> 8 - idx % 1 * 8)) {
            charCode = str.charCodeAt(idx += 3 / 4);

            if (charCode > 0xFF) {
                throw new E();
            }

            block = block << 8 | charCode;
        }

        return output;
    }

    var btoa_1 = btoa;

    var cookies = utils.isStandardBrowserEnv() ? // Standard browser envs support document.cookie
        function standardBrowserEnv() {
            return {
                write: function write(name, value, expires, path, domain, secure) {
                    var cookie = [];
                    cookie.push(name + '=' + encodeURIComponent(value));

                    if (utils.isNumber(expires)) {
                        cookie.push('expires=' + new Date(expires).toGMTString());
                    }

                    if (utils.isString(path)) {
                        cookie.push('path=' + path);
                    }

                    if (utils.isString(domain)) {
                        cookie.push('domain=' + domain);
                    }

                    if (secure === true) {
                        cookie.push('secure');
                    }

                    document.cookie = cookie.join('; ');
                },
                read: function read(name) {
                    var match = document.cookie.match(new RegExp('(^|;\\s*)(' + name + ')=([^;]*)'));
                    return match ? decodeURIComponent(match[3]) : null;
                },
                remove: function remove(name) {
                    this.write(name, '', Date.now() - 86400000);
                }
            };
        }() : // Non standard browser env (web workers, react-native) lack needed support.
        function nonStandardBrowserEnv() {
            return {
                write: function write() {},
                read: function read() {
                    return null;
                },
                remove: function remove() {}
            };
        }();

    var btoa$1 = typeof window !== 'undefined' && window.btoa && window.btoa.bind(window) || btoa_1;

    var xhr = function xhrAdapter(config) {
        return new Promise(function dispatchXhrRequest(resolve, reject) {
            var requestData = config.data;
            var requestHeaders = config.headers;

            if (utils.isFormData(requestData)) {
                delete requestHeaders['Content-Type']; // Let the browser set it
            }

            var request = new XMLHttpRequest();
            var loadEvent = 'onreadystatechange';
            var xDomain = false; // For IE 8/9 CORS support
            // Only supports POST and GET calls and doesn't returns the response headers.
            // DON'T do this for testing b/c XMLHttpRequest is mocked, not XDomainRequest.

            if (typeof window !== 'undefined' && window.XDomainRequest && !('withCredentials' in request) && !isURLSameOrigin(config.url)) {
                request = new window.XDomainRequest();
                loadEvent = 'onload';
                xDomain = true;

                request.onprogress = function handleProgress() {};

                request.ontimeout = function handleTimeout() {};
            } // HTTP basic authentication


            if (config.auth) {
                var username = config.auth.username || '';
                var password = config.auth.password || '';
                requestHeaders.Authorization = 'Basic ' + btoa$1(username + ':' + password);
            }

            request.open(config.method.toUpperCase(), buildURL(config.url, config.params, config.paramsSerializer), true); // Set the request timeout in MS

            request.timeout = config.timeout; // Listen for ready state

            request[loadEvent] = function handleLoad() {
                if (!request || request.readyState !== 4 && !xDomain) {
                    return;
                } // The request errored out and we didn't get a response, this will be
                // handled by onerror instead
                // With one exception: request that using file: protocol, most browsers
                // will return status as 0 even though it's a successful request


                if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {
                    return;
                } // Prepare the response


                var responseHeaders = 'getAllResponseHeaders' in request ? parseHeaders(request.getAllResponseHeaders()) : null;
                var responseData = !config.responseType || config.responseType === 'text' ? request.responseText : request.response;
                var response = {
                    data: responseData,
                    // IE sends 1223 instead of 204 (https://github.com/axios/axios/issues/201)
                    status: request.status === 1223 ? 204 : request.status,
                    statusText: request.status === 1223 ? 'No Content' : request.statusText,
                    headers: responseHeaders,
                    config: config,
                    request: request
                };
                settle(resolve, reject, response); // Clean up request

                request = null;
            }; // Handle low level network errors


            request.onerror = function handleError() {
                // Real errors are hidden from us by the browser
                // onerror should only fire if it's a network error
                reject(createError('Network Error', config, null, request)); // Clean up request

                request = null;
            }; // Handle timeout


            request.ontimeout = function handleTimeout() {
                reject(createError('timeout of ' + config.timeout + 'ms exceeded', config, 'ECONNABORTED', request)); // Clean up request

                request = null;
            }; // Add xsrf header
            // This is only done if running in a standard browser environment.
            // Specifically not if we're in a web worker, or react-native.


            if (utils.isStandardBrowserEnv()) {
                var cookies$1 = cookies; // Add xsrf header

                var xsrfValue = (config.withCredentials || isURLSameOrigin(config.url)) && config.xsrfCookieName ? cookies$1.read(config.xsrfCookieName) : undefined;

                if (xsrfValue) {
                    requestHeaders[config.xsrfHeaderName] = xsrfValue;
                }
            } // Add headers to the request


            if ('setRequestHeader' in request) {
                utils.forEach(requestHeaders, function setRequestHeader(val, key) {
                    if (typeof requestData === 'undefined' && key.toLowerCase() === 'content-type') {
                        // Remove Content-Type if data is undefined
                        delete requestHeaders[key];
                    } else {
                        // Otherwise add header to the request
                        request.setRequestHeader(key, val);
                    }
                });
            } // Add withCredentials to request if needed


            if (config.withCredentials) {
                request.withCredentials = true;
            } // Add responseType to request if needed


            if (config.responseType) {
                try {
                    request.responseType = config.responseType;
                } catch (e) {
                    // Expected DOMException thrown by browsers not compatible XMLHttpRequest Level 2.
                    // But, this can be suppressed for 'json' type as it can be parsed by default 'transformResponse' function.
                    if (config.responseType !== 'json') {
                        throw e;
                    }
                }
            } // Handle progress if needed


            if (typeof config.onDownloadProgress === 'function') {
                request.addEventListener('progress', config.onDownloadProgress);
            } // Not all browsers support upload events


            if (typeof config.onUploadProgress === 'function' && request.upload) {
                request.upload.addEventListener('progress', config.onUploadProgress);
            }

            if (config.cancelToken) {
                // Handle cancellation
                config.cancelToken.promise.then(function onCanceled(cancel) {
                    if (!request) {
                        return;
                    }

                    request.abort();
                    reject(cancel); // Clean up request

                    request = null;
                });
            }

            if (requestData === undefined) {
                requestData = null;
            } // Send the request


            request.send(requestData);
        });
    };

    var DEFAULT_CONTENT_TYPE = {
        'Content-Type': 'application/x-www-form-urlencoded'
    };

    function setContentTypeIfUnset(headers, value) {
        if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {
            headers['Content-Type'] = value;
        }
    }

    function getDefaultAdapter() {
        var adapter;

        if (typeof XMLHttpRequest !== 'undefined') {
            // For browsers use XHR adapter
            adapter = xhr;
        } else if (typeof process !== 'undefined') {
            // For node use HTTP adapter
            adapter = xhr;
        }

        return adapter;
    }

    var defaults = {
        adapter: getDefaultAdapter(),
        transformRequest: [function transformRequest(data, headers) {
            normalizeHeaderName(headers, 'Content-Type');

            if (utils.isFormData(data) || utils.isArrayBuffer(data) || utils.isBuffer(data) || utils.isStream(data) || utils.isFile(data) || utils.isBlob(data)) {
                return data;
            }

            if (utils.isArrayBufferView(data)) {
                return data.buffer;
            }

            if (utils.isURLSearchParams(data)) {
                setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');
                return data.toString();
            }

            if (utils.isObject(data)) {
                setContentTypeIfUnset(headers, 'application/json;charset=utf-8');
                return JSON.stringify(data);
            }

            return data;
        }],
        transformResponse: [function transformResponse(data) {
            /*eslint no-param-reassign:0*/
            if (typeof data === 'string') {
                try {
                    data = JSON.parse(data);
                } catch (e) {
                    /* Ignore */
                }
            }

            return data;
        }],

        /**
         * A timeout in milliseconds to abort a request. If set to 0 (default) a
         * timeout is not created.
         */
        timeout: 0,
        xsrfCookieName: 'XSRF-TOKEN',
        xsrfHeaderName: 'X-XSRF-TOKEN',
        maxContentLength: -1,
        validateStatus: function validateStatus(status) {
            return status >= 200 && status < 300;
        }
    };
    defaults.headers = {
        common: {
            'Accept': 'application/json, text/plain, */*'
        }
    };
    utils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {
        defaults.headers[method] = {};
    });
    utils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {
        defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);
    });
    var defaults_1 = defaults;

    function InterceptorManager() {
        this.handlers = [];
    }
    /**
     * Add a new interceptor to the stack
     *
     * @param {Function} fulfilled The function to handle `then` for a `Promise`
     * @param {Function} rejected The function to handle `reject` for a `Promise`
     *
     * @return {Number} An ID used to remove interceptor later
     */


    InterceptorManager.prototype.use = function use(fulfilled, rejected) {
        this.handlers.push({
            fulfilled: fulfilled,
            rejected: rejected
        });
        return this.handlers.length - 1;
    };
    /**
     * Remove an interceptor from the stack
     *
     * @param {Number} id The ID that was returned by `use`
     */


    InterceptorManager.prototype.eject = function eject(id) {
        if (this.handlers[id]) {
            this.handlers[id] = null;
        }
    };
    /**
     * Iterate over all the registered interceptors
     *
     * This method is particularly useful for skipping over any
     * interceptors that may have become `null` calling `eject`.
     *
     * @param {Function} fn The function to call for each interceptor
     */


    InterceptorManager.prototype.forEach = function forEach(fn) {
        utils.forEach(this.handlers, function forEachHandler(h) {
            if (h !== null) {
                fn(h);
            }
        });
    };

    var InterceptorManager_1 = InterceptorManager;

    /**
     * Transform the data for a request or a response
     *
     * @param {Object|String} data The data to be transformed
     * @param {Array} headers The headers for the request or response
     * @param {Array|Function} fns A single function or Array of functions
     * @returns {*} The resulting transformed data
     */


    var transformData = function transformData(data, headers, fns) {
        /*eslint no-param-reassign:0*/
        utils.forEach(fns, function transform(fn) {
            data = fn(data, headers);
        });
        return data;
    };

    var isCancel = function isCancel(value) {
        return !!(value && value.__CANCEL__);
    };

    /**
     * Determines whether the specified URL is absolute
     *
     * @param {string} url The URL to test
     * @returns {boolean} True if the specified URL is absolute, otherwise false
     */

    var isAbsoluteURL = function isAbsoluteURL(url) {
        // A URL is considered absolute if it begins with "<scheme>://" or "//" (protocol-relative URL).
        // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed
        // by any combination of letters, digits, plus, period, or hyphen.
        return /^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(url);
    };

    /**
     * Creates a new URL by combining the specified URLs
     *
     * @param {string} baseURL The base URL
     * @param {string} relativeURL The relative URL
     * @returns {string} The combined URL
     */

    var combineURLs = function combineURLs(baseURL, relativeURL) {
        return relativeURL ? baseURL.replace(/\/+$/, '') + '/' + relativeURL.replace(/^\/+/, '') : baseURL;
    };

    /**
     * Throws a `Cancel` if cancellation has been requested.
     */


    function throwIfCancellationRequested(config) {
        if (config.cancelToken) {
            config.cancelToken.throwIfRequested();
        }
    }
    /**
     * Dispatch a request to the server using the configured adapter.
     *
     * @param {object} config The config that is to be used for the request
     * @returns {Promise} The Promise to be fulfilled
     */


    var dispatchRequest = function dispatchRequest(config) {
        throwIfCancellationRequested(config); // Support baseURL config

        if (config.baseURL && !isAbsoluteURL(config.url)) {
            config.url = combineURLs(config.baseURL, config.url);
        } // Ensure headers exist


        config.headers = config.headers || {}; // Transform request data

        config.data = transformData(config.data, config.headers, config.transformRequest); // Flatten headers

        config.headers = utils.merge(config.headers.common || {}, config.headers[config.method] || {}, config.headers || {});
        utils.forEach(['delete', 'get', 'head', 'post', 'put', 'patch', 'common'], function cleanHeaderConfig(method) {
            delete config.headers[method];
        });
        var adapter = config.adapter || defaults_1.adapter;
        return adapter(config).then(function onAdapterResolution(response) {
            throwIfCancellationRequested(config); // Transform response data

            response.data = transformData(response.data, response.headers, config.transformResponse);
            return response;
        }, function onAdapterRejection(reason) {
            if (!isCancel(reason)) {
                throwIfCancellationRequested(config); // Transform response data

                if (reason && reason.response) {
                    reason.response.data = transformData(reason.response.data, reason.response.headers, config.transformResponse);
                }
            }

            return Promise.reject(reason);
        });
    };

    /**
     * Create a new instance of Axios
     *
     * @param {Object} instanceConfig The default config for the instance
     */


    function Axios(instanceConfig) {
        this.defaults = instanceConfig;
        this.interceptors = {
            request: new InterceptorManager_1(),
            response: new InterceptorManager_1()
        };
    }
    /**
     * Dispatch a request
     *
     * @param {Object} config The config specific for this request (merged with this.defaults)
     */


    Axios.prototype.request = function request(config) {
        /*eslint no-param-reassign:0*/
        // Allow for axios('example/url'[, config]) a la fetch API
        if (typeof config === 'string') {
            config = utils.merge({
                url: arguments[0]
            }, arguments[1]);
        }

        config = utils.merge(defaults_1, {
            method: 'get'
        }, this.defaults, config);
        config.method = config.method.toLowerCase(); // Hook up interceptors middleware

        var chain = [dispatchRequest, undefined];
        var promise = Promise.resolve(config);
        this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {
            chain.unshift(interceptor.fulfilled, interceptor.rejected);
        });
        this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {
            chain.push(interceptor.fulfilled, interceptor.rejected);
        });

        while (chain.length) {
            promise = promise.then(chain.shift(), chain.shift());
        }

        return promise;
    }; // Provide aliases for supported request methods


    utils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {
        /*eslint func-names:0*/
        Axios.prototype[method] = function (url, config) {
            return this.request(utils.merge(config || {}, {
                method: method,
                url: url
            }));
        };
    });
    utils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {
        /*eslint func-names:0*/
        Axios.prototype[method] = function (url, data, config) {
            return this.request(utils.merge(config || {}, {
                method: method,
                url: url,
                data: data
            }));
        };
    });
    var Axios_1 = Axios;

    /**
     * A `Cancel` is an object that is thrown when an operation is canceled.
     *
     * @class
     * @param {string=} message The message.
     */

    function Cancel(message) {
        this.message = message;
    }

    Cancel.prototype.toString = function toString() {
        return 'Cancel' + (this.message ? ': ' + this.message : '');
    };

    Cancel.prototype.__CANCEL__ = true;
    var Cancel_1 = Cancel;

    /**
     * A `CancelToken` is an object that can be used to request cancellation of an operation.
     *
     * @class
     * @param {Function} executor The executor function.
     */


    function CancelToken(executor) {
        if (typeof executor !== 'function') {
            throw new TypeError('executor must be a function.');
        }

        var resolvePromise;
        this.promise = new Promise(function promiseExecutor(resolve) {
            resolvePromise = resolve;
        });
        var token = this;
        executor(function cancel(message) {
            if (token.reason) {
                // Cancellation has already been requested
                return;
            }

            token.reason = new Cancel_1(message);
            resolvePromise(token.reason);
        });
    }
    /**
     * Throws a `Cancel` if cancellation has been requested.
     */


    CancelToken.prototype.throwIfRequested = function throwIfRequested() {
        if (this.reason) {
            throw this.reason;
        }
    };
    /**
     * Returns an object that contains a new `CancelToken` and a function that, when called,
     * cancels the `CancelToken`.
     */


    CancelToken.source = function source() {
        var cancel;
        var token = new CancelToken(function executor(c) {
            cancel = c;
        });
        return {
            token: token,
            cancel: cancel
        };
    };

    var CancelToken_1 = CancelToken;

    /**
     * Syntactic sugar for invoking a function and expanding an array for arguments.
     *
     * Common use case would be to use `Function.prototype.apply`.
     *
     *  ```js
     *  function f(x, y, z) {}
     *  var args = [1, 2, 3];
     *  f.apply(null, args);
     *  ```
     *
     * With `spread` this example can be re-written.
     *
     *  ```js
     *  spread(function(x, y, z) {})([1, 2, 3]);
     *  ```
     *
     * @param {Function} callback
     * @returns {Function}
     */

    var spread = function spread(callback) {
        return function wrap(arr) {
            return callback.apply(null, arr);
        };
    };

    /**
     * Create an instance of Axios
     *
     * @param {Object} defaultConfig The default config for the instance
     * @return {Axios} A new instance of Axios
     */


    function createInstance(defaultConfig) {
        var context = new Axios_1(defaultConfig);
        var instance = bind(Axios_1.prototype.request, context); // Copy axios.prototype to instance

        utils.extend(instance, Axios_1.prototype, context); // Copy context to instance

        utils.extend(instance, context);
        return instance;
    } // Create the default instance to be exported


    var axios = createInstance(defaults_1); // Expose Axios class to allow class inheritance

    axios.Axios = Axios_1; // Factory for creating new instances

    axios.create = function create(instanceConfig) {
        return createInstance(utils.merge(defaults_1, instanceConfig));
    }; // Expose Cancel & CancelToken


    axios.Cancel = Cancel_1;
    axios.CancelToken = CancelToken_1;
    axios.isCancel = isCancel; // Expose all/spread

    axios.all = function all(promises) {
        return Promise.all(promises);
    };

    axios.spread = spread;
    var axios_1 = axios; // Allow use of default import syntax in TypeScript

    var default_1 = axios;
    axios_1.default = default_1;

    var axios$1 = axios_1;

    var tooltipSpacing = {
        x: 10,
        y: 10
    };

    var WidgetHeatmap =
        /*#__PURE__*/
        function (_Component) {
            _inheritsLoose(WidgetHeatmap, _Component);

            function WidgetHeatmap(props) {
                var _this;

                _this = _Component.call(this, props) || this;
                _this.wrapper = React__default.createRef();
                _this.toggleDendrogram = _this.toggleDendrogram.bind(_assertThisInitialized(_this));
                _this.computeData = _this.computeData.bind(_assertThisInitialized(_this));
                _this.computeDendrogramSize = _this.computeDendrogramSize.bind(_assertThisInitialized(_this));
                _this.handleHeatmapClick = _this.handleHeatmapClick.bind(_assertThisInitialized(_this));
                _this.handleHeatmapMouseMove = _this.handleHeatmapMouseMove.bind(_assertThisInitialized(_this));
                _this.handleHeatmapMouseLeave = _this.handleHeatmapMouseLeave.bind(_assertThisInitialized(_this));
                _this.handleDendrogramMouseMove = _this.handleDendrogramMouseMove.bind(_assertThisInitialized(_this));
                _this.handleDendrogramMouseLeave = _this.handleDendrogramMouseLeave.bind(_assertThisInitialized(_this));
                var _this$props = _this.props,
                    tree = _this$props.tree,
                    xLabels = _this$props.xLabels,
                    data = _this$props.data;
                _this.rawData = data;
                _this.xLabels = {};
                _this.yLabels = {};
                var axisTree = tree.data;

                if (tree.dataConfig && tree.dataConfig.id && tree.dataConfig.parent) {
                    axisTree = d3.stratify().id(function (d) {
                        return d[tree.dataConfig.id];
                    }).parentId(function (d) {
                        return d[tree.dataConfig.parent];
                    })(tree.data);
                }

                _this.hierarchyTree = d3.hierarchy(axisTree);

                var yLabels = _this.getHierarchyLabels(_this.hierarchyTree, tree.dataConfig);

                var heatmapData = _this.computeData(_this.rawData, yLabels);

                var _this$computeDataInfo = _this.computeDataInfos(heatmapData),
                    min = _this$computeDataInfo.min,
                    max = _this$computeDataInfo.max;

                tree.data.forEach(function (d) {
                    var label = new charts.Utils.Label(d, tree.dataConfig);
                    _this.yLabels[label.getId()] = label.getLabel();
                });
                _this.state = {
                    axisTree: axisTree,
                    min: min,
                    max: max,
                    zoom: 4,
                    //bomDeep
                    xLabels: xLabels.data.map(function (item) {
                        var label = new charts.Utils.Label(item, xLabels.dataConfig);
                        _this.xLabels[label.getId()] = label.getLabel().substr(label.getLabel().lastIndexOf('#') + 1);
                        return label.getId();
                    }).sort(function (a, b) {
                        var aLabel = a.substr(a.lastIndexOf('#') + 1);
                        var bLabel = b.substr(b.lastIndexOf('#') + 1);

                        if (aLabel < bLabel) {
                            return -1;
                        }

                        if (aLabel > bLabel) {
                            return 1;
                        }

                        return 0;
                    }),
                    heatmapData: heatmapData,
                    yLabels: yLabels,
                    tooltip: false,
                    dendrogramSize: _this.computeDendrogramSize(axisTree)
                };
                return _this;
            }

            var _proto = WidgetHeatmap.prototype;

            _proto.componentDidMount = function componentDidMount() {
                var _this2 = this;

                var _this$props2 = this.props,
                    cloudview = _this$props2.cloudview,
                    tree = _this$props2.tree;
                var loadDataFromNodes = charts.Utils.hierarchy.getNodesWithChildren(this.hierarchyTree).map(function (n) {
                    var node = new charts.Utils.Label(n.data.data, tree.dataConfig);
                    return node.getId();
                });

                if (cloudview && loadDataFromNodes) {
                    var multidim = cloudview.dataSource.match(/facetId=([a-z_-]*),/);
                    var yLabels = this.state.yLabels;

                    if (multidim && multidim[1]) {
                        var promises = loadDataFromNodes.map(function (node) {
                            var refine = "f/" + multidim[1] + "/0/" + node;
                            return axios$1.get(cloudview.refineUrl + "?feedname=" + cloudview.feed + "&dataSource=" + cloudview.dataSource + "&r=" + refine + "&page=" + cloudview.page);
                        });
                        Promise.all(promises).then(function (values) {
                            values.forEach(function (_ref) {
                                var status = _ref.status,
                                    data = _ref.data;

                                if (status === 200 && data) {
                                    _this2.rawData = _this2.rawData.concat(data);
                                }
                            });

                            var heatmapData = _this2.computeData(_this2.rawData, yLabels);

                            var _this2$computeDataInf = _this2.computeDataInfos(heatmapData),
                                min = _this2$computeDataInf.min,
                                max = _this2$computeDataInf.max;

                            _this2.setState({
                                heatmapData: heatmapData,
                                min: min,
                                max: max
                            });
                        }).catch(function (e) {
                            console.error(e);
                        });
                    }
                }
            };

            _proto.componentDidUpdate_ = function componentDidUpdate_(prevProps) {
                var data = this.props.data;
                var isUpdated = false;

                if (prevProps.data !== data) {
                    isUpdated = true;
                }

                if (isUpdated) {
                    this.setState(this.updataHeatmapData());
                }
            };

            _proto.handleHeatmapClick = function handleHeatmapClick(e) {
                if (e.data && e.data.value) {
                    var labelX = this.xLabels[e.data.x];
                    var labelY = this.yLabels[e.data.y];
                    this.props.onClick(e, labelX, labelY);
                }
            };

            _proto.handleHeatmapMouseMove = function handleHeatmapMouseMove(e) {
                if (e.data && e.data.value && this.props.tooltip) {
                    var label = this.xLabels[e.data.x];
                    var tooltip = new charts.Utils.Tooltip(label + "/" + this.yLabels[e.data.y] + ": " + e.data.value, e.clientX, e.clientY, tooltipSpacing);
                    tooltip.setTooltipArea(this.wrapper.current);
                    tooltip.setTooltipArea(this.wrapper.current.svgRef);
                    this.setState({
                        tooltip: tooltip.getData()
                    });
                } else {
                    this.setState({
                        tooltip: false
                    });
                }
            };

            _proto.handleHeatmapMouseLeave = function handleHeatmapMouseLeave() {
                if (this.props.tooltip) {
                    this.setState({
                        tooltip: false
                    });
                }
            };

            _proto.handleDendrogramMouseMove = function handleDendrogramMouseMove(e) {
                var tooltip = new charts.Utils.Tooltip(e.target.getAttribute('data-name'), e.clientX, e.clientY, tooltipSpacing);
                tooltip.setTooltipArea(this.wrapper.current);
                tooltip.setTooltipArea(this.wrapper.current.svgRef.current);
                this.setState({
                    tooltip: tooltip.getData()
                });
            };

            _proto.handleDendrogramMouseLeave = function handleDendrogramMouseLeave() {
                this.setState({
                    tooltip: false
                });
            };

            _proto.updataHeatmapData = function updataHeatmapData() {
                var _this$props3 = this.props,
                    tree = _this$props3.tree,
                    data = _this$props3.data;
                this.rawData = data;
                var yLabels = this.getHierarchyLabels(this.hierarchyTree, tree.dataConfig);
                var heatmapData = this.computeData(this.rawData, yLabels);

                var _this$computeDataInfo2 = this.computeDataInfos(heatmapData),
                    min = _this$computeDataInfo2.min,
                    max = _this$computeDataInfo2.max;

                return {
                    min: min,
                    max: max,
                    heatmapData: heatmapData,
                    yLabels: yLabels
                };
            };

            _proto.getHierarchyLabels = function getHierarchyLabels(hierarchyTree, dataConfig) {
                return hierarchyTree.leaves().map(function (child) {
                    var label = new charts.Utils.Label(child.data.data, dataConfig);
                    return label.getId();
                });
            };

            _proto.computeDendrogramSize = function computeDendrogramSize(axisTree) {
                var tree = this.props.tree;
                var dendrogramProps = {
                    data: axisTree,
                    hideRootLabel: true,
                    getLabel: tree.dataConfig.getLabel
                };
                return charts.Utils.Dendrogram.computeWidth(dendrogramProps);
            };

            _proto.computeDataInfos = function computeDataInfos(data) {
                return {
                    min: charts.Utils.min(data, function (elt) {
                        return typeof elt.value === 'string' ? parseInt(elt.value) : elt.value;
                    }),
                    max: charts.Utils.max(data, function (elt) {
                        return typeof elt.value === 'string' ? parseInt(elt.value) : elt.value;
                    })
                };
            };

            _proto.computeData = function computeData(data, labels) {
                labels = labels || this.state.yLabels;
                var heatmap = [];
                data && data.forEach(function (item) {
                    if (labels.indexOf(item.y) >= 0) {
                        heatmap.push(item);
                    }
                });
                return heatmap;
            };

            _proto.changeZoom = function changeZoom(e) {
                var zoom = e.target.value;
                var hierarchyTree = d3.hierarchy(this.state.axisTree); //updated bom with children opend/closed
                //and get the leaves

                charts.Utils.hierarchy.zoom(hierarchyTree, zoom);
                var yLabels = hierarchyTree.leaves().map(function (child) {
                    return child.data.reference;
                });
                var heatmapData = this.computeData(this.rawData, yLabels);

                var _this$computeDataInfo3 = this.computeDataInfos(heatmapData),
                    min = _this$computeDataInfo3.min,
                    max = _this$computeDataInfo3.max;

                this.setState({
                    zoom: zoom,
                    yLabels: yLabels,
                    heatmapData: heatmapData,
                    min: min,
                    max: max
                });
            };

            _proto.toggleDendrogram = function toggleDendrogram(data) {
                var hierarchyTree = data.nodes[0].leaves ? data.nodes[0] : d3.hierarchy(data.nodes[0]);
                var yLabels = this.getHierarchyLabels(hierarchyTree, this.props.tree.dataConfig);
                var heatmapData = this.computeData(this.rawData, yLabels);

                var _this$computeDataInfo4 = this.computeDataInfos(heatmapData),
                    min = _this$computeDataInfo4.min,
                    max = _this$computeDataInfo4.max;

                this.setState({
                    yLabels: yLabels,
                    heatmapData: heatmapData,
                    min: min,
                    max: max
                });
            };

            _proto.render = function render() {
                var _this3 = this;

                var _this$props4 = this.props,
                    height = _this$props4.height,
                    tree = _this$props4.tree,
                    colors = _this$props4.colors,
                    width = _this$props4.width,
                    onClick = _this$props4.onClick;
                var _this$state = this.state,
                    zoom = _this$state.zoom,
                    xLabels = _this$state.xLabels,
                    yLabels = _this$state.yLabels,
                    heatmapData = _this$state.heatmapData,
                    max = _this$state.max,
                    axisTree = _this$state.axisTree,
                    tooltip = _this$state.tooltip,
                    dendrogramSize = _this$state.dendrogramSize;
                var color = d3.scaleQuantize().domain([0, max]).range(charts.Utils.getQuantileColor(colors[0], 10));
                var dendrogramProps = {
                    data: axisTree,
                    hideRootLabel: true,
                    getLabel: tree.dataConfig.getLabel
                };
                var margin = {
                    top: 100,
                    right: 60,
                    bottom: 10,
                    left: dendrogramSize
                };

                return React__default.createElement(charts.Svg, {
                    width: width,
                    height: height,
                    ref: this.wrapper
                }, React__default.createElement(charts.XYPlot, {
                    margin: margin,
                    xLabels: xLabels,
                    yLabels: yLabels
                }, React__default.createElement(charts.Heatmap, {
                    domainX: "x",
                    domainY: "y",
                    value: "value",
                    data: heatmapData,
                    getColor: function getColor(_ref2) {
                        var value = _ref2.value;
                        return color(value);
                    },
                    onClick: this.handleHeatmapClick,
                    onMouseMove: this.handleHeatmapMouseMove,
                    onMouseLeave: this.handleHeatmapMouseLeave
                }), React__default.createElement(charts.Axis, {
                    position: "top",
                    rotation: "-45",
                    tickFormat: function tickFormat(item) {
                        return _this3.xLabels[item.label];
                    }
                }), React__default.createElement(charts.Axis, {
                    style: {
                        transform: "translate(-" + dendrogramSize + ",0)"
                    },
                    render: function render() {
                        return React__default.createElement(charts.Dendrogram, _extends({
                            width: dendrogramSize,
                            height: height - margin.top - margin.bottom,
                            margin: {
                                top: 0,
                                right: 90,
                                bottom: 0,
                                left: 0
                            },
                            deep: zoom,
                            onUpdate: _this3.toggleDendrogram,
                            onNodeMouseover: _this3.handleDendrogramMouseMove,
                            onNodeMouseleave: _this3.handleDendrogramMouseLeave,
                            showLeavesNode: true,
                            collapsable: true
                        }, dendrogramProps, {
                            getLabel: function getLabel(item) {
                                return _this3.yLabels[item.id];
                            }
                        }));
                    }
                })), tooltip && React__default.createElement(charts.Tooltip, _extends({}, tooltip, {
                    spacing: tooltipSpacing,
                    svgClientRect: {
                        x: this.wrapper.current.state.svgRect.x,
                        y: this.wrapper.current.state.svgRect.y
                    }
                })));
            };

            return WidgetHeatmap;
        }(React.Component);

    WidgetHeatmap.propTypes = {
        width: propTypes.oneOfType([propTypes.string, propTypes.number]),
        colors: propTypes.arrayOf(propTypes.string),
        xLabels: propTypes.object.isRequired,
        onClick: propTypes.func,
        tooltip: propTypes.bool
    };
    WidgetHeatmap.defaultProps = {
        colors: ['#368ec4'],
        width: '100%',
        tooltip: false
    };

    return WidgetHeatmap;

}));
