<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Compare Hit" group="PLM Analytics/Compare" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <Description>Store displayed hit in Compare Collection.</Description>

    <Platforms>
        <Platform type="web" supported="true" />
        <Platform type="mobile" supported="true" />
    </Platforms>

    <Includes>
        <Include type="css" path="css/style.less" />
        <Include type="js" path="js/compareHit.js" />
    </Includes>

    <Dependencies>
        <Widget name="plmaResources" />
    </Dependencies>

    <SupportWidgetsId arity="ZERO" />
    <SupportFeedsId arity="ONE" />
    <SupportI18N supported="true">
        <JsKeys>
        </JsKeys>
    </SupportI18N>

    <OptionsGroup name="General">
        <Option id="label" name="Label" isEvaluated="true" arity="ONE">
            <Description>Specifies the label of the button. Briefly explains the purpose of the button (3 words max).</Description>
        </Option>
        <Option id="hitID" name="Hit ID" arity="ONE">
            <Description>Hit ID MEL expression.</Description>
        </Option>
        <Option id="collection" name="Collection" arity="ONE">
            <Description>Preferred hit collection name.</Description>
        </Option>
        <Option id="showLabel" name="Show label" arity="ONE">
            <Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>
        <Option id="counterSelector" name="Favorites counter selector" arity="ONE">
            <Description>Preferred hits counter selector (top bar widget).</Description>
        </Option>
    </OptionsGroup>

    <DefaultValues displayName="Normal" previewUrl="">
        <DefaultValue name="label">Add to Compare</DefaultValue>
        <DefaultValue name="showLabel">false</DefaultValue>
        <DefaultValue name="hitID">${entry.metas["id"]}</DefaultValue>
        <DefaultValue name="collection">compare</DefaultValue>
        <DefaultValue name="counterSelector">.compared-hits</DefaultValue>
    </DefaultValues>
</Widget>
