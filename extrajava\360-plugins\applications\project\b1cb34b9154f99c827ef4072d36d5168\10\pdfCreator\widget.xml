<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Create PDF" group="PLM Analytics/Layout" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<!-- This widget is extention of plmaButton -->
	<Description><![CDATA[
Button to create the PDF of the current page as per provided configurations.<br/>
<hr/>
<b>How to write custom page template?</b><br/>
Create your own class based of implementation of 'PDFDefaultTemplate'.
For every widget rendering the pageTemplate object is created and 'renderPageTemplate' followed by 'renderWidget' functions will be called.
<hr/>
<b>How to write custom rendering?</b><br/>
Being fully JS functionality, you can write custom rendering as follows<br/>
<pre>
/*
 * template: PDF template Provided in file properties option
 * doc: PDF doc object
 * widgetData: widget Regarding Details
 *headerFooterData: Header and Footer Regarding Details
 */
renderWidgetHandler : function (template,doc,widgetData,headerFooterData){
  let dfd = $.Deferred();
  /* Add code to handle custom widget rendering implementation.
   * Refer following implementation
   * See plmaPdfBuilder.js -> renderPLMAChart2 or renderPLMADataTable
   * See publishTo3DDashboardUtils.js -> render_custom_widget
   */
	
  if(success) {
    dfd.resolve(template);
  }
  else{
    dfd.reject(template);
  }
  /* Must return a promise object and resolve or reject the promise based 
  on the rendering of the widget */
  return dfd.promise();
}</pre>
	]]></Description>
	<Preview>		
	</Preview>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="css" path="../plmaResources/lib/notify/notify-plma.less" />

		<Include type="js" path="../plmaResources/lib/jspdf/jspdf.umd.min.js"/>
		<Include type="js" path="../plmaResources/lib/jspdf/jspdf.plugin.autotable.min.js"/>
		<Include type="js" path="../plmaResources/lib/jspdf/html2canvas.min.js"/>
		<Include type="js" path="../plmaResources/lib/jspdf/canvg.min.js"/>
		<Include type="js" path="../plmaResources/lib/jspdf/pdfobject.min.js"/>
		<Include type="js" path="../plmaResources/lib/jspdf/purify.min.js"/>

		<Include type="js" path="js/plmaPdfBuilder.js"/>
		<Include type="js" path="js/pdfCreator.js"/>
		<Include type="js" path="js/NotoSansSC-bold.js"/>

		<Include type="js" path="../plmaResources/js/lightbox.js"/>
		<Include type="js" path="../plmaResources/lib/notify/notify.js"/>
		<Include type="js" path="../plmaResources/lib/notify/notify-plma.js"/>
	</Includes>
		
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO_OR_MANY" />
	<SupportI18N supported="true">
		<JsKeys>
		</JsKeys> 
	</SupportI18N>
	
	<OptionsGroup name="General">
		<Option id="iconCss" name="Icon CSS" >
			<Description>Specifies the CSS class name of the icon added to the button. If empty, no icon is displayed.</Description>
		</Option>
		<Option id="label" name="Label" isEvaluated="true" arity="ONE">
			<Description>Specifies the label of the button. Briefly explains the purpose of the button (3 words max).</Description>
		</Option>
		<Option id="showLabel" name="Show label" arity="ONE">
			<Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="onInit" name="On init" isEvaluated="true">
			<Description>The JavaScript code that is executed once when the button is loaded on the page. The "this" keyword points to the button as a jQuery object.</Description>
			<Placeholder>function() {}</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="onDone" name="On Done" isEvaluated="true">
			<Description>Called after PDF rendering is done and just before preview.</Description>
			<Placeholder>function(e) {}</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<!--
		<Option id="renderOnce" name="Render script execution once" arity="ONE">
			<Description>Renders Javascript code in a renderOnce tag element.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		-->
	</OptionsGroup>

	<OptionsGroup name="Export Config">
		<Option id="fileName" name="File Name" arity="ONE" isEvaluated="true">
			<Description>Name of file to be exported.</Description>
		</Option>
		<Option id="fileOptions" name="Options" arity="ONE" isEvaluated="true">
			<Description><![CDATA[
List of options used for rendering the pdf. Except template and jsPDF options, other options(like pageheader, widgetHeader)
are specific to the template. If you have created your custom template, you can define your own options.<br/>
<b>How to write custom pageTemplate? <i>Ref widget info</i></b><br/>
<ul>
	<li><b>template:</b> JS Class which defines/creates PDF Page template. Ref. PDFDefaultTemplate</li>	
	<li><b style="color:#09A4DE;">*&nbsp;:</b> All other options supported by jsPDF library</li>
	<li>&nbsp;&nbsp;</li>
	<li><b>pageheader:</b> PDF Page Header options <i>used by PDFDefaultTemplate</i>.</li>
	<li><b>pagefooter:</b> PDF Page Footer options <i>used by PDFDefaultTemplate</i>.</li>
	<li><b>widgetHeader:</b> Widget Header rendring options <i>used by PDFDefaultTemplate</i>.</li>
	<li><b>widgetContent:</b> Widget Content rendering options <i>used by PDFDefaultTemplate</i>.</li>	
</ul>
			]]></Description>
			<Values>
				<Value>{
  template:PDFDefaultTemplate,
  pageheader: {
    title: $('.pageTitleWidget h1.pageTitle').text(),
    description: '',
    dateFormat: 'DD-MMM-YYYY, hh:mm:ss A',
    logoPath: mashup.baseUrl + '/resources/logo/images/cvlogo_mobile.png',
	activeFilters: {
	  label: $('.stickyContainer .activeFilters .widgetTitle').text(),
	  selector: '.stickyContainer .activeFilters .activeFilter' 
	}
  },
  pagefooter: {
    delimeter:'/',
    copyright:'Dassault Systemes',
  },
  widgetHeader: {
    height: 20,
    fontSize: 12
  },
  widgetContent: {
    fontSize: 10
  },
  
  /* jsPDF Option*/
  orientation:'p', /*portrait*/
  unit: 'pt',
  format: 'a4',
  putOnlyUsedFonts:true,
  fontFamily: 'times',
  fontSize: 14,
  textColor: '#000000',
  margin: {
    top: 15,
    bottom: 15,
    left: 10,
    right: 10
  },
  header: {
    height: 50,
    fontSize: 10,
    titleFontSize: 18
  },
  footer: {
    height: 30,
    fontSize: 10
  }
}				</Value>
			</Values>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		
		<Option id="widgetOptions" name="Widgets Info" isEvaluated="true" arity="ZERO_OR_MANY">
			<Description><![CDATA[
Options required for rendering the widget.
<ul>
	<li><b>type:</b> Type of Widget "Data Table|datatable" or "Chart|chart" or "Custom|custom". Based on this rendering function will be called.</li>
	<li><b>selector:</b> Css Selector for the widget(s) e.g. ".plmaCharts", will add all plmaCharts on the page to PDF</li>
	<li><b>headerSelector:</b> Css Selector for the widget( Header Text</li>
	<li><b>renderWidgetHandler:</b> Widget rendering custom function. Will be used in case the type is 'custom'</li>
</ul>
<i><b>Note:</b></i>
<ul>
  <li>1. The widgets rendered will be in the same order as supplied.</li>
  <li>2. Only <b>plmaDataTable and plmaCharts</b> is supported by default. For any other widget or tweak in default rendring you can use tpye:'custom'.</li>
</ul>
<b>How to write custom rendering? <i>Ref widget info</i></b>
			]]></Description>
			<Values>
				<Value>{
  type: '',
  selector: '',
  headerSelector: '.widgetHeader .widgetTitle'
  /*renderWidgetHandler: function(){}*/ 
}				</Value>
			</Values>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
	</OptionsGroup>
	
	<DefaultValues displayName="Normal" previewUrl="">
		<DefaultValue name="renderOnce">false</DefaultValue>
		<DefaultValue name="showLabel">false</DefaultValue>
		<DefaultValue name="onInit">function() {
  /* Use 'this' to handle the button as a jQuery object */
}</DefaultValue>
		<DefaultValue name="onClick">function(e) {
  this.toggleClass('active');
}</DefaultValue>
	</DefaultValues>
	
</Widget>
