//  update hit selection if user hit back/forward browser btn
$(document).ready(function () {

    $(window).on('popstate.plmaresultlist', function() {
        var helper = new DetailHitHelper();
        if(document.location.href.split('#').length > 1){
            var anchor = document.location.href.split('#')[1];
            if(anchor != undefined && anchor != ""){
                helper.deselectHit();
                var $hit = helper.selectHitFromAnchor(anchor);
                $hit.addClass("selected");
                var paramName = $hit.data('parameter');
                if(paramName){
                    helper.loadDetail(paramName, anchor);
                }
            }
        }
    });

});