var PLMADateRefine = function (uCssId, facetName, refineParam, facetData, currentUrl, ranges, loadDateFacetParameter, calendarFormat) {
	this.uCssId = uCssId;
	this.facetName = facetName;
	this.refineParam = refineParam;
	this.facetData = facetData;
	this.currentUrl = new BuildUrl(window.location.href);
	this.ranges = eval(ranges);
	this.loadDateFacetParameter = loadDateFacetParameter;

	this.i18nClient = new I18nClient();

	this.facetObject = $('.' + this.uCssId + '.refinePanel .filter-list-facet-elem.filter-list-facet-elem-' + this.facetName)[0];

	this.lang = 'en';
	this.calendarFormat = calendarFormat;
	if (document.getElementById('currentLangRefinePanel') !== undefined) {
		this.lang = document.getElementById('currentLangRefinePanel').innerHTML
	}
	moment.locale(this.lang);
	this.isFullSize = false;
};

PLMADateRefine.DEFAULT_START_DATE = '01/01/1970';
PLMADateRefine.DEFAULT_END_DATE = '01/01/2500';

PLMADateRefine.prototype.init = function () {
	/* Activate calendars (in inputs) */
	this.getInitRange();
	this.activateCalendar();
	this.displaySelectedDates();
};

PLMADateRefine.prototype.displaySelectedDates = function () {
	var newDate = this.getNewDateRange();
	if(newDate != undefined)
		this.displaySelectedDatesRefine(newDate);
};

PLMADateRefine.prototype.displaySelectedDatesRefine = function(newDate) {
	var $facetValue = $(this.facetObject).find('.refineName');
	$(this.facetObject).find('.dateRefine-'+this.facetName).removeClass('hidden').addClass('active refined');
	newDate == false ? $facetValue.text(this.getDateRefineValue()) : $facetValue.text(newDate);
};

PLMADateRefine.prototype.getDateRefineValue = function() {
	var value = '';
	var initStartDate = this.getInitStartDate();
	var initEndDate = this.getInitEndDate();
	if (initStartDate === moment(PLMADateRefine.DEFAULT_START_DATE, 'MM/DD/YYYY').format(this.calendarFormat)
		&& initEndDate !== moment(PLMADateRefine.DEFAULT_END_DATE, 'MM/DD/YYYY').format(this.calendarFormat)) {
		value = RefinePanelSearch.getMessage('rangeselector.to') + ' ' + initEndDate;
	} else if (initStartDate !== moment(PLMADateRefine.DEFAULT_START_DATE, 'MM/DD/YYYY').format(this.calendarFormat)
		&& initEndDate === moment(PLMADateRefine.DEFAULT_END_DATE, 'MM/DD/YYYY').format(this.calendarFormat)) {
		value = RefinePanelSearch.getMessage('rangeselector.from') + ' ' + initStartDate;
	} else
		value = initStartDate + ' - ' + initEndDate;
	return value;
};

PLMADateRefine.prototype.getNewDateRange = function () {
	var customParam = this.facetHasCustomDateParam();
	if (this.facetHasDateParam()) {
		return false;
	} else if (customParam) {
		for (var i = 0; i < this.ranges.length; i++) {
			var range = this.ranges[i];
			if (range[5] === customParam.split(',')[1].replace(']', '')) {
				return range[4];
			}
		}
	}
};

PLMADateRefine.prototype.facetHasDateParam = function () {
	var dateParam = this.currentUrl.getParameter(this.refineParam);
	var result = false;
	if (dateParam !== undefined) {
		dateParam.forEach($.proxy(function (param) {
			if (param.indexOf(this.facetName) !== -1 && param.split(',').length === 3) {
				result = param;
			}
		}, this));
	}
	return result;
};

PLMADateRefine.prototype.facetHasCustomDateParam = function () {
	var dateParam = this.currentUrl.getParameter(this.refineParam);
	var result = false;
	if (dateParam !== undefined) {
		dateParam.forEach($.proxy(function (param) {
			if (param.indexOf(this.facetName) !== -1 && param.split(',').length === 2) {
				result = param;
			}
		}, this));
	}
	return result;
};

PLMADateRefine.prototype.getInitStartDate = function () {
	/* Search if this facet is already refine by date */
	var startDate = "";
	var facetParam = this.facetHasDateParam();
	if (facetParam && facetParam.split(',')[1] !== 'null') {
		startDate = moment(facetParam.split(',')[1], 'YYYY-MM-DD').format(this.calendarFormat);
	} else if (facetParam && facetParam.split(',')[1] === 'null') {
		startDate = moment(PLMADateRefine.DEFAULT_START_DATE, 'MM/DD/YYYY').format(this.calendarFormat);
	} else {
		startDate = moment(moment().subtract(1, 'month').format(this.calendarFormat), this.calendarFormat).format(this.calendarFormat);
	}
	return startDate;
};

PLMADateRefine.prototype.getInitEndDate = function () {
	/* Search if this facet is already refine by date */
	var endDate = "";
	var facetParam = this.facetHasDateParam();
	if (facetParam && facetParam.split(',')[2].replace(']', '') !== 'null') {
		endDate = moment(facetParam.split(',')[2].replace(']', ''), 'YYYY-MM-DD').format(this.calendarFormat);
	} else if (facetParam && facetParam.split(',')[2].replace(']', '') === 'null') {
		endDate = moment(PLMADateRefine.DEFAULT_END_DATE, 'MM/DD/YYYY').format(this.calendarFormat);
	} else {
		endDate = moment(moment().format(this.calendarFormat), this.calendarFormat).format(this.calendarFormat);
	}
	return endDate;
};

PLMADateRefine.prototype.getInitRange = function () {
	var dateParam = this.currentUrl.getParameter(this.refineParam);
	if (dateParam !== undefined) {
		dateParam.forEach($.proxy(function (param) {
			if (param.indexOf(this.facetName) !== -1 && param.split(',').length === 2) {
				this.activeRange = param.split(',')[1].replace(']', '');
			}
		}, this));
	}
};

PLMADateRefine.prototype.computeCalendarRanges = function () {
	var ranges = {};
	this.rangesId = {};

	for (var i = 0; i < this.ranges.length; i++) {
		var range = this.ranges[i];
		ranges[range[4]] = [moment().subtract(range[0], range[1]), moment().subtract(range[2], range[3])];
		this.rangesId[range[4]] = range[5];
	}

	return ranges;
};

PLMADateRefine.prototype.activateCalendar = function () {
	var objectTitle = $(this.facetObject).find('.title-container');
	var initStartDate = this.getInitStartDate();
	var initEndDate = this.getInitEndDate();
	var newDateRange = this.getNewDateRange();
	if(newDateRange) {
		var ranges = this.computeCalendarRanges();
		initStartDate = ranges[newDateRange][0].format(this.calendarFormat);
		initEndDate = ranges[newDateRange][1].format(this.calendarFormat);
	}
	objectTitle.daterangepicker({
			startDate: initStartDate,
			endDate: initEndDate,
			ranges: this.computeCalendarRanges(),
			locale: {
				format: this.calendarFormat
			},
			linkedCalendars: false,
			noApplyAction: true,
			autoUpdateInput: true,
			alwaysShowCalendars: true,
			showCustomRangeLabel: false,
			opens: "left",
			showDropdowns: true,
			parentEl: '.' + this.uCssId
		},
		$.proxy(function (start, end, label) {
		}, this)
	);

	this.dateRangePicker = $(this.facetObject).find('.title-container').data('daterangepicker');
	/* Hide custom range button */
	this.dateRangePicker.container.find('.ranges .list-custom-ranges .button:last').addClass('hidden');

	/* Add display of selected date */
	this.dateRangePicker.container.find('.ranges .list-custom-ranges').after($('<div class="current-date"><span class="from"></span><span class="separator"></span><span class="to"></span></div>'));
	this.writeSelectedDate();

	objectTitle.on('apply.daterangepicker', $.proxy(function (ev, picker) {
		this.activeRange = this.getCustomRange(picker);
		this.activateApplyButton(picker.startDate, picker.endDate);
	}, this));

	this.dateRangePicker.container.find('.calendar').on('click.daterangepicker', '.prev, .next', $.proxy(function () {
		if (this.facetData && this.facetData.xAxis) {
			this.displayDateFacetData();
		}
		this.activateToStartButton();
		this.activateToEndButton();

		/* Fix daterangepicker issue on right calendar unable to go to previous years */
		this.fixRightCalendarYearSelect();
	}, this));

	this.dateRangePicker.container.find('.calendar').on('change.daterangepicker', 'select.yearselect, select.monthselect', $.proxy(function () {
		if (this.facetData && this.facetData.xAxis) {
			this.displayDateFacetData();
		}
		this.activateToStartButton();
		this.activateToEndButton();

		/* Fix daterangepicker issue on right calendar unable to go to previous years */
		this.fixRightCalendarYearSelect();
	}, this));

	objectTitle.on('show.daterangepicker', $.proxy(function () {
		if (!this.facetData || !this.facetData.xAxis) {
			this.getDateFacetData();
		}
		this.activateToStartButton();
		this.activateToEndButton();

		if (initStartDate === moment(PLMADateRefine.DEFAULT_START_DATE, 'MM/DD/YYYY').format(this.calendarFormat)) {
			this.reloadDateRangeCalendar(moment(initEndDate, this.calendarFormat));
		}

		if (initEndDate === moment(PLMADateRefine.DEFAULT_END_DATE, 'MM/DD/YYYY').format(this.calendarFormat)) {
			this.reloadDateRangeCalendar(moment(initStartDate, this.calendarFormat));
		}

		/* Fix daterangepicker issue on right calendar unable to go to previous years */
		this.fixRightCalendarYearSelect();
	}, this));

	this.dateRangePicker.container.find('.calendar').on('click.daterangepicker', 'td.available', $.proxy(function (e) {
		var $e = $(e.target);
		this.setDatesIfClickOnData($e);

		if (this.facetData && this.facetData.xAxis) {
			this.displayDateFacetData();
		}
		this.activateToStartButton();
		this.activateToEndButton();

		/* Fix daterangepicker issue on right calendar unable to go to previous years */
		this.fixRightCalendarYearSelect();

		this.writeSelectedDate();
	}, this));
};

PLMADateRefine.prototype.writeSelectedDate = function () {
	// console.log(`Write selected dates --> ${this.dateRangePicker.startDate} - ${this.dateRangePicker.endDate}`);
	this.dateRangePicker.container.find('.ranges .current-date').html(`${this.dateRangePicker.startDate ? this.dateRangePicker.startDate.format(this.calendarFormat) : '&infin;'} - ${this.dateRangePicker.endDate ? this.dateRangePicker.endDate.format(this.calendarFormat) : '&infin;'}`);
	// if (this.dateRangePicker.startDate && this.dateRangePicker.endDate && this.dateRangePicker.endDate.format('L') ===  moment(PLMADateRefine.DEFAULT_END_DATE, this.calendarFormat).format('L')) {
	// 	this.dateRangePicker.container.find('.ranges .current-date .from').html(this.dateRangePicker.startDate.format('L')).before(this.dateRangePicker.container.find('.ranges .current-date .separator').html(RefinePanelSearch.getMessage('rangeselector.to') + ' ').detach());
	// 	this.dateRangePicker.container.find('.ranges .current-date .to').html();
	// } else if (this.dateRangePicker.startDate && this.dateRangePicker.startDate.format('L') ===  moment(PLMADateRefine.DEFAULT_START_DATE, this.calendarFormat).format('L')) {
	// 	this.dateRangePicker.container.find('.ranges .current-date .from').html();
	// 	this.dateRangePicker.container.find('.ranges .current-date .to').html(this.dateRangePicker.endDate.format('L')).before(this.dateRangePicker.container.find('.ranges .current-date .separator').html(RefinePanelSearch.getMessage('rangeselector.to') + ' ').detach());
	// } else if (this.dateRangePicker.startDate && this.dateRangePicker.endDate) {
	// 	this.dateRangePicker.container.find('.ranges .current-date .from').html(this.dateRangePicker.startDate.format('L'));
	// 	this.dateRangePicker.container.find('.ranges .current-date .to').html(this.dateRangePicker.endDate.format('L')).before(this.dateRangePicker.container.find('.ranges .current-date .separator').html(' - ').detach());
	// }
	//
	// this.dateRangePicker.container.find('.ranges .current-date .from').off().on('click', function () {
	// 	if (this.dateRangePicker.startDate) {
	// 		this.dateRangePicker.setStartDate(this.dateRangePicker.startDate);
	// 		this.dateRangePicker.renderCalendar('left');
	// 		this.activateToStartButton();
	// 		this.activateToEndButton();
	// 	}
	// }.bind(this));
	//
	// this.dateRangePicker.container.find('.ranges .current-date .to').off().on('click', function () {
	// 	if (this.dateRangePicker.endDate) {
	// 		this.dateRangePicker.setEndDate(this.dateRangePicker.endDate);
	// 		this.dateRangePicker.renderCalendar('right');
	// 		this.activateToStartButton();
	// 		this.activateToEndButton();
	// 	}
	// }.bind(this));
};

PLMADateRefine.prototype.fixRightCalendarYearSelect = function () {
	/* Fix daterangepicker issue on right calendar unable to go to previous years */
	var year = this.dateRangePicker.rightCalendar.month.year();
	var yearSelect = this.dateRangePicker.container.find('.calendar.right .yearselect');
	if (year < parseInt(yearSelect.val())) {
		var tempYear = parseInt(yearSelect.val()) - 1;
		while (tempYear >= year) {
			yearSelect.prepend($('<option value="' + tempYear + '">' + tempYear + '</option>'));
			tempYear --;
		}
		yearSelect.val(year);
	}
};

PLMADateRefine.prototype.getCustomRange = function (picker) {
	for (var index in picker.ranges) {
		if (index === picker.chosenLabel) {
			return this.rangesId[index];
		}
	}
	return undefined;
};

PLMADateRefine.prototype.setDatesIfClickOnData = function (dayTd) {
	/* If the user clicks on the span with the count (orange one), the set date is not pushed, let's do it */
	if (dayTd.hasClass('valueFacetBorder')) {
		var side = dayTd.closest('.calendar').hasClass('left') ? 'left' : 'right';
		var year = this.dateRangePicker.container.find('.' + side + ' .yearselect').val();
		var month = parseInt(this.dateRangePicker.container.find('.' + side + ' .monthselect').val()) + 1;
		if (dayTd.closest('.available').hasClass('off') && dayTd.closest('.available').data('title').split('c')[0] === 'r0') {
			month = month - 1;
			if (month === 0) {
				month = 12;
				year = year - 1;
			}
		} else if (dayTd.closest('.available').hasClass('off') && dayTd.closest('.available').data('title').split('c')[0] === 'r5') {
			month = month + 1;
			if (month === 13) {
				month = 1;
				year = year + 1;
			}
		}
		var clickDate = month + '/' + dayTd.closest('.available')[0].innerHTML.split('<')[0] + '/' + year;
		var momentClickDate = moment(clickDate, 'MM/DD/YYYY').format(this.calendarFormat);
		if (this.dateRangePicker.startDate && momentClickDate.isAfter(this.dateRangePicker.startDate) && !this.dateRangePicker.endDate) {
			this.setDateRangePickerDates(this.dateRangePicker.startDate, moment(momentClickDate, this.calendarFormat));
		} else {
			this.setDateRangePickerDates(moment(momentClickDate, this.calendarFormat), undefined);
		}
	}
};

PLMADateRefine.prototype.setStartDateIfClickOnData = function (dayTd) {
	/* If the user clicks on the span with the count (orange one), the set date is not pushed, let's do it */
	var side = dayTd.closest('.calendar').hasClass('left') ? 'left' : 'right';
	var month = parseInt(this.dateRangePicker.container.find('.' + side + ' .monthselect').val()) + 1;
	var clickDate = month + '/' + dayTd.closest('.available')[0].innerHTML.split('<')[0] + '/' + this.dateRangePicker.container.find('.' + side + ' .yearselect').val();
	this.setDateRangePickerDates(moment(clickDate, 'MM/DD/YYYY').format(this.calendarFormat), undefined);
};


PLMADateRefine.prototype.setEndDateIfClickOnData = function (dayTd) {
	/* If the user clicks on the span with the count (orange one), the set date is not pushed, let's do it */
	var side = dayTd.closest('.calendar').hasClass('left') ? 'left' : 'right';
	var month = parseInt(this.dateRangePicker.container.find('.' + side + ' .monthselect').val()) + 1;
	var clickDate = month + '/' + dayTd.closest('.available')[0].innerHTML.split('<')[0] + '/' + this.dateRangePicker.container.find('.' + side + ' .yearselect').val();
	this.setDateRangePickerDates(this.dateRangePicker.startDate, moment(clickDate, 'MM/DD/YYYY').format(this.calendarFormat));
};

PLMADateRefine.prototype.getDateFacetData = function () {
	var facetConfig = {};
	facetConfig.facetId = this.facetName;

	var addParams = {};
	addParams[this.loadDateFacetParameter] = this.facetName;

	var decoded = decodeURIComponent(this.currentUrl.toString());
	var client = new PlmaAjaxClient($('.' + this.uCssId));
	var params = decoded.split('?');
	var parameters = getUrlParameters(params[1]);

	/* Delete the base params */
	client.getAjaxUrl().params = {};

	for (var i = 0; i < parameters.length; i++) {
		var key = parameters[i].key;
		var value = parameters[i].value;
		if (key !== "" && key !== "_") {
			/* Remove actual date facet filter if exists */
			if (key !== this.refineParam || value.indexOf(this.facetName) === -1) {
				client.getAjaxUrl().addParameter(key, value, false);
			}
		}
	}

	for (var key in addParams) {
		if (key && key !== "" && addParams[key] && addParams[key] !== "") {
			client.getAjaxUrl().addParameter(key, addParams[key]);
		}
	}

	/* Add refines on loadFacetParameter */
	var params = this.currentUrl.getParameters();
	for (var paramKey in params) {
		if (paramKey.indexOf('.r') !== -1 || paramKey.indexOf('.zr') !== -1) {
			for (var i = 0; i < params[paramKey].length; i++) {
				client.getAjaxUrl().addParameter(this.loadDateFacetParameter, params[paramKey][i].split('/')[1], false);
			}
		}
	}

	/*  set wuids to update */
	client.addWidget(this.uCssId);

	client.getWidget($.proxy(function (widget, script) {
		let facetDataHtml = $(widget[0].html).find('.filter-list-facet-elem.filter-list-facet-elem-' + this.facetName + ' .loadedFacetData').html();
		if(facetDataHtml){
			this.facetData = JSON.parse(facetDataHtml);
			this.maxCountfacet = 0;
			if (this.facetData.series.length > 0) {
				for (var i = 0; i < this.facetData.series[0].data.length; i++) {
					if (this.facetData.series[0].data[i].y > this.maxCountfacet) {
						this.maxCountfacet = this.facetData.series[0].data[i].y;
					}
				}
			}
			this.displayDateFacetData();
		}
	}, this), function () {

	});
};

PLMADateRefine.prototype.displayDateFacetData = function () {
	/* Get Month and Year of left calendar */
	this.addDataForCalendar('left');
	this.addDataForCalendar('right');

};

PLMADateRefine.prototype.addDataForCalendar = function (side) {
	var calendarName = side + "Calendar";
	for (var i = 0; i < this.dateRangePicker[calendarName].calendar.length; i++) {
		var row = this.dateRangePicker[calendarName].calendar[i];
		for (var j = 0; j < row.length; j++) {
			/* Get the cell date */
			var cell = row[j];
			var dateCell = cell.format('YYYY/MM/DD');

			/* Get the date facet count */
			var facetDateIndex = this.facetData.xAxis.categories.indexOf(dateCell);
			if (facetDateIndex !== -1) {
				var facetCount = this.facetData.series[0].data[facetDateIndex].y;

				/* Set this count into the corresponding calendar cell */
				if (facetCount > 0) {
					var htmlRow = this.dateRangePicker.container.find('.calendar.' + side + ' table tbody tr')[i];
					var htmlCell = $(htmlRow).find('td')[j];
					if ($(htmlCell).find('.valueFacetBorder').length === 0) {
						$(htmlCell).append($('<span class="valueFacetBorder" style="height: ' + Math.round(facetCount * $(htmlCell).height() / this.maxCountfacet) + 'px"></span>'));
						$(htmlCell).attr('title', facetCount);
					}
				}
			}
		}
	}
};

PLMADateRefine.prototype.activateApplyButton = function (start, end) {
	/* Compute the url */
	var dateParam = this.refineParam;
	var facetName = this.facetName;
	/* Search if this facet is already refine by date */
	this.currentUrl.removeParameterWithPrefixedValue_(dateParam, facetName);
	this.currentUrl.removeParameter("_");
	this.currentUrl.removeParameter("");
	this.currentUrl.removeParameter("amp;_");
	this.currentUrl.removeParameter(RefinePanelSearch.PARAMETER_IS_SUGGEST_SEARCH);
	this.currentUrl.removeParameter(RefinePanelSearch.PARAMETER_IS_SUGGEST_SEARCH_EMPTY);

	/* add the new refine */
	if (this.activeRange && this.activeRange !== '') {
		this.currentUrl.addParameter(dateParam, '[' + facetName + ',' + this.activeRange + ']');
	} else {
		if (start.isSame(moment(PLMADateRefine.DEFAULT_START_DATE, 'MM/DD/YYYY').format(this.calendarFormat)) && !end.isSame(moment(PLMADateRefine.DEFAULT_END_DATE, 'MM/DD/YYYY').format(this.calendarFormat))) {
			this.currentUrl.addParameter(dateParam, '[' + facetName + ',' + null + ',' + end.format('YYYY-MM-DD') + ']', false);
		} else if (!start.isSame(moment(PLMADateRefine.DEFAULT_START_DATE, 'MM/DD/YYYY').format(this.calendarFormat)) && end.isSame(moment(PLMADateRefine.DEFAULT_END_DATE, 'MM/DD/YYYY').format(this.calendarFormat))) {
			this.currentUrl.addParameter(dateParam, '[' + facetName + ',' + start.format('YYYY-MM-DD') + ',' + null + ']', false);
		} else {
			this.currentUrl.addParameter(dateParam, '[' + facetName + ',' + start.format('YYYY-MM-DD') + ',' + end.format('YYYY-MM-DD') + ']', false);
		}
	}

    /* If normal mode, apply the refine */
    window.location.replace(this.currentUrl.toString());
};

PLMADateRefine.prototype.activateToStartButton = function () {
	/* Adds button that set the start date to -inf */
	var $toStartButton = this.addToStartButton();
	$toStartButton.on('click', $.proxy(function () {
		/* We don't want the calendar to go to -inf */
		this.dateRangePicker.container.find('.ranges').off('mouseenter.daterangepicker', 'li.button');

		/* Sets start date to -inf */
		this.setDateRangePickerDates(moment(PLMADateRefine.DEFAULT_START_DATE, this.calendarFormat), null);

		/* Removes the +inf button */
		this.dateRangePicker.container.find('.to-end-icon').remove();

		if (this.facetData && this.facetData.xAxis) {
			this.displayDateFacetData();
		}

		/* After the user chose the end date, we set the calendar to the end date month */
		var reloaded = false;
		this.dateRangePicker.container.find('.calendar').one('click.daterangepicker', 'td.available', $.proxy(function (e) {
			if (!reloaded) {
				this.reloadDateRangeCalendar(this.dateRangePicker.endDate);
				reloaded = true;

				if (this.facetData && this.facetData.xAxis) {
					this.displayDateFacetData();
				}
			}
		}, this));
	}, this));
};

PLMADateRefine.prototype.activateToEndButton = function () {
	if (!this.dateRangePicker.startDate.isSame(moment(PLMADateRefine.DEFAULT_START_DATE, this.calendarFormat))) {
		var $toEndButton = this.addToEndButton();
		$toEndButton.on('click', $.proxy(function () {
			/* We don't want the calendar to go to -inf */
			this.dateRangePicker.container.find('.ranges').off('mouseenter.daterangepicker', 'li.button');

			/* Sets end date to +inf */
			this.setDateRangePickerDates(this.dateRangePicker.startDate, moment(PLMADateRefine.DEFAULT_END_DATE, this.calendarFormat));
			this.dateRangePicker.container.find('.applyBtn').removeAttr('disabled').removeClass('disabled');

			if (this.facetData && this.facetData.xAxis) {
				this.displayDateFacetData();
			}

			this.writeSelectedDate();
		}, this));
	}
};

PLMADateRefine.prototype.setDateRangePickerDates = function (startDate, endDate) {
	this.dateRangePicker.startDate = startDate;
	this.dateRangePicker.endDate = endDate;
	this.dateRangePicker.updateCalendars();
	this.activateToStartButton();
	this.activateToEndButton();
};

PLMADateRefine.prototype.reloadDateRangeCalendar = function (date) {
	this.dateRangePicker.leftCalendar.month = date.clone().date(2).subtract(1, 'month');
	this.dateRangePicker.rightCalendar.month = date.clone().date(2);
	this.dateRangePicker.updateCalendars();
	this.activateToStartButton();
	this.activateToEndButton();
};

PLMADateRefine.prototype.addToStartButton = function () {
	var $toStartButton = $('<span class="to-start-icon fonticon fonticon-to-start" title="' + RefinePanelSearch.getMessage('rangeselector.calendar.select.from') + '"></span>');
	if (this.dateRangePicker.container.find('.left thead tr:first th:last .to-start-icon').length > 0) {
		$toStartButton = this.dateRangePicker.container.find('.left thead tr:first th:last .to-start-icon');
	} else {
		this.dateRangePicker.container.find('.left thead tr:first th:last').append($toStartButton);
	}
	return $toStartButton;
};

PLMADateRefine.prototype.addToEndButton = function () {
	var $toEndButton = $('<span class="to-end-icon fonticon fonticon-to-end" title="' + RefinePanelSearch.getMessage('rangeselector.calendar.select.to') + '"></span>');
	if (this.dateRangePicker.container.find('.right thead tr:first th:first .to-end-icon').length > 0) {
		$toEndButton = this.dateRangePicker.container.find('.right thead tr:first th:first .to-end-icon');
	} else {
		this.dateRangePicker.container.find('.right thead tr:first th:first').prepend($toEndButton);
	}
	return $toEndButton;
};
