function AlertingPopup(options) {
	this.options = options;
	this.init();
}

AlertingPopup.prototype.init = function () {
	var formBuilder = new AlertingFormBuilder();
	var form = formBuilder
		.withType(AlertingForm.TYPE_CREATE)
		.withFacets(this.options.facets)
        .withSeriesAndFacets(this.options.seriesAndFacets)
		.withSeries(this.options.alertingSeries)
		.withPageName(this.options.pageName)
		.withChartId(this.options.chartID)
		.withOnSubmitSuccess(function (alertingForm) {
			$.notify('"' + alertingForm.options.data.title + '" has been successfully created', "success");
			this.hidePopup();
		}.bind(this))
		.withOnSubmitFail(function (alertingForm) {
			$.notify('creation error', "error");
		})
		.withOnCancel(this.hidePopup.bind(this))
		.withUser(this.options.user)
		.build();
	this.initPopup(form);
};

AlertingPopup.prototype.initPopup = function (form) {
	var lightboxOptions = {
		container: $(this.options.lightboxContainer),
		title : '<h2 class="h2">Create an alert</h2>',
		content: form.to$(),
		extraCss: 'alert_lb'
	};
	this.lightbox = new PLMALightbox(lightboxOptions);
};

AlertingPopup.prototype.showPopup = function () {
	this.lightbox.show();
};

AlertingPopup.prototype.hidePopup = function () {
	this.lightbox.hide();
};

AlertingPopup.utils = {
	ALERT_BTN_EVENT: "plma:alerting-popup",
	ALERT_BTN_ICON: "fonticon fonticon-bell-alt",
	ALERT_BTN_LABEL: mashupI18N.get('alerting', 'alerting.alert'),
	findChartWidget : function($e) {
		if ($e.hasClass("plmaCharts")) {
			return $e;
		} else {
			if ($e.hasClass("highcharts-container")) {
				return $e.closest(".plmaCharts");
			} else {
				//target is the flipContainer -> find plmaCharts inside
				return $e.find(".plmaCharts");
			}
		}
	},
	createAlertBtn: function(uCssId){
		var buttonManager = new WidgetButtonManager(uCssId, 'menu', '.headerChartContainer');
		if (!buttonManager.hasButton(AlertingPopup.utils.ALERT_BTN_ICON, AlertingPopup.utils.ALERT_BTN_LABEL)) {
			buttonManager.addButton(
				AlertingPopup.utils.ALERT_BTN_ICON,
				AlertingPopup.utils.ALERT_BTN_LABEL,
				function () {
					$(document).trigger(AlertingPopup.utils.ALERT_BTN_EVENT, uCssId)
				},
				undefined,
				"alerting-btn"
			);
		}
	},
	renderPopupAfterClick: function(options){
		$(document).on(AlertingPopup.utils.ALERT_BTN_EVENT, function (e, uCssId){
			var plmaChart2 = $("."+uCssId).data("widget");
			if(plmaChart2){
				var response = this.getFeedsResponse(plmaChart2);
				$.when.apply(null, response.ajaxPromises)
					.done(function () {
						// args is an array of (data, textStatus, jqXHR)
						var args = Array.prototype.slice.call(arguments);
						/*
							let n > 1, promises.length === n => args.length === n
							but promises.length === 1 => args.length === 3 (expected: 1)
						*/
						if (response.ajaxPromises.length === 1) {
							args = [args];
						}

                        // facets is an array of object with a mapping facetName => categories
                        var facets = [];
                        args.forEach(function(arg){
                            arg[0].facets.forEach(function (facet) {
                                facets.push(facet);
                            })
                        });

                        // series + corresponding facets from request
                        var seriesAndFacets = [];
                        args.forEach(function (arg, i) {
                            var series = response.seriesConfiguration[i];
                            series.forEach(function(serie){
                                arg[0].facets.forEach(function(facet,i){
                                    var facetName = Object.keys(facet)[0];
                                    if(serie.isMultiDimensionFacet){
                                        var dim2 = (arg[0].facets[i+1]?arg[0].facets[i+1][facetName]:undefined);
                                        if((facetName === serie.facet || facetName === serie.facetI18n) && dim2){
                                            serie["categories"] = {"dimension1": facet[facetName], "dimension2" : dim2};
                                        }
                                    }else{
                                        if(facetName === serie.facet || facetName === serie.facetI18n){
                                            serie["categories"] = facet[facetName];
                                        }
                                    }
                                });
                                seriesAndFacets.push(serie);
                            });
                        });

                        var alerting = new AlertingPopup($.extend({
							lightboxContainer   : "#mainWrapper",
							pageName 			: window.mashup.pageName,
							chartID 			: uCssId.split("_")[0],
							selectedFeeds 	 	: plmaChart2.options.selectedFeeds,
							alertingSeries 	  	: plmaChart2.options.dataSeriesConfiguration,
                            facets				: facets,
                            seriesAndFacets     : seriesAndFacets
						}, options));
						alerting.showPopup();
					});
			}else {
				if(typeof console != "undefined" && console.error !== undefined) {
					console.error("Could not resolve chart configuration used by alerting...");
				}
			}
		}.bind(this));
	},
    createBtnFromChart : function($chart){
        if ($chart && $chart.length) {
            var chartUcssId = PlmaAjaxClient._getUCssId($chart.attr("class"));
            AlertingPopup.utils.createAlertBtn(chartUcssId);
        }
    },
	renderAlertBtn: function(options){
        if (options.waitForTrigger === "true") {
            $(document).on("plma:chart-is-loaded.alerting", function(e){
                var $chart = AlertingPopup.utils.findChartWidget($(e.target));
                AlertingPopup.utils.createBtnFromChart($chart);
            }.bind(this));
        }else{
            $(".plmaCharts").each(function (index, chart) {
                AlertingPopup.utils.createBtnFromChart($(chart));
            });
        }
	},
    getFeedsResponse: function (plmaChart2) {
        // mapping feedName => { uri, facetIdList }
        var feeds = {};
        plmaChart2.options.dataSeriesConfiguration.forEach(function (serieConfig) {
            var feedName = serieConfig.feed || plmaChart2.options.selectedFeeds[0];
            if (!feeds[feedName]) {
                feeds[feedName] = {
                    uri: plmaChart2.options.feedsDefinition[feedName],
                    facetIdList: [],
                    limitCategoriesFacetList: [],
                    seriesConfiguration: []
                }
            }
            feeds[feedName].facetIdList.push(serieConfig.facet);
            if(serieConfig.isMultiDimensionFacet){
                feeds[feedName].limitCategoriesFacetList.push(serieConfig.multiDimensionFacetIds);
            }
            feeds[feedName].seriesConfiguration.push(serieConfig);
        });
        var response = {ajaxPromises : [], seriesConfiguration: []};
        Object.keys(feeds).forEach(function (feedName) {
            var feed = feeds[feedName];
            response.ajaxPromises.push($.ajax({
                url: mashup.baseUrl + '/ajax/resultFeed/' + feed.uri,
                dataType: 'json',
                data: {
                    "facetIdList": feed.facetIdList,
                    "limitCategoriesFacetList": feed.limitCategoriesFacetList
                }
            }));
            response.seriesConfiguration.push(feed.seriesConfiguration);
        });
        return response;
    }
};
