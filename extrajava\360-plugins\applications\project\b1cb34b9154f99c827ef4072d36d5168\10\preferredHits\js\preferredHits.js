/**
 * Bookmarks library
 *
 * @param uCssId Widget CSS UUID
 * @param options Library options
 * @constructor Constructor
 */
var PreferredHits = function (uCssId, options) {
    this.uCssId = uCssId;
    this.widget = $('.' + uCssId);
    this.options = options;
    this.init();
};

PreferredHits.prototype.init = function () {
    $('.favorites-actions').on('click', $.proxy(function (e) {
        var currentUrl = new BuildUrl(window.location.href);
        if(currentUrl.getParameter("displayView") == 'compare'){
            currentUrl.removeParameter('displayView')
        }
        if (currentUrl.getParameter(this.options.urlParameter) == this.options.collection) {
            currentUrl.removeParameter(this.options.urlParameter);
        } else {
            currentUrl.addParameter(this.options.urlParameter, this.options.collection, true);
        }
        window.location.href = currentUrl.toString();
    }, this));

    $('#clear-preferred-hits').on('click', $.proxy(function (e) {
        phUUID = this.uCssId;
        $.ajax({
            method: 'DELETE',
            url: this.options.url + '/clear/' + this.options.collection,
            dataType: 'JSON',
            async: false,
            success: function (data) {
                //$('.' + phUUID).find('.counter').text('0');
                //$.notify('Hits successfully removed from favorites', "success");
                // Reload page to be sure displayed selected hits are not selected anymore
                window.location.href = window.location.pathname;
            },
            error: function (data) {
                $.notify('Error removing hits from favorites', "error");
            }
        });
        window.location.reload()
    }, this));
}