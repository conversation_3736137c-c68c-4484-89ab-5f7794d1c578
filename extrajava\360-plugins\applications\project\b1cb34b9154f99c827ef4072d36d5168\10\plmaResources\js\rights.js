var RightManager = function (readers, writers, user) {
	this.readers = readers;
	this.writers = writers;
	this.user = user;
};

RightManager.prototype.hasWriteRight = function () {
	return this.hasRight(this.writers);
};

RightManager.prototype.hasReadRight = function () {
	return this.hasRight(this.readers);
};

RightManager.prototype.hasRight = function (rights) {
	for (var i = 0; i < rights.length; i++) {
		if (rights[i].trim().toLowerCase() === this.user.toLowerCase() || rights[i].trim() === 'ALL') {
			return true;
		}
	}
	return false;
};