/**
 * vis.js
 * https://github.com/almende/vis
 *
 * A dynamic, browser-based visualization library.
 *
 * @version 4.16.1
 * @date    2016-10-06
 *
 * @license
 * Copyright (C) 2011-2016 Almende B.V, http://almende.com
 *
 * Vis.js is dual licensed under both
 *
 * * The Apache 2.0 License
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * and
 *
 * * The MIT License
 *   http://opensource.org/licenses/MIT
 *
 * Vis.js may be distributed under either license.
 */
"use strict";!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.vis=e():t.vis=e()}(this,function(){return function(t){function e(n){if(i[n])return i[n].exports;var r=i[n]={exports:{},id:n,loaded:!1};return t[n].call(r.exports,r,r.exports,e),r.loaded=!0,r.exports}var i={};return e.m=t,e.c=i,e.p="",e(0)}([function(t,e,i){e.util=i(1),e.DOMutil=i(7),e.DataSet=i(8),e.DataView=i(10),e.Queue=i(9),e.Graph3d=i(11),e.graph3d={Camera:i(15),Filter:i(16),Point2d:i(14),Point3d:i(13),Slider:i(17),StepNumber:i(18)},e.moment=i(2),e.Hammer=i(19),e.keycharm=i(22)},function(t,e,i){var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol?"symbol":typeof t},r=i(2),s=i(6);e.isNumber=function(t){return t instanceof Number||"number"==typeof t},e.recursiveDOMDelete=function(t){if(t)for(;t.hasChildNodes()===!0;)e.recursiveDOMDelete(t.firstChild),t.removeChild(t.firstChild)},e.giveRange=function(t,e,i,n){if(e==t)return.5;var r=1/(e-t);return Math.max(0,(n-t)*r)},e.isString=function(t){return t instanceof String||"string"==typeof t},e.isDate=function(t){if(t instanceof Date)return!0;if(e.isString(t)){var i=o.exec(t);if(i)return!0;if(!isNaN(Date.parse(t)))return!0}return!1},e.randomUUID=function(){return s.v4()},e.assignAllKeys=function(t,e){for(var i in t)t.hasOwnProperty(i)&&"object"!==n(t[i])&&(t[i]=e)},e.fillIfDefined=function(t,i){var r=!(arguments.length<=2||void 0===arguments[2])&&arguments[2];for(var s in t)void 0!==i[s]&&("object"!==n(i[s])?void 0!==i[s]&&null!==i[s]||void 0===t[s]||r!==!0?t[s]=i[s]:delete t[s]:"object"===n(t[s])&&e.fillIfDefined(t[s],i[s],r))},e.protoExtend=function(t,e){for(var i=1;i<arguments.length;i++){var n=arguments[i];for(var r in n)t[r]=n[r]}return t},e.extend=function(t,e){for(var i=1;i<arguments.length;i++){var n=arguments[i];for(var r in n)n.hasOwnProperty(r)&&(t[r]=n[r])}return t},e.selectiveExtend=function(t,e,i){if(!Array.isArray(t))throw new Error("Array with property names expected as first argument");for(var n=2;n<arguments.length;n++)for(var r=arguments[n],s=0;s<t.length;s++){var o=t[s];r.hasOwnProperty(o)&&(e[o]=r[o])}return e},e.selectiveDeepExtend=function(t,i,n){var r=!(arguments.length<=3||void 0===arguments[3])&&arguments[3];if(Array.isArray(n))throw new TypeError("Arrays are not supported by deepExtend");for(var s=2;s<arguments.length;s++)for(var o=arguments[s],a=0;a<t.length;a++){var h=t[a];if(o.hasOwnProperty(h))if(n[h]&&n[h].constructor===Object)void 0===i[h]&&(i[h]={}),i[h].constructor===Object?e.deepExtend(i[h],n[h],!1,r):null===n[h]&&void 0!==i[h]&&r===!0?delete i[h]:i[h]=n[h];else{if(Array.isArray(n[h]))throw new TypeError("Arrays are not supported by deepExtend");null===n[h]&&void 0!==i[h]&&r===!0?delete i[h]:i[h]=n[h]}}return i},e.selectiveNotDeepExtend=function(t,i,n){var r=!(arguments.length<=3||void 0===arguments[3])&&arguments[3];if(Array.isArray(n))throw new TypeError("Arrays are not supported by deepExtend");for(var s in n)if(n.hasOwnProperty(s)&&t.indexOf(s)==-1)if(n[s]&&n[s].constructor===Object)void 0===i[s]&&(i[s]={}),i[s].constructor===Object?e.deepExtend(i[s],n[s]):null===n[s]&&void 0!==i[s]&&r===!0?delete i[s]:i[s]=n[s];else if(Array.isArray(n[s])){i[s]=[];for(var o=0;o<n[s].length;o++)i[s].push(n[s][o])}else null===n[s]&&void 0!==i[s]&&r===!0?delete i[s]:i[s]=n[s];return i},e.deepExtend=function(t,i,n,r){for(var s in i)if(i.hasOwnProperty(s)||n===!0)if(i[s]&&i[s].constructor===Object)void 0===t[s]&&(t[s]={}),t[s].constructor===Object?e.deepExtend(t[s],i[s],n):null===i[s]&&void 0!==t[s]&&r===!0?delete t[s]:t[s]=i[s];else if(Array.isArray(i[s])){t[s]=[];for(var o=0;o<i[s].length;o++)t[s].push(i[s][o])}else null===i[s]&&void 0!==t[s]&&r===!0?delete t[s]:t[s]=i[s];return t},e.equalArray=function(t,e){if(t.length!=e.length)return!1;for(var i=0,n=t.length;i<n;i++)if(t[i]!=e[i])return!1;return!0},e.convert=function(t,i){var n;if(void 0!==t){if(null===t)return null;if(!i)return t;if("string"!=typeof i&&!(i instanceof String))throw new Error("Type must be a string");switch(i){case"boolean":case"Boolean":return Boolean(t);case"number":case"Number":return Number(t.valueOf());case"string":case"String":return String(t);case"Date":if(e.isNumber(t))return new Date(t);if(t instanceof Date)return new Date(t.valueOf());if(r.isMoment(t))return new Date(t.valueOf());if(e.isString(t))return n=o.exec(t),n?new Date(Number(n[1])):r(t).toDate();throw new Error("Cannot convert object of type "+e.getType(t)+" to type Date");case"Moment":if(e.isNumber(t))return r(t);if(t instanceof Date)return r(t.valueOf());if(r.isMoment(t))return r(t);if(e.isString(t))return n=o.exec(t),r(n?Number(n[1]):t);throw new Error("Cannot convert object of type "+e.getType(t)+" to type Date");case"ISODate":if(e.isNumber(t))return new Date(t);if(t instanceof Date)return t.toISOString();if(r.isMoment(t))return t.toDate().toISOString();if(e.isString(t))return n=o.exec(t),n?new Date(Number(n[1])).toISOString():new Date(t).toISOString();throw new Error("Cannot convert object of type "+e.getType(t)+" to type ISODate");case"ASPDate":if(e.isNumber(t))return"/Date("+t+")/";if(t instanceof Date)return"/Date("+t.valueOf()+")/";if(e.isString(t)){n=o.exec(t);var s;return s=n?new Date(Number(n[1])).valueOf():new Date(t).valueOf(),"/Date("+s+")/"}throw new Error("Cannot convert object of type "+e.getType(t)+" to type ASPDate");default:throw new Error('Unknown type "'+i+'"')}}};var o=/^\/?Date\((\-?\d+)/i;e.getType=function(t){var e="undefined"==typeof t?"undefined":n(t);return"object"==e?null===t?"null":t instanceof Boolean?"Boolean":t instanceof Number?"Number":t instanceof String?"String":Array.isArray(t)?"Array":t instanceof Date?"Date":"Object":"number"==e?"Number":"boolean"==e?"Boolean":"string"==e?"String":void 0===e?"undefined":e},e.copyAndExtendArray=function(t,e){for(var i=[],n=0;n<t.length;n++)i.push(t[n]);return i.push(e),i},e.copyArray=function(t){for(var e=[],i=0;i<t.length;i++)e.push(t[i]);return e},e.getAbsoluteLeft=function(t){return t.getBoundingClientRect().left},e.getAbsoluteRight=function(t){return t.getBoundingClientRect().right},e.getAbsoluteTop=function(t){return t.getBoundingClientRect().top},e.addClassName=function(t,e){var i=t.className.split(" ");i.indexOf(e)==-1&&(i.push(e),t.className=i.join(" "))},e.removeClassName=function(t,e){var i=t.className.split(" "),n=i.indexOf(e);n!=-1&&(i.splice(n,1),t.className=i.join(" "))},e.forEach=function(t,e){var i,n;if(Array.isArray(t))for(i=0,n=t.length;i<n;i++)e(t[i],i,t);else for(i in t)t.hasOwnProperty(i)&&e(t[i],i,t)},e.toArray=function(t){var e=[];for(var i in t)t.hasOwnProperty(i)&&e.push(t[i]);return e},e.updateProperty=function(t,e,i){return t[e]!==i&&(t[e]=i,!0)},e.throttle=function(t,e){var i=null,n=!1;return function r(){i?n=!0:(n=!1,t(),i=setTimeout(function(){i=null,n&&r()},e))}},e.addEventListener=function(t,e,i,n){t.addEventListener?(void 0===n&&(n=!1),"mousewheel"===e&&navigator.userAgent.indexOf("Firefox")>=0&&(e="DOMMouseScroll"),t.addEventListener(e,i,n)):t.attachEvent("on"+e,i)},e.removeEventListener=function(t,e,i,n){t.removeEventListener?(void 0===n&&(n=!1),"mousewheel"===e&&navigator.userAgent.indexOf("Firefox")>=0&&(e="DOMMouseScroll"),t.removeEventListener(e,i,n)):t.detachEvent("on"+e,i)},e.preventDefault=function(t){t||(t=window.event),t.preventDefault?t.preventDefault():t.returnValue=!1},e.getTarget=function(t){t||(t=window.event);var e;return t.target?e=t.target:t.srcElement&&(e=t.srcElement),void 0!=e.nodeType&&3==e.nodeType&&(e=e.parentNode),e},e.hasParent=function(t,e){for(var i=t;i;){if(i===e)return!0;i=i.parentNode}return!1},e.option={},e.option.asBoolean=function(t,e){return"function"==typeof t&&(t=t()),null!=t?0!=t:e||null},e.option.asNumber=function(t,e){return"function"==typeof t&&(t=t()),null!=t?Number(t)||e||null:e||null},e.option.asString=function(t,e){return"function"==typeof t&&(t=t()),null!=t?String(t):e||null},e.option.asSize=function(t,i){return"function"==typeof t&&(t=t()),e.isString(t)?t:e.isNumber(t)?t+"px":i||null},e.option.asElement=function(t,e){return"function"==typeof t&&(t=t()),t||e||null},e.hexToRGB=function(t){var e=/^#?([a-f\d])([a-f\d])([a-f\d])$/i;t=t.replace(e,function(t,e,i,n){return e+e+i+i+n+n});var i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return i?{r:parseInt(i[1],16),g:parseInt(i[2],16),b:parseInt(i[3],16)}:null},e.overrideOpacity=function(t,i){if(t.indexOf("rgba")!=-1)return t;if(t.indexOf("rgb")!=-1){var n=t.substr(t.indexOf("(")+1).replace(")","").split(",");return"rgba("+n[0]+","+n[1]+","+n[2]+","+i+")"}var n=e.hexToRGB(t);return null==n?t:"rgba("+n.r+","+n.g+","+n.b+","+i+")"},e.RGBToHex=function(t,e,i){return"#"+((1<<24)+(t<<16)+(e<<8)+i).toString(16).slice(1)},e.parseColor=function(t){var i;if(e.isString(t)===!0){if(e.isValidRGB(t)===!0){var n=t.substr(4).substr(0,t.length-5).split(",").map(function(t){return parseInt(t)});t=e.RGBToHex(n[0],n[1],n[2])}if(e.isValidHex(t)===!0){var r=e.hexToHSV(t),s={h:r.h,s:.8*r.s,v:Math.min(1,1.02*r.v)},o={h:r.h,s:Math.min(1,1.25*r.s),v:.8*r.v},a=e.HSVToHex(o.h,o.s,o.v),h=e.HSVToHex(s.h,s.s,s.v);i={background:t,border:a,highlight:{background:h,border:a},hover:{background:h,border:a}}}else i={background:t,border:t,highlight:{background:t,border:t},hover:{background:t,border:t}}}else i={},i.background=t.background||void 0,i.border=t.border||void 0,e.isString(t.highlight)?i.highlight={border:t.highlight,background:t.highlight}:(i.highlight={},i.highlight.background=t.highlight&&t.highlight.background||void 0,i.highlight.border=t.highlight&&t.highlight.border||void 0),e.isString(t.hover)?i.hover={border:t.hover,background:t.hover}:(i.hover={},i.hover.background=t.hover&&t.hover.background||void 0,i.hover.border=t.hover&&t.hover.border||void 0);return i},e.RGBToHSV=function(t,e,i){t/=255,e/=255,i/=255;var n=Math.min(t,Math.min(e,i)),r=Math.max(t,Math.max(e,i));if(n==r)return{h:0,s:0,v:n};var s=t==n?e-i:i==n?t-e:i-t,o=t==n?3:i==n?1:5,a=60*(o-s/(r-n))/360,h=(r-n)/r,l=r;return{h:a,s:h,v:l}};var a={split:function(t){var e={};return t.split(";").forEach(function(t){if(""!=t.trim()){var i=t.split(":"),n=i[0].trim(),r=i[1].trim();e[n]=r}}),e},join:function(t){return Object.keys(t).map(function(e){return e+": "+t[e]}).join("; ")}};e.addCssText=function(t,i){var n=a.split(t.style.cssText),r=a.split(i),s=e.extend(n,r);t.style.cssText=a.join(s)},e.removeCssText=function(t,e){var i=a.split(t.style.cssText),n=a.split(e);for(var r in n)n.hasOwnProperty(r)&&delete i[r];t.style.cssText=a.join(i)},e.HSVToRGB=function(t,e,i){var n,r,s,o=Math.floor(6*t),a=6*t-o,h=i*(1-e),l=i*(1-a*e),u=i*(1-(1-a)*e);switch(o%6){case 0:n=i,r=u,s=h;break;case 1:n=l,r=i,s=h;break;case 2:n=h,r=i,s=u;break;case 3:n=h,r=l,s=i;break;case 4:n=u,r=h,s=i;break;case 5:n=i,r=h,s=l}return{r:Math.floor(255*n),g:Math.floor(255*r),b:Math.floor(255*s)}},e.HSVToHex=function(t,i,n){var r=e.HSVToRGB(t,i,n);return e.RGBToHex(r.r,r.g,r.b)},e.hexToHSV=function(t){var i=e.hexToRGB(t);return e.RGBToHSV(i.r,i.g,i.b)},e.isValidHex=function(t){var e=/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(t);return e},e.isValidRGB=function(t){t=t.replace(" ","");var e=/rgb\((\d{1,3}),(\d{1,3}),(\d{1,3})\)/i.test(t);return e},e.isValidRGBA=function(t){t=t.replace(" ","");var e=/rgba\((\d{1,3}),(\d{1,3}),(\d{1,3}),(.{1,3})\)/i.test(t);return e},e.selectiveBridgeObject=function(t,i){if("object"==("undefined"==typeof i?"undefined":n(i))){for(var r=Object.create(i),s=0;s<t.length;s++)i.hasOwnProperty(t[s])&&"object"==n(i[t[s]])&&(r[t[s]]=e.bridgeObject(i[t[s]]));return r}return null},e.bridgeObject=function(t){if("object"==("undefined"==typeof t?"undefined":n(t))){var i=Object.create(t);for(var r in t)t.hasOwnProperty(r)&&"object"==n(t[r])&&(i[r]=e.bridgeObject(t[r]));return i}return null},e.insertSort=function(t,e){for(var i=0;i<t.length;i++){for(var n=t[i],r=i;r>0&&e(n,t[r-1])<0;r--)t[r]=t[r-1];t[r]=n}return t},e.mergeOptions=function(t,e,i){var n=(!(arguments.length<=3||void 0===arguments[3])&&arguments[3],arguments.length<=4||void 0===arguments[4]?{}:arguments[4]);if(null===e[i])t[i]=Object.create(n[i]);else if(void 0!==e[i])if("boolean"==typeof e[i])t[i].enabled=e[i];else{void 0===e[i].enabled&&(t[i].enabled=!0);for(var r in e[i])e[i].hasOwnProperty(r)&&(t[i][r]=e[i][r])}},e.binarySearchCustom=function(t,e,i,n){for(var r=1e4,s=0,o=0,a=t.length-1;o<=a&&s<r;){var h=Math.floor((o+a)/2),l=t[h],u=void 0===n?l[i]:l[i][n],d=e(u);if(0==d)return h;d==-1?o=h+1:a=h-1,s++}return-1},e.binarySearchValue=function(t,e,i,n,r){for(var s,o,a,h,l=1e4,u=0,d=0,c=t.length-1,r=void 0!=r?r:function(t,e){return t==e?0:t<e?-1:1};d<=c&&u<l;){if(h=Math.floor(.5*(c+d)),s=t[Math.max(0,h-1)][i],o=t[h][i],a=t[Math.min(t.length-1,h+1)][i],0==r(o,e))return h;if(r(s,e)<0&&r(o,e)>0)return"before"==n?Math.max(0,h-1):h;if(r(o,e)<0&&r(a,e)>0)return"before"==n?h:Math.min(t.length-1,h+1);r(o,e)<0?d=h+1:c=h-1,u++}return-1},e.easingFunctions={linear:function(t){return t},easeInQuad:function(t){return t*t},easeOutQuad:function(t){return t*(2-t)},easeInOutQuad:function(t){return t<.5?2*t*t:-1+(4-2*t)*t},easeInCubic:function(t){return t*t*t},easeOutCubic:function(t){return--t*t*t+1},easeInOutCubic:function(t){return t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1},easeInQuart:function(t){return t*t*t*t},easeOutQuart:function(t){return 1- --t*t*t*t},easeInOutQuart:function(t){return t<.5?8*t*t*t*t:1-8*--t*t*t*t},easeInQuint:function(t){return t*t*t*t*t},easeOutQuint:function(t){return 1+--t*t*t*t*t},easeInOutQuint:function(t){return t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t}}},function(t,e,i){t.exports="undefined"!=typeof window&&window.moment||i(3)},function(t,e,i){(function(t){!function(e,i){t.exports=i()}(this,function(){function e(){return pn.apply(null,arguments)}function i(t){pn=t}function n(t){return t instanceof Array||"[object Array]"===Object.prototype.toString.call(t)}function r(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function s(t){var e;for(e in t)return!1;return!0}function o(t){return t instanceof Date||"[object Date]"===Object.prototype.toString.call(t)}function a(t,e){var i,n=[];for(i=0;i<t.length;++i)n.push(e(t[i],i));return n}function h(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function l(t,e){for(var i in e)h(e,i)&&(t[i]=e[i]);return h(e,"toString")&&(t.toString=e.toString),h(e,"valueOf")&&(t.valueOf=e.valueOf),t}function u(t,e,i,n){return ye(t,e,i,n,!0).utc()}function d(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],meridiem:null}}function c(t){return null==t._pf&&(t._pf=d()),t._pf}function f(t){if(null==t._isValid){var e=c(t),i=mn.call(e.parsedDateParts,function(t){return null!=t}),n=!isNaN(t._d.getTime())&&e.overflow<0&&!e.empty&&!e.invalidMonth&&!e.invalidWeekday&&!e.nullInput&&!e.invalidFormat&&!e.userInvalidated&&(!e.meridiem||e.meridiem&&i);if(t._strict&&(n=n&&0===e.charsLeftOver&&0===e.unusedTokens.length&&void 0===e.bigHour),null!=Object.isFrozen&&Object.isFrozen(t))return n;t._isValid=n}return t._isValid}function p(t){var e=u(NaN);return null!=t?l(c(e),t):c(e).userInvalidated=!0,e}function m(t){return void 0===t}function v(t,e){var i,n,r;if(m(e._isAMomentObject)||(t._isAMomentObject=e._isAMomentObject),m(e._i)||(t._i=e._i),m(e._f)||(t._f=e._f),m(e._l)||(t._l=e._l),m(e._strict)||(t._strict=e._strict),m(e._tzm)||(t._tzm=e._tzm),m(e._isUTC)||(t._isUTC=e._isUTC),m(e._offset)||(t._offset=e._offset),m(e._pf)||(t._pf=c(e)),m(e._locale)||(t._locale=e._locale),vn.length>0)for(i in vn)n=vn[i],r=e[n],m(r)||(t[n]=r);return t}function y(t){v(this,t),this._d=new Date(null!=t._d?t._d.getTime():NaN),yn===!1&&(yn=!0,e.updateOffset(this),yn=!1)}function g(t){return t instanceof y||null!=t&&null!=t._isAMomentObject}function _(t){return t<0?Math.ceil(t)||0:Math.floor(t)}function x(t){var e=+t,i=0;return 0!==e&&isFinite(e)&&(i=_(e)),i}function w(t,e,i){var n,r=Math.min(t.length,e.length),s=Math.abs(t.length-e.length),o=0;for(n=0;n<r;n++)(i&&t[n]!==e[n]||!i&&x(t[n])!==x(e[n]))&&o++;return o+s}function b(t){e.suppressDeprecationWarnings===!1&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+t)}function M(t,i){var n=!0;return l(function(){if(null!=e.deprecationHandler&&e.deprecationHandler(null,t),n){for(var r,s=[],o=0;o<arguments.length;o++){if(r="","object"==typeof arguments[o]){r+="\n["+o+"] ";for(var a in arguments[0])r+=a+": "+arguments[0][a]+", ";r=r.slice(0,-2)}else r=arguments[o];s.push(r)}b(t+"\nArguments: "+Array.prototype.slice.call(s).join("")+"\n"+(new Error).stack),n=!1}return i.apply(this,arguments)},i)}function S(t,i){null!=e.deprecationHandler&&e.deprecationHandler(t,i),gn[t]||(b(i),gn[t]=!0)}function T(t){return t instanceof Function||"[object Function]"===Object.prototype.toString.call(t)}function D(t){var e,i;for(i in t)e=t[i],T(e)?this[i]=e:this["_"+i]=e;this._config=t,this._ordinalParseLenient=new RegExp(this._ordinalParse.source+"|"+/\d{1,2}/.source)}function k(t,e){var i,n=l({},t);for(i in e)h(e,i)&&(r(t[i])&&r(e[i])?(n[i]={},l(n[i],t[i]),l(n[i],e[i])):null!=e[i]?n[i]=e[i]:delete n[i]);for(i in t)h(t,i)&&!h(e,i)&&r(t[i])&&(n[i]=l({},n[i]));return n}function C(t){null!=t&&this.set(t)}function O(t,e,i){var n=this._calendar[t]||this._calendar.sameElse;return T(n)?n.call(e,i):n}function P(t){var e=this._longDateFormat[t],i=this._longDateFormat[t.toUpperCase()];return e||!i?e:(this._longDateFormat[t]=i.replace(/MMMM|MM|DD|dddd/g,function(t){return t.slice(1)}),this._longDateFormat[t])}function E(){return this._invalidDate}function L(t){return this._ordinal.replace("%d",t)}function Y(t,e,i,n){var r=this._relativeTime[i];return T(r)?r(t,e,i,n):r.replace(/%d/i,t)}function R(t,e){var i=this._relativeTime[t>0?"future":"past"];return T(i)?i(e):i.replace(/%s/i,e)}function A(t,e){var i=t.toLowerCase();kn[i]=kn[i+"s"]=kn[e]=t}function I(t){return"string"==typeof t?kn[t]||kn[t.toLowerCase()]:void 0}function z(t){var e,i,n={};for(i in t)h(t,i)&&(e=I(i),e&&(n[e]=t[i]));return n}function W(t,e){Cn[t]=e}function N(t){var e=[];for(var i in t)e.push({unit:i,priority:Cn[i]});return e.sort(function(t,e){return t.priority-e.priority}),e}function F(t,i){return function(n){return null!=n?(j(this,t,n),e.updateOffset(this,i),this):V(this,t)}}function V(t,e){return t.isValid()?t._d["get"+(t._isUTC?"UTC":"")+e]():NaN}function j(t,e,i){t.isValid()&&t._d["set"+(t._isUTC?"UTC":"")+e](i)}function B(t){return t=I(t),T(this[t])?this[t]():this}function U(t,e){if("object"==typeof t){t=z(t);for(var i=N(t),n=0;n<i.length;n++)this[i[n].unit](t[i[n].unit])}else if(t=I(t),T(this[t]))return this[t](e);return this}function H(t,e,i){var n=""+Math.abs(t),r=e-n.length,s=t>=0;return(s?i?"+":"":"-")+Math.pow(10,Math.max(0,r)).toString().substr(1)+n}function G(t,e,i,n){var r=n;"string"==typeof n&&(r=function(){return this[n]()}),t&&(Ln[t]=r),e&&(Ln[e[0]]=function(){return H(r.apply(this,arguments),e[1],e[2])}),i&&(Ln[i]=function(){return this.localeData().ordinal(r.apply(this,arguments),t)})}function X(t){return t.match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"")}function Z(t){var e,i,n=t.match(On);for(e=0,i=n.length;e<i;e++)Ln[n[e]]?n[e]=Ln[n[e]]:n[e]=X(n[e]);return function(e){var r,s="";for(r=0;r<i;r++)s+=n[r]instanceof Function?n[r].call(e,t):n[r];return s}}function q(t,e){return t.isValid()?(e=Q(e,t.localeData()),En[e]=En[e]||Z(e),En[e](t)):t.localeData().invalidDate()}function Q(t,e){function i(t){return e.longDateFormat(t)||t}var n=5;for(Pn.lastIndex=0;n>=0&&Pn.test(t);)t=t.replace(Pn,i),Pn.lastIndex=0,n-=1;return t}function $(t,e,i){Qn[t]=T(e)?e:function(t,n){return t&&i?i:e}}function J(t,e){return h(Qn,t)?Qn[t](e._strict,e._locale):new RegExp(K(t))}function K(t){return tt(t.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(t,e,i,n,r){return e||i||n||r}))}function tt(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function et(t,e){var i,n=e;for("string"==typeof t&&(t=[t]),"number"==typeof e&&(n=function(t,i){i[e]=x(t)}),i=0;i<t.length;i++)$n[t[i]]=n}function it(t,e){et(t,function(t,i,n,r){n._w=n._w||{},e(t,n._w,n,r)})}function nt(t,e,i){null!=e&&h($n,t)&&$n[t](e,i._a,i,t)}function rt(t,e){return new Date(Date.UTC(t,e+1,0)).getUTCDate()}function st(t,e){return t?n(this._months)?this._months[t.month()]:this._months[(this._months.isFormat||ar).test(e)?"format":"standalone"][t.month()]:this._months}function ot(t,e){return t?n(this._monthsShort)?this._monthsShort[t.month()]:this._monthsShort[ar.test(e)?"format":"standalone"][t.month()]:this._monthsShort}function at(t,e,i){var n,r,s,o=t.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],n=0;n<12;++n)s=u([2e3,n]),this._shortMonthsParse[n]=this.monthsShort(s,"").toLocaleLowerCase(),this._longMonthsParse[n]=this.months(s,"").toLocaleLowerCase();return i?"MMM"===e?(r=xn.call(this._shortMonthsParse,o),r!==-1?r:null):(r=xn.call(this._longMonthsParse,o),r!==-1?r:null):"MMM"===e?(r=xn.call(this._shortMonthsParse,o),r!==-1?r:(r=xn.call(this._longMonthsParse,o),r!==-1?r:null)):(r=xn.call(this._longMonthsParse,o),r!==-1?r:(r=xn.call(this._shortMonthsParse,o),r!==-1?r:null))}function ht(t,e,i){var n,r,s;if(this._monthsParseExact)return at.call(this,t,e,i);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),n=0;n<12;n++){if(r=u([2e3,n]),i&&!this._longMonthsParse[n]&&(this._longMonthsParse[n]=new RegExp("^"+this.months(r,"").replace(".","")+"$","i"),this._shortMonthsParse[n]=new RegExp("^"+this.monthsShort(r,"").replace(".","")+"$","i")),i||this._monthsParse[n]||(s="^"+this.months(r,"")+"|^"+this.monthsShort(r,""),this._monthsParse[n]=new RegExp(s.replace(".",""),"i")),i&&"MMMM"===e&&this._longMonthsParse[n].test(t))return n;if(i&&"MMM"===e&&this._shortMonthsParse[n].test(t))return n;if(!i&&this._monthsParse[n].test(t))return n}}function lt(t,e){var i;if(!t.isValid())return t;if("string"==typeof e)if(/^\d+$/.test(e))e=x(e);else if(e=t.localeData().monthsParse(e),"number"!=typeof e)return t;return i=Math.min(t.date(),rt(t.year(),e)),t._d["set"+(t._isUTC?"UTC":"")+"Month"](e,i),t}function ut(t){return null!=t?(lt(this,t),e.updateOffset(this,!0),this):V(this,"Month")}function dt(){return rt(this.year(),this.month())}function ct(t){return this._monthsParseExact?(h(this,"_monthsRegex")||pt.call(this),t?this._monthsShortStrictRegex:this._monthsShortRegex):(h(this,"_monthsShortRegex")||(this._monthsShortRegex=ur),this._monthsShortStrictRegex&&t?this._monthsShortStrictRegex:this._monthsShortRegex)}function ft(t){return this._monthsParseExact?(h(this,"_monthsRegex")||pt.call(this),t?this._monthsStrictRegex:this._monthsRegex):(h(this,"_monthsRegex")||(this._monthsRegex=dr),this._monthsStrictRegex&&t?this._monthsStrictRegex:this._monthsRegex)}function pt(){function t(t,e){return e.length-t.length}var e,i,n=[],r=[],s=[];for(e=0;e<12;e++)i=u([2e3,e]),n.push(this.monthsShort(i,"")),r.push(this.months(i,"")),s.push(this.months(i,"")),s.push(this.monthsShort(i,""));for(n.sort(t),r.sort(t),s.sort(t),e=0;e<12;e++)n[e]=tt(n[e]),r[e]=tt(r[e]);for(e=0;e<24;e++)s[e]=tt(s[e]);this._monthsRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+n.join("|")+")","i")}function mt(t){return vt(t)?366:365}function vt(t){return t%4===0&&t%100!==0||t%400===0}function yt(){return vt(this.year())}function gt(t,e,i,n,r,s,o){var a=new Date(t,e,i,n,r,s,o);return t<100&&t>=0&&isFinite(a.getFullYear())&&a.setFullYear(t),a}function _t(t){var e=new Date(Date.UTC.apply(null,arguments));return t<100&&t>=0&&isFinite(e.getUTCFullYear())&&e.setUTCFullYear(t),e}function xt(t,e,i){var n=7+e-i,r=(7+_t(t,0,n).getUTCDay()-e)%7;return-r+n-1}function wt(t,e,i,n,r){var s,o,a=(7+i-n)%7,h=xt(t,n,r),l=1+7*(e-1)+a+h;return l<=0?(s=t-1,o=mt(s)+l):l>mt(t)?(s=t+1,o=l-mt(t)):(s=t,o=l),{year:s,dayOfYear:o}}function bt(t,e,i){var n,r,s=xt(t.year(),e,i),o=Math.floor((t.dayOfYear()-s-1)/7)+1;return o<1?(r=t.year()-1,n=o+Mt(r,e,i)):o>Mt(t.year(),e,i)?(n=o-Mt(t.year(),e,i),r=t.year()+1):(r=t.year(),n=o),{week:n,year:r}}function Mt(t,e,i){var n=xt(t,e,i),r=xt(t+1,e,i);return(mt(t)-n+r)/7}function St(t){return bt(t,this._week.dow,this._week.doy).week}function Tt(){return this._week.dow}function Dt(){return this._week.doy}function kt(t){var e=this.localeData().week(this);return null==t?e:this.add(7*(t-e),"d")}function Ct(t){var e=bt(this,1,4).week;return null==t?e:this.add(7*(t-e),"d")}function Ot(t,e){return"string"!=typeof t?t:isNaN(t)?(t=e.weekdaysParse(t),"number"==typeof t?t:null):parseInt(t,10)}function Pt(t,e){return"string"==typeof t?e.weekdaysParse(t)%7||7:isNaN(t)?null:t}function Et(t,e){return t?n(this._weekdays)?this._weekdays[t.day()]:this._weekdays[this._weekdays.isFormat.test(e)?"format":"standalone"][t.day()]:this._weekdays}function Lt(t){return t?this._weekdaysShort[t.day()]:this._weekdaysShort}function Yt(t){return t?this._weekdaysMin[t.day()]:this._weekdaysMin}function Rt(t,e,i){var n,r,s,o=t.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],n=0;n<7;++n)s=u([2e3,1]).day(n),this._minWeekdaysParse[n]=this.weekdaysMin(s,"").toLocaleLowerCase(),this._shortWeekdaysParse[n]=this.weekdaysShort(s,"").toLocaleLowerCase(),this._weekdaysParse[n]=this.weekdays(s,"").toLocaleLowerCase();return i?"dddd"===e?(r=xn.call(this._weekdaysParse,o),r!==-1?r:null):"ddd"===e?(r=xn.call(this._shortWeekdaysParse,o),r!==-1?r:null):(r=xn.call(this._minWeekdaysParse,o),r!==-1?r:null):"dddd"===e?(r=xn.call(this._weekdaysParse,o),r!==-1?r:(r=xn.call(this._shortWeekdaysParse,o),r!==-1?r:(r=xn.call(this._minWeekdaysParse,o),r!==-1?r:null))):"ddd"===e?(r=xn.call(this._shortWeekdaysParse,o),r!==-1?r:(r=xn.call(this._weekdaysParse,o),r!==-1?r:(r=xn.call(this._minWeekdaysParse,o),r!==-1?r:null))):(r=xn.call(this._minWeekdaysParse,o),r!==-1?r:(r=xn.call(this._weekdaysParse,o),r!==-1?r:(r=xn.call(this._shortWeekdaysParse,o),r!==-1?r:null)))}function At(t,e,i){var n,r,s;if(this._weekdaysParseExact)return Rt.call(this,t,e,i);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),n=0;n<7;n++){if(r=u([2e3,1]).day(n),i&&!this._fullWeekdaysParse[n]&&(this._fullWeekdaysParse[n]=new RegExp("^"+this.weekdays(r,"").replace(".",".?")+"$","i"),this._shortWeekdaysParse[n]=new RegExp("^"+this.weekdaysShort(r,"").replace(".",".?")+"$","i"),this._minWeekdaysParse[n]=new RegExp("^"+this.weekdaysMin(r,"").replace(".",".?")+"$","i")),this._weekdaysParse[n]||(s="^"+this.weekdays(r,"")+"|^"+this.weekdaysShort(r,"")+"|^"+this.weekdaysMin(r,""),this._weekdaysParse[n]=new RegExp(s.replace(".",""),"i")),i&&"dddd"===e&&this._fullWeekdaysParse[n].test(t))return n;if(i&&"ddd"===e&&this._shortWeekdaysParse[n].test(t))return n;if(i&&"dd"===e&&this._minWeekdaysParse[n].test(t))return n;if(!i&&this._weekdaysParse[n].test(t))return n}}function It(t){if(!this.isValid())return null!=t?this:NaN;var e=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=t?(t=Ot(t,this.localeData()),this.add(t-e,"d")):e}function zt(t){if(!this.isValid())return null!=t?this:NaN;var e=(this.day()+7-this.localeData()._week.dow)%7;return null==t?e:this.add(t-e,"d")}function Wt(t){if(!this.isValid())return null!=t?this:NaN;if(null!=t){var e=Pt(t,this.localeData());return this.day(this.day()%7?e:e-7)}return this.day()||7}function Nt(t){return this._weekdaysParseExact?(h(this,"_weekdaysRegex")||jt.call(this),t?this._weekdaysStrictRegex:this._weekdaysRegex):(h(this,"_weekdaysRegex")||(this._weekdaysRegex=yr),this._weekdaysStrictRegex&&t?this._weekdaysStrictRegex:this._weekdaysRegex)}function Ft(t){return this._weekdaysParseExact?(h(this,"_weekdaysRegex")||jt.call(this),t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(h(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=gr),this._weekdaysShortStrictRegex&&t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}function Vt(t){return this._weekdaysParseExact?(h(this,"_weekdaysRegex")||jt.call(this),t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(h(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=_r),this._weekdaysMinStrictRegex&&t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function jt(){function t(t,e){return e.length-t.length}var e,i,n,r,s,o=[],a=[],h=[],l=[];for(e=0;e<7;e++)i=u([2e3,1]).day(e),n=this.weekdaysMin(i,""),r=this.weekdaysShort(i,""),s=this.weekdays(i,""),o.push(n),a.push(r),h.push(s),l.push(n),l.push(r),l.push(s);for(o.sort(t),a.sort(t),h.sort(t),l.sort(t),e=0;e<7;e++)a[e]=tt(a[e]),h[e]=tt(h[e]),l[e]=tt(l[e]);this._weekdaysRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+h.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+o.join("|")+")","i")}function Bt(){return this.hours()%12||12}function Ut(){return this.hours()||24}function Ht(t,e){G(t,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),e)})}function Gt(t,e){return e._meridiemParse}function Xt(t){return"p"===(t+"").toLowerCase().charAt(0)}function Zt(t,e,i){return t>11?i?"pm":"PM":i?"am":"AM"}function qt(t){return t?t.toLowerCase().replace("_","-"):t}function Qt(t){for(var e,i,n,r,s=0;s<t.length;){for(r=qt(t[s]).split("-"),e=r.length,i=qt(t[s+1]),i=i?i.split("-"):null;e>0;){if(n=$t(r.slice(0,e).join("-")))return n;if(i&&i.length>=e&&w(r,i,!0)>=e-1)break;e--}s++}return null}function $t(e){var i=null;if(!Sr[e]&&"undefined"!=typeof t&&t&&t.exports)try{i=xr._abbr,!function(){var t=new Error('Cannot find module "./locale"');throw t.code="MODULE_NOT_FOUND",t}(),Jt(i)}catch(n){}return Sr[e]}function Jt(t,e){var i;return t&&(i=m(e)?ee(t):Kt(t,e),i&&(xr=i)),xr._abbr}function Kt(t,e){if(null!==e){var i=Mr;return e.abbr=t,null!=Sr[t]?(S("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),i=Sr[t]._config):null!=e.parentLocale&&(null!=Sr[e.parentLocale]?i=Sr[e.parentLocale]._config:S("parentLocaleUndefined","specified parentLocale is not defined yet. See http://momentjs.com/guides/#/warnings/parent-locale/")),Sr[t]=new C(k(i,e)),Jt(t),Sr[t]}return delete Sr[t],null}function te(t,e){if(null!=e){var i,n=Mr;null!=Sr[t]&&(n=Sr[t]._config),e=k(n,e),i=new C(e),i.parentLocale=Sr[t],Sr[t]=i,Jt(t)}else null!=Sr[t]&&(null!=Sr[t].parentLocale?Sr[t]=Sr[t].parentLocale:null!=Sr[t]&&delete Sr[t]);return Sr[t]}function ee(t){var e;if(t&&t._locale&&t._locale._abbr&&(t=t._locale._abbr),!t)return xr;if(!n(t)){if(e=$t(t))return e;t=[t]}return Qt(t)}function ie(){return _n(Sr)}function ne(t){var e,i=t._a;return i&&c(t).overflow===-2&&(e=i[Kn]<0||i[Kn]>11?Kn:i[tr]<1||i[tr]>rt(i[Jn],i[Kn])?tr:i[er]<0||i[er]>24||24===i[er]&&(0!==i[ir]||0!==i[nr]||0!==i[rr])?er:i[ir]<0||i[ir]>59?ir:i[nr]<0||i[nr]>59?nr:i[rr]<0||i[rr]>999?rr:-1,c(t)._overflowDayOfYear&&(e<Jn||e>tr)&&(e=tr),c(t)._overflowWeeks&&e===-1&&(e=sr),c(t)._overflowWeekday&&e===-1&&(e=or),c(t).overflow=e),t}function re(t){var e,i,n,r,s,o,a=t._i,h=Tr.exec(a)||Dr.exec(a);if(h){for(c(t).iso=!0,e=0,i=Cr.length;e<i;e++)if(Cr[e][1].exec(h[1])){r=Cr[e][0],n=Cr[e][2]!==!1;break}if(null==r)return void(t._isValid=!1);if(h[3]){for(e=0,i=Or.length;e<i;e++)if(Or[e][1].exec(h[3])){s=(h[2]||" ")+Or[e][0];break}if(null==s)return void(t._isValid=!1)}if(!n&&null!=s)return void(t._isValid=!1);if(h[4]){if(!kr.exec(h[4]))return void(t._isValid=!1);o="Z"}t._f=r+(s||"")+(o||""),
ue(t)}else t._isValid=!1}function se(t){var i=Pr.exec(t._i);return null!==i?void(t._d=new Date((+i[1]))):(re(t),void(t._isValid===!1&&(delete t._isValid,e.createFromInputFallback(t))))}function oe(t,e,i){return null!=t?t:null!=e?e:i}function ae(t){var i=new Date(e.now());return t._useUTC?[i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()]:[i.getFullYear(),i.getMonth(),i.getDate()]}function he(t){var e,i,n,r,s=[];if(!t._d){for(n=ae(t),t._w&&null==t._a[tr]&&null==t._a[Kn]&&le(t),t._dayOfYear&&(r=oe(t._a[Jn],n[Jn]),t._dayOfYear>mt(r)&&(c(t)._overflowDayOfYear=!0),i=_t(r,0,t._dayOfYear),t._a[Kn]=i.getUTCMonth(),t._a[tr]=i.getUTCDate()),e=0;e<3&&null==t._a[e];++e)t._a[e]=s[e]=n[e];for(;e<7;e++)t._a[e]=s[e]=null==t._a[e]?2===e?1:0:t._a[e];24===t._a[er]&&0===t._a[ir]&&0===t._a[nr]&&0===t._a[rr]&&(t._nextDay=!0,t._a[er]=0),t._d=(t._useUTC?_t:gt).apply(null,s),null!=t._tzm&&t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),t._nextDay&&(t._a[er]=24)}}function le(t){var e,i,n,r,s,o,a,h;e=t._w,null!=e.GG||null!=e.W||null!=e.E?(s=1,o=4,i=oe(e.GG,t._a[Jn],bt(ge(),1,4).year),n=oe(e.W,1),r=oe(e.E,1),(r<1||r>7)&&(h=!0)):(s=t._locale._week.dow,o=t._locale._week.doy,i=oe(e.gg,t._a[Jn],bt(ge(),s,o).year),n=oe(e.w,1),null!=e.d?(r=e.d,(r<0||r>6)&&(h=!0)):null!=e.e?(r=e.e+s,(e.e<0||e.e>6)&&(h=!0)):r=s),n<1||n>Mt(i,s,o)?c(t)._overflowWeeks=!0:null!=h?c(t)._overflowWeekday=!0:(a=wt(i,n,r,s,o),t._a[Jn]=a.year,t._dayOfYear=a.dayOfYear)}function ue(t){if(t._f===e.ISO_8601)return void re(t);t._a=[],c(t).empty=!0;var i,n,r,s,o,a=""+t._i,h=a.length,l=0;for(r=Q(t._f,t._locale).match(On)||[],i=0;i<r.length;i++)s=r[i],n=(a.match(J(s,t))||[])[0],n&&(o=a.substr(0,a.indexOf(n)),o.length>0&&c(t).unusedInput.push(o),a=a.slice(a.indexOf(n)+n.length),l+=n.length),Ln[s]?(n?c(t).empty=!1:c(t).unusedTokens.push(s),nt(s,n,t)):t._strict&&!n&&c(t).unusedTokens.push(s);c(t).charsLeftOver=h-l,a.length>0&&c(t).unusedInput.push(a),t._a[er]<=12&&c(t).bigHour===!0&&t._a[er]>0&&(c(t).bigHour=void 0),c(t).parsedDateParts=t._a.slice(0),c(t).meridiem=t._meridiem,t._a[er]=de(t._locale,t._a[er],t._meridiem),he(t),ne(t)}function de(t,e,i){var n;return null==i?e:null!=t.meridiemHour?t.meridiemHour(e,i):null!=t.isPM?(n=t.isPM(i),n&&e<12&&(e+=12),n||12!==e||(e=0),e):e}function ce(t){var e,i,n,r,s;if(0===t._f.length)return c(t).invalidFormat=!0,void(t._d=new Date(NaN));for(r=0;r<t._f.length;r++)s=0,e=v({},t),null!=t._useUTC&&(e._useUTC=t._useUTC),e._f=t._f[r],ue(e),f(e)&&(s+=c(e).charsLeftOver,s+=10*c(e).unusedTokens.length,c(e).score=s,(null==n||s<n)&&(n=s,i=e));l(t,i||e)}function fe(t){if(!t._d){var e=z(t._i);t._a=a([e.year,e.month,e.day||e.date,e.hour,e.minute,e.second,e.millisecond],function(t){return t&&parseInt(t,10)}),he(t)}}function pe(t){var e=new y(ne(me(t)));return e._nextDay&&(e.add(1,"d"),e._nextDay=void 0),e}function me(t){var e=t._i,i=t._f;return t._locale=t._locale||ee(t._l),null===e||void 0===i&&""===e?p({nullInput:!0}):("string"==typeof e&&(t._i=e=t._locale.preparse(e)),g(e)?new y(ne(e)):(n(i)?ce(t):o(e)?t._d=e:i?ue(t):ve(t),f(t)||(t._d=null),t))}function ve(t){var i=t._i;void 0===i?t._d=new Date(e.now()):o(i)?t._d=new Date(i.valueOf()):"string"==typeof i?se(t):n(i)?(t._a=a(i.slice(0),function(t){return parseInt(t,10)}),he(t)):"object"==typeof i?fe(t):"number"==typeof i?t._d=new Date(i):e.createFromInputFallback(t)}function ye(t,e,i,o,a){var h={};return"boolean"==typeof i&&(o=i,i=void 0),(r(t)&&s(t)||n(t)&&0===t.length)&&(t=void 0),h._isAMomentObject=!0,h._useUTC=h._isUTC=a,h._l=i,h._i=t,h._f=e,h._strict=o,pe(h)}function ge(t,e,i,n){return ye(t,e,i,n,!1)}function _e(t,e){var i,r;if(1===e.length&&n(e[0])&&(e=e[0]),!e.length)return ge();for(i=e[0],r=1;r<e.length;++r)e[r].isValid()&&!e[r][t](i)||(i=e[r]);return i}function xe(){var t=[].slice.call(arguments,0);return _e("isBefore",t)}function we(){var t=[].slice.call(arguments,0);return _e("isAfter",t)}function be(t){var e=z(t),i=e.year||0,n=e.quarter||0,r=e.month||0,s=e.week||0,o=e.day||0,a=e.hour||0,h=e.minute||0,l=e.second||0,u=e.millisecond||0;this._milliseconds=+u+1e3*l+6e4*h+1e3*a*60*60,this._days=+o+7*s,this._months=+r+3*n+12*i,this._data={},this._locale=ee(),this._bubble()}function Me(t){return t instanceof be}function Se(t){return t<0?Math.round(-1*t)*-1:Math.round(t)}function Te(t,e){G(t,0,0,function(){var t=this.utcOffset(),i="+";return t<0&&(t=-t,i="-"),i+H(~~(t/60),2)+e+H(~~t%60,2)})}function De(t,e){var i=(e||"").match(t)||[],n=i[i.length-1]||[],r=(n+"").match(Rr)||["-",0,0],s=+(60*r[1])+x(r[2]);return"+"===r[0]?s:-s}function ke(t,i){var n,r;return i._isUTC?(n=i.clone(),r=(g(t)||o(t)?t.valueOf():ge(t).valueOf())-n.valueOf(),n._d.setTime(n._d.valueOf()+r),e.updateOffset(n,!1),n):ge(t).local()}function Ce(t){return 15*-Math.round(t._d.getTimezoneOffset()/15)}function Oe(t,i){var n,r=this._offset||0;return this.isValid()?null!=t?("string"==typeof t?t=De(Xn,t):Math.abs(t)<16&&(t=60*t),!this._isUTC&&i&&(n=Ce(this)),this._offset=t,this._isUTC=!0,null!=n&&this.add(n,"m"),r!==t&&(!i||this._changeInProgress?He(this,Fe(t-r,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,e.updateOffset(this,!0),this._changeInProgress=null)),this):this._isUTC?r:Ce(this):null!=t?this:NaN}function Pe(t,e){return null!=t?("string"!=typeof t&&(t=-t),this.utcOffset(t,e),this):-this.utcOffset()}function Ee(t){return this.utcOffset(0,t)}function Le(t){return this._isUTC&&(this.utcOffset(0,t),this._isUTC=!1,t&&this.subtract(Ce(this),"m")),this}function Ye(){if(this._tzm)this.utcOffset(this._tzm);else if("string"==typeof this._i){var t=De(Gn,this._i);0===t?this.utcOffset(0,!0):this.utcOffset(De(Gn,this._i))}return this}function Re(t){return!!this.isValid()&&(t=t?ge(t).utcOffset():0,(this.utcOffset()-t)%60===0)}function Ae(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function Ie(){if(!m(this._isDSTShifted))return this._isDSTShifted;var t={};if(v(t,this),t=me(t),t._a){var e=t._isUTC?u(t._a):ge(t._a);this._isDSTShifted=this.isValid()&&w(t._a,e.toArray())>0}else this._isDSTShifted=!1;return this._isDSTShifted}function ze(){return!!this.isValid()&&!this._isUTC}function We(){return!!this.isValid()&&this._isUTC}function Ne(){return!!this.isValid()&&(this._isUTC&&0===this._offset)}function Fe(t,e){var i,n,r,s=t,o=null;return Me(t)?s={ms:t._milliseconds,d:t._days,M:t._months}:"number"==typeof t?(s={},e?s[e]=t:s.milliseconds=t):(o=Ar.exec(t))?(i="-"===o[1]?-1:1,s={y:0,d:x(o[tr])*i,h:x(o[er])*i,m:x(o[ir])*i,s:x(o[nr])*i,ms:x(Se(1e3*o[rr]))*i}):(o=Ir.exec(t))?(i="-"===o[1]?-1:1,s={y:Ve(o[2],i),M:Ve(o[3],i),w:Ve(o[4],i),d:Ve(o[5],i),h:Ve(o[6],i),m:Ve(o[7],i),s:Ve(o[8],i)}):null==s?s={}:"object"==typeof s&&("from"in s||"to"in s)&&(r=Be(ge(s.from),ge(s.to)),s={},s.ms=r.milliseconds,s.M=r.months),n=new be(s),Me(t)&&h(t,"_locale")&&(n._locale=t._locale),n}function Ve(t,e){var i=t&&parseFloat(t.replace(",","."));return(isNaN(i)?0:i)*e}function je(t,e){var i={milliseconds:0,months:0};return i.months=e.month()-t.month()+12*(e.year()-t.year()),t.clone().add(i.months,"M").isAfter(e)&&--i.months,i.milliseconds=+e-+t.clone().add(i.months,"M"),i}function Be(t,e){var i;return t.isValid()&&e.isValid()?(e=ke(e,t),t.isBefore(e)?i=je(t,e):(i=je(e,t),i.milliseconds=-i.milliseconds,i.months=-i.months),i):{milliseconds:0,months:0}}function Ue(t,e){return function(i,n){var r,s;return null===n||isNaN(+n)||(S(e,"moment()."+e+"(period, number) is deprecated. Please use moment()."+e+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),s=i,i=n,n=s),i="string"==typeof i?+i:i,r=Fe(i,n),He(this,r,t),this}}function He(t,i,n,r){var s=i._milliseconds,o=Se(i._days),a=Se(i._months);t.isValid()&&(r=null==r||r,s&&t._d.setTime(t._d.valueOf()+s*n),o&&j(t,"Date",V(t,"Date")+o*n),a&&lt(t,V(t,"Month")+a*n),r&&e.updateOffset(t,o||a))}function Ge(t,e){var i=t.diff(e,"days",!0);return i<-6?"sameElse":i<-1?"lastWeek":i<0?"lastDay":i<1?"sameDay":i<2?"nextDay":i<7?"nextWeek":"sameElse"}function Xe(t,i){var n=t||ge(),r=ke(n,this).startOf("day"),s=e.calendarFormat(this,r)||"sameElse",o=i&&(T(i[s])?i[s].call(this,n):i[s]);return this.format(o||this.localeData().calendar(s,this,ge(n)))}function Ze(){return new y(this)}function qe(t,e){var i=g(t)?t:ge(t);return!(!this.isValid()||!i.isValid())&&(e=I(m(e)?"millisecond":e),"millisecond"===e?this.valueOf()>i.valueOf():i.valueOf()<this.clone().startOf(e).valueOf())}function Qe(t,e){var i=g(t)?t:ge(t);return!(!this.isValid()||!i.isValid())&&(e=I(m(e)?"millisecond":e),"millisecond"===e?this.valueOf()<i.valueOf():this.clone().endOf(e).valueOf()<i.valueOf())}function $e(t,e,i,n){return n=n||"()",("("===n[0]?this.isAfter(t,i):!this.isBefore(t,i))&&(")"===n[1]?this.isBefore(e,i):!this.isAfter(e,i))}function Je(t,e){var i,n=g(t)?t:ge(t);return!(!this.isValid()||!n.isValid())&&(e=I(e||"millisecond"),"millisecond"===e?this.valueOf()===n.valueOf():(i=n.valueOf(),this.clone().startOf(e).valueOf()<=i&&i<=this.clone().endOf(e).valueOf()))}function Ke(t,e){return this.isSame(t,e)||this.isAfter(t,e)}function ti(t,e){return this.isSame(t,e)||this.isBefore(t,e)}function ei(t,e,i){var n,r,s,o;return this.isValid()?(n=ke(t,this),n.isValid()?(r=6e4*(n.utcOffset()-this.utcOffset()),e=I(e),"year"===e||"month"===e||"quarter"===e?(o=ii(this,n),"quarter"===e?o/=3:"year"===e&&(o/=12)):(s=this-n,o="second"===e?s/1e3:"minute"===e?s/6e4:"hour"===e?s/36e5:"day"===e?(s-r)/864e5:"week"===e?(s-r)/6048e5:s),i?o:_(o)):NaN):NaN}function ii(t,e){var i,n,r=12*(e.year()-t.year())+(e.month()-t.month()),s=t.clone().add(r,"months");return e-s<0?(i=t.clone().add(r-1,"months"),n=(e-s)/(s-i)):(i=t.clone().add(r+1,"months"),n=(e-s)/(i-s)),-(r+n)||0}function ni(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function ri(){var t=this.clone().utc();return 0<t.year()&&t.year()<=9999?T(Date.prototype.toISOString)?this.toDate().toISOString():q(t,"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]"):q(t,"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]")}function si(t){t||(t=this.isUtc()?e.defaultFormatUtc:e.defaultFormat);var i=q(this,t);return this.localeData().postformat(i)}function oi(t,e){return this.isValid()&&(g(t)&&t.isValid()||ge(t).isValid())?Fe({to:this,from:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()}function ai(t){return this.from(ge(),t)}function hi(t,e){return this.isValid()&&(g(t)&&t.isValid()||ge(t).isValid())?Fe({from:this,to:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()}function li(t){return this.to(ge(),t)}function ui(t){var e;return void 0===t?this._locale._abbr:(e=ee(t),null!=e&&(this._locale=e),this)}function di(){return this._locale}function ci(t){switch(t=I(t)){case"year":this.month(0);case"quarter":case"month":this.date(1);case"week":case"isoWeek":case"day":case"date":this.hours(0);case"hour":this.minutes(0);case"minute":this.seconds(0);case"second":this.milliseconds(0)}return"week"===t&&this.weekday(0),"isoWeek"===t&&this.isoWeekday(1),"quarter"===t&&this.month(3*Math.floor(this.month()/3)),this}function fi(t){return t=I(t),void 0===t||"millisecond"===t?this:("date"===t&&(t="day"),this.startOf(t).add(1,"isoWeek"===t?"week":t).subtract(1,"ms"))}function pi(){return this._d.valueOf()-6e4*(this._offset||0)}function mi(){return Math.floor(this.valueOf()/1e3)}function vi(){return new Date(this.valueOf())}function yi(){var t=this;return[t.year(),t.month(),t.date(),t.hour(),t.minute(),t.second(),t.millisecond()]}function gi(){var t=this;return{years:t.year(),months:t.month(),date:t.date(),hours:t.hours(),minutes:t.minutes(),seconds:t.seconds(),milliseconds:t.milliseconds()}}function _i(){return this.isValid()?this.toISOString():null}function xi(){return f(this)}function wi(){return l({},c(this))}function bi(){return c(this).overflow}function Mi(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}function Si(t,e){G(0,[t,t.length],0,e)}function Ti(t){return Oi.call(this,t,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)}function Di(t){return Oi.call(this,t,this.isoWeek(),this.isoWeekday(),1,4)}function ki(){return Mt(this.year(),1,4)}function Ci(){var t=this.localeData()._week;return Mt(this.year(),t.dow,t.doy)}function Oi(t,e,i,n,r){var s;return null==t?bt(this,n,r).year:(s=Mt(t,n,r),e>s&&(e=s),Pi.call(this,t,e,i,n,r))}function Pi(t,e,i,n,r){var s=wt(t,e,i,n,r),o=_t(s.year,0,s.dayOfYear);return this.year(o.getUTCFullYear()),this.month(o.getUTCMonth()),this.date(o.getUTCDate()),this}function Ei(t){return null==t?Math.ceil((this.month()+1)/3):this.month(3*(t-1)+this.month()%3)}function Li(t){var e=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==t?e:this.add(t-e,"d")}function Yi(t,e){e[rr]=x(1e3*("0."+t))}function Ri(){return this._isUTC?"UTC":""}function Ai(){return this._isUTC?"Coordinated Universal Time":""}function Ii(t){return ge(1e3*t)}function zi(){return ge.apply(null,arguments).parseZone()}function Wi(t){return t}function Ni(t,e,i,n){var r=ee(),s=u().set(n,e);return r[i](s,t)}function Fi(t,e,i){if("number"==typeof t&&(e=t,t=void 0),t=t||"",null!=e)return Ni(t,e,i,"month");var n,r=[];for(n=0;n<12;n++)r[n]=Ni(t,n,i,"month");return r}function Vi(t,e,i,n){"boolean"==typeof t?("number"==typeof e&&(i=e,e=void 0),e=e||""):(e=t,i=e,t=!1,"number"==typeof e&&(i=e,e=void 0),e=e||"");var r=ee(),s=t?r._week.dow:0;if(null!=i)return Ni(e,(i+s)%7,n,"day");var o,a=[];for(o=0;o<7;o++)a[o]=Ni(e,(o+s)%7,n,"day");return a}function ji(t,e){return Fi(t,e,"months")}function Bi(t,e){return Fi(t,e,"monthsShort")}function Ui(t,e,i){return Vi(t,e,i,"weekdays")}function Hi(t,e,i){return Vi(t,e,i,"weekdaysShort")}function Gi(t,e,i){return Vi(t,e,i,"weekdaysMin")}function Xi(){var t=this._data;return this._milliseconds=Zr(this._milliseconds),this._days=Zr(this._days),this._months=Zr(this._months),t.milliseconds=Zr(t.milliseconds),t.seconds=Zr(t.seconds),t.minutes=Zr(t.minutes),t.hours=Zr(t.hours),t.months=Zr(t.months),t.years=Zr(t.years),this}function Zi(t,e,i,n){var r=Fe(e,i);return t._milliseconds+=n*r._milliseconds,t._days+=n*r._days,t._months+=n*r._months,t._bubble()}function qi(t,e){return Zi(this,t,e,1)}function Qi(t,e){return Zi(this,t,e,-1)}function $i(t){return t<0?Math.floor(t):Math.ceil(t)}function Ji(){var t,e,i,n,r,s=this._milliseconds,o=this._days,a=this._months,h=this._data;return s>=0&&o>=0&&a>=0||s<=0&&o<=0&&a<=0||(s+=864e5*$i(tn(a)+o),o=0,a=0),h.milliseconds=s%1e3,t=_(s/1e3),h.seconds=t%60,e=_(t/60),h.minutes=e%60,i=_(e/60),h.hours=i%24,o+=_(i/24),r=_(Ki(o)),a+=r,o-=$i(tn(r)),n=_(a/12),a%=12,h.days=o,h.months=a,h.years=n,this}function Ki(t){return 4800*t/146097}function tn(t){return 146097*t/4800}function en(t){var e,i,n=this._milliseconds;if(t=I(t),"month"===t||"year"===t)return e=this._days+n/864e5,i=this._months+Ki(e),"month"===t?i:i/12;switch(e=this._days+Math.round(tn(this._months)),t){case"week":return e/7+n/6048e5;case"day":return e+n/864e5;case"hour":return 24*e+n/36e5;case"minute":return 1440*e+n/6e4;case"second":return 86400*e+n/1e3;case"millisecond":return Math.floor(864e5*e)+n;default:throw new Error("Unknown unit "+t)}}function nn(){return this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*x(this._months/12)}function rn(t){return function(){return this.as(t)}}function sn(t){return t=I(t),this[t+"s"]()}function on(t){return function(){return this._data[t]}}function an(){return _(this.days()/7)}function hn(t,e,i,n,r){return r.relativeTime(e||1,!!i,t,n)}function ln(t,e,i){var n=Fe(t).abs(),r=us(n.as("s")),s=us(n.as("m")),o=us(n.as("h")),a=us(n.as("d")),h=us(n.as("M")),l=us(n.as("y")),u=r<ds.s&&["s",r]||s<=1&&["m"]||s<ds.m&&["mm",s]||o<=1&&["h"]||o<ds.h&&["hh",o]||a<=1&&["d"]||a<ds.d&&["dd",a]||h<=1&&["M"]||h<ds.M&&["MM",h]||l<=1&&["y"]||["yy",l];return u[2]=e,u[3]=+t>0,u[4]=i,hn.apply(null,u)}function un(t){return void 0===t?us:"function"==typeof t&&(us=t,!0)}function dn(t,e){return void 0!==ds[t]&&(void 0===e?ds[t]:(ds[t]=e,!0))}function cn(t){var e=this.localeData(),i=ln(this,!t,e);return t&&(i=e.pastFuture(+this,i)),e.postformat(i)}function fn(){var t,e,i,n=cs(this._milliseconds)/1e3,r=cs(this._days),s=cs(this._months);t=_(n/60),e=_(t/60),n%=60,t%=60,i=_(s/12),s%=12;var o=i,a=s,h=r,l=e,u=t,d=n,c=this.asSeconds();return c?(c<0?"-":"")+"P"+(o?o+"Y":"")+(a?a+"M":"")+(h?h+"D":"")+(l||u||d?"T":"")+(l?l+"H":"")+(u?u+"M":"")+(d?d+"S":""):"P0D"}var pn,mn;mn=Array.prototype.some?Array.prototype.some:function(t){for(var e=Object(this),i=e.length>>>0,n=0;n<i;n++)if(n in e&&t.call(this,e[n],n,e))return!0;return!1};var vn=e.momentProperties=[],yn=!1,gn={};e.suppressDeprecationWarnings=!1,e.deprecationHandler=null;var _n;_n=Object.keys?Object.keys:function(t){var e,i=[];for(e in t)h(t,e)&&i.push(e);return i};var xn,wn={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},bn={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},Mn="Invalid date",Sn="%d",Tn=/\d{1,2}/,Dn={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},kn={},Cn={},On=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,Pn=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,En={},Ln={},Yn=/\d/,Rn=/\d\d/,An=/\d{3}/,In=/\d{4}/,zn=/[+-]?\d{6}/,Wn=/\d\d?/,Nn=/\d\d\d\d?/,Fn=/\d\d\d\d\d\d?/,Vn=/\d{1,3}/,jn=/\d{1,4}/,Bn=/[+-]?\d{1,6}/,Un=/\d+/,Hn=/[+-]?\d+/,Gn=/Z|[+-]\d\d:?\d\d/gi,Xn=/Z|[+-]\d\d(?::?\d\d)?/gi,Zn=/[+-]?\d+(\.\d{1,3})?/,qn=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+|[\u0600-\u06FF\/]+(\s*?[\u0600-\u06FF]+){1,2}/i,Qn={},$n={},Jn=0,Kn=1,tr=2,er=3,ir=4,nr=5,rr=6,sr=7,or=8;xn=Array.prototype.indexOf?Array.prototype.indexOf:function(t){var e;for(e=0;e<this.length;++e)if(this[e]===t)return e;return-1},G("M",["MM",2],"Mo",function(){return this.month()+1}),G("MMM",0,0,function(t){return this.localeData().monthsShort(this,t)}),G("MMMM",0,0,function(t){return this.localeData().months(this,t)}),A("month","M"),W("month",8),$("M",Wn),$("MM",Wn,Rn),$("MMM",function(t,e){return e.monthsShortRegex(t)}),$("MMMM",function(t,e){return e.monthsRegex(t)}),et(["M","MM"],function(t,e){e[Kn]=x(t)-1}),et(["MMM","MMMM"],function(t,e,i,n){var r=i._locale.monthsParse(t,n,i._strict);null!=r?e[Kn]=r:c(i).invalidMonth=t});var ar=/D[oD]?(\[[^\[\]]*\]|\s+)+MMMM?/,hr="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),lr="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),ur=qn,dr=qn;G("Y",0,0,function(){var t=this.year();return t<=9999?""+t:"+"+t}),G(0,["YY",2],0,function(){return this.year()%100}),G(0,["YYYY",4],0,"year"),G(0,["YYYYY",5],0,"year"),G(0,["YYYYYY",6,!0],0,"year"),A("year","y"),W("year",1),$("Y",Hn),$("YY",Wn,Rn),$("YYYY",jn,In),$("YYYYY",Bn,zn),$("YYYYYY",Bn,zn),et(["YYYYY","YYYYYY"],Jn),et("YYYY",function(t,i){i[Jn]=2===t.length?e.parseTwoDigitYear(t):x(t)}),et("YY",function(t,i){i[Jn]=e.parseTwoDigitYear(t)}),et("Y",function(t,e){e[Jn]=parseInt(t,10)}),e.parseTwoDigitYear=function(t){return x(t)+(x(t)>68?1900:2e3)};var cr=F("FullYear",!0);G("w",["ww",2],"wo","week"),G("W",["WW",2],"Wo","isoWeek"),A("week","w"),A("isoWeek","W"),W("week",5),W("isoWeek",5),$("w",Wn),$("ww",Wn,Rn),$("W",Wn),$("WW",Wn,Rn),it(["w","ww","W","WW"],function(t,e,i,n){e[n.substr(0,1)]=x(t)});var fr={dow:0,doy:6};G("d",0,"do","day"),G("dd",0,0,function(t){return this.localeData().weekdaysMin(this,t)}),G("ddd",0,0,function(t){return this.localeData().weekdaysShort(this,t)}),G("dddd",0,0,function(t){return this.localeData().weekdays(this,t)}),G("e",0,0,"weekday"),G("E",0,0,"isoWeekday"),A("day","d"),A("weekday","e"),A("isoWeekday","E"),W("day",11),W("weekday",11),W("isoWeekday",11),$("d",Wn),$("e",Wn),$("E",Wn),$("dd",function(t,e){return e.weekdaysMinRegex(t)}),$("ddd",function(t,e){return e.weekdaysShortRegex(t)}),$("dddd",function(t,e){return e.weekdaysRegex(t)}),it(["dd","ddd","dddd"],function(t,e,i,n){var r=i._locale.weekdaysParse(t,n,i._strict);null!=r?e.d=r:c(i).invalidWeekday=t}),it(["d","e","E"],function(t,e,i,n){e[n]=x(t)});var pr="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),mr="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),vr="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),yr=qn,gr=qn,_r=qn;G("H",["HH",2],0,"hour"),G("h",["hh",2],0,Bt),G("k",["kk",2],0,Ut),G("hmm",0,0,function(){return""+Bt.apply(this)+H(this.minutes(),2)}),G("hmmss",0,0,function(){return""+Bt.apply(this)+H(this.minutes(),2)+H(this.seconds(),2)}),G("Hmm",0,0,function(){return""+this.hours()+H(this.minutes(),2)}),G("Hmmss",0,0,function(){return""+this.hours()+H(this.minutes(),2)+H(this.seconds(),2)}),Ht("a",!0),Ht("A",!1),A("hour","h"),W("hour",13),$("a",Gt),$("A",Gt),$("H",Wn),$("h",Wn),$("HH",Wn,Rn),$("hh",Wn,Rn),$("hmm",Nn),$("hmmss",Fn),$("Hmm",Nn),$("Hmmss",Fn),et(["H","HH"],er),et(["a","A"],function(t,e,i){i._isPm=i._locale.isPM(t),i._meridiem=t}),et(["h","hh"],function(t,e,i){e[er]=x(t),c(i).bigHour=!0}),et("hmm",function(t,e,i){var n=t.length-2;e[er]=x(t.substr(0,n)),e[ir]=x(t.substr(n)),c(i).bigHour=!0}),et("hmmss",function(t,e,i){var n=t.length-4,r=t.length-2;e[er]=x(t.substr(0,n)),e[ir]=x(t.substr(n,2)),e[nr]=x(t.substr(r)),c(i).bigHour=!0}),et("Hmm",function(t,e,i){var n=t.length-2;e[er]=x(t.substr(0,n)),e[ir]=x(t.substr(n))}),et("Hmmss",function(t,e,i){var n=t.length-4,r=t.length-2;e[er]=x(t.substr(0,n)),e[ir]=x(t.substr(n,2)),e[nr]=x(t.substr(r))});var xr,wr=/[ap]\.?m?\.?/i,br=F("Hours",!0),Mr={calendar:wn,longDateFormat:bn,invalidDate:Mn,ordinal:Sn,ordinalParse:Tn,relativeTime:Dn,months:hr,monthsShort:lr,week:fr,weekdays:pr,weekdaysMin:vr,weekdaysShort:mr,meridiemParse:wr},Sr={},Tr=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/,Dr=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/,kr=/Z|[+-]\d\d(?::?\d\d)?/,Cr=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]],Or=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],Pr=/^\/?Date\((\-?\d+)/i;e.createFromInputFallback=M("value provided is not in a recognized ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non ISO date formats are discouraged and will be removed in an upcoming major release. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(t){t._d=new Date(t._i+(t._useUTC?" UTC":""))}),e.ISO_8601=function(){};var Er=M("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=ge.apply(null,arguments);return this.isValid()&&t.isValid()?t<this?this:t:p()}),Lr=M("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=ge.apply(null,arguments);return this.isValid()&&t.isValid()?t>this?this:t:p()}),Yr=function(){return Date.now?Date.now():+new Date};Te("Z",":"),Te("ZZ",""),$("Z",Xn),$("ZZ",Xn),et(["Z","ZZ"],function(t,e,i){i._useUTC=!0,i._tzm=De(Xn,t)});var Rr=/([\+\-]|\d\d)/gi;e.updateOffset=function(){};var Ar=/^(\-)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)(\.\d*)?)?$/,Ir=/^(-)?P(?:(-?[0-9,.]*)Y)?(?:(-?[0-9,.]*)M)?(?:(-?[0-9,.]*)W)?(?:(-?[0-9,.]*)D)?(?:T(?:(-?[0-9,.]*)H)?(?:(-?[0-9,.]*)M)?(?:(-?[0-9,.]*)S)?)?$/;Fe.fn=be.prototype;var zr=Ue(1,"add"),Wr=Ue(-1,"subtract");e.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",e.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var Nr=M("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(t){return void 0===t?this.localeData():this.locale(t)});G(0,["gg",2],0,function(){return this.weekYear()%100}),G(0,["GG",2],0,function(){return this.isoWeekYear()%100}),Si("gggg","weekYear"),Si("ggggg","weekYear"),Si("GGGG","isoWeekYear"),Si("GGGGG","isoWeekYear"),A("weekYear","gg"),A("isoWeekYear","GG"),W("weekYear",1),W("isoWeekYear",1),$("G",Hn),$("g",Hn),$("GG",Wn,Rn),$("gg",Wn,Rn),$("GGGG",jn,In),$("gggg",jn,In),$("GGGGG",Bn,zn),$("ggggg",Bn,zn),it(["gggg","ggggg","GGGG","GGGGG"],function(t,e,i,n){e[n.substr(0,2)]=x(t)}),it(["gg","GG"],function(t,i,n,r){i[r]=e.parseTwoDigitYear(t)}),G("Q",0,"Qo","quarter"),A("quarter","Q"),W("quarter",7),$("Q",Yn),et("Q",function(t,e){e[Kn]=3*(x(t)-1)}),G("D",["DD",2],"Do","date"),A("date","D"),W("date",9),$("D",Wn),$("DD",Wn,Rn),$("Do",function(t,e){return t?e._ordinalParse:e._ordinalParseLenient}),et(["D","DD"],tr),et("Do",function(t,e){e[tr]=x(t.match(Wn)[0],10)});var Fr=F("Date",!0);G("DDD",["DDDD",3],"DDDo","dayOfYear"),A("dayOfYear","DDD"),W("dayOfYear",4),$("DDD",Vn),$("DDDD",An),et(["DDD","DDDD"],function(t,e,i){i._dayOfYear=x(t)}),G("m",["mm",2],0,"minute"),A("minute","m"),W("minute",14),$("m",Wn),$("mm",Wn,Rn),et(["m","mm"],ir);var Vr=F("Minutes",!1);G("s",["ss",2],0,"second"),A("second","s"),W("second",15),$("s",Wn),$("ss",Wn,Rn),et(["s","ss"],nr);var jr=F("Seconds",!1);G("S",0,0,function(){return~~(this.millisecond()/100)}),G(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),G(0,["SSS",3],0,"millisecond"),G(0,["SSSS",4],0,function(){return 10*this.millisecond()}),G(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),G(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),G(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),G(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),G(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),A("millisecond","ms"),W("millisecond",16),$("S",Vn,Yn),$("SS",Vn,Rn),$("SSS",Vn,An);var Br;for(Br="SSSS";Br.length<=9;Br+="S")$(Br,Un);for(Br="S";Br.length<=9;Br+="S")et(Br,Yi);var Ur=F("Milliseconds",!1);G("z",0,0,"zoneAbbr"),G("zz",0,0,"zoneName");var Hr=y.prototype;Hr.add=zr,Hr.calendar=Xe,Hr.clone=Ze,Hr.diff=ei,Hr.endOf=fi,Hr.format=si,Hr.from=oi,Hr.fromNow=ai,Hr.to=hi,Hr.toNow=li,Hr.get=B,Hr.invalidAt=bi,Hr.isAfter=qe,Hr.isBefore=Qe,Hr.isBetween=$e,Hr.isSame=Je,Hr.isSameOrAfter=Ke,Hr.isSameOrBefore=ti,Hr.isValid=xi,Hr.lang=Nr,Hr.locale=ui,Hr.localeData=di,Hr.max=Lr,Hr.min=Er,Hr.parsingFlags=wi,Hr.set=U,Hr.startOf=ci,Hr.subtract=Wr,Hr.toArray=yi,Hr.toObject=gi,Hr.toDate=vi,Hr.toISOString=ri,Hr.toJSON=_i,Hr.toString=ni,Hr.unix=mi,Hr.valueOf=pi,Hr.creationData=Mi,Hr.year=cr,Hr.isLeapYear=yt,Hr.weekYear=Ti,Hr.isoWeekYear=Di,Hr.quarter=Hr.quarters=Ei,Hr.month=ut,Hr.daysInMonth=dt,Hr.week=Hr.weeks=kt,Hr.isoWeek=Hr.isoWeeks=Ct,Hr.weeksInYear=Ci,Hr.isoWeeksInYear=ki,Hr.date=Fr,Hr.day=Hr.days=It,Hr.weekday=zt,Hr.isoWeekday=Wt,Hr.dayOfYear=Li,Hr.hour=Hr.hours=br,Hr.minute=Hr.minutes=Vr,Hr.second=Hr.seconds=jr,Hr.millisecond=Hr.milliseconds=Ur,Hr.utcOffset=Oe,Hr.utc=Ee,Hr.local=Le,Hr.parseZone=Ye,Hr.hasAlignedHourOffset=Re,Hr.isDST=Ae,Hr.isLocal=ze,Hr.isUtcOffset=We,Hr.isUtc=Ne,Hr.isUTC=Ne,Hr.zoneAbbr=Ri,Hr.zoneName=Ai,Hr.dates=M("dates accessor is deprecated. Use date instead.",Fr),Hr.months=M("months accessor is deprecated. Use month instead",ut),Hr.years=M("years accessor is deprecated. Use year instead",cr),Hr.zone=M("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",Pe),Hr.isDSTShifted=M("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",Ie);var Gr=Hr,Xr=C.prototype;Xr.calendar=O,Xr.longDateFormat=P,Xr.invalidDate=E,Xr.ordinal=L,Xr.preparse=Wi,Xr.postformat=Wi,Xr.relativeTime=Y,Xr.pastFuture=R,Xr.set=D,Xr.months=st,Xr.monthsShort=ot,Xr.monthsParse=ht,Xr.monthsRegex=ft,Xr.monthsShortRegex=ct,Xr.week=St,Xr.firstDayOfYear=Dt,Xr.firstDayOfWeek=Tt,Xr.weekdays=Et,Xr.weekdaysMin=Yt,Xr.weekdaysShort=Lt,Xr.weekdaysParse=At,Xr.weekdaysRegex=Nt,Xr.weekdaysShortRegex=Ft,Xr.weekdaysMinRegex=Vt,Xr.isPM=Xt,Xr.meridiem=Zt,Jt("en",{ordinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(t){var e=t%10,i=1===x(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th";return t+i}}),e.lang=M("moment.lang is deprecated. Use moment.locale instead.",Jt),e.langData=M("moment.langData is deprecated. Use moment.localeData instead.",ee);var Zr=Math.abs,qr=rn("ms"),Qr=rn("s"),$r=rn("m"),Jr=rn("h"),Kr=rn("d"),ts=rn("w"),es=rn("M"),is=rn("y"),ns=on("milliseconds"),rs=on("seconds"),ss=on("minutes"),os=on("hours"),as=on("days"),hs=on("months"),ls=on("years"),us=Math.round,ds={s:45,m:45,h:22,d:26,M:11},cs=Math.abs,fs=be.prototype;fs.abs=Xi,fs.add=qi,fs.subtract=Qi,fs.as=en,fs.asMilliseconds=qr,fs.asSeconds=Qr,fs.asMinutes=$r,fs.asHours=Jr,fs.asDays=Kr,fs.asWeeks=ts,fs.asMonths=es,fs.asYears=is,fs.valueOf=nn,fs._bubble=Ji,fs.get=sn,fs.milliseconds=ns,fs.seconds=rs,fs.minutes=ss,fs.hours=os,fs.days=as,fs.weeks=an,fs.months=hs,fs.years=ls,fs.humanize=cn,fs.toISOString=fn,fs.toString=fn,fs.toJSON=fn,fs.locale=ui,fs.localeData=di,fs.toIsoString=M("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",fn),fs.lang=Nr,G("X",0,0,"unix"),G("x",0,0,"valueOf"),$("x",Hn),$("X",Zn),et("X",function(t,e,i){i._d=new Date(1e3*parseFloat(t,10))}),et("x",function(t,e,i){i._d=new Date(x(t))}),e.version="2.15.1",i(ge),e.fn=Gr,e.min=xe,e.max=we,e.now=Yr,e.utc=u,e.unix=Ii,e.months=ji,e.isDate=o,e.locale=Jt,e.invalid=p,e.duration=Fe,e.isMoment=g,e.weekdays=Ui,e.parseZone=zi,e.localeData=ee,e.isDuration=Me,e.monthsShort=Bi,e.weekdaysMin=Gi,e.defineLocale=Kt,e.updateLocale=te,e.locales=ie,e.weekdaysShort=Hi,e.normalizeUnits=I,e.relativeTimeRounding=un,e.relativeTimeThreshold=dn,e.calendarFormat=Ge,e.prototype=Gr;var ps=e;return ps})}).call(e,i(4)(t))},function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children=[],t.webpackPolyfill=1),t}},function(t,e){function i(t){throw new Error("Cannot find module '"+t+"'.")}i.keys=function(){return[]},i.resolve=i,t.exports=i,i.id=5},function(t,e){(function(e){function i(t,e,i){var n=e&&i||0,r=0;for(e=e||[],t.toLowerCase().replace(/[0-9a-f]{2}/g,function(t){r<16&&(e[n+r++]=d[t])});r<16;)e[n+r++]=0;return e}function n(t,e){var i=e||0,n=u;return n[t[i++]]+n[t[i++]]+n[t[i++]]+n[t[i++]]+"-"+n[t[i++]]+n[t[i++]]+"-"+n[t[i++]]+n[t[i++]]+"-"+n[t[i++]]+n[t[i++]]+"-"+n[t[i++]]+n[t[i++]]+n[t[i++]]+n[t[i++]]+n[t[i++]]+n[t[i++]]}function r(t,e,i){var r=e&&i||0,s=e||[];t=t||{};var o=void 0!==t.clockseq?t.clockseq:m,a=void 0!==t.msecs?t.msecs:(new Date).getTime(),h=void 0!==t.nsecs?t.nsecs:y+1,l=a-v+(h-y)/1e4;if(l<0&&void 0===t.clockseq&&(o=o+1&16383),(l<0||a>v)&&void 0===t.nsecs&&(h=0),h>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");v=a,y=h,m=o,a+=122192928e5;var u=(1e4*(268435455&a)+h)%4294967296;s[r++]=u>>>24&255,s[r++]=u>>>16&255,s[r++]=u>>>8&255,s[r++]=255&u;var d=a/4294967296*1e4&268435455;s[r++]=d>>>8&255,s[r++]=255&d,s[r++]=d>>>24&15|16,s[r++]=d>>>16&255,s[r++]=o>>>8|128,s[r++]=255&o;for(var c=t.node||p,f=0;f<6;f++)s[r+f]=c[f];return e?e:n(s)}function s(t,e,i){var r=e&&i||0;"string"==typeof t&&(e="binary"==t?new Array(16):null,t=null),t=t||{};var s=t.random||(t.rng||o)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,e)for(var a=0;a<16;a++)e[r+a]=s[a];return e||n(s)}var o,a="undefined"!=typeof window?window:"undefined"!=typeof e?e:null;if(a&&a.crypto&&crypto.getRandomValues){var h=new Uint8Array(16);o=function(){return crypto.getRandomValues(h),
h}}if(!o){var l=new Array(16);o=function(){for(var t,e=0;e<16;e++)0===(3&e)&&(t=4294967296*Math.random()),l[e]=t>>>((3&e)<<3)&255;return l}}for(var u=[],d={},c=0;c<256;c++)u[c]=(c+256).toString(16).substr(1),d[u[c]]=c;var f=o(),p=[1|f[0],f[1],f[2],f[3],f[4],f[5]],m=16383&(f[6]<<8|f[7]),v=0,y=0,g=s;g.v1=r,g.v4=s,g.parse=i,g.unparse=n,t.exports=g}).call(e,function(){return this}())},function(t,e){e.prepareElements=function(t){for(var e in t)t.hasOwnProperty(e)&&(t[e].redundant=t[e].used,t[e].used=[])},e.cleanupElements=function(t){for(var e in t)if(t.hasOwnProperty(e)&&t[e].redundant){for(var i=0;i<t[e].redundant.length;i++)t[e].redundant[i].parentNode.removeChild(t[e].redundant[i]);t[e].redundant=[]}},e.resetElements=function(t){e.prepareElements(t),e.cleanupElements(t),e.prepareElements(t)},e.getSVGElement=function(t,e,i){var n;return e.hasOwnProperty(t)?e[t].redundant.length>0?(n=e[t].redundant[0],e[t].redundant.shift()):(n=document.createElementNS("http://www.w3.org/2000/svg",t),i.appendChild(n)):(n=document.createElementNS("http://www.w3.org/2000/svg",t),e[t]={used:[],redundant:[]},i.appendChild(n)),e[t].used.push(n),n},e.getDOMElement=function(t,e,i,n){var r;return e.hasOwnProperty(t)?e[t].redundant.length>0?(r=e[t].redundant[0],e[t].redundant.shift()):(r=document.createElement(t),void 0!==n?i.insertBefore(r,n):i.appendChild(r)):(r=document.createElement(t),e[t]={used:[],redundant:[]},void 0!==n?i.insertBefore(r,n):i.appendChild(r)),e[t].used.push(r),r},e.drawPoint=function(t,i,n,r,s,o){var a;if("circle"==n.style?(a=e.getSVGElement("circle",r,s),a.setAttributeNS(null,"cx",t),a.setAttributeNS(null,"cy",i),a.setAttributeNS(null,"r",.5*n.size)):(a=e.getSVGElement("rect",r,s),a.setAttributeNS(null,"x",t-.5*n.size),a.setAttributeNS(null,"y",i-.5*n.size),a.setAttributeNS(null,"width",n.size),a.setAttributeNS(null,"height",n.size)),void 0!==n.styles&&a.setAttributeNS(null,"style",n.styles),a.setAttributeNS(null,"class",n.className+" vis-point"),o){var h=e.getSVGElement("text",r,s);o.xOffset&&(t+=o.xOffset),o.yOffset&&(i+=o.yOffset),o.content&&(h.textContent=o.content),o.className&&h.setAttributeNS(null,"class",o.className+" vis-label"),h.setAttributeNS(null,"x",t),h.setAttributeNS(null,"y",i)}return a},e.drawBar=function(t,i,n,r,s,o,a,h){if(0!=r){r<0&&(r*=-1,i-=r);var l=e.getSVGElement("rect",o,a);l.setAttributeNS(null,"x",t-.5*n),l.setAttributeNS(null,"y",i),l.setAttributeNS(null,"width",n),l.setAttributeNS(null,"height",r),l.setAttributeNS(null,"class",s),h&&l.setAttributeNS(null,"style",h)}}},function(t,e,i){function n(t,e){if(t&&!Array.isArray(t)&&(e=t,t=null),this._options=e||{},this._data={},this.length=0,this._fieldId=this._options.fieldId||"id",this._type={},this._options.type)for(var i=Object.keys(this._options.type),n=0,r=i.length;n<r;n++){var s=i[n],o=this._options.type[s];"Date"==o||"ISODate"==o||"ASPDate"==o?this._type[s]="Date":this._type[s]=o}if(this._options.convert)throw new Error('Option "convert" is deprecated. Use "type" instead.');this._subscribers={},t&&this.add(t),this.setOptions(e)}var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol?"symbol":typeof t},s=i(1),o=i(9);n.prototype.setOptions=function(t){t&&void 0!==t.queue&&(t.queue===!1?this._queue&&(this._queue.destroy(),delete this._queue):(this._queue||(this._queue=o.extend(this,{replace:["add","update","remove"]})),"object"===r(t.queue)&&this._queue.setOptions(t.queue)))},n.prototype.on=function(t,e){var i=this._subscribers[t];i||(i=[],this._subscribers[t]=i),i.push({callback:e})},n.prototype.subscribe=function(){throw new Error("DataSet.subscribe is deprecated. Use DataSet.on instead.")},n.prototype.off=function(t,e){var i=this._subscribers[t];i&&(this._subscribers[t]=i.filter(function(t){return t.callback!=e}))},n.prototype.unsubscribe=function(){throw new Error("DataSet.unsubscribe is deprecated. Use DataSet.off instead.")},n.prototype._trigger=function(t,e,i){if("*"==t)throw new Error("Cannot trigger event *");var n=[];t in this._subscribers&&(n=n.concat(this._subscribers[t])),"*"in this._subscribers&&(n=n.concat(this._subscribers["*"]));for(var r=0,s=n.length;r<s;r++){var o=n[r];o.callback&&o.callback(t,e,i||null)}},n.prototype.add=function(t,e){var i,n=[],r=this;if(Array.isArray(t))for(var s=0,o=t.length;s<o;s++)i=r._addItem(t[s]),n.push(i);else{if(!(t instanceof Object))throw new Error("Unknown dataType");i=r._addItem(t),n.push(i)}return n.length&&this._trigger("add",{items:n},e),n},n.prototype.update=function(t,e){var i=[],n=[],r=[],o=[],a=this,h=a._fieldId,l=function(t){var e=t[h];if(a._data[e]){var l=s.extend({},a._data[e]);e=a._updateItem(t),n.push(e),o.push(t),r.push(l)}else e=a._addItem(t),i.push(e)};if(Array.isArray(t))for(var u=0,d=t.length;u<d;u++)t[u]instanceof Object?l(t[u]):console.warn("Ignoring input item, which is not an object at index "+u);else{if(!(t instanceof Object))throw new Error("Unknown dataType");l(t)}if(i.length&&this._trigger("add",{items:i},e),n.length){var c={items:n,oldData:r,data:o};this._trigger("update",c,e)}return i.concat(n)},n.prototype.get=function(t){var e,i,n,r=this,o=s.getType(arguments[0]);"String"==o||"Number"==o?(e=arguments[0],n=arguments[1]):"Array"==o?(i=arguments[0],n=arguments[1]):n=arguments[0];var a;if(n&&n.returnType){var h=["Array","Object"];a=h.indexOf(n.returnType)==-1?"Array":n.returnType}else a="Array";var l,u,d,c,f,p=n&&n.type||this._options.type,m=n&&n.filter,v=[];if(void 0!=e)l=r._getItem(e,p),l&&m&&!m(l)&&(l=null);else if(void 0!=i)for(c=0,f=i.length;c<f;c++)l=r._getItem(i[c],p),m&&!m(l)||v.push(l);else for(u=Object.keys(this._data),c=0,f=u.length;c<f;c++)d=u[c],l=r._getItem(d,p),m&&!m(l)||v.push(l);if(n&&n.order&&void 0==e&&this._sort(v,n.order),n&&n.fields){var y=n.fields;if(void 0!=e)l=this._filterFields(l,y);else for(c=0,f=v.length;c<f;c++)v[c]=this._filterFields(v[c],y)}if("Object"==a){var g,_={};for(c=0,f=v.length;c<f;c++)g=v[c],_[g.id]=g;return _}return void 0!=e?l:v},n.prototype.getIds=function(t){var e,i,n,r,s,o=this._data,a=t&&t.filter,h=t&&t.order,l=t&&t.type||this._options.type,u=Object.keys(o),d=[];if(a)if(h){for(s=[],e=0,i=u.length;e<i;e++)n=u[e],r=this._getItem(n,l),a(r)&&s.push(r);for(this._sort(s,h),e=0,i=s.length;e<i;e++)d.push(s[e][this._fieldId])}else for(e=0,i=u.length;e<i;e++)n=u[e],r=this._getItem(n,l),a(r)&&d.push(r[this._fieldId]);else if(h){for(s=[],e=0,i=u.length;e<i;e++)n=u[e],s.push(o[n]);for(this._sort(s,h),e=0,i=s.length;e<i;e++)d.push(s[e][this._fieldId])}else for(e=0,i=u.length;e<i;e++)n=u[e],r=o[n],d.push(r[this._fieldId]);return d},n.prototype.getDataSet=function(){return this},n.prototype.forEach=function(t,e){var i,n,r,s,o=e&&e.filter,a=e&&e.type||this._options.type,h=this._data,l=Object.keys(h);if(e&&e.order){var u=this.get(e);for(i=0,n=u.length;i<n;i++)r=u[i],s=r[this._fieldId],t(r,s)}else for(i=0,n=l.length;i<n;i++)s=l[i],r=this._getItem(s,a),o&&!o(r)||t(r,s)},n.prototype.map=function(t,e){var i,n,r,s,o=e&&e.filter,a=e&&e.type||this._options.type,h=[],l=this._data,u=Object.keys(l);for(i=0,n=u.length;i<n;i++)r=u[i],s=this._getItem(r,a),o&&!o(s)||h.push(t(s,r));return e&&e.order&&this._sort(h,e.order),h},n.prototype._filterFields=function(t,e){if(!t)return t;var i,n,r={},s=Object.keys(t),o=s.length;if(Array.isArray(e))for(i=0;i<o;i++)n=s[i],e.indexOf(n)!=-1&&(r[n]=t[n]);else for(i=0;i<o;i++)n=s[i],e.hasOwnProperty(n)&&(r[e[n]]=t[n]);return r},n.prototype._sort=function(t,e){if(s.isString(e)){var i=e;t.sort(function(t,e){var n=t[i],r=e[i];return n>r?1:n<r?-1:0})}else{if("function"!=typeof e)throw new TypeError("Order must be a function or a string");t.sort(e)}},n.prototype.remove=function(t,e){var i,n,r,s=[];if(Array.isArray(t))for(i=0,n=t.length;i<n;i++)r=this._remove(t[i]),null!=r&&s.push(r);else r=this._remove(t),null!=r&&s.push(r);return s.length&&this._trigger("remove",{items:s},e),s},n.prototype._remove=function(t){if(s.isNumber(t)||s.isString(t)){if(this._data[t])return delete this._data[t],this.length--,t}else if(t instanceof Object){var e=t[this._fieldId];if(void 0!==e&&this._data[e])return delete this._data[e],this.length--,e}return null},n.prototype.clear=function(t){var e=Object.keys(this._data);return this._data={},this.length=0,this._trigger("remove",{items:e},t),e},n.prototype.max=function(t){var e,i,n=this._data,r=Object.keys(n),s=null,o=null;for(e=0,i=r.length;e<i;e++){var a=r[e],h=n[a],l=h[t];null!=l&&(!s||l>o)&&(s=h,o=l)}return s},n.prototype.min=function(t){var e,i,n=this._data,r=Object.keys(n),s=null,o=null;for(e=0,i=r.length;e<i;e++){var a=r[e],h=n[a],l=h[t];null!=l&&(!s||l<o)&&(s=h,o=l)}return s},n.prototype.distinct=function(t){var e,i,n,r=this._data,o=Object.keys(r),a=[],h=this._options.type&&this._options.type[t]||null,l=0;for(e=0,n=o.length;e<n;e++){var u=o[e],d=r[u],c=d[t],f=!1;for(i=0;i<l;i++)if(a[i]==c){f=!0;break}f||void 0===c||(a[l]=c,l++)}if(h)for(e=0,n=a.length;e<n;e++)a[e]=s.convert(a[e],h);return a},n.prototype._addItem=function(t){var e=t[this._fieldId];if(void 0!=e){if(this._data[e])throw new Error("Cannot add item: item with id "+e+" already exists")}else e=s.randomUUID(),t[this._fieldId]=e;var i,n,r={},o=Object.keys(t);for(i=0,n=o.length;i<n;i++){var a=o[i],h=this._type[a];r[a]=s.convert(t[a],h)}return this._data[e]=r,this.length++,e},n.prototype._getItem=function(t,e){var i,n,r,o,a=this._data[t];if(!a)return null;var h={},l=Object.keys(a);if(e)for(r=0,o=l.length;r<o;r++)i=l[r],n=a[i],h[i]=s.convert(n,e[i]);else for(r=0,o=l.length;r<o;r++)i=l[r],n=a[i],h[i]=n;return h},n.prototype._updateItem=function(t){var e=t[this._fieldId];if(void 0==e)throw new Error("Cannot update item: item has no id (item: "+JSON.stringify(t)+")");var i=this._data[e];if(!i)throw new Error("Cannot update item: no item with id "+e+" found");for(var n=Object.keys(t),r=0,o=n.length;r<o;r++){var a=n[r],h=this._type[a];i[a]=s.convert(t[a],h)}return e},t.exports=n},function(t,e){function i(t){this.delay=null,this.max=1/0,this._queue=[],this._timeout=null,this._extended=null,this.setOptions(t)}i.prototype.setOptions=function(t){t&&"undefined"!=typeof t.delay&&(this.delay=t.delay),t&&"undefined"!=typeof t.max&&(this.max=t.max),this._flushIfNeeded()},i.extend=function(t,e){var n=new i(e);if(void 0!==t.flush)throw new Error("Target object already has a property flush");t.flush=function(){n.flush()};var r=[{name:"flush",original:void 0}];if(e&&e.replace)for(var s=0;s<e.replace.length;s++){var o=e.replace[s];r.push({name:o,original:t[o]}),n.replace(t,o)}return n._extended={object:t,methods:r},n},i.prototype.destroy=function(){if(this.flush(),this._extended){for(var t=this._extended.object,e=this._extended.methods,i=0;i<e.length;i++){var n=e[i];n.original?t[n.name]=n.original:delete t[n.name]}this._extended=null}},i.prototype.replace=function(t,e){var i=this,n=t[e];if(!n)throw new Error("Method "+e+" undefined");t[e]=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];i.queue({args:t,fn:n,context:this})}},i.prototype.queue=function(t){"function"==typeof t?this._queue.push({fn:t}):this._queue.push(t),this._flushIfNeeded()},i.prototype._flushIfNeeded=function(){if(this._queue.length>this.max&&this.flush(),clearTimeout(this._timeout),this.queue.length>0&&"number"==typeof this.delay){var t=this;this._timeout=setTimeout(function(){t.flush()},this.delay)}},i.prototype.flush=function(){for(;this._queue.length>0;){var t=this._queue.shift();t.fn.apply(t.context||t.fn,t.args||[])}},t.exports=i},function(t,e,i){function n(t,e){this._data=null,this._ids={},this.length=0,this._options=e||{},this._fieldId="id",this._subscribers={};var i=this;this.listener=function(){i._onEvent.apply(i,arguments)},this.setData(t)}var r=i(1),s=i(8);n.prototype.setData=function(t){var e,i,n,r;if(this._data&&(this._data.off&&this._data.off("*",this.listener),e=Object.keys(this._ids),this._ids={},this.length=0,this._trigger("remove",{items:e})),this._data=t,this._data){for(this._fieldId=this._options.fieldId||this._data&&this._data.options&&this._data.options.fieldId||"id",e=this._data.getIds({filter:this._options&&this._options.filter}),n=0,r=e.length;n<r;n++)i=e[n],this._ids[i]=!0;this.length=e.length,this._trigger("add",{items:e}),this._data.on&&this._data.on("*",this.listener)}},n.prototype.refresh=function(){var t,e,i,n=this._data.getIds({filter:this._options&&this._options.filter}),r=Object.keys(this._ids),s={},o=[],a=[];for(e=0,i=n.length;e<i;e++)t=n[e],s[t]=!0,this._ids[t]||(o.push(t),this._ids[t]=!0);for(e=0,i=r.length;e<i;e++)t=r[e],s[t]||(a.push(t),delete this._ids[t]);this.length+=o.length-a.length,o.length&&this._trigger("add",{items:o}),a.length&&this._trigger("remove",{items:a})},n.prototype.get=function(t){var e,i,n,s=this,o=r.getType(arguments[0]);"String"==o||"Number"==o||"Array"==o?(e=arguments[0],i=arguments[1],n=arguments[2]):(i=arguments[0],n=arguments[1]);var a=r.extend({},this._options,i);this._options.filter&&i&&i.filter&&(a.filter=function(t){return s._options.filter(t)&&i.filter(t)});var h=[];return void 0!=e&&h.push(e),h.push(a),h.push(n),this._data&&this._data.get.apply(this._data,h)},n.prototype.getIds=function(t){var e;if(this._data){var i,n=this._options.filter;i=t&&t.filter?n?function(e){return n(e)&&t.filter(e)}:t.filter:n,e=this._data.getIds({filter:i,order:t&&t.order})}else e=[];return e},n.prototype.map=function(t,e){var i=[];if(this._data){var n,r=this._options.filter;n=e&&e.filter?r?function(t){return r(t)&&e.filter(t)}:e.filter:r,i=this._data.map(t,{filter:n,order:e&&e.order})}else i=[];return i},n.prototype.getDataSet=function(){for(var t=this;t instanceof n;)t=t._data;return t||null},n.prototype._onEvent=function(t,e,i){var n,r,s,o,a=e&&e.items,h=this._data,l=[],u=[],d=[],c=[];if(a&&h){switch(t){case"add":for(n=0,r=a.length;n<r;n++)s=a[n],o=this.get(s),o&&(this._ids[s]=!0,u.push(s));break;case"update":for(n=0,r=a.length;n<r;n++)s=a[n],o=this.get(s),o?this._ids[s]?(d.push(s),l.push(e.data[n])):(this._ids[s]=!0,u.push(s)):this._ids[s]&&(delete this._ids[s],c.push(s));break;case"remove":for(n=0,r=a.length;n<r;n++)s=a[n],this._ids[s]&&(delete this._ids[s],c.push(s))}this.length+=u.length-c.length,u.length&&this._trigger("add",{items:u},i),d.length&&this._trigger("update",{items:d,data:l},i),c.length&&this._trigger("remove",{items:c},i)}},n.prototype.on=s.prototype.on,n.prototype.off=s.prototype.off,n.prototype._trigger=s.prototype._trigger,n.prototype.subscribe=n.prototype.on,n.prototype.unsubscribe=n.prototype.off,t.exports=n},function(t,e,i){function n(t,e,i){if(!(this instanceof n))throw new SyntaxError("Constructor must be called with the new operator");this.containerElement=t,this.width="400px",this.height="400px",this.margin=10,this.defaultXCenter="55%",this.defaultYCenter="50%",this.xLabel="x",this.yLabel="y",this.zLabel="z";var r=function(t){return t};this.xValueLabel=r,this.yValueLabel=r,this.zValueLabel=r,this.filterLabel="time",this.legendLabel="value",this.style=n.STYLE.DOT,this.showPerspective=!0,this.showGrid=!0,this.keepAspectRatio=!0,this.showShadow=!1,this.showGrayBottom=!1,this.showTooltip=!1,this.verticalRatio=.5,this.animationInterval=1e3,this.animationPreload=!1,this.camera=new f,this.camera.setArmRotation(1,.5),this.camera.setArmLength(1.7),this.eye=new d(0,0,(-1)),this.dataTable=null,this.dataPoints=null,this.colX=void 0,this.colY=void 0,this.colZ=void 0,this.colValue=void 0,this.colFilter=void 0,this.xMin=0,this.xStep=void 0,this.xMax=1,this.yMin=0,this.yStep=void 0,this.yMax=1,this.zMin=0,this.zStep=void 0,this.zMax=1,this.valueMin=0,this.valueMax=1,this.xBarWidth=1,this.yBarWidth=1,this.axisColor="#4D4D4D",this.gridColor="#D3D3D3",this.dataColor={fill:"#7DC1FF",stroke:"#3267D2",strokeWidth:1},this.dotSizeRatio=.02,this.create(),this.setOptions(i),e&&this.setData(e)}function r(t){return"clientX"in t?t.clientX:t.targetTouches[0]&&t.targetTouches[0].clientX||0}function s(t){return"clientY"in t?t.clientY:t.targetTouches[0]&&t.targetTouches[0].clientY||0}var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol?"symbol":typeof t},a=i(12),h=i(8),l=i(10),u=i(1),d=i(13),c=i(14),f=i(15),p=i(16),m=i(17),v=i(18);a(n.prototype),n.prototype._setScale=function(){this.scale=new d(1/(this.xMax-this.xMin),1/(this.yMax-this.yMin),1/(this.zMax-this.zMin)),this.keepAspectRatio&&(this.scale.x<this.scale.y?this.scale.y=this.scale.x:this.scale.x=this.scale.y),this.scale.z*=this.verticalRatio,this.scale.value=1/(this.valueMax-this.valueMin);var t=(this.xMax+this.xMin)/2*this.scale.x,e=(this.yMax+this.yMin)/2*this.scale.y,i=(this.zMax+this.zMin)/2*this.scale.z;this.camera.setArmLocation(t,e,i)},n.prototype._convert3Dto2D=function(t){var e=this._convertPointToTranslation(t);return this._convertTranslationToScreen(e)},n.prototype._convertPointToTranslation=function(t){var e=t.x*this.scale.x,i=t.y*this.scale.y,n=t.z*this.scale.z,r=this.camera.getCameraLocation().x,s=this.camera.getCameraLocation().y,o=this.camera.getCameraLocation().z,a=Math.sin(this.camera.getCameraRotation().x),h=Math.cos(this.camera.getCameraRotation().x),l=Math.sin(this.camera.getCameraRotation().y),u=Math.cos(this.camera.getCameraRotation().y),c=Math.sin(this.camera.getCameraRotation().z),f=Math.cos(this.camera.getCameraRotation().z),p=u*(c*(i-s)+f*(e-r))-l*(n-o),m=a*(u*(n-o)+l*(c*(i-s)+f*(e-r)))+h*(f*(i-s)-c*(e-r)),v=h*(u*(n-o)+l*(c*(i-s)+f*(e-r)))-a*(f*(i-s)-c*(e-r));return new d(p,m,v)},n.prototype._convertTranslationToScreen=function(t){var e,i,n=this.eye.x,r=this.eye.y,s=this.eye.z,o=t.x,a=t.y,h=t.z;return this.showPerspective?(e=(o-n)*(s/h),i=(a-r)*(s/h)):(e=o*-(s/this.camera.getArmLength()),i=a*-(s/this.camera.getArmLength())),new c(this.xcenter+e*this.frame.canvas.clientWidth,this.ycenter-i*this.frame.canvas.clientWidth)},n.prototype._setBackgroundColor=function(t){var e="white",i="gray",n=1;if("string"==typeof t)e=t,i="none",n=0;else if("object"===("undefined"==typeof t?"undefined":o(t)))void 0!==t.fill&&(e=t.fill),void 0!==t.stroke&&(i=t.stroke),void 0!==t.strokeWidth&&(n=t.strokeWidth);else if(void 0!==t)throw"Unsupported type of backgroundColor";this.frame.style.backgroundColor=e,this.frame.style.borderColor=i,this.frame.style.borderWidth=n+"px",this.frame.style.borderStyle="solid"},n.STYLE={BAR:0,BARCOLOR:1,BARSIZE:2,DOT:3,DOTLINE:4,DOTCOLOR:5,DOTSIZE:6,GRID:7,LINE:8,SURFACE:9},n.prototype._getStyleNumber=function(t){switch(t){case"dot":return n.STYLE.DOT;case"dot-line":return n.STYLE.DOTLINE;case"dot-color":return n.STYLE.DOTCOLOR;case"dot-size":return n.STYLE.DOTSIZE;case"line":return n.STYLE.LINE;case"grid":return n.STYLE.GRID;case"surface":return n.STYLE.SURFACE;case"bar":return n.STYLE.BAR;case"bar-color":return n.STYLE.BARCOLOR;case"bar-size":return n.STYLE.BARSIZE}return-1},n.prototype._determineColumnIndexes=function(t,e){if(this.style===n.STYLE.DOT||this.style===n.STYLE.DOTLINE||this.style===n.STYLE.LINE||this.style===n.STYLE.GRID||this.style===n.STYLE.SURFACE||this.style===n.STYLE.BAR)this.colX=0,this.colY=1,this.colZ=2,this.colValue=void 0,t.getNumberOfColumns()>3&&(this.colFilter=3);else{if(this.style!==n.STYLE.DOTCOLOR&&this.style!==n.STYLE.DOTSIZE&&this.style!==n.STYLE.BARCOLOR&&this.style!==n.STYLE.BARSIZE)throw'Unknown style "'+this.style+'"';this.colX=0,this.colY=1,this.colZ=2,this.colValue=3,t.getNumberOfColumns()>4&&(this.colFilter=4)}},n.prototype.getNumberOfRows=function(t){return t.length},n.prototype.getNumberOfColumns=function(t){var e=0;for(var i in t[0])t[0].hasOwnProperty(i)&&e++;return e},n.prototype.getDistinctValues=function(t,e){for(var i=[],n=0;n<t.length;n++)i.indexOf(t[n][e])==-1&&i.push(t[n][e]);return i},n.prototype.getColumnRange=function(t,e){for(var i={min:t[0][e],max:t[0][e]},n=0;n<t.length;n++)i.min>t[n][e]&&(i.min=t[n][e]),i.max<t[n][e]&&(i.max=t[n][e]);return i},n.prototype._dataInitialize=function(t,e){var i=this;if(this.dataSet&&this.dataSet.off("*",this._onChange),void 0!==t){Array.isArray(t)&&(t=new h(t));var r;if(!(t instanceof h||t instanceof l))throw new Error("Array, DataSet, or DataView expected");if(r=t.get(),0!=r.length){this.dataSet=t,this.dataTable=r,this._onChange=function(){i.setData(i.dataSet)},this.dataSet.on("*",this._onChange),this.colX="x",this.colY="y",this.colZ="z",this.colValue="style",this.colFilter="filter",r[0].hasOwnProperty("filter")&&void 0===this.dataFilter&&(this.dataFilter=new p(t,this.colFilter,this),this.dataFilter.setOnLoadCallback(function(){i.redraw()}));var s=this.style==n.STYLE.BAR||this.style==n.STYLE.BARCOLOR||this.style==n.STYLE.BARSIZE;if(s){if(void 0!==this.defaultXBarWidth)this.xBarWidth=this.defaultXBarWidth;else{var o=this.getDistinctValues(r,this.colX);this.xBarWidth=o[1]-o[0]||1}if(void 0!==this.defaultYBarWidth)this.yBarWidth=this.defaultYBarWidth;else{var a=this.getDistinctValues(r,this.colY);this.yBarWidth=a[1]-a[0]||1}}var u=this.getColumnRange(r,this.colX);s&&(u.min-=this.xBarWidth/2,u.max+=this.xBarWidth/2),this.xMin=void 0!==this.defaultXMin?this.defaultXMin:u.min,this.xMax=void 0!==this.defaultXMax?this.defaultXMax:u.max,this.xMax<=this.xMin&&(this.xMax=this.xMin+1),this.xStep=void 0!==this.defaultXStep?this.defaultXStep:(this.xMax-this.xMin)/5;var d=this.getColumnRange(r,this.colY);s&&(d.min-=this.yBarWidth/2,d.max+=this.yBarWidth/2),this.yMin=void 0!==this.defaultYMin?this.defaultYMin:d.min,this.yMax=void 0!==this.defaultYMax?this.defaultYMax:d.max,this.yMax<=this.yMin&&(this.yMax=this.yMin+1),this.yStep=void 0!==this.defaultYStep?this.defaultYStep:(this.yMax-this.yMin)/5;var c=this.getColumnRange(r,this.colZ);if(this.zMin=void 0!==this.defaultZMin?this.defaultZMin:c.min,this.zMax=void 0!==this.defaultZMax?this.defaultZMax:c.max,this.zMax<=this.zMin&&(this.zMax=this.zMin+1),this.zStep=void 0!==this.defaultZStep?this.defaultZStep:(this.zMax-this.zMin)/5,void 0!==this.colValue){var f=this.getColumnRange(r,this.colValue);this.valueMin=void 0!==this.defaultValueMin?this.defaultValueMin:f.min,this.valueMax=void 0!==this.defaultValueMax?this.defaultValueMax:f.max,this.valueMax<=this.valueMin&&(this.valueMax=this.valueMin+1)}this._setScale()}}},n.prototype._getDataPoints=function(t){var e,i,r,s,o,a,h=[];if(this.style===n.STYLE.GRID||this.style===n.STYLE.SURFACE){var l=[],u=[];for(r=0;r<this.getNumberOfRows(t);r++)e=t[r][this.colX]||0,i=t[r][this.colY]||0,l.indexOf(e)===-1&&l.push(e),u.indexOf(i)===-1&&u.push(i);var c=function(t,e){return t-e};l.sort(c),u.sort(c);var f=[];for(r=0;r<t.length;r++){e=t[r][this.colX]||0,i=t[r][this.colY]||0,s=t[r][this.colZ]||0;var p=l.indexOf(e),m=u.indexOf(i);void 0===f[p]&&(f[p]=[]);var v=new d;v.x=e,v.y=i,v.z=s,o={},o.point=v,o.trans=void 0,o.screen=void 0,o.bottom=new d(e,i,this.zMin),f[p][m]=o,h.push(o)}for(e=0;e<f.length;e++)for(i=0;i<f[e].length;i++)f[e][i]&&(f[e][i].pointRight=e<f.length-1?f[e+1][i]:void 0,f[e][i].pointTop=i<f[e].length-1?f[e][i+1]:void 0,f[e][i].pointCross=e<f.length-1&&i<f[e].length-1?f[e+1][i+1]:void 0)}else for(r=0;r<t.length;r++)a=new d,a.x=t[r][this.colX]||0,a.y=t[r][this.colY]||0,a.z=t[r][this.colZ]||0,void 0!==this.colValue&&(a.value=t[r][this.colValue]||0),o={},o.point=a,o.bottom=new d(a.x,a.y,this.zMin),o.trans=void 0,o.screen=void 0,h.push(o);return h},n.prototype.create=function(){for(;this.containerElement.hasChildNodes();)this.containerElement.removeChild(this.containerElement.firstChild);this.frame=document.createElement("div"),this.frame.style.position="relative",this.frame.style.overflow="hidden",this.frame.canvas=document.createElement("canvas"),this.frame.canvas.style.position="relative",this.frame.appendChild(this.frame.canvas);var t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerHTML="Error: your browser does not support HTML canvas",this.frame.canvas.appendChild(t),this.frame.filter=document.createElement("div"),this.frame.filter.style.position="absolute",this.frame.filter.style.bottom="0px",this.frame.filter.style.left="0px",this.frame.filter.style.width="100%",this.frame.appendChild(this.frame.filter);var e=this,i=function(t){e._onMouseDown(t)},n=function(t){e._onTouchStart(t)},r=function(t){e._onWheel(t)},s=function(t){e._onTooltip(t)};u.addEventListener(this.frame.canvas,"keydown",onkeydown),u.addEventListener(this.frame.canvas,"mousedown",i),u.addEventListener(this.frame.canvas,"touchstart",n),u.addEventListener(this.frame.canvas,"mousewheel",r),u.addEventListener(this.frame.canvas,"mousemove",s),this.containerElement.appendChild(this.frame)},n.prototype.setSize=function(t,e){this.frame.style.width=t,this.frame.style.height=e,this._resizeCanvas()},n.prototype._resizeCanvas=function(){this.frame.canvas.style.width="100%",this.frame.canvas.style.height="100%",this.frame.canvas.width=this.frame.canvas.clientWidth,this.frame.canvas.height=this.frame.canvas.clientHeight,this.frame.filter.style.width=this.frame.canvas.clientWidth-20+"px"},n.prototype.animationStart=function(){if(!this.frame.filter||!this.frame.filter.slider)throw"No animation available";this.frame.filter.slider.play()},n.prototype.animationStop=function(){this.frame.filter&&this.frame.filter.slider&&this.frame.filter.slider.stop()},n.prototype._resizeCenter=function(){"%"===this.defaultXCenter.charAt(this.defaultXCenter.length-1)?this.xcenter=parseFloat(this.defaultXCenter)/100*this.frame.canvas.clientWidth:this.xcenter=parseFloat(this.defaultXCenter),"%"===this.defaultYCenter.charAt(this.defaultYCenter.length-1)?this.ycenter=parseFloat(this.defaultYCenter)/100*(this.frame.canvas.clientHeight-this.frame.filter.clientHeight):this.ycenter=parseFloat(this.defaultYCenter)},n.prototype.setCameraPosition=function(t){void 0!==t&&(void 0!==t.horizontal&&void 0!==t.vertical&&this.camera.setArmRotation(t.horizontal,t.vertical),void 0!==t.distance&&this.camera.setArmLength(t.distance),this.redraw())},n.prototype.getCameraPosition=function(){var t=this.camera.getArmRotation();return t.distance=this.camera.getArmLength(),t},n.prototype._readData=function(t){this._dataInitialize(t,this.style),this.dataFilter?this.dataPoints=this.dataFilter._getDataPoints():this.dataPoints=this._getDataPoints(this.dataTable),this._redrawFilter()},n.prototype.setData=function(t){this._readData(t),this.redraw(),this.animationAutoStart&&this.dataFilter&&this.animationStart()},n.prototype.setOptions=function(t){var e=void 0;if(this.animationStop(),void 0!==t){if(void 0!==t.width&&(this.width=t.width),void 0!==t.height&&(this.height=t.height),void 0!==t.xCenter&&(this.defaultXCenter=t.xCenter),void 0!==t.yCenter&&(this.defaultYCenter=t.yCenter),void 0!==t.filterLabel&&(this.filterLabel=t.filterLabel),void 0!==t.legendLabel&&(this.legendLabel=t.legendLabel),void 0!==t.xLabel&&(this.xLabel=t.xLabel),void 0!==t.yLabel&&(this.yLabel=t.yLabel),void 0!==t.zLabel&&(this.zLabel=t.zLabel),void 0!==t.xValueLabel&&(this.xValueLabel=t.xValueLabel),void 0!==t.yValueLabel&&(this.yValueLabel=t.yValueLabel),void 0!==t.zValueLabel&&(this.zValueLabel=t.zValueLabel),void 0!==t.dotSizeRatio&&(this.dotSizeRatio=t.dotSizeRatio),void 0!==t.style){var i=this._getStyleNumber(t.style);i!==-1&&(this.style=i)}void 0!==t.showGrid&&(this.showGrid=t.showGrid),void 0!==t.showPerspective&&(this.showPerspective=t.showPerspective),void 0!==t.showShadow&&(this.showShadow=t.showShadow),void 0!==t.tooltip&&(this.showTooltip=t.tooltip),void 0!==t.showAnimationControls&&(this.showAnimationControls=t.showAnimationControls),void 0!==t.keepAspectRatio&&(this.keepAspectRatio=t.keepAspectRatio),void 0!==t.verticalRatio&&(this.verticalRatio=t.verticalRatio),void 0!==t.animationInterval&&(this.animationInterval=t.animationInterval),void 0!==t.animationPreload&&(this.animationPreload=t.animationPreload),void 0!==t.animationAutoStart&&(this.animationAutoStart=t.animationAutoStart),void 0!==t.xBarWidth&&(this.defaultXBarWidth=t.xBarWidth),void 0!==t.yBarWidth&&(this.defaultYBarWidth=t.yBarWidth),void 0!==t.xMin&&(this.defaultXMin=t.xMin),void 0!==t.xStep&&(this.defaultXStep=t.xStep),void 0!==t.xMax&&(this.defaultXMax=t.xMax),void 0!==t.yMin&&(this.defaultYMin=t.yMin),void 0!==t.yStep&&(this.defaultYStep=t.yStep),void 0!==t.yMax&&(this.defaultYMax=t.yMax),void 0!==t.zMin&&(this.defaultZMin=t.zMin),void 0!==t.zStep&&(this.defaultZStep=t.zStep),void 0!==t.zMax&&(this.defaultZMax=t.zMax),void 0!==t.valueMin&&(this.defaultValueMin=t.valueMin),void 0!==t.valueMax&&(this.defaultValueMax=t.valueMax),void 0!==t.backgroundColor&&this._setBackgroundColor(t.backgroundColor),void 0!==t.cameraPosition&&(e=t.cameraPosition),void 0!==e&&(this.camera.setArmRotation(e.horizontal,e.vertical),this.camera.setArmLength(e.distance)),void 0!==t.axisColor&&(this.axisColor=t.axisColor),void 0!==t.gridColor&&(this.gridColor=t.gridColor),t.dataColor&&("string"==typeof t.dataColor?(this.dataColor.fill=t.dataColor,this.dataColor.stroke=t.dataColor):(t.dataColor.fill&&(this.dataColor.fill=t.dataColor.fill),t.dataColor.stroke&&(this.dataColor.stroke=t.dataColor.stroke),void 0!==t.dataColor.strokeWidth&&(this.dataColor.strokeWidth=t.dataColor.strokeWidth)))}this.setSize(this.width,this.height),this.dataTable&&this.setData(this.dataTable),this.animationAutoStart&&this.dataFilter&&this.animationStart()},n.prototype.redraw=function(){if(void 0===this.dataPoints)throw"Error: graph data not initialized";this._resizeCanvas(),this._resizeCenter(),this._redrawSlider(),this._redrawClear(),this._redrawAxis(),this.style===n.STYLE.GRID||this.style===n.STYLE.SURFACE?this._redrawDataGrid():this.style===n.STYLE.LINE?this._redrawDataLine():this.style===n.STYLE.BAR||this.style===n.STYLE.BARCOLOR||this.style===n.STYLE.BARSIZE?this._redrawDataBar():this._redrawDataDot(),this._redrawInfo(),this._redrawLegend()},n.prototype._redrawClear=function(){var t=this.frame.canvas,e=t.getContext("2d");e.clearRect(0,0,t.width,t.height)},n.prototype._redrawLegend=function(){var t;if(this.style===n.STYLE.DOTCOLOR||this.style===n.STYLE.DOTSIZE){var e,i,r=this.frame.clientWidth*this.dotSizeRatio;this.style===n.STYLE.DOTSIZE?(e=r/2,i=r/2+2*r):(e=20,i=20);var s=Math.max(.25*this.frame.clientHeight,100),o=this.margin,a=this.frame.clientWidth-this.margin,h=a-i,l=o+s}var u=this.frame.canvas,d=u.getContext("2d");if(d.lineWidth=1,d.font="14px arial",this.style===n.STYLE.DOTCOLOR){var c=0,f=s;for(t=c;t<f;t++){var p=(t-c)/(f-c),m=240*p,y=this._hsv2rgb(m,1,1);d.strokeStyle=y,d.beginPath(),d.moveTo(h,o+t),d.lineTo(a,o+t),d.stroke()}d.strokeStyle=this.axisColor,d.strokeRect(h,o,i,s)}if(this.style===n.STYLE.DOTSIZE&&(d.strokeStyle=this.axisColor,d.fillStyle=this.dataColor.fill,d.beginPath(),d.moveTo(h,o),d.lineTo(a,o),d.lineTo(a-i+e,l),d.lineTo(h,l),d.closePath(),d.fill(),d.stroke()),this.style===n.STYLE.DOTCOLOR||this.style===n.STYLE.DOTSIZE){var g=5,_=new v(this.valueMin,this.valueMax,(this.valueMax-this.valueMin)/5,(!0));for(_.start(),_.getCurrent()<this.valueMin&&_.next();!_.end();)t=l-(_.getCurrent()-this.valueMin)/(this.valueMax-this.valueMin)*s,d.beginPath(),d.moveTo(h-g,t),d.lineTo(h,t),d.stroke(),d.textAlign="right",d.textBaseline="middle",d.fillStyle=this.axisColor,d.fillText(_.getCurrent(),h-2*g,t),_.next();d.textAlign="right",d.textBaseline="top";var x=this.legendLabel;d.fillText(x,a,l+this.margin)}},n.prototype._redrawFilter=function(){if(this.frame.filter.innerHTML="",this.dataFilter){var t={visible:this.showAnimationControls},e=new m(this.frame.filter,t);this.frame.filter.slider=e,this.frame.filter.style.padding="10px",e.setValues(this.dataFilter.values),e.setPlayInterval(this.animationInterval);var i=this,n=function(){var t=e.getIndex();i.dataFilter.selectValue(t),i.dataPoints=i.dataFilter._getDataPoints(),i.redraw()};e.setOnChangeCallback(n)}else this.frame.filter.slider=void 0},n.prototype._redrawSlider=function(){void 0!==this.frame.filter.slider&&this.frame.filter.slider.redraw()},n.prototype._redrawInfo=function(){if(this.dataFilter){var t=this.frame.canvas,e=t.getContext("2d");e.font="14px arial",e.lineStyle="gray",e.fillStyle="gray",e.textAlign="left",
e.textBaseline="top";var i=this.margin,n=this.margin;e.fillText(this.dataFilter.getLabel()+": "+this.dataFilter.getSelectedValue(),i,n)}},n.prototype._redrawAxis=function(){var t,e,i,n,r,s,o,a,h,l,u,c,f,p=this.frame.canvas,m=p.getContext("2d");m.font=24/this.camera.getArmLength()+"px arial";var y=.025/this.scale.x,g=.025/this.scale.y,_=5/this.camera.getArmLength(),x=this.camera.getArmRotation().horizontal;for(m.lineWidth=1,n=void 0===this.defaultXStep,i=new v(this.xMin,this.xMax,this.xStep,n),i.start(),i.getCurrent()<this.xMin&&i.next();!i.end();){var w=i.getCurrent();this.showGrid?(t=this._convert3Dto2D(new d(w,this.yMin,this.zMin)),e=this._convert3Dto2D(new d(w,this.yMax,this.zMin)),m.strokeStyle=this.gridColor,m.beginPath(),m.moveTo(t.x,t.y),m.lineTo(e.x,e.y),m.stroke()):(t=this._convert3Dto2D(new d(w,this.yMin,this.zMin)),e=this._convert3Dto2D(new d(w,this.yMin+y,this.zMin)),m.strokeStyle=this.axisColor,m.beginPath(),m.moveTo(t.x,t.y),m.lineTo(e.x,e.y),m.stroke(),t=this._convert3Dto2D(new d(w,this.yMax,this.zMin)),e=this._convert3Dto2D(new d(w,this.yMax-y,this.zMin)),m.strokeStyle=this.axisColor,m.beginPath(),m.moveTo(t.x,t.y),m.lineTo(e.x,e.y),m.stroke()),o=Math.cos(x)>0?this.yMin:this.yMax,r=this._convert3Dto2D(new d(w,o,this.zMin)),Math.cos(2*x)>0?(m.textAlign="center",m.textBaseline="top",r.y+=_):Math.sin(2*x)<0?(m.textAlign="right",m.textBaseline="middle"):(m.textAlign="left",m.textBaseline="middle"),m.fillStyle=this.axisColor,m.fillText("  "+this.xValueLabel(i.getCurrent())+"  ",r.x,r.y),i.next()}for(m.lineWidth=1,n=void 0===this.defaultYStep,i=new v(this.yMin,this.yMax,this.yStep,n),i.start(),i.getCurrent()<this.yMin&&i.next();!i.end();)this.showGrid?(t=this._convert3Dto2D(new d(this.xMin,i.getCurrent(),this.zMin)),e=this._convert3Dto2D(new d(this.xMax,i.getCurrent(),this.zMin)),m.strokeStyle=this.gridColor,m.beginPath(),m.moveTo(t.x,t.y),m.lineTo(e.x,e.y),m.stroke()):(t=this._convert3Dto2D(new d(this.xMin,i.getCurrent(),this.zMin)),e=this._convert3Dto2D(new d(this.xMin+g,i.getCurrent(),this.zMin)),m.strokeStyle=this.axisColor,m.beginPath(),m.moveTo(t.x,t.y),m.lineTo(e.x,e.y),m.stroke(),t=this._convert3Dto2D(new d(this.xMax,i.getCurrent(),this.zMin)),e=this._convert3Dto2D(new d(this.xMax-g,i.getCurrent(),this.zMin)),m.strokeStyle=this.axisColor,m.beginPath(),m.moveTo(t.x,t.y),m.lineTo(e.x,e.y),m.stroke()),s=Math.sin(x)>0?this.xMin:this.xMax,r=this._convert3Dto2D(new d(s,i.getCurrent(),this.zMin)),Math.cos(2*x)<0?(m.textAlign="center",m.textBaseline="top",r.y+=_):Math.sin(2*x)>0?(m.textAlign="right",m.textBaseline="middle"):(m.textAlign="left",m.textBaseline="middle"),m.fillStyle=this.axisColor,m.fillText("  "+this.yValueLabel(i.getCurrent())+"  ",r.x,r.y),i.next();for(m.lineWidth=1,n=void 0===this.defaultZStep,i=new v(this.zMin,this.zMax,this.zStep,n),i.start(),i.getCurrent()<this.zMin&&i.next(),s=Math.cos(x)>0?this.xMin:this.xMax,o=Math.sin(x)<0?this.yMin:this.yMax;!i.end();)t=this._convert3Dto2D(new d(s,o,i.getCurrent())),m.strokeStyle=this.axisColor,m.beginPath(),m.moveTo(t.x,t.y),m.lineTo(t.x-_,t.y),m.stroke(),m.textAlign="right",m.textBaseline="middle",m.fillStyle=this.axisColor,m.fillText(this.zValueLabel(i.getCurrent())+" ",t.x-5,t.y),i.next();m.lineWidth=1,t=this._convert3Dto2D(new d(s,o,this.zMin)),e=this._convert3Dto2D(new d(s,o,this.zMax)),m.strokeStyle=this.axisColor,m.beginPath(),m.moveTo(t.x,t.y),m.lineTo(e.x,e.y),m.stroke(),m.lineWidth=1,c=this._convert3Dto2D(new d(this.xMin,this.yMin,this.zMin)),f=this._convert3Dto2D(new d(this.xMax,this.yMin,this.zMin)),m.strokeStyle=this.axisColor,m.beginPath(),m.moveTo(c.x,c.y),m.lineTo(f.x,f.y),m.stroke(),c=this._convert3Dto2D(new d(this.xMin,this.yMax,this.zMin)),f=this._convert3Dto2D(new d(this.xMax,this.yMax,this.zMin)),m.strokeStyle=this.axisColor,m.beginPath(),m.moveTo(c.x,c.y),m.lineTo(f.x,f.y),m.stroke(),m.lineWidth=1,t=this._convert3Dto2D(new d(this.xMin,this.yMin,this.zMin)),e=this._convert3Dto2D(new d(this.xMin,this.yMax,this.zMin)),m.strokeStyle=this.axisColor,m.beginPath(),m.moveTo(t.x,t.y),m.lineTo(e.x,e.y),m.stroke(),t=this._convert3Dto2D(new d(this.xMax,this.yMin,this.zMin)),e=this._convert3Dto2D(new d(this.xMax,this.yMax,this.zMin)),m.strokeStyle=this.axisColor,m.beginPath(),m.moveTo(t.x,t.y),m.lineTo(e.x,e.y),m.stroke();var b=this.xLabel;b.length>0&&(u=.1/this.scale.y,s=(this.xMin+this.xMax)/2,o=Math.cos(x)>0?this.yMin-u:this.yMax+u,r=this._convert3Dto2D(new d(s,o,this.zMin)),Math.cos(2*x)>0?(m.textAlign="center",m.textBaseline="top"):Math.sin(2*x)<0?(m.textAlign="right",m.textBaseline="middle"):(m.textAlign="left",m.textBaseline="middle"),m.fillStyle=this.axisColor,m.fillText(b,r.x,r.y));var M=this.yLabel;M.length>0&&(l=.1/this.scale.x,s=Math.sin(x)>0?this.xMin-l:this.xMax+l,o=(this.yMin+this.yMax)/2,r=this._convert3Dto2D(new d(s,o,this.zMin)),Math.cos(2*x)<0?(m.textAlign="center",m.textBaseline="top"):Math.sin(2*x)>0?(m.textAlign="right",m.textBaseline="middle"):(m.textAlign="left",m.textBaseline="middle"),m.fillStyle=this.axisColor,m.fillText(M,r.x,r.y));var S=this.zLabel;S.length>0&&(h=30,s=Math.cos(x)>0?this.xMin:this.xMax,o=Math.sin(x)<0?this.yMin:this.yMax,a=(this.zMin+this.zMax)/2,r=this._convert3Dto2D(new d(s,o,a)),m.textAlign="right",m.textBaseline="middle",m.fillStyle=this.axisColor,m.fillText(S,r.x-h,r.y))},n.prototype._hsv2rgb=function(t,e,i){var n,r,s,o,a,h;switch(o=i*e,a=Math.floor(t/60),h=o*(1-Math.abs(t/60%2-1)),a){case 0:n=o,r=h,s=0;break;case 1:n=h,r=o,s=0;break;case 2:n=0,r=o,s=h;break;case 3:n=0,r=h,s=o;break;case 4:n=h,r=0,s=o;break;case 5:n=o,r=0,s=h;break;default:n=0,r=0,s=0}return"RGB("+parseInt(255*n)+","+parseInt(255*r)+","+parseInt(255*s)+")"},n.prototype._redrawDataGrid=function(){var t,e,i,r,s,o,a,h,l,u,c,f,p=this.frame.canvas,m=p.getContext("2d");if(m.lineJoin="round",m.lineCap="round",!(void 0===this.dataPoints||this.dataPoints.length<=0)){for(s=0;s<this.dataPoints.length;s++){var v=this._convertPointToTranslation(this.dataPoints[s].point),y=this._convertTranslationToScreen(v);this.dataPoints[s].trans=v,this.dataPoints[s].screen=y;var g=this._convertPointToTranslation(this.dataPoints[s].bottom);this.dataPoints[s].dist=this.showPerspective?g.length():-g.z}var _=function(t,e){return e.dist-t.dist};if(this.dataPoints.sort(_),this.style===n.STYLE.SURFACE){for(s=0;s<this.dataPoints.length;s++)if(t=this.dataPoints[s],e=this.dataPoints[s].pointRight,i=this.dataPoints[s].pointTop,r=this.dataPoints[s].pointCross,void 0!==t&&void 0!==e&&void 0!==i&&void 0!==r){if(this.showGrayBottom||this.showShadow){var x=d.subtract(r.trans,t.trans),w=d.subtract(i.trans,e.trans),b=d.crossProduct(x,w),M=b.length();o=b.z>0}else o=!0;o?(f=(t.point.z+e.point.z+i.point.z+r.point.z)/4,l=240*(1-(f-this.zMin)*this.scale.z/this.verticalRatio),u=1,this.showShadow?(c=Math.min(1+b.x/M/2,1),a=this._hsv2rgb(l,u,c),h=a):(c=1,a=this._hsv2rgb(l,u,c),h=this.axisColor)):(a="gray",h=this.axisColor),m.lineWidth=this._getStrokeWidth(t),m.fillStyle=a,m.strokeStyle=h,m.beginPath(),m.moveTo(t.screen.x,t.screen.y),m.lineTo(e.screen.x,e.screen.y),m.lineTo(r.screen.x,r.screen.y),m.lineTo(i.screen.x,i.screen.y),m.closePath(),m.fill(),m.stroke()}}else for(s=0;s<this.dataPoints.length;s++)t=this.dataPoints[s],e=this.dataPoints[s].pointRight,i=this.dataPoints[s].pointTop,void 0!==t&&void 0!==e&&(f=(t.point.z+e.point.z)/2,l=240*(1-(f-this.zMin)*this.scale.z/this.verticalRatio),m.lineWidth=2*this._getStrokeWidth(t),m.strokeStyle=this._hsv2rgb(l,1,1),m.beginPath(),m.moveTo(t.screen.x,t.screen.y),m.lineTo(e.screen.x,e.screen.y),m.stroke()),void 0!==t&&void 0!==i&&(f=(t.point.z+i.point.z)/2,l=240*(1-(f-this.zMin)*this.scale.z/this.verticalRatio),m.lineWidth=2*this._getStrokeWidth(t),m.strokeStyle=this._hsv2rgb(l,1,1),m.beginPath(),m.moveTo(t.screen.x,t.screen.y),m.lineTo(i.screen.x,i.screen.y),m.stroke())}},n.prototype._getStrokeWidth=function(t){return void 0!==t?this.showPerspective?1/-t.trans.z*this.dataColor.strokeWidth:-(this.eye.z/this.camera.getArmLength())*this.dataColor.strokeWidth:this.dataColor.strokeWidth},n.prototype._redrawDataDot=function(){var t,e=this.frame.canvas,i=e.getContext("2d");if(!(void 0===this.dataPoints||this.dataPoints.length<=0)){for(t=0;t<this.dataPoints.length;t++){var r=this._convertPointToTranslation(this.dataPoints[t].point),s=this._convertTranslationToScreen(r);this.dataPoints[t].trans=r,this.dataPoints[t].screen=s;var o=this._convertPointToTranslation(this.dataPoints[t].bottom);this.dataPoints[t].dist=this.showPerspective?o.length():-o.z}var a=function(t,e){return e.dist-t.dist};this.dataPoints.sort(a);var h=this.frame.clientWidth*this.dotSizeRatio;for(t=0;t<this.dataPoints.length;t++){var l=this.dataPoints[t];if(this.style===n.STYLE.DOTLINE){var u=this._convert3Dto2D(l.bottom);i.lineWidth=1,i.strokeStyle=this.gridColor,i.beginPath(),i.moveTo(u.x,u.y),i.lineTo(l.screen.x,l.screen.y),i.stroke()}var d;d=this.style===n.STYLE.DOTSIZE?h/2+2*h*(l.point.value-this.valueMin)/(this.valueMax-this.valueMin):h;var c;c=this.showPerspective?d/-l.trans.z:d*-(this.eye.z/this.camera.getArmLength()),c<0&&(c=0);var f,p,m;this.style===n.STYLE.DOTCOLOR?(f=240*(1-(l.point.value-this.valueMin)*this.scale.value),p=this._hsv2rgb(f,1,1),m=this._hsv2rgb(f,1,.8)):this.style===n.STYLE.DOTSIZE?(p=this.dataColor.fill,m=this.dataColor.stroke):(f=240*(1-(l.point.z-this.zMin)*this.scale.z/this.verticalRatio),p=this._hsv2rgb(f,1,1),m=this._hsv2rgb(f,1,.8)),i.lineWidth=this._getStrokeWidth(l),i.strokeStyle=m,i.fillStyle=p,i.beginPath(),i.arc(l.screen.x,l.screen.y,c,0,2*Math.PI,!0),i.fill(),i.stroke()}}},n.prototype._redrawDataBar=function(){var t,e,i,r,s=this.frame.canvas,o=s.getContext("2d");if(!(void 0===this.dataPoints||this.dataPoints.length<=0)){for(t=0;t<this.dataPoints.length;t++){var a=this._convertPointToTranslation(this.dataPoints[t].point),h=this._convertTranslationToScreen(a);this.dataPoints[t].trans=a,this.dataPoints[t].screen=h;var l=this._convertPointToTranslation(this.dataPoints[t].bottom);this.dataPoints[t].dist=this.showPerspective?l.length():-l.z}var u=function(t,e){return e.dist-t.dist};this.dataPoints.sort(u),o.lineJoin="round",o.lineCap="round";var c=this.xBarWidth/2,f=this.yBarWidth/2;for(t=0;t<this.dataPoints.length;t++){var p,m,v,y=this.dataPoints[t];this.style===n.STYLE.BARCOLOR?(p=240*(1-(y.point.value-this.valueMin)*this.scale.value),m=this._hsv2rgb(p,1,1),v=this._hsv2rgb(p,1,.8)):this.style===n.STYLE.BARSIZE?(m=this.dataColor.fill,v=this.dataColor.stroke):(p=240*(1-(y.point.z-this.zMin)*this.scale.z/this.verticalRatio),m=this._hsv2rgb(p,1,1),v=this._hsv2rgb(p,1,.8)),this.style===n.STYLE.BARSIZE&&(c=this.xBarWidth/2*((y.point.value-this.valueMin)/(this.valueMax-this.valueMin)*.8+.2),f=this.yBarWidth/2*((y.point.value-this.valueMin)/(this.valueMax-this.valueMin)*.8+.2));var g=this,_=y.point,x=[{point:new d(_.x-c,_.y-f,_.z)},{point:new d(_.x+c,_.y-f,_.z)},{point:new d(_.x+c,_.y+f,_.z)},{point:new d(_.x-c,_.y+f,_.z)}],w=[{point:new d(_.x-c,_.y-f,this.zMin)},{point:new d(_.x+c,_.y-f,this.zMin)},{point:new d(_.x+c,_.y+f,this.zMin)},{point:new d(_.x-c,_.y+f,this.zMin)}];x.forEach(function(t){t.screen=g._convert3Dto2D(t.point)}),w.forEach(function(t){t.screen=g._convert3Dto2D(t.point)});var b=[{corners:x,center:d.avg(w[0].point,w[2].point)},{corners:[x[0],x[1],w[1],w[0]],center:d.avg(w[1].point,w[0].point)},{corners:[x[1],x[2],w[2],w[1]],center:d.avg(w[2].point,w[1].point)},{corners:[x[2],x[3],w[3],w[2]],center:d.avg(w[3].point,w[2].point)},{corners:[x[3],x[0],w[0],w[3]],center:d.avg(w[0].point,w[3].point)}];for(y.surfaces=b,e=0;e<b.length;e++){i=b[e];var M=this._convertPointToTranslation(i.center);i.dist=this.showPerspective?M.length():-M.z}for(b.sort(function(t,e){var i=e.dist-t.dist;return i?i:t.corners===x?1:e.corners===x?-1:0}),o.lineWidth=this._getStrokeWidth(y),o.strokeStyle=v,o.fillStyle=m,e=2;e<b.length;e++)i=b[e],r=i.corners,o.beginPath(),o.moveTo(r[3].screen.x,r[3].screen.y),o.lineTo(r[0].screen.x,r[0].screen.y),o.lineTo(r[1].screen.x,r[1].screen.y),o.lineTo(r[2].screen.x,r[2].screen.y),o.lineTo(r[3].screen.x,r[3].screen.y),o.fill(),o.stroke()}}},n.prototype._redrawDataLine=function(){var t,e,i=this.frame.canvas,n=i.getContext("2d");if(!(void 0===this.dataPoints||this.dataPoints.length<=0)){for(e=0;e<this.dataPoints.length;e++){var r=this._convertPointToTranslation(this.dataPoints[e].point),s=this._convertTranslationToScreen(r);this.dataPoints[e].trans=r,this.dataPoints[e].screen=s}if(this.dataPoints.length>0){for(t=this.dataPoints[0],n.lineWidth=this._getStrokeWidth(t),n.lineJoin="round",n.lineCap="round",n.strokeStyle=this.dataColor.stroke,n.beginPath(),n.moveTo(t.screen.x,t.screen.y),e=1;e<this.dataPoints.length;e++)t=this.dataPoints[e],n.lineTo(t.screen.x,t.screen.y);n.stroke()}}},n.prototype._onMouseDown=function(t){if(t=t||window.event,this.leftButtonDown&&this._onMouseUp(t),this.leftButtonDown=t.which?1===t.which:1===t.button,this.leftButtonDown||this.touchDown){this.startMouseX=r(t),this.startMouseY=s(t),this.startStart=new Date(this.start),this.startEnd=new Date(this.end),this.startArmRotation=this.camera.getArmRotation(),this.frame.style.cursor="move";var e=this;this.onmousemove=function(t){e._onMouseMove(t)},this.onmouseup=function(t){e._onMouseUp(t)},u.addEventListener(document,"mousemove",e.onmousemove),u.addEventListener(document,"mouseup",e.onmouseup),u.preventDefault(t)}},n.prototype._onMouseMove=function(t){t=t||window.event;var e=parseFloat(r(t))-this.startMouseX,i=parseFloat(s(t))-this.startMouseY,n=this.startArmRotation.horizontal+e/200,o=this.startArmRotation.vertical+i/200,a=4,h=Math.sin(a/360*2*Math.PI);Math.abs(Math.sin(n))<h&&(n=Math.round(n/Math.PI)*Math.PI-.001),Math.abs(Math.cos(n))<h&&(n=(Math.round(n/Math.PI-.5)+.5)*Math.PI-.001),Math.abs(Math.sin(o))<h&&(o=Math.round(o/Math.PI)*Math.PI),Math.abs(Math.cos(o))<h&&(o=(Math.round(o/Math.PI-.5)+.5)*Math.PI),this.camera.setArmRotation(n,o),this.redraw();var l=this.getCameraPosition();this.emit("cameraPositionChange",l),u.preventDefault(t)},n.prototype._onMouseUp=function(t){this.frame.style.cursor="auto",this.leftButtonDown=!1,u.removeEventListener(document,"mousemove",this.onmousemove),u.removeEventListener(document,"mouseup",this.onmouseup),u.preventDefault(t)},n.prototype._onTooltip=function(t){var e=300,i=this.frame.getBoundingClientRect(),n=r(t)-i.left,o=s(t)-i.top;if(this.showTooltip){if(this.tooltipTimeout&&clearTimeout(this.tooltipTimeout),this.leftButtonDown)return void this._hideTooltip();if(this.tooltip&&this.tooltip.dataPoint){var a=this._dataPointFromXY(n,o);a!==this.tooltip.dataPoint&&(a?this._showTooltip(a):this._hideTooltip())}else{var h=this;this.tooltipTimeout=setTimeout(function(){h.tooltipTimeout=null;var t=h._dataPointFromXY(n,o);t&&h._showTooltip(t)},e)}}},n.prototype._onTouchStart=function(t){this.touchDown=!0;var e=this;this.ontouchmove=function(t){e._onTouchMove(t)},this.ontouchend=function(t){e._onTouchEnd(t)},u.addEventListener(document,"touchmove",e.ontouchmove),u.addEventListener(document,"touchend",e.ontouchend),this._onMouseDown(t)},n.prototype._onTouchMove=function(t){this._onMouseMove(t)},n.prototype._onTouchEnd=function(t){this.touchDown=!1,u.removeEventListener(document,"touchmove",this.ontouchmove),u.removeEventListener(document,"touchend",this.ontouchend),this._onMouseUp(t)},n.prototype._onWheel=function(t){t||(t=window.event);var e=0;if(t.wheelDelta?e=t.wheelDelta/120:t.detail&&(e=-t.detail/3),e){var i=this.camera.getArmLength(),n=i*(1-e/10);this.camera.setArmLength(n),this.redraw(),this._hideTooltip()}var r=this.getCameraPosition();this.emit("cameraPositionChange",r),u.preventDefault(t)},n.prototype._insideTriangle=function(t,e){function i(t){return t>0?1:t<0?-1:0}var n=e[0],r=e[1],s=e[2],o=i((r.x-n.x)*(t.y-n.y)-(r.y-n.y)*(t.x-n.x)),a=i((s.x-r.x)*(t.y-r.y)-(s.y-r.y)*(t.x-r.x)),h=i((n.x-s.x)*(t.y-s.y)-(n.y-s.y)*(t.x-s.x));return!(0!=o&&0!=a&&o!=a||0!=a&&0!=h&&a!=h||0!=o&&0!=h&&o!=h)},n.prototype._dataPointFromXY=function(t,e){var i,r=100,s=null,o=null,a=null,h=new c(t,e);if(this.style===n.STYLE.BAR||this.style===n.STYLE.BARCOLOR||this.style===n.STYLE.BARSIZE)for(i=this.dataPoints.length-1;i>=0;i--){s=this.dataPoints[i];var l=s.surfaces;if(l)for(var u=l.length-1;u>=0;u--){var d=l[u],f=d.corners,p=[f[0].screen,f[1].screen,f[2].screen],m=[f[2].screen,f[3].screen,f[0].screen];if(this._insideTriangle(h,p)||this._insideTriangle(h,m))return s}}else for(i=0;i<this.dataPoints.length;i++){s=this.dataPoints[i];var v=s.screen;if(v){var y=Math.abs(t-v.x),g=Math.abs(e-v.y),_=Math.sqrt(y*y+g*g);(null===a||_<a)&&_<r&&(a=_,o=s)}}return o},n.prototype._showTooltip=function(t){var e,i,n;this.tooltip?(e=this.tooltip.dom.content,i=this.tooltip.dom.line,n=this.tooltip.dom.dot):(e=document.createElement("div"),e.style.position="absolute",e.style.padding="10px",e.style.border="1px solid #4d4d4d",e.style.color="#1a1a1a",e.style.background="rgba(255,255,255,0.7)",e.style.borderRadius="2px",e.style.boxShadow="5px 5px 10px rgba(128,128,128,0.5)",i=document.createElement("div"),i.style.position="absolute",i.style.height="40px",i.style.width="0",i.style.borderLeft="1px solid #4d4d4d",n=document.createElement("div"),n.style.position="absolute",n.style.height="0",n.style.width="0",n.style.border="5px solid #4d4d4d",n.style.borderRadius="5px",this.tooltip={dataPoint:null,dom:{content:e,line:i,dot:n}}),this._hideTooltip(),this.tooltip.dataPoint=t,"function"==typeof this.showTooltip?e.innerHTML=this.showTooltip(t.point):e.innerHTML="<table><tr><td>"+this.xLabel+":</td><td>"+t.point.x+"</td></tr><tr><td>"+this.yLabel+":</td><td>"+t.point.y+"</td></tr><tr><td>"+this.zLabel+":</td><td>"+t.point.z+"</td></tr></table>",e.style.left="0",e.style.top="0",this.frame.appendChild(e),this.frame.appendChild(i),this.frame.appendChild(n);var r=e.offsetWidth,s=e.offsetHeight,o=i.offsetHeight,a=n.offsetWidth,h=n.offsetHeight,l=t.screen.x-r/2;l=Math.min(Math.max(l,10),this.frame.clientWidth-10-r),i.style.left=t.screen.x+"px",i.style.top=t.screen.y-o+"px",e.style.left=l+"px",e.style.top=t.screen.y-o-s+"px",n.style.left=t.screen.x-a/2+"px",n.style.top=t.screen.y-h/2+"px"},n.prototype._hideTooltip=function(){if(this.tooltip){this.tooltip.dataPoint=null;for(var t in this.tooltip.dom)if(this.tooltip.dom.hasOwnProperty(t)){var e=this.tooltip.dom[t];e&&e.parentNode&&e.parentNode.removeChild(e)}}},t.exports=n},function(t,e){function i(t){if(t)return n(t)}function n(t){for(var e in i.prototype)t[e]=i.prototype[e];return t}t.exports=i,i.prototype.on=i.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks[t]=this._callbacks[t]||[]).push(e),this},i.prototype.once=function(t,e){function i(){n.off(t,i),e.apply(this,arguments)}var n=this;return this._callbacks=this._callbacks||{},i.fn=e,this.on(t,i),this},i.prototype.off=i.prototype.removeListener=i.prototype.removeAllListeners=i.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var i=this._callbacks[t];if(!i)return this;if(1==arguments.length)return delete this._callbacks[t],this;for(var n,r=0;r<i.length;r++)if(n=i[r],n===e||n.fn===e){i.splice(r,1);break}return this},i.prototype.emit=function(t){this._callbacks=this._callbacks||{};var e=[].slice.call(arguments,1),i=this._callbacks[t];if(i){i=i.slice(0);for(var n=0,r=i.length;n<r;++n)i[n].apply(this,e)}return this},i.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks[t]||[]},i.prototype.hasListeners=function(t){return!!this.listeners(t).length}},function(t,e){function i(t,e,i){this.x=void 0!==t?t:0,this.y=void 0!==e?e:0,this.z=void 0!==i?i:0}i.subtract=function(t,e){var n=new i;return n.x=t.x-e.x,n.y=t.y-e.y,n.z=t.z-e.z,n},i.add=function(t,e){var n=new i;return n.x=t.x+e.x,n.y=t.y+e.y,n.z=t.z+e.z,n},i.avg=function(t,e){return new i((t.x+e.x)/2,(t.y+e.y)/2,(t.z+e.z)/2)},i.crossProduct=function(t,e){var n=new i;return n.x=t.y*e.z-t.z*e.y,n.y=t.z*e.x-t.x*e.z,n.z=t.x*e.y-t.y*e.x,n},i.prototype.length=function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)},t.exports=i},function(t,e){function i(t,e){this.x=void 0!==t?t:0,this.y=void 0!==e?e:0}t.exports=i},function(t,e,i){function n(){this.armLocation=new r,this.armRotation={},this.armRotation.horizontal=0,this.armRotation.vertical=0,this.armLength=1.7,this.cameraLocation=new r,this.cameraRotation=new r(.5*Math.PI,0,0),this.calculateCameraOrientation()}var r=i(13);n.prototype.setArmLocation=function(t,e,i){this.armLocation.x=t,this.armLocation.y=e,this.armLocation.z=i,this.calculateCameraOrientation()},n.prototype.setArmRotation=function(t,e){void 0!==t&&(this.armRotation.horizontal=t),void 0!==e&&(this.armRotation.vertical=e,this.armRotation.vertical<0&&(this.armRotation.vertical=0),this.armRotation.vertical>.5*Math.PI&&(this.armRotation.vertical=.5*Math.PI)),void 0===t&&void 0===e||this.calculateCameraOrientation()},n.prototype.getArmRotation=function(){var t={};return t.horizontal=this.armRotation.horizontal,t.vertical=this.armRotation.vertical,t},n.prototype.setArmLength=function(t){void 0!==t&&(this.armLength=t,this.armLength<.71&&(this.armLength=.71),this.armLength>5&&(this.armLength=5),this.calculateCameraOrientation())},n.prototype.getArmLength=function(){return this.armLength},n.prototype.getCameraLocation=function(){return this.cameraLocation},n.prototype.getCameraRotation=function(){return this.cameraRotation},n.prototype.calculateCameraOrientation=function(){this.cameraLocation.x=this.armLocation.x-this.armLength*Math.sin(this.armRotation.horizontal)*Math.cos(this.armRotation.vertical),this.cameraLocation.y=this.armLocation.y-this.armLength*Math.cos(this.armRotation.horizontal)*Math.cos(this.armRotation.vertical),this.cameraLocation.z=this.armLocation.z+this.armLength*Math.sin(this.armRotation.vertical),this.cameraRotation.x=Math.PI/2-this.armRotation.vertical,this.cameraRotation.y=0,this.cameraRotation.z=-this.armRotation.horizontal},t.exports=n},function(t,e,i){function n(t,e,i){this.data=t,this.column=e,this.graph=i,this.index=void 0,this.value=void 0,this.values=i.getDistinctValues(t.get(),this.column),this.values.sort(function(t,e){return t>e?1:t<e?-1:0}),this.values.length>0&&this.selectValue(0),this.dataPoints=[],this.loaded=!1,this.onLoadCallback=void 0,i.animationPreload?(this.loaded=!1,this.loadInBackground()):this.loaded=!0}var r=i(10);n.prototype.isLoaded=function(){return this.loaded},n.prototype.getLoadedProgress=function(){for(var t=this.values.length,e=0;this.dataPoints[e];)e++;return Math.round(e/t*100)},n.prototype.getLabel=function(){return this.graph.filterLabel},n.prototype.getColumn=function(){return this.column},n.prototype.getSelectedValue=function(){if(void 0!==this.index)return this.values[this.index]},n.prototype.getValues=function(){return this.values},n.prototype.getValue=function(t){if(t>=this.values.length)throw"Error: index out of range";return this.values[t]},n.prototype._getDataPoints=function(t){if(void 0===t&&(t=this.index),void 0===t)return[];var e;if(this.dataPoints[t])e=this.dataPoints[t];else{var i={};i.column=this.column,i.value=this.values[t];var n=new r(this.data,{filter:function(t){return t[i.column]==i.value}}).get();e=this.graph._getDataPoints(n),this.dataPoints[t]=e}return e},n.prototype.setOnLoadCallback=function(t){this.onLoadCallback=t},n.prototype.selectValue=function(t){if(t>=this.values.length)throw"Error: index out of range";this.index=t,this.value=this.values[t]},n.prototype.loadInBackground=function(t){void 0===t&&(t=0);var e=this.graph.frame;if(t<this.values.length){this._getDataPoints(t);void 0===e.progress&&(e.progress=document.createElement("DIV"),e.progress.style.position="absolute",e.progress.style.color="gray",e.appendChild(e.progress));var i=this.getLoadedProgress();e.progress.innerHTML="Loading animation... "+i+"%",e.progress.style.bottom="60px",e.progress.style.left="10px";var n=this;setTimeout(function(){n.loadInBackground(t+1)},10),this.loaded=!1}else this.loaded=!0,void 0!==e.progress&&(e.removeChild(e.progress),e.progress=void 0),this.onLoadCallback&&this.onLoadCallback()},t.exports=n},function(t,e,i){function n(t,e){if(void 0===t)throw"Error: No container element defined";if(this.container=t,this.visible=!e||void 0==e.visible||e.visible,this.visible){this.frame=document.createElement("DIV"),this.frame.style.width="100%",this.frame.style.position="relative",this.container.appendChild(this.frame),this.frame.prev=document.createElement("INPUT"),this.frame.prev.type="BUTTON",this.frame.prev.value="Prev",this.frame.appendChild(this.frame.prev),this.frame.play=document.createElement("INPUT"),this.frame.play.type="BUTTON",this.frame.play.value="Play",this.frame.appendChild(this.frame.play),this.frame.next=document.createElement("INPUT"),this.frame.next.type="BUTTON",this.frame.next.value="Next",this.frame.appendChild(this.frame.next),this.frame.bar=document.createElement("INPUT"),this.frame.bar.type="BUTTON",this.frame.bar.style.position="absolute",this.frame.bar.style.border="1px solid red",this.frame.bar.style.width="100px",this.frame.bar.style.height="6px",this.frame.bar.style.borderRadius="2px",this.frame.bar.style.MozBorderRadius="2px",this.frame.bar.style.border="1px solid #7F7F7F",this.frame.bar.style.backgroundColor="#E5E5E5",this.frame.appendChild(this.frame.bar),this.frame.slide=document.createElement("INPUT"),this.frame.slide.type="BUTTON",this.frame.slide.style.margin="0px",this.frame.slide.value=" ",this.frame.slide.style.position="relative",this.frame.slide.style.left="-100px",this.frame.appendChild(this.frame.slide);var i=this;this.frame.slide.onmousedown=function(t){i._onMouseDown(t)},this.frame.prev.onclick=function(t){i.prev(t)},this.frame.play.onclick=function(t){i.togglePlay(t)},this.frame.next.onclick=function(t){i.next(t)}}this.onChangeCallback=void 0,this.values=[],this.index=void 0,this.playTimeout=void 0,this.playInterval=1e3,this.playLoop=!0}var r=i(1);n.prototype.prev=function(){var t=this.getIndex();t>0&&(t--,this.setIndex(t))},n.prototype.next=function(){var t=this.getIndex();t<this.values.length-1&&(t++,this.setIndex(t))},n.prototype.playNext=function(){var t=new Date,e=this.getIndex();e<this.values.length-1?(e++,this.setIndex(e)):this.playLoop&&(e=0,this.setIndex(e));var i=new Date,n=i-t,r=Math.max(this.playInterval-n,0),s=this;this.playTimeout=setTimeout(function(){s.playNext()},r)},n.prototype.togglePlay=function(){void 0===this.playTimeout?this.play():this.stop()},n.prototype.play=function(){this.playTimeout||(this.playNext(),this.frame&&(this.frame.play.value="Stop"))},n.prototype.stop=function(){clearInterval(this.playTimeout),this.playTimeout=void 0,this.frame&&(this.frame.play.value="Play")},n.prototype.setOnChangeCallback=function(t){this.onChangeCallback=t},n.prototype.setPlayInterval=function(t){this.playInterval=t},n.prototype.getPlayInterval=function(t){return this.playInterval},n.prototype.setPlayLoop=function(t){this.playLoop=t},n.prototype.onChange=function(){void 0!==this.onChangeCallback&&this.onChangeCallback()},n.prototype.redraw=function(){if(this.frame){this.frame.bar.style.top=this.frame.clientHeight/2-this.frame.bar.offsetHeight/2+"px",this.frame.bar.style.width=this.frame.clientWidth-this.frame.prev.clientWidth-this.frame.play.clientWidth-this.frame.next.clientWidth-30+"px";var t=this.indexToLeft(this.index);this.frame.slide.style.left=t+"px"}},n.prototype.setValues=function(t){this.values=t,this.values.length>0?this.setIndex(0):this.index=void 0},n.prototype.setIndex=function(t){if(!(t<this.values.length))throw"Error: index out of range";this.index=t,this.redraw(),this.onChange()},n.prototype.getIndex=function(){return this.index},n.prototype.get=function(){return this.values[this.index]},n.prototype._onMouseDown=function(t){var e=t.which?1===t.which:1===t.button;if(e){this.startClientX=t.clientX,this.startSlideX=parseFloat(this.frame.slide.style.left),this.frame.style.cursor="move";var i=this;this.onmousemove=function(t){i._onMouseMove(t)},this.onmouseup=function(t){i._onMouseUp(t)},r.addEventListener(document,"mousemove",this.onmousemove),r.addEventListener(document,"mouseup",this.onmouseup),r.preventDefault(t)}},n.prototype.leftToIndex=function(t){var e=parseFloat(this.frame.bar.style.width)-this.frame.slide.clientWidth-10,i=t-3,n=Math.round(i/e*(this.values.length-1));return n<0&&(n=0),n>this.values.length-1&&(n=this.values.length-1),n},n.prototype.indexToLeft=function(t){var e=parseFloat(this.frame.bar.style.width)-this.frame.slide.clientWidth-10,i=t/(this.values.length-1)*e,n=i+3;return n},n.prototype._onMouseMove=function(t){var e=t.clientX-this.startClientX,i=this.startSlideX+e,n=this.leftToIndex(i);this.setIndex(n),r.preventDefault()},n.prototype._onMouseUp=function(t){this.frame.style.cursor="auto",r.removeEventListener(document,"mousemove",this.onmousemove),r.removeEventListener(document,"mouseup",this.onmouseup),r.preventDefault()},t.exports=n},function(t,e){function i(t,e,i,n){this._start=0,this._end=0,this._step=1,this.prettyStep=!0,this.precision=5,this._current=0,this.setRange(t,e,i,n)}i.prototype.setRange=function(t,e,i,n){this._start=t?t:0,this._end=e?e:0,this.setStep(i,n)},i.prototype.setStep=function(t,e){void 0===t||t<=0||(void 0!==e&&(this.prettyStep=e),this.prettyStep===!0?this._step=i.calculatePrettyStep(t):this._step=t)},i.calculatePrettyStep=function(t){var e=function(t){return Math.log(t)/Math.LN10},i=Math.pow(10,Math.round(e(t))),n=2*Math.pow(10,Math.round(e(t/2))),r=5*Math.pow(10,Math.round(e(t/5))),s=i;return Math.abs(n-t)<=Math.abs(s-t)&&(s=n),Math.abs(r-t)<=Math.abs(s-t)&&(s=r),s<=0&&(s=1),s},i.prototype.getCurrent=function(){return parseFloat(this._current.toPrecision(this.precision))},i.prototype.getStep=function(){return this._step},i.prototype.start=function(){this._current=this._start-this._start%this._step},i.prototype.next=function(){this._current+=this._step},i.prototype.end=function(){return this._current>this._end},t.exports=i},function(t,e,i){if("undefined"!=typeof window){var n=i(20),r=window.Hammer||i(21);t.exports=n(r,{preventDefault:"mouse"})}else t.exports=function(){throw Error("hammer.js is only available in a browser, not in node.js.")}},function(t,e,i){var n,r,s;!function(i){r=[],n=i,s="function"==typeof n?n.apply(e,r):n,!(void 0!==s&&(t.exports=s))}(function(){var t=null;return function e(i,n){function r(t){return t.match(/[^ ]+/g)}function s(e){if("hammer.input"!==e.type){if(e.srcEvent._handled||(e.srcEvent._handled={}),e.srcEvent._handled[e.type])return;e.srcEvent._handled[e.type]=!0}var i=!1;e.stopPropagation=function(){i=!0};var n=e.srcEvent.stopPropagation.bind(e.srcEvent);"function"==typeof n&&(e.srcEvent.stopPropagation=function(){n(),e.stopPropagation()}),e.firstTarget=t;for(var r=t;r&&!i;){var s=r.hammer;if(s)for(var o,a=0;a<s.length;a++)if(o=s[a]._handlers[e.type])for(var h=0;h<o.length&&!i;h++)o[h](e);r=r.parentNode}}var o=n||{preventDefault:!1};if(i.Manager){var a=i,h=function(t,i){var n=Object.create(o);return i&&a.assign(n,i),e(new a(t,n),n)};return a.assign(h,a),h.Manager=function(t,i){var n=Object.create(o);return i&&a.assign(n,i),e(new a.Manager(t,n),n)},h}var l=Object.create(i),u=i.element;return u.hammer||(u.hammer=[]),u.hammer.push(l),i.on("hammer.input",function(e){o.preventDefault!==!0&&o.preventDefault!==e.pointerType||e.preventDefault(),e.isFirst&&(t=e.target)}),l._handlers={},l.on=function(t,e){return r(t).forEach(function(t){var n=l._handlers[t];n||(l._handlers[t]=n=[],i.on(t,s)),n.push(e)}),l},l.off=function(t,e){return r(t).forEach(function(t){var n=l._handlers[t];n&&(n=e?n.filter(function(t){return t!==e}):[],n.length>0?l._handlers[t]=n:(i.off(t,s),delete l._handlers[t]))}),l},l.emit=function(e,n){t=n.target,i.emit(e,n)},l.destroy=function(){var t=i.element.hammer,e=t.indexOf(l);e!==-1&&t.splice(e,1),t.length||delete i.element.hammer,l._handlers={},i.destroy()},l}})},function(t,e,i){var n;/*! Hammer.JS - v2.0.7 - 2016-04-22
   * http://hammerjs.github.io/
   *
   * Copyright (c) 2016 Jorik Tangelder;
   * Licensed under the MIT license */
!function(r,s,o,a){function h(t,e,i){return setTimeout(f(t,i),e)}function l(t,e,i){return!!Array.isArray(t)&&(u(t,i[e],i),!0)}function u(t,e,i){var n;if(t)if(t.forEach)t.forEach(e,i);else if(t.length!==a)for(n=0;n<t.length;)e.call(i,t[n],n,t),n++;else for(n in t)t.hasOwnProperty(n)&&e.call(i,t[n],n,t)}function d(t,e,i){var n="DEPRECATED METHOD: "+e+"\n"+i+" AT \n";return function(){var e=new Error("get-stack-trace"),i=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",s=r.console&&(r.console.warn||r.console.log);return s&&s.call(r.console,n,i),t.apply(this,arguments)}}function c(t,e,i){var n,r=e.prototype;n=t.prototype=Object.create(r),n.constructor=t,n._super=r,i&&mt(n,i)}function f(t,e){return function(){return t.apply(e,arguments)}}function p(t,e){return typeof t==gt?t.apply(e?e[0]||a:a,e):t}function m(t,e){return t===a?e:t}function v(t,e,i){u(x(e),function(e){t.addEventListener(e,i,!1)})}function y(t,e,i){u(x(e),function(e){t.removeEventListener(e,i,!1)})}function g(t,e){for(;t;){if(t==e)return!0;t=t.parentNode}return!1}function _(t,e){return t.indexOf(e)>-1}function x(t){return t.trim().split(/\s+/g)}function w(t,e,i){if(t.indexOf&&!i)return t.indexOf(e);for(var n=0;n<t.length;){if(i&&t[n][i]==e||!i&&t[n]===e)return n;n++}return-1}function b(t){return Array.prototype.slice.call(t,0)}function M(t,e,i){for(var n=[],r=[],s=0;s<t.length;){var o=e?t[s][e]:t[s];w(r,o)<0&&n.push(t[s]),r[s]=o,s++}return i&&(n=e?n.sort(function(t,i){return t[e]>i[e]}):n.sort()),n}function S(t,e){for(var i,n,r=e[0].toUpperCase()+e.slice(1),s=0;s<vt.length;){if(i=vt[s],n=i?i+r:e,n in t)return n;s++}return a}function T(){return St++}function D(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||r}function k(t,e){var i=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){p(t.options.enable,[t])&&i.handler(e)},this.init()}function C(t){var e,i=t.options.inputClass;return new(e=i?i:kt?j:Ct?H:Dt?X:V)(t,O)}function O(t,e,i){var n=i.pointers.length,r=i.changedPointers.length,s=e&Rt&&n-r===0,o=e&(It|zt)&&n-r===0;i.isFirst=!!s,i.isFinal=!!o,s&&(t.session={}),i.eventType=e,P(t,i),t.emit("hammer.input",i),t.recognize(i),t.session.prevInput=i}function P(t,e){var i=t.session,n=e.pointers,r=n.length;i.firstInput||(i.firstInput=Y(e)),r>1&&!i.firstMultiple?i.firstMultiple=Y(e):1===r&&(i.firstMultiple=!1);var s=i.firstInput,o=i.firstMultiple,a=o?o.center:s.center,h=e.center=R(n);e.timeStamp=wt(),e.deltaTime=e.timeStamp-s.timeStamp,e.angle=W(a,h),e.distance=z(a,h),E(i,e),e.offsetDirection=I(e.deltaX,e.deltaY);var l=A(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=l.x,e.overallVelocityY=l.y,e.overallVelocity=xt(l.x)>xt(l.y)?l.x:l.y,e.scale=o?F(o.pointers,n):1,e.rotation=o?N(o.pointers,n):0,e.maxPointers=i.prevInput?e.pointers.length>i.prevInput.maxPointers?e.pointers.length:i.prevInput.maxPointers:e.pointers.length,L(i,e);var u=t.element;g(e.srcEvent.target,u)&&(u=e.srcEvent.target),e.target=u}function E(t,e){var i=e.center,n=t.offsetDelta||{},r=t.prevDelta||{},s=t.prevInput||{};e.eventType!==Rt&&s.eventType!==It||(r=t.prevDelta={x:s.deltaX||0,y:s.deltaY||0},n=t.offsetDelta={x:i.x,y:i.y}),e.deltaX=r.x+(i.x-n.x),e.deltaY=r.y+(i.y-n.y)}function L(t,e){var i,n,r,s,o=t.lastInterval||e,h=e.timeStamp-o.timeStamp;if(e.eventType!=zt&&(h>Yt||o.velocity===a)){var l=e.deltaX-o.deltaX,u=e.deltaY-o.deltaY,d=A(h,l,u);n=d.x,r=d.y,i=xt(d.x)>xt(d.y)?d.x:d.y,s=I(l,u),t.lastInterval=e}else i=o.velocity,n=o.velocityX,r=o.velocityY,s=o.direction;e.velocity=i,e.velocityX=n,e.velocityY=r,e.direction=s}function Y(t){for(var e=[],i=0;i<t.pointers.length;)e[i]={clientX:_t(t.pointers[i].clientX),clientY:_t(t.pointers[i].clientY)},i++;return{timeStamp:wt(),pointers:e,center:R(e),deltaX:t.deltaX,deltaY:t.deltaY}}function R(t){var e=t.length;if(1===e)return{x:_t(t[0].clientX),y:_t(t[0].clientY)};for(var i=0,n=0,r=0;r<e;)i+=t[r].clientX,n+=t[r].clientY,r++;return{x:_t(i/e),y:_t(n/e)}}function A(t,e,i){return{x:e/t||0,y:i/t||0}}function I(t,e){return t===e?Wt:xt(t)>=xt(e)?t<0?Nt:Ft:e<0?Vt:jt}function z(t,e,i){i||(i=Gt);var n=e[i[0]]-t[i[0]],r=e[i[1]]-t[i[1]];return Math.sqrt(n*n+r*r)}function W(t,e,i){i||(i=Gt);var n=e[i[0]]-t[i[0]],r=e[i[1]]-t[i[1]];return 180*Math.atan2(r,n)/Math.PI}function N(t,e){return W(e[1],e[0],Xt)+W(t[1],t[0],Xt)}function F(t,e){return z(e[0],e[1],Xt)/z(t[0],t[1],Xt)}function V(){this.evEl=qt,this.evWin=Qt,this.pressed=!1,k.apply(this,arguments)}function j(){this.evEl=Kt,this.evWin=te,k.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}function B(){this.evTarget=ie,this.evWin=ne,this.started=!1,k.apply(this,arguments)}function U(t,e){var i=b(t.touches),n=b(t.changedTouches);return e&(It|zt)&&(i=M(i.concat(n),"identifier",!0)),[i,n]}function H(){this.evTarget=se,this.targetIds={},k.apply(this,arguments)}function G(t,e){var i=b(t.touches),n=this.targetIds;if(e&(Rt|At)&&1===i.length)return n[i[0].identifier]=!0,[i,i];var r,s,o=b(t.changedTouches),a=[],h=this.target;if(s=i.filter(function(t){return g(t.target,h)}),e===Rt)for(r=0;r<s.length;)n[s[r].identifier]=!0,r++;for(r=0;r<o.length;)n[o[r].identifier]&&a.push(o[r]),e&(It|zt)&&delete n[o[r].identifier],r++;return a.length?[M(s.concat(a),"identifier",!0),a]:void 0}function X(){k.apply(this,arguments);var t=f(this.handler,this);this.touch=new H(this.manager,t),this.mouse=new V(this.manager,t),this.primaryTouch=null,this.lastTouches=[]}function Z(t,e){t&Rt?(this.primaryTouch=e.changedPointers[0].identifier,q.call(this,e)):t&(It|zt)&&q.call(this,e)}function q(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var i={x:e.clientX,y:e.clientY};this.lastTouches.push(i);var n=this.lastTouches,r=function(){var t=n.indexOf(i);t>-1&&n.splice(t,1)};setTimeout(r,oe)}}function Q(t){for(var e=t.srcEvent.clientX,i=t.srcEvent.clientY,n=0;n<this.lastTouches.length;n++){var r=this.lastTouches[n],s=Math.abs(e-r.x),o=Math.abs(i-r.y);if(s<=ae&&o<=ae)return!0}return!1}function $(t,e){this.manager=t,this.set(e)}function J(t){if(_(t,fe))return fe;var e=_(t,pe),i=_(t,me);return e&&i?fe:e||i?e?pe:me:_(t,ce)?ce:de}function K(){if(!le)return!1;var t={},e=r.CSS&&r.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(i){t[i]=!e||r.CSS.supports("touch-action",i)}),t}function tt(t){this.options=mt({},this.defaults,t||{}),this.id=T(),this.manager=null,this.options.enable=m(this.options.enable,!0),this.state=ye,this.simultaneous={},this.requireFail=[]}function et(t){return t&be?"cancel":t&xe?"end":t&_e?"move":t&ge?"start":""}function it(t){return t==jt?"down":t==Vt?"up":t==Nt?"left":t==Ft?"right":""}function nt(t,e){var i=e.manager;return i?i.get(t):t}function rt(){tt.apply(this,arguments)}function st(){rt.apply(this,arguments),this.pX=null,this.pY=null}function ot(){rt.apply(this,arguments)}function at(){tt.apply(this,arguments),this._timer=null,this._input=null}function ht(){rt.apply(this,arguments)}function lt(){rt.apply(this,arguments)}function ut(){tt.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}function dt(t,e){return e=e||{},e.recognizers=m(e.recognizers,dt.defaults.preset),new ct(t,e)}function ct(t,e){this.options=mt({},dt.defaults,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=C(this),this.touchAction=new $(this,this.options.touchAction),ft(this,!0),u(this.options.recognizers,function(t){var e=this.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])},this)}function ft(t,e){var i=t.element;if(i.style){var n;u(t.options.cssProps,function(r,s){n=S(i.style,s),e?(t.oldCssProps[n]=i.style[n],i.style[n]=r):i.style[n]=t.oldCssProps[n]||""}),e||(t.oldCssProps={})}}function pt(t,e){var i=s.createEvent("Event");i.initEvent(t,!0,!0),i.gesture=e,e.target.dispatchEvent(i)}var mt,vt=["","webkit","Moz","MS","ms","o"],yt=s.createElement("div"),gt="function",_t=Math.round,xt=Math.abs,wt=Date.now;mt="function"!=typeof Object.assign?function(t){if(t===a||null===t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),i=1;i<arguments.length;i++){var n=arguments[i];if(n!==a&&null!==n)for(var r in n)n.hasOwnProperty(r)&&(e[r]=n[r])}return e}:Object.assign;var bt=d(function(t,e,i){for(var n=Object.keys(e),r=0;r<n.length;)(!i||i&&t[n[r]]===a)&&(t[n[r]]=e[n[r]]),r++;return t},"extend","Use `assign`."),Mt=d(function(t,e){return bt(t,e,!0)},"merge","Use `assign`."),St=1,Tt=/mobile|tablet|ip(ad|hone|od)|android/i,Dt="ontouchstart"in r,kt=S(r,"PointerEvent")!==a,Ct=Dt&&Tt.test(navigator.userAgent),Ot="touch",Pt="pen",Et="mouse",Lt="kinect",Yt=25,Rt=1,At=2,It=4,zt=8,Wt=1,Nt=2,Ft=4,Vt=8,jt=16,Bt=Nt|Ft,Ut=Vt|jt,Ht=Bt|Ut,Gt=["x","y"],Xt=["clientX","clientY"];k.prototype={handler:function(){},init:function(){this.evEl&&v(this.element,this.evEl,this.domHandler),this.evTarget&&v(this.target,this.evTarget,this.domHandler),this.evWin&&v(D(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&y(this.element,this.evEl,this.domHandler),this.evTarget&&y(this.target,this.evTarget,this.domHandler),this.evWin&&y(D(this.element),this.evWin,this.domHandler)}};var Zt={mousedown:Rt,mousemove:At,mouseup:It},qt="mousedown",Qt="mousemove mouseup";c(V,k,{handler:function(t){var e=Zt[t.type];e&Rt&&0===t.button&&(this.pressed=!0),e&At&&1!==t.which&&(e=It),this.pressed&&(e&It&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:Et,srcEvent:t}))}});var $t={pointerdown:Rt,pointermove:At,pointerup:It,pointercancel:zt,pointerout:zt},Jt={2:Ot,3:Pt,4:Et,5:Lt},Kt="pointerdown",te="pointermove pointerup pointercancel";r.MSPointerEvent&&!r.PointerEvent&&(Kt="MSPointerDown",te="MSPointerMove MSPointerUp MSPointerCancel"),c(j,k,{handler:function(t){var e=this.store,i=!1,n=t.type.toLowerCase().replace("ms",""),r=$t[n],s=Jt[t.pointerType]||t.pointerType,o=s==Ot,a=w(e,t.pointerId,"pointerId");r&Rt&&(0===t.button||o)?a<0&&(e.push(t),a=e.length-1):r&(It|zt)&&(i=!0),a<0||(e[a]=t,this.callback(this.manager,r,{pointers:e,changedPointers:[t],pointerType:s,srcEvent:t}),i&&e.splice(a,1))}});var ee={touchstart:Rt,touchmove:At,touchend:It,touchcancel:zt},ie="touchstart",ne="touchstart touchmove touchend touchcancel";c(B,k,{handler:function(t){var e=ee[t.type];if(e===Rt&&(this.started=!0),this.started){var i=U.call(this,t,e);e&(It|zt)&&i[0].length-i[1].length===0&&(this.started=!1),this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:Ot,srcEvent:t})}}});var re={touchstart:Rt,touchmove:At,touchend:It,touchcancel:zt},se="touchstart touchmove touchend touchcancel";c(H,k,{handler:function(t){var e=re[t.type],i=G.call(this,t,e);i&&this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:Ot,srcEvent:t})}});var oe=2500,ae=25;c(X,k,{handler:function(t,e,i){var n=i.pointerType==Ot,r=i.pointerType==Et;if(!(r&&i.sourceCapabilities&&i.sourceCapabilities.firesTouchEvents)){if(n)Z.call(this,e,i);else if(r&&Q.call(this,i))return;this.callback(t,e,i)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});var he=S(yt.style,"touchAction"),le=he!==a,ue="compute",de="auto",ce="manipulation",fe="none",pe="pan-x",me="pan-y",ve=K();$.prototype={set:function(t){t==ue&&(t=this.compute()),le&&this.manager.element.style&&ve[t]&&(this.manager.element.style[he]=t),this.actions=t.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var t=[];return u(this.manager.recognizers,function(e){p(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))}),J(t.join(" "))},preventDefaults:function(t){var e=t.srcEvent,i=t.offsetDirection;if(this.manager.session.prevented)return void e.preventDefault();var n=this.actions,r=_(n,fe)&&!ve[fe],s=_(n,me)&&!ve[me],o=_(n,pe)&&!ve[pe];if(r){var a=1===t.pointers.length,h=t.distance<2,l=t.deltaTime<250;if(a&&h&&l)return}return o&&s?void 0:r||s&&i&Bt||o&&i&Ut?this.preventSrc(e):void 0},preventSrc:function(t){this.manager.session.prevented=!0,t.preventDefault()}};var ye=1,ge=2,_e=4,xe=8,we=xe,be=16,Me=32;tt.prototype={defaults:{},set:function(t){return mt(this.options,t),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(t){if(l(t,"recognizeWith",this))return this;var e=this.simultaneous;return t=nt(t,this),e[t.id]||(e[t.id]=t,t.recognizeWith(this)),this},dropRecognizeWith:function(t){return l(t,"dropRecognizeWith",this)?this:(t=nt(t,this),delete this.simultaneous[t.id],this)},requireFailure:function(t){if(l(t,"requireFailure",this))return this;var e=this.requireFail;return t=nt(t,this),w(e,t)===-1&&(e.push(t),t.requireFailure(this)),this},dropRequireFailure:function(t){if(l(t,"dropRequireFailure",this))return this;t=nt(t,this);var e=w(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(t){return!!this.simultaneous[t.id]},emit:function(t){function e(e){i.manager.emit(e,t)}var i=this,n=this.state;n<xe&&e(i.options.event+et(n)),e(i.options.event),t.additionalEvent&&e(t.additionalEvent),n>=xe&&e(i.options.event+et(n))},tryEmit:function(t){return this.canEmit()?this.emit(t):void(this.state=Me)},canEmit:function(){for(var t=0;t<this.requireFail.length;){if(!(this.requireFail[t].state&(Me|ye)))return!1;t++}return!0},recognize:function(t){var e=mt({},t);return p(this.options.enable,[this,e])?(this.state&(we|be|Me)&&(this.state=ye),this.state=this.process(e),void(this.state&(ge|_e|xe|be)&&this.tryEmit(e))):(this.reset(),void(this.state=Me))},process:function(t){},getTouchAction:function(){},reset:function(){}},c(rt,tt,{defaults:{pointers:1},attrTest:function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},process:function(t){var e=this.state,i=t.eventType,n=e&(ge|_e),r=this.attrTest(t);return n&&(i&zt||!r)?e|be:n||r?i&It?e|xe:e&ge?e|_e:ge:Me}}),c(st,rt,{defaults:{event:"pan",threshold:10,pointers:1,direction:Ht},getTouchAction:function(){var t=this.options.direction,e=[];return t&Bt&&e.push(me),t&Ut&&e.push(pe),e},directionTest:function(t){var e=this.options,i=!0,n=t.distance,r=t.direction,s=t.deltaX,o=t.deltaY;return r&e.direction||(e.direction&Bt?(r=0===s?Wt:s<0?Nt:Ft,i=s!=this.pX,n=Math.abs(t.deltaX)):(r=0===o?Wt:o<0?Vt:jt,i=o!=this.pY,n=Math.abs(t.deltaY))),t.direction=r,i&&n>e.threshold&&r&e.direction},attrTest:function(t){return rt.prototype.attrTest.call(this,t)&&(this.state&ge||!(this.state&ge)&&this.directionTest(t))},emit:function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=it(t.direction);e&&(t.additionalEvent=this.options.event+e),this._super.emit.call(this,t)}}),c(ot,rt,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[fe]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||this.state&ge)},emit:function(t){if(1!==t.scale){var e=t.scale<1?"in":"out";t.additionalEvent=this.options.event+e}this._super.emit.call(this,t)}}),c(at,tt,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return[de]},process:function(t){var e=this.options,i=t.pointers.length===e.pointers,n=t.distance<e.threshold,r=t.deltaTime>e.time;if(this._input=t,!n||!i||t.eventType&(It|zt)&&!r)this.reset();else if(t.eventType&Rt)this.reset(),this._timer=h(function(){this.state=we,this.tryEmit()},e.time,this);else if(t.eventType&It)return we;return Me},reset:function(){clearTimeout(this._timer)},emit:function(t){this.state===we&&(t&&t.eventType&It?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=wt(),this.manager.emit(this.options.event,this._input)))}}),c(ht,rt,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[fe]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||this.state&ge)}}),c(lt,rt,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:Bt|Ut,pointers:1},getTouchAction:function(){return st.prototype.getTouchAction.call(this)},attrTest:function(t){var e,i=this.options.direction;return i&(Bt|Ut)?e=t.overallVelocity:i&Bt?e=t.overallVelocityX:i&Ut&&(e=t.overallVelocityY),this._super.attrTest.call(this,t)&&i&t.offsetDirection&&t.distance>this.options.threshold&&t.maxPointers==this.options.pointers&&xt(e)>this.options.velocity&&t.eventType&It},emit:function(t){var e=it(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)}}),c(ut,tt,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return[ce]},process:function(t){var e=this.options,i=t.pointers.length===e.pointers,n=t.distance<e.threshold,r=t.deltaTime<e.time;if(this.reset(),t.eventType&Rt&&0===this.count)return this.failTimeout();if(n&&r&&i){if(t.eventType!=It)return this.failTimeout();var s=!this.pTime||t.timeStamp-this.pTime<e.interval,o=!this.pCenter||z(this.pCenter,t.center)<e.posThreshold;this.pTime=t.timeStamp,this.pCenter=t.center,o&&s?this.count+=1:this.count=1,this._input=t;var a=this.count%e.taps;if(0===a)return this.hasRequireFailures()?(this._timer=h(function(){this.state=we,this.tryEmit()},e.interval,this),ge):we}return Me},failTimeout:function(){return this._timer=h(function(){this.state=Me},this.options.interval,this),Me},reset:function(){clearTimeout(this._timer)},emit:function(){this.state==we&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}}),dt.VERSION="2.0.7",dt.defaults={domEvents:!1,touchAction:ue,enable:!0,inputTarget:null,inputClass:null,preset:[[ht,{enable:!1}],[ot,{enable:!1},["rotate"]],[lt,{direction:Bt}],[st,{direction:Bt},["swipe"]],[ut],[ut,{event:"doubletap",taps:2},["tap"]],[at]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}};var Se=1,Te=2;ct.prototype={set:function(t){return mt(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},stop:function(t){this.session.stopped=t?Te:Se},recognize:function(t){var e=this.session;if(!e.stopped){this.touchAction.preventDefaults(t);var i,n=this.recognizers,r=e.curRecognizer;(!r||r&&r.state&we)&&(r=e.curRecognizer=null);for(var s=0;s<n.length;)i=n[s],e.stopped===Te||r&&i!=r&&!i.canRecognizeWith(r)?i.reset():i.recognize(t),!r&&i.state&(ge|_e|xe)&&(r=e.curRecognizer=i),s++}},get:function(t){if(t instanceof tt)return t;for(var e=this.recognizers,i=0;i<e.length;i++)if(e[i].options.event==t)return e[i];return null},add:function(t){if(l(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},remove:function(t){if(l(t,"remove",this))return this;if(t=this.get(t)){var e=this.recognizers,i=w(e,t);i!==-1&&(e.splice(i,1),this.touchAction.update())}return this},on:function(t,e){if(t!==a&&e!==a){var i=this.handlers;return u(x(t),function(t){i[t]=i[t]||[],i[t].push(e)}),this}},off:function(t,e){if(t!==a){var i=this.handlers;return u(x(t),function(t){e?i[t]&&i[t].splice(w(i[t],e),1):delete i[t]}),this}},emit:function(t,e){this.options.domEvents&&pt(t,e);var i=this.handlers[t]&&this.handlers[t].slice();if(i&&i.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var n=0;n<i.length;)i[n](e),n++}},destroy:function(){this.element&&ft(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}},mt(dt,{INPUT_START:Rt,INPUT_MOVE:At,INPUT_END:It,INPUT_CANCEL:zt,STATE_POSSIBLE:ye,STATE_BEGAN:ge,STATE_CHANGED:_e,STATE_ENDED:xe,STATE_RECOGNIZED:we,STATE_CANCELLED:be,STATE_FAILED:Me,DIRECTION_NONE:Wt,DIRECTION_LEFT:Nt,DIRECTION_RIGHT:Ft,DIRECTION_UP:Vt,DIRECTION_DOWN:jt,DIRECTION_HORIZONTAL:Bt,DIRECTION_VERTICAL:Ut,DIRECTION_ALL:Ht,Manager:ct,Input:k,TouchAction:$,TouchInput:H,MouseInput:V,PointerEventInput:j,TouchMouseInput:X,SingleTouchInput:B,Recognizer:tt,AttrRecognizer:rt,Tap:ut,Pan:st,Swipe:lt,Pinch:ot,Rotate:ht,Press:at,on:v,off:y,each:u,merge:Mt,extend:bt,assign:mt,inherit:c,bindFn:f,prefixed:S});var De="undefined"!=typeof r?r:"undefined"!=typeof self?self:{};De.Hammer=dt,n=function(){return dt}.call(e,i,e,t),!(n!==a&&(t.exports=n))}(window,document,"Hammer")},function(t,e,i){var n,r,s;!function(i,o){r=[],n=o,s="function"==typeof n?n.apply(e,r):n,!(void 0!==s&&(t.exports=s))}(this,function(){function t(t){var e,i=t&&t.preventDefault||!1,n=t&&t.container||window,r={},s={keydown:{},keyup:{}},o={};for(e=97;e<=122;e++)o[String.fromCharCode(e)]={code:65+(e-97),shift:!1};for(e=65;e<=90;e++)o[String.fromCharCode(e)]={code:e,shift:!0};for(e=0;e<=9;e++)o[""+e]={code:48+e,shift:!1};for(e=1;e<=12;e++)o["F"+e]={code:111+e,shift:!1};for(e=0;e<=9;e++)o["num"+e]={code:96+e,shift:!1};o["num*"]={code:106,shift:!1},o["num+"]={code:107,shift:!1},o["num-"]={code:109,shift:!1},o["num/"]={code:111,shift:!1},o["num."]={code:110,shift:!1},o.left={code:37,shift:!1},o.up={code:38,shift:!1},o.right={code:39,shift:!1},o.down={code:40,shift:!1},o.space={code:32,shift:!1},o.enter={code:13,shift:!1},o.shift={code:16,shift:void 0},o.esc={code:27,shift:!1},o.backspace={code:8,shift:!1},o.tab={code:9,shift:!1},o.ctrl={code:17,shift:!1},o.alt={code:18,shift:!1},o["delete"]={code:46,shift:!1},o.pageup={code:33,shift:!1},o.pagedown={code:34,shift:!1},o["="]={code:187,shift:!1},o["-"]={code:189,shift:!1},o["]"]={code:221,shift:!1},o["["]={code:219,shift:!1};var a=function(t){l(t,"keydown")},h=function(t){l(t,"keyup")},l=function(t,e){if(void 0!==s[e][t.keyCode]){for(var n=s[e][t.keyCode],r=0;r<n.length;r++)void 0===n[r].shift?n[r].fn(t):1==n[r].shift&&1==t.shiftKey?n[r].fn(t):0==n[r].shift&&0==t.shiftKey&&n[r].fn(t);1==i&&t.preventDefault()}};return r.bind=function(t,e,i){if(void 0===i&&(i="keydown"),void 0===o[t])throw new Error("unsupported key: "+t);void 0===s[i][o[t].code]&&(s[i][o[t].code]=[]),s[i][o[t].code].push({fn:e,shift:o[t].shift})},r.bindAll=function(t,e){void 0===e&&(e="keydown");for(var i in o)o.hasOwnProperty(i)&&r.bind(i,t,e)},r.getKey=function(t){for(var e in o)if(o.hasOwnProperty(e)){if(1==t.shiftKey&&1==o[e].shift&&t.keyCode==o[e].code)return e;if(0==t.shiftKey&&0==o[e].shift&&t.keyCode==o[e].code)return e;if(t.keyCode==o[e].code&&"shift"==e)return e}return"unknown key, currently not supported"},r.unbind=function(t,e,i){if(void 0===i&&(i="keydown"),void 0===o[t])throw new Error("unsupported key: "+t);if(void 0!==e){var n=[],r=s[i][o[t].code];if(void 0!==r)for(var a=0;a<r.length;a++)r[a].fn==e&&r[a].shift==o[t].shift||n.push(s[i][o[t].code][a]);s[i][o[t].code]=n}else s[i][o[t].code]=[]},r.reset=function(){s={keydown:{},keyup:{}}},r.destroy=function(){s={keydown:{},keyup:{}},n.removeEventListener("keydown",a,!0),n.removeEventListener("keyup",h,!0)},n.addEventListener("keydown",a,!0),n.addEventListener("keyup",h,!0),r}return t})}])});