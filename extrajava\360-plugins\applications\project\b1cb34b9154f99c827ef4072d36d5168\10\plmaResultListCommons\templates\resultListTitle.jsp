<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>

<%-- Default result list view title template --%>

<render:import varFeeds="feeds" varWidget="widget"/>
<render:import parameters="viewWidget,viewIcon" ignore="true"/>

<span class="title">
    <c:choose>
        <c:when test="${plma:hasParam(viewWidget, 'title')}">
            <string:eval string="${plma:getStringParam(viewWidget, 'title','' )}" feeds="${feeds}"/>
        </c:when>
        <c:otherwise>
            <c:if test="${not empty viewIcon}">
                <span class="fonticon ${viewIcon}"></span>
            </c:if>
            <%-- Num results --%>
            <search:getPaginationInfos varStart="startIndex" varEnd="endIndex" varTotal="totalResults" feeds="${feeds}"/>
            <c:choose>
                <c:when test="${totalResults > 0}">
                    <i18n:message code="hitcustom.title" arguments="${totalResults}"/>
                </c:when>
                <c:otherwise>
                    <i18n:message code="hitcustom.no_result"/>
                </c:otherwise>
            </c:choose>
        </c:otherwise>
    </c:choose>
</span>