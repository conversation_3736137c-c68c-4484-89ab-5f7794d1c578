(function (window) {

	var $templates = $('.alertingTemplates');
	var $tab = $templates.find('.preference-tab.preference-alerts-tab').clone();
	var $container = $templates.find('.preference-container.preference-alerts-container').clone();
	var $liOwned = $templates.find('.item.owned').clone();
	var $liSubscribed = $templates.find('.item.subscribed').clone();

	function getListItemTitle(form, owned) {
		if (owned) {
			return form.options.data.title;
		} else {
			return Alerting.getMessage('alerting.tab.alertTitle')
				.replace('{0}', form.options.data.title)
				.replace('{1}', form.options.data.owner.mail);
		}
	}

	function Alerting(options) {
		this.options = options;
		this.alerts = [];
		this.forms = [];
		this.$container = $container.clone();
		this.init();
	}

	Alerting.ALERT_ID = 'alertId';
	Alerting.SUBSCRIPTION_ID = 'subscriptionId';

	Alerting.getMessage = function (code) {
		return mashupI18N.get('alerting', code);
	};

	Alerting.prototype.init = function () {
		this.addTabToPreferences();
	};

	Alerting.prototype.buildForms = function (alerts) {
		return alerts.map(function (alert) {
			return new AlertingFormBuilder()
				.withType(AlertingForm.TYPE_EDIT)
				.withChartId(alert.chartID)
				.withPageName(alert.pageName)
				.withId(alert.id)
				.withCondition(alert.condition)
				.withEnabled(alert.enabled)
				.withStartDate(alert.startDate)
				.withEndDate(alert.endDate)
				.withOwner(alert.owner)
				.withSubscriptions(alert.subscriptions)
				.withThresholds(alert.thresholds)
				.withTitle(alert.title)
				.withOnCancel(this.onCancel.bind(this))
				.withUser(this.options.user)
				.withOnSubmitSuccess(function () {
					$.notify('The alert has been successfully updated', 'success');
				})
				.withOnSubmitFail(function () {
					$.notify('An error occurred while processing your request', 'error');
				})
				.build();
		}, this);
	};

	Alerting.prototype.onCancel = function () {
		this.deselect();
	};

	Alerting.prototype.buildAlertList = function (forms) {
		var alertList = [];
		if (forms.owned.length > 0) {
			alertList.push(this.buildAlertListWith(forms.owned, true));
		}
		if (forms.subscribed.length > 0) {
			alertList.push(this.buildAlertListWith(forms.subscribed,false));
		}
		return alertList;
	};

	Alerting.prototype.buildAlertListWith = function (forms, owned) {
		var $ul, $liTemplate;
		if (owned) {
			$ul = $('<ul class="owned_alerts"><h3>My alerts</h3></ul>');
			$liTemplate = $liOwned;
		} else {
			$ul = $('<ul class="subscribed_alerts"><h3>My subscriptions</h3></ul>');
			$liTemplate = $liSubscribed;
		}
		$ul.append(forms.map(function (form) {
			var $li = $liTemplate.clone();
			// get subscription for the current user
			var subscription = _.find(form.options.data.subscriptions, {subscribedUserID: this.options.user}) || {};
			$li
				.data(Alerting.ALERT_ID, form.options.data.id)
				// get subscription id for the current user
				.data(Alerting.SUBSCRIPTION_ID, subscription.id)
				.find('.hit-name')
					.text(getListItemTitle(form, owned))
				.end()
				.find('.toggle')
					.prop('title', subscription.subscribed
						? Alerting.getMessage('alerting.tab.alert.unsubscribe')
						: Alerting.getMessage('alerting.tab.alert.subscribe')
					)
					.find('input[name="enable"]')
						.prop('id', form.options.data.id)
						.prop('checked', subscription.subscribed)
					.end()
					.find('label')
						.prop('for', form.options.data.id);
			return $li;
		}.bind(this)));
		return $ul;
	};

	Alerting.prototype.displayAlerts = function (forms) {
		this.$container.find('.alert_list').empty().append(this.buildAlertList(forms));
	};

	Alerting.prototype.deselect = function () {
		this.$container
			.find('.alert_details')
				.addClass('hidden')
			.end()
			.find('.alert_list')
				.removeClass('wrapped')
			.end()
			.find('li.item')
				.removeClass('selected');
	};

	Alerting.prototype.displayAlertsInPreferences = function () {
		this.deselect();
		AlertingClient.getAlerts()
			.then(function (data) {
				this.alerts = data;
				this.forms = {
					owned: this.buildForms(data.owned),
					subscribed: this.buildForms(data.subscribed)
				};
				return this.forms;
			}.bind(this), function () {
				console.error('Can not retrieve alerts');
			})
			.then(function (forms) {
				this.displayAlerts(forms);
			}.bind(this));
	};

	Alerting.prototype.addTabToPreferences = function () {
		if (window.Preferences) {
			Preferences.getInitCallbacks()
				.add(function (preferences) {
					preferences.addTab({
						id: 'alerts',
						onInit: $.noop,
						tab: $tab,
						container: this.$container
					});
					this.bindEvents(this.$container);
					this.displayAlertsInPreferences();
				}.bind(this))
		}
	};

	Alerting.prototype.findAlertById = function (id) {
		return this.alerts.owned.concat(this.alerts.subscribed)
			.filter(function (alert) {
				return alert.id === id;
			})[0];
	};

	Alerting.prototype.findFormById = function (id) {
		return this.forms.owned.concat(this.forms.subscribed)
			.filter(function (form) {
				return form.options.data.id === id;
			})[0];
	};

	Alerting.prototype.bindEvents = function ($container) {
		$container
			.on('click', '.refresh-alerts', function (e) {
				this.displayAlertsInPreferences();
			}.bind(this))
			// label and input[name="enable"]
			.on('click', '.toggle', function (e) {
				e.stopPropagation();
			})
			.on('change', 'input[name="enable"]', function (e) {
				var $target = $(e.currentTarget);
				var subscriptionId = $target.closest('li.item').data(Alerting.SUBSCRIPTION_ID);
				var value = $target.prop('checked');
				AlertingClient.updateSubscription({
					subscriptionId: subscriptionId,
					is_subscribed: value
				}).then(function () {
					$target.parent().prop('title', value
						? Alerting.getMessage('alerting.tab.alert.unsubscribe')
						: Alerting.getMessage('alerting.tab.alert.subscribe')
					);
				}, function () {
					$.notify('update error', 'error')
				});
			}.bind(this))
			.on('click', '.delete-alert', function (e) {
				e.stopPropagation();
				var alertId = $(e.currentTarget).closest('li.item').data('alertId');
				var alert = this.findAlertById(alertId);
				if (confirm("Delete alert ?")) {
					AlertingClient
						.deleteAlert(alert.id)
						.then(function (status) {
							$.notify('"' + alert.title + '" has been successfully deleted', "success");
							this.displayAlertsInPreferences();
						}.bind(this), function (error) {
							$.notify('creation error : ' + error, "error");
						});
				}
			}.bind(this))
			.on('click', 'li.item', function (e) {
				var $li = $(e.currentTarget);
				$li.toggleClass('selected');
				$container.find('li.item').not($li).removeClass('selected');
				var alertId = $li.data('alertId');
				var form = this.findFormById(alertId);
				var $form = (form && form.to$()) || $();
				$container
					.find('.alert_details')
						.children()
							.detach() // remove the form without removing the event handlers
						.end()
						.append($form)
						.toggleClass('hidden', !$li.hasClass('selected'))
					.end()
					.find('.alert_list')
						.toggleClass('wrapped', $li.hasClass('selected'));
			}.bind(this))
		;
	};

	window.Alerting = Alerting;
})(window);