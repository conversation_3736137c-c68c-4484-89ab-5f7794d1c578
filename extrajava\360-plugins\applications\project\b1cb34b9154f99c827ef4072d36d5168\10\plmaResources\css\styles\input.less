@import "variables.less";

input.form-control, textarea.form-control, select.form-control {
  display: block;
  padding: 6px 12px;
  width: 100%;
  height: 24px;
  border: 1px solid @cblock-border-alt;
  border-radius: 4px;
  background-color: #fff;
  background-image: none;
  color: @cblock;
  font-size: @m-font;
  line-height: 1.42857;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

.form-control:focus {
  outline: 0;
  border-color: @clink;
}

.form-control::-moz-placeholder {
  color: @ctext-weak;
  opacity: 1;
}

.form-control:-ms-input-placeholder {
  color: @ctext-weak;
}

.form-control::-webkit-input-placeholder {
  color: @ctext-weak;
}

.form-control.placeholder {
  color: @ctext-weak;
}

textarea.form-control {
  height: auto;
  padding-top: 10px;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
  background-color: #F4F5F6;
  cursor: not-allowed;
  user-select: none;
  -ms-user-select: none;
}

/* TOGGLES ====================================================================== */
/* COMMON ------------------ */
.toggle, .toggle-inline {
  :before, :after {
    box-sizing: border-box;
  }
}

.toggle {
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
  padding-left: 20px;
  min-height: 25px;
}

.toggle .control-label {
  display: inline-block;
  font-weight: normal;
  cursor: pointer; }

.toggle input[type='radio'], .toggle input[type='checkbox'] {
  float: left;
  margin-left: -20px; }

.toggle + .toggle {
  margin-top: -5px; }

/** Radios and checkboxes on same line */
.toggle-inline {
  display: inline-block;
  margin-top: 0;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  margin-right: 10px; }

.toggle-inline input[type='radio'], .toggle-inline input[type='checkbox'] {
  float: left;
  margin-left: -20px; }

.toggle-inline + .toggle-inline {
  margin-top: 0; }

.toggle-primary [type="radio"]:not(:checked), .toggle-primary [type="radio"]:checked, .toggle-primary [type="checkbox"]:not(:checked), .toggle-primary [type="checkbox"]:checked, .toggle-switch [type="checkbox"]:not(:checked), .toggle-switch [type="checkbox"]:checked {
  position: absolute;
  left: -9999px; }

.toggle-primary [type="radio"]:not(:checked) + .control-label, .toggle-primary [type="radio"]:checked + .control-label, .toggle-primary [type="checkbox"]:not(:checked) + .control-label, .toggle-primary [type="checkbox"]:checked + .control-label, .toggle-switch [type="checkbox"]:not(:checked) + .control-label, .toggle-switch [type="checkbox"]:checked + .control-label {
  position: relative;
  cursor: pointer;
  padding-left: 5px; }

.toggle-primary [type="radio"]:not(:checked) + .control-label:before, .toggle-primary [type="radio"]:checked + .control-label:before, .toggle-primary [type="radio"]:not(:checked) + .control-label:after, .toggle-primary [type="radio"]:checked + .control-label:after, .toggle-primary [type="checkbox"]:not(:checked) + .control-label:before, .toggle-primary [type="checkbox"]:checked + .control-label:before, .toggle-primary [type="checkbox"]:not(:checked) + .control-label:after, .toggle-primary [type="checkbox"]:checked + .control-label:after, .toggle-switch [type="checkbox"]:not(:checked) + .control-label:before, .toggle-switch [type="checkbox"]:checked + .control-label:before, .toggle-switch [type="checkbox"]:not(:checked) + .control-label:after, .toggle-switch [type="checkbox"]:checked + .control-label:after {
  content: '';
  position: absolute;
}

.toggle-switch [type="checkbox"]:checked + .control-label:after {
  content: '' !important; }

.toggle-primary [type="checkbox"]:focus:not(:checked) + .control-label:before {
  border-color: #78BEFA; }

/* DISABLED ------------------ */
.toggle-primary [type="checkbox"][disabled]:not(:checked) + .control-label:before, .toggle-switch [type="checkbox"][disabled]:not(:checked) + .control-label:before {
  background-color: #F1F1F1;
  border-color: #E1E2E3; }

.toggle-primary [type="checkbox"][disabled]:checked + .control-label:before, .toggle-switch [type="checkbox"][disabled]:checked + .control-label:before {
  background-color: #F1F1F1;
  border-color: #E1E2E3; }

.toggle-switch [type="checkbox"][disabled]:checked + .control-label:before {
  background: #AFD1E7;
  border-color: #AFD1E7; }

.toggle-primary [type="radio"][disabled]:not(:checked) + .control-label:before {
  background-color: #F1F1F1;
  border-color: #E1E2E3; }

.toggle-primary [type="radio"][disabled]:checked + .control-label:before {
  border: none;
  opacity: 0.4; }

.toggle [type="radio"][disabled] + .control-label, .toggle-primary [type="radio"][disabled] + .control-label {
  color: #B4B6Ba; }

.toggle-primary [type="checkbox"][disabled] + .control-label {
  color: #B4B6Ba; }

.toggle [type="radio"][disabled] + .control-label, .toggle [type="checkbox"][disabled] + .control-label {
  cursor: not-allowed; }

/* RADIO ==================================================================== */
.toggle-primary [type="radio"]:not(:checked) + .control-label:before, .toggle-primary [type="radio"]:checked + .control-label:before {
  top: 0;
  left: -20px;
  width: 16px;
  height: 16px;
  border: 1px solid #ccc;
  border-radius: 30px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); }

.toggle-primary [type="radio"]:checked + .control-label:after {
  left: -16px;
  top: 4px;
  width: 8px;
  height: 8px;
  background: #fff;
  border-radius: 30px; }

/* UNCHECKED ------------------ */
/* Unchecked - Normal */
.toggle-primary [type="radio"]:not(:checked) + .control-label:before {
  background-color: #F1F1F1;
  border-color: #B4B6Ba; }

/* Unchecked - Hover */
.toggle-primary [type="radio"]:hover:not(:checked):not([disabled]) + .control-label:before {
  background-color: #E2E4E3;
  border-color: #77797C; }

/* Unchecked - Focus */
.toggle-primary [type="radio"]:focus:not(:checked):not([disabled]) + .control-label:before {
  background-color: #E2E4E3;
  border-color: #368EC4; }

/* Unchecked - Active */
.toggle-primary [type="radio"]:active:not(:checked):not([disabled]) + .control-label:before {
  background-color: #D1D4D4;
  border-color: #3D3D3D; }

/* CHECKED ------------------ */
/* Checked - Normal */
.toggle-primary [type="radio"]:checked + .control-label:before {
  border-color: #368EC4;
  background: #42A2DA; }

/* Checked - Hover */
.toggle-primary [type="radio"]:hover:checked:not([disabled]) + .control-label:before {
  background-color: #368EC4;
  border-color: #003C5A; }

/* Checked - Active */
.toggle-primary [type="radio"]:active:checked:not([disabled]) + .control-label:before {
  background-color: #005686;
  border-color: #003C5A; }

/* Checked - Focus */
.toggle-primary [type="radio"]:focus:checked:not([disabled]) + .control-label:before {
  background-color: #42A2DA;
  border-color: #005686; }

/* SMALL / MOUSE ------------------ */
.toggle-sm.toggle-primary [type="radio"] + .control-label {
  padding-left: 1px;
  font-size: 12px; }

.toggle-sm.toggle-primary [type="radio"] + .control-label:before {
  width: 14px;
  height: 14px; }

.toggle-sm.toggle-primary [type="radio"]:checked + .control-label:after {
  width: 6px;
  height: 6px; }

/* LARGE / TOUCH ------------------ */
.toggle-lg.toggle-primary [type="radio"] + .control-label {
  padding-left: 11px;
  font-size: 18px; }

.toggle-lg.toggle-primary [type="radio"] + .control-label:before {
  top: -1px;
  width: 22px;
  height: 22px; }

.toggle-lg.toggle-primary [type="radio"]:checked + .control-label:after {
  top: 4px;
  left: -15px;
  width: 12px;
  height: 12px; }

/* CHECKBOX ==================================================================== */
.toggle-primary [type="checkbox"]:not(:checked) + .control-label:before, .toggle-primary [type="checkbox"]:checked + .control-label:before {
  top: 0;
  left: -20px;
  width: 16px;
  height: 16px;
  border: 1px solid #ccc;
  border-radius: 2px;
}

.toggle-primary [type="checkbox"]:checked + .control-label:after {
  content: '\e014';
  font-family: entypo;
  left: -18px;
  top: -1px;
  width: 12px;
  height: 12px;
  color: #fff;
  font-size: 14px; }

/* UNCHECKED ------------------ */
/* Unchecked - Normal */
.toggle-primary [type="checkbox"]:not(:checked) + .control-label:before {
  background-color: #F1F1F1;
  border-color: #B4B6Ba;
}

/* Unchecked - Hover */
.toggle-primary [type="checkbox"]:hover:not(:checked):not([disabled]) + .control-label:before {
  background-color: #E2E4E3;
  border-color: #77797C; }

/* Unchecked - Focus */
.toggle-primary [type="checkbox"]:focus:not(:checked):not([disabled]) + .control-label:before {
  background-color: #E2E4E3;
  border-color: #368EC4; }

/* Unchecked - Active */
.toggle-primary [type="checkbox"]:active:not(:checked):not([disabled]) + .control-label:before {
  background-color: #D1D4D4;
  border-color: #3D3D3D; }

/* CHECKED ------------------ */
/* Checked - Normal */
.toggle-primary [type="checkbox"]:checked + .control-label:before {
  border-color: #368EC4;
  background: #42A2DA; }

/* Checked - Hover */
.toggle-primary [type="checkbox"]:hover:checked:not([disabled]) + .control-label:before {
  background-color: #368EC4;
  border-color: #003C5A; }

/* Checked - Active */
.toggle-primary [type="checkbox"]:active:checked:not([disabled]) + .control-label:before {
  background-color: #005686;
  border-color: #003C5A; }

/* Checked - Focus */
.toggle-primary [type="checkbox"]:focus:checked:not([disabled]) + .control-label:before {
  background-color: #42A2DA;
  border-color: #005686; }

/* SMALL / MOUSE ------------------ */
.toggle-sm.toggle-primary [type="checkbox"] + .control-label {
  padding-left: 1px;
  font-size: 12px; }

.toggle-sm.toggle-primary [type="checkbox"] + .control-label:before {
  width: 14px;
  height: 14px; }

.toggle-sm.toggle-primary [type="checkbox"]:checked + .control-label:after {
  font-size: 12px;
  left: -18px;
  top: 0px; }

/* LARGE / TOUCH ------------------ */
.toggle-lg.toggle-primary [type="checkbox"] + .control-label {
  padding-left: 11px;
  font-size: 18px; }

.toggle-lg.toggle-primary [type="checkbox"] + .control-label:before {
  top: -1px;
  width: 22px;
  height: 22px; }

.toggle-lg.toggle-primary [type="checkbox"]:checked + .control-label:after {
  top: -3px;
  left: -18px;
  font-size: 20px; }

/* SWITCH ==================================================================== */
.toggle-switch [type="checkbox"]:not(:checked) + .control-label, .toggle-switch [type="checkbox"]:checked + .control-label {
  padding: 2px 0 0 34px; }

.toggle-switch [type="checkbox"]:not(:checked) + .control-label:before, .toggle-switch [type="checkbox"]:checked + .control-label:before {
  top: 0;
  left: -20px;
  width: 44px;
  height: 22px;
  border-radius: 11px;
  border: 2px solid; }

.toggle-switch [type="checkbox"]:not(:checked) + .control-label:after, .toggle-switch [type="checkbox"]:checked + .control-label:after {
  left: 4px;
  top: 2px;
  width: 18px;
  height: 18px;
  border-radius: 18px;
  background: #fff;
  -webkit-transition: width 0.1s ease-in, left 0.1s ease-in;
  transition: width 0.1s ease-in, left 0.1s ease-in; }

.toggle-switch [type="checkbox"]:not(:checked) + .control-label:after {
  left: -18px; }

.toggle-switch [type="checkbox"]:not(:checked):not([disabled]) + .control-label:active:after, .toggle-switch [type="checkbox"]:checked:not([disabled]) + .control-label:active:after {
  width: 21px; }

.toggle-switch [type="checkbox"]:checked:not([disabled]) + .control-label:active:after {
  left: 1px; }

/* UNCHECKED ------------------ */
/* Unchecked - Normal */
.toggle-switch [type="checkbox"]:not(:checked):not([disabled]) + .control-label:before {
  background: #F1F1F1;
  border-color: #B4B6Ba; }

/* Unchecked - Hover */
.toggle-switch [type="checkbox"]:hover:not(:checked):not([disabled]) + .control-label:before {
  background: #E2E4E3;
  border-color: #77797C; }

/* Unchecked - Active */
.toggle-switch [type="checkbox"]:active:not(:checked):not([disabled]) + .control-label:before {
  background: #D1D4D4;
  border-color: #3D3D3D; }

/* CHECKED ------------------ */
/* Checked - Normal */
.toggle-switch [type="checkbox"]:checked:not([disabled]) + .control-label:before {
  background: #42A2DA;
  border-color: #368EC4; }

/* Checked - Hover */
.toggle-switch [type="checkbox"]:hover:checked:not([disabled]) + .control-label:before {
  background: #368EC4;
  border-color: #003C5A; }

/* Checked - Active */
.toggle-switch [type="checkbox"]:active:checked:not([disabled]) + .control-label:before {
  background: #005686;
  border-color: #003C5A; }

/* Checked - Focus */
.toggle-switch [type="checkbox"]:focus:checked:not([disabled]) + .control-label:before {
  border-color: #005686; }

/* Unchecked - Focus */
.toggle-switch [type="checkbox"]:focus:not(:checked):not([disabled]) + .control-label:before {
  background: #E2E4E3;
  border-color: #368EC4; }

/* LARGE / TOUCH ------------------ */
.toggle-lg.toggle-switch [type="checkbox"]:not(:checked) + .control-label, .toggle-lg.toggle-switch [type="checkbox"]:checked + .control-label {
  padding: 6px 0 0 50px; }

.toggle-lg.toggle-switch [type="checkbox"]:not(:checked) + .control-label:before, .toggle-lg.toggle-switch [type="checkbox"]:checked + .control-label:before {
  width: 60px;
  height: 30px;
  border-radius: 30px; }

.toggle-lg.toggle-switch [type="checkbox"]:not(:checked) + .control-label:after, .toggle-lg.toggle-switch [type="checkbox"]:checked + .control-label:after {
  left: 12px;
  top: 2px;
  width: 26px;
  height: 26px;
  border-radius: 30px; }

.toggle-lg.toggle-switch [type="checkbox"]:not(:checked) + .control-label:after {
  left: -18px; }

.toggle-lg.toggle-switch [type="checkbox"]:not(:checked):not([disabled]) + .control-label:active:after, .toggle-lg.toggle-switch [type="checkbox"]:checked:not([disabled]) + .control-label:active:after {
  width: 36px; }

.toggle-lg.toggle-switch [type="checkbox"]:checked:not([disabled]) + .control-label:active:after {
  left: 3px; }

.toggle-lg + .toggle {
  margin-top: 5px; }

/* SMALL / MOUSE ------------------ */
.toggle-sm.toggle-switch [type="checkbox"]:not(:checked) + .control-label, .toggle-sm.toggle-switch [type="checkbox"]:checked + .control-label {
  padding-left: 28px; }

.toggle-sm.toggle-switch [type="checkbox"]:not(:checked) + .control-label:before, .toggle-sm.toggle-switch [type="checkbox"]:checked + .control-label:before {
  width: 38px;
  height: 18px;
  border-radius: 10px; }

.toggle-sm.toggle-switch [type="checkbox"]:not(:checked) + .control-label:after, .toggle-sm.toggle-switch [type="checkbox"]:checked + .control-label:after {
  left: 2px;
  top: 2px;
  width: 14px;
  height: 14px;
  border-radius: 14px; }

.toggle-sm.toggle-switch [type="checkbox"]:not(:checked) + .control-label:after {
  left: -18px; }

.toggle-sm.toggle-switch [type="checkbox"]:not(:checked):not([disabled]) + .control-label:active:after, .toggle-sm.toggle-switch [type="checkbox"]:checked:not([disabled]) + .control-label:active:after {
  width: 16px; }

.toggle-sm.toggle-switch [type="checkbox"]:checked:not([disabled]) + .control-label:active:after {
  left: 0px; }

/* ANIMATION ------------------ */
.toggle-switch [type="checkbox"]:not(:checked):not([disabled]) + .control-label:before {
  -webkit-transition: box-shadow 0.1s ease-in;
  transition: box-shadow 0.1s ease-in; }