var ChartboardToolbar = function (chartboard) {
	this.chartboard = chartboard;
};

ChartboardToolbar.prototype.initEditToolbar = function () {
	/* Toolbar div creation */
	this.toolbar = $('<div class="chartboard-edit-toolbar"></div>');
	var pageBlock = $('<div class="toolbar-elem-container toolbar-page-container"></div>');
	pageBlock.append($('<div class="toolbar-main-icon toolbar-page-icon fonticon fonticon-doc"></div>'));
	var pageDropdown = $('<div class="toolbar-dropdown-container toolbar-page-dropdown" title="' + Chartboard.getMessage('chartboard.page.tooltip') + '"></div>');
	pageDropdown.append($('<span class="value" ></span>'));
	if (this.chartboard.allowEditPages) {
		pageDropdown.append($('<span class="toolbar-page-dropdown-icon toolbar-dropdown-icon fonticon fonticon-down-open"></span>'));
	} else {
		pageDropdown.addClass('no-page-modification');
	}
	pageBlock.append(pageDropdown);
	this.toolbar.append(pageBlock);

	var buttonContainer = $('<div class="toolbar-button-container"></div>');
	var resetButton = $('<div class="toolbar-button toolbar-reset-button fonticon fonticon-reset" title="' + Chartboard.getMessage('chartboard.reset.tooltip') + '"></div>');
	var saveButton = $('<div class="toolbar-button toolbar-save-button fonticon fonticon-check" title="' + Chartboard.getMessage('chartboard.save.tooltip') + '"></div>');
	var cancelButton = $('<div class="toolbar-button toolbar-cancel-button fonticon fonticon-cancel" title="' + Chartboard.getMessage('chartboard.cancel.tooltip') + '"></div>');
	if (this.chartboard.page && this.chartboard.page.layout) {
		buttonContainer.append(resetButton);
		buttonContainer.addClass('with-three-buttons')
	}
	buttonContainer.append(cancelButton);
	buttonContainer.append(saveButton);
	this.toolbar.append(buttonContainer);

	/* Add button actions */
	/* Open popup to create/modify page */
	if (this.chartboard.allowEditPages) {
		pageDropdown.on('click', $.proxy(function (e) {
			this.openCreatePagePopup();
		}, this));
	}

	/* Reset layout */
	resetButton.on('click', $.proxy(function (e) {
		this.chartboard.deleteLayout();
	}, this));
	/* Save layout */
	saveButton.on('click', $.proxy(function (e) {
		this.savePageInfo();
		this.chartboard.saveEdit(this.isDuplicate);
		/* Verify that the storage layout or page did not change
		if (this.chartboard.allowShare) {
			this.chartboard.hasPageChanges($.proxy(function () {
				// Display message to validate the save override
				this.displayConfirmSave();
			}, this), $.proxy(function () {
				this.chartboard.hasLayoutChanges($.proxy(function () {
					this.displayConfirmSave();
				}, this), $.proxy(function () {
					this.chartboard.saveEdit();
				}, this));
			}, this));
		} else {
			this.chartboard.saveEdit();
		}
		*/
	}, this));
	/* Cancel modification */
	cancelButton.on('click', $.proxy(function (e) {
		this.chartboard.cancelEdit();
	}, this));

	/* Add toolbar to page */
	if (this.chartboard.options.editToolbarLocationSelector) {
		$(this.chartboard.options.editToolbarLocationSelector).prepend(this.toolbar);
	} else {
		this.chartboard.widget.prepend(this.toolbar);
	}

	this.initCreatePagePopup();

	this.updatePageLabel();
};

ChartboardToolbar.prototype.initCreatePagePopup = function () {
	var icon = this.chartboard.getPageIcon();
	var spanIcon = '<span class="input input-icon ' + this.chartboard.getPageIcon() + '"></span>';
	if (!icon || icon === '') {
		spanIcon = '<span class="input input-icon">' + Chartboard.getMessage('chartboard.toolbar.page.noicon') + '</span>';
	}

	/* Open an popup to modify layout label and description or create a new one */
	var container = $('<div class="chartboard-popup chartboard-create-page-popup"></div>');
	container.append($('<div class="input-container label-container"><span class="label fonticon fonticon-text"></span><input type="text" class="input input-label" maxlength="100" value="" placeholder="' + Chartboard.getMessage('chartboard.toolbar.layout.label') + '"/></div>'));
	container.append($('<div class="input-container description-container"><span class="label fonticon fonticon-info"></span><textarea cols="40" rows="4" maxlength="300" placeholder="' + Chartboard.getMessage('chartboard.toolbar.layout.description') + '" class="input input-description">' + this.chartboard.getPageDescription() + '</textarea></div>'));
	container.append($('<div class="input-container icon-container"><span class="label fonticon fonticon-picture"></span>' + spanIcon + '</div>'));
	var saveContainer = $('<div class="chartboard-create-page-save-container"></div>');
	saveContainer.append($('<div class="save-container">OK</div>'));
	container.append(saveContainer);

	container.find('.input-icon').on('click', $.proxy(function () {
		if (this.toolbar.find('.chartboard-create-page-popup .chartboard-icon-list').length < 1) {
			this.initIcons();
		}
		this.toolbar.find('.chartboard-create-page-popup .chartboard-icon-list').toggleClass('hidden');
	}, this));

	container.find('.save-container').on('click', $.proxy(function (e) {
		this.savePageInfo();
	}, this));

	/* Add popup to page */
	this.toolbar.find('.toolbar-page-container').append(container);
	this.updatePageInputLabel();
};

ChartboardToolbar.prototype.savePageInfo = function () {
	var container = this.toolbar.find('.chartboard-create-page-popup');
	if (!container.find('.input-label')[0].value.trim()) {
		container.find('.input-label').addClass('empty');
	} else {
		container.find('.input-label').removeClass('empty');
		if (!this.chartboard.page) {
			this.chartboard.page = {};
			this.chartboard.page.id = this.chartboard.getTemplatePage();
			this.chartboard.page.templatePage = this.chartboard.getTemplatePage();
		}
		if (container.find('.label-container').data('labeli18n') && container.find('.label-container').data('label') && container.find('.label-container').data('label') === container.find('.input-label').val()) {
			this.chartboard.page.label = container.find('.label-container').data('labeli18n');
		} else {
			this.chartboard.page.label = container.find('.input-label').val();
		}
		if (container.find('.description-container').data('descriptioni18n') && container.find('.description-container').data('description') && container.find('.description-container').data('description') === container.find('.input-description').val()) {
			this.chartboard.page.description = container.find('.description-container').data('descriptioni18n');
		} else {
			this.chartboard.page.description = container.find('.input-description').val();
		}
		this.chartboard.page.icon = container.find('.input-icon')[0].getClassList()[2] + ' ' + container.find('.input-icon')[0].getClassList()[3];
		this.toolbar.find('.toolbar-page-container .toolbar-page-dropdown .value').text(container.find('.input-label').val());
		this.chartboard.recentlyAddedOrDeletedWidgets = true;
		this.closeCreatePagePopup();
	}
};

ChartboardToolbar.prototype.initIcons = function () {
	var icons = $(this.chartboard.widget.find('.chartboard-icon-list')[0]).clone();
	icons.addClass('hidden');
	icons.css('display', '');
	icons.find('.chartboard-icon-list-input').on('keyup', function () {
		var searchValue = this.value;
		var iconList = this.parentElement.children;
		for (var i = 1; i < iconList.length; i++) {
			var elemClassList = iconList[i].getClassList();
			if (elemClassList[2].indexOf(searchValue.replace('fonticon ', '')) !== -1) {
				elemClassList.remove('hidden');
			} else {
				elemClassList.add('hidden');
			}
		}
	});
	icons.find('.fonticon-elem').on('click', $.proxy(function (e) {
		var iconInput = this.toolbar.find('.chartboard-create-page-popup .icon-container .input-icon');
		iconInput.removeClass();
		iconInput.addClass('input input-icon ' + e.currentTarget.getClassList()[1] + ' ' + e.currentTarget.getClassList()[2]);
		iconInput.innerHeight = '';
	}, this));
	this.toolbar.find('.chartboard-create-page-popup .icon-container').append(icons);
};

ChartboardToolbar.prototype.updatePageLabel = function () {
	let label = this.chartboard.getPageLabel();
	this.toolbar.find('.toolbar-page-container .toolbar-page-dropdown .value').text(label);
};

ChartboardToolbar.prototype.updatePageInputLabel = function () {
	let label = this.chartboard.getPageLabel();
	this.toolbar.find('.toolbar-page-container .chartboard-create-page-popup .label-container .input-label')[0].value = label;
};

ChartboardToolbar.prototype.openCreatePagePopup = function () {
	if (this.toolbar.find('.chartboard-create-page-popup').hasClass('active')) {
		this.closeCreatePagePopup();
	} else {
		this.closeAllPopup();
		this.toolbar.find('.toolbar-page-dropdown').addClass('active');
		this.toolbar.find('.chartboard-create-page-popup').addClass('active');
	}
};

ChartboardToolbar.prototype.closeCreatePagePopup = function () {
	this.toolbar.find('.toolbar-page-dropdown').removeClass('active');
	this.toolbar.find('.chartboard-create-page-popup').removeClass('active');
};

ChartboardToolbar.prototype.closeAllPopup = function () {
	this.closeCreatePagePopup();
};

ChartboardToolbar.prototype.displayConfirmSave = function () {
	$.notify.defaults({autoHide: false, style: 'plmabutton'});
	$.notify(Chartboard.getMessage('chartboard.share.page.edit'), 'warn');
	$.notify.defaults({autoHide: true, style: 'plma'});
	$('.notifyjs-container .notifyjs-plmabutton-base').on('click', $.proxy(function (e) {
		this.chartboard.saveEdit();
	}, this));
};
