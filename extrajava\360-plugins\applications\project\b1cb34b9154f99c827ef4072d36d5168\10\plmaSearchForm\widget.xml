<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA Search Form" group="PLM Analytics/Search" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>The CloudView standard search form with support for suggest.</Description>

	<Preview>
		<![CDATA[
			<img src="/resources/widgets/plmaSearchForm/images/preview.png" alt="Search Form" />
		]]>
	</Preview>

	<Dependencies>
		<Widget name="formInput" includeCondition="enableSuggest=true" /> <!-- Include jquery.suggest.js -->
		<Widget name="formForm" /> <!-- include all exa/ and searchAction.js -->
		<Widget name="plmaResources" />
	</Dependencies>

	<Includes>
		<Include type="css" path="css/searchForm.less" />
		<Include type="js" path="js/searchSuggest.js" />
	</Includes>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO" />
	<SupportI18N supported="true" />
	
	<OptionsGroup name="General">
		<Option id="inputName" name="Input parameter" arity="ONE">
			<Description>Specifies the Parameter Name associated with the input submitted for the entry box.</Description>
			<Functions>
				<ContextMenu>PageParameters()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="inputSize" name="Input size" arity="ONE">
			<Description>Specifies the width of the query input field (don't forget the unit).</Description>
		</Option>
		<Option id="placeHolder" name="Placeholder" isEvaluated="true">
			<Description>The text to display in the text input if it is empty.</Description>
		</Option>
		<Option id="buttonName" name="Button label" arity="ONE" isEvaluated="true">
			<Description>Specifies the label of the form submit button.</Description>
		</Option>
		<OptionComposite id="action" name="Search actions" arity="ZERO_OR_MANY" glue="##" isEvaluated="true">
			<Option id="label" name="Action's label">
				<Description>Specifies the label that will be displayed above your search form for this action.</Description>
				<Functions>
					<ContextMenu>I18N()</ContextMenu>
				</Functions>
			</Option>
			<Option id="name" name="Action's name">
				<Description>Specifies the name of a page or an absolute URL, corresponding to the form action. If empty, the form will be submitted to the current page.</Description>
				<Functions>
					<ContextMenu>Pages()</ContextMenu>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
		</OptionComposite>
		<Option id="focus" name="Auto focus" arity="ONE">
			<Description>Places the keyboard focus on the query input when the page is loaded.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option arity="ONE" name="Keep parameters" id="keepParameters">
			<Description>Keep the url parameters or not in the new search</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch: ['false'], hideOptions: ['exceptParams']})</Display>
			</Functions>
		</Option>
		<Option id="exceptParams" name="Parameters to delete">
			<Description>Parameters to delete when the url parameters are kept in the new search</Description>	
			<Functions>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="expandedForm" name="Expanded Form" arity="ONE">
			<Description>If true, the search bar will be expanded by default</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Suggest">
		<Description>The Suggest service cannot work for input of type "password" or "hidden".</Description>
		<Option id="enableSuggest" name="Enable suggest" arity="ONE">
			<Description>Enables the suggest service on the search field. You must then select a suggest service - previously defined in the Administration Console - from 'Suggest Name'.</Description>		
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('false',['suggestNames', 'suggestApiAction', 'suggestApiConfig', 'suggestCustomSearchAPIs', 'suggestApiCommand'],[], true, false)</Display>
			</Functions>
		</Option>
		<Option id="suggestNames" name="Suggest Name" arity="MANY">
			<Description>Suggest service name.</Description>
			<Functions>
				<ContextMenu>SuggestNames('suggestApiAction')</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="suggestApiAction" name="Action" arity="ONE">
			<Description>Specifies whether the suggest should use a simple service or a suggest dispatcher which varies depending on the query input. </Description>			
			<Values>
				<Value>dispatcher</Value>
				<Value>service</Value>
			</Values>
		</Option>
		<Option id="suggestApiConfig" name="Config" arity="ZERO_OR_ONE">
			<Description>
				<![CDATA[
					Indicates the name of the default Search API, for example, <code>sapi0</code>.
				]]>
			</Description>
			<Placeholder>sapi0</Placeholder>
			<Functions>
				<ContextMenu>ApiConfig()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				<Check>isSpecified('ONE', 'suggestCustomSearchAPIs')</Check>
			</Functions>
		</Option>
		<Option id="suggestCustomSearchAPIs" name="Search API URL" arity="ZERO_OR_MANY">
			<Description>Defines the URL that will be used by the Search API.</Description>
			<Placeholder>http://HOST:PORT/</Placeholder>
			<Functions>
				<Check>isSpecified('ONE', 'suggestApiConfig')</Check>
			</Functions>
		</Option>
		<Option id="suggestApiCommand" name="API command" arity="ONE">
			<Description>Specifies the suggest API command name that will be appended to the 'Search API URL'.</Description>
			<Placeholder>suggest</Placeholder>
			<Functions>
				<ContextMenu>ApiCommand()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
	</OptionsGroup>


	<DefaultValues>
		<DefaultValue name="inputName">q</DefaultValue>
		<DefaultValue name="inputSize">300px</DefaultValue>
		<DefaultValue name="placeHolder">Type your search...</DefaultValue>
		<DefaultValue name="buttonName">${i18n['search']}</DefaultValue>
		<DefaultValue name="focus">true</DefaultValue>
		<DefaultValue name="keepParameters">false</DefaultValue>
		
		<DefaultValue name="enableSuggest">false</DefaultValue>
		<DefaultValue name="suggestApiAction">service</DefaultValue>
		<DefaultValue name="suggestApiConfig">sapi0</DefaultValue>
		<DefaultValue name="suggestApiCommand">suggest</DefaultValue>

	</DefaultValues>
</Widget>
