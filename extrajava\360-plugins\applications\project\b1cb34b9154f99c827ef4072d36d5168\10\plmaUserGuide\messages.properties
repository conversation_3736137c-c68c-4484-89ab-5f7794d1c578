configuring_charts.title=Configuring charts
configuring_charts.content=You can configure some chart parameters. Configuration changes are only applied to the current chart and only appear on your own <b>3D</b>EXPERIENCE platform. \
<h1>Hide or Display Chart Series</h1> \
	You can hide or display chart series. \
	<ol> \
		<li>To hide a series, clear the check box located next to its name.</li> \
		<li>To display a series, select the check box located next to its name.</li> \
		<li>Click <b>Save</b>.</li> \
	</ol> \
	Series are hidden or displayed accordingly. If the legend is displayed, you can also hide or display series directly from the chart. For more information, see <a href="#hide_or_display_chart_series">Hide or Display Chart Series</a>. \
<h1>Select the Chart Type</h1> \
	You can change the default chart types used to represent the series. \
	<ol> \
		<li>From the <b>Representation</b> drop-down list, select the chart type of your choice for each series.</li> \
		<li>Click <b>Save</b>.</li> \
	</ol> \
<h1>Display Statistical Lines</h1> \
	You can display an average and a median line for each series. \
	<ol> \
		<li>For each series for which you want to display an average line, select the check box from <b>Average</b> column.</li> \
		<li>For each series for which you want to display a median line, select the check box from <b>Median</b> column.</li> \
		<li>Click <b>Save</b>.</li> \
	</ol> \
<h1>Select the Stacking Option</h1> \
	You can set column and area type series to stack on top of each other instead of overlapping.<br /> \
	<ol> \
		<li>From the <b>Stacking list</b>, select one of the following option: \
			<ul> \
				<li><b>Disabled</b></li> \
				<li><b>Normal</b>. Series are stacked on top of each other in order.</li> \
				<li><b>Percent</b>. Fills the plot area and draws each point of data with a relative percentage to all the points in the same category.</li> \
			</ul> \
		</li> \
		<li>Click <b>Save</b>.</li> \
	</ol> \
<h1>Hide Legend</h1> \
	You can hide the legend from the chart. \
	<ol> \
		<li>Clear the <b>Display legend</b> box.</li> \
		<li>Click <b>Save</b>.</li> \
	</ol> \
<h1>Add a Comment</h1> \
	You can hide the legend from the chart. \
	<ol> \
		<li>In the <b>Comment</b> box, enter text.</li> \
		<li>Click <b>Save</b>.</li> \
	</ol> \
<h1>Revert to Default Configuration</h1> \
	You can revert all the changes you have ever made to the configuration of the chart. \
	<ul> \
		<li>Click <b>Restore</b>.</li> \
	</ul>
using_charts.title=Using charts
using_charts.content=You can interact with most charts to explore data. \
<h1>Display More Chart Entries</h1> \
	You can paginate through entries on charts that contain more entries than can be displayed at once on their axes. \
	<ol> \
		<li>On a chart that contain more entries than displayed, click <b>See more…</b>.</li> \
		<li>Use the pagination to navigate through all the entries.</li> \
	</ol> \
<h1 id="hide_or_display_chart_series">Hide or Display Chart Series</h1> \
	You can hide or display chart series. \
	<ol> \
		<li>On a chart legend, click any series name to hide its content from the chart.<br />The series is grayed out in the legend and its corresponding items are hidden.</li> \
		<li>To display a hidden series, click its grayed name in the legend.</li> \
	</ol> \
	Series are hidden or displayed accordingly. You can also hide or display series from the configuration menu. For more information, see <a href="#tabs-configuring_charts">Configuring Charts</a>. \
<h1>Zoom in and out on a Chart</h1> \
	You can zoom in and out on chart content. \
	<ul> \
		<li>Click and drag on the chart to zoom in. You can repeat this step multiple times to zoom in closer.</li> \
		<li>To zoom out, click <b>Reset zoom</b>.</li> \
	</ul> \
<h1>Annotate a Chart</h1> \
	You can add notes to charts. \
	<ol> \
		<li>On a chart, click <b>Menu</b><i class="fonticon fonticon-menu-dot"></i> > <i class="fonticon fonticon-pencil"></i><b>Annotate</b>.</li> \
		<li>Click the chart.</li> \
		<li>In the text editor, enter text.</li> \
		<li>Click <b>Add</b>.</li> \
		<li><b>Optional</b>: Drag the note to wherever you want to place it on the chart.</li> \
		<li>Click the <i class="fonticon fonticon-pencil"></i> button to quit the <b>Annotate</b> menu. The note is added to the chart.<br />You can repeat the steps to add several notes to the same chart.</li> \
		<li> \
			<b>Optional</b>: To delete a note, do all of the following: \
			<ol type="a"> \
				<li>Click <b>Menu</b><i class="fonticon fonticon-menu-dot"></i> > <i class="fonticon fonticon-pencil"></i><b>Annotate</b></li> \
				<li> \
					Hover over the note you want to delete and click <i class="fonticon fonticon-wrong"></i> \
					<div class="box">Warning: There is no confirmation message and no undo.</div> \
				</li> \
				<li>Click the <i class="fonticon fonticon-pencil"></i> button to quit the Annotate menu.</li> \
			</ol> \
		</li> \
	</ol> \
<h1>View Chart in Full Screen</h1> \
	You can display the chart in full screen. \
	<ol> \
		<li>On a chart, click Menu <i class="fonticon fonticon-menu-dot"></i> > <i class="fonticon fonticon-resize-full"></i> Full Screen.</li> \
		<li>To exit full screen mode, click anywhere outside the chart or click Menu <i class="fonticon fonticon-menu-dot"></i> > <i class="fonticon fonticon-resize-small"></i> Full Screen.</li> \
	</ol> \
<h1>Display Chart Data as a Table</h1> \
	On most charts, you can switch their view to display their content in a table. \
	<ol> \
		<li>On a chart, click <b>Menu</b><i class="fonticon fonticon-menu-dot"></i> > <i class="fonticon fonticon-list"></i><b>Switch to table</b>.</li> \
		<li> \
			<b>Optional</b>: Do any of the following: \
			<ol type="a"> \
				<li>Click <b>Menu</b><i class="fonticon fonticon-menu-dot"></i> > <i class="fonticon fonticon-search"></i><b>Search data</b> to search the content contained in the table.</li> \
				<li>Click <b>Menu</b><i class="fonticon fonticon-menu-dot"></i> > <i class="fonticon fonticon-refresh"></i><b>Swap axes</b> to change the layout of the table.</li> \
				<li>Click <b>Menu</b><i class="fonticon fonticon-menu-dot"></i> > <i class="fonticon fonticon-download"></i><b>Export data</b> to download the data as a CSV file.</li> \
				<li>Click <b>Menu</b><i class="fonticon fonticon-menu-dot"></i> > <i class="fonticon fonticon-cog"></i><b>Edit settings…</b> to display or hide columns, and fix columns.</li> \
				<li>Click <b>Menu</b><i class="fonticon fonticon-menu-dot"></i> > <i class="fonticon fonticon-reset"></i><b>Reset table state</b> to discard any of the settings editing you may have made.</li> \
				<li>Click <b>Menu</b><i class="fonticon fonticon-menu-dot"></i> > <i class="fonticon fonticon-chart-bar"></i><b>Switch to chart</b> to return to the chart view.</li> \
			</ol> \
		</li>
refining_content.title=Refining content
refining_content.content=You can add and save filters to any page—except the homepage— to refine the displayed content. \
<h1 id="add_and_delete_filters">Add and Delete Filters</h1> \
	You can use filters to refine the content displayed by the widgets. Filters are cumulative and applied to the whole application, meaning that they remain activated while you browse the other pages of the application until you remove them from the Active filter bar. \
	<ol> \
		<li> \
			To add a filter, do any of the following: \
			<ul> \
				<li>On a chart, click the item you want the widgets to be refined on.</li> \
				<li>Enter your search in the search box of the dashboard's top bar and click <b>Search</b><i class="fonticon fonticon-search"></i>. For example, enter an owner/assignee's name to refine the search on his/her issues, and a status, to further refine issues on a specific status.</li> \
				<li> \
					Click <b>Filter</b><i class="fonticon fonticon-filter"></i> and do both or either of the following: \
					<ul> \
						<li>From the refine panel, click a facet category to add it as a filter.</li> \
						<li>From the refine pane, click <b>Exclude</b><i class="fonticon fonticon-block"></i> on a facet category to add a filter that excludes the content associated with this category.</li> \
					</ul> \
				Result: Filters are added to the Active filters bar that is located at the top of each page. \
				</li> \
			</ul> \
		</li> \
		<li> \
			To delete filters, do either of the following: \
			<ul> \
				<li>To delete filters one by one, click <b>Remove this filter</b><i class="fonticon fonticon-wrong"></i> either from the Active filters bar or from the Refine hand-right panel.</li> \
				<li>To delete all the active filters at once, click <b>Remove current filters (Saved filters won't be removed)</b> <i class="fonticon fonticon-trash"></i>. \
			</ul> \
		</li> \
<h1>Save Filters</h1> \
	On each page, you can save filters that will always be applied to the page whenever you open it until you intentionally remove them. Saved filters are not applied to the other pages of the application while you browse them. \
	<ol> \
		<li>On the page on which you want to save filters, add those filters. For more information, see <a href="#add_and_delete_filters">Add and Delete Filters</a>.</li> \
		<li>Click <b>Modify current page</b><i class="fonticon fonticon-pencil"></i> > <i class="fonticon fonticon-pin"></i><b>Save Filters</b>.<br />Saved filters are represented by a lock icon on the Active Filters bar.</li> \
		<li>To edit saved filters, click <b>Saved</b><i class="fonticon fonticon-lock"></i> on the Active filter bar, remove or add filters and click <b>Click to save</b><i class="fonticon fonticon-lock-open"></i>.</li> \
		<li>To remove saved filters from the page, click <b>Modify current page</b><i class="fonticon fonticon-pencil"></i> > <i class="fonticon fonticon-pin-off"></i><b>Remove Filters</b>.</li> \
	</ol> \
<h1>Refine Content over Time</h1> \
	You can add filters to set a date interval on the content. Otherwise, the business objects provided by the sources are included in the page regardless of their dates. \
	<ol> \
		<li>Click <b>Filter</b><i class="fonticon fonticon-filter"></i>.</li> \
		<li>From the <b>Actual Start</b> section of the Refine panel, click <b>Select Date Range</b><i class="fonticon fonticon-calendar"></i>, select a date range, and click <b>Apply</b>.</li> \
		<li> \
			<b>Optional</b>: From the <b>Actual End</b> section of the Refine panel, click <b>Select Date Range</b><i class="fonticon fonticon-calendar"></i>, select a date range, and click <b>Apply</b>. \
			<div class="box"><b>Tip</b>: You can configure the default date ranges in the Preferences. For more information, see Configuring Date Ranges.</div> \
		</li> \
	</ol>
modifying_pages.title=Modifying Pages
modifying_pages.content=You can modify page properties, edit the layout, add notes, and create copy of pages. \
<h1>Edit a Page Title, Description and Icon</h1> \
	You can edit the page properties, that is the title, description and icon of any page except the home page. \
	<ol> \
		<li>On any page -except the home page-, click <b>Modify current page</b><i class="fonticon fonticon-pencil"></i> > <b>Edit</b><i class="fonticon fonticon-pencil"></i>.</li> \
		<li>Click <b>Modify page properties</b><i class="fonticon fonticon-chevron-down"></i></li> \
		<li> \
			Edit the title, the description, the icon, and click <b>OK</b>.<br /> \
			Note: Page descriptions appear at the bottom of the left-hand menu when you hover over a page name. \
		</li> \
		<li>Click <b>Save modifications</b><i class="fonticon fonticon-check"></i>.</li> \
	</ol> \
<h1>Edit the Layout of a Page</h1> \
	You can move and remove widgets from any page except the home page. \
	<ol> \
		<li>On any page -except the home page-, click <b>Modify current page</b><i class="fonticon fonticon-pencil"></i> > <b>Edit</b><i class="fonticon fonticon-pencil"></i>.</li> \
		<li> \
			Do any of the following: \
			<ol type="a"> \
				<li>To move a widget, drag it to the page area of your choice.</li> \
				<li>To resize a widget, drag the bottom right corner resize icon.</li> \
				<li> \
					To remove a widget, drag it to the <b>Available widgets</b> pane.<br /> \
					Note: Removed widgets are not deleted. You can add them back to the page at any time. For more information, see step d. \
				</li> \
				<li> \
					To add a widget, drag it from the <b>Available widgets</b> pane to the body of the page.<br /> \
					Note: If all the widgets have already been added to the page, the <b>Available widgets</b> appears empty. \
				</li> \
			</ol> \
		</li> \
		<li>Click <b>Save modifications</b><i class="fonticon fonticon-check"></i>.</li> \
		<li><b>Optional</b>: To revert the page to the default layout, click <b>Modify current page</b><i class="fonticon fonticon-pencil"></i> > <b>Edit</b><i class="fonticon fonticon-pencil"></i> > <b>Reset layout to the default configuration</b><i class="fonticon fonticon-reset"></i>.</li> \
	</ol> \
<h1>Add, Edit, and Delete Notes</h1> \
	You can add note widgets to any page except the home page. \
	<ol> \
		<li>On any page -except the home page-, click <b>Modify current page</b><i class="fonticon fonticon-pencil"></i> > <b>Edit</b><i class="fonticon fonticon-pencil"></i>.</li> \
		<li> \
			Do either of the following: \
			<ul> \
				<li> \
					To add a note, do all of the following: \
					<ol type="a"> \
						<li>Scroll down the page and drag a Note widget from the Custom content pane to the body of the page.</li> \
						<li>On the note, click <b>Edit</b><i class="fonticon fonticon-pencil"></i>.</li> \
						<li>In the text editor, enter and format text.</li> \
						<li>Click <b>Save</b><i class="fonticon fonticon-check"></i> to save the note content.</li> \
						<li>Optional: Drag the bottom right corner resize icon to resize the note.</li> \
						<li>Click <b>Save modifications</b><i class="fonticon fonticon-check"></i>.</li> \
				</li> \
				<li> \
					To edit a note, do all of the following: \
					<ol type="a"> \
						<li>On the note, click <b>Edit</b><i class="fonticon fonticon-pencil"></i>.</li> \
						<li>In the text editor, enter and format text.</li> \
						<li>Click <b>Save</b><i class="fonticon fonticon-check"></i> to save the note content.</li> \
						<li>Click <b>Save modifications</b><i class="fonticon fonticon-check"></i></li> \
					</ol> \
				</li> \
				<li> \
					To delete a note: \
					<ol type="a"> \
						<li>Drag the note you want to delete to the <b>Available widgets</b> pane.</li> \
						<li>Click <b>Save modifications</b><i class="fonticon fonticon-check"></i>.</li> \
					</ol> \
				</li> \
			</ul> \
		</li> \
	</ol> \
<h1>Create and Delete a Copy of a Page</h1> \
	You can create a copy of any page except the home page. This is particularly useful when you both want to make changes to a page while keeping its original version. \
	<ol> \
		<li> \
			On any page -except the home page-, click <b>Modify current page</b><i class="fonticon fonticon-pencil"></i> > <b>Duplicate</b><i class="fonticon fonticon-copy"></i>.<br /> \
			A copy of the page is created and is added to the top of left-hand menu, under the <b>My pages</b> section. \
		</li> \
		<li> \
			To delete a copy of a page, go to the page you want to delete and click <b>Modify current page</b><i class="fonticon fonticon-pencil"></i> > <i class="fonticon fonticon-trash"></i><b>Delete</b>. \
			<div class="box">Warning: There is no confirmation message and no undo.</div> \
		</li> \
	</ol>
setting_the_landing_page.title=Setting the Landing Page
setting_the_landing_page.content=You can choose which page is displayed when you open the application. By default, the landing page is the home page. \
	<ol> \
		<li>On the page you want to set as the landing page, click <i class="fonticon fonticon-favorite-off"></i> > <i class="fonticon fonticon-home"></i><b>Set as landing page</b>.</li> \
		<li>To restore the home page as the landing page, open the home page and click <i class="fonticon fonticon-home"></i><b>Set as landing page</b>.</li> \
	</ol>
