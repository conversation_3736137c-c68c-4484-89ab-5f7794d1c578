<?xml version="1.0" encoding='UTF-8'?>
<Widget name="chart serie configuration" group="Layout">

	<Description>
		This widget should be added to the page as a subwidget of a PLMA lightbox widget.
		If a trigger "plma:lightbox-chart-configuration" is fired (with uCssId on the plmaChart2), a popup is displayed.
		This popup modifies a plmaChart2 widget and allows to :
		 - plot an 'Average'/'Median' measure
		 - display or hide the chart legend
		 - add a description
	</Description>

	<Preview>
		<![CDATA[
            <img src="/resources/widgets/chartSerieConfiguration/images/preview.PNG" alt="Chart Serie Configuration" />
        ]]>
	</Preview>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="js/chartSerieConfiguration.js" />
		<Include type="js" path="../plmaResources/js/polyfills.js" />
		<Include type="js" path="../plmaResources/js/widgetButtonManager.js" />
		<Include type="js" path="../plmaResources/lib/notify/notify.js" />
		<Include type="js" path="../plmaCharts2/js/plmaCharts.js" />
	</Includes>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO" consumeFeed="false" />
    <SupportI18N supported="true">
        <JsKeys>
            <JsKey>Edit</JsKey>
			<JsKey>chart.configuration.btn.label</JsKey>
			<JsKey>operator.median</JsKey>
			<JsKey>operator.average</JsKey>
			<JsKey>representation.columnsToBars</JsKey>
			<JsKey>representation.barsToColumns</JsKey>
        </JsKeys>
    </SupportI18N>

	<OptionsGroup name="General">
        <Option id="popupButtonSelector" name="Popup button selector" arity="ONE">
            <Description>The CSS selector of the button that will trigger the lightbox.</Description>
        </Option>
		<Option id="waitForTrigger" name="Load configuration after trigger event">
			<Description>If option is set to 'true', the chart series configuration is loaded after a "plma:chart-is-loaded" trigger event
				 has been sent. &lt;br/&gt;
                &lt;b&gt;WARNING&lt;/b&gt;:  Mandatory when plmaChart2 widget uses a plma Asynchronous
				ajax loading trigger.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="btnSelector" name="Button selector" arity="ONE">
			<Description>jQuery selector to specify the widgets on which the configuration button is displayed.</Description>
		</Option>
		<Option id="btnIcon" name="Button icon">
			<Description>Specifies the CSS class of the icon for the button.</Description>
		</Option>
		<Option id="storageKey" name="Storage key" isEvaluated="true" arity="ONE">
			<Description>Sets the default storage key for the Chart Serie Configuration widget.</Description>
		</Option>
 	</OptionsGroup>

	<DefaultValues>
        <DefaultValue name="popupButtonSelector">.plma-lightbox-chart-configuration</DefaultValue>
		<DefaultValue name="waitForTrigger">false</DefaultValue>
		<DefaultValue name="btnSelector">.wuid.plmaCharts</DefaultValue>
		<DefaultValue name="btnIcon">fonticon fonticon-tools</DefaultValue>
	</DefaultValues>

</Widget>
