<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="experience" uri="http://www.exalead.com/jspapi/experience" %>

<render:import parameters="accessFeeds,feed,entry,widget,uCssId"/>
<render:import parameters="hitUrl,hitTitle,columnsConfig,doTrasnspose" ignore="true"/>

<%-- retrieve the widget options --%>
<config:getOption var="hitUrlTarget" name="hitUrlTarget" defaultValue=""/>
<config:getOption var="hitContent" name="hitContent" defaultValue="${entry.content}" entry="${entry}" feed="${feed}"/>
<config:getOption name="hitContentDisplay" var="hitContentDisplay" defaultValue="false"/>
<request:getCookieValue name="description_result_list${uCssId}" var="displayDescription" defaultValue="${hitContentDisplay}"/>
<config:getOption var="maxContentLength" name="maxContentLength" defaultValue="0"/>
<config:getOption var="showThumbnail" name="showThumbnail" defaultValue="false"/>
<config:getOption var="typeFacetId" name="typeFacet"/>
<config:getOption var="typeFacetIcon" name="typeFacetIcon" defaultValue="fonticon-legend"/>
<config:getOption var="renderCategoryLinks" name="renderCategoryLinks" defaultValue="false"/>
<config:getOption var="underlineCategoryLinks" name="underlineCategoryLinks" defaultValue="false"/>
<config:getOption var="ignoreCategoryColor" name="ignoreCategoryColor" defaultValue="false"/>
<config:getOption var="pinRow" name="pinRow" defaultValue="false"/>
<c:if test="${renderCategoryLinks == false}">
	<c:set var="underlineCategoryLinks" value="false"/>
</c:if>
<search:getFacet var="typeFacet" facetId="${typeFacetId}" entry="${entry}"/>
<c:set var="hitUrlTarget" value="${hitUrlTarget == 'New Page' ? '_blank' : ''}"/>

<%-- class="${wh:cleanEntryId(entry.id)}" is used by GMap widgets to append markers --%>
<c:set var="entryIdCssClass" value="${search:cleanEntryId(entry)}"/>

<config:getOption name="defaultLayout" var="defaultLayout" defaultValue="Responsive"/>
<c:set var="isGridTable" value="${defaultLayout == 'GridTable'}"/>

<%--
'entryHitId' must be url value, as used by selection code.
'entryUri' will be same as value of 'hitParamValue' if not empty else equals to 'entryHitId'
--%>
<search:getEntryInfo var="rawEntryUri" name="url" entry="${entry}"/>
<string:escape var="entryHitId" value="${rawEntryUri}" escapeType="HTML"/>
<config:getOption name="hitParamValue" var="hitParamValue" defaultValue=""/>
<config:getOption name="hitParamName" var="hitParamName" defaultValue="hit"/>
<config:getOption var="enableMetaClass" name="enableMetaClass" defaultValue="false"/>
<config:getOption var="enableMetai18n" name="enableMetai18n" defaultValue="false"/>
<config:getOption var="metaList" name="metaList" defaultValue=""/>
<experience:is3DXP var="isIn3DXP"/>

<c:set var="entryUri" value="${entryHitId}"/>
<c:if test="${hitParamValue != null && hitParamValue != ''}">
    <search:getMetaValue var="metaValue" entry="${entry}" metaName="${hitParamValue}"/>
    <c:if test="${metaValue != null && metaValue != ''}">
        <c:set var="entryUri" value="${metaValue}"/>
    </c:if>
</c:if>

<li class="hit hit-list ${entryIdCssClass}"
	data-hit-id="${entryHitId}"
	data-uri="${entryUri}"
	data-parameter="${hitParamName}"
	data-id-css-class="${entryIdCssClass}">
	<i18n:message var="titleLabel" code="hitcustom.column.label"/>
	<c:if test="${!isGridTable}">
		<div class="hit-container hit-default">
			<c:if test="${not empty hitTitle}">
				<div class="hitHeader ${empty typeFacetId ? 'no-icon' : ''}">
					<c:choose>
						<c:when test="${showThumbnail == 'true'}">
							<div class="hitThumbnail ${isIn3DXP ? 'hitCursor' : ''}">
								<render:template template="thumbnail.jsp" widget="thumbnail">
									<render:parameter name="feed" value="${feed}"/>
									<render:parameter name="entry" value="${entry}"/>
									<render:parameter name="widget" value="${widget}"/>
									<render:parameter name="hitUrl" value="${hitUrl}"/>
								</render:template>
							</div>
						</c:when>
						<c:when test="${not empty typeFacetId}">
							<search:forEachCategory var="typeCategory" root="${typeFacet}">
							    <c:set var="categoryIcon" value=""/>
								<plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
								<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
								<plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
								<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
								<div class="icon-container">
									<div class="hitIcon" style="background-color:${plma:toRGB(categoryColor, '.3')};">
										<i class="icon fonticon ${not empty categoryIcon? categoryIcon : typeFacetIcon}"
										   style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"></i>
									</div>
								</div>								
							</search:forEachCategory>
						</c:when>
					</c:choose>
					<div class="hitMainContainer">
						<div class="hitTitle" title="${titleLabel}: ${fn:escapeXml(hitTitle)}">
							<render:link href="${hitUrl}" target="${hitUrlTarget}">
								<c:if test="${showThumbnail == 'true' && not empty typeFacetId}">
									<search:forEachCategory var="typeCategory" root="${typeFacet}">
                                        <c:set var="categoryIcon" value=""/>
										<plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
										<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
										<plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
										<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
										<i class="icon fonticon ${not empty categoryIcon? categoryIcon : typeFacetIcon}"
										   style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"></i>										
									</search:forEachCategory>
								</c:if>
								${hitTitle}
							</render:link>
						</div>
						<div class="metaContainer">
							<div class="hitMetas">
								<c:forEach var="colConfig" items="${columnsConfig}">
								    <string:eval var="showColGroup" string="${colConfig.condExpr}" feeds="${accessFeeds}" feed="${feed}"/>
									<string:eval var="showColumn" string="${colConfig.displayCondition}" entry="${entry}" feeds="${accessFeeds}" feed="${feed}"/>
									<c:if test="${showColGroup == 'true' && colConfig.isFacet == 'false' && colConfig.isShown != 'false' && colConfig.isShown != 'titleConfig'}">
										<search:getMeta var="meta" metaName="${colConfig.meta}" entry="${entry}"/>
										<c:choose>
                                            <c:when test="${colConfig.label != null}">
                                                <c:set var="metaLabel" value="${colConfig.label}"/>
                                            </c:when>
                                            <c:otherwise>
                                                <search:getMetaLabel var="metaLabel" meta="${meta}"/>
                                            </c:otherwise>
                                        </c:choose>
										<c:set var="metaValue" value=""/>
										<c:set var="rawMetaValue" value=""/>	<%-- Value with no highlight --%>
										<search:forEachMetaValue meta="${meta}" entry="${entry}" var="value" varRaw="rawValue" varStatus="loop">
											<c:if test="${showColumn == 'true'}">
												<c:set var="metaValue" value="${metaValue}${value}"/>
												<c:set var="rawMetaValue" value="${rawMetaValue}${rawValue}"/>
												<c:if test="${not loop.last}"><c:set var="metaValue" value="${metaValue}, "/></c:if>
												<c:if test="${not loop.last}"><c:set var="rawMetaValue" value="${rawMetaValue}, "/></c:if>
											</c:if>
										</search:forEachMetaValue>

										<%-- If titleValue is present, change rawMetaValue to titleValue to show correct title --%>
                                        <c:choose>
                                            <c:when test="${not empty colConfig.titleValue}">
                                                <string:eval var="titleValue" string="${colConfig.titleValue}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}" isHtmlEscape="true"/>
                                            </c:when>
                                            <c:otherwise>
                                                <c:set var="titleValue" value="${plma:escapeHTML(rawMetaValue)}"/>
                                            </c:otherwise>
                                        </c:choose>

										<c:if test="${metaValue != ''}">
										    <c:if test="${not empty colConfig.displayJSPTemplateWidget}">
                                                <render:template template="${colConfig.displayTemplate}" widget="${colConfig.displayJSPTemplateWidget}">
                                                    <render:parameter name="accessFeeds" value="${feeds}" />
                                                    <render:parameter name="feed" value="${feed}" />
                                                    <render:parameter name="entry" value="${entry}" />
                                                    <render:parameter name="group" value="${group}" />
                                                    <render:parameter name="colConfig" value="${colConfig}" />
                                                </render:template>
                                            </c:if>
                                            <c:if test="${empty colConfig.displayJSPTemplateWidget}">
                                                <c:if test="${not empty colConfig.displayTemplate && empty colConfig.displayValue}">
                                                    <string:eval var="displayValue" string="${colConfig.displayTemplate}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
                                                    <span class="hitMeta" title="${metaLabel}: ${titleValue}">
                                                        <c:if test="${plma:getBooleanParam(widget, 'showMetaName', false)}">
                                                            <span class="metaName">${metaLabel}:</span>
                                                        </c:if>
                                                        <span class="metaValue">${displayValue}</span>
                                                    </span>
                                                </c:if>
                                                <c:if test="${not empty colConfig.displayValue}">
                                                    <string:eval var="displayValue" string="${colConfig.displayValue}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
                                                    <span class="hitMeta" title="${metaLabel}: ${titleValue}">
                                                        <c:if test="${plma:getBooleanParam(widget, 'showMetaName', false)}">
                                                            <span class="metaName">${metaLabel}:</span>
                                                        </c:if>
                                                        <span class="metaValue">${displayValue}</span>
                                                    </span>
                                                </c:if>
                                                <c:if test="${empty colConfig.displayTemplate && empty colConfig.displayValue}">
                                                    <span class="hitMeta" title="${metaLabel}: ${titleValue}">
                                                        <c:if test="${plma:getBooleanParam(widget, 'showMetaName', false)}">
                                                            <span class="metaName">${metaLabel}:</span>
                                                        </c:if>
                                                        <span class="metaValue">${metaValue}</span>
                                                    </span>
                                                </c:if>
                                            </c:if>
										</c:if>
									</c:if>
								</c:forEach>
							</div>
						</div>
					</div>
				</div>
			</c:if>
			<div class="hitContent ${empty hitContent ? 'empty' : ''} ${displayDescription == 'true' ? '' : 'hidden'}">
				<c:choose>
					<c:when test="${maxContentLength > 0}">
						${fn:substring(hitContent, 0, maxContentLength)}${fn:length(hitContent) > maxContentLength ? '...' : ''}
					</c:when>
					<c:otherwise>${hitContent}</c:otherwise>
				</c:choose>
			</div>
			<div class="hitFacets">
				<c:forEach var="colConfig" items="${columnsConfig}">
				    <string:eval var="showColGroup" string="${colConfig.condExpr}" feeds="${accessFeeds}" feed="${feed}"/>
					<string:eval var="showColumn" string="${colConfig.displayCondition}" entry="${entry}" feeds="${accessFeeds}" feed="${feed}"/>
					<c:if test="${showColGroup == 'true' && colConfig.isFacet == 'true' && colConfig.isShown != 'false' && colConfig.isShown != 'titleConfig'}">
						<search:getFacet var="facet" facetId="${colConfig.meta}" entry="${entry}"/>
						<c:choose>
                            <c:when test="${colConfig.label != null}">
                                <c:set var="facetLabel" value="${colConfig.label}"/>
                            </c:when>
                            <c:otherwise>
                                <plma:getFacetLabel var="facetLabel" feed="${feed}" facetId="${facet.id}"/>
                            </c:otherwise>
                        </c:choose>
                        <c:set var="facetIcon" value=""/>
						<plma:getIconName var="facetIcon" facetId="${facet.id}"/>
						<string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML"/>
						<plma:getCategoriesWithParentTag var="cats" facet="${facet}" iterationMode="LEAVES"/>
                        <c:if test="${showColumn == 'true'}">
                            <span class="hitFacet">
                                <c:if test="${not empty facet}">
                                    <c:if test="${plma:getBooleanParam(widget, 'showFacetIcon', false)}">
                                        <span class="facetIcon fonticon ${facetIcon}"></span>
                                    </c:if>
                                    <c:if test="${plma:getBooleanParam(widget, 'showFacetName', false)}">
                                        <span class="facetLabel">${facetLabel}:</span>
                                    </c:if>
                                </c:if>
                                <c:forEach var="cat" items="${cats}" varStatus="catStatus">
                                    <c:set var="category" value="${cat.category}"/>
                                    <plma:getIconName var="categoryIcon" category="${category}" entry="${entry}"/>
                                    <string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
                                    <plma:getCategoryColor var="categoryColor" category="${category}"/>
                                    <string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
                                    <search:getCategoryLabel var="categoryLabel" category="${category}"/>
                                    <c:set var="catPath" value="${plma:getHierarchicalDescription(cat, pageContext, ' > ')}"/>

                                    <c:if test="${renderCategoryLinks}">
                                        <search:getCategoryUrl var="categoryUrl" category="${category}" baseUrl="" feeds="${accessFeeds}"/>
                                        <url:url var="pageUrl" value="" keepQueryString="true"/>

                                        <plma:correctUrl var="categoryUrl" url="${categoryUrl}" pageUrl="${pageUrl}"
                                                         skipProfiles="${plma:toList('ajaxInfiniteScroll')}" delete2DFacet="false" feeds="${accessFeeds}"
                                                         keepExtraRefinements="true"/>

                                        <search:getCategoryState var="catState" varClassName="catStateCss" category="${category}"/>
                                        <a class="category-link ${fn:toLowerCase(catState)}" href="${categoryUrl}">
                                    </c:if>
                                        <c:if test="${!plma:getBooleanParam(widget, 'hideCategoryIcon', false)}">
                                            <span class="categoryIcon fonticon ${not empty categoryIcon ? categoryIcon : facetIcon}"
                                                    style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"
                                                    title="${facetLabel} : ${catPath}"></span>
                                        </c:if>
                                                <c:if test="${not empty colConfig.displayJSPTemplateWidget}">
                                                    <render:template template="${colConfig.displayTemplate}" widget="${colConfig.displayJSPTemplateWidget}">
                                                        <render:parameter name="accessFeeds" value="${feeds}" />
                                                        <render:parameter name="feed" value="${feed}" />
                                                        <render:parameter name="entry" value="${entry}" />
                                                        <render:parameter name="group" value="${group}" />
                                                        <render:parameter name="colConfig" value="${colConfig}" />
                                                    </render:template>
                                                </c:if>
                                                <c:if test="${empty colConfig.displayJSPTemplateWidget}">
                                                    <c:set var="categoryLabel" value="${fn:escapeXml(categoryLabel)}" />
                                                    <c:if test="${not empty colConfig.displayTemplate && empty colConfig.displayValue}">
                                                        <string:eval var="displayValue" string="${colConfig.displayTemplate}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
                                                        <c:if test="${!plma:getBooleanParam(widget, 'hideCategoryValue', false)}">
                                                            <span class="categoryName" title="${facetLabel}: ${catPath}">${displayValue}</span>
                                                        </c:if>
                                                    </c:if>
                                                    <c:if test="${not empty colConfig.displayValue}">
                                                        <string:eval var="displayValue" string="${colConfig.displayValue}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
                                                        <c:if test="${!plma:getBooleanParam(widget, 'hideCategoryValue', false)}">
                                                            <span class="categoryName" title="${facetLabel}: ${catPath}">${displayValue}</span>
                                                        </c:if>
                                                    </c:if>
                                                    <c:if test="${empty colConfig.displayTemplate && empty colConfig.displayValue}">
                                                        <c:if test="${!plma:getBooleanParam(widget, 'hideCategoryValue', false)}">
                                                            <span class="categoryName" title="${facetLabel}: ${catPath}">${categoryLabel}</span>
                                                        </c:if>
                                                    </c:if>
                                                </c:if>
                                    <c:if test="${renderCategoryLinks}">
                                        </a>
                                    </c:if>
                                    <c:if test="${!catStatus.last}">
                                        <span class="separator">|</span>
                                    </c:if>
                                </c:forEach>
                            </span>
                        </c:if>
					</c:if>
				</c:forEach>
			</div>
		</div>
	</c:if>
	
	<div class="hit-container ${isGridTable? 'grid-row' : 'hit-table'}">
		<c:if test="${isGridTable || not empty hitTitle}">
			<div class="${isGridTable? 'td fixed-column' : ''} table-column mainColumn" data-column-name="title" data-column-show="true" data-column-type="String" >
				<div class="title-container ${empty typeFacetId ? 'no-icon' : ''} ${showThumbnail? 'with-thumbnail' : 'no-thumbnail'}">
					<span class="toolbarContainer">
					    <c:if test="${pinRow == 'true' && isGridTable}">
					        <div class="freezeButtonClass">
					            <i class="freezeIcon fonticon fonticon-pin-off" onclick="GridTableSetup.handleFreezeRowConfig(event,'${entryIdCssClass}')"></i>
					        </div>
					    </c:if>
                    </span>
					<c:choose>
						<c:when test="${showThumbnail == 'true'}">
							<div class="hitThumbnail ${isIn3DXP ? 'hitCursor' : ''}">
								<render:template template="thumbnail.jsp" widget="thumbnail">
									<render:parameter name="feed" value="${feed}"/>
									<render:parameter name="entry" value="${entry}"/>
									<render:parameter name="widget" value="${widget}"/>
									<render:parameter name="hitUrl" value="${hitUrl}"/>
								</render:template>
							</div>
						</c:when>
						<c:when test="${not empty typeFacetId}">
							<c:if test="${doTrasnspose != 'transpose'}">
								<search:forEachCategory var="typeCategory" root="${typeFacet}">
								    <c:set var="categoryIcon" value=""/>
									<plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
									<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
									<plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
									<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
									<div class="icon-container">
										<div class="hitIcon" style="background-color:${plma:toRGB(categoryColor, '.3')};">
											<i class="icon fonticon ${not empty categoryIcon? categoryIcon : typeFacetIcon}"
											   style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"></i>
										</div>
									</div>
								</search:forEachCategory>
							</c:if>
						</c:when>
					</c:choose>
					<c:if test="${doTrasnspose != 'transpose'}">
						<div class="hitTitle" title="${titleLabel}: ${fn:escapeXml(hitTitle)}">
							<render:link href="${hitUrl}" target="${hitUrlTarget}">
								<c:if test="${showThumbnail == 'true' && not empty typeFacetId}">
									<search:forEachCategory var="typeCategory" root="${typeFacet}">
                                        <c:set var="categoryIcon" value=""/>
										<plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
										<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
										<plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
										<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
										<i class="icon fonticon ${not empty categoryIcon? categoryIcon : typeFacetIcon}"
										   style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"></i>
									</search:forEachCategory>
								</c:if>
								${hitTitle}
							</render:link>
						</div>
					</c:if>
				</div>
				<div class="hitContent ${empty hitContent ? 'empty' : ''} ${displayDescription == 'true' ? '' : 'hidden'}">
					<span class="content">
					<c:choose>
						<c:when test="${maxContentLength > 0}">
							${fn:substring(hitContent, 0, maxContentLength)}${fn:length(hitContent) > maxContentLength ? '...' : ''}
						</c:when>
						<c:otherwise>${hitContent}</c:otherwise>
					</c:choose>
					</span>
				</div>
			</div>
		</c:if>

		<c:forEach var="colConfig" items="${columnsConfig}">
		    <string:eval var="showColGroup" string="${colConfig.condExpr}" feeds="${accessFeeds}" feed="${feed}"/>
			<string:eval var="showColumn" string="${colConfig.displayCondition}" entry="${entry}" feeds="${accessFeeds}" feed="${feed}"/>
			<c:if test="${showColGroup == 'true' && colConfig.isFacet == 'true' && colConfig.isShown != 'false' && colConfig.isShown != 'titleConfig'}">
				<search:getFacet var="facet" facetId="${colConfig.meta}" entry="${entry}"/>
				<c:choose>
                    <c:when test="${colConfig.label != null}">
                        <c:set var="facetLabel" value="${colConfig.label}"/>
                    </c:when>
                    <c:otherwise>
                        <plma:getFacetLabel var="facetLabel" feed="${feed}" facetId="${facet.id}"/>
                    </c:otherwise>
                </c:choose>
                <c:set var="facetIcon" value=""/>
				<plma:getIconName var="facetIcon" facetId="${facet.id}"/>
				<string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML"/>
				<div class="${isGridTable? 'td' : ''} table-column hitFacet ${empty facetIcon ? 'no-icon' : ''}" data-column-name="${colConfig.meta}" 
					data-colgroup-id="${colConfig.colGroupId}" data-column-show="${colConfig.isShown}" data-column-type="${colConfig.type}" >
					<c:set var="eachCategory">
                        <search:forEachCategory root="${facet}" var="category" iterationMode="LEAVES">
                            <c:if test="${showColumn == 'true'}">
                                <c:choose>
                                    <c:when test="${isGridTable && ignoreCategoryColor}">
                                        <!-- Ignore categoryColor when gridtable mode and ignoreCategoryColor is true -->
                                    </c:when>
                                    <c:otherwise>
                                        <plma:getCategoryColor var="categoryColor" category="${category}"/>
                                    </c:otherwise>
                                </c:choose>

                                <string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
                                <search:getCategoryLabel var="categoryLabel" category="${category}"/>

                                <c:if test="${renderCategoryLinks}">
                                    <search:getCategoryUrl var="categoryUrl" category="${category}" baseUrl="" feeds="${accessFeeds}"/>

                                    <plma:correctUrl var="categoryUrl" url="${categoryUrl}" pageUrl="${pageUrl}"
                                                     skipProfiles="${plma:toList('ajaxInfiniteScroll')}" delete2DFacet="false" feeds="${accessFeeds}"
                                                     keepExtraRefinements="true"/>

                                    <search:getCategoryState var="catState" varClassName="catStateCss" category="${category}"/>
                                    <a class="category-link ${catStateCss}" href="${categoryUrl}">
                                </c:if>
                                <c:if test="${not empty colConfig.displayJSPTemplateWidget}">
                                    <render:template template="${colConfig.displayTemplate}" widget="${colConfig.displayJSPTemplateWidget}">
                                        <render:parameter name="accessFeeds" value="${feeds}" />
                                        <render:parameter name="feed" value="${feed}" />
                                        <render:parameter name="entry" value="${entry}" />
                                        <render:parameter name="group" value="${group}" />
                                        <render:parameter name="colConfig" value="${colConfig}" />
                                    </render:template>
                                </c:if>
                                <c:if test="${empty colConfig.displayJSPTemplateWidget}">
                                    <c:set var="decorateCategoryLinks" value="${underlineCategoryLinks && isGridTable ? 'categoryLinksDecoration' : ''}"/>
                                    <c:set var="categoryLabel" value="${fn:escapeXml(categoryLabel)}" />
                                    <c:if test="${not empty colConfig.displayTemplate && empty colConfig.displayValue}">
                                        <string:eval var="displayValue" string="${colConfig.displayTemplate}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
                                        <span class="categoryName ${decorateCategoryLinks}" title="${facetLabel}: ${categoryLabel}"
                                                                        style="${not empty categoryColor ? 'color:' : ''}${categoryColor}">${displayValue}</span>
                                    </c:if>
                                    <c:if test="${not empty colConfig.displayValue}">
                                        <string:eval var="displayValue" string="${colConfig.displayValue}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
                                        <span class="categoryName ${decorateCategoryLinks}" title="${facetLabel}: ${categoryLabel}"
                                                                        style="${not empty categoryColor ? 'color:' : ''}${categoryColor}">${displayValue}</span>
                                    </c:if>
                                    <c:if test="${empty colConfig.displayTemplate && empty colConfig.displayValue}">
                                        <span class="categoryName ${decorateCategoryLinks}" title="${facetLabel}: ${categoryLabel}"
                                                                        style="${not empty categoryColor ? 'color:' : ''}${categoryColor}">${categoryLabel}</span>
                                    </c:if>
                                </c:if>
                                <c:if test="${renderCategoryLinks}">
                                    </a>
                                </c:if>
                            </c:if>
                        </search:forEachCategory>
                    </c:set>

                    <%-- to display in column --%>
                    <div class="cell-content">
                        ${eachCategory}
                    </div>
                    <%-- to display in dropdown for multivalued facet  --%>
                    <div class="hidden large-content-tooltip">
                        ${eachCategory}
                    </div>
				</div>
			</c:if>
			<c:if test="${showColGroup == 'true' && colConfig.isFacet == 'false' && colConfig.isShown != 'false' && (colConfig.isShown != 'titleConfig' || doTrasnspose == 'transpose')}">
				<c:set var="metaValue" value=""/>
				<c:set var="rawMetaValue" value=""/>	<%-- Value with no highlight --%>
				<c:set var="metaLabel" value=""/>
	            <search:getMeta var="meta" metaName="${colConfig.meta}" entry="${entry}"/>
               <c:choose>
                    <c:when test="${colConfig.label != null}">
                        <c:set var="metaLabel" value="${colConfig.label}"/>
                    </c:when>
                    <c:otherwise>
                        <search:getMetaLabel var="metaLabel" metaName="${colConfig.meta}"/>
                    </c:otherwise>
                </c:choose>
                <search:forEachMetaValue meta="${meta}" entry="${entry}" var="value" varRaw="rawValue" varStatus="loop">
					<c:if test="${showColumn == 'true'}">
						<c:set var="metaValue" value="${metaValue}${value}"/>
						<c:set var="rawMetaValue" value="${rawMetaValue}${rawValue}"/>
						<c:if test="${not loop.last}"><c:set var="metaValue" value="${metaValue}, "/></c:if>
						<c:if test="${not loop.last}"><c:set var="rawMetaValue" value="${rawMetaValue}, "/></c:if>
					</c:if>
                </search:forEachMetaValue>

                <%-- If titleValue is present, change rawMetaValue to titleValue to show correct title --%>
                <c:choose>
                    <c:when test="${not empty colConfig.titleValue}">
                        <string:eval var="titleValue" string="${colConfig.titleValue}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}" isHtmlEscape="true"/>
                    </c:when>
                    <c:otherwise>
                        <c:set var="titleValue" value="${plma:escapeHTML(rawMetaValue)}"/>
                    </c:otherwise>
                </c:choose>
                <c:set var="facetIcon" value=""/>
				<plma:getIconName var="facetIcon" facetId="${meta.name}"/>
				<string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML"/>
				<span class="${isGridTable? 'td' : ''} table-column hitMeta ${empty facetIcon ? 'no-icon' : ''}" data-column-name="${colConfig.meta}"
                        data-colgroup-id="${colConfig.colGroupId}" data-column-show="${colConfig.isShown}" data-column-type="${colConfig.type}">
                    <c:if test="${not empty colConfig.displayJSPTemplateWidget}">
                        <render:template template="${colConfig.displayTemplate}" widget="${colConfig.displayJSPTemplateWidget}">
                            <render:parameter name="accessFeeds" value="${feeds}" />
                            <render:parameter name="feed" value="${feed}" />
                            <render:parameter name="entry" value="${entry}" />
                            <render:parameter name="group" value="${group}" />
                            <render:parameter name="colConfig" value="${colConfig}" />
                        </render:template>
                    </c:if>
                    <c:if test="${empty colConfig.displayJSPTemplateWidget}">
                        <c:set var="isMetaMatch" value="false" />
                        <c:if test="${metaList != ''}">
                            <c:set var="isMetaMatch" value="${fn:contains(metaList, colConfig.meta) ? true : false}" />
                        </c:if>
                        <c:if test="${not empty colConfig.displayTemplate && empty colConfig.displayValue}">
                            <string:eval var="displayValue" string="${colConfig.displayTemplate}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
                            <span class="metaValue" title="${metaLabel}: ${titleValue}">${displayValue}</span>
                        </c:if>
                        <c:if test="${not empty colConfig.displayValue}">
                            <string:eval var="displayValue" string="${colConfig.displayValue}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
                            <span class="metaValue" title="${metaLabel}: ${titleValue}">${displayValue}</span>
                        </c:if>
                        <c:if test="${empty colConfig.displayTemplate && empty colConfig.displayValue}">
                            <c:if test="${enableMetai18n && isMetaMatch}">
                                <i18n:message var="metaValue" code="${metaValue}"/>
                            </c:if>
                            <span class="metaValue ${isMetaMatch ? rawMetaValue : ''}" title="${metaLabel}: ${titleValue}">${metaValue}</span>
                        </c:if>
                    </c:if>
                </span>
			</c:if>
		</c:forEach>
        <%-- Custom Columns --%>
        <config:getOption var="customMetaJSP" name="customMetaJSP" defaultValue=""/>
        <c:if test="${not empty customMetaJSP}">
            <render:template template="${customMetaJSP}" defaultTemplate="">
                <render:parameter name="accessFeeds" value="${accessFeeds}" />
                <render:parameter name="feed" value="${feed}" />
                <render:parameter name="widget" value="${widget}"/>
                <render:parameter name="uCssId" value="${uCssId}"/>
                <render:parameter name="isGridTable" value="${empty isGridTable? false : isGridTable}"/>
                <render:parameter name="entry" value="${entry}"/>
            </render:template>
        </c:if>
	</div>
	<div class="subwidgets">
		<widget:forEachSubWidget widgetContainer="${widget}" feed="${feed}" entry="${entry}">
			<render:widget/>
		</widget:forEachSubWidget>
	</div>
</li>
