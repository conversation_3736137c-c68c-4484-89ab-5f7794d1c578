<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import parameters="section, extraCss" ignore="true" />

<string:eval var="section" string="${section}"/>

<string:escape var="extraCss" value="${extraCss}" escapeType="HTML"/>
<string:escape var="section" value="${section}" escapeType="HTML"/>
<div class="section-separation ${extraCss}" data-section-id="${section}">
    <c:if test="${not empty section}">
        <div class="section-label">${section}</div>
    </c:if>
</div>
