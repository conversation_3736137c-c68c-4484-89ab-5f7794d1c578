<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>

<render:import varWidget="widget" varFeeds="feeds" />

<config:getOption name="title" var="title"/>
<config:getOption name="facetName" var="facetName"/>

<config:getOption name="filter" var="filter"/>
<config:getOption name="filterList" var="filterList"/>
<c:set var="filterListTemp" value="${fn:split(filterList,',')}"/>
<list:new var="filterList"/>
<c:forEach var="item" items="${filterListTemp}">
	<list:add value="${fn:trim(item)}" list="${filterList}"/>
</c:forEach>

<config:getOptionsComposite name="categoryStyle" var="categoryStyleConfig" mapIndex="true"/>
<map:new var="categoryStyles"/>
<c:if test="${not empty categoryStyleConfig}">
	<c:forEach items="${categoryStyleConfig}" var="categoryStyle">
		<map:put key="${categoryStyle.categoryName}" value="${categoryStyle}" map="${categoryStyles}"/>
	</c:forEach>
</c:if>

<search:getFacet var="facet" facetId="${facetName}" feeds="${feeds}"/>

<widget:widget varUcssId="uCssId" extraCss="SingleValueKPI flexbox-container">
	
	<c:set var="displayedTitle" value="${empty title ? facetLabel : title}"/>

	<widget:header>
		<c:choose>
			<c:when test="${not empty title}">
				${title}
			</c:when>
			<c:otherwise>
				<search:getFacetLabel facet="${facet}"/>
			</c:otherwise>
		</c:choose>
	</widget:header>
	
	<widget:content >
		<div class="row">
			<c:choose>
				<c:when test="${filter == 'Exclude'}">
					<search:forEachCategory root="${facet}" var="category">
						<c:if test="${not list:contains(filterList, category.description)}">
							<render:template template="templates/card.jsp">
								<render:parameter name="category" value="${category}"/>
							</render:template>
						</c:if>
					</search:forEachCategory>
				</c:when>
				<c:when test="${filter == 'Include'}">
					<map:new var="categories"/>
					
					<search:forEachCategory root="${facet}" var="category">
						<map:put key="${category.description}" value="${category}" map="${categories}"/>
					</search:forEachCategory>
					
					<c:forEach var="categoryName" items="${filterList}">
						<c:if test="${not empty categories[categoryName]}">
							<render:template template="templates/card.jsp">
								<render:parameter name="category" value="${categories[categoryName]}"/>
								<render:parameter name="categoryStyles" value="${categoryStyles}"/>
							</render:template>
						</c:if>
					</c:forEach>
				</c:when>
			
			</c:choose>
		</div>
	</widget:content>
	


</widget:widget>
