<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>

<render:import ignore="true" parameters="feed,feeds,filters"/>

<%-- Compact mode: no sections, no titles, all filters are rendered next to each other --%>

<render:template template="templates/filterSection.jsp">
    <render:parameter name="feed" value="${feed}"/>
    <render:parameter name="feeds" value="${feeds}"/>
    <render:parameter name="filterType" value="SEARCH"/>
    <render:parameter name="filters" value="${filters}"/>
</render:template>
<render:template template="templates/filterSection.jsp">
    <render:parameter name="feed" value="${feed}"/>
    <render:parameter name="feeds" value="${feeds}"/>
    <render:parameter name="filterType" value="REFINED"/>
    <render:parameter name="filters" value="${filters}"/>
</render:template>
<render:template template="templates/filterSection.jsp">
    <render:parameter name="feed" value="${feed}"/>
    <render:parameter name="feeds" value="${feeds}"/>
    <render:parameter name="filterType" value="EXCLUDED"/>
    <render:parameter name="filters" value="${filters}"/>
</render:template>
<render:template template="templates/filterSection.jsp">
    <render:parameter name="feed" value="${feed}"/>
    <render:parameter name="feeds" value="${feeds}"/>
    <render:parameter name="filterType" value="ZAPPED"/>
    <render:parameter name="filters" value="${filters}"/>
</render:template>
<render:template template="templates/filterSection.jsp">
    <render:parameter name="feed" value="${feed}"/>
    <render:parameter name="feeds" value="${feeds}"/>
    <render:parameter name="filterType" value="FROM_URL"/>
    <render:parameter name="filters" value="${filters}"/>
</render:template>
<render:template template="templates/filterSection.jsp">
    <render:parameter name="feed" value="${feed}"/>
    <render:parameter name="feeds" value="${feeds}"/>
    <render:parameter name="filterType" value="DATE"/>
    <render:parameter name="filters" value="${filters}"/>
</render:template>
<render:template template="templates/filterSection.jsp">
    <render:parameter name="feed" value="${feed}"/>
    <render:parameter name="feeds" value="${feeds}"/>
    <render:parameter name="filterType" value="CUSTOM"/>
    <render:parameter name="filters" value="${filters}"/>
</render:template>