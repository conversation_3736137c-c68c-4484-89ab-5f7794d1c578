/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:32 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.chartboard;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class widget_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_0;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_1;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_2;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_3;

static {
  _jspx_fnmap_0= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:getBooleanParam", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "getBooleanParam", new Class[] {com.exalead.cv360.searchui.configuration.v10.Widget.class, java.lang.String.class, boolean.class});
  _jspx_fnmap_1= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:toList", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "toList", new Class[] {java.lang.String.class});
  _jspx_fnmap_2= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:isPageOwner", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "isPageOwner", new Class[] {jakarta.servlet.http.HttpServletRequest.class, java.lang.String.class});
  _jspx_fnmap_3= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:isEditable", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "isEditable", new Class[] {jakarta.servlet.http.HttpServletRequest.class, java.lang.String.class});
}

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(17);
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/widget.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/security.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/i18n.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/list.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/url.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fmt.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/request.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/map.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetPageName_0026_005fvar_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fheader_0026_005fextraCss;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fcontent;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetChartboardConfig_0026_005fvarCells_005fvar_005fpageId_005fnbColumns_005fmashupPage_005flayout_005fhideAllCells_005fchartBoardId_005fcellHeight_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005ficonList_0026_005fvar_005ffilePath_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fforEachSubWidget_0026_005fwidgetContainer_005ffeed_005fentry;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005fwidget_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fignoreFields_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsecurity_005fisUserConnected_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetPageName_0026_005fvar_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fheader_0026_005fextraCss = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fcontent = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetChartboardConfig_0026_005fvarCells_005fvar_005fpageId_005fnbColumns_005fmashupPage_005flayout_005fhideAllCells_005fchartBoardId_005fcellHeight_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005ficonList_0026_005fvar_005ffilePath_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fforEachSubWidget_0026_005fwidgetContainer_005ffeed_005fentry = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005fwidget_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fignoreFields_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsecurity_005fisUserConnected_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetPageName_0026_005fvar_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss.release();
    _005fjspx_005ftagPool_005fwidget_005fheader_0026_005fextraCss.release();
    _005fjspx_005ftagPool_005fwidget_005fcontent.release();
    _005fjspx_005ftagPool_005fplma_005fgetChartboardConfig_0026_005fvarCells_005fvar_005fpageId_005fnbColumns_005fmashupPage_005flayout_005fhideAllCells_005fchartBoardId_005fcellHeight_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005ficonList_0026_005fvar_005ffilePath_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.release();
    _005fjspx_005ftagPool_005fwidget_005fforEachSubWidget_0026_005fwidgetContainer_005ffeed_005fentry.release();
    _005fjspx_005ftagPool_005frender_005fwidget_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fignoreFields_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.release();
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fsecurity_005fisUserConnected_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fset_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f4(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_config_005fgetOption_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f4(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f5(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f6(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f7(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_config_005fgetOption_005f8(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_string_005feval_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_request_005fgetParameterValue_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_request_005fgetParameterValue_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_search_005fgetPageName_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f5(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fchoose_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_widget_005fwidget_005f0(_jspx_page_context))
        return;
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(17,0) name = varWidget type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarWidget("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(17,0) name = varFeeds type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarFeeds("feeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(17,0) name = varParentEntry type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarParentEntry("parentEntry");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(17,0) name = varParentFeed type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarParentFeed("parentFeed");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f0_reused = false;
    try {
      _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(19,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setVar("nbDivisions");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(19,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(19,0) '12'",_jsp_getExpressionFactory().createValueExpression("12",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
      if (_jspx_th_c_005fset_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
      _jspx_th_c_005fset_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f1_reused = false;
    try {
      _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(20,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setVar("baseX");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(20,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(20,0) ''auto''",_jsp_getExpressionFactory().createValueExpression("'auto'",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
      if (_jspx_th_c_005fset_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
      _jspx_th_c_005fset_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f2_reused = false;
    try {
      _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(21,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setVar("baseY");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(21,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(21,0) '60'",_jsp_getExpressionFactory().createValueExpression("60",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
      if (_jspx_th_c_005fset_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
      _jspx_th_c_005fset_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f3_reused = false;
    try {
      _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(22,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setVar("marginX");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(22,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(22,0) '10'",_jsp_getExpressionFactory().createValueExpression("10",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
      if (_jspx_th_c_005fset_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
      _jspx_th_c_005fset_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f4_reused = false;
    try {
      _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(23,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setVar("marginY");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(23,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(23,0) '10'",_jsp_getExpressionFactory().createValueExpression("10",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
      if (_jspx_th_c_005fset_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
      _jspx_th_c_005fset_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f0 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f0_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f0.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(25,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setVar("widgetTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(25,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setName("widgetTitle");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(25,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f0.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f0 = _jspx_th_config_005fgetOption_005f0.doStartTag();
        if (_jspx_th_config_005fgetOption_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f0);
      _jspx_th_config_005fgetOption_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f0, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f1 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f1_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f1.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(26,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setVar("cellHeight");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(26,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setName("cellHeight");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(26,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f1.setDefaultValue("240");
      int[] _jspx_push_body_count_config_005fgetOption_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f1 = _jspx_th_config_005fgetOption_005f1.doStartTag();
        if (_jspx_th_config_005fgetOption_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f1);
      _jspx_th_config_005fgetOption_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f1, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f2 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f2_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f2.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(27,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setVar("editButtonLocationSelector");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(27,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setName("editButtonLocationSelector");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(27,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f2.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f2 = _jspx_th_config_005fgetOption_005f2.doStartTag();
        if (_jspx_th_config_005fgetOption_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f2);
      _jspx_th_config_005fgetOption_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f2, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f3 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f3_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f3.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(28,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setVar("editToolbarLocationSelector");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(28,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setName("editToolbarLocationSelector");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(28,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f3.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f3 = _jspx_th_config_005fgetOption_005f3.doStartTag();
        if (_jspx_th_config_005fgetOption_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f3);
      _jspx_th_config_005fgetOption_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f3, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f4 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f4_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f4.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(29,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setVar("hideAllCells");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(29,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setName("hideAllCells");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(29,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f4.setDefaultValue("");
      int[] _jspx_push_body_count_config_005fgetOption_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f4 = _jspx_th_config_005fgetOption_005f4.doStartTag();
        if (_jspx_th_config_005fgetOption_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f4);
      _jspx_th_config_005fgetOption_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f4, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f5(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f5 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f5_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f5.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f5.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(30,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setVar("allowEditPages");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(30,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setName("allowEditPages");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(30,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f5.setDefaultValue("true");
      int[] _jspx_push_body_count_config_005fgetOption_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f5 = _jspx_th_config_005fgetOption_005f5.doStartTag();
        if (_jspx_th_config_005fgetOption_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f5);
      _jspx_th_config_005fgetOption_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f5, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f6(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f6 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f6_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f6.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f6.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(31,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setVar("allowEditFilters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(31,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setName("allowEditFilters");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(31,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f6.setDefaultValue("true");
      int[] _jspx_push_body_count_config_005fgetOption_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f6 = _jspx_th_config_005fgetOption_005f6.doStartTag();
        if (_jspx_th_config_005fgetOption_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f6);
      _jspx_th_config_005fgetOption_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f6, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f7(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f7 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f7_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f7.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f7.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(32,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setVar("allowShare");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(32,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setName("allowShare");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(32,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f7.setDefaultValue("true");
      int[] _jspx_push_body_count_config_005fgetOption_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f7 = _jspx_th_config_005fgetOption_005f7.doStartTag();
        if (_jspx_th_config_005fgetOption_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f7);
      _jspx_th_config_005fgetOption_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f7, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_config_005fgetOption_005f8(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  config:getOption
    com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag _jspx_th_config_005fgetOption_005f8 = (com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag) _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.config.GetOptionTag.class);
    boolean _jspx_th_config_005fgetOption_005f8_reused = false;
    try {
      _jspx_th_config_005fgetOption_005f8.setPageContext(_jspx_page_context);
      _jspx_th_config_005fgetOption_005f8.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(33,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setVar("lockMashupPages");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(33,0) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setName("lockMashupPages");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(33,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_config_005fgetOption_005f8.setDefaultValue("false");
      int[] _jspx_push_body_count_config_005fgetOption_005f8 = new int[] { 0 };
      try {
        int _jspx_eval_config_005fgetOption_005f8 = _jspx_th_config_005fgetOption_005f8.doStartTag();
        if (_jspx_th_config_005fgetOption_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_config_005fgetOption_005f8[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_config_005fgetOption_005f8.doCatch(_jspx_exception);
      } finally {
        _jspx_th_config_005fgetOption_005f8.doFinally();
      }
      _005fjspx_005ftagPool_005fconfig_005fgetOption_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_config_005fgetOption_005f8);
      _jspx_th_config_005fgetOption_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_config_005fgetOption_005f8, _jsp_getInstanceManager(), _jspx_th_config_005fgetOption_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f0_reused = false;
    try {
      _jspx_th_string_005feval_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(35,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setVar("user");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(35,0) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setString("${security.username}");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(35,0) name = isJsEscape type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setIsJsEscape(true);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(35,0) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005feval_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f0 = _jspx_th_string_005feval_005f0.doStartTag();
        if (_jspx_th_string_005feval_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fisJsEscape_005ffeeds_005fnobody.reuse(_jspx_th_string_005feval_005f0);
      _jspx_th_string_005feval_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f0, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetParameterValue_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getParameterValue
    com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag _jspx_th_request_005fgetParameterValue_005f0 = (com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag) _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag.class);
    boolean _jspx_th_request_005fgetParameterValue_005f0_reused = false;
    try {
      _jspx_th_request_005fgetParameterValue_005f0.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetParameterValue_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(37,0) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setName("chartboardId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(37,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setVar("chartboardId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(37,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setDefaultValue("");
      int[] _jspx_push_body_count_request_005fgetParameterValue_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetParameterValue_005f0 = _jspx_th_request_005fgetParameterValue_005f0.doStartTag();
        if (_jspx_th_request_005fgetParameterValue_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetParameterValue_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetParameterValue_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetParameterValue_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_request_005fgetParameterValue_005f0);
      _jspx_th_request_005fgetParameterValue_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetParameterValue_005f0, _jsp_getInstanceManager(), _jspx_th_request_005fgetParameterValue_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetParameterValue_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getParameterValue
    com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag _jspx_th_request_005fgetParameterValue_005f1 = (com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag) _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag.class);
    boolean _jspx_th_request_005fgetParameterValue_005f1_reused = false;
    try {
      _jspx_th_request_005fgetParameterValue_005f1.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetParameterValue_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(38,0) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f1.setName("pageId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(38,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f1.setVar("pageId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(38,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f1.setDefaultValue("");
      int[] _jspx_push_body_count_request_005fgetParameterValue_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetParameterValue_005f1 = _jspx_th_request_005fgetParameterValue_005f1.doStartTag();
        if (_jspx_th_request_005fgetParameterValue_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetParameterValue_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetParameterValue_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetParameterValue_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_request_005fgetParameterValue_005f1);
      _jspx_th_request_005fgetParameterValue_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetParameterValue_005f1, _jsp_getInstanceManager(), _jspx_th_request_005fgetParameterValue_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetPageName_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getPageName
    com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag _jspx_th_search_005fgetPageName_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag) _005fjspx_005ftagPool_005fsearch_005fgetPageName_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag.class);
    boolean _jspx_th_search_005fgetPageName_005f0_reused = false;
    try {
      _jspx_th_search_005fgetPageName_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetPageName_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(39,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetPageName_005f0.setVar("pageName");
      int[] _jspx_push_body_count_search_005fgetPageName_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetPageName_005f0 = _jspx_th_search_005fgetPageName_005f0.doStartTag();
        if (_jspx_th_search_005fgetPageName_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetPageName_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetPageName_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetPageName_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetPageName_0026_005fvar_005fnobody.reuse(_jspx_th_search_005fgetPageName_005f0);
      _jspx_th_search_005fgetPageName_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetPageName_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetPageName_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f5(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f5 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f5_reused = false;
    try {
      _jspx_th_c_005fset_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f5.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(40,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setVar("isMashupPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(40,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(40,0) 'false'",_jsp_getExpressionFactory().createValueExpression("false",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f5 = _jspx_th_c_005fset_005f5.doStartTag();
      if (_jspx_th_c_005fset_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f5);
      _jspx_th_c_005fset_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent(null);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(43,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageId == undefined || pageId == ''}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fset_005f6(_jspx_th_c_005fwhen_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fset_005f7(_jspx_th_c_005fwhen_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f6 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f6_reused = false;
    try {
      _jspx_th_c_005fset_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(44,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setVar("pageId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(44,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(44,8) '${pageName}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${pageName}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f6 = _jspx_th_c_005fset_005f6.doStartTag();
      if (_jspx_th_c_005fset_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f6);
      _jspx_th_c_005fset_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f7 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f7_reused = false;
    try {
      _jspx_th_c_005fset_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(45,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setVar("isMashupPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(45,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(45,8) 'true'",_jsp_getExpressionFactory().createValueExpression("true",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f7 = _jspx_th_c_005fset_005f7.doStartTag();
      if (_jspx_th_c_005fset_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f7);
      _jspx_th_c_005fset_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fif_005f0(_jspx_th_c_005fotherwise_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(48,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageName == pageId}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fset_005f8(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f8 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f8_reused = false;
    try {
      _jspx_th_c_005fset_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(49,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f8.setVar("isMashupPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(49,12) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f8.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(49,12) 'true'",_jsp_getExpressionFactory().createValueExpression("true",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f8 = _jspx_th_c_005fset_005f8.doStartTag();
      if (_jspx_th_c_005fset_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f8);
      _jspx_th_c_005fset_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fwidget_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:widget
    com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag _jspx_th_widget_005fwidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag) _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss.get(com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag.class);
    boolean _jspx_th_widget_005fwidget_005f0_reused = false;
    try {
      _jspx_th_widget_005fwidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fwidget_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(54,0) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setExtraCss("chartboard");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(54,0) name = varUcssId type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setVarUcssId("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(54,0) name = varCssId type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setVarCssId("cssId");
      int[] _jspx_push_body_count_widget_005fwidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fwidget_005f0 = _jspx_th_widget_005fwidget_005f0.doStartTag();
        if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fwidget_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fwidget_005f0);
          }
          do {
            out.write("\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fif_005f1(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_widget_005fcontent_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fif_005f8(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fchoose_005f5(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_render_005frenderScript_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            int evalDoAfterBody = _jspx_th_widget_005fwidget_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fwidget_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fwidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fwidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fwidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fwidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss.reuse(_jspx_th_widget_005fwidget_005f0);
      _jspx_th_widget_005fwidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fwidget_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fwidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(56,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty widgetTitle || empty editButtonLocationSelector}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_widget_005fheader_005f0(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fheader_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:header
    com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag _jspx_th_widget_005fheader_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag) _005fjspx_005ftagPool_005fwidget_005fheader_0026_005fextraCss.get(com.exalead.cv360.searchui.view.jspapi.widget.HeaderTag.class);
    boolean _jspx_th_widget_005fheader_005f0_reused = false;
    try {
      _jspx_th_widget_005fheader_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fheader_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(57,8) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fheader_005f0.setExtraCss("chartboard-header");
      int[] _jspx_push_body_count_widget_005fheader_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fheader_005f0 = _jspx_th_widget_005fheader_005f0.doStartTag();
        if (_jspx_eval_widget_005fheader_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fheader_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fheader_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fheader_005f0);
          }
          do {
            out.write("\r\n");
            out.write("            <span class=\"widgetTitle\">");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widgetTitle}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("</span>\r\n");
            out.write("        ");
            int evalDoAfterBody = _jspx_th_widget_005fheader_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fheader_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fheader_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fheader_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fheader_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fheader_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fheader_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fheader_0026_005fextraCss.reuse(_jspx_th_widget_005fheader_005f0);
      _jspx_th_widget_005fheader_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fheader_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fheader_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fcontent_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:content
    com.exalead.cv360.searchui.view.jspapi.widget.ContentTag _jspx_th_widget_005fcontent_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.ContentTag) _005fjspx_005ftagPool_005fwidget_005fcontent.get(com.exalead.cv360.searchui.view.jspapi.widget.ContentTag.class);
    boolean _jspx_th_widget_005fcontent_005f0_reused = false;
    try {
      _jspx_th_widget_005fcontent_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fcontent_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      int[] _jspx_push_body_count_widget_005fcontent_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fcontent_005f0 = _jspx_th_widget_005fcontent_005f0.doStartTag();
        if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fcontent_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fcontent_005f0);
          }
          do {
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_plma_005fgetChartboardConfig_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("        <div class=\"chartboard-popup-tinymce\">\r\n");
            out.write("            <div class=\"chartboard-popup-tinymce-popup\">\r\n");
            out.write("                <div class=\"chartboard-tinymce-template\">\r\n");
            out.write("                    <div class=\"chartboard-tinymce-title\">");
            if (_jspx_meth_i18n_005fmessage_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</div>\r\n");
            out.write("                    <div class=\"chartboard-tinymce-template-elem chartboard-tinymce-template-title active\">\r\n");
            out.write("                        <span class=\"title\">");
            if (_jspx_meth_i18n_005fmessage_005f1(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("                        <span class=\"text\">");
            if (_jspx_meth_i18n_005fmessage_005f2(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("                    </div>\r\n");
            out.write("                    <div class=\"chartboard-tinymce-template-elem chartboard-tinymce-template-note\">\r\n");
            out.write("                        <span class=\"title\">");
            if (_jspx_meth_i18n_005fmessage_005f3(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("                        <span class=\"text\">");
            if (_jspx_meth_i18n_005fmessage_005f4(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("                    </div>\r\n");
            out.write("                    <div class=\"chartboard-tinymce-template-elem chartboard-tinymce-template-widget\">\r\n");
            out.write("                        <span class=\"title\">");
            if (_jspx_meth_i18n_005fmessage_005f5(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("                        <span class=\"text\">");
            if (_jspx_meth_i18n_005fmessage_005f6(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("                    </div>\r\n");
            out.write("                    <div class=\"chartboard-tinymce-template-elem chartboard-tinymce-template-arrow\">\r\n");
            out.write("                        <span class=\"title\">");
            if (_jspx_meth_i18n_005fmessage_005f7(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("                        <span class=\"text\">");
            if (_jspx_meth_i18n_005fmessage_005f8(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("                        <span class=\"arrow\"></span>\r\n");
            out.write("                    </div>\r\n");
            out.write("                    <div class=\"chartboard-tinymce-template-elem chartboard-tinymce-template-material\">\r\n");
            out.write("                        <span class=\"title\">");
            if (_jspx_meth_i18n_005fmessage_005f9(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("                        <span class=\"text\">");
            if (_jspx_meth_i18n_005fmessage_005f10(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span>\r\n");
            out.write("                    </div>\r\n");
            out.write("                </div>\r\n");
            out.write("                <div class=\"chartboard-tinymce-container\">\r\n");
            out.write("                    <div class=\"chartboard-tinymce-title\">");
            if (_jspx_meth_i18n_005fmessage_005f11(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</div>\r\n");
            out.write("                    <div class=\"chartboard-tinymce-title-container\">\r\n");
            out.write("                        <span>");
            if (_jspx_meth_i18n_005fmessage_005f12(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write(": </span>\r\n");
            out.write("                        <input type=\"text\"/>\r\n");
            out.write("                    </div>\r\n");
            out.write("                        ");
            out.write("\r\n");
            out.write("                    <div class=\"chartboard-tinymce-editor-container\">\r\n");
            out.write("                        <div class=\"chartboard-tinymce-editor\"></div>\r\n");
            out.write("                    </div>\r\n");
            out.write("                </div>\r\n");
            out.write("            </div>\r\n");
            out.write("        </div>\r\n");
            out.write("\r\n");
            out.write("        <div class=\"chartboard-icon-list\" style=\"display: none;\">\r\n");
            out.write("            <input class=\"chartboard-icon-list-input\"/>\r\n");
            out.write("            ");
            if (_jspx_meth_plma_005ficonList_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("            ");
            if (_jspx_meth_c_005fforEach_005f0(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("        </div>\r\n");
            out.write("\r\n");
            out.write("        <div class=\"chartboard-layout-container\">\r\n");
            out.write("            <div class=\"chartboard-layout grid-stack\">\r\n");
            out.write("                ");
            if (_jspx_meth_c_005fset_005f9(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_c_005fif_005f2(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_c_005fchoose_005f1(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("            </div>\r\n");
            out.write("        </div>\r\n");
            out.write("\r\n");
            out.write("        <div class=\"chartboard-sidebar\">\r\n");
            out.write("            <div class=\"sidebar-block-title\">");
            if (_jspx_meth_i18n_005fmessage_005f14(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</div>\r\n");
            out.write("            <div class=\"search-bar-container\">\r\n");
            out.write("                <div class=\"search-input\">\r\n");
            out.write("                    <input type=\"text\" title=\"");
            if (_jspx_meth_i18n_005fmessage_005f15(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\"/>\r\n");
            out.write("                    <span class=\"fonticon fonticon-search\"></span>\r\n");
            out.write("                </div>\r\n");
            out.write("            </div>\r\n");
            out.write("            <div class=\"available-widgets\">\r\n");
            out.write("                ");
            if (_jspx_meth_c_005fforEach_005f2(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\r\n");
            out.write("            </div>\r\n");
            out.write("            <div class=\"trash-area\">\r\n");
            out.write("                <div class=\"trash-icon fonticon fonticon-trash\">\r\n");
            out.write("                </div>\r\n");
            out.write("            </div>\r\n");
            out.write("            <div class=\"sidebar-block-title\">");
            if (_jspx_meth_i18n_005fmessage_005f16(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</div>\r\n");
            out.write("            <div class=\"add-custom-widgets grid-stack\">\r\n");
            out.write("                <div class=\"grid-stack-item custom-widget\"\r\n");
            out.write("                     data-is-not-saved=\"true\"\r\n");
            out.write("                     data-cell-cssclass=\"chartboard-tinymce-template-note\"\r\n");
            out.write("                     data-gs-x=\"0\"\r\n");
            out.write("                     data-gs-y=\"0\"\r\n");
            out.write("                     data-gs-width=\"2\"\r\n");
            out.write("                     data-gs-height=\"1\">\r\n");
            out.write("                    <div class=\"tile grid-stack-item-content\">\r\n");
            out.write("                        <div class=\"content chartboard-tinymce-template-note\"><span\r\n");
            out.write("                                class=\"custom-widget-title\">");
            if (_jspx_meth_i18n_005fmessage_005f17(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("</span><span\r\n");
            out.write("                                class=\"custom-widget-icon fonticon fonticon-comment\"></span></div>\r\n");
            out.write("                        <div class=\"editable-custom-widget-icon fonticon fonticon-pencil\"\r\n");
            out.write("                             title=\"");
            if (_jspx_meth_i18n_005fmessage_005f18(_jspx_th_widget_005fcontent_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
              return true;
            out.write("\"></div>\r\n");
            out.write("                    </div>\r\n");
            out.write("                </div>\r\n");
            out.write("            </div>\r\n");
            out.write("        </div>\r\n");
            out.write("    ");
            int evalDoAfterBody = _jspx_th_widget_005fcontent_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fcontent_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fcontent_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fcontent_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fcontent_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fcontent_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fcontent_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fcontent.reuse(_jspx_th_widget_005fcontent_005f0);
      _jspx_th_widget_005fcontent_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fcontent_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fcontent_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetChartboardConfig_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getChartboardConfig
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.chartboard.GetCharboardFromStorageTag _jspx_th_plma_005fgetChartboardConfig_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.chartboard.GetCharboardFromStorageTag) _005fjspx_005ftagPool_005fplma_005fgetChartboardConfig_0026_005fvarCells_005fvar_005fpageId_005fnbColumns_005fmashupPage_005flayout_005fhideAllCells_005fchartBoardId_005fcellHeight_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.chartboard.GetCharboardFromStorageTag.class);
    boolean _jspx_th_plma_005fgetChartboardConfig_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetChartboardConfig_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetChartboardConfig_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(63,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetChartboardConfig_005f0.setVar("chartboardConfig");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(63,8) name = varCells type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetChartboardConfig_005f0.setVarCells("cellsMap");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(63,8) name = layout type = com.exalead.cv360.searchui.configuration.v10.Layout reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetChartboardConfig_005f0.setLayout((com.exalead.cv360.searchui.configuration.v10.Layout) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget.layout}", com.exalead.cv360.searchui.configuration.v10.Layout.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(63,8) name = chartBoardId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetChartboardConfig_005f0.setChartBoardId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${chartboardId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(63,8) name = pageId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetChartboardConfig_005f0.setPageId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(63,8) name = mashupPage type = java.lang.Boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetChartboardConfig_005f0.setMashupPage((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isMashupPage}", java.lang.Boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(63,8) name = nbColumns type = java.lang.Integer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetChartboardConfig_005f0.setNbColumns(((java.lang.Integer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${nbDivisions}", int.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).intValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(63,8) name = cellHeight type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetChartboardConfig_005f0.setCellHeight(((java.lang.Integer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cellHeight}", int.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).intValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(63,8) name = hideAllCells type = java.lang.Boolean reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetChartboardConfig_005f0.setHideAllCells((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${hideAllCells}", java.lang.Boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetChartboardConfig_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetChartboardConfig_005f0 = _jspx_th_plma_005fgetChartboardConfig_005f0.doStartTag();
        if (_jspx_th_plma_005fgetChartboardConfig_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetChartboardConfig_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetChartboardConfig_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetChartboardConfig_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetChartboardConfig_0026_005fvarCells_005fvar_005fpageId_005fnbColumns_005fmashupPage_005flayout_005fhideAllCells_005fchartBoardId_005fcellHeight_005fnobody.reuse(_jspx_th_plma_005fgetChartboardConfig_005f0);
      _jspx_th_plma_005fgetChartboardConfig_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetChartboardConfig_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetChartboardConfig_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f0 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f0_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f0.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(76,58) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setCode("chartboard.template.name");
      int[] _jspx_push_body_count_i18n_005fmessage_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f0 = _jspx_th_i18n_005fmessage_005f0.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f0);
      _jspx_th_i18n_005fmessage_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f0, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f1 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f1_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f1.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(78,44) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setCode("chartboard.template.title");
      int[] _jspx_push_body_count_i18n_005fmessage_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f1 = _jspx_th_i18n_005fmessage_005f1.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f1);
      _jspx_th_i18n_005fmessage_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f1, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f2 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f2_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f2.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(79,43) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setCode("chartboard.template.text");
      int[] _jspx_push_body_count_i18n_005fmessage_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f2 = _jspx_th_i18n_005fmessage_005f2.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f2);
      _jspx_th_i18n_005fmessage_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f2, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f3 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f3_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f3.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(82,44) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f3.setCode("chartboard.template.title");
      int[] _jspx_push_body_count_i18n_005fmessage_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f3 = _jspx_th_i18n_005fmessage_005f3.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f3);
      _jspx_th_i18n_005fmessage_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f3, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f4 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f4_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f4.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(83,43) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f4.setCode("chartboard.template.text");
      int[] _jspx_push_body_count_i18n_005fmessage_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f4 = _jspx_th_i18n_005fmessage_005f4.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f4);
      _jspx_th_i18n_005fmessage_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f4, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f5 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f5_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f5.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(86,44) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f5.setCode("chartboard.template.title");
      int[] _jspx_push_body_count_i18n_005fmessage_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f5 = _jspx_th_i18n_005fmessage_005f5.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f5);
      _jspx_th_i18n_005fmessage_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f5, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f6 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f6_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f6.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(87,43) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f6.setCode("chartboard.template.text");
      int[] _jspx_push_body_count_i18n_005fmessage_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f6 = _jspx_th_i18n_005fmessage_005f6.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f6);
      _jspx_th_i18n_005fmessage_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f6, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f7 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f7_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f7.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(90,44) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f7.setCode("chartboard.template.title");
      int[] _jspx_push_body_count_i18n_005fmessage_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f7 = _jspx_th_i18n_005fmessage_005f7.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f7);
      _jspx_th_i18n_005fmessage_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f7, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f8 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f8_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f8.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(91,43) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f8.setCode("chartboard.template.text");
      int[] _jspx_push_body_count_i18n_005fmessage_005f8 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f8 = _jspx_th_i18n_005fmessage_005f8.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f8[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f8.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f8.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f8);
      _jspx_th_i18n_005fmessage_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f8, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f9 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f9_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f9.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(95,44) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f9.setCode("chartboard.template.title");
      int[] _jspx_push_body_count_i18n_005fmessage_005f9 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f9 = _jspx_th_i18n_005fmessage_005f9.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f9[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f9.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f9.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f9);
      _jspx_th_i18n_005fmessage_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f9, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f10 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f10_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f10.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f10.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(96,43) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f10.setCode("chartboard.template.text");
      int[] _jspx_push_body_count_i18n_005fmessage_005f10 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f10 = _jspx_th_i18n_005fmessage_005f10.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f10[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f10.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f10.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f10);
      _jspx_th_i18n_005fmessage_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f10, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f11 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f11_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f11.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f11.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(100,58) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f11.setCode("chartboard.note");
      int[] _jspx_push_body_count_i18n_005fmessage_005f11 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f11 = _jspx_th_i18n_005fmessage_005f11.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f11[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f11.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f11.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f11);
      _jspx_th_i18n_005fmessage_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f11, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f12 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f12_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f12.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f12.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(102,30) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f12.setCode("chartboard.note.title");
      int[] _jspx_push_body_count_i18n_005fmessage_005f12 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f12 = _jspx_th_i18n_005fmessage_005f12.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f12.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f12[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f12.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f12.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f12);
      _jspx_th_i18n_005fmessage_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f12, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005ficonList_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:iconList
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetIconsTag _jspx_th_plma_005ficonList_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetIconsTag) _005fjspx_005ftagPool_005fplma_005ficonList_0026_005fvar_005ffilePath_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetIconsTag.class);
    boolean _jspx_th_plma_005ficonList_005f0_reused = false;
    try {
      _jspx_th_plma_005ficonList_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005ficonList_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(115,12) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ficonList_005f0.setVar("iconList");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(115,12) name = filePath type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ficonList_005f0.setFilePath("");
      int[] _jspx_push_body_count_plma_005ficonList_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005ficonList_005f0 = _jspx_th_plma_005ficonList_005f0.doStartTag();
        if (_jspx_th_plma_005ficonList_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005ficonList_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005ficonList_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005ficonList_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005ficonList_0026_005fvar_005ffilePath_005fnobody.reuse(_jspx_th_plma_005ficonList_005f0);
      _jspx_th_plma_005ficonList_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005ficonList_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005ficonList_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f0_reused = false;
    try {
      _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(116,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setVar("icon");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(116,12) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(116,12) '${iconList}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${iconList}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
        if (_jspx_eval_c_005fforEach_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                <span class=\"fonticon-elem fonticon ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${icon}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\"></span>\r\n");
            out.write("            ");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
      _jspx_th_c_005fforEach_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f9 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f9_reused = false;
    try {
      _jspx_th_c_005fset_005f9.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(123,16) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f9.setVar("isAllowed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(123,16) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f9.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(123,16) 'true'",_jsp_getExpressionFactory().createValueExpression("true",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f9 = _jspx_th_c_005fset_005f9.doStartTag();
      if (_jspx_th_c_005fset_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f9);
      _jspx_th_c_005fset_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(124,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${writer == 'true' || reader == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fset_005f10(_jspx_th_c_005fif_005f2, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f10 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f10_reused = false;
    try {
      _jspx_th_c_005fset_005f10.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f10.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(125,20) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f10.setVar("isAllowed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(125,20) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f10.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(125,20) 'true'",_jsp_getExpressionFactory().createValueExpression("true",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f10 = _jspx_th_c_005fset_005f10.doStartTag();
      if (_jspx_th_c_005fset_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f10);
      _jspx_th_c_005fset_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f10, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f1 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f1_reused = false;
    try {
      _jspx_th_c_005fchoose_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      int _jspx_eval_c_005fchoose_005f1 = _jspx_th_c_005fchoose_005f1.doStartTag();
      if (_jspx_eval_c_005fchoose_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    ");
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fwhen_005f1(_jspx_th_c_005fchoose_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fotherwise_005f1(_jspx_th_c_005fchoose_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f1);
      _jspx_th_c_005fchoose_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f1 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f1_reused = false;
    try {
      _jspx_th_c_005fwhen_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(129,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${!isAllowed.equals('true') && isMashupPage != 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f1 = _jspx_th_c_005fwhen_005f1.doStartTag();
      if (_jspx_eval_c_005fwhen_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_render_005ftemplate_005f0(_jspx_th_c_005fwhen_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f1);
      _jspx_th_c_005fwhen_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f0);
    try {
      _jspx_th_render_005ftemplate_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f0.setParent(_jspx_th_c_005fwhen_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(130,24) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setTemplate("template/unauthorizedPage.jsp");
      _jspx_th_render_005ftemplate_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f1 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f1_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      int _jspx_eval_c_005fotherwise_005f1 = _jspx_th_c_005fotherwise_005f1.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        <div id=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("-notification-template\" class=\"notification-template\">\r\n");
          out.write("                            ");
          if (_jspx_meth_render_005ftemplate_005f1(_jspx_th_c_005fotherwise_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("\r\n");
          out.write("                        </div>\r\n");
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_c_005fset_005f11(_jspx_th_c_005fotherwise_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_c_005fforEach_005f1(_jspx_th_c_005fotherwise_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fcontent_005f0))
            return true;
          out.write("\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f1);
      _jspx_th_c_005fotherwise_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f1);
    try {
      _jspx_th_render_005ftemplate_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f1.setParent(_jspx_th_c_005fotherwise_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(134,28) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f1.setTemplate("../plmaResources/template/message/message.jsp");
      _jspx_th_render_005ftemplate_005f1.setJspBody(new Helper( 0, _jspx_page_context, _jspx_th_render_005ftemplate_005f1, _jspx_push_body_count_widget_005fcontent_005f0));
      _jspx_th_render_005ftemplate_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f0);
    try {
      _jspx_th_render_005fparameter_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f0.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(135,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setName("result");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(135,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setValue("alert");
      _jspx_th_render_005fparameter_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f1);
    try {
      _jspx_th_render_005fparameter_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f1.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(136,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setName("message");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(136,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setValue("");
      _jspx_th_render_005fparameter_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f2);
    try {
      _jspx_th_render_005fparameter_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f2.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(137,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setName("container");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(137,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}-notification-template", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f11 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f11_reused = false;
    try {
      _jspx_th_c_005fset_005f11.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f11.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(141,24) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f11.setVar("customWidgetCount");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(141,24) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f11.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(141,24) '0'",_jsp_getExpressionFactory().createValueExpression("0",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f11 = _jspx_th_c_005fset_005f11.doStartTag();
      if (_jspx_th_c_005fset_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f11);
      _jspx_th_c_005fset_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f11, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f1 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f1_reused = false;
    try {
      _jspx_th_c_005fforEach_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(143,24) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f1.setVar("tileConfig");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(143,24) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f1.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(143,24) '${chartboardConfig.tiles}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${chartboardConfig.tiles}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int[] _jspx_push_body_count_c_005fforEach_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f1 = _jspx_th_c_005fforEach_005f1.doStartTag();
        if (_jspx_eval_c_005fforEach_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                            ");
            out.write("\r\n");
            out.write("                            ");
            if (_jspx_meth_c_005fset_005f12(_jspx_th_c_005fforEach_005f1, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
              return true;
            out.write("\r\n");
            out.write("                            ");
            if (_jspx_meth_c_005fif_005f3(_jspx_th_c_005fforEach_005f1, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
              return true;
            out.write("\r\n");
            out.write("                        ");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f1);
      _jspx_th_c_005fforEach_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f12 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f12_reused = false;
    try {
      _jspx_th_c_005fset_005f12.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f12.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(145,28) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f12.setVar("isCustomWidget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(145,28) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f12.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(145,28) '${empty tileConfig.cellId}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${empty tileConfig.cellId}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f12 = _jspx_th_c_005fset_005f12.doStartTag();
      if (_jspx_th_c_005fset_005f12.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f12);
      _jspx_th_c_005fset_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f12, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(146,28) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.displayed}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                <div class=\"grid-stack-item ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty tileConfig.cssClass ? tileConfig.cssClass : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(' ');
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isCustomWidget ? 'custom-widget' : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                                     ");
          if (_jspx_meth_c_005fif_005f4(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write("\r\n");
          out.write("                                        ");
          if (_jspx_meth_c_005fchoose_005f2(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write("\r\n");
          out.write("                                     data-cell-cssclass=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.cssClass}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                                     data-gs-x=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.x}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                                     data-gs-y=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.y}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                                     data-y=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.y}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                                     data-gs-width=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.width}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                                     data-gs-height=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.height}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                                     data-displayed=\"true\">\r\n");
          out.write("                                    <div class=\"tile grid-stack-item-content\">\r\n");
          out.write("                                        ");
          if (_jspx_meth_c_005fchoose_005f3(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write("\r\n");
          out.write("                                    </div>\r\n");
          out.write("                                </div>\r\n");
          out.write("                            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f4_reused = false;
    try {
      _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(148,37) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty tileConfig.cssId}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
      if (_jspx_eval_c_005fif_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("id=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write('"');
          out.write(' ');
          int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      _jspx_th_c_005fif_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f2 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f2_reused = false;
    try {
      _jspx_th_c_005fchoose_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      int _jspx_eval_c_005fchoose_005f2 = _jspx_th_c_005fchoose_005f2.doStartTag();
      if (_jspx_eval_c_005fchoose_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                            ");
          if (_jspx_meth_c_005fwhen_005f2(_jspx_th_c_005fchoose_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write("\r\n");
          out.write("                                            ");
          if (_jspx_meth_c_005fotherwise_005f2(_jspx_th_c_005fchoose_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write("\r\n");
          out.write("                                        ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f2);
      _jspx_th_c_005fchoose_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f2 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f2_reused = false;
    try {
      _jspx_th_c_005fwhen_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(150,44) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${!isCustomWidget}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f2 = _jspx_th_c_005fwhen_005f2.doStartTag();
      if (_jspx_eval_c_005fwhen_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                                data-cell-id=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.cellId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                                            ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f2);
      _jspx_th_c_005fwhen_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f2 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f2_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f2);
      int _jspx_eval_c_005fotherwise_005f2 = _jspx_th_c_005fotherwise_005f2.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                                data-custom-cell-id=\"chartboard-custom-widget-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${customWidgetCount}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                                                ");
          if (_jspx_meth_c_005fset_005f13(_jspx_th_c_005fotherwise_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write("\r\n");
          out.write("                                            ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f2);
      _jspx_th_c_005fotherwise_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f13 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f13_reused = false;
    try {
      _jspx_th_c_005fset_005f13.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f13.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(155,48) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f13.setVar("customWidgetCount");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(155,48) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f13.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(155,48) '${customWidgetCount + 1}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${customWidgetCount + 1}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f13 = _jspx_th_c_005fset_005f13.doStartTag();
      if (_jspx_th_c_005fset_005f13.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f13);
      _jspx_th_c_005fset_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f13, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f3 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f3_reused = false;
    try {
      _jspx_th_c_005fchoose_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      int _jspx_eval_c_005fchoose_005f3 = _jspx_th_c_005fchoose_005f3.doStartTag();
      if (_jspx_eval_c_005fchoose_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                            ");
          if (_jspx_meth_c_005fwhen_005f3(_jspx_th_c_005fchoose_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write("\r\n");
          out.write("                                            ");
          out.write("\r\n");
          out.write("                                            ");
          if (_jspx_meth_c_005fotherwise_005f3(_jspx_th_c_005fchoose_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write("\r\n");
          out.write("                                        ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f3);
      _jspx_th_c_005fchoose_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f3 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f3_reused = false;
    try {
      _jspx_th_c_005fwhen_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(167,44) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${!isCustomWidget}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f3 = _jspx_th_c_005fwhen_005f3.doStartTag();
      if (_jspx_eval_c_005fwhen_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                                ");
          if (_jspx_meth_widget_005fforEachSubWidget_005f0(_jspx_th_c_005fwhen_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write("\r\n");
          out.write("                                                <div class=\"grid-stack-hide-item hidden\"></div>\r\n");
          out.write("                                            ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f3);
      _jspx_th_c_005fwhen_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fforEachSubWidget_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:forEachSubWidget
    com.exalead.cv360.searchui.view.jspapi.widget.ForEachSubWidgetTag _jspx_th_widget_005fforEachSubWidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.ForEachSubWidgetTag) _005fjspx_005ftagPool_005fwidget_005fforEachSubWidget_0026_005fwidgetContainer_005ffeed_005fentry.get(com.exalead.cv360.searchui.view.jspapi.widget.ForEachSubWidgetTag.class);
    boolean _jspx_th_widget_005fforEachSubWidget_005f0_reused = false;
    try {
      _jspx_th_widget_005fforEachSubWidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fforEachSubWidget_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(168,48) name = widgetContainer type = com.exalead.cv360.searchui.configuration.v10.WidgetContainer reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fforEachSubWidget_005f0.setWidgetContainer((com.exalead.cv360.searchui.configuration.v10.WidgetContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cellsMap[tileConfig.cellId]}", com.exalead.cv360.searchui.configuration.v10.WidgetContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(168,48) name = feed type = com.exalead.access.feedapi.ResultFeed reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fforEachSubWidget_005f0.setFeed((com.exalead.access.feedapi.ResultFeed) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${parentFeed}", com.exalead.access.feedapi.ResultFeed.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(168,48) name = entry type = com.exalead.access.feedapi.Entry reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fforEachSubWidget_005f0.setEntry((com.exalead.access.feedapi.Entry) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${parentEntry}", com.exalead.access.feedapi.Entry.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_widget_005fforEachSubWidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fforEachSubWidget_005f0 = _jspx_th_widget_005fforEachSubWidget_005f0.doStartTag();
        if (_jspx_eval_widget_005fforEachSubWidget_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                                                    ");
            if (_jspx_meth_render_005fwidget_005f0(_jspx_th_widget_005fforEachSubWidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fforEachSubWidget_005f0))
              return true;
            out.write("\r\n");
            out.write("                                                ");
            int evalDoAfterBody = _jspx_th_widget_005fforEachSubWidget_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_widget_005fforEachSubWidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fforEachSubWidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fforEachSubWidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fforEachSubWidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fforEachSubWidget_0026_005fwidgetContainer_005ffeed_005fentry.reuse(_jspx_th_widget_005fforEachSubWidget_005f0);
      _jspx_th_widget_005fforEachSubWidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fforEachSubWidget_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fforEachSubWidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fwidget_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fforEachSubWidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fforEachSubWidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:widget
    com.exalead.cv360.searchui.view.jspapi.render.WidgetTag _jspx_th_render_005fwidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.WidgetTag) _005fjspx_005ftagPool_005frender_005fwidget_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.render.WidgetTag.class);
    boolean _jspx_th_render_005fwidget_005f0_reused = false;
    try {
      _jspx_th_render_005fwidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005fwidget_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fforEachSubWidget_005f0);
      int[] _jspx_push_body_count_render_005fwidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005fwidget_005f0 = _jspx_th_render_005fwidget_005f0.doStartTag();
        if (_jspx_th_render_005fwidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005fwidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005fwidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005fwidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005fwidget_005fnobody.reuse(_jspx_th_render_005fwidget_005f0);
      _jspx_th_render_005fwidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005fwidget_005f0, _jsp_getInstanceManager(), _jspx_th_render_005fwidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f3 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f3_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f3);
      int _jspx_eval_c_005fotherwise_005f3 = _jspx_th_c_005fotherwise_005f3.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                                <div class=\"content ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty tileConfig.cssClass ? tileConfig.cssClass : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(' ');
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty tileConfig.backgroundColor ? '' : 'no-color'}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(' ');
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty tileConfig.arrowSide ? tileConfig.arrowSide : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                                                     style=\"background-color:");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty tileConfig.backgroundColor ? tileConfig.backgroundColor : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(";\"\r\n");
          out.write("                                                     data-background-color=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty tileConfig.backgroundColor ? tileConfig.backgroundColor : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                                                     data-arrow-side=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty tileConfig.arrowSide ? tileConfig.arrowSide : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("                                                        ");
          out.write("\r\n");
          out.write("                                                        ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.preview}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\r\n");
          out.write("                                                    ");
          if (_jspx_meth_c_005fif_005f5(_jspx_th_c_005fotherwise_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write("\r\n");
          out.write("                                                </div>\r\n");
          out.write("                                                <div class=\"editable-custom-widget-icon fonticon fonticon-pencil\"\r\n");
          out.write("                                                     title=\"");
          if (_jspx_meth_i18n_005fmessage_005f13(_jspx_th_c_005fotherwise_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f1))
            return true;
          out.write("\"></div>\r\n");
          out.write("                                            ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f3);
      _jspx_th_c_005fotherwise_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f5 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f5_reused = false;
    try {
      _jspx_th_c_005fif_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(183,52) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.cssClass == 'chartboard-tinymce-template-arrow'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f5 = _jspx_th_c_005fif_005f5.doStartTag();
      if (_jspx_eval_c_005fif_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                                        <div class=\"arrow-container ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty tileConfig.arrowSide ? tileConfig.arrowSide : 'left'}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("                                                            <div class=\"arrow-relative-container\"\r\n");
          out.write("                                                                 style=\"border-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.arrowSide == 'left' ? 'right' : tileConfig.arrowSide == 'top' ? 'bottom' : tileConfig.arrowSide == 'right' ? 'left' : tileConfig.arrowSide == 'bottom' ? 'top' : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("-color:");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty tileConfig.backgroundColor ? tileConfig.backgroundColor : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(";\"></div>\r\n");
          out.write("                                                        </div>\r\n");
          out.write("                                                    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f5);
      _jspx_th_c_005fif_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f13 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f13_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f13.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f13.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(191,60) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f13.setCode("chartboard.focus.tile");
      int[] _jspx_push_body_count_i18n_005fmessage_005f13 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f13 = _jspx_th_i18n_005fmessage_005f13.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f13.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f13[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f13.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f13.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f13);
      _jspx_th_i18n_005fmessage_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f13, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f14 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f14_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f14.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f14.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(204,45) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f14.setCode("chartboard.avalaible.widgets");
      int[] _jspx_push_body_count_i18n_005fmessage_005f14 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f14 = _jspx_th_i18n_005fmessage_005f14.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f14.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f14[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f14.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f14.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f14);
      _jspx_th_i18n_005fmessage_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f14, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f15(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f15 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f15_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f15.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f15.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(207,46) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f15.setCode("chartboard.search.available");
      int[] _jspx_push_body_count_i18n_005fmessage_005f15 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f15 = _jspx_th_i18n_005fmessage_005f15.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f15.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f15[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f15.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f15.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f15);
      _jspx_th_i18n_005fmessage_005f15_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f15, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f15_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f2 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f2_reused = false;
    try {
      _jspx_th_c_005fforEach_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(212,16) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f2.setVar("tileConfig");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(212,16) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f2.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(212,16) '${chartboardConfig.tiles}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${chartboardConfig.tiles}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int[] _jspx_push_body_count_c_005fforEach_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f2 = _jspx_th_c_005fforEach_005f2.doStartTag();
        if (_jspx_eval_c_005fforEach_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                    ");
            if (_jspx_meth_c_005fif_005f6(_jspx_th_c_005fforEach_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f2))
              return true;
            out.write("\r\n");
            out.write("                ");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f2.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f2);
      _jspx_th_c_005fforEach_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f6 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f6_reused = false;
    try {
      _jspx_th_c_005fif_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(213,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f6.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not tileConfig.displayed}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f6 = _jspx_th_c_005fif_005f6.doStartTag();
      if (_jspx_eval_c_005fif_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_string_005fescape_005f0(_jspx_th_c_005fif_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f2))
            return true;
          out.write("\r\n");
          out.write("                        <div class=\"available-widget preview ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tilePreview == undefined || tilePreview == '' ? '' : 'with-preview' }", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                             ");
          if (_jspx_meth_c_005fif_005f7(_jspx_th_c_005fif_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f2))
            return true;
          out.write("\r\n");
          out.write("                             data-cell-id=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.cellId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                             data-cell-cssclass=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.cssClass}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                             data-displayed=\"false\"\r\n");
          out.write("                             data-preview=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tilePreview}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                        >\r\n");
          out.write("							<span class=\"gridstack-elem-title-label\">");
          if (_jspx_meth_string_005feval_005f1(_jspx_th_c_005fif_005f6, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f2))
            return true;
          out.write("</span>\r\n");
          out.write("                                ");
          out.write("\r\n");
          out.write("                            <div class=\"tile grid-stack-item-content\">\r\n");
          out.write("                            </div>\r\n");
          out.write("                        </div>\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f6);
      _jspx_th_c_005fif_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f0_reused = false;
    try {
      _jspx_th_string_005fescape_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(214,24) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setVar("tilePreview");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(214,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.preview}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(214,24) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f0 = _jspx_th_string_005fescape_005f0.doStartTag();
        if (_jspx_th_string_005fescape_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f0);
      _jspx_th_string_005fescape_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f0, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f7 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f7_reused = false;
    try {
      _jspx_th_c_005fif_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(216,29) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f7.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty tileConfig.cssId}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f7 = _jspx_th_c_005fif_005f7.doStartTag();
      if (_jspx_eval_c_005fif_005f7 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("id=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write('"');
          out.write(' ');
          int evalDoAfterBody = _jspx_th_c_005fif_005f7.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f7);
      _jspx_th_c_005fif_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f1 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f1_reused = false;
    try {
      _jspx_th_string_005feval_005f1.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(222,48) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${tileConfig.title}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(222,48) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005feval_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f1 = _jspx_th_string_005feval_005f1.doStartTag();
        if (_jspx_th_string_005feval_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fstring_005ffeeds_005fnobody.reuse(_jspx_th_string_005feval_005f1);
      _jspx_th_string_005feval_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f1, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f16(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f16 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f16_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f16.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f16.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(235,45) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f16.setCode("chartboard.custom.widgets");
      int[] _jspx_push_body_count_i18n_005fmessage_005f16 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f16 = _jspx_th_i18n_005fmessage_005f16.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f16.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f16[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f16.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f16.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f16);
      _jspx_th_i18n_005fmessage_005f16_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f16, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f16_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f17(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f17 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f17_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f17.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f17.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(246,60) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f17.setCode("chartboard.note");
      int[] _jspx_push_body_count_i18n_005fmessage_005f17 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f17 = _jspx_th_i18n_005fmessage_005f17.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f17.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f17[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f17.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f17.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f17);
      _jspx_th_i18n_005fmessage_005f17_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f17, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f17_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f18(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fcontent_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fcontent_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f18 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f18_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f18.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f18.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fcontent_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(249,36) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f18.setCode("chartboard.focus.tile");
      int[] _jspx_push_body_count_i18n_005fmessage_005f18 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f18 = _jspx_th_i18n_005fmessage_005f18.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f18.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f18[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f18.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f18.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f18);
      _jspx_th_i18n_005fmessage_005f18_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f18, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f18_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f8 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f8_reused = false;
    try {
      _jspx_th_c_005fif_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(256,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f8.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty chartboardConfig}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f8 = _jspx_th_c_005fif_005f8.doStartTag();
      if (_jspx_eval_c_005fif_005f8 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fchoose_005f4(_jspx_th_c_005fif_005f8, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f8.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f8);
      _jspx_th_c_005fif_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f4 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f4_reused = false;
    try {
      _jspx_th_c_005fchoose_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f8);
      int _jspx_eval_c_005fchoose_005f4 = _jspx_th_c_005fchoose_005f4.doStartTag();
      if (_jspx_eval_c_005fchoose_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fwhen_005f4(_jspx_th_c_005fchoose_005f4, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fotherwise_005f4(_jspx_th_c_005fchoose_005f4, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f4);
      _jspx_th_c_005fchoose_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f4 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f4_reused = false;
    try {
      _jspx_th_c_005fwhen_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(258,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getBooleanParam(widget, 'savePreview', false)}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_0)).booleanValue());
      int _jspx_eval_c_005fwhen_005f4 = _jspx_th_c_005fwhen_005f4.doStartTag();
      if (_jspx_eval_c_005fwhen_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_plma_005ftoJSON_005f0(_jspx_th_c_005fwhen_005f4, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_plma_005ftoJSON_005f1(_jspx_th_c_005fwhen_005f4, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f4);
      _jspx_th_c_005fwhen_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005ftoJSON_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:toJSON
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag _jspx_th_plma_005ftoJSON_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag) _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag.class);
    boolean _jspx_th_plma_005ftoJSON_005f0_reused = false;
    try {
      _jspx_th_plma_005ftoJSON_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005ftoJSON_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(259,16) name = object type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f0.setObject((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${chartboardConfig.tiles}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(259,16) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f0.setDefaultValue("[]");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(259,16) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f0.setVar("jsonTiles");
      int[] _jspx_push_body_count_plma_005ftoJSON_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005ftoJSON_005f0 = _jspx_th_plma_005ftoJSON_005f0.doStartTag();
        if (_jspx_th_plma_005ftoJSON_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005ftoJSON_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005ftoJSON_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005ftoJSON_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fdefaultValue_005fnobody.reuse(_jspx_th_plma_005ftoJSON_005f0);
      _jspx_th_plma_005ftoJSON_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005ftoJSON_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005ftoJSON_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005ftoJSON_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:toJSON
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag _jspx_th_plma_005ftoJSON_005f1 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag) _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag.class);
    boolean _jspx_th_plma_005ftoJSON_005f1_reused = false;
    try {
      _jspx_th_plma_005ftoJSON_005f1.setPageContext(_jspx_page_context);
      _jspx_th_plma_005ftoJSON_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(260,16) name = object type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f1.setObject((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${chartboardConfig}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(260,16) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f1.setDefaultValue("{}");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(260,16) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f1.setVar("jsonChartboard");
      int[] _jspx_push_body_count_plma_005ftoJSON_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005ftoJSON_005f1 = _jspx_th_plma_005ftoJSON_005f1.doStartTag();
        if (_jspx_th_plma_005ftoJSON_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005ftoJSON_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005ftoJSON_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005ftoJSON_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fdefaultValue_005fnobody.reuse(_jspx_th_plma_005ftoJSON_005f1);
      _jspx_th_plma_005ftoJSON_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005ftoJSON_005f1, _jsp_getInstanceManager(), _jspx_th_plma_005ftoJSON_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f4 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f4_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f4);
      int _jspx_eval_c_005fotherwise_005f4 = _jspx_th_c_005fotherwise_005f4.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_plma_005ftoJSON_005f2(_jspx_th_c_005fotherwise_005f4, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("                ");
          if (_jspx_meth_plma_005ftoJSON_005f3(_jspx_th_c_005fotherwise_005f4, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f4);
      _jspx_th_c_005fotherwise_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005ftoJSON_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:toJSON
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag _jspx_th_plma_005ftoJSON_005f2 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag) _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fignoreFields_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag.class);
    boolean _jspx_th_plma_005ftoJSON_005f2_reused = false;
    try {
      _jspx_th_plma_005ftoJSON_005f2.setPageContext(_jspx_page_context);
      _jspx_th_plma_005ftoJSON_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(263,16) name = object type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f2.setObject((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${chartboardConfig.tiles}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(263,16) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f2.setDefaultValue("[]");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(263,16) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f2.setVar("jsonTiles");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(263,16) name = ignoreFields type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f2.setIgnoreFields((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:toList('preview')}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_1));
      int[] _jspx_push_body_count_plma_005ftoJSON_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005ftoJSON_005f2 = _jspx_th_plma_005ftoJSON_005f2.doStartTag();
        if (_jspx_th_plma_005ftoJSON_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005ftoJSON_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005ftoJSON_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005ftoJSON_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fignoreFields_005fdefaultValue_005fnobody.reuse(_jspx_th_plma_005ftoJSON_005f2);
      _jspx_th_plma_005ftoJSON_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005ftoJSON_005f2, _jsp_getInstanceManager(), _jspx_th_plma_005ftoJSON_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005ftoJSON_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:toJSON
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag _jspx_th_plma_005ftoJSON_005f3 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag) _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fignoreFields_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.json.GetJSONTag.class);
    boolean _jspx_th_plma_005ftoJSON_005f3_reused = false;
    try {
      _jspx_th_plma_005ftoJSON_005f3.setPageContext(_jspx_page_context);
      _jspx_th_plma_005ftoJSON_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(264,16) name = object type = java.lang.Object reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f3.setObject((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${chartboardConfig}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(264,16) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f3.setDefaultValue("{}");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(264,16) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f3.setVar("jsonChartboard");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(264,16) name = ignoreFields type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005ftoJSON_005f3.setIgnoreFields((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:toList('preview')}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_1));
      int[] _jspx_push_body_count_plma_005ftoJSON_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005ftoJSON_005f3 = _jspx_th_plma_005ftoJSON_005f3.doStartTag();
        if (_jspx_th_plma_005ftoJSON_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005ftoJSON_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005ftoJSON_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005ftoJSON_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005ftoJSON_0026_005fvar_005fobject_005fignoreFields_005fdefaultValue_005fnobody.reuse(_jspx_th_plma_005ftoJSON_005f3);
      _jspx_th_plma_005ftoJSON_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005ftoJSON_005f3, _jsp_getInstanceManager(), _jspx_th_plma_005ftoJSON_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f5 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f5_reused = false;
    try {
      _jspx_th_c_005fchoose_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      int _jspx_eval_c_005fchoose_005f5 = _jspx_th_c_005fchoose_005f5.doStartTag();
      if (_jspx_eval_c_005fchoose_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fwhen_005f5(_jspx_th_c_005fchoose_005f5, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fotherwise_005f5(_jspx_th_c_005fchoose_005f5, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f5);
      _jspx_th_c_005fchoose_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f5 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f5_reused = false;
    try {
      _jspx_th_c_005fwhen_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(270,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty chartboardConfig && not empty chartboardConfig.label}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f5 = _jspx_th_c_005fwhen_005f5.doStartTag();
      if (_jspx_eval_c_005fwhen_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fset_005f14(_jspx_th_c_005fwhen_005f5, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f5);
      _jspx_th_c_005fwhen_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f14 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f14_reused = false;
    try {
      _jspx_th_c_005fset_005f14.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f14.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(271,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f14.setVar("chartboardLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(271,12) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f14.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(271,12) '${chartboardConfig.label}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${chartboardConfig.label}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f14 = _jspx_th_c_005fset_005f14.doStartTag();
      if (_jspx_th_c_005fset_005f14.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f14);
      _jspx_th_c_005fset_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f14, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f5 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f5_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f5);
      int _jspx_eval_c_005fotherwise_005f5 = _jspx_th_c_005fotherwise_005f5.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_search_005fgetPageName_005f1(_jspx_th_c_005fotherwise_005f5, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f5);
      _jspx_th_c_005fotherwise_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetPageName_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getPageName
    com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag _jspx_th_search_005fgetPageName_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag) _005fjspx_005ftagPool_005fsearch_005fgetPageName_0026_005fvar_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag.class);
    boolean _jspx_th_search_005fgetPageName_005f1_reused = false;
    try {
      _jspx_th_search_005fgetPageName_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetPageName_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(274,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetPageName_005f1.setVar("chartboardLabel");
      int[] _jspx_push_body_count_search_005fgetPageName_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetPageName_005f1 = _jspx_th_search_005fgetPageName_005f1.doStartTag();
        if (_jspx_th_search_005fgetPageName_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetPageName_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetPageName_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetPageName_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetPageName_0026_005fvar_005fnobody.reuse(_jspx_th_search_005fgetPageName_005f1);
      _jspx_th_search_005fgetPageName_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetPageName_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fgetPageName_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f0_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(278,4) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f0.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f0 = _jspx_th_render_005frenderScript_005f0.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f0);
          }
          do {
            out.write("\r\n");
            out.write("        var options = {};\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fif_005f9(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fif_005f10(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("        options.label = '");
            if (_jspx_meth_string_005fescape_005f1(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("        options.icon = ");
            if (_jspx_meth_c_005fchoose_005f6(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write(";\r\n");
            out.write("        options.description = ");
            if (_jspx_meth_c_005fchoose_005f7(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write(";\r\n");
            out.write("        options.layoutLastEdit = ");
            if (_jspx_meth_c_005fchoose_005f8(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write(";\r\n");
            out.write("        options.templatePage = '");
            if (_jspx_meth_search_005fgetPageName_005f2(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("        options.pageId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty pageId ? pageId : undefined}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("        options.chartboardConfig = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${jsonChartboard}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(";\r\n");
            out.write("        options.baseY = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${baseY}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(";\r\n");
            out.write("        options.marginY = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${marginY}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(";\r\n");
            out.write("        options.originalConfig = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${jsonTiles}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(";\r\n");
            out.write("        options.url = '");
            if (_jspx_meth_c_005furl_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("        options.tinymceUrl = '");
            if (_jspx_meth_c_005furl_005f1(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("        options.suggestUrl = '");
            if (_jspx_meth_c_005furl_005f2(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("        options.cssId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("        options.wuid = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget.wuid}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("        options.user = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${user}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("        options.allowEditPages = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${allowEditPages}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("        options.hasUser = '");
            if (_jspx_meth_security_005fisUserConnected_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("        options.dateParamName = '");
            if (_jspx_meth_plma_005fgetConstantValueFromNameTag_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("        options.lockMashupPages = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${lockMashupPages}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("        options.savePreview = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getBooleanParam(widget, \"savePreview\", false)}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_0));
            out.write(";\r\n");
            out.write("        options.pageOwner = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:isPageOwner(pageContext.request, pageId )}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_2));
            out.write(";\r\n");
            out.write("        options.pageEditable = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:isEditable(pageContext.request, pageId )}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_3));
            out.write(";\r\n");
            out.write("\r\n");
            out.write("        new Chartboard('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("',options);\r\n");
            out.write("    ");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f0);
      _jspx_th_render_005frenderScript_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f9 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f9_reused = false;
    try {
      _jspx_th_c_005fif_005f9.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(280,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f9.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty editButtonLocationSelector}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f9 = _jspx_th_c_005fif_005f9.doStartTag();
      if (_jspx_eval_c_005fif_005f9 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            options.editButtonLocationSelector = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${editButtonLocationSelector}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f9.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f9);
      _jspx_th_c_005fif_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f10 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f10_reused = false;
    try {
      _jspx_th_c_005fif_005f10.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f10.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(283,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f10.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty editToolbarLocationSelector}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f10 = _jspx_th_c_005fif_005f10.doStartTag();
      if (_jspx_eval_c_005fif_005f10 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            options.editToolbarLocationSelector = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${editToolbarLocationSelector}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f10.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f10);
      _jspx_th_c_005fif_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f10, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f1 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f1_reused = false;
    try {
      _jspx_th_string_005fescape_005f1.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(286,25) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${chartboardLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(286,25) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setEscapeType("JAVASCRIPT");
      int[] _jspx_push_body_count_string_005fescape_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f1 = _jspx_th_string_005fescape_005f1.doStartTag();
        if (_jspx_th_string_005fescape_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f1);
      _jspx_th_string_005fescape_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f1, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f6 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f6_reused = false;
    try {
      _jspx_th_c_005fchoose_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int _jspx_eval_c_005fchoose_005f6 = _jspx_th_c_005fchoose_005f6.doStartTag();
      if (_jspx_eval_c_005fchoose_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          if (_jspx_meth_c_005fwhen_005f6(_jspx_th_c_005fchoose_005f6, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
            return true;
          if (_jspx_meth_c_005fotherwise_005f6(_jspx_th_c_005fchoose_005f6, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
            return true;
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f6);
      _jspx_th_c_005fchoose_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f6 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f6_reused = false;
    try {
      _jspx_th_c_005fwhen_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(287,33) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f6.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty chartboardConfig && not empty chartboardConfig.icon}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f6 = _jspx_th_c_005fwhen_005f6.doStartTag();
      if (_jspx_eval_c_005fwhen_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write('\'');
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${chartboardConfig.icon}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write('\'');
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f6);
      _jspx_th_c_005fwhen_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f6 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f6_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f6);
      int _jspx_eval_c_005fotherwise_005f6 = _jspx_th_c_005fotherwise_005f6.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("undefined");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f6);
      _jspx_th_c_005fotherwise_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f7 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f7_reused = false;
    try {
      _jspx_th_c_005fchoose_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int _jspx_eval_c_005fchoose_005f7 = _jspx_th_c_005fchoose_005f7.doStartTag();
      if (_jspx_eval_c_005fchoose_005f7 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          if (_jspx_meth_c_005fwhen_005f7(_jspx_th_c_005fchoose_005f7, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
            return true;
          if (_jspx_meth_c_005fotherwise_005f7(_jspx_th_c_005fchoose_005f7, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
            return true;
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f7.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f7);
      _jspx_th_c_005fchoose_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f7 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f7_reused = false;
    try {
      _jspx_th_c_005fwhen_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f7);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(289,40) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f7.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty chartboardConfig && not empty chartboardConfig.description}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f7 = _jspx_th_c_005fwhen_005f7.doStartTag();
      if (_jspx_eval_c_005fwhen_005f7 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write('\'');
          if (_jspx_meth_string_005fescape_005f2(_jspx_th_c_005fwhen_005f7, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
            return true;
          out.write('\'');
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f7.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f7);
      _jspx_th_c_005fwhen_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f2 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f2_reused = false;
    try {
      _jspx_th_string_005fescape_005f2.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f7);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(290,92) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f2.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${chartboardConfig.description}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(290,92) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f2.setEscapeType("JAVASCRIPT");
      int[] _jspx_push_body_count_string_005fescape_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f2 = _jspx_th_string_005fescape_005f2.doStartTag();
        if (_jspx_th_string_005fescape_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f2);
      _jspx_th_string_005fescape_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f2, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f7 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f7_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f7);
      int _jspx_eval_c_005fotherwise_005f7 = _jspx_th_c_005fotherwise_005f7.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f7 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write('\'');
          out.write('\'');
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f7.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f7);
      _jspx_th_c_005fotherwise_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f8 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f8_reused = false;
    try {
      _jspx_th_c_005fchoose_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int _jspx_eval_c_005fchoose_005f8 = _jspx_th_c_005fchoose_005f8.doStartTag();
      if (_jspx_eval_c_005fchoose_005f8 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          if (_jspx_meth_c_005fwhen_005f8(_jspx_th_c_005fchoose_005f8, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
            return true;
          if (_jspx_meth_c_005fotherwise_005f8(_jspx_th_c_005fchoose_005f8, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
            return true;
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f8.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f8);
      _jspx_th_c_005fchoose_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f8 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f8_reused = false;
    try {
      _jspx_th_c_005fwhen_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(293,43) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f8.setTest(false);
      int _jspx_eval_c_005fwhen_005f8 = _jspx_th_c_005fwhen_005f8.doStartTag();
      if (_jspx_eval_c_005fwhen_005f8 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${chartboardConfig.lastEdit}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f8.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f8);
      _jspx_th_c_005fwhen_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f8 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f8_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f8);
      int _jspx_eval_c_005fotherwise_005f8 = _jspx_th_c_005fotherwise_005f8.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f8 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("undefined");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f8.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f8);
      _jspx_th_c_005fotherwise_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetPageName_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getPageName
    com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag _jspx_th_search_005fgetPageName_005f2 = (com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag) _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag.class);
    boolean _jspx_th_search_005fgetPageName_005f2_reused = false;
    try {
      _jspx_th_search_005fgetPageName_005f2.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetPageName_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int[] _jspx_push_body_count_search_005fgetPageName_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetPageName_005f2 = _jspx_th_search_005fgetPageName_005f2.doStartTag();
        if (_jspx_th_search_005fgetPageName_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetPageName_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetPageName_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetPageName_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.reuse(_jspx_th_search_005fgetPageName_005f2);
      _jspx_th_search_005fgetPageName_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetPageName_005f2, _jsp_getInstanceManager(), _jspx_th_search_005fgetPageName_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f0 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f0_reused = false;
    try {
      _jspx_th_c_005furl_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(301,23) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f0.setValue("/");
      int _jspx_eval_c_005furl_005f0 = _jspx_th_c_005furl_005f0.doStartTag();
      if (_jspx_th_c_005furl_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f0);
      _jspx_th_c_005furl_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f0, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f1 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f1_reused = false;
    try {
      _jspx_th_c_005furl_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(302,30) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f1.setValue("/resources/widgets/plmaResources/lib/tinymce/tinymce.min.js");
      int _jspx_eval_c_005furl_005f1 = _jspx_th_c_005furl_005f1.doStartTag();
      if (_jspx_th_c_005furl_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f1);
      _jspx_th_c_005furl_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f1, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f2 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f2_reused = false;
    try {
      _jspx_th_c_005furl_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(303,30) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f2.setValue("/utils/suggest");
      int _jspx_eval_c_005furl_005f2 = _jspx_th_c_005furl_005f2.doStartTag();
      if (_jspx_th_c_005furl_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f2);
      _jspx_th_c_005furl_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f2, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_security_005fisUserConnected_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  security:isUserConnected
    com.exalead.cv360.searchui.view.jspapi.security.IsUserConnectedTag _jspx_th_security_005fisUserConnected_005f0 = (com.exalead.cv360.searchui.view.jspapi.security.IsUserConnectedTag) _005fjspx_005ftagPool_005fsecurity_005fisUserConnected_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.security.IsUserConnectedTag.class);
    boolean _jspx_th_security_005fisUserConnected_005f0_reused = false;
    try {
      _jspx_th_security_005fisUserConnected_005f0.setPageContext(_jspx_page_context);
      _jspx_th_security_005fisUserConnected_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int[] _jspx_push_body_count_security_005fisUserConnected_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_security_005fisUserConnected_005f0 = _jspx_th_security_005fisUserConnected_005f0.doStartTag();
        if (_jspx_th_security_005fisUserConnected_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_security_005fisUserConnected_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_security_005fisUserConnected_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_security_005fisUserConnected_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsecurity_005fisUserConnected_005fnobody.reuse(_jspx_th_security_005fisUserConnected_005f0);
      _jspx_th_security_005fisUserConnected_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_security_005fisUserConnected_005f0, _jsp_getInstanceManager(), _jspx_th_security_005fisUserConnected_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetConstantValueFromNameTag_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getConstantValueFromNameTag
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag _jspx_th_plma_005fgetConstantValueFromNameTag_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag) _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag.class);
    boolean _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(309,33) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setVar("simpleDateRefine");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/chartboard/widget.jsp(309,33) name = constantName type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setConstantName("SIMPLE_DATE_PARAM");
      int[] _jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetConstantValueFromNameTag_005f0 = _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doStartTag();
        if (_jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fvar_005fconstantName_005fnobody.reuse(_jspx_th_plma_005fgetConstantValueFromNameTag_005f0);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetConstantValueFromNameTag_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused);
    }
    return false;
  }

  private class Helper
      extends org.apache.jasper.runtime.JspFragmentHelper
  {
    private jakarta.servlet.jsp.tagext.JspTag _jspx_parent;
    private int[] _jspx_push_body_count;

    public Helper( int discriminator, jakarta.servlet.jsp.JspContext jspContext, jakarta.servlet.jsp.tagext.JspTag _jspx_parent, int[] _jspx_push_body_count ) {
      super( discriminator, jspContext, _jspx_parent );
      this._jspx_parent = _jspx_parent;
      this._jspx_push_body_count = _jspx_push_body_count;
    }
    public boolean invoke0( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f0(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f1(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f2(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                            ");
      return false;
    }
    public void invoke( java.io.Writer writer )
      throws jakarta.servlet.jsp.JspException
    {
      jakarta.servlet.jsp.JspWriter out = null;
      if( writer != null ) {
        out = this.jspContext.pushBody(writer);
      } else {
        out = this.jspContext.getOut();
      }
      try {
        Object _jspx_saved_JspContext = this.jspContext.getELContext().getContext(jakarta.servlet.jsp.JspContext.class);
        this.jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,this.jspContext);
        switch( this.discriminator ) {
          case 0:
            invoke0( out );
            break;
        }
        jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,_jspx_saved_JspContext);
      }
      catch( java.lang.Throwable e ) {
        if (e instanceof jakarta.servlet.jsp.SkipPageException)
            throw (jakarta.servlet.jsp.SkipPageException) e;
        throw new jakarta.servlet.jsp.JspException( e );
      }
      finally {
        if( writer != null ) {
          this.jspContext.popBody();
        }
      }
    }
  }
}
