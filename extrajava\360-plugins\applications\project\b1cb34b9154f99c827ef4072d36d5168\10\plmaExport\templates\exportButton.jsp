<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>

<render:import parameters="uid,feed,numHits,columnsConfig,exportMode,exportPerPage,fileName,exportEncoding,exportSeparator,recordDelimiter,addBOM,iconCss,label,showLabel,exportRawValues" ignore="true"/>

<search:getPageFeed var="pageFeed"/>
<search:getFeedInfo var="sapiquery" name="searchAPIUrl" feed="${feed}"/>
<string:escape var="sapiquery" value="${sapiquery}" escapeType="HTML"/>
<c:set var="parameterSeparator" value="~~~~~" />
<url:url var="exportUrl" value="../c/exportdata/${exportMode == 'Search API' ? 'stream' : 'batch'}" keepQueryString="true">
    <c:if test="${exportPerPage != -1}">
        <url:parameter name="${feed.id}.per_page" value="${exportPerPage}"/>
    </c:if>
</url:url>
<form id="${uid}-export-data" class="export-data-form" action="${exportUrl}" autocomplete="off" method="post" >
    <input type="hidden" name="filename" value="${fileName}">
    <input type="hidden" name="page" value="${pageFeed.id}">
    <input type="hidden" name="feedname" value="${feed.id}">
    <input type="hidden" name="hf" value="${numHits}">
    <input type="hidden" name="exportFormatParams" value="${exportSeparator}${parameterSeparator}${exportEncoding}${parameterSeparator}${recordDelimiter}">
    <input type="hidden" name="columnsConfig" value="${columnsConfig}">
    <%-- Removing the output format parameter --%>
    <input type="hidden" name="sapiquery" value="${fn:replace(sapiquery, 'of=flea', '')}">
    <input type="hidden" name="addBOM" value="${addBOM}" />
    <plma:signature var="sapiSign" value="${fn:replace(sapiquery, 'of=flea', '')}"/>
    <input type="hidden" name="sapiSign" value="${sapiSign}" />
    <input type="hidden" name="exportRawValues" value="${exportRawValues}" />
    <render:csrf />
</form>

<div class="exportButton">
    <c:if test="${not empty iconCss}">
        <i class="${iconCss}" title="${label}"></i>
    </c:if>
    <c:if test="${showLabel}">
        <span class="button-label">${label}</span>
    </c:if>
</div>


<render:renderScript position="READY">
    new ExportButtonHandler('${uid}');
</render:renderScript>
