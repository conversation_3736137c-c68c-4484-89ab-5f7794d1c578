/**
* Storage for Storing/persiting the Collection Entries.
* options = {
		store: 'db|bowser',
		storeKey: 'unique-id',
		limit: integer,
		limitStrategy: 'freeze|remove-old'		
* }
*/
var HitsStorage = function (options) {
	this.options = options;
    this.entryList = undefined;
};

HitsStorage.prototype.init = function () {
	var dfd = $.Deferred();
    switch (this.options.store) {
        case 'browser':
            this.storage = localStorage;
            var res = this.storage.getItem( this.options.storeKey );
            this.entryList = res != null ? JSON.parse(res) : [];
            dfd.resolve(true);
            break;
        case 'db':
            this.storage = new StorageClient(this.options.storeType);
            this.storage.get(this.options.storeKey, function (res) {
                    this.entryList = res.length > 0 ? JSON.parse(res[0].value) : [];
                    dfd.resolve(true);
                }.bind(this), function () {
                    dfd.reject(false);
                });
            break;
        default:
			dfd.reject(false);
            throw new Error("Unknown Storage Type " + this.options.store);			
    }
    return dfd.promise();
};

HitsStorage.prototype.clear = function () {
	var dfd = $.Deferred();
	switch (this.options.store) {
		case 'browser':
			this.storage.removeItem(this.options.storeKey);
			this.entryList = [];
			dfd.resolve(true);
			break;
		case 'db':
			this.storage.set(this.options.storeKey, [], function (res) {
				this.entryList = [];
				dfd.resolve(true);
			}.bind(this));
			break;
		default:
			dfd.reject(false);
			throw new Error("Unknown Storage Type " + this.options.store);			
	}
	return dfd.promise();
};

HitsStorage.prototype.add = function (entryData) {
	return this._updateInternal(entryData, false);
}
HitsStorage.prototype.update = function (entryData) {
	return this._updateInternal(entryData, true);
}

HitsStorage.prototype._updateInternal = function (entryData, toggleEntry) {
	var dfd = $.Deferred();
	var updateResult = this._updateEntryList(entryData, toggleEntry);

	switch (this.options.store) {
		case 'browser':
			this.storage.setItem(this.options.storeKey, JSON.stringify(this.entryList));
			dfd.resolve(updateResult.status, updateResult.popedEntries);
			break;
		case 'db':
			this.storage.set(this.options.storeKey, this.entryList, function () {
				dfd.resolve(updateResult.status, updateResult.popedEntries);
			}.bind(this));
			break;
		default:
			throw new Error("Unknown Storage Type " + this.options.store);
			dfd.reject(undefined);
	}
	return dfd.promise();
};

/**
* updateList : checks it the entryToUpdate is present in the current Basket Entries.
* If present, remove else Add.
* Update callback will be used to notify removal of the old Entry(in remove-old strategy).
*/

HitsStorage.ADDED = 1;
HitsStorage.REMOVED = 0;
HitsStorage.IGNORED = -1;
HitsStorage.ADDED_REMOVED_OLD = 10;


HitsStorage.prototype.getEntries = function () {
	return this.entryList;
};

HitsStorage.prototype.containsEntry = function (entryToFind) {
	var found = false;
	this.entryList.every(function(entry, entryIndex){
		if(entry.id === entryToFind.id){
			found = true;
			return false; // This breaks the loop. Return false means stop the loop.
		}
		return true;
	});
	return found ? HitsStorage.ADDED : HitsStorage.REMOVED;
};

HitsStorage.prototype._updateEntryList = function (entryToUpdate, toggleEntry) {
	// Remove from entryList if already present.
	var removedEntries = _.remove(this.entryList, function (entry) {
		// If matched, removed from entryList and added to removedList.
		return entry.id === entryToUpdate.id;
	});

	if(toggleEntry && removedEntries.length > 0){
		// entryToUpdate was present in the list hence removed.
		return { status: HitsStorage.REMOVED }
	}
	
	if( this.options.limitStrategy != undefined &&
		this.options.limitStrategy == "freeze" &&
		this.entryList.length >= this.options.limit){
		// Ignore the entry as limit is reached and strategy is to freeze the list.
		return { status: HitsStorage.IGNORED };
	}

	// limit Strategy is remove-old. Hence always add the new entry at the beginning
	this.entryList.unshift(entryToUpdate);
	var result = { status: HitsStorage.ADDED, popedEntries: [] };
	
	// limit == 0 means infinite...???
	if(this.options.limit > 0){
		while (this.entryList.length > this.options.limit) {
			// As the entry is removed, do send back to update its button.
			result.popedEntries.push(this.entryList.pop()); // remove oldest entry
			result.status = HitsStorage.ADDED_REMOVED_OLD;
		}
	}
	return result;
};