@import "../../plmaResources/css/styles/variables.less";

.mashup {
	.searchForm {
		margin: 0;
		padding: 0;
		border: 0;
	
		// Form container
		.searchWidget{
			//padding:8px;
			.searchContainer{
				.searchFormContent{ 
					white-space: nowrap;
					font-family: "3ds";
					font-size: @m-font;
					// position: relative;
					display: inline-block;
					// position: unset;
					.searchInput{
						border: 1px solid @cblock-border;
						border-radius: 3px;
				
						padding: 0 0 0 5px;
						
						line-height: 26px;
						height: 26px;
				
						text-align: left;
						
						width: 100%;
	
					}
					// Button
					.searchButton{
						cursor: pointer;
						font-size: var(--icons-size, @l-font);
						position: relative;
						display: inline-block;
						right:33px;
						top: 1px;
						color: #b4b6ba;
						font-size:20px;
						width: 24px;
						//top: -8px;

						// Input
						input {
							margin: 0;
							padding: 0 10px;
							border: 0;
							outline: 0;
				
							font-size:14px;
							font-weight:bold;
							overflow: visible;
							white-space: nowrap;
				
							cursor:pointer;
							max-width: 120px;
							background:transparent;
				
							z-index: 5;
						}
						&:hover{
							color:#3d3d3d;
						}
					}
					::placeholder{
					 font-family:arial;
					 font-style:italic;
					 color:#b4b6ba;
					 font-size:12px;
	
					}
					&.hidden{
						display: none;
					}
				}
			}
		}
	
		// Multiple Actions
	
		.actions {
			text-align: left;
			span {
				cursor: pointer;
				color: lighten(@clink, 20%);
				font-weight: normal;
				margin-left: 5px;
				&:first-child {
					margin-left: 0;
				}
				&.selected {
					color: @clink;
					font-weight: bold;
				}
				&:hover {
					text-decoration: underline;
				}
			}
		}
	}
	.header-container{
		.searchWidget {
			.searchContainer{
				.searchFormContent{
						.searchInput{
						   padding: 0 25px 0 5px;
						   width: 90%;
						}
				}
			}
		}
	}	
	// IE Fixes
	&.legacy-ie {
		.searchButton {
			display: inline;
			zoom: 1;
			vertical-align: baseline;
			input {
				height: 26px;
			}
		}
	}
}
/*
 * Warning !
 * Cannot be in &.mashup because of Sharepoint.
 * The .mashup-suggest-container div is append to the body.
 */
.mashup-search-suggest-container {
    position: absolute;
    margin-top: 8px;
    margin-left: -2px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.2);
    background-color: @cblock-bg;
    z-index: 30000;
    font-size: 13px;

    ul {
        list-style-type: none;
        border-collapse: separate;
        border-spacing: 3px;
        overflow: hidden;
        li {
            padding: 2px 5px;
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            &.header {
                font-family:'3ds';
                display: table-cell;
                text-align: right;
                white-space: normal;
                border-right: 2px solid #ddd; /* TODO: use @cblock-border-alt from theme */
                word-wrap: break-word;
                max-width: 100px;
            }

            .matched {
                font-family:'3ds';
                color: @clink;
            }

            &.highlighted {
                background-color: @clink;
                color: @ctext-inverted;
                cursor: pointer;
                .matched {
                    color: @ctext-inverted;
                }
            }

            &.first-line {
                font-family:'3ds';
                border-bottom: 1px solid #ddd; /* TODO: use @cblock-border-alt from theme */
                line-height: 24px;
                padding: 3px 6px;
                max-width: 100%;
            }
        }

        ul {
            display: table-row;
            &.highlighted .header {
                border-right: 2px solid @clink;
                color: @clink;
            }
        }
    }
}