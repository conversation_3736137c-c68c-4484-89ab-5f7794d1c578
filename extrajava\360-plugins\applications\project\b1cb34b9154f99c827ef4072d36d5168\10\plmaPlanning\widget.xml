<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA Planning" group="PLM Analytics/Visualizations" premium="true"
		xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description><![CDATA[
		This widget displays a custom Vis Timeline timeseries data as planning/gantt.<br/>
		How widget works?
		<ul>
			<li><b>Step1:</b> Load the basic widget with initial data if the feed.nhits > 0, else load initial data by sending ajax widget load, and 'additionalParams' configuation</li>
			<li><b>Step2:</b> For each Hit in feed, compute groups and items using Group and Item Config provided the condition config evaluates=true</li>
			<li><b>Step3:</b> For each hit, if it has subfeed, then iterate entries of subfeed and repeat Step2</li>
			<li><b>Step4:</b> When clicked on element, if the subwidget is provided, then load the sub-widget with hit=item.hitId</li>
		</ul>
	]]></Description>

	<Preview>
		<![CDATA[
		<img src="../plmaResources/lib/visjs/images/timeline.png" alt="Timeline" />
	]]>
	</Preview>

	<Platforms>
		<Platform type="web" supported="true"/>
		<Platform type="mobile" supported="true"/>
	</Platforms>

	<Includes>
		<Include path="css/style.less" type="css" />
		<Include path="../plmaResources/lib/visjs/vis-timeline-graph2d.min.css" type="css" />
		<Include type="css" path="../plmaResources/lib/notify/notify-plma.less" />
		
		<Include type="js" path="../plmaResources/lib/visjs/vis-timeline-graph2d.min.js"/>
		<Include type="js" path="js/plmaPlanning.js"/>
		<Include type="js" path="js/plmaPlanningTimeRange.js"/>
		<Include type="js" path="js/plmaPlanningExporter.js"/>
		<Include type="js" path="js/timelineConfigurator.js"/>
		<Include type="js" path="js/timelineDataSetBuilder.js"/>

		<Include type="js" path="../plmaResources/js/widgetButtonManager.js"/>
		<Include type="js" path="../plmaResources/js/fullScreenWidget.js"/>
		<Include type="js" path="../plmaResources/lib/notify/notify.js"/>
		<Include type="js" path="../plmaResources/lib/notify/notify-plma.js"/>
		
		<Include type="js" path="../plmaResources/lib/jspdf/html2canvas.min.js"/>
		<Include type="js" path="../plmaResources/lib/jspdf/canvg.min.js"/>
		<Include type="js" path="../plmaResources/js/exportTo3DSpace/EXPUtils.js"/>
		<Include type="js" path="../plmaResources/js/exportTo3DSpace/documentManager3DSpace.js"/>
		<Include type="js" path="../plmaResources/js/exportTo3DSpace/exportTo3DSpace.js"/>
		
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
	</Includes>

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO_OR_ONE"></SupportWidgetsId>
	<SupportFeedsId arity="ONE" consumeFeed="false"/>
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>widget.plma.planning.err.nodata</JsKey>
			<JsKey>widget.plma.planning.err.perpage</JsKey>
			<JsKey>widget.plma.planning.zoom.help</JsKey>
			<JsKey>widget.plma.planning.menu.exportimage</JsKey>
			<JsKey>widget.plma.planning.export.err.convert</JsKey>
		</JsKeys>
	</SupportI18N>
	
	<OptionsGroup name="General">
		<Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
		</Option>
		<Option id="additionalParams" name="Additionnal Parameters" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Additionnal parameters to apply in the query for the objects.</Description>
		</Option>
		<Option id="paginationFeedId" name="Pagination FeedId" arity="ZERO_OR_ONE">
			<Description>In case of feed and subfeeds, specify the feed to apply pagination on`.</Description>
			<Functions>
				<ContextMenu>Feeds()</ContextMenu>
			</Functions>
		</Option>
		<Option id="hitsPerPage" name="Hits Per Page" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Specify hits per page. Default to 10.</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="forceFirstLoad" name="Load Initial" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Default true. Incase of Analytics feeds you need to load hits(with per_page param) on first load</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="filterUrlParams" name="Filter Url Params">
			<Description>This function can provide the URL with filtered parameters from the current page load url.
				e.g. if you want to trasfer specific refine values only, while loading data use this function.
			</Description>
			<Values>
				<Value><![CDATA[function (pageLoadUrl) {
  /*Modify url as per need*/
  return pageLoadUrl;
}]]></Value>
			</Values>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
	</OptionsGroup>
	
	<OptionsGroup name="Groups">
		<Description>Define DataSet Groups. Using groups, items can be grouped together. Every Group Definition will be ran for Each Entry in the result Feeds Or Sub Feeds</Description>
		<OptionComposite id="groupsDef" name="Groups Definition" arity="ZERO_OR_MANY" glue="##">
			<Option id="condition" name="Condition" arity="ZERO_OR_ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>MEL expression to decide Group to be added or not for current entry.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="id" name="Id" arity="ZERO_OR_ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>An id for the group. The group will display all items having a property group which matches the id of the group.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="content" name="Content" arity="ZERO_OR_ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>The contents of the group. This can be plain text, html code or an html element.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="other" name="Other Properties" arity="ZERO_OR_ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>Provide Json with any extra properties you want to add. Ref visjs timeline documentation. You can also add your own properties to be used in further actions, if any.</Description>
				<Values>
					<Value><![CDATA[{
  className:'', 
  style:'',
}]]></Value>
				</Values>
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>
			<Option id="nestedGroups" name="Nested Groups" arity="ZERO_OR_ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>Provide comma separated values. Array of group ids nested in the group. Nested groups will appear under this nesting group.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<ContextMenu>COMMON_appendOnChange(', ')</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="title" name="Tooltip" arity="ZERO_OR_ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>HTML + MEL expression to compute the Tooltip. If empty, no tooltip shown.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>
	
	<OptionsGroup name="Items">
		<Description>Define DataSet Items. Every Item Definition will be ran for Each Entry in the result Feeds Or Sub Feeds</Description>
		<OptionComposite id="itemsDef" name="Items Definition" arity="ZERO_OR_MANY" glue="##">
			<Option id="condition" name="Condition" arity="ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>MEL expression to decide Item to be added or not for current entry.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="id" name="Id" arity="ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>An id for the item. An id is needed when dynamically adding, updating, and removing items in a DataSet.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="content" name="Content" arity="ZERO_OR_ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>The contents of the item. This can be plain text or html code.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="type" name="Type" arity="ONE">
				<Description><![CDATA[
					The type of the item. Can be 'box' (default), 'point', 'range', or 'background'. 
					Types 'box' and 'point' need a start date, the types 'range' and 'background' needs both a start and end date.
					<hr/>
					<i>Type <b>'estact' is spacial type</b>, it pushes 2 range items with uniqe subgroup, one for est and other for actual. 
					This type is provided for simplicity of config. It needs start,end,act start, act end all four dates. Actual start/end can have no value, but config is must.<br/>
					You can supply typeColor value in 'Other Properties' which will be applied appropriately.
					</i>
				]]></Description>
				<Values>
					<Value>box</Value>
					<Value>point</Value>
					<Value>range</Value>
					<Value>background</Value>
					<Value>estact</Value>
				</Values>
				<Functions>		
					<Display>ToggleCompositeDisplay({ valueToMatch: ['box', 'point','range','background','estact'],
							showOptions: [],
							hideOptions: ['actStart','actEnd'],
							ifEquals:true })
					</Display>
					<Display>ToggleCompositeDisplay({ valueToMatch: [ 'box', 'point'],
							showOptions: [],
							hideOptions: ['end'],
							ifEquals:true })
					</Display>
					<Display>ToggleCompositeDisplay({ valueToMatch: 'estact',
							showOptions: ['actStart','actEnd'],
							hideOptions: [],
							ifEquals:true })
					</Display>
				</Functions>
			</Option>
			
			<Option id="start" name="Start date" arity="ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>The start date of the item. Provide MEL expression to get the date. e.g. ${entry.metas['estimated_start_date']}</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="end" name="End date" arity="ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>The end date of the item. Provide MEL expression to get the date. e.g. ${entry.metas['estimated_finish_date']}</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="other" name="Other Properties" arity="ZERO_OR_ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>Provide Json with any extra properties you want to add. Ref visjs timeline DataSet-Item documentation. You can also add your own properties to be used in further actions, if any.</Description>
				<Values>
					<Value><![CDATA[{
  /*To display items of one type together, use 'subgroup'. Check visjs timeline subgroups example
    subgroup: 'issue-<groupId>', will create subgroup of all issues together.
  */
  className:'', 
  style:'',
}]]></Value>
				</Values>
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>
			<Option id="group" name="Group Id" arity="ZERO_OR_ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>An id of the group. When the group column is provided, all items with the same group are placed on one line. A vertical axis is displayed showing the groups. Grouping items can be useful for example when showing availability of multiple people, rooms, or other resources next to each other.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="title" name="Tooltip" arity="ZERO_OR_ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>HTML + MEL expression to compute the Tooltip. If empty, no tooltip shown.</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="actStart" name="Actual Start date" arity="ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>The actual start date of the item. Provide MEL expression to get the date. e.g. ${entry.metas['actual_start_date']}</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Option id="actEnd" name="Actual End date" arity="ONE" isEvaluated="true"
				isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
				<Description>The actual end date of the item. Provide MEL expression to get the date. e.g. ${entry.metas['actual_finish_date']}</Description>
				<Functions>
					<ContextMenu>Eval()</ContextMenu>
					<Display>SetType('code', 'mel')</Display>
				</Functions>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>
	
	<OptionsGroup name="Config Options">
		<Option id="opts" name="Configuration options" arity="ONE" isEvaluated="true">
			<Description>Lets you customize visjs global Configuration Options. Options provided here 
				will overwrite the default ones.
			</Description>
			<Values>
				<Value><![CDATA[{
  plma: { /*plma specific options not the visjs options*/
    timespanDateFormat: 'YYYY, MMM-DD',
  },
  editable: false,
  selectable: false,
  zoomKey: 'ctrlKey',
  stack: false, /*For estact, make sure stack=false & stackSubgroups=true*/
  stackSubgroups: false, 
  /*groupOrder: function (a, b) {
    return a.content > b.content? 1 : (a.content < b.content? -1 : 0);
  },*/
  /* If you are using 'estact' type, then use following to add status(start/end) representation.
  template: function (item, element, data) {
    return TimelineDataSetBuilder.renderStatus(item, $(element));
  },
  */
}]]></Value>
			</Values>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
	</OptionsGroup>
	
	<OptionsGroup name="Actions">
		<Description>The Data Processor allows you to manipulate the json before its execution. Be careful, some
			properties of the data object may be overridden by the ones set in the Javascript tab.
		</Description>
		<Option id="onInit" name="On Init">
			<Description>Executed when Timeline object is created. Note timeline may not be fully created till this point.</Description>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<OptionComposite id="actions" name="Actions" arity="ZERO_OR_MANY"
			glue="##" isHighlighted="false" isUrlEncoded="false" isXmlEscaped="false">
			<Description>You can register handlers for different visjs events</Description>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
			<Option id="name" name="Name">
				<Description>Actions exposed by Visjs. E.g. 'click', 'changed', 'doubleClick', 'drop' etc.</Description>
			</Option>
			<Option id="function" name="Function">
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>
		</OptionComposite>
	</OptionsGroup>
	
	<OptionsGroup name="Documentation">
		<Option id="displayDoc" name="Enable Documentation">
			<Description>If true, adds a button to display a HTML documentation to give the user information about the chart. (Default value is false)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['doc']})</Display>
			</Functions>
		</Option>
		<Option id="doc" name="User Reference" isEvaluated="true">
			<Description>This is an HTML code to describe the chart.</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
		<Option id="techDoc" name="Technical Reference" isEvaluated="true">
			<Description>This is the chart's technical reference (it won't be displayed in the application).</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Style">
		<Option id="height" name="Height">
			<Description>Specifies the widget height (pixels). If no integer is given, the chart will take 100% of the
				available height.
                &lt;b&gt;WARNING&lt;/b&gt;:
                If widget is used as a sub-widget of a Chart board cell widget, height must not be set to take all available height.
			</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="enableFullScreen" name="Enable full screen" arity="ONE">
			<Description>Allows the user to resize the chart to full screen.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="onInit"><![CDATA[function(plmaPlanning) {
  /*plmaPlanning object. Note vis timeline is still building not yet created*/
  /* Enable Export
  plmaPlanning.enableExport({
    fileNamePrefix: mashup.pageName,
  });*/
}]]></DefaultValue>
		<DefaultValue name="actions"><![CDATA[click##function(properties) {
  /*Check visjs timeline events documentation.
  console.log(properties);*/
}]]></DefaultValue>
		<DefaultValue name="height">350</DefaultValue>		
		<DefaultValue name="hitsPerPage">10</DefaultValue>		
		<DefaultValue name="forceFirstLoad">true</DefaultValue>		
	</DefaultValues>
</Widget>
	
