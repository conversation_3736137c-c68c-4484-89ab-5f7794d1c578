@import "utils.less";
@import "../polyfills.less";

.transform-rotate(@angle) {
  -webkit-transform: rotate(@angle);
     -moz-transform: rotate(@angle);
      -ms-transform: rotate(@angle);
       -o-transform: rotate(@angle);
          transform: rotate(@angle);
}


.appspopup-button{
	cursor: help;
	overflow: visible;
	position: relative;
}

.appspopup{
	background: none repeat scroll 0 0 @ctext-bold;
	color: white;
    border: thin solid @ctext-weak;
    border-radius: 5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    font-family: "3DS Light", "Lucida Grande", Helvetica, Arial, Verdana, sans-serif;
	cursor: auto;
    font-size: 13px;
    font-style: normal;
    min-height: 44px;
    min-width: 80px;
    position: absolute;
    z-index: 1;
   
    &.direction-down {
    	margin-top: 25px;
    }
	display: none;
	
	&.active{
		display: block;
	}
	
	&.appspopup-positionned{
		position: fixed;
	}
	
	> .appspopup-arrow{
		border: medium none;
	    overflow: hidden;
	    position: absolute;
	    
		&:after{
		    background: none repeat scroll 0 0 @ctext-bold;
		    border: thin solid @ctext-weak;
		    box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
		    content: "";
		    display: block;
		    height: 24px;
		    width: 24px;
		    .transform-rotate(45deg);
		}
	}
	
	
    	
   	&.direction-up{
    	left: -10px;
	    bottom: 80%;
	    
	    > .appspopup-arrow{
		    height: 18px;
		    bottom: -18px;
		    left: 0;
		    width: 42px;
		    
		    &:after{
			    margin-left: 10px;
			    margin-top: -14px;
			}
		}
    }
    &.direction-right{
    	left: 80%;
	    top: -10px;
	    
	    > .appspopup-arrow{
		    height: 42px;
		    left: -18px;
		    top: 6px;
		    width: 18px;
			
			&:after{
			    margin-left: 6px;
			    margin-top: 8px;
			}
		}
    }
    &.direction-down{
    	right: -10px;
	    top: 80%;
	    
	    > .appspopup-arrow{
		    height: 18px;
		    right: 6px;
		    top: -18px;
		    width: 42px;
		    
		    &:after{
			    margin-left: 10px;
			    margin-top: 5px;
			}
		}
    }
    &.direction-left{
    	right: 80%;
	    top: -10px;
	    
	    > .appspopup-arrow{
		    height: 42px;
		    right: -18px;
		    top: 0px;
		    width: 18px;
			
			&:after{
			    margin-left: -14px;
			    margin-top: 8px;
			}
		}
    }
    
   
    .appspopup-wrapper{
		padding: 0;
		
		
   	}
}

	


/* IE fixes */
.mashup.mashup-style.ie8 {
	.appspopup {
		&.direction-up{
		    > .appspopup-arrow{
				left: 252px;
				bottom: -12px;
				border-left: 12px solid transparent;
				border-right: 12px solid transparent;
				border-top: 12px solid #ddd;
			}
	    }
	    &.direction-right{
		    > .appspopup-arrow{
				top: 15px;
				left: -12px;
				border-top: 12px solid transparent;
				border-right: 12px solid #ddd;
				border-bottom: 12px solid transparent;
			}
	    }
	    &.direction-down{
		    > .appspopup-arrow{
				left: 252px;
				border-left: 12px solid transparent;
				border-right: 12px solid transparent;
				border-bottom: 12px solid #ddd;
			}
	    }
	    &.direction-left{
		    > .appspopup-arrow{
				top: 15px;
				right: -12px;
				border-top: 12px solid transparent;
				border-left: 12px solid #ddd;
				border-bottom: 12px solid transparent;
			}
	    }
		
		&.direction-up > .appspopup-arrow,
		&.direction-right > .appspopup-arrow,
		&.direction-down > .appspopup-arrow,
		&.direction-left > .appspopup-arrow
		{
			width: 0; 
			height: 0; 
			
			&:after{
				display: none;
			}
			
			&.up{
				left: 252px;
				border-left: 12px solid transparent;
				border-right: 12px solid transparent;
				border-bottom: 12px solid #ddd;
			}
			
			&.left{
				top: 15px;
				left: -12px;
				border-top: 12px solid transparent;
				border-right: 12px solid #ddd;
				border-bottom: 12px solid transparent;
			}
			
		}
	}
}

.plmalightbox-box.layers-builder{
	padding: 7px;
	min-width: 80%;
	position: relative;
	
	.plmalightbox-header{
		padding-bottom: 7px;
		border-bottom: 2px solid #d1d4d4;
	}
	.plmalightbox-contentwrapper{
		overflow-y: auto;
		.icons-container{
			padding: 0px 5px;
			.fonticon{
				font-size: 22px;
				margin: 0px 10px;
				cursor: pointer;
				line-height: 100%;
				&:hover{
					color: #368ec4;
				}				
			}
			.data-invalidate-menu{
				border-left: 1px solid;
				font-size: 20px;
				margin-right: 0px;
				.action-save-layers, .action-undo-layers{
					color: #E87B00;
					cursor: pointer;
					&:hover{
						color: #368ec4;
					}
				}
				&.disabled{
					.action-save-layers, .action-undo-layers{
						color: #FFF3E9;
						cursor: not-allowed;
					}
				}
			}
			.action-publish{
				float: right;
				&.disabled{
					color: #f1f1f1;
					cursor: not-allowed;
				}
			}
			.action-enable-layer-query.plma-checkbox-container{
				label{
					display: inline-flex;
					font-size: 12px;
					color: inherit;
					.fonticon{
						margin: 0px;
						font-size: 14px;
						line-height: 22px;
					}
				}
				.plma-checkbox:checked + label{
					color: #477738;
					&:before{
						background-color: #477738;
					}
				}		
			}
			.action-query-view{
				&.active{						
					color: #42a2da;
				}
			}
			
			.action-api-select {
				float: right;
				border-left: 1px solid;
				padding-left: 10px;
				.plma-checkbox-container{
					label{
						display: inline-flex;
						font-size: 12px;
						color: inherit;
						margin-bottom: 0px;
						.fonticon{
							margin: 0px;
							font-size: 14px;
							line-height: 22px;
							&:before{
								margin-right: 5px;
							}
						}
					}
					.plma-radiobutton:checked + label{
						color: #368ec4;
					}
				}
			}
		}
		
		.notifyjs-wrapper{
			width: 100%;
			.notifyjs-arrow{
				visibility: hidden;
			}
			.notifyjs-container{
				position: initial;
				.notifyjs-plma-base{
					float: right;
					margin-right: 15px;
					margin-top: 2px;
					font-size: 14px;
				}
			}
		}

		.container{
			max-height: 80vh;
			.display-flex();
			padding:15px;
			padding-top: 5px;
			.flex-grow(1);
			.flex-shrink(0);
			.flex-basis(auto);
			.flex-direction(column);
			
			.display-container{
				margin-bottom: 5px;
				padding: 5px;
				border: 1px solid;
				border-radius: 5px;
				.board{
					background-color: #d5e8f2;
					color: #000000;
					font-size: 14px;
					max-width: fit-content;
					min-width: -webkit-fill-available;
					word-break: break-word;
					height: 100px;
					overflow-y: auto;
					padding: 7px;
					border-radius: 5px;
					text-align: left;
				}
			}
			
			.layers-container{
				margin-bottom: 15px;
				.label-container{
					.display-flex();
					line-height: 1.5em;
					flex-grow: 1;
					background-color: #d1d4d4;
					padding-top: 5px;
					align-items: center;
					color: #3d3d3d;
					font-size: 12px;
					padding-left: 5px;
					.column-label{
						flex-grow: 0;
						text-align: left;
						&.sortable{
							cursor: pointer;
							line-height: 27px;							
							.fonticon-sorting{
								margin-right: 5px;
							}
							&:hover{
								background-color: #e2e4e3;
							}
						}
					}
					.column-label-visibility {						
						flex-basis: 28px;
						text-align: left;
						&.plma-checkbox-container{
							margin-right: 0px;
							label{
								height: 22px;
							}
							.decorate.plma-checkbox + label:before {
								border: 1px solid #b4b6ba;
								background: #f9f9f9;
							}
							.decorate.plma-checkbox:checked + label:before {
								border: 1px solid #77797c;
								background: #77797c;
							}
							.decorate.plma-checkbox.partial + label:before {
								background: #f9f9f9;
							}
							.decorate.plma-checkbox.partial + label:after {
								background: #77797c;
							}													
						}
					}
					.column-label-series, .column-label-categories {
						flex-basis: 15%;
					}
					.column-label-agg-value{
						flex-basis: 10%;
					}
					.column-label-layers {
						flex-grow: 1;
					}
					.column-label-color {
						flex-basis: 22px;
						margin-right: 15px;
					}
				}
				ul.not-supported-layer-list,
				ul.layer-list{
					.display-flex();
					.flex-direction(column);
					position: relative;
					
					li.layer{
						align-items: center;
						padding-top: 6px;
						padding-bottom: 6px;
						.display-flex();
						height: 32px;
						padding-left: 5px;
						.plma-checkbox-container {
							margin-right: 0px;
							display: inline-block;
							padding-top: 5px;
							flex-grow: 0;
							flex-basis: 28px;
							label{
								height: 22px;
							}
						}
						.serie-label, .category-label, .agg-value {
							font-size: 12px;
							flex-grow: 0;
							flex-basis: 15%;
							word-break: break-word;
						}
						.agg-value {
							flex-basis: 10%;
						}
						.layer-label {
							flex-grow: 1;
							padding-right: 15px;
							input{
								width: 100%;
								font-size: 14px;
								line-height: 15px;
							}
						}
						.layer-color{
							float: right;
							height: 22px;
							width: 22px;
							margin-right: 15px;
							.fonticon-cog{ 
								display: none;
								float: right;
								margin-right: -14px;
								line-height: 22px;
								height: 22px;
							}
						}
					}
					
					&.edit-enabled{
						li.layer .action-edit-color{
							cursor: pointer;
							.fonticon-cog{ 
								display: inline-block;								
							}
							&:hover{
								color: #368ec4;
							}
							&.active{
								border: 1px solid white;
								z-index: 3;
								border-top-right-radius: 10px;
								border-bottom-right-radius: 10px;
							}
						}
					}
					
					.colorpicker-overlay{
						position: absolute;
						width: ~"calc(100% - 40px)";
						height:100%;
						display: block;
						z-index: 2;
						background: #b4b6ba;
						opacity: 0.5;
					}
					.color-picker-container.hidden + .colorpicker-overlay{
						display: none;
					}
					.color-picker-container{
						display: flex;
						flex-wrap: wrap;
						position: absolute;
						padding: 10px;
						background: white;
						z-index: 3;
						box-shadow: -5px -4px 30px 2px #003c5a;
						width: 335px;
						
						&.hidden{
							display: none;
						}						
						.slider-wrapper{
							width: 25px;
							margin-right: 10px;
							position:relative;
							.slider-indicator{
								width: inherit;
								height: 10px;
								position: absolute;
								left: -3px;
								border: 3px solid black;
								border-radius: 4px;
								background-color: transparent;								
							}
						}
						.picker-wrapper{
							.picker-indicator{
								width: 10px;
								height: 10px;
								border: 2px solid black;
								background-color: white;
								border-radius: 13px;
								font-size: 10px;
								line-height: 11px;
								opacity: 0.5;
							}
						}
						.pallet-wrapper{
							display: flex;
							flex-wrap: wrap;
							.pallet{
								.display-flex();
								flex-wrap: wrap;
								margin: 5px 0px;
								&.col-50{
									width: 50%;
								}
								.title{
									width: 75px;
									color: black;
								}
								.colors{
									.display-flex();
									flex-wrap: wrap;
									row-gap: 8px;									
									.color{
									    width: 20px;
										height: 12px;
										margin: 0px 4px;
										border: 1px solid;
										cursor: pointer;
									}
								}
								&.col-100 .colors{
									width: 335px - 75px;
								}
							}
						}
						&:after{
							content: " ";
							position: absolute;
							right: -7px;
							background: white;
							width: 10px;
							height: 24px;
							top: 0px;
						}
					}
				}
				ul.not-supported-layer-list{
					margin-top: 10px;
					border-top: 1px solid black;
					background-color: #FFF0EE;
					color: black;
					cursor: not-allowed;
					&.hidden{
						display: none;
					}
					&> label{
						font-size: 16px;
						padding: 5px;
						background: #FEE000;
						text-align: left;
					}
					input, .decorate.plma-checkbox + label{
						cursor: not-allowed;
					}
				}
			}
		}
		
		.layers-received{
			display: flex;
			flex-direction: column;
			text-align: left;
			li{
				display: flex;
				height: 20px;
				align-items: center;
				padding: 0px 7px;
				border-bottom: 1px solid #b4b6ba;
				
				span.layer-entry-count{
					flex-basis: 110px;
					flex-grow: 0;
				}
				span.layer-label{
					flex-grow: 1;
				}
				&.header{
					border-top: 1px solid #b4b6ba;
					background: #78befa;
				}
			}			
		}
		.feed-status-all{
			display: flex;
			flex-wrap: wrap;
			.feed-status {
				margin-top: 8px;
				border: 1px solid #d1d4d4;
				border-left: 5px solid #d1d4d4;
				padding: 5px;
				background: #e2e4e3;
				span{
					margin: 0px 5px;
					display: flex;
					padding-left: 10px;
				}
				span:first-child{
					padding-left: 0px;
				}
			}
		}
		.feed-query-all{
			display: flex;
			flex-wrap: wrap;
			.feed-query {
				margin-bottom: 8px;
				border: 1px solid #d1d4d4;
				border-left: 5px solid #d1d4d4;
				padding: 5px;
				background: #e2e4e3;
				span{
					margin: 0px 5px;
					display: flex;
					padding-left: 10px;
				}
				span:first-child{
					padding-left: 0px;
				}
			}
		}
		span.badge.badge-failure{
		    padding: 10px 5px;
		    color: black;
            background-color: #FFF0EE;
            border-color: #df7489;
            border-width: 0px 0px 0px 6px;
		}
	}
}
