<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Planning" group="PLM Analytics/Visualizations" premium="true" > 

	<Description>Displays items on an interactive timeline.</Description>

	<Preview>
		<![CDATA[
			<img src="img/preview.png"/>
		]]>
	</Preview>

	<SupportFeedsId arity="MANY" consumeFeed="false" />
	<SupportI18N supported="true" />
	<SupportWidgetsId arity="ZERO" />

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Includes>
		<Include path="js/vis/vis.min.js" type="js" />
		<Include path="css/vis/vis.css" type="css" />
		<Include path="css/style.less" type="css" />
		<Include path="css/style_item.css" type="css" />
		<Include type="js" path="js/planning.js" />
		<Include type="js" path="../plmaResources/js/widgetButtonManager.js"/>
		<Include type="js" path="../plmaResources/js/fullScreenWidget.js"/>
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<OptionsGroup name="General">
		<Option id="title" name="Widget Title" arity="ZERO_OR_ONE"
			isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
			isXmlEscaped="false">
			<Description>
				Widget title, if blank no name is displayed.
			</Description>
		</Option>
		<Option id="startInterval" name="Start Interval" arity="ZERO_OR_ONE">
			<Description>Specifies the default start and end dates of the timeline. For example, if you enter '2 months', the timeline will start 2 months before the current date and end 2 months in the future.</Description>
		</Option>
		<Option id="extendToValue" name="Extend  interval if no value" arity="ONE">
			<Description>Extends the interval if there are no values within the defined start interval (up to 1 year).</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="loadAll" name="Load all data" arity="ZERO_OR_ONE">
			<Description>Defines if all data must be loaded by default or if we should load a limited number of objects at first load. (Default is false)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['false'], showOptions:['nbObject']})</Display>
			</Functions>
		</Option>
		<Option id="nbObject" name="Number of objects to load" arity="ZERO_OR_ONE">
			<Description>Number of objects to load initially in the planning widget. (Default is 50)</Description>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Configuration">

		<Option id="groupBy" name="Group items by" arity="ZERO_OR_ONE"
			isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
			isXmlEscaped="false">
			<Description>Meta name of the parent object used to stack items. It is displayed in the first column of the widget.</Description>
		</Option>
		
		<Option id="id" name="ID" arity="ONE" isHighlighted="false"
			isUrlEncoded="false" isEvaluated="true" isXmlEscaped="false">
			<Description>Meta name of the Unique Id of items displayed on the timeline.</Description>
		</Option>
		<Option id="label" name="Label" arity="ONE" isHighlighted="false"
			isUrlEncoded="false" isEvaluated="true" isXmlEscaped="false">
			<Description>Meta name of the labels of timeline items.</Description>
		</Option>
		
		<Option id="refineDateStart" name="Start Date Meta's To Search On Name" arity="ONE"
			isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
			isXmlEscaped="false">
			<Description>Start Date Meta name to filter for the displayed date range.</Description>
		</Option>
		
		<Option id="refineDateEnd" name="End Date Meta's To Search On Name" arity="ONE"
			isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
			isXmlEscaped="false">
			<Description>End Date Meta name to filter for the displayed date range.</Description>
		</Option>

		<Option id="start" name="Item main Start date" arity="ONE"
			isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
			isXmlEscaped="false">
			<Description>Meta name of the actual start dates of timeline items.</Description>
		</Option>

		<Option id="end" name="Item main End date" arity="ONE"
			isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
			isXmlEscaped="false">
			<Description>Meta name of the actual end dates of timeline items.</Description>
		</Option>

		<Option id="secondStart" name="Item secondary Start date" arity="ONE"
			isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
			isXmlEscaped="false">
			<Description>Meta name of the estimated start dates of timeline items.</Description>
		</Option>

		<Option id="secondEnd" name="Item secondary End date" arity="ONE"
			isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
			isXmlEscaped="false">
			<Description>Meta name of the estimated end dates of timeline items.</Description>
		</Option>
		
		<Option id="additionalParams" name="Additionnal Parameters" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Additionnal parameters to apply in the query for the objects (for example : bo_type=risk,r=f/current/closed).</Description>
		</Option>
		
		<Option id="colorMeta" name="Color Meta" arity="ZERO_OR_ONE">
			<Description>Meta name used to specify the timeline items' colors.</Description>
		</Option>
		
		<Option id="additionalMetas" name="Other metas to display in info panel" arity="ZERO_OR_ONE">
			<Description>Item's meta to display in info panel (for example : owner,project_related_organization).</Description>
		</Option>
		
		<Option id="extraClasses" name="Extra CSS Classes" arity="ZERO_OR_ONE" isHighlighted="false"
			isUrlEncoded="false" isEvaluated="true" isXmlEscaped="false">
				<Description><![CDATA[
					<table border="1"  width="100%">
						  <tr>
						    <th>Shape</th>
						    <th>size</th>
						    <th>color</th>
						  </tr>
						  <tr>
						    <td>circle,square,triangle</td>
						    <td>lg:large, md:medium, sm:small</td>
						    <td>red,green,yellow</td>
						  </tr>
						 </table>
					]]>
					syntax- vis-shape-name-name color
					eg. if you want a large square with red color then class will be "vis-shape-square-lg vis-shape-red".
				</Description>
			<Functions>
				<ContextMenu>Eval()</ContextMenu>
				<Display>SetType('code', 'mel')</Display>
			</Functions>
		</Option>
		
		<Option id="enableSubObjects" name="Enable sub-objects" arity="ONE">
			<Description>Displays sub-objects on the timeline.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['dateMeta', 'relatedMeta', 'subDisplayType', 'additionalParamsSubItems', 'subItemColorMeta', 'nameSubItem', 'idSubItem']})</Display>
			</Functions>
		</Option>
		<Option id="nameSubItem" name="Label to Display for sub-item name" arity="ZERO_OR_ONE">
			<Description>The name of the sub-items to display.</Description>
		</Option>
		<Option id="idSubItem" name="Id to Display for sub-item name" arity="ZERO_OR_ONE">
			<Description>The id of the sub-items to display.</Description>
		</Option>
		<Option id="dateMeta" name="Date Meta to Display" arity="ZERO_OR_ONE">
			<Description>The name of the date meta to display.</Description>
		</Option>
		<Option id="relatedMeta" name="Related Meta" arity="ZERO_OR_ONE">
			<Description>The name of the meta to relate the object to the sub-objects.</Description>
		</Option>
		<Option id="additionalParamsSubItems" name="Additionnal Parameters" arity="ZERO_OR_ONE">
			<Description>Additionnal parameters to apply in the query for the sub-objects (for example: bo_type=risk,r=f/current/closed).</Description>
		</Option>
		<Option id="subItemColorMeta" name="Sub Item Color Meta" arity="ZERO_OR_ONE">
			<Description>Meta name used to specify the timeline sub-items' colors.</Description>
		</Option>
		<Option id="additionalSubItemMetas" name="Other metas to display in info panel for sub item" arity="ZERO_OR_ONE">
			<Description>Item's meta to display in info panel for sub-item (for example: owner,project_related_organization).</Description>
		</Option>
		<Option id="subDisplayType" name="Display Type" arity="ONE">
				<Description>Specifies the display type of sub-objects.</Description>
				<Values>
					<Value>square</Value>
					<Value>circle</Value>
					<Value>triangle</Value>
					<Value>diamond</Value>
				</Values>
			</Option>
	</OptionsGroup>

	<OptionsGroup name="Documentation">
		<Option id="displayDoc" name="Enable Documentation">
			<Description>If true, adds a button to display a HTML documentation to give the user information about the chart. (Default value is false)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['doc']})</Display>
			</Functions>
		</Option>
		<Option id="doc" name="User Reference" isEvaluated="true">
			<Description>This is an HTML code to describe the chart.</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
		<Option id="techDoc" name="Technical Reference" isEvaluated="true">
			<Description>This is the chart's technical reference (it won't be displayed in the application).</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
	</OptionsGroup>
	
	<OptionsGroup name="Style">
		<Option id="height" name="Height" arity="ZERO_OR_ONE"
			isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
			isXmlEscaped="false">
			<Description>Specifies the height of the widget (pixels). You
				must enter an integer.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="enableFullScreen" name="Enable full screen" arity="ONE">
			<Description>Allows you to resize your chart to full screen.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="actions"><![CDATA[
			function timeline_onclick(properties) {
				/*console.log(properties);*/
			}
		]]></DefaultValue>
		<DefaultValue name="title">Project Timeline</DefaultValue>
		<DefaultValue name="minheight">200</DefaultValue>
		<DefaultValue name="maxheight">1000</DefaultValue>
		<DefaultValue name="id">${entry.metas["id"]}</DefaultValue>
	</DefaultValues>
</Widget>
