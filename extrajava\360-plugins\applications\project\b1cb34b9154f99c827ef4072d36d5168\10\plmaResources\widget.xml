<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA common resources" group="PLM Analytics/Technical" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>PLMA Common Resources. No need to include this widget in a page, it is only used to deploy common resources in a plugin.</Description>
	
	
	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="false" />
	</Platforms>

	<SupportWidgetsId arity="ZERO" />
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>widget.action.close</JsKey>
			<JsKey>widget.action.fullScreen</JsKey>
			<JsKey>widget.action.exitFullScreen</JsKey>
			<JsKey>statusCode.description.403</JsKey>
			
			<JsKey>plma.selectapi.selsearch.history.label</JsKey>
			<JsKey>plma.exportto3dspace.title</JsKey>
			<JsKey>plma.exportto3dspace.adddocchkbox.label</JsKey>
			<JsKey>plma.exportto3dspace.exportbutton.label</JsKey>
			<JsKey>plma.exportto3dspace.drop.label</JsKey>
			<JsKey>plma.exportto3dspace.publish.label</JsKey>
			<JsKey>plma.exportto3dspace.docinfo.label</JsKey>
			<JsKey>plma.exportto3dspace.fileinfo.label</JsKey>
			<JsKey>plma.exportto3dspace.addfilechkbox.label</JsKey>
			<JsKey>plma.exportto3dspace.nameinput.label</JsKey>
			<JsKey>plma.exportto3dspace.nameinput.placeHolder</JsKey>
			<JsKey>plma.exportto3dspace.titleinput.label</JsKey>
			<JsKey>plma.exportto3dspace.descriptioninput.label</JsKey>
			<JsKey>plma.exportto3dspace.commentinput.label</JsKey>
			<JsKey>plma.exportto3dspace.fileselect.placeholder</JsKey>
			<JsKey>plma.exportto3dspace.error.configurations</JsKey>
			<JsKey>plma.exportto3dspace.error.apiexecution</JsKey>
			<JsKey>plma.exportto3dspace.error.outside.3dexp</JsKey>
			<JsKey>plma.exportto3dspace.error.unsupported.type</JsKey>
			<JsKey>plma.exportto3dspace.error.insufficient.access</JsKey>
			<JsKey>plma.exportto3dspace.commentinput.defaultcomments</JsKey>
			<JsKey>plma.exportto3dspace.controlvalidation.doc</JsKey>
			<JsKey>plma.exportto3dspace.controlvalidation.doc.title</JsKey>
			<JsKey>plma.exportto3dspace.controlvalidation.file.name</JsKey>
			<JsKey>plma.exportto3dspace.controlvalidation.file.ext</JsKey>
			<JsKey>plma.exportto3dspace.controlvalidation.file.duplicate</JsKey>
			<JsKey>plma.exportto3dspace.controlvalidation.file.notselected</JsKey>
			<JsKey>plma.exportto3dspace.success.done</JsKey>
			<JsKey>plma.exportto3dspace.success.share.object</JsKey>
			<JsKey>plma.swympublishmanager.success.publish</JsKey>
			<JsKey>plma.swympublishmanager.warn.wrong.contenttype</JsKey>
			<JsKey>plma.swympublishmanager.error.outside.3dexp</JsKey>
			<JsKey>plma.swympublishmanager.error.apiexecution</JsKey>
			<JsKey>plma.swympublishmanager.error.not.publish</JsKey>
			<JsKey>plma.swympublishmanager.error.noswym</JsKey>
			<JsKey>plma.exportto3dspace.controlvalidation.bookmark.doc</JsKey>
			<JsKey>plma.exportto3dspace.controlvalidation.bookmark</JsKey>
			<JsKey>plma.exportto3dspace.error.nobookmark</JsKey>
			<JsKey>plma.publishto3ddashboard.id</JsKey>
			<JsKey>plma.publishto3ddashboard.container.title</JsKey>
			<JsKey>plma.publishto3ddashboard.id</JsKey>
			<JsKey>plma.publishto3ddashboard.container.title</JsKey>
			<JsKey>plma.publishto3ddashboard.bookmarkform.button.label</JsKey>
			<JsKey>plma.publishto3ddashboard.container.title</JsKey>
			<JsKey>plma.publishto3ddashboard.swym.tab.label</JsKey>
			<JsKey>plma.publishto3ddashboard.bookmark.tab.label</JsKey>
			<JsKey>plma.publishto3ddashboard.preview.label</JsKey>
			<JsKey>plma.publishto3ddashboard.preview.input.label</JsKey>
			<JsKey>plma.publishto3ddashboard.error.outside.3dexp</JsKey>
			<JsKey>plma.publishto3ddashboard.info.copiedtoclipboard</JsKey>
			<JsKey>plma.publishto3ddashboard.copytoclipboard</JsKey>
			<JsKey>plma.publishto3ddashboard.container.title.context</JsKey>
			<JsKey>plma.publishto3ddashboard.container.title.snapshot</JsKey>
			<JsKey>plma.error.outside.3dexperience</JsKey>
			<JsKey>plma.error.no.3dcontent.incontext</JsKey>
			<JsKey>plma.entrich3d.popup.header</JsKey>
			<JsKey>plma.enrich3d.colorize.button.title</JsKey>
			<JsKey>plma.entrich3d.popup.layerquery.add</JsKey>
			<JsKey>plma.entrich3d.popup.layerquery.show</JsKey>
			<JsKey>plma.entrich3d.popup.changes.save</JsKey>
			<JsKey>plma.entrich3d.popup.changes.undo</JsKey>
			<JsKey>plma.entrich3d.popup.restore.default</JsKey>
			<JsKey>plma.entrich3d.popup.publish.event</JsKey>
			<JsKey>plma.entrich3d.popup.api.poi</JsKey>
			<JsKey>plma.entrich3d.popup.api.colorize</JsKey>
			<JsKey>plma.entrich3d.popup.select.all</JsKey>
			<JsKey>plma.entrich3d.popup.column.series</JsKey>
			<JsKey>plma.entrich3d.popup.column.categories</JsKey>
			<JsKey>plma.entrich3d.popup.column.aggvalue</JsKey>
			<JsKey>plma.entrich3d.popup.column.layerlabel</JsKey>
			<JsKey>plma.entrich3d.popup.column.color</JsKey>
			<JsKey>plma.entrich3d.popup.series.notsupported</JsKey>
			<JsKey>plma.enrich3d.error.no.data.from.chart</JsKey>
			<JsKey>plma.enrich3d.error.controller.failed</JsKey>
			<JsKey>plma.enrich3d.error.dyndate.notsupported</JsKey>
			<JsKey>plma.entrich3d.error.no.entries</JsKey>
			<JsKey>plma.entrich3d.error.no.matches</JsKey>
			<JsKey>plma.entrich3d.error.unsaved.changes</JsKey>
			<JsKey>plma.entrich3d.error.no.layer.selected</JsKey>
			<JsKey>plma.entrich3d.error.no.layers.found</JsKey>
			<JsKey>plma.entrich3d.warn.unmatched.entries</JsKey>
			<JsKey>plma.entrich3d.warn.index.has.paths</JsKey>
		</JsKeys>
	</SupportI18N>
	
<!-- 	WARNING: resources included here will be included by all PLMA-tools widgets,
		as they all inherit this widget's dependencies. Only add light, compulsory
		resources here.
		All other resources brought by this widget are still available for other widgets
		to include separately. -->
	<Includes>
		<Include type="js" path="js/polyfills.js"/>
		<Include type="js" path="js/polyfill-classList.js"/>
	</Includes>
	
</Widget>
