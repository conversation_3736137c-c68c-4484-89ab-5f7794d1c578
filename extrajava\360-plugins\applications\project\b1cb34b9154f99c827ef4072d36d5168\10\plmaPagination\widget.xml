<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA Pagination" group="Navigation">

	<Description>The pagination widget allows you to scroll into your result set.</Description>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/paginationManager.js" />
	</Includes>
	
	<Platforms>
		<Platform type="mobile" supported="true" />
		<Platform type="web" supported="true" />
	</Platforms>
	
	<Preview>
		<![CDATA[
			<img src="/resources/widgets/plmaPagination/images/preview.png" alt="Pagination" />
		]]>
	</Preview>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="MANY" />
	<SupportI18N supported="true" />

	<OptionsGroup name="General">
		<Option id="jspPath" name="Pagination JSP template" arity="ONE">
			<Description>
				<![CDATA[
					Specifies the Navigation header JSP template.<br />
					Change this if you do not want to use the original template.<br />
					Templates are located in /templates/.
				]]>			
			</Description>
			<Functions>
				<Check>isJspPath</Check>
				<Display>PARAMETER_doHide('default.jsp', [], ['nbPageToShowInPagination', 'moreResultsUrl', 'moreResultsLabel'], true, false)</Display>
				<ContextMenu>JAVA_listFiles('templates/', '*.jsp', 'JSP base templates')</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="nbPageToShowInPagination" name="Number of pagination pages">
			<Description>Specifies the number of pages to display in the pagination. </Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="wuid" name="Widget to paginate" arity="ONE">
			<Description>Specifies the wuid of the widget to paginate. </Description>
			<Functions>
				<ContextMenu>WIDGET_getWUIDS()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="jspPath">default.jsp</DefaultValue>
		<DefaultValue name="nbPageToShowInPagination">9</DefaultValue>
		<DefaultValue name="templateBasePath">templates/</DefaultValue>
	</DefaultValues>
</Widget>
