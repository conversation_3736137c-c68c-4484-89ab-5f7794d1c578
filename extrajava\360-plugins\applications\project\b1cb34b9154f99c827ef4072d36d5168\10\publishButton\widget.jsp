<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="security" uri="http://www.exalead.com/jspapi/security" %>
<render:import varWidget="widget" varFeeds="feeds" />

<config:getOption name="iconCss" var="iconCss"/>
<config:getOption name="label" var="label"/>
<config:getOption name="showLabel" var="showLabel" defaultValue="true"/>
<config:getOption name="onInit" var="onInit" defaultValue="function(){}"/>
<config:getOption name="onDone" var="onDone" defaultValue="function(){}"/>
<config:getOption name="publishToSwym" var="publishToSwym"/>
<config:getOption name="publishToBookmark" var="publishToBookmark"/>
<config:getOption name="enableReloadContext" var="enableReloadContext" defaultValue="true"/>

<c:set var="fileMode" value="false"/>
<widget:widget varUcssId="uCssId" extraCss="plmaButton publishButton disabled" varCssId="cssId">
	<span class="button-content">
		<c:if test="${not empty iconCss}">
			<i class="${iconCss}" title="${label}"></i>
		</c:if>
		<c:if test="${showLabel}">
			<span class="button-label">${label}</span>
		</c:if>
	</span>
	<span class="hidden">
	    <div class="tabs-container">
			<ul class="tabs">
				<c:if test="${publishToBookmark}">
					<li>
						<a href="#${cssId}-bookmark" class="tab-item">
							<i class="fonticon fonticon-bookmark"></i>
							<i18n:message code="plma.publishto3ddashboard.bookmark.tab.label"/>
						</a>
					</li>
				</c:if>
				<c:if test="${publishToSwym}">
					<li>
						<a href="#${cssId}-swym" class="tab-item">
							<i class="fonticon fonticon-logo-3dswym"></i>
							<i18n:message code="plma.publishto3ddashboard.swym.tab.label"/>
						</a>
					</li>
				</c:if>
			</ul>
			<div class="contents">
				<c:if test="${publishToBookmark}">
					<div id="${cssId}-bookmark" class="tab-content tabs-publish-bookmark"></div>
				</c:if>
				<c:if test="${publishToSwym}">
					<div id="${cssId}-swym" class="tab-content tabs-publish-swym"></div>
				</c:if>
			</div>
		</div>

		<c:set var="getPreviewContainer" value="function(){
            return PublishButton.getPreviewContainer('${cssId}');
        }"/>
        <c:set var="previewHandler" value="function($previewContainer, pdfDoc, fileName, id){
            PublishButton.filePreviewHandler('${cssId}', $previewContainer, pdfDoc, fileName, id);
        }"/>
		<c:choose>
		    <c:when test="${widget:hasSubWidgets(widget)}">
		        <widget:forEachSubWidget>
                    <c:set var="fileMode" value="true"/>
                    <render:widget>
                        <render:parameter name="getPreviewContainer" value="${getPreviewContainer}"/>
                        <render:parameter name="previewHandler" value="${previewHandler}"/>
                    </render:widget>
                </widget:forEachSubWidget>
		    </c:when>
		    <c:when test="${fileMode == true}">
		        <div class="${uCssId}-PDFCreator pdfCreator">
                    <span class="button-content">
                        <i class="fonticon fonticon-export" title="Create PDF"></i>
                    </span>
                </div>
                <render:renderScript position="READY">
                    <config:getOption name="fileName" var="fileName" defaultValue="plma_page"/>
                    <config:getOption name="fileOptions" var="fileOptions" defaultValue="{}"/>
                    <config:getOptions name="widgetOptions" var="widgetOptions" defaultValue="{}"/>
                    <render:template template="javascript.jsp" widget="pdfCreator">
                        <render:parameter name="buttonSelector" value=".${uCssId}-PDFCreator.pdfCreator > .button-content" />
                        <render:parameter name="onInit" value="function(){}" />
                        <render:parameter name="onDone" value="function(){}" />
                        <render:parameter name="fileName" value="${fileName}" />
                        <render:parameter name="fileOptions" value="${fileOptions}" />
                        <render:parameter name="widgetOptions" value="${widgetOptions}" />
                        <render:parameter name="getPreviewContainer" value="${getPreviewContainer}"/>
                        <render:parameter name="previewHandler" value="${previewHandler}"/>
                    </render:template>
                </render:renderScript>
            </c:when>
        </c:choose>
	</span>
	<render:renderScript position="READY">
		<security:getUser var="currUser"/>
		(function() {
			new PublishButton({
				cssId: '${cssId}',
				onInit: ${onInit},
				onDone: ${onDone},
				fileMode: ${fileMode},
				title: '<i18n:message code="plma.publishto3ddashboard.container.title" javaScriptEscape="${true}"/>',
				isBookmarkEnable: ${publishToBookmark},
				isSwymEnable: ${publishToSwym},
				enableDrop: ${enableReloadContext}
			});
		})();
	</render:renderScript>
</widget:widget>