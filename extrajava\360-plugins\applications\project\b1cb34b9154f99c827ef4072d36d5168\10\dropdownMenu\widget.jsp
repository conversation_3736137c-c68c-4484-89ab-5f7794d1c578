<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>

<config:getOption name="iconCss" var="iconCss" />
<config:getOption name="title" var="title" />

<widget:widget extraCss="dropdownMenu" varUcssId="uCssId" varCssId="cssId">
    <widget:content>
        <span class="${iconCss}" title="${title}"></span>
        <div class="content hidden">
            <widget:forEachSubWidget>
                <config:getOption name="cssClass" var="cssClass" />
                <div class="button-container ${fn:contains(cssClass, 'dropdownMenu-separator') ? 'separator' : ''}">
                    <render:widget />
                </div>
            </widget:forEachSubWidget>
        </div>
    </widget:content>
</widget:widget>

<render:renderScript position="READY">
    var $widget = $('#${cssId}');
    var $widgetContent = $widget.find('.content');
    $widget.popup({
        content: $widgetContent,
        mode: 'click',
        direction: 'down',
        closeOnClickOutside: true,
        delay: 0,
        autoposition: false,
        onInit: function() {
            $widgetContent.removeClass('hidden');
        }
    });
</render:renderScript>