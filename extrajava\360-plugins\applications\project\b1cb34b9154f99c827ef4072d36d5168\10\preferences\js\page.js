var PreferencePage = function (menu, uCssId, options) {
	this.menu = menu;
	this.uCssId = uCssId;
	var defaultOptions = {};
	this.hasChanges = false;
	this.options = $.extend({}, defaultOptions, options);
	this.widget = $('.' + this.uCssId);
	this.container = $('.' + this.uCssId + ' .preference-menu-container');
	this.i18nClient = new I18nClient();
	this.chartboardStorageManager = new ChartboardStorageManager();
	this.landingPageManager = options.landingPageManager;
	this.deletedElems = [];
};

PreferencePage.prototype.init = function () {
    this.initTab();
};

PreferencePage.prototype.initTab = function () {
	let listURL = this.options.url + (this.options.url.endsWith('/') ? '' : '/') + 'pages/list';

	this.container.updateHTML(listURL, {wuid: this.uCssId}, $.proxy(function () {
		this.initButtons();
		this.initDetail();
	},this), function () {
		console.error("Error fetching pages")
	})
}

PreferencePage.prototype.initButtons = function () {
	var closeButton = this.container.find('.close-button');
    var resetButton = this.container.find('.reset-button');
	var trashIcon = this.container.find('.trash-icon.fonticon.fonticon-trash');
	var unsubscribeIcon = this.container.find('.unsubscribe-page.fonticon.fonticon-user-times');

    resetButton.on('click', $.proxy(function () {
        $.ajax({
            type: 'DELETE',
            url: this.options.url + 'config/user/facet/display/reset',
            data: {
                confName: this.options.configName,
                path: ''
            },
            success: $.proxy(function () {
				$.notify(Preferences.getMessage('plma.preference.save.success'), 'success');
                setTimeout($.proxy(function() {
                    this.reload();
                },this),1000);
            }, this)
        });
    }, this));

    closeButton.on('click', $.proxy(function () {
        window.location.reload();
    }, this));

	trashIcon.on('click', $.proxy(function (e) {
		e.stopPropagation();
		$e = $(e.target).closest('.page-elem');
		//this.deleteElem($e);
		this.removePageTemp($e);
	}, this));

	unsubscribeIcon.on('click', $.proxy(function (e) {
		e.stopPropagation();
		$e = $(e.target).closest('.page-elem');
		//this.deleteElem($e);
		this.removeSelf($e);
	}, this));
}


PreferencePage.prototype.initDetail = function () {
	$('.page-section').on('click', $.proxy(function (e) {
		e.stopPropagation();
		var el = $(e.target);
		if(el.closest('.page-elem').length > 0) {
			if(!el.closest('.page-elem').hasClass('selected')) {
				this.container.find('.selected').removeClass('selected');
				el.closest('.page-elem').addClass('selected');
				let selectedPage = this.container.find('.selected .page-description');
				let pageId = selectedPage.data('id');
				this.displayDetail(pageId);
			}
		}
	}, this));
}

PreferencePage.prototype.displayDetail = function (id) {
	let fetchPageURL = this.options.url + 'savedPages/admin/' + id;
	let detailEl = $('.preference-detail.preference-page-detail');
	detailEl.removeClass('hidden');

	detailEl.updateHTML(fetchPageURL, {wuid: this.uCssId}, $.proxy(function () {
		this.detail = detailEl;
		this.initDetailButtons(id);
	},this), function () {
		console.error("Error fetching page")
	})
}

PreferencePage.prototype.initDetailButtons = function (currPageId) {
	this.initIconContainer(this.detail);

	if (this.options.user) {
        this.detail.find('.icon-container .icon-input').on('click', $.proxy(function (e) {
            this.toggleIconContainer(this.detail);
        }, this));
    }

	this.detail.find('.description-input').on('blur', $.proxy(function (e) {
        let inputValue = e.target.value;
        let $elem = this.container.find('.page-elem.selected .page-description');
        let currentValue = this.container.find('.page-elem.selected .page-description').data('description-i18n')
        if (inputValue !== currentValue) {
            $elem.attr('data-description-i18n', inputValue);
            this.updatePageInfo('description', inputValue, Preferences.getMessage('plma.preference.pages.updateDescription'));
        }
    }, this));

	this.detail.find('.label-input').on('blur', $.proxy(function (e) {
        let inputValue = e.target.value;
        let $elem = this.container.find('.page-elem.selected');
        let currentValue = $elem.find('.elem-label')[0].innerText;
        if (inputValue !== currentValue) {
            $elem.find('.elem-label')[0].innerText = inputValue;
            this.updatePageInfo('label', inputValue, Preferences.getMessage('plma.preference.pages.updateLabel'));
            if (this.options.isIn3DXP) {
                this.landingPageManager.editPageLabelById($elem.find('.elem-description').data('id'), inputValue);
            }
        }
    }, this));

	this.detail.find('.fonticon-elem').on('click', $.proxy(function (e) {
		$e = $(e.target);
		if (this.detail.find('.icon-input.no-icon').length > 0) {
			this.detail.find('.icon-input.no-icon').text('');
		}
		this.detail.find('.icon-input').removeClass().addClass('icon-input').addClass($e.attr('class')).removeClass('fonticon-elem');
		this.container.find('.page-elem.selected .elem-icon').removeClass().addClass('elem-icon page-icon').addClass($e.attr('class')).removeClass('fonticon-elem');
		this.container.find('.page-elem.selected .elem-icon').data('icon', $e.attr('class').split('fonticon-elem')[1]);
		var classes = $e.attr('class').split(' ');
        this.updatePageInfo('icon', 'fonticon '+classes[classes.length - 1], Preferences.getMessage('plma.preference.pages.updateIcon'));
	}, this));

  	this.detail.find('.share-popup-wrapper .share-write .add-button').on('click', $.proxy(function (e) {
  		var user = this.detail.find('.share-popup-wrapper .share-write .share-input').val().trim();
  		if (user !== '') {
  			this.addUser(currPageId, user, true);
  			this.detail.find('.share-popup-wrapper .share-write .share-input').val('');
  			this.detail.find('.share-popup-wrapper .share-write .users-input-container .share-suggest-container').remove();
  		}
  	}, this));
  	this.detail.find('.share-popup-wrapper .share-write .share-input').on('keypress', $.proxy(function (e) {
  		if (e.which === 13) {
  			var user = this.detail.find('.share-popup-wrapper .share-write .share-input').val().trim();
  			if (user !== '') {
  				this.addUser(currPageId, user, true);
  				this.detail.find('.share-popup-wrapper .share-write .share-input').val('');
  				this.detail.find('.share-popup-wrapper .share-write .users-input-container .share-suggest-container').remove();
  			}
  		}
  	}, this));

  	this.detail.find('.share-write .share-checkbox-input').on('change', $.proxy(function (e) {
		if ($(e.target).is(':checked')) {
			this.addAll(currPageId, true);
		} else {
			this.removeAll(currPageId, true);
		}
  	}, this));

	this.detail.find('.share-write .user .remove-user').on('click', $.proxy(function (e) {
		let user = $(e.target).data('user');
  		this.removeUser(currPageId, user, true);
  	}, this));

  	this.detail.find('.share-popup-wrapper .share-read .read-add-button').on('click', $.proxy(function (e) {
  		var user = this.detail.find('.share-popup-wrapper .share-read .share-input').val().trim();
  		if (user !== '') {
  			this.addUser(currPageId, user, false);
  			this.detail.find('.share-popup-wrapper .share-read .share-input').val('');
  			this.detail.find('.share-popup-wrapper .share-read .users-input-container .share-suggest-container').remove();
  		}
  	}, this));
  	this.detail.find('.share-popup-wrapper .share-read .share-input').on('keypress', $.proxy(function (e) {
  		if (e.which === 13) {
  			var user = this.detail.find('.share-popup-wrapper .share-read .share-input').val().trim();
  			if (user !== '') {
  				this.addUser(currPageId, user, false);
  				this.detail.find('.share-popup-wrapper .share-read .share-input').val('');
  				this.detail.find('.share-popup-wrapper .share-read .users-input-container .share-suggest-container').remove();
  			}
  		}
  	}, this));

  	this.detail.find('.share-read .share-checkbox-input').on('click', $.proxy(function (e) {
		if ($(e.target).is(':checked')) {
			this.addAll(currPageId, false);
		} else {
			this.removeAll(currPageId, false);
		}
  	}, this));

	this.detail.find('.share-read .user .remove-user').on('click', $.proxy(function (e) {
		let user = $(e.target).data('user');
  		this.removeUser(currPageId, user, false);
  	}, this));
}

PreferencePage.prototype.addUser = function (pageId, user, write) {
	$.ajax({
		type: 'POST',
		url: this.options.url + 'savedPages/share/',
		data: {
			id: pageId,
			user: user,
			write: write
		},
		success: $.proxy(function () {
			$.notify('User added', "success");
			this.displayDetail(pageId);
		}, this)
	});
}

PreferencePage.prototype.removeUser = function (pageId, user, write) {
	$.ajax({
		type: 'POST',
		url: this.options.url + 'savedPages/unshare/',
		data: {
			id: pageId,
			user: user,
			write: write
		},
		success: $.proxy(function () {
			$.notify('User removed', "success");
			this.displayDetail(pageId)
		}, this)
	});
}

PreferencePage.prototype.addAll = function (pageId, write) {
	$.ajax({
		type: 'POST',
		url: this.options.url + 'savedPages/shareAll/',
		data: {
			id: pageId,
			write: write
		},
		success: $.proxy(function () {
			$.notify('Page shared with all users', "success");
		}, this)
	});
}

PreferencePage.prototype.removeAll = function (pageId, write) {
	$.ajax({
		type: 'POST',
		url: this.options.url + 'savedPages/unshareAll/',
		data: {
			id: pageId,
			write: write
		},
		success: $.proxy(function () {
			$.notify('Page sharing removed', "success");
		}, this)
	});
}

PreferencePage.prototype.hideDetail = function () {
	if (this.detail) {
		this.detail.addClass('hidden');
	}
};

PreferencePage.prototype.initIconContainer = function (container) {
	container.find('.preference-icon-tooltip-input').on('keyup', function () {
		var searchValue = this.value;
		var iconList = this.parentElement.children;
		for (var i = 1; i < iconList.length; i++) {
			var elemClassList = iconList[i].getClassList();
			if (elemClassList[2].indexOf(searchValue.replace('fonticon ', '')) !== -1) {
				elemClassList.remove('hidden');
			} else {
				elemClassList.add('hidden');
			}
		}
	});
};

PreferencePage.prototype.toggleIconContainer = function (container) {
	container.find('.preference-icon-tooltip').toggleClass('hidden');
};

PreferencePage.prototype.updatePageInfo = function (type, elem, msg) {
    var pageId = this.container.find('.page-elem.selected .page-description').data('id');
    var url = '';
    switch(type){
        case "label":
            url = this.options.url + 'savedPages/update/' + pageId + '?' + 'label=' + elem;
            break;
        case "icon":
            url = this.options.url + 'savedPages/update/' + pageId + '?' + 'icon=' + elem;
            break;
        case "description":
            url = this.options.url + 'savedPages/update/' + pageId + '?' + 'description=' + elem;
            break;
    }
    $.ajax({
        method: 'PATCH',
        url: url,
        dataType: 'JSON',
        async: 'false',
        success: function (data) {
            if (data === 500) {
                this.failureTemp();
            } else {
                /* Activate save and cancel button */
                $.notify(msg, "success");
            }
        },
        error: function (err) {
            $.notify('Error updating fields (' + err.responseJSON.error + ')', "error");
        }
    });
}

PreferencePage.prototype.editPageTemp = function (msg) {
    var pageId = this.container.find('.page-elem.selected .page-description').data('id');
    var pageLabel = this.detail.find('.label-container .label-input')[0].value;
    var pageDescription = this.detail.find('.description-container .description-input')[0].value;
    var pageTemplate = this.container.find('.page-elem.selected .page-description').data('template')
    // var pageSection = this.detail.find('.section-container .section-input')[0].value;
    var classes = this.detail.find('.icon-container .icon-input').attr('class').split(' ');
    var pageIcon = 'fonticon ' + classes[classes.length - 1];
    $.ajax({
        method: 'POST',
        url: this.options.url + 'savedPages/save',
        data: {
            id: pageId,
            label: pageLabel,
            description: pageDescription,
            icon: pageIcon,
            template: pageTemplate
        },
        dataType: 'JSON',
        async: 'false',
        success: $.proxy(function (data) {
            if (data === 500) {
                this.failureTemp();
            } else {
                /* Activate save and cancel button */
                $.notify(msg, "success");
            }
        }, this)
    })
};

PreferencePage.prototype.removePageTemp = function (elem) {
	var elemId = elem.find('.page-description').data('id');

	$.ajax({
        method: 'DELETE',
        url: this.options.url + 'savedPages/delete/' + elemId,
        dataType: 'JSON',
        async: false,
        success: $.proxy(function (data) {
            $.notify('Page successfully deleted', "success");
            this.init();
        }, this),
        error: function (data) {
            $.notify('Error deleting page (' + data.responseJSON.error + ')', "error");
        }
    });
	if (this.options.isIn3DXP) {
		this.landingPageManager.removePageById(elemId);
	}
};

PreferencePage.prototype.removeSelf = function (elem) {
	var elemId = elem.find('.page-description').data('id');

	$.ajax({
        method: 'POST',
        url: this.options.url + 'savedPages/unshareMe/' + elemId,
        dataType: 'JSON',
        async: false,
        success: $.proxy(function (data) {
            $.notify('Page successfully removed', "success");
            this.init();
        }, this),
        error: function (data) {
            $.notify('Error removing page (' + data.responseJSON.error + ')', "error");
        }
    });
	if (this.options.isIn3DXP) {
		this.landingPageManager.removePageById(elemId);
	}
};

PreferencePage.prototype.failureTemp = function () {
	$.notify(Preferences.getMessage('plma.preference.save.error'), 'error');
};

PreferencePage.prototype.reload = function () {
	var url = new BuildUrl(window.location.href);
	if (this.deletedElems.length > 0 && url.getParameter('pageId')) {
		for (var i = 0; i < this.deletedElems.length; i++) {
			url.removeParameterWithValue_('pageId', this.deletedElems[i]);
		}
		window.location.replace(url.toString());
	} else {
		location.reload();
	}
};

PreferencePage.utils = {
	getI18nKey: function (message) {
		if (message) {
			var matches = message.match(/\${i18n\['(.*?)'\]}/);
			if (matches && matches[1]) {
				return matches[1];
			}
		}
		return null;
	},
	addI18nKeyToMap: function (map, key) {
		if (key) {
			map[key] = '';
		}
	},
	addI18nKeysFromPage: function (map, page) {
		var sectionKey = PreferencePage.utils.getI18nKey(page.section);
		var labelKey = PreferencePage.utils.getI18nKey(page.label);
		var descriptionKey = PreferencePage.utils.getI18nKey(page.description);
		PreferencePage.utils.addI18nKeyToMap(map, sectionKey);
		PreferencePage.utils.addI18nKeyToMap(map, labelKey);
		PreferencePage.utils.addI18nKeyToMap(map, descriptionKey);
	},
	buildUrl: function (target, whitelist, additionalParameters) {
		var url = new BuildUrl(window.location.origin + window.location.pathname);
		var thisPage = url.getPage();
		for (var key in additionalParameters) {
			url.addParameter(key, additionalParameters[key], true);
		}
		url = PinnedPages.utils.getCleanUrl(url.toString(), whitelist);
		/* Handle special case for the home page */
		if (target === '/') {
			target = 'index';
		}
		return url.replace(thisPage, target);
	}
};