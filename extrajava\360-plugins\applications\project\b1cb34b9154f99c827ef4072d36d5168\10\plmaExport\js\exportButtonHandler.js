var ExportButtonHandler = function (uCssId) {
    if (uCssId) {
        this.$widget = $('.' + uCssId);
        // this.widget.data('widget', this);
    } else {
        this.$widget = $();
    }
    this.$elements = {
        EXPORT_BUTTON: this.$widget.find('.exportButton'),
        EXPORT_FORM: this.$widget.find('.export-data-form')
    };
    this.init();
    return this;
};

ExportButtonHandler.prototype.init = function () {
    this.$elements.EXPORT_BUTTON.off('click').on('click', function () {
        this.$elements.EXPORT_FORM.submit();
    }.bind(this));
}