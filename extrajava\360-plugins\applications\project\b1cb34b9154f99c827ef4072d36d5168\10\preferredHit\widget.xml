<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Preferred hit" group="PLM Analytics/Favorites" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>Store displayed hit in favorite hits.</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/plmaButton/images/preview.png" alt="Preferred hit widget" />
        ]]>
	</Preview>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/preferredHit.js" />
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ONE" />
	<SupportI18N supported="true">
		<JsKeys>
		</JsKeys> 
	</SupportI18N>
	
	<OptionsGroup name="General">
		<Option id="label" name="Label" isEvaluated="true" arity="ONE">
			<Description>Specifies the label of the button. Briefly explains the purpose of the button (3 words max).</Description>
		</Option>
		<Option id="hitID" name="Hit ID" arity="ONE">
			<Description>Hit ID MEL expression.</Description>
		</Option>
		<Option id="collection" name="Collection" arity="ONE">
			<Description>Preferred hit collection name.</Description>
		</Option>
		<Option id="showLabel" name="Show label" arity="ONE">
			<Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="counterSelector" name="Counter selector" arity="ZERO_OR_ONE">
			<Description>Counter div selector (displayed in result list or layout header).</Description>
		</Option>
	</OptionsGroup>

	<DefaultValues displayName="Normal" previewUrl="">
		<DefaultValue name="label">Favorite</DefaultValue>
		<DefaultValue name="showLabel">false</DefaultValue>
		<DefaultValue name="hitID">${entry.metas["id"]}</DefaultValue>
		<DefaultValue name="collection">favoriteHits</DefaultValue>
		<DefaultValue name="counterSelector">.preferred-hits</DefaultValue>
	</DefaultValues>
</Widget>
