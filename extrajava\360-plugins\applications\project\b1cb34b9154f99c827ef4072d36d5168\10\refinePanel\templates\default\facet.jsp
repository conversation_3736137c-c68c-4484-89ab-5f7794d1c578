<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import parameters="facet,uCssId,facetConfig,collapseValue,accessFeeds,isSuggestSearch" />

<h3 name="${search:cleanFacetId(facet)}" class="title-container sub-header-collapsible">
	<span class="facet-name" id="${facet.id}"><search:getFacetLabel facet="${facet}" /></span>
	<span class="collapsible-buttons">
		<span class="icon-collapse-button
			 ${collapseValue == 'hidden'? 'icon-collapsible-button' : 'icon-collapsed-button'} fonticon fonticon-chevron-down
			 ${collapseValue == 'hidden'? '' : 'hidden'}" title="Expand">
        </span>

        <span class="icon-collapse-button
            ${collapseValue == 'hidden'? 'icon-collapsed-button' : 'icon-collapsible-button'} fonticon fonticon-chevron-up
            ${collapseValue == 'hidden'? 'hidden' : ''}" title="Collapse">
        </span>
	</span>
    <c:if test="${not empty facetConfig}">
        <c:if test="${not isSuggestSearch}">
            <span class="toolbox ${collapseValue}" title="Options">
                <span class="menu-icon fonticon fonticon-menu-dot">
                    <span class="view-menu hidden">
                        <c:if test="${facetConfig.enableListView == 'true'}">
                            <span class="fonticon-list" id="list" title="Tags View"></span>
                        </c:if>
                        <c:if test="${facetConfig.enableColumnView == 'true'}">
                            <span class="fonticon-chart-bar" id="column" title="Column View"></span>
                        </c:if>
                        <c:if test="${facetConfig.enablePieView == 'true'}">
                            <span class="fonticon-chart-pie" id="pie" title="Pie View"></span>
                        </c:if>
                    </span>
                    <c:if test="${facetConfig.enableSort == 'true'}">
                        <string:eval var="reverse" string="\${feed.facets['${facet.id}'].infos['reverse']}"  feeds="${accessFeeds}"/>
                        <string:eval var="sort" string="\${feed.facets['${facet.id}'].infos['sort']}"  feeds="${accessFeeds}"/>
                        <span class="sort-menu hidden">
                            <c:choose>
                                <c:when test = "${reverse == 'true'}">
                                    <c:choose>
                                        <c:when test = "${sort == 'count'}">
                                            <span class="alpha fonticon-sort-alpha-asc" id="ALPHANUM_ASC" title="Alphabetical Order ASC"></span>
                                            <span class="alpha fonticon-sort-alpha-desc hidden" id="ALPHANUM_DESC" title="Alphabetical Order DESC"></span>
                                            <span class="num fonticon-sort-num-desc hidden" id="COUNT_DESC" title="Occurence Order DESC"></span>
                                            <span class="num fonticon-sort-num-asc selected" id="COUNT_ASC" title="Occurence Order ASC"></span>
                                        </c:when>
                                        <c:when test = "${sort == 'alphanum'}">
                                            <span class="alpha fonticon-sort-alpha-asc hidden" id="ALPHANUM_ASC" title="Alphabetical Order ASC"></span>
                                            <span class="alpha fonticon-sort-alpha-desc selected" id="ALPHANUM_DESC" title="Alphabetical Order DESC"></span>
                                            <span class="num fonticon-sort-num-asc" id="COUNT_ASC" title="Occurence Order ASC"></span>
                                            <span class="num fonticon-sort-num-desc hidden" id="COUNT_DESC" title="Occurence Order DESC"></span>
                                        </c:when>
                                    </c:choose>
                                </c:when>
                                <c:otherwise>
                                    <c:choose>
                                        <c:when test = "${sort == 'count'}">
                                            <span class="alpha fonticon-sort-alpha-asc" id="ALPHANUM_ASC" title="Alphabetical Order ASC"></span>
                                            <span class="alpha fonticon-sort-alpha-desc hidden" id="ALPHANUM_DESC" title="Alphabetical Order DESC"></span>
                                            <span class="num fonticon-sort-num-asc hidden" id="COUNT_ASC" title="Occurence Order ASC"></span>
                                            <span class="num fonticon-sort-num-desc selected" id="COUNT_DESC" title="Occurence Order DESC"></span>
                                        </c:when>
                                        <c:when test = "${sort == 'alphanum'}">
                                            <span class="alpha fonticon-sort-alpha-asc selected" id="ALPHANUM_ASC" title="Alphabetical Order ASC"></span>
                                            <span class="alpha fonticon-sort-alpha-desc hidden" id="ALPHANUM_DESC" title="Alphabetical Order DESC"></span>
                                            <span class="num fonticon-sort-num-asc" id="COUNT_ASC" title="Occurence Order ASC"></span>
                                            <span class="num fonticon-sort-num-desc hidden" id="COUNT_DESC" title="Occurence Order DESC"></span>
                                        </c:when>
                                    </c:choose>
                                </c:otherwise>
                            </c:choose>
                            <c:if test="${sort != 'count' && sort != 'alphanum'}">
                                <span class="alpha fonticon-sort-alpha-asc" id="ALPHANUM_ASC" title="Alphabetical Order ASC"></span>
                                <span class="alpha fonticon-sort-alpha-desc hidden" id="ALPHANUM_DESC" title="Alphabetical Order DESC"></span>
                                <span class="num fonticon-sort-num-asc" id="COUNT_ASC" title="Occurence Order ASC"></span>
                                <span class="num fonticon-sort-num-desc hidden" id="COUNT_DESC" title="Occurence Order DESC"></span>
                            </c:if>
                            <span class="fonticon-reset" id="DEFAULT_SORT" title="Default Sort"></span>
                        </span>
                    </c:if>
                </span>
            </span>
        </c:if>
    </c:if>
</h3>