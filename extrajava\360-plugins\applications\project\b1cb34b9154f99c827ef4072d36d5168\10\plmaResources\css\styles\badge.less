@import "colorPreferences.less";

/* BADGE ==================================== */

.badge {
    border-radius: 2px;
    display: inline-block;
    vertical-align: baseline;
    margin: 1px;
    cursor: default;
    max-width: calc(100% - 2px);
    border: 1px solid;
}

.badge .badge-content {
    vertical-align: middle;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    word-wrap: break-word;
    white-space: nowrap;
    line-height: 1em;
    padding: 0.1em 0.36em;
}

.badge .fonticon-cancel:last-child {
    display: none;
    border-left: 1px solid @grey-3;
    color: @grey-5;
    margin: 0;
    float: right;
    background-color: white;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    line-height: 1em;
    padding: 0.2em 0.36em;
    width: 25px;
}

.badge:not(.disabled) .fonticon-cancel:last-child {
    cursor: pointer;
}

.badge.badge-closable .fonticon-cancel:last-child {
    display: block;
}

.badge.badge-closable .badge-content {
    max-width: calc(100% - 25px);
}

.badge .badge-icon {
    vertical-align: middle;
    margin: 0 0 0 7px;
}

.badge.badge-closable:not(.disabled) .fonticon-cancel:last-child:hover {
    color: @grey-7;
}

/* Selectable ---------------------------- */
.badge.badge-selectable:not(.badge-selected):not(.disabled):hover {
    border-color: @grey-4;
}

/* Variations ---------------------------- */
.badge.badge-default {
    color: @grey-6;
    border-color: @grey-3;
    background: @grey-1;
}

.badge.badge-default.badge-selected {
    border-color: @grey-6;
    background-color: @grey-3;
}

.badge-primary {
    color: white;
    background-color: @blue-2;
    border-color: @blue-2;
}

.badge.badge-primary.badge-selectable:not(.badge-selected):not(.disabled):hover {
    border-color: @grey-5;
}

.badge.badge-primary.badge-selected {
    border-color: @grey-6;
}

.badge-primary.badge-closable .fonticon-cancel:last-child {
    background-color: @blue-2;
    color: white;
}

.badge-info {
    color: white;
    background-color: @cyan-1;
    border-color: @cyan-1;
}

.badge.badge-info.badge-selectable:not(.badge-selected):not(.disabled):hover {
    border-color: @grey-5;
}

.badge.badge-info.badge-selected {
    border-color: @grey-6;
}

.badge-info.badge-closable .fonticon-cancel:last-child {
    background-color: @cyan-1;
    color: white;
}

.badge-success {
    color: white;
    background-color: @green-1;
    border-color: @green-1;
}

.badge.badge-success.badge-selectable:not(.badge-selected):not(.disabled):hover {
    border-color: @grey-5;
}

.badge.badge-success.badge-selected {
    border-color: @grey-6;
}

.badge-success.badge-closable .fonticon-cancel:last-child {
    background-color: @green-1;
    color: white;
}

.badge-warning {
    color: white;
    background-color: @orange-1;
    border-color: @orange-1;
}

.badge.badge-warning.badge-selectable:not(.badge-selected):not(.disabled):hover {
    border-color: @grey-5;
}

.badge.badge-warning.badge-selected {
    border-color: @grey-6;
}

.badge-warning.badge-closable .fonticon-cancel:last-child {
    background-color: @orange-1;
    color: white;
}

.badge-error {
    color: white;
    background-color: @red-1;
    border-color: @red-1;
}

.badge.badge-error.badge-selectable:not(.badge-selected):not(.disabled):hover {
    border-color: @grey-5;
}

.badge.badge-error.badge-selected {
    border-color: @grey-6;
}

.badge-error.badge-closable .fonticon-cancel:last-child {
    background-color: @red-1;
    color: white;
}

.badge-rounded {
    padding-right: 0.4em;
    padding-left: 0.4em;
    border-radius: 15em;
}

/* Disabled ---------------------------- */
.badge.badge-default.disabled,
.badge.badge-primary.disabled,
.badge.badge-success.disabled,
.badge.badge-info.disabled,
.badge.badge-warning.disabled,
.badge.badge-error.disabled {
    color: @grey-4;
    border-color: @grey-3;
    background: @grey-1;
}

.badge.badge-default.badge-closable.disabled .fonticon-cancel:last-child,
.badge.badge-primary.badge-closable.disabled .fonticon-cancel:last-child,
.badge.badge-success.badge-closable.disabled .fonticon-cancel:last-child,
.badge.badge-info.badge-closable.disabled .fonticon-cancel:last-child,
.badge.badge-warning.badge-closable.disabled .fonticon-cancel:last-child,
.badge.badge-error.badge-closable.disabled .fonticon-cancel:last-child {
    color: @grey-4;
    background: @grey-1;
}

.badge-rounded.badge-count {
    margin-left: -10px;
    margin-right: 7px;
    font-size: 11px;
    background: @orange-1;
    border-color: @orange-1;
    border: 1px solid #fff;
}