/**
 * vis.js
 * https://github.com/almende/vis
 *
 * A dynamic, browser-based visualization library.
 *
 * @version 4.16.1
 * @date    2016-10-06
 *
 * @license
 * Copyright (C) 2011-2016 Almende B.V, http://almende.com
 *
 * Vis.js is dual licensed under both
 *
 * * The Apache 2.0 License
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * and
 *
 * * The MIT License
 *   http://opensource.org/licenses/MIT
 *
 * Vis.js may be distributed under either license.
 */
"use strict";!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.vis=t():e.vis=t()}(this,function(){return function(e){function t(o){if(i[o])return i[o].exports;var n=i[o]={exports:{},id:o,loaded:!1};return e[o].call(n.exports,n,n.exports,t),n.loaded=!0,n.exports}var i={};return t.m=e,t.c=i,t.p="",t(0)}([function(e,t,i){t.util=i(1),t.DOMutil=i(7),t.DataSet=i(8),t.DataView=i(10),t.Queue=i(9),t.Network=i(11),t.network={Images:i(12),dotparser:i(77),gephiParser:i(78),allOptions:i(72)},t.network.convertDot=function(e){return t.network.dotparser.DOTToGraph(e)},t.network.convertGephi=function(e,i){return t.network.gephiParser.parseGephi(e,i)},t.moment=i(2),t.Hammer=i(58),t.keycharm=i(65)},function(e,t,i){var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},n=i(2),s=i(6);t.isNumber=function(e){return e instanceof Number||"number"==typeof e},t.recursiveDOMDelete=function(e){if(e)for(;e.hasChildNodes()===!0;)t.recursiveDOMDelete(e.firstChild),e.removeChild(e.firstChild)},t.giveRange=function(e,t,i,o){if(t==e)return.5;var n=1/(t-e);return Math.max(0,(o-e)*n)},t.isString=function(e){return e instanceof String||"string"==typeof e},t.isDate=function(e){if(e instanceof Date)return!0;if(t.isString(e)){var i=r.exec(e);if(i)return!0;if(!isNaN(Date.parse(e)))return!0}return!1},t.randomUUID=function(){return s.v4()},t.assignAllKeys=function(e,t){for(var i in e)e.hasOwnProperty(i)&&"object"!==o(e[i])&&(e[i]=t)},t.fillIfDefined=function(e,i){var n=!(arguments.length<=2||void 0===arguments[2])&&arguments[2];for(var s in e)void 0!==i[s]&&("object"!==o(i[s])?void 0!==i[s]&&null!==i[s]||void 0===e[s]||n!==!0?e[s]=i[s]:delete e[s]:"object"===o(e[s])&&t.fillIfDefined(e[s],i[s],n))},t.protoExtend=function(e,t){for(var i=1;i<arguments.length;i++){var o=arguments[i];for(var n in o)e[n]=o[n]}return e},t.extend=function(e,t){for(var i=1;i<arguments.length;i++){var o=arguments[i];for(var n in o)o.hasOwnProperty(n)&&(e[n]=o[n])}return e},t.selectiveExtend=function(e,t,i){if(!Array.isArray(e))throw new Error("Array with property names expected as first argument");for(var o=2;o<arguments.length;o++)for(var n=arguments[o],s=0;s<e.length;s++){var r=e[s];n.hasOwnProperty(r)&&(t[r]=n[r])}return t},t.selectiveDeepExtend=function(e,i,o){var n=!(arguments.length<=3||void 0===arguments[3])&&arguments[3];if(Array.isArray(o))throw new TypeError("Arrays are not supported by deepExtend");for(var s=2;s<arguments.length;s++)for(var r=arguments[s],a=0;a<e.length;a++){var h=e[a];if(r.hasOwnProperty(h))if(o[h]&&o[h].constructor===Object)void 0===i[h]&&(i[h]={}),i[h].constructor===Object?t.deepExtend(i[h],o[h],!1,n):null===o[h]&&void 0!==i[h]&&n===!0?delete i[h]:i[h]=o[h];else{if(Array.isArray(o[h]))throw new TypeError("Arrays are not supported by deepExtend");null===o[h]&&void 0!==i[h]&&n===!0?delete i[h]:i[h]=o[h]}}return i},t.selectiveNotDeepExtend=function(e,i,o){var n=!(arguments.length<=3||void 0===arguments[3])&&arguments[3];if(Array.isArray(o))throw new TypeError("Arrays are not supported by deepExtend");for(var s in o)if(o.hasOwnProperty(s)&&e.indexOf(s)==-1)if(o[s]&&o[s].constructor===Object)void 0===i[s]&&(i[s]={}),i[s].constructor===Object?t.deepExtend(i[s],o[s]):null===o[s]&&void 0!==i[s]&&n===!0?delete i[s]:i[s]=o[s];else if(Array.isArray(o[s])){i[s]=[];for(var r=0;r<o[s].length;r++)i[s].push(o[s][r])}else null===o[s]&&void 0!==i[s]&&n===!0?delete i[s]:i[s]=o[s];return i},t.deepExtend=function(e,i,o,n){for(var s in i)if(i.hasOwnProperty(s)||o===!0)if(i[s]&&i[s].constructor===Object)void 0===e[s]&&(e[s]={}),e[s].constructor===Object?t.deepExtend(e[s],i[s],o):null===i[s]&&void 0!==e[s]&&n===!0?delete e[s]:e[s]=i[s];else if(Array.isArray(i[s])){e[s]=[];for(var r=0;r<i[s].length;r++)e[s].push(i[s][r])}else null===i[s]&&void 0!==e[s]&&n===!0?delete e[s]:e[s]=i[s];return e},t.equalArray=function(e,t){if(e.length!=t.length)return!1;for(var i=0,o=e.length;i<o;i++)if(e[i]!=t[i])return!1;return!0},t.convert=function(e,i){var o;if(void 0!==e){if(null===e)return null;if(!i)return e;if("string"!=typeof i&&!(i instanceof String))throw new Error("Type must be a string");switch(i){case"boolean":case"Boolean":return Boolean(e);case"number":case"Number":return Number(e.valueOf());case"string":case"String":return String(e);case"Date":if(t.isNumber(e))return new Date(e);if(e instanceof Date)return new Date(e.valueOf());if(n.isMoment(e))return new Date(e.valueOf());if(t.isString(e))return o=r.exec(e),o?new Date(Number(o[1])):n(e).toDate();throw new Error("Cannot convert object of type "+t.getType(e)+" to type Date");case"Moment":if(t.isNumber(e))return n(e);if(e instanceof Date)return n(e.valueOf());if(n.isMoment(e))return n(e);if(t.isString(e))return o=r.exec(e),n(o?Number(o[1]):e);throw new Error("Cannot convert object of type "+t.getType(e)+" to type Date");case"ISODate":if(t.isNumber(e))return new Date(e);if(e instanceof Date)return e.toISOString();if(n.isMoment(e))return e.toDate().toISOString();if(t.isString(e))return o=r.exec(e),o?new Date(Number(o[1])).toISOString():new Date(e).toISOString();throw new Error("Cannot convert object of type "+t.getType(e)+" to type ISODate");case"ASPDate":if(t.isNumber(e))return"/Date("+e+")/";if(e instanceof Date)return"/Date("+e.valueOf()+")/";if(t.isString(e)){o=r.exec(e);var s;return s=o?new Date(Number(o[1])).valueOf():new Date(e).valueOf(),"/Date("+s+")/"}throw new Error("Cannot convert object of type "+t.getType(e)+" to type ASPDate");default:throw new Error('Unknown type "'+i+'"')}}};var r=/^\/?Date\((\-?\d+)/i;t.getType=function(e){var t="undefined"==typeof e?"undefined":o(e);return"object"==t?null===e?"null":e instanceof Boolean?"Boolean":e instanceof Number?"Number":e instanceof String?"String":Array.isArray(e)?"Array":e instanceof Date?"Date":"Object":"number"==t?"Number":"boolean"==t?"Boolean":"string"==t?"String":void 0===t?"undefined":t},t.copyAndExtendArray=function(e,t){for(var i=[],o=0;o<e.length;o++)i.push(e[o]);return i.push(t),i},t.copyArray=function(e){for(var t=[],i=0;i<e.length;i++)t.push(e[i]);return t},t.getAbsoluteLeft=function(e){return e.getBoundingClientRect().left},t.getAbsoluteRight=function(e){return e.getBoundingClientRect().right},t.getAbsoluteTop=function(e){return e.getBoundingClientRect().top},t.addClassName=function(e,t){var i=e.className.split(" ");i.indexOf(t)==-1&&(i.push(t),e.className=i.join(" "))},t.removeClassName=function(e,t){var i=e.className.split(" "),o=i.indexOf(t);o!=-1&&(i.splice(o,1),e.className=i.join(" "))},t.forEach=function(e,t){var i,o;if(Array.isArray(e))for(i=0,o=e.length;i<o;i++)t(e[i],i,e);else for(i in e)e.hasOwnProperty(i)&&t(e[i],i,e)},t.toArray=function(e){var t=[];for(var i in e)e.hasOwnProperty(i)&&t.push(e[i]);return t},t.updateProperty=function(e,t,i){return e[t]!==i&&(e[t]=i,!0)},t.throttle=function(e,t){var i=null,o=!1;return function n(){i?o=!0:(o=!1,e(),i=setTimeout(function(){i=null,o&&n()},t))}},t.addEventListener=function(e,t,i,o){e.addEventListener?(void 0===o&&(o=!1),"mousewheel"===t&&navigator.userAgent.indexOf("Firefox")>=0&&(t="DOMMouseScroll"),e.addEventListener(t,i,o)):e.attachEvent("on"+t,i)},t.removeEventListener=function(e,t,i,o){e.removeEventListener?(void 0===o&&(o=!1),"mousewheel"===t&&navigator.userAgent.indexOf("Firefox")>=0&&(t="DOMMouseScroll"),e.removeEventListener(t,i,o)):e.detachEvent("on"+t,i)},t.preventDefault=function(e){e||(e=window.event),e.preventDefault?e.preventDefault():e.returnValue=!1},t.getTarget=function(e){e||(e=window.event);var t;return e.target?t=e.target:e.srcElement&&(t=e.srcElement),void 0!=t.nodeType&&3==t.nodeType&&(t=t.parentNode),t},t.hasParent=function(e,t){for(var i=e;i;){if(i===t)return!0;i=i.parentNode}return!1},t.option={},t.option.asBoolean=function(e,t){return"function"==typeof e&&(e=e()),null!=e?0!=e:t||null},t.option.asNumber=function(e,t){return"function"==typeof e&&(e=e()),null!=e?Number(e)||t||null:t||null},t.option.asString=function(e,t){return"function"==typeof e&&(e=e()),null!=e?String(e):t||null},t.option.asSize=function(e,i){return"function"==typeof e&&(e=e()),t.isString(e)?e:t.isNumber(e)?e+"px":i||null},t.option.asElement=function(e,t){return"function"==typeof e&&(e=e()),e||t||null},t.hexToRGB=function(e){var t=/^#?([a-f\d])([a-f\d])([a-f\d])$/i;e=e.replace(t,function(e,t,i,o){return t+t+i+i+o+o});var i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return i?{r:parseInt(i[1],16),g:parseInt(i[2],16),b:parseInt(i[3],16)}:null},t.overrideOpacity=function(e,i){if(e.indexOf("rgba")!=-1)return e;if(e.indexOf("rgb")!=-1){var o=e.substr(e.indexOf("(")+1).replace(")","").split(",");return"rgba("+o[0]+","+o[1]+","+o[2]+","+i+")"}var o=t.hexToRGB(e);return null==o?e:"rgba("+o.r+","+o.g+","+o.b+","+i+")"},t.RGBToHex=function(e,t,i){return"#"+((1<<24)+(e<<16)+(t<<8)+i).toString(16).slice(1)},t.parseColor=function(e){var i;if(t.isString(e)===!0){if(t.isValidRGB(e)===!0){var o=e.substr(4).substr(0,e.length-5).split(",").map(function(e){return parseInt(e)});e=t.RGBToHex(o[0],o[1],o[2])}if(t.isValidHex(e)===!0){var n=t.hexToHSV(e),s={h:n.h,s:.8*n.s,v:Math.min(1,1.02*n.v)},r={h:n.h,s:Math.min(1,1.25*n.s),v:.8*n.v},a=t.HSVToHex(r.h,r.s,r.v),h=t.HSVToHex(s.h,s.s,s.v);i={background:e,border:a,highlight:{background:h,border:a},hover:{background:h,border:a}}}else i={background:e,border:e,highlight:{background:e,border:e},hover:{background:e,border:e}}}else i={},i.background=e.background||void 0,i.border=e.border||void 0,t.isString(e.highlight)?i.highlight={border:e.highlight,background:e.highlight}:(i.highlight={},i.highlight.background=e.highlight&&e.highlight.background||void 0,i.highlight.border=e.highlight&&e.highlight.border||void 0),t.isString(e.hover)?i.hover={border:e.hover,background:e.hover}:(i.hover={},i.hover.background=e.hover&&e.hover.background||void 0,i.hover.border=e.hover&&e.hover.border||void 0);return i},t.RGBToHSV=function(e,t,i){e/=255,t/=255,i/=255;var o=Math.min(e,Math.min(t,i)),n=Math.max(e,Math.max(t,i));if(o==n)return{h:0,s:0,v:o};var s=e==o?t-i:i==o?e-t:i-e,r=e==o?3:i==o?1:5,a=60*(r-s/(n-o))/360,h=(n-o)/n,d=n;return{h:a,s:h,v:d}};var a={split:function(e){var t={};return e.split(";").forEach(function(e){if(""!=e.trim()){var i=e.split(":"),o=i[0].trim(),n=i[1].trim();t[o]=n}}),t},join:function(e){return Object.keys(e).map(function(t){return t+": "+e[t]}).join("; ")}};t.addCssText=function(e,i){var o=a.split(e.style.cssText),n=a.split(i),s=t.extend(o,n);e.style.cssText=a.join(s)},t.removeCssText=function(e,t){var i=a.split(e.style.cssText),o=a.split(t);for(var n in o)o.hasOwnProperty(n)&&delete i[n];e.style.cssText=a.join(i)},t.HSVToRGB=function(e,t,i){var o,n,s,r=Math.floor(6*e),a=6*e-r,h=i*(1-t),d=i*(1-a*t),l=i*(1-(1-a)*t);switch(r%6){case 0:o=i,n=l,s=h;break;case 1:o=d,n=i,s=h;break;case 2:o=h,n=i,s=l;break;case 3:o=h,n=d,s=i;break;case 4:o=l,n=h,s=i;break;case 5:o=i,n=h,s=d}return{r:Math.floor(255*o),g:Math.floor(255*n),b:Math.floor(255*s)}},t.HSVToHex=function(e,i,o){var n=t.HSVToRGB(e,i,o);return t.RGBToHex(n.r,n.g,n.b)},t.hexToHSV=function(e){var i=t.hexToRGB(e);return t.RGBToHSV(i.r,i.g,i.b)},t.isValidHex=function(e){var t=/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(e);return t},t.isValidRGB=function(e){e=e.replace(" ","");var t=/rgb\((\d{1,3}),(\d{1,3}),(\d{1,3})\)/i.test(e);return t},t.isValidRGBA=function(e){e=e.replace(" ","");var t=/rgba\((\d{1,3}),(\d{1,3}),(\d{1,3}),(.{1,3})\)/i.test(e);return t},t.selectiveBridgeObject=function(e,i){if("object"==("undefined"==typeof i?"undefined":o(i))){for(var n=Object.create(i),s=0;s<e.length;s++)i.hasOwnProperty(e[s])&&"object"==o(i[e[s]])&&(n[e[s]]=t.bridgeObject(i[e[s]]));return n}return null},t.bridgeObject=function(e){if("object"==("undefined"==typeof e?"undefined":o(e))){var i=Object.create(e);for(var n in e)e.hasOwnProperty(n)&&"object"==o(e[n])&&(i[n]=t.bridgeObject(e[n]));return i}return null},t.insertSort=function(e,t){for(var i=0;i<e.length;i++){for(var o=e[i],n=i;n>0&&t(o,e[n-1])<0;n--)e[n]=e[n-1];e[n]=o}return e},t.mergeOptions=function(e,t,i){var o=(!(arguments.length<=3||void 0===arguments[3])&&arguments[3],arguments.length<=4||void 0===arguments[4]?{}:arguments[4]);if(null===t[i])e[i]=Object.create(o[i]);else if(void 0!==t[i])if("boolean"==typeof t[i])e[i].enabled=t[i];else{void 0===t[i].enabled&&(e[i].enabled=!0);for(var n in t[i])t[i].hasOwnProperty(n)&&(e[i][n]=t[i][n])}},t.binarySearchCustom=function(e,t,i,o){for(var n=1e4,s=0,r=0,a=e.length-1;r<=a&&s<n;){var h=Math.floor((r+a)/2),d=e[h],l=void 0===o?d[i]:d[i][o],c=t(l);if(0==c)return h;c==-1?r=h+1:a=h-1,s++}return-1},t.binarySearchValue=function(e,t,i,o,n){for(var s,r,a,h,d=1e4,l=0,c=0,u=e.length-1,n=void 0!=n?n:function(e,t){return e==t?0:e<t?-1:1};c<=u&&l<d;){if(h=Math.floor(.5*(u+c)),s=e[Math.max(0,h-1)][i],r=e[h][i],a=e[Math.min(e.length-1,h+1)][i],0==n(r,t))return h;if(n(s,t)<0&&n(r,t)>0)return"before"==o?Math.max(0,h-1):h;if(n(r,t)<0&&n(a,t)>0)return"before"==o?h:Math.min(e.length-1,h+1);n(r,t)<0?c=h+1:u=h-1,l++}return-1},t.easingFunctions={linear:function(e){return e},easeInQuad:function(e){return e*e},easeOutQuad:function(e){return e*(2-e)},easeInOutQuad:function(e){return e<.5?2*e*e:-1+(4-2*e)*e},easeInCubic:function(e){return e*e*e},easeOutCubic:function(e){return--e*e*e+1},easeInOutCubic:function(e){return e<.5?4*e*e*e:(e-1)*(2*e-2)*(2*e-2)+1},easeInQuart:function(e){return e*e*e*e},easeOutQuart:function(e){return 1- --e*e*e*e},easeInOutQuart:function(e){return e<.5?8*e*e*e*e:1-8*--e*e*e*e},easeInQuint:function(e){return e*e*e*e*e},easeOutQuint:function(e){return 1+--e*e*e*e*e},easeInOutQuint:function(e){return e<.5?16*e*e*e*e*e:1+16*--e*e*e*e*e}}},function(e,t,i){e.exports="undefined"!=typeof window&&window.moment||i(3)},function(e,t,i){(function(e){!function(t,i){e.exports=i()}(this,function(){function t(){return po.apply(null,arguments)}function i(e){po=e}function o(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function n(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function s(e){var t;for(t in e)return!1;return!0}function r(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function a(e,t){var i,o=[];for(i=0;i<e.length;++i)o.push(t(e[i],i));return o}function h(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function d(e,t){for(var i in t)h(t,i)&&(e[i]=t[i]);return h(t,"toString")&&(e.toString=t.toString),h(t,"valueOf")&&(e.valueOf=t.valueOf),e}function l(e,t,i,o){return gt(e,t,i,o,!0).utc()}function c(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],meridiem:null}}function u(e){return null==e._pf&&(e._pf=c()),e._pf}function f(e){if(null==e._isValid){var t=u(e),i=vo.call(t.parsedDateParts,function(e){return null!=e}),o=!isNaN(e._d.getTime())&&t.overflow<0&&!t.empty&&!t.invalidMonth&&!t.invalidWeekday&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&i);if(e._strict&&(o=o&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e))return o;e._isValid=o}return e._isValid}function p(e){var t=l(NaN);return null!=e?d(u(t),e):u(t).userInvalidated=!0,t}function v(e){return void 0===e}function y(e,t){var i,o,n;if(v(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),v(t._i)||(e._i=t._i),v(t._f)||(e._f=t._f),v(t._l)||(e._l=t._l),v(t._strict)||(e._strict=t._strict),v(t._tzm)||(e._tzm=t._tzm),v(t._isUTC)||(e._isUTC=t._isUTC),v(t._offset)||(e._offset=t._offset),v(t._pf)||(e._pf=u(t)),v(t._locale)||(e._locale=t._locale),yo.length>0)for(i in yo)o=yo[i],n=t[o],v(n)||(e[o]=n);return e}function g(e){y(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),go===!1&&(go=!0,t.updateOffset(this),go=!1)}function b(e){return e instanceof g||null!=e&&null!=e._isAMomentObject}function m(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function _(e){var t=+e,i=0;return 0!==t&&isFinite(t)&&(i=m(t)),i}function w(e,t,i){var o,n=Math.min(e.length,t.length),s=Math.abs(e.length-t.length),r=0;for(o=0;o<n;o++)(i&&e[o]!==t[o]||!i&&_(e[o])!==_(t[o]))&&r++;return r+s}function k(e){t.suppressDeprecationWarnings===!1&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function x(e,i){var o=!0;return d(function(){if(null!=t.deprecationHandler&&t.deprecationHandler(null,e),o){for(var n,s=[],r=0;r<arguments.length;r++){if(n="","object"==typeof arguments[r]){n+="\n["+r+"] ";for(var a in arguments[0])n+=a+": "+arguments[0][a]+", ";n=n.slice(0,-2)}else n=arguments[r];s.push(n)}k(e+"\nArguments: "+Array.prototype.slice.call(s).join("")+"\n"+(new Error).stack),o=!1}return i.apply(this,arguments)},i)}function O(e,i){null!=t.deprecationHandler&&t.deprecationHandler(e,i),bo[e]||(k(i),bo[e]=!0)}function E(e){return e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function M(e){var t,i;for(i in e)t=e[i],E(t)?this[i]=t:this["_"+i]=t;this._config=e,this._ordinalParseLenient=new RegExp(this._ordinalParse.source+"|"+/\d{1,2}/.source)}function S(e,t){var i,o=d({},e);for(i in t)h(t,i)&&(n(e[i])&&n(t[i])?(o[i]={},d(o[i],e[i]),d(o[i],t[i])):null!=t[i]?o[i]=t[i]:delete o[i]);for(i in e)h(e,i)&&!h(t,i)&&n(e[i])&&(o[i]=d({},o[i]));return o}function D(e){null!=e&&this.set(e)}function C(e,t,i){var o=this._calendar[e]||this._calendar.sameElse;return E(o)?o.call(t,i):o}function T(e){var t=this._longDateFormat[e],i=this._longDateFormat[e.toUpperCase()];return t||!i?t:(this._longDateFormat[e]=i.replace(/MMMM|MM|DD|dddd/g,function(e){return e.slice(1)}),this._longDateFormat[e])}function P(){return this._invalidDate}function F(e){return this._ordinal.replace("%d",e)}function B(e,t,i,o){var n=this._relativeTime[i];return E(n)?n(e,t,i,o):n.replace(/%d/i,e)}function I(e,t){var i=this._relativeTime[e>0?"future":"past"];return E(i)?i(t):i.replace(/%s/i,t)}function j(e,t){var i=e.toLowerCase();So[i]=So[i+"s"]=So[t]=e}function N(e){return"string"==typeof e?So[e]||So[e.toLowerCase()]:void 0}function z(e){var t,i,o={};for(i in e)h(e,i)&&(t=N(i),t&&(o[t]=e[i]));return o}function R(e,t){Do[e]=t}function A(e){var t=[];for(var i in e)t.push({unit:i,priority:Do[i]});return t.sort(function(e,t){return e.priority-t.priority}),t}function L(e,i){return function(o){return null!=o?(W(this,e,o),t.updateOffset(this,i),this):H(this,e)}}function H(e,t){return e.isValid()?e._d["get"+(e._isUTC?"UTC":"")+t]():NaN}function W(e,t,i){e.isValid()&&e._d["set"+(e._isUTC?"UTC":"")+t](i)}function Y(e){return e=N(e),E(this[e])?this[e]():this}function U(e,t){if("object"==typeof e){e=z(e);for(var i=A(e),o=0;o<i.length;o++)this[i[o].unit](e[i[o].unit])}else if(e=N(e),E(this[e]))return this[e](t);return this}function V(e,t,i){var o=""+Math.abs(e),n=t-o.length,s=e>=0;return(s?i?"+":"":"-")+Math.pow(10,Math.max(0,n)).toString().substr(1)+o}function q(e,t,i,o){var n=o;"string"==typeof o&&(n=function(){return this[o]()}),e&&(Fo[e]=n),t&&(Fo[t[0]]=function(){return V(n.apply(this,arguments),t[1],t[2])}),i&&(Fo[i]=function(){return this.localeData().ordinal(n.apply(this,arguments),e)})}function G(e){return e.match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"")}function X(e){var t,i,o=e.match(Co);for(t=0,i=o.length;t<i;t++)Fo[o[t]]?o[t]=Fo[o[t]]:o[t]=G(o[t]);return function(t){var n,s="";for(n=0;n<i;n++)s+=o[n]instanceof Function?o[n].call(t,e):o[n];return s}}function K(e,t){return e.isValid()?(t=Z(t,e.localeData()),Po[t]=Po[t]||X(t),Po[t](e)):e.localeData().invalidDate()}function Z(e,t){function i(e){return t.longDateFormat(e)||e}var o=5;for(To.lastIndex=0;o>=0&&To.test(e);)e=e.replace(To,i),To.lastIndex=0,o-=1;return e}function Q(e,t,i){Zo[e]=E(t)?t:function(e,o){return e&&i?i:t}}function J(e,t){return h(Zo,e)?Zo[e](t._strict,t._locale):new RegExp($(e))}function $(e){return ee(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,i,o,n){return t||i||o||n}))}function ee(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function te(e,t){var i,o=t;for("string"==typeof e&&(e=[e]),"number"==typeof t&&(o=function(e,i){i[t]=_(e)}),i=0;i<e.length;i++)Qo[e[i]]=o}function ie(e,t){te(e,function(e,i,o,n){o._w=o._w||{},t(e,o._w,o,n)})}function oe(e,t,i){null!=t&&h(Qo,e)&&Qo[e](t,i._a,i,e)}function ne(e,t){return new Date(Date.UTC(e,t+1,0)).getUTCDate()}function se(e,t){return e?o(this._months)?this._months[e.month()]:this._months[(this._months.isFormat||hn).test(t)?"format":"standalone"][e.month()]:this._months}function re(e,t){return e?o(this._monthsShort)?this._monthsShort[e.month()]:this._monthsShort[hn.test(t)?"format":"standalone"][e.month()]:this._monthsShort}function ae(e,t,i){var o,n,s,r=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],o=0;o<12;++o)s=l([2e3,o]),this._shortMonthsParse[o]=this.monthsShort(s,"").toLocaleLowerCase(),this._longMonthsParse[o]=this.months(s,"").toLocaleLowerCase();return i?"MMM"===t?(n=_o.call(this._shortMonthsParse,r),n!==-1?n:null):(n=_o.call(this._longMonthsParse,r),n!==-1?n:null):"MMM"===t?(n=_o.call(this._shortMonthsParse,r),n!==-1?n:(n=_o.call(this._longMonthsParse,r),n!==-1?n:null)):(n=_o.call(this._longMonthsParse,r),n!==-1?n:(n=_o.call(this._shortMonthsParse,r),n!==-1?n:null))}function he(e,t,i){var o,n,s;if(this._monthsParseExact)return ae.call(this,e,t,i);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),o=0;o<12;o++){if(n=l([2e3,o]),i&&!this._longMonthsParse[o]&&(this._longMonthsParse[o]=new RegExp("^"+this.months(n,"").replace(".","")+"$","i"),this._shortMonthsParse[o]=new RegExp("^"+this.monthsShort(n,"").replace(".","")+"$","i")),i||this._monthsParse[o]||(s="^"+this.months(n,"")+"|^"+this.monthsShort(n,""),this._monthsParse[o]=new RegExp(s.replace(".",""),"i")),i&&"MMMM"===t&&this._longMonthsParse[o].test(e))return o;if(i&&"MMM"===t&&this._shortMonthsParse[o].test(e))return o;if(!i&&this._monthsParse[o].test(e))return o}}function de(e,t){var i;if(!e.isValid())return e;if("string"==typeof t)if(/^\d+$/.test(t))t=_(t);else if(t=e.localeData().monthsParse(t),"number"!=typeof t)return e;return i=Math.min(e.date(),ne(e.year(),t)),e._d["set"+(e._isUTC?"UTC":"")+"Month"](t,i),e}function le(e){return null!=e?(de(this,e),t.updateOffset(this,!0),this):H(this,"Month")}function ce(){return ne(this.year(),this.month())}function ue(e){return this._monthsParseExact?(h(this,"_monthsRegex")||pe.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(h(this,"_monthsShortRegex")||(this._monthsShortRegex=cn),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)}function fe(e){return this._monthsParseExact?(h(this,"_monthsRegex")||pe.call(this),e?this._monthsStrictRegex:this._monthsRegex):(h(this,"_monthsRegex")||(this._monthsRegex=un),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)}function pe(){function e(e,t){return t.length-e.length}var t,i,o=[],n=[],s=[];for(t=0;t<12;t++)i=l([2e3,t]),o.push(this.monthsShort(i,"")),n.push(this.months(i,"")),s.push(this.months(i,"")),s.push(this.monthsShort(i,""));for(o.sort(e),n.sort(e),s.sort(e),t=0;t<12;t++)o[t]=ee(o[t]),n[t]=ee(n[t]);for(t=0;t<24;t++)s[t]=ee(s[t]);this._monthsRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+n.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+o.join("|")+")","i")}function ve(e){return ye(e)?366:365}function ye(e){return e%4===0&&e%100!==0||e%400===0}function ge(){return ye(this.year())}function be(e,t,i,o,n,s,r){var a=new Date(e,t,i,o,n,s,r);return e<100&&e>=0&&isFinite(a.getFullYear())&&a.setFullYear(e),a}function me(e){var t=new Date(Date.UTC.apply(null,arguments));return e<100&&e>=0&&isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e),t}function _e(e,t,i){var o=7+t-i,n=(7+me(e,0,o).getUTCDay()-t)%7;return-n+o-1}function we(e,t,i,o,n){var s,r,a=(7+i-o)%7,h=_e(e,o,n),d=1+7*(t-1)+a+h;return d<=0?(s=e-1,r=ve(s)+d):d>ve(e)?(s=e+1,r=d-ve(e)):(s=e,r=d),{year:s,dayOfYear:r}}function ke(e,t,i){var o,n,s=_e(e.year(),t,i),r=Math.floor((e.dayOfYear()-s-1)/7)+1;return r<1?(n=e.year()-1,o=r+xe(n,t,i)):r>xe(e.year(),t,i)?(o=r-xe(e.year(),t,i),n=e.year()+1):(n=e.year(),o=r),{week:o,year:n}}function xe(e,t,i){var o=_e(e,t,i),n=_e(e+1,t,i);return(ve(e)-o+n)/7}function Oe(e){return ke(e,this._week.dow,this._week.doy).week}function Ee(){return this._week.dow}function Me(){return this._week.doy}function Se(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")}function De(e){var t=ke(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")}function Ce(e,t){return"string"!=typeof e?e:isNaN(e)?(e=t.weekdaysParse(e),"number"==typeof e?e:null):parseInt(e,10)}function Te(e,t){return"string"==typeof e?t.weekdaysParse(e)%7||7:isNaN(e)?null:e}function Pe(e,t){return e?o(this._weekdays)?this._weekdays[e.day()]:this._weekdays[this._weekdays.isFormat.test(t)?"format":"standalone"][e.day()]:this._weekdays}function Fe(e){return e?this._weekdaysShort[e.day()]:this._weekdaysShort}function Be(e){return e?this._weekdaysMin[e.day()]:this._weekdaysMin}function Ie(e,t,i){var o,n,s,r=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],o=0;o<7;++o)s=l([2e3,1]).day(o),this._minWeekdaysParse[o]=this.weekdaysMin(s,"").toLocaleLowerCase(),this._shortWeekdaysParse[o]=this.weekdaysShort(s,"").toLocaleLowerCase(),this._weekdaysParse[o]=this.weekdays(s,"").toLocaleLowerCase();return i?"dddd"===t?(n=_o.call(this._weekdaysParse,r),n!==-1?n:null):"ddd"===t?(n=_o.call(this._shortWeekdaysParse,r),n!==-1?n:null):(n=_o.call(this._minWeekdaysParse,r),n!==-1?n:null):"dddd"===t?(n=_o.call(this._weekdaysParse,r),n!==-1?n:(n=_o.call(this._shortWeekdaysParse,r),n!==-1?n:(n=_o.call(this._minWeekdaysParse,r),n!==-1?n:null))):"ddd"===t?(n=_o.call(this._shortWeekdaysParse,r),n!==-1?n:(n=_o.call(this._weekdaysParse,r),n!==-1?n:(n=_o.call(this._minWeekdaysParse,r),n!==-1?n:null))):(n=_o.call(this._minWeekdaysParse,r),n!==-1?n:(n=_o.call(this._weekdaysParse,r),n!==-1?n:(n=_o.call(this._shortWeekdaysParse,r),n!==-1?n:null)))}function je(e,t,i){var o,n,s;if(this._weekdaysParseExact)return Ie.call(this,e,t,i);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),o=0;o<7;o++){if(n=l([2e3,1]).day(o),i&&!this._fullWeekdaysParse[o]&&(this._fullWeekdaysParse[o]=new RegExp("^"+this.weekdays(n,"").replace(".",".?")+"$","i"),this._shortWeekdaysParse[o]=new RegExp("^"+this.weekdaysShort(n,"").replace(".",".?")+"$","i"),this._minWeekdaysParse[o]=new RegExp("^"+this.weekdaysMin(n,"").replace(".",".?")+"$","i")),this._weekdaysParse[o]||(s="^"+this.weekdays(n,"")+"|^"+this.weekdaysShort(n,"")+"|^"+this.weekdaysMin(n,""),this._weekdaysParse[o]=new RegExp(s.replace(".",""),"i")),i&&"dddd"===t&&this._fullWeekdaysParse[o].test(e))return o;if(i&&"ddd"===t&&this._shortWeekdaysParse[o].test(e))return o;if(i&&"dd"===t&&this._minWeekdaysParse[o].test(e))return o;if(!i&&this._weekdaysParse[o].test(e))return o}}function Ne(e){if(!this.isValid())return null!=e?this:NaN;var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(e=Ce(e,this.localeData()),this.add(e-t,"d")):t}function ze(e){if(!this.isValid())return null!=e?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")}function Re(e){if(!this.isValid())return null!=e?this:NaN;if(null!=e){var t=Te(e,this.localeData());return this.day(this.day()%7?t:t-7)}return this.day()||7}function Ae(e){return this._weekdaysParseExact?(h(this,"_weekdaysRegex")||We.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(h(this,"_weekdaysRegex")||(this._weekdaysRegex=bn),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)}function Le(e){return this._weekdaysParseExact?(h(this,"_weekdaysRegex")||We.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(h(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=mn),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}function He(e){return this._weekdaysParseExact?(h(this,"_weekdaysRegex")||We.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(h(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=_n),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function We(){function e(e,t){return t.length-e.length}var t,i,o,n,s,r=[],a=[],h=[],d=[];for(t=0;t<7;t++)i=l([2e3,1]).day(t),o=this.weekdaysMin(i,""),n=this.weekdaysShort(i,""),s=this.weekdays(i,""),r.push(o),a.push(n),h.push(s),d.push(o),d.push(n),d.push(s);for(r.sort(e),a.sort(e),h.sort(e),d.sort(e),t=0;t<7;t++)a[t]=ee(a[t]),h[t]=ee(h[t]),d[t]=ee(d[t]);this._weekdaysRegex=new RegExp("^("+d.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+h.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+r.join("|")+")","i")}function Ye(){return this.hours()%12||12}function Ue(){return this.hours()||24}function Ve(e,t){q(e,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}function qe(e,t){return t._meridiemParse}function Ge(e){return"p"===(e+"").toLowerCase().charAt(0)}function Xe(e,t,i){return e>11?i?"pm":"PM":i?"am":"AM"}function Ke(e){return e?e.toLowerCase().replace("_","-"):e}function Ze(e){for(var t,i,o,n,s=0;s<e.length;){for(n=Ke(e[s]).split("-"),t=n.length,i=Ke(e[s+1]),i=i?i.split("-"):null;t>0;){if(o=Qe(n.slice(0,t).join("-")))return o;if(i&&i.length>=t&&w(n,i,!0)>=t-1)break;t--}s++}return null}function Qe(t){var i=null;if(!En[t]&&"undefined"!=typeof e&&e&&e.exports)try{i=wn._abbr,!function(){var e=new Error('Cannot find module "./locale"');throw e.code="MODULE_NOT_FOUND",e}(),Je(i)}catch(o){}return En[t]}function Je(e,t){var i;return e&&(i=v(t)?tt(e):$e(e,t),i&&(wn=i)),wn._abbr}function $e(e,t){if(null!==t){var i=On;return t.abbr=e,null!=En[e]?(O("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),i=En[e]._config):null!=t.parentLocale&&(null!=En[t.parentLocale]?i=En[t.parentLocale]._config:O("parentLocaleUndefined","specified parentLocale is not defined yet. See http://momentjs.com/guides/#/warnings/parent-locale/")),En[e]=new D(S(i,t)),Je(e),En[e]}return delete En[e],null}function et(e,t){if(null!=t){var i,o=On;null!=En[e]&&(o=En[e]._config),t=S(o,t),i=new D(t),i.parentLocale=En[e],En[e]=i,Je(e)}else null!=En[e]&&(null!=En[e].parentLocale?En[e]=En[e].parentLocale:null!=En[e]&&delete En[e]);return En[e]}function tt(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return wn;if(!o(e)){if(t=Qe(e))return t;e=[e]}return Ze(e)}function it(){return mo(En)}function ot(e){var t,i=e._a;return i&&u(e).overflow===-2&&(t=i[$o]<0||i[$o]>11?$o:i[en]<1||i[en]>ne(i[Jo],i[$o])?en:i[tn]<0||i[tn]>24||24===i[tn]&&(0!==i[on]||0!==i[nn]||0!==i[sn])?tn:i[on]<0||i[on]>59?on:i[nn]<0||i[nn]>59?nn:i[sn]<0||i[sn]>999?sn:-1,u(e)._overflowDayOfYear&&(t<Jo||t>en)&&(t=en),u(e)._overflowWeeks&&t===-1&&(t=rn),u(e)._overflowWeekday&&t===-1&&(t=an),u(e).overflow=t),e}function nt(e){var t,i,o,n,s,r,a=e._i,h=Mn.exec(a)||Sn.exec(a);if(h){for(u(e).iso=!0,t=0,i=Cn.length;t<i;t++)if(Cn[t][1].exec(h[1])){n=Cn[t][0],o=Cn[t][2]!==!1;break}if(null==n)return void(e._isValid=!1);if(h[3]){for(t=0,i=Tn.length;t<i;t++)if(Tn[t][1].exec(h[3])){s=(h[2]||" ")+Tn[t][0];break}if(null==s)return void(e._isValid=!1);
}if(!o&&null!=s)return void(e._isValid=!1);if(h[4]){if(!Dn.exec(h[4]))return void(e._isValid=!1);r="Z"}e._f=n+(s||"")+(r||""),lt(e)}else e._isValid=!1}function st(e){var i=Pn.exec(e._i);return null!==i?void(e._d=new Date((+i[1]))):(nt(e),void(e._isValid===!1&&(delete e._isValid,t.createFromInputFallback(e))))}function rt(e,t,i){return null!=e?e:null!=t?t:i}function at(e){var i=new Date(t.now());return e._useUTC?[i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()]:[i.getFullYear(),i.getMonth(),i.getDate()]}function ht(e){var t,i,o,n,s=[];if(!e._d){for(o=at(e),e._w&&null==e._a[en]&&null==e._a[$o]&&dt(e),e._dayOfYear&&(n=rt(e._a[Jo],o[Jo]),e._dayOfYear>ve(n)&&(u(e)._overflowDayOfYear=!0),i=me(n,0,e._dayOfYear),e._a[$o]=i.getUTCMonth(),e._a[en]=i.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=s[t]=o[t];for(;t<7;t++)e._a[t]=s[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[tn]&&0===e._a[on]&&0===e._a[nn]&&0===e._a[sn]&&(e._nextDay=!0,e._a[tn]=0),e._d=(e._useUTC?me:be).apply(null,s),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[tn]=24)}}function dt(e){var t,i,o,n,s,r,a,h;t=e._w,null!=t.GG||null!=t.W||null!=t.E?(s=1,r=4,i=rt(t.GG,e._a[Jo],ke(bt(),1,4).year),o=rt(t.W,1),n=rt(t.E,1),(n<1||n>7)&&(h=!0)):(s=e._locale._week.dow,r=e._locale._week.doy,i=rt(t.gg,e._a[Jo],ke(bt(),s,r).year),o=rt(t.w,1),null!=t.d?(n=t.d,(n<0||n>6)&&(h=!0)):null!=t.e?(n=t.e+s,(t.e<0||t.e>6)&&(h=!0)):n=s),o<1||o>xe(i,s,r)?u(e)._overflowWeeks=!0:null!=h?u(e)._overflowWeekday=!0:(a=we(i,o,n,s,r),e._a[Jo]=a.year,e._dayOfYear=a.dayOfYear)}function lt(e){if(e._f===t.ISO_8601)return void nt(e);e._a=[],u(e).empty=!0;var i,o,n,s,r,a=""+e._i,h=a.length,d=0;for(n=Z(e._f,e._locale).match(Co)||[],i=0;i<n.length;i++)s=n[i],o=(a.match(J(s,e))||[])[0],o&&(r=a.substr(0,a.indexOf(o)),r.length>0&&u(e).unusedInput.push(r),a=a.slice(a.indexOf(o)+o.length),d+=o.length),Fo[s]?(o?u(e).empty=!1:u(e).unusedTokens.push(s),oe(s,o,e)):e._strict&&!o&&u(e).unusedTokens.push(s);u(e).charsLeftOver=h-d,a.length>0&&u(e).unusedInput.push(a),e._a[tn]<=12&&u(e).bigHour===!0&&e._a[tn]>0&&(u(e).bigHour=void 0),u(e).parsedDateParts=e._a.slice(0),u(e).meridiem=e._meridiem,e._a[tn]=ct(e._locale,e._a[tn],e._meridiem),ht(e),ot(e)}function ct(e,t,i){var o;return null==i?t:null!=e.meridiemHour?e.meridiemHour(t,i):null!=e.isPM?(o=e.isPM(i),o&&t<12&&(t+=12),o||12!==t||(t=0),t):t}function ut(e){var t,i,o,n,s;if(0===e._f.length)return u(e).invalidFormat=!0,void(e._d=new Date(NaN));for(n=0;n<e._f.length;n++)s=0,t=y({},e),null!=e._useUTC&&(t._useUTC=e._useUTC),t._f=e._f[n],lt(t),f(t)&&(s+=u(t).charsLeftOver,s+=10*u(t).unusedTokens.length,u(t).score=s,(null==o||s<o)&&(o=s,i=t));d(e,i||t)}function ft(e){if(!e._d){var t=z(e._i);e._a=a([t.year,t.month,t.day||t.date,t.hour,t.minute,t.second,t.millisecond],function(e){return e&&parseInt(e,10)}),ht(e)}}function pt(e){var t=new g(ot(vt(e)));return t._nextDay&&(t.add(1,"d"),t._nextDay=void 0),t}function vt(e){var t=e._i,i=e._f;return e._locale=e._locale||tt(e._l),null===t||void 0===i&&""===t?p({nullInput:!0}):("string"==typeof t&&(e._i=t=e._locale.preparse(t)),b(t)?new g(ot(t)):(o(i)?ut(e):r(t)?e._d=t:i?lt(e):yt(e),f(e)||(e._d=null),e))}function yt(e){var i=e._i;void 0===i?e._d=new Date(t.now()):r(i)?e._d=new Date(i.valueOf()):"string"==typeof i?st(e):o(i)?(e._a=a(i.slice(0),function(e){return parseInt(e,10)}),ht(e)):"object"==typeof i?ft(e):"number"==typeof i?e._d=new Date(i):t.createFromInputFallback(e)}function gt(e,t,i,r,a){var h={};return"boolean"==typeof i&&(r=i,i=void 0),(n(e)&&s(e)||o(e)&&0===e.length)&&(e=void 0),h._isAMomentObject=!0,h._useUTC=h._isUTC=a,h._l=i,h._i=e,h._f=t,h._strict=r,pt(h)}function bt(e,t,i,o){return gt(e,t,i,o,!1)}function mt(e,t){var i,n;if(1===t.length&&o(t[0])&&(t=t[0]),!t.length)return bt();for(i=t[0],n=1;n<t.length;++n)t[n].isValid()&&!t[n][e](i)||(i=t[n]);return i}function _t(){var e=[].slice.call(arguments,0);return mt("isBefore",e)}function wt(){var e=[].slice.call(arguments,0);return mt("isAfter",e)}function kt(e){var t=z(e),i=t.year||0,o=t.quarter||0,n=t.month||0,s=t.week||0,r=t.day||0,a=t.hour||0,h=t.minute||0,d=t.second||0,l=t.millisecond||0;this._milliseconds=+l+1e3*d+6e4*h+1e3*a*60*60,this._days=+r+7*s,this._months=+n+3*o+12*i,this._data={},this._locale=tt(),this._bubble()}function xt(e){return e instanceof kt}function Ot(e){return e<0?Math.round(-1*e)*-1:Math.round(e)}function Et(e,t){q(e,0,0,function(){var e=this.utcOffset(),i="+";return e<0&&(e=-e,i="-"),i+V(~~(e/60),2)+t+V(~~e%60,2)})}function Mt(e,t){var i=(t||"").match(e)||[],o=i[i.length-1]||[],n=(o+"").match(jn)||["-",0,0],s=+(60*n[1])+_(n[2]);return"+"===n[0]?s:-s}function St(e,i){var o,n;return i._isUTC?(o=i.clone(),n=(b(e)||r(e)?e.valueOf():bt(e).valueOf())-o.valueOf(),o._d.setTime(o._d.valueOf()+n),t.updateOffset(o,!1),o):bt(e).local()}function Dt(e){return 15*-Math.round(e._d.getTimezoneOffset()/15)}function Ct(e,i){var o,n=this._offset||0;return this.isValid()?null!=e?("string"==typeof e?e=Mt(Go,e):Math.abs(e)<16&&(e=60*e),!this._isUTC&&i&&(o=Dt(this)),this._offset=e,this._isUTC=!0,null!=o&&this.add(o,"m"),n!==e&&(!i||this._changeInProgress?Vt(this,Lt(e-n,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,t.updateOffset(this,!0),this._changeInProgress=null)),this):this._isUTC?n:Dt(this):null!=e?this:NaN}function Tt(e,t){return null!=e?("string"!=typeof e&&(e=-e),this.utcOffset(e,t),this):-this.utcOffset()}function Pt(e){return this.utcOffset(0,e)}function Ft(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(Dt(this),"m")),this}function Bt(){if(this._tzm)this.utcOffset(this._tzm);else if("string"==typeof this._i){var e=Mt(qo,this._i);0===e?this.utcOffset(0,!0):this.utcOffset(Mt(qo,this._i))}return this}function It(e){return!!this.isValid()&&(e=e?bt(e).utcOffset():0,(this.utcOffset()-e)%60===0)}function jt(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function Nt(){if(!v(this._isDSTShifted))return this._isDSTShifted;var e={};if(y(e,this),e=vt(e),e._a){var t=e._isUTC?l(e._a):bt(e._a);this._isDSTShifted=this.isValid()&&w(e._a,t.toArray())>0}else this._isDSTShifted=!1;return this._isDSTShifted}function zt(){return!!this.isValid()&&!this._isUTC}function Rt(){return!!this.isValid()&&this._isUTC}function At(){return!!this.isValid()&&(this._isUTC&&0===this._offset)}function Lt(e,t){var i,o,n,s=e,r=null;return xt(e)?s={ms:e._milliseconds,d:e._days,M:e._months}:"number"==typeof e?(s={},t?s[t]=e:s.milliseconds=e):(r=Nn.exec(e))?(i="-"===r[1]?-1:1,s={y:0,d:_(r[en])*i,h:_(r[tn])*i,m:_(r[on])*i,s:_(r[nn])*i,ms:_(Ot(1e3*r[sn]))*i}):(r=zn.exec(e))?(i="-"===r[1]?-1:1,s={y:Ht(r[2],i),M:Ht(r[3],i),w:Ht(r[4],i),d:Ht(r[5],i),h:Ht(r[6],i),m:Ht(r[7],i),s:Ht(r[8],i)}):null==s?s={}:"object"==typeof s&&("from"in s||"to"in s)&&(n=Yt(bt(s.from),bt(s.to)),s={},s.ms=n.milliseconds,s.M=n.months),o=new kt(s),xt(e)&&h(e,"_locale")&&(o._locale=e._locale),o}function Ht(e,t){var i=e&&parseFloat(e.replace(",","."));return(isNaN(i)?0:i)*t}function Wt(e,t){var i={milliseconds:0,months:0};return i.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(i.months,"M").isAfter(t)&&--i.months,i.milliseconds=+t-+e.clone().add(i.months,"M"),i}function Yt(e,t){var i;return e.isValid()&&t.isValid()?(t=St(t,e),e.isBefore(t)?i=Wt(e,t):(i=Wt(t,e),i.milliseconds=-i.milliseconds,i.months=-i.months),i):{milliseconds:0,months:0}}function Ut(e,t){return function(i,o){var n,s;return null===o||isNaN(+o)||(O(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),s=i,i=o,o=s),i="string"==typeof i?+i:i,n=Lt(i,o),Vt(this,n,e),this}}function Vt(e,i,o,n){var s=i._milliseconds,r=Ot(i._days),a=Ot(i._months);e.isValid()&&(n=null==n||n,s&&e._d.setTime(e._d.valueOf()+s*o),r&&W(e,"Date",H(e,"Date")+r*o),a&&de(e,H(e,"Month")+a*o),n&&t.updateOffset(e,r||a))}function qt(e,t){var i=e.diff(t,"days",!0);return i<-6?"sameElse":i<-1?"lastWeek":i<0?"lastDay":i<1?"sameDay":i<2?"nextDay":i<7?"nextWeek":"sameElse"}function Gt(e,i){var o=e||bt(),n=St(o,this).startOf("day"),s=t.calendarFormat(this,n)||"sameElse",r=i&&(E(i[s])?i[s].call(this,o):i[s]);return this.format(r||this.localeData().calendar(s,this,bt(o)))}function Xt(){return new g(this)}function Kt(e,t){var i=b(e)?e:bt(e);return!(!this.isValid()||!i.isValid())&&(t=N(v(t)?"millisecond":t),"millisecond"===t?this.valueOf()>i.valueOf():i.valueOf()<this.clone().startOf(t).valueOf())}function Zt(e,t){var i=b(e)?e:bt(e);return!(!this.isValid()||!i.isValid())&&(t=N(v(t)?"millisecond":t),"millisecond"===t?this.valueOf()<i.valueOf():this.clone().endOf(t).valueOf()<i.valueOf())}function Qt(e,t,i,o){return o=o||"()",("("===o[0]?this.isAfter(e,i):!this.isBefore(e,i))&&(")"===o[1]?this.isBefore(t,i):!this.isAfter(t,i))}function Jt(e,t){var i,o=b(e)?e:bt(e);return!(!this.isValid()||!o.isValid())&&(t=N(t||"millisecond"),"millisecond"===t?this.valueOf()===o.valueOf():(i=o.valueOf(),this.clone().startOf(t).valueOf()<=i&&i<=this.clone().endOf(t).valueOf()))}function $t(e,t){return this.isSame(e,t)||this.isAfter(e,t)}function ei(e,t){return this.isSame(e,t)||this.isBefore(e,t)}function ti(e,t,i){var o,n,s,r;return this.isValid()?(o=St(e,this),o.isValid()?(n=6e4*(o.utcOffset()-this.utcOffset()),t=N(t),"year"===t||"month"===t||"quarter"===t?(r=ii(this,o),"quarter"===t?r/=3:"year"===t&&(r/=12)):(s=this-o,r="second"===t?s/1e3:"minute"===t?s/6e4:"hour"===t?s/36e5:"day"===t?(s-n)/864e5:"week"===t?(s-n)/6048e5:s),i?r:m(r)):NaN):NaN}function ii(e,t){var i,o,n=12*(t.year()-e.year())+(t.month()-e.month()),s=e.clone().add(n,"months");return t-s<0?(i=e.clone().add(n-1,"months"),o=(t-s)/(s-i)):(i=e.clone().add(n+1,"months"),o=(t-s)/(i-s)),-(n+o)||0}function oi(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function ni(){var e=this.clone().utc();return 0<e.year()&&e.year()<=9999?E(Date.prototype.toISOString)?this.toDate().toISOString():K(e,"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]"):K(e,"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]")}function si(e){e||(e=this.isUtc()?t.defaultFormatUtc:t.defaultFormat);var i=K(this,e);return this.localeData().postformat(i)}function ri(e,t){return this.isValid()&&(b(e)&&e.isValid()||bt(e).isValid())?Lt({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function ai(e){return this.from(bt(),e)}function hi(e,t){return this.isValid()&&(b(e)&&e.isValid()||bt(e).isValid())?Lt({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function di(e){return this.to(bt(),e)}function li(e){var t;return void 0===e?this._locale._abbr:(t=tt(e),null!=t&&(this._locale=t),this)}function ci(){return this._locale}function ui(e){switch(e=N(e)){case"year":this.month(0);case"quarter":case"month":this.date(1);case"week":case"isoWeek":case"day":case"date":this.hours(0);case"hour":this.minutes(0);case"minute":this.seconds(0);case"second":this.milliseconds(0)}return"week"===e&&this.weekday(0),"isoWeek"===e&&this.isoWeekday(1),"quarter"===e&&this.month(3*Math.floor(this.month()/3)),this}function fi(e){return e=N(e),void 0===e||"millisecond"===e?this:("date"===e&&(e="day"),this.startOf(e).add(1,"isoWeek"===e?"week":e).subtract(1,"ms"))}function pi(){return this._d.valueOf()-6e4*(this._offset||0)}function vi(){return Math.floor(this.valueOf()/1e3)}function yi(){return new Date(this.valueOf())}function gi(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]}function bi(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}}function mi(){return this.isValid()?this.toISOString():null}function _i(){return f(this)}function wi(){return d({},u(this))}function ki(){return u(this).overflow}function xi(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}function Oi(e,t){q(0,[e,e.length],0,t)}function Ei(e){return Ci.call(this,e,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)}function Mi(e){return Ci.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)}function Si(){return xe(this.year(),1,4)}function Di(){var e=this.localeData()._week;return xe(this.year(),e.dow,e.doy)}function Ci(e,t,i,o,n){var s;return null==e?ke(this,o,n).year:(s=xe(e,o,n),t>s&&(t=s),Ti.call(this,e,t,i,o,n))}function Ti(e,t,i,o,n){var s=we(e,t,i,o,n),r=me(s.year,0,s.dayOfYear);return this.year(r.getUTCFullYear()),this.month(r.getUTCMonth()),this.date(r.getUTCDate()),this}function Pi(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)}function Fi(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")}function Bi(e,t){t[sn]=_(1e3*("0."+e))}function Ii(){return this._isUTC?"UTC":""}function ji(){return this._isUTC?"Coordinated Universal Time":""}function Ni(e){return bt(1e3*e)}function zi(){return bt.apply(null,arguments).parseZone()}function Ri(e){return e}function Ai(e,t,i,o){var n=tt(),s=l().set(o,t);return n[i](s,e)}function Li(e,t,i){if("number"==typeof e&&(t=e,e=void 0),e=e||"",null!=t)return Ai(e,t,i,"month");var o,n=[];for(o=0;o<12;o++)n[o]=Ai(e,o,i,"month");return n}function Hi(e,t,i,o){"boolean"==typeof e?("number"==typeof t&&(i=t,t=void 0),t=t||""):(t=e,i=t,e=!1,"number"==typeof t&&(i=t,t=void 0),t=t||"");var n=tt(),s=e?n._week.dow:0;if(null!=i)return Ai(t,(i+s)%7,o,"day");var r,a=[];for(r=0;r<7;r++)a[r]=Ai(t,(r+s)%7,o,"day");return a}function Wi(e,t){return Li(e,t,"months")}function Yi(e,t){return Li(e,t,"monthsShort")}function Ui(e,t,i){return Hi(e,t,i,"weekdays")}function Vi(e,t,i){return Hi(e,t,i,"weekdaysShort")}function qi(e,t,i){return Hi(e,t,i,"weekdaysMin")}function Gi(){var e=this._data;return this._milliseconds=Kn(this._milliseconds),this._days=Kn(this._days),this._months=Kn(this._months),e.milliseconds=Kn(e.milliseconds),e.seconds=Kn(e.seconds),e.minutes=Kn(e.minutes),e.hours=Kn(e.hours),e.months=Kn(e.months),e.years=Kn(e.years),this}function Xi(e,t,i,o){var n=Lt(t,i);return e._milliseconds+=o*n._milliseconds,e._days+=o*n._days,e._months+=o*n._months,e._bubble()}function Ki(e,t){return Xi(this,e,t,1)}function Zi(e,t){return Xi(this,e,t,-1)}function Qi(e){return e<0?Math.floor(e):Math.ceil(e)}function Ji(){var e,t,i,o,n,s=this._milliseconds,r=this._days,a=this._months,h=this._data;return s>=0&&r>=0&&a>=0||s<=0&&r<=0&&a<=0||(s+=864e5*Qi(eo(a)+r),r=0,a=0),h.milliseconds=s%1e3,e=m(s/1e3),h.seconds=e%60,t=m(e/60),h.minutes=t%60,i=m(t/60),h.hours=i%24,r+=m(i/24),n=m($i(r)),a+=n,r-=Qi(eo(n)),o=m(a/12),a%=12,h.days=r,h.months=a,h.years=o,this}function $i(e){return 4800*e/146097}function eo(e){return 146097*e/4800}function to(e){var t,i,o=this._milliseconds;if(e=N(e),"month"===e||"year"===e)return t=this._days+o/864e5,i=this._months+$i(t),"month"===e?i:i/12;switch(t=this._days+Math.round(eo(this._months)),e){case"week":return t/7+o/6048e5;case"day":return t+o/864e5;case"hour":return 24*t+o/36e5;case"minute":return 1440*t+o/6e4;case"second":return 86400*t+o/1e3;case"millisecond":return Math.floor(864e5*t)+o;default:throw new Error("Unknown unit "+e)}}function io(){return this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*_(this._months/12)}function oo(e){return function(){return this.as(e)}}function no(e){return e=N(e),this[e+"s"]()}function so(e){return function(){return this._data[e]}}function ro(){return m(this.days()/7)}function ao(e,t,i,o,n){return n.relativeTime(t||1,!!i,e,o)}function ho(e,t,i){var o=Lt(e).abs(),n=cs(o.as("s")),s=cs(o.as("m")),r=cs(o.as("h")),a=cs(o.as("d")),h=cs(o.as("M")),d=cs(o.as("y")),l=n<us.s&&["s",n]||s<=1&&["m"]||s<us.m&&["mm",s]||r<=1&&["h"]||r<us.h&&["hh",r]||a<=1&&["d"]||a<us.d&&["dd",a]||h<=1&&["M"]||h<us.M&&["MM",h]||d<=1&&["y"]||["yy",d];return l[2]=t,l[3]=+e>0,l[4]=i,ao.apply(null,l)}function lo(e){return void 0===e?cs:"function"==typeof e&&(cs=e,!0)}function co(e,t){return void 0!==us[e]&&(void 0===t?us[e]:(us[e]=t,!0))}function uo(e){var t=this.localeData(),i=ho(this,!e,t);return e&&(i=t.pastFuture(+this,i)),t.postformat(i)}function fo(){var e,t,i,o=fs(this._milliseconds)/1e3,n=fs(this._days),s=fs(this._months);e=m(o/60),t=m(e/60),o%=60,e%=60,i=m(s/12),s%=12;var r=i,a=s,h=n,d=t,l=e,c=o,u=this.asSeconds();return u?(u<0?"-":"")+"P"+(r?r+"Y":"")+(a?a+"M":"")+(h?h+"D":"")+(d||l||c?"T":"")+(d?d+"H":"")+(l?l+"M":"")+(c?c+"S":""):"P0D"}var po,vo;vo=Array.prototype.some?Array.prototype.some:function(e){for(var t=Object(this),i=t.length>>>0,o=0;o<i;o++)if(o in t&&e.call(this,t[o],o,t))return!0;return!1};var yo=t.momentProperties=[],go=!1,bo={};t.suppressDeprecationWarnings=!1,t.deprecationHandler=null;var mo;mo=Object.keys?Object.keys:function(e){var t,i=[];for(t in e)h(e,t)&&i.push(t);return i};var _o,wo={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},ko={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},xo="Invalid date",Oo="%d",Eo=/\d{1,2}/,Mo={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},So={},Do={},Co=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,To=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,Po={},Fo={},Bo=/\d/,Io=/\d\d/,jo=/\d{3}/,No=/\d{4}/,zo=/[+-]?\d{6}/,Ro=/\d\d?/,Ao=/\d\d\d\d?/,Lo=/\d\d\d\d\d\d?/,Ho=/\d{1,3}/,Wo=/\d{1,4}/,Yo=/[+-]?\d{1,6}/,Uo=/\d+/,Vo=/[+-]?\d+/,qo=/Z|[+-]\d\d:?\d\d/gi,Go=/Z|[+-]\d\d(?::?\d\d)?/gi,Xo=/[+-]?\d+(\.\d{1,3})?/,Ko=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+|[\u0600-\u06FF\/]+(\s*?[\u0600-\u06FF]+){1,2}/i,Zo={},Qo={},Jo=0,$o=1,en=2,tn=3,on=4,nn=5,sn=6,rn=7,an=8;_o=Array.prototype.indexOf?Array.prototype.indexOf:function(e){var t;for(t=0;t<this.length;++t)if(this[t]===e)return t;return-1},q("M",["MM",2],"Mo",function(){return this.month()+1}),q("MMM",0,0,function(e){return this.localeData().monthsShort(this,e)}),q("MMMM",0,0,function(e){return this.localeData().months(this,e)}),j("month","M"),R("month",8),Q("M",Ro),Q("MM",Ro,Io),Q("MMM",function(e,t){return t.monthsShortRegex(e)}),Q("MMMM",function(e,t){return t.monthsRegex(e)}),te(["M","MM"],function(e,t){t[$o]=_(e)-1}),te(["MMM","MMMM"],function(e,t,i,o){var n=i._locale.monthsParse(e,o,i._strict);null!=n?t[$o]=n:u(i).invalidMonth=e});var hn=/D[oD]?(\[[^\[\]]*\]|\s+)+MMMM?/,dn="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ln="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),cn=Ko,un=Ko;q("Y",0,0,function(){var e=this.year();return e<=9999?""+e:"+"+e}),q(0,["YY",2],0,function(){return this.year()%100}),q(0,["YYYY",4],0,"year"),q(0,["YYYYY",5],0,"year"),q(0,["YYYYYY",6,!0],0,"year"),j("year","y"),R("year",1),Q("Y",Vo),Q("YY",Ro,Io),Q("YYYY",Wo,No),Q("YYYYY",Yo,zo),Q("YYYYYY",Yo,zo),te(["YYYYY","YYYYYY"],Jo),te("YYYY",function(e,i){i[Jo]=2===e.length?t.parseTwoDigitYear(e):_(e)}),te("YY",function(e,i){i[Jo]=t.parseTwoDigitYear(e)}),te("Y",function(e,t){t[Jo]=parseInt(e,10)}),t.parseTwoDigitYear=function(e){return _(e)+(_(e)>68?1900:2e3)};var fn=L("FullYear",!0);q("w",["ww",2],"wo","week"),q("W",["WW",2],"Wo","isoWeek"),j("week","w"),j("isoWeek","W"),R("week",5),R("isoWeek",5),Q("w",Ro),Q("ww",Ro,Io),Q("W",Ro),Q("WW",Ro,Io),ie(["w","ww","W","WW"],function(e,t,i,o){t[o.substr(0,1)]=_(e)});var pn={dow:0,doy:6};q("d",0,"do","day"),q("dd",0,0,function(e){return this.localeData().weekdaysMin(this,e)}),q("ddd",0,0,function(e){return this.localeData().weekdaysShort(this,e)}),q("dddd",0,0,function(e){return this.localeData().weekdays(this,e)}),q("e",0,0,"weekday"),q("E",0,0,"isoWeekday"),j("day","d"),j("weekday","e"),j("isoWeekday","E"),R("day",11),R("weekday",11),R("isoWeekday",11),Q("d",Ro),Q("e",Ro),Q("E",Ro),Q("dd",function(e,t){return t.weekdaysMinRegex(e)}),Q("ddd",function(e,t){return t.weekdaysShortRegex(e)}),Q("dddd",function(e,t){return t.weekdaysRegex(e)}),ie(["dd","ddd","dddd"],function(e,t,i,o){var n=i._locale.weekdaysParse(e,o,i._strict);null!=n?t.d=n:u(i).invalidWeekday=e}),ie(["d","e","E"],function(e,t,i,o){t[o]=_(e)});var vn="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),yn="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),gn="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),bn=Ko,mn=Ko,_n=Ko;q("H",["HH",2],0,"hour"),q("h",["hh",2],0,Ye),q("k",["kk",2],0,Ue),q("hmm",0,0,function(){return""+Ye.apply(this)+V(this.minutes(),2)}),q("hmmss",0,0,function(){return""+Ye.apply(this)+V(this.minutes(),2)+V(this.seconds(),2)}),q("Hmm",0,0,function(){return""+this.hours()+V(this.minutes(),2)}),q("Hmmss",0,0,function(){return""+this.hours()+V(this.minutes(),2)+V(this.seconds(),2)}),Ve("a",!0),Ve("A",!1),j("hour","h"),R("hour",13),Q("a",qe),Q("A",qe),Q("H",Ro),Q("h",Ro),Q("HH",Ro,Io),Q("hh",Ro,Io),Q("hmm",Ao),Q("hmmss",Lo),Q("Hmm",Ao),Q("Hmmss",Lo),te(["H","HH"],tn),te(["a","A"],function(e,t,i){i._isPm=i._locale.isPM(e),i._meridiem=e}),te(["h","hh"],function(e,t,i){t[tn]=_(e),u(i).bigHour=!0}),te("hmm",function(e,t,i){var o=e.length-2;t[tn]=_(e.substr(0,o)),t[on]=_(e.substr(o)),u(i).bigHour=!0}),te("hmmss",function(e,t,i){var o=e.length-4,n=e.length-2;t[tn]=_(e.substr(0,o)),t[on]=_(e.substr(o,2)),t[nn]=_(e.substr(n)),u(i).bigHour=!0}),te("Hmm",function(e,t,i){var o=e.length-2;t[tn]=_(e.substr(0,o)),t[on]=_(e.substr(o))}),te("Hmmss",function(e,t,i){var o=e.length-4,n=e.length-2;t[tn]=_(e.substr(0,o)),t[on]=_(e.substr(o,2)),t[nn]=_(e.substr(n))});var wn,kn=/[ap]\.?m?\.?/i,xn=L("Hours",!0),On={calendar:wo,longDateFormat:ko,invalidDate:xo,ordinal:Oo,ordinalParse:Eo,relativeTime:Mo,months:dn,monthsShort:ln,week:pn,weekdays:vn,weekdaysMin:gn,weekdaysShort:yn,meridiemParse:kn},En={},Mn=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/,Sn=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/,Dn=/Z|[+-]\d\d(?::?\d\d)?/,Cn=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]],Tn=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],Pn=/^\/?Date\((\-?\d+)/i;t.createFromInputFallback=x("value provided is not in a recognized ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non ISO date formats are discouraged and will be removed in an upcoming major release. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))}),t.ISO_8601=function(){};var Fn=x("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=bt.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:p()}),Bn=x("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=bt.apply(null,arguments);return this.isValid()&&e.isValid()?e>this?this:e:p()}),In=function(){return Date.now?Date.now():+new Date};Et("Z",":"),Et("ZZ",""),Q("Z",Go),Q("ZZ",Go),te(["Z","ZZ"],function(e,t,i){i._useUTC=!0,i._tzm=Mt(Go,e)});var jn=/([\+\-]|\d\d)/gi;t.updateOffset=function(){};var Nn=/^(\-)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)(\.\d*)?)?$/,zn=/^(-)?P(?:(-?[0-9,.]*)Y)?(?:(-?[0-9,.]*)M)?(?:(-?[0-9,.]*)W)?(?:(-?[0-9,.]*)D)?(?:T(?:(-?[0-9,.]*)H)?(?:(-?[0-9,.]*)M)?(?:(-?[0-9,.]*)S)?)?$/;Lt.fn=kt.prototype;var Rn=Ut(1,"add"),An=Ut(-1,"subtract");t.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",t.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var Ln=x("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(e){return void 0===e?this.localeData():this.locale(e)});q(0,["gg",2],0,function(){return this.weekYear()%100}),q(0,["GG",2],0,function(){return this.isoWeekYear()%100}),Oi("gggg","weekYear"),Oi("ggggg","weekYear"),Oi("GGGG","isoWeekYear"),Oi("GGGGG","isoWeekYear"),j("weekYear","gg"),j("isoWeekYear","GG"),R("weekYear",1),R("isoWeekYear",1),Q("G",Vo),Q("g",Vo),Q("GG",Ro,Io),Q("gg",Ro,Io),Q("GGGG",Wo,No),Q("gggg",Wo,No),Q("GGGGG",Yo,zo),Q("ggggg",Yo,zo),ie(["gggg","ggggg","GGGG","GGGGG"],function(e,t,i,o){t[o.substr(0,2)]=_(e)}),ie(["gg","GG"],function(e,i,o,n){i[n]=t.parseTwoDigitYear(e)}),q("Q",0,"Qo","quarter"),j("quarter","Q"),R("quarter",7),Q("Q",Bo),te("Q",function(e,t){t[$o]=3*(_(e)-1)}),q("D",["DD",2],"Do","date"),j("date","D"),R("date",9),Q("D",Ro),Q("DD",Ro,Io),Q("Do",function(e,t){return e?t._ordinalParse:t._ordinalParseLenient}),te(["D","DD"],en),te("Do",function(e,t){t[en]=_(e.match(Ro)[0],10)});var Hn=L("Date",!0);q("DDD",["DDDD",3],"DDDo","dayOfYear"),j("dayOfYear","DDD"),R("dayOfYear",4),Q("DDD",Ho),Q("DDDD",jo),te(["DDD","DDDD"],function(e,t,i){i._dayOfYear=_(e)}),q("m",["mm",2],0,"minute"),j("minute","m"),R("minute",14),Q("m",Ro),Q("mm",Ro,Io),te(["m","mm"],on);var Wn=L("Minutes",!1);q("s",["ss",2],0,"second"),j("second","s"),R("second",15),Q("s",Ro),Q("ss",Ro,Io),te(["s","ss"],nn);var Yn=L("Seconds",!1);q("S",0,0,function(){return~~(this.millisecond()/100)}),q(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),q(0,["SSS",3],0,"millisecond"),q(0,["SSSS",4],0,function(){return 10*this.millisecond()}),q(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),q(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),q(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),q(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),q(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),j("millisecond","ms"),R("millisecond",16),Q("S",Ho,Bo),Q("SS",Ho,Io),Q("SSS",Ho,jo);var Un;for(Un="SSSS";Un.length<=9;Un+="S")Q(Un,Uo);for(Un="S";Un.length<=9;Un+="S")te(Un,Bi);var Vn=L("Milliseconds",!1);q("z",0,0,"zoneAbbr"),q("zz",0,0,"zoneName");var qn=g.prototype;qn.add=Rn,qn.calendar=Gt,qn.clone=Xt,qn.diff=ti,qn.endOf=fi,qn.format=si,qn.from=ri,qn.fromNow=ai,qn.to=hi,qn.toNow=di,qn.get=Y,qn.invalidAt=ki,qn.isAfter=Kt,qn.isBefore=Zt,qn.isBetween=Qt,qn.isSame=Jt,qn.isSameOrAfter=$t,qn.isSameOrBefore=ei,qn.isValid=_i,qn.lang=Ln,qn.locale=li,qn.localeData=ci,qn.max=Bn,qn.min=Fn,qn.parsingFlags=wi,qn.set=U,qn.startOf=ui,qn.subtract=An,qn.toArray=gi,qn.toObject=bi,qn.toDate=yi,qn.toISOString=ni,qn.toJSON=mi,qn.toString=oi,qn.unix=vi,qn.valueOf=pi,qn.creationData=xi,qn.year=fn,qn.isLeapYear=ge,qn.weekYear=Ei,qn.isoWeekYear=Mi,qn.quarter=qn.quarters=Pi,qn.month=le,qn.daysInMonth=ce,qn.week=qn.weeks=Se,qn.isoWeek=qn.isoWeeks=De,qn.weeksInYear=Di,qn.isoWeeksInYear=Si,qn.date=Hn,qn.day=qn.days=Ne,qn.weekday=ze,qn.isoWeekday=Re,qn.dayOfYear=Fi,qn.hour=qn.hours=xn,qn.minute=qn.minutes=Wn,qn.second=qn.seconds=Yn,qn.millisecond=qn.milliseconds=Vn,qn.utcOffset=Ct,qn.utc=Pt,qn.local=Ft,qn.parseZone=Bt,qn.hasAlignedHourOffset=It,qn.isDST=jt,qn.isLocal=zt,qn.isUtcOffset=Rt,qn.isUtc=At,qn.isUTC=At,qn.zoneAbbr=Ii,qn.zoneName=ji,qn.dates=x("dates accessor is deprecated. Use date instead.",Hn),qn.months=x("months accessor is deprecated. Use month instead",le),qn.years=x("years accessor is deprecated. Use year instead",fn),qn.zone=x("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",Tt),qn.isDSTShifted=x("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",Nt);var Gn=qn,Xn=D.prototype;Xn.calendar=C,Xn.longDateFormat=T,Xn.invalidDate=P,Xn.ordinal=F,Xn.preparse=Ri,Xn.postformat=Ri,Xn.relativeTime=B,Xn.pastFuture=I,Xn.set=M,Xn.months=se,Xn.monthsShort=re,Xn.monthsParse=he,Xn.monthsRegex=fe,Xn.monthsShortRegex=ue,Xn.week=Oe,Xn.firstDayOfYear=Me,Xn.firstDayOfWeek=Ee,Xn.weekdays=Pe,Xn.weekdaysMin=Be,Xn.weekdaysShort=Fe,Xn.weekdaysParse=je,Xn.weekdaysRegex=Ae,Xn.weekdaysShortRegex=Le,Xn.weekdaysMinRegex=He,Xn.isPM=Ge,Xn.meridiem=Xe,Je("en",{ordinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10,i=1===_(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+i}}),t.lang=x("moment.lang is deprecated. Use moment.locale instead.",Je),t.langData=x("moment.langData is deprecated. Use moment.localeData instead.",tt);var Kn=Math.abs,Zn=oo("ms"),Qn=oo("s"),Jn=oo("m"),$n=oo("h"),es=oo("d"),ts=oo("w"),is=oo("M"),os=oo("y"),ns=so("milliseconds"),ss=so("seconds"),rs=so("minutes"),as=so("hours"),hs=so("days"),ds=so("months"),ls=so("years"),cs=Math.round,us={s:45,m:45,h:22,d:26,M:11},fs=Math.abs,ps=kt.prototype;ps.abs=Gi,ps.add=Ki,ps.subtract=Zi,ps.as=to,ps.asMilliseconds=Zn,ps.asSeconds=Qn,ps.asMinutes=Jn,ps.asHours=$n,ps.asDays=es,ps.asWeeks=ts,ps.asMonths=is,ps.asYears=os,ps.valueOf=io,ps._bubble=Ji,ps.get=no,ps.milliseconds=ns,ps.seconds=ss,ps.minutes=rs,ps.hours=as,ps.days=hs,ps.weeks=ro,ps.months=ds,ps.years=ls,ps.humanize=uo,ps.toISOString=fo,ps.toString=fo,ps.toJSON=fo,ps.locale=li,ps.localeData=ci,ps.toIsoString=x("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",fo),ps.lang=Ln,q("X",0,0,"unix"),q("x",0,0,"valueOf"),Q("x",Vo),Q("X",Xo),te("X",function(e,t,i){i._d=new Date(1e3*parseFloat(e,10))}),te("x",function(e,t,i){i._d=new Date(_(e))}),t.version="2.15.1",i(bt),t.fn=Gn,t.min=_t,t.max=wt,t.now=In,t.utc=l,t.unix=Ni,t.months=Wi,t.isDate=r,t.locale=Je,t.invalid=p,t.duration=Lt,t.isMoment=b,t.weekdays=Ui,t.parseZone=zi,t.localeData=tt,t.isDuration=xt,t.monthsShort=Yi,t.weekdaysMin=qi,t.defineLocale=$e,t.updateLocale=et,t.locales=it,t.weekdaysShort=Vi,t.normalizeUnits=N,t.relativeTimeRounding=lo,t.relativeTimeThreshold=co,t.calendarFormat=qt,t.prototype=Gn;var vs=t;return vs})}).call(t,i(4)(e))},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children=[],e.webpackPolyfill=1),e}},function(e,t){function i(e){throw new Error("Cannot find module '"+e+"'.")}i.keys=function(){return[]},i.resolve=i,e.exports=i,i.id=5},function(e,t){(function(t){function i(e,t,i){var o=t&&i||0,n=0;for(t=t||[],e.toLowerCase().replace(/[0-9a-f]{2}/g,function(e){n<16&&(t[o+n++]=c[e])});n<16;)t[o+n++]=0;return t}function o(e,t){var i=t||0,o=l;return o[e[i++]]+o[e[i++]]+o[e[i++]]+o[e[i++]]+"-"+o[e[i++]]+o[e[i++]]+"-"+o[e[i++]]+o[e[i++]]+"-"+o[e[i++]]+o[e[i++]]+"-"+o[e[i++]]+o[e[i++]]+o[e[i++]]+o[e[i++]]+o[e[i++]]+o[e[i++]]}function n(e,t,i){var n=t&&i||0,s=t||[];e=e||{};var r=void 0!==e.clockseq?e.clockseq:v,a=void 0!==e.msecs?e.msecs:(new Date).getTime(),h=void 0!==e.nsecs?e.nsecs:g+1,d=a-y+(h-g)/1e4;if(d<0&&void 0===e.clockseq&&(r=r+1&16383),(d<0||a>y)&&void 0===e.nsecs&&(h=0),h>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");y=a,g=h,v=r,a+=122192928e5;var l=(1e4*(268435455&a)+h)%4294967296;s[n++]=l>>>24&255,s[n++]=l>>>16&255,s[n++]=l>>>8&255,s[n++]=255&l;var c=a/4294967296*1e4&268435455;s[n++]=c>>>8&255,s[n++]=255&c,s[n++]=c>>>24&15|16,s[n++]=c>>>16&255,s[n++]=r>>>8|128,s[n++]=255&r;for(var u=e.node||p,f=0;f<6;f++)s[n+f]=u[f];return t?t:o(s)}function s(e,t,i){var n=t&&i||0;"string"==typeof e&&(t="binary"==e?new Array(16):null,e=null),e=e||{};var s=e.random||(e.rng||r)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t)for(var a=0;a<16;a++)t[n+a]=s[a];return t||o(s)}var r,a="undefined"!=typeof window?window:"undefined"!=typeof t?t:null;
if(a&&a.crypto&&crypto.getRandomValues){var h=new Uint8Array(16);r=function(){return crypto.getRandomValues(h),h}}if(!r){var d=new Array(16);r=function(){for(var e,t=0;t<16;t++)0===(3&t)&&(e=4294967296*Math.random()),d[t]=e>>>((3&t)<<3)&255;return d}}for(var l=[],c={},u=0;u<256;u++)l[u]=(u+256).toString(16).substr(1),c[l[u]]=u;var f=r(),p=[1|f[0],f[1],f[2],f[3],f[4],f[5]],v=16383&(f[6]<<8|f[7]),y=0,g=0,b=s;b.v1=n,b.v4=s,b.parse=i,b.unparse=o,e.exports=b}).call(t,function(){return this}())},function(e,t){t.prepareElements=function(e){for(var t in e)e.hasOwnProperty(t)&&(e[t].redundant=e[t].used,e[t].used=[])},t.cleanupElements=function(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t].redundant){for(var i=0;i<e[t].redundant.length;i++)e[t].redundant[i].parentNode.removeChild(e[t].redundant[i]);e[t].redundant=[]}},t.resetElements=function(e){t.prepareElements(e),t.cleanupElements(e),t.prepareElements(e)},t.getSVGElement=function(e,t,i){var o;return t.hasOwnProperty(e)?t[e].redundant.length>0?(o=t[e].redundant[0],t[e].redundant.shift()):(o=document.createElementNS("http://www.w3.org/2000/svg",e),i.appendChild(o)):(o=document.createElementNS("http://www.w3.org/2000/svg",e),t[e]={used:[],redundant:[]},i.appendChild(o)),t[e].used.push(o),o},t.getDOMElement=function(e,t,i,o){var n;return t.hasOwnProperty(e)?t[e].redundant.length>0?(n=t[e].redundant[0],t[e].redundant.shift()):(n=document.createElement(e),void 0!==o?i.insertBefore(n,o):i.appendChild(n)):(n=document.createElement(e),t[e]={used:[],redundant:[]},void 0!==o?i.insertBefore(n,o):i.appendChild(n)),t[e].used.push(n),n},t.drawPoint=function(e,i,o,n,s,r){var a;if("circle"==o.style?(a=t.getSVGElement("circle",n,s),a.setAttributeNS(null,"cx",e),a.setAttributeNS(null,"cy",i),a.setAttributeNS(null,"r",.5*o.size)):(a=t.getSVGElement("rect",n,s),a.setAttributeNS(null,"x",e-.5*o.size),a.setAttributeNS(null,"y",i-.5*o.size),a.setAttributeNS(null,"width",o.size),a.setAttributeNS(null,"height",o.size)),void 0!==o.styles&&a.setAttributeNS(null,"style",o.styles),a.setAttributeNS(null,"class",o.className+" vis-point"),r){var h=t.getSVGElement("text",n,s);r.xOffset&&(e+=r.xOffset),r.yOffset&&(i+=r.yOffset),r.content&&(h.textContent=r.content),r.className&&h.setAttributeNS(null,"class",r.className+" vis-label"),h.setAttributeNS(null,"x",e),h.setAttributeNS(null,"y",i)}return a},t.drawBar=function(e,i,o,n,s,r,a,h){if(0!=n){n<0&&(n*=-1,i-=n);var d=t.getSVGElement("rect",r,a);d.setAttributeNS(null,"x",e-.5*o),d.setAttributeNS(null,"y",i),d.setAttributeNS(null,"width",o),d.setAttributeNS(null,"height",n),d.setAttributeNS(null,"class",s),h&&d.setAttributeNS(null,"style",h)}}},function(e,t,i){function o(e,t){if(e&&!Array.isArray(e)&&(t=e,e=null),this._options=t||{},this._data={},this.length=0,this._fieldId=this._options.fieldId||"id",this._type={},this._options.type)for(var i=Object.keys(this._options.type),o=0,n=i.length;o<n;o++){var s=i[o],r=this._options.type[s];"Date"==r||"ISODate"==r||"ASPDate"==r?this._type[s]="Date":this._type[s]=r}if(this._options.convert)throw new Error('Option "convert" is deprecated. Use "type" instead.');this._subscribers={},e&&this.add(e),this.setOptions(t)}var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},s=i(1),r=i(9);o.prototype.setOptions=function(e){e&&void 0!==e.queue&&(e.queue===!1?this._queue&&(this._queue.destroy(),delete this._queue):(this._queue||(this._queue=r.extend(this,{replace:["add","update","remove"]})),"object"===n(e.queue)&&this._queue.setOptions(e.queue)))},o.prototype.on=function(e,t){var i=this._subscribers[e];i||(i=[],this._subscribers[e]=i),i.push({callback:t})},o.prototype.subscribe=function(){throw new Error("DataSet.subscribe is deprecated. Use DataSet.on instead.")},o.prototype.off=function(e,t){var i=this._subscribers[e];i&&(this._subscribers[e]=i.filter(function(e){return e.callback!=t}))},o.prototype.unsubscribe=function(){throw new Error("DataSet.unsubscribe is deprecated. Use DataSet.off instead.")},o.prototype._trigger=function(e,t,i){if("*"==e)throw new Error("Cannot trigger event *");var o=[];e in this._subscribers&&(o=o.concat(this._subscribers[e])),"*"in this._subscribers&&(o=o.concat(this._subscribers["*"]));for(var n=0,s=o.length;n<s;n++){var r=o[n];r.callback&&r.callback(e,t,i||null)}},o.prototype.add=function(e,t){var i,o=[],n=this;if(Array.isArray(e))for(var s=0,r=e.length;s<r;s++)i=n._addItem(e[s]),o.push(i);else{if(!(e instanceof Object))throw new Error("Unknown dataType");i=n._addItem(e),o.push(i)}return o.length&&this._trigger("add",{items:o},t),o},o.prototype.update=function(e,t){var i=[],o=[],n=[],r=[],a=this,h=a._fieldId,d=function(e){var t=e[h];if(a._data[t]){var d=s.extend({},a._data[t]);t=a._updateItem(e),o.push(t),r.push(e),n.push(d)}else t=a._addItem(e),i.push(t)};if(Array.isArray(e))for(var l=0,c=e.length;l<c;l++)e[l]instanceof Object?d(e[l]):console.warn("Ignoring input item, which is not an object at index "+l);else{if(!(e instanceof Object))throw new Error("Unknown dataType");d(e)}if(i.length&&this._trigger("add",{items:i},t),o.length){var u={items:o,oldData:n,data:r};this._trigger("update",u,t)}return i.concat(o)},o.prototype.get=function(e){var t,i,o,n=this,r=s.getType(arguments[0]);"String"==r||"Number"==r?(t=arguments[0],o=arguments[1]):"Array"==r?(i=arguments[0],o=arguments[1]):o=arguments[0];var a;if(o&&o.returnType){var h=["Array","Object"];a=h.indexOf(o.returnType)==-1?"Array":o.returnType}else a="Array";var d,l,c,u,f,p=o&&o.type||this._options.type,v=o&&o.filter,y=[];if(void 0!=t)d=n._getItem(t,p),d&&v&&!v(d)&&(d=null);else if(void 0!=i)for(u=0,f=i.length;u<f;u++)d=n._getItem(i[u],p),v&&!v(d)||y.push(d);else for(l=Object.keys(this._data),u=0,f=l.length;u<f;u++)c=l[u],d=n._getItem(c,p),v&&!v(d)||y.push(d);if(o&&o.order&&void 0==t&&this._sort(y,o.order),o&&o.fields){var g=o.fields;if(void 0!=t)d=this._filterFields(d,g);else for(u=0,f=y.length;u<f;u++)y[u]=this._filterFields(y[u],g)}if("Object"==a){var b,m={};for(u=0,f=y.length;u<f;u++)b=y[u],m[b.id]=b;return m}return void 0!=t?d:y},o.prototype.getIds=function(e){var t,i,o,n,s,r=this._data,a=e&&e.filter,h=e&&e.order,d=e&&e.type||this._options.type,l=Object.keys(r),c=[];if(a)if(h){for(s=[],t=0,i=l.length;t<i;t++)o=l[t],n=this._getItem(o,d),a(n)&&s.push(n);for(this._sort(s,h),t=0,i=s.length;t<i;t++)c.push(s[t][this._fieldId])}else for(t=0,i=l.length;t<i;t++)o=l[t],n=this._getItem(o,d),a(n)&&c.push(n[this._fieldId]);else if(h){for(s=[],t=0,i=l.length;t<i;t++)o=l[t],s.push(r[o]);for(this._sort(s,h),t=0,i=s.length;t<i;t++)c.push(s[t][this._fieldId])}else for(t=0,i=l.length;t<i;t++)o=l[t],n=r[o],c.push(n[this._fieldId]);return c},o.prototype.getDataSet=function(){return this},o.prototype.forEach=function(e,t){var i,o,n,s,r=t&&t.filter,a=t&&t.type||this._options.type,h=this._data,d=Object.keys(h);if(t&&t.order){var l=this.get(t);for(i=0,o=l.length;i<o;i++)n=l[i],s=n[this._fieldId],e(n,s)}else for(i=0,o=d.length;i<o;i++)s=d[i],n=this._getItem(s,a),r&&!r(n)||e(n,s)},o.prototype.map=function(e,t){var i,o,n,s,r=t&&t.filter,a=t&&t.type||this._options.type,h=[],d=this._data,l=Object.keys(d);for(i=0,o=l.length;i<o;i++)n=l[i],s=this._getItem(n,a),r&&!r(s)||h.push(e(s,n));return t&&t.order&&this._sort(h,t.order),h},o.prototype._filterFields=function(e,t){if(!e)return e;var i,o,n={},s=Object.keys(e),r=s.length;if(Array.isArray(t))for(i=0;i<r;i++)o=s[i],t.indexOf(o)!=-1&&(n[o]=e[o]);else for(i=0;i<r;i++)o=s[i],t.hasOwnProperty(o)&&(n[t[o]]=e[o]);return n},o.prototype._sort=function(e,t){if(s.isString(t)){var i=t;e.sort(function(e,t){var o=e[i],n=t[i];return o>n?1:o<n?-1:0})}else{if("function"!=typeof t)throw new TypeError("Order must be a function or a string");e.sort(t)}},o.prototype.remove=function(e,t){var i,o,n,s=[];if(Array.isArray(e))for(i=0,o=e.length;i<o;i++)n=this._remove(e[i]),null!=n&&s.push(n);else n=this._remove(e),null!=n&&s.push(n);return s.length&&this._trigger("remove",{items:s},t),s},o.prototype._remove=function(e){if(s.isNumber(e)||s.isString(e)){if(this._data[e])return delete this._data[e],this.length--,e}else if(e instanceof Object){var t=e[this._fieldId];if(void 0!==t&&this._data[t])return delete this._data[t],this.length--,t}return null},o.prototype.clear=function(e){var t=Object.keys(this._data);return this._data={},this.length=0,this._trigger("remove",{items:t},e),t},o.prototype.max=function(e){var t,i,o=this._data,n=Object.keys(o),s=null,r=null;for(t=0,i=n.length;t<i;t++){var a=n[t],h=o[a],d=h[e];null!=d&&(!s||d>r)&&(s=h,r=d)}return s},o.prototype.min=function(e){var t,i,o=this._data,n=Object.keys(o),s=null,r=null;for(t=0,i=n.length;t<i;t++){var a=n[t],h=o[a],d=h[e];null!=d&&(!s||d<r)&&(s=h,r=d)}return s},o.prototype.distinct=function(e){var t,i,o,n=this._data,r=Object.keys(n),a=[],h=this._options.type&&this._options.type[e]||null,d=0;for(t=0,o=r.length;t<o;t++){var l=r[t],c=n[l],u=c[e],f=!1;for(i=0;i<d;i++)if(a[i]==u){f=!0;break}f||void 0===u||(a[d]=u,d++)}if(h)for(t=0,o=a.length;t<o;t++)a[t]=s.convert(a[t],h);return a},o.prototype._addItem=function(e){var t=e[this._fieldId];if(void 0!=t){if(this._data[t])throw new Error("Cannot add item: item with id "+t+" already exists")}else t=s.randomUUID(),e[this._fieldId]=t;var i,o,n={},r=Object.keys(e);for(i=0,o=r.length;i<o;i++){var a=r[i],h=this._type[a];n[a]=s.convert(e[a],h)}return this._data[t]=n,this.length++,t},o.prototype._getItem=function(e,t){var i,o,n,r,a=this._data[e];if(!a)return null;var h={},d=Object.keys(a);if(t)for(n=0,r=d.length;n<r;n++)i=d[n],o=a[i],h[i]=s.convert(o,t[i]);else for(n=0,r=d.length;n<r;n++)i=d[n],o=a[i],h[i]=o;return h},o.prototype._updateItem=function(e){var t=e[this._fieldId];if(void 0==t)throw new Error("Cannot update item: item has no id (item: "+JSON.stringify(e)+")");var i=this._data[t];if(!i)throw new Error("Cannot update item: no item with id "+t+" found");for(var o=Object.keys(e),n=0,r=o.length;n<r;n++){var a=o[n],h=this._type[a];i[a]=s.convert(e[a],h)}return t},e.exports=o},function(e,t){function i(e){this.delay=null,this.max=1/0,this._queue=[],this._timeout=null,this._extended=null,this.setOptions(e)}i.prototype.setOptions=function(e){e&&"undefined"!=typeof e.delay&&(this.delay=e.delay),e&&"undefined"!=typeof e.max&&(this.max=e.max),this._flushIfNeeded()},i.extend=function(e,t){var o=new i(t);if(void 0!==e.flush)throw new Error("Target object already has a property flush");e.flush=function(){o.flush()};var n=[{name:"flush",original:void 0}];if(t&&t.replace)for(var s=0;s<t.replace.length;s++){var r=t.replace[s];n.push({name:r,original:e[r]}),o.replace(e,r)}return o._extended={object:e,methods:n},o},i.prototype.destroy=function(){if(this.flush(),this._extended){for(var e=this._extended.object,t=this._extended.methods,i=0;i<t.length;i++){var o=t[i];o.original?e[o.name]=o.original:delete e[o.name]}this._extended=null}},i.prototype.replace=function(e,t){var i=this,o=e[t];if(!o)throw new Error("Method "+t+" undefined");e[t]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];i.queue({args:e,fn:o,context:this})}},i.prototype.queue=function(e){"function"==typeof e?this._queue.push({fn:e}):this._queue.push(e),this._flushIfNeeded()},i.prototype._flushIfNeeded=function(){if(this._queue.length>this.max&&this.flush(),clearTimeout(this._timeout),this.queue.length>0&&"number"==typeof this.delay){var e=this;this._timeout=setTimeout(function(){e.flush()},this.delay)}},i.prototype.flush=function(){for(;this._queue.length>0;){var e=this._queue.shift();e.fn.apply(e.context||e.fn,e.args||[])}},e.exports=i},function(e,t,i){function o(e,t){this._data=null,this._ids={},this.length=0,this._options=t||{},this._fieldId="id",this._subscribers={};var i=this;this.listener=function(){i._onEvent.apply(i,arguments)},this.setData(e)}var n=i(1),s=i(8);o.prototype.setData=function(e){var t,i,o,n;if(this._data&&(this._data.off&&this._data.off("*",this.listener),t=Object.keys(this._ids),this._ids={},this.length=0,this._trigger("remove",{items:t})),this._data=e,this._data){for(this._fieldId=this._options.fieldId||this._data&&this._data.options&&this._data.options.fieldId||"id",t=this._data.getIds({filter:this._options&&this._options.filter}),o=0,n=t.length;o<n;o++)i=t[o],this._ids[i]=!0;this.length=t.length,this._trigger("add",{items:t}),this._data.on&&this._data.on("*",this.listener)}},o.prototype.refresh=function(){var e,t,i,o=this._data.getIds({filter:this._options&&this._options.filter}),n=Object.keys(this._ids),s={},r=[],a=[];for(t=0,i=o.length;t<i;t++)e=o[t],s[e]=!0,this._ids[e]||(r.push(e),this._ids[e]=!0);for(t=0,i=n.length;t<i;t++)e=n[t],s[e]||(a.push(e),delete this._ids[e]);this.length+=r.length-a.length,r.length&&this._trigger("add",{items:r}),a.length&&this._trigger("remove",{items:a})},o.prototype.get=function(e){var t,i,o,s=this,r=n.getType(arguments[0]);"String"==r||"Number"==r||"Array"==r?(t=arguments[0],i=arguments[1],o=arguments[2]):(i=arguments[0],o=arguments[1]);var a=n.extend({},this._options,i);this._options.filter&&i&&i.filter&&(a.filter=function(e){return s._options.filter(e)&&i.filter(e)});var h=[];return void 0!=t&&h.push(t),h.push(a),h.push(o),this._data&&this._data.get.apply(this._data,h)},o.prototype.getIds=function(e){var t;if(this._data){var i,o=this._options.filter;i=e&&e.filter?o?function(t){return o(t)&&e.filter(t)}:e.filter:o,t=this._data.getIds({filter:i,order:e&&e.order})}else t=[];return t},o.prototype.map=function(e,t){var i=[];if(this._data){var o,n=this._options.filter;o=t&&t.filter?n?function(e){return n(e)&&t.filter(e)}:t.filter:n,i=this._data.map(e,{filter:o,order:t&&t.order})}else i=[];return i},o.prototype.getDataSet=function(){for(var e=this;e instanceof o;)e=e._data;return e||null},o.prototype._onEvent=function(e,t,i){var o,n,s,r,a=t&&t.items,h=this._data,d=[],l=[],c=[],u=[];if(a&&h){switch(e){case"add":for(o=0,n=a.length;o<n;o++)s=a[o],r=this.get(s),r&&(this._ids[s]=!0,l.push(s));break;case"update":for(o=0,n=a.length;o<n;o++)s=a[o],r=this.get(s),r?this._ids[s]?(c.push(s),d.push(t.data[o])):(this._ids[s]=!0,l.push(s)):this._ids[s]&&(delete this._ids[s],u.push(s));break;case"remove":for(o=0,n=a.length;o<n;o++)s=a[o],this._ids[s]&&(delete this._ids[s],u.push(s))}this.length+=l.length-u.length,l.length&&this._trigger("add",{items:l},i),c.length&&this._trigger("update",{items:c,data:d},i),u.length&&this._trigger("remove",{items:u},i)}},o.prototype.on=s.prototype.on,o.prototype.off=s.prototype.off,o.prototype._trigger=s.prototype._trigger,o.prototype.subscribe=o.prototype.on,o.prototype.unsubscribe=o.prototype.off,e.exports=o},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t,i){var o=this;if(!(this instanceof n))throw new SyntaxError("Constructor must be called with the new operator");this.options={},this.defaultOptions={locale:"en",locales:Y,clickToUse:!1},A.extend(this.options,this.defaultOptions),this.body={container:e,nodes:{},nodeIndices:[],edges:{},edgeIndices:[],emitter:{on:this.on.bind(this),off:this.off.bind(this),emit:this.emit.bind(this),once:this.once.bind(this)},eventListeners:{onTap:function(){},onTouch:function(){},onDoubleTap:function(){},onHold:function(){},onDragStart:function(){},onDrag:function(){},onDragEnd:function(){},onMouseWheel:function(){},onPinch:function(){},onMouseMove:function(){},onRelease:function(){},onContext:function(){}},data:{nodes:null,edges:null},functions:{createNode:function(){},createEdge:function(){},getPointer:function(){}},modules:{},view:{scale:1,translation:{x:0,y:0}}},this.bindEventListeners(),this.images=new r["default"](function(){return o.body.emitter.emit("_requestRedraw")}),this.groups=new h["default"],this.canvas=new _["default"](this.body),this.selectionHandler=new M["default"](this.body,this.canvas),this.interactionHandler=new O["default"](this.body,this.canvas,this.selectionHandler),this.view=new k["default"](this.body,this.canvas),this.renderer=new b["default"](this.body,this.canvas),this.physics=new p["default"](this.body),this.layoutEngine=new D["default"](this.body),this.clustering=new y["default"](this.body),this.manipulation=new T["default"](this.body,this.canvas,this.selectionHandler),this.nodesHandler=new l["default"](this.body,this.images,this.groups,this.layoutEngine),this.edgesHandler=new u["default"](this.body,this.images,this.groups),this.body.modules.kamadaKawai=new z["default"](this.body,150,.05),this.body.modules.clustering=this.clustering,this.canvas._create(),this.setOptions(i),this.setData(t)}var s=i(12),r=o(s),a=i(13),h=o(a),d=i(14),l=o(d),c=i(35),u=o(c),f=i(44),p=o(f),v=i(53),y=o(v),g=i(56),b=o(g),m=i(57),_=o(m),w=i(62),k=o(w),x=i(63),O=o(x),E=i(67),M=o(E),S=i(68),D=o(S),C=i(69),T=o(C),P=i(70),F=o(P),B=i(34),I=o(B),j=i(72),N=i(73),z=o(N);i(75);var R=i(76),A=i(1),L=(i(8),i(10),i(77)),H=i(78),W=i(79),Y=i(80);R(n.prototype),n.prototype.setOptions=function(e){var t=this;if(void 0!==e){var i=I["default"].validate(e,j.allOptions);i===!0&&console.log("%cErrors have been found in the supplied options object.",B.printStyle);var o=["locale","locales","clickToUse"];if(A.selectiveDeepExtend(o,this.options,e),e=this.layoutEngine.setOptions(e.layout,e),this.canvas.setOptions(e),this.groups.setOptions(e.groups),this.nodesHandler.setOptions(e.nodes),this.edgesHandler.setOptions(e.edges),this.physics.setOptions(e.physics),this.manipulation.setOptions(e.manipulation,e,this.options),this.interactionHandler.setOptions(e.interaction),this.renderer.setOptions(e.interaction),this.selectionHandler.setOptions(e.interaction),void 0!==e.groups&&this.body.emitter.emit("refreshNodes"),"configure"in e&&(this.configurator||(this.configurator=new F["default"](this,this.body.container,j.configureOptions,this.canvas.pixelRatio)),this.configurator.setOptions(e.configure)),this.configurator&&this.configurator.options.enabled===!0){var n={nodes:{},edges:{},layout:{},interaction:{},manipulation:{},physics:{},global:{}};A.deepExtend(n.nodes,this.nodesHandler.options),A.deepExtend(n.edges,this.edgesHandler.options),A.deepExtend(n.layout,this.layoutEngine.options),A.deepExtend(n.interaction,this.selectionHandler.options),A.deepExtend(n.interaction,this.renderer.options),A.deepExtend(n.interaction,this.interactionHandler.options),A.deepExtend(n.manipulation,this.manipulation.options),A.deepExtend(n.physics,this.physics.options),A.deepExtend(n.global,this.canvas.options),A.deepExtend(n.global,this.options),this.configurator.setModuleOptions(n)}void 0!==e.clickToUse?e.clickToUse===!0?void 0===this.activator&&(this.activator=new W(this.canvas.frame),this.activator.on("change",function(){t.body.emitter.emit("activate")})):(void 0!==this.activator&&(this.activator.destroy(),delete this.activator),this.body.emitter.emit("activate")):this.body.emitter.emit("activate"),this.canvas.setSize(),this.body.emitter.emit("startSimulation")}},n.prototype._updateVisibleIndices=function(){var e=this.body.nodes,t=this.body.edges;this.body.nodeIndices=[],this.body.edgeIndices=[];for(var i in e)e.hasOwnProperty(i)&&e[i].options.hidden===!1&&this.body.nodeIndices.push(e[i].id);for(var o in t)t.hasOwnProperty(o)&&t[o].options.hidden===!1&&this.body.edgeIndices.push(t[o].id)},n.prototype.bindEventListeners=function(){var e=this;this.body.emitter.on("_dataChanged",function(){e._updateVisibleIndices(),e.body.emitter.emit("_requestRedraw"),e.body.emitter.emit("_dataUpdated")}),this.body.emitter.on("_dataUpdated",function(){e._updateValueRange(e.body.nodes),e._updateValueRange(e.body.edges),e.body.emitter.emit("startSimulation"),e.body.emitter.emit("_requestRedraw")})},n.prototype.setData=function(e){if(this.body.emitter.emit("resetPhysics"),this.body.emitter.emit("_resetData"),this.selectionHandler.unselectAll(),e&&e.dot&&(e.nodes||e.edges))throw new SyntaxError('Data must contain either parameter "dot" or  parameter pair "nodes" and "edges", but not both.');if(this.setOptions(e&&e.options),e&&e.dot){console.log("The dot property has been depricated. Please use the static convertDot method to convert DOT into vis.network format and use the normal data format with nodes and edges. This converter is used like this: var data = vis.network.convertDot(dotString);");var t=L.DOTToGraph(e.dot);return void this.setData(t)}if(e&&e.gephi){console.log("The gephi property has been depricated. Please use the static convertGephi method to convert gephi into vis.network format and use the normal data format with nodes and edges. This converter is used like this: var data = vis.network.convertGephi(gephiJson);");var i=H.parseGephi(e.gephi);return void this.setData(i)}this.nodesHandler.setData(e&&e.nodes,!0),this.edgesHandler.setData(e&&e.edges,!0),this.body.emitter.emit("_dataChanged"),this.body.emitter.emit("_dataLoaded"),this.body.emitter.emit("initPhysics")},n.prototype.destroy=function(){this.body.emitter.emit("destroy"),this.body.emitter.off(),this.off(),delete this.groups,delete this.canvas,delete this.selectionHandler,delete this.interactionHandler,delete this.view,delete this.renderer,delete this.physics,delete this.layoutEngine,delete this.clustering,delete this.manipulation,delete this.nodesHandler,delete this.edgesHandler,delete this.configurator,delete this.images;for(var e in this.body.nodes)delete this.body.nodes[e];for(var t in this.body.edges)delete this.body.edges[t];A.recursiveDOMDelete(this.body.container)},n.prototype._updateValueRange=function(e){var t,i=void 0,o=void 0,n=0;for(t in e)if(e.hasOwnProperty(t)){var s=e[t].getValue();void 0!==s&&(i=void 0===i?s:Math.min(s,i),o=void 0===o?s:Math.max(s,o),n+=s)}if(void 0!==i&&void 0!==o)for(t in e)e.hasOwnProperty(t)&&e[t].setValueRange(i,o,n)},n.prototype.isActive=function(){return!this.activator||this.activator.active},n.prototype.setSize=function(){return this.canvas.setSize.apply(this.canvas,arguments)},n.prototype.canvasToDOM=function(){return this.canvas.canvasToDOM.apply(this.canvas,arguments)},n.prototype.DOMtoCanvas=function(){return this.canvas.DOMtoCanvas.apply(this.canvas,arguments)},n.prototype.findNode=function(){return this.clustering.findNode.apply(this.clustering,arguments)},n.prototype.isCluster=function(){return this.clustering.isCluster.apply(this.clustering,arguments)},n.prototype.openCluster=function(){return this.clustering.openCluster.apply(this.clustering,arguments)},n.prototype.cluster=function(){return this.clustering.cluster.apply(this.clustering,arguments)},n.prototype.getNodesInCluster=function(){return this.clustering.getNodesInCluster.apply(this.clustering,arguments)},n.prototype.clusterByConnection=function(){return this.clustering.clusterByConnection.apply(this.clustering,arguments)},n.prototype.clusterByHubsize=function(){return this.clustering.clusterByHubsize.apply(this.clustering,arguments)},n.prototype.clusterOutliers=function(){return this.clustering.clusterOutliers.apply(this.clustering,arguments)},n.prototype.getSeed=function(){return this.layoutEngine.getSeed.apply(this.layoutEngine,arguments)},n.prototype.enableEditMode=function(){return this.manipulation.enableEditMode.apply(this.manipulation,arguments)},n.prototype.disableEditMode=function(){return this.manipulation.disableEditMode.apply(this.manipulation,arguments)},n.prototype.addNodeMode=function(){return this.manipulation.addNodeMode.apply(this.manipulation,arguments)},n.prototype.editNode=function(){return this.manipulation.editNode.apply(this.manipulation,arguments)},n.prototype.editNodeMode=function(){return console.log("Deprecated: Please use editNode instead of editNodeMode."),this.manipulation.editNode.apply(this.manipulation,arguments)},n.prototype.addEdgeMode=function(){return this.manipulation.addEdgeMode.apply(this.manipulation,arguments)},n.prototype.editEdgeMode=function(){return this.manipulation.editEdgeMode.apply(this.manipulation,arguments)},n.prototype.deleteSelected=function(){return this.manipulation.deleteSelected.apply(this.manipulation,arguments)},n.prototype.getPositions=function(){return this.nodesHandler.getPositions.apply(this.nodesHandler,arguments)},n.prototype.storePositions=function(){return this.nodesHandler.storePositions.apply(this.nodesHandler,arguments)},n.prototype.moveNode=function(){return this.nodesHandler.moveNode.apply(this.nodesHandler,arguments)},n.prototype.getBoundingBox=function(){return this.nodesHandler.getBoundingBox.apply(this.nodesHandler,arguments)},n.prototype.getConnectedNodes=function(e){return void 0!==this.body.nodes[e]?this.nodesHandler.getConnectedNodes.apply(this.nodesHandler,arguments):this.edgesHandler.getConnectedNodes.apply(this.edgesHandler,arguments)},n.prototype.getConnectedEdges=function(){return this.nodesHandler.getConnectedEdges.apply(this.nodesHandler,arguments)},n.prototype.startSimulation=function(){return this.physics.startSimulation.apply(this.physics,arguments)},n.prototype.stopSimulation=function(){return this.physics.stopSimulation.apply(this.physics,arguments)},n.prototype.stabilize=function(){return this.physics.stabilize.apply(this.physics,arguments)},n.prototype.getSelection=function(){return this.selectionHandler.getSelection.apply(this.selectionHandler,arguments)},n.prototype.setSelection=function(){return this.selectionHandler.setSelection.apply(this.selectionHandler,arguments)},n.prototype.getSelectedNodes=function(){return this.selectionHandler.getSelectedNodes.apply(this.selectionHandler,arguments)},n.prototype.getSelectedEdges=function(){return this.selectionHandler.getSelectedEdges.apply(this.selectionHandler,arguments)},n.prototype.getNodeAt=function(){var e=this.selectionHandler.getNodeAt.apply(this.selectionHandler,arguments);return void 0!==e&&void 0!==e.id?e.id:e},n.prototype.getEdgeAt=function(){var e=this.selectionHandler.getEdgeAt.apply(this.selectionHandler,arguments);return void 0!==e&&void 0!==e.id?e.id:e},n.prototype.selectNodes=function(){return this.selectionHandler.selectNodes.apply(this.selectionHandler,arguments)},n.prototype.selectEdges=function(){return this.selectionHandler.selectEdges.apply(this.selectionHandler,arguments)},n.prototype.unselectAll=function(){this.selectionHandler.unselectAll.apply(this.selectionHandler,arguments),this.redraw()},n.prototype.redraw=function(){return this.renderer.redraw.apply(this.renderer,arguments)},n.prototype.getScale=function(){return this.view.getScale.apply(this.view,arguments)},n.prototype.getViewPosition=function(){return this.view.getViewPosition.apply(this.view,arguments)},n.prototype.fit=function(){return this.view.fit.apply(this.view,arguments)},n.prototype.moveTo=function(){return this.view.moveTo.apply(this.view,arguments)},n.prototype.focus=function(){return this.view.focus.apply(this.view,arguments)},n.prototype.releaseNode=function(){return this.view.releaseNode.apply(this.view,arguments)},n.prototype.getOptionsFromConfigurator=function(){var e={};return this.configurator&&(e=this.configurator.getOptions.apply(this.configurator)),e},e.exports=n},function(e,t){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),n=function(){function e(t){i(this,e),this.images={},this.imageBroken={},this.callback=t}return o(e,[{key:"_addImageToCache",value:function(e,t){0===t.width&&(document.body.appendChild(t),t.width=t.offsetWidth,t.height=t.offsetHeight,document.body.removeChild(t)),this.images[e]=t}},{key:"_tryloadBrokenUrl",value:function(e,t,i){var o=this;void 0!==e&&void 0!==t&&void 0!==i&&(i.onerror=function(){console.error("Could not load brokenImage:",t),o._addImageToCache(e,new Image)},i.src=t)}},{key:"_redrawWithImage",value:function(e){this.callback&&this.callback(e)}},{key:"load",value:function(e,t,i){var o=this,n=this.images[e];if(n)return n;var s=new Image;return s.onload=function(){o._addImageToCache(e,s),o._redrawWithImage(s)},s.onerror=function(){console.error("Could not load image:",e),o._tryloadBrokenUrl(e,t,s)},s.src=e,s}}]),e}();t["default"]=n},function(e,t,i){function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),s=i(1),r=function(){function e(){o(this,e),this.clear(),this.defaultIndex=0,this.groupsArray=[],this.groupIndex=0,this.defaultGroups=[{border:"#2B7CE9",background:"#97C2FC",highlight:{border:"#2B7CE9",background:"#D2E5FF"},hover:{border:"#2B7CE9",background:"#D2E5FF"}},{border:"#FFA500",background:"#FFFF00",highlight:{border:"#FFA500",background:"#FFFFA3"},hover:{border:"#FFA500",background:"#FFFFA3"}},{border:"#FA0A10",background:"#FB7E81",highlight:{border:"#FA0A10",background:"#FFAFB1"},hover:{border:"#FA0A10",background:"#FFAFB1"}},{border:"#41A906",background:"#7BE141",highlight:{border:"#41A906",background:"#A1EC76"},hover:{border:"#41A906",background:"#A1EC76"}},{border:"#E129F0",background:"#EB7DF4",highlight:{border:"#E129F0",background:"#F0B3F5"},hover:{border:"#E129F0",background:"#F0B3F5"}},{border:"#7C29F0",background:"#AD85E4",highlight:{border:"#7C29F0",background:"#D3BDF0"},hover:{border:"#7C29F0",background:"#D3BDF0"}},{border:"#C37F00",background:"#FFA807",highlight:{border:"#C37F00",background:"#FFCA66"},hover:{border:"#C37F00",background:"#FFCA66"}},{border:"#4220FB",background:"#6E6EFD",highlight:{border:"#4220FB",background:"#9B9BFD"},hover:{border:"#4220FB",background:"#9B9BFD"}},{border:"#FD5A77",background:"#FFC0CB",highlight:{border:"#FD5A77",background:"#FFD1D9"},hover:{border:"#FD5A77",background:"#FFD1D9"}},{border:"#4AD63A",background:"#C2FABC",highlight:{border:"#4AD63A",background:"#E6FFE3"},hover:{border:"#4AD63A",background:"#E6FFE3"}},{border:"#990000",background:"#EE0000",highlight:{border:"#BB0000",background:"#FF3333"},hover:{border:"#BB0000",background:"#FF3333"}},{border:"#FF6000",background:"#FF6000",highlight:{border:"#FF6000",background:"#FF6000"},hover:{border:"#FF6000",background:"#FF6000"}},{border:"#97C2FC",background:"#2B7CE9",highlight:{border:"#D2E5FF",background:"#2B7CE9"},hover:{border:"#D2E5FF",background:"#2B7CE9"}},{border:"#399605",background:"#255C03",highlight:{border:"#399605",background:"#255C03"},hover:{border:"#399605",background:"#255C03"}},{border:"#B70054",background:"#FF007E",highlight:{border:"#B70054",background:"#FF007E"},hover:{border:"#B70054",background:"#FF007E"}},{border:"#AD85E4",background:"#7C29F0",highlight:{border:"#D3BDF0",background:"#7C29F0"},hover:{border:"#D3BDF0",background:"#7C29F0"}},{border:"#4557FA",background:"#000EA1",highlight:{border:"#6E6EFD",background:"#000EA1"},hover:{border:"#6E6EFD",background:"#000EA1"}},{border:"#FFC0CB",background:"#FD5A77",highlight:{border:"#FFD1D9",background:"#FD5A77"},hover:{border:"#FFD1D9",background:"#FD5A77"}},{border:"#C2FABC",background:"#74D66A",highlight:{border:"#E6FFE3",background:"#74D66A"},hover:{border:"#E6FFE3",background:"#74D66A"}},{border:"#EE0000",background:"#990000",highlight:{border:"#FF3333",background:"#BB0000"},hover:{border:"#FF3333",background:"#BB0000"}}],this.options={},this.defaultOptions={useDefaultGroups:!0},s.extend(this.options,this.defaultOptions)}return n(e,[{key:"setOptions",value:function(e){var t=["useDefaultGroups"];if(void 0!==e)for(var i in e)if(e.hasOwnProperty(i)&&t.indexOf(i)===-1){var o=e[i];this.add(i,o)}}},{key:"clear",value:function(){this.groups={},this.groupsArray=[]}},{key:"get",value:function(e){var t=this.groups[e];if(void 0===t)if(this.options.useDefaultGroups===!1&&this.groupsArray.length>0){var i=this.groupIndex%this.groupsArray.length;this.groupIndex++,t={},
t.color=this.groups[this.groupsArray[i]],this.groups[e]=t}else{var o=this.defaultIndex%this.defaultGroups.length;this.defaultIndex++,t={},t.color=this.defaultGroups[o],this.groups[e]=t}return t}},{key:"add",value:function(e,t){return this.groups[e]=t,this.groupsArray.push(e),t}}]),e}();t["default"]=r},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),r=i(15),a=o(r),h=i(16),d=o(h),l=i(1),c=i(8),u=i(10),f=function(){function e(t,i,o,s){var r=this;n(this,e),this.body=t,this.images=i,this.groups=o,this.layoutEngine=s,this.body.functions.createNode=this.create.bind(this),this.nodesListeners={add:function(e,t){r.add(t.items)},update:function(e,t){r.update(t.items,t.data)},remove:function(e,t){r.remove(t.items)}},this.options={},this.defaultOptions={borderWidth:1,borderWidthSelected:2,brokenImage:void 0,color:{border:"#2B7CE9",background:"#97C2FC",highlight:{border:"#2B7CE9",background:"#D2E5FF"},hover:{border:"#2B7CE9",background:"#D2E5FF"}},fixed:{x:!1,y:!1},font:{color:"#343434",size:14,face:"arial",background:"none",strokeWidth:0,strokeColor:"#ffffff",align:"center"},group:void 0,hidden:!1,icon:{face:"FontAwesome",code:void 0,size:50,color:"#2B7CE9"},image:void 0,label:void 0,labelHighlightBold:!0,level:void 0,mass:1,physics:!0,scaling:{min:10,max:30,label:{enabled:!1,min:14,max:30,maxVisible:30,drawThreshold:5},customScalingFunction:function(e,t,i,o){if(t===e)return.5;var n=1/(t-e);return Math.max(0,(o-e)*n)}},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:10,x:5,y:5},shape:"ellipse",shapeProperties:{borderDashes:!1,borderRadius:6,interpolation:!0,useImageSize:!1,useBorderWithImage:!1},size:25,title:void 0,value:void 0,x:void 0,y:void 0},l.extend(this.options,this.defaultOptions),this.bindEventListeners()}return s(e,[{key:"bindEventListeners",value:function(){var e=this;this.body.emitter.on("refreshNodes",this.refresh.bind(this)),this.body.emitter.on("refresh",this.refresh.bind(this)),this.body.emitter.on("destroy",function(){l.forEach(e.nodesListeners,function(t,i){e.body.data.nodes&&e.body.data.nodes.off(i,t)}),delete e.body.functions.createNode,delete e.nodesListeners.add,delete e.nodesListeners.update,delete e.nodesListeners.remove,delete e.nodesListeners})}},{key:"setOptions",value:function(e){if(void 0!==e){if(a["default"].parseOptions(this.options,e),void 0!==e.shape)for(var t in this.body.nodes)this.body.nodes.hasOwnProperty(t)&&this.body.nodes[t].updateShape();if(void 0!==e.font){d["default"].parseOptions(this.options.font,e);for(var i in this.body.nodes)this.body.nodes.hasOwnProperty(i)&&(this.body.nodes[i].updateLabelModule(),this.body.nodes[i]._reset())}if(void 0!==e.size)for(var o in this.body.nodes)this.body.nodes.hasOwnProperty(o)&&this.body.nodes[o]._reset();void 0===e.hidden&&void 0===e.physics||this.body.emitter.emit("_dataChanged")}}},{key:"setData",value:function(e){var t=this,i=!(arguments.length<=1||void 0===arguments[1])&&arguments[1],o=this.body.data.nodes;if(e instanceof c||e instanceof u)this.body.data.nodes=e;else if(Array.isArray(e))this.body.data.nodes=new c,this.body.data.nodes.add(e);else{if(e)throw new TypeError("Array or DataSet expected");this.body.data.nodes=new c}o&&l.forEach(this.nodesListeners,function(e,t){o.off(t,e)}),this.body.nodes={},this.body.data.nodes&&!function(){var e=t;l.forEach(t.nodesListeners,function(t,i){e.body.data.nodes.on(i,t)});var i=t.body.data.nodes.getIds();t.add(i,!0)}(),i===!1&&this.body.emitter.emit("_dataChanged")}},{key:"add",value:function(e){for(var t=!(arguments.length<=1||void 0===arguments[1])&&arguments[1],i=void 0,o=[],n=0;n<e.length;n++){i=e[n];var s=this.body.data.nodes.get(i),r=this.create(s);o.push(r),this.body.nodes[i]=r}this.layoutEngine.positionInitially(o),t===!1&&this.body.emitter.emit("_dataChanged")}},{key:"update",value:function(e,t){for(var i=this.body.nodes,o=!1,n=0;n<e.length;n++){var s=e[n],r=i[s],a=t[n];void 0!==r?o=r.setOptions(a):(o=!0,r=this.create(a),i[s]=r)}o===!0?this.body.emitter.emit("_dataChanged"):this.body.emitter.emit("_dataUpdated")}},{key:"remove",value:function(e){for(var t=this.body.nodes,i=0;i<e.length;i++){var o=e[i];delete t[o]}this.body.emitter.emit("_dataChanged")}},{key:"create",value:function(e){var t=arguments.length<=1||void 0===arguments[1]?a["default"]:arguments[1];return new t(e,this.body,this.images,this.groups,this.options)}},{key:"refresh",value:function(){var e=!(arguments.length<=0||void 0===arguments[0])&&arguments[0],t=this.body.nodes;for(var i in t){var o=void 0;t.hasOwnProperty(i)&&(o=t[i]);var n=this.body.data.nodes._data[i];void 0!==o&&void 0!==n&&(e===!0&&o.setOptions({x:null,y:null}),o.setOptions({fixed:!1}),o.setOptions(n))}}},{key:"getPositions",value:function(e){var t={};if(void 0!==e){if(Array.isArray(e)===!0){for(var i=0;i<e.length;i++)if(void 0!==this.body.nodes[e[i]]){var o=this.body.nodes[e[i]];t[e[i]]={x:Math.round(o.x),y:Math.round(o.y)}}}else if(void 0!==this.body.nodes[e]){var n=this.body.nodes[e];t[e]={x:Math.round(n.x),y:Math.round(n.y)}}}else for(var s=0;s<this.body.nodeIndices.length;s++){var r=this.body.nodes[this.body.nodeIndices[s]];t[this.body.nodeIndices[s]]={x:Math.round(r.x),y:Math.round(r.y)}}return t}},{key:"storePositions",value:function(){var e=[],t=this.body.data.nodes.getDataSet();for(var i in t._data)if(t._data.hasOwnProperty(i)){var o=this.body.nodes[i];t._data[i].x==Math.round(o.x)&&t._data[i].y==Math.round(o.y)||e.push({id:o.id,x:Math.round(o.x),y:Math.round(o.y)})}t.update(e)}},{key:"getBoundingBox",value:function(e){if(void 0!==this.body.nodes[e])return this.body.nodes[e].shape.boundingBox}},{key:"getConnectedNodes",value:function(e){var t=[];if(void 0!==this.body.nodes[e])for(var i=this.body.nodes[e],o={},n=0;n<i.edges.length;n++){var s=i.edges[n];s.toId==i.id?void 0===o[s.fromId]&&(t.push(s.fromId),o[s.fromId]=!0):s.fromId==i.id&&void 0===o[s.toId]&&(t.push(s.toId),o[s.toId]=!0)}return t}},{key:"getConnectedEdges",value:function(e){var t=[];if(void 0!==this.body.nodes[e])for(var i=this.body.nodes[e],o=0;o<i.edges.length;o++)t.push(i.edges[o].id);else console.log("NodeId provided for getConnectedEdges does not exist. Provided: ",e);return t}},{key:"moveNode",value:function(e,t,i){var o=this;void 0!==this.body.nodes[e]?(this.body.nodes[e].x=Number(t),this.body.nodes[e].y=Number(i),setTimeout(function(){o.body.emitter.emit("startSimulation")},0)):console.log("Node id supplied to moveNode does not exist. Provided: ",e)}}]),e}();t["default"]=f},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),r=i(16),a=o(r),h=i(17),d=o(h),l=i(19),c=o(l),u=i(21),f=o(u),p=i(22),v=o(p),y=i(23),g=o(y),b=i(25),m=o(b),_=i(26),w=o(_),k=i(27),x=o(k),O=i(28),E=o(O),M=i(29),S=o(M),D=i(30),C=o(D),T=i(31),P=o(T),F=i(32),B=o(F),I=i(33),j=o(I),N=i(34),z=(o(N),i(1)),R=function(){function e(t,i,o,s,r){n(this,e),this.options=z.bridgeObject(r),this.globalOptions=r,this.body=i,this.edges=[],this.id=void 0,this.imagelist=o,this.grouplist=s,this.x=void 0,this.y=void 0,this.baseSize=this.options.size,this.baseFontSize=this.options.font.size,this.predefinedPosition=!1,this.selected=!1,this.hover=!1,this.labelModule=new a["default"](this.body,this.options,(!1)),this.setOptions(t)}return s(e,[{key:"attachEdge",value:function(e){this.edges.indexOf(e)===-1&&this.edges.push(e)}},{key:"detachEdge",value:function(e){var t=this.edges.indexOf(e);t!=-1&&this.edges.splice(t,1)}},{key:"setOptions",value:function(t){var i=this.options.shape;if(t){if(void 0!==t.id&&(this.id=t.id),void 0===this.id)throw"Node must have an id";if(void 0!==t.x&&(null===t.x?(this.x=void 0,this.predefinedPosition=!1):(this.x=parseInt(t.x),this.predefinedPosition=!0)),void 0!==t.y&&(null===t.y?(this.y=void 0,this.predefinedPosition=!1):(this.y=parseInt(t.y),this.predefinedPosition=!0)),void 0!==t.size&&(this.baseSize=t.size),void 0!==t.value&&(t.value=parseFloat(t.value)),"number"==typeof t.group||"string"==typeof t.group&&""!=t.group){var o=this.grouplist.get(t.group);z.deepExtend(this.options,o),this.options.color=z.parseColor(this.options.color)}if(e.parseOptions(this.options,t,!0,this.globalOptions),void 0!==this.options.image){if(!this.imagelist)throw"No imagelist provided";this.imageObj=this.imagelist.load(this.options.image,this.options.brokenImage,this.id)}return this.updateLabelModule(),this.updateShape(i),void 0!==t.hidden||void 0!==t.physics}}},{key:"updateLabelModule",value:function(){void 0!==this.options.label&&null!==this.options.label||(this.options.label=""),this.labelModule.setOptions(this.options,!0),void 0!==this.labelModule.baseSize&&(this.baseFontSize=this.labelModule.baseSize)}},{key:"updateShape",value:function(e){if(e===this.options.shape&&this.shape)this.shape.setOptions(this.options,this.imageObj);else switch(this.options.shape){case"box":this.shape=new d["default"](this.options,this.body,this.labelModule);break;case"circle":this.shape=new c["default"](this.options,this.body,this.labelModule);break;case"circularImage":this.shape=new f["default"](this.options,this.body,this.labelModule,this.imageObj);break;case"database":this.shape=new v["default"](this.options,this.body,this.labelModule);break;case"diamond":this.shape=new g["default"](this.options,this.body,this.labelModule);break;case"dot":this.shape=new m["default"](this.options,this.body,this.labelModule);break;case"ellipse":this.shape=new w["default"](this.options,this.body,this.labelModule);break;case"icon":this.shape=new x["default"](this.options,this.body,this.labelModule);break;case"image":this.shape=new E["default"](this.options,this.body,this.labelModule,this.imageObj);break;case"square":this.shape=new S["default"](this.options,this.body,this.labelModule);break;case"star":this.shape=new C["default"](this.options,this.body,this.labelModule);break;case"text":this.shape=new P["default"](this.options,this.body,this.labelModule);break;case"triangle":this.shape=new B["default"](this.options,this.body,this.labelModule);break;case"triangleDown":this.shape=new j["default"](this.options,this.body,this.labelModule);break;default:this.shape=new w["default"](this.options,this.body,this.labelModule)}this._reset()}},{key:"select",value:function(){this.selected=!0,this._reset()}},{key:"unselect",value:function(){this.selected=!1,this._reset()}},{key:"_reset",value:function(){this.shape.width=void 0,this.shape.height=void 0}},{key:"getTitle",value:function(){return this.options.title}},{key:"distanceToBorder",value:function(e,t){return this.shape.distanceToBorder(e,t)}},{key:"isFixed",value:function(){return this.options.fixed.x&&this.options.fixed.y}},{key:"isSelected",value:function(){return this.selected}},{key:"getValue",value:function(){return this.options.value}},{key:"setValueRange",value:function(e,t,i){if(void 0!==this.options.value){var o=this.options.scaling.customScalingFunction(e,t,i,this.options.value),n=this.options.scaling.max-this.options.scaling.min;if(this.options.scaling.label.enabled===!0){var s=this.options.scaling.label.max-this.options.scaling.label.min;this.options.font.size=this.options.scaling.label.min+o*s}this.options.size=this.options.scaling.min+o*n}else this.options.size=this.baseSize,this.options.font.size=this.baseFontSize;this.updateLabelModule()}},{key:"draw",value:function(e){this.shape.draw(e,this.x,this.y,this.selected,this.hover)}},{key:"updateBoundingBox",value:function(e){this.shape.updateBoundingBox(this.x,this.y,e)}},{key:"resize",value:function(e){this.shape.resize(e,this.selected)}},{key:"isOverlappingWith",value:function(e){return this.shape.left<e.right&&this.shape.left+this.shape.width>e.left&&this.shape.top<e.bottom&&this.shape.top+this.shape.height>e.top}},{key:"isBoundingBoxOverlappingWith",value:function(e){return this.shape.boundingBox.left<e.right&&this.shape.boundingBox.right>e.left&&this.shape.boundingBox.top<e.bottom&&this.shape.boundingBox.bottom>e.top}}],[{key:"parseOptions",value:function(e,t){var i=!(arguments.length<=2||void 0===arguments[2])&&arguments[2],o=arguments.length<=3||void 0===arguments[3]?{}:arguments[3],n=["color","font","fixed","shadow"];if(z.selectiveNotDeepExtend(n,e,t,i),z.mergeOptions(e,t,"shadow",i,o),void 0!==t.color&&null!==t.color){var s=z.parseColor(t.color);z.fillIfDefined(e.color,s)}else i===!0&&null===t.color&&(e.color=z.bridgeObject(o.color));void 0!==t.fixed&&null!==t.fixed&&("boolean"==typeof t.fixed?(e.fixed.x=t.fixed,e.fixed.y=t.fixed):(void 0!==t.fixed.x&&"boolean"==typeof t.fixed.x&&(e.fixed.x=t.fixed.x),void 0!==t.fixed.y&&"boolean"==typeof t.fixed.y&&(e.fixed.y=t.fixed.y))),void 0!==t.font&&null!==t.font?a["default"].parseOptions(e.font,t):i===!0&&null===t.font&&(e.font=z.bridgeObject(o.font)),void 0!==t.scaling&&z.mergeOptions(e.scaling,t.scaling,"label",i,o.scaling)}}]),e}();t["default"]=R},function(e,t,i){function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){var i=[],o=!0,n=!1,s=void 0;try{for(var r,a=e[Symbol.iterator]();!(o=(r=a.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(h){n=!0,s=h}finally{try{!o&&a["return"]&&a["return"]()}finally{if(n)throw s}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},r=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),a=i(1),h=function(){function e(t,i){var n=!(arguments.length<=2||void 0===arguments[2])&&arguments[2];o(this,e),this.body=t,this.pointToSelf=!1,this.baseSize=void 0,this.fontOptions={},this.setOptions(i),this.size={top:0,left:0,width:0,height:0,yLine:0},this.isEdgeLabel=n}return r(e,[{key:"setOptions",value:function(t){var i=!(arguments.length<=1||void 0===arguments[1])&&arguments[1];this.nodeOptions=t,this.fontOptions=a.deepExtend({},t.font,!0),void 0!==t.label&&(this.labelDirty=!0),void 0!==t.font&&(e.parseOptions(this.fontOptions,t,i),"string"==typeof t.font?this.baseSize=this.fontOptions.size:"object"===s(t.font)&&void 0!==t.font.size&&(this.baseSize=t.font.size))}},{key:"draw",value:function(e,t,i,o){var n=arguments.length<=4||void 0===arguments[4]?"middle":arguments[4];if(void 0!==this.nodeOptions.label){var s=this.fontOptions.size*this.body.view.scale;this.nodeOptions.label&&s<this.nodeOptions.scaling.label.drawThreshold-1||(this.calculateLabelSize(e,o,t,i,n),this._drawBackground(e),this._drawText(e,o,t,i,n))}}},{key:"_drawBackground",value:function(e){if(void 0!==this.fontOptions.background&&"none"!==this.fontOptions.background){e.fillStyle=this.fontOptions.background;var t=2;if(this.isEdgeLabel)switch(this.fontOptions.align){case"middle":e.fillRect(.5*-this.size.width,.5*-this.size.height,this.size.width,this.size.height);break;case"top":e.fillRect(.5*-this.size.width,-(this.size.height+t),this.size.width,this.size.height);break;case"bottom":e.fillRect(.5*-this.size.width,t,this.size.width,this.size.height);break;default:e.fillRect(this.size.left,this.size.top-.5*t,this.size.width,this.size.height)}else e.fillRect(this.size.left,this.size.top-.5*t,this.size.width,this.size.height)}}},{key:"_drawText",value:function(e,t,i,o){var s=arguments.length<=4||void 0===arguments[4]?"middle":arguments[4],r=this.fontOptions.size,a=r*this.body.view.scale;a>=this.nodeOptions.scaling.label.maxVisible&&(r=Number(this.nodeOptions.scaling.label.maxVisible)/this.body.view.scale);var h=this.size.yLine,d=this._getColor(a),l=n(d,2),c=l[0],u=l[1],f=this._setAlignment(e,i,h,s),p=n(f,2);i=p[0],h=p[1],e.font=(t&&this.nodeOptions.labelHighlightBold?"bold ":"")+r+"px "+this.fontOptions.face,e.fillStyle=c,this.isEdgeLabel||"left"!==this.fontOptions.align?e.textAlign="center":(e.textAlign=this.fontOptions.align,i-=.5*this.size.width),this.fontOptions.strokeWidth>0&&(e.lineWidth=this.fontOptions.strokeWidth,e.strokeStyle=u,e.lineJoin="round");for(var v=0;v<this.lineCount;v++)this.fontOptions.strokeWidth>0&&e.strokeText(this.lines[v],i,h),e.fillText(this.lines[v],i,h),h+=r}},{key:"_setAlignment",value:function(e,t,i,o){if(this.isEdgeLabel&&"horizontal"!==this.fontOptions.align&&this.pointToSelf===!1){t=0,i=0;var n=2;"top"===this.fontOptions.align?(e.textBaseline="alphabetic",i-=2*n):"bottom"===this.fontOptions.align?(e.textBaseline="hanging",i+=2*n):e.textBaseline="middle"}else e.textBaseline=o;return[t,i]}},{key:"_getColor",value:function(e){var t=this.fontOptions.color||"#000000",i=this.fontOptions.strokeColor||"#ffffff";if(e<=this.nodeOptions.scaling.label.drawThreshold){var o=Math.max(0,Math.min(1,1-(this.nodeOptions.scaling.label.drawThreshold-e)));t=a.overrideOpacity(t,o),i=a.overrideOpacity(i,o)}return[t,i]}},{key:"getTextSize",value:function(e){var t=!(arguments.length<=1||void 0===arguments[1])&&arguments[1],i={width:this._processLabel(e,t),height:this.fontOptions.size*this.lineCount,lineCount:this.lineCount};return i}},{key:"calculateLabelSize",value:function(e,t){var i=arguments.length<=2||void 0===arguments[2]?0:arguments[2],o=arguments.length<=3||void 0===arguments[3]?0:arguments[3],n=arguments.length<=4||void 0===arguments[4]?"middle":arguments[4];this.labelDirty===!0&&(this.size.width=this._processLabel(e,t)),this.size.height=this.fontOptions.size*this.lineCount,this.size.left=i-.5*this.size.width,this.size.top=o-.5*this.size.height,this.size.yLine=o+.5*(1-this.lineCount)*this.fontOptions.size,"hanging"===n&&(this.size.top+=.5*this.fontOptions.size,this.size.top+=4,this.size.yLine+=4),this.labelDirty=!1}},{key:"_processLabel",value:function(e,t){var i=0,o=[""],n=0;if(void 0!==this.nodeOptions.label){o=String(this.nodeOptions.label).split("\n"),n=o.length,e.font=(t&&this.nodeOptions.labelHighlightBold?"bold ":"")+this.fontOptions.size+"px "+this.fontOptions.face,i=e.measureText(o[0]).width;for(var s=1;s<n;s++){var r=e.measureText(o[s]).width;i=r>i?r:i}}return this.lines=o,this.lineCount=n,i}}],[{key:"parseOptions",value:function(e,t){var i=!(arguments.length<=2||void 0===arguments[2])&&arguments[2];if("string"==typeof t.font){var o=t.font.split(" ");e.size=o[0].replace("px",""),e.face=o[1],e.color=o[2]}else"object"===s(t.font)&&a.fillIfDefined(e,t.font,i);e.size=Number(e.size)}}]),e}();t["default"]=h},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(18),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(e,t){if(void 0===this.width){var i=5,o=this.labelModule.getTextSize(e,t);this.width=o.width+2*i,this.height=o.height+2*i,this.radius=.5*this.width}}},{key:"draw",value:function(e,t,i,o,n){this.resize(e,o),this.left=t-this.width/2,this.top=i-this.height/2;var s=this.options.borderWidth,r=this.options.borderWidthSelected||2*this.options.borderWidth;e.strokeStyle=o?this.options.color.highlight.border:n?this.options.color.hover.border:this.options.color.border,e.lineWidth=o?r:s,e.lineWidth/=this.body.view.scale,e.lineWidth=Math.min(this.width,e.lineWidth),e.fillStyle=o?this.options.color.highlight.background:n?this.options.color.hover.background:this.options.color.background;var a=this.options.shapeProperties.borderRadius;e.roundRect(this.left,this.top,this.width,this.height,a),this.enableShadow(e),e.fill(),this.disableShadow(e),e.save(),s>0&&(this.enableBorderDashes(e),e.stroke(),this.disableBorderDashes(e)),e.restore(),this.updateBoundingBox(t,i,e,o),this.labelModule.draw(e,t,i,o)}},{key:"updateBoundingBox",value:function(e,t,i,o){this.resize(i,o),this.left=e-.5*this.width,this.top=t-.5*this.height;var n=this.options.shapeProperties.borderRadius;this.boundingBox.left=this.left-n,this.boundingBox.top=this.top-n,this.boundingBox.bottom=this.top+this.height+n,this.boundingBox.right=this.left+this.width+n}},{key:"distanceToBorder",value:function(e,t){this.resize(e);var i=this.options.borderWidth;return Math.min(Math.abs(this.width/2/Math.cos(t)),Math.abs(this.height/2/Math.sin(t)))+i}}]),t}(d["default"]);t["default"]=l},function(e,t){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),n=function(){function e(t,o,n){i(this,e),this.body=o,this.labelModule=n,this.setOptions(t),this.top=void 0,this.left=void 0,this.height=void 0,this.width=void 0,this.radius=void 0,this.boundingBox={top:0,left:0,right:0,bottom:0}}return o(e,[{key:"setOptions",value:function(e){this.options=e}},{key:"_distanceToBorder",value:function(e,t){var i=this.options.borderWidth;return this.resize(e),Math.min(Math.abs(this.width/2/Math.cos(t)),Math.abs(this.height/2/Math.sin(t)))+i}},{key:"enableShadow",value:function(e){this.options.shadow.enabled===!0&&(e.shadowColor=this.options.shadow.color,e.shadowBlur=this.options.shadow.size,e.shadowOffsetX=this.options.shadow.x,e.shadowOffsetY=this.options.shadow.y)}},{key:"disableShadow",value:function(e){this.options.shadow.enabled===!0&&(e.shadowColor="rgba(0,0,0,0)",e.shadowBlur=0,e.shadowOffsetX=0,e.shadowOffsetY=0)}},{key:"enableBorderDashes",value:function(e){if(this.options.shapeProperties.borderDashes!==!1)if(void 0!==e.setLineDash){var t=this.options.shapeProperties.borderDashes;t===!0&&(t=[5,15]),e.setLineDash(t)}else console.warn("setLineDash is not supported in this browser. The dashed borders cannot be used."),this.options.shapeProperties.borderDashes=!1}},{key:"disableBorderDashes",value:function(e){this.options.shapeProperties.borderDashes!==!1&&(void 0!==e.setLineDash?e.setLineDash([0]):(console.warn("setLineDash is not supported in this browser. The dashed borders cannot be used."),this.options.shapeProperties.borderDashes=!1))}}]),e}();t["default"]=n},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(20),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(e,t){if(void 0===this.width){var i=5,o=this.labelModule.getTextSize(e,t),n=Math.max(o.width,o.height)+2*i;this.options.size=n/2,this.width=n,this.height=n,this.radius=.5*this.width}}},{key:"draw",value:function(e,t,i,o,n){this.resize(e,o),this.left=t-this.width/2,this.top=i-this.height/2,this._drawRawCircle(e,t,i,o,n,this.options.size),this.boundingBox.top=i-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=i+this.options.size,this.updateBoundingBox(t,i),this.labelModule.draw(e,t,i,o)}},{key:"updateBoundingBox",value:function(e,t){this.boundingBox.top=t-this.options.size,this.boundingBox.left=e-this.options.size,this.boundingBox.right=e+this.options.size,this.boundingBox.bottom=t+this.options.size}},{key:"distanceToBorder",value:function(e,t){return this.resize(e),.5*this.width}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(18),d=o(h),l=function(e){function t(e,i,o){n(this,t);var r=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o));return r.labelOffset=0,r.imageLoaded=!1,r}return r(t,e),a(t,[{key:"setOptions",value:function(e,t){this.options=e,t&&(this.imageObj=t)}},{key:"_resizeImage",value:function(){var e=!1;if(this.imageObj.width&&this.imageObj.height?this.imageLoaded===!1&&(this.imageLoaded=!0,e=!0):this.imageLoaded=!1,!this.width||!this.height||e===!0){var t,i,o;this.imageObj.width&&this.imageObj.height&&(t=0,i=0),this.options.shapeProperties.useImageSize===!1?this.imageObj.width>this.imageObj.height?(o=this.imageObj.width/this.imageObj.height,t=2*this.options.size*o||this.imageObj.width,i=2*this.options.size||this.imageObj.height):(o=this.imageObj.width&&this.imageObj.height?this.imageObj.height/this.imageObj.width:1,t=2*this.options.size,i=2*this.options.size*o):(t=this.imageObj.width,i=this.imageObj.height),this.width=t,this.height=i,this.radius=.5*this.width}}},{key:"_drawRawCircle",value:function(e,t,i,o,n,s){var r=this.options.borderWidth,a=this.options.borderWidthSelected||2*this.options.borderWidth,h=(o?a:r)/this.body.view.scale;e.lineWidth=Math.min(this.width,h),e.strokeStyle=o?this.options.color.highlight.border:n?this.options.color.hover.border:this.options.color.border,e.fillStyle=o?this.options.color.highlight.background:n?this.options.color.hover.background:this.options.color.background,e.circle(t,i,s),this.enableShadow(e),e.fill(),this.disableShadow(e),e.save(),h>0&&(this.enableBorderDashes(e),e.stroke(),this.disableBorderDashes(e)),e.restore()}},{key:"_drawImageAtPosition",value:function(e){if(0!=this.imageObj.width){e.globalAlpha=1,this.enableShadow(e);var t=this.imageObj.width/this.width/this.body.view.scale;if(t>2&&this.options.shapeProperties.interpolation===!0){var i=this.imageObj.width,o=this.imageObj.height,n=document.createElement("canvas");n.width=i,n.height=i;var s=n.getContext("2d");t*=.5,i*=.5,o*=.5,s.drawImage(this.imageObj,0,0,i,o);for(var r=0,a=1;t>2&&a<4;)s.drawImage(n,r,0,i,o,r+i,0,i/2,o/2),r+=i,t*=.5,i*=.5,o*=.5,a+=1;e.drawImage(n,r,0,i,o,this.left,this.top,this.width,this.height)}else e.drawImage(this.imageObj,this.left,this.top,this.width,this.height);this.disableShadow(e)}}},{key:"_drawImageLabel",value:function(e,t,i,o){var n,s=0;if(void 0!==this.height){s=.5*this.height;var r=this.labelModule.getTextSize(e);r.lineCount>=1&&(s+=r.height/2)}n=i+s,this.options.label&&(this.labelOffset=s),this.labelModule.draw(e,t,n,o,"hanging")}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(20),d=o(h),l=function(e){function t(e,i,o,r){n(this,t);var a=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o));return a.imageObj=r,a._swapToImageResizeWhenImageLoaded=!0,a}return r(t,e),a(t,[{key:"resize",value:function(){if(void 0===this.imageObj.src||void 0===this.imageObj.width||void 0===this.imageObj.height){if(!this.width){var e=2*this.options.size;this.width=e,this.height=e,this._swapToImageResizeWhenImageLoaded=!0,this.radius=.5*this.width}}else this._swapToImageResizeWhenImageLoaded&&(this.width=void 0,this.height=void 0,this._swapToImageResizeWhenImageLoaded=!1),this._resizeImage()}},{key:"draw",value:function(e,t,i,o,n){this.resize(),this.left=t-this.width/2,this.top=i-this.height/2;var s=Math.min(.5*this.height,.5*this.width);this._drawRawCircle(e,t,i,o,n,s),e.save(),e.clip(),this._drawImageAtPosition(e),e.restore(),this._drawImageLabel(e,t,i,o),this.updateBoundingBox(t,i)}},{key:"updateBoundingBox",value:function(e,t){this.boundingBox.top=t-this.options.size,this.boundingBox.left=e-this.options.size,this.boundingBox.right=e+this.options.size,this.boundingBox.bottom=t+this.options.size,this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelOffset)}},{key:"distanceToBorder",value:function(e,t){return this.resize(e),.5*this.width}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function");
}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(18),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(e,t){if(void 0===this.width){var i=5,o=this.labelModule.getTextSize(e,t),n=o.width+2*i;this.width=n,this.height=n,this.radius=.5*this.width}}},{key:"draw",value:function(e,t,i,o,n){this.resize(e,o),this.left=t-this.width/2,this.top=i-this.height/2;var s=this.options.borderWidth,r=this.options.borderWidthSelected||2*this.options.borderWidth,a=(o?r:s)/this.body.view.scale;e.lineWidth=Math.min(this.width,a),e.strokeStyle=o?this.options.color.highlight.border:n?this.options.color.hover.border:this.options.color.border,e.fillStyle=o?this.options.color.highlight.background:n?this.options.color.hover.background:this.options.color.background,e.database(t-this.width/2,i-.5*this.height,this.width,this.height),this.enableShadow(e),e.fill(),this.disableShadow(e),e.save(),a>0&&(this.enableBorderDashes(e),e.stroke(),this.disableBorderDashes(e)),e.restore(),this.updateBoundingBox(t,i,e,o),this.labelModule.draw(e,t,i,o)}},{key:"updateBoundingBox",value:function(e,t,i,o){this.resize(i,o),this.left=e-.5*this.width,this.top=t-.5*this.height,this.boundingBox.left=this.left,this.boundingBox.top=this.top,this.boundingBox.bottom=this.top+this.height,this.boundingBox.right=this.left+this.width}},{key:"distanceToBorder",value:function(e,t){return this._distanceToBorder(e,t)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(24),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(e){this._resizeShape()}},{key:"draw",value:function(e,t,i,o,n){this._drawShape(e,"diamond",4,t,i,o,n)}},{key:"distanceToBorder",value:function(e,t){return this._distanceToBorder(e,t)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(18),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"_resizeShape",value:function(){if(void 0===this.width){var e=2*this.options.size;this.width=e,this.height=e,this.radius=.5*this.width}}},{key:"_drawShape",value:function(e,t,i,o,n,s,r){this._resizeShape(),this.left=o-this.width/2,this.top=n-this.height/2;var a=this.options.borderWidth,h=this.options.borderWidthSelected||2*this.options.borderWidth,d=(s?h:a)/this.body.view.scale;if(e.lineWidth=Math.min(this.width,d),e.strokeStyle=s?this.options.color.highlight.border:r?this.options.color.hover.border:this.options.color.border,e.fillStyle=s?this.options.color.highlight.background:r?this.options.color.hover.background:this.options.color.background,e[t](o,n,this.options.size),this.enableShadow(e),e.fill(),this.disableShadow(e),e.save(),d>0&&(this.enableBorderDashes(e),e.stroke(),this.disableBorderDashes(e)),e.restore(),void 0!==this.options.label){var l=n+.5*this.height+3;this.labelModule.draw(e,o,l,s,"hanging")}this.updateBoundingBox(o,n)}},{key:"updateBoundingBox",value:function(e,t){this.boundingBox.top=t-this.options.size,this.boundingBox.left=e-this.options.size,this.boundingBox.right=e+this.options.size,this.boundingBox.bottom=t+this.options.size,void 0!==this.options.label&&this.labelModule.size.width>0&&(this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelModule.size.height+3))}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(24),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(e){this._resizeShape()}},{key:"draw",value:function(e,t,i,o,n){this._drawShape(e,"circle",2,t,i,o,n)}},{key:"distanceToBorder",value:function(e,t){return this.resize(e),this.options.size}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(18),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(e,t){if(void 0===this.width){var i=this.labelModule.getTextSize(e,t);this.width=1.5*i.width,this.height=2*i.height,this.width<this.height&&(this.width=this.height),this.radius=.5*this.width}}},{key:"draw",value:function(e,t,i,o,n){this.resize(e,o),this.left=t-.5*this.width,this.top=i-.5*this.height;var s=this.options.borderWidth,r=this.options.borderWidthSelected||2*this.options.borderWidth,a=(o?r:s)/this.body.view.scale;e.lineWidth=Math.min(this.width,a),e.strokeStyle=o?this.options.color.highlight.border:n?this.options.color.hover.border:this.options.color.border,e.fillStyle=o?this.options.color.highlight.background:n?this.options.color.hover.background:this.options.color.background,e.ellipse(this.left,this.top,this.width,this.height),this.enableShadow(e),e.fill(),this.disableShadow(e),e.save(),a>0&&(this.enableBorderDashes(e),e.stroke(),this.disableBorderDashes(e)),e.restore(),this.updateBoundingBox(t,i,e,o),this.labelModule.draw(e,t,i,o)}},{key:"updateBoundingBox",value:function(e,t,i,o){this.resize(i,o),this.left=e-.5*this.width,this.top=t-.5*this.height,this.boundingBox.left=this.left,this.boundingBox.top=this.top,this.boundingBox.bottom=this.top+this.height,this.boundingBox.right=this.left+this.width}},{key:"distanceToBorder",value:function(e,t){this.resize(e);var i=.5*this.width,o=.5*this.height,n=Math.sin(t)*i,s=Math.cos(t)*o;return i*o/Math.sqrt(n*n+s*s)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(18),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(e){if(void 0===this.width){var t=5,i={width:Number(this.options.icon.size),height:Number(this.options.icon.size)};this.width=i.width+2*t,this.height=i.height+2*t,this.radius=.5*this.width}}},{key:"draw",value:function(e,t,i,o,n){if(this.resize(e),this.options.icon.size=this.options.icon.size||50,this.left=t-.5*this.width,this.top=i-.5*this.height,this._icon(e,t,i,o),void 0!==this.options.label){var s=5;this.labelModule.draw(e,t,i+.5*this.height+s,o)}this.updateBoundingBox(t,i)}},{key:"updateBoundingBox",value:function(e,t){if(this.boundingBox.top=t-.5*this.options.icon.size,this.boundingBox.left=e-.5*this.options.icon.size,this.boundingBox.right=e+.5*this.options.icon.size,this.boundingBox.bottom=t+.5*this.options.icon.size,void 0!==this.options.label&&this.labelModule.size.width>0){var i=5;this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelModule.size.height+i)}}},{key:"_icon",value:function(e,t,i,o){var n=Number(this.options.icon.size);void 0!==this.options.icon.code?(e.font=(o?"bold ":"")+n+"px "+this.options.icon.face,e.fillStyle=this.options.icon.color||"black",e.textAlign="center",e.textBaseline="middle",this.enableShadow(e),e.fillText(this.options.icon.code,t,i),this.disableShadow(e)):console.error("When using the icon shape, you need to define the code in the icon options object. This can be done per node or globally.")}},{key:"distanceToBorder",value:function(e,t){return this._distanceToBorder(e,t)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(20),d=o(h),l=function(e){function t(e,i,o,r){n(this,t);var a=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o));return a.imageObj=r,a}return r(t,e),a(t,[{key:"resize",value:function(){this._resizeImage()}},{key:"draw",value:function(e,t,i,o,n){if(this.resize(),this.left=t-this.width/2,this.top=i-this.height/2,this.options.shapeProperties.useBorderWithImage===!0){var s=this.options.borderWidth,r=this.options.borderWidthSelected||2*this.options.borderWidth,a=(o?r:s)/this.body.view.scale;e.lineWidth=Math.min(this.width,a),e.beginPath(),e.strokeStyle=o?this.options.color.highlight.border:n?this.options.color.hover.border:this.options.color.border,e.fillStyle=o?this.options.color.highlight.background:n?this.options.color.hover.background:this.options.color.background,e.rect(this.left-.5*e.lineWidth,this.top-.5*e.lineWidth,this.width+e.lineWidth,this.height+e.lineWidth),e.fill(),e.save(),a>0&&(this.enableBorderDashes(e),e.stroke(),this.disableBorderDashes(e)),e.restore(),e.closePath()}this._drawImageAtPosition(e),this._drawImageLabel(e,t,i,o||n),this.updateBoundingBox(t,i)}},{key:"updateBoundingBox",value:function(e,t){this.resize(),this.left=e-this.width/2,this.top=t-this.height/2,this.boundingBox.top=this.top,this.boundingBox.left=this.left,this.boundingBox.right=this.left+this.width,this.boundingBox.bottom=this.top+this.height,void 0!==this.options.label&&this.labelModule.size.width>0&&(this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelOffset))}},{key:"distanceToBorder",value:function(e,t){return this._distanceToBorder(e,t)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(24),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(){this._resizeShape()}},{key:"draw",value:function(e,t,i,o,n){this._drawShape(e,"square",2,t,i,o,n)}},{key:"distanceToBorder",value:function(e,t){return this._distanceToBorder(e,t)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(24),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(e){this._resizeShape()}},{key:"draw",value:function(e,t,i,o,n){this._drawShape(e,"star",4,t,i,o,n)}},{key:"distanceToBorder",value:function(e,t){return this._distanceToBorder(e,t)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(18),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(e,t){if(void 0===this.width){var i=5,o=this.labelModule.getTextSize(e,t);this.width=o.width+2*i,this.height=o.height+2*i,this.radius=.5*this.width}}},{key:"draw",value:function(e,t,i,o,n){this.resize(e,o||n),this.left=t-this.width/2,this.top=i-this.height/2,this.enableShadow(e),this.labelModule.draw(e,t,i,o||n),this.disableShadow(e),this.updateBoundingBox(t,i,e,o)}},{key:"updateBoundingBox",value:function(e,t,i,o){this.resize(i,o),this.left=e-this.width/2,this.top=t-this.height/2,this.boundingBox.top=this.top,this.boundingBox.left=this.left,this.boundingBox.right=this.left+this.width,this.boundingBox.bottom=this.top+this.height}},{key:"distanceToBorder",value:function(e,t){return this._distanceToBorder(e,t)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(24),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(e){this._resizeShape()}},{key:"draw",value:function(e,t,i,o,n){this._drawShape(e,"triangle",3,t,i,o,n)}},{key:"distanceToBorder",value:function(e,t){return this._distanceToBorder(e,t)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(24),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"resize",value:function(e){this._resizeShape()}},{key:"draw",value:function(e,t,i,o,n){this._drawShape(e,"triangleDown",3,t,i,o,n)}},{key:"distanceToBorder",value:function(e,t){return this._distanceToBorder(e,t)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},s=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),r=i(1),a=!1,h=void 0,d="background: #FFeeee; color: #dd0000",l=function(){function e(){o(this,e)}return s(e,null,[{key:"validate",value:function(t,i,o){a=!1,h=i;var n=i;return void 0!==o&&(n=i[o]),e.parse(t,n,[]),a}},{key:"parse",value:function(t,i,o){for(var n in t)t.hasOwnProperty(n)&&e.check(n,t,i,o)}},{key:"check",value:function(t,i,o,n){void 0===o[t]&&void 0===o.__any__?e.getSuggestion(t,o,n):void 0===o[t]&&void 0!==o.__any__?"object"===e.getType(i[t])&&void 0!==o.__any__.__type__?e.checkFields(t,i,o,"__any__",o.__any__.__type__,n):e.checkFields(t,i,o,"__any__",o.__any__,n):void 0!==o[t].__type__?e.checkFields(t,i,o,t,o[t].__type__,n):e.checkFields(t,i,o,t,o[t],n)}},{key:"checkFields",value:function(t,i,o,n,s,h){var l=e.getType(i[t]),c=s[l];void 0!==c?"array"===e.getType(c)&&c.indexOf(i[t])===-1?(console.log('%cInvalid option detected in "'+t+'". Allowed values are:'+e.print(c)+' not "'+i[t]+'". '+e.printLocation(h,t),d),a=!0):"object"===l&&"__any__"!==n&&(h=r.copyAndExtendArray(h,t),e.parse(i[t],o[n],h)):void 0===s.any&&(console.log('%cInvalid type received for "'+t+'". Expected: '+e.print(Object.keys(s))+". Received ["+l+'] "'+i[t]+'"'+e.printLocation(h,t),d),a=!0)}},{key:"getType",value:function(e){var t="undefined"==typeof e?"undefined":n(e);return"object"===t?null===e?"null":e instanceof Boolean?"boolean":e instanceof Number?"number":e instanceof String?"string":Array.isArray(e)?"array":e instanceof Date?"date":void 0!==e.nodeType?"dom":e._isAMomentObject===!0?"moment":"object":"number"===t?"number":"boolean"===t?"boolean":"string"===t?"string":void 0===t?"undefined":t}},{key:"getSuggestion",value:function(t,i,o){var n=e.findInOptions(t,i,o,!1),s=e.findInOptions(t,h,[],!0),r=8,l=4;void 0!==n.indexMatch?console.log('%cUnknown option detected: "'+t+'" in '+e.printLocation(n.path,t,"")+'Perhaps it was incomplete? Did you mean: "'+n.indexMatch+'"?\n\n',d):s.distance<=l&&n.distance>s.distance?console.log('%cUnknown option detected: "'+t+'" in '+e.printLocation(n.path,t,"")+"Perhaps it was misplaced? Matching option found at: "+e.printLocation(s.path,s.closestMatch,""),d):n.distance<=r?console.log('%cUnknown option detected: "'+t+'". Did you mean "'+n.closestMatch+'"?'+e.printLocation(n.path,t),d):console.log('%cUnknown option detected: "'+t+'". Did you mean one of these: '+e.print(Object.keys(i))+e.printLocation(o,t),d),a=!0}},{key:"findInOptions",value:function(t,i,o){var n=!(arguments.length<=3||void 0===arguments[3])&&arguments[3],s=1e9,a="",h=[],d=t.toLowerCase(),l=void 0;for(var c in i){var u=void 0;if(void 0!==i[c].__type__&&n===!0){var f=e.findInOptions(t,i[c],r.copyAndExtendArray(o,c));s>f.distance&&(a=f.closestMatch,h=f.path,s=f.distance,l=f.indexMatch)}else c.toLowerCase().indexOf(d)!==-1&&(l=c),u=e.levenshteinDistance(t,c),s>u&&(a=c,h=r.copyArray(o),s=u)}return{closestMatch:a,path:h,distance:s,indexMatch:l}}},{key:"printLocation",value:function(e,t){for(var i=arguments.length<=2||void 0===arguments[2]?"Problem value found at: \n":arguments[2],o="\n\n"+i+"options = {\n",n=0;n<e.length;n++){for(var s=0;s<n+1;s++)o+="  ";o+=e[n]+": {\n"}for(var r=0;r<e.length+1;r++)o+="  ";o+=t+"\n";for(var a=0;a<e.length+1;a++){for(var h=0;h<e.length-a;h++)o+="  ";o+="}\n"}return o+"\n\n"}},{key:"print",value:function(e){return JSON.stringify(e).replace(/(\")|(\[)|(\])|(,"__type__")/g,"").replace(/(\,)/g,", ")}},{key:"levenshteinDistance",value:function(e,t){if(0===e.length)return t.length;if(0===t.length)return e.length;var i,o=[];for(i=0;i<=t.length;i++)o[i]=[i];var n;for(n=0;n<=e.length;n++)o[0][n]=n;for(i=1;i<=t.length;i++)for(n=1;n<=e.length;n++)t.charAt(i-1)==e.charAt(n-1)?o[i][n]=o[i-1][n-1]:o[i][n]=Math.min(o[i-1][n-1]+1,Math.min(o[i][n-1]+1,o[i-1][n]+1));return o[t.length][e.length]}}]),e}();t["default"]=l,t.printStyle=d},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),r=i(36),a=o(r),h=i(16),d=o(h),l=i(1),c=i(8),u=i(10),f=function(){function e(t,i,o){var s=this;n(this,e),this.body=t,this.images=i,this.groups=o,this.body.functions.createEdge=this.create.bind(this),this.edgesListeners={add:function(e,t){s.add(t.items)},update:function(e,t){s.update(t.items)},remove:function(e,t){s.remove(t.items)}},this.options={},this.defaultOptions={arrows:{to:{enabled:!1,scaleFactor:1},middle:{enabled:!1,scaleFactor:1},from:{enabled:!1,scaleFactor:1}},arrowStrikethrough:!0,color:{color:"#848484",highlight:"#848484",hover:"#848484",inherit:"from",opacity:1},dashes:!1,font:{color:"#343434",size:14,face:"arial",background:"none",strokeWidth:2,strokeColor:"#ffffff",align:"horizontal"},hidden:!1,hoverWidth:1.5,label:void 0,labelHighlightBold:!0,length:void 0,physics:!0,scaling:{min:1,max:15,label:{enabled:!0,min:14,max:30,maxVisible:30,drawThreshold:5},customScalingFunction:function(e,t,i,o){if(t===e)return.5;var n=1/(t-e);return Math.max(0,(o-e)*n)}},selectionWidth:1.5,selfReferenceSize:20,shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:10,x:5,y:5},smooth:{enabled:!0,type:"dynamic",forceDirection:"none",roundness:.5},title:void 0,width:1,value:void 0},l.extend(this.options,this.defaultOptions),this.bindEventListeners()}return s(e,[{key:"bindEventListeners",value:function(){var e=this;this.body.emitter.on("_forceDisableDynamicCurves",function(t){"dynamic"===t&&(t="continuous");var i=!1;for(var o in e.body.edges)if(e.body.edges.hasOwnProperty(o)){var n=e.body.edges[o],s=e.body.data.edges._data[o];if(void 0!==s){var r=s.smooth;void 0!==r&&r.enabled===!0&&"dynamic"===r.type&&(void 0===t?n.setOptions({smooth:!1}):n.setOptions({smooth:{type:t}}),i=!0)}}i===!0&&e.body.emitter.emit("_dataChanged")}),this.body.emitter.on("_dataUpdated",function(){e.reconnectEdges(),e.markAllEdgesAsDirty()}),this.body.emitter.on("refreshEdges",this.refresh.bind(this)),this.body.emitter.on("refresh",this.refresh.bind(this)),this.body.emitter.on("destroy",function(){l.forEach(e.edgesListeners,function(t,i){e.body.data.edges&&e.body.data.edges.off(i,t)}),delete e.body.functions.createEdge,delete e.edgesListeners.add,delete e.edgesListeners.update,delete e.edgesListeners.remove,delete e.edgesListeners})}},{key:"setOptions",value:function(e){if(void 0!==e){a["default"].parseOptions(this.options,e),void 0!==e.color&&this.markAllEdgesAsDirty();var t=!1;if(void 0!==e.smooth)for(var i in this.body.edges)this.body.edges.hasOwnProperty(i)&&(t=this.body.edges[i].updateEdgeType()||t);if(void 0!==e.font){d["default"].parseOptions(this.options.font,e);for(var o in this.body.edges)this.body.edges.hasOwnProperty(o)&&this.body.edges[o].updateLabelModule()}void 0===e.hidden&&void 0===e.physics&&t!==!0||this.body.emitter.emit("_dataChanged")}}},{key:"setData",value:function(e){var t=this,i=!(arguments.length<=1||void 0===arguments[1])&&arguments[1],o=this.body.data.edges;if(e instanceof c||e instanceof u)this.body.data.edges=e;else if(Array.isArray(e))this.body.data.edges=new c,this.body.data.edges.add(e);else{if(e)throw new TypeError("Array or DataSet expected");this.body.data.edges=new c}if(o&&l.forEach(this.edgesListeners,function(e,t){o.off(t,e)}),this.body.edges={},this.body.data.edges){l.forEach(this.edgesListeners,function(e,i){t.body.data.edges.on(i,e)});var n=this.body.data.edges.getIds();this.add(n,!0)}i===!1&&this.body.emitter.emit("_dataChanged")}},{key:"add",value:function(e){for(var t=!(arguments.length<=1||void 0===arguments[1])&&arguments[1],i=this.body.edges,o=this.body.data.edges,n=0;n<e.length;n++){var s=e[n],r=i[s];r&&r.disconnect();var a=o.get(s,{showInternalIds:!0});i[s]=this.create(a)}t===!1&&this.body.emitter.emit("_dataChanged")}},{key:"update",value:function(e){for(var t=this.body.edges,i=this.body.data.edges,o=!1,n=0;n<e.length;n++){var s=e[n],r=i.get(s),a=t[s];void 0!==a?(a.disconnect(),o=a.setOptions(r)||o,a.connect()):(this.body.edges[s]=this.create(r),o=!0)}o===!0?this.body.emitter.emit("_dataChanged"):this.body.emitter.emit("_dataUpdated")}},{key:"remove",value:function(e){for(var t=this.body.edges,i=0;i<e.length;i++){var o=e[i],n=t[o];void 0!==n&&(n.cleanup(),n.disconnect(),delete t[o])}this.body.emitter.emit("_dataChanged")}},{
key:"refresh",value:function(){var e=this.body.edges;for(var t in e){var i=void 0;e.hasOwnProperty(t)&&(i=e[t]);var o=this.body.data.edges._data[t];void 0!==i&&void 0!==o&&i.setOptions(o)}}},{key:"create",value:function(e){return new a["default"](e,this.body,this.options)}},{key:"markAllEdgesAsDirty",value:function(){for(var e in this.body.edges)this.body.edges[e].edgeType.colorDirty=!0}},{key:"reconnectEdges",value:function(){var e,t=this.body.nodes,i=this.body.edges;for(e in t)t.hasOwnProperty(e)&&(t[e].edges=[]);for(e in i)if(i.hasOwnProperty(e)){var o=i[e];o.from=null,o.to=null,o.connect()}}},{key:"getConnectedNodes",value:function(e){var t=[];if(void 0!==this.body.edges[e]){var i=this.body.edges[e];i.fromId&&t.push(i.fromId),i.toId&&t.push(i.toId)}return t}}]),e}();t["default"]=f},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},r=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),a=i(16),h=o(a),d=i(37),l=o(d),c=i(41),u=o(c),f=i(42),p=o(f),v=i(43),y=o(v),g=i(1),b=function(){function e(t,i,o){if(n(this,e),void 0===i)throw"No body provided";this.options=g.bridgeObject(o),this.globalOptions=o,this.body=i,this.id=void 0,this.fromId=void 0,this.toId=void 0,this.selected=!1,this.hover=!1,this.labelDirty=!0,this.colorDirty=!0,this.baseWidth=this.options.width,this.baseFontSize=this.options.font.size,this.from=void 0,this.to=void 0,this.edgeType=void 0,this.connected=!1,this.labelModule=new h["default"](this.body,this.options,(!0)),this.setOptions(t)}return r(e,[{key:"setOptions",value:function(t){if(t){this.colorDirty=!0,e.parseOptions(this.options,t,!0,this.globalOptions),void 0!==t.id&&(this.id=t.id),void 0!==t.from&&(this.fromId=t.from),void 0!==t.to&&(this.toId=t.to),void 0!==t.title&&(this.title=t.title),void 0!==t.value&&(t.value=parseFloat(t.value)),this.updateLabelModule();var i=this.updateEdgeType();return this._setInteractionWidths(),this.connect(),void 0===t.hidden&&void 0===t.physics||(i=!0),i}}},{key:"updateLabelModule",value:function(){this.labelModule.setOptions(this.options,!0),void 0!==this.labelModule.baseSize&&(this.baseFontSize=this.labelModule.baseSize)}},{key:"updateEdgeType",value:function(){var e=!1,t=!0,i=this.options.smooth;return void 0!==this.edgeType&&(this.edgeType instanceof u["default"]&&i.enabled===!0&&"dynamic"===i.type&&(t=!1),this.edgeType instanceof l["default"]&&i.enabled===!0&&"cubicBezier"===i.type&&(t=!1),this.edgeType instanceof p["default"]&&i.enabled===!0&&"dynamic"!==i.type&&"cubicBezier"!==i.type&&(t=!1),this.edgeType instanceof y["default"]&&i.enabled===!1&&(t=!1),t===!0&&(e=this.cleanup())),t===!0?this.options.smooth.enabled===!0?"dynamic"===this.options.smooth.type?(e=!0,this.edgeType=new u["default"](this.options,this.body,this.labelModule)):"cubicBezier"===this.options.smooth.type?this.edgeType=new l["default"](this.options,this.body,this.labelModule):this.edgeType=new p["default"](this.options,this.body,this.labelModule):this.edgeType=new y["default"](this.options,this.body,this.labelModule):this.edgeType.setOptions(this.options),e}},{key:"connect",value:function(){this.disconnect(),this.from=this.body.nodes[this.fromId]||void 0,this.to=this.body.nodes[this.toId]||void 0,this.connected=void 0!==this.from&&void 0!==this.to,this.connected===!0?(this.from.attachEdge(this),this.to.attachEdge(this)):(this.from&&this.from.detachEdge(this),this.to&&this.to.detachEdge(this)),this.edgeType.connect()}},{key:"disconnect",value:function(){this.from&&(this.from.detachEdge(this),this.from=void 0),this.to&&(this.to.detachEdge(this),this.to=void 0),this.connected=!1}},{key:"getTitle",value:function(){return this.title}},{key:"isSelected",value:function(){return this.selected}},{key:"getValue",value:function(){return this.options.value}},{key:"setValueRange",value:function(e,t,i){if(void 0!==this.options.value){var o=this.options.scaling.customScalingFunction(e,t,i,this.options.value),n=this.options.scaling.max-this.options.scaling.min;if(this.options.scaling.label.enabled===!0){var s=this.options.scaling.label.max-this.options.scaling.label.min;this.options.font.size=this.options.scaling.label.min+o*s}this.options.width=this.options.scaling.min+o*n}else this.options.width=this.baseWidth,this.options.font.size=this.baseFontSize;this._setInteractionWidths(),this.updateLabelModule()}},{key:"_setInteractionWidths",value:function(){"function"==typeof this.options.hoverWidth?this.edgeType.hoverWidth=this.options.hoverWidth(this.options.width):this.edgeType.hoverWidth=this.options.hoverWidth+this.options.width,"function"==typeof this.options.selectionWidth?this.edgeType.selectionWidth=this.options.selectionWidth(this.options.width):this.edgeType.selectionWidth=this.options.selectionWidth+this.options.width}},{key:"draw",value:function(e){var t=this.edgeType.getViaNode(),i={};this.edgeType.fromPoint=this.edgeType.from,this.edgeType.toPoint=this.edgeType.to,this.options.arrows.from.enabled===!0&&(i.from=this.edgeType.getArrowData(e,"from",t,this.selected,this.hover),this.options.arrowStrikethrough===!1&&(this.edgeType.fromPoint=i.from.core)),this.options.arrows.to.enabled===!0&&(i.to=this.edgeType.getArrowData(e,"to",t,this.selected,this.hover),this.options.arrowStrikethrough===!1&&(this.edgeType.toPoint=i.to.core)),this.options.arrows.middle.enabled===!0&&(i.middle=this.edgeType.getArrowData(e,"middle",t,this.selected,this.hover)),this.edgeType.drawLine(e,this.selected,this.hover,t),this.drawArrows(e,i),this.drawLabel(e,t)}},{key:"drawArrows",value:function(e,t){this.options.arrows.from.enabled===!0&&this.edgeType.drawArrowHead(e,this.selected,this.hover,t.from),this.options.arrows.middle.enabled===!0&&this.edgeType.drawArrowHead(e,this.selected,this.hover,t.middle),this.options.arrows.to.enabled===!0&&this.edgeType.drawArrowHead(e,this.selected,this.hover,t.to)}},{key:"drawLabel",value:function(e,t){if(void 0!==this.options.label){var i=this.from,o=this.to,n=this.from.selected||this.to.selected||this.selected;if(i.id!=o.id){this.labelModule.pointToSelf=!1;var s=this.edgeType.getPoint(.5,t);e.save(),"horizontal"!==this.options.font.align&&(this.labelModule.calculateLabelSize(e,n,s.x,s.y),e.translate(s.x,this.labelModule.size.yLine),this._rotateForLabelAlignment(e)),this.labelModule.draw(e,s.x,s.y,n),e.restore()}else{this.labelModule.pointToSelf=!0;var r,a,h=this.options.selfReferenceSize;i.shape.width>i.shape.height?(r=i.x+.5*i.shape.width,a=i.y-h):(r=i.x+h,a=i.y-.5*i.shape.height),s=this._pointOnCircle(r,a,h,.125),this.labelModule.draw(e,s.x,s.y,n)}}}},{key:"isOverlappingWith",value:function(e){if(this.connected){var t=10,i=this.from.x,o=this.from.y,n=this.to.x,s=this.to.y,r=e.left,a=e.top,h=this.edgeType.getDistanceToEdge(i,o,n,s,r,a);return h<t}return!1}},{key:"_rotateForLabelAlignment",value:function(e){var t=this.from.y-this.to.y,i=this.from.x-this.to.x,o=Math.atan2(t,i);(o<-1&&i<0||o>0&&i<0)&&(o+=Math.PI),e.rotate(o)}},{key:"_pointOnCircle",value:function(e,t,i,o){var n=2*o*Math.PI;return{x:e+i*Math.cos(n),y:t-i*Math.sin(n)}}},{key:"select",value:function(){this.selected=!0}},{key:"unselect",value:function(){this.selected=!1}},{key:"cleanup",value:function(){return this.edgeType.cleanup()}}],[{key:"parseOptions",value:function(e,t){var i=!(arguments.length<=2||void 0===arguments[2])&&arguments[2],o=arguments.length<=3||void 0===arguments[3]?{}:arguments[3],n=["arrowStrikethrough","id","from","hidden","hoverWidth","label","labelHighlightBold","length","line","opacity","physics","scaling","selectionWidth","selfReferenceSize","to","title","value","width"];if(g.selectiveDeepExtend(n,e,t,i),g.mergeOptions(e,t,"smooth",i,o),g.mergeOptions(e,t,"shadow",i,o),void 0!==t.dashes&&null!==t.dashes?e.dashes=t.dashes:i===!0&&null===t.dashes&&(e.dashes=Object.create(o.dashes)),void 0!==t.scaling&&null!==t.scaling?(void 0!==t.scaling.min&&(e.scaling.min=t.scaling.min),void 0!==t.scaling.max&&(e.scaling.max=t.scaling.max),g.mergeOptions(e.scaling,t.scaling,"label",i,o.scaling)):i===!0&&null===t.scaling&&(e.scaling=Object.create(o.scaling)),void 0!==t.arrows&&null!==t.arrows)if("string"==typeof t.arrows){var r=t.arrows.toLowerCase();e.arrows.to.enabled=r.indexOf("to")!=-1,e.arrows.middle.enabled=r.indexOf("middle")!=-1,e.arrows.from.enabled=r.indexOf("from")!=-1}else{if("object"!==s(t.arrows))throw new Error("The arrow newOptions can only be an object or a string. Refer to the documentation. You used:"+JSON.stringify(t.arrows));g.mergeOptions(e.arrows,t.arrows,"to",i,o.arrows),g.mergeOptions(e.arrows,t.arrows,"middle",i,o.arrows),g.mergeOptions(e.arrows,t.arrows,"from",i,o.arrows)}else i===!0&&null===t.arrows&&(e.arrows=Object.create(o.arrows));if(void 0!==t.color&&null!==t.color)if(e.color=g.deepExtend({},e.color,!0),g.isString(t.color))e.color.color=t.color,e.color.highlight=t.color,e.color.hover=t.color,e.color.inherit=!1;else{var a=!1;void 0!==t.color.color&&(e.color.color=t.color.color,a=!0),void 0!==t.color.highlight&&(e.color.highlight=t.color.highlight,a=!0),void 0!==t.color.hover&&(e.color.hover=t.color.hover,a=!0),void 0!==t.color.inherit&&(e.color.inherit=t.color.inherit),void 0!==t.color.opacity&&(e.color.opacity=Math.min(1,Math.max(0,t.color.opacity))),void 0===t.color.inherit&&a===!0&&(e.color.inherit=!1)}else i===!0&&null===t.color&&(e.color=g.bridgeObject(o.color));void 0!==t.font&&null!==t.font?h["default"].parseOptions(e.font,t):i===!0&&null===t.font&&(e.font=g.bridgeObject(o.font))}}]),e}();t["default"]=b},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){var i=[],o=!0,n=!1,s=void 0;try{for(var r,a=e[Symbol.iterator]();!(o=(r=a.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(h){n=!0,s=h}finally{try{!o&&a["return"]&&a["return"]()}finally{if(n)throw s}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),h=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),d=i(38),l=o(d),c=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),h(t,[{key:"_line",value:function(e,t){var i=t[0],o=t[1];e.beginPath(),e.moveTo(this.fromPoint.x,this.fromPoint.y),void 0===t||void 0===i.x?e.lineTo(this.toPoint.x,this.toPoint.y):e.bezierCurveTo(i.x,i.y,o.x,o.y,this.toPoint.x,this.toPoint.y),this.enableShadow(e),e.stroke(),this.disableShadow(e)}},{key:"_getViaCoordinates",value:function(){var e=this.from.x-this.to.x,t=this.from.y-this.to.y,i=void 0,o=void 0,n=void 0,s=void 0,r=this.options.smooth.roundness;return(Math.abs(e)>Math.abs(t)||this.options.smooth.forceDirection===!0||"horizontal"===this.options.smooth.forceDirection)&&"vertical"!==this.options.smooth.forceDirection?(o=this.from.y,s=this.to.y,i=this.from.x-r*e,n=this.to.x+r*e):(o=this.from.y-r*t,s=this.to.y+r*t,i=this.from.x,n=this.to.x),[{x:i,y:o},{x:n,y:s}]}},{key:"getViaNode",value:function(){return this._getViaCoordinates()}},{key:"_findBorderPosition",value:function(e,t){return this._findBorderPositionBezier(e,t)}},{key:"_getDistanceToEdge",value:function(e,t,i,o,n,s){var r=arguments.length<=6||void 0===arguments[6]?this._getViaCoordinates():arguments[6],h=a(r,2),d=h[0],l=h[1];return this._getDistanceToBezierEdge(e,t,i,o,n,s,d,l)}},{key:"getPoint",value:function(e){var t=arguments.length<=1||void 0===arguments[1]?this._getViaCoordinates():arguments[1],i=a(t,2),o=i[0],n=i[1],s=e,r=[];r[0]=Math.pow(1-s,3),r[1]=3*s*Math.pow(1-s,2),r[2]=3*Math.pow(s,2)*(1-s),r[3]=Math.pow(s,3);var h=r[0]*this.fromPoint.x+r[1]*o.x+r[2]*n.x+r[3]*this.toPoint.x,d=r[0]*this.fromPoint.y+r[1]*o.y+r[2]*n.y+r[3]*this.toPoint.y;return{x:h,y:d}}}]),t}(l["default"]);t["default"]=c},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(39),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"_getDistanceToBezierEdge",value:function(e,t,i,o,n,s,r,a){var h=1e9,d=void 0,l=void 0,c=void 0,u=void 0,f=void 0,p=e,v=t,y=[0,0,0,0];for(l=1;l<10;l++)c=.1*l,y[0]=Math.pow(1-c,3),y[1]=3*c*Math.pow(1-c,2),y[2]=3*Math.pow(c,2)*(1-c),y[3]=Math.pow(c,3),u=y[0]*e+y[1]*r.x+y[2]*a.x+y[3]*i,f=y[0]*t+y[1]*r.y+y[2]*a.y+y[3]*o,l>0&&(d=this._getDistanceToLine(p,v,u,f,n,s),h=d<h?d:h),p=u,v=f;return h}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(40),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"_findBorderPositionBezier",value:function(e,t){var i,o,n,s,r,a=arguments.length<=2||void 0===arguments[2]?this._getViaCoordinates():arguments[2],h=10,d=0,l=0,c=1,u=.2,f=this.to,p=!1;for(e.id===this.from.id&&(f=this.from,p=!0);l<=c&&d<h;){var v=.5*(l+c);if(i=this.getPoint(v,a),o=Math.atan2(f.y-i.y,f.x-i.x),n=f.distanceToBorder(t,o),s=Math.sqrt(Math.pow(i.x-f.x,2)+Math.pow(i.y-f.y,2)),r=n-s,Math.abs(r)<u)break;r<0?p===!1?l=v:c=v:p===!1?c=v:l=v,d++}return i.t=v,i}},{key:"_getDistanceToBezierEdge",value:function(e,t,i,o,n,s,r){var a=1e9,h=void 0,d=void 0,l=void 0,c=void 0,u=void 0,f=e,p=t;for(d=1;d<10;d++)l=.1*d,c=Math.pow(1-l,2)*e+2*l*(1-l)*r.x+Math.pow(l,2)*i,u=Math.pow(1-l,2)*t+2*l*(1-l)*r.y+Math.pow(l,2)*o,d>0&&(h=this._getDistanceToLine(f,p,c,u,n,s),a=h<a?h:a),f=c,p=u;return a}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){var i=[],o=!0,n=!1,s=void 0;try{for(var r,a=e[Symbol.iterator]();!(o=(r=a.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(h){n=!0,s=h}finally{try{!o&&a["return"]&&a["return"]()}finally{if(n)throw s}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),s=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),r=i(1),a=function(){function e(t,i,n){o(this,e),this.body=i,this.labelModule=n,this.options={},this.setOptions(t),this.colorDirty=!0,this.color={},this.selectionWidth=2,this.hoverWidth=1.5,this.fromPoint=this.from,this.toPoint=this.to}return s(e,[{key:"connect",value:function(){this.from=this.body.nodes[this.options.from],this.to=this.body.nodes[this.options.to]}},{key:"cleanup",value:function(){return!1}},{key:"setOptions",value:function(e){this.options=e,this.from=this.body.nodes[this.options.from],this.to=this.body.nodes[this.options.to],this.id=this.options.id}},{key:"drawLine",value:function(e,t,i,o){e.strokeStyle=this.getColor(e,t,i),e.lineWidth=this.getLineWidth(t,i),this.options.dashes!==!1?this._drawDashedLine(e,o):this._drawLine(e,o)}},{key:"_drawLine",value:function(e,t,i,o){if(this.from!=this.to)this._line(e,t,i,o);else{var s=this._getCircleData(e),r=n(s,3),a=r[0],h=r[1],d=r[2];this._circle(e,a,h,d)}}},{key:"_drawDashedLine",value:function(e,t,i,o){e.lineCap="round";var s=[5,5];if(Array.isArray(this.options.dashes)===!0&&(s=this.options.dashes),void 0!==e.setLineDash){if(e.save(),e.setLineDash(s),e.lineDashOffset=0,this.from!=this.to)this._line(e,t);else{var r=this._getCircleData(e),a=n(r,3),h=a[0],d=a[1],l=a[2];this._circle(e,h,d,l)}e.setLineDash([0]),e.lineDashOffset=0,e.restore()}else{if(this.from!=this.to)e.dashedLine(this.from.x,this.from.y,this.to.x,this.to.y,s);else{var c=this._getCircleData(e),u=n(c,3),f=u[0],p=u[1],v=u[2];this._circle(e,f,p,v)}this.enableShadow(e),e.stroke(),this.disableShadow(e)}}},{key:"findBorderPosition",value:function(e,t,i){return this.from!=this.to?this._findBorderPosition(e,t,i):this._findBorderPositionCircle(e,t,i)}},{key:"findBorderPositions",value:function(e){var t={},i={};if(this.from!=this.to)t=this._findBorderPosition(this.from,e),i=this._findBorderPosition(this.to,e);else{var o=this._getCircleData(e),s=n(o,3),r=s[0],a=s[1];s[2];t=this._findBorderPositionCircle(this.from,e,{x:r,y:a,low:.25,high:.6,direction:-1}),i=this._findBorderPositionCircle(this.from,e,{x:r,y:a,low:.6,high:.8,direction:1})}return{from:t,to:i}}},{key:"_getCircleData",value:function(e){var t=void 0,i=void 0,o=this.from,n=this.options.selfReferenceSize;return void 0!==e&&void 0===o.shape.width&&o.shape.resize(e),o.shape.width>o.shape.height?(t=o.x+.5*o.shape.width,i=o.y-n):(t=o.x+n,i=o.y-.5*o.shape.height),[t,i,n]}},{key:"_pointOnCircle",value:function(e,t,i,o){var n=2*o*Math.PI;return{x:e+i*Math.cos(n),y:t-i*Math.sin(n)}}},{key:"_findBorderPositionCircle",value:function(e,t,i){for(var o=i.x,n=i.y,s=i.low,r=i.high,a=i.direction,h=10,d=0,l=this.options.selfReferenceSize,c=void 0,u=void 0,f=void 0,p=void 0,v=void 0,y=.05,g=.5*(s+r);s<=r&&d<h&&(g=.5*(s+r),c=this._pointOnCircle(o,n,l,g),u=Math.atan2(e.y-c.y,e.x-c.x),f=e.distanceToBorder(t,u),p=Math.sqrt(Math.pow(c.x-e.x,2)+Math.pow(c.y-e.y,2)),v=f-p,!(Math.abs(v)<y));)v>0?a>0?s=g:r=g:a>0?r=g:s=g,d++;return c.t=g,c}},{key:"getLineWidth",value:function(e,t){return e===!0?Math.max(this.selectionWidth,.3/this.body.view.scale):t===!0?Math.max(this.hoverWidth,.3/this.body.view.scale):Math.max(this.options.width,.3/this.body.view.scale)}},{key:"getColor",value:function(e,t,i){var o=this.options.color;if(o.inherit!==!1){if("both"===o.inherit&&this.from.id!==this.to.id){var n=e.createLinearGradient(this.from.x,this.from.y,this.to.x,this.to.y),s=void 0,a=void 0;return s=this.from.options.color.highlight.border,a=this.to.options.color.highlight.border,this.from.selected===!1&&this.to.selected===!1?(s=r.overrideOpacity(this.from.options.color.border,this.options.color.opacity),a=r.overrideOpacity(this.to.options.color.border,this.options.color.opacity)):this.from.selected===!0&&this.to.selected===!1?a=this.to.options.color.border:this.from.selected===!1&&this.to.selected===!0&&(s=this.from.options.color.border),n.addColorStop(0,s),n.addColorStop(1,a),n}this.colorDirty===!0&&("to"===o.inherit?(this.color.highlight=this.to.options.color.highlight.border,this.color.hover=this.to.options.color.hover.border,this.color.color=r.overrideOpacity(this.to.options.color.border,o.opacity)):(this.color.highlight=this.from.options.color.highlight.border,this.color.hover=this.from.options.color.hover.border,this.color.color=r.overrideOpacity(this.from.options.color.border,o.opacity)))}else this.colorDirty===!0&&(this.color.highlight=o.highlight,this.color.hover=o.hover,this.color.color=r.overrideOpacity(o.color,o.opacity));return this.colorDirty=!1,t===!0?this.color.highlight:i===!0?this.color.hover:this.color.color}},{key:"_circle",value:function(e,t,i,o){this.enableShadow(e),e.beginPath(),e.arc(t,i,o,0,2*Math.PI,!1),e.stroke(),this.disableShadow(e)}},{key:"getDistanceToEdge",value:function(e,t,i,o,s,r,a){var h=0;if(this.from!=this.to)h=this._getDistanceToEdge(e,t,i,o,s,r,a);else{var d=this._getCircleData(),l=n(d,3),c=l[0],u=l[1],f=l[2],p=c-s,v=u-r;h=Math.abs(Math.sqrt(p*p+v*v)-f)}return this.labelModule.size.left<s&&this.labelModule.size.left+this.labelModule.size.width>s&&this.labelModule.size.top<r&&this.labelModule.size.top+this.labelModule.size.height>r?0:h}},{key:"_getDistanceToLine",value:function(e,t,i,o,n,s){var r=i-e,a=o-t,h=r*r+a*a,d=((n-e)*r+(s-t)*a)/h;d>1?d=1:d<0&&(d=0);var l=e+d*r,c=t+d*a,u=l-n,f=c-s;return Math.sqrt(u*u+f*f)}},{key:"getArrowData",value:function(e,t,i,o,s){var r=void 0,a=void 0,h=void 0,d=void 0,l=void 0,c=void 0,u=this.getLineWidth(o,s);if("from"===t?(h=this.from,d=this.to,l=.1,c=this.options.arrows.from.scaleFactor):"to"===t?(h=this.to,d=this.from,l=-.1,c=this.options.arrows.to.scaleFactor):(h=this.to,d=this.from,c=this.options.arrows.middle.scaleFactor),h!=d)if("middle"!==t)if(this.options.smooth.enabled===!0){a=this.findBorderPosition(h,e,{via:i});var f=this.getPoint(Math.max(0,Math.min(1,a.t+l)),i);r=Math.atan2(a.y-f.y,a.x-f.x)}else r=Math.atan2(h.y-d.y,h.x-d.x),a=this.findBorderPosition(h,e);else r=Math.atan2(h.y-d.y,h.x-d.x),a=this.getPoint(.5,i);else{var p=this._getCircleData(e),v=n(p,3),y=v[0],g=v[1],b=v[2];"from"===t?(a=this.findBorderPosition(this.from,e,{x:y,y:g,low:.25,high:.6,direction:-1}),r=a.t*-2*Math.PI+1.5*Math.PI+.1*Math.PI):"to"===t?(a=this.findBorderPosition(this.from,e,{x:y,y:g,low:.6,high:1,direction:1}),r=a.t*-2*Math.PI+1.5*Math.PI-1.1*Math.PI):(a=this._pointOnCircle(y,g,b,.175),r=3.9269908169872414)}var m=15*c+3*u,_=a.x-.9*m*Math.cos(r),w=a.y-.9*m*Math.sin(r),k={x:_,y:w};return{point:a,core:k,angle:r,length:m}}},{key:"drawArrowHead",value:function(e,t,i,o){e.strokeStyle=this.getColor(e,t,i),e.fillStyle=e.strokeStyle,e.lineWidth=this.getLineWidth(t,i),e.arrow(o.point.x,o.point.y,o.angle,o.length),this.enableShadow(e),e.fill(),this.disableShadow(e)}},{key:"enableShadow",value:function(e){this.options.shadow.enabled===!0&&(e.shadowColor=this.options.shadow.color,e.shadowBlur=this.options.shadow.size,e.shadowOffsetX=this.options.shadow.x,e.shadowOffsetY=this.options.shadow.y)}},{key:"disableShadow",value:function(e){this.options.shadow.enabled===!0&&(e.shadowColor="rgba(0,0,0,0)",e.shadowBlur=0,e.shadowOffsetX=0,e.shadowOffsetY=0)}}]),e}();t["default"]=a},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(39),d=o(h),l=function(e){function t(e,i,o){n(this,t);var r=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o));return r._boundFunction=function(){r.positionBezierNode()},r.body.emitter.on("_repositionBezierNodes",r._boundFunction),r}return r(t,e),a(t,[{key:"setOptions",value:function(e){var t=!1;this.options.physics!==e.physics&&(t=!0),this.options=e,this.id=this.options.id,this.from=this.body.nodes[this.options.from],this.to=this.body.nodes[this.options.to],this.setupSupportNode(),this.connect(),t===!0&&(this.via.setOptions({physics:this.options.physics}),this.positionBezierNode())}},{key:"connect",value:function(){this.from=this.body.nodes[this.options.from],this.to=this.body.nodes[this.options.to],void 0===this.from||void 0===this.to||this.options.physics===!1?this.via.setOptions({physics:!1}):this.from.id===this.to.id?this.via.setOptions({physics:!1}):this.via.setOptions({physics:!0})}},{key:"cleanup",value:function(){return this.body.emitter.off("_repositionBezierNodes",this._boundFunction),void 0!==this.via&&(delete this.body.nodes[this.via.id],this.via=void 0,!0)}},{key:"setupSupportNode",value:function(){if(void 0===this.via){var e="edgeId:"+this.id,t=this.body.functions.createNode({id:e,shape:"circle",physics:!0,hidden:!0});this.body.nodes[e]=t,this.via=t,this.via.parentEdgeId=this.id,this.positionBezierNode()}}},{key:"positionBezierNode",value:function(){void 0!==this.via&&void 0!==this.from&&void 0!==this.to?(this.via.x=.5*(this.from.x+this.to.x),this.via.y=.5*(this.from.y+this.to.y)):void 0!==this.via&&(this.via.x=0,this.via.y=0)}},{key:"_line",value:function(e,t){e.beginPath(),e.moveTo(this.fromPoint.x,this.fromPoint.y),void 0===t.x?e.lineTo(this.toPoint.x,this.toPoint.y):e.quadraticCurveTo(t.x,t.y,this.toPoint.x,this.toPoint.y),this.enableShadow(e),e.stroke(),this.disableShadow(e)}},{key:"getViaNode",value:function(){return this.via}},{key:"getPoint",value:function(e){var t=arguments.length<=1||void 0===arguments[1]?this.via:arguments[1],i=e,o=Math.pow(1-i,2)*this.fromPoint.x+2*i*(1-i)*t.x+Math.pow(i,2)*this.toPoint.x,n=Math.pow(1-i,2)*this.fromPoint.y+2*i*(1-i)*t.y+Math.pow(i,2)*this.toPoint.y;return{x:o,y:n}}},{key:"_findBorderPosition",value:function(e,t){return this._findBorderPositionBezier(e,t,this.via)}},{key:"_getDistanceToEdge",value:function(e,t,i,o,n,s){return this._getDistanceToBezierEdge(e,t,i,o,n,s,this.via)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(39),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"_line",value:function(e,t){e.beginPath(),e.moveTo(this.fromPoint.x,this.fromPoint.y),void 0===t.x?e.lineTo(this.toPoint.x,this.toPoint.y):e.quadraticCurveTo(t.x,t.y,this.toPoint.x,this.toPoint.y),this.enableShadow(e),e.stroke(),this.disableShadow(e)}},{key:"getViaNode",value:function(){return this._getViaCoordinates()}},{key:"_getViaCoordinates",value:function(){var e=void 0,t=void 0,i=this.options.smooth.roundness,o=this.options.smooth.type,n=Math.abs(this.from.x-this.to.x),s=Math.abs(this.from.y-this.to.y);if("discrete"===o||"diagonalCross"===o)Math.abs(this.from.x-this.to.x)<=Math.abs(this.from.y-this.to.y)?(this.from.y>=this.to.y?this.from.x<=this.to.x?(e=this.from.x+i*s,t=this.from.y-i*s):this.from.x>this.to.x&&(e=this.from.x-i*s,t=this.from.y-i*s):this.from.y<this.to.y&&(this.from.x<=this.to.x?(e=this.from.x+i*s,t=this.from.y+i*s):this.from.x>this.to.x&&(e=this.from.x-i*s,t=this.from.y+i*s)),"discrete"===o&&(e=n<i*s?this.from.x:e)):Math.abs(this.from.x-this.to.x)>Math.abs(this.from.y-this.to.y)&&(this.from.y>=this.to.y?this.from.x<=this.to.x?(e=this.from.x+i*n,t=this.from.y-i*n):this.from.x>this.to.x&&(e=this.from.x-i*n,t=this.from.y-i*n):this.from.y<this.to.y&&(this.from.x<=this.to.x?(e=this.from.x+i*n,t=this.from.y+i*n):this.from.x>this.to.x&&(e=this.from.x-i*n,t=this.from.y+i*n)),"discrete"===o&&(t=s<i*n?this.from.y:t));else if("straightCross"===o)Math.abs(this.from.x-this.to.x)<=Math.abs(this.from.y-this.to.y)?(e=this.from.x,t=this.from.y<this.to.y?this.to.y-(1-i)*s:this.to.y+(1-i)*s):Math.abs(this.from.x-this.to.x)>Math.abs(this.from.y-this.to.y)&&(e=this.from.x<this.to.x?this.to.x-(1-i)*n:this.to.x+(1-i)*n,t=this.from.y);else if("horizontal"===o)e=this.from.x<this.to.x?this.to.x-(1-i)*n:this.to.x+(1-i)*n,t=this.from.y;else if("vertical"===o)e=this.from.x,t=this.from.y<this.to.y?this.to.y-(1-i)*s:this.to.y+(1-i)*s;else if("curvedCW"===o){n=this.to.x-this.from.x,s=this.from.y-this.to.y;var r=Math.sqrt(n*n+s*s),a=Math.PI,h=Math.atan2(s,n),d=(h+(.5*i+.5)*a)%(2*a);e=this.from.x+(.5*i+.5)*r*Math.sin(d),t=this.from.y+(.5*i+.5)*r*Math.cos(d)}else if("curvedCCW"===o){n=this.to.x-this.from.x,s=this.from.y-this.to.y;var l=Math.sqrt(n*n+s*s),c=Math.PI,u=Math.atan2(s,n),f=(u+(.5*-i+.5)*c)%(2*c);e=this.from.x+(.5*i+.5)*l*Math.sin(f),t=this.from.y+(.5*i+.5)*l*Math.cos(f)}else Math.abs(this.from.x-this.to.x)<=Math.abs(this.from.y-this.to.y)?this.from.y>=this.to.y?this.from.x<=this.to.x?(e=this.from.x+i*s,t=this.from.y-i*s,e=this.to.x<e?this.to.x:e):this.from.x>this.to.x&&(e=this.from.x-i*s,t=this.from.y-i*s,e=this.to.x>e?this.to.x:e):this.from.y<this.to.y&&(this.from.x<=this.to.x?(e=this.from.x+i*s,t=this.from.y+i*s,e=this.to.x<e?this.to.x:e):this.from.x>this.to.x&&(e=this.from.x-i*s,t=this.from.y+i*s,e=this.to.x>e?this.to.x:e)):Math.abs(this.from.x-this.to.x)>Math.abs(this.from.y-this.to.y)&&(this.from.y>=this.to.y?this.from.x<=this.to.x?(e=this.from.x+i*n,t=this.from.y-i*n,t=this.to.y>t?this.to.y:t):this.from.x>this.to.x&&(e=this.from.x-i*n,t=this.from.y-i*n,t=this.to.y>t?this.to.y:t):this.from.y<this.to.y&&(this.from.x<=this.to.x?(e=this.from.x+i*n,t=this.from.y+i*n,
t=this.to.y<t?this.to.y:t):this.from.x>this.to.x&&(e=this.from.x-i*n,t=this.from.y+i*n,t=this.to.y<t?this.to.y:t)));return{x:e,y:t}}},{key:"_findBorderPosition",value:function(e,t){var i=arguments.length<=2||void 0===arguments[2]?{}:arguments[2];return this._findBorderPositionBezier(e,t,i.via)}},{key:"_getDistanceToEdge",value:function(e,t,i,o,n,s){var r=arguments.length<=6||void 0===arguments[6]?this._getViaCoordinates():arguments[6];return this._getDistanceToBezierEdge(e,t,i,o,n,s,r)}},{key:"getPoint",value:function(e){var t=arguments.length<=1||void 0===arguments[1]?this._getViaCoordinates():arguments[1],i=e,o=Math.pow(1-i,2)*this.fromPoint.x+2*i*(1-i)*t.x+Math.pow(i,2)*this.toPoint.x,n=Math.pow(1-i,2)*this.fromPoint.y+2*i*(1-i)*t.y+Math.pow(i,2)*this.toPoint.y;return{x:o,y:n}}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(40),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"_line",value:function(e){e.beginPath(),e.moveTo(this.fromPoint.x,this.fromPoint.y),e.lineTo(this.toPoint.x,this.toPoint.y),this.enableShadow(e),e.stroke(),this.disableShadow(e)}},{key:"getViaNode",value:function(){}},{key:"getPoint",value:function(e){return{x:(1-e)*this.fromPoint.x+e*this.toPoint.x,y:(1-e)*this.fromPoint.y+e*this.toPoint.y}}},{key:"_findBorderPosition",value:function(e,t){var i=this.to,o=this.from;e.id===this.from.id&&(i=this.from,o=this.to);var n=Math.atan2(i.y-o.y,i.x-o.x),s=i.x-o.x,r=i.y-o.y,a=Math.sqrt(s*s+r*r),h=e.distanceToBorder(t,n),d=(a-h)/a,l={};return l.x=(1-d)*o.x+d*i.x,l.y=(1-d)*o.y+d*i.y,l}},{key:"_getDistanceToEdge",value:function(e,t,i,o,n,s){return this._getDistanceToLine(e,t,i,o,n,s)}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),r=i(45),a=o(r),h=i(46),d=o(h),l=i(47),c=o(l),u=i(48),f=o(u),p=i(49),v=o(p),y=i(50),g=o(y),b=i(51),m=o(b),_=i(52),w=o(_),k=i(1),x=function(){function e(t){n(this,e),this.body=t,this.physicsBody={physicsNodeIndices:[],physicsEdgeIndices:[],forces:{},velocities:{}},this.physicsEnabled=!0,this.simulationInterval=1e3/60,this.requiresTimeout=!0,this.previousStates={},this.referenceState={},this.freezeCache={},this.renderTimer=void 0,this.adaptiveTimestep=!1,this.adaptiveTimestepEnabled=!1,this.adaptiveCounter=0,this.adaptiveInterval=3,this.stabilized=!1,this.startedStabilization=!1,this.stabilizationIterations=0,this.ready=!1,this.options={},this.defaultOptions={enabled:!0,barnesHut:{theta:.5,gravitationalConstant:-2e3,centralGravity:.3,springLength:95,springConstant:.04,damping:.09,avoidOverlap:0},forceAtlas2Based:{theta:.5,gravitationalConstant:-50,centralGravity:.01,springConstant:.08,springLength:100,damping:.4,avoidOverlap:0},repulsion:{centralGravity:.2,springLength:200,springConstant:.05,nodeDistance:100,damping:.09,avoidOverlap:0},hierarchicalRepulsion:{centralGravity:0,springLength:100,springConstant:.01,nodeDistance:120,damping:.09},maxVelocity:50,minVelocity:.75,solver:"barnesHut",stabilization:{enabled:!0,iterations:1e3,updateInterval:50,onlyDynamicEdges:!1,fit:!0},timestep:.5,adaptiveTimestep:!0},k.extend(this.options,this.defaultOptions),this.timestep=.5,this.layoutFailed=!1,this.bindEventListeners()}return s(e,[{key:"bindEventListeners",value:function(){var e=this;this.body.emitter.on("initPhysics",function(){e.initPhysics()}),this.body.emitter.on("_layoutFailed",function(){e.layoutFailed=!0}),this.body.emitter.on("resetPhysics",function(){e.stopSimulation(),e.ready=!1}),this.body.emitter.on("disablePhysics",function(){e.physicsEnabled=!1,e.stopSimulation()}),this.body.emitter.on("restorePhysics",function(){e.setOptions(e.options),e.ready===!0&&e.startSimulation()}),this.body.emitter.on("startSimulation",function(){e.ready===!0&&e.startSimulation()}),this.body.emitter.on("stopSimulation",function(){e.stopSimulation()}),this.body.emitter.on("destroy",function(){e.stopSimulation(!1),e.body.emitter.off()}),this.body.emitter.on("_dataChanged",function(){e.updatePhysicsData()})}},{key:"setOptions",value:function(e){void 0!==e&&(e===!1?(this.options.enabled=!1,this.physicsEnabled=!1,this.stopSimulation()):(this.physicsEnabled=!0,k.selectiveNotDeepExtend(["stabilization"],this.options,e),k.mergeOptions(this.options,e,"stabilization"),void 0===e.enabled&&(this.options.enabled=!0),this.options.enabled===!1&&(this.physicsEnabled=!1,this.stopSimulation()),this.timestep=this.options.timestep)),this.init()}},{key:"init",value:function(){var e;"forceAtlas2Based"===this.options.solver?(e=this.options.forceAtlas2Based,this.nodesSolver=new m["default"](this.body,this.physicsBody,e),this.edgesSolver=new f["default"](this.body,this.physicsBody,e),this.gravitySolver=new w["default"](this.body,this.physicsBody,e)):"repulsion"===this.options.solver?(e=this.options.repulsion,this.nodesSolver=new d["default"](this.body,this.physicsBody,e),this.edgesSolver=new f["default"](this.body,this.physicsBody,e),this.gravitySolver=new g["default"](this.body,this.physicsBody,e)):"hierarchicalRepulsion"===this.options.solver?(e=this.options.hierarchicalRepulsion,this.nodesSolver=new c["default"](this.body,this.physicsBody,e),this.edgesSolver=new v["default"](this.body,this.physicsBody,e),this.gravitySolver=new g["default"](this.body,this.physicsBody,e)):(e=this.options.barnesHut,this.nodesSolver=new a["default"](this.body,this.physicsBody,e),this.edgesSolver=new f["default"](this.body,this.physicsBody,e),this.gravitySolver=new g["default"](this.body,this.physicsBody,e)),this.modelOptions=e}},{key:"initPhysics",value:function(){this.physicsEnabled===!0&&this.options.enabled===!0?this.options.stabilization.enabled===!0?this.stabilize():(this.stabilized=!1,this.ready=!0,this.body.emitter.emit("fit",{},this.layoutFailed),this.startSimulation()):(this.ready=!0,this.body.emitter.emit("fit"))}},{key:"startSimulation",value:function(){this.physicsEnabled===!0&&this.options.enabled===!0?(this.stabilized=!1,this.adaptiveTimestep=!1,this.body.emitter.emit("_resizeNodes"),void 0===this.viewFunction&&(this.viewFunction=this.simulationStep.bind(this),this.body.emitter.on("initRedraw",this.viewFunction),this.body.emitter.emit("_startRendering"))):this.body.emitter.emit("_redraw")}},{key:"stopSimulation",value:function(){var e=arguments.length<=0||void 0===arguments[0]||arguments[0];this.stabilized=!0,e===!0&&this._emitStabilized(),void 0!==this.viewFunction&&(this.body.emitter.off("initRedraw",this.viewFunction),this.viewFunction=void 0,e===!0&&this.body.emitter.emit("_stopRendering"))}},{key:"simulationStep",value:function(){var e=Date.now();this.physicsTick();var t=Date.now()-e;(t<.4*this.simulationInterval||this.runDoubleSpeed===!0)&&this.stabilized===!1&&(this.physicsTick(),this.runDoubleSpeed=!0),this.stabilized===!0&&this.stopSimulation()}},{key:"_emitStabilized",value:function(){var e=this,t=arguments.length<=0||void 0===arguments[0]?this.stabilizationIterations:arguments[0];(this.stabilizationIterations>1||this.startedStabilization===!0)&&setTimeout(function(){e.body.emitter.emit("stabilized",{iterations:t}),e.startedStabilization=!1,e.stabilizationIterations=0},0)}},{key:"physicsTick",value:function(){if(this.startedStabilization===!1&&(this.body.emitter.emit("startStabilizing"),this.startedStabilization=!0),this.stabilized===!1){if(this.adaptiveTimestep===!0&&this.adaptiveTimestepEnabled===!0){var e=1.2;this.adaptiveCounter%this.adaptiveInterval===0?(this.timestep=2*this.timestep,this.calculateForces(),this.moveNodes(),this.revert(),this.timestep=.5*this.timestep,this.calculateForces(),this.moveNodes(),this.calculateForces(),this.moveNodes(),this._evaluateStepQuality()===!0?this.timestep=e*this.timestep:this.timestep/e<this.options.timestep?this.timestep=this.options.timestep:(this.adaptiveCounter=-1,this.timestep=Math.max(this.options.timestep,this.timestep/e))):(this.calculateForces(),this.moveNodes()),this.adaptiveCounter+=1}else this.timestep=this.options.timestep,this.calculateForces(),this.moveNodes();this.stabilized===!0&&this.revert(),this.stabilizationIterations++}}},{key:"updatePhysicsData",value:function(){this.physicsBody.forces={},this.physicsBody.physicsNodeIndices=[],this.physicsBody.physicsEdgeIndices=[];var e=this.body.nodes,t=this.body.edges;for(var i in e)e.hasOwnProperty(i)&&e[i].options.physics===!0&&this.physicsBody.physicsNodeIndices.push(e[i].id);for(var o in t)t.hasOwnProperty(o)&&t[o].options.physics===!0&&this.physicsBody.physicsEdgeIndices.push(t[o].id);for(var n=0;n<this.physicsBody.physicsNodeIndices.length;n++){var s=this.physicsBody.physicsNodeIndices[n];this.physicsBody.forces[s]={x:0,y:0},void 0===this.physicsBody.velocities[s]&&(this.physicsBody.velocities[s]={x:0,y:0})}for(var r in this.physicsBody.velocities)void 0===e[r]&&delete this.physicsBody.velocities[r]}},{key:"revert",value:function(){var e=Object.keys(this.previousStates),t=this.body.nodes,i=this.physicsBody.velocities;this.referenceState={};for(var o=0;o<e.length;o++){var n=e[o];void 0!==t[n]?t[n].options.physics===!0&&(this.referenceState[n]={positions:{x:t[n].x,y:t[n].y}},i[n].x=this.previousStates[n].vx,i[n].y=this.previousStates[n].vy,t[n].x=this.previousStates[n].x,t[n].y=this.previousStates[n].y):delete this.previousStates[n]}}},{key:"_evaluateStepQuality",value:function(){var e=void 0,t=void 0,i=void 0,o=this.body.nodes,n=this.referenceState,s=.3;for(var r in this.referenceState)if(this.referenceState.hasOwnProperty(r)&&void 0!==o[r]&&(e=o[r].x-n[r].positions.x,t=o[r].y-n[r].positions.y,i=Math.sqrt(Math.pow(e,2)+Math.pow(t,2)),i>s))return!1;return!0}},{key:"moveNodes",value:function(){for(var e=this.physicsBody.physicsNodeIndices,t=this.options.maxVelocity?this.options.maxVelocity:1e9,i=0,o=0,n=5,s=0;s<e.length;s++){var r=e[s],a=this._performStep(r,t);i=Math.max(i,a),o+=a}this.adaptiveTimestepEnabled=o/e.length<n,this.stabilized=i<this.options.minVelocity}},{key:"_performStep",value:function(e,t){var i=this.body.nodes[e],o=this.timestep,n=this.physicsBody.forces,s=this.physicsBody.velocities;if(this.previousStates[e]={x:i.x,y:i.y,vx:s[e].x,vy:s[e].y},i.options.fixed.x===!1){var r=this.modelOptions.damping*s[e].x,a=(n[e].x-r)/i.options.mass;s[e].x+=a*o,s[e].x=Math.abs(s[e].x)>t?s[e].x>0?t:-t:s[e].x,i.x+=s[e].x*o}else n[e].x=0,s[e].x=0;if(i.options.fixed.y===!1){var h=this.modelOptions.damping*s[e].y,d=(n[e].y-h)/i.options.mass;s[e].y+=d*o,s[e].y=Math.abs(s[e].y)>t?s[e].y>0?t:-t:s[e].y,i.y+=s[e].y*o}else n[e].y=0,s[e].y=0;var l=Math.sqrt(Math.pow(s[e].x,2)+Math.pow(s[e].y,2));return l}},{key:"calculateForces",value:function(){this.gravitySolver.solve(),this.nodesSolver.solve(),this.edgesSolver.solve()}},{key:"_freezeNodes",value:function(){var e=this.body.nodes;for(var t in e)e.hasOwnProperty(t)&&e[t].x&&e[t].y&&(this.freezeCache[t]={x:e[t].options.fixed.x,y:e[t].options.fixed.y},e[t].options.fixed.x=!0,e[t].options.fixed.y=!0)}},{key:"_restoreFrozenNodes",value:function(){var e=this.body.nodes;for(var t in e)e.hasOwnProperty(t)&&void 0!==this.freezeCache[t]&&(e[t].options.fixed.x=this.freezeCache[t].x,e[t].options.fixed.y=this.freezeCache[t].y);this.freezeCache={}}},{key:"stabilize",value:function(){var e=this,t=arguments.length<=0||void 0===arguments[0]?this.options.stabilization.iterations:arguments[0];return"number"!=typeof t&&(console.log("The stabilize method needs a numeric amount of iterations. Switching to default: ",this.options.stabilization.iterations),t=this.options.stabilization.iterations),0===this.physicsBody.physicsNodeIndices.length?void(this.ready=!0):(this.adaptiveTimestep=this.options.adaptiveTimestep,this.body.emitter.emit("_resizeNodes"),this.stopSimulation(),this.stabilized=!1,this.body.emitter.emit("_blockRedraw"),this.targetIterations=t,this.options.stabilization.onlyDynamicEdges===!0&&this._freezeNodes(),this.stabilizationIterations=0,void setTimeout(function(){return e._stabilizationBatch()},0))}},{key:"_stabilizationBatch",value:function(){this.startedStabilization===!1&&(this.body.emitter.emit("startStabilizing"),this.startedStabilization=!0);for(var e=0;this.stabilized===!1&&e<this.options.stabilization.updateInterval&&this.stabilizationIterations<this.targetIterations;)this.physicsTick(),e++;this.stabilized===!1&&this.stabilizationIterations<this.targetIterations?(this.body.emitter.emit("stabilizationProgress",{iterations:this.stabilizationIterations,total:this.targetIterations}),setTimeout(this._stabilizationBatch.bind(this),0)):this._finalizeStabilization()}},{key:"_finalizeStabilization",value:function(){this.body.emitter.emit("_allowRedraw"),this.options.stabilization.fit===!0&&this.body.emitter.emit("fit"),this.options.stabilization.onlyDynamicEdges===!0&&this._restoreFrozenNodes(),this.body.emitter.emit("stabilizationIterationsDone"),this.body.emitter.emit("_requestRedraw"),this.stabilized===!0?this._emitStabilized():this.startSimulation(),this.ready=!0}},{key:"_drawForces",value:function(e){for(var t=0;t<this.physicsBody.physicsNodeIndices.length;t++){var i=this.body.nodes[this.physicsBody.physicsNodeIndices[t]],o=this.physicsBody.forces[this.physicsBody.physicsNodeIndices[t]],n=20,s=.03,r=Math.sqrt(Math.pow(o.x,2)+Math.pow(o.x,2)),a=Math.min(Math.max(5,r),15),h=3*a,d=k.HSVToHex((180-180*Math.min(1,Math.max(0,s*r)))/360,1,1);e.lineWidth=a,e.strokeStyle=d,e.beginPath(),e.moveTo(i.x,i.y),e.lineTo(i.x+n*o.x,i.y+n*o.y),e.stroke();var l=Math.atan2(o.y,o.x);e.fillStyle=d,e.arrow(i.x+n*o.x+Math.cos(l)*h,i.y+n*o.y+Math.sin(l)*h,l,h),e.fill()}}}]),e}();t["default"]=x},function(e,t){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),n=function(){function e(t,o,n){i(this,e),this.body=t,this.physicsBody=o,this.barnesHutTree,this.setOptions(n),this.randomSeed=5}return o(e,[{key:"setOptions",value:function(e){this.options=e,this.thetaInversed=1/this.options.theta,this.overlapAvoidanceFactor=1-Math.max(0,Math.min(1,this.options.avoidOverlap))}},{key:"seededRandom",value:function(){var e=1e4*Math.sin(this.randomSeed++);return e-Math.floor(e)}},{key:"solve",value:function(){if(0!==this.options.gravitationalConstant&&this.physicsBody.physicsNodeIndices.length>0){var e=void 0,t=this.body.nodes,i=this.physicsBody.physicsNodeIndices,o=i.length,n=this._formBarnesHutTree(t,i);this.barnesHutTree=n;for(var s=0;s<o;s++)e=t[i[s]],e.options.mass>0&&(this._getForceContribution(n.root.children.NW,e),this._getForceContribution(n.root.children.NE,e),this._getForceContribution(n.root.children.SW,e),this._getForceContribution(n.root.children.SE,e))}}},{key:"_getForceContribution",value:function(e,t){if(e.childrenCount>0){var i=void 0,o=void 0,n=void 0;i=e.centerOfMass.x-t.x,o=e.centerOfMass.y-t.y,n=Math.sqrt(i*i+o*o),n*e.calcSize>this.thetaInversed?this._calculateForces(n,i,o,t,e):4===e.childrenCount?(this._getForceContribution(e.children.NW,t),this._getForceContribution(e.children.NE,t),this._getForceContribution(e.children.SW,t),this._getForceContribution(e.children.SE,t)):e.children.data.id!=t.id&&this._calculateForces(n,i,o,t,e)}}},{key:"_calculateForces",value:function(e,t,i,o,n){0===e&&(e=.1,t=e),this.overlapAvoidanceFactor<1&&(e=Math.max(.1+this.overlapAvoidanceFactor*o.shape.radius,e-o.shape.radius));var s=this.options.gravitationalConstant*n.mass*o.options.mass/Math.pow(e,3),r=t*s,a=i*s;this.physicsBody.forces[o.id].x+=r,this.physicsBody.forces[o.id].y+=a}},{key:"_formBarnesHutTree",value:function(e,t){for(var i=void 0,o=t.length,n=e[t[0]].x,s=e[t[0]].y,r=e[t[0]].x,a=e[t[0]].y,h=1;h<o;h++){var d=e[t[h]].x,l=e[t[h]].y;e[t[h]].options.mass>0&&(d<n&&(n=d),d>r&&(r=d),l<s&&(s=l),l>a&&(a=l))}var c=Math.abs(r-n)-Math.abs(a-s);c>0?(s-=.5*c,a+=.5*c):(n+=.5*c,r-=.5*c);var u=1e-5,f=Math.max(u,Math.abs(r-n)),p=.5*f,v=.5*(n+r),y=.5*(s+a),g={root:{centerOfMass:{x:0,y:0},mass:0,range:{minX:v-p,maxX:v+p,minY:y-p,maxY:y+p},size:f,calcSize:1/f,children:{data:null},maxWidth:0,level:0,childrenCount:4}};this._splitBranch(g.root);for(var b=0;b<o;b++)i=e[t[b]],i.options.mass>0&&this._placeInTree(g.root,i);return g}},{key:"_updateBranchMass",value:function(e,t){var i=e.mass+t.options.mass,o=1/i;e.centerOfMass.x=e.centerOfMass.x*e.mass+t.x*t.options.mass,e.centerOfMass.x*=o,e.centerOfMass.y=e.centerOfMass.y*e.mass+t.y*t.options.mass,e.centerOfMass.y*=o,e.mass=i;var n=Math.max(Math.max(t.height,t.radius),t.width);e.maxWidth=e.maxWidth<n?n:e.maxWidth}},{key:"_placeInTree",value:function(e,t,i){1==i&&void 0!==i||this._updateBranchMass(e,t),e.children.NW.range.maxX>t.x?e.children.NW.range.maxY>t.y?this._placeInRegion(e,t,"NW"):this._placeInRegion(e,t,"SW"):e.children.NW.range.maxY>t.y?this._placeInRegion(e,t,"NE"):this._placeInRegion(e,t,"SE")}},{key:"_placeInRegion",value:function(e,t,i){switch(e.children[i].childrenCount){case 0:e.children[i].children.data=t,e.children[i].childrenCount=1,this._updateBranchMass(e.children[i],t);break;case 1:e.children[i].children.data.x===t.x&&e.children[i].children.data.y===t.y?(t.x+=this.seededRandom(),t.y+=this.seededRandom()):(this._splitBranch(e.children[i]),this._placeInTree(e.children[i],t));break;case 4:this._placeInTree(e.children[i],t)}}},{key:"_splitBranch",value:function(e){var t=null;1===e.childrenCount&&(t=e.children.data,e.mass=0,e.centerOfMass.x=0,e.centerOfMass.y=0),e.childrenCount=4,e.children.data=null,this._insertRegion(e,"NW"),this._insertRegion(e,"NE"),this._insertRegion(e,"SW"),this._insertRegion(e,"SE"),null!=t&&this._placeInTree(e,t)}},{key:"_insertRegion",value:function(e,t){var i=void 0,o=void 0,n=void 0,s=void 0,r=.5*e.size;switch(t){case"NW":i=e.range.minX,o=e.range.minX+r,n=e.range.minY,s=e.range.minY+r;break;case"NE":i=e.range.minX+r,o=e.range.maxX,n=e.range.minY,s=e.range.minY+r;break;case"SW":i=e.range.minX,o=e.range.minX+r,n=e.range.minY+r,s=e.range.maxY;break;case"SE":i=e.range.minX+r,o=e.range.maxX,n=e.range.minY+r,s=e.range.maxY}e.children[t]={centerOfMass:{x:0,y:0},mass:0,range:{minX:i,maxX:o,minY:n,maxY:s},size:.5*e.size,calcSize:2*e.calcSize,children:{data:null},maxWidth:0,level:e.level+1,childrenCount:0}}},{key:"_debug",value:function(e,t){void 0!==this.barnesHutTree&&(e.lineWidth=1,this._drawBranch(this.barnesHutTree.root,e,t))}},{key:"_drawBranch",value:function(e,t,i){void 0===i&&(i="#FF0000"),4===e.childrenCount&&(this._drawBranch(e.children.NW,t),this._drawBranch(e.children.NE,t),this._drawBranch(e.children.SE,t),this._drawBranch(e.children.SW,t)),t.strokeStyle=i,t.beginPath(),t.moveTo(e.range.minX,e.range.minY),t.lineTo(e.range.maxX,e.range.minY),t.stroke(),t.beginPath(),t.moveTo(e.range.maxX,e.range.minY),t.lineTo(e.range.maxX,e.range.maxY),t.stroke(),t.beginPath(),t.moveTo(e.range.maxX,e.range.maxY),t.lineTo(e.range.minX,e.range.maxY),t.stroke(),t.beginPath(),t.moveTo(e.range.minX,e.range.maxY),t.lineTo(e.range.minX,e.range.minY),t.stroke()}}]),e}();t["default"]=n},function(e,t){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),n=function(){function e(t,o,n){i(this,e),this.body=t,this.physicsBody=o,this.setOptions(n)}return o(e,[{key:"setOptions",value:function(e){this.options=e}},{key:"solve",value:function(){for(var e,t,i,o,n,s,r,a,h=this.body.nodes,d=this.physicsBody.physicsNodeIndices,l=this.physicsBody.forces,c=this.options.nodeDistance,u=-2/3/c,f=4/3,p=0;p<d.length-1;p++){r=h[d[p]];for(var v=p+1;v<d.length;v++)a=h[d[v]],e=a.x-r.x,t=a.y-r.y,i=Math.sqrt(e*e+t*t),0===i&&(i=.1*Math.random(),e=i),i<2*c&&(s=i<.5*c?1:u*i+f,s/=i,o=e*s,n=t*s,l[r.id].x-=o,l[r.id].y-=n,l[a.id].x+=o,l[a.id].y+=n)}}}]),e}();t["default"]=n},function(e,t){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),n=function(){function e(t,o,n){i(this,e),this.body=t,this.physicsBody=o,this.setOptions(n)}return o(e,[{key:"setOptions",value:function(e){this.options=e}},{key:"solve",value:function(){var e,t,i,o,n,s,r,a,h,d,l=this.body.nodes,c=this.physicsBody.physicsNodeIndices,u=this.physicsBody.forces,f=this.options.nodeDistance;for(h=0;h<c.length-1;h++)for(r=l[c[h]],d=h+1;d<c.length;d++)if(a=l[c[d]],r.level===a.level){e=a.x-r.x,t=a.y-r.y,i=Math.sqrt(e*e+t*t);var p=.05;s=i<f?-Math.pow(p*i,2)+Math.pow(p*f,2):0,0===i?i=.01:s/=i,o=e*s,n=t*s,u[r.id].x-=o,u[r.id].y-=n,u[a.id].x+=o,u[a.id].y+=n}}}]),e}();t["default"]=n},function(e,t){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),n=function(){function e(t,o,n){i(this,e),this.body=t,this.physicsBody=o,this.setOptions(n)}return o(e,[{key:"setOptions",value:function(e){this.options=e}},{key:"solve",value:function(){for(var e=void 0,t=void 0,i=this.physicsBody.physicsEdgeIndices,o=this.body.edges,n=void 0,s=void 0,r=void 0,a=0;a<i.length;a++)t=o[i[a]],t.connected===!0&&t.toId!==t.fromId&&void 0!==this.body.nodes[t.toId]&&void 0!==this.body.nodes[t.fromId]&&(void 0!==t.edgeType.via?(e=void 0===t.options.length?this.options.springLength:t.options.length,n=t.to,s=t.edgeType.via,r=t.from,this._calculateSpringForce(n,s,.5*e),this._calculateSpringForce(s,r,.5*e)):(e=void 0===t.options.length?1.5*this.options.springLength:t.options.length,this._calculateSpringForce(t.from,t.to,e)))}},{key:"_calculateSpringForce",value:function(e,t,i){var o=e.x-t.x,n=e.y-t.y,s=Math.max(Math.sqrt(o*o+n*n),.01),r=this.options.springConstant*(i-s)/s,a=o*r,h=n*r;void 0!==this.physicsBody.forces[e.id]&&(this.physicsBody.forces[e.id].x+=a,this.physicsBody.forces[e.id].y+=h),void 0!==this.physicsBody.forces[t.id]&&(this.physicsBody.forces[t.id].x-=a,this.physicsBody.forces[t.id].y-=h)}}]),e}();t["default"]=n},function(e,t){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),n=function(){function e(t,o,n){i(this,e),this.body=t,this.physicsBody=o,this.setOptions(n)}return o(e,[{key:"setOptions",value:function(e){this.options=e}},{key:"solve",value:function(){for(var e,t,i,o,n,s,r,a,h=this.body.edges,d=.5,l=this.physicsBody.physicsEdgeIndices,c=this.physicsBody.physicsNodeIndices,u=this.physicsBody.forces,f=0;f<c.length;f++){var p=c[f];u[p].springFx=0,u[p].springFy=0}for(var v=0;v<l.length;v++)t=h[l[v]],t.connected===!0&&(e=void 0===t.options.length?this.options.springLength:t.options.length,i=t.from.x-t.to.x,o=t.from.y-t.to.y,a=Math.sqrt(i*i+o*o),a=0===a?.01:a,r=this.options.springConstant*(e-a)/a,n=i*r,s=o*r,t.to.level!=t.from.level?(void 0!==u[t.toId]&&(u[t.toId].springFx-=n,u[t.toId].springFy-=s),void 0!==u[t.fromId]&&(u[t.fromId].springFx+=n,u[t.fromId].springFy+=s)):(void 0!==u[t.toId]&&(u[t.toId].x-=d*n,u[t.toId].y-=d*s),void 0!==u[t.fromId]&&(u[t.fromId].x+=d*n,u[t.fromId].y+=d*s)));for(var y,g,r=1,b=0;b<c.length;b++){var m=c[b];y=Math.min(r,Math.max(-r,u[m].springFx)),g=Math.min(r,Math.max(-r,u[m].springFy)),u[m].x+=y,u[m].y+=g}for(var _=0,w=0,k=0;k<c.length;k++){var x=c[k];_+=u[x].x,w+=u[x].y}for(var O=_/c.length,E=w/c.length,M=0;M<c.length;M++){var S=c[M];u[S].x-=O,u[S].y-=E}}}]),e}();t["default"]=n},function(e,t){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),n=function(){function e(t,o,n){i(this,e),this.body=t,this.physicsBody=o,this.setOptions(n)}return o(e,[{key:"setOptions",value:function(e){this.options=e}},{key:"solve",value:function(){for(var e=void 0,t=void 0,i=void 0,o=void 0,n=this.body.nodes,s=this.physicsBody.physicsNodeIndices,r=this.physicsBody.forces,a=0;a<s.length;a++){var h=s[a];o=n[h],e=-o.x,t=-o.y,i=Math.sqrt(e*e+t*t),this._calculateForces(i,e,t,r,o)}}},{key:"_calculateForces",value:function(e,t,i,o,n){var s=0===e?0:this.options.centralGravity/e;o[n.id].x=t*s,o[n.id].y=i*s}}]),e}();t["default"]=n},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(45),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"_calculateForces",value:function(e,t,i,o,n){0===e&&(e=.1*Math.random(),t=e),this.overlapAvoidanceFactor<1&&(e=Math.max(.1+this.overlapAvoidanceFactor*o.shape.radius,e-o.shape.radius));var s=o.edges.length+1,r=this.options.gravitationalConstant*n.mass*o.options.mass*s/Math.pow(e,2),a=t*r,h=i*r;this.physicsBody.forces[o.id].x+=a,this.physicsBody.forces[o.id].y+=h}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(50),d=o(h),l=function(e){function t(e,i,o){return n(this,t),s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o))}return r(t,e),a(t,[{key:"_calculateForces",value:function(e,t,i,o,n){if(e>0){var s=n.edges.length+1,r=this.options.centralGravity*s*n.options.mass;o[n.id].x=t*r,o[n.id].y=i*r}}}]),t}(d["default"]);t["default"]=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},r=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),a=i(54),h=o(a),d=i(55),l=o(d),c=i(1),u=function(){function e(t){var i=this;n(this,e),this.body=t,this.clusteredNodes={},this.clusteredEdges={},this.options={},this.defaultOptions={},c.extend(this.options,this.defaultOptions),this.body.emitter.on("_resetData",function(){i.clusteredNodes={},i.clusteredEdges={}})}return r(e,[{key:"setOptions",value:function(e){}},{key:"clusterByHubsize",value:function(e,t){void 0===e?e=this._getHubSize():"object"===("undefined"==typeof e?"undefined":s(e))&&(t=this._checkOptions(e),e=this._getHubSize());for(var i=[],o=0;o<this.body.nodeIndices.length;o++){var n=this.body.nodes[this.body.nodeIndices[o]];n.edges.length>=e&&i.push(n.id)}for(var r=0;r<i.length;r++)this.clusterByConnection(i[r],t,!0);this.body.emitter.emit("_dataChanged")}},{key:"cluster",value:function(){var e=arguments.length<=0||void 0===arguments[0]?{}:arguments[0],t=arguments.length<=1||void 0===arguments[1]||arguments[1];if(void 0===e.joinCondition)throw new Error("Cannot call clusterByNodeData without a joinCondition function in the options.");e=this._checkOptions(e);for(var i={},o={},n=0;n<this.body.nodeIndices.length;n++){var s=this.body.nodeIndices[n],r=this.body.nodes[s],a=h["default"].cloneOptions(r);if(e.joinCondition(a)===!0){i[s]=this.body.nodes[s];for(var d=0;d<r.edges.length;d++){var l=r.edges[d];void 0===this.clusteredEdges[l.id]&&(o[l.id]=l)}}}this._cluster(i,o,e,t)}},{key:"clusterByEdgeCount",value:function(e,t){var i=arguments.length<=2||void 0===arguments[2]||arguments[2];t=this._checkOptions(t);for(var o=[],n={},s=void 0,r=void 0,a=void 0,d=void 0,l=void 0,c=0;c<this.body.nodeIndices.length;c++){var u={},f={};if(d=this.body.nodeIndices[c],void 0===n[d]){l=0,a=this.body.nodes[d],r=[];for(var p=0;p<a.edges.length;p++)s=a.edges[p],void 0===this.clusteredEdges[s.id]&&(s.toId!==s.fromId&&l++,r.push(s));if(l===e){for(var v=!0,y=0;y<r.length;y++){s=r[y];var g=this._getConnectedId(s,d);if(void 0===t.joinCondition)f[s.id]=s,
u[d]=this.body.nodes[d],u[g]=this.body.nodes[g],n[d]=!0;else{var b=h["default"].cloneOptions(this.body.nodes[d]);if(t.joinCondition(b)!==!0){v=!1;break}f[s.id]=s,u[d]=this.body.nodes[d],n[d]=!0}}Object.keys(u).length>0&&Object.keys(f).length>0&&v===!0&&o.push({nodes:u,edges:f})}}}for(var m=0;m<o.length;m++)this._cluster(o[m].nodes,o[m].edges,t,!1);i===!0&&this.body.emitter.emit("_dataChanged")}},{key:"clusterOutliers",value:function(e){var t=arguments.length<=1||void 0===arguments[1]||arguments[1];this.clusterByEdgeCount(1,e,t)}},{key:"clusterBridges",value:function(e){var t=arguments.length<=1||void 0===arguments[1]||arguments[1];this.clusterByEdgeCount(2,e,t)}},{key:"clusterByConnection",value:function(e,t){var i=arguments.length<=2||void 0===arguments[2]||arguments[2];if(void 0===e)throw new Error("No nodeId supplied to clusterByConnection!");if(void 0===this.body.nodes[e])throw new Error("The nodeId given to clusterByConnection does not exist!");var o=this.body.nodes[e];t=this._checkOptions(t,o),void 0===t.clusterNodeProperties.x&&(t.clusterNodeProperties.x=o.x),void 0===t.clusterNodeProperties.y&&(t.clusterNodeProperties.y=o.y),void 0===t.clusterNodeProperties.fixed&&(t.clusterNodeProperties.fixed={},t.clusterNodeProperties.fixed.x=o.options.fixed.x,t.clusterNodeProperties.fixed.y=o.options.fixed.y);var n={},s={},r=o.id,a=h["default"].cloneOptions(o);n[r]=o;for(var d=0;d<o.edges.length;d++){var l=o.edges[d];if(void 0===this.clusteredEdges[l.id]){var c=this._getConnectedId(l,r);if(void 0===this.clusteredNodes[c])if(c!==r)if(void 0===t.joinCondition)s[l.id]=l,n[c]=this.body.nodes[c];else{var u=h["default"].cloneOptions(this.body.nodes[c]);t.joinCondition(a,u)===!0&&(s[l.id]=l,n[c]=this.body.nodes[c])}else s[l.id]=l}}this._cluster(n,s,t,i)}},{key:"_createClusterEdges",value:function(e,t,i,o){for(var n=void 0,s=void 0,r=void 0,a=void 0,d=void 0,l=void 0,u=Object.keys(e),f=[],p=0;p<u.length;p++){s=u[p],r=e[s];for(var v=0;v<r.edges.length;v++)n=r.edges[v],void 0===this.clusteredEdges[n.id]&&(n.toId==n.fromId?t[n.id]=n:n.toId==s?(a=i.id,d=n.fromId,l=d):(a=n.toId,d=i.id,l=a),void 0===e[l]&&f.push({edge:n,fromId:d,toId:a}))}for(var y=0;y<f.length;y++){var g=f[y].edge,b=h["default"].cloneOptions(g,"edge");c.deepExtend(b,o),b.from=f[y].fromId,b.to=f[y].toId,b.id="clusterEdge:"+c.randomUUID();var m=this.body.functions.createEdge(b);m.clusteringEdgeReplacingId=g.id,this.body.edges[m.id]=m,m.connect(),this._backupEdgeOptions(g),g.setOptions({physics:!1,hidden:!0})}}},{key:"_checkOptions",value:function(){var e=arguments.length<=0||void 0===arguments[0]?{}:arguments[0];return void 0===e.clusterEdgeProperties&&(e.clusterEdgeProperties={}),void 0===e.clusterNodeProperties&&(e.clusterNodeProperties={}),e}},{key:"_cluster",value:function(e,t,i){var o=arguments.length<=3||void 0===arguments[3]||arguments[3];if(!(Object.keys(e).length<2)){for(var n in e)if(e.hasOwnProperty(n)&&void 0!==this.clusteredNodes[n])return;var s=c.deepExtend({},i.clusterNodeProperties);if(void 0!==i.processProperties){var r=[];for(var a in e)if(e.hasOwnProperty(a)){var d=h["default"].cloneOptions(e[a]);r.push(d)}var u=[];for(var f in t)if(t.hasOwnProperty(f)&&"clusterEdge:"!==f.substr(0,12)){var p=h["default"].cloneOptions(t[f],"edge");u.push(p)}if(s=i.processProperties(s,r,u),!s)throw new Error("The processProperties function does not return properties!")}void 0===s.id&&(s.id="cluster:"+c.randomUUID());var v=s.id;void 0===s.label&&(s.label="cluster");var y=void 0;void 0===s.x&&(y=this._getClusterPosition(e),s.x=y.x),void 0===s.y&&(void 0===y&&(y=this._getClusterPosition(e)),s.y=y.y),s.id=v;var g=this.body.functions.createNode(s,l["default"]);g.isCluster=!0,g.containedNodes=e,g.containedEdges=t,g.clusterEdgeProperties=i.clusterEdgeProperties,this.body.nodes[s.id]=g,this._createClusterEdges(e,t,s,i.clusterEdgeProperties);for(var b in t)if(t.hasOwnProperty(b)&&void 0!==this.body.edges[b]){var m=this.body.edges[b];this._backupEdgeOptions(m),m.setOptions({physics:!1,hidden:!0})}for(var _ in e)e.hasOwnProperty(_)&&(this.clusteredNodes[_]={clusterId:s.id,node:this.body.nodes[_]},this.body.nodes[_].setOptions({hidden:!0,physics:!1}));s.id=void 0,o===!0&&this.body.emitter.emit("_dataChanged")}}},{key:"_backupEdgeOptions",value:function(e){void 0===this.clusteredEdges[e.id]&&(this.clusteredEdges[e.id]={physics:e.options.physics,hidden:e.options.hidden})}},{key:"_restoreEdge",value:function(e){var t=this.clusteredEdges[e.id];void 0!==t&&(e.setOptions({physics:t.physics,hidden:t.hidden}),delete this.clusteredEdges[e.id])}},{key:"isCluster",value:function(e){return void 0!==this.body.nodes[e]?this.body.nodes[e].isCluster===!0:(console.log("Node does not exist."),!1)}},{key:"_getClusterPosition",value:function(e){for(var t=Object.keys(e),i=e[t[0]].x,o=e[t[0]].x,n=e[t[0]].y,s=e[t[0]].y,r=void 0,a=1;a<t.length;a++)r=e[t[a]],i=r.x<i?r.x:i,o=r.x>o?r.x:o,n=r.y<n?r.y:n,s=r.y>s?r.y:s;return{x:.5*(i+o),y:.5*(n+s)}}},{key:"openCluster",value:function(e,t){var i=arguments.length<=2||void 0===arguments[2]||arguments[2];if(void 0===e)throw new Error("No clusterNodeId supplied to openCluster.");if(void 0===this.body.nodes[e])throw new Error("The clusterNodeId supplied to openCluster does not exist.");if(void 0===this.body.nodes[e].containedNodes)return void console.log("The node:"+e+" is not a cluster.");var o=this.body.nodes[e],n=o.containedNodes,s=o.containedEdges;if(void 0!==t&&void 0!==t.releaseFunction&&"function"==typeof t.releaseFunction){var r={},a={x:o.x,y:o.y};for(var d in n)if(n.hasOwnProperty(d)){var l=this.body.nodes[d];r[d]={x:l.x,y:l.y}}var u=t.releaseFunction(a,r);for(var f in n)if(n.hasOwnProperty(f)){var p=this.body.nodes[f];void 0!==u[f]&&(p.x=void 0===u[f].x?o.x:u[f].x,p.y=void 0===u[f].y?o.y:u[f].y)}}else for(var v in n)if(n.hasOwnProperty(v)){var y=this.body.nodes[v];y=n[v],y.options.fixed.x===!1&&(y.x=o.x),y.options.fixed.y===!1&&(y.y=o.y)}for(var g in n)if(n.hasOwnProperty(g)){var b=this.body.nodes[g];b.vx=o.vx,b.vy=o.vy,b.setOptions({hidden:!1,physics:!0}),delete this.clusteredNodes[g]}for(var m=[],_=0;_<o.edges.length;_++)m.push(o.edges[_]);for(var w=0;w<m.length;w++){var k=m[w],x=this._getConnectedId(k,e);if(void 0!==this.clusteredNodes[x]){var O=this.body.nodes[this.clusteredNodes[x].clusterId],E=this.body.edges[k.clusteringEdgeReplacingId];if(void 0!==E){O.containedEdges[E.id]=E,delete s[E.id];var M=E.fromId,S=E.toId;E.toId==x?S=this.clusteredNodes[x].clusterId:M=this.clusteredNodes[x].clusterId;var D=h["default"].cloneOptions(E,"edge");c.deepExtend(D,O.clusterEdgeProperties);var C="clusterEdge:"+c.randomUUID();c.deepExtend(D,{from:M,to:S,hidden:!1,physics:!0,id:C});var T=this.body.functions.createEdge(D);T.clusteringEdgeReplacingId=E.id,this.body.edges[C]=T,this.body.edges[C].connect()}}else{var P=this.body.edges[k.clusteringEdgeReplacingId];void 0!==P&&this._restoreEdge(P)}k.cleanup(),k.disconnect(),delete this.body.edges[k.id]}for(var F in s)s.hasOwnProperty(F)&&this._restoreEdge(s[F]);delete this.body.nodes[e],i===!0&&this.body.emitter.emit("_dataChanged")}},{key:"getNodesInCluster",value:function(e){var t=[];if(this.isCluster(e)===!0){var i=this.body.nodes[e].containedNodes;for(var o in i)i.hasOwnProperty(o)&&t.push(this.body.nodes[o].id)}return t}},{key:"findNode",value:function(e){for(var t=[],i=100,o=0;void 0!==this.clusteredNodes[e]&&o<i;)t.push(this.body.nodes[e].id),e=this.clusteredNodes[e].clusterId,o++;return t.push(this.body.nodes[e].id),t.reverse(),t}},{key:"_getConnectedId",value:function(e,t){return e.toId!=t?e.toId:e.fromId!=t?e.fromId:e.fromId}},{key:"_getHubSize",value:function(){for(var e=0,t=0,i=0,o=0,n=0;n<this.body.nodeIndices.length;n++){var s=this.body.nodes[this.body.nodeIndices[n]];s.edges.length>o&&(o=s.edges.length),e+=s.edges.length,t+=Math.pow(s.edges.length,2),i+=1}e/=i,t/=i;var r=t-Math.pow(e,2),a=Math.sqrt(r),h=Math.floor(e+2*a);return h>o&&(h=o),h}}]),e}();t["default"]=u},function(e,t,i){function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),s=i(1),r=function(){function e(){o(this,e)}return n(e,null,[{key:"getRange",value:function(e){var t,i=arguments.length<=1||void 0===arguments[1]?[]:arguments[1],o=1e9,n=-1e9,s=1e9,r=-1e9;if(i.length>0)for(var a=0;a<i.length;a++)t=e[i[a]],s>t.shape.boundingBox.left&&(s=t.shape.boundingBox.left),r<t.shape.boundingBox.right&&(r=t.shape.boundingBox.right),o>t.shape.boundingBox.top&&(o=t.shape.boundingBox.top),n<t.shape.boundingBox.bottom&&(n=t.shape.boundingBox.bottom);return 1e9===s&&r===-1e9&&1e9===o&&n===-1e9&&(o=0,n=0,s=0,r=0),{minX:s,maxX:r,minY:o,maxY:n}}},{key:"getRangeCore",value:function(e){var t,i=arguments.length<=1||void 0===arguments[1]?[]:arguments[1],o=1e9,n=-1e9,s=1e9,r=-1e9;if(i.length>0)for(var a=0;a<i.length;a++)t=e[i[a]],s>t.x&&(s=t.x),r<t.x&&(r=t.x),o>t.y&&(o=t.y),n<t.y&&(n=t.y);return 1e9===s&&r===-1e9&&1e9===o&&n===-1e9&&(o=0,n=0,s=0,r=0),{minX:s,maxX:r,minY:o,maxY:n}}},{key:"findCenter",value:function(e){return{x:.5*(e.maxX+e.minX),y:.5*(e.maxY+e.minY)}}},{key:"cloneOptions",value:function(e,t){var i={};return void 0===t||"node"===t?(s.deepExtend(i,e.options,!0),i.x=e.x,i.y=e.y,i.amountOfConnections=e.edges.length):s.deepExtend(i,e.options,!0),i}}]),e}();t["default"]=r},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function r(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var a=i(15),h=o(a),d=function(e){function t(e,i,o,r,a){n(this,t);var h=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,i,o,r,a));return h.isCluster=!0,h.containedNodes={},h.containedEdges={},h}return r(t,e),t}(h["default"]);t["default"]=d},function(e,t,i){function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}();"undefined"!=typeof window&&(window.requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame);var s=i(1),r=function(){function e(t,i){o(this,e),this.body=t,this.canvas=i,this.redrawRequested=!1,this.renderTimer=void 0,this.requiresTimeout=!0,this.renderingActive=!1,this.renderRequests=0,this.pixelRatio=void 0,this.allowRedraw=!0,this.dragging=!1,this.options={},this.defaultOptions={hideEdgesOnDrag:!1,hideNodesOnDrag:!1},s.extend(this.options,this.defaultOptions),this._determineBrowserMethod(),this.bindEventListeners()}return n(e,[{key:"bindEventListeners",value:function(){var e=this;this.body.emitter.on("dragStart",function(){e.dragging=!0}),this.body.emitter.on("dragEnd",function(){return e.dragging=!1}),this.body.emitter.on("_resizeNodes",function(){return e._resizeNodes()}),this.body.emitter.on("_redraw",function(){e.renderingActive===!1&&e._redraw()}),this.body.emitter.on("_blockRedraw",function(){e.allowRedraw=!1}),this.body.emitter.on("_allowRedraw",function(){e.allowRedraw=!0,e.redrawRequested=!1}),this.body.emitter.on("_requestRedraw",this._requestRedraw.bind(this)),this.body.emitter.on("_startRendering",function(){e.renderRequests+=1,e.renderingActive=!0,e._startRendering()}),this.body.emitter.on("_stopRendering",function(){e.renderRequests-=1,e.renderingActive=e.renderRequests>0,e.renderTimer=void 0}),this.body.emitter.on("destroy",function(){e.renderRequests=0,e.allowRedraw=!1,e.renderingActive=!1,e.requiresTimeout===!0?clearTimeout(e.renderTimer):cancelAnimationFrame(e.renderTimer),e.body.emitter.off()})}},{key:"setOptions",value:function(e){if(void 0!==e){var t=["hideEdgesOnDrag","hideNodesOnDrag"];s.selectiveDeepExtend(t,this.options,e)}}},{key:"_startRendering",value:function(){this.renderingActive===!0&&void 0===this.renderTimer&&(this.requiresTimeout===!0?this.renderTimer=window.setTimeout(this._renderStep.bind(this),this.simulationInterval):this.renderTimer=window.requestAnimationFrame(this._renderStep.bind(this)))}},{key:"_renderStep",value:function(){this.renderingActive===!0&&(this.renderTimer=void 0,this.requiresTimeout===!0&&this._startRendering(),this._redraw(),this.requiresTimeout===!1&&this._startRendering())}},{key:"redraw",value:function(){this.body.emitter.emit("setSize"),this._redraw()}},{key:"_requestRedraw",value:function(){var e=this;this.redrawRequested!==!0&&this.renderingActive===!1&&this.allowRedraw===!0&&(this.redrawRequested=!0,this.requiresTimeout===!0?window.setTimeout(function(){e._redraw(!1)},0):window.requestAnimationFrame(function(){e._redraw(!1)}))}},{key:"_redraw",value:function(){var e=!(arguments.length<=0||void 0===arguments[0])&&arguments[0];if(this.allowRedraw===!0){this.body.emitter.emit("initRedraw"),this.redrawRequested=!1;var t=this.canvas.frame.canvas.getContext("2d");0!==this.canvas.frame.canvas.width&&0!==this.canvas.frame.canvas.height||this.canvas.setSize(),this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1),t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);var i=this.canvas.frame.canvas.clientWidth,o=this.canvas.frame.canvas.clientHeight;if(t.clearRect(0,0,i,o),0===this.canvas.frame.clientWidth)return;t.save(),t.translate(this.body.view.translation.x,this.body.view.translation.y),t.scale(this.body.view.scale,this.body.view.scale),t.beginPath(),this.body.emitter.emit("beforeDrawing",t),t.closePath(),e===!1&&(this.dragging===!1||this.dragging===!0&&this.options.hideEdgesOnDrag===!1)&&this._drawEdges(t),(this.dragging===!1||this.dragging===!0&&this.options.hideNodesOnDrag===!1)&&this._drawNodes(t,e),t.beginPath(),this.body.emitter.emit("afterDrawing",t),t.closePath(),t.restore(),e===!0&&t.clearRect(0,0,i,o)}}},{key:"_resizeNodes",value:function(){var e=this.canvas.frame.canvas.getContext("2d");void 0===this.pixelRatio&&(this.pixelRatio=(window.devicePixelRatio||1)/(e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1)),e.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0),e.save(),e.translate(this.body.view.translation.x,this.body.view.translation.y),e.scale(this.body.view.scale,this.body.view.scale);var t=this.body.nodes,i=void 0;for(var o in t)t.hasOwnProperty(o)&&(i=t[o],i.resize(e),i.updateBoundingBox(e,i.selected));e.restore()}},{key:"_drawNodes",value:function(e){for(var t=!(arguments.length<=1||void 0===arguments[1])&&arguments[1],i=this.body.nodes,o=this.body.nodeIndices,n=void 0,s=[],r=20,a=this.canvas.DOMtoCanvas({x:-r,y:-r}),h=this.canvas.DOMtoCanvas({x:this.canvas.frame.canvas.clientWidth+r,y:this.canvas.frame.canvas.clientHeight+r}),d={top:a.y,left:a.x,bottom:h.y,right:h.x},l=0;l<o.length;l++)n=i[o[l]],n.isSelected()?s.push(o[l]):t===!0?n.draw(e):n.isBoundingBoxOverlappingWith(d)===!0?n.draw(e):n.updateBoundingBox(e,n.selected);for(var c=0;c<s.length;c++)n=i[s[c]],n.draw(e)}},{key:"_drawEdges",value:function(e){for(var t=this.body.edges,i=this.body.edgeIndices,o=void 0,n=0;n<i.length;n++)o=t[i[n]],o.connected===!0&&o.draw(e)}},{key:"_determineBrowserMethod",value:function(){if("undefined"!=typeof window){var e=navigator.userAgent.toLowerCase();this.requiresTimeout=!1,e.indexOf("msie 9.0")!=-1?this.requiresTimeout=!0:e.indexOf("safari")!=-1&&e.indexOf("chrome")<=-1&&(this.requiresTimeout=!0)}else this.requiresTimeout=!0}}]),e}();t["default"]=r},function(e,t,i){function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),s=i(58),r=i(61),a=i(1),h=function(){function e(t){o(this,e),this.body=t,this.pixelRatio=1,this.resizeTimer=void 0,this.resizeFunction=this._onResize.bind(this),this.cameraState={},this.initialized=!1,this.options={},this.defaultOptions={autoResize:!0,height:"100%",width:"100%"},a.extend(this.options,this.defaultOptions),this.bindEventListeners()}return n(e,[{key:"bindEventListeners",value:function(){var e=this;this.body.emitter.once("resize",function(t){0!==t.width&&(e.body.view.translation.x=.5*t.width),0!==t.height&&(e.body.view.translation.y=.5*t.height)}),this.body.emitter.on("setSize",this.setSize.bind(this)),this.body.emitter.on("destroy",function(){e.hammerFrame.destroy(),e.hammer.destroy(),e._cleanUp()})}},{key:"setOptions",value:function(e){var t=this;if(void 0!==e){var i=["width","height","autoResize"];a.selectiveDeepExtend(i,this.options,e)}this.options.autoResize===!0&&(this._cleanUp(),this.resizeTimer=setInterval(function(){var e=t.setSize();e===!0&&t.body.emitter.emit("_requestRedraw")},1e3),this.resizeFunction=this._onResize.bind(this),a.addEventListener(window,"resize",this.resizeFunction))}},{key:"_cleanUp",value:function(){void 0!==this.resizeTimer&&clearInterval(this.resizeTimer),a.removeEventListener(window,"resize",this.resizeFunction),this.resizeFunction=void 0}},{key:"_onResize",value:function(){this.setSize(),this.body.emitter.emit("_redraw")}},{key:"_getCameraState",value:function(){var e=arguments.length<=0||void 0===arguments[0]?this.pixelRatio:arguments[0];this.initialized===!0&&(this.cameraState.previousWidth=this.frame.canvas.width/e,this.cameraState.previousHeight=this.frame.canvas.height/e,this.cameraState.scale=this.body.view.scale,this.cameraState.position=this.DOMtoCanvas({x:.5*this.frame.canvas.width/e,y:.5*this.frame.canvas.height/e}))}},{key:"_setCameraState",value:function(){if(void 0!==this.cameraState.scale&&0!==this.frame.canvas.clientWidth&&0!==this.frame.canvas.clientHeight&&0!==this.pixelRatio&&this.cameraState.previousWidth>0){var e=this.frame.canvas.width/this.pixelRatio/this.cameraState.previousWidth,t=this.frame.canvas.height/this.pixelRatio/this.cameraState.previousHeight,i=this.cameraState.scale;1!=e&&1!=t?i=.5*this.cameraState.scale*(e+t):1!=e?i=this.cameraState.scale*e:1!=t&&(i=this.cameraState.scale*t),this.body.view.scale=i;var o=this.DOMtoCanvas({x:.5*this.frame.canvas.clientWidth,y:.5*this.frame.canvas.clientHeight}),n={x:o.x-this.cameraState.position.x,y:o.y-this.cameraState.position.y};this.body.view.translation.x+=n.x*this.body.view.scale,this.body.view.translation.y+=n.y*this.body.view.scale}}},{key:"_prepareValue",value:function(e){if("number"==typeof e)return e+"px";if("string"==typeof e){if(e.indexOf("%")!==-1||e.indexOf("px")!==-1)return e;if(e.indexOf("%")===-1)return e+"px"}throw new Error("Could not use the value supplied for width or height:"+e)}},{key:"_create",value:function(){for(;this.body.container.hasChildNodes();)this.body.container.removeChild(this.body.container.firstChild);if(this.frame=document.createElement("div"),this.frame.className="vis-network",this.frame.style.position="relative",this.frame.style.overflow="hidden",this.frame.tabIndex=900,this.frame.canvas=document.createElement("canvas"),this.frame.canvas.style.position="relative",this.frame.appendChild(this.frame.canvas),this.frame.canvas.getContext){var e=this.frame.canvas.getContext("2d");this.pixelRatio=(window.devicePixelRatio||1)/(e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1),this.frame.canvas.getContext("2d").setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}else{var t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerHTML="Error: your browser does not support HTML canvas",this.frame.canvas.appendChild(t)}this.body.container.appendChild(this.frame),this.body.view.scale=1,this.body.view.translation={x:.5*this.frame.canvas.clientWidth,y:.5*this.frame.canvas.clientHeight},this._bindHammer()}},{key:"_bindHammer",value:function(){var e=this;void 0!==this.hammer&&this.hammer.destroy(),this.drag={},this.pinch={},this.hammer=new s(this.frame.canvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.get("pan").set({threshold:5,direction:s.DIRECTION_ALL}),r.onTouch(this.hammer,function(t){e.body.eventListeners.onTouch(t)}),this.hammer.on("tap",function(t){e.body.eventListeners.onTap(t)}),this.hammer.on("doubletap",function(t){e.body.eventListeners.onDoubleTap(t)}),this.hammer.on("press",function(t){e.body.eventListeners.onHold(t)}),this.hammer.on("panstart",function(t){e.body.eventListeners.onDragStart(t)}),this.hammer.on("panmove",function(t){e.body.eventListeners.onDrag(t)}),this.hammer.on("panend",function(t){e.body.eventListeners.onDragEnd(t)}),this.hammer.on("pinch",function(t){e.body.eventListeners.onPinch(t)}),this.frame.canvas.addEventListener("mousewheel",function(t){e.body.eventListeners.onMouseWheel(t)}),this.frame.canvas.addEventListener("DOMMouseScroll",function(t){e.body.eventListeners.onMouseWheel(t)}),this.frame.canvas.addEventListener("mousemove",function(t){e.body.eventListeners.onMouseMove(t)}),this.frame.canvas.addEventListener("contextmenu",function(t){e.body.eventListeners.onContext(t)}),this.hammerFrame=new s(this.frame),r.onRelease(this.hammerFrame,function(t){e.body.eventListeners.onRelease(t)})}},{key:"setSize",value:function(){var e=arguments.length<=0||void 0===arguments[0]?this.options.width:arguments[0],t=arguments.length<=1||void 0===arguments[1]?this.options.height:arguments[1];e=this._prepareValue(e),t=this._prepareValue(t);var i=!1,o=this.frame.canvas.width,n=this.frame.canvas.height,s=this.frame.canvas.getContext("2d"),r=this.pixelRatio;return this.pixelRatio=(window.devicePixelRatio||1)/(s.webkitBackingStorePixelRatio||s.mozBackingStorePixelRatio||s.msBackingStorePixelRatio||s.oBackingStorePixelRatio||s.backingStorePixelRatio||1),e!=this.options.width||t!=this.options.height||this.frame.style.width!=e||this.frame.style.height!=t?(this._getCameraState(r),this.frame.style.width=e,this.frame.style.height=t,this.frame.canvas.style.width="100%",this.frame.canvas.style.height="100%",this.frame.canvas.width=Math.round(this.frame.canvas.clientWidth*this.pixelRatio),this.frame.canvas.height=Math.round(this.frame.canvas.clientHeight*this.pixelRatio),this.options.width=e,this.options.height=t,i=!0):(this.frame.canvas.width==Math.round(this.frame.canvas.clientWidth*this.pixelRatio)&&this.frame.canvas.height==Math.round(this.frame.canvas.clientHeight*this.pixelRatio)||this._getCameraState(r),this.frame.canvas.width!=Math.round(this.frame.canvas.clientWidth*this.pixelRatio)&&(this.frame.canvas.width=Math.round(this.frame.canvas.clientWidth*this.pixelRatio),i=!0),this.frame.canvas.height!=Math.round(this.frame.canvas.clientHeight*this.pixelRatio)&&(this.frame.canvas.height=Math.round(this.frame.canvas.clientHeight*this.pixelRatio),i=!0)),i===!0&&(this.body.emitter.emit("resize",{width:Math.round(this.frame.canvas.width/this.pixelRatio),height:Math.round(this.frame.canvas.height/this.pixelRatio),oldWidth:Math.round(o/this.pixelRatio),oldHeight:Math.round(n/this.pixelRatio)}),this._setCameraState()),this.initialized=!0,i}},{key:"_XconvertDOMtoCanvas",value:function(e){return(e-this.body.view.translation.x)/this.body.view.scale}},{key:"_XconvertCanvasToDOM",value:function(e){return e*this.body.view.scale+this.body.view.translation.x}},{key:"_YconvertDOMtoCanvas",value:function(e){return(e-this.body.view.translation.y)/this.body.view.scale}},{key:"_YconvertCanvasToDOM",value:function(e){return e*this.body.view.scale+this.body.view.translation.y}},{key:"canvasToDOM",value:function(e){return{x:this._XconvertCanvasToDOM(e.x),y:this._YconvertCanvasToDOM(e.y)}}},{key:"DOMtoCanvas",value:function(e){return{x:this._XconvertDOMtoCanvas(e.x),y:this._YconvertDOMtoCanvas(e.y)}}}]),e}();t["default"]=h},function(e,t,i){if("undefined"!=typeof window){var o=i(59),n=window.Hammer||i(60);e.exports=o(n,{preventDefault:"mouse"})}else e.exports=function(){throw Error("hammer.js is only available in a browser, not in node.js.")}},function(e,t,i){var o,n,s;!function(i){n=[],o=i,s="function"==typeof o?o.apply(t,n):o,!(void 0!==s&&(e.exports=s))}(function(){var e=null;return function t(i,o){function n(e){return e.match(/[^ ]+/g)}function s(t){if("hammer.input"!==t.type){if(t.srcEvent._handled||(t.srcEvent._handled={}),t.srcEvent._handled[t.type])return;t.srcEvent._handled[t.type]=!0}var i=!1;t.stopPropagation=function(){i=!0};var o=t.srcEvent.stopPropagation.bind(t.srcEvent);"function"==typeof o&&(t.srcEvent.stopPropagation=function(){o(),t.stopPropagation()}),t.firstTarget=e;for(var n=e;n&&!i;){var s=n.hammer;if(s)for(var r,a=0;a<s.length;a++)if(r=s[a]._handlers[t.type])for(var h=0;h<r.length&&!i;h++)r[h](t);n=n.parentNode}}var r=o||{preventDefault:!1};if(i.Manager){var a=i,h=function(e,i){var o=Object.create(r);return i&&a.assign(o,i),t(new a(e,o),o)};return a.assign(h,a),h.Manager=function(e,i){var o=Object.create(r);return i&&a.assign(o,i),t(new a.Manager(e,o),o)},h}var d=Object.create(i),l=i.element;return l.hammer||(l.hammer=[]),l.hammer.push(d),i.on("hammer.input",function(t){r.preventDefault!==!0&&r.preventDefault!==t.pointerType||t.preventDefault(),t.isFirst&&(e=t.target)}),d._handlers={},d.on=function(e,t){return n(e).forEach(function(e){var o=d._handlers[e];o||(d._handlers[e]=o=[],i.on(e,s)),o.push(t)}),d},d.off=function(e,t){return n(e).forEach(function(e){var o=d._handlers[e];o&&(o=t?o.filter(function(e){return e!==t}):[],o.length>0?d._handlers[e]=o:(i.off(e,s),delete d._handlers[e]))}),d},d.emit=function(t,o){e=o.target,i.emit(t,o)},d.destroy=function(){var e=i.element.hammer,t=e.indexOf(d);t!==-1&&e.splice(t,1),e.length||delete i.element.hammer,d._handlers={},i.destroy()},d}})},function(e,t,i){var o;/*! Hammer.JS - v2.0.7 - 2016-04-22
   * http://hammerjs.github.io/
   *
   * Copyright (c) 2016 Jorik Tangelder;
   * Licensed under the MIT license */
!function(n,s,r,a){function h(e,t,i){return setTimeout(f(e,i),t)}function d(e,t,i){return!!Array.isArray(e)&&(l(e,i[t],i),!0)}function l(e,t,i){var o;if(e)if(e.forEach)e.forEach(t,i);else if(e.length!==a)for(o=0;o<e.length;)t.call(i,e[o],o,e),o++;else for(o in e)e.hasOwnProperty(o)&&t.call(i,e[o],o,e)}function c(e,t,i){var o="DEPRECATED METHOD: "+t+"\n"+i+" AT \n";return function(){var t=new Error("get-stack-trace"),i=t&&t.stack?t.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",s=n.console&&(n.console.warn||n.console.log);return s&&s.call(n.console,o,i),e.apply(this,arguments)}}function u(e,t,i){var o,n=t.prototype;o=e.prototype=Object.create(n),o.constructor=e,o._super=n,i&&ve(o,i)}function f(e,t){return function(){return e.apply(t,arguments)}}function p(e,t){return typeof e==be?e.apply(t?t[0]||a:a,t):e}function v(e,t){return e===a?t:e}function y(e,t,i){l(_(t),function(t){e.addEventListener(t,i,!1)})}function g(e,t,i){l(_(t),function(t){e.removeEventListener(t,i,!1)})}function b(e,t){for(;e;){if(e==t)return!0;e=e.parentNode}return!1}function m(e,t){return e.indexOf(t)>-1}function _(e){return e.trim().split(/\s+/g)}function w(e,t,i){if(e.indexOf&&!i)return e.indexOf(t);for(var o=0;o<e.length;){if(i&&e[o][i]==t||!i&&e[o]===t)return o;o++}return-1}function k(e){return Array.prototype.slice.call(e,0)}function x(e,t,i){for(var o=[],n=[],s=0;s<e.length;){var r=t?e[s][t]:e[s];w(n,r)<0&&o.push(e[s]),n[s]=r,s++}return i&&(o=t?o.sort(function(e,i){return e[t]>i[t]}):o.sort()),o}function O(e,t){for(var i,o,n=t[0].toUpperCase()+t.slice(1),s=0;s<ye.length;){if(i=ye[s],o=i?i+n:t,o in e)return o;s++}return a}function E(){return Oe++}function M(e){var t=e.ownerDocument||e;return t.defaultView||t.parentWindow||n}function S(e,t){var i=this;this.manager=e,this.callback=t,this.element=e.element,this.target=e.options.inputTarget,this.domHandler=function(t){p(e.options.enable,[e])&&i.handler(t)},this.init()}function D(e){var t,i=e.options.inputClass;return new(t=i?i:Se?W:De?V:Me?G:H)(e,C)}function C(e,t,i){var o=i.pointers.length,n=i.changedPointers.length,s=t&Ie&&o-n===0,r=t&(Ne|ze)&&o-n===0;i.isFirst=!!s,i.isFinal=!!r,s&&(e.session={}),i.eventType=t,T(e,i),e.emit("hammer.input",i),e.recognize(i),e.session.prevInput=i}function T(e,t){var i=e.session,o=t.pointers,n=o.length;i.firstInput||(i.firstInput=B(t)),n>1&&!i.firstMultiple?i.firstMultiple=B(t):1===n&&(i.firstMultiple=!1);var s=i.firstInput,r=i.firstMultiple,a=r?r.center:s.center,h=t.center=I(o);t.timeStamp=we(),t.deltaTime=t.timeStamp-s.timeStamp,t.angle=R(a,h),t.distance=z(a,h),P(i,t),t.offsetDirection=N(t.deltaX,t.deltaY);var d=j(t.deltaTime,t.deltaX,t.deltaY);t.overallVelocityX=d.x,t.overallVelocityY=d.y,t.overallVelocity=_e(d.x)>_e(d.y)?d.x:d.y,t.scale=r?L(r.pointers,o):1,t.rotation=r?A(r.pointers,o):0,t.maxPointers=i.prevInput?t.pointers.length>i.prevInput.maxPointers?t.pointers.length:i.prevInput.maxPointers:t.pointers.length,F(i,t);var l=e.element;b(t.srcEvent.target,l)&&(l=t.srcEvent.target),t.target=l}function P(e,t){var i=t.center,o=e.offsetDelta||{},n=e.prevDelta||{},s=e.prevInput||{};t.eventType!==Ie&&s.eventType!==Ne||(n=e.prevDelta={x:s.deltaX||0,y:s.deltaY||0},o=e.offsetDelta={x:i.x,y:i.y}),t.deltaX=n.x+(i.x-o.x),t.deltaY=n.y+(i.y-o.y)}function F(e,t){var i,o,n,s,r=e.lastInterval||t,h=t.timeStamp-r.timeStamp;if(t.eventType!=ze&&(h>Be||r.velocity===a)){var d=t.deltaX-r.deltaX,l=t.deltaY-r.deltaY,c=j(h,d,l);o=c.x,n=c.y,i=_e(c.x)>_e(c.y)?c.x:c.y,s=N(d,l),e.lastInterval=t}else i=r.velocity,o=r.velocityX,n=r.velocityY,s=r.direction;t.velocity=i,t.velocityX=o,t.velocityY=n,t.direction=s}function B(e){for(var t=[],i=0;i<e.pointers.length;)t[i]={clientX:me(e.pointers[i].clientX),clientY:me(e.pointers[i].clientY)},i++;return{timeStamp:we(),pointers:t,center:I(t),deltaX:e.deltaX,deltaY:e.deltaY}}function I(e){var t=e.length;if(1===t)return{x:me(e[0].clientX),y:me(e[0].clientY)};for(var i=0,o=0,n=0;n<t;)i+=e[n].clientX,o+=e[n].clientY,n++;return{x:me(i/t),y:me(o/t)}}function j(e,t,i){return{x:t/e||0,y:i/e||0}}function N(e,t){return e===t?Re:_e(e)>=_e(t)?e<0?Ae:Le:t<0?He:We}function z(e,t,i){i||(i=qe);var o=t[i[0]]-e[i[0]],n=t[i[1]]-e[i[1]];return Math.sqrt(o*o+n*n)}function R(e,t,i){i||(i=qe);var o=t[i[0]]-e[i[0]],n=t[i[1]]-e[i[1]];return 180*Math.atan2(n,o)/Math.PI}function A(e,t){return R(t[1],t[0],Ge)+R(e[1],e[0],Ge)}function L(e,t){return z(t[0],t[1],Ge)/z(e[0],e[1],Ge)}function H(){this.evEl=Ke,this.evWin=Ze,this.pressed=!1,S.apply(this,arguments)}function W(){this.evEl=$e,this.evWin=et,S.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}function Y(){this.evTarget=it,this.evWin=ot,this.started=!1,S.apply(this,arguments)}function U(e,t){var i=k(e.touches),o=k(e.changedTouches);return t&(Ne|ze)&&(i=x(i.concat(o),"identifier",!0)),[i,o]}function V(){this.evTarget=st,this.targetIds={},S.apply(this,arguments)}function q(e,t){var i=k(e.touches),o=this.targetIds;if(t&(Ie|je)&&1===i.length)return o[i[0].identifier]=!0,[i,i];var n,s,r=k(e.changedTouches),a=[],h=this.target;if(s=i.filter(function(e){return b(e.target,h)}),t===Ie)for(n=0;n<s.length;)o[s[n].identifier]=!0,n++;for(n=0;n<r.length;)o[r[n].identifier]&&a.push(r[n]),t&(Ne|ze)&&delete o[r[n].identifier],n++;return a.length?[x(s.concat(a),"identifier",!0),a]:void 0}function G(){S.apply(this,arguments);var e=f(this.handler,this);this.touch=new V(this.manager,e),this.mouse=new H(this.manager,e),this.primaryTouch=null,this.lastTouches=[]}function X(e,t){e&Ie?(this.primaryTouch=t.changedPointers[0].identifier,K.call(this,t)):e&(Ne|ze)&&K.call(this,t)}function K(e){var t=e.changedPointers[0];if(t.identifier===this.primaryTouch){var i={x:t.clientX,y:t.clientY};this.lastTouches.push(i);var o=this.lastTouches,n=function(){var e=o.indexOf(i);e>-1&&o.splice(e,1)};setTimeout(n,rt)}}function Z(e){for(var t=e.srcEvent.clientX,i=e.srcEvent.clientY,o=0;o<this.lastTouches.length;o++){var n=this.lastTouches[o],s=Math.abs(t-n.x),r=Math.abs(i-n.y);if(s<=at&&r<=at)return!0}return!1}function Q(e,t){this.manager=e,this.set(t)}function J(e){if(m(e,ft))return ft;var t=m(e,pt),i=m(e,vt);return t&&i?ft:t||i?t?pt:vt:m(e,ut)?ut:ct}function $(){if(!dt)return!1;var e={},t=n.CSS&&n.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(i){e[i]=!t||n.CSS.supports("touch-action",i)}),e}function ee(e){this.options=ve({},this.defaults,e||{}),this.id=E(),this.manager=null,this.options.enable=v(this.options.enable,!0),this.state=gt,this.simultaneous={},this.requireFail=[]}function te(e){return e&kt?"cancel":e&_t?"end":e&mt?"move":e&bt?"start":""}function ie(e){return e==We?"down":e==He?"up":e==Ae?"left":e==Le?"right":""}function oe(e,t){var i=t.manager;return i?i.get(e):e}function ne(){ee.apply(this,arguments)}function se(){ne.apply(this,arguments),this.pX=null,this.pY=null}function re(){ne.apply(this,arguments)}function ae(){ee.apply(this,arguments),this._timer=null,this._input=null}function he(){ne.apply(this,arguments)}function de(){ne.apply(this,arguments)}function le(){ee.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}function ce(e,t){return t=t||{},t.recognizers=v(t.recognizers,ce.defaults.preset),new ue(e,t)}function ue(e,t){this.options=ve({},ce.defaults,t||{}),this.options.inputTarget=this.options.inputTarget||e,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=e,this.input=D(this),this.touchAction=new Q(this,this.options.touchAction),fe(this,!0),l(this.options.recognizers,function(e){var t=this.add(new e[0](e[1]));e[2]&&t.recognizeWith(e[2]),e[3]&&t.requireFailure(e[3])},this)}function fe(e,t){var i=e.element;if(i.style){var o;l(e.options.cssProps,function(n,s){o=O(i.style,s),t?(e.oldCssProps[o]=i.style[o],i.style[o]=n):i.style[o]=e.oldCssProps[o]||""}),t||(e.oldCssProps={})}}function pe(e,t){var i=s.createEvent("Event");i.initEvent(e,!0,!0),i.gesture=t,t.target.dispatchEvent(i)}var ve,ye=["","webkit","Moz","MS","ms","o"],ge=s.createElement("div"),be="function",me=Math.round,_e=Math.abs,we=Date.now;ve="function"!=typeof Object.assign?function(e){if(e===a||null===e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),i=1;i<arguments.length;i++){var o=arguments[i];if(o!==a&&null!==o)for(var n in o)o.hasOwnProperty(n)&&(t[n]=o[n])}return t}:Object.assign;var ke=c(function(e,t,i){for(var o=Object.keys(t),n=0;n<o.length;)(!i||i&&e[o[n]]===a)&&(e[o[n]]=t[o[n]]),n++;return e},"extend","Use `assign`."),xe=c(function(e,t){return ke(e,t,!0)},"merge","Use `assign`."),Oe=1,Ee=/mobile|tablet|ip(ad|hone|od)|android/i,Me="ontouchstart"in n,Se=O(n,"PointerEvent")!==a,De=Me&&Ee.test(navigator.userAgent),Ce="touch",Te="pen",Pe="mouse",Fe="kinect",Be=25,Ie=1,je=2,Ne=4,ze=8,Re=1,Ae=2,Le=4,He=8,We=16,Ye=Ae|Le,Ue=He|We,Ve=Ye|Ue,qe=["x","y"],Ge=["clientX","clientY"];S.prototype={handler:function(){},init:function(){this.evEl&&y(this.element,this.evEl,this.domHandler),this.evTarget&&y(this.target,this.evTarget,this.domHandler),this.evWin&&y(M(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&g(this.element,this.evEl,this.domHandler),this.evTarget&&g(this.target,this.evTarget,this.domHandler),this.evWin&&g(M(this.element),this.evWin,this.domHandler)}};var Xe={mousedown:Ie,mousemove:je,mouseup:Ne},Ke="mousedown",Ze="mousemove mouseup";u(H,S,{handler:function(e){var t=Xe[e.type];t&Ie&&0===e.button&&(this.pressed=!0),t&je&&1!==e.which&&(t=Ne),this.pressed&&(t&Ne&&(this.pressed=!1),this.callback(this.manager,t,{pointers:[e],changedPointers:[e],pointerType:Pe,srcEvent:e}))}});var Qe={pointerdown:Ie,pointermove:je,pointerup:Ne,pointercancel:ze,pointerout:ze},Je={2:Ce,3:Te,4:Pe,5:Fe},$e="pointerdown",et="pointermove pointerup pointercancel";n.MSPointerEvent&&!n.PointerEvent&&($e="MSPointerDown",et="MSPointerMove MSPointerUp MSPointerCancel"),u(W,S,{handler:function(e){var t=this.store,i=!1,o=e.type.toLowerCase().replace("ms",""),n=Qe[o],s=Je[e.pointerType]||e.pointerType,r=s==Ce,a=w(t,e.pointerId,"pointerId");n&Ie&&(0===e.button||r)?a<0&&(t.push(e),a=t.length-1):n&(Ne|ze)&&(i=!0),a<0||(t[a]=e,this.callback(this.manager,n,{pointers:t,changedPointers:[e],pointerType:s,srcEvent:e}),i&&t.splice(a,1))}});var tt={touchstart:Ie,touchmove:je,touchend:Ne,touchcancel:ze},it="touchstart",ot="touchstart touchmove touchend touchcancel";u(Y,S,{handler:function(e){var t=tt[e.type];if(t===Ie&&(this.started=!0),this.started){var i=U.call(this,e,t);t&(Ne|ze)&&i[0].length-i[1].length===0&&(this.started=!1),this.callback(this.manager,t,{pointers:i[0],changedPointers:i[1],pointerType:Ce,srcEvent:e})}}});var nt={touchstart:Ie,touchmove:je,touchend:Ne,touchcancel:ze},st="touchstart touchmove touchend touchcancel";u(V,S,{handler:function(e){var t=nt[e.type],i=q.call(this,e,t);i&&this.callback(this.manager,t,{pointers:i[0],changedPointers:i[1],pointerType:Ce,srcEvent:e})}});var rt=2500,at=25;u(G,S,{handler:function(e,t,i){var o=i.pointerType==Ce,n=i.pointerType==Pe;if(!(n&&i.sourceCapabilities&&i.sourceCapabilities.firesTouchEvents)){if(o)X.call(this,t,i);else if(n&&Z.call(this,i))return;this.callback(e,t,i)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});var ht=O(ge.style,"touchAction"),dt=ht!==a,lt="compute",ct="auto",ut="manipulation",ft="none",pt="pan-x",vt="pan-y",yt=$();Q.prototype={set:function(e){e==lt&&(e=this.compute()),dt&&this.manager.element.style&&yt[e]&&(this.manager.element.style[ht]=e),this.actions=e.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var e=[];return l(this.manager.recognizers,function(t){p(t.options.enable,[t])&&(e=e.concat(t.getTouchAction()))}),J(e.join(" "))},preventDefaults:function(e){var t=e.srcEvent,i=e.offsetDirection;if(this.manager.session.prevented)return void t.preventDefault();var o=this.actions,n=m(o,ft)&&!yt[ft],s=m(o,vt)&&!yt[vt],r=m(o,pt)&&!yt[pt];if(n){var a=1===e.pointers.length,h=e.distance<2,d=e.deltaTime<250;if(a&&h&&d)return}return r&&s?void 0:n||s&&i&Ye||r&&i&Ue?this.preventSrc(t):void 0},preventSrc:function(e){this.manager.session.prevented=!0,e.preventDefault()}};var gt=1,bt=2,mt=4,_t=8,wt=_t,kt=16,xt=32;ee.prototype={defaults:{},set:function(e){return ve(this.options,e),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(e){if(d(e,"recognizeWith",this))return this;var t=this.simultaneous;return e=oe(e,this),t[e.id]||(t[e.id]=e,e.recognizeWith(this)),this},dropRecognizeWith:function(e){return d(e,"dropRecognizeWith",this)?this:(e=oe(e,this),delete this.simultaneous[e.id],this)},requireFailure:function(e){if(d(e,"requireFailure",this))return this;var t=this.requireFail;return e=oe(e,this),w(t,e)===-1&&(t.push(e),e.requireFailure(this)),this},dropRequireFailure:function(e){if(d(e,"dropRequireFailure",this))return this;e=oe(e,this);var t=w(this.requireFail,e);return t>-1&&this.requireFail.splice(t,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(e){return!!this.simultaneous[e.id]},emit:function(e){function t(t){i.manager.emit(t,e)}var i=this,o=this.state;o<_t&&t(i.options.event+te(o)),t(i.options.event),e.additionalEvent&&t(e.additionalEvent),o>=_t&&t(i.options.event+te(o))},tryEmit:function(e){return this.canEmit()?this.emit(e):void(this.state=xt)},canEmit:function(){for(var e=0;e<this.requireFail.length;){if(!(this.requireFail[e].state&(xt|gt)))return!1;e++}return!0},recognize:function(e){var t=ve({},e);return p(this.options.enable,[this,t])?(this.state&(wt|kt|xt)&&(this.state=gt),this.state=this.process(t),void(this.state&(bt|mt|_t|kt)&&this.tryEmit(t))):(this.reset(),void(this.state=xt))},process:function(e){},getTouchAction:function(){},reset:function(){}},u(ne,ee,{defaults:{pointers:1},attrTest:function(e){var t=this.options.pointers;return 0===t||e.pointers.length===t},process:function(e){var t=this.state,i=e.eventType,o=t&(bt|mt),n=this.attrTest(e);return o&&(i&ze||!n)?t|kt:o||n?i&Ne?t|_t:t&bt?t|mt:bt:xt}}),u(se,ne,{defaults:{event:"pan",threshold:10,pointers:1,direction:Ve},getTouchAction:function(){var e=this.options.direction,t=[];return e&Ye&&t.push(vt),e&Ue&&t.push(pt),t},directionTest:function(e){var t=this.options,i=!0,o=e.distance,n=e.direction,s=e.deltaX,r=e.deltaY;return n&t.direction||(t.direction&Ye?(n=0===s?Re:s<0?Ae:Le,i=s!=this.pX,o=Math.abs(e.deltaX)):(n=0===r?Re:r<0?He:We,i=r!=this.pY,o=Math.abs(e.deltaY))),e.direction=n,i&&o>t.threshold&&n&t.direction},attrTest:function(e){return ne.prototype.attrTest.call(this,e)&&(this.state&bt||!(this.state&bt)&&this.directionTest(e))},emit:function(e){this.pX=e.deltaX,this.pY=e.deltaY;var t=ie(e.direction);t&&(e.additionalEvent=this.options.event+t),this._super.emit.call(this,e)}}),u(re,ne,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[ft]},attrTest:function(e){return this._super.attrTest.call(this,e)&&(Math.abs(e.scale-1)>this.options.threshold||this.state&bt)},emit:function(e){if(1!==e.scale){var t=e.scale<1?"in":"out";e.additionalEvent=this.options.event+t}this._super.emit.call(this,e)}}),u(ae,ee,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return[ct]},process:function(e){var t=this.options,i=e.pointers.length===t.pointers,o=e.distance<t.threshold,n=e.deltaTime>t.time;if(this._input=e,!o||!i||e.eventType&(Ne|ze)&&!n)this.reset();else if(e.eventType&Ie)this.reset(),this._timer=h(function(){this.state=wt,this.tryEmit()},t.time,this);else if(e.eventType&Ne)return wt;return xt},reset:function(){clearTimeout(this._timer)},emit:function(e){this.state===wt&&(e&&e.eventType&Ne?this.manager.emit(this.options.event+"up",e):(this._input.timeStamp=we(),this.manager.emit(this.options.event,this._input)))}}),u(he,ne,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[ft]},attrTest:function(e){return this._super.attrTest.call(this,e)&&(Math.abs(e.rotation)>this.options.threshold||this.state&bt)}}),u(de,ne,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:Ye|Ue,pointers:1},getTouchAction:function(){return se.prototype.getTouchAction.call(this)},attrTest:function(e){var t,i=this.options.direction;return i&(Ye|Ue)?t=e.overallVelocity:i&Ye?t=e.overallVelocityX:i&Ue&&(t=e.overallVelocityY),this._super.attrTest.call(this,e)&&i&e.offsetDirection&&e.distance>this.options.threshold&&e.maxPointers==this.options.pointers&&_e(t)>this.options.velocity&&e.eventType&Ne},emit:function(e){var t=ie(e.offsetDirection);t&&this.manager.emit(this.options.event+t,e),this.manager.emit(this.options.event,e)}}),u(le,ee,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return[ut]},process:function(e){var t=this.options,i=e.pointers.length===t.pointers,o=e.distance<t.threshold,n=e.deltaTime<t.time;if(this.reset(),e.eventType&Ie&&0===this.count)return this.failTimeout();if(o&&n&&i){if(e.eventType!=Ne)return this.failTimeout();var s=!this.pTime||e.timeStamp-this.pTime<t.interval,r=!this.pCenter||z(this.pCenter,e.center)<t.posThreshold;this.pTime=e.timeStamp,this.pCenter=e.center,r&&s?this.count+=1:this.count=1,this._input=e;var a=this.count%t.taps;if(0===a)return this.hasRequireFailures()?(this._timer=h(function(){this.state=wt,this.tryEmit()},t.interval,this),bt):wt}return xt},failTimeout:function(){return this._timer=h(function(){this.state=xt},this.options.interval,this),xt},reset:function(){clearTimeout(this._timer)},emit:function(){this.state==wt&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}}),ce.VERSION="2.0.7",ce.defaults={domEvents:!1,touchAction:lt,enable:!0,inputTarget:null,inputClass:null,preset:[[he,{enable:!1}],[re,{enable:!1},["rotate"]],[de,{direction:Ye}],[se,{direction:Ye},["swipe"]],[le],[le,{event:"doubletap",taps:2},["tap"]],[ae]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}};var Ot=1,Et=2;ue.prototype={set:function(e){return ve(this.options,e),e.touchAction&&this.touchAction.update(),e.inputTarget&&(this.input.destroy(),this.input.target=e.inputTarget,this.input.init()),this},stop:function(e){this.session.stopped=e?Et:Ot},recognize:function(e){var t=this.session;if(!t.stopped){this.touchAction.preventDefaults(e);var i,o=this.recognizers,n=t.curRecognizer;(!n||n&&n.state&wt)&&(n=t.curRecognizer=null);for(var s=0;s<o.length;)i=o[s],t.stopped===Et||n&&i!=n&&!i.canRecognizeWith(n)?i.reset():i.recognize(e),!n&&i.state&(bt|mt|_t)&&(n=t.curRecognizer=i),s++}},get:function(e){if(e instanceof ee)return e;for(var t=this.recognizers,i=0;i<t.length;i++)if(t[i].options.event==e)return t[i];return null},add:function(e){if(d(e,"add",this))return this;var t=this.get(e.options.event);return t&&this.remove(t),this.recognizers.push(e),e.manager=this,this.touchAction.update(),e},remove:function(e){if(d(e,"remove",this))return this;if(e=this.get(e)){var t=this.recognizers,i=w(t,e);i!==-1&&(t.splice(i,1),this.touchAction.update())}return this},on:function(e,t){if(e!==a&&t!==a){var i=this.handlers;return l(_(e),function(e){i[e]=i[e]||[],i[e].push(t)}),this}},off:function(e,t){if(e!==a){var i=this.handlers;return l(_(e),function(e){t?i[e]&&i[e].splice(w(i[e],t),1):delete i[e]}),this}},emit:function(e,t){this.options.domEvents&&pe(e,t);var i=this.handlers[e]&&this.handlers[e].slice();if(i&&i.length){t.type=e,t.preventDefault=function(){t.srcEvent.preventDefault()};for(var o=0;o<i.length;)i[o](t),o++}},destroy:function(){this.element&&fe(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}},ve(ce,{INPUT_START:Ie,INPUT_MOVE:je,INPUT_END:Ne,INPUT_CANCEL:ze,STATE_POSSIBLE:gt,STATE_BEGAN:bt,STATE_CHANGED:mt,STATE_ENDED:_t,STATE_RECOGNIZED:wt,STATE_CANCELLED:kt,STATE_FAILED:xt,DIRECTION_NONE:Re,DIRECTION_LEFT:Ae,DIRECTION_RIGHT:Le,DIRECTION_UP:He,DIRECTION_DOWN:We,DIRECTION_HORIZONTAL:Ye,DIRECTION_VERTICAL:Ue,DIRECTION_ALL:Ve,Manager:ue,Input:S,TouchAction:Q,TouchInput:V,MouseInput:H,PointerEventInput:W,TouchMouseInput:G,SingleTouchInput:Y,Recognizer:ee,AttrRecognizer:ne,Tap:le,Pan:se,Swipe:de,Pinch:re,Rotate:he,Press:ae,on:y,off:g,each:l,merge:xe,extend:ke,assign:ve,inherit:u,bindFn:f,prefixed:O});var Mt="undefined"!=typeof n?n:"undefined"!=typeof self?self:{};Mt.Hammer=ce,o=function(){return ce}.call(t,i,t,e),!(o!==a&&(e.exports=o))}(window,document,"Hammer")},function(e,t,i){i(58);t.onTouch=function(e,t){t.inputHandler=function(e){e.isFirst&&t(e)},e.on("hammer.input",t.inputHandler)},t.onRelease=function(e,t){return t.inputHandler=function(e){e.isFinal&&t(e)},e.on("hammer.input",t.inputHandler)},t.offTouch=function(e,t){e.off("hammer.input",t.inputHandler)},t.offRelease=t.offTouch,t.disablePreventDefaultVertically=function(e){var t="pan-y";return e.getTouchAction=function(){return[t]},e}},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),r=i(54),a=o(r),h=i(1),d=function(){function e(t,i){var o=this;n(this,e),this.body=t,this.canvas=i,this.animationSpeed=1/this.renderRefreshRate,this.animationEasingFunction="easeInOutQuint",this.easingTime=0,this.sourceScale=0,this.targetScale=0,this.sourceTranslation=0,this.targetTranslation=0,this.lockedOnNodeId=void 0,this.lockedOnNodeOffset=void 0,this.touchTime=0,this.viewFunction=void 0,this.body.emitter.on("fit",this.fit.bind(this)),this.body.emitter.on("animationFinished",function(){o.body.emitter.emit("_stopRendering")}),this.body.emitter.on("unlockNode",this.releaseNode.bind(this))}return s(e,[{key:"setOptions",value:function(){var e=arguments.length<=0||void 0===arguments[0]?{}:arguments[0];this.options=e}},{key:"fit",value:function(){var e=arguments.length<=0||void 0===arguments[0]?{nodes:[]}:arguments[0],t=!(arguments.length<=1||void 0===arguments[1])&&arguments[1],i=void 0,o=void 0;if(void 0!==e.nodes&&0!==e.nodes.length||(e.nodes=this.body.nodeIndices),t===!0){var n=0;for(var s in this.body.nodes)if(this.body.nodes.hasOwnProperty(s)){var r=this.body.nodes[s];r.predefinedPosition===!0&&(n+=1)}if(n>.5*this.body.nodeIndices.length)return void this.fit(e,!1);i=a["default"].getRange(this.body.nodes,e.nodes);var h=this.body.nodeIndices.length;o=12.662/(h+7.4147)+.0964822;var d=Math.min(this.canvas.frame.canvas.clientWidth/600,this.canvas.frame.canvas.clientHeight/600);o*=d}else{this.body.emitter.emit("_resizeNodes"),i=a["default"].getRange(this.body.nodes,e.nodes);var l=1.1*Math.abs(i.maxX-i.minX),c=1.1*Math.abs(i.maxY-i.minY),u=this.canvas.frame.canvas.clientWidth/l,f=this.canvas.frame.canvas.clientHeight/c;o=u<=f?u:f}o>1?o=1:0===o&&(o=1);var p=a["default"].findCenter(i),v={position:p,scale:o,animation:e.animation};this.moveTo(v)}},{key:"focus",value:function(e){var t=arguments.length<=1||void 0===arguments[1]?{}:arguments[1];if(void 0!==this.body.nodes[e]){var i={x:this.body.nodes[e].x,y:this.body.nodes[e].y};t.position=i,t.lockedOnNode=e,this.moveTo(t)}else console.log("Node: "+e+" cannot be found.")}},{key:"moveTo",value:function(e){return void 0===e?void(e={}):(void 0===e.offset&&(e.offset={x:0,y:0}),void 0===e.offset.x&&(e.offset.x=0),void 0===e.offset.y&&(e.offset.y=0),void 0===e.scale&&(e.scale=this.body.view.scale),void 0===e.position&&(e.position=this.getViewPosition()),void 0===e.animation&&(e.animation={duration:0}),e.animation===!1&&(e.animation={duration:0}),e.animation===!0&&(e.animation={}),void 0===e.animation.duration&&(e.animation.duration=1e3),void 0===e.animation.easingFunction&&(e.animation.easingFunction="easeInOutQuad"),void this.animateView(e))}},{key:"animateView",value:function(e){if(void 0!==e){this.animationEasingFunction=e.animation.easingFunction,this.releaseNode(),e.locked===!0&&(this.lockedOnNodeId=e.lockedOnNode,this.lockedOnNodeOffset=e.offset),0!=this.easingTime&&this._transitionRedraw(!0),this.sourceScale=this.body.view.scale,this.sourceTranslation=this.body.view.translation,this.targetScale=e.scale,this.body.view.scale=this.targetScale;var t=this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight}),i={x:t.x-e.position.x,y:t.y-e.position.y};this.targetTranslation={x:this.sourceTranslation.x+i.x*this.targetScale+e.offset.x,y:this.sourceTranslation.y+i.y*this.targetScale+e.offset.y},0===e.animation.duration?void 0!=this.lockedOnNodeId?(this.viewFunction=this._lockedRedraw.bind(this),this.body.emitter.on("initRedraw",this.viewFunction)):(this.body.view.scale=this.targetScale,this.body.view.translation=this.targetTranslation,this.body.emitter.emit("_requestRedraw")):(this.animationSpeed=1/(60*e.animation.duration*.001)||1/60,this.animationEasingFunction=e.animation.easingFunction,this.viewFunction=this._transitionRedraw.bind(this),this.body.emitter.on("initRedraw",this.viewFunction),this.body.emitter.emit("_startRendering"))}}},{key:"_lockedRedraw",value:function(){var e={x:this.body.nodes[this.lockedOnNodeId].x,y:this.body.nodes[this.lockedOnNodeId].y},t=this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight}),i={x:t.x-e.x,y:t.y-e.y},o=this.body.view.translation,n={x:o.x+i.x*this.body.view.scale+this.lockedOnNodeOffset.x,y:o.y+i.y*this.body.view.scale+this.lockedOnNodeOffset.y};this.body.view.translation=n}},{key:"releaseNode",value:function(){void 0!==this.lockedOnNodeId&&void 0!==this.viewFunction&&(this.body.emitter.off("initRedraw",this.viewFunction),this.lockedOnNodeId=void 0,this.lockedOnNodeOffset=void 0)}},{key:"_transitionRedraw",value:function(){var e=!(arguments.length<=0||void 0===arguments[0])&&arguments[0];this.easingTime+=this.animationSpeed,this.easingTime=e===!0?1:this.easingTime;var t=h.easingFunctions[this.animationEasingFunction](this.easingTime);this.body.view.scale=this.sourceScale+(this.targetScale-this.sourceScale)*t,this.body.view.translation={x:this.sourceTranslation.x+(this.targetTranslation.x-this.sourceTranslation.x)*t,y:this.sourceTranslation.y+(this.targetTranslation.y-this.sourceTranslation.y)*t},this.easingTime>=1&&(this.body.emitter.off("initRedraw",this.viewFunction),this.easingTime=0,void 0!=this.lockedOnNodeId&&(this.viewFunction=this._lockedRedraw.bind(this),this.body.emitter.on("initRedraw",this.viewFunction)),this.body.emitter.emit("animationFinished"))}},{key:"getScale",value:function(){return this.body.view.scale}},{key:"getViewPosition",value:function(){return this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight})}}]),e}();t["default"]=d},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),r=i(64),a=o(r),h=i(66),d=o(h),l=i(1),c=function(){function e(t,i,o){n(this,e),this.body=t,this.canvas=i,this.selectionHandler=o,this.navigationHandler=new a["default"](t,i),this.body.eventListeners.onTap=this.onTap.bind(this),this.body.eventListeners.onTouch=this.onTouch.bind(this),this.body.eventListeners.onDoubleTap=this.onDoubleTap.bind(this),this.body.eventListeners.onHold=this.onHold.bind(this),this.body.eventListeners.onDragStart=this.onDragStart.bind(this),this.body.eventListeners.onDrag=this.onDrag.bind(this),this.body.eventListeners.onDragEnd=this.onDragEnd.bind(this),this.body.eventListeners.onMouseWheel=this.onMouseWheel.bind(this),this.body.eventListeners.onPinch=this.onPinch.bind(this),this.body.eventListeners.onMouseMove=this.onMouseMove.bind(this),this.body.eventListeners.onRelease=this.onRelease.bind(this),this.body.eventListeners.onContext=this.onContext.bind(this),this.touchTime=0,this.drag={},this.pinch={},this.popup=void 0,this.popupObj=void 0,this.popupTimer=void 0,this.body.functions.getPointer=this.getPointer.bind(this),this.options={},this.defaultOptions={dragNodes:!0,dragView:!0,hover:!1,keyboard:{enabled:!1,speed:{x:10,y:10,zoom:.02},bindToWindow:!0},navigationButtons:!1,tooltipDelay:300,zoomView:!0},l.extend(this.options,this.defaultOptions),this.bindEventListeners()}return s(e,[{key:"bindEventListeners",value:function(){var e=this;this.body.emitter.on("destroy",function(){clearTimeout(e.popupTimer),delete e.body.functions.getPointer})}},{key:"setOptions",value:function(e){if(void 0!==e){var t=["hideEdgesOnDrag","hideNodesOnDrag","keyboard","multiselect","selectable","selectConnectedEdges"];l.selectiveNotDeepExtend(t,this.options,e),l.mergeOptions(this.options,e,"keyboard"),e.tooltip&&(l.extend(this.options.tooltip,e.tooltip),e.tooltip.color&&(this.options.tooltip.color=l.parseColor(e.tooltip.color)))}this.navigationHandler.setOptions(this.options)}},{key:"getPointer",value:function(e){return{x:e.x-l.getAbsoluteLeft(this.canvas.frame.canvas),y:e.y-l.getAbsoluteTop(this.canvas.frame.canvas)}}},{key:"onTouch",value:function(e){(new Date).valueOf()-this.touchTime>50&&(this.drag.pointer=this.getPointer(e.center),this.drag.pinched=!1,this.pinch.scale=this.body.view.scale,this.touchTime=(new Date).valueOf())}},{key:"onTap",value:function(e){var t=this.getPointer(e.center),i=this.selectionHandler.options.multiselect&&(e.changedPointers[0].ctrlKey||e.changedPointers[0].metaKey);this.checkSelectionChanges(t,e,i),this.selectionHandler._generateClickEvent("click",e,t)}},{key:"onDoubleTap",value:function(e){var t=this.getPointer(e.center);this.selectionHandler._generateClickEvent("doubleClick",e,t)}},{key:"onHold",value:function(e){var t=this.getPointer(e.center),i=this.selectionHandler.options.multiselect;this.checkSelectionChanges(t,e,i),this.selectionHandler._generateClickEvent("click",e,t),this.selectionHandler._generateClickEvent("hold",e,t)}},{key:"onRelease",value:function(e){if((new Date).valueOf()-this.touchTime>10){var t=this.getPointer(e.center);this.selectionHandler._generateClickEvent("release",e,t),this.touchTime=(new Date).valueOf()}}},{key:"onContext",value:function(e){var t=this.getPointer({x:e.clientX,y:e.clientY});this.selectionHandler._generateClickEvent("oncontext",e,t)}},{key:"checkSelectionChanges",value:function(e,t){var i=!(arguments.length<=2||void 0===arguments[2])&&arguments[2],o=this.selectionHandler._getSelectedEdgeCount(),n=this.selectionHandler._getSelectedNodeCount(),s=this.selectionHandler.getSelection(),r=void 0;r=i===!0?this.selectionHandler.selectAdditionalOnPoint(e):this.selectionHandler.selectOnPoint(e);var a=this.selectionHandler._getSelectedEdgeCount(),h=this.selectionHandler._getSelectedNodeCount(),d=this.selectionHandler.getSelection(),l=this._determineIfDifferent(s,d),c=l.nodesChanged,u=l.edgesChanged,f=!1;h-n>0?(this.selectionHandler._generateClickEvent("selectNode",t,e),r=!0,f=!0):c===!0&&h>0?(this.selectionHandler._generateClickEvent("deselectNode",t,e,s),this.selectionHandler._generateClickEvent("selectNode",t,e),f=!0,r=!0):h-n<0&&(this.selectionHandler._generateClickEvent("deselectNode",t,e,s),r=!0),a-o>0&&f===!1?(this.selectionHandler._generateClickEvent("selectEdge",t,e),r=!0):a>0&&u===!0?(this.selectionHandler._generateClickEvent("deselectEdge",t,e,s),this.selectionHandler._generateClickEvent("selectEdge",t,e),r=!0):a-o<0&&(this.selectionHandler._generateClickEvent("deselectEdge",t,e,s),r=!0),r===!0&&this.selectionHandler._generateClickEvent("select",t,e)}},{key:"_determineIfDifferent",value:function(e,t){
for(var i=!1,o=!1,n=0;n<e.nodes.length;n++)t.nodes.indexOf(e.nodes[n])===-1&&(i=!0);for(var s=0;s<t.nodes.length;s++)e.nodes.indexOf(e.nodes[s])===-1&&(i=!0);for(var r=0;r<e.edges.length;r++)t.edges.indexOf(e.edges[r])===-1&&(o=!0);for(var a=0;a<t.edges.length;a++)e.edges.indexOf(e.edges[a])===-1&&(o=!0);return{nodesChanged:i,edgesChanged:o}}},{key:"onDragStart",value:function(e){void 0===this.drag.pointer&&this.onTouch(e);var t=this.selectionHandler.getNodeAt(this.drag.pointer);if(this.drag.dragging=!0,this.drag.selection=[],this.drag.translation=l.extend({},this.body.view.translation),this.drag.nodeId=void 0,void 0!==t&&this.options.dragNodes===!0){this.drag.nodeId=t.id,t.isSelected()===!1&&(this.selectionHandler.unselectAll(),this.selectionHandler.selectObject(t)),this.selectionHandler._generateClickEvent("dragStart",e,this.drag.pointer);var i=this.selectionHandler.selectionObj.nodes;for(var o in i)if(i.hasOwnProperty(o)){var n=i[o],s={id:n.id,node:n,x:n.x,y:n.y,xFixed:n.options.fixed.x,yFixed:n.options.fixed.y};n.options.fixed.x=!0,n.options.fixed.y=!0,this.drag.selection.push(s)}}else this.selectionHandler._generateClickEvent("dragStart",e,this.drag.pointer,void 0,!0)}},{key:"onDrag",value:function(e){var t=this;if(this.drag.pinched!==!0){this.body.emitter.emit("unlockNode");var i=this.getPointer(e.center),o=this.drag.selection;if(o&&o.length&&this.options.dragNodes===!0)!function(){t.selectionHandler._generateClickEvent("dragging",e,i);var n=i.x-t.drag.pointer.x,s=i.y-t.drag.pointer.y;o.forEach(function(e){var i=e.node;e.xFixed===!1&&(i.x=t.canvas._XconvertDOMtoCanvas(t.canvas._XconvertCanvasToDOM(e.x)+n)),e.yFixed===!1&&(i.y=t.canvas._YconvertDOMtoCanvas(t.canvas._YconvertCanvasToDOM(e.y)+s))}),t.body.emitter.emit("startSimulation")}();else if(this.options.dragView===!0){if(this.selectionHandler._generateClickEvent("dragging",e,i,void 0,!0),void 0===this.drag.pointer)return void this.onDragStart(e);var n=i.x-this.drag.pointer.x,s=i.y-this.drag.pointer.y;this.body.view.translation={x:this.drag.translation.x+n,y:this.drag.translation.y+s},this.body.emitter.emit("_redraw")}}}},{key:"onDragEnd",value:function(e){this.drag.dragging=!1;var t=this.drag.selection;t&&t.length?(t.forEach(function(e){e.node.options.fixed.x=e.xFixed,e.node.options.fixed.y=e.yFixed}),this.selectionHandler._generateClickEvent("dragEnd",e,this.getPointer(e.center)),this.body.emitter.emit("startSimulation")):(this.selectionHandler._generateClickEvent("dragEnd",e,this.getPointer(e.center),void 0,!0),this.body.emitter.emit("_requestRedraw"))}},{key:"onPinch",value:function(e){var t=this.getPointer(e.center);this.drag.pinched=!0,void 0===this.pinch.scale&&(this.pinch.scale=1);var i=this.pinch.scale*e.scale;this.zoom(i,t)}},{key:"zoom",value:function(e,t){if(this.options.zoomView===!0){var i=this.body.view.scale;e<1e-5&&(e=1e-5),e>10&&(e=10);var o=void 0;void 0!==this.drag&&this.drag.dragging===!0&&(o=this.canvas.DOMtoCanvas(this.drag.pointer));var n=this.body.view.translation,s=e/i,r=(1-s)*t.x+n.x*s,a=(1-s)*t.y+n.y*s;if(this.body.view.scale=e,this.body.view.translation={x:r,y:a},void 0!=o){var h=this.canvas.canvasToDOM(o);this.drag.pointer.x=h.x,this.drag.pointer.y=h.y}this.body.emitter.emit("_requestRedraw"),i<e?this.body.emitter.emit("zoom",{direction:"+",scale:this.body.view.scale}):this.body.emitter.emit("zoom",{direction:"-",scale:this.body.view.scale})}}},{key:"onMouseWheel",value:function(e){if(this.options.zoomView===!0){var t=0;if(e.wheelDelta?t=e.wheelDelta/120:e.detail&&(t=-e.detail/3),0!==t){var i=this.body.view.scale,o=t/10;t<0&&(o/=1-o),i*=1+o;var n=this.getPointer({x:e.clientX,y:e.clientY});this.zoom(i,n)}e.preventDefault()}}},{key:"onMouseMove",value:function(e){var t=this,i=this.getPointer({x:e.clientX,y:e.clientY}),o=!1;if(void 0!==this.popup&&(this.popup.hidden===!1&&this._checkHidePopup(i),this.popup.hidden===!1&&(o=!0,this.popup.setPosition(i.x+3,i.y-5),this.popup.show())),this.options.keyboard.bindToWindow===!1&&this.options.keyboard.enabled===!0&&this.canvas.frame.focus(),o===!1&&(void 0!==this.popupTimer&&(clearInterval(this.popupTimer),this.popupTimer=void 0),this.drag.dragging||(this.popupTimer=setTimeout(function(){return t._checkShowPopup(i)},this.options.tooltipDelay))),this.options.hover===!0){var n=this.selectionHandler.getNodeAt(i);void 0===n&&(n=this.selectionHandler.getEdgeAt(i)),this.selectionHandler.hoverObject(n)}}},{key:"_checkShowPopup",value:function(e){var t=this.canvas._XconvertDOMtoCanvas(e.x),i=this.canvas._YconvertDOMtoCanvas(e.y),o={left:t,top:i,right:t,bottom:i},n=void 0===this.popupObj?void 0:this.popupObj.id,s=!1,r="node";if(void 0===this.popupObj){for(var a=this.body.nodeIndices,h=this.body.nodes,l=void 0,c=[],u=0;u<a.length;u++)l=h[a[u]],l.isOverlappingWith(o)===!0&&void 0!==l.getTitle()&&c.push(a[u]);c.length>0&&(this.popupObj=h[c[c.length-1]],s=!0)}if(void 0===this.popupObj&&s===!1){for(var f=this.body.edgeIndices,p=this.body.edges,v=void 0,y=[],g=0;g<f.length;g++)v=p[f[g]],v.isOverlappingWith(o)===!0&&v.connected===!0&&void 0!==v.getTitle()&&y.push(f[g]);y.length>0&&(this.popupObj=p[y[y.length-1]],r="edge")}void 0!==this.popupObj?this.popupObj.id!==n&&(void 0===this.popup&&(this.popup=new d["default"](this.canvas.frame)),this.popup.popupTargetType=r,this.popup.popupTargetId=this.popupObj.id,this.popup.setPosition(e.x+3,e.y-5),this.popup.setText(this.popupObj.getTitle()),this.popup.show(),this.body.emitter.emit("showPopup",this.popupObj.id)):void 0!==this.popup&&(this.popup.hide(),this.body.emitter.emit("hidePopup"))}},{key:"_checkHidePopup",value:function(e){var t=this.selectionHandler._pointerToPositionObject(e),i=!1;if("node"===this.popup.popupTargetType){if(void 0!==this.body.nodes[this.popup.popupTargetId]&&(i=this.body.nodes[this.popup.popupTargetId].isOverlappingWith(t),i===!0)){var o=this.selectionHandler.getNodeAt(e);i=o.id===this.popup.popupTargetId}}else void 0===this.selectionHandler.getNodeAt(e)&&void 0!==this.body.edges[this.popup.popupTargetId]&&(i=this.body.edges[this.popup.popupTargetId].isOverlappingWith(t));i===!1&&(this.popupObj=void 0,this.popup.hide(),this.body.emitter.emit("hidePopup"))}}]),e}();t["default"]=c},function(e,t,i){function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),s=(i(1),i(58)),r=i(61),a=i(65),h=function(){function e(t,i){var n=this;o(this,e),this.body=t,this.canvas=i,this.iconsCreated=!1,this.navigationHammers=[],this.boundFunctions={},this.touchTime=0,this.activated=!1,this.body.emitter.on("activate",function(){n.activated=!0,n.configureKeyboardBindings()}),this.body.emitter.on("deactivate",function(){n.activated=!1,n.configureKeyboardBindings()}),this.body.emitter.on("destroy",function(){void 0!==n.keycharm&&n.keycharm.destroy()}),this.options={}}return n(e,[{key:"setOptions",value:function(e){void 0!==e&&(this.options=e,this.create())}},{key:"create",value:function(){this.options.navigationButtons===!0?this.iconsCreated===!1&&this.loadNavigationElements():this.iconsCreated===!0&&this.cleanNavigation(),this.configureKeyboardBindings()}},{key:"cleanNavigation",value:function(){if(0!=this.navigationHammers.length){for(var e=0;e<this.navigationHammers.length;e++)this.navigationHammers[e].destroy();this.navigationHammers=[]}this.navigationDOM&&this.navigationDOM.wrapper&&this.navigationDOM.wrapper.parentNode&&this.navigationDOM.wrapper.parentNode.removeChild(this.navigationDOM.wrapper),this.iconsCreated=!1}},{key:"loadNavigationElements",value:function(){var e=this;this.cleanNavigation(),this.navigationDOM={};var t=["up","down","left","right","zoomIn","zoomOut","zoomExtends"],i=["_moveUp","_moveDown","_moveLeft","_moveRight","_zoomIn","_zoomOut","_fit"];this.navigationDOM.wrapper=document.createElement("div"),this.navigationDOM.wrapper.className="vis-navigation",this.canvas.frame.appendChild(this.navigationDOM.wrapper);for(var o=0;o<t.length;o++){this.navigationDOM[t[o]]=document.createElement("div"),this.navigationDOM[t[o]].className="vis-button vis-"+t[o],this.navigationDOM.wrapper.appendChild(this.navigationDOM[t[o]]);var n=new s(this.navigationDOM[t[o]]);"_fit"===i[o]?r.onTouch(n,this._fit.bind(this)):r.onTouch(n,this.bindToRedraw.bind(this,i[o])),this.navigationHammers.push(n)}var a=new s(this.canvas.frame);r.onRelease(a,function(){e._stopMovement()}),this.navigationHammers.push(a),this.iconsCreated=!0}},{key:"bindToRedraw",value:function(e){void 0===this.boundFunctions[e]&&(this.boundFunctions[e]=this[e].bind(this),this.body.emitter.on("initRedraw",this.boundFunctions[e]),this.body.emitter.emit("_startRendering"))}},{key:"unbindFromRedraw",value:function(e){void 0!==this.boundFunctions[e]&&(this.body.emitter.off("initRedraw",this.boundFunctions[e]),this.body.emitter.emit("_stopRendering"),delete this.boundFunctions[e])}},{key:"_fit",value:function(){(new Date).valueOf()-this.touchTime>700&&(this.body.emitter.emit("fit",{duration:700}),this.touchTime=(new Date).valueOf())}},{key:"_stopMovement",value:function(){for(var e in this.boundFunctions)this.boundFunctions.hasOwnProperty(e)&&(this.body.emitter.off("initRedraw",this.boundFunctions[e]),this.body.emitter.emit("_stopRendering"));this.boundFunctions={}}},{key:"_moveUp",value:function(){this.body.view.translation.y+=this.options.keyboard.speed.y}},{key:"_moveDown",value:function(){this.body.view.translation.y-=this.options.keyboard.speed.y}},{key:"_moveLeft",value:function(){this.body.view.translation.x+=this.options.keyboard.speed.x}},{key:"_moveRight",value:function(){this.body.view.translation.x-=this.options.keyboard.speed.x}},{key:"_zoomIn",value:function(){this.body.view.scale*=1+this.options.keyboard.speed.zoom,this.body.emitter.emit("zoom",{direction:"+",scale:this.body.view.scale})}},{key:"_zoomOut",value:function(){this.body.view.scale/=1+this.options.keyboard.speed.zoom,this.body.emitter.emit("zoom",{direction:"-",scale:this.body.view.scale})}},{key:"configureKeyboardBindings",value:function(){var e=this;void 0!==this.keycharm&&this.keycharm.destroy(),this.options.keyboard.enabled===!0&&(this.options.keyboard.bindToWindow===!0?this.keycharm=a({container:window,preventDefault:!0}):this.keycharm=a({container:this.canvas.frame,preventDefault:!0}),this.keycharm.reset(),this.activated===!0&&(this.keycharm.bind("up",function(){e.bindToRedraw("_moveUp")},"keydown"),this.keycharm.bind("down",function(){e.bindToRedraw("_moveDown")},"keydown"),this.keycharm.bind("left",function(){e.bindToRedraw("_moveLeft")},"keydown"),this.keycharm.bind("right",function(){e.bindToRedraw("_moveRight")},"keydown"),this.keycharm.bind("=",function(){e.bindToRedraw("_zoomIn")},"keydown"),this.keycharm.bind("num+",function(){e.bindToRedraw("_zoomIn")},"keydown"),this.keycharm.bind("num-",function(){e.bindToRedraw("_zoomOut")},"keydown"),this.keycharm.bind("-",function(){e.bindToRedraw("_zoomOut")},"keydown"),this.keycharm.bind("[",function(){e.bindToRedraw("_zoomOut")},"keydown"),this.keycharm.bind("]",function(){e.bindToRedraw("_zoomIn")},"keydown"),this.keycharm.bind("pageup",function(){e.bindToRedraw("_zoomIn")},"keydown"),this.keycharm.bind("pagedown",function(){e.bindToRedraw("_zoomOut")},"keydown"),this.keycharm.bind("up",function(){e.unbindFromRedraw("_moveUp")},"keyup"),this.keycharm.bind("down",function(){e.unbindFromRedraw("_moveDown")},"keyup"),this.keycharm.bind("left",function(){e.unbindFromRedraw("_moveLeft")},"keyup"),this.keycharm.bind("right",function(){e.unbindFromRedraw("_moveRight")},"keyup"),this.keycharm.bind("=",function(){e.unbindFromRedraw("_zoomIn")},"keyup"),this.keycharm.bind("num+",function(){e.unbindFromRedraw("_zoomIn")},"keyup"),this.keycharm.bind("num-",function(){e.unbindFromRedraw("_zoomOut")},"keyup"),this.keycharm.bind("-",function(){e.unbindFromRedraw("_zoomOut")},"keyup"),this.keycharm.bind("[",function(){e.unbindFromRedraw("_zoomOut")},"keyup"),this.keycharm.bind("]",function(){e.unbindFromRedraw("_zoomIn")},"keyup"),this.keycharm.bind("pageup",function(){e.unbindFromRedraw("_zoomIn")},"keyup"),this.keycharm.bind("pagedown",function(){e.unbindFromRedraw("_zoomOut")},"keyup")))}}]),e}();t["default"]=h},function(e,t,i){var o,n,s;!function(i,r){n=[],o=r,s="function"==typeof o?o.apply(t,n):o,!(void 0!==s&&(e.exports=s))}(this,function(){function e(e){var t,i=e&&e.preventDefault||!1,o=e&&e.container||window,n={},s={keydown:{},keyup:{}},r={};for(t=97;t<=122;t++)r[String.fromCharCode(t)]={code:65+(t-97),shift:!1};for(t=65;t<=90;t++)r[String.fromCharCode(t)]={code:t,shift:!0};for(t=0;t<=9;t++)r[""+t]={code:48+t,shift:!1};for(t=1;t<=12;t++)r["F"+t]={code:111+t,shift:!1};for(t=0;t<=9;t++)r["num"+t]={code:96+t,shift:!1};r["num*"]={code:106,shift:!1},r["num+"]={code:107,shift:!1},r["num-"]={code:109,shift:!1},r["num/"]={code:111,shift:!1},r["num."]={code:110,shift:!1},r.left={code:37,shift:!1},r.up={code:38,shift:!1},r.right={code:39,shift:!1},r.down={code:40,shift:!1},r.space={code:32,shift:!1},r.enter={code:13,shift:!1},r.shift={code:16,shift:void 0},r.esc={code:27,shift:!1},r.backspace={code:8,shift:!1},r.tab={code:9,shift:!1},r.ctrl={code:17,shift:!1},r.alt={code:18,shift:!1},r["delete"]={code:46,shift:!1},r.pageup={code:33,shift:!1},r.pagedown={code:34,shift:!1},r["="]={code:187,shift:!1},r["-"]={code:189,shift:!1},r["]"]={code:221,shift:!1},r["["]={code:219,shift:!1};var a=function(e){d(e,"keydown")},h=function(e){d(e,"keyup")},d=function(e,t){if(void 0!==s[t][e.keyCode]){for(var o=s[t][e.keyCode],n=0;n<o.length;n++)void 0===o[n].shift?o[n].fn(e):1==o[n].shift&&1==e.shiftKey?o[n].fn(e):0==o[n].shift&&0==e.shiftKey&&o[n].fn(e);1==i&&e.preventDefault()}};return n.bind=function(e,t,i){if(void 0===i&&(i="keydown"),void 0===r[e])throw new Error("unsupported key: "+e);void 0===s[i][r[e].code]&&(s[i][r[e].code]=[]),s[i][r[e].code].push({fn:t,shift:r[e].shift})},n.bindAll=function(e,t){void 0===t&&(t="keydown");for(var i in r)r.hasOwnProperty(i)&&n.bind(i,e,t)},n.getKey=function(e){for(var t in r)if(r.hasOwnProperty(t)){if(1==e.shiftKey&&1==r[t].shift&&e.keyCode==r[t].code)return t;if(0==e.shiftKey&&0==r[t].shift&&e.keyCode==r[t].code)return t;if(e.keyCode==r[t].code&&"shift"==t)return t}return"unknown key, currently not supported"},n.unbind=function(e,t,i){if(void 0===i&&(i="keydown"),void 0===r[e])throw new Error("unsupported key: "+e);if(void 0!==t){var o=[],n=s[i][r[e].code];if(void 0!==n)for(var a=0;a<n.length;a++)n[a].fn==t&&n[a].shift==r[e].shift||o.push(s[i][r[e].code][a]);s[i][r[e].code]=o}else s[i][r[e].code]=[]},n.reset=function(){s={keydown:{},keyup:{}}},n.destroy=function(){s={keydown:{},keyup:{}},o.removeEventListener("keydown",a,!0),o.removeEventListener("keyup",h,!0)},o.addEventListener("keydown",a,!0),o.addEventListener("keyup",h,!0),n}return e})},function(e,t){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),n=function(){function e(t){i(this,e),this.container=t,this.x=0,this.y=0,this.padding=5,this.hidden=!1,this.frame=document.createElement("div"),this.frame.className="vis-network-tooltip",this.container.appendChild(this.frame)}return o(e,[{key:"setPosition",value:function(e,t){this.x=parseInt(e),this.y=parseInt(t)}},{key:"setText",value:function(e){e instanceof Element?(this.frame.innerHTML="",this.frame.appendChild(e)):this.frame.innerHTML=e}},{key:"show",value:function(e){if(void 0===e&&(e=!0),e===!0){var t=this.frame.clientHeight,i=this.frame.clientWidth,o=this.frame.parentNode.clientHeight,n=this.frame.parentNode.clientWidth,s=this.y-t;s+t+this.padding>o&&(s=o-t-this.padding),s<this.padding&&(s=this.padding);var r=this.x;r+i+this.padding>n&&(r=n-i-this.padding),r<this.padding&&(r=this.padding),this.frame.style.left=r+"px",this.frame.style.top=s+"px",this.frame.style.visibility="visible",this.hidden=!1}else this.hide()}},{key:"hide",value:function(){this.hidden=!0,this.frame.style.visibility="hidden"}}]),e}();t["default"]=n},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),r=i(15),a=o(r),h=i(36),d=o(h),l=i(1),c=function(){function e(t,i){var o=this;n(this,e),this.body=t,this.canvas=i,this.selectionObj={nodes:[],edges:[]},this.hoverObj={nodes:{},edges:{}},this.options={},this.defaultOptions={multiselect:!1,selectable:!0,selectConnectedEdges:!0,hoverConnectedEdges:!0},l.extend(this.options,this.defaultOptions),this.body.emitter.on("_dataChanged",function(){o.updateSelection()})}return s(e,[{key:"setOptions",value:function(e){if(void 0!==e){var t=["multiselect","hoverConnectedEdges","selectable","selectConnectedEdges"];l.selectiveDeepExtend(t,this.options,e)}}},{key:"selectOnPoint",value:function(e){var t=!1;if(this.options.selectable===!0){var i=this.getNodeAt(e)||this.getEdgeAt(e);this.unselectAll(),void 0!==i&&(t=this.selectObject(i)),this.body.emitter.emit("_requestRedraw")}return t}},{key:"selectAdditionalOnPoint",value:function(e){var t=!1;if(this.options.selectable===!0){var i=this.getNodeAt(e)||this.getEdgeAt(e);void 0!==i&&(t=!0,i.isSelected()===!0?this.deselectObject(i):this.selectObject(i),this.body.emitter.emit("_requestRedraw"))}return t}},{key:"_generateClickEvent",value:function(e,t,i,o){var n=!(arguments.length<=4||void 0===arguments[4])&&arguments[4],s=void 0;s=n===!0?{nodes:[],edges:[]}:this.getSelection(),s.pointer={DOM:{x:i.x,y:i.y},canvas:this.canvas.DOMtoCanvas(i)},s.event=t,void 0!==o&&(s.previousSelection=o),this.body.emitter.emit(e,s)}},{key:"selectObject",value:function(e){var t=arguments.length<=1||void 0===arguments[1]?this.options.selectConnectedEdges:arguments[1];return void 0!==e&&(e instanceof a["default"]&&t===!0&&this._selectConnectedEdges(e),e.select(),this._addToSelection(e),!0)}},{key:"deselectObject",value:function(e){e.isSelected()===!0&&(e.selected=!1,this._removeFromSelection(e))}},{key:"_getAllNodesOverlappingWith",value:function(e){for(var t=[],i=this.body.nodes,o=0;o<this.body.nodeIndices.length;o++){var n=this.body.nodeIndices[o];i[n].isOverlappingWith(e)&&t.push(n)}return t}},{key:"_pointerToPositionObject",value:function(e){var t=this.canvas.DOMtoCanvas(e);return{left:t.x-1,top:t.y+1,right:t.x+1,bottom:t.y-1}}},{key:"getNodeAt",value:function(e){var t=arguments.length<=1||void 0===arguments[1]||arguments[1],i=this._pointerToPositionObject(e),o=this._getAllNodesOverlappingWith(i);return o.length>0?t===!0?this.body.nodes[o[o.length-1]]:o[o.length-1]:void 0}},{key:"_getEdgesOverlappingWith",value:function(e,t){for(var i=this.body.edges,o=0;o<this.body.edgeIndices.length;o++){var n=this.body.edgeIndices[o];i[n].isOverlappingWith(e)&&t.push(n)}}},{key:"_getAllEdgesOverlappingWith",value:function(e){var t=[];return this._getEdgesOverlappingWith(e,t),t}},{key:"getEdgeAt",value:function(e){var t=arguments.length<=1||void 0===arguments[1]||arguments[1],i=this._pointerToPositionObject(e),o=this._getAllEdgesOverlappingWith(i);return o.length>0?t===!0?this.body.edges[o[o.length-1]]:o[o.length-1]:void 0}},{key:"_addToSelection",value:function(e){e instanceof a["default"]?this.selectionObj.nodes[e.id]=e:this.selectionObj.edges[e.id]=e}},{key:"_addToHover",value:function(e){e instanceof a["default"]?this.hoverObj.nodes[e.id]=e:this.hoverObj.edges[e.id]=e}},{key:"_removeFromSelection",value:function(e){e instanceof a["default"]?(delete this.selectionObj.nodes[e.id],this._unselectConnectedEdges(e)):delete this.selectionObj.edges[e.id]}},{key:"unselectAll",value:function(){for(var e in this.selectionObj.nodes)this.selectionObj.nodes.hasOwnProperty(e)&&this.selectionObj.nodes[e].unselect();for(var t in this.selectionObj.edges)this.selectionObj.edges.hasOwnProperty(t)&&this.selectionObj.edges[t].unselect();this.selectionObj={nodes:{},edges:{}}}},{key:"_getSelectedNodeCount",value:function(){var e=0;for(var t in this.selectionObj.nodes)this.selectionObj.nodes.hasOwnProperty(t)&&(e+=1);return e}},{key:"_getSelectedNode",value:function(){for(var e in this.selectionObj.nodes)if(this.selectionObj.nodes.hasOwnProperty(e))return this.selectionObj.nodes[e]}},{key:"_getSelectedEdge",value:function(){for(var e in this.selectionObj.edges)if(this.selectionObj.edges.hasOwnProperty(e))return this.selectionObj.edges[e]}},{key:"_getSelectedEdgeCount",value:function(){var e=0;for(var t in this.selectionObj.edges)this.selectionObj.edges.hasOwnProperty(t)&&(e+=1);return e}},{key:"_getSelectedObjectCount",value:function(){var e=0;for(var t in this.selectionObj.nodes)this.selectionObj.nodes.hasOwnProperty(t)&&(e+=1);for(var i in this.selectionObj.edges)this.selectionObj.edges.hasOwnProperty(i)&&(e+=1);return e}},{key:"_selectionIsEmpty",value:function(){for(var e in this.selectionObj.nodes)if(this.selectionObj.nodes.hasOwnProperty(e))return!1;for(var t in this.selectionObj.edges)if(this.selectionObj.edges.hasOwnProperty(t))return!1;return!0}},{key:"_clusterInSelection",value:function(){for(var e in this.selectionObj.nodes)if(this.selectionObj.nodes.hasOwnProperty(e)&&this.selectionObj.nodes[e].clusterSize>1)return!0;return!1}},{key:"_selectConnectedEdges",value:function(e){for(var t=0;t<e.edges.length;t++){var i=e.edges[t];i.select(),this._addToSelection(i)}}},{key:"_hoverConnectedEdges",value:function(e){for(var t=0;t<e.edges.length;t++){var i=e.edges[t];i.hover=!0,this._addToHover(i)}}},{key:"_unselectConnectedEdges",value:function(e){for(var t=0;t<e.edges.length;t++){var i=e.edges[t];i.unselect(),this._removeFromSelection(i)}}},{key:"blurObject",value:function(e){e.hover===!0&&(e.hover=!1,e instanceof a["default"]?this.body.emitter.emit("blurNode",{node:e.id}):this.body.emitter.emit("blurEdge",{edge:e.id}))}},{key:"hoverObject",value:function(e){var t=!1;for(var i in this.hoverObj.nodes)this.hoverObj.nodes.hasOwnProperty(i)&&(void 0===e||e instanceof a["default"]&&e.id!=i||e instanceof d["default"])&&(this.blurObject(this.hoverObj.nodes[i]),delete this.hoverObj.nodes[i],t=!0);for(var o in this.hoverObj.edges)this.hoverObj.edges.hasOwnProperty(o)&&(t===!0?(this.hoverObj.edges[o].hover=!1,delete this.hoverObj.edges[o]):void 0===e&&(this.blurObject(this.hoverObj.edges[o]),delete this.hoverObj.edges[o],t=!0));void 0!==e&&(e.hover===!1&&(e.hover=!0,this._addToHover(e),t=!0,e instanceof a["default"]?this.body.emitter.emit("hoverNode",{node:e.id}):this.body.emitter.emit("hoverEdge",{edge:e.id})),e instanceof a["default"]&&this.options.hoverConnectedEdges===!0&&this._hoverConnectedEdges(e)),t===!0&&this.body.emitter.emit("_requestRedraw")}},{key:"getSelection",value:function(){var e=this.getSelectedNodes(),t=this.getSelectedEdges();return{nodes:e,edges:t}}},{key:"getSelectedNodes",value:function(){var e=[];if(this.options.selectable===!0)for(var t in this.selectionObj.nodes)this.selectionObj.nodes.hasOwnProperty(t)&&e.push(this.selectionObj.nodes[t].id);return e}},{key:"getSelectedEdges",value:function(){var e=[];if(this.options.selectable===!0)for(var t in this.selectionObj.edges)this.selectionObj.edges.hasOwnProperty(t)&&e.push(this.selectionObj.edges[t].id);return e}},{key:"setSelection",value:function(e){var t=arguments.length<=1||void 0===arguments[1]?{}:arguments[1],i=void 0,o=void 0;if(!e||!e.nodes&&!e.edges)throw"Selection must be an object with nodes and/or edges properties";if((t.unselectAll||void 0===t.unselectAll)&&this.unselectAll(),e.nodes)for(i=0;i<e.nodes.length;i++){o=e.nodes[i];var n=this.body.nodes[o];if(!n)throw new RangeError('Node with id "'+o+'" not found');this.selectObject(n,t.highlightEdges)}if(e.edges)for(i=0;i<e.edges.length;i++){o=e.edges[i];var s=this.body.edges[o];if(!s)throw new RangeError('Edge with id "'+o+'" not found');this.selectObject(s)}this.body.emitter.emit("_requestRedraw")}},{key:"selectNodes",value:function(e){var t=arguments.length<=1||void 0===arguments[1]||arguments[1];if(!e||void 0===e.length)throw"Selection must be an array with ids";this.setSelection({nodes:e},{highlightEdges:t})}},{key:"selectEdges",value:function(e){if(!e||void 0===e.length)throw"Selection must be an array with ids";this.setSelection({edges:e})}},{key:"updateSelection",value:function(){for(var e in this.selectionObj.nodes)this.selectionObj.nodes.hasOwnProperty(e)&&(this.body.nodes.hasOwnProperty(e)||delete this.selectionObj.nodes[e]);for(var t in this.selectionObj.edges)this.selectionObj.edges.hasOwnProperty(t)&&(this.body.edges.hasOwnProperty(t)||delete this.selectionObj.edges[t])}}]),e}();t["default"]=c},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s=function(){function e(e,t){var i=[],o=!0,n=!1,s=void 0;try{for(var r,a=e[Symbol.iterator]();!(o=(r=a.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(h){n=!0,s=h}finally{try{!o&&a["return"]&&a["return"]()}finally{if(n)throw s}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},a=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),h=i(54),d=o(h),l=i(1),c=function(){function e(t){n(this,e),this.body=t,this.initialRandomSeed=Math.round(1e6*Math.random()),this.randomSeed=this.initialRandomSeed,this.setPhysics=!1,this.options={},this.optionsBackup={physics:{}},this.defaultOptions={randomSeed:void 0,improvedLayout:!0,hierarchical:{enabled:!1,levelSeparation:150,nodeSpacing:100,treeSpacing:200,blockShifting:!0,edgeMinimization:!0,parentCentralization:!0,direction:"UD",sortMethod:"hubsize"}},l.extend(this.options,this.defaultOptions),this.bindEventListeners()}return a(e,[{key:"bindEventListeners",value:function(){var e=this;this.body.emitter.on("_dataChanged",function(){e.setupHierarchicalLayout()}),this.body.emitter.on("_dataLoaded",function(){e.layoutNetwork()}),this.body.emitter.on("_resetHierarchicalLayout",function(){e.setupHierarchicalLayout()})}},{key:"setOptions",value:function(e,t){if(void 0!==e){var i=this.options.hierarchical.enabled;if(l.selectiveDeepExtend(["randomSeed","improvedLayout"],this.options,e),l.mergeOptions(this.options,e,"hierarchical"),void 0!==e.randomSeed&&(this.initialRandomSeed=e.randomSeed),this.options.hierarchical.enabled===!0)return i===!0&&this.body.emitter.emit("refresh",!0),"RL"===this.options.hierarchical.direction||"DU"===this.options.hierarchical.direction?this.options.hierarchical.levelSeparation>0&&(this.options.hierarchical.levelSeparation*=-1):this.options.hierarchical.levelSeparation<0&&(this.options.hierarchical.levelSeparation*=-1),this.body.emitter.emit("_resetHierarchicalLayout"),this.adaptAllOptionsForHierarchicalLayout(t);if(i===!0)return this.body.emitter.emit("refresh"),l.deepExtend(t,this.optionsBackup)}return t}},{key:"adaptAllOptionsForHierarchicalLayout",value:function(e){if(this.options.hierarchical.enabled===!0){void 0===e.physics||e.physics===!0?(e.physics={enabled:void 0===this.optionsBackup.physics.enabled||this.optionsBackup.physics.enabled,solver:"hierarchicalRepulsion"},this.optionsBackup.physics.enabled=void 0===this.optionsBackup.physics.enabled||this.optionsBackup.physics.enabled,this.optionsBackup.physics.solver=this.optionsBackup.physics.solver||"barnesHut"):"object"===r(e.physics)?(this.optionsBackup.physics.enabled=void 0===e.physics.enabled||e.physics.enabled,this.optionsBackup.physics.solver=e.physics.solver||"barnesHut",e.physics.solver="hierarchicalRepulsion"):e.physics!==!1&&(this.optionsBackup.physics.solver="barnesHut",e.physics={solver:"hierarchicalRepulsion"});var t="horizontal";"RL"!==this.options.hierarchical.direction&&"LR"!==this.options.hierarchical.direction||(t="vertical"),void 0===e.edges?(this.optionsBackup.edges={smooth:{enabled:!0,type:"dynamic"}},e.edges={smooth:!1}):void 0===e.edges.smooth?(this.optionsBackup.edges={smooth:{enabled:!0,type:"dynamic"}},e.edges.smooth=!1):"boolean"==typeof e.edges.smooth?(this.optionsBackup.edges={smooth:e.edges.smooth},e.edges.smooth={enabled:e.edges.smooth,type:t}):(void 0!==e.edges.smooth.type&&"dynamic"!==e.edges.smooth.type&&(t=e.edges.smooth.type),this.optionsBackup.edges={smooth:void 0===e.edges.smooth.enabled||e.edges.smooth.enabled,type:void 0===e.edges.smooth.type?"dynamic":e.edges.smooth.type,roundness:void 0===e.edges.smooth.roundness?.5:e.edges.smooth.roundness,forceDirection:void 0!==e.edges.smooth.forceDirection&&e.edges.smooth.forceDirection},e.edges.smooth={enabled:void 0===e.edges.smooth.enabled||e.edges.smooth.enabled,type:t,roundness:void 0===e.edges.smooth.roundness?.5:e.edges.smooth.roundness,forceDirection:void 0!==e.edges.smooth.forceDirection&&e.edges.smooth.forceDirection}),this.body.emitter.emit("_forceDisableDynamicCurves",t)}return e}},{key:"seededRandom",value:function(){var e=1e4*Math.sin(this.randomSeed++);return e-Math.floor(e)}},{key:"positionInitially",value:function(e){if(this.options.hierarchical.enabled!==!0){this.randomSeed=this.initialRandomSeed;for(var t=0;t<e.length;t++){var i=e[t],o=1*e.length+10,n=2*Math.PI*this.seededRandom();void 0===i.x&&(i.x=o*Math.cos(n)),void 0===i.y&&(i.y=o*Math.sin(n))}}}},{key:"layoutNetwork",value:function(){if(this.options.hierarchical.enabled!==!0&&this.options.improvedLayout===!0){for(var e=0,t=0;t<this.body.nodeIndices.length;t++){var i=this.body.nodes[this.body.nodeIndices[t]];i.predefinedPosition===!0&&(e+=1)}if(e<.5*this.body.nodeIndices.length){var o=10,n=0,s=100;if(this.body.nodeIndices.length>s){for(var r=this.body.nodeIndices.length;this.body.nodeIndices.length>s;){n+=1;var a=this.body.nodeIndices.length;n%3===0?this.body.modules.clustering.clusterBridges():this.body.modules.clustering.clusterOutliers();var h=this.body.nodeIndices.length;if(a==h&&n%3!==0||n>o)return this._declusterAll(),this.body.emitter.emit("_layoutFailed"),void console.info("This network could not be positioned by this version of the improved layout algorithm. Please disable improvedLayout for better performance.")}this.body.modules.kamadaKawai.setOptions({springLength:Math.max(150,2*r)})}this.body.modules.kamadaKawai.solve(this.body.nodeIndices,this.body.edgeIndices,!0),this._shiftToCenter();for(var d=70,l=0;l<this.body.nodeIndices.length;l++)this.body.nodes[this.body.nodeIndices[l]].x+=(.5-this.seededRandom())*d,this.body.nodes[this.body.nodeIndices[l]].y+=(.5-this.seededRandom())*d;this._declusterAll(),this.body.emitter.emit("_repositionBezierNodes")}}}},{key:"_shiftToCenter",value:function(){for(var e=d["default"].getRangeCore(this.body.nodes,this.body.nodeIndices),t=d["default"].findCenter(e),i=0;i<this.body.nodeIndices.length;i++)this.body.nodes[this.body.nodeIndices[i]].x-=t.x,this.body.nodes[this.body.nodeIndices[i]].y-=t.y}},{key:"_declusterAll",value:function(){for(var e=!0;e===!0;){e=!1;for(var t=0;t<this.body.nodeIndices.length;t++)this.body.nodes[this.body.nodeIndices[t]].isCluster===!0&&(e=!0,
this.body.modules.clustering.openCluster(this.body.nodeIndices[t],{},!1));e===!0&&this.body.emitter.emit("_dataChanged")}}},{key:"getSeed",value:function(){return this.initialRandomSeed}},{key:"setupHierarchicalLayout",value:function(){if(this.options.hierarchical.enabled===!0&&this.body.nodeIndices.length>0){var e=void 0,t=void 0,i=!1,o=!0,n=!1;this.hierarchicalLevels={},this.lastNodeOnLevel={},this.hierarchicalChildrenReference={},this.hierarchicalParentReference={},this.hierarchicalTrees={},this.treeIndex=-1,this.distributionOrdering={},this.distributionIndex={},this.distributionOrderingPresence={};for(t in this.body.nodes)this.body.nodes.hasOwnProperty(t)&&(e=this.body.nodes[t],void 0===e.options.x&&void 0===e.options.y&&(o=!1),void 0!==e.options.level?(i=!0,this.hierarchicalLevels[t]=e.options.level):n=!0);if(n===!0&&i===!0)throw new Error("To use the hierarchical layout, nodes require either no predefined levels or levels have to be defined for all nodes.");n===!0&&("hubsize"===this.options.hierarchical.sortMethod?this._determineLevelsByHubsize():"directed"===this.options.hierarchical.sortMethod?this._determineLevelsDirected():"custom"===this.options.hierarchical.sortMethod&&this._determineLevelsCustomCallback());for(var s in this.body.nodes)this.body.nodes.hasOwnProperty(s)&&void 0===this.hierarchicalLevels[s]&&(this.hierarchicalLevels[s]=0);var r=this._getDistribution();this._generateMap(),this._placeNodesByHierarchy(r),this._condenseHierarchy(),this._shiftToCenter()}}},{key:"_condenseHierarchy",value:function(){var e=this,t=!1,i={},o=function(){for(var t=a(),i=0;i<t.length-1;i++){var o=t[i].max-t[i+1].min;n(i+1,o+e.options.hierarchical.treeSpacing)}},n=function(t,i){for(var o in e.hierarchicalTrees)if(e.hierarchicalTrees.hasOwnProperty(o)&&e.hierarchicalTrees[o]===t){var n=e.body.nodes[o],s=e._getPositionForHierarchy(n);e._setPositionForHierarchy(n,s+i,void 0,!0)}},r=function(t){var i=1e9,o=-1e9;for(var n in e.hierarchicalTrees)if(e.hierarchicalTrees.hasOwnProperty(n)&&e.hierarchicalTrees[n]===t){var s=e._getPositionForHierarchy(e.body.nodes[n]);i=Math.min(s,i),o=Math.max(s,o)}return{min:i,max:o}},a=function(){for(var t=[],i=0;i<=e.treeIndex;i++)t.push(r(i));return t},h=function _(t,i){if(i[t.id]=!0,e.hierarchicalChildrenReference[t.id]){var o=e.hierarchicalChildrenReference[t.id];if(o.length>0)for(var n=0;n<o.length;n++)_(e.body.nodes[o[n]],i)}},d=function(t){var i=arguments.length<=1||void 0===arguments[1]?1e9:arguments[1],o=1e9,n=1e9,r=1e9,a=-1e9;for(var h in t)if(t.hasOwnProperty(h)){var d=e.body.nodes[h],l=e.hierarchicalLevels[d.id],c=e._getPositionForHierarchy(d),u=e._getSpaceAroundNode(d,t),f=s(u,2),p=f[0],v=f[1];o=Math.min(p,o),n=Math.min(v,n),l<=i&&(r=Math.min(c,r),a=Math.max(c,a))}return[r,a,o,n]},l=function w(t){var i=e.hierarchicalLevels[t];if(e.hierarchicalChildrenReference[t]){var o=e.hierarchicalChildrenReference[t];if(o.length>0)for(var n=0;n<o.length;n++)i=Math.max(i,w(o[n]))}return i},c=function(e,t){var i=l(e.id),o=l(t.id);return Math.min(i,o)},u=function(t,i){var o=e.hierarchicalParentReference[t.id],n=e.hierarchicalParentReference[i.id];if(void 0===o||void 0===n)return!1;for(var s=0;s<o.length;s++)for(var r=0;r<n.length;r++)if(o[s]==n[r])return!0;return!1},f=function(t,i,o){for(var n=0;n<i.length;n++){var s=i[n],r=e.distributionOrdering[s];if(r.length>1)for(var a=0;a<r.length-1;a++)u(r[a],r[a+1])===!0&&e.hierarchicalTrees[r[a].id]===e.hierarchicalTrees[r[a+1].id]&&t(r[a],r[a+1],o)}},p=function(i,o){var n=!(arguments.length<=2||void 0===arguments[2])&&arguments[2],r=e._getPositionForHierarchy(i),a=e._getPositionForHierarchy(o),l=Math.abs(a-r);if(l>e.options.hierarchical.nodeSpacing){var u={};u[i.id]=!0;var f={};f[o.id]=!0,h(i,u),h(o,f);var p=c(i,o),v=d(u,p),y=s(v,4),g=(y[0],y[1]),b=(y[2],y[3],d(f,p)),m=s(b,4),_=m[0],w=(m[1],m[2]),k=(m[3],Math.abs(g-_));if(k>e.options.hierarchical.nodeSpacing){var x=g-_+e.options.hierarchical.nodeSpacing;x<-w+e.options.hierarchical.nodeSpacing&&(x=-w+e.options.hierarchical.nodeSpacing),x<0&&(e._shiftBlock(o.id,x),t=!0,n===!0&&e._centerParent(o))}}},v=function(o,n){for(var r=n.id,a=n.edges,l=e.hierarchicalLevels[n.id],c=e.options.hierarchical.levelSeparation*e.options.hierarchical.levelSeparation,u={},f=[],p=0;p<a.length;p++){var v=a[p];if(v.toId!=v.fromId){var y=v.toId==r?v.from:v.to;u[a[p].id]=y,e.hierarchicalLevels[y.id]<l&&f.push(v)}}var g=function(t,i){for(var o=0,n=0;n<i.length;n++)if(void 0!==u[i[n].id]){var s=e._getPositionForHierarchy(u[i[n].id])-t;o+=s/Math.sqrt(s*s+c)}return o},b=function(t,i){for(var o=0,n=0;n<i.length;n++)if(void 0!==u[i[n].id]){var s=e._getPositionForHierarchy(u[i[n].id])-t;o-=c*Math.pow(s*s+c,-1.5)}return o},m=function(t,i){for(var o=e._getPositionForHierarchy(n),s={},r=0;r<t;r++){var a=g(o,i),h=b(o,i),d=40,l=Math.max(-d,Math.min(d,Math.round(a/h)));if(o-=l,void 0!==s[o])break;s[o]=r}return o},_=function(o){var r=e._getPositionForHierarchy(n);if(void 0===i[n.id]){var a={};a[n.id]=!0,h(n,a),i[n.id]=a}var l=d(i[n.id]),c=s(l,4),u=(c[0],c[1],c[2]),f=c[3],p=o-r,v=0;p>0?v=Math.min(p,f-e.options.hierarchical.nodeSpacing):p<0&&(v=-Math.min(-p,u-e.options.hierarchical.nodeSpacing)),0!=v&&(e._shiftBlock(n.id,v),t=!0)},w=function(i){var o=e._getPositionForHierarchy(n),r=e._getSpaceAroundNode(n),a=s(r,2),h=a[0],d=a[1],l=i-o,c=o;l>0?c=Math.min(o+(d-e.options.hierarchical.nodeSpacing),i):l<0&&(c=Math.max(o-(h-e.options.hierarchical.nodeSpacing),i)),c!==o&&(e._setPositionForHierarchy(n,c,void 0,!0),t=!0)},k=m(o,f);_(k),k=m(o,a),w(k)},y=function(i){var o=Object.keys(e.distributionOrdering);o=o.reverse();for(var n=0;n<i;n++){t=!1;for(var s=0;s<o.length;s++)for(var r=o[s],a=e.distributionOrdering[r],h=0;h<a.length;h++)v(1e3,a[h]);if(t!==!0)break}},g=function(i){var o=Object.keys(e.distributionOrdering);o=o.reverse();for(var n=0;n<i&&(t=!1,f(p,o,!0),t===!0);n++);},b=function(){for(var t in e.body.nodes)e.body.nodes.hasOwnProperty(t)&&e._centerParent(e.body.nodes[t])},m=function(){var t=Object.keys(e.distributionOrdering);t=t.reverse();for(var i=0;i<t.length;i++)for(var o=t[i],n=e.distributionOrdering[o],s=0;s<n.length;s++)e._centerParent(n[s])};this.options.hierarchical.blockShifting===!0&&(g(5),b()),this.options.hierarchical.edgeMinimization===!0&&y(20),this.options.hierarchical.parentCentralization===!0&&m(),o()}},{key:"_getSpaceAroundNode",value:function(e,t){var i=!0;void 0===t&&(i=!1);var o=this.hierarchicalLevels[e.id];if(void 0!==o){var n=this.distributionIndex[e.id],s=this._getPositionForHierarchy(e),r=1e9,a=1e9;if(0!==n){var h=this.distributionOrdering[o][n-1];if(i===!0&&void 0===t[h.id]||i===!1){var d=this._getPositionForHierarchy(h);r=s-d}}if(n!=this.distributionOrdering[o].length-1){var l=this.distributionOrdering[o][n+1];if(i===!0&&void 0===t[l.id]||i===!1){var c=this._getPositionForHierarchy(l);a=Math.min(a,c-s)}}return[r,a]}return[0,0]}},{key:"_centerParent",value:function(e){if(this.hierarchicalParentReference[e.id])for(var t=this.hierarchicalParentReference[e.id],i=0;i<t.length;i++){var o=t[i],n=this.body.nodes[o];if(this.hierarchicalChildrenReference[o]){var r=1e9,a=-1e9,h=this.hierarchicalChildrenReference[o];if(h.length>0)for(var d=0;d<h.length;d++){var l=this.body.nodes[h[d]];r=Math.min(r,this._getPositionForHierarchy(l)),a=Math.max(a,this._getPositionForHierarchy(l))}var c=this._getPositionForHierarchy(n),u=this._getSpaceAroundNode(n),f=s(u,2),p=f[0],v=f[1],y=.5*(r+a),g=c-y;(g<0&&Math.abs(g)<v-this.options.hierarchical.nodeSpacing||g>0&&Math.abs(g)<p-this.options.hierarchical.nodeSpacing)&&this._setPositionForHierarchy(n,y,void 0,!0)}}}},{key:"_placeNodesByHierarchy",value:function(e){this.positionedNodes={};for(var t in e)if(e.hasOwnProperty(t)){var i=Object.keys(e[t]);i=this._indexArrayToNodes(i),this._sortNodeArray(i);for(var o=0,n=0;n<i.length;n++){var s=i[n];if(void 0===this.positionedNodes[s.id]){var r=this.options.hierarchical.nodeSpacing*o;o>0&&(r=this._getPositionForHierarchy(i[n-1])+this.options.hierarchical.nodeSpacing),this._setPositionForHierarchy(s,r,t),this._validataPositionAndContinue(s,t,r),o++}}}}},{key:"_placeBranchNodes",value:function(e,t){if(void 0!==this.hierarchicalChildrenReference[e]){for(var i=[],o=0;o<this.hierarchicalChildrenReference[e].length;o++)i.push(this.body.nodes[this.hierarchicalChildrenReference[e][o]]);this._sortNodeArray(i);for(var n=0;n<i.length;n++){var s=i[n],r=this.hierarchicalLevels[s.id];if(!(r>t&&void 0===this.positionedNodes[s.id]))return;var a=void 0;a=0===n?this._getPositionForHierarchy(this.body.nodes[e]):this._getPositionForHierarchy(i[n-1])+this.options.hierarchical.nodeSpacing,this._setPositionForHierarchy(s,a,r),this._validataPositionAndContinue(s,r,a)}for(var h=1e9,d=-1e9,l=0;l<i.length;l++){var c=i[l].id;h=Math.min(h,this._getPositionForHierarchy(this.body.nodes[c])),d=Math.max(d,this._getPositionForHierarchy(this.body.nodes[c]))}this._setPositionForHierarchy(this.body.nodes[e],.5*(h+d),t)}}},{key:"_validataPositionAndContinue",value:function(e,t,i){if(void 0!==this.lastNodeOnLevel[t]){var o=this._getPositionForHierarchy(this.body.nodes[this.lastNodeOnLevel[t]]);if(i-o<this.options.hierarchical.nodeSpacing){var n=o+this.options.hierarchical.nodeSpacing-i,s=this._findCommonParent(this.lastNodeOnLevel[t],e.id);this._shiftBlock(s.withChild,n)}}this.lastNodeOnLevel[t]=e.id,this.positionedNodes[e.id]=!0,this._placeBranchNodes(e.id,t)}},{key:"_indexArrayToNodes",value:function(e){for(var t=[],i=0;i<e.length;i++)t.push(this.body.nodes[e[i]]);return t}},{key:"_getDistribution",value:function(){var e={},t=void 0,i=void 0;for(t in this.body.nodes)if(this.body.nodes.hasOwnProperty(t)){i=this.body.nodes[t];var o=void 0===this.hierarchicalLevels[t]?0:this.hierarchicalLevels[t];"UD"===this.options.hierarchical.direction||"DU"===this.options.hierarchical.direction?(i.y=this.options.hierarchical.levelSeparation*o,i.options.fixed.y=!0):(i.x=this.options.hierarchical.levelSeparation*o,i.options.fixed.x=!0),void 0===e[o]&&(e[o]={}),e[o][t]=i}return e}},{key:"_getHubSize",value:function(){var e=0;for(var t in this.body.nodes)if(this.body.nodes.hasOwnProperty(t)){var i=this.body.nodes[t];void 0===this.hierarchicalLevels[t]&&(e=i.edges.length<e?e:i.edges.length)}return e}},{key:"_determineLevelsByHubsize",value:function(){for(var e=this,t=1,i=function(t,i){void 0===e.hierarchicalLevels[i.id]&&(void 0===e.hierarchicalLevels[t.id]&&(e.hierarchicalLevels[t.id]=0),e.hierarchicalLevels[i.id]=e.hierarchicalLevels[t.id]+1)};t>0&&(t=this._getHubSize(),0!==t);)for(var o in this.body.nodes)if(this.body.nodes.hasOwnProperty(o)){var n=this.body.nodes[o];n.edges.length===t&&this._crawlNetwork(i,o)}}},{key:"_determineLevelsCustomCallback",value:function(){var e=this,t=1e5,i=function(e,t,i){},o=function(o,n,s){var r=e.hierarchicalLevels[o.id];void 0===r&&(e.hierarchicalLevels[o.id]=t);var a=i(d["default"].cloneOptions(o,"node"),d["default"].cloneOptions(n,"node"),d["default"].cloneOptions(s,"edge"));e.hierarchicalLevels[n.id]=e.hierarchicalLevels[o.id]+a};this._crawlNetwork(o),this._setMinLevelToZero()}},{key:"_determineLevelsDirected",value:function(){var e=this,t=1e4,i=function(i,o,n){var s=e.hierarchicalLevels[i.id];void 0===s&&(e.hierarchicalLevels[i.id]=t),n.toId==o.id?e.hierarchicalLevels[o.id]=e.hierarchicalLevels[i.id]+1:e.hierarchicalLevels[o.id]=e.hierarchicalLevels[i.id]-1};this._crawlNetwork(i),this._setMinLevelToZero()}},{key:"_setMinLevelToZero",value:function(){var e=1e9;for(var t in this.body.nodes)this.body.nodes.hasOwnProperty(t)&&void 0!==this.hierarchicalLevels[t]&&(e=Math.min(this.hierarchicalLevels[t],e));for(var i in this.body.nodes)this.body.nodes.hasOwnProperty(i)&&void 0!==this.hierarchicalLevels[i]&&(this.hierarchicalLevels[i]-=e)}},{key:"_generateMap",value:function(){var e=this,t=function(t,i){if(e.hierarchicalLevels[i.id]>e.hierarchicalLevels[t.id]){var o=t.id,n=i.id;void 0===e.hierarchicalChildrenReference[o]&&(e.hierarchicalChildrenReference[o]=[]),e.hierarchicalChildrenReference[o].push(n),void 0===e.hierarchicalParentReference[n]&&(e.hierarchicalParentReference[n]=[]),e.hierarchicalParentReference[n].push(o)}};this._crawlNetwork(t)}},{key:"_crawlNetwork",value:function(){var e=this,t=arguments.length<=0||void 0===arguments[0]?function(){}:arguments[0],i=arguments[1],o={},n=0,s=function d(i,n){if(void 0===o[i.id]){void 0===e.hierarchicalTrees[i.id]&&(e.hierarchicalTrees[i.id]=n,e.treeIndex=Math.max(n,e.treeIndex)),o[i.id]=!0;for(var s=void 0,r=0;r<i.edges.length;r++)i.edges[r].connected===!0&&(s=i.edges[r].toId===i.id?i.edges[r].from:i.edges[r].to,i.id!==s.id&&(t(i,s,i.edges[r]),d(s,n)))}};if(void 0===i)for(var r=0;r<this.body.nodeIndices.length;r++){var a=this.body.nodes[this.body.nodeIndices[r]];void 0===o[a.id]&&(s(a,n),n+=1)}else{var h=this.body.nodes[i];if(void 0===h)return void console.error("Node not found:",i);s(h)}}},{key:"_shiftBlock",value:function(e,t){if("UD"===this.options.hierarchical.direction||"DU"===this.options.hierarchical.direction?this.body.nodes[e].x+=t:this.body.nodes[e].y+=t,void 0!==this.hierarchicalChildrenReference[e])for(var i=0;i<this.hierarchicalChildrenReference[e].length;i++)this._shiftBlock(this.hierarchicalChildrenReference[e][i],t)}},{key:"_findCommonParent",value:function(e,t){var i=this,o={},n=function r(e,t){if(void 0!==i.hierarchicalParentReference[t])for(var o=0;o<i.hierarchicalParentReference[t].length;o++){var n=i.hierarchicalParentReference[t][o];e[n]=!0,r(e,n)}},s=function a(e,t){if(void 0!==i.hierarchicalParentReference[t])for(var o=0;o<i.hierarchicalParentReference[t].length;o++){var n=i.hierarchicalParentReference[t][o];if(void 0!==e[n])return{foundParent:n,withChild:t};var s=a(e,n);if(null!==s.foundParent)return s}return{foundParent:null,withChild:t}};return n(o,e),s(o,t)}},{key:"_setPositionForHierarchy",value:function(e,t,i){var o=!(arguments.length<=3||void 0===arguments[3])&&arguments[3];o!==!0&&(void 0===this.distributionOrdering[i]&&(this.distributionOrdering[i]=[],this.distributionOrderingPresence[i]={}),void 0===this.distributionOrderingPresence[i][e.id]&&(this.distributionOrdering[i].push(e),this.distributionIndex[e.id]=this.distributionOrdering[i].length-1),this.distributionOrderingPresence[i][e.id]=!0),"UD"===this.options.hierarchical.direction||"DU"===this.options.hierarchical.direction?e.x=t:e.y=t}},{key:"_getPositionForHierarchy",value:function(e){return"UD"===this.options.hierarchical.direction||"DU"===this.options.hierarchical.direction?e.x:e.y}},{key:"_sortNodeArray",value:function(e){e.length>1&&("UD"===this.options.hierarchical.direction||"DU"===this.options.hierarchical.direction?e.sort(function(e,t){return e.x-t.x}):e.sort(function(e,t){return e.y-t.y}))}}]),e}();t["default"]=c},function(e,t,i){function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),s=i(1),r=i(58),a=i(61),h=function(){function e(t,i,n){var r=this;o(this,e),this.body=t,this.canvas=i,this.selectionHandler=n,this.editMode=!1,this.manipulationDiv=void 0,this.editModeDiv=void 0,this.closeDiv=void 0,this.manipulationHammers=[],this.temporaryUIFunctions={},this.temporaryEventFunctions=[],this.touchTime=0,this.temporaryIds={nodes:[],edges:[]},this.guiEnabled=!1,this.inMode=!1,this.selectedControlNode=void 0,this.options={},this.defaultOptions={enabled:!1,initiallyActive:!1,addNode:!0,addEdge:!0,editNode:void 0,editEdge:!0,deleteNode:!0,deleteEdge:!0,controlNodeStyle:{shape:"dot",size:6,color:{background:"#ff0000",border:"#3c3c3c",highlight:{background:"#07f968",border:"#3c3c3c"}},borderWidth:2,borderWidthSelected:2}},s.extend(this.options,this.defaultOptions),this.body.emitter.on("destroy",function(){r._clean()}),this.body.emitter.on("_dataChanged",this._restore.bind(this)),this.body.emitter.on("_resetData",this._restore.bind(this))}return n(e,[{key:"_restore",value:function(){this.inMode!==!1&&(this.options.initiallyActive===!0?this.enableEditMode():this.disableEditMode())}},{key:"setOptions",value:function(e,t,i){void 0!==t&&(void 0!==t.locale?this.options.locale=t.locale:this.options.locale=i.locale,void 0!==t.locales?this.options.locales=t.locales:this.options.locales=i.locales),void 0!==e&&("boolean"==typeof e?this.options.enabled=e:(this.options.enabled=!0,s.deepExtend(this.options,e)),this.options.initiallyActive===!0&&(this.editMode=!0),this._setup())}},{key:"toggleEditMode",value:function(){this.editMode===!0?this.disableEditMode():this.enableEditMode()}},{key:"enableEditMode",value:function(){this.editMode=!0,this._clean(),this.guiEnabled===!0&&(this.manipulationDiv.style.display="block",this.closeDiv.style.display="block",this.editModeDiv.style.display="none",this.showManipulatorToolbar())}},{key:"disableEditMode",value:function(){this.editMode=!1,this._clean(),this.guiEnabled===!0&&(this.manipulationDiv.style.display="none",this.closeDiv.style.display="none",this.editModeDiv.style.display="block",this._createEditButton())}},{key:"showManipulatorToolbar",value:function(){if(this._clean(),this.manipulationDOM={},this.guiEnabled===!0){this.editMode=!0,this.manipulationDiv.style.display="block",this.closeDiv.style.display="block";var e=this.selectionHandler._getSelectedNodeCount(),t=this.selectionHandler._getSelectedEdgeCount(),i=e+t,o=this.options.locales[this.options.locale],n=!1;this.options.addNode!==!1&&(this._createAddNodeButton(o),n=!0),this.options.addEdge!==!1&&(n===!0?this._createSeperator(1):n=!0,this._createAddEdgeButton(o)),1===e&&"function"==typeof this.options.editNode?(n===!0?this._createSeperator(2):n=!0,this._createEditNodeButton(o)):1===t&&0===e&&this.options.editEdge!==!1&&(n===!0?this._createSeperator(3):n=!0,this._createEditEdgeButton(o)),0!==i&&(e>0&&this.options.deleteNode!==!1?(n===!0&&this._createSeperator(4),this._createDeleteButton(o)):0===e&&this.options.deleteEdge!==!1&&(n===!0&&this._createSeperator(4),this._createDeleteButton(o))),this._bindHammerToDiv(this.closeDiv,this.toggleEditMode.bind(this)),this._temporaryBindEvent("select",this.showManipulatorToolbar.bind(this))}this.body.emitter.emit("_redraw")}},{key:"addNodeMode",value:function(){if(this.editMode!==!0&&this.enableEditMode(),this._clean(),this.inMode="addNode",this.guiEnabled===!0){var e=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(e),this._createSeperator(),this._createDescription(e.addDescription||this.options.locales.en.addDescription),this._bindHammerToDiv(this.closeDiv,this.toggleEditMode.bind(this))}this._temporaryBindEvent("click",this._performAddNode.bind(this))}},{key:"editNode",value:function(){var e=this;this.editMode!==!0&&this.enableEditMode(),this._clean();var t=this.selectionHandler._getSelectedNode();if(void 0!==t){if(this.inMode="editNode","function"!=typeof this.options.editNode)throw new Error("No function has been configured to handle the editing of nodes.");if(t.isCluster!==!0){var i=s.deepExtend({},t.options,!1);if(i.x=t.x,i.y=t.y,2!==this.options.editNode.length)throw new Error("The function for edit does not support two arguments (data, callback)");this.options.editNode(i,function(t){null!==t&&void 0!==t&&"editNode"===e.inMode&&e.body.data.nodes.getDataSet().update(t),e.showManipulatorToolbar()})}else alert(this.options.locales[this.options.locale].editClusterError||this.options.locales.en.editClusterError)}else this.showManipulatorToolbar()}},{key:"addEdgeMode",value:function(){if(this.editMode!==!0&&this.enableEditMode(),this._clean(),this.inMode="addEdge",this.guiEnabled===!0){var e=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(e),this._createSeperator(),this._createDescription(e.edgeDescription||this.options.locales.en.edgeDescription),this._bindHammerToDiv(this.closeDiv,this.toggleEditMode.bind(this))}this._temporaryBindUI("onTouch",this._handleConnect.bind(this)),this._temporaryBindUI("onDragEnd",this._finishConnect.bind(this)),this._temporaryBindUI("onDrag",this._dragControlNode.bind(this)),this._temporaryBindUI("onRelease",this._finishConnect.bind(this)),this._temporaryBindUI("onDragStart",function(){}),this._temporaryBindUI("onHold",function(){})}},{key:"editEdgeMode",value:function(){var e=this;if(this.editMode!==!0&&this.enableEditMode(),this._clean(),this.inMode="editEdge",this.guiEnabled===!0){var t=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(t),this._createSeperator(),this._createDescription(t.editEdgeDescription||this.options.locales.en.editEdgeDescription),this._bindHammerToDiv(this.closeDiv,this.toggleEditMode.bind(this))}this.edgeBeingEditedId=this.selectionHandler.getSelectedEdges()[0],void 0!==this.edgeBeingEditedId?!function(){var t=e.body.edges[e.edgeBeingEditedId],i=e._getNewTargetNode(t.from.x,t.from.y),o=e._getNewTargetNode(t.to.x,t.to.y);e.temporaryIds.nodes.push(i.id),e.temporaryIds.nodes.push(o.id),e.body.nodes[i.id]=i,e.body.nodeIndices.push(i.id),e.body.nodes[o.id]=o,e.body.nodeIndices.push(o.id),e._temporaryBindUI("onTouch",e._controlNodeTouch.bind(e)),e._temporaryBindUI("onTap",function(){}),e._temporaryBindUI("onHold",function(){}),e._temporaryBindUI("onDragStart",e._controlNodeDragStart.bind(e)),e._temporaryBindUI("onDrag",e._controlNodeDrag.bind(e)),e._temporaryBindUI("onDragEnd",e._controlNodeDragEnd.bind(e)),e._temporaryBindUI("onMouseMove",function(){}),e._temporaryBindEvent("beforeDrawing",function(e){var n=t.edgeType.findBorderPositions(e);i.selected===!1&&(i.x=n.from.x,i.y=n.from.y),o.selected===!1&&(o.x=n.to.x,o.y=n.to.y)}),e.body.emitter.emit("_redraw")}():this.showManipulatorToolbar()}},{key:"deleteSelected",value:function(){var e=this;this.editMode!==!0&&this.enableEditMode(),this._clean(),this.inMode="delete";var t=this.selectionHandler.getSelectedNodes(),i=this.selectionHandler.getSelectedEdges(),o=void 0;if(t.length>0){for(var n=0;n<t.length;n++)if(this.body.nodes[t[n]].isCluster===!0)return void alert(this.options.locales[this.options.locale].deleteClusterError||this.options.locales.en.deleteClusterError);"function"==typeof this.options.deleteNode&&(o=this.options.deleteNode)}else i.length>0&&"function"==typeof this.options.deleteEdge&&(o=this.options.deleteEdge);if("function"==typeof o){var s={nodes:t,edges:i};if(2!==o.length)throw new Error("The function for delete does not support two arguments (data, callback)");o(s,function(t){null!==t&&void 0!==t&&"delete"===e.inMode?(e.body.data.edges.getDataSet().remove(t.edges),e.body.data.nodes.getDataSet().remove(t.nodes),e.body.emitter.emit("startSimulation"),e.showManipulatorToolbar()):(e.body.emitter.emit("startSimulation"),e.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().remove(i),this.body.data.nodes.getDataSet().remove(t),this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar()}},{key:"_setup",value:function(){this.options.enabled===!0?(this.guiEnabled=!0,this._createWrappers(),this.editMode===!1?this._createEditButton():this.showManipulatorToolbar()):(this._removeManipulationDOM(),this.guiEnabled=!1)}},{key:"_createWrappers",value:function(){void 0===this.manipulationDiv&&(this.manipulationDiv=document.createElement("div"),this.manipulationDiv.className="vis-manipulation",this.editMode===!0?this.manipulationDiv.style.display="block":this.manipulationDiv.style.display="none",this.canvas.frame.appendChild(this.manipulationDiv)),void 0===this.editModeDiv&&(this.editModeDiv=document.createElement("div"),this.editModeDiv.className="vis-edit-mode",this.editMode===!0?this.editModeDiv.style.display="none":this.editModeDiv.style.display="block",this.canvas.frame.appendChild(this.editModeDiv)),void 0===this.closeDiv&&(this.closeDiv=document.createElement("div"),this.closeDiv.className="vis-close",this.closeDiv.style.display=this.manipulationDiv.style.display,this.canvas.frame.appendChild(this.closeDiv))}},{key:"_getNewTargetNode",value:function(e,t){var i=s.deepExtend({},this.options.controlNodeStyle);i.id="targetNode"+s.randomUUID(),i.hidden=!1,i.physics=!1,i.x=e,i.y=t;var o=this.body.functions.createNode(i);return o.shape.boundingBox={left:e,right:e,top:t,bottom:t},o}},{key:"_createEditButton",value:function(){this._clean(),this.manipulationDOM={},s.recursiveDOMDelete(this.editModeDiv);var e=this.options.locales[this.options.locale],t=this._createButton("editMode","vis-button vis-edit vis-edit-mode",e.edit||this.options.locales.en.edit);this.editModeDiv.appendChild(t),this._bindHammerToDiv(t,this.toggleEditMode.bind(this))}},{key:"_clean",value:function(){this.inMode=!1,this.guiEnabled===!0&&(s.recursiveDOMDelete(this.editModeDiv),s.recursiveDOMDelete(this.manipulationDiv),this._cleanManipulatorHammers()),this._cleanupTemporaryNodesAndEdges(),this._unbindTemporaryUIs(),this._unbindTemporaryEvents(),this.body.emitter.emit("restorePhysics")}},{key:"_cleanManipulatorHammers",value:function(){if(0!=this.manipulationHammers.length){for(var e=0;e<this.manipulationHammers.length;e++)this.manipulationHammers[e].destroy();this.manipulationHammers=[]}}},{key:"_removeManipulationDOM",value:function(){this._clean(),s.recursiveDOMDelete(this.manipulationDiv),s.recursiveDOMDelete(this.editModeDiv),s.recursiveDOMDelete(this.closeDiv),this.manipulationDiv&&this.canvas.frame.removeChild(this.manipulationDiv),this.editModeDiv&&this.canvas.frame.removeChild(this.editModeDiv),this.closeDiv&&this.canvas.frame.removeChild(this.closeDiv),this.manipulationDiv=void 0,this.editModeDiv=void 0,this.closeDiv=void 0}},{key:"_createSeperator",value:function(){var e=arguments.length<=0||void 0===arguments[0]?1:arguments[0];this.manipulationDOM["seperatorLineDiv"+e]=document.createElement("div"),this.manipulationDOM["seperatorLineDiv"+e].className="vis-separator-line",this.manipulationDiv.appendChild(this.manipulationDOM["seperatorLineDiv"+e])}},{key:"_createAddNodeButton",value:function(e){var t=this._createButton("addNode","vis-button vis-add",e.addNode||this.options.locales.en.addNode);this.manipulationDiv.appendChild(t),this._bindHammerToDiv(t,this.addNodeMode.bind(this))}},{key:"_createAddEdgeButton",value:function(e){var t=this._createButton("addEdge","vis-button vis-connect",e.addEdge||this.options.locales.en.addEdge);this.manipulationDiv.appendChild(t),this._bindHammerToDiv(t,this.addEdgeMode.bind(this))}},{key:"_createEditNodeButton",value:function(e){var t=this._createButton("editNode","vis-button vis-edit",e.editNode||this.options.locales.en.editNode);this.manipulationDiv.appendChild(t),this._bindHammerToDiv(t,this.editNode.bind(this))}},{key:"_createEditEdgeButton",value:function(e){var t=this._createButton("editEdge","vis-button vis-edit",e.editEdge||this.options.locales.en.editEdge);this.manipulationDiv.appendChild(t),this._bindHammerToDiv(t,this.editEdgeMode.bind(this))}},{key:"_createDeleteButton",value:function(e){if(this.options.rtl)var t="vis-button vis-delete-rtl";else var t="vis-button vis-delete";var i=this._createButton("delete",t,e.del||this.options.locales.en.del);this.manipulationDiv.appendChild(i),this._bindHammerToDiv(i,this.deleteSelected.bind(this))}},{key:"_createBackButton",value:function(e){var t=this._createButton("back","vis-button vis-back",e.back||this.options.locales.en.back);this.manipulationDiv.appendChild(t),this._bindHammerToDiv(t,this.showManipulatorToolbar.bind(this))}},{key:"_createButton",value:function(e,t,i){var o=arguments.length<=3||void 0===arguments[3]?"vis-label":arguments[3];return this.manipulationDOM[e+"Div"]=document.createElement("div"),this.manipulationDOM[e+"Div"].className=t,this.manipulationDOM[e+"Label"]=document.createElement("div"),this.manipulationDOM[e+"Label"].className=o,this.manipulationDOM[e+"Label"].innerHTML=i,this.manipulationDOM[e+"Div"].appendChild(this.manipulationDOM[e+"Label"]),this.manipulationDOM[e+"Div"]}},{key:"_createDescription",value:function(e){this.manipulationDiv.appendChild(this._createButton("description","vis-button vis-none",e))}},{key:"_temporaryBindEvent",value:function(e,t){this.temporaryEventFunctions.push({event:e,boundFunction:t}),this.body.emitter.on(e,t)}},{key:"_temporaryBindUI",value:function(e,t){if(void 0===this.body.eventListeners[e])throw new Error("This UI function does not exist. Typo? You tried: "+e+" possible are: "+JSON.stringify(Object.keys(this.body.eventListeners)));this.temporaryUIFunctions[e]=this.body.eventListeners[e],this.body.eventListeners[e]=t}},{key:"_unbindTemporaryUIs",value:function(){for(var e in this.temporaryUIFunctions)this.temporaryUIFunctions.hasOwnProperty(e)&&(this.body.eventListeners[e]=this.temporaryUIFunctions[e],delete this.temporaryUIFunctions[e]);this.temporaryUIFunctions={}}},{key:"_unbindTemporaryEvents",value:function(){for(var e=0;e<this.temporaryEventFunctions.length;e++){var t=this.temporaryEventFunctions[e].event,i=this.temporaryEventFunctions[e].boundFunction;this.body.emitter.off(t,i)}this.temporaryEventFunctions=[]}},{key:"_bindHammerToDiv",value:function(e,t){var i=new r(e,{});a.onTouch(i,t),this.manipulationHammers.push(i)}},{key:"_cleanupTemporaryNodesAndEdges",value:function(){for(var e=0;e<this.temporaryIds.edges.length;e++){this.body.edges[this.temporaryIds.edges[e]].disconnect(),delete this.body.edges[this.temporaryIds.edges[e]];var t=this.body.edgeIndices.indexOf(this.temporaryIds.edges[e]);t!==-1&&this.body.edgeIndices.splice(t,1)}for(var i=0;i<this.temporaryIds.nodes.length;i++){delete this.body.nodes[this.temporaryIds.nodes[i]];var o=this.body.nodeIndices.indexOf(this.temporaryIds.nodes[i]);o!==-1&&this.body.nodeIndices.splice(o,1)}this.temporaryIds={nodes:[],edges:[]}}},{key:"_controlNodeTouch",value:function(e){this.selectionHandler.unselectAll(),this.lastTouch=this.body.functions.getPointer(e.center),this.lastTouch.translation=s.extend({},this.body.view.translation)}},{key:"_controlNodeDragStart",value:function(e){var t=this.lastTouch,i=this.selectionHandler._pointerToPositionObject(t),o=this.body.nodes[this.temporaryIds.nodes[0]],n=this.body.nodes[this.temporaryIds.nodes[1]],s=this.body.edges[this.edgeBeingEditedId];this.selectedControlNode=void 0;var r=o.isOverlappingWith(i),a=n.isOverlappingWith(i);r===!0?(this.selectedControlNode=o,s.edgeType.from=o):a===!0&&(this.selectedControlNode=n,s.edgeType.to=n),void 0!==this.selectedControlNode&&this.selectionHandler.selectObject(this.selectedControlNode),this.body.emitter.emit("_redraw")}},{key:"_controlNodeDrag",value:function(e){this.body.emitter.emit("disablePhysics");var t=this.body.functions.getPointer(e.center),i=this.canvas.DOMtoCanvas(t);if(void 0!==this.selectedControlNode)this.selectedControlNode.x=i.x,this.selectedControlNode.y=i.y;else{var o=t.x-this.lastTouch.x,n=t.y-this.lastTouch.y;this.body.view.translation={x:this.lastTouch.translation.x+o,y:this.lastTouch.translation.y+n}}this.body.emitter.emit("_redraw")}},{key:"_controlNodeDragEnd",value:function(e){var t=this.body.functions.getPointer(e.center),i=this.selectionHandler._pointerToPositionObject(t),o=this.body.edges[this.edgeBeingEditedId];if(void 0!==this.selectedControlNode){this.selectionHandler.unselectAll();for(var n=this.selectionHandler._getAllNodesOverlappingWith(i),s=void 0,r=n.length-1;r>=0;r--)if(n[r]!==this.selectedControlNode.id){s=this.body.nodes[n[r]];break}if(void 0!==s&&void 0!==this.selectedControlNode)if(s.isCluster===!0)alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError);else{var a=this.body.nodes[this.temporaryIds.nodes[0]];this.selectedControlNode.id===a.id?this._performEditEdge(s.id,o.to.id):this._performEditEdge(o.from.id,s.id)}else o.updateEdgeType(),this.body.emitter.emit("restorePhysics");this.body.emitter.emit("_redraw")}}},{key:"_handleConnect",value:function(e){if((new Date).valueOf()-this.touchTime>100){this.lastTouch=this.body.functions.getPointer(e.center),this.lastTouch.translation=s.extend({},this.body.view.translation);
var t=this.lastTouch,i=this.selectionHandler.getNodeAt(t);if(void 0!==i)if(i.isCluster===!0)alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError);else{var o=this._getNewTargetNode(i.x,i.y);this.body.nodes[o.id]=o,this.body.nodeIndices.push(o.id);var n=this.body.functions.createEdge({id:"connectionEdge"+s.randomUUID(),from:i.id,to:o.id,physics:!1,smooth:{enabled:!0,type:"continuous",roundness:.5}});this.body.edges[n.id]=n,this.body.edgeIndices.push(n.id),this.temporaryIds.nodes.push(o.id),this.temporaryIds.edges.push(n.id)}this.touchTime=(new Date).valueOf()}}},{key:"_dragControlNode",value:function(e){var t=this.body.functions.getPointer(e.center);if(void 0!==this.temporaryIds.nodes[0]){var i=this.body.nodes[this.temporaryIds.nodes[0]];i.x=this.canvas._XconvertDOMtoCanvas(t.x),i.y=this.canvas._YconvertDOMtoCanvas(t.y),this.body.emitter.emit("_redraw")}else{var o=t.x-this.lastTouch.x,n=t.y-this.lastTouch.y;this.body.view.translation={x:this.lastTouch.translation.x+o,y:this.lastTouch.translation.y+n}}}},{key:"_finishConnect",value:function(e){var t=this.body.functions.getPointer(e.center),i=this.selectionHandler._pointerToPositionObject(t),o=void 0;void 0!==this.temporaryIds.edges[0]&&(o=this.body.edges[this.temporaryIds.edges[0]].fromId);for(var n=this.selectionHandler._getAllNodesOverlappingWith(i),s=void 0,r=n.length-1;r>=0;r--)if(this.temporaryIds.nodes.indexOf(n[r])===-1){s=this.body.nodes[n[r]];break}this._cleanupTemporaryNodesAndEdges(),void 0!==s&&(s.isCluster===!0?alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError):void 0!==this.body.nodes[o]&&void 0!==this.body.nodes[s.id]&&this._performAddEdge(o,s.id)),this.body.emitter.emit("_redraw")}},{key:"_performAddNode",value:function(e){var t=this,i={id:s.randomUUID(),x:e.pointer.canvas.x,y:e.pointer.canvas.y,label:"new"};if("function"==typeof this.options.addNode){if(2!==this.options.addNode.length)throw new Error("The function for add does not support two arguments (data,callback)");this.options.addNode(i,function(e){null!==e&&void 0!==e&&"addNode"===t.inMode&&(t.body.data.nodes.getDataSet().add(e),t.showManipulatorToolbar())})}else this.body.data.nodes.getDataSet().add(i),this.showManipulatorToolbar()}},{key:"_performAddEdge",value:function(e,t){var i=this,o={from:e,to:t};if("function"==typeof this.options.addEdge){if(2!==this.options.addEdge.length)throw new Error("The function for connect does not support two arguments (data,callback)");this.options.addEdge(o,function(e){null!==e&&void 0!==e&&"addEdge"===i.inMode&&(i.body.data.edges.getDataSet().add(e),i.selectionHandler.unselectAll(),i.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().add(o),this.selectionHandler.unselectAll(),this.showManipulatorToolbar()}},{key:"_performEditEdge",value:function(e,t){var i=this,o={id:this.edgeBeingEditedId,from:e,to:t};if("function"==typeof this.options.editEdge){if(2!==this.options.editEdge.length)throw new Error("The function for edit does not support two arguments (data, callback)");this.options.editEdge(o,function(e){null===e||void 0===e||"editEdge"!==i.inMode?(i.body.edges[o.id].updateEdgeType(),i.body.emitter.emit("_redraw")):(i.body.data.edges.getDataSet().update(e),i.selectionHandler.unselectAll(),i.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().update(o),this.selectionHandler.unselectAll(),this.showManipulatorToolbar()}}]),e}();t["default"]=h},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},r=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),a=i(71),h=o(a),d=i(1),l=function(){function e(t,i,o){var s=arguments.length<=3||void 0===arguments[3]?1:arguments[3];n(this,e),this.parent=t,this.changedOptions=[],this.container=i,this.allowCreation=!1,this.options={},this.initialized=!1,this.popupCounter=0,this.defaultOptions={enabled:!1,filter:!0,container:void 0,showButton:!0},d.extend(this.options,this.defaultOptions),this.configureOptions=o,this.moduleOptions={},this.domElements=[],this.popupDiv={},this.popupLimit=5,this.popupHistory={},this.colorPicker=new h["default"](s),this.wrapper=void 0}return r(e,[{key:"setOptions",value:function(e){if(void 0!==e){this.popupHistory={},this._removePopup();var t=!0;"string"==typeof e?this.options.filter=e:e instanceof Array?this.options.filter=e.join():"object"===("undefined"==typeof e?"undefined":s(e))?(void 0!==e.container&&(this.options.container=e.container),void 0!==e.filter&&(this.options.filter=e.filter),void 0!==e.showButton&&(this.options.showButton=e.showButton),void 0!==e.enabled&&(t=e.enabled)):"boolean"==typeof e?(this.options.filter=!0,t=e):"function"==typeof e&&(this.options.filter=e,t=!0),this.options.filter===!1&&(t=!1),this.options.enabled=t}this._clean()}},{key:"setModuleOptions",value:function(e){this.moduleOptions=e,this.options.enabled===!0&&(this._clean(),void 0!==this.options.container&&(this.container=this.options.container),this._create())}},{key:"_create",value:function(){var e=this;this._clean(),this.changedOptions=[];var t=this.options.filter,i=0,o=!1;for(var n in this.configureOptions)this.configureOptions.hasOwnProperty(n)&&(this.allowCreation=!1,o=!1,"function"==typeof t?(o=t(n,[]),o=o||this._handleObject(this.configureOptions[n],[n],!0)):t!==!0&&t.indexOf(n)===-1||(o=!0),o!==!1&&(this.allowCreation=!0,i>0&&this._makeItem([]),this._makeHeader(n),this._handleObject(this.configureOptions[n],[n])),i++);this.options.showButton===!0&&!function(){var t=document.createElement("div");t.className="vis-configuration vis-config-button",t.innerHTML="generate options",t.onclick=function(){e._printOptions()},t.onmouseover=function(){t.className="vis-configuration vis-config-button hover"},t.onmouseout=function(){t.className="vis-configuration vis-config-button"},e.optionsContainer=document.createElement("div"),e.optionsContainer.className="vis-configuration vis-config-option-container",e.domElements.push(e.optionsContainer),e.domElements.push(t)}(),this._push()}},{key:"_push",value:function(){this.wrapper=document.createElement("div"),this.wrapper.className="vis-configuration-wrapper",this.container.appendChild(this.wrapper);for(var e=0;e<this.domElements.length;e++)this.wrapper.appendChild(this.domElements[e]);this._showPopupIfNeeded()}},{key:"_clean",value:function(){for(var e=0;e<this.domElements.length;e++)this.wrapper.removeChild(this.domElements[e]);void 0!==this.wrapper&&(this.container.removeChild(this.wrapper),this.wrapper=void 0),this.domElements=[],this._removePopup()}},{key:"_getValue",value:function(e){for(var t=this.moduleOptions,i=0;i<e.length;i++){if(void 0===t[e[i]]){t=void 0;break}t=t[e[i]]}return t}},{key:"_makeItem",value:function(e){var t=arguments,i=this;if(this.allowCreation===!0){var o,n,r,a=function(){var s=document.createElement("div");for(s.className="vis-configuration vis-config-item vis-config-s"+e.length,o=t.length,n=Array(o>1?o-1:0),r=1;r<o;r++)n[r-1]=t[r];return n.forEach(function(e){s.appendChild(e)}),i.domElements.push(s),{v:i.domElements.length}}();if("object"===("undefined"==typeof a?"undefined":s(a)))return a.v}return 0}},{key:"_makeHeader",value:function(e){var t=document.createElement("div");t.className="vis-configuration vis-config-header",t.innerHTML=e,this._makeItem([],t)}},{key:"_makeLabel",value:function(e,t){var i=!(arguments.length<=2||void 0===arguments[2])&&arguments[2],o=document.createElement("div");return o.className="vis-configuration vis-config-label vis-config-s"+t.length,i===!0?o.innerHTML="<i><b>"+e+":</b></i>":o.innerHTML=e+":",o}},{key:"_makeDropdown",value:function(e,t,i){var o=document.createElement("select");o.className="vis-configuration vis-config-select";var n=0;void 0!==t&&e.indexOf(t)!==-1&&(n=e.indexOf(t));for(var s=0;s<e.length;s++){var r=document.createElement("option");r.value=e[s],s===n&&(r.selected="selected"),r.innerHTML=e[s],o.appendChild(r)}var a=this;o.onchange=function(){a._update(this.value,i)};var h=this._makeLabel(i[i.length-1],i);this._makeItem(i,h,o)}},{key:"_makeRange",value:function(e,t,i){var o=e[0],n=e[1],s=e[2],r=e[3],a=document.createElement("input");a.className="vis-configuration vis-config-range";try{a.type="range",a.min=n,a.max=s}catch(h){}a.step=r;var d="",l=0;if(void 0!==t){var c=1.2;t<0&&t*c<n?(a.min=Math.ceil(t*c),l=a.min,d="range increased"):t/c<n&&(a.min=Math.ceil(t/c),l=a.min,d="range increased"),t*c>s&&1!==s&&(a.max=Math.ceil(t*c),l=a.max,d="range increased"),a.value=t}else a.value=o;var u=document.createElement("input");u.className="vis-configuration vis-config-rangeinput",u.value=a.value;var f=this;a.onchange=function(){u.value=this.value,f._update(Number(this.value),i)},a.oninput=function(){u.value=this.value};var p=this._makeLabel(i[i.length-1],i),v=this._makeItem(i,p,a,u);""!==d&&this.popupHistory[v]!==l&&(this.popupHistory[v]=l,this._setupPopup(d,v))}},{key:"_setupPopup",value:function(e,t){var i=this;if(this.initialized===!0&&this.allowCreation===!0&&this.popupCounter<this.popupLimit){var o=document.createElement("div");o.id="vis-configuration-popup",o.className="vis-configuration-popup",o.innerHTML=e,o.onclick=function(){i._removePopup()},this.popupCounter+=1,this.popupDiv={html:o,index:t}}}},{key:"_removePopup",value:function(){void 0!==this.popupDiv.html&&(this.popupDiv.html.parentNode.removeChild(this.popupDiv.html),clearTimeout(this.popupDiv.hideTimeout),clearTimeout(this.popupDiv.deleteTimeout),this.popupDiv={})}},{key:"_showPopupIfNeeded",value:function(){var e=this;if(void 0!==this.popupDiv.html){var t=this.domElements[this.popupDiv.index],i=t.getBoundingClientRect();this.popupDiv.html.style.left=i.left+"px",this.popupDiv.html.style.top=i.top-30+"px",document.body.appendChild(this.popupDiv.html),this.popupDiv.hideTimeout=setTimeout(function(){e.popupDiv.html.style.opacity=0},1500),this.popupDiv.deleteTimeout=setTimeout(function(){e._removePopup()},1800)}}},{key:"_makeCheckbox",value:function(e,t,i){var o=document.createElement("input");o.type="checkbox",o.className="vis-configuration vis-config-checkbox",o.checked=e,void 0!==t&&(o.checked=t,t!==e&&("object"===("undefined"==typeof e?"undefined":s(e))?t!==e.enabled&&this.changedOptions.push({path:i,value:t}):this.changedOptions.push({path:i,value:t})));var n=this;o.onchange=function(){n._update(this.checked,i)};var r=this._makeLabel(i[i.length-1],i);this._makeItem(i,r,o)}},{key:"_makeTextInput",value:function(e,t,i){var o=document.createElement("input");o.type="text",o.className="vis-configuration vis-config-text",o.value=t,t!==e&&this.changedOptions.push({path:i,value:t});var n=this;o.onchange=function(){n._update(this.value,i)};var s=this._makeLabel(i[i.length-1],i);this._makeItem(i,s,o)}},{key:"_makeColorField",value:function(e,t,i){var o=this,n=e[1],s=document.createElement("div");t=void 0===t?n:t,"none"!==t?(s.className="vis-configuration vis-config-colorBlock",s.style.backgroundColor=t):s.className="vis-configuration vis-config-colorBlock none",t=void 0===t?n:t,s.onclick=function(){o._showColorPicker(t,s,i)};var r=this._makeLabel(i[i.length-1],i);this._makeItem(i,r,s)}},{key:"_showColorPicker",value:function(e,t,i){var o=this;t.onclick=function(){},this.colorPicker.insertTo(t),this.colorPicker.show(),this.colorPicker.setColor(e),this.colorPicker.setUpdateCallback(function(e){var n="rgba("+e.r+","+e.g+","+e.b+","+e.a+")";t.style.backgroundColor=n,o._update(n,i)}),this.colorPicker.setCloseCallback(function(){t.onclick=function(){o._showColorPicker(e,t,i)}})}},{key:"_handleObject",value:function(e){var t=arguments.length<=1||void 0===arguments[1]?[]:arguments[1],i=!(arguments.length<=2||void 0===arguments[2])&&arguments[2],o=!1,n=this.options.filter,s=!1;for(var r in e)if(e.hasOwnProperty(r)){o=!0;var a=e[r],h=d.copyAndExtendArray(t,r);if("function"==typeof n&&(o=n(r,t),o===!1&&!(a instanceof Array)&&"string"!=typeof a&&"boolean"!=typeof a&&a instanceof Object&&(this.allowCreation=!1,o=this._handleObject(a,h,!0),this.allowCreation=i===!1)),o!==!1){s=!0;var l=this._getValue(h);if(a instanceof Array)this._handleArray(a,l,h);else if("string"==typeof a)this._makeTextInput(a,l,h);else if("boolean"==typeof a)this._makeCheckbox(a,l,h);else if(a instanceof Object){var c=!0;if(t.indexOf("physics")!==-1&&this.moduleOptions.physics.solver!==r&&(c=!1),c===!0)if(void 0!==a.enabled){var u=d.copyAndExtendArray(h,"enabled"),f=this._getValue(u);if(f===!0){var p=this._makeLabel(r,h,!0);this._makeItem(h,p),s=this._handleObject(a,h)||s}else this._makeCheckbox(a,f,h)}else{var v=this._makeLabel(r,h,!0);this._makeItem(h,v),s=this._handleObject(a,h)||s}}else console.error("dont know how to handle",a,r,h)}}return s}},{key:"_handleArray",value:function(e,t,i){"string"==typeof e[0]&&"color"===e[0]?(this._makeColorField(e,t,i),e[1]!==t&&this.changedOptions.push({path:i,value:t})):"string"==typeof e[0]?(this._makeDropdown(e,t,i),e[0]!==t&&this.changedOptions.push({path:i,value:t})):"number"==typeof e[0]&&(this._makeRange(e,t,i),e[0]!==t&&this.changedOptions.push({path:i,value:Number(t)}))}},{key:"_update",value:function(e,t){var i=this._constructOptions(e,t);this.parent.body&&this.parent.body.emitter&&this.parent.body.emitter.emit&&this.parent.body.emitter.emit("configChange",i),this.initialized=!0,this.parent.setOptions(i)}},{key:"_constructOptions",value:function(e,t){var i=arguments.length<=2||void 0===arguments[2]?{}:arguments[2],o=i;e="true"===e||e,e="false"!==e&&e;for(var n=0;n<t.length;n++)"global"!==t[n]&&(void 0===o[t[n]]&&(o[t[n]]={}),n!==t.length-1?o=o[t[n]]:o[t[n]]=e);return i}},{key:"_printOptions",value:function(){var e=this.getOptions();this.optionsContainer.innerHTML="<pre>var options = "+JSON.stringify(e,null,2)+"</pre>"}},{key:"getOptions",value:function(){for(var e={},t=0;t<this.changedOptions.length;t++)this._constructOptions(this.changedOptions[t].value,this.changedOptions[t].path,e);return e}}]),e}();t["default"]=l},function(e,t,i){function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),s=i(58),r=i(61),a=i(1),h=function(){function e(){var t=arguments.length<=0||void 0===arguments[0]?1:arguments[0];o(this,e),this.pixelRatio=t,this.generated=!1,this.centerCoordinates={x:144.5,y:144.5},this.r=289*.49,this.color={r:255,g:255,b:255,a:1},this.hueCircle=void 0,this.initialColor={r:255,g:255,b:255,a:1},this.previousColor=void 0,this.applied=!1,this.updateCallback=function(){},this.closeCallback=function(){},this._create()}return n(e,[{key:"insertTo",value:function(e){void 0!==this.hammer&&(this.hammer.destroy(),this.hammer=void 0),this.container=e,this.container.appendChild(this.frame),this._bindHammer(),this._setSize()}},{key:"setUpdateCallback",value:function(e){if("function"!=typeof e)throw new Error("Function attempted to set as colorPicker update callback is not a function.");this.updateCallback=e}},{key:"setCloseCallback",value:function(e){if("function"!=typeof e)throw new Error("Function attempted to set as colorPicker closing callback is not a function.");this.closeCallback=e}},{key:"_isColorString",value:function(e){var t={black:"#000000",navy:"#000080",darkblue:"#00008B",mediumblue:"#0000CD",blue:"#0000FF",darkgreen:"#006400",green:"#008000",teal:"#008080",darkcyan:"#008B8B",deepskyblue:"#00BFFF",darkturquoise:"#00CED1",mediumspringgreen:"#00FA9A",lime:"#00FF00",springgreen:"#00FF7F",aqua:"#00FFFF",cyan:"#00FFFF",midnightblue:"#191970",dodgerblue:"#1E90FF",lightseagreen:"#20B2AA",forestgreen:"#228B22",seagreen:"#2E8B57",darkslategray:"#2F4F4F",limegreen:"#32CD32",mediumseagreen:"#3CB371",turquoise:"#40E0D0",royalblue:"#4169E1",steelblue:"#4682B4",darkslateblue:"#483D8B",mediumturquoise:"#48D1CC",indigo:"#4B0082",darkolivegreen:"#556B2F",cadetblue:"#5F9EA0",cornflowerblue:"#6495ED",mediumaquamarine:"#66CDAA",dimgray:"#696969",slateblue:"#6A5ACD",olivedrab:"#6B8E23",slategray:"#708090",lightslategray:"#778899",mediumslateblue:"#7B68EE",lawngreen:"#7CFC00",chartreuse:"#7FFF00",aquamarine:"#7FFFD4",maroon:"#800000",purple:"#800080",olive:"#808000",gray:"#808080",skyblue:"#87CEEB",lightskyblue:"#87CEFA",blueviolet:"#8A2BE2",darkred:"#8B0000",darkmagenta:"#8B008B",saddlebrown:"#8B4513",darkseagreen:"#8FBC8F",lightgreen:"#90EE90",mediumpurple:"#9370D8",darkviolet:"#9400D3",palegreen:"#98FB98",darkorchid:"#9932CC",yellowgreen:"#9ACD32",sienna:"#A0522D",brown:"#A52A2A",darkgray:"#A9A9A9",lightblue:"#ADD8E6",greenyellow:"#ADFF2F",paleturquoise:"#AFEEEE",lightsteelblue:"#B0C4DE",powderblue:"#B0E0E6",firebrick:"#B22222",darkgoldenrod:"#B8860B",mediumorchid:"#BA55D3",rosybrown:"#BC8F8F",darkkhaki:"#BDB76B",silver:"#C0C0C0",mediumvioletred:"#C71585",indianred:"#CD5C5C",peru:"#CD853F",chocolate:"#D2691E",tan:"#D2B48C",lightgrey:"#D3D3D3",palevioletred:"#D87093",thistle:"#D8BFD8",orchid:"#DA70D6",goldenrod:"#DAA520",crimson:"#DC143C",gainsboro:"#DCDCDC",plum:"#DDA0DD",burlywood:"#DEB887",lightcyan:"#E0FFFF",lavender:"#E6E6FA",darksalmon:"#E9967A",violet:"#EE82EE",palegoldenrod:"#EEE8AA",lightcoral:"#F08080",khaki:"#F0E68C",aliceblue:"#F0F8FF",honeydew:"#F0FFF0",azure:"#F0FFFF",sandybrown:"#F4A460",wheat:"#F5DEB3",beige:"#F5F5DC",whitesmoke:"#F5F5F5",mintcream:"#F5FFFA",ghostwhite:"#F8F8FF",salmon:"#FA8072",antiquewhite:"#FAEBD7",linen:"#FAF0E6",lightgoldenrodyellow:"#FAFAD2",oldlace:"#FDF5E6",red:"#FF0000",fuchsia:"#FF00FF",magenta:"#FF00FF",deeppink:"#FF1493",orangered:"#FF4500",tomato:"#FF6347",hotpink:"#FF69B4",coral:"#FF7F50",darkorange:"#FF8C00",lightsalmon:"#FFA07A",orange:"#FFA500",lightpink:"#FFB6C1",pink:"#FFC0CB",gold:"#FFD700",peachpuff:"#FFDAB9",navajowhite:"#FFDEAD",moccasin:"#FFE4B5",bisque:"#FFE4C4",mistyrose:"#FFE4E1",blanchedalmond:"#FFEBCD",papayawhip:"#FFEFD5",lavenderblush:"#FFF0F5",seashell:"#FFF5EE",cornsilk:"#FFF8DC",lemonchiffon:"#FFFACD",floralwhite:"#FFFAF0",snow:"#FFFAFA",yellow:"#FFFF00",lightyellow:"#FFFFE0",ivory:"#FFFFF0",white:"#FFFFFF"};if("string"==typeof e)return t[e]}},{key:"setColor",value:function(e){var t=arguments.length<=1||void 0===arguments[1]||arguments[1];if("none"!==e){var i=void 0,o=this._isColorString(e);if(void 0!==o&&(e=o),a.isString(e)===!0){if(a.isValidRGB(e)===!0){var n=e.substr(4).substr(0,e.length-5).split(",");i={r:n[0],g:n[1],b:n[2],a:1}}else if(a.isValidRGBA(e)===!0){var s=e.substr(5).substr(0,e.length-6).split(",");i={r:s[0],g:s[1],b:s[2],a:s[3]}}else if(a.isValidHex(e)===!0){var r=a.hexToRGB(e);i={r:r.r,g:r.g,b:r.b,a:1}}}else if(e instanceof Object&&void 0!==e.r&&void 0!==e.g&&void 0!==e.b){var h=void 0!==e.a?e.a:"1.0";i={r:e.r,g:e.g,b:e.b,a:h}}if(void 0===i)throw new Error("Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: "+JSON.stringify(e));this._setColor(i,t)}}},{key:"show",value:function(){void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0),this.applied=!1,this.frame.style.display="block",this._generateHueCircle()}},{key:"_hide",value:function(){var e=this,t=arguments.length<=0||void 0===arguments[0]||arguments[0];t===!0&&(this.previousColor=a.extend({},this.color)),this.applied===!0&&this.updateCallback(this.initialColor),this.frame.style.display="none",setTimeout(function(){void 0!==e.closeCallback&&(e.closeCallback(),e.closeCallback=void 0)},0)}},{key:"_save",value:function(){this.updateCallback(this.color),this.applied=!1,this._hide()}},{key:"_apply",value:function(){this.applied=!0,this.updateCallback(this.color),this._updatePicker(this.color)}},{key:"_loadLast",value:function(){void 0!==this.previousColor?this.setColor(this.previousColor,!1):alert("There is no last color to load...")}},{key:"_setColor",value:function(e){var t=arguments.length<=1||void 0===arguments[1]||arguments[1];t===!0&&(this.initialColor=a.extend({},e)),this.color=e;var i=a.RGBToHSV(e.r,e.g,e.b),o=2*Math.PI,n=this.r*i.s,s=this.centerCoordinates.x+n*Math.sin(o*i.h),r=this.centerCoordinates.y+n*Math.cos(o*i.h);this.colorPickerSelector.style.left=s-.5*this.colorPickerSelector.clientWidth+"px",this.colorPickerSelector.style.top=r-.5*this.colorPickerSelector.clientHeight+"px",this._updatePicker(e)}},{key:"_setOpacity",value:function(e){this.color.a=e/100,this._updatePicker(this.color)}},{key:"_setBrightness",value:function(e){var t=a.RGBToHSV(this.color.r,this.color.g,this.color.b);t.v=e/100;var i=a.HSVToRGB(t.h,t.s,t.v);i.a=this.color.a,this.color=i,this._updatePicker()}},{key:"_updatePicker",value:function(){var e=arguments.length<=0||void 0===arguments[0]?this.color:arguments[0],t=a.RGBToHSV(e.r,e.g,e.b),i=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(i.webkitBackingStorePixelRatio||i.mozBackingStorePixelRatio||i.msBackingStorePixelRatio||i.oBackingStorePixelRatio||i.backingStorePixelRatio||1)),i.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);var o=this.colorPickerCanvas.clientWidth,n=this.colorPickerCanvas.clientHeight;i.clearRect(0,0,o,n),i.putImageData(this.hueCircle,0,0),i.fillStyle="rgba(0,0,0,"+(1-t.v)+")",i.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),i.fill(),this.brightnessRange.value=100*t.v,this.opacityRange.value=100*e.a,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}},{key:"_setSize",value:function(){this.colorPickerCanvas.style.width="100%",this.colorPickerCanvas.style.height="100%",this.colorPickerCanvas.width=289*this.pixelRatio,this.colorPickerCanvas.height=289*this.pixelRatio}},{key:"_create",value:function(){if(this.frame=document.createElement("div"),this.frame.className="vis-color-picker",this.colorPickerDiv=document.createElement("div"),this.colorPickerSelector=document.createElement("div"),this.colorPickerSelector.className="vis-selector",this.colorPickerDiv.appendChild(this.colorPickerSelector),this.colorPickerCanvas=document.createElement("canvas"),this.colorPickerDiv.appendChild(this.colorPickerCanvas),this.colorPickerCanvas.getContext){var e=this.colorPickerCanvas.getContext("2d");this.pixelRatio=(window.devicePixelRatio||1)/(e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1),this.colorPickerCanvas.getContext("2d").setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}else{var t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerHTML="Error: your browser does not support HTML canvas",this.colorPickerCanvas.appendChild(t)}this.colorPickerDiv.className="vis-color",this.opacityDiv=document.createElement("div"),this.opacityDiv.className="vis-opacity",this.brightnessDiv=document.createElement("div"),this.brightnessDiv.className="vis-brightness",this.arrowDiv=document.createElement("div"),this.arrowDiv.className="vis-arrow",this.opacityRange=document.createElement("input");try{this.opacityRange.type="range",this.opacityRange.min="0",this.opacityRange.max="100"}catch(i){}this.opacityRange.value="100",this.opacityRange.className="vis-range",this.brightnessRange=document.createElement("input");try{this.brightnessRange.type="range",this.brightnessRange.min="0",this.brightnessRange.max="100"}catch(i){}this.brightnessRange.value="100",this.brightnessRange.className="vis-range",this.opacityDiv.appendChild(this.opacityRange),this.brightnessDiv.appendChild(this.brightnessRange);var o=this;this.opacityRange.onchange=function(){o._setOpacity(this.value)},this.opacityRange.oninput=function(){o._setOpacity(this.value)},this.brightnessRange.onchange=function(){o._setBrightness(this.value)},this.brightnessRange.oninput=function(){o._setBrightness(this.value)},this.brightnessLabel=document.createElement("div"),this.brightnessLabel.className="vis-label vis-brightness",this.brightnessLabel.innerHTML="brightness:",this.opacityLabel=document.createElement("div"),this.opacityLabel.className="vis-label vis-opacity",this.opacityLabel.innerHTML="opacity:",this.newColorDiv=document.createElement("div"),this.newColorDiv.className="vis-new-color",this.newColorDiv.innerHTML="new",this.initialColorDiv=document.createElement("div"),this.initialColorDiv.className="vis-initial-color",this.initialColorDiv.innerHTML="initial",this.cancelButton=document.createElement("div"),this.cancelButton.className="vis-button vis-cancel",this.cancelButton.innerHTML="cancel",this.cancelButton.onclick=this._hide.bind(this,!1),this.applyButton=document.createElement("div"),this.applyButton.className="vis-button vis-apply",this.applyButton.innerHTML="apply",this.applyButton.onclick=this._apply.bind(this),this.saveButton=document.createElement("div"),this.saveButton.className="vis-button vis-save",this.saveButton.innerHTML="save",this.saveButton.onclick=this._save.bind(this),this.loadButton=document.createElement("div"),this.loadButton.className="vis-button vis-load",this.loadButton.innerHTML="load last",this.loadButton.onclick=this._loadLast.bind(this),this.frame.appendChild(this.colorPickerDiv),this.frame.appendChild(this.arrowDiv),this.frame.appendChild(this.brightnessLabel),this.frame.appendChild(this.brightnessDiv),this.frame.appendChild(this.opacityLabel),this.frame.appendChild(this.opacityDiv),this.frame.appendChild(this.newColorDiv),this.frame.appendChild(this.initialColorDiv),this.frame.appendChild(this.cancelButton),this.frame.appendChild(this.applyButton),this.frame.appendChild(this.saveButton),this.frame.appendChild(this.loadButton)}},{key:"_bindHammer",value:function(){var e=this;this.drag={},this.pinch={},this.hammer=new s(this.colorPickerCanvas),this.hammer.get("pinch").set({enable:!0}),r.onTouch(this.hammer,function(t){e._moveSelector(t)}),this.hammer.on("tap",function(t){e._moveSelector(t)}),this.hammer.on("panstart",function(t){e._moveSelector(t)}),this.hammer.on("panmove",function(t){e._moveSelector(t)}),this.hammer.on("panend",function(t){e._moveSelector(t)})}},{key:"_generateHueCircle",value:function(){if(this.generated===!1){var e=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1)),e.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);var t=this.colorPickerCanvas.clientWidth,i=this.colorPickerCanvas.clientHeight;e.clearRect(0,0,t,i);var o=void 0,n=void 0,s=void 0,r=void 0;this.centerCoordinates={x:.5*t,y:.5*i},this.r=.49*t;var h=2*Math.PI/360,d=1/360,l=1/this.r,c=void 0;for(s=0;s<360;s++)for(r=0;r<this.r;r++)o=this.centerCoordinates.x+r*Math.sin(h*s),n=this.centerCoordinates.y+r*Math.cos(h*s),c=a.HSVToRGB(s*d,r*l,1),e.fillStyle="rgb("+c.r+","+c.g+","+c.b+")",e.fillRect(o-.5,n-.5,2,2);e.strokeStyle="rgba(0,0,0,1)",e.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),e.stroke(),this.hueCircle=e.getImageData(0,0,t,i)}this.generated=!0}},{key:"_moveSelector",value:function(e){var t=this.colorPickerDiv.getBoundingClientRect(),i=e.center.x-t.left,o=e.center.y-t.top,n=.5*this.colorPickerDiv.clientHeight,s=.5*this.colorPickerDiv.clientWidth,r=i-s,h=o-n,d=Math.atan2(r,h),l=.98*Math.min(Math.sqrt(r*r+h*h),s),c=Math.cos(d)*l+n,u=Math.sin(d)*l+s;this.colorPickerSelector.style.top=c-.5*this.colorPickerSelector.clientHeight+"px",this.colorPickerSelector.style.left=u-.5*this.colorPickerSelector.clientWidth+"px";var f=d/(2*Math.PI);f=f<0?f+1:f;var p=l/this.r,v=a.RGBToHSV(this.color.r,this.color.g,this.color.b);v.h=f,v.s=p;var y=a.HSVToRGB(v.h,v.s,v.v);y.a=this.color.a,this.color=y,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}}]),e}();t["default"]=h},function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var i="string",o="boolean",n="number",s="array",r="object",a="dom",h="any",d={configure:{enabled:{"boolean":o},filter:{"boolean":o,string:i,array:s,"function":"function"},container:{dom:a},showButton:{"boolean":o},__type__:{object:r,"boolean":o,string:i,array:s,"function":"function"}},edges:{arrows:{to:{enabled:{"boolean":o},scaleFactor:{number:n},__type__:{object:r,"boolean":o}},middle:{enabled:{"boolean":o},scaleFactor:{number:n},__type__:{object:r,"boolean":o}},from:{enabled:{"boolean":o},scaleFactor:{number:n},__type__:{object:r,"boolean":o}},__type__:{string:["from","to","middle"],object:r}},arrowStrikethrough:{"boolean":o},color:{color:{string:i},highlight:{string:i},hover:{string:i},inherit:{string:["from","to","both"],"boolean":o},opacity:{number:n},__type__:{object:r,string:i}},dashes:{"boolean":o,array:s},font:{color:{string:i},size:{number:n},face:{string:i},background:{string:i},strokeWidth:{number:n},strokeColor:{string:i},align:{string:["horizontal","top","middle","bottom"]},__type__:{object:r,string:i}},hidden:{"boolean":o},hoverWidth:{"function":"function",number:n},label:{string:i,undefined:"undefined"},labelHighlightBold:{"boolean":o},length:{number:n,undefined:"undefined"},physics:{"boolean":o},scaling:{min:{number:n},max:{number:n},label:{enabled:{"boolean":o},min:{number:n},max:{number:n},maxVisible:{number:n},drawThreshold:{number:n},__type__:{object:r,"boolean":o}},customScalingFunction:{"function":"function"},__type__:{object:r}},selectionWidth:{"function":"function",number:n},selfReferenceSize:{number:n},shadow:{enabled:{"boolean":o},color:{string:i},size:{number:n},x:{number:n},y:{number:n},__type__:{object:r,"boolean":o}},smooth:{enabled:{"boolean":o},type:{string:["dynamic","continuous","discrete","diagonalCross","straightCross","horizontal","vertical","curvedCW","curvedCCW","cubicBezier"]},roundness:{number:n},forceDirection:{string:["horizontal","vertical","none"],"boolean":o},__type__:{object:r,"boolean":o}},title:{string:i,undefined:"undefined"},width:{number:n},value:{number:n,undefined:"undefined"},__type__:{object:r}},groups:{useDefaultGroups:{"boolean":o},__any__:"get from nodes, will be overwritten below",__type__:{object:r}},interaction:{dragNodes:{"boolean":o},dragView:{"boolean":o},hideEdgesOnDrag:{"boolean":o},hideNodesOnDrag:{"boolean":o},hover:{"boolean":o},keyboard:{enabled:{"boolean":o},speed:{x:{number:n},y:{number:n},zoom:{number:n},__type__:{object:r}},bindToWindow:{"boolean":o},__type__:{object:r,"boolean":o}},multiselect:{"boolean":o},navigationButtons:{"boolean":o},selectable:{"boolean":o},selectConnectedEdges:{"boolean":o},hoverConnectedEdges:{"boolean":o},tooltipDelay:{number:n},zoomView:{"boolean":o},__type__:{object:r}},layout:{randomSeed:{undefined:"undefined",number:n},improvedLayout:{"boolean":o},hierarchical:{enabled:{"boolean":o},levelSeparation:{number:n},nodeSpacing:{number:n},treeSpacing:{number:n},blockShifting:{"boolean":o},edgeMinimization:{"boolean":o},parentCentralization:{"boolean":o},direction:{string:["UD","DU","LR","RL"]},sortMethod:{string:["hubsize","directed"]},__type__:{object:r,"boolean":o}},__type__:{object:r}},manipulation:{enabled:{"boolean":o},initiallyActive:{
"boolean":o},addNode:{"boolean":o,"function":"function"},addEdge:{"boolean":o,"function":"function"},editNode:{"function":"function"},editEdge:{"boolean":o,"function":"function"},deleteNode:{"boolean":o,"function":"function"},deleteEdge:{"boolean":o,"function":"function"},controlNodeStyle:"get from nodes, will be overwritten below",__type__:{object:r,"boolean":o}},nodes:{borderWidth:{number:n},borderWidthSelected:{number:n,undefined:"undefined"},brokenImage:{string:i,undefined:"undefined"},color:{border:{string:i},background:{string:i},highlight:{border:{string:i},background:{string:i},__type__:{object:r,string:i}},hover:{border:{string:i},background:{string:i},__type__:{object:r,string:i}},__type__:{object:r,string:i}},fixed:{x:{"boolean":o},y:{"boolean":o},__type__:{object:r,"boolean":o}},font:{align:{string:i},color:{string:i},size:{number:n},face:{string:i},background:{string:i},strokeWidth:{number:n},strokeColor:{string:i},__type__:{object:r,string:i}},group:{string:i,number:n,undefined:"undefined"},hidden:{"boolean":o},icon:{face:{string:i},code:{string:i},size:{number:n},color:{string:i},__type__:{object:r}},id:{string:i,number:n},image:{string:i,undefined:"undefined"},label:{string:i,undefined:"undefined"},labelHighlightBold:{"boolean":o},level:{number:n,undefined:"undefined"},mass:{number:n},physics:{"boolean":o},scaling:{min:{number:n},max:{number:n},label:{enabled:{"boolean":o},min:{number:n},max:{number:n},maxVisible:{number:n},drawThreshold:{number:n},__type__:{object:r,"boolean":o}},customScalingFunction:{"function":"function"},__type__:{object:r}},shadow:{enabled:{"boolean":o},color:{string:i},size:{number:n},x:{number:n},y:{number:n},__type__:{object:r,"boolean":o}},shape:{string:["ellipse","circle","database","box","text","image","circularImage","diamond","dot","star","triangle","triangleDown","square","icon"]},shapeProperties:{borderDashes:{"boolean":o,array:s},borderRadius:{number:n},interpolation:{"boolean":o},useImageSize:{"boolean":o},useBorderWithImage:{"boolean":o},__type__:{object:r}},size:{number:n},title:{string:i,undefined:"undefined"},value:{number:n,undefined:"undefined"},x:{number:n},y:{number:n},__type__:{object:r}},physics:{enabled:{"boolean":o},barnesHut:{gravitationalConstant:{number:n},centralGravity:{number:n},springLength:{number:n},springConstant:{number:n},damping:{number:n},avoidOverlap:{number:n},__type__:{object:r}},forceAtlas2Based:{gravitationalConstant:{number:n},centralGravity:{number:n},springLength:{number:n},springConstant:{number:n},damping:{number:n},avoidOverlap:{number:n},__type__:{object:r}},repulsion:{centralGravity:{number:n},springLength:{number:n},springConstant:{number:n},nodeDistance:{number:n},damping:{number:n},__type__:{object:r}},hierarchicalRepulsion:{centralGravity:{number:n},springLength:{number:n},springConstant:{number:n},nodeDistance:{number:n},damping:{number:n},__type__:{object:r}},maxVelocity:{number:n},minVelocity:{number:n},solver:{string:["barnesHut","repulsion","hierarchicalRepulsion","forceAtlas2Based"]},stabilization:{enabled:{"boolean":o},iterations:{number:n},updateInterval:{number:n},onlyDynamicEdges:{"boolean":o},fit:{"boolean":o},__type__:{object:r,"boolean":o}},timestep:{number:n},adaptiveTimestep:{"boolean":o},__type__:{object:r,"boolean":o}},autoResize:{"boolean":o},clickToUse:{"boolean":o},locale:{string:i},locales:{__any__:{any:h},__type__:{object:r}},height:{string:i},width:{string:i},__type__:{object:r}};d.groups.__any__=d.nodes,d.manipulation.controlNodeStyle=d.nodes;var l={nodes:{borderWidth:[1,0,10,1],borderWidthSelected:[2,0,10,1],color:{border:["color","#2B7CE9"],background:["color","#97C2FC"],highlight:{border:["color","#2B7CE9"],background:["color","#D2E5FF"]},hover:{border:["color","#2B7CE9"],background:["color","#D2E5FF"]}},fixed:{x:!1,y:!1},font:{color:["color","#343434"],size:[14,0,100,1],face:["arial","verdana","tahoma"],background:["color","none"],strokeWidth:[0,0,50,1],strokeColor:["color","#ffffff"]},hidden:!1,labelHighlightBold:!0,physics:!0,scaling:{min:[10,0,200,1],max:[30,0,200,1],label:{enabled:!1,min:[14,0,200,1],max:[30,0,200,1],maxVisible:[30,0,200,1],drawThreshold:[5,0,20,1]}},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:[10,0,20,1],x:[5,-30,30,1],y:[5,-30,30,1]},shape:["ellipse","box","circle","database","diamond","dot","square","star","text","triangle","triangleDown"],shapeProperties:{borderDashes:!1,borderRadius:[6,0,20,1],interpolation:!0,useImageSize:!1},size:[25,0,200,1]},edges:{arrows:{to:{enabled:!1,scaleFactor:[1,0,3,.05]},middle:{enabled:!1,scaleFactor:[1,0,3,.05]},from:{enabled:!1,scaleFactor:[1,0,3,.05]}},arrowStrikethrough:!0,color:{color:["color","#848484"],highlight:["color","#848484"],hover:["color","#848484"],inherit:["from","to","both",!0,!1],opacity:[1,0,1,.05]},dashes:!1,font:{color:["color","#343434"],size:[14,0,100,1],face:["arial","verdana","tahoma"],background:["color","none"],strokeWidth:[2,0,50,1],strokeColor:["color","#ffffff"],align:["horizontal","top","middle","bottom"]},hidden:!1,hoverWidth:[1.5,0,5,.1],labelHighlightBold:!0,physics:!0,scaling:{min:[1,0,100,1],max:[15,0,100,1],label:{enabled:!0,min:[14,0,200,1],max:[30,0,200,1],maxVisible:[30,0,200,1],drawThreshold:[5,0,20,1]}},selectionWidth:[1.5,0,5,.1],selfReferenceSize:[20,0,200,1],shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:[10,0,20,1],x:[5,-30,30,1],y:[5,-30,30,1]},smooth:{enabled:!0,type:["dynamic","continuous","discrete","diagonalCross","straightCross","horizontal","vertical","curvedCW","curvedCCW","cubicBezier"],forceDirection:["horizontal","vertical","none"],roundness:[.5,0,1,.05]},width:[1,0,30,1]},layout:{hierarchical:{enabled:!1,levelSeparation:[150,20,500,5],nodeSpacing:[100,20,500,5],treeSpacing:[200,20,500,5],blockShifting:!0,edgeMinimization:!0,parentCentralization:!0,direction:["UD","DU","LR","RL"],sortMethod:["hubsize","directed"]}},interaction:{dragNodes:!0,dragView:!0,hideEdgesOnDrag:!1,hideNodesOnDrag:!1,hover:!1,keyboard:{enabled:!1,speed:{x:[10,0,40,1],y:[10,0,40,1],zoom:[.02,0,.1,.005]},bindToWindow:!0},multiselect:!1,navigationButtons:!1,selectable:!0,selectConnectedEdges:!0,hoverConnectedEdges:!0,tooltipDelay:[300,0,1e3,25],zoomView:!0},manipulation:{enabled:!1,initiallyActive:!1},physics:{enabled:!0,barnesHut:{gravitationalConstant:[-2e3,-3e4,0,50],centralGravity:[.3,0,10,.05],springLength:[95,0,500,5],springConstant:[.04,0,1.2,.005],damping:[.09,0,1,.01],avoidOverlap:[0,0,1,.01]},forceAtlas2Based:{gravitationalConstant:[-50,-500,0,1],centralGravity:[.01,0,1,.005],springLength:[95,0,500,5],springConstant:[.08,0,1.2,.005],damping:[.4,0,1,.01],avoidOverlap:[0,0,1,.01]},repulsion:{centralGravity:[.2,0,10,.05],springLength:[200,0,500,5],springConstant:[.05,0,1.2,.005],nodeDistance:[100,0,500,5],damping:[.09,0,1,.01]},hierarchicalRepulsion:{centralGravity:[.2,0,10,.05],springLength:[100,0,500,5],springConstant:[.01,0,1.2,.005],nodeDistance:[120,0,500,5],damping:[.09,0,1,.01]},maxVelocity:[50,0,150,1],minVelocity:[.1,.01,.5,.01],solver:["barnesHut","forceAtlas2Based","repulsion","hierarchicalRepulsion"],timestep:[.5,.01,1,.01]},global:{locale:["en","nl"]}};t.allOptions=d,t.configureOptions=l},function(e,t,i){function o(e){return e&&e.__esModule?e:{"default":e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var s=function(){function e(e,t){var i=[],o=!0,n=!1,s=void 0;try{for(var r,a=e[Symbol.iterator]();!(o=(r=a.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(h){n=!0,s=h}finally{try{!o&&a["return"]&&a["return"]()}finally{if(n)throw s}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),r=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),a=i(74),h=o(a),d=function(){function e(t,i,o){n(this,e),this.body=t,this.springLength=i,this.springConstant=o,this.distanceSolver=new h["default"]}return r(e,[{key:"setOptions",value:function(e){e&&(e.springLength&&(this.springLength=e.springLength),e.springConstant&&(this.springConstant=e.springConstant))}},{key:"solve",value:function(e,t){var i=!(arguments.length<=2||void 0===arguments[2])&&arguments[2],o=this.distanceSolver.getDistances(this.body,e,t);this._createL_matrix(o),this._createK_matrix(o);for(var n=.01,r=1,a=0,h=Math.max(1e3,Math.min(10*this.body.nodeIndices.length,6e3)),d=5,l=1e9,c=0,u=0,f=0,p=0,v=0;l>n&&a<h;){a+=1;var y=this._getHighestEnergyNode(i),g=s(y,4);for(c=g[0],l=g[1],u=g[2],f=g[3],p=l,v=0;p>r&&v<d;){v+=1,this._moveNode(c,u,f);var b=this._getEnergy(c),m=s(b,3);p=m[0],u=m[1],f=m[2]}}}},{key:"_getHighestEnergyNode",value:function(e){for(var t=this.body.nodeIndices,i=this.body.nodes,o=0,n=t[0],r=0,a=0,h=0;h<t.length;h++){var d=t[h];if(i[d].predefinedPosition===!1||i[d].isCluster===!0&&e===!0||i[d].options.fixed.x===!0||i[d].options.fixed.y===!0){var l=this._getEnergy(d),c=s(l,3),u=c[0],f=c[1],p=c[2];o<u&&(o=u,n=d,r=f,a=p)}}return[n,o,r,a]}},{key:"_getEnergy",value:function(e){for(var t=this.body.nodeIndices,i=this.body.nodes,o=i[e].x,n=i[e].y,s=0,r=0,a=0;a<t.length;a++){var h=t[a];if(h!==e){var d=i[h].x,l=i[h].y,c=1/Math.sqrt(Math.pow(o-d,2)+Math.pow(n-l,2));s+=this.K_matrix[e][h]*(o-d-this.L_matrix[e][h]*(o-d)*c),r+=this.K_matrix[e][h]*(n-l-this.L_matrix[e][h]*(n-l)*c)}}var u=Math.sqrt(Math.pow(s,2)+Math.pow(r,2));return[u,s,r]}},{key:"_moveNode",value:function(e,t,i){for(var o=this.body.nodeIndices,n=this.body.nodes,s=0,r=0,a=0,h=n[e].x,d=n[e].y,l=0;l<o.length;l++){var c=o[l];if(c!==e){var u=n[c].x,f=n[c].y,p=1/Math.pow(Math.pow(h-u,2)+Math.pow(d-f,2),1.5);s+=this.K_matrix[e][c]*(1-this.L_matrix[e][c]*Math.pow(d-f,2)*p),r+=this.K_matrix[e][c]*(this.L_matrix[e][c]*(h-u)*(d-f)*p),a+=this.K_matrix[e][c]*(1-this.L_matrix[e][c]*Math.pow(h-u,2)*p)}}var v=s,y=r,g=t,b=a,m=i,_=(g/v+m/y)/(y/v-b/y),w=-(y*_+g)/v;n[e].x+=w,n[e].y+=_}},{key:"_createL_matrix",value:function(e){var t=this.body.nodeIndices,i=this.springLength;this.L_matrix=[];for(var o=0;o<t.length;o++){this.L_matrix[t[o]]={};for(var n=0;n<t.length;n++)this.L_matrix[t[o]][t[n]]=i*e[t[o]][t[n]]}}},{key:"_createK_matrix",value:function(e){var t=this.body.nodeIndices,i=this.springConstant;this.K_matrix=[];for(var o=0;o<t.length;o++){this.K_matrix[t[o]]={};for(var n=0;n<t.length;n++)this.K_matrix[t[o]][t[n]]=i*Math.pow(e[t[o]][t[n]],-2)}}}]),e}();t["default"]=d},function(e,t){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}(),n=function(){function e(){i(this,e)}return o(e,[{key:"getDistances",value:function(e,t,i){for(var o={},n=e.edges,s=0;s<t.length;s++){o[t[s]]={},o[t[s]]={};for(var r=0;r<t.length;r++)o[t[s]][t[r]]=s==r?0:1e9,o[t[s]][t[r]]=s==r?0:1e9}for(var a=0;a<i.length;a++){var h=n[i[a]];h.connected===!0&&void 0!==o[h.fromId]&&void 0!==o[h.toId]&&(o[h.fromId][h.toId]=1,o[h.toId][h.fromId]=1)}for(var d=t.length,l=0;l<d;l++)for(var c=0;c<d-1;c++)for(var u=c+1;u<d;u++)o[t[c]][t[u]]=Math.min(o[t[c]][t[u]],o[t[c]][t[l]]+o[t[l]][t[u]]),o[t[u]][t[c]]=o[t[c]][t[u]];return o}}]),e}();t["default"]=n},function(e,t){"undefined"!=typeof CanvasRenderingContext2D&&(CanvasRenderingContext2D.prototype.circle=function(e,t,i){this.beginPath(),this.arc(e,t,i,0,2*Math.PI,!1),this.closePath()},CanvasRenderingContext2D.prototype.square=function(e,t,i){this.beginPath(),this.rect(e-i,t-i,2*i,2*i),this.closePath()},CanvasRenderingContext2D.prototype.triangle=function(e,t,i){this.beginPath(),i*=1.15,t+=.275*i;var o=2*i,n=o/2,s=Math.sqrt(3)/6*o,r=Math.sqrt(o*o-n*n);this.moveTo(e,t-(r-s)),this.lineTo(e+n,t+s),this.lineTo(e-n,t+s),this.lineTo(e,t-(r-s)),this.closePath()},CanvasRenderingContext2D.prototype.triangleDown=function(e,t,i){this.beginPath(),i*=1.15,t-=.275*i;var o=2*i,n=o/2,s=Math.sqrt(3)/6*o,r=Math.sqrt(o*o-n*n);this.moveTo(e,t+(r-s)),this.lineTo(e+n,t-s),this.lineTo(e-n,t-s),this.lineTo(e,t+(r-s)),this.closePath()},CanvasRenderingContext2D.prototype.star=function(e,t,i){this.beginPath(),i*=.82,t+=.1*i;for(var o=0;o<10;o++){var n=o%2===0?1.3*i:.5*i;this.lineTo(e+n*Math.sin(2*o*Math.PI/10),t-n*Math.cos(2*o*Math.PI/10))}this.closePath()},CanvasRenderingContext2D.prototype.diamond=function(e,t,i){this.beginPath(),this.lineTo(e,t+i),this.lineTo(e+i,t),this.lineTo(e,t-i),this.lineTo(e-i,t),this.closePath()},CanvasRenderingContext2D.prototype.roundRect=function(e,t,i,o,n){var s=Math.PI/180;i-2*n<0&&(n=i/2),o-2*n<0&&(n=o/2),this.beginPath(),this.moveTo(e+n,t),this.lineTo(e+i-n,t),this.arc(e+i-n,t+n,n,270*s,360*s,!1),this.lineTo(e+i,t+o-n),this.arc(e+i-n,t+o-n,n,0,90*s,!1),this.lineTo(e+n,t+o),this.arc(e+n,t+o-n,n,90*s,180*s,!1),this.lineTo(e,t+n),this.arc(e+n,t+n,n,180*s,270*s,!1),this.closePath()},CanvasRenderingContext2D.prototype.ellipse=function(e,t,i,o){var n=.5522848,s=i/2*n,r=o/2*n,a=e+i,h=t+o,d=e+i/2,l=t+o/2;this.beginPath(),this.moveTo(e,l),this.bezierCurveTo(e,l-r,d-s,t,d,t),this.bezierCurveTo(d+s,t,a,l-r,a,l),this.bezierCurveTo(a,l+r,d+s,h,d,h),this.bezierCurveTo(d-s,h,e,l+r,e,l),this.closePath()},CanvasRenderingContext2D.prototype.database=function(e,t,i,o){var n=1/3,s=i,r=o*n,a=.5522848,h=s/2*a,d=r/2*a,l=e+s,c=t+r,u=e+s/2,f=t+r/2,p=t+(o-r/2),v=t+o;this.beginPath(),this.moveTo(l,f),this.bezierCurveTo(l,f+d,u+h,c,u,c),this.bezierCurveTo(u-h,c,e,f+d,e,f),this.bezierCurveTo(e,f-d,u-h,t,u,t),this.bezierCurveTo(u+h,t,l,f-d,l,f),this.lineTo(l,p),this.bezierCurveTo(l,p+d,u+h,v,u,v),this.bezierCurveTo(u-h,v,e,p+d,e,p),this.lineTo(e,f)},CanvasRenderingContext2D.prototype.arrow=function(e,t,i,o){var n=e-o*Math.cos(i),s=t-o*Math.sin(i),r=e-.9*o*Math.cos(i),a=t-.9*o*Math.sin(i),h=n+o/3*Math.cos(i+.5*Math.PI),d=s+o/3*Math.sin(i+.5*Math.PI),l=n+o/3*Math.cos(i-.5*Math.PI),c=s+o/3*Math.sin(i-.5*Math.PI);this.beginPath(),this.moveTo(e,t),this.lineTo(h,d),this.lineTo(r,a),this.lineTo(l,c),this.closePath()},CanvasRenderingContext2D.prototype.dashedLine=function(e,t,i,o,n){this.beginPath(),this.moveTo(e,t);for(var s=n.length,r=i-e,a=o-t,h=a/r,d=Math.sqrt(r*r+a*a),l=0,c=!0,u=0,f=n[0];d>=.1;)f=n[l++%s],f>d&&(f=d),u=Math.sqrt(f*f/(1+h*h)),u=r<0?-u:u,e+=u,t+=h*u,c===!0?this.lineTo(e,t):this.moveTo(e,t),d-=f,c=!c})},function(e,t){function i(e){if(e)return o(e)}function o(e){for(var t in i.prototype)e[t]=i.prototype[t];return e}e.exports=i,i.prototype.on=i.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks[e]=this._callbacks[e]||[]).push(t),this},i.prototype.once=function(e,t){function i(){o.off(e,i),t.apply(this,arguments)}var o=this;return this._callbacks=this._callbacks||{},i.fn=t,this.on(e,i),this},i.prototype.off=i.prototype.removeListener=i.prototype.removeAllListeners=i.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var i=this._callbacks[e];if(!i)return this;if(1==arguments.length)return delete this._callbacks[e],this;for(var o,n=0;n<i.length;n++)if(o=i[n],o===t||o.fn===t){i.splice(n,1);break}return this},i.prototype.emit=function(e){this._callbacks=this._callbacks||{};var t=[].slice.call(arguments,1),i=this._callbacks[e];if(i){i=i.slice(0);for(var o=0,n=i.length;o<n;++o)i[o].apply(this,t)}return this},i.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks[e]||[]},i.prototype.hasListeners=function(e){return!!this.listeners(e).length}},function(e,t){function i(e){return P=e,f()}function o(){F=0,B=P.charAt(0)}function n(){F++,B=P.charAt(F)}function s(){return P.charAt(F+1)}function r(e){return N.test(e)}function a(e,t){if(e||(e={}),t)for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);return e}function h(e,t,i){for(var o=t.split("."),n=e;o.length;){var s=o.shift();o.length?(n[s]||(n[s]={}),n=n[s]):n[s]=i}}function d(e,t){for(var i,o,n=null,s=[e],r=e;r.parent;)s.push(r.parent),r=r.parent;if(r.nodes)for(i=0,o=r.nodes.length;i<o;i++)if(t.id===r.nodes[i].id){n=r.nodes[i];break}for(n||(n={id:t.id},e.node&&(n.attr=a(n.attr,e.node))),i=s.length-1;i>=0;i--){var h=s[i];h.nodes||(h.nodes=[]),h.nodes.indexOf(n)===-1&&h.nodes.push(n)}t.attr&&(n.attr=a(n.attr,t.attr))}function l(e,t){if(e.edges||(e.edges=[]),e.edges.push(t),e.edge){var i=a({},e.edge);t.attr=a(i,t.attr)}}function c(e,t,i,o,n){var s={from:t,to:i,type:o};return e.edge&&(s.attr=a({},e.edge)),s.attr=a(s.attr||{},n),s}function u(){for(j=C.NULL,I="";" "===B||"\t"===B||"\n"===B||"\r"===B;)n();do{var e=!1;if("#"===B){for(var t=F-1;" "===P.charAt(t)||"\t"===P.charAt(t);)t--;if("\n"===P.charAt(t)||""===P.charAt(t)){for(;""!=B&&"\n"!=B;)n();e=!0}}if("/"===B&&"/"===s()){for(;""!=B&&"\n"!=B;)n();e=!0}if("/"===B&&"*"===s()){for(;""!=B;){if("*"===B&&"/"===s()){n(),n();break}n()}e=!0}for(;" "===B||"\t"===B||"\n"===B||"\r"===B;)n()}while(e);if(""===B)return void(j=C.DELIMITER);var i=B+s();if(T[i])return j=C.DELIMITER,I=i,n(),void n();if(T[B])return j=C.DELIMITER,I=B,void n();if(r(B)||"-"===B){for(I+=B,n();r(B);)I+=B,n();return"false"===I?I=!1:"true"===I?I=!0:isNaN(Number(I))||(I=Number(I)),void(j=C.IDENTIFIER)}if('"'===B){for(n();""!=B&&('"'!=B||'"'===B&&'"'===s());)I+=B,'"'===B&&n(),n();if('"'!=B)throw w('End of string " expected');return n(),void(j=C.IDENTIFIER)}for(j=C.UNKNOWN;""!=B;)I+=B,n();throw new SyntaxError('Syntax error in part "'+k(I,30)+'"')}function f(){var e={};if(o(),u(),"strict"===I&&(e.strict=!0,u()),"graph"!==I&&"digraph"!==I||(e.type=I,u()),j===C.IDENTIFIER&&(e.id=I,u()),"{"!=I)throw w("Angle bracket { expected");if(u(),p(e),"}"!=I)throw w("Angle bracket } expected");if(u(),""!==I)throw w("End of file expected");return u(),delete e.node,delete e.edge,delete e.graph,e}function p(e){for(;""!==I&&"}"!=I;)v(e),";"===I&&u()}function v(e){var t=y(e);if(t)return void m(e,t);var i=g(e);if(!i){if(j!=C.IDENTIFIER)throw w("Identifier expected");var o=I;if(u(),"="===I){if(u(),j!=C.IDENTIFIER)throw w("Identifier expected");e[o]=I,u()}else b(e,o)}}function y(e){var t=null;if("subgraph"===I&&(t={},t.type="subgraph",u(),j===C.IDENTIFIER&&(t.id=I,u())),"{"===I){if(u(),t||(t={}),t.parent=e,t.node=e.node,t.edge=e.edge,t.graph=e.graph,p(t),"}"!=I)throw w("Angle bracket } expected");u(),delete t.node,delete t.edge,delete t.graph,delete t.parent,e.subgraphs||(e.subgraphs=[]),e.subgraphs.push(t)}return t}function g(e){return"node"===I?(u(),e.node=_(),"node"):"edge"===I?(u(),e.edge=_(),"edge"):"graph"===I?(u(),e.graph=_(),"graph"):null}function b(e,t){var i={id:t},o=_();o&&(i.attr=o),d(e,i),m(e,t)}function m(e,t){for(;"->"===I||"--"===I;){var i,o=I;u();var n=y(e);if(n)i=n;else{if(j!=C.IDENTIFIER)throw w("Identifier or subgraph expected");i=I,d(e,{id:i}),u()}var s=_(),r=c(e,t,i,o,s);l(e,r),t=i}}function _(){for(var e=null;"["===I;){for(u(),e={};""!==I&&"]"!=I;){if(j!=C.IDENTIFIER)throw w("Attribute name expected");var t=I;if(u(),"="!=I)throw w("Equal sign = expected");if(u(),j!=C.IDENTIFIER)throw w("Attribute value expected");var i=I;h(e,t,i),u(),","==I&&u()}if("]"!=I)throw w("Bracket ] expected");u()}return e}function w(e){return new SyntaxError(e+', got "'+k(I,30)+'" (char '+F+")")}function k(e,t){return e.length<=t?e:e.substr(0,27)+"..."}function x(e,t,i){Array.isArray(e)?e.forEach(function(e){Array.isArray(t)?t.forEach(function(t){i(e,t)}):i(e,t)}):Array.isArray(t)?t.forEach(function(t){i(e,t)}):i(e,t)}function O(e,t,i){for(var o=t.split("."),n=o.pop(),s=e,r=0;r<o.length;r++){var a=o[r];a in s||(s[a]={}),s=s[a]}return s[n]=i,e}function E(e,t){var i={};for(var o in e)if(e.hasOwnProperty(o)){var n=t[o];Array.isArray(n)?n.forEach(function(t){O(i,t,e[o])}):"string"==typeof n?O(i,n,e[o]):O(i,o,e[o])}return i}function M(e){var t=i(e),o={nodes:[],edges:[],options:{}};if(t.nodes&&t.nodes.forEach(function(e){var t={id:e.id,label:String(e.label||e.id)};a(t,E(e.attr,S)),t.image&&(t.shape="image"),o.nodes.push(t)}),t.edges){var n=function(e){var t={from:e.from,to:e.to};return a(t,E(e.attr,D)),t.arrows="->"===e.type?"to":void 0,t};t.edges.forEach(function(e){var t,i;t=e.from instanceof Object?e.from.nodes:{id:e.from},i=e.to instanceof Object?e.to.nodes:{id:e.to},e.from instanceof Object&&e.from.edges&&e.from.edges.forEach(function(e){var t=n(e);o.edges.push(t)}),x(t,i,function(t,i){var s=c(o,t.id,i.id,e.type,e.attr),r=n(s);o.edges.push(r)}),e.to instanceof Object&&e.to.edges&&e.to.edges.forEach(function(e){var t=n(e);o.edges.push(t)})})}return t.attr&&(o.options=t.attr),o}var S={fontsize:"font.size",fontcolor:"font.color",labelfontcolor:"font.color",fontname:"font.face",color:["color.border","color.background"],fillcolor:"color.background",tooltip:"title",labeltooltip:"title"},D=Object.create(S);D.color="color.color";var C={NULL:0,DELIMITER:1,IDENTIFIER:2,UNKNOWN:3},T={"{":!0,"}":!0,"[":!0,"]":!0,";":!0,"=":!0,",":!0,"->":!0,"--":!0},P="",F=0,B="",I="",j=C.NULL,N=/[a-zA-Z_0-9.:#]/;t.parseDOT=i,t.DOTToGraph=M},function(e,t){function i(e,t){var i=[],o=[],n={edges:{inheritColor:!1},nodes:{fixed:!1,parseColor:!1}};void 0!==t&&(void 0!==t.fixed&&(n.nodes.fixed=t.fixed),void 0!==t.parseColor&&(n.nodes.parseColor=t.parseColor),void 0!==t.inheritColor&&(n.edges.inheritColor=t.inheritColor));for(var s=e.edges,r=e.nodes,a=0;a<s.length;a++){var h={},d=s[a];h.id=d.id,h.from=d.source,h.to=d.target,h.attributes=d.attributes,h.label=d.label,h.title=void 0!==d.attributes?d.attributes.title:void 0,"Directed"===d.type&&(h.arrows="to"),d.color&&n.inheritColor===!1&&(h.color=d.color),i.push(h)}for(var a=0;a<r.length;a++){var l={},c=r[a];l.id=c.id,l.attributes=c.attributes,l.title=c.title,l.x=c.x,l.y=c.y,l.label=c.label,l.title=void 0!==c.attributes?c.attributes.title:void 0,n.nodes.parseColor===!0?l.color=c.color:l.color=void 0!==c.color?{background:c.color,border:c.color,highlight:{background:c.color,border:c.color},hover:{background:c.color,border:c.color}}:void 0,l.size=c.size,l.fixed=n.nodes.fixed&&void 0!==c.x&&void 0!==c.y,o.push(l)}return{nodes:o,edges:i}}t.parseGephi=i},function(e,t,i){function o(e){this.active=!1,this.dom={container:e},this.dom.overlay=document.createElement("div"),this.dom.overlay.className="vis-overlay",this.dom.container.appendChild(this.dom.overlay),this.hammer=a(this.dom.overlay),this.hammer.on("tap",this._onTapOverlay.bind(this));var t=this,i=["tap","doubletap","press","pinch","pan","panstart","panmove","panend"];i.forEach(function(e){t.hammer.on(e,function(e){e.stopPropagation()})}),document&&document.body&&(this.onClick=function(i){n(i.target,e)||t.deactivate()},document.body.addEventListener("click",this.onClick)),void 0!==this.keycharm&&this.keycharm.destroy(),this.keycharm=s(),this.escListener=this.deactivate.bind(this)}function n(e,t){for(;e;){if(e===t)return!0;e=e.parentNode}return!1}var s=i(65),r=i(76),a=i(58),h=i(1);r(o.prototype),o.current=null,o.prototype.destroy=function(){this.deactivate(),this.dom.overlay.parentNode.removeChild(this.dom.overlay),this.onClick&&document.body.removeEventListener("click",this.onClick),this.hammer.destroy(),this.hammer=null},o.prototype.activate=function(){o.current&&o.current.deactivate(),o.current=this,this.active=!0,this.dom.overlay.style.display="none",h.addClassName(this.dom.container,"vis-active"),this.emit("change"),this.emit("activate"),this.keycharm.bind("esc",this.escListener)},o.prototype.deactivate=function(){this.active=!1,this.dom.overlay.style.display="",h.removeClassName(this.dom.container,"vis-active"),this.keycharm.unbind("esc",this.escListener),this.emit("change"),this.emit("deactivate")},o.prototype._onTapOverlay=function(e){this.activate(),e.stopPropagation()},e.exports=o},function(e,t){t.en={edit:"Edit",del:"Delete selected",back:"Back",addNode:"Add Node",addEdge:"Add Edge",editNode:"Edit Node",editEdge:"Edit Edge",addDescription:"Click in an empty space to place a new node.",edgeDescription:"Click on a node and drag the edge to another node to connect them.",editEdgeDescription:"Click on the control points and drag them to a node to connect to it.",createEdgeError:"Cannot link edges to a cluster.",deleteClusterError:"Clusters cannot be deleted.",editClusterError:"Clusters cannot be edited."},t.en_EN=t.en,t.en_US=t.en,t.de={edit:"Editieren",del:"Lösche Auswahl",back:"Zurück",addNode:"Knoten hinzufügen",addEdge:"Kante hinzufügen",editNode:"Knoten editieren",editEdge:"Kante editieren",addDescription:"Klicke auf eine freie Stelle, um einen neuen Knoten zu plazieren.",edgeDescription:"Klicke auf einen Knoten und ziehe die Kante zu einem anderen Knoten, um diese zu verbinden.",editEdgeDescription:"Klicke auf die Verbindungspunkte und ziehe diese auf einen Knoten, um sie zu verbinden.",createEdgeError:"Es ist nicht möglich, Kanten mit Clustern zu verbinden.",deleteClusterError:"Cluster können nicht gelöscht werden.",editClusterError:"Cluster können nicht editiert werden."},t.de_DE=t.de,t.es={edit:"Editar",del:"Eliminar selección",back:"Átras",addNode:"Añadir nodo",addEdge:"Añadir arista",editNode:"Editar nodo",editEdge:"Editar arista",addDescription:"Haga clic en un lugar vacío para colocar un nuevo nodo.",edgeDescription:"Haga clic en un nodo y arrastre la arista hacia otro nodo para conectarlos.",editEdgeDescription:"Haga clic en un punto de control y arrastrelo a un nodo para conectarlo.",createEdgeError:"No se puede conectar una arista a un grupo.",deleteClusterError:"No es posible eliminar grupos.",editClusterError:"No es posible editar grupos."},t.es_ES=t.es,t.nl={edit:"Wijzigen",del:"Selectie verwijderen",back:"Terug",addNode:"Node toevoegen",addEdge:"Link toevoegen",editNode:"Node wijzigen",editEdge:"Link wijzigen",addDescription:"Klik op een leeg gebied om een nieuwe node te maken.",edgeDescription:"Klik op een node en sleep de link naar een andere node om ze te verbinden.",editEdgeDescription:"Klik op de verbindingspunten en sleep ze naar een node om daarmee te verbinden.",createEdgeError:"Kan geen link maken naar een cluster.",deleteClusterError:"Clusters kunnen niet worden verwijderd.",editClusterError:"Clusters kunnen niet worden aangepast."},t.nl_NL=t.nl,t.nl_BE=t.nl,t.ru={edit:"Редактировать",del:"Удалить выбранное",back:"Назад",addNode:"Добавить узел",addEdge:"Добавить ребро",editNode:"Редактировать узел",editEdge:"Редактировать ребро",addDescription:"Кликните в свободное место, чтобы добавить новый узел.",edgeDescription:"Кликните на узел и протяните ребро к другому узлу, чтобы соединить их.",editEdgeDescription:"Кликните на контрольные точки и перетащите их в узел, чтобы подключиться к нему.",createEdgeError:"Невозможно соединить ребра в кластер.",deleteClusterError:"Кластеры не могут быть удалены",editClusterError:"Кластеры недоступны для редактирования."},t.ru_RU=t.ru}])});