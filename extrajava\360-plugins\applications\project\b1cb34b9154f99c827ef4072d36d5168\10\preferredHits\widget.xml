<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Preferred hits" group="PLM Analytics/Favorites" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description></Description>
	<Dependencies>
		<Trigger position="self" className="com.exalead.cv360.searchui.triggers.impl.RemoveIfNotLoggedIn" />
		<Widget name="plmaResources" />
	</Dependencies>
	<Includes>
		<Include type="css" path="css/style.less"></Include>
		<Include type="css" path="/resources/widgets/plmaResources/lib/notify/notify-plma.less" />
		<Include type="js" path="js/preferredHits.js" />

		<Include type="js" path="/resources/javascript/jquery.cookie.js" />
		<Include type="js" path="/resources/widgets/plmaResources/js/lodash.min.js" />
		<Include type="js" path="/resources/widgets/plmaResources/lib/notify/notify.js" />
		<Include type="js" path="/resources/widgets/plmaResources/lib/notify/notify-plma.js" />
	</Includes>
	<Preview>
		<![CDATA[
			<img src="img/preview.png" />
		]]>
	</Preview>
	<SupportWidgetsId arity="ZERO_OR_ONE"/>
	<SupportFeedsId arity="ONE" consumeFeed="false"/>
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>recentSearches.pin</JsKey>
			<JsKey>recentSearches.unpin</JsKey>
			<JsKey>plma.hits.collection.labels.counter</JsKey>
			<JsKey>plma.hits.collection.labels.tooltip</JsKey>
			<JsKey>plma.hits.collection.labels.emptyCollection</JsKey>
			<JsKey>plma.hits.collection.labels.trashTitle</JsKey>
			<JsKey>plma.hits.collection.add-entry</JsKey>
			<JsKey>plma.hits.collection.remove-entry</JsKey>
			<JsKey>plma.hits.collection.alerts.add</JsKey>
			<JsKey>plma.hits.collection.alerts.remove</JsKey>
			<JsKey>plma.hits.collection.alerts.removeAll</JsKey>
			<JsKey>plma.hits.collection.alerts.limitReached</JsKey>
			<JsKey>plma.hits.collection.alerts.removeOld</JsKey>
			<JsKey>plma.hits.collection.alerts.minDisplaySize</JsKey>
		</JsKeys>
	</SupportI18N>
	<OptionsGroup name="General">
		<Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
			<Functions>
				<ContextMenu>addContext('Title', [${i18n['plma.hits.collection.labels.label.colletionId']}, "${i18n['recentSearches.getter.title']}", "${i18n['recentSearches.pins_getter.title']}"])</ContextMenu>
			</Functions>
		</Option>
		<Option id="collection" name="Collection" arity="ONE">
			<Description>Preferred hit collection name.</Description>
		</Option>
		<Option id="urlParameter" name="Collection URL parameter" arity="ONE">
			<Description>Target collection URL parameter</Description>
		</Option>
		<Option id="compactHeader" name="Compact header" arity="ONE">
			<Description>Display compact view in header.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>

	<DefaultValues displayName="Normal" previewUrl="">
		<DefaultValue name="urlParameter">preferredHits</DefaultValue>
		<DefaultValue name="compactHeader">true</DefaultValue>
	</DefaultValues>
</Widget>
