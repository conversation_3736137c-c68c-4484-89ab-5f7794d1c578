<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA Preferences" group="PLM Analytics/Miscellaneous" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>
		<![CDATA[
			This widget displays the preferences menu which allows users to choose configuration settings.
			In this widget each user can choose colors and icons for facets and categories,
			modify the ranges used to create some range facets as lateness,
			modify the date ranges used in the refine panel date input and manage its pages. <br />
		]]>
	</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/preferences/images/preview.PNG" alt="Preference" />
        ]]>
	</Preview>
	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="css" path="../plmaResources/css/styles/colorpicker.less" />
		<Include type="js" path="js/saveState.js" />
		<Include type="js" path="js/facetDisplay.js" />
        <Include type="js" path="js/facetRange.js" />
		<Include type="js" path="js/dateRange.js" />
		<Include type="js" path="js/layout.js" />
		<Include type="js" path="js/page.js" />
		<Include type="js" path="js/preferences.js" />
		<Include type="js" path="js/landingPageManager.js" />
		<Include type="js" path="../plmaResources/js/lodash.min.js" />
		<Include type="js" path="../plmaResources/js/i18nClient.js" />
		<Include type="js" path="../plmaResources/js/colorpicker.js" />
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="../3dxpSearchAndRefine/js/message-handler.js"/>
		<Include type="js" path="../plmaResources/js/experienceAPIs/Session3DContent.js"/>
		<Include type="js" path="../plmaResources/js/select.js" />
		<Include type="js" path="../plmaResources/js/moment-with-locales.min.js" />
		<Include type="js" path="../plmaResources/js/daterangepicker.js" />
		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js"/>
		<Include type="js" path="../plmaResources/js/popupLib.js"/>
		<Include type="js" path="../plmaResources/js/rights.js"/>
		<Include type="js" path="../chartboard/js/model.js" />
		<Include type="js" path="../pinnedPages/js/pinnedPages.js" />

		<Include type="css" path="../plmaResultList/css/resultlist-pref.less" />
		<Include type="js" path="../plmaResultList/js/plmaResultListPref.js"/>
		<Include type="css" path="../plmaHitDetails/css/hitdetails-pref.less" />
		<Include type="js" path="../plmaHitDetails/js/plmaHitDetailsPref.js"/>
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO_OR_MANY" />
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>plma.preference.facet.display.title</JsKey>
			<JsKey>plma.preference.facet.display.description</JsKey>
            <JsKey>plma.preference.facet.range.title</JsKey>
            <JsKey>plma.preference.facet.range.description</JsKey>
			<JsKey>plma.preference.date.range.title</JsKey>
			<JsKey>plma.preference.date.range.description</JsKey>
			<JsKey>plma.preference.layout.title</JsKey>
			<JsKey>plma.preference.layout.description</JsKey>
			<JsKey>plma.preference.pages.title</JsKey>
			<JsKey>plma.preference.pages.description</JsKey>
			<JsKey>plma.preference.save</JsKey>
			<JsKey>plma.preference.save.success</JsKey>
			<JsKey>plma.preference.save.error</JsKey>
			<JsKey>plma.preference.cancel</JsKey>
			<JsKey>plma.preference.close</JsKey>
			<JsKey>plma.preference.reset</JsKey>
			<JsKey>plma.preference.enable</JsKey>
            <JsKey>plma.preference.detail.id</JsKey>
            <JsKey>plma.preference.detail.icon</JsKey>
            <JsKey>plma.preference.detail.color</JsKey>
            <JsKey>plma.preference.detail.label</JsKey>
            <JsKey>plma.preference.detail.description</JsKey>
            <JsKey>plma.preference.detail.start</JsKey>
            <JsKey>plma.preference.detail.end</JsKey>
            <JsKey>plma.preference.detail.fromNb</JsKey>
            <JsKey>plma.preference.detail.fromUnit</JsKey>
            <JsKey>plma.preference.detail.toNb</JsKey>
            <JsKey>plma.preference.detail.toUnit</JsKey>
			<JsKey>plma.preference.detail.section</JsKey>
			<JsKey>plma.preference.detail.facet.title</JsKey>
			<JsKey>plma.preference.detail.category.title</JsKey>
			<JsKey>plma.preference.detail.date.title</JsKey>
			<JsKey>plma.preference.detail.layout.title</JsKey>
			<JsKey>plma.preference.detail.pages.title</JsKey>
			<JsKey>plma.preference.detail.facet.range.title</JsKey>
			<JsKey>plma.preference.detail.noicon</JsKey>
			<JsKey>plma.preference.detail.nocolor</JsKey>
			<JsKey>plma.preference.date.current</JsKey>
			<JsKey>plma.preference.date.start</JsKey>
			<JsKey>plma.preference.date.end</JsKey>
			<JsKey>plma.preference.landingPage.title</JsKey>
			<JsKey>plma.preference.landingPage.description</JsKey>
			<JsKey>plma.preference.landingPage.fixedPage</JsKey>
			<JsKey>plma.preference.landingPage.lastVisitedPage</JsKey>
			<JsKey>plma.preference.pages.mashup</JsKey>
			<JsKey>plma.preference.pages.user</JsKey>
			<JsKey>plma.preference.pages.shared</JsKey>
			<JsKey>plma.preference.pages.updateLabel</JsKey>
			<JsKey>plma.preference.pages.updateDescription</JsKey>
			<JsKey>plma.preference.pages.updateIcon</JsKey>
			<JsKey>plma.preference.share.all</JsKey>
			<JsKey>plma.preference.share.title</JsKey>
			<JsKey>plma.preference.share.write</JsKey>
			<JsKey>plma.preference.share.read</JsKey>
			<JsKey>plma.preference.share.change.user</JsKey>
			<JsKey>plma.preference.share.change.group</JsKey>
			<JsKey>plma.preference.share.added.user</JsKey>
			<JsKey>plma.preference.share.add.tooltip</JsKey>
			<JsKey>plma.preference.share.remove.tooltip</JsKey>
			<JsKey>plma.preference.selection</JsKey>
			<JsKey>plma.preference.selection.title</JsKey>
			<JsKey>plma.preference.selection.description</JsKey>
			<JsKey>plma.preference.selection.listen</JsKey>
			<JsKey>plma.preference.selection.send</JsKey>

			<JsKey>plma.preference.hitdetails.tab.label</JsKey>
			<JsKey>plma.preference.hitdetails.tab.title</JsKey>
			<JsKey>plma.preference.hitdetails.tab.description</JsKey>
		</JsKeys>
	</SupportI18N>

	<OptionsGroup name="General">
		<Option id="title" name="Widget Title" isEvaluated="true">
			<Description>Widget title, if blank then no title.</Description>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Advanced">
		<Option id="parametersWhitelist" name="Parameters whitelist" arity="ONE_OR_MANY">
			<Description>
			<![CDATA[
				Only in a 3DXP context. If the user wants to save the application state,
				parameters of the query string that should be kept in the URL.
			]]>
			</Description>
		</Option>
		<Option id="configureTabs" name="Tabs" arity="ONE">
			<Description>
				<![CDATA[Configure preference tabs, default to false[show all tabs].
					NOTE: ResultList and HitDetail preference tabs are configurable via individual widget.
				]]>
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('false',['tabList'])</Display>
			</Functions>
		</Option>
		<Option id="tabList" name="Select Tabs" arity="ZERO_OR_MANY">
			<Description>Select tab to be shown</Description>
			<Functions>
				<ContextMenu>addContext("Preference tabs options",
					[{value:'facetDisplay', display: 'Facet Display'},
					{value:'facetRange', display: 'Facet Ranges'},
					{value:'dateRange', display: 'Date Ranges'},
					{value:'menu', display: 'Pages'}])</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Group share suggest">
		<Option id="enableSuggest" name="Enable suggest" arity="ONE">
			<Description>Enables the suggest service on the search field. You must then select a suggest service - previously defined in the Administration Console - from 'Suggest Name'.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>PARAMETER_doHide('false',['suggestNames', 'suggestApiAction', 'suggestApiConfig', 'suggestCustomSearchAPIs', 'suggestApiCommand'],[], true, false)</Display>
			</Functions>
		</Option>
		<Option id="suggestNames" name="Suggest Name" arity="MANY">
			<Description>Suggest service name.</Description>
			<Functions>
				<ContextMenu>SuggestNames('suggestApiAction')</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="suggestApiAction" name="Action" arity="ONE">
			<Description>Specifies whether the suggest should use a simple service or a suggest dispatcher which varies depending on the query input. </Description>
			<Values>
				<Value>dispatcher</Value>
				<Value>service</Value>
			</Values>
		</Option>
		<Option id="suggestApiConfig" name="Config" arity="ZERO_OR_ONE">
			<Description>
				<![CDATA[
					Indicates the name of the default Search API, for example, <code>sapi0</code>.
				]]>
			</Description>
			<Placeholder>sapi0</Placeholder>
			<Functions>
				<ContextMenu>ApiConfig()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				<Check>isSpecified('ONE', 'suggestCustomSearchAPIs')</Check>
			</Functions>
		</Option>
		<Option id="suggestCustomSearchAPIs" name="Search API URL" arity="ZERO_OR_MANY">
			<Description>Defines the URL that will be used by the Search API.</Description>
			<Placeholder>http://HOST:PORT/</Placeholder>
			<Functions>
				<Check>isSpecified('ONE', 'suggestApiConfig')</Check>
			</Functions>
		</Option>
		<Option id="suggestApiCommand" name="API command" arity="ONE">
			<Description>Specifies the suggest API command name that will be appended to the 'Search API URL'.</Description>
			<Placeholder>suggest</Placeholder>
			<Functions>
				<ContextMenu>ApiCommand()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="parametersWhitelist">pageId</DefaultValue>
		<DefaultValue name="configureTabs">false</DefaultValue>
	</DefaultValues>
</Widget>
