<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import parameters="feed,entry,itemsDef,groupsDef" ignore="true" />

<config:getOptionsComposite var="itemsDef" name="itemsDef" mapIndex="true" doEval="false"/>
<config:getOptionsComposite var="groupsDef" name="groupsDef" mapIndex="true" doEval="false"/>

<c:forEach var="itemDef" items="${itemsDef}">
	<string:eval var="showItem" string="${itemDef.condition}" feed="${feed}" entry="${entry}"/>
	<c:if test="${showItem == 'true'}">
		(function(){
			let itemData = {};
			<string:eval var="itemId" string="${itemDef.id}" feed="${feed}" entry="${entry}" isJsEscape="true" isHtmlEscape="false"/>
			itemData.id = '${itemId}';
			itemData.content = '<string:eval string="${itemDef.content}" feed="${feed}" entry="${entry}" isJsEscape="true" isHtmlEscape="false"/>';
			itemData.type = '<string:eval string="${itemDef.type}" feed="${feed}" entry="${entry}" isJsEscape="true" isHtmlEscape="false"/>';
			itemData.start = '<string:eval string="${itemDef.start}" feed="${feed}" entry="${entry}" isJsEscape="false" isHtmlEscape="false"/>';
			<c:if test="${itemDef.type == 'range' || itemDef.type == 'background' || itemDef.type == 'estact'}">
				itemData.end = '<string:eval string="${itemDef.end}" feed="${feed}" entry="${entry}" isJsEscape="false" isHtmlEscape="false"/>';
			</c:if>
			<c:if test="${itemDef.type == 'estact'}">
				itemData.actStart = '<string:eval string="${itemDef.actStart}" feed="${feed}" entry="${entry}" isJsEscape="false" isHtmlEscape="false"/>';
				itemData.actEnd = '<string:eval string="${itemDef.actEnd}" feed="${feed}" entry="${entry}" isJsEscape="false" isHtmlEscape="false"/>';
			</c:if>
			<c:if test="${not empty itemDef.group}">
				itemData.group = '<string:eval string="${itemDef.group}" feed="${feed}" entry="${entry}" isJsEscape="true" isHtmlEscape="false"/>';
			</c:if>
			itemData.title = '<string:eval string="${itemDef.title}" feed="${feed}" entry="${entry}" isJsEscape="true" isHtmlEscape="false"/>';
			itemData.idClass = '<string:sanitize string="${itemId}"/>'
			<string:eval var="otherOptions" string="${itemDef.other}" feed="${feed}" entry="${entry}" isJsEscape="false" isHtmlEscape="false"/>;
			let other = ${empty otherOptions ? {} : otherOptions};
			itemData = $.extend({}, other, itemData);					
			tDataSetBuilder.addItem(itemData);
		})();
	</c:if>
</c:forEach>
		
<c:forEach var="groupDef" items="${groupsDef}">
	<string:eval var="showGroup" string="${groupDef.condition}" feed="${feed}" entry="${entry}"/>
	<c:if test="${showGroup == 'true'}">
		<string:eval var="groupId" string="${groupDef.id}" feed="${feed}" entry="${entry}" isJsEscape="true" isHtmlEscape="false"/>
		<string:eval var="nestedGroups" string="${groupDef.nestedGroups}" feed="${feed}" entry="${entry}" isJsEscape="true" isHtmlEscape="false"/>
		(function(){						
			let groupData = {};
			groupData.id = '${groupId}';
			groupData.content = '<string:eval string="${groupDef.content}" feed="${feed}" entry="${entry}" isJsEscape="true" isHtmlEscape="false"/>';
			groupData.nestedGroups = '${nestedGroups}';
			groupData.title = '<string:eval string="${groupDef.title}" feed="${feed}" entry="${entry}" isJsEscape="true" isHtmlEscape="false"/>';

			<string:eval var="otherOptions" string="${groupDef.other}" feed="${feed}" entry="${entry}" isJsEscape="false" isHtmlEscape="false"/>;
			let other = ${empty otherOptions ? {} : otherOptions};
			groupData = $.extend({}, other, groupData);					
			tDataSetBuilder.addGroup(groupData);
		})();
	</c:if>
</c:forEach>
