<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA menu" group="PLM Analytics/Navigation" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget displays the navigation menu. To be displayed as a sidebar, it must either be added as a subwidget of the "Flex panels container" widget or the page custom layout option must be set to flex-layout.</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/sideBarMenu/images/preview.PNG" alt="Side Bar Menu" />
        ]]>
	</Preview>
	<Includes>
		<Include type="css" path="css/style.less"/>
		<Include type="js" path="/resources/javascript/jquery.cookie.js" />
		<Include type="js" path="/resources/jquery-ui-1.11.4.custom/jquery-ui.min.js" includeCondition="showUserMenu=true"/>
		<Include type="js" path="js/plmaMenu.js"/>
		<Include type="js" path="js/editMenu.js"/>
		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js"/>
		<Include type="js" path="../plmaResources/js/jquery.plmaSuggest.js" />
		<Include type="js" path="../plmaResources/js/lodash.min.js" />
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
	</Includes>	
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportWidgetsId arity="ZERO_OR_MANY" />
	<SupportFeedsId arity="ZERO_OR_MANY" />
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>widget.plmaMenu.add.section</JsKey>
			<JsKey>widget.plmaMenu.save</JsKey>
			<JsKey>widget.plmaMenu.cancel</JsKey>
			<JsKey>widget.plmaMenu.error</JsKey>
			<JsKey>widget.plmaMenu.delete.section.title</JsKey>
			<JsKey>widget.plmaMenu.delete.section.label</JsKey>
			<JsKey>widget.plmaMenu.add.section.tooltip</JsKey>
			<JsKey>widget.plmaMenu.edit.drag.section</JsKey>
			<JsKey>widget.plmaMenu.edit.drag.page</JsKey>
		</JsKeys>
	</SupportI18N>
	
	<OptionsGroup name="General">
		<Option id="config" name="Configuration name" arity="ZERO_OR_ONE" >
			<Description>Configuration name (can be located in '[DATADIR]/config/[APP]/[name].json' or '[DATADIR]/config/[APP]/[name].xml').</Description>
		</Option>

		<Option id="showUserMenu" name="Show user menu" arity="ONE">
			<Description>Displays the pages duplicated by the user on the navigation menu.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="keepUserItemQueryString" name="Keep query string" arity="ONE">
			<Description>Keeps the current URL query string (including all filters) in the destination URL. It allows you to keep filters throughout the navigation.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="disableEditMode" name="Disable edit mode" arity="ONE">
			<Description>Prevents users from editing the menu (Default is false).</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Display">
		<Option id="display" name="Display" arity="ONE">
			<Description>Whether to show the whole menu (both icons and labels are displayed), collapse it (icons are displayed but labels are hidden), hide it (the menu can be opened by clicking an icon), or responsively switch between these modes. </Description>
			<Values>
				<Value>Expanded</Value>
				<Value>Collapsed</Value>
				<Value>Hidden</Value>
				<Value>Responsive</Value>
			</Values>
		</Option>
		<Option id="expandIcon" name="Expand button icon">
			<Description>Set a specific icon to expand the menu. Default is 'fonticon-fast-forward'</Description>
		</Option>
		<Option id="collapseIcon" name="Collapse button icon">
			<Description>Set a specific icon to collapse the menu. Default is 'fonticon-fast-backward'</Description>
		</Option>
		<Option id="background" name="Background">
			<Description>The menu background color. Defaults to the theme value.</Description>
		</Option>
		<Option id="displaySearch" name="Display search input for pages">
			<Description>Display or not a search input allowing the user to filter the menu to find a specific page.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="showDescription" name="Show description" arity="ONE">
			<Description>Displays the item descriptions in the space between the main menu and the secondary menu items when hovering the corresponding items.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="sidebarCookie" name="Sidebar cookie">
			<Description>Specify if a cookie should be set to save open/close state of the sidebar panels. (default is true)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="display">Responsive</DefaultValue>
		<DefaultValue name="indent">0</DefaultValue>
		<DefaultValue name="expandIcon">fonticon fonticon-fast-forward</DefaultValue>
		<DefaultValue name="collapseIcon">fonticon fonticon-fast-backward</DefaultValue>
		<DefaultValue name="sidebarCookie">true</DefaultValue>
	</DefaultValues>
</Widget>
