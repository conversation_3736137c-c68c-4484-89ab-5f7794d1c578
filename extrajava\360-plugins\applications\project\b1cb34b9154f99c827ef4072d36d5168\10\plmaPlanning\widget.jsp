<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="storage" uri="http://www.exalead.com/jspapi/storage" %>

<render:import varWidget="widget" varFeeds="accessFeeds"/>
<render:import parameters="planningMode" ignore="true"/>
<c:if test="${empty planningMode}">
    <c:set var="planningMode" value="widget"/>
</c:if>

<config:getOption name="title" var="title" defaultValue=""/>
<config:getOption name="height" var="height" defaultValue=""/>
<config:getOption var="fullScreen" name="enableFullScreen"/>
<config:getOption var="displayDoc" name="displayDoc" defaultValue="false"/>
<config:getOption var="doc" name="doc" defaultValue=""/>

<request:isAjax var="isAjax"/>
<request:getParameterValue var="isDataReq" name="plmaPlanningDataReq" defaultValue="false"/>

<%-- Get First feed from the widget Feeds --%>
<search:getFeed var="widgetFeed" feeds="${accessFeeds}"/>
<c:set var="widgetFeedId" value="${widgetFeed.id}"/>
<config:getOption var="paginationFeedId" name="paginationFeedId" defaultValue="${widgetFeedId}"/>

<c:if test="${isAjax == true && isDataReq == true}">	
	<widget:getUcssId var="uCssId"/>
	<render:renderScript position="READY">
		(function(){
			<render:template template="javascript.jsp">
				<render:parameter name="uCssId" value="${uCssId}"/>
				<render:parameter name="feed" value="${widgetFeed}"/>			
				<render:parameter name="paginationFeedId" value="${paginationFeedId}"/>
			</render:template>
		})();
	</render:renderScript>	
</c:if>

<c:if test="${isAjax == false || isDataReq == false}">	
	<c:set var="heightStyle" value=""/>
	<c:if test="${not empty height}">
		<c:set var="heightStyle" value="height:${height+94}px;"/>
	</c:if>

	<widget:widget extraCss="plmaPlanning" varUcssId="uCssId" extraStyles="${heightStyle}">
		<c:set var="timelineId" value="timeline-vis-${uCssId}"/>

		<widget:header>
			<span class="widgetHeaderIcon fonticon fonticon-chart-timeline"></span>
			<div class="widgetChartHeader widgetTitle">${title}</div>
			<div class="timespan-info frame fonticon">
				<b><span class="lable"><i18n:message code='widget.plma.planning.timespan' text='Time Span'/> </span></b>
				<span class="timespan"></span>
			</div>
		</widget:header>

		<widget:content>
			<div class="main-widget">
				<div class="left-panel hidden">
					<c:if test="${displayDoc}">
						<div class="timeline-doc hidden">
							<div class="container">${doc}</div>							
						</div>
					</c:if>
					<div class="timeline-configurator hidden">
						<div class="header">
							<span class="title">Configurations</span>
							<span class="buttons">
								<span class="reset-button fonticon fonticon-reset" title="<i18n:message code='widget.plma.planning.reset.config' />"></span>
								<span class="save-button fonticon fonticon-floppy" title="<i18n:message code='widget.plma.planning.save.config' />"></span>
							</span>
						</div>
						<div class="custom-config">						
						</div>
					</div>
					<span class="close-button fonticon fonticon-cancel" title="<i18n:message code='generic.close' />"></span>
				</div>
				<div class="timeline-vis" id="${timelineId}"></div>
				<div class="right-panel hidden">
					<div class="timeline-info visible">
						<c:set var="detailUCSSId" value=""/>
						<c:if test="${widget:hasSubWidgets(widget)}">
							<widget:forEachSubWidget>
								<widget:getUcssId var="detailUCSSId"/>							
								<render:widget/>
							</widget:forEachSubWidget>
						</c:if>
					</div>
					<span class="close-button fonticon fonticon-cancel" title="<i18n:message code='generic.close' />"></span>
				</div>
			</div>
			<div id="footer-${uCssId}" class="timelineFooter">
				<div class="left-panel-button">
					<span class="configurator-button fonticon fonticon-cog"></span>
					<c:if test="${displayDoc}">
						<span class="timelinedoc-button fonticon fonticon-help"></span>					
					</c:if>
				</div>
				<div class="loadData" data-currentpage="0">
					<span class="label fonticon"></span>
					<span class="loadNextDataButton fonticon fonticon-play" title="<i18n:message code='widget.plma.planning.loadNext' text='Load Next'/>"></span>
					<span class="loadAllDataButton fonticon fonticon-play-next" title="<i18n:message code='widget.plma.planning.loadAll' text='Load All' />"></span>
				</div>
				
				<div class="moveButtons buttonsContainer">
					<span class="actionButton moveLeftButton fonticon fonticon-left"></span>
					<span class="actionButton moveRightButton fonticon fonticon-right"></span>
				</div>
				<div class="zoomButtons buttonsContainer">
					<span class="actionButton zoomOutButton fonticon fonticon-minus"></span>
					<span class="actionButton fitButton fonticon fonticon-resize-fullscreen "></span>
					<span class="actionButton zoomInButton fonticon fonticon-plus"></span>
				</div>
			</div>
		</widget:content>

		<render:renderScript position="READY">
		(function(){
			let buttonOptions = {};
			buttonOptions.buttons = [];
			<c:if test="${fullScreen == 'true'}">
				var elem = {};
				elem.icon = FullScreenWidget.BASE_ICON_CSS_CLASS + ' ' + FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS;
				elem.label = FullScreenWidget.getMessage('widget.action.fullScreen');
				buttonOptions.buttons.push(elem);
			</c:if>
						
			<config:getOption name="additionalParams" var="additionalParams" defaultValue=""/>
			<config:getOption var="timelineConfigOpts" name="opts" defaultValue="{}"/>
			<config:getOptionsComposite var="groupsDef" name="groupsDef" mapIndex="true" doEval="false" defaultValue=""/>
			<config:getOptionsComposite var="actions" name="actions" defaultValue="" doEval="true" mapIndex="true"/>
			<config:getOption var="onInit" name="onInit" defaultValue="function(){}" />
			<config:getOption var="filterUrlParams" name="filterUrlParams" defaultValue="function(){}" />
			
			<url:getPageName var="pageName"/>
			<request:getParameterValue var="pageId" name="pageId" defaultValue="${pageName}"/>
			<c:set var="uniqueKey" value="${pageId}_${uCssId}"/>
			<storage:getUserValue var="userOptions" key="plmaPlanningConf[]" uniqueKey="${uniqueKey}"/>
			
			let userOptions = {};
			<c:if test="${userOptions != null}">
				userOptions = ${userOptions};
			</c:if>
			let actions = [];
			<c:forEach var="action" items="${actions}">
				actions.push({
					name: '${action.name}',
					handler: ${action.function}
				});
			</c:forEach>
			
			let plmaPlanning = new PLMAPlanningNew('${uCssId}',{
				buttons: buttonOptions,
				hasGroups: ${not empty groupsDef},
				timelineConfigOpts: ${timelineConfigOpts},
				userOptions: userOptions,
				wuid: '${widget.wuid}',
				paginationFeed: '${paginationFeedId}',
				hitsPerPage: <config:getOption name="hitsPerPage" defaultValue="10"/>,
				forceFirstLoad: <config:getOption name="forceFirstLoad" defaultValue="true"/>,
				loadUrl: '<url:url keepQueryString="true" xmlEscape="false"/>',
				loadUrlFilter: ${filterUrlParams},
				loadLabel: '<i18n:message code="widget.plma.planning.pages"/>',
				actions: actions,
				additionalParams:  '${additionalParams}',
				detailsWidgetId: '${detailUCSSId}',
				uniqueKey: '${uniqueKey}',
				baseAjaxReload: '<c:url value="/plma/ajax/planning"/>/<search:getPageName/>/${planningMode}',
			});

			<c:if test="${search:hasEntries(accessFeeds) == true}">
				<render:template template="javascript.jsp">
					<render:parameter name="uCssId" value="${uCssId}"/>
					<render:parameter name="feed" value="${widgetFeed}"/>
					<render:parameter name="paginationFeedId" value="${paginationFeedId}"/>					
				</render:template>
			</c:if>
			(${onInit})(plmaPlanning);
		})();
		</render:renderScript>		
	</widget:widget>
</c:if>