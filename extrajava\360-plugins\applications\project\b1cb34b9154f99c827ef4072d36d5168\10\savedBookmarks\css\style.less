@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/polyfills.less";

.mashup {

	.bookmarkConf{
	    padding: 1em;
	}

	.bookmark_param{
		margin: 15px 15px 0 15px;

		h3{
		    margin-bottom: 15px;
		}

		.empty_span{
	    	display: none;
	    }

	    .empty_error{
	    	display: block;
		    color: #CC092F;
		    text-align: center;
		    margin-top: 10px;
	    }

	    .name_div, .desc_div{
	    	margin-top: 5px;
		    text-align: right;
	    }
	    
	    .buttons_div{
	    	margin-top: 10px;
    	    text-align: center;
	    }
	   
	    .btn {
	        cursor: pointer;
		    color: @ctext;
		    background-color: #F1F1F1;
		    background-image: none;
		    font-size: 12px;
		    border: 1px solid @ctext-weak;
		    border-radius: 4px;
		    padding: 5px 10px;
		    margin-left: 5px;
   			width: 80px;
		}

		.bm_name{
		    border: 1px solid #ccc;
		    border-radius: 4px;
		    height: 20px;
		    vertical-align: middle;
		    padding-left: 5px;
		}

		.bm_desc{
			width: 167px;
		    resize: none;
		    border: 1px solid #ccc;
		    border-radius: 4px;
		    vertical-align: middle;
		    padding-left: 5px;
		}

		.bm_desc:focus{
			outline: none;
		}

	    .red_border.bm_name{
			border: 2px solid #CC092F;
	    }

		.active-btn:hover {
		    background: @clink;
		    color: white;
		    border: 1px solid @clink;
		}

		.inactive-btn{
			cursor: initial;
			background: @ctext-weak;
		}
	}

	.savedBookmarks-list {
		ul {
			padding : 3px 0px 3px 0px;
		}
		
		li.empty-data{
			margin-left: 5px;
		}
		
		li.item {
			padding: 0 (@line-height /2);
		    text-align: left;
		    line-height: (2 * @line-height);
	        border-bottom: 1px solid @cblock-border;
	        -webkit-transition: height 0.4s;
	        transition: height 0.4s;

			a {
				text-decoration : none;
				&:hover{
					text-decoration : none;
					color: @clink-hover;
					background-color: @clink-bg-hover;
				}

				&.opened{
		        	white-space: normal;
	    			line-height: (@line-height * 1.5);
				    padding-top: 3.5px;
				    padding-bottom: 3.5px;
	        	    text-overflow: initial;
		        }
			}
	
			&:hover {
				.cross{
					visibility:visible;
				}
			}

			.arrow, .conf{
				vertical-align: top;
				cursor: pointer;
				float: right;
				.flex(0 0 (2* @line-height));
				
				&:hover{
                    color: @clink-hover;
                    background-color: @cblock-border;
                }
			}

			.cross {
				visibility:hidden;
				vertical-align: top;
				float: right;
			    font-size: 11px;
				&:hover{
					color: #AE0000;
					text-decoration: none;
					cursor : pointer;
				}
			}
		}
    
        .hitHeader {
            .display-flex();
            .flex-flow(row nowrap);
            align-items: flex-start;
        }
        
		.hit-name{
			display: inline-block;
		    text-overflow: ellipsis;
		    overflow: hidden;
		    font-size: @m-font;
		    line-height: 2 * @line-height;
	  		color : @clink;
	  		padding: 0 (@line-height/2);
	  		.flex(1 0 0);
		}

		.hitDetails{
	        margin-bottom: 5px;
	        padding: 0 (@line-height /2);
            white-space: normal;

		    .filtre{
		    	white-space: normal;
	    	    margin-left: 0;
			    display: block;
			    line-height: 15px;
			    overflow: hidden;
		        cursor: default;
	            padding: 3.5px 3.5px 3.5px 7px;
	            pointer-events: none;
		    }

		    .activeFiltersContainer{
		    	padding: 0;
		    }
		}
		
		.hidden{
			display: none;
		}

		.hitDesc{
			display: block;
		    line-height: 15px;
		    margin-bottom: 5px;
		    padding-left: 2px;
		}
	}
}