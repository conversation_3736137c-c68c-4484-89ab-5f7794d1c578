<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Flip container" group="PLM Analytics/Layout/Hide and show" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description> A container widget that allows the user to switch between two subwidgets.</Description>

	
	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/flip.js" />
		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js"/>
		<Include type="js" path="../plmaResources/js/plmaSpinner.js"/>
		<Include type="js" path="../plmaResources/js/widgetButtonManager.js" />
		<Include type="js" path="../plmaResources/js/fullScreenWidget.js" />
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportWidgetsId arity="ZERO_OR_MANY"/>
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportI18N supported="true" >
		<JsKeys>
			<JsKey>widget.flipContainer.error.server</JsKey>
			<JsKey>widget.flipContainer.menu.fullsize</JsKey>
		</JsKeys>
	</SupportI18N>
	
	<OptionsGroup name="General">
		<Option id="firstButtonIcon" name="First button icon" arity="ONE">
			<Description>Specifies the class name of the icon added to the button that is displayed on the first subwidget.</Description>
		</Option>
		<Option id="firstButtonTitle" name="First button title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>The title for the button of the first subwidget. It is displayed as a tooltip when hovering over the button.</Description>
		</Option>
		<Option id="firstButtonCssSelector" name="First button css selector" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>The css selector to specify where to place the button on the first subwidget. Default is '.widgetHeader'</Description>
		</Option>
		<Option id="secondButtonIcon" name="Second button icon" arity="ONE">
			<Description>Specifies the class name of the icon added to the button that is displayed on the second subwidget.</Description>
		</Option>
		<Option id="secondButtonTitle" name="Second button title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>The title for the button of the second subwidget. It is displayed as a tooltip when hovering over the button.</Description>
		</Option>
		<Option id="secondButtonCssSelector" name="Second button css selector" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>The css selector to specify where to place the button on the second subwidget. Default is '.widgetHeader'</Description>
		</Option>
		<Option id="enableFullScreen" name="Enable full screen" arity="ONE">
			<Description>Allows the user to resize the widget to full screen. &lt;br/&gt;
                &lt;b&gt;WARNING&lt;/b&gt;:
                Full screen option must be disabled from sub-widgets to allow the flip container widget to handle this behavior appropriately.
            </Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>
	<OptionsGroup name="Ajax">
		<Option id="activateAsynch" name="Render second widget only on click" arity="ONE">
			<Description>Allows you to load the hidden widget only on first click on switch button.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<OptionComposite id="additionalParams" name="Additional parameters" arity="ZERO_OR_MANY">
			<Description>Additionnal URL parameters to be sent when the hidden widget is called through Ajax.</Description>
			<Option id="paramName" name="Name" arity="ONE">
				<Description>The name of the parameter.</Description>
				<Functions>
					<ContextMenu>PageParameters()</ContextMenu>
				</Functions>
			</Option>
			<Option id="paramValue" name="Value" arity="ZERO_OR_ONE" isEvaluated="true">
				<Description>The value to send for this parameter. Can be a MEL expression.</Description>
			</Option>
		</OptionComposite>
	</OptionsGroup>
	<OptionsGroup name="Style">
		<Option id="height" name="Height" arity="ZERO_OR_ONE">
			<Description>The height of the tabs content (in pixels). Leave empty if you want it to adapt to inner widgets.</Description>
		</Option>
		<Option id="minwidth" name="Min Width">
			<Description>Specifies the minimum width of the widget (pixels). You must enter an integer.</Description>		
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
	</OptionsGroup>

</Widget>
