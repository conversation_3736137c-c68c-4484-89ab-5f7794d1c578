<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<render:import varWidget="widget" varFeeds="feeds" />


<widget:widget disableStyles="true" varCssId="cssId" varUcssId="uCssId" extraCss="stickyContainer">
	<widget:forEachSubWidget >
		<render:widget />
	</widget:forEachSubWidget>

	<render:renderScript position="READY">
		new StickyTop('${uCssId}');
	</render:renderScript>
</widget:widget>