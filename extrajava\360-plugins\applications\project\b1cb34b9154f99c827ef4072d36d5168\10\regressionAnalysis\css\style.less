@import "../../plmaResources/css/styles/variables.less";
@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/polyfills.less";

@margin: 10px;
@width: 50%;

.regressionAnalysis {
	position: relative;
	display: flex;
	flex-direction: column;
	height: 100%;
	
	.hidden {
		display: none;
	}
	
	input[type="number"] {
		width: 100%;
	}
	
	/* TODO find a better way to fix this : when inside a Chartboard, the header is squeezed */
	.widgetHeader{
		.flex(0 0 auto);
	}
	
	.regression-container {
		padding: 10px;
	}
	
	.regression-menu {
		width: @width;
		float: left;
	}
	
	.regression-chart {
		width: @width;
		float: right;
	}
	
	.regression-info {
		padding: 10px;
		
		tr {
			white-space: nowrap;
		}
		
		th {
			width: 200px;
			text-align: left;
			display: inline-block;
		}
		
		td {
			text-align: right;
		}
	}
	
	.regression-warning {
		padding: 10px;
		width: 300px;
	}
	
	.buttons-group {
		float: right;
	}
	
	.regression-tooltip-button {
		margin-top: 10px;
		font-size: 20px;
	}

	.form-control {
		padding: 1px 0px 0px 3px;
	}

	@media screen and (max-width: 768px) {
		.regression-menu, .regression-chart {
			width: auto;
			float: none;
		}
	}
	
	.regression-table {
		margin-bottom: 0px;
		width: auto;
		
		thead, tbody {
			display: block;
		}
		
		thead {
			background-color: @clink;
		}
		
		tbody {
			overflow-y: auto;
		}
		
		th {
		    color: white;
		}

		.toggle {
			margin: 0;
			position: relative;
			top: -7px;
			right: -4px;
		}
	}

	.regression-type {
		margin-top: 10px;

		input[type="number"] {
			display: inline-block;
			height: 11px;
			vertical-align: text-bottom;
			padding: 4px 2px;

			&.regression-polynomial-order {
				width: 30px;
			}

			&.regression-loess-smooth {
				width: 40px;
			}
		}
	}
	
	.regression-lightbox {
		padding: @margin;
		min-width: 200px;
		
		form {
			margin-top: @margin !important;
		}
		
		.table {
			margin-top: @margin;
			margin-bottom: 0px;
		}
	}

	form[name="model-form"] {
		input[name="name"] {
			position: relative;
			top: 2px;
			display: inline-block;
			width: 200px;
		}
		button[type="submit"] {
			padding: 3px 14px;
		}
	}

	form[name="regression-settings"] {
		button[type="submit"] {
			margin-top: 10px;
		}
	}
}