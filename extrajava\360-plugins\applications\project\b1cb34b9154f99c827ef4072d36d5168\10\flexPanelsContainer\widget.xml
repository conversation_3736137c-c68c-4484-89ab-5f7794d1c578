<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Flex panels container" group="PLM Analytics/Layout/Flex display" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>This widget is a container that displays panels using CSS flexbox.</Description>
	
	<Includes>
    	<Include type="css" path="css/style.less" />
    </Includes>
    
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportWidgetsId arity="ZERO_OR_MANY" displayType="LAYOUT" />

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<OptionsGroup name="General">
		<Option id="width" name="Width" arity="ZERO_OR_ONE">
			<Description>Specifies the table width. Defaults to '100'.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="widthFormat" name="Width format" arity="ZERO_OR_ONE">
			<Description>Specifies the width format of the table. Defaults to '%'.</Description>
			<Values>
				<Value></Value>
				<Value>%</Value>
				<Value>px</Value>
			</Values>
		</Option>
		<Option name="Wrap" id="wrap" arity="ONE">
			<Description>Allows panels to be placed on several rows if the available width is too small.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<OptionComposite id="variables" name="Additional CSS variables" arity="ZERO_OR_MANY" glue="##" isEvaluated="true">
			<Description>Add variables to widget div, it can be useful to configure CSS using variables for example.</Description>
			<Option id="name" name="Name" arity="ONE" isEvaluated="true">
				<Description>Variable name to add.</Description>
				<Functions>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="value" name="Value" arity="ONE" isEvaluated="true">
				<Description>Aggregation function expression.</Description>
				<Functions>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
		</OptionComposite>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="wrap">false</DefaultValue>
	</DefaultValues>
</Widget>
