var DetailHitHelper = function (responsive) {
	this.responsive = responsive;
};

DetailHitHelper.prototype.loadDetail = function (parameterName, parameterValue) {
	$(window).trigger('plma:panel-detail-reload', [parameterName, parameterValue]);
	this.openDetailPanel();
	if (this.responsive) {
		this.displayTileView();
	}
};

DetailHitHelper.prototype.loadDetailFromAnchor = function (anchor,parameter) {
	anchor = decodeURIComponent(anchor);
	var $hit = $('li.hit[data-uri="' + anchor + '"]');
	var paramValue = anchor;
	var paramName = parameter;
	if ($hit.length > 0) {
		paramValue = $hit.data('uri');
		paramName = $hit.data('parameter');
	}
	setTimeout($.proxy(function () {
		this.loadDetail(paramName, paramValue);
		this.openDetailPanel();
	}, this),500);
};

DetailHitHelper.prototype.selectHit = function (hit) {
	$('.hit.selected').removeClass('selected');
	hit.addClass('selected');
};

DetailHitHelper.prototype.deselectHit = function () {
	var $resultList = $('.plmaResultList');
	var resultList = $resultList.data('widget');
	if (resultList && resultList.options.selectionConfig.enablePublication) {
		resultList.setSelectedHits([]);
	}
	$('.hit.selected').removeClass('selected');
};

DetailHitHelper.prototype.selectHitFromAnchor = function (anchor, $widget) {
	anchor = decodeURIComponent(anchor);
    var $hit = undefined;
	if($widget != undefined){
		$hit = $widget.find('li.hit[data-uri="' + anchor + '"]').addClass('selected');
	}else{
        $hit = $('li.hit[data-uri="' + anchor + '"]').addClass('selected');
	}
	return $hit;
};

DetailHitHelper.prototype.openDetailPanel = function () {
	var detailPanel = $('.detail-panel');
	if (detailPanel.hasClass('hidden')) {
		detailPanel.removeClass('hidden');
	}
	var resultPanel = $('.result-panel');
	resultPanel.addClass('no-hit-selected');
};

DetailHitHelper.prototype.closeDetailPanel = function () {
	var detailPanel = $('.detail-panel');
	if (!detailPanel.hasClass('hidden')) {
		detailPanel.addClass('hidden');
	}
	var resultPanel = $('.result-panel');
	resultPanel.removeClass('no-hit-selected');
};

DetailHitHelper.prototype.pushState = function (parameterValue) {
	if (!(typeof history.pushState === 'undefined')) {
		var url = document.location.href.split('#')[0];
		if (parameterValue) {
			url += "#" + parameterValue;
		}

		var stateObj = {};
		window.history.pushState(stateObj, "myPage", url);
	}
};

DetailHitHelper.prototype.displayTableView = function($resultList) {
	if($resultList  == undefined){
		$resultList = $(".plmaResultList");
	}
	$resultList.find('.hits').removeClass('tile-layout').addClass('table-layout');
	$resultList.find('.resultsTitle').removeClass('tile-layout').addClass('table-layout');
};

DetailHitHelper.prototype.displayTileView = function($resultList) {
	if($resultList == undefined){
		$resultList = $(".plmaResultList");
	}
	$resultList.find('.hits').removeClass('table-layout').addClass('tile-layout');
	$resultList.find('.resultsTitle').removeClass('table-layout').addClass('tile-layout');
};

/**
 * Initialize responsive view
 * @param cssId - init responsive on a specific uCssId
 */
DetailHitHelper.prototype.initResponsive = function (uCssId) {
	this.displayRightView(uCssId);
	$(window).on('plma:hitSelection', $.proxy(function () {
		this.displayRightView(uCssId);
	}, this));
	window.addEventListener('resize', $.proxy(function () {
		this.displayRightView(uCssId);
	}, this));
};

DetailHitHelper.prototype.displayRightView = function (uCssId) {
    var $resultList = $('.plmaResultList');
    if(uCssId !== undefined){
        $resultList = $('.'+uCssId);
    }
    //In case if selection tab or hit tab is visible result list should be tile mode
	var hasHit = ($('.detail-panel').length >0 ? !$('.detail-panel').hasClass('hidden') : false);
	var hasSelection = ($('.selection-panel').length >0 ? !$('.selection-panel').hasClass('hidden') : false);
	(hasSelection || hasHit) ? this.displayTileView($resultList) : this.displayTableView($resultList);
};