<?xml version="1.0" encoding='UTF-8'?>
<Widget name="iiTreeMap" group="PLM Analytics/Visualizations/Charts" premium="true">

	<Description>A treemap is a representation of hierarchical data using nested rectangles.</Description>

	<Preview>
		<![CDATA[
            <img src="/resources/widgets/iiTreeMap/images/preview.PNG" alt="Tree Map" />
        ]]>
	</Preview>

	<DefaultValues>
		<DefaultValue name="height">350</DefaultValue>
		<DefaultValue name="dataProcessor"><![CDATA[
function(data) {
	return data;
}
]]></DefaultValue>
	</DefaultValues>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>

	<Includes>
        <Include type="css" path="css/style.less" />
		<Include type="js" path="../plmaResources/js/react.production.min.js" />
		<Include type="js" path="../plmaResources/js/react-dom.production.min.js" />
		<Include type="js" path="../plmaResources/js/d3.min.js" />
		<Include type="js" path="../plmaResources/js/iiCharts.js" />
		<Include type="js" path="js/treemap.js" />
	</Includes>
	
	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="MANY" />
	<SupportI18N supported="true" />

	<OptionsGroup name="General">
		<Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
		</Option>
		<Option id="facet" name="Facet" arity="ONE">
			<Description>Specifies the facet that will be used by the treemap.</Description>		
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="onClick" name="On click" isEvaluated="true">
			<Description>The JavaScript code that is executed when a rectangle is clicked. Event is passed as a parameter, second argument is data about the rectangle.</Description>
			<Placeholder>function(e, node) {}</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="dataProcessor" name="Data format" isEvaluated="true">
			<Description>Pre-processing function that formats or transforms the data.</Description>
			<Placeholder>function(data) { return data }</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="customColors" name="Custom colors" isEvaluated="true">
			<Description>Array of node's object. Node must have a property to filter, nodes and a color. [{ property: 'id', nodes: ['NOT_INVESTIGATED'], color: '#777'}]</Description>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="highlight" name="Highlight" isEvaluated="true">
			<Description>Highlight a node. Node to filter { property: 'id', nodes: ['NOT_INVESTIGATED']}</Description>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
        <Option id="displayLabel" name="Display treemap labels">
            <Description>Specify if treemap labels should be displayed. If 'false', labels are displayed on hover.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>
        <OptionComposite id="displayLabelList" name="Display specific treemap labels">
            <Description>Specify a list of labels that should always be displayed in the treemap</Description>
            <Option id="labelName" name="Label name" arity="ZERO_OR_MANY" isEvaluated="true">
                <Description>Specify the label name to display</Description>
            </Option>
            <Option id="labelAlias" name="Label alias" isEvaluated="true">
                <Description>Specify an alias for the corresponding label (mel syntax allowed). Each label will be replaced by the alias if not blank.</Description>
            </Option>
        </OptionComposite>
	</OptionsGroup>

	<OptionsGroup name="Style">
		<Option id="width" name="Width">
			<Description>Specifies the widget width (pixels). You must enter an integer.</Description>		
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="height" name="Height">
			<Description>Specifies the widget height (pixels).  If no integer is given, the chart will take 100% of the available height.</Description>			
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
	</OptionsGroup>

    <DefaultValues>
        <DefaultValue name="DisplayLabel">false</DefaultValue>
    </DefaultValues>

</Widget>
