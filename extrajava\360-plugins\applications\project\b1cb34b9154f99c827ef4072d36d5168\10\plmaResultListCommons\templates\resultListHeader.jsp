<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds"/>
<render:import parameters="defaultView,parentFeed,parentEntry" ignore="true"/>
<render:import
        parameters="wuid,columnsConfig,layoutName,recursiveButtonsWidgets,directButtonsWidgets,buttonsIconSize,ajaxSort,enableExport,channelNames,enablePublication,enableSubscription"/>

<c:if test="${empty defaultView}">
    <c:set var="defaultView" value="tiles"/>
</c:if>

<search:getFeed var="rf" feeds="${feeds}"/>
<search:getFeedInfo name="searchAPIUrl" feed="${rf}" var="sapiURL"/>
<plma:debugObject prefix="SAPI URL" object="${sapiURL}"/>

<request:getParameterValue var="displayView" name="displayView" defaultValue="${defaultView}"/>
<c:set var="viewCollectionParam" value="${plma:getStringParam(widget, 'urlParameter', 'viewCollection')}"/>
<request:getParameterValue var="viewCollection" name="${viewCollectionParam}"/>

<%-- Get selected view widget, it can be 'widget' variable itself if not instanciated by result list layout so set default value widget --%>
<c:set var="selectedViewWidget" value="${plma:getSubWidgetWithParam(widget, 'displayViewId', displayView ,widget )}"/>
<%-- Get result list ID and preference tab from selected view but not from layout if instanciated by layout --%>
<config:getOption name="resultListId" var="resultListId"/>
<%--<c:set var="resultListId" value="${plma:getStringParam(selectedViewWidget, 'resultListId', '')}"/>--%>
<c:set var="displayPrefs" value="${!plma:getBooleanParam(selectedViewWidget, 'disableResultListPrefTab', true)}"/>

<div class="resultListHeaderWithToolbar widgetHeaderWithToolbar ${layoutName}">
    <div class="headerTitle">
        <%-- Generate drag icon if 3dxp share is activated --%>
        <c:if test="${plma:getBooleanParam(widget, 'enableShareAll', false)}">
            <span class="fonticon fonticon-drag-grip drag-all-hits draggable" title="Drag&drop all items in other widget"/>
        </c:if>

        <%-- Generate title part (by default number of documents) --%>
        <c:choose>
            <c:when test="${plma:hasParam(selectedViewWidget, 'titleTemplatePath')}">
                <%-- Get template source and path and execute template --%>
                <render:template
                        template="${plma:getStringParam(selectedViewWidget, 'templateBasePath', '')}${plma:getStringParam(selectedViewWidget, 'titleTemplatePath', '')}"
                        widget="${plma:getStringParam(selectedViewWidget, 'titleTemplateWidget', selectedViewWidget.id)}">
                    <render:parameter name="viewWidget" value="${selectedViewWidget}"/>
                </render:template>
            </c:when>
            <c:when test="${plma:hasTemplate(pageContext, selectedViewWidget, 'templates/resultListTitle.jsp')}">
                <%-- If widget implements title view, use this template --%>
                <render:template template="templates/resultListTitle.jsp" widget="${selectedViewWidget.id}">
                    <render:parameter name="viewWidget" value="${selectedViewWidget}"/>
                </render:template>
            </c:when>
            <c:otherwise>
                <%-- By default, use standard result list title in commons widget --%>
                <render:template template="templates/resultListTitle.jsp" widget="plmaResultListCommons">
                    <render:parameter name="viewWidget" value="${selectedViewWidget}"/>
                    <c:choose>
                        <c:when test="${plma:getBooleanParam(widget, 'enableCompare', false) && plma:getStringParam(widget, 'compareView', 'compare') == viewCollection }">
                            <render:parameter name="viewIcon" value="fonticon-compare-on"/>
                        </c:when>
                        <c:when test="${plma:getBooleanParam(widget, 'enablefavorites', false) && plma:getStringParam(widget, 'favoritesCollection', 'favoriteHits') == viewCollection }">
                            <render:parameter name="viewIcon" value="fonticon-favorite-on"/>
                        </c:when>
                        <c:otherwise>
                            <render:parameter name="viewIcon" value=""/>
                        </c:otherwise>
                    </c:choose>
                </render:template>
            </c:otherwise>
        </c:choose>
    </div>

    <div class="headerToolbar" style="${plma:format('--icons-size:%spx;', buttonsIconSize, '')}">
        <c:if test="${plma:getBooleanParam(widget, 'enableSortButton', true) && plma:hasSortableColumn(columnsConfig)}">
            <render:template template="templates/sortDisplay.jsp" widget="plmaResultListCommons">
                <render:parameter name="feeds" value="${feeds}"/>
                <render:parameter name="columnsConfig" value="${columnsConfig}"/>
            </render:template>
            <span class="insert-after"></span>
        </c:if>

        <c:if test="${plma:getBooleanParam(widget, 'enableCompare', false)}">
            <render:template template="templates/collectionHeaderButton.jsp" widget="plmaResultListCommons">
                <render:parameter name="collection" value="${plma:getStringParam(widget, 'compareCollection', 'compare')}"/>
                <render:parameter name="buttonPrefix" value="fonticon-compare"/>
                <render:parameter name="actionName" value="compare"/>
                <render:parameter name="collectionMode" value="${plma:getStringParam(widget, 'compareMode', 'trigger')}"/>
                <render:parameter name="displayDeleteButton" value="${plma:getBooleanParam(widget, 'enableCompareDelete', true)}"/>
                <render:parameter name="displayView" value="${plma:getStringParam(widget, 'compareView', 'compare')}"/>
            </render:template>
            <span class="insert-after"></span>
        </c:if>

        <c:if test="${plma:getBooleanParam(widget, 'enablefavorites', false)}">
            <render:template template="templates/preferredHits.jsp" widget="preferredHits">
                <render:parameter name="widget" value="${widget}"/>
                <render:parameter name="uCssId" value="${wuid}"/>
                <render:parameter name="urlParameter" value="${viewCollectionParam}"/>
                <render:parameter name="compactHeader" value="${plma:getBooleanParam(widget, 'compactHeader', true)}"/>
                <render:parameter name="collection" value="${plma:getStringParam(widget, 'favoritesCollection', 'favoriteHits')}"/>
                <render:parameter name="buttonPrefix" value="fonticon-favorite"/>
                <render:parameter name="actionName" value="favorites"/>
                <render:parameter name="collectionMode" value="${plma:getStringParam(widget, 'favoriteMode', 'trigger')}"/>
                <render:parameter name="displayDeleteButton" value="${plma:getBooleanParam(widget, 'enableFavoritesDelete', true)}"/>
            </render:template>
            <span class="insert-after"></span>
        </c:if>

        <c:if test="${plma:getBooleanParam(widget, 'enableExport', false)}">
            <render:template template="templates/exportDisplay.jsp" widget="plmaResultListCommons">
                <render:parameter name="columnsConfig" value="${resultListId}"/>
                <render:parameter name="buttonWidgetCssId" value="${plma:getWidgetUID(widget)}"/>
            </render:template>
            <span class="insert-after"></span>
        </c:if>

        <c:if test="${not empty recursiveButtonsWidgets}">
            <%--
                Displays sub-widgets header widgets (except plma buttons)
                If header generated from view: simply get related widgets
                If header from layout (without 'displayViewId' attribute), filter from this view
            --%>
            <c:choose>
                <c:when test="${plma:hasParam(widget, 'displayViewId')}">
                    <c:if test="${plma:hasSubWidgetRecurse(widget, recursiveButtonsWidgets)}">
                        <plma:forEachSubWidget includes="${recursiveButtonsWidgets}" recurse="false" feed="${parentFeed}" entry="${parentEntry}">
                            <%-- Render actions subwidgets in header --%>
                            <render:widget/>
                        </plma:forEachSubWidget>
                    </c:if>
                </c:when>
                <c:otherwise>
                    <%-- Get header buttons from displayed view only --%>
                    <c:if test="${plma:hasSubWidgetRecurse(widget, recursiveButtonsWidgets)}">
                        <plma:forEachSubWidget widgetContainer="${selectedViewWidget}" includes="${recursiveButtonsWidgets}" recurse="false" feed="${parentFeed}" entry="${parentEntry}">
                            <%-- Render actions subwidgets in header --%>
                            <render:widget/>
                        </plma:forEachSubWidget>
                    </c:if>
                </c:otherwise>
            </c:choose>
        </c:if>

        <c:if test="${not empty directButtonsWidgets}">
            <%-- Displays sub-widgets header widgets --%>
            <c:if test="${plma:hasSubWidget(widget, directButtonsWidgets)}">
                <plma:forEachSubWidget includes="${directButtonsWidgets}" recurse="false">
                    <%-- Render actions subwidgets in header --%>
                    <render:widget/>
                </plma:forEachSubWidget>
            </c:if>
        </c:if>

        <c:if test="${plma:getBooleanParam(selectedViewWidget, 'descriptionButton', true)}">
            <span class="display-description-button">
                <config:getOption name="hitContentDisplay" var="hitContentDisplay" defaultValue="false"/>
                <request:getCookieValue name="description_result_list${uCssId}" var="displayDescription" defaultValue="${hitContentDisplay}"/>
                <span class="icon icon-visible fonticon fonticon-eye-off ${displayDescription == 'true' ? '' : 'hidden'}"
                      title="<i18n:message code='hitcustom.column.description.hide'/>"></span>
                <span class="icon icon-hidden fonticon fonticon-eye ${displayDescription == 'true' ? 'hidden' : ''}"
                      title="<i18n:message code='hitcustom.column.description.display'/>"></span>
                <span class="label label-visible ${displayDescription == 'true' ? '' : 'hidden'}">
                    <i18n:message code="hitcustom.column.description.hide"/>
                </span>
                <span class="label label-hidden ${displayDescription == 'true' ? 'hidden' : ''}">
                    <i18n:message code="hitcustom.column.description.display"/>
                </span>
            </span>
        </c:if>

        <c:if test="${not plma:hasParam(widget, 'displayViewId')}">
            <%--  In this case, header is generated by layout, so we call header template from view subwidget --%>
            <%-- Render specific view header (if template 'templates/layoutHeader.jsp' available) --%>
            <plma:forEachSubWidgetWithParam param="displayViewId" value=".*" recurse="true" feed="${parentFeed}" entry="${parentEntry}">
                <render:import varWidget="viewWidget"/>
                <render:hasTemplate var="hasLayoutHeader" template="templates/layoutHeader.jsp" widget="${plma:getWidgetID(viewWidget)}"/>
                <c:if test="${plma:getStringParam(viewWidget, 'displayViewId', '') == displayView && hasLayoutHeader }">
                    <span class="insert-before"></span>
                    <%-- Render view header template --%>
                    <render:template template="templates/layoutHeader.jsp" widget="${plma:getWidgetID(viewWidget)}">
                        <render:parameter name="viewWidget" value="${viewWidget}"/>
                        <render:parameter name="parentWidgetID" value="${wuid}"/>
                    </render:template>
                </c:if>
            </plma:forEachSubWidgetWithParam>
        </c:if>

        <%--        Render view switch buttons --%>
        <plma:forEachSubWidgetWithParam param="displayViewId" value=".*" recurse="true" feed="${parentFeed}" entry="${parentEntry}">
            <render:import varWidget="viewWidget"/>
            <c:if test="${plma:getStringParam(viewWidget, 'displayViewId', '') != displayView && !plma:getBooleanParam(viewWidget, 'hideHeaderIcon', false)}">
                <span class="insert-before"></span>
                <%-- Render view button and title --%>
                <url:url var="url" keepQueryString="true">
                    <c:forEach var="paramToRemove" items="${plma:getParameterValues(selectedViewWidget, 'removeParamsOnLeave')}">
                        <url:parameter name="${paramToRemove}" value="" override="true"/>
                    </c:forEach>
                    <url:parameter name="displayView" value="${plma:getStringParam(viewWidget, 'displayViewId', '')}" override="true"/>
                </url:url>
                <string:eval string="${plma:getStringParam(viewWidget, 'displayViewTitle', '')}" var="viewTitle"/>
                <span class="action-switch-listtable" onclick="window.location.replace('${url}')" title="${viewTitle}" style="cursor: pointer;">
                    <span class="fonticon ${plma:getStringParam(viewWidget, 'displayViewIcon', '')}"></span>
                </span>
            </c:if>
        </plma:forEachSubWidgetWithParam>

        <c:if test="${displayPrefs}">
            <span class="insert-before"></span>
            <span class="action-display-settings" title="<i18n:message code='plma.resultlist.tab.title'/>">
                <span class="fonticon fonticon-cog"></span>
            </span>
        </c:if>

        <%-- Display close button if applicable in current view (can be based on 'displayView' or 'viewCollection' attribute) --%>
        <c:set var="diaplayCloseButton" value="${plma:containsParameterValue(widget, 'closeButtonOnViews', displayView) || plma:containsParameterValue(widget, 'closeButtonOnViews', viewCollection)}"/>
        <c:if test="${diaplayCloseButton}">
            <span class="insert-before"></span>
            <plma:getURL var="defaultPageURL" profiles="resultListViews"/>
            <span class="fonticon fonticon-cancel" id="closeCurrentResultView" data-url="${defaultPageURL}"></span>
        </c:if>
    </div>
</div>

<render:renderScript position="READY">
    var options = {};
    options['ajaxSorting'] = ${ajaxSort};
    options.feedsName =  ${plma:getKeys(feeds, '[]')};
    options.enableExport = ${enableExport};
    options.selectionConfig = {
    topicNames:  ${plma:toJSArray(channelNames)},
    enablePublication: ${enablePublication},
    enableSubscription: ${enableSubscription}
    }
    var plmaResultList = new PlmaResultList('${wuid}', options);
    <c:if test="${displayPrefs}">
        <plma:resultListJSONConfig var="resultListJSON" resultListId="${resultListId}"/>
        var prefOptions = {
        buttonSelector: '.resultListHeaderWithToolbar .headerToolbar .action-display-settings'
        };
        var prefObj = new PlmaResultListPref('${uCssId}', prefOptions, ${resultListJSON});
        $(prefOptions.buttonSelector).on('click', function (e) {
        prefObj.$elements.LIGHTBOX.show();
        }.bind(this));
    </c:if>
    <c:if test="${diaplayCloseButton}">
        $('#closeCurrentResultView').addURLEvent();
    </c:if>
    <c:if test="${plma:getBooleanParam(widget, 'enableShareAll', false)}">
        <search:getPageName var="pageName"/>
        <search:getFeed var="rf" feeds="${feeds}"/>
        <search:getFeedInfo name="searchAPIUrl" feed="${rf}" var="sapiURL"/>

        Share3DXItems.shareAll({
            containerId: '.drag-all-hits',
            wuid: '${widget.wuid}',
            sapiquery: '${sapiURL}',
            baseURL: '<c:url value="/intercom/query"/>',
            page: '${pageName}',
            feedname: '${rf.id}'
        });
    </c:if>
</render:renderScript>


