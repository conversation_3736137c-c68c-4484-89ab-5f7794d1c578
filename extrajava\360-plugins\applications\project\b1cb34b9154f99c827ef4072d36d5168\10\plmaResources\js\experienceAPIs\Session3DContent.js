(function(window){
	'use strict';
	if (!window.SessionContent) {
		
		var Session3DContentListener = function(options) {
			this.sessionContent = {};
			this.init();
			return this;
		}
		
		Session3DContentListener.prototype.init = function (){
			let topic = 'DS/PADUtils/PADCommandProxy/sessionContent';
			let that = this;
			// As this is global singleton class Make sure that MessageHandler is loaded
			// else there will be js error.
			if(MessageHandler){
                MessageHandler.afterStart.done(function (dashboardController){
                    dashboardController.subscribeTopic(topic, that.handler.bind(that));

                    var widgetInfos = dashboardController.widgetInfos;
                    var event = {
                        metadata: {
                            uid: window.SelectAPI.utils.getRandomString(4),
                            originUid: widgetInfos.widgetId,
                            timestamp: Date.now(),
                            appid: widgetInfos.appId,
                            originWidgetId: widgetInfos.widgetId
                        }
                    };
                    dashboardController.publishTopic('DS/PADUtils/PADCommandProxy/getSessionContent', event);

                    // catch stop event to re-register the topic (subscribe & publish).
                    MessageHandler.afterStop.done(function (stopped){
                        that.init();
                    });
                });
            }
		}
		
		Session3DContentListener.prototype.handler = function(e){
			this.sessionContent[e.data.widgetId] = {
				rootIds: e.data.rootIds
				//, viewPoint = e.data.viewPoint
			};
		}
		
		Session3DContentListener.prototype.getLoaded3DRootIds = function(){
			let loadedRootIds = [];
			for (var key in this.sessionContent) {
				if (this.sessionContent.hasOwnProperty(key)) {
					var val = this.sessionContent[key];
					val.rootIds.forEach(function(el){
						loadedRootIds.push(el);
					});
				}
			}
			return _.uniq(loadedRootIds);
		}
		
		var instance = new Session3DContentListener();
		Session3DContentListener.getInstance = function(){
			return instance;
		}
		
		window.SessionContent = {
			getContent: function(){
				return Session3DContentListener.getInstance().getLoaded3DRootIds();
			}
		}
	}
})(window);

