(function (window) {
	'use strict';

	const DATE_FORMAT = 'YYYY/MM/DD HH:mm:ss';
	function getVisDateStr(dateStr){
		return dateStr.replaceAll('/', '-')
	}

	var PLMAPlanning = function (uCssId, options) {
		this.uCssId = uCssId;
		this.options = options;
		this.zoomLevel = "";
		this.widget = $('.' + this.uCssId);
		this.extendMove = "";
		this.otherFilters = [];

		this.minDate = moment().subtract(1, "months").format(DATE_FORMAT);
		this.maxDate = moment().add(1, "months").format(DATE_FORMAT);
		this.startDate = moment().subtract(1, "months").format(DATE_FORMAT);
		this.endDate = moment().add(1, "months").format(DATE_FORMAT);
		if (this.options.startInterval !== undefined && this.options.startInterval !== ''
			&& this.options.startInterval.split(' ').length > 1) {
			this.minDate = moment().subtract(parseInt(this.options.startInterval.split(' ')[0]), this.options.startInterval.split(' ')[1]).format(DATE_FORMAT);
			this.maxDate = moment().add(parseInt(this.options.startInterval.split(' ')[0]), this.options.startInterval.split(' ')[1]).format(DATE_FORMAT);
			this.startDate = moment().subtract(parseInt(this.options.startInterval.split(' ')[0]), this.options.startInterval.split(' ')[1]).format(DATE_FORMAT);
			this.endDate = moment().add(parseInt(this.options.startInterval.split(' ')[0]), this.options.startInterval.split(' ')[1]).format(DATE_FORMAT);
		}

		this.init();
	};

	PLMAPlanning.prototype.init = function () {
		/* Init Buttons */
		this.buttonManager = new WidgetButtonManager(this.uCssId, 'menu', '.widgetHeader');
		if (this.options.buttons.buttons.length > 0) {
			for (var i = 0 ; i< this.options.buttons.buttons.length ; i++) {
				var button = this.options.buttons.buttons[i];
				if (!this.buttonManager.hasButton(button.icon, button.label)) {
					var $button = this.buttonManager.addButton(button.icon, button.label, button.action);
					if (button.icon.includes(FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS)) {
						this.widget.fullScreenWidget({
							button: $button
						});
					}
				}
			}
		}

		/* Activate load all data button */
		this.widget.find('.loadAllDataButton').on('click', $.proxy(function (e) {
			this.loadAllData();
		}, this));

		/* Get filters not applied in context but by page Trigger */
		this.getOtherFilters($.proxy(function () {
			/* Activate move buttons */
			this.widget.find('.moveLeftButton').on('click', $.proxy(function () {
				this.moveLeft();
			}, this));
			this.widget.find('.moveRightButton').on('click', $.proxy(function () {
				this.moveRight();
			}, this));
			this.widget.find('.zoomInButton').on('click', $.proxy(function () {
				this.zoomIn();
			}, this));
			this.widget.find('.zoomOutButton').on('click', $.proxy(function () {
				this.zoomOut();
			}, this));

			//Activate spinner
			this.widget.find('.loader').toggleClass('activeSpinner');
			this.widget.find('.timeline-vis').toggleClass('activeSpinner');

			var timelineConfiguration = {};
			timelineConfiguration.minDate = this.startDate;
			timelineConfiguration.maxDate = this.endDate;
			timelineConfiguration.feeds = this.options.feeds;
			timelineConfiguration.pageName = this.options.pageName;
			timelineConfiguration.urlParams = this.options.urlParams;
			timelineConfiguration.groupBy = this.options.groupBy;
			timelineConfiguration.configId = this.options.id;
			timelineConfiguration.configLabel = this.options.label;
			timelineConfiguration.refineDateStart = this.options.refineDateStart;
			timelineConfiguration.refineDateEnd = this.options.refineDateEnd;
			timelineConfiguration.configStart = this.options.start;
			timelineConfiguration.configEnd = this.options.end;
			timelineConfiguration.configSecondStart = this.options.secondStart;
			timelineConfiguration.configSecondEnd = this.options.secondEnd;
			timelineConfiguration.additionalMetas = this.options.additionalMetas;
			timelineConfiguration.configExtraClasses = this.options.extraClasses;
			timelineConfiguration.enableSubObjects = this.options.enableSubObjects;
			timelineConfiguration.dateMeta = this.options.dateMeta;
			timelineConfiguration.nameSubItem = this.options.nameSubItem;
			timelineConfiguration.idSubItem = this.options.idSubItem;
			timelineConfiguration.relatedMeta = this.options.relatedMeta;
			timelineConfiguration.subDisplayType = this.options.subDisplayType;
			timelineConfiguration.additionalParams = this.options.additionalParams;
			timelineConfiguration.additionalParamsSubItems = this.options.additionalParamsSubItems;
			timelineConfiguration.additionalSubItemMetas = this.options.additionalSubItemMetas;
			timelineConfiguration.colorMeta = this.options.colorMeta;
			timelineConfiguration.subItemColorMeta = this.options.subItemColorMeta;
			timelineConfiguration.loadAll = this.options.loadAll;
			timelineConfiguration.nbObject = this.options.nbObject;
			timelineConfiguration.isFirstQuery = 'true';

			$.ajax({
				type: 'GET',
				url: this.options.url,
				data: {
					timelineConfiguration: JSON.stringify(timelineConfiguration)
				},
				dataType: 'json',
				success: $.proxy(function (data) {
					this.timelineData = {};
					this.timelineData.items = [];
					this.timelineData.groups = [];
					if (data) {
						this.timelineData = data;
					}
					this.activateTimeline();
					this.activateSearch();
					this.activateTooltip();
					//DesActivate spinner
					this.widget.find('.loader').toggleClass('activeSpinner');
					this.widget.find('.timeline-vis').toggleClass('activeSpinner');
					if (this.options.extendToValue === 'true' && (!this.timelineData.groups || this.timelineData.groups.length === 0) && this.timelineData.items.length === 0) {
						this.zoomOut();
					} else if (this.options.extendToValue === 'true') {
						this.options.extendToValue = 'false';
					}
				}, this)
			});
		}, this));
	};

	PLMAPlanning.prototype.getOtherFilters = function (success) {
		/* Get filters from savedFilters */
		if (typeof ChartboardStorageManager !== 'undefined') {
			var pageId = this.options.pageId;
			if (pageId === '') {
				this.options.pageId = this.options.pageName;
				pageId = this.options.pageName;
			}

			this.model = new ChartboardStorageManager();
			this.model.getFilterFromPage(pageId, pageId !== this.options.pageName, $.proxy(function (filters) {
				if (filters.params) {
					for (var paramName in filters.params) {
						for(var i=0 ; i<filters.params[paramName].length ; i++) {
							var param = paramName + '=' + filters.params[paramName][i];
							if(this.options.urlParams !== '') {
								param = '&' + param;
							}
							this.options.urlParams += param;
						}
					}
				}
				this.renameFiltersFeed();
				success.call(null);
			}, this), $.proxy(function () {
				this.renameFiltersFeed();
				success.call(null);
			}, this));
		} else {
			success.call(null);
		}
	};

	PLMAPlanning.prototype.renameFiltersFeed = function () {
		/* Get filters for unknown feed */
		if (this.options.feeds.length > 0) {
			var paramsTemp = '';
			var params = this.options.urlParams.split('&');
			for (var i = 0; i < params.length; i++) {
				var param = '';
				if (params[i].split("=").length > 1 && (params[i].split("=")[0].indexOf('.r') !== -1 || params[i].split("=")[0].indexOf('.zr') !== -1 || params[i].split("=")[0].indexOf('.cr') !== -1)) {
					param = this.options.feeds.split(',')[0].replace('[','').replace(']','') + '.' + params[i].split("=")[0].split('.')[1] + '=' + params[i].split("=")[1];
				} else {
					param = params[i];
				}

				if (i > 0) {
					param = '&' + param;
				}

				paramsTemp += param;
			}
			this.options.urlParams = paramsTemp;
		}
	};

	PLMAPlanning.prototype.activateTimeline = function () {
		// DOM element where the Timeline will be attached
		var container = document.getElementById('timeline-vis-' + this.uCssId);
		// Create a DataSet (allows two way data-binding)

		var items = new vis.DataSet(this.timelineData.items);
		var groups = new vis.DataSet(this.timelineData.groups);

		var height = "";
		if (this.options.height === "") {
			height = '100%';
		} else {
			height = this.options.height + 'px';
		}

		// Configuration for the Timeline
		var options = {
			editable: false,				// Allows to drag items on the timeline
			selectable: false,

			height: height,					// Width of timeline

			start: getVisDateStr(this.startDate),
			end: getVisDateStr(this.endDate),
			dataAttributes: 'all',

			zoomKey: 'ctrlKey',
			stack: true,
			margin: {
				item: {
					vertical: 30
				}
			},

			orientation: {
				axis: 'top',
				item: 'top'
			}
		};

		// Create a Timeline
		if (this.options.groupBy == "") {
			this.timeline = new vis.Timeline(container, items, options);
		} else {
			this.timeline = new vis.Timeline(container, items, options, groups);
		}

		this.timeline.on('rangechanged', $.proxy(function (e) {
			this.onChangeRange(e)
		}, this));
		this.timeline.on('changed', $.proxy(function (e) {
			this.onRedraw(e)
		}, this));

		this.widget.find('.vis-left').on('scroll', $.proxy(function (e) {
			var $e = $(e.currentTarget);
			var centerDiv = this.widget.find('.vis-center .vis-content');
			centerDiv.css('top', -$e.scrollTop() + 'px');
		}, this));


		this.widget.find('.timeline-info .close-button').on('click', $.proxy(function (e) {
			this.widget.find('.timeline-info').removeClass('visible');
			this.widget.find('.selected').removeClass('selected');
		}, this));

		this.widget.find('.timeline-info .refine-button').on('click', $.proxy(function (e) {
			this.refineOnMainItem();
		}, this));

		this.activateClickAction();
	};

	PLMAPlanning.prototype.activateClickAction = function () {
		this.widget.find('.vis-estimated-actual').off();
		this.widget.find('.vis-estimated-actual').on('click', $.proxy(function (e) {
			var $e = $(e.currentTarget);
			this.showInfo($e);
		}, this));

		this.widget.find('.vis-estimated-actual .vis-time-breakdown .subElem').off();
		this.widget.find('.vis-estimated-actual .vis-time-breakdown .subElem').on('click', $.proxy(function (e) {
			e.stopPropagation();
			var $e = $(e.currentTarget);
			this.showInfoForSubItem($e);
		}, this));
	};

	PLMAPlanning.prototype.zoomIn = function () {
		var range = this.timeline.getWindow();
		var interval = range.end - range.start;

		this.startDate = moment(range.start.valueOf() + interval * 0.25).format(DATE_FORMAT);
		this.endDate = moment(range.end.valueOf() - interval * 0.25).format(DATE_FORMAT);
		this.timeline.setWindow({
			start: getVisDateStr(this.startDate),
			end: getVisDateStr(this.endDate)
		});
	};

	PLMAPlanning.prototype.zoomOut = function () {
		var range = this.timeline.getWindow();
		var interval = range.end - range.start;
		this.extendMove = "";

		this.startDate = moment(range.start.valueOf() - interval * 0.5).format(DATE_FORMAT);
		this.endDate = moment(range.end.valueOf() + interval * 0.5).format(DATE_FORMAT);

		if (this.startDate < this.minDate) {
			//Get new objects from access api and add them to the timeline
			this.extendMove = "left";
			this.getNewObjectsForTimeline(this.startDate, this.minDate);

			this.minDate = this.startDate
		}
		if (this.endDate > this.maxDate) {
			//Get new objects from access api and add them to the timeline
			this.extendMove = "right";
			this.getNewObjectsForTimeline(this.maxDate, this.endDate);

			this.maxDate = this.endDate
		}

		this.timeline.setWindow({
			start: getVisDateStr(this.startDate),
			end: getVisDateStr(this.endDate)
		});
	};

	PLMAPlanning.prototype.moveRight = function () {
		var range = this.timeline.getWindow();
		var interval = range.end - range.start;


		this.startDate = moment(range.start.valueOf() + interval * 0.5).format(DATE_FORMAT);
		this.endDate = moment(range.end.valueOf() + interval * 0.5).format(DATE_FORMAT);
		if (this.endDate > this.maxDate) {
			//Get new objects from access api and add them to the timeline
			this.getNewObjectsForTimeline(this.maxDate, this.endDate);

			this.maxDate = this.endDate
		}
		this.timeline.setWindow({
			start: getVisDateStr(this.startDate),
			end: getVisDateStr(this.endDate)
		});

		this.timeline.setWindow({
			start: getVisDateStr(this.startDate),
			end: getVisDateStr(this.endDate)
		});
	};

	PLMAPlanning.prototype.moveLeft = function () {
		var range = this.timeline.getWindow();
		var interval = range.end - range.start;


		this.startDate = moment(range.start.valueOf() - interval * 0.5).format(DATE_FORMAT);
		this.endDate = moment(range.end.valueOf() - interval * 0.5).format(DATE_FORMAT);
		if (this.startDate < this.minDate) {
			//Get new objects from access api and add them to the timeline
			this.getNewObjectsForTimeline(this.startDate, this.minDate);

			this.minDate = this.startDate
		}

		this.timeline.setWindow({
			start: getVisDateStr(this.startDate),
			end: getVisDateStr(this.endDate)
		});
	};

	PLMAPlanning.prototype.onChangeRange = function (e) {
		this.startDate = moment(e.start).format(DATE_FORMAT);
		this.endDate = moment(e.end).format(DATE_FORMAT);

		if (this.startDate < this.minDate) {
			//Get new objects from access api and add them to the timeline
			this.getNewObjectsForTimeline(this.startDate, this.minDate);

			this.minDate = this.startDate
		}
		if (this.endDate > this.maxDate) {
			//Get new objects from access api and add them to the timeline
			this.getNewObjectsForTimeline(this.maxDate, this.endDate);

			this.maxDate = this.endDate
		}


		this.timeline.setWindow({
			start: getVisDateStr(this.startDate),
			end: getVisDateStr(this.endDate)
		});

	};

	PLMAPlanning.prototype.onRedraw = function () {
		this.widget.find('.vis-estimated-actual').each(function (i, e) {
			var $e = $(e);
			if ($e.css("left").split('px')[0] > 250) {
				$e.addClass('vis-estimated-actual-right');
			} else {
				$e.removeClass('vis-estimated-actual-right');
			}
		});

		var centerDiv = this.widget.find('.vis-center .vis-content');
		if (centerDiv.css('top') != undefined) {
			var scrollTop = parseInt(centerDiv.css('top').split('px'));
			var leftDiv = this.widget.find('.vis-left');
			leftDiv.scrollTop(-scrollTop);
			var leftDivContent = this.widget.find('.vis-left .vis-content');
			leftDivContent.css('top', 0);
		}

		//Set title visible if it is possible
		this.widget.find('.vis-item.vis-estimated-actual .vis-item-content').each(function (i, e) {
			var $e = $(e);
			$e.css('margin-left', $e.css('left'));
		});
	};

	PLMAPlanning.prototype.mergeData = function (newData) {
		var mergedObject = {};
		var tempGroups = [];
		if (newData.groups) {
			for (var i = 0; i < newData.groups.length; i++) {
				var newGroupAlreadyExists = false;
				for (var j = 0; j < this.timelineData.groups.length; j++) {
					if (this.timelineData.groups[j].id === newData.groups[i].id) {
						newGroupAlreadyExists = true;
						break;
					}
				}
				if (!newGroupAlreadyExists) {
					tempGroups.push(newData.groups[i]);
				}
			}
			mergedObject.groups = this.timelineData.groups.concat(tempGroups);
		}
		tempGroups = [];
		if (newData.items) {
			for (var i = 0; i < newData.items.length; i++) {
				var newItemAlreadyExists = false;
				var newSubItemsIndex = -1;
				for (var j = 0; j < this.timelineData.items.length; j++) {
					if (this.timelineData.items[j].id === newData.items[i].id) {
						newItemAlreadyExists = true;
						newSubItemsIndex = j;
						break;
					}
				}
				if (!newItemAlreadyExists) {
					tempGroups.push(newData.items[i]);
				} else {
					/* YFAIRE See to add only differentrs sub elems */
					this.timelineData.items[newSubItemsIndex].dateRanges.subElems.concat(newData.items[i].dateRanges.subElems);
				}
			}
			mergedObject.items = this.timelineData.items.concat(tempGroups);
		}
		this.timelineData = mergedObject;
	};

	PLMAPlanning.prototype.getNewObjectsForTimeline = function (fromDate, toDate) {
		//Activate spinner
		this.widget.find('.loader').toggleClass('activeSpinner');
		this.widget.find('.timeline-vis').toggleClass('activeSpinner');

		var timelineConfiguration = {};
		timelineConfiguration.minDate = fromDate;
		timelineConfiguration.maxDate = toDate;
		timelineConfiguration.feeds = this.options.feeds;
		timelineConfiguration.pageName = this.options.pageName;
		timelineConfiguration.urlParams = this.options.urlParams;
		timelineConfiguration.groupBy = this.options.groupBy;
		timelineConfiguration.configId = this.options.id;
		timelineConfiguration.configLabel = this.options.label;
		timelineConfiguration.refineDateStart = this.options.refineDateStart;
		timelineConfiguration.refineDateEnd = this.options.refineDateEnd;
		timelineConfiguration.configStart = this.options.start;
		timelineConfiguration.configEnd = this.options.end;
		timelineConfiguration.configSecondStart = this.options.secondStart;
		timelineConfiguration.configSecondEnd = this.options.secondEnd;
		timelineConfiguration.additionalMetas = this.options.additionalMetas;
		timelineConfiguration.configExtraClasses = this.options.extraClasses;
		timelineConfiguration.enableSubObjects = this.options.enableSubObjects;
		timelineConfiguration.dateMeta = this.options.dateMeta;
		timelineConfiguration.nameSubItem = this.options.nameSubItem;
		timelineConfiguration.idSubItem = this.options.idSubItem;
		timelineConfiguration.relatedMeta = this.options.relatedMeta;
		timelineConfiguration.subDisplayType = this.options.subDisplayType;
		timelineConfiguration.additionalParams = this.options.additionalParams;
		timelineConfiguration.additionalParamsSubItems = this.options.additionalParamsSubItems;
		timelineConfiguration.additionalSubItemMetas = this.options.additionalSubItemMetas;
		timelineConfiguration.colorMeta = this.options.colorMeta;
		timelineConfiguration.subItemColorMeta = this.options.subItemColorMeta;
		timelineConfiguration.loadAll = 'true';
		timelineConfiguration.nbObject = this.options.nbObject;
		timelineConfiguration.isFirstQuery = 'false';

		$.ajax({
			type: 'GET',
			url: this.options.url,
			data: {
				timelineConfiguration: JSON.stringify(timelineConfiguration)
			},
			success: $.proxy(function (data) {
				var newData = {};
				newData.items = [];
				newData.groups = [];
				if (data) {
					newData = data;
				}
				this.mergeData(newData);
				this.timeline.setData(this.timelineData);

				//DesActivate spinner
				this.widget.find('.loader').toggleClass('activeSpinner');
				this.widget.find('.timeline-vis').toggleClass('activeSpinner');
				if (this.extendMove !== "" && this.options.extendToValue === 'true' && (!newData.groups || newData.groups.length === 0) && newData.items.length === 0 && moment(this.maxDate).diff(moment(this.minDate), "d") < 365) {
					if (this.extendMove === "right" && this.noDataLeft) {
						this.zoomOut();
					} else {
						this.extendMove = "right";
						this.noDataLeft = true;
					}
				} else {
					this.noDataLeft = false;
				}

				setTimeout($.proxy(function () {
					this.activateClickAction();
				}, this), 1000);
			}, this)
		})
	};

	PLMAPlanning.prototype.loadAllData = function () {
		//Activate spinner
		this.widget.find('.loader').toggleClass('activeSpinner');
		this.widget.find('.timeline-vis').toggleClass('activeSpinner');

		/* Remove all objects */
		this.timeline.destroy();

		var timelineConfiguration = {};
		timelineConfiguration.minDate = this.startDate;
		timelineConfiguration.maxDate = this.endDate;
		timelineConfiguration.feeds = this.options.feeds;
		timelineConfiguration.pageName = this.options.pageName;
		timelineConfiguration.urlParams = this.options.urlParams;
		timelineConfiguration.groupBy = this.options.groupBy;
		timelineConfiguration.configId = this.options.id;
		timelineConfiguration.configLabel = this.options.label;
		timelineConfiguration.refineDateStart = this.options.refineDateStart;
		timelineConfiguration.refineDateEnd = this.options.refineDateEnd;
		timelineConfiguration.configStart = this.options.start;
		timelineConfiguration.configEnd = this.options.end;
		timelineConfiguration.configSecondStart = this.options.secondStart;
		timelineConfiguration.configSecondEnd = this.options.secondEnd;
		timelineConfiguration.additionalMetas = this.options.additionalMetas;
		timelineConfiguration.configExtraClasses = this.options.extraClasses;
		timelineConfiguration.enableSubObjects = this.options.enableSubObjects;
		timelineConfiguration.dateMeta = this.options.dateMeta;
		timelineConfiguration.nameSubItem = this.options.nameSubItem;
		timelineConfiguration.idSubItem = this.options.idSubItem;
		timelineConfiguration.relatedMeta = this.options.relatedMeta;
		timelineConfiguration.subDisplayType = this.options.subDisplayType;
		timelineConfiguration.additionalParams = this.options.additionalParams;
		timelineConfiguration.additionalParamsSubItems = this.options.additionalParamsSubItems;
		timelineConfiguration.additionalSubItemMetas = this.options.additionalSubItemMetas;
		timelineConfiguration.colorMeta = this.options.colorMeta;
		timelineConfiguration.subItemColorMeta = this.options.subItemColorMeta;
		timelineConfiguration.loadAll = 'true';
		timelineConfiguration.nbObject = this.options.nbObject;
		timelineConfiguration.isFirstQuery = 'true';

		$.ajax({
			type: 'GET',
			url: this.options.url,
			data: {
				timelineConfiguration: JSON.stringify(timelineConfiguration)
			},
			success: $.proxy(function (data) {
				this.timelineData = {};
				this.timelineData.items = [];
				this.timelineData.groups = [];
				if (data) {
					this.timelineData = data;
				}
				this.activateTimeline();
				//DesActivate spinner
				this.widget.find('.loader').toggleClass('activeSpinner');
				this.widget.find('.timeline-vis').toggleClass('activeSpinner');

				this.widget.find('.loadAllData').remove();
			}, this)
		})
	};

	PLMAPlanning.prototype.activateSearch = function () {
		this.widget.find('.searchTimelineButton').on('click', $.proxy(function () {
			this.widget.find('.searchTimelineInput').toggleClass('hidden');
		}, this));

		var timer = 0;

		//When the input value changes, we call the filter facet/category function
		this.widget.find('input.searchTimelineInput').on('keyup', $.proxy(function (e) {
			var inputValue = e.target.value;

			//Search by name in timelineData
			var elemData = {};
			for (var i = 0; i < this.timelineData.items.length; i++) {
				if (this.timelineData.items[i].content == inputValue) {
					elemData = this.timelineData.items[i];
					break;
				}
			}

			if (elemData.id != undefined && elemData.id != '') {
				clearTimeout(timer);
				timer = setTimeout($.proxy(function () {
					this.focusOnObject(elemData.id);
				}, this), 1000);
			} else {
				clearTimeout(timer);
				timer = setTimeout($.proxy(function () {
					this.focusOnObject(inputValue);
				}, this), 1000);
			}
		}, this));
	};

	PLMAPlanning.prototype.activateTooltip = function () {
		this.widget.find('.tooltip-button').on('click', $.proxy(function () {
			this.widget.find('.timeline-tooltip').toggleClass('hidden');
		}, this));
	};

	PLMAPlanning.prototype.focusOnObject = function (id) {
		if (id != undefined && id != "") {
			//Verify the id exists
			var exists = false;
			for (var i = 0; i < this.timelineData.items.length; i++) {
				if (this.timelineData.items[i].id == id) {
					exists = true;
					break;
				}
			}
			if (exists) {
				this.widget.find('.searchContainer').removeClass('no-result');
				this.timeline.focus(id);
			} else {
				this.widget.find('.searchContainer').addClass('no-result');
			}
		} else {
			this.widget.find('.searchContainer').removeClass('no-result');
		}
	};

	PLMAPlanning.prototype.showInfo = function (elem) {
		this.widget.find('.selected').removeClass('selected');

		//Search the info about the clicked elem in the timeline data
		var elemName = elem.find('.vis-item-content')[0].innerHTML;
		var elemData = {};
		for (var l = 0; l < this.timelineData.items.length; l++) {
			if (this.timelineData.items[l].content == elemName) {
				elemData = this.timelineData.items[l];
				break;
			}
		}

		//Set the info in the panel
		var infoPanel = this.widget.find('.timeline-info');
		infoPanel.find('.info-title .title-value')[0].innerHTML = elemData.content;
		if (elemData.dateRanges.act !== undefined) {
			infoPanel.find('.info-main-date-start .value')[0].innerHTML = moment(elemData.dateRanges.act.start,DATE_FORMAT).format('L');
			if (elemData.noEndDate == "true") {
				infoPanel.find('.info-main-date-end').css('display', 'none');
			} else {
				infoPanel.find('.info-main-date-end').css('display', 'block');
				infoPanel.find('.info-main-date-end .value')[0].innerHTML = moment(elemData.dateRanges.act.end,DATE_FORMAT).format('L');
			}
		} else {
			infoPanel.find('.info-main-date-start .value')[0].innerHTML = '';
			if (elemData.noEndDate == "true") {
				infoPanel.find('.info-main-date-end').css('display', 'none');
			} else {
				infoPanel.find('.info-main-date-end').css('display', 'block');
				infoPanel.find('.info-main-date-end .value')[0].innerHTML = '';
			}
		}

		if (elemData.dateRanges.est !== undefined) {
			infoPanel.find('.info-second-date-start .value')[0].innerHTML = moment(elemData.dateRanges.est.start,DATE_FORMAT).format('L');
			infoPanel.find('.info-second-date-end .value')[0].innerHTML = moment(elemData.dateRanges.est.end,DATE_FORMAT).format('L');
		} else {
			infoPanel.find('.info-second-date-start .value')[0].innerHTML = '';
			infoPanel.find('.info-second-date-end .value')[0].innerHTML = '';
		}

		if (elemData.group && elemData.group !== elemData.id) {
			infoPanel.find('.info-related-parent').css('display', 'block');
			infoPanel.find('.info-related-parent .value')[0].innerHTML = elemData.group;
		} else {
			infoPanel.find('.info-related-parent').css('display', 'none');
		}

		if (elemData.colorMetaValue) {
			infoPanel.find('.info-color').css('display', 'block');
			infoPanel.find('.info-color .value')[0].innerHTML = elemData.colorMetaValue;
		} else {
			infoPanel.find('.info-color').css('display', 'none');
		}

		//Add additionnal metas values
		this.widget.find('.add-info-main-data').remove();
		for (var i = 0; i < elemData.addMetas.length; i++) {
			var obj = elemData.addMetas[i];
			for (var key in obj) {
				infoPanel.find('.info-main').append('<div class="info-main-data add-info-main-data info-main-add-data-' + i + '"></div>');
				infoPanel.find('.info-main-add-data-' + i).append('<span class="label">' + key + ': </span>');
				infoPanel.find('.info-main-add-data-' + i).append('<span class="value">' + obj[key] + '</span>');
			}
		}

		//Set sub-items info
		if (this.options.enableSubObjects === "true") {
			var subItemElems = infoPanel.find('.info-sub-item-elem');
			if (subItemElems.length > 1) {
				for (var k = 1; k < subItemElems.length; k++) {
					if (subItemElems[k].remove === undefined) {
						subItemElems[k].parentElement.removeChild(subItemElems[k])
					} else {
						subItemElems[k].remove();
					}
				}
			}

			if (elemData.dateRanges.subElems.length > 0) {
				subItemElems.removeClass('hidden');
				infoPanel.find('.info-sub-item-elem .info-sub-item-title')[0].innerHTML = elemData.dateRanges.subElems[0].name;
				infoPanel.find('.info-sub-item-elem .info-sub-item-start .value')[0].innerHTML = moment(elemData.dateRanges.subElems[0].start,DATE_FORMAT).format('L');
				infoPanel.find('.info-sub-item-elem .info-sub-item-color-meta .value')[0].innerHTML = elemData.dateRanges.subElems[0].colorMetaValue;

				//Add additionnal metas values
				this.widget.find('.info-sub-item-main .add-info-main-data').remove();
				for (var j = 0; j < elemData.dateRanges.subElems[0].addMetas.length; j++) {
					var obj = elemData.dateRanges.subElems[0].addMetas[j];
					for (var key in obj) {
						infoPanel.find('.info-sub-item-main').append('<div class="info-main-data add-info-main-data info-main-add-data-' + j + '"></div>');
						infoPanel.find('.info-sub-item-main .info-main-add-data-' + j).append('<span class="label">' + key + ': </span>');
						infoPanel.find('.info-sub-item-main .info-main-add-data-' + j).append('<span class="value">' + moment(obj[key],DATE_FORMAT)._isValid ? moment(obj[key],DATE_FORMAT).format('L') : obj[key] + '</span>');
					}
				}
			} else {
				subItemElems.addClass('hidden');
			}
			for (var m = 1; m < elemData.dateRanges.subElems.length; m++) {
				var newElem = $(infoPanel.find('.info-sub-item-elem')[0]).clone();
				newElem.find('.info-sub-item-title')[0].innerHTML = elemData.dateRanges.subElems[m].name;
				newElem.find('.info-sub-item-start .value')[0].innerHTML = moment(elemData.dateRanges.subElems[m].start,DATE_FORMAT).format('L');
				newElem.find('.info-sub-item-color-meta .value')[0].innerHTML = elemData.dateRanges.subElems[m].colorMetaValue;

				//Add additionnal metas values
				newElem.find('.add-info-main-data').remove();
				for (var n = 0; n < elemData.dateRanges.subElems[m].addMetas.length; n++) {
					var obj = elemData.dateRanges.subElems[m].addMetas[n];
					for (var key in obj) {
						newElem.find('.info-sub-item-main').append('<div class="info-main-data add-info-main-data info-main-add-data-' + n + '"></div>');
						newElem.find('.info-main-add-data-' + n).append('<span class="label">' + key + ': </span>');
						newElem.find('.info-main-add-data-' + n).append('<span class="value">' + moment(obj[key],DATE_FORMAT)._isValid ? moment(obj[key],DATE_FORMAT).format('L') : obj[key] + '</span>');
					}
				}

				newElem.appendTo(infoPanel.find('.info-sub-item'));
			}
		} else {
			var subItemElems = infoPanel.find('.info-sub-item-elem');
			subItemElems.addClass('hidden');
		}

		//Get i18n value for additional metas
		this.getI18nForAddMetas();

		//Open the info panel
		infoPanel.addClass('visible');

		//Add selected indication on object
		elem.addClass('selected');

		//Add on click event on sub elems
		infoPanel.find('.info-sub-item-elem').on('click', $.proxy(function (e) {
			var $e = $(e.currentTarget);
			this.refineOnSubItem($e);
		}, this))
	};

	PLMAPlanning.prototype.showInfoForSubItem = function (elem) {
		this.widget.find('.selected').removeClass('selected');
		var parent = elem.parent().parent();
		//Search the info about the clicked elem in the timeline data
		var elemName = parent.find('.vis-item-content')[0].innerHTML;
		var elemData = {};
		for (var i = 0; i < this.timelineData.items.length; i++) {
			if (this.timelineData.items[i].content == elemName) {
				elemData = this.timelineData.items[i];
				break;
			}
		}

		//Set the info in the panel
		var infoPanel = this.widget.find('.timeline-info');
		infoPanel.find('.info-title .title-value')[0].innerHTML = elemData.content;
		if (elemData.dateRanges.act) {
			infoPanel.find('.info-main-date-start .value')[0].innerHTML = moment(elemData.dateRanges.act.start,DATE_FORMAT).format('L');
			infoPanel.find('.info-main-date-end .value')[0].innerHTML = moment(elemData.dateRanges.act.end,DATE_FORMAT).format('L');
		} else {
			infoPanel.find('.info-main-date-start .value')[0].innerHTML = '';
			infoPanel.find('.info-main-date-end .value')[0].innerHTML = '';
		}

		infoPanel.find('.info-second-date-start .value')[0].innerHTML = moment(elemData.dateRanges.est.start,DATE_FORMAT).format('L');
		infoPanel.find('.info-second-date-end .value')[0].innerHTML = moment(elemData.dateRanges.est.end,DATE_FORMAT).format('L');
		infoPanel.find('.info-related-parent .value')[0].innerHTML = elemData.group;
		infoPanel.find('.info-color .value')[0].innerHTML = elemData.colorMetaValue;

		//Add additionnal metas values
		this.widget.find('.add-info-main-data').remove();
		for (var i = 0; i < elemData.addMetas.length; i++) {
			var obj = elemData.addMetas[i];
			for (var key in obj) {
				infoPanel.find('.info-main').append('<div class="info-main-data add-info-main-data info-main-add-data-' + i + '"></div>');
				infoPanel.find('.info-main-add-data-' + i).append('<span class="label">' + key + ': </span>');
				infoPanel.find('.info-main-add-data-' + i).append('<span class="value">' + moment(obj[key],DATE_FORMAT)._isValid ? moment(obj[key],DATE_FORMAT).format('L') : obj[key] + '</span>');
			}
		}

		//Set sub-items info
		var subItemElems = infoPanel.find('.info-sub-item-elem');
		if (subItemElems.length > 1) {
			for (var i = 1; i < subItemElems.length; i++) {
				subItemElems[i].remove();
			}
		}
		var indexSubItem = elem.parent().find('.subElem').index(elem);
		infoPanel.find('.info-sub-item-elem .info-sub-item-title')[0].innerHTML = elemData.dateRanges.subElems[indexSubItem].name;
		infoPanel.find('.info-sub-item-elem .info-sub-item-start .value')[0].innerHTML = moment(elemData.dateRanges.subElems[indexSubItem].start,DATE_FORMAT).format('L');
		infoPanel.find('.info-sub-item-elem .info-sub-item-color-meta .value')[0].innerHTML = elemData.dateRanges.subElems[indexSubItem].colorMetaValue;

		//Add additionnal metas values
		this.widget.find('.info-sub-item-main .add-info-main-data').remove();
		for (var j = 0; j < elemData.dateRanges.subElems[0].addMetas.length; j++) {
			var obj = elemData.dateRanges.subElems[0].addMetas[j];
			for (var key in obj) {
				infoPanel.find('.info-sub-item-main').append('<div class="info-main-data add-info-main-data info-main-add-data-' + j + '"></div>');
				infoPanel.find('.info-sub-item-main .info-main-add-data-' + j).append('<span class="label">' + key + ': </span>');
				infoPanel.find('.info-sub-item-main .info-main-add-data-' + j).append('<span class="value">' + moment(obj[key],DATE_FORMAT)._isValid ? moment(obj[key],DATE_FORMAT).format('L') : obj[key] + '</span>');
			}
		}

		//Get i18n value for additional metas
		this.getI18nForAddMetas();

		//Open the info panel
		infoPanel.addClass('visible');

		//Add selected indication on object
		elem.addClass('selected');
		parent.addClass('selected');
	};

	PLMAPlanning.prototype.getI18nForAddMetas = function () {
		this.widget.find('.add-info-main-data').each($.proxy(function (i, e) {
			var key = $(e).find('.label')[0].innerHTML.replace(':', '');

			//Get i18n meta translation
			$.ajax({
				type: 'GET',
				url: this.options.urlI18n + '?key=meta_' + key,
				success: $.proxy(function (data) {
					$(e).find('.label')[0].innerHTML = data + ': ';
				}, this)
			})
		}, this));

	};

	PLMAPlanning.prototype.refineOnMainItem = function () {
		//Search the info about the clicked elem in the timeline data
		var elemName = this.widget.find('.timeline-info .info-title .title-value')[0].innerHTML;
		var elemData = {};
		for (var i = 0; i < this.timelineData.items.length; i++) {
			if (this.timelineData.items[i].content == elemName) {
				elemData = this.timelineData.items[i];
				break;
			}
		}

		//If there are some sub-items
		var url = new BuildUrl(window.location.href);
		if (this.options.enableSubObjects == "true") {
			//Push as refine on related_meta
			var refine = "f/" + this.options.relatedMeta + "/" + elemData.id;
			if (url.getParameter(this.options.feeds.replace('[', '').replace(']', '') + ".r") != undefined) {
				var alreadyExist = false;
				for (var i = 0; i < url.getParameter(this.options.feeds.replace('[', '').replace(']', '') + ".r").length; i++) {
					if (url.getParameter(this.options.feeds.replace('[', '').replace(']', '') + ".r")[i] == refine) {
						alreadyExist = true;
						break;
					}
				}
				if (!alreadyExist) {
					url.addParameter(this.options.feeds.replace('[', '').replace(']', '') + ".r", refine, false);
				}
			} else {
				url.addParameter(this.options.feeds.replace('[', '').replace(']', '') + ".r", refine, false);
			}
		} else {
			//Push as query id:
			var query = this.options.id + ":" + elemData.id;
			if (url.getParameter("q") != undefined && url.getParameter("q").indexOf(query) == -1) {
				url.addParameter("q", "(" + url.getParameter("q") + ") AND " + query, true);
			} else {
				url.addParameter("q", query, true);
			}
		}
		window.location.replace(url.toString());
	};

	PLMAPlanning.prototype.refineOnSubItem = function (elem) {
		//Search the info about the clicked elem in the timeline data
		var elemName = this.widget.find('.timeline-info .info-title .title-value')[0].innerHTML;
		var elemData = {};
		for (var i = 0; i < this.timelineData.items.length; i++) {
			if (this.timelineData.items[i].content == elemName) {
				elemData = this.timelineData.items[i];
				break;
			}
		}

		//Get the right sub-elem
		var indexSubItem = elem.parent().find('.info-sub-item-elem').index(elem);

		//Create new url
		var url = new BuildUrl(window.location.href);
		var query = this.options.idSubItem + ":" + elemData.dateRanges.subElems[indexSubItem].id;
		if (url.getParameter("q") != undefined && url.getParameter("q").indexOf(query) == -1) {
			url.addParameter("q", "(" + url.getParameter("q") + ") AND " + query, true);
		} else {
			url.addParameter("q", query, true);
		}
		window.location.replace(url.toString());
	};

	window.PLMAPlanning = PLMAPlanning;
})(window);

var fullScreenTimeline = function (uCssId) {
	var selector = '.' + uCssId;
	var originalChart = $(selector);
	var button = originalChart.find(".resize-button");

	button.on('click', function () {
		originalChart[0].getClassList().toggle('full-size-active');
		button.toggleClass('fonticon-resize-full fonticon-resize-small');
		$(window).trigger('plma:resize');
	});

	var onRemoveFullSize = function(e) {
		originalChart[0].getClassList().remove('full-size-active');
		button.toggleClass('fonticon-resize-full fonticon-resize-small');
		$(window).trigger('plma:resize');
	}

	originalChart.on('click', function (e) {
		if (e.target.getClassList().contains('searchWidget')) {
			onRemoveFullSize(e);
		}
	});

	$(document).keyup(function (e) {
		if (e.keyCode == 27 && originalChart.hasClass('full-size-active')) { // escape key maps to keycode `27`
			onRemoveFullSize(e);
		}
	});
};	
