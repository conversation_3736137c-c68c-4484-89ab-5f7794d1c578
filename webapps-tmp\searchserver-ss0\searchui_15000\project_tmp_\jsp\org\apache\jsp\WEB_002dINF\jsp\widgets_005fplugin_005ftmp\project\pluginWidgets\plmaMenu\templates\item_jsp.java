/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:30 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.plmaMenu.templates;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class item_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_0;

static {
  _jspx_fnmap_0= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:isEditable", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "isEditable", new Class[] {jakarta.servlet.http.HttpServletRequest.class, java.lang.String.class});
}

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(12);
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/widget.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/request.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/widget.menu.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/url.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005flink_0026_005fhref;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005furl_005fresource_0026_005ffile_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fisPreferredPage_0026_005fvar_005fid_005fnobody;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005flink_0026_005fhref = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005furl_005fresource_0026_005ffile_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fisPreferredPage_0026_005fvar_005fid_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005flink_0026_005fhref.release();
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005furl_005fresource_0026_005ffile_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.release();
    _005fjspx_005ftagPool_005fplma_005fisPreferredPage_0026_005fvar_005fid_005fnobody.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_render_005fimport_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_string_005feval_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_string_005feval_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_string_005feval_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_string_005fescape_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_string_005fescape_005f1(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_string_005fescape_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_string_005fescape_005f3(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_string_005fescape_005f4(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write("<div class=\"menu-item ");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${extraCss}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write(' ');
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:isEditable(pageContext.request, pageId) ? 'editable-item': ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_0));
      out.write("\"\r\n");
      out.write("	 title=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty section ? section.concat(' - ').concat(label) : label}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\"\r\n");
      out.write("	 data-togglepanel=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${togglePanel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\"\r\n");
      out.write("     data-descId=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${descCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      out.write("\" >\r\n");
      out.write("\r\n");
      out.write("	");
      if (_jspx_meth_render_005flink_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("</div>\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(12,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setParameters("uCssId");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fimport_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f1);
    try {
      _jspx_th_render_005fimport_005f1.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(13,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f1.setParameters("itemCssId,descCssId,pageId,extraCss,label,description,togglePanel,url,isFolding,iconCss,iconUrl,section,onClick,type");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(13,0) name = ignore type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f1.setIgnore(true);
      _jspx_th_render_005fimport_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f0_reused = false;
    try {
      _jspx_th_string_005feval_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(15,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setVar("label");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(15,0) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${label}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005feval_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f0 = _jspx_th_string_005feval_005f0.doStartTag();
        if (_jspx_th_string_005feval_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fnobody.reuse(_jspx_th_string_005feval_005f0);
      _jspx_th_string_005feval_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f0, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f1 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f1_reused = false;
    try {
      _jspx_th_string_005feval_005f1.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(16,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setVar("section");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(16,0) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${section}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005feval_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f1 = _jspx_th_string_005feval_005f1.doStartTag();
        if (_jspx_th_string_005feval_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fnobody.reuse(_jspx_th_string_005feval_005f1);
      _jspx_th_string_005feval_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f1, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f2 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f2_reused = false;
    try {
      _jspx_th_string_005feval_005f2.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(17,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f2.setVar("description");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(17,0) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f2.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${description}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005feval_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f2 = _jspx_th_string_005feval_005f2.doStartTag();
        if (_jspx_th_string_005feval_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005fnobody.reuse(_jspx_th_string_005feval_005f2);
      _jspx_th_string_005feval_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f2, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f0_reused = false;
    try {
      _jspx_th_string_005fescape_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(19,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setVar("label");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(19,0) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${label}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(19,0) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f0.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f0 = _jspx_th_string_005fescape_005f0.doStartTag();
        if (_jspx_th_string_005fescape_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f0);
      _jspx_th_string_005fescape_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f0, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f1 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f1_reused = false;
    try {
      _jspx_th_string_005fescape_005f1.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(20,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setVar("section");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(20,0) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${section}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(20,0) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f1.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f1 = _jspx_th_string_005fescape_005f1.doStartTag();
        if (_jspx_th_string_005fescape_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f1);
      _jspx_th_string_005fescape_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f1, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f2 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f2_reused = false;
    try {
      _jspx_th_string_005fescape_005f2.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(21,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f2.setVar("description");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(21,0) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f2.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${description}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(21,0) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f2.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f2 = _jspx_th_string_005fescape_005f2.doStartTag();
        if (_jspx_th_string_005fescape_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f2);
      _jspx_th_string_005fescape_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f2, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f3 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f3_reused = false;
    try {
      _jspx_th_string_005fescape_005f3.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(22,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f3.setVar("extraCss");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(22,0) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f3.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${extraCss}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(22,0) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f3.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f3 = _jspx_th_string_005fescape_005f3.doStartTag();
        if (_jspx_th_string_005fescape_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f3);
      _jspx_th_string_005fescape_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f3, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fescape_005f4(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:escape
    com.exalead.cv360.searchui.view.jspapi.string.EscapeTag _jspx_th_string_005fescape_005f4 = (com.exalead.cv360.searchui.view.jspapi.string.EscapeTag) _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EscapeTag.class);
    boolean _jspx_th_string_005fescape_005f4_reused = false;
    try {
      _jspx_th_string_005fescape_005f4.setPageContext(_jspx_page_context);
      _jspx_th_string_005fescape_005f4.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(23,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f4.setVar("iconCss");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(23,0) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f4.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${iconCss}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(23,0) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fescape_005f4.setEscapeType("HTML");
      int[] _jspx_push_body_count_string_005fescape_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fescape_005f4 = _jspx_th_string_005fescape_005f4.doStartTag();
        if (_jspx_th_string_005fescape_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fescape_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fescape_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fescape_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fescape_0026_005fvar_005fvalue_005fescapeType_005fnobody.reuse(_jspx_th_string_005fescape_005f4);
      _jspx_th_string_005fescape_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fescape_005f4, _jsp_getInstanceManager(), _jspx_th_string_005fescape_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005flink_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:link
    com.exalead.cv360.searchui.view.jspapi.render.LinkTag _jspx_th_render_005flink_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.LinkTag) _005fjspx_005ftagPool_005frender_005flink_0026_005fhref.get(com.exalead.cv360.searchui.view.jspapi.render.LinkTag.class);
    boolean _jspx_th_render_005flink_005f0_reused = false;
    try {
      _jspx_th_render_005flink_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005flink_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(30,1) name = href type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005flink_005f0.setHref((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${url}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_render_005flink_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005flink_005f0 = _jspx_th_render_005flink_005f0.doStartTag();
        if (_jspx_eval_render_005flink_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005flink_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005flink_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005flink_005f0);
          }
          do {
            out.write("\r\n");
            out.write("        <div class=\"item-wrapper\">\r\n");
            out.write("            ");
            if (_jspx_meth_c_005fif_005f0(_jspx_th_render_005flink_005f0, _jspx_page_context, _jspx_push_body_count_render_005flink_005f0))
              return true;
            out.write("\r\n");
            out.write("            <div class=\"item-icon ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${iconCss}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(' ');
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isFolding ? 'icon-folder' : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\">\r\n");
            out.write("                ");
            if (_jspx_meth_c_005fif_005f1(_jspx_th_render_005flink_005f0, _jspx_page_context, _jspx_push_body_count_render_005flink_005f0))
              return true;
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_c_005fif_005f2(_jspx_th_render_005flink_005f0, _jspx_page_context, _jspx_push_body_count_render_005flink_005f0))
              return true;
            out.write("\r\n");
            out.write("            </div>\r\n");
            out.write("            ");
            if (_jspx_meth_c_005fif_005f3(_jspx_th_render_005flink_005f0, _jspx_page_context, _jspx_push_body_count_render_005flink_005f0))
              return true;
            out.write("\r\n");
            out.write("            ");
            if (_jspx_meth_plma_005fisPreferredPage_005f0(_jspx_th_render_005flink_005f0, _jspx_page_context, _jspx_push_body_count_render_005flink_005f0))
              return true;
            out.write("\r\n");
            out.write("            ");
            if (_jspx_meth_c_005fif_005f4(_jspx_th_render_005flink_005f0, _jspx_page_context, _jspx_push_body_count_render_005flink_005f0))
              return true;
            out.write("\r\n");
            out.write("        </div>\r\n");
            out.write("	");
            int evalDoAfterBody = _jspx_th_render_005flink_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005flink_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005flink_005f0[0]--;
          }
        }
        if (_jspx_th_render_005flink_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005flink_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005flink_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005flink_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005flink_0026_005fhref.reuse(_jspx_th_render_005flink_005f0);
      _jspx_th_render_005flink_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005flink_005f0, _jsp_getInstanceManager(), _jspx_th_render_005flink_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005flink_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005flink_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005flink_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(32,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isFolding}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                <span class=\"fold-icon fonticon fonticon-down-open\"></span>\r\n");
          out.write("                <span class=\"fold-icon fonticon fonticon-right-open hidden\"></span>\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005flink_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005flink_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005flink_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(37,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty iconUrl}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_url_005fresource_005f0(_jspx_th_c_005fif_005f1, _jspx_page_context, _jspx_push_body_count_render_005flink_005f0))
            return true;
          out.write("\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_url_005fresource_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005flink_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  url:resource
    com.exalead.cv360.searchui.view.jspapi.url.ResourceTag _jspx_th_url_005fresource_005f0 = (com.exalead.cv360.searchui.view.jspapi.url.ResourceTag) _005fjspx_005ftagPool_005furl_005fresource_0026_005ffile_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.url.ResourceTag.class);
    boolean _jspx_th_url_005fresource_005f0_reused = false;
    try {
      _jspx_th_url_005fresource_005f0.setPageContext(_jspx_page_context);
      _jspx_th_url_005fresource_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(38,20) name = file type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005fresource_005f0.setFile((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${iconUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_url_005fresource_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_url_005fresource_005f0 = _jspx_th_url_005fresource_005f0.doStartTag();
        if (_jspx_th_url_005fresource_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_url_005fresource_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_url_005fresource_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_url_005fresource_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005furl_005fresource_0026_005ffile_005fnobody.reuse(_jspx_th_url_005fresource_005f0);
      _jspx_th_url_005fresource_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_url_005fresource_005f0, _jsp_getInstanceManager(), _jspx_th_url_005fresource_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005flink_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005flink_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005flink_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(40,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty onClick && not empty itemCssId}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_render_005frenderScript_005f0(_jspx_th_c_005fif_005f2, _jspx_page_context, _jspx_push_body_count_render_005flink_005f0))
            return true;
          out.write("\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005flink_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f0_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(41,20) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f0.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f0 = _jspx_th_render_005frenderScript_005f0.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f0);
          }
          do {
            out.write("\r\n");
            out.write("                        $('.");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(' ');
            out.write('.');
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${itemCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("').on('click', function(){\r\n");
            out.write("                        ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${onClick}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\r\n");
            out.write("                        });\r\n");
            out.write("                    ");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f0);
      _jspx_th_render_005frenderScript_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005flink_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005flink_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005flink_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(48,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty label}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                <div class=\"item-label ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isFolding ? 'label-folder' : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write('"');
          out.write('>');
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${label}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("</div>\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fisPreferredPage_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005flink_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005flink_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:isPreferredPage
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.storage.HasPreferredPage _jspx_th_plma_005fisPreferredPage_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.storage.HasPreferredPage) _005fjspx_005ftagPool_005fplma_005fisPreferredPage_0026_005fvar_005fid_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.storage.HasPreferredPage.class);
    boolean _jspx_th_plma_005fisPreferredPage_005f0_reused = false;
    try {
      _jspx_th_plma_005fisPreferredPage_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fisPreferredPage_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005flink_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(51,12) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fisPreferredPage_005f0.setVar("isPreferredPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(51,12) name = id type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fisPreferredPage_005f0.setId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fisPreferredPage_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fisPreferredPage_005f0 = _jspx_th_plma_005fisPreferredPage_005f0.doStartTag();
        if (_jspx_th_plma_005fisPreferredPage_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fisPreferredPage_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fisPreferredPage_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fisPreferredPage_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fisPreferredPage_0026_005fvar_005fid_005fnobody.reuse(_jspx_th_plma_005fisPreferredPage_005f0);
      _jspx_th_plma_005fisPreferredPage_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fisPreferredPage_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fisPreferredPage_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005flink_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005flink_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f4_reused = false;
    try {
      _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005flink_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaMenu/templates/item.jsp(52,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isPreferredPage}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
      if (_jspx_eval_c_005fif_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                <i class=\"icon-favorite fonticon fonticon-favorite-on\"></i>\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      _jspx_th_c_005fif_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f4_reused);
    }
    return false;
  }
}
