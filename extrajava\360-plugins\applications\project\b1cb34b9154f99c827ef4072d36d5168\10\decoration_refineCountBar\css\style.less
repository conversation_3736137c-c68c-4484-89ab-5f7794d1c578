.mashup{
	.category.repartition-position-left {
		.refinecontainer {
			margin-left: 105px;
		}
	}
	.refinecontainer{
		position: inherit;
	}
	
	.aggregation-bar{
		position: absolute;

		&.position-under {
			height: 100%;
			border-bottom: 1px solid @cblock-border-alt;
		}
		
		&.position-behind {
			height: @line-height;
			margin: 5px;
			background-color: @cblock-border-alt;
		}
		
		&.position-left {
			height: 10px;
			margin-top: 8px;
			border-radius: 3px;
			background-color: @cblock-border-alt;
		}
		
		&.position-right {
			height: 16px;
			margin-top: 2px;
			opacity: 0.3;
			right: 1px;
			border-radius: 3px;
			background-color: @cblock-border-alt;
		}
	}
	
	.category {
        .refinecontainer {
            padding-top: (@line-height /4);
        }
        
        &.refined{
            .aggregation-bar{
                display: none;
            }
        }
	}
}
