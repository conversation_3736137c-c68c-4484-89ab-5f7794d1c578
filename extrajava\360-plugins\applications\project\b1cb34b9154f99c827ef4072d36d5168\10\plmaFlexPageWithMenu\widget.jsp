<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>

<render:import varWidget="widget" varParentEntry="parentEntry" varParentFeed="parentFeed" varFeeds="feeds"/>

<search:getFeed var="feed" feeds="${feeds}"/>
<config:getOption var="wrap" name="wrap" defaultValue="false"/>
<config:getOption var="hasRefinePanel" name="hasRefinePanel" defaultValue="true"/>
<config:getOption var="displayHideButton" name="displayHideButton" defaultValue="true"/>
<config:getOption var="displayOpenButton" name="displayOpenButton" defaultValue="true"/>
<config:getOption var="refinesContainerMaxWith" name="refinesContainerMaxWith"/>
<config:getOption var="refinePanelId" name="refinePanelId"/>
<config:getOption var="buttonsIconSize" name="buttonsIconSize"/>

<config:getOptionsComposite var="additionalVariables" name="variables" mapIndex="true" />

<request:getCookieValue var="refinesContainer_displayState" name="refinesContainer_displayState" defaultValue="expanded"/>

<plma:getLayoutConfig var="layoutConfig" widget="${widget}" id="${plma:getStringParam(widget, 'layoutId', null )}"/>

<widget:widget disableStyles="true" varCssId="cssId" varUcssId="uCssId" extraStyles="${plma:getCssStyles(additionalVariables)}">
    <c:set var="layout" value="${widget.layout}"/>
    <c:set var="widthFormat" value="${layout.widthFormat}"/>
    <c:if test="${widthFormat == null || widthFormat == ''}">
        <c:set var="widthFormat" value="%"/>
    </c:if>

    <c:set var="refineWidth" value="${not empty layoutConfig.refine ? layoutConfig.refine.width : 0}"/>
    <div id="plmaFlexPageMainContainer" class="plmaPageContainer" style="${plma:format('--icons-size:%spx;', buttonsIconSize, '')}">
            <%-- Render Menu column --%>
        <div id="plmaFlexPageMenu" class="plmaPageColumn menuContainer plmaFlexContainer-col-${layoutConfig.menu.width}">
            <plma:createDataWidgetWrapper var="dwwMenu" widgetId="plmaMenu" config="${layoutConfig.menu}"
                                          widgetUUID="${layoutConfig.menu.wuid}"
                                          parentEntry="${parentEntry}" parentFeed="${parentFeed}"/>
            <render:widget dataWidgetWrapper="${dwwMenu}">
                <render:parameter name="menuMode" value="layout"/>
            </render:widget>
        </div>

            <%-- Render main content (now can contain multiple rows and columns) --%>
        <div id="plmaFlexPageMainContent" class="plmaPageColumn plmaMainContainer plmaFlexContainer-col-${100-layoutConfig.menu.width-refineWidth}">
            <c:forEach var="table" varStatus="tableStatus" items="${layout.tables}">
                <c:forEach var="row" items="${table.rows}" varStatus="rowStatus">
                    <config:getOption var="rowId" name="cssId" component="${row}" defaultValue="${cssId}_row_${rowStatus.index}"/>
                    <config:getOption var="rowCssClass" name="cssClass" component="${row}" defaultValue=""/>
                    <div id="${rowId}" class="${not empty rowCssClass ? rowCssClass : ''} plmaFlexRow">
                        <c:forEach varStatus="statusCell" var="cell" items="${row.cells}">
                            <c:set var="flexGrowCSS" value="plmaFlexContainer-col-${plma:getFexGrow(table, statusCell.index)}"/>
                            <config:getOption var="cellId" name="cssId" component="${cell}" defaultValue="${rowId}_cell_${statusCell.index}"/>
                            <config:getOption var="cellCssClass" name="cssClass" component="${cell}" defaultValue=""/>
                            <div id="${cellId}" class="plmaPageColumn ${flexGrowCSS} ${flexColCSS}">
                                <widget:forEachSubWidget widgetContainer="${cell}" feed="${parentFeed}" entry="${parentEntry}">
                                    <render:widget/>
                                </widget:forEachSubWidget>
                            </div>
                        </c:forEach>
                    </div>
                </c:forEach>
            </c:forEach>
        </div>

        <%-- Render refine Panel column --%>
        <c:if test="${not empty layoutConfig.refine && hasRefinePanel}">
            <c:set var="refinesExtraStyles"
                   value="--refines-max-width:${refinesContainerMaxWith}px;--refines-flex-grow:${refineWidth}%;"/>
            <div id="plmaFlexPageRefineContainer"
                 class="plmaPageColumn refinesContainer plmaFlexContainer-col-${refineWidth} ${refinesExtraStyles} refinesPanel state-${refinesContainer_displayState}">
                <c:if test="${displayHideButton == 'true'}">
                    <div class="refinesContainerCollapse refinesPanelToggle">
                        <span class="refinesContainerCollapse fonticon fonticon-fast-forward"/>
                    </div>
                </c:if>
                <c:if test="${displayOpenButton == 'true'}">
                    <div class="refinesContainerExtend refinesPanelToggle">
                        <div><span class="display-mode-expand fonticon fonticon-filter"/></div>
                        <div class="filtersLabel"><i18n:message code="widget.flexpage.filters"/></div>
                    </div>
                </c:if>
                <div class="refinesPanelContent">
                    <plma:createDataWidgetWrapper var="dwwRefine" widgetId="refinePanel" config="${layoutConfig.refine}"
                                                  widgetUUID="${layoutConfig.refine.wuid}" parentWidget="${widget}"
                                                  parentEntry="${parentEntry}" parentFeed="${parentFeed}"/>
                    <render:widget dataWidgetWrapper="${dwwRefine}">
                        <render:parameter name="refinesPanelMode" value="layout"/>
                        <render:parameter name="refinesPanelId" value="${refinePanelId}"/>
                    </render:widget>
                </div>
            </div>
        </c:if>
    </div>

    <%-- Generate preferences lightbox if present in layout config --%>
    <c:if test="${not empty layoutConfig.preferences}">
        <plma:createDataWidgetWrapper var="dwwPreferences" widgetId="preferences" config="${layoutConfig.preferences}"
                                      widgetUUID="${layoutConfig.preferences.wuid}"
                                      parentEntry="${parentEntry}" parentFeed="${parentFeed}"/>
        <div class="wuid plmaLightboxWidget ${layoutConfig.preferences.selector} ${layoutConfig.preferences.wuid}_preferences" id="${layoutConfig.preferences.wuid}_preferences">
            <div class="plmaLightboxWidgetWrapper">
                <render:widget dataWidgetWrapper="${dwwPreferences}"/>
            </div>
        </div>
    </c:if>

    <%-- Generate user guide lightbox if present in layout config --%>
    <c:if test="${not empty layoutConfig.userGuide}">
        <plma:createDataWidgetWrapper var="dwwUserGuide" widgetId="plmaUserGuide" config="${layoutConfig.userGuide}"
                                      widgetUUID="${layoutConfig.userGuide.wuid}"
                                      parentEntry="${parentEntry}" parentFeed="${parentFeed}"/>
        <div class="wuid plmaLightboxWidget ${layoutConfig.userGuide.selector} ${layoutConfig.userGuide.wuid}_user_guide" id="${layoutConfig.userGuide.wuid}_user_guide">
            <div class="plmaLightboxWidgetWrapper">
                <render:widget dataWidgetWrapper="${dwwUserGuide}"/>
            </div>
        </div>
    </c:if>

    <%-- Generate application information lightbox if present in layout config --%>
    <c:if test="${not empty layoutConfig.about}">
        <plma:createDataWidgetWrapper var="dwwAbout" widgetId="plmaAbout" config="${layoutConfig.about}"
                                      widgetUUID="${layoutConfig.about.wuid}"
                                      parentEntry="${parentEntry}" parentFeed="${parentFeed}"/>
        <div class="wuid plmaLightboxWidget ${layoutConfig.about.selector} ${layoutConfig.about.wuid}_about" id="${layoutConfig.about.wuid}_about">
            <div class="plmaLightboxWidgetWrapper">
                <render:widget dataWidgetWrapper="${dwwAbout}"/>
            </div>
        </div>
    </c:if>
</widget:widget>


<render:renderScript position="READY">
    new PLMACollapsibleContainer('${uCssId}', {});
    <c:if test="${not empty layoutConfig.about}">
        (function(){
        var button = $('.${layoutConfig.about.selector}');
        if ($ && $.fn.plmalightbox){
        button.plmalightbox({
        container: $('#${layoutConfig.about.wuid}_about').parent(),
        uCssId: '${layoutConfig.about.wuid}_about',
        showOverlay: true,
        content: $('#${layoutConfig.about.wuid}_about .plmaLightboxWidgetWrapper'),
        title: <c:choose><c:when
            test="${not empty layoutConfig.about.title}">"${layoutConfig.about.title}"</c:when><c:otherwise>null</c:otherwise></c:choose>,
        extraCss: '${layoutConfig.about.selector}',
        triggerEvent: '${layoutConfig.about.event}',
        onInit: function(){},
        onShow: function(){},
        onHide: function(){},
        draggable: false
        });
        }
        })();
    </c:if>
    <c:if test="${not empty layoutConfig.userGuide}">
        (function(){
        var button = $('.${layoutConfig.userGuide.selector}');
        if ($ && $.fn.plmalightbox){
        button.plmalightbox({
        container: $('#${layoutConfig.userGuide.wuid}_user_guide').parent(),
        uCssId: '${layoutConfig.userGuide.wuid}_user_guide',
        showOverlay: true,
        content: $('#${layoutConfig.userGuide.wuid}_user_guide .plmaLightboxWidgetWrapper'),
        title: <c:choose><c:when
            test="${not empty layoutConfig.userGuide.title}">"${layoutConfig.userGuide.title}"</c:when><c:otherwise>null</c:otherwise></c:choose>,
        extraCss: '${layoutConfig.userGuide.selector}',
        triggerEvent: '${layoutConfig.userGuide.event}',
        onInit: function(){},
        onShow: function(){},
        onHide: function(){},
        draggable: false
        });
        }
        })();
    </c:if>
    <c:if test="${not empty layoutConfig.preferences}">
        (function(){
        var button = $('.${layoutConfig.preferences.selector}');
        if ($ && $.fn.plmalightbox){
        button.plmalightbox({
        container: $('#${layoutConfig.preferences.wuid}_preferences').parent(),
        uCssId: '${layoutConfig.preferences.wuid}_preferences',
        showOverlay: true,
        content: $('#${layoutConfig.preferences.wuid}_preferences .plmaLightboxWidgetWrapper'),
        title: <c:choose><c:when
            test="${not empty layoutConfig.preferences.title}">"${layoutConfig.preferences.title}"</c:when><c:otherwise>null</c:otherwise></c:choose>,
        extraCss: '${layoutConfig.preferences.selector}',
        triggerEvent: '${layoutConfig.preferences.event}',
        onInit: function(){},
        onShow: function(){},
        onHide: function(){},
        draggable: false
        });
        }
        })();
    </c:if>
</render:renderScript>
