<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import parameters="feed,paginationFeedId,itemsDef,groupsDef"/>

<c:if test="${paginationFeedId == feed.id}">
	<search:getPaginationInfos varCurrentPage="currentPage" varLastPage="lastPage" varPerPage="perPage" varTotal="total" feed="${feed}" />
	tDataSetBuilder.setPaginiationInfo({
		currentpage: ${currentPage}, 
		lastpage: ${lastPage}, 
		perpage: ${perPage}, 
		total:${total} 
	});
</c:if>

<search:forEachEntry var="entry" feed="${feed}" varStatus="entryStatus">
	<render:template template="entry.jsp">
		<render:parameter name="feed" value="${feed}"/>
		<render:parameter name="entry" value="${entry}"/>
		<render:parameter name="itemsDef" value="${itemsDef}"/>
		<render:parameter name="groupsDef" value="${groupsDef}"/>			
	</render:template>
	
	<c:forEach var="subfeed" items="${entry.subfeeds}">
		<render:template template="feed.jsp">
			<render:parameter name="feed" value="${subfeed}"/>
			<render:parameter name="paginationFeedId" value="${paginationFeedId}"/>
			<render:parameter name="itemsDef" value="${itemsDef}"/>
			<render:parameter name="groupsDef" value="${groupsDef}"/>			
		</render:template>
	</c:forEach>
</search:forEachEntry>
