<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import parameters="uCssId,feed,paginationFeedId"/>

<config:getOptionsComposite var="itemsDef" name="itemsDef" mapIndex="true" doEval="false"/>
<config:getOptionsComposite var="groupsDef" name="groupsDef" mapIndex="true" doEval="false"/>

let tDataSetBuilder = new TimelineDataSetBuilder('${uCssId}');
<render:template template="feed.jsp">
	<render:parameter name="feed" value="${feed}"/>
	<render:parameter name="paginationFeedId" value="${paginationFeedId}"/>
	<render:parameter name="itemsDef" value="${itemsDef}"/>
	<render:parameter name="groupsDef" value="${groupsDef}"/>			
</render:template>
tDataSetBuilder.update();
