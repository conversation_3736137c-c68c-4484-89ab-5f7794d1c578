<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>

<render:import varWidget="widget" varFeeds="feeds" />


<config:getOption name="displayHeader" var="displayHeader"/>
<c:if test="${displayHeader}">
	<config:getOption name="title" var="title"/>
</c:if>
<config:getOption name="facetName" var="facetName"/>
<config:getOption name="aggregation" var="aggregation"/>
<config:getOption name="colors" var="colors" defaultValue="#5E61A3, #597DAB, #5296B5, #4AB2C2, #42CFCC, #7557BD, #8052B0, #874FA3, #944A99, #8F3869, #BD4D6E, #B04A42, #A34F36, #995E38, #8F7038, #BD8538, #B08A2E, #A38C24, #999426, #668F29"/>
<config:getOption name="thickness" var="thickness" defaultValue="32"/>
<c:set var="colorListTemp" value="${fn:split(colors,',')}"/>
<list:new var="colors"/>
<c:forEach var="item" items="${colorListTemp}">
	<list:add value="${fn:trim(item)}" list="${colors}"/>
</c:forEach>

<config:getOption name="filter" var="filter"/>
<config:getOption name="filterList" var="filterList"/>
<c:set var="filterListTemp" value="${fn:split(filterList,',')}"/>
<list:new var="filterList"/>
<c:forEach var="item" items="${filterListTemp}">
	<list:add value="${fn:trim(item)}" list="${filterList}"/>
</c:forEach>

<search:getFacet var="facet" facetId="${facetName}" feeds="${feeds}"/>

<config:getOptionsComposite name="categoryStyle" var="categoryStyleConfig" mapIndex="true"/>
<map:new var="categoryStyles"/>
<c:if test="${not empty categoryStyleConfig}">
	<c:forEach items="${categoryStyleConfig}" var="categoryStyle">
		<map:put key="${categoryStyle.categoryName}" value="${categoryStyle.color}" map="${categoryStyles}"/>
	</c:forEach>
</c:if>


<widget:widget varUcssId="uCssId" extraCss="HorizontalRepartition">
	
	<c:set var="displayedTitle" value="${empty title ? facetLabel : title}"/>


	<c:if test="${displayHeader}">
		<widget:header>
			<c:choose>
				<c:when test="${not empty title}">
					${title}
				</c:when>
				<c:otherwise>
					<search:getFacetLabel facet="${facet}"/>
				</c:otherwise>
			</c:choose>
		</widget:header>
	</c:if>
	
	<c:set var="total" value="0"/>
	<search:forEachCategory root="${facet}" var="category">
		<search:getCategoryValue var="value" name="${aggregation}" category="${category}"/>
		<c:set var="total" value="${total + value}"/>
	</search:forEachCategory>
	
	<widget:content extraCss="container">
		
		<div class="bar card" style="height: ${thickness}px">
			<c:choose>
				<c:when test="${filter == 'Exclude'}">
					<search:forEachCategory root="${facet}" var="category" varStatus="loop">
						<c:if test="${not list:contains(filterList, category.description)}">
							<render:template template="templates/segment.jsp">
								<render:parameter name="category" value="${category}"/>
								<render:parameter name="aggregation" value="${aggregation}"/>
								<render:parameter name="categoryStyles" value="${categoryStyles}"/>
								<render:parameter name="colors" value="${colors}"/>
								<render:parameter name="total" value="${total}"/>
								<render:parameter name="index" value="${loop.index}"/>
							</render:template>
						</c:if>
					</search:forEachCategory>
				</c:when>
				<c:when test="${filter == 'Include'}">
					<map:new var="categories"/>
					
					<search:forEachCategory root="${facet}" var="category">
						<map:put key="${category.description}" value="${category}" map="${categories}"/>
					</search:forEachCategory>
					
					<c:forEach var="categoryName" items="${filterList}" varStatus="loop">
						<c:if test="${not empty categories[categoryName]}">
							<render:template template="templates/segment.jsp">
								<render:parameter name="category" value="${categories[categoryName]}"/>
								<render:parameter name="aggregation" value="${aggregation}"/>
								<render:parameter name="categoryStyles" value="${categoryStyles}"/>
								<render:parameter name="colors" value="${colors}"/>
								<render:parameter name="total" value="${total}"/>
								<render:parameter name="index" value="${loop.index}"/>
							</render:template>
						</c:if>
					</c:forEach>
				</c:when>
			
			</c:choose>
		</div>
	</widget:content>
	


</widget:widget>
