.mashup .flipContainer{
	height: 100%;
	flex: 1 0 0;
	flex-grow: 1;
	&.full-size-active {
		position: fixed;
		position: -ms-page;
	    width: 100%;
	    height: 100%;
	    top: 0;
	    left: 0;
	    padding: 0;
	    z-index: 2000;
	    background-color: rgba(0, 0, 0, 0.3);
	    .content {
	    	height: 100% !important;
	    	.plmaCharts {
		    	display: flex !important;
		    	height: 100%;
    			padding: 50px;
			    flex-direction: column;
			    .widgetHeader {
			    	flex-basis: 20px;
			    }
			    .widgetContent {
			    	flex: 1;
			    	.chart-wrapper{
			    		height: 100% !important;
			    		.highChartsSVGWrapper {
			    			height: 100% !important;
			    		}
			    	}
			    }
	    	}
	    	.plmaTable {
		    	display: flex !important;
			    flex-direction: column;
    			padding: 50px;
			    .widgetHeader {
			    	flex-basis: 20px;
			    }
			    .widgetContent {
			    	flex: 1;
			    }
	    	}
	    }
	}
	.content{
		height: 100%;
		position: relative;
		overflow: auto;
		perspective: 800px;
		min-height: 200px;
		.content-wrapper{
			position: absolute;
			width: 100%;
			height: 100%;
			transform-style: preserve-3d;
    		transition: transform 1s;
			div.searchWidget{
				transition: ease-out 0.5s transform;
				backface-visibility: hidden;
				position: absolute;
				width: 100%;
				transform-style: preserve-3d;
				display: block;
				padding: 0;
			}
			.searchWidget:nth-child(1){
				z-index: 2;
				transform: rotateY(0deg);
			}
			.searchWidget:nth-child(2){
				transform: rotateY(-180deg);
				height: 100%;
				overflow: hidden;
			}
			&.switched{
				.searchWidget:nth-child(1){
					transform: rotateY(180deg);
				}
				.searchWidget:nth-child(2){
					transform: rotateY(0deg);
					z-index: 10;
					overflow: auto;
				}
			}
		}
	}
}