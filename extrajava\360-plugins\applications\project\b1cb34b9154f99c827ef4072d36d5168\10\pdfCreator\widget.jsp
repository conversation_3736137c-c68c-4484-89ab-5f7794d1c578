<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="security" uri="http://www.exalead.com/jspapi/security" %>

<render:import varWidget="widget" varFeeds="feeds" />
<render:import parameters="getPreviewContainer,previewHandler" ignore="true" />

<config:getOption name="iconCss" var="iconCss"/>
<config:getOption name="label" var="label"/>
<config:getOption name="showLabel" var="showLabel"/>
<config:getOption name="onInit" var="onInit" defaultValue="function(){}"/>
<config:getOption name="onDone" var="onDone" defaultValue="function(){}"/>

<widget:widget varUcssId="uCssId" extraCss="plmaButton pdfCreator" varCssId="cssId">
	<span class="button-content">
		<c:if test="${not empty iconCss}">
			<i class="${iconCss}" title="${label}"></i>
		</c:if>
		<c:if test="${showLabel}">
			<span class="button-label">${label}</span>
		</c:if>
	</span>
</widget:widget>

<render:renderScript position="READY">
	<config:getOption name="fileName" var="fileName" defaultValue="plma_page"/>
	<config:getOption name="fileOptions" var="fileOptions" defaultValue="{}"/>
	<config:getOptions name="widgetOptions" var="widgetOptions" defaultValue="{}"/>
    (function(){
		<render:template template="javascript.jsp">
            <render:parameter name="buttonSelector" value=".${uCssId} > .button-content" />
            <render:parameter name="onInit" value="${onInit}" />
			<render:parameter name="onDone" value="${onDone}" />
            <render:parameter name="fileName" value="${fileName}" />
            <render:parameter name="fileOptions" value="${fileOptions}" />
            <render:parameter name="widgetOptions" value="${widgetOptions}" />
            <render:parameter name="getPreviewContainer" value="${getPreviewContainer}" />
            <render:parameter name="previewHandler" value="${previewHandler}" />
        </render:template>
    })();
</render:renderScript>
