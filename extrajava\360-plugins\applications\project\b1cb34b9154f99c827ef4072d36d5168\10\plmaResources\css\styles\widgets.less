@import "utils.less";
@import "../polyfills.less";

// Widget Theme
.m-no-padding(){
	padding: 0;
	.widgetContent{
		padding: 0;
	}
}
.m-no-background(){
	background: none;
	.widgetHeader, .widgetContent{
		background: none;	
	}
}
.m-no-border(){
	border: none;
	.widgetHeader, .widgetContent{
		border: none;
	}
}
	

.mashup.mashup-style{
	.searchWidget {
		.metrics {
			.metric-wrapper {
				.main {
				}
			}
		}
		&.no-space-after{
			margin-bottom: 0px;
		}
		
		&.no-padding{
			.m-no-padding();
		}
		
		&.stacked-top {
			padding-bottom: 0;
		}
		&.stacked{
			padding-top: 0;
			padding-bottom: 0;
			.widgetContent, .widgetHeader{
				border-radius: 0;
				border-top:0;
			}	
		}
		
		&.no-background{
			.m-no-background();
		}
		
		&.no-border{
			.m-no-border();
		}
		
		
		&.no-wrapping{
			.m-no-background();
			.m-no-border();
		}
		
		&.no-header-widget{
			.widgetHeader{
				border: 0;
			}
		}
		
		.widgetHeader{
			position: relative;
			padding-right: 0;
			.display-flex();
			.widgetTitle {
				padding-right: @line-height;
				.flex-grow(1);
				.flex-shrink(1);
				.flex-basis(0%);
			}
			.widgetHeaderButton{
				color: @ctext-weak;
				font-size: 1.35em;
				width: 44px;
				float: right;
				cursor: pointer;
				.flex(0 0 auto);
				.transition(@transition-quick, "~color;");
				margin: 0;
				
				&:hover{
					color: @ctext-bold;
					.transition(@transition-quick, "~color;");
				}
				&.active{
					color: @clink;
				}
			}
		}
		
		.widgetContent {
			.secondaryTitle {
				font-family: "3ds";
				color: @ctext-weak;
				padding: (@line-height / 4) 0 0 (@line-height / 2);
				text-transform: uppercase;
			}
			
			.mainValue{
				font-size: @xl-font;
			    line-height: @xl-font;
			    padding: (@line-height/2) 0;
			}
		}

		.doc-button {
			float: right;
			cursor: pointer;
			font-size: 20px;
			margin-top: 4px;
		}
		.doc-container {
			position: absolute;
			top: 0;
			width: 100%;
			height: 100%;
			background-color: white;
			z-index: 10;
			&.hidden {
				display: none;
			}
			.container {
				padding: 15px;
				li {
					list-style: inherit;
					margin-left: 20px;
				}
			}
			.close-doc {
				position: absolute;
				right: 10px;
				top: 18px;
				font-size: 20px;
				cursor: pointer;
				opacity: 0.1;
				&:hover {
					opacity: 1;
				}
			}
		}
	}
}

.widgetHeader {
	.widgetHeaderIcon {
		font-size: 22px;
		position: relative;
		left: -12px;
		top: -1px;
	}
}

// Header div with title div on the left and toolbar on the right
// It's a generic UC where we want to display title and icons on the right for actions
// Used for example by result list layout or view, hit details widget ...
// Also added a way to configure right hand icons size through 'icons-size' variable (default 14px)
.widgetHeaderWithToolbar {
	.display-flex();
	.flex-direction(row);
	.justify-content(space-between);
	width: inherit;
	padding: 10px;
	align-items: center;
	min-height: 23px;

	.headerTitle {
		padding-left: 10px;

		.title {
			font-size: 18px;
			color: @clink;
		}
	}

	.headerToolbar {
	    cursor: pointer;
		.display-flex();
		font-size: var(--icons-size, @m-font);
		padding-right: 10px;

		span.insert-before, span.insert-after {
			border-left: 1px solid;
			border-color: #e2e4e3;
		}

		span.fonticon, i.fonticon{
			// To avoid having icons too close...
			margin: 0 5px;
		}
	}
}

.plmalightbox .plmalightbox-box.export-to-3dspace{
    overflow-y: auto;
    padding:10px;
    min-width: 80%;
    max-width: 90%;
	min-height: 50%;
    max-height: 90%;
    margin: 0px 20px;
	&.SnapshotExport{
		.preview-content .pdf-preview-pane{
			height: 300px;
		}
	}
	.plmalightbox-contentwrapper{
		height: 100%;
	}
	#export-container-global {
		width: 100%;
		margin-top: 10px;
		.container {
			background-color: white;
			display: flex;
			flex-direction: column;
			height: ~'calc(100% - 0px)';
			.exp-context-container{
			    font-size: 1.2em;
                line-height: 100%;
                padding: 10px;
                padding-top: 0px;
                position: relative;
				border-bottom: 1px solid;
				.tabs-container .contents{
                    min-height: 90px;
                }
                .export-done-overlay{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(209, 212, 212, 0.4);
                    color: black;
                    border: 1px dashed;
                    .fonticon{
                        font-size: 4em;
                        margin-bottom: 0.5em;
                    }
                    .label{
                        font-size: 1.5em;
                    }
                }
                .controls{
                    display: flex;
                    margin-bottom: 10px;
                    .add-new-document{
                        margin-bottom: 0px;
                        .label{
                            width: 80px;
                        }
                        &.full-width{
                            flex-grow: 1;
                        }
                    }
                    .export-button{
                        margin: 2px 10px;
                        font-size: 16px;
                    }
                    .drop-area {
                        text-align: center;
                        font-size: 2em;
                        line-height: 1.5em;
                        border: 2px dashed;
                        flex-grow: 1;
						
                        .label{
                            font-size: 0.6em;
                        }
						.disabled-overlay{
							display: none;
						}
						&.disabled{
							position: relative;
							color: #EA4F37;
							.disabled-overlay{
								display: block;
								position: absolute;
								top: 0;
								left: 0;
								width: 100%;
								height: 100%;
								background-color: #f1f1f1;
								opacity: 0.4;
							}
						}
                    }
					.selectors-list{
						margin: auto;
						margin-right: 15px;
						flex-grow: 1;
						.bookmark-selector,
						.document-selector{
							.selector{
								font-size: 16px;
								width: 100%;
							}
						}
						.bookmark-selector{
							margin-bottom: 7px;
						}
					}
                }

                .document-info-container{
                    width: 100%;
                    margin-bottom: 10px;
                    display: flex;
                    .info-container{
                        width: 50%;
                        border: 1px solid;
                        margin-right: 5px;
                        padding: 5px;
                        border-radius: 5px;
						
						&.document-info{
							flex-grow: 1;
						}
                        .section-title{
                            padding: 0px 5px;
                            font-size: 1.1em;
                            img{
                                height: 1.2em;
                            }
                        }
                        .label-conatiner{
                            display: flex;
                            font-size: 1.0em;
                            line-height: 100%;
                            margin-bottom: 0px;
                            margin-top: 10px;
                            .required{
                                color: #E87B00;
                                font-size: 1.2em;
                                font-weight: bold;
                            }
                            .label{
                                margin-right: 10px;
                                width: 20%;
                            }
                            .value{
                                flex-grow: 1;
                                &.select-editable{
                                    display: flex;
                                    select, input{
                                        flex-grow: 1;
                                    }
                                    select{
                                        padding: 2px;
                                        option:disabled{
                                            color: #EA4F37;
                                        }
                                    }
                                }
                            }
                            .add-new-file{
                                margin: 0px;
                            }
                        }
                        .label-conatiner.has-error{
                            .label{
                                font-weight: bold;
                            }
                            .value{
                                box-shadow: 1px 1px 3px #CC092F;
                            }
                        }
                    }
                }
            }
            .preview-container{
                flex-grow: 1;
				display: flex;
				flex-direction: column;
                .preview-header{
                    text-align: center;
                    font-size: 1.5em;
                    line-height: 100%;
                    padding: 5px 5px;
					display: flex;
                    .download-link{
                        float: right;
                    }
                }
                .preview-content{
					flex-grow: 1;
					padding-bottom: 7px;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
            }
		}
	}
}
