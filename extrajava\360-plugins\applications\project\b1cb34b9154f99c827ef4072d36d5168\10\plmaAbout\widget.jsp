<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds" />
<render:import parameters="widgetCfg" ignore="true"/>

<c:if test="${empty widgetCfg}">
	<plma:getConfig var="widgetCfg" id="about" widget="${widget}"/>
</c:if>

<i18n:message code="label.about" var="label_about"/>
<i18n:message code="label.version" var="label_version"/>
<i18n:message code="label.revision" var="label_revision"/>
<i18n:message code="label.build-date" var="label_date"/>

<c:if test="${plma:hasApplicationConfig('ProductInfo')}">
	<plma:productInfo var="productInfo" varName="name" varVersion="version" varRevision="revision" varDate="buildDate"/>

	<widget:widget extraCss="plmaAbout">
		<widget:header>${label_about}</widget:header>

		<widget:content>
			<div class="app-title">
				<div class="app-name">${name}</div>
				<div class="app-author"><a href="http://www.3ds.com">Dassault Syst&eacute;mes</a></div>
			</div>

			<ul class="app-build">
				<li>
					<span class="label">${label_version}</span>
					<span class="value">${version}</span>
				</li>
				<li>
					<span class="label">${label_revision}</span>
					<span class="value">${revision}</span>
				</li>
				<li>
					<span class="label">${label_date}</span>
					<span class="value">${buildDate}</span>
				</li>
			</ul>

			<c:if test="${widgetCfg.showLastIndexTime}">
				<plma:getLastIndexTime var="lastIndexTime" buildGroupNames="${widgetCfg.buildGroup}" />
				<c:if test="${lastIndexTime != null}">
					<div class="last-indexing-time">
						<i18n:message code="label.last-indexing-date" /> ${lastIndexTime}
					</div>
				</c:if>
			</c:if>

		</widget:content>
	</widget:widget>
</c:if>
