/*
* Hits Collection Manager is global object to manage the Hit Collections created.
* This has to be loaded as part of Hearder JS, to make sure the HitsCollectionManager is
* Available for individual buttons and recentSearches  Instances. Loading at html header position
* is handled in widget.xml.
*/
(function(window){
	'use strict';
	if (window.HitsCollectionManager !== undefined) {
		throw new Error("Can't instantiate: window.HitsCollectionManager is already taken");
	}
	
	var HitsCollection = function (options) {
		this.initialized = $.Deferred();
		
		this.options = options;
		if(this.options.minDisplaySize < 1){
			throw "MinDisplaySize can not be 0. Must be 1 or more.";
		}
		this.$widget = this.options.uCssId? $('.wuid.' + this.options.uCssId) : $();
		this.$elements = {
			DETAIL_WRAPPER: 	this.$widget,
			LABEL_SECTION:		this.$widget.find('.label-section'),
			LABEL:				this.$widget.find('.label-section .title'),
			COUNTER:			this.$widget.find('.label-section .counter'),
			BTN_DISPLAY:		this.$widget.find('.action-details'),
			BTN_ADD_ENTRIES:	this.$widget.find('.action-details-multi-select.addEntries'),
			BTN_CLEAR_ENTRIES:	this.$widget.find('.action-details-multi-select.clearEntries'),
			BTN_CLEAR:			this.$widget.find('.action-clear')	,
			BTN_POPUP_TOGGLE:	this.$widget.find('.collection-header .fonticon-open-down'),
			LABEL_ADD_ENTRIES : this.$widget.find('.action-details-multi-select.addEntries .multiselect-title'),
			LABEL_CLEAR_ENTRIES : this.$widget.find('.action-details-multi-select.clearEntries .multiselect-title'),
			LABEL_COLLECTION_OPTION : this.$widget.find('.collection-header .fonticon-open-down')
		};
		
		this.buttonsData = {};
		this.storage = new HitsStorage({
            store: this.options.store,
            storeKey: this.options.storeKey,
            storeType: this.options.storeType,
            limit: this.options.limit,
            limitStrategy: this.options.limitStrategy
        });
		this.dataHelper = {
		    getWidgetSelector: function(){
                return this.options.uCssId ? '.wuid.' + this.options.uCssId : '';
            }.bind(this),
			getQuery: function(){
				return HitsCollection.Utils.buildQuery(this.storage.getEntries(), this.options.detailsView.metaForId)
			}.bind(this),
			getEntryList: function(){
				return HitsCollection.Utils.getCopy(this.storage.getEntries())
			}.bind(this) 
		};
		this._init();

		return this;
	};
	HitsCollection.prototype._promise = function () {
		return this.initialized.promise();
	}
	
	HitsCollection.STATUS_INIT = 'init';
	HitsCollection.STATUS_CLEARED = 'cleared';
	HitsCollection.STATUS_UPDATED = 'updated';
	HitsCollection.STATUS_TOGGLE = 'toggle-display';
	HitsCollection.STATUS_SWITCH = 'switch-display';
	HitsCollection.CLASS_NO_UPDATES = 'no-updates';
	HitsCollection.CLASS_DISPLAYED = 'collection-displayed';
	HitsCollection.URL_PARAM = 'viewcollection';
	HitsCollection.STATUS_ADD_MULTIPLE ='added'
	HitsCollection.prototype._init = function () {
	    var helper = {
			registerButton: this._registerButton.bind(this),
			addEntry: this._addEntry.bind(this)
		};

        /*Initialize the Store and then Update UI*/
        this.initialized = $.Deferred();
		this.storage.init().then(function(loaded){
			this._updateCollectionUI({ what: HitsCollection.STATUS_INIT });
			this.$elements.BTN_CLEAR.click(this._clearAll.bind(this));
			this.$elements.BTN_POPUP_TOGGLE.click(this._openButtonPopup.bind(this))
			this.$elements.BTN_ADD_ENTRIES.click(this._addMaxSearchesToCompare.bind(this));
			this.$elements.BTN_CLEAR_ENTRIES.click(this._clearAll.bind(this));
			this.$elements.LABEL_ADD_ENTRIES.text(this.options.labels.addMultipleEntry.replace("{limit}", this.options.limit));
			this.$elements.LABEL_CLEAR_ENTRIES.text(this.options.labels.clearMultipleEntry.replace("{collectionDef.id}", this.options.id));
			this.$elements.LABEL_COLLECTION_OPTION.attr('title' ,this.options.labels.collectionOptions.replace("{collectionDef.id}", this.options.id));
			if(this.options.detailsView.activateToggle == true){
				this.$widget.addClass('toggle-activated');
				var data = $.extend({}, this.dataHelper, { what: HitsCollection.STATUS_TOGGLE });
				this.$elements.BTN_DISPLAY.click(this._toggleDisplayHits.bind(this, data));
			}
			if(this.options.detailsView.displayHitsMode == 'custom'){
				var data = $.extend({}, this.dataHelper, { what: HitsCollection.STATUS_INIT });
				var fn = $.isFunction(this.options.detailsView.onInit)? this.options.detailsView.onInit : $.noop;
				fn(data);
			}
			this.initialized.resolve(helper);
		}.bind(this));		
	};
	
	HitsCollection.prototype.isDisplayed = function () {
		return this.options.detailsView.activateToggle == false || 
			this.$widget.hasClass(HitsCollection.CLASS_DISPLAYED);
	}


	 HitsCollection.prototype._addMaxSearchesToCompare = function (data) {
		var _this = this;
		var added =0;
		this.storage.init()
		var count = _this.storage.entryList.length;
		if(_this.storage.entryList.length >= this.options.limit){
			$.notify(this.options.alerts.limitReached, 'error' );
			return;
		}
		 $(".plmaResultList .scroll-container .hit").each(function(index , hitElement){
			this.initialized = $.Deferred();
			let entryToAdd = {'id' : hitElement.getAttribute('data-uri')}
			// check wheater the entry present in localstorage or not and upfate the list
			let isPresent = _this.storage.entryList.some(entry => entry.id === entryToAdd.id);
			//to be ask whterther clear the list before adding fierts max searc ot, check whether the entry present or not and then add it.
			if(index < (_this.storage.options.limit) ){
			   if(isPresent!=true && added < (_this.options.limit - count)){
				let cssSelector = $(hitElement).find(".subwidgets .compare-button").attr('id');
				let btndata = { cssSelector: '#' + cssSelector +' i',
				entry: {
				 'id' : hitElement.getAttribute('data-uri'),
				 	'url': HitsViewInPageReload.getEntryUrl() + 'hitElement.getAttribute("data-uri")'
				}
			}
				added++;
				_this._updateEntry(btndata , HitsCollection.STATUS_ADD_MULTIPLE);
				}
			} else{
				return false;
			}})
			_this._notifyUpdateStatus(HitsStorage.ADDED)
	 }


	 HitsCollection.prototype._openButtonPopup = function () {
		if(this.$widget.find('.button-popup').hasClass('hidden')){
			this.$widget.find('.button-popup').removeClass('hidden');
		}
		else{
			this.$widget.find('.button-popup').addClass('hidden');
		}
	}
	HitsCollection.prototype._toggleDisplayHits = function (data) {
		if(this.options.detailsView.showInPopup == true){
			if(this.lightbox == undefined ){
				this.lightbox = new PLMALightbox({
					title: this.$widget.find('.collection-header').clone(),
					content: this.$elements.DETAIL_WRAPPER.find('.collection-details'),
					extraCss: 'recentSearches hit-details-popup collection-'+this.options.id,
					onShow: (function(){
						this.$widget.toggleClass(HitsCollection.CLASS_DISPLAYED);
						this.$elements.DETAIL_WRAPPER.find('.counter').text(this.$elements.COUNTER.text());
						this._showHits(data);
					}).bind(this),
					onHide: (function(){
						this.$widget.toggleClass(HitsCollection.CLASS_DISPLAYED);
						this._hideHits(data);						
					}).bind(this),
					draggable : false
				});
				this.$elements.DETAIL_WRAPPER = $('.recentSearches.hit-details-popup.collection-'+this.options.id);
				this.$elements.BTN_CLEAR.remove();
				this.$elements.DETAIL_WRAPPER.find('.action-clear').click(function(){
					this._clearAll();
					this.lightbox.hide();
				}.bind(this));
			}
			if(this.isDisplayed()){
				this.lightbox.hide();
			}else{
				if(data.what != HitsCollection.STATUS_SWITCH){
					this._switchCollectionDisplay(data);
				}
				this.lightbox.show();
			}
			return;
		}
		
		if(data.what != HitsCollection.STATUS_SWITCH && this.$widget.hasClass(HitsCollection.CLASS_DISPLAYED) == false){
			// This collection is hidden and we are here to show it.
			// WHile doing so make sure we close existing collection display if any.
			this._switchCollectionDisplay(data);
		}
		this.$widget.toggleClass(HitsCollection.CLASS_DISPLAYED);
		if(this.$widget.hasClass(HitsCollection.CLASS_DISPLAYED)){
			this._showHits(data);
		}else{
			this._hideHits(data);
		}			
	}
	
	HitsCollection.prototype._switchCollectionDisplay = function () {
		// Handle Switching of the Collection display(e.g. Compare to Bookmark).
		Object.keys(hitsCollectionList).every(function(key){
			if( key != this.options.id && 
				hitsCollectionList[key].options.detailsView.activateToggle == true &&
				hitsCollectionList[key].isDisplayed() ){				
				var data = $.extend({}, hitsCollectionList[key].dataHelper, { what: HitsCollection.STATUS_SWITCH, toId: this.options.id });
                hitsCollectionList[key]._toggleDisplayHits(data);
			}			
			return true; 
		}.bind(this));
	}
	
	HitsCollection.prototype._updateCollectionUI = function (data) {
		if(this.$widget.length == 0){ 
			return; // If No Widget there is nothing update on collection UI. Runs only in 'pin', 'listener' mode.
		}

		// Switch Collection State enabled/disabled. and update all.
				
		// Update Header Section
		var count = this.storage.getEntries().length;
		this.$elements.DETAIL_WRAPPER.attr('data-count', count);
		if(count == 0 ){
			this.$widget.find('.collection-header .button-popup .addEntries').addClass('button-enabled');//enable
			this.$widget.find('.collection-header .button-popup .clearEntries').removeClass('button-enabled');//disable
		}
		else if(count == this.options.limit){
			this.$widget.find('.collection-header .button-popup .addEntries').removeClass('button-enabled');//disable;//disable
			this.$widget.find('.collection-header .button-popup .clearEntries').addClass('button-enabled');//enable
		}
		else{
			this.$widget.find('.collection-header .button-popup .addEntries').addClass('button-enabled');//enable
			this.$widget.find('.collection-header .button-popup .clearEntries').addClass('button-enabled');//enable
		}
		if(count >= this.options.minDisplaySize){
			this.$widget.removeClass('collection-disabled');
			this.$elements.LABEL.text(this.options.labels.label);
		}else{
			this.$widget.addClass('collection-disabled');
			this.$elements.LABEL.text(this.options.labels.label? this.options.labels.emptyCollection : '');
		}
		// Update the Counter.
		this.$elements.COUNTER.text(this.options.labels.counter.replace("{count}", count).replace("{limit}", this.options.limit));
		if(this.options.detailsView.activateToggle == true){
			this.$elements.LABEL_SECTION.attr("title", this.options.tooltipTitle != '' ? this.options.tooltipTitle : (this.options.labels.tooltip.replace('{count}', count).replace("{limit}", this.options.limit)));
			 this.$elements.BTN_ADD_ENTRIES.attr("title" ,this.options.labels.addMultipleEntry.replace("{limit}", this.options.limit));
			 this.$elements.BTN_CLEAR_ENTRIES.attr("title" ,this.options.labels.clearMultipleEntry.replace("{collectionDef.id}", this.options.id));

		}
		
		if(data.what == HitsCollection.STATUS_UPDATED && this.$widget.hasClass(HitsCollection.CLASS_NO_UPDATES)){			
			return;
		}
		this._showHits(data);
	};

	HitsCollection.prototype._showHits = function (inputData) {
		var entryList = this.storage.getEntries();
		var data = $.extend({}, this.dataHelper, inputData);
		
		// Entry Updated then call the onupdate. DO not call for init as it is already handled.
		if(data.what == HitsCollection.STATUS_UPDATED){
			var fn = $.isFunction(this.options.detailsView.onDbUpdate)? this.options.detailsView.onDbUpdate : $.noop;
			fn(data);
		}
		
		// If not displayed then no need of any UI updates.
		if(this.isDisplayed() == false){
			return;
		}
		
		if(entryList.length < this.options.minDisplaySize){
			// Hide details if all the entries are removed(this is not clear all call though.
			if(data.what != HitsCollection.STATUS_INIT ){
				$.notify(this.options.alerts.minDisplaySize.replace('{minDisplaySize}', this.options.minDisplaySize), 'warn');
			}
			let timeout = 0;
			if(data.btnData){
				data.btnData.$element.off('click');
				timeout = 500;
			}
			setTimeout(function(data){
				var data = $.extend({}, data, { what: HitsCollection.STATUS_CLEARED });
				if(this.options.detailsView.activateToggle){
					this._toggleDisplayHits(data);
				}else{
					this._hideHits(data);
				}
			}.bind(this, data), timeout);
			return;
		}
		
		switch(this.options.detailsView.displayHitsMode){
			case 'default':
				data.uCssId = this.options.uCssId;
				this._displayHits(data)
					.then(this._afterDisplayHits.bind(this));
			break;
			case 'subwidget':
				data.uCssId = this.options.detailsView.containerCss;				
				if(data.uCssId == undefined || data.uCssId == ''){
					console.error("Undefined Subwidget uCSSId Check the configurations");
					throw new Error("Undefined Subwidget uCSSId Check the configurations");
				}
				this._displayHits(data);
			break;
			case 'custom':
				var fn = $.isFunction(this.options.detailsView.onShow)? this.options.detailsView.onShow : $.noop;
				fn(data);
			break;
			default:
				throw new Error("Unknown displayHits Mode(default,subwidget,custom)!= " + this.options.detailsView.displayHitsMode);
		}
	}	
	
	HitsCollection.prototype._hideHits = function (data) {
		switch(this.options.detailsView.displayHitsMode){
			case 'default':
				this.$elements.DETAIL_WRAPPER.find('.collection-details').empty();
				break;
			case 'subwidget':
				this.$elements.DETAIL_WRAPPER.find('.' + this.options.detailsView.containerCss).empty();
				this.$elements.DETAIL_WRAPPER.find('.' + this.options.detailsView.containerCss).addClass('hidden');
				var fn = $.isFunction(this.options.detailsView.onHide)? this.options.detailsView.onHide : $.noop;
                fn(data);
				break;
			break;
			case 'custom':
				var fn = $.isFunction(this.options.detailsView.onHide)? this.options.detailsView.onHide : $.noop;
				fn(data);
			break;
			default:
				throw new Error("Unknown displayHits Mode(default,subwidget,custom)!= " + this.options.detailsView.displayHitsMode);
		}
	}
	
	HitsCollection.prototype._clearAll = function () {
		if(this.storage.getEntries().length > 0){
			this.storage.getEntries().forEach(function(entry, entryIndex){
				if(this.buttonsData[entry.id] != undefined){
					this._updateButtonUI(HitsStorage.REMOVED, Object.values(this.buttonsData[entry.id]));
				}
			}.bind(this));
			this.storage.clear().then(function(){
				$.notify(this.options.alerts.removeAll, 'warn');
				this._updateCollectionUI({what: HitsCollection.STATUS_CLEARED});
			}.bind(this));
		}
	}
	
	HitsCollection.prototype._addEntry = function (entryData) {
		this.storage.add(entryData).then(function(updateStatus, popedEntries){
			// No button UI update done in this case as just adding the entry.
			// TODO: May need to update the Collection UI.
			this._updateCollectionUI({what: HitsCollection.STATUS_UPDATED});
		}.bind(this));
	}
	
	HitsCollection.prototype._updateEntry= function (btnData , addMultipleStatus) {
		var entryData = btnData.entry;
		this.storage.update(entryData).then(function(updateStatus, popedEntries){
			var entryUpdateStatus = updateStatus;
			if(popedEntries != undefined){
				// Keep this update silent as there will be next one.
				popedEntries.forEach(function(popedEntryData){
					if(this.buttonsData[popedEntryData.id] != undefined){
						this._updateButtonUI(HitsStorage.REMOVED, Object.values(this.buttonsData[popedEntryData.id]));
					}
				}.bind(this));
				entryUpdateStatus = HitsStorage.ADDED; // Poped existing and added entryData
			}

			if(addMultipleStatus !== HitsCollection.STATUS_ADD_MULTIPLE){
				this._notifyUpdateStatus(updateStatus)
			}
			if(entryUpdateStatus != HitsStorage.IGNORED ){ // if IGNORED, No UI Update is required.				
				this._updateButtonUI(entryUpdateStatus, Object.values(this.buttonsData[entryData.id])); 
				this._updateCollectionUI({what: HitsCollection.STATUS_UPDATED, btnData: btnData});
			}
		}.bind(this));
	};
	
	HitsCollection.prototype._notifyUpdateStatus = function(updateStatus) {
		if(updateStatus == HitsStorage.IGNORED ){
			$.notify(this.options.alerts.limitReached, 'error' );			
		}else if(updateStatus == HitsStorage.ADDED && this.options.disableAlert === false) {
			$.notify(this.options.alerts.add, 'info' );
		}else if(updateStatus == HitsStorage.REMOVED && this.options.disableAlert === false){
			$.notify(this.options.alerts.remove, 'info' );
		}else if(updateStatus == HitsStorage.ADDED_REMOVED_OLD ){
			$.notify(this.options.alerts.removeOld, 'warn' );			
		}
	};
	
	HitsCollection.prototype._registerButton = function (btnData) {
		if(this.buttonsData[btnData.entry.id] == undefined){
			this.buttonsData[btnData.entry.id] = {};				
		}
		// As we have siglton collection, the same button can be recreated again and again.
		// e.g. same hit detials is opened multiple times, and the button in inside hit details.
		// To handle this set the selector as the key. so every entry with unique selector have one set of data.
		if(btnData.$element == undefined){
            btnData.$element = $(btnData.cssSelector);
        }
		this.buttonsData[btnData.entry.id][btnData.cssSelector]=btnData

		// 1. Initialize the button state as define in storage
		this._updateButtonUI(this.storage.containsEntry(btnData.entry), [ btnData ]);
		
		// 2. Bind the click event to toggle state
		btnData.$element.addClass('collection-modifier');
		btnData.$element.click(this._updateEntry.bind(this, btnData));
	};
		
	HitsCollection.prototype._updateButtonUI = function(updateStatus, buttonsData) {
		var entryPresentIcon = this.options.buttonConfig.entryPresentIcon;
		var entryAbsentIcon = this.options.buttonConfig.entryAbsentIcon;
		buttonsData.forEach(function(btnData){
			var $button = btnData.$element;
			$button.removeClass(entryPresentIcon +" "+ entryAbsentIcon);
			if(updateStatus == HitsStorage.ADDED){
				$button.addClass(entryPresentIcon);
				$button.attr('title', this.options.labels.removeEntry);
			}
			else{
				$button.addClass(entryAbsentIcon);
				$button.attr('title', this.options.labels.addEntry);
			}
		}.bind(this));
	};
	
	/**
	 * @param {Entry[]} entryList
	 */
	HitsCollection.prototype._displayHits = function (data) {
		var _this = this;
		var client = new PlmaAjaxClient(this.$elements.DETAIL_WRAPPER, {
				spinner: this.options.detailsView.displayHitsMode != 'default'
			});
		// Remove the existing filters or queries else results are complex to understand.
		client.url = new BuildUrl(window.location.path); 
		client.addParameter('q', data.getQuery());
		// Push so easy to understand which collection is getting displayed.
		client.addParameter(HitsCollection.URL_PARAM, this.options.id); 
		client.addParameter(this.options.detailsView.feedName + '.per_page', (this.options.limit==0?20:this.options.limit));		
		client.addWidget(data.uCssId);
		if(this.options.detailsView.displayHitsMode == 'default'){
			client.addParameter('loadColletionDetailsView', 'true');
			var $details = this.$elements.DETAIL_WRAPPER.find('.collection-details');
			$details.css('min-height', '200px');
			$details.showPLMASpinner({overlay: true});
			return client.getWidget(function(widgets, appendScript){
				for (var i = 0; i < widgets.length; i++) {
					if (widgets[i].html && widgets[i].cssId) {
						var $newDetails = $(widgets[i].html).find('.collection-details');
						$details.replaceWith($newDetails.eq(0));						
					}
				}
				$('#mainWrapper').append(appendScript);
				client.remove();
			}, function() {
				client.remove();
				$details.hidePLMASpinner({overlay: true});
			}).then(function () {
				return data;
			});
		}
		this.$elements.DETAIL_WRAPPER.find('.'+data.uCssId).removeClass('hidden');
		this.$elements.DETAIL_WRAPPER.find('.'+data.uCssId).css('min-height', '200px');
		return client.update().then(function () {
			var fn = $.isFunction(this.options.detailsView.onAfterShow)? this.options.detailsView.onAfterShow : $.noop;
			fn(data);			
		}.bind(this));	
	};

	/**
	 * Add style, event handlers after hits retrieval.
	 * @param {RecentSearches.MODE} mode
	 * @param {Entry[]} entryList
	 */
	HitsCollection.prototype._afterDisplayHits = function (data) {		
		var entryList = data.getEntryList();
		var $history = this.$elements.DETAIL_WRAPPER.find('.history');
		var $pagination = this.$elements.DETAIL_WRAPPER.find('.pagination');
		if ($history.children().length) {
			$history.toggleClass('hidden');
			$history.children().each(function (i, element) {
				var $entry = $(element);
				// set link
				var entryId = $entry.find('.entry').data('entryId');
				var entry = entryList.find(function (entry) {
					return entry.id === entryId;
				});
				if (entry) {
					$entry.prop('href', HitsCollection.Utils.sanitizeUrl(entry.url));
				}
				// set icon color
				var $icon = $entry.find('.icon');
				var iconColor = $icon.data('color');
				if (iconColor) {
					var rgb = HitsCollection.Utils.hexToRGB(iconColor);
					$icon.css('background-color', 'rgba(' + rgb.r + ',' + rgb.g + ',' + rgb.b + ',0.3)');
					$icon.find('i').css('color', iconColor);
				}
			});
			// use the order from the history
			entryList.forEach(function (entry) {
				$history.find('[data-entry-id="' + entry.id + '"]').parent().appendTo($history);
			});
		}
		if ($pagination.children().length) {
			$pagination.find('.page-button')
				.on('click', function (e) {
					$pagination.find('.page-button.selected').removeClass('selected');
					var $e = $(e.target);
					$e.addClass('selected');
					var pageNumber = $e.data('pageNb');
					this.$elements.DETAIL_WRAPPER.find('.history').children().each(function (index, elem) {
						var $elem = $(elem);
						if (pageNumber * this.options.detailsView.elemPerPage <= index && index < (pageNumber + 1) * this.options.detailsView.elemPerPage) {
							$elem.removeClass('hidden');
						} else {
							$elem.addClass('hidden');
						}
					}.bind(this));
				}.bind(this))
				.triggerHandler('click');
		} else {
			// no pagination needed: show entries
			this.$elements.DETAIL_WRAPPER.find('.history').children().removeClass('hidden');
		}		
	};
	
	HitsCollection.Utils = {
		/**
		 * @param {Entry[]} entryList
		 * @param {string} prefix
		 * @returns {string}
		 */
		buildQuery: function (entryList, prefix) {
			return entryList.map(function (entry) {
				return prefix + ':"' + entry.id + '"';
			}).join(' OR ');
		},
		
		/**
		 * @param {Entry[]} entryList
		 * @returns {Id[]}
		 */
		getCopy: function (entryList) {
			let copyArray = [];
			entryList.map(function (entry) {
				copyArray.push($.extend({},entry));
			});
			return copyArray;
		},
		
		/**
		 * @param {string} url
		 * @returns {string}
		 */
		sanitizeUrl: function (url) {
			// params from 3dxp wrapper
			var blacklistedParams = ['_context', 'lang'];
			var a = document.createElement('a');
			a.href = url;
			var sanitized = new BuildUrl(url);
			blacklistedParams.forEach(function (param) {
				sanitized.removeParameter(param);
			});
			return sanitized.toString() + a.hash;
		},
		
		/**
		 * @param {string} hex
		 * @returns {{
		 *     r: number,
		 *     g: number,
		 *     b: number
		 * }}
		 */
		hexToRGB: function (hex) {
			var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
			return result ? {
				r: parseInt(result[1], 16),
				g: parseInt(result[2], 16),
				b: parseInt(result[3], 16)
			} : null;
		}
	}
	
	var hitsCollectionList = {};
	window.HitsCollectionManager = {
	    /**
         * getCollection is to get the existing collection or undefined.
         */
        getCollection: function (collectionId) {            
            var res = hitsCollectionList[collectionId];
			
			// Not really required as we render the creation call before anyother scripts(check widget.jsp)
			// But keeping Incase some issue....
			if(res == undefined){
				console.error("getCollection is called prior to createCollection call. Meaning recentSearches widget with id=" + collectionId +" is not created yet.");
				throw new Error("getCollection is called prior to createCollection call. Meaning recentSearches widget with id=" + collectionId +" is not created yet.");
			}
			return res._promise();
        },
		
		/**
		 * Instantiate new Collection. Unique collections are manged using options.id
         * If collection with options.id already exists, then existing collection promise will be returned,
         * else new collection will be added and the new one will be returned. Provide jsone object options
		 * as detailed below.
		 * @param {options.uCssId} 'Widget CSS Id'
		 * @param {options.id} 'bookmark'
		 * @param {options.store} 'bookmark'
		 * @param {options.storeKey} 'hit_collection_bookmark'
		 * @param {options.limit} integer (0 meaning no limit on the collection)
		 * @param {options.limitStrategy} 'freeze|remove-old'
		 * @param {options.labels} List of lable traslated text. label, counter, tooltip, emptyCollection, addEntry, removeEntry
		 * @param {options.alerts} List of alert traslated messages. add, remove, remove-old, removeAll, limitReached
		 * @param {options.detailsView} List of options for rendring the details. loadOnInt, displayHitsMode, metaForId, and action handlers. 
		 * @return Promise Object. Once the colletion is initialized, the Helper class will be returned. Which can be used to interact with collection.
		 */
        createCollection: function (options) {
			if(hitsCollectionList[options.id] == undefined){
				hitsCollectionList[options.id] = new HitsCollection(options);				
			}
            return hitsCollectionList[options.id]._promise();
        }        
	}
	return window.HitsCollectionManager;
})(window);
