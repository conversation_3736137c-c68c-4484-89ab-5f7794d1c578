var PLMAFlexContainerWithHeader = function (uCssId, options) {
    'use strict';

    var defaults = {
        animationDuration: 200
    };

    this.options = $.extend({}, defaults, options);
    this.uCssId = uCssId;

    if (uCssId) {
        this.widget = $('.' + uCssId);
    } else {
        this.widget = $();
    }

    if (this.widget.length === 0) {
        throw new Error('Unable to initialize widget PLMAFlexContainerWithHeader : widget not found (uCssId: "' + uCssId + '").');
    } else {
        this.init();
    }
};

PLMAFlexContainerWithHeader.prototype.init = function () {
    'use strict';
    this.widget.find('.fullscreen-button').on('click', $.proxy(function(){
        this.widget.find('.fullscreen-button').toggleClass('hidden');
        this.widget.toggleClass('full-size-active');
    }, this));

    this.widget.find('.collapsible-button').on('click', $.proxy(function(){
        this.widget.find('.collapsible-button').toggleClass('hidden');
        this.widget.find('.widgetContent').toggleClass('hidden');
    }, this));
};