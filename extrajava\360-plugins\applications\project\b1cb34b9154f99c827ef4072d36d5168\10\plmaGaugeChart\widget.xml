<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA Gauge" group="PLM Analytics/Visualizations" premium="true">

	<Description>This widget displays PLMA data in a gauge chart.</Description>

	<Preview>
		<![CDATA[
            <img src="/resources/widgets/plmaGaugeChart/images/preview.PNG" alt="Gauge Chart" />
        ]]>
	</Preview>
	
	<Includes>
		<Include path="js/plmaGaugeChart.js" type="js" />
		<Include path="css/style.less" type="css" />
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO_OR_MANY" />

	<OptionsGroup name="General">
		<Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
		</Option>
		<Option id="value" name="Gauge value" isEvaluated="true">
			<Description>Displayed value. Can be a MEL expression.</Description>
		</Option>
		<Option id="minValue" name="Min value" isEvaluated="true" arity="ONE">
			<Description>Minimum displayed value.</Description>
		</Option>
		<Option id="maxValue" name="Max value" isEvaluated="true" arity="ONE">
			<Description>Maximum displayed value.</Description>
		</Option>
		<Option id="unit" name="Unit" isEvaluated="true">
			<Description>Unit.</Description>
		</Option>
		<OptionComposite id="ranges" name="Ranges" arity="ZERO_OR_MANY">
			<Option id="minRange" name="Range min">
				<Description>Minimum value of the range.</Description>
				<Functions>
					<Check>isEmpty</Check>
				</Functions>
			</Option>
			<Option id="maxRange" name="Range max">
				<Description>Maximum value of the range.</Description>
				<Functions>
					<Check>isEmpty</Check>
				</Functions>
			</Option>
			<Option id="colorRange" name="Range Color">
				<Description>Hexadecimal color code of the range. The gauge will turn into this color when its value is within the corresponding range.</Description>
				<Functions>
					<Check>isEmpty</Check>
				</Functions>
			</Option>		
		</OptionComposite>
	</OptionsGroup>
	
	<OptionsGroup name="Style">
		<Option id="width" name="Width">
			<Description>Specifies the width of the widget (pixels). You must enter an integer.</Description>		
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="height" name="Height" arity="ONE">
			<Description>Specifies the height of the widget (pixels). You must enter an integer.</Description>			
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Documentation">
		<Option id="displayDoc" name="Enable Documentation">
			<Description>If true, adds a button to display a HTML documentation to give the user information about the chart. (Default value is false)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['doc']})</Display>
			</Functions>
		</Option>
		<Option id="doc" name="User Reference" isEvaluated="true">
			<Description>This is an HTML code to describe the chart.</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
		<Option id="techDoc" name="Technical Reference" isEvaluated="true">
			<Description>This is the chart's technical reference (it won't be displayed in the application).</Description>
			<Functions>
				<Display>SetType('code', 'html')</Display>
			</Functions>
		</Option>
	</OptionsGroup>

</Widget>
