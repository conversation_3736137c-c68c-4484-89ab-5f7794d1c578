<?xml version="1.0" encoding='UTF-8'?>
<Widget name="chart annotation configuration" group="Layout">

	<Description>
		This widget allows users to annotate plmaChart2 widgets. It adds an "annotate" button to all the plmaChart2 widgets of the page.
		Clicking an "annotate" button switches the corresponding plmaChart2 widget to annotation mode. When in annotation mode, an annotation can be created by clicking anywhere on the chart. 
		Only one instance of this widget per page is necessary. It can be placed anywhere on the page.
	</Description>


	<Preview>
		<![CDATA[
			<img src="/resources/widgets/annotateChart/images/preview.PNG" alt="Annotate chart" />
		]]>
	</Preview>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="js/annotateChart.js" />
		<Include type="js" path="../plmaResources/js/popupLib.js" />
		<Include type="js" path="/resources/highcharts/js/modules/annotations.js" />
		<Include type="js" path="../plmaResources/js/polyfills.js" />
		<Include type="js" path="../plmaResources/lib/extend-highcharts/extend-highcharts.js" />
		<Include type="js" path="../plmaResources/js/widgetButtonManager.js" />
	</Includes>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO" consumeFeed="false" />
	<SupportI18N supported="true">
		<JsKeys>
			<JsKey>chart.annotation.btn.label</JsKey>
			<JsKey>chart.annotation.btn.title_shared</JsKey>
			<JsKey>chart.annotation.btn.title_user</JsKey>
			<JsKey>chart.annotation.title</JsKey>
			<JsKey>chart.annotation.close</JsKey>
			<JsKey>chart.annotation.close.tooltip</JsKey>
		</JsKeys>
	</SupportI18N>

	<OptionsGroup name="General">
		<Option id="enableFullScreen" name="Enable full screen">
		<Description>Enables full screen when annotation mode is enabled.</Description>
		<Values>
			<Value>true</Value>
			<Value>false</Value>
		</Values>
		</Option>
		<Option id="waitForTrigger" name="Load annotation after trigger event">
			<Description>If option is set to 'true', chart annotation is loaded after a "plma:chart-is-loaded" trigger event
			 has been sent.  &lt;br/&gt;
				&lt;b&gt;WARNING&lt;/b&gt;: Mandatory when plmaChart2 widget uses a plma Asynchronous
			ajax loading trigger.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="btnSelector" name="Button selector" arity="ONE">
			<Description>jQuery selector to specify the widgets on which the annotation button is displayed.</Description>
		</Option>
		<Option id="btnIcon" name="Button icon">
			<Description>Specifies the CSS class of the icon for the button.</Description>
		</Option>
		<Option id="annotationScope" name="Annotation scope" isEvaluated="true" arity="ONE">
			<Description>Sets the scope of the annotation feature. If set to 'user', annotations are only visible to the user who created them. If set to 'shared',
				annotations are visible to all users.</Description>
			<Values>
				<Value>shared</Value>
				<Value>user</Value>
			</Values>
		</Option>
		<Option id="storageKey" name="Storage key" isEvaluated="true" arity="ONE">
			<Description>Sets the default storage key for the annotation widget.</Description>
		</Option>
 	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="enableFullScreen">false</DefaultValue>
		<DefaultValue name="waitForTrigger">false</DefaultValue>
		<DefaultValue name="btnSelector">.plmaCharts</DefaultValue>
		<DefaultValue name="btnIcon">fonticon fonticon-highlighter</DefaultValue>
		<DefaultValue name="annotationScope">shared</DefaultValue>
	</DefaultValues>

</Widget>
