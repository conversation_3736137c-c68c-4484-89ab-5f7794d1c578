var PLMATable = function(uCssId, options){
	var defaults = {
			
	};
	
	this.options = $.extend({}, defaults, options);
	
	if (uCssId){
		this.widget = $('.' + uCssId);
	}else{
		this.widget = $();
	}
	
	if (this.widget.length === 0){
		new Error('Unable to initialize widget custom hit list : widget not found (uCssId: "' + uCssId + '").');
	}else{
		this.init(uCssId);	
	}
};

PLMATable.prototype.init = function(uCssId){
	this.transposeBtn = this.widget.find('.button-transpose'); 
	this.transposeBtn.on('click', $.proxy(this.transpose, this));
};

PLMATable.prototype.transpose = function() {
    this.widget.toggleClass('transposed');
    this.transposeBtn.toggleClass('active');
}

PLMATable.prototype.initDoc = function() {
	this.widget.find('.close-doc').click(function() {
		this.widget.find('.doc-container').toggleClass('hidden');
	}.bind(this));
};

PLMATable.utils = { 
	refineOnFacet : function(refines,withAjax,ucssId,widgetsId){
		var url = window.location.href;
		
		if(withAjax=='true'){
			var paramsHtml = document.getElementById('param_ajax_'+ucssId);
			
			var client = new PlmaAjaxClient($('#body'));
			client.addWidget(ucssId);
			
			widgetsId = widgetsId.replace('[','');
			widgetsId = widgetsId.replace(']','');
			for(var i=0; i<widgetsId.split(',').length ; i++){
				if(widgetsId.split(',')[i] != ""){
					client.addWidget(widgetsId.split(',')[i].replace(' ',''));
				}
			}
			
			for(var i=0 ; i<paramsHtml.innerHTML.split('##').length ; i++){
				if(paramsHtml.innerHTML.split('##')[i].split('@@').length>1){
					client.updateParameter(paramsHtml.innerHTML.split('##')[i].split('@@')[0],paramsHtml.innerHTML.split('##')[i].split('@@')[1]);
				}
			}
			
			for(var i=0 ; i<refines.split('&').length ; i++){
				if(refines.split('&')[i] != ''){
					paramsHtml.innerHTML = paramsHtml.innerHTML + '##' + refines.split('&')[i].split('=%2B')[0] + '@@' + refines.split('&')[i].replace(/%2F/g,'/').split('=%2B')[1];
					client.updateParameter(refines.split('&')[i].split('=%2B')[0],refines.split('&')[i].replace(/%2F/g,'/').split('=%2B')[1]);
				}
			}
			
			client.addParameter('paramAjax',paramsHtml.innerHTML);
			client.addParameter('isFromAjax',true);
			
			client.update();
			
			$(window).trigger('plma:resize');
		}else{
			if(url.split('#').length>1){
				url = url.split('#')[0];
			}
			if(url.split('?').length>1){
				url = url + refines;
			}else{
				refines = refines.replace('&','?');
				url = url + refines;
			}
			
			window.location.assign(url);
		}
	},

	nextPage : function(ucssId,page){
		var client = new PlmaAjaxClient($('.'+ucssId));
		client.addWidget(ucssId);
		client.addParameter('pageNb',parseInt(page)+1);
		client.addParameter('isFromAjax',true);
		client.update();
	},
	
	previousPage : function(ucssId,page){
		var client = new PlmaAjaxClient($('.'+ucssId));
		client.addWidget(ucssId);
		client.addParameter('pageNb',parseInt(page)-1);
		client.addParameter('isFromAjax',true);
		client.update();
	}
}
