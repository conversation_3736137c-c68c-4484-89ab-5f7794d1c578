PlmaSimpleDataTable.prototype.constructor = PlmaSimpleDataTable;

function PlmaSimpleDataTable(uCssId, options) {
    this.options = options;
    var buttons = [];
    if (options.pageLengthButton) {
        buttons.push('pageLength');
    }
    if (options.colvisButton) {
        buttons.push('colvis');
    }
    if ((options.colvisButton || options.pageLengthButton) && options.saveState) {
        buttons.push({
            text: function (dt) {
                return dt.i18n('stateRestore.removeTitle', 'Remove state');
            },
            //text: mashupI18N.get('plmaSimpleDataTable', 'widget.plmaDataTable.button.resetState'),
            className: 'button-reset-state',
            action: function (e, dt, node, config) {
                $.ajax({
                    type: 'DELETE',
                    dataType: "json",
                    url: options.baseControllerURL + '/state/delete/' + options.resultListId,
                    //context: this,
                    async: 'false',
                    success: function (data) {
                        window.location.reload();
                    }
                });
            }
        });
    }

    var drawCallbacks = [options.drawCallback];

    var responsive = options.responsive ? {details: {type: 'column'}} : false;

    this.initSelection = function () {
        let selectedUri = window.location.hash;
        let anchor = '';
        if (selectedUri !== '') {
            anchor = selectedUri.substring(1);
            this.datatable.rows((idx, data) => {
                if (data[options.idAttribute] === decodeURI(anchor)) {
                    this.datatable.row(idx).select();
                    this.openHitDetails(decodeURI(anchor));
                }
            });
        }
    }.bind(this);

    // this.hideResponsiveColumn = function () {
    //     $(".dtr-control").hide();
    // }

    if (options.enableHitDetails){
        // Add selected element initialisation on page loading only if hit details option is activated to
        // optimize performances
        drawCallbacks.push(this.initSelection);
    }

    // if (!options.responsive){
    //     drawCallbacks.push(this.hideResponsiveColumn);
    // }

    this.openHitDetails = function (hitID) {
        if (this.options.enableHitDetails) {
            let helper = new DetailHitHelper(true);
            helper.loadDetail(this.options.hitParamName, hitID);
            /* push state in url */
            helper.pushState(hitID);
        }
    }.bind(this);

    this.closeHitDetails = function () {
        if (this.options.enableHitDetails) {
            let helper = new DetailHitHelper(true);
            helper.closeDetailPanel();
            helper.pushState();
        }
    }.bind(this);

    // We pass 'colsDef' options
    this.datatable = $('#datatable_' + uCssId).DataTable({
        ajax: options.ajaxURL,
        language: {url: options.i18nFile},
        data: options.data,
        columns: options.colsDef,
        deferLoading: options.totalResults,
        processing: true,
        serverSide: true,
        pageLength: options.perPage,
        responsive: responsive,
        // responsive: true,
        searching: false,
        fixedHeader: options.fixedHeader,
        order: [], // When table is loaded first time, let's consider no sort as default behavior
        createdRow: function (row, data, dataIndex) {
            options.rowCallbacks.forEach(callback => {
                callback.call(options, row, data, dataIndex);
            });
        },
        drawCallback: function (settings) {
            drawCallbacks.forEach(callback => {
                callback.call(options, settings);
            });
        },
        select: {
            // TODO make configurable
            style: 'os',
            selector: responsive ? 'td:not(:first-child)' : 'td'
        },
        layout: {
            topStart: {
                buttons: buttons
            },
            bottomStart: 'info',
            bottomEnd: 'paging'
        },
        colReorder: options.colsReorder,
        stateSave: options.saveState,
        stateSaveCallback: function (settings, data) {
            $.ajax({
                type: 'POST',
                dataType: "json",
                contentType: "application/json; charset=utf-8",
                url: options.baseControllerURL + '/state/save/' + options.resultListId + '/' + options.responsive,
                //context: this,
                async: 'false',
                data: JSON.stringify(data),
                success: function (data) {
                    if (data.userState) {
                        // If returned state is specific to connected user -> display reset button
                        $('.button-reset-state').removeClass('hidden')
                    } else {
                        // If OOTB state --> don't display reset button
                        $('.button-reset-state').addClass('hidden');
                    }
                },
                error: function (err) {
                    $.notify('Error saving datatable state (' + err.responseJSON.error + ')', 'error');
                }
            });
        },
        stateLoadCallback: function (settings, callback) {
            $.ajax({
                type: 'GET',
                dataType: "json",
                url: options.baseControllerURL + '/state/get/' + options.pageName + '/' + options.resultListId + '?wuid=' + options.wuid,
                //context: this,
                async: 'false',
                success: function (data) {
                    callback(data);
                    if (data.userState) {
                        // If returned state is specific to connected user -> display reset button
                        $('.button-reset-state').removeClass('hidden')
                    } else {
                        // If OOTB state --> don't display reset button
                        $('.button-reset-state').addClass('hidden');
                    }
                }
            });
        },
        scrollX: !options.responsive,
        scrollY: options.scrollY,
        scroller: options.infiniteScroll
    });

    /*
        Colreorder plugin has a side effect after columns reordering --> cells event listeners are not kept; so, to
        workaround this issue, rows and table callbacks are replayed after columns reordering
     */
    this.datatable.on('columns-reordered', $.proxy(function (e, settings, details) {
        this.datatable.row((idx, data) => {
            this.options.rowCallbacks.forEach((callback) => {
                callback.call(this.options, this.datatable.row(idx).node(), data, idx);
            });
        });
        options.drawCallback.call(this.options, this.datatable.settings());
    }, this));

    $('.resultlistView').on('hit-details:close', $.proxy(function (e) {
        this.datatable.rows().deselect();
    }, this))

    // TODO investigate issue on infinite scroll --> disable for now
    $('.resultlistView').on('hit-details:opened', $.proxy(function (e) {
        var rows = this.datatable.rows('.selected').indexes();
        if (rows.length > 0 && !this.options.infiniteScroll) {
            var row = this.datatable.row(rows[0]).node();
            var rowPosition = $(row).position().top;
            $('.dt-scroll-body').scrollTo({top: rowPosition, left: 0, behavior: "smooth"})
        }
    }, this))

    this.datatable.on('select', $.proxy(function (e, dt, type, indexes) {
        if (this.options.enableHitDetails) {
            let bClose = false;
            if (indexes.length > 1) {
                // In case of multi-selection --> hide hit details panel, it does not make sense to display one of the items
                bClose = true;
            } else {
                let selectedUri = window.location.hash;
                let anchor = '';
                if (selectedUri !== '') {
                    anchor = selectedUri.substring(1);
                }
                var row = this.datatable.row(indexes[0]).data();
                let hitID = row[this.options.idAttribute];
                if (hitID !== decodeURI(anchor)) {
                    this.openHitDetails(hitID);
                } else {
                    bClose = true;
                }
            }
            if (bClose) {
                this.closeHitDetails();
            }
        }
    }, this))

    this.datatable.on('deselect', $.proxy(function (e, dt, type, indexes) {
        if (this.options.enableHitDetails) {
            this.closeHitDetails();
        }
    }, this))
}


(function ($) {
    $.fn.toggleHitDetails = function (parameter, hitID) {
        // Init event handlers ...
        this.off('click').on('click', $.proxy(function (e) {
            let selectedUri = window.location.hash;
            let anchor = '';
            if (selectedUri !== '') {
                anchor = selectedUri.substring(1);
            }
            /* if an anchor is present, check if hit already selected and deselect it */
            let helper = new DetailHitHelper(true);
            if (hitID !== decodeURI(anchor)) {
                helper.selectHit(this);
                $('.selection-panel').addClass('hidden');
                helper.loadDetail(parameter, hitID);
                //$(window).trigger('resize');
                /* push state in url */
                helper.pushState(hitID);
            } else {
                helper.closeDetailPanel();
                helper.pushState();
                helper.deselectHit();
            }
        }, this));
    };

    $.fn.addRefineClickEvents = function () {
        this.find('.refine-link').off('click').on('click', function (e) {
            // Don't execute parent element(s) event(s)
            e.stopPropagation();
            // Reload page using data-url attribute
            window.location = $(e.target).data('url');
        });
    };
}(jQuery));
