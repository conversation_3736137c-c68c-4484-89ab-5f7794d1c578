<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<render:import parameters="accessFeeds,feed,entry,widget"/>
<render:import parameters="hitTitle" ignore="true"/>

<%-- retrieve the widget options --%>
<config:getOption var="templateBasePath" name="templateBasePath" defaultValue="templates/"/>
<config:getOptionsComposite var="metaGroups" name="metaGroups" mapIndex="true"/>
<config:getOptions name="forceRefineOnFeeds" var="forceRefineOnFeeds"/>
<config:getOption name="typeFacetId" var="typeFacetId"/>
<config:getOption var="facetIcon" name="facetIcon" defaultValue="fonticon-legend"/>

<config:getOption var="hitUrl" name="hitUrl" entry="${entry}"/>
<config:getOption var="hitContent" name="hitContent" entry="${entry}" defaultValue="${entry.content}"/>
<config:getOption var="hitSubTitleDescription" name="hitSubTitleDescription" entry="${entry}"/>
<config:getOptions name="resetPageParameters" var="resetPageParameters" />

<i18n:message code="widget.action.expand" var="tooltipExpand"/>
<i18n:message code="widget.action.collapse" var="tooltipCollapse"/>
<i18n:message code="hitdetail.metas.title" var="titleMetas"/>
<i18n:message code="hitdetail.action.openinsource" var="openInSourceTooltip"/>

<div class="hitDetailsHeader">
	<div class="hitDetailsHeaderTitle">
		<div class="hitHeader">
			<c:if test="${not empty hitTitle && !plma:getBooleanParam(widget, 'titleInHeader', false)}">
				<render:template template="templates/hit-details-title.jsp">
					<render:parameter name="hitTitle" value="${hitTitle}"/>
					<render:parameter name="entry" value="${entry}"/>
					<render:parameter name="widget" value="${widget}"/>
					<render:parameter name="iconContainer" value="${true}"/>
				</render:template>
			</c:if>
		</div>
	</div>
</div>

<%-- class="${wh:cleanEntryId(entry.id)}" is used by GMap widgets to append markers --%>
<li class="hit hit-list ${search:cleanEntryId(entry)} hit-default">
	<c:if test="${not empty hitContent}">
		<div class="hitDetailsContent">${hitContent}</div>
	</c:if>

	<config:getOption var="showEmptyMetas" name="showEmptyMetas" defaultValue="true"/>
	<config:getOption var="sortModeMetas" name="sortModeMetas" defaultValue="default"/>
	<config:getOption var="showAnalysisDate" name="showAnalysisDate" defaultValue="false"/>
	<config:getOption var="useExternalisedConfig" name="useExternalisedConfig" defaultValue="false"/>

	<div class="metaWrapper">
		<c:choose>
			<c:when test="${useExternalisedConfig == 'true'}">
				<plma:getOptionComposite var="externalConfig" name="externalConfig" mapIndex="true"/>
				<plma:hitDetailsConfig var="hitDetailsConfig" configName="${externalConfig.configName}" hitDetailsId="${externalConfig.hitDetailsId}"/>
				<c:forEach var="group" items="${hitDetailsConfig.fieldGroup}">
					<c:set var="showGroup" value="true"/>
					<c:if test="${not empty group.displayCondition}">
						<string:eval var="showGroup" string="${group.displayCondition.expr}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
					</c:if>
					<c:if test="${showGroup == 'true'}">
						<div class="metaGroup">
							<div class="metaGroupTitle">
								<span class="fonticon ${group.icon}"/>
								<span class="label">${group.label}</span>
								<c:if test="${not empty group.description}">
									<string:eval var="groupDesc" string="${group.description}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
									<c:set var="groupCondDesc" value=""/>
									<c:if test="${not empty group.displayCondition && not empty group.description}">
										<string:eval var="groupCondDesc" string="${group.description}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
									</c:if>
									<c:if test="${not empty groupDesc}">
										<span class="groupinfo">
											<span class="fonticon fonticon-info"/>
											<span class="groupinfo-tooltip">
												<span class="description">
													<span class="group-description">${groupDesc}</span>
													<c:if test="${not empty groupCondDesc}">
														<span class="group-condDescription">${groupCondDesc}</span>
													</c:if>
												</span>
											</span>
										</span>
									</c:if>
								</c:if>
							</div>
							<table>
								<colgroup>
									<col name="metaName"/>
									<col name="metaValue"/>
								</colgroup>
								<tbody>
								<c:forEach var="field" items="${group.field}">
									<c:set var="showField" value="true"/>
									<c:if test="${not empty field.displayCondition}">
										<string:eval var="showField" string="${field.displayCondition.expr}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
									</c:if>
									<c:if test="${showField == 'true'}">
										<c:choose>
											<c:when test="${not empty field.displayTemplate}">
												<c:set var="displayTemplate" value="${field.displayTemplate}"/>
												<%-- Split template --%>
												<c:if test = "${fn:endsWith(field.displayTemplate, '.jsp')}">
													<c:set var="displayTemplate" value="${fn:split(field.displayTemplate, '#')[1]}"/>
													<c:set var="displayJSPTemplateWidget" value="${fn:split(field.displayTemplate, '#')[0]}"/>
												</c:if>
												<tr class="metaDetailWrapper">
													<td class="metaLabel">${field.label}:</td>
													<td class="metaValue">
														<c:choose>
															<c:when test="${empty displayJSPTemplateWidget}">
																<string:eval var="displayValue" string="${displayTemplate}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
																${displayValue}
																<c:if test="${not empty displayValue}">
																	<c:set var="isThisGroupEmpty" value="${false}"/>
																</c:if>
															</c:when>
															<c:otherwise>
																<render:template template="${displayTemplate}" widget="${displayJSPTemplateWidget}">
																	<render:parameter name="accessFeeds" value="${feeds}" />
																	<render:parameter name="feed" value="${feed}" />
																	<render:parameter name="entry" value="${entry}" />
																	<render:parameter name="group" value="${group}" />
																	<render:parameter name="field" value="${field}" />
																</render:template>
																<c:set var="isThisGroupEmpty" value="${false}"/>
															</c:otherwise>
														</c:choose>
													</td>
												</tr>
											</c:when>
											<c:when test="${empty field.displayTemplate && not empty field.displayValue}">
												<tr class="metaDetailWrapper">
													<td class="metaLabel">${not empty field.label? field.label : metaLabel}:</td>
													<td class="metaValue">
														<string:eval string="${field.displayValue}" var="value" entry="${entry}" meta="${meta}" feeds="${accessFeeds}" feed="${feed}"/>
														${value}
														<c:if test="${not empty value}">
															<c:set var="isThisGroupEmpty" value="${false}"/>
														</c:if>
													</td>
												</tr>
											</c:when>
											<c:when test="${empty field.displayTemplate && field.isFacet == 'false'}">
												<search:getMeta var="meta" metaName="${field.meta}" entry="${entry}"/>
												<search:getMetaLabel var="metaLabel" metaName="${field.meta}"/>
												<tr class="metaDetailWrapper">
													<td class="metaLabel">${not empty field.label? field.label : metaLabel}:</td>
													<td class="metaValue">
														<search:forEachMetaValue entry="${entry}" meta="${meta}" var="value"
																				 varUrl="metaUrl" varStatus="status">
															${value}
															<c:if test="${status.last == false}">,</c:if>
															<c:if test="${not empty value}">
																<c:set var="isThisGroupEmpty" value="${false}"/>
															</c:if>
														</search:forEachMetaValue>
													</td>
												</tr>
											</c:when>
											<c:when test="${empty field.displayTemplate && field.isFacet == 'true'}">
												<search:getFacet var="facet" facetId="${field.meta}" entry="${entry}"/>
												<plma:getFacetLabel var="facetLabel" feed="${feed}" facetId="${facet.id}"/>
												<tr class="metaDetailWrapper">
													<td class="metaLabel">${not empty field.label? field.label : facetLabel}:</td>
													<td class="metaValue">
														<search:forEachCategory root="${facet}" iterationMode="ALL" var="category"
																				varStatus="status">
															<search:getCategoryUrl var="categoryUrl" varClassName="className" category="${category}" feed="${feed}" forceRefineOn="${forceRefineOnFeeds}"/>
                                                            <search:getCategoryLabel var="categoryLabel" category="${category}"/>
                                                            <plma:correctUrl var="categoryUrl" url="${categoryUrl}" pageUrl="" skipProfiles="${plma:toList('hitDetailsView')}" skip="${resetPageParameters}" delete2DFacet="" feeds="${accessFeeds}" keepExtraRefinements="" />
															<a href="${categoryUrl}" class="hit-detail-link-refine ${className}">${categoryLabel}</a>
															<c:if test="${status.last == false}">,</c:if>
															<c:if test="${not empty category}">
																<c:set var="isThisGroupEmpty" value="${false}"/>
															</c:if>
														</search:forEachCategory>
													</td>
												</tr>
											</c:when>
											<c:otherwise>
												<tr class="metaDetailWrapper">
													<td class="metaLabel">${field.label}:</td>
													<td class="metaValue"></td>
												</tr>
											</c:otherwise>
										</c:choose>
									</c:if>
								</c:forEach>
								</tbody>
							</table>
						</div>
					</c:if>
				</c:forEach>
			</c:when>
			
			<c:otherwise>
				<c:forEach items="${metaGroups}" var="metaGroup">
					<%-- The group should not be rendered (not even the title) if it only contains empty metas --%>
					<c:set var="isThisGroupEmpty" value="${true}"/>
					<c:set var="renderedGroup">
						<div class="metaGroup">
							<div class="metaGroupTitle">${metaGroup.groupTitle}</div>
							<table>
								<colgroup>
									<col name="metaName"></col>
									<col name="metaValue"></col>
								</colgroup>
								<tbody>
								<search:forEachMeta var="meta" entry="${entry}"
													filterMode="${metaGroup.groupMetaFilter}"
													metasList="${metaGroup.groupMetaList}"
													showEmptyMetas="${showEmptyMetas}" sortMode="${sortModeMetas}">
									<search:getMetaLabel var="metaLabel" meta="${meta}"/>
									<tr class="metaDetailWrapper">
										<td class="metaLabel">${metaLabel}:</td>
										<td class="metaValue">
											<search:forEachMetaValue entry="${entry}" meta="${meta}" var="value"
																	 varUrl="metaUrl" varStatus="status">
												${value}
												<c:if test="${status.last == false}">,</c:if>
												<c:if test="${not empty value}">
													<c:set var="isThisGroupEmpty" value="${false}"/>
												</c:if>
											</search:forEachMetaValue>
										</td>
									</tr>
								</search:forEachMeta>
								<search:forEachFacet var="facet" entry="${entry}"
													 filterMode="${metaGroup.groupMetaFilter}"
													 facetsList="${metaGroup.groupFacetList}"
													 showEmptyFacets="${showEmptyMetas}" sortMode="${sortModeMetas}">
									<plma:getFacetLabel var="facetLabel" feed="${feed}" facetId="${facet.id}"/>
									<tr class="metaDetailWrapper">
										<td class="metaLabel">${facetLabel}:</td>
										<td class="metaValue">
											<search:forEachCategory root="${facet}" iterationMode="ALL" var="category"
																	varStatus="status">
												<render:categoryLink category="${category}" feed="${feed}"
																	 forceRefineOn="${forceRefineOnFeeds}"
																	 extraCss="hit-detail-link-refine"/>
												<c:if test="${status.last == false}">,</c:if>
												<c:if test="${not empty category}">
													<c:set var="isThisGroupEmpty" value="${false}"/>
												</c:if>
											</search:forEachCategory>
										</td>
									</tr>
								</search:forEachFacet>

								</tbody>
							</table>

						</div>
					</c:set>

					<c:if test="${not isThisGroupEmpty || showEmptyMetas}">
						${renderedGroup}
					</c:if>
				</c:forEach>
			</c:otherwise>
		</c:choose>
	</div>

	<%-- Displays sub-widgets (except plma buttons) --%>
	<c:if test="${plma:hasExceptSubWidget(widget, 'plmaButtonsContainer')}">
		<div class="subWidget">
			<plma:forEachSubWidget excludes="plmaButtonsContainer">
				<render:widget/>
			</plma:forEachSubWidget>
		</div>
	</c:if>

	<%-- Displays analysis date --%>
    <c:if test="${showAnalysisDate}">
        <config:getOption var="analysisDateMetaName" name="analysisDateMetaName" defaultValue="analysisdate"/>
        <config:getOptionComposite var="analysisDateFormat" name="analysisDateFormat" mapIndex="true"/>

        <div class="analysisDate">
            <search:getMetaValue var="analysisDate" entry="${entry}" metaName="${analysisDateMetaName}"/>
            <fmt:parseDate var="date" value="${analysisDate}" pattern="${empty analysisDateFormat.inputFormat?'yyyy/MM/dd H:m:s':analysisDateFormat.inputFormat}" timeZone="GMT"/>
            <fmt:formatDate var="outputString" value="${date}" pattern="${empty analysisDateFormat.outputFormat?'EEE MMM dd HH:mm:ss zzz yyyy':analysisDateFormat.outputFormat}"/>
            <i18n:message code="hitdetail.lastRefreshIndex" arguments="${outputString}"/>
        </div>
    </c:if>
</li>
