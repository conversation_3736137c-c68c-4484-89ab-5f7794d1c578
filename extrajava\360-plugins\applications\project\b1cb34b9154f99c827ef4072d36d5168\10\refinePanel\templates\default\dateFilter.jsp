<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="highcharts" uri="http://www.exalead.com/jspapi/highcharts" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import parameters="widget,accessFeeds,facet,uCssId"/>

<plma:getConstantValueFromNameTag var="dateFacetParameter" constantName="SIMPLE_DATE_PARAM" />

<!-- Title -->
<h3 class="title-container sub-header-collapsable">
    <span class="facet-name" id="${facet}">
		<i18n:message var="facetLabel" code="field_${facet}"/>
		<c:choose>
			<c:when test="${facetLabel != undefined && facetLabel != ''}">${facetLabel}</c:when>
			<c:otherwise>${facet}</c:otherwise>
		</c:choose>
	</span>

	<span class="facet-value"></span>
	<span class="fonticon fonticon-resize-full hidden"></span>
	<i18n:message var="calendarTooltip" code="widgets.refinePanel.calendar" text="Select date range"/>
	<span class="calendar-icon fonticon fonticon-calendar" title="${calendarTooltip}"></span>
</h3>
<url:url keepQueryString="true" var="cancelDateUrl"></url:url>
<request:getParameterValues var="simpleDateRefineParams" name="${dateFacetParameter}" defaultValue=""/>

<!-- To display selected date refine -->
<tr class="category dateRefine dateRefine-${facet} hidden">
	<td class="refineName"></td>
	<c:forEach items="${simpleDateRefineParams}" var="refine">
		<c:set var="list" value="${fn:replace(fn:split(refine,',')[0],'[','')}"></c:set>
		<c:if test="${refine != undefined && refine != '' && list == facet}">
			<url:encode var="stringToReplace" value="${refine}"/>
			<c:set var="stringToReplace" value="${dateFacetParameter}=${stringToReplace}"/>
			<c:set var="cancelDateUrl" value="${fn:replace(cancelDateUrl,stringToReplace,'')}"/>
		</c:if>
	</c:forEach>
	<td class="exclude"><a class="refine-link cancel" href="${cancelDateUrl}" title="<i18n:message code="rangeselector.cancelFilter" />"><span class="fonticon fonticon-cancel"></span></a></span></td>
</tr>

<plma:getDateRangeConfigTag ranges="configRanges"/>

<c:set var="aggregValue" value="${fn:split('count,spline,0',',')}"/>
<list:new var="aggregList"/>
<list:add list="${aggregList}" value="${aggregValue}"/>
<highcharts:json var="data" x="${facet}" y="${aggregList}" feeds="${accessFeeds}"/>

<config:getOption var="calendarFormat" name="calendarFormat" defaultValue="MM/DD/YYYY"/>
<string:eval var="calendarFormat" string="${calendarFormat}"/>

<render:renderScript position="READY">
	var plmaDateRefine = new PLMADateRefine('${uCssId}','${facet}','${dateFacetParameter}','${data}','<url:url keepQueryString="true"/>', '<string:escape value="${configRanges}" escapeType="JAVASCRIPT" />', '${loadDateFacetParameter}', '${fn:toUpperCase(calendarFormat)}');
	plmaDateRefine.init();
</render:renderScript>