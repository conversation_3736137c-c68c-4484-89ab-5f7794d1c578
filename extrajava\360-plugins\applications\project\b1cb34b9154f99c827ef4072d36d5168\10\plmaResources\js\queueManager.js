var QueueManager = (function(){
	
	var Queue = function(id) {
		this.id = id;
		this.running = false;
		this.frozen = false;
		this.thisArgs = []
		this.args = [];
		this.callbacks = [];
	};
	
	/**
	 * Queues methods and their arguments
	 */
	Queue.prototype.add = function (callback, thisArg, callbackArgs) {
		this.callbacks.push(callback);
		this.thisArgs.push(thisArg);
		this.args.push(callbackArgs);
	};
	
	/**
	 * Run the queue: callbacks are executed in order, as long
	 * as the queue is not empty nor frozen.
	 */
	Queue.prototype.run = function() {
		if (!this.isRunning()) {
			this.running = true;
			while (this.callbacks.length && !this.isFrozen()) {
				this.callbacks.shift().apply(this.thisArgs.shift(), this.args.shift());
			}
			this.running = false;
		}
	};

	Queue.prototype.freeze = function() {
		this.frozen = true;
	};
	
	Queue.prototype.unfreeze = function() {
		this.frozen = false;
	};
	
	Queue.prototype.isRunning = function() {
		return this.running;
	};
	
	Queue.prototype.isFrozen = function() {
		return this.frozen;
	};
	
	/*
	 * All queues are stored in this object
	 */
	var QUEUES = {};
	
	var getQueue = function (queueId) {
		if (!QUEUES.hasOwnProperty(queueId)) {
			QUEUES[queueId] = new Queue(queueId);
		}
		return QUEUES[queueId];
	};
	
	return {
		/**
		 * Will add the provided callback and its arguments
		 * to the queue specified by the provided ID.
		 */
		queue: function (queueId, callback, thisArg, callbackArgs) {
			var queue = getQueue(queueId);
			queue.add(callback, thisArg, callbackArgs);
		},
		
		/**
		 * Will run the specified queue of callbacks.
		 */
		run: function (queueId) {
			var queue = getQueue(queueId);
			queue.run();
		},

		/**
		 * Will freeze the execution of the specified
		 * queue of callbacks.
		 */
		freeze: function (queueId) {
			var queue = getQueue(queueId);
			queue.freeze();
		},
		
		/**
		 * Will unfreeze the specified queue of callbacks,
		 * allowing it to run again when asked to.
		 */
		unfreeze: function (queueId) {
			var queue = getQueue(queueId);
			queue.unfreeze();
		},
		
		/**
		 * For inspection
		 */
		getQueue: getQueue
	};
})();