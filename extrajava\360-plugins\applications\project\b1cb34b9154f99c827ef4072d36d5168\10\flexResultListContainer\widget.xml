<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Flex result list container" group="PLM Analytics/Layout/Flex display" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>This widget is a flex result list container with PLMA Result List, PLMA Hit Details and Reload Container. First column is considered as container for PLMA Result List.</Description>
	
	<Includes>
    	<Include type="css" path="css/style.less" />
    </Includes>
    
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportWidgetsId arity="ZERO_OR_MANY" displayType="LAYOUT"/>

	<Dependencies>
		<Widget name="plmaResources" />
		<Widget name="plmaResultListCommons" />
		<Widget name="preferredHits" />
		<Widget name="plmaExport" />
	</Dependencies>

	<OptionsGroup name="General">
		<Option id="width" name="Width" arity="ZERO_OR_ONE">
			<Description>Specifies the table width. Defaults to '100'.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="widthFormat" name="Width format" arity="ZERO_OR_ONE">
			<Description>Specifies the width format of the table. Defaults to '%'.</Description>
			<Values>
				<Value></Value>
				<Value>%</Value>
				<Value>px</Value>
			</Values>
		</Option>
		<Option id="resultListId" name="ResultList Id" isEvaluated="true" arity="ONE">
			<Description>ResultList Configurations ID to use. You can provide MEL expression as well if you need conditional display.</Description>
		</Option>
		<Option id="hasHitDetails" name="Has hit details" arity="ONE">
			<Description>This layout has collapsible hit details. (If yes, second column is considered as hit details container)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="hasReloadContainer" name="Has reload container" arity="ONE">
			<Description>This layout has collapsible reload container. (If yes, third column is considered as reload container)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="displayHeader" name="Display header" isEvaluated="true">
			<Description>Display header with number of hits and actions buttons.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>

		<Option id="ajaxSort" name="Enable sort with ajax">
			<Description>Enable sorting of the widget with an ajax request.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>

		<Option id="enableSortButton" name="Display header sort button">
			<Description>Enable sort button in result list header, if applicable for at least one column.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>

		<Option id="buttonsIconSize" name="Buttons font size" arity="ZERO_OR_ONE"
				isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
				isXmlEscaped="false">
			<Description>Specifies buttons font size (in pixels). You must enter an integer. Default inherited.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="defaultView" name="Default View" arity="ZERO_OR_ONE">
			<Description>
				Specifies the default view of the result list.&lt;br/&gt;
				&lt;b&gt;NOTE&lt;/b&gt;: Value should be same with ResultList's default view ID
			</Description>
		</Option>

		<Option id="closeButtonOnViews" name="Close button on views" arity="ZERO_OR_MANY">
			<Description>Display close button on specific views (for example to close compare or preferred hits views without removing documents from collections).</Description>
		</Option>

		<OptionComposite id="variables" name="Additional CSS variables" arity="ZERO_OR_MANY" glue="##" isEvaluated="true">
			<Description>Add variables to widget div, it can be useful to configure CSS using variables for example.</Description>
			<Option id="name" name="Name" arity="ONE" isEvaluated="true">
				<Description>Variable name to add.</Description>
				<Functions>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="value" name="Value" arity="ONE" isEvaluated="true">
				<Description>Aggregation function expression.</Description>
				<Functions>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
		</OptionComposite>
	</OptionsGroup>

	<OptionsGroup name="Collections management">
		<Description>Enable or disable collections buttons (compare, favorites...)</Description>

		<!-- Compare config -->
		<Option id="section-compare-config" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Compare hits &lt;/b&gt;&lt;/span&gt;">
			<Functions>
				<Display>SetType('literal')</Display>
			</Functions>
		</Option>

		<Option id="enableCompare" name="Enable" arity="ONE">
			<Description>
				Enables "compare" button that allows user to compare multiple hits.&lt;br/&gt;
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['enableCompareDelete', 'minCompareSize', 'compareCollection', 'compareMode']})</Display>
			</Functions>
		</Option>

		<Option id="enableCompareDelete" name="Display delete button" arity="ONE">
			<Description>
				Display delete button.&lt;br/&gt;
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>

		<Option id="minCompareSize" name="Minimum items to display button" arity="ZERO_OR_ONE">
			<Description>Minimum items in compare collection to display button.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>

		<Option id="compareCollection" name="Compare collection name">
			<Description>Collection name.</Description>
		</Option>

		<Option id="compareView" name="Compare view name">
			<Description>Compare view.</Description>
		</Option>

		<Option id="compareMode" name="Compare collection mode">
			<Description>Collection parameter creation mode (JS to pass hits in URL, UI trigger to forge param in PreRequestTrigger).</Description>
			<Values>
				<Value>trigger</Value>
				<Value>js</Value>
			</Values>
		</Option>

		<!-- Preferred hits config -->
		<Option id="section-favorites-config" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Preferred hits &lt;/b&gt;&lt;/span&gt;">
			<Functions>
				<Display>SetType('literal')</Display>
			</Functions>
		</Option>

		<Option id="enablefavorites" name="Enable" arity="ONE">
			<Description>
				Enables "favorite hits" button that allows user to display multiple hits.&lt;br/&gt;
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['enableFavoritesDelete','favoritesCollection']})</Display>
			</Functions>
		</Option>

		<Option id="enableFavoritesDelete" name="Display delete button" arity="ONE">
			<Description>
				Display delete button.&lt;br/&gt;
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>

		<Option id="favoritesCollection" name="Collection name">
			<Description>Preferred hits collection name.</Description>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Export">
		<Description>ReslutList Export option (recommended to use streaming mode to avoid too many feed queries on server side). </Description>
		<Option id="enableExport" name="Enable" arity="ONE">
			<Description>
				Enables an "export" button that allows the user to download data to a CSV file. &lt;br/&gt;
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['exportIconCss','exportLabel','exportShowLabel','exportNumHits','exportColumnsConfig','exportMode','exportPerPage','exportFileName','exportEncoding','exportSeparator','exportDelimiter','exportAddBOM','exportRawValues']})</Display>
			</Functions>
		</Option>
		<Option id="exportIconCss" name="Icon CSS" >
			<Description>Specifies the CSS class name of the icon added to the button. If empty, no icon is displayed.</Description>
		</Option>
		<Option id="exportLabel" name="Label" isEvaluated="true" arity="ONE">
			<Description>Specifies the label of the button. Briefly explains the purpose of the button (3 words max).</Description>
		</Option>
		<Option id="exportShowLabel" name="Show label" arity="ONE">
			<Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<Option id="exportNumHits" name="Hits limit" arity="ONE">
			<Description>
				(Only for &lt;b&gt;Hits&lt;/b&gt; mode) The maximum number of hits to export. Set to &lt;code&gt;-1&lt;/code&gt; to set no limit on the export.&lt;br/&gt;
				&lt;b&gt;WARNING&lt;/b&gt;: exporting a large set of results could take a while. It is highly recommended to use the
				&lt;b&gt;Search API&lt;/b&gt; mode for high number of hits, as it allows streaming results.
			</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="exportColumnsConfig" name="ResultList ID" arity="ZERO_OR_ONE">
			<Description>Columns external config ID</Description>
		</Option>
		<Option id="exportMode" name="Export mode" arity="ONE">
			<Description>
				(Only for &lt;b&gt;Hits&lt;/b&gt; mode) The underlying API used to export the data. Only used in &lt;b&gt;Hits&lt;/b&gt; mode (in &lt;b&gt;Synthesis&lt;/b&gt; mode, the data is fully loaded).&lt;br/&gt;
				&lt;b&gt;Access API&lt;/b&gt; mode will execute the feed and attached Access triggers to get the data, querying page after page to get the desired number of results.&lt;br/&gt;
				&lt;b&gt;Search API&lt;/b&gt; mode will execute the provided query to get the data, and stream the results from the index without needing to paginate. It is more efficient than the Access API.&lt;br/&gt;
				&lt;b&gt;WARNINGS&lt;/b&gt;
				&lt;ul&gt;
				&lt;li&gt;
				In &lt;b&gt;Search API&lt;/b&gt; mode, the row order in the exported file is not guaranteed to be the same as displayed in the table. This is because this mode uses
				the streaming option of the Search API, which allows fast download but prevents from sorting documents. Make sure to adapt the hits limit so your users have all the data they need.
				&lt;/li&gt;
				&lt;li&gt;
				In &lt;b&gt;Search API&lt;/b&gt; mode, MEL expressions are evaluated from the &lt;b&gt;entry&lt;/b&gt; scope only. In the Data tab, when configuring column values, the MEL expressions should
				not use any other scopes as the MEL evaluator will not be aware of them (such as &lt;b&gt;feeds&lt;/b&gt;).
				&lt;/li&gt;
				&lt;li&gt;
				The &lt;b&gt;Search API&lt;/b&gt; mode only works with CloudView feeds.
				&lt;/li&gt;
				&lt;/ul&gt;
			</Description>
			<Values>
				<Value>Search API</Value>
				<Value>Access API</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['Access API'], showOptions:['exportPerPage']})</Display>
			</Functions>
		</Option>
		<Option id="exportPerPage" name="Override 'per_page'" arity="ZERO_OR_ONE">
			<Description>
				When using the &lt;b&gt;Access API&lt;/b&gt; export mode, you can override the &lt;code&gt;per_page&lt;/code&gt; parameter of the feed. Setting a higher value will allow to produce less queries to the index.
				Set to &lt;code&gt;-1&lt;/code&gt; to fetch all hits in one query (the &lt;b&gt;Hits limit&lt;/b&gt; still applies). Leave empty to use the value set on the feed.
			</Description>
			<Functions>
				<Display>SetType('number')</Display>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="exportFileName" name="File name" isEvaluated="true" >
			<Description>The name of the created CSV file. Defaults to 'export.csv'</Description>
		</Option>
		<Option id="exportEncoding" name="Encoding" arity="ZERO_OR_ONE" >
			<Description>You can choose an encoding for the exported CSV file. Defaults to UTF-8.</Description>
		</Option>
		<Option id="exportSeparator" name="Separator" arity="ZERO_OR_ONE">
			<Description>Character to use as a separator in the exported CSV file. Defaults to &lt;b&gt;;&lt;/b&gt;.</Description>
		</Option>
		<Option id="exportDelimiter" name="Record Delimiter">
			<Description>
				Character that is used to separate consecutive records in the output file.&lt;br/&gt;Default option will take the value based on OS.&lt;br/&gt;
				&lt;ul&gt;
				&lt;li&gt;Unix -> LF&lt;/li&gt;
				&lt;li&gt;Windows -> CR + LF&lt;/li&gt;
				&lt;li&gt;Mac -> CR&lt;/li&gt;
				&lt;/ul&gt;
			</Description>
			<Values>
				<Value>Default</Value>
				<Value>LF</Value>
				<Value>CR+LF</Value>
				<Value>CR</Value>
			</Values>
		</Option>
		<Option id="exportAddBOM" name="Add BOM">
			<Description>Whether to add the byte order mark (BOM) at the start of the file.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="exportRawValues" name="Export raw values">
			<Description>Export raw values or I18N values (applicable for facets and headers).</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Shared object">
		<Description>Define the objects to be shared with other 3DDashboard widgets.</Description>

		<!-- General config -->
		<Option id="general-share-options" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Share options &lt;/b&gt;&lt;/span&gt;">
			<Functions>
				<Display>SetType('literal')</Display>
			</Functions>
		</Option>

		<Option arity="ONE" id="shareObjectId" isEvaluated="false" name="Object identifier">
			<Description>The identifier of the object to be shared.</Description>
		</Option>
		<Option arity="ONE" id="shareObjectType" isEvaluated="false" name="Object type">
			<Description>The type of the object to be shared.</Description>
		</Option>
		<Option id="shareDisplayName" isEvaluated="false" name="Display name">
			<Description>The displayed name of the object to be shared.</Description>
		</Option>
		<Option id="shareDisplayType" isEvaluated="false" name="Display type">
			<Description>The displayed type of the object to be shared.</Description>
		</Option>
		<Option id="shareDisplayIcon" isEvaluated="false" name="Display icon">
			<Description>The icon of the object to be shared.</Description>
		</Option>
		<Option id="shareDisplayPreview" isEvaluated="false" name="Preview image">
			<Description>The preview image of the object to be shared.</Description>
		</Option>
		<Option id="shareContextId" isEvaluated="false" name="Context">
			<Description>The security context.</Description>
		</Option>

		<!-- Share all items -->
		<Option id="all-share-options" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Share all hits &lt;/b&gt;&lt;/span&gt;">
			<Functions>
				<Display>SetType('literal')</Display>
			</Functions>
		</Option>

		<Option id="enableShareAll" name="Enable" arity="ONE">
			<Description>
				Activate ability to share all objects based on result list query.
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['shareMaxEntries', 'shareIgnoreParams']})</Display>
			</Functions>
		</Option>

		<Option id="shareMaxEntries" isEvaluated="true" name="Max entries" >
			<Description>Maximum number of shared items (default 100, -1 to export all values).</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>

		<Option id="shareIgnoreParams" isEvaluated="true" name="Ignore params" arity="ZERO_OR_MANY">
			<Description>Ignore parameter names from initial query.</Description>
		</Option>

		<!-- Share hits from collection parameters -->

		<Option id="selected-share-options" name="&lt;span class=&quot;parameter-container-header&quot;&gt;&lt;b&gt; Share selected items &lt;/b&gt;&lt;/span&gt;">
			<Functions>
				<Display>SetType('literal')</Display>
			</Functions>
		</Option>

		<Option id="enableShareCollection" name="Enable share collection" arity="ONE">
			<Description>
				Activate ability to share all objects from given collection.
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['shareCollection', 'sharePrefix']})</Display>
			</Functions>
		</Option>

		<Option arity="ONE" id="shareCollection" isEvaluated="true" name="Collection name">
			<Description>Collection name to be shared.</Description>
		</Option>
		<Option arity="ZERO_OR_ONE" id="sharePrefix" isEvaluated="true" name="Query prefix handler">
			<Description>Query prefix handler used to generate query for collection hits. ('rawurl' is used by default if empty)</Description>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Select API">
		<Description>This widget can interact with other widgets in a 3DDashboard. It can publish the user's selection and can subscribe to other widgets' publication.</Description>
		<Option id="enablePublication" name="Enable publication">
			<Description>Enable publication of the user's selection.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="enableSubscription" name="Enable subscription">
			<Description>Enable subscription of selected hits from other widgets.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="channels" name="Channels" arity="ZERO_OR_MANY">
			<Description>Channels to use.</Description>
		</Option>
		<Option id="useApplicationConfig" name="Use the application configuration">
			<Description>Use the application configuration or specify custom configuration.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({ valueToMatch: ['true'], hideOptions: ['channelsConfig'] })</Display>
			</Functions>
		</Option>
		<OptionComposite id="channelsConfig" name="Channels configuration" arity="ZERO_OR_MANY">
			<Option id="topic" name="Topic">
				<Description>Name of the topic.</Description>
			</Option>
			<Option id="data" name="Data" isEvaluated="true">
				<Description>
					<![CDATA[
						JavaScript object that contains useful data. You can use MEL (scope: entry). The hit URL as key is mandatory.<br />
						Example:
<pre>
{
    '${entry.metas['url']}': {
        fullName: '${entry.metas['fullname']}'
    }
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Option id="normalizer" name="Normalizer">
				<Description>
					<![CDATA[
						JavaScript function that returns the appropriate message based on two parameters:
						<ol>
							<li><i>selectedHits</i>: array of hit URLs</li>
							<li><i>data</i>: data previously described</li>
						</ol>
						Example:
<pre>
function (selectedHits, data) {
    return {
        type: 'hello',
        data: selectedHits.map(function (hitUrl) {
            return data[hitUrl].fullName;
        })
    };
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Option id="denormalizer" name="Denormalizer">
				<Description>
					<![CDATA[
						JavaScript function that returns the selected hits based on two parameters:
						<ol>
							<li><i>message</i>: message sent by other widgets</li>
							<li><i>data</i>: data previously described</li>
						</ol>
						Example:
<pre>
function (message, data) {
    return data.map(function (fullName) {
        for (var hitUrl in data) {
            if (data[hitUrl].fullName === fullName) {
                return hitUrl;
            }
        }
        return null;
    });
}
</pre>
					]]>
				</Description>
				<Functions>
					<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
				</Functions>
			</Option>
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="hasHitDetails">true</DefaultValue>
		<DefaultValue name="hasReloadContainer">true</DefaultValue>
		<DefaultValue name="displayHeader">true</DefaultValue>
		<DefaultValue name="ajaxSort">false</DefaultValue>
		<DefaultValue name="numHits">1000</DefaultValue>
		<DefaultValue name="recordDelimiter">Default</DefaultValue>
		<DefaultValue name="excludeMetas">None</DefaultValue>
		<DefaultValue name="enableCompare">false</DefaultValue>
		<DefaultValue name="enableSelect">false</DefaultValue>
		<DefaultValue name="compareCollection">compare</DefaultValue>
		<DefaultValue name="compareView">compare</DefaultValue>
		<DefaultValue name="selectCollection">selected-hits</DefaultValue>
		<DefaultValue name="minCompareSize">2</DefaultValue>
		<DefaultValue name="compareMode">trigger</DefaultValue>
		<DefaultValue name="defaultView">tile</DefaultValue>
		<DefaultValue name="enableExport">false</DefaultValue>
		<DefaultValue name="exportEncoding">UTF-8</DefaultValue>
		<DefaultValue name="exportSeparator">;</DefaultValue>
		<DefaultValue name="exportFileName">export</DefaultValue>
		<DefaultValue name="exportIconCss">fonticon fonticon-export-multiple</DefaultValue>
		<DefaultValue name="exportShowLabel">false</DefaultValue>
		<DefaultValue name="exportLabel">Export data</DefaultValue>
		<DefaultValue name="exportNumHits">1000</DefaultValue>
		<DefaultValue name="exportMode">Search API</DefaultValue>
		<DefaultValue name="exportDelimiter">Default</DefaultValue>
		<DefaultValue name="exportAddBOM">true</DefaultValue>
		<DefaultValue name="exportRawValues">false</DefaultValue>
		<DefaultValue name="closeButtonOnViews">compare</DefaultValue>
		<DefaultValue name="closeButtonOnViews">favoriteHits</DefaultValue>
		<DefaultValue name="enableShareCollection">false</DefaultValue>
		<DefaultValue name="enableShareAll">false</DefaultValue>
		<DefaultValue name="shareCollection">selected-hits</DefaultValue>
		<DefaultValue name="shareObjectId">${entry.metas['physicalid']!-h}</DefaultValue>
		<DefaultValue name="shareObjectType">${entry.metas['type']!-h}</DefaultValue>
		<DefaultValue name="shareDisplayName">${entry.metas['name']!-h}</DefaultValue>
		<DefaultValue name="shareIgnoreParams">of</DefaultValue>
		<DefaultValue name="shareIgnoreParams">hf</DefaultValue>
		<DefaultValue name="shareMaxEntries">100</DefaultValue>
		<DefaultValue name="enableSortButton">true</DefaultValue>
	</DefaultValues>
</Widget>
