<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import varWidget="widget" varFeeds="feeds"/>
<render:import parameters="userGuideCfg" ignore="true"/>

<c:if test="${empty userGuideCfg}">
    <plma:getConfig var="userGuideCfg" id="user_guide" widget="${widget}"/>
</c:if>

<widget:widget extraCss="plmaUserGuide" varUcssId="uCssId">
    <%--
    <widget:header>
        ${title}
    </widget:header>
    --%>
    <widget:content>
        <%-- Tabs --%>
        <ul class="tabs">
                <%-- data-index is a zero-based index used by the tabs widget --%>
            <c:forEach items="${userGuideCfg.section}" var="section" varStatus="status">
                <li><a href="#tabs-${section}" class="tab-item" data-index="${status.count - 1}"><i18n:message code="${section}.title"/></a></li>
            </c:forEach>
            <c:forEach items="${userGuideCfg.additionalSection}" var="additionalSection" varStatus="status">
                <li><a href="#tabs-additional-${status.count}" class="tab-item"
                       data-index="${fn:length(userGuideCfg.section) + status.count - 1}"><string:eval string="${additionalSection.title}"/></a></li>
            </c:forEach>
        </ul>

        <%-- Contents --%>
        <div class="contents">
            <c:forEach items="${userGuideCfg.section}" var="section" varStatus="status">
                <div id="tabs-${section}" class="content" data-index="${status.count - 1}"><i18n:message code="${section}.content"
                                                                                                         htmlEscape="false"/></div>
            </c:forEach>
            <c:forEach items="${userGuideCfg.additionalSection}" var="additionalSection" varStatus="status">
                <div id="tabs-additional-${status.count}" class="content" data-index="${fn:length(userGuideCfg.section) + status.count - 1}">
                    <string:eval string="${additionalSection.section}"/></div>
            </c:forEach>
        </div>
    </widget:content>

    <render:renderScript position="READY">
        var $tabs = $('.' + '${uCssId}' + ' .widgetContent');
        $tabs.tabs();
        /* Links within documentation: open the right tab and scroll to the content */
        $tabs.find('.contents a').click(function(e) {
        e.preventDefault();
        var id = $(this).attr('href');
        var $el = $tabs.find(id);
        if ($el.length) {
        var index = $el.hasClass('tab-item')
        ? $el.data('index')
        : $el.closest('.content').data('index');
        var el = $el.get(0);
        $tabs.tabs('option', 'active', index);
        $tabs
        .scrollTop(0) // reset scrollbar position
        .scrollTop($el.position().top); // then the element is at the top
        }
        });
    </render:renderScript>

</widget:widget>

