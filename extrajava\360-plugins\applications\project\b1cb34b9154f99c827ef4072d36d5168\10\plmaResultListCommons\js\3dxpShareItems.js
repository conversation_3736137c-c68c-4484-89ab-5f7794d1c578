(function (window) {
    'use strict';

    if (window.Share3DXItems !== undefined) {
        return;
    }

    window.Share3DXItems = {
        shareAll: function (options) {
            var $element = $(options.containerId);
            var domElement = $element[0];

            domElement.draggable = true;
            domElement.addEventListener('dragstart', function (event) {
                // Call WS to get all items to share
                $.ajax({
                    method: 'POST',
                    url: options.baseURL,
                    data: {
                        sapiquery: options.sapiquery,
                        page: options.page,
                        feedname: options.feedname,
                        wuid: options.wuid
                    },
                    dataType: 'JSON',
                    async: false,
                    success: function (data) {
                        event.dataTransfer.effectAllowed = 'all';
                        event.dataTransfer.setData('Text', JSON.stringify(data));
                    },
                    error: function (data) {
                        console.error('Error calling URL [' + options.controllerURL + ']');
                    }
                })
            }, false);
        },

        shareCollection: function (options) {
            var $element = $(options.containerId);
            var domElement = $element[0];

            domElement.draggable = true;
            domElement.addEventListener('dragstart', function (event) {
                // Call WS to get all items to share
                $.ajax({
                    method: 'GET',
                    url: options.baseURL,
                    data: {
                        page: options.page,
                        feedname: options.feedname,
                        wuid: options.wuid
                    },
                    dataType: 'JSON',
                    async: false,
                    success: function (data) {
                        event.dataTransfer.effectAllowed = 'all';
                        event.dataTransfer.setData('Text', JSON.stringify(data));
                    },
                    error: function (data) {
                        console.error('Error calling URL [' + options.controllerURL + ']');
                    }
                })
            }, false);
        }
    };
})(window);