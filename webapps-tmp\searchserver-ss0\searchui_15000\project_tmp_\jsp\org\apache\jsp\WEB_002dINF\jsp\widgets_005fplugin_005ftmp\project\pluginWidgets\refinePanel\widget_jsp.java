/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:33 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.refinePanel;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class widget_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_0;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_1;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_2;

static {
  _jspx_fnmap_0= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:getKeys", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "getKeys", new Class[] {java.util.Map.class, java.lang.String.class});
  _jspx_fnmap_1= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:getSuggestFacets", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "getSuggestFacets", new Class[] {java.util.List.class});
  _jspx_fnmap_2= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:trim", org.apache.taglibs.standard.functions.Functions.class, "trim", new Class[] {java.lang.String.class});
}

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(17);
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/deprecated/DisplayHitsWidgetHelper.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/widget.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/i18n.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/list.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/config.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/url.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fmt.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/request.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/map.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetOptionComposite_0026_005fvar_005fname_005fkey_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetConfig_0026_005fwidget_005fvar_005fid_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetFacetSuggestContext_0026_005fvar_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005frefinesPanelConfig_0026_005fvar_005frefinesPanelId_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005frefineFacetsOldConfig_0026_005fvar_005ffeeds_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frequest_005fgetCookieValue_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005funescape_0026_005fvar_005fescapeType;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fsplit_0026_005fvar_005fstring_005fseparator_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fgetLang_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvar_005fshowEmptyFacets_005ffilterMode_005ffeeds_005ffacetsList;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fremove_0026_005fvar_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005fsubWidgets_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frender_005frenderOnce_0026_005fid;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetOptionComposite_0026_005fvar_005fname_005fkey_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetConfig_0026_005fwidget_005fvar_005fid_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetFacetSuggestContext_0026_005fvar_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005frefinesPanelConfig_0026_005fvar_005frefinesPanelId_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005frefineFacetsOldConfig_0026_005fvar_005ffeeds_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frequest_005fgetCookieValue_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005funescape_0026_005fvar_005fescapeType = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fsplit_0026_005fvar_005fstring_005fseparator_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fgetLang_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvar_005fshowEmptyFacets_005ffilterMode_005ffeeds_005ffacetsList = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fremove_0026_005fvar_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005fsubWidgets_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frender_005frenderOnce_0026_005fid = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetOptionComposite_0026_005fvar_005fname_005fkey_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetConfig_0026_005fwidget_005fvar_005fid_005fnobody.release();
    _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss.release();
    _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetFacetSuggestContext_0026_005fvar_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fplma_005frefinesPanelConfig_0026_005fvar_005frefinesPanelId_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
    _005fjspx_005ftagPool_005fplma_005frefineFacetsOldConfig_0026_005fvar_005ffeeds_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.release();
    _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.release();
    _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody.release();
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005frequest_005fgetCookieValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005funescape_0026_005fvar_005fescapeType.release();
    _005fjspx_005ftagPool_005fstring_005fsplit_0026_005fvar_005fstring_005fseparator_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fgetLang_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvar_005fshowEmptyFacets_005ffilterMode_005ffeeds_005ffacetsList.release();
    _005fjspx_005ftagPool_005fc_005fremove_0026_005fvar_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005fsubWidgets_005fnobody.release();
    _005fjspx_005ftagPool_005frender_005frenderOnce_0026_005fid.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_render_005fimport_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fif_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fif_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fif_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fif_005f3(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fset_005f2(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_widget_005fwidget_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005frenderScript_005f2(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(17,0) name = varWidget type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarWidget("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(17,0) name = varFeeds type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setVarFeeds("feeds");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fimport_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f1);
    try {
      _jspx_th_render_005fimport_005f1.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(18,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f1.setParameters("refinePanelCfg,refinesPanelId,refinesPanelMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(18,0) name = ignore type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f1.setIgnore(true);
      _jspx_th_render_005fimport_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(21,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${empty refinesPanelMode}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fset_005f0(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f0_reused = false;
    try {
      _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(22,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setVar("refinesPanelMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(22,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(22,4) 'widget'",_jsp_getExpressionFactory().createValueExpression("widget",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
      if (_jspx_th_c_005fset_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
      _jspx_th_c_005fset_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(26,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${empty refinesPanelId}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_plma_005fgetOptionComposite_005f0(_jspx_th_c_005fif_005f1, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetOptionComposite_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getOptionComposite
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.PLMAGetOptionCompositeTag _jspx_th_plma_005fgetOptionComposite_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.PLMAGetOptionCompositeTag) _005fjspx_005ftagPool_005fplma_005fgetOptionComposite_0026_005fvar_005fname_005fkey_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.PLMAGetOptionCompositeTag.class);
    boolean _jspx_th_plma_005fgetOptionComposite_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetOptionComposite_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetOptionComposite_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(27,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetOptionComposite_005f0.setVar("refinesPanelId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(27,4) name = name type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetOptionComposite_005f0.setName("externalConfig");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(27,4) name = key type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetOptionComposite_005f0.setKey("refinesPanelId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(27,4) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetOptionComposite_005f0.setDefaultValue("");
      int[] _jspx_push_body_count_plma_005fgetOptionComposite_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetOptionComposite_005f0 = _jspx_th_plma_005fgetOptionComposite_005f0.doStartTag();
        if (_jspx_th_plma_005fgetOptionComposite_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetOptionComposite_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetOptionComposite_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetOptionComposite_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetOptionComposite_0026_005fvar_005fname_005fkey_005fdefaultValue_005fnobody.reuse(_jspx_th_plma_005fgetOptionComposite_005f0);
      _jspx_th_plma_005fgetOptionComposite_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetOptionComposite_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetOptionComposite_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(31,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${empty refinePanelCfg}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_plma_005fgetConfig_005f0(_jspx_th_c_005fif_005f2, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetConfig_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getConfig
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.GetConfigObject _jspx_th_plma_005fgetConfig_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.GetConfigObject) _005fjspx_005ftagPool_005fplma_005fgetConfig_0026_005fwidget_005fvar_005fid_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.config.GetConfigObject.class);
    boolean _jspx_th_plma_005fgetConfig_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetConfig_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetConfig_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(32,4) name = var type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConfig_005f0.setVar("refinePanelCfg");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(32,4) name = id type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConfig_005f0.setId("refine_panel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(32,4) name = widget type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConfig_005f0.setWidget((com.exalead.cv360.searchui.configuration.v10.Widget) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", com.exalead.cv360.searchui.configuration.v10.Widget.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetConfig_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetConfig_005f0 = _jspx_th_plma_005fgetConfig_005f0.doStartTag();
        if (_jspx_th_plma_005fgetConfig_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetConfig_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetConfig_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetConfig_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetConfig_0026_005fwidget_005fvar_005fid_005fnobody.reuse(_jspx_th_plma_005fgetConfig_005f0);
      _jspx_th_plma_005fgetConfig_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetConfig_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetConfig_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(36,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${empty refinesPanelId && not empty refinePanelCfg.refinesPanelId}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fset_005f1(_jspx_th_c_005fif_005f3, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f1_reused = false;
    try {
      _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(37,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setVar("refinesPanelId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(37,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(37,4) '${refinePanelCfg.refinesPanelId}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${refinePanelCfg.refinesPanelId}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
      if (_jspx_th_c_005fset_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
      _jspx_th_c_005fset_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f2_reused = false;
    try {
      _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(40,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setVar("suggestCfg");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(40,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(40,0) '${refinePanelCfg.search}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${refinePanelCfg.search}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
      if (_jspx_th_c_005fset_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
      _jspx_th_c_005fset_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_widget_005fwidget_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  widget:widget
    com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag _jspx_th_widget_005fwidget_005f0 = (com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag) _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss.get(com.exalead.cv360.searchui.view.jspapi.widget.WidgetTag.class);
    boolean _jspx_th_widget_005fwidget_005f0_reused = false;
    try {
      _jspx_th_widget_005fwidget_005f0.setPageContext(_jspx_page_context);
      _jspx_th_widget_005fwidget_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(42,0) name = varCssId type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setVarCssId("cssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(42,0) name = varUcssId type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setVarUcssId("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(42,0) name = extraCss type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_widget_005fwidget_005f0.setExtraCss("refinePanel refines");
      int[] _jspx_push_body_count_widget_005fwidget_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_widget_005fwidget_005f0 = _jspx_th_widget_005fwidget_005f0.doStartTag();
        if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_widget_005fwidget_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_widget_005fwidget_005f0);
          }
          do {
            out.write("\r\n");
            out.write("    ");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_string_005feval_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_plma_005fgetFacetSuggestContext_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fchoose_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("\r\n");
            out.write("    <div class=\"spinner-container\">\r\n");
            out.write("        <div class=\"spinner\"></div>\r\n");
            out.write("    </div>\r\n");
            out.write("\r\n");
            out.write("    <div class=\"refine-main-container\">\r\n");
            out.write("            ");
            out.write("\r\n");
            out.write("        <div class=\"searchWidget searchFormRefine\">\r\n");
            out.write("            ");
            if (_jspx_meth_c_005fif_005f4(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("            <!-- Add collapse Button -->\r\n");
            out.write("            <div class=\"collapseButton\">\r\n");
            out.write("                ");
            if (_jspx_meth_i18n_005fmessage_005f1(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("                <span class=\"fonticon fonticon-collapse-all\" title=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${collapseButtonTooltip}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\"></span>\r\n");
            out.write("            </div>\r\n");
            out.write("            <!-- Add expand Button -->\r\n");
            out.write("            <div class=\"expandButton\">\r\n");
            out.write("                ");
            if (_jspx_meth_i18n_005fmessage_005f2(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("                <span class=\"fonticon fonticon-expand-all\" title=\"");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${expandButtonTooltip}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("\"></span>\r\n");
            out.write("            </div>\r\n");
            out.write("\r\n");
            out.write("                ");
            out.write("\r\n");
            out.write("            <div class=\"searchContainer\">\r\n");
            out.write("                ");
            if (_jspx_meth_c_005fif_005f5(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("            </div>\r\n");
            out.write("        </div>\r\n");
            out.write("            ");
            out.write("\r\n");
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fset_005f3(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fset_005f4(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("        <div class=\"is-filter-activated\" style=\"display: none;\">");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.activateFilter}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("</div>\r\n");
            out.write("        <div class=\"activation-filter-min\" style=\"display: none;\">");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.filterMin}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("</div>\r\n");
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_request_005fgetCookieValue_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_string_005funescape_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_string_005fsplit_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_c_005fset_005f5(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_i18n_005fmessage_005f4(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("        ");
            if (_jspx_meth_i18n_005fmessage_005f5(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("        <div class=\"widgetContent facets\">\r\n");
            out.write("            <div class=\"overlay-facets\"></div>\r\n");
            out.write("            <div id=\"currentLangRefinePanel\" style=\"display: none;\">");
            if (_jspx_meth_i18n_005fgetLang_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("</div>\r\n");
            out.write("            ");
            if (_jspx_meth_c_005fforEach_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("        </div>\r\n");
            out.write("    </div>\r\n");
            out.write("\r\n");
            out.write("    <div class=\"subwidgets\">\r\n");
            out.write("        ");
            if (_jspx_meth_render_005fsubWidgets_005f0(_jspx_th_widget_005fwidget_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
              return true;
            out.write("\r\n");
            out.write("    </div>\r\n");
            out.write("\r\n");
            int evalDoAfterBody = _jspx_th_widget_005fwidget_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_widget_005fwidget_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_widget_005fwidget_005f0[0]--;
          }
        }
        if (_jspx_th_widget_005fwidget_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_widget_005fwidget_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_widget_005fwidget_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_widget_005fwidget_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fwidget_005fwidget_0026_005fvarUcssId_005fvarCssId_005fextraCss.reuse(_jspx_th_widget_005fwidget_005f0);
      _jspx_th_widget_005fwidget_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_widget_005fwidget_005f0, _jsp_getInstanceManager(), _jspx_th_widget_005fwidget_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f0_reused = false;
    try {
      _jspx_th_string_005feval_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(44,4) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${suggestCfg.placeHolder}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(44,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setVar("placeHolder");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(44,4) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005feval_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f0 = _jspx_th_string_005feval_005f0.doStartTag();
        if (_jspx_th_string_005feval_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffeeds_005fnobody.reuse(_jspx_th_string_005feval_005f0);
      _jspx_th_string_005feval_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f0, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetFacetSuggestContext_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getFacetSuggestContext
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetFacetSuggestContextTag _jspx_th_plma_005fgetFacetSuggestContext_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetFacetSuggestContextTag) _005fjspx_005ftagPool_005fplma_005fgetFacetSuggestContext_0026_005fvar_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetFacetSuggestContextTag.class);
    boolean _jspx_th_plma_005fgetFacetSuggestContext_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetFacetSuggestContext_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetFacetSuggestContext_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(46,4) name = var type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetFacetSuggestContext_005f0.setVar("facetsSuggestContext");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(46,4) name = feeds type = java.util.Map reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetFacetSuggestContext_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetFacetSuggestContext_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetFacetSuggestContext_005f0 = _jspx_th_plma_005fgetFacetSuggestContext_005f0.doStartTag();
        if (_jspx_th_plma_005fgetFacetSuggestContext_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetFacetSuggestContext_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetFacetSuggestContext_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetFacetSuggestContext_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetFacetSuggestContext_0026_005fvar_005ffeeds_005fnobody.reuse(_jspx_th_plma_005fgetFacetSuggestContext_005f0);
      _jspx_th_plma_005fgetFacetSuggestContext_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetFacetSuggestContext_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetFacetSuggestContext_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(49,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.useExternalisedConfig}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_plma_005frefinesPanelConfig_005f0(_jspx_th_c_005fwhen_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005frefinesPanelConfig_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:refinesPanelConfig
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetRefinesPanelConfigTag _jspx_th_plma_005frefinesPanelConfig_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetRefinesPanelConfigTag) _005fjspx_005ftagPool_005fplma_005frefinesPanelConfig_0026_005fvar_005frefinesPanelId_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetRefinesPanelConfigTag.class);
    boolean _jspx_th_plma_005frefinesPanelConfig_005f0_reused = false;
    try {
      _jspx_th_plma_005frefinesPanelConfig_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005frefinesPanelConfig_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(50,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005frefinesPanelConfig_005f0.setVar("allFacets");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(50,12) name = refinesPanelId type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005frefinesPanelConfig_005f0.setRefinesPanelId((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinesPanelId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005frefinesPanelConfig_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005frefinesPanelConfig_005f0 = _jspx_th_plma_005frefinesPanelConfig_005f0.doStartTag();
        if (_jspx_th_plma_005frefinesPanelConfig_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005frefinesPanelConfig_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005frefinesPanelConfig_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005frefinesPanelConfig_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005frefinesPanelConfig_0026_005fvar_005frefinesPanelId_005fnobody.reuse(_jspx_th_plma_005frefinesPanelConfig_005f0);
      _jspx_th_plma_005frefinesPanelConfig_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005frefinesPanelConfig_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005frefinesPanelConfig_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_plma_005frefineFacetsOldConfig_005f0(_jspx_th_c_005fotherwise_005f0, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005frefineFacetsOldConfig_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:refineFacetsOldConfig
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetRefineFacetsFromOldConfig _jspx_th_plma_005frefineFacetsOldConfig_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetRefineFacetsFromOldConfig) _005fjspx_005ftagPool_005fplma_005frefineFacetsOldConfig_0026_005fvar_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.facet.GetRefineFacetsFromOldConfig.class);
    boolean _jspx_th_plma_005frefineFacetsOldConfig_005f0_reused = false;
    try {
      _jspx_th_plma_005frefineFacetsOldConfig_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005frefineFacetsOldConfig_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(53,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005frefineFacetsOldConfig_005f0.setVar("allFacets");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(53,12) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005frefineFacetsOldConfig_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005frefineFacetsOldConfig_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005frefineFacetsOldConfig_005f0 = _jspx_th_plma_005frefineFacetsOldConfig_005f0.doStartTag();
        if (_jspx_th_plma_005frefineFacetsOldConfig_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005frefineFacetsOldConfig_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005frefineFacetsOldConfig_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005frefineFacetsOldConfig_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005frefineFacetsOldConfig_0026_005fvar_005ffeeds_005fnobody.reuse(_jspx_th_plma_005frefineFacetsOldConfig_005f0);
      _jspx_th_plma_005frefineFacetsOldConfig_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005frefineFacetsOldConfig_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005frefineFacetsOldConfig_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f4_reused = false;
    try {
      _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(65,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.displayCloseBtn}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
      if (_jspx_eval_c_005fif_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                <!-- Add Close Button -->\r\n");
          out.write("                <div class=\"closeButton\">\r\n");
          out.write("                    ");
          if (_jspx_meth_i18n_005fmessage_005f0(_jspx_th_c_005fif_005f4, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("                    <span class=\"fonticon fonticon-fast-forward\" title=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${closeButtonTooltip}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"></span>\r\n");
          out.write("                </div>\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      _jspx_th_c_005fif_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f0 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f0_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f0.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(68,20) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setVar("closeButtonTooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(68,20) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setCode("refinepanel.toolbar.item.cancel.tooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(68,20) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setText("Close Refine Panel");
      int[] _jspx_push_body_count_i18n_005fmessage_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f0 = _jspx_th_i18n_005fmessage_005f0.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f0);
      _jspx_th_i18n_005fmessage_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f0, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f1 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f1_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f1.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(75,16) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setVar("collapseButtonTooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(75,16) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setCode("refinepanel.toolbar.item.collapseall.tooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(75,16) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setText("Collapse all");
      int[] _jspx_push_body_count_i18n_005fmessage_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f1 = _jspx_th_i18n_005fmessage_005f1.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f1);
      _jspx_th_i18n_005fmessage_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f1, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f2 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f2_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f2.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(80,16) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setVar("expandButtonTooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(80,16) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setCode("refinepanel.toolbar.item.expandall.tooltip");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(80,16) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setText("Expand all");
      int[] _jspx_push_body_count_i18n_005fmessage_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f2 = _jspx_th_i18n_005fmessage_005f2.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f2);
      _jspx_th_i18n_005fmessage_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f2, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f5 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f5_reused = false;
    try {
      _jspx_th_c_005fif_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(86,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.enableSuggest}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f5 = _jspx_th_c_005fif_005f5.doStartTag();
      if (_jspx_eval_c_005fif_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    <div class=\"searchFormContent\">\r\n");
          out.write("                        <input type=\"text\"\r\n");
          out.write("                               name=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                               value=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetsSuggestContext.suggestContext ? facetsSuggestContext.suggestQuery : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"\r\n");
          out.write("                               class=\"searchInput\"\r\n");
          out.write("                               placeholder=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${placeHolder}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\"/>\r\n");
          out.write("                        ");
          if (_jspx_meth_i18n_005fmessage_005f3(_jspx_th_c_005fif_005f5, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("                        <div class=\"searchButton fonticon fonticon-search-filter\" title=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${searchFilterText}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("                        </div>\r\n");
          out.write("                    </div>\r\n");
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fchoose_005f1(_jspx_th_c_005fif_005f5, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f5);
      _jspx_th_c_005fif_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f3 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f3_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f3.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(93,24) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f3.setVar("searchFilterText");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(93,24) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f3.setCode("widgets.refinePanel.search-filter.placeholder");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(93,24) name = text type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f3.setText("Search filters...");
      int[] _jspx_push_body_count_i18n_005fmessage_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f3 = _jspx_th_i18n_005fmessage_005f3.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005ftext_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f3);
      _jspx_th_i18n_005fmessage_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f3, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f1 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f1_reused = false;
    try {
      _jspx_th_c_005fchoose_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f5);
      int _jspx_eval_c_005fchoose_005f1 = _jspx_th_c_005fchoose_005f1.doStartTag();
      if (_jspx_eval_c_005fchoose_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_c_005fwhen_005f1(_jspx_th_c_005fchoose_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_c_005fotherwise_005f1(_jspx_th_c_005fchoose_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f1);
      _jspx_th_c_005fchoose_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f1 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f1_reused = false;
    try {
      _jspx_th_c_005fwhen_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(100,24) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${suggestCfg.suggestApiAction == 'access'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f1 = _jspx_th_c_005fwhen_005f1.doStartTag();
      if (_jspx_eval_c_005fwhen_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_render_005frenderScript_005f0(_jspx_th_c_005fwhen_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f1);
      _jspx_th_c_005fwhen_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f0_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(101,28) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f0.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f0 = _jspx_th_render_005frenderScript_005f0.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f0);
          }
          do {
            out.write("\r\n");
            out.write("                                var options = {};\r\n");
            out.write("                                options.cssId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.feedName = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.refinesPanelId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinesPanelId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.baseSuggestURL = '");
            if (_jspx_meth_c_005furl_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write('/');
            if (_jspx_meth_search_005fgetPageName_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("                                options.baseAjaxReload = '");
            if (_jspx_meth_c_005furl_005f1(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write('/');
            if (_jspx_meth_search_005fgetPageName_005f1(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write('/');
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinesPanelMode}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.feeds = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getKeys(feeds, '[]')}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_0));
            out.write(";\r\n");
            out.write("                                options.wuid = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget.wuid}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.pageName = '");
            if (_jspx_meth_url_005fgetPageName_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("                                options.minLength = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${suggestCfg.minLength}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.suggestFacets = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getSuggestFacets(allFacets)}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_1));
            out.write("';\r\n");
            out.write("                                options.paramNameReload = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.paramNameReload}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.dispatcherName = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.search.dispatcherName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.delayTime = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.search.delayTime}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.loadDateFacetParameter = '';\r\n");
            out.write("                                options.loadFacetParameter = '");
            if (_jspx_meth_plma_005fgetConstantValueFromNameTag_005f0(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("';\r\n");
            out.write("                                options.maxSuggests = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${suggestCfg.maxSuggests}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(";\r\n");
            out.write("                                ");
            if (_jspx_meth_c_005fif_005f6(_jspx_th_render_005frenderScript_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f0))
              return true;
            out.write("\r\n");
            out.write("                                var refinePanelSearch = new RefinePanelSearchAccess('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("', options);\r\n");
            out.write("                            ");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f0);
      _jspx_th_render_005frenderScript_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f0 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f0_reused = false;
    try {
      _jspx_th_c_005furl_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(106,58) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f0.setValue("/plma/suggest/facets");
      int _jspx_eval_c_005furl_005f0 = _jspx_th_c_005furl_005f0.doStartTag();
      if (_jspx_th_c_005furl_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f0);
      _jspx_th_c_005furl_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f0, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetPageName_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getPageName
    com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag _jspx_th_search_005fgetPageName_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag) _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag.class);
    boolean _jspx_th_search_005fgetPageName_005f0_reused = false;
    try {
      _jspx_th_search_005fgetPageName_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetPageName_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int[] _jspx_push_body_count_search_005fgetPageName_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetPageName_005f0 = _jspx_th_search_005fgetPageName_005f0.doStartTag();
        if (_jspx_th_search_005fgetPageName_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetPageName_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetPageName_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetPageName_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.reuse(_jspx_th_search_005fgetPageName_005f0);
      _jspx_th_search_005fgetPageName_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetPageName_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetPageName_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f1 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f1_reused = false;
    try {
      _jspx_th_c_005furl_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(107,58) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f1.setValue("/plma/ajax/refine");
      int _jspx_eval_c_005furl_005f1 = _jspx_th_c_005furl_005f1.doStartTag();
      if (_jspx_th_c_005furl_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f1);
      _jspx_th_c_005furl_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f1, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetPageName_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getPageName
    com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag _jspx_th_search_005fgetPageName_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag) _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag.class);
    boolean _jspx_th_search_005fgetPageName_005f1_reused = false;
    try {
      _jspx_th_search_005fgetPageName_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetPageName_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int[] _jspx_push_body_count_search_005fgetPageName_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetPageName_005f1 = _jspx_th_search_005fgetPageName_005f1.doStartTag();
        if (_jspx_th_search_005fgetPageName_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetPageName_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetPageName_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetPageName_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.reuse(_jspx_th_search_005fgetPageName_005f1);
      _jspx_th_search_005fgetPageName_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetPageName_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fgetPageName_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_url_005fgetPageName_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  url:getPageName
    com.exalead.cv360.searchui.view.jspapi.url.GetPageNameTag _jspx_th_url_005fgetPageName_005f0 = (com.exalead.cv360.searchui.view.jspapi.url.GetPageNameTag) _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.url.GetPageNameTag.class);
    boolean _jspx_th_url_005fgetPageName_005f0_reused = false;
    try {
      _jspx_th_url_005fgetPageName_005f0.setPageContext(_jspx_page_context);
      _jspx_th_url_005fgetPageName_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      int[] _jspx_push_body_count_url_005fgetPageName_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_url_005fgetPageName_005f0 = _jspx_th_url_005fgetPageName_005f0.doStartTag();
        if (_jspx_th_url_005fgetPageName_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_url_005fgetPageName_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_url_005fgetPageName_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_url_005fgetPageName_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody.reuse(_jspx_th_url_005fgetPageName_005f0);
      _jspx_th_url_005fgetPageName_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_url_005fgetPageName_005f0, _jsp_getInstanceManager(), _jspx_th_url_005fgetPageName_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetConstantValueFromNameTag_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getConstantValueFromNameTag
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag _jspx_th_plma_005fgetConstantValueFromNameTag_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag) _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag.class);
    boolean _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(117,62) name = constantName type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.setConstantName("CLEAN_FEED_PARAMETER");
      int[] _jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetConstantValueFromNameTag_005f0 = _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doStartTag();
        if (_jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody.reuse(_jspx_th_plma_005fgetConstantValueFromNameTag_005f0);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetConstantValueFromNameTag_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetConstantValueFromNameTag_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f6 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f6_reused = false;
    try {
      _jspx_th_c_005fif_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(119,32) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f6.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refineMode.equals('Event')}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f6 = _jspx_th_c_005fif_005f6.doStartTag();
      if (_jspx_eval_c_005fif_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                    options.refineEventMode = true;\r\n");
          out.write("                                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f6);
      _jspx_th_c_005fif_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f1 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f1_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      int _jspx_eval_c_005fotherwise_005f1 = _jspx_th_c_005fotherwise_005f1.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_render_005frenderScript_005f1(_jspx_th_c_005fotherwise_005f1, _jspx_page_context, _jspx_push_body_count_widget_005fwidget_005f0))
            return true;
          out.write("\r\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f1);
      _jspx_th_c_005fotherwise_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f1 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f1_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f1.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(126,28) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f1.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f1 = _jspx_th_render_005frenderScript_005f1.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f1[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f1);
          }
          do {
            out.write("\r\n");
            out.write("                                var options = {};\r\n");
            out.write("                                options.paramNameReload = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.paramNameReload}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.hasSuggest = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.enableSuggest}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write(";\r\n");
            out.write("                                options.cssId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.suggestUrl = '");
            if (_jspx_meth_c_005furl_005f2(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write("';\r\n");
            out.write("                                options.feedName = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feedName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.refinesPanelId = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinesPanelId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.baseAjaxReload = '");
            if (_jspx_meth_c_005furl_005f3(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write('/');
            if (_jspx_meth_search_005fgetPageName_005f2(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write('/');
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinesPanelMode}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.feeds = ");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:getKeys(feeds, '[]')}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_0));
            out.write(";\r\n");
            out.write("                                options.wuid = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget.wuid}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.loadFacetParameter = '");
            if (_jspx_meth_plma_005fgetConstantValueFromNameTag_005f1(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write("';\r\n");
            out.write("                                options.pageName = '");
            if (_jspx_meth_url_005fgetPageName_005f1(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write("';\r\n");
            out.write("                                options.pageId = '");
            if (_jspx_meth_request_005fgetParameterValue_005f0(_jspx_th_render_005frenderScript_005f1, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f1))
              return true;
            out.write("';\r\n");
            out.write("                                options.customDelimiter = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${suggestCfg.customDelimiter}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                options.minLength = '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${suggestCfg.minLength}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("';\r\n");
            out.write("                                var refinePanelSearch = new RefinePanelSearch('");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("', options);\r\n");
            out.write("                            ");
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f1.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f1 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f1[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f1);
      _jspx_th_render_005frenderScript_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f1, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f2 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f2_reused = false;
    try {
      _jspx_th_c_005furl_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(131,54) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f2.setValue((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("/plma/suggest/widget/${feedName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int _jspx_eval_c_005furl_005f2 = _jspx_th_c_005furl_005f2.doStartTag();
      if (_jspx_th_c_005furl_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f2);
      _jspx_th_c_005furl_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f2, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005furl_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:url
    org.apache.taglibs.standard.tag.rt.core.UrlTag _jspx_th_c_005furl_005f3 = (org.apache.taglibs.standard.tag.rt.core.UrlTag) _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.UrlTag.class);
    boolean _jspx_th_c_005furl_005f3_reused = false;
    try {
      _jspx_th_c_005furl_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005furl_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(134,58) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005furl_005f3.setValue("/plma/ajax/refine");
      int _jspx_eval_c_005furl_005f3 = _jspx_th_c_005furl_005f3.doStartTag();
      if (_jspx_th_c_005furl_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005furl_0026_005fvalue_005fnobody.reuse(_jspx_th_c_005furl_005f3);
      _jspx_th_c_005furl_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005furl_005f3, _jsp_getInstanceManager(), _jspx_th_c_005furl_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetPageName_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getPageName
    com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag _jspx_th_search_005fgetPageName_005f2 = (com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag) _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetPageNameTag.class);
    boolean _jspx_th_search_005fgetPageName_005f2_reused = false;
    try {
      _jspx_th_search_005fgetPageName_005f2.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetPageName_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      int[] _jspx_push_body_count_search_005fgetPageName_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetPageName_005f2 = _jspx_th_search_005fgetPageName_005f2.doStartTag();
        if (_jspx_th_search_005fgetPageName_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetPageName_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetPageName_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetPageName_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetPageName_005fnobody.reuse(_jspx_th_search_005fgetPageName_005f2);
      _jspx_th_search_005fgetPageName_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetPageName_005f2, _jsp_getInstanceManager(), _jspx_th_search_005fgetPageName_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetConstantValueFromNameTag_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getConstantValueFromNameTag
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag _jspx_th_plma_005fgetConstantValueFromNameTag_005f1 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag) _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag.class);
    boolean _jspx_th_plma_005fgetConstantValueFromNameTag_005f1_reused = false;
    try {
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f1.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(137,62) name = constantName type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f1.setConstantName("CLEAN_FEED_PARAMETER");
      int[] _jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetConstantValueFromNameTag_005f1 = _jspx_th_plma_005fgetConstantValueFromNameTag_005f1.doStartTag();
        if (_jspx_th_plma_005fgetConstantValueFromNameTag_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody.reuse(_jspx_th_plma_005fgetConstantValueFromNameTag_005f1);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetConstantValueFromNameTag_005f1, _jsp_getInstanceManager(), _jspx_th_plma_005fgetConstantValueFromNameTag_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_url_005fgetPageName_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  url:getPageName
    com.exalead.cv360.searchui.view.jspapi.url.GetPageNameTag _jspx_th_url_005fgetPageName_005f1 = (com.exalead.cv360.searchui.view.jspapi.url.GetPageNameTag) _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.url.GetPageNameTag.class);
    boolean _jspx_th_url_005fgetPageName_005f1_reused = false;
    try {
      _jspx_th_url_005fgetPageName_005f1.setPageContext(_jspx_page_context);
      _jspx_th_url_005fgetPageName_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      int[] _jspx_push_body_count_url_005fgetPageName_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_url_005fgetPageName_005f1 = _jspx_th_url_005fgetPageName_005f1.doStartTag();
        if (_jspx_th_url_005fgetPageName_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_url_005fgetPageName_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_url_005fgetPageName_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_url_005fgetPageName_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005furl_005fgetPageName_005fnobody.reuse(_jspx_th_url_005fgetPageName_005f1);
      _jspx_th_url_005fgetPageName_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_url_005fgetPageName_005f1, _jsp_getInstanceManager(), _jspx_th_url_005fgetPageName_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetParameterValue_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f1)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getParameterValue
    com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag _jspx_th_request_005fgetParameterValue_005f0 = (com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag) _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag.class);
    boolean _jspx_th_request_005fgetParameterValue_005f0_reused = false;
    try {
      _jspx_th_request_005fgetParameterValue_005f0.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetParameterValue_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(139,50) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setName("pageId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(139,50) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setDefaultValue("");
      int[] _jspx_push_body_count_request_005fgetParameterValue_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetParameterValue_005f0 = _jspx_th_request_005fgetParameterValue_005f0.doStartTag();
        if (_jspx_th_request_005fgetParameterValue_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetParameterValue_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetParameterValue_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetParameterValue_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_request_005fgetParameterValue_005f0);
      _jspx_th_request_005fgetParameterValue_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetParameterValue_005f0, _jsp_getInstanceManager(), _jspx_th_request_005fgetParameterValue_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f3_reused = false;
    try {
      _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(151,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setVar("facetTemplateDirectory");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(151,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(151,8) 'templates/default'",_jsp_getExpressionFactory().createValueExpression("templates/default",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
      if (_jspx_th_c_005fset_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
      _jspx_th_c_005fset_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f4_reused = false;
    try {
      _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(152,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setVar("noResults");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(152,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(152,8) 'true'",_jsp_getExpressionFactory().createValueExpression("true",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
      if (_jspx_th_c_005fset_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
      _jspx_th_c_005fset_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetCookieValue_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getCookieValue
    com.exalead.cv360.searchui.view.jspapi.request.GetCookieValueTag _jspx_th_request_005fgetCookieValue_005f0 = (com.exalead.cv360.searchui.view.jspapi.request.GetCookieValueTag) _005fjspx_005ftagPool_005frequest_005fgetCookieValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetCookieValueTag.class);
    boolean _jspx_th_request_005fgetCookieValue_005f0_reused = false;
    try {
      _jspx_th_request_005fgetCookieValue_005f0.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetCookieValue_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(157,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetCookieValue_005f0.setVar("cookieVal");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(157,8) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetCookieValue_005f0.setName("refineWidget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(157,8) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetCookieValue_005f0.setDefaultValue("");
      int[] _jspx_push_body_count_request_005fgetCookieValue_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetCookieValue_005f0 = _jspx_th_request_005fgetCookieValue_005f0.doStartTag();
        if (_jspx_th_request_005fgetCookieValue_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetCookieValue_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetCookieValue_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetCookieValue_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetCookieValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_request_005fgetCookieValue_005f0);
      _jspx_th_request_005fgetCookieValue_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetCookieValue_005f0, _jsp_getInstanceManager(), _jspx_th_request_005fgetCookieValue_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005funescape_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:unescape
    com.exalead.cv360.searchui.view.jspapi.string.UnescapeTag _jspx_th_string_005funescape_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.UnescapeTag) _005fjspx_005ftagPool_005fstring_005funescape_0026_005fvar_005fescapeType.get(com.exalead.cv360.searchui.view.jspapi.string.UnescapeTag.class);
    boolean _jspx_th_string_005funescape_005f0_reused = false;
    try {
      _jspx_th_string_005funescape_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005funescape_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(158,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005funescape_005f0.setVar("cookieVal");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(158,8) name = escapeType type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005funescape_005f0.setEscapeType("URL");
      int[] _jspx_push_body_count_string_005funescape_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005funescape_005f0 = _jspx_th_string_005funescape_005f0.doStartTag();
        if (_jspx_eval_string_005funescape_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_string_005funescape_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_string_005funescape_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_string_005funescape_005f0);
          }
          do {
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cookieVal}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            int evalDoAfterBody = _jspx_th_string_005funescape_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_string_005funescape_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_string_005funescape_005f0[0]--;
          }
        }
        if (_jspx_th_string_005funescape_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005funescape_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005funescape_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005funescape_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005funescape_0026_005fvar_005fescapeType.reuse(_jspx_th_string_005funescape_005f0);
      _jspx_th_string_005funescape_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005funescape_005f0, _jsp_getInstanceManager(), _jspx_th_string_005funescape_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fsplit_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:split
    com.exalead.cv360.searchui.view.jspapi.string.SplitTag _jspx_th_string_005fsplit_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.SplitTag) _005fjspx_005ftagPool_005fstring_005fsplit_0026_005fvar_005fstring_005fseparator_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.SplitTag.class);
    boolean _jspx_th_string_005fsplit_005f0_reused = false;
    try {
      _jspx_th_string_005fsplit_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005fsplit_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(159,8) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fsplit_005f0.setVar("cookieValues");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(159,8) name = separator type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fsplit_005f0.setSeparator(";");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(159,8) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005fsplit_005f0.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cookieVal}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005fsplit_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fsplit_005f0 = _jspx_th_string_005fsplit_005f0.doStartTag();
        if (_jspx_th_string_005fsplit_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fsplit_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fsplit_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fsplit_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fsplit_0026_005fvar_005fstring_005fseparator_005fnobody.reuse(_jspx_th_string_005fsplit_005f0);
      _jspx_th_string_005fsplit_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fsplit_005f0, _jsp_getInstanceManager(), _jspx_th_string_005fsplit_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f5 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f5_reused = false;
    try {
      _jspx_th_c_005fset_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(160,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setVar("cookieClassName");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(160,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(160,8) 'table-collapsable'",_jsp_getExpressionFactory().createValueExpression("table-collapsable",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f5 = _jspx_th_c_005fset_005f5.doStartTag();
      if (_jspx_th_c_005fset_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f5);
      _jspx_th_c_005fset_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f4 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f4_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f4.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(162,8) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f4.setCode("plma.tools.search");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(162,8) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f4.setVar("searchI18n");
      int[] _jspx_push_body_count_i18n_005fmessage_005f4 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f4 = _jspx_th_i18n_005fmessage_005f4.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f4[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f4.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f4.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f4);
      _jspx_th_i18n_005fmessage_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f4, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f5 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f5_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f5.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(163,8) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f5.setCode("refines.noresult");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(163,8) name = var type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f5.setVar("noResultI18n");
      int[] _jspx_push_body_count_i18n_005fmessage_005f5 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f5 = _jspx_th_i18n_005fmessage_005f5.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f5[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f5.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f5.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fvar_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f5);
      _jspx_th_i18n_005fmessage_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f5, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fgetLang_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:getLang
    com.exalead.cv360.searchui.view.jspapi.i18n.GetLangTag _jspx_th_i18n_005fgetLang_005f0 = (com.exalead.cv360.searchui.view.jspapi.i18n.GetLangTag) _005fjspx_005ftagPool_005fi18n_005fgetLang_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.GetLangTag.class);
    boolean _jspx_th_i18n_005fgetLang_005f0_reused = false;
    try {
      _jspx_th_i18n_005fgetLang_005f0.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fgetLang_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      int[] _jspx_push_body_count_i18n_005fgetLang_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fgetLang_005f0 = _jspx_th_i18n_005fgetLang_005f0.doStartTag();
        if (_jspx_th_i18n_005fgetLang_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fgetLang_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fgetLang_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fgetLang_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fgetLang_005fnobody.reuse(_jspx_th_i18n_005fgetLang_005f0);
      _jspx_th_i18n_005fgetLang_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fgetLang_005f0, _jsp_getInstanceManager(), _jspx_th_i18n_005fgetLang_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fforEach_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:forEach
    org.apache.taglibs.standard.tag.rt.core.ForEachTag _jspx_th_c_005fforEach_005f0 = (org.apache.taglibs.standard.tag.rt.core.ForEachTag) _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.get(org.apache.taglibs.standard.tag.rt.core.ForEachTag.class);
    boolean _jspx_th_c_005fforEach_005f0_reused = false;
    try {
      _jspx_th_c_005fforEach_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fforEach_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(168,12) name = items type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setItems(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(168,12) '${allFacets}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${allFacets}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(168,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fforEach_005f0.setVar("facetConfig");
      int[] _jspx_push_body_count_c_005fforEach_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_c_005fforEach_005f0 = _jspx_th_c_005fforEach_005f0.doStartTag();
        if (_jspx_eval_c_005fforEach_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_c_005fset_005f6(_jspx_th_c_005fforEach_005f0, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
              return true;
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_c_005fif_005f7(_jspx_th_c_005fforEach_005f0, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("                ");
            if (_jspx_meth_c_005fchoose_005f2(_jspx_th_c_005fforEach_005f0, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
              return true;
            out.write("\r\n");
            out.write("            ");
            int evalDoAfterBody = _jspx_th_c_005fforEach_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_c_005fforEach_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_c_005fforEach_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_c_005fforEach_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_c_005fforEach_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fc_005fforEach_0026_005fvar_005fitems.reuse(_jspx_th_c_005fforEach_005f0);
      _jspx_th_c_005fforEach_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fforEach_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fforEach_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f6 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f6_reused = false;
    try {
      _jspx_th_c_005fset_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(169,16) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setVar("isShow");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(169,16) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(169,16) 'true'",_jsp_getExpressionFactory().createValueExpression("true",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f6 = _jspx_th_c_005fset_005f6.doStartTag();
      if (_jspx_th_c_005fset_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f6);
      _jspx_th_c_005fset_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f7 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f7_reused = false;
    try {
      _jspx_th_c_005fif_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(170,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f7.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetConfig.displayCondition != null && not empty facetConfig.displayCondition.expr}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f7 = _jspx_th_c_005fif_005f7.doStartTag();
      if (_jspx_eval_c_005fif_005f7 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_string_005feval_005f1(_jspx_th_c_005fif_005f7, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f7.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f7);
      _jspx_th_c_005fif_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005feval_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:eval
    com.exalead.cv360.searchui.view.jspapi.string.EvalTag _jspx_th_string_005feval_005f1 = (com.exalead.cv360.searchui.view.jspapi.string.EvalTag) _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffeeds_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.string.EvalTag.class);
    boolean _jspx_th_string_005feval_005f1_reused = false;
    try {
      _jspx_th_string_005feval_005f1.setPageContext(_jspx_page_context);
      _jspx_th_string_005feval_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f7);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(171,20) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setVar("isShow");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(171,20) name = string type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setString((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetConfig.displayCondition.expr}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(171,20) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_string_005feval_005f1.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_string_005feval_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_string_005feval_005f1 = _jspx_th_string_005feval_005f1.doStartTag();
        if (_jspx_th_string_005feval_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005feval_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005feval_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005feval_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005feval_0026_005fvar_005fstring_005ffeeds_005fnobody.reuse(_jspx_th_string_005feval_005f1);
      _jspx_th_string_005feval_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005feval_005f1, _jsp_getInstanceManager(), _jspx_th_string_005feval_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fforEach_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f2 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f2_reused = false;
    try {
      _jspx_th_c_005fchoose_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fforEach_005f0);
      int _jspx_eval_c_005fchoose_005f2 = _jspx_th_c_005fchoose_005f2.doStartTag();
      if (_jspx_eval_c_005fchoose_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fwhen_005f2(_jspx_th_c_005fchoose_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fwhen_005f3(_jspx_th_c_005fchoose_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fwhen_005f4(_jspx_th_c_005fchoose_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f2);
      _jspx_th_c_005fchoose_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f2 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f2_reused = false;
    try {
      _jspx_th_c_005fwhen_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(175,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isShow && facetConfig.type == 'facet'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f2 = _jspx_th_c_005fwhen_005f2.doStartTag();
      if (_jspx_eval_c_005fwhen_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_search_005fforEachFacet_005f0(_jspx_th_c_005fwhen_005f2, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f2);
      _jspx_th_c_005fwhen_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachFacet_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachFacet
    com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag _jspx_th_search_005fforEachFacet_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag) _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvar_005fshowEmptyFacets_005ffilterMode_005ffeeds_005ffacetsList.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachFacetTag.class);
    boolean _jspx_th_search_005fforEachFacet_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachFacet_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachFacet_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(176,24) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setVar("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(176,24) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(176,24) name = filterMode type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setFilterMode("INCLUDE");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(176,24) name = facetsList type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setFacetsList((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetConfig.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(176,24) name = showEmptyFacets type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachFacet_005f0.setShowEmptyFacets((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.showEmptyFacets}", java.lang.Boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fforEachFacet_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachFacet_005f0 = _jspx_th_search_005fforEachFacet_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachFacet_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("                            ");
            if (_jspx_meth_c_005fremove_005f0(_jspx_th_search_005fforEachFacet_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFacet_005f0))
              return true;
            out.write("\r\n");
            out.write("                            ");
            if (_jspx_meth_render_005ftemplate_005f0(_jspx_th_search_005fforEachFacet_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachFacet_005f0))
              return true;
            out.write("\r\n");
            out.write("                        ");
            int evalDoAfterBody = _jspx_th_search_005fforEachFacet_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachFacet_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachFacet_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachFacet_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachFacet_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachFacet_0026_005fvar_005fshowEmptyFacets_005ffilterMode_005ffeeds_005ffacetsList.reuse(_jspx_th_search_005fforEachFacet_005f0);
      _jspx_th_search_005fforEachFacet_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachFacet_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachFacet_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fremove_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFacet_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFacet_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:remove
    org.apache.taglibs.standard.tag.common.core.RemoveTag _jspx_th_c_005fremove_005f0 = (org.apache.taglibs.standard.tag.common.core.RemoveTag) _005fjspx_005ftagPool_005fc_005fremove_0026_005fvar_005fnobody.get(org.apache.taglibs.standard.tag.common.core.RemoveTag.class);
    boolean _jspx_th_c_005fremove_005f0_reused = false;
    try {
      _jspx_th_c_005fremove_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fremove_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachFacet_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(182,28) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fremove_005f0.setVar("noResults");
      int _jspx_eval_c_005fremove_005f0 = _jspx_th_c_005fremove_005f0.doStartTag();
      if (_jspx_th_c_005fremove_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fremove_0026_005fvar_005fnobody.reuse(_jspx_th_c_005fremove_005f0);
      _jspx_th_c_005fremove_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fremove_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fremove_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachFacet_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachFacet_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f0);
    try {
      _jspx_th_render_005ftemplate_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f0.setParent(_jspx_th_search_005fforEachFacet_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(183,28) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/normalFilter.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(183,28) name = defaultTemplate type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setDefaultTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/normalFilter.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(183,28) name = widget type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setWidget("refinePanel");
      _jspx_th_render_005ftemplate_005f0.setJspBody(new Helper( 0, _jspx_page_context, _jspx_th_render_005ftemplate_005f0, _jspx_push_body_count_search_005fforEachFacet_005f0));
      _jspx_th_render_005ftemplate_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f0);
    try {
      _jspx_th_render_005fparameter_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f0.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(185,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setName("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(185,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f1);
    try {
      _jspx_th_render_005fparameter_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f1.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(186,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setName("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(186,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f2);
    try {
      _jspx_th_render_005fparameter_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f2.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(187,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setName("feeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(187,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f3 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f3);
    try {
      _jspx_th_render_005fparameter_005f3.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f3.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(188,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setName("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(188,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f3.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f3);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f4 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f4);
    try {
      _jspx_th_render_005fparameter_005f4.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f4.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(189,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setName("facetConfig");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(189,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetConfig}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f4.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f4);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f5 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f5);
    try {
      _jspx_th_render_005fparameter_005f5.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f5.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(190,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setName("isSuggestSearch");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(190,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetsSuggestContext.suggestContext}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f5.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f5);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f6 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f6);
    try {
      _jspx_th_render_005fparameter_005f6.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f6.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(191,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setName("facetsSuggestContext");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(191,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetsSuggestContext}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f6.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f6);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f7 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f7);
    try {
      _jspx_th_render_005fparameter_005f7.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f7.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(192,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setName("suggestApiAction");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(192,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${suggestCfg.suggestApiAction}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f7.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f7);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f8 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f8);
    try {
      _jspx_th_render_005fparameter_005f8.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f8.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(193,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f8.setName("refinePanelCfg");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(193,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f8.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f8.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f8);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f9 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f9);
    try {
      _jspx_th_render_005fparameter_005f9.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f9.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(194,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f9.setName("suggestCfg");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(194,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f9.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${suggestCfg}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f9.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f9);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f10 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f10);
    try {
      _jspx_th_render_005fparameter_005f10.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f10.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(195,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f10.setName("refinesPanelId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(195,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f10.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinesPanelId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f10.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f10);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f11 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f11);
    try {
      _jspx_th_render_005fparameter_005f11.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f11.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(196,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f11.setName("refinesPanelMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(196,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f11.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinesPanelMode}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f11.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f11);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f3 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f3_reused = false;
    try {
      _jspx_th_c_005fwhen_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(200,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isShow && facetConfig.type == 'date'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f3 = _jspx_th_c_005fwhen_005f3.doStartTag();
      if (_jspx_eval_c_005fwhen_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_c_005fif_005f8(_jspx_th_c_005fwhen_005f3, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f3);
      _jspx_th_c_005fwhen_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f8 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f8_reused = false;
    try {
      _jspx_th_c_005fif_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(201,24) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f8.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not facetsSuggestContext.suggestContext}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f8 = _jspx_th_c_005fif_005f8.doStartTag();
      if (_jspx_eval_c_005fif_005f8 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_c_005fremove_005f1(_jspx_th_c_005fif_005f8, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_c_005fset_005f7(_jspx_th_c_005fif_005f8, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                            <div class=\"filter-list-facet-elem filter-list-facet-elem-date filter-list-facet-elem-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("                                <div class=\"facet-value-container\">\r\n");
          out.write("                                    <div class=\"facet-container-list\">\r\n");
          out.write("                                        <table class=\"facet table table-layout table-facets table-hover ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cookieClassName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(" table-list-facet-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("                                            <tr class=\"currentPageNumber\" style=\"display: none;\">\r\n");
          out.write("                                                <td>1</td>\r\n");
          out.write("                                            </tr>\r\n");
          out.write("                                            ");
          if (_jspx_meth_render_005ftemplate_005f1(_jspx_th_c_005fif_005f8, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                                        </table>\r\n");
          out.write("                                    </div>\r\n");
          out.write("                                </div>\r\n");
          out.write("                            </div>\r\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f8.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f8);
      _jspx_th_c_005fif_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fremove_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:remove
    org.apache.taglibs.standard.tag.common.core.RemoveTag _jspx_th_c_005fremove_005f1 = (org.apache.taglibs.standard.tag.common.core.RemoveTag) _005fjspx_005ftagPool_005fc_005fremove_0026_005fvar_005fnobody.get(org.apache.taglibs.standard.tag.common.core.RemoveTag.class);
    boolean _jspx_th_c_005fremove_005f1_reused = false;
    try {
      _jspx_th_c_005fremove_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fremove_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(202,28) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fremove_005f1.setVar("noResults");
      int _jspx_eval_c_005fremove_005f1 = _jspx_th_c_005fremove_005f1.doStartTag();
      if (_jspx_th_c_005fremove_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fremove_0026_005fvar_005fnobody.reuse(_jspx_th_c_005fremove_005f1);
      _jspx_th_c_005fremove_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fremove_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fremove_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f7 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f7_reused = false;
    try {
      _jspx_th_c_005fset_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(203,28) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setVar("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(203,28) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(203,28) '${fn:trim(facetConfig.id)}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_2),"${fn:trim(facetConfig.id)}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f7 = _jspx_th_c_005fset_005f7.doStartTag();
      if (_jspx_th_c_005fset_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f7);
      _jspx_th_c_005fset_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f1);
    try {
      _jspx_th_render_005ftemplate_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f1.setParent(_jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(211,44) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f1.setTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/dateFilter.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(211,44) name = defaultTemplate type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f1.setDefaultTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/dateFilter.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(211,44) name = widget type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f1.setWidget("refinePanel");
      _jspx_th_render_005ftemplate_005f1.setJspBody(new Helper( 1, _jspx_page_context, _jspx_th_render_005ftemplate_005f1, _jspx_push_body_count_c_005fforEach_005f0));
      _jspx_th_render_005ftemplate_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f12 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f12);
    try {
      _jspx_th_render_005fparameter_005f12.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f12.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(213,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f12.setName("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(213,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f12.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f12.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f12);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f13 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f13);
    try {
      _jspx_th_render_005fparameter_005f13.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f13.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(214,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f13.setName("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(214,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f13.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f13.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f13);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f14 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f14);
    try {
      _jspx_th_render_005fparameter_005f14.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f14.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(215,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f14.setName("accessFeeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(215,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f14.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f14.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f14);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f15(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f15 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f15);
    try {
      _jspx_th_render_005fparameter_005f15.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f15.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(216,48) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f15.setName("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(216,48) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f15.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f15.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f15);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f4 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f4_reused = false;
    try {
      _jspx_th_c_005fwhen_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(224,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isShow && facetConfig.type == 'range'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f4 = _jspx_th_c_005fwhen_005f4.doStartTag();
      if (_jspx_eval_c_005fwhen_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_c_005fif_005f9(_jspx_th_c_005fwhen_005f4, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f4);
      _jspx_th_c_005fwhen_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f9 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f9_reused = false;
    try {
      _jspx_th_c_005fif_005f9.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(225,24) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f9.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not facetsSuggestContext.suggestContext}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f9 = _jspx_th_c_005fif_005f9.doStartTag();
      if (_jspx_eval_c_005fif_005f9 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_c_005fremove_005f2(_jspx_th_c_005fif_005f9, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_render_005ftemplate_005f2(_jspx_th_c_005fif_005f9, _jspx_page_context, _jspx_push_body_count_c_005fforEach_005f0))
            return true;
          out.write("\r\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f9.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f9);
      _jspx_th_c_005fif_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fremove_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f9, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:remove
    org.apache.taglibs.standard.tag.common.core.RemoveTag _jspx_th_c_005fremove_005f2 = (org.apache.taglibs.standard.tag.common.core.RemoveTag) _005fjspx_005ftagPool_005fc_005fremove_0026_005fvar_005fnobody.get(org.apache.taglibs.standard.tag.common.core.RemoveTag.class);
    boolean _jspx_th_c_005fremove_005f2_reused = false;
    try {
      _jspx_th_c_005fremove_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fremove_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f9);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(226,28) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fremove_005f2.setVar("noResults");
      int _jspx_eval_c_005fremove_005f2 = _jspx_th_c_005fremove_005f2.doStartTag();
      if (_jspx_th_c_005fremove_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fremove_0026_005fvar_005fnobody.reuse(_jspx_th_c_005fremove_005f2);
      _jspx_th_c_005fremove_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fremove_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fremove_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f9, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_c_005fforEach_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f2);
    try {
      _jspx_th_render_005ftemplate_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f2.setParent(_jspx_th_c_005fif_005f9);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(227,28) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f2.setTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/numericalFilter.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(227,28) name = defaultTemplate type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f2.setDefaultTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetTemplateDirectory}/numericalFilter.jsp", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(227,28) name = widget type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f2.setWidget("refinePanel");
      _jspx_th_render_005ftemplate_005f2.setJspBody(new Helper( 2, _jspx_page_context, _jspx_th_render_005ftemplate_005f2, _jspx_push_body_count_c_005fforEach_005f0));
      _jspx_th_render_005ftemplate_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f16(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f16 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f16);
    try {
      _jspx_th_render_005fparameter_005f16.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f16.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(229,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f16.setName("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(229,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f16.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f16.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f16);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f17(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f17 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f17);
    try {
      _jspx_th_render_005fparameter_005f17.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f17.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(230,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f17.setName("feeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(230,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f17.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${feeds}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f17.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f17);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f18(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f18 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f18);
    try {
      _jspx_th_render_005fparameter_005f18.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f18.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(231,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f18.setName("facet");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(231,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f18.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetConfig.id}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f18.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f18);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f19(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f19 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f19);
    try {
      _jspx_th_render_005fparameter_005f19.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f19.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(232,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f19.setName("min");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(232,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f19.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetConfig.min}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f19.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f19);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f20(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f20 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f20);
    try {
      _jspx_th_render_005fparameter_005f20.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f20.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(233,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f20.setName("max");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(233,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f20.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetConfig.max}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f20.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f20);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f21(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f21 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f21);
    try {
      _jspx_th_render_005fparameter_005f21.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f21.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(234,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f21.setName("step");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(234,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f21.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetConfig.step}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f21.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f21);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f22(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f22 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f22);
    try {
      _jspx_th_render_005fparameter_005f22.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f22.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(235,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f22.setName("uCssId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(235,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f22.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f22.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f22);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f23(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f23 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f23);
    try {
      _jspx_th_render_005fparameter_005f23.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f23.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(236,32) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f23.setName("refinePanelCfg");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(236,32) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f23.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f23.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f23);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fsubWidgets_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_widget_005fwidget_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_widget_005fwidget_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:subWidgets
    com.exalead.cv360.searchui.view.jspapi.render.SubWidgetsTag _jspx_th_render_005fsubWidgets_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.SubWidgetsTag) _005fjspx_005ftagPool_005frender_005fsubWidgets_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.render.SubWidgetsTag.class);
    boolean _jspx_th_render_005fsubWidgets_005f0_reused = false;
    try {
      _jspx_th_render_005fsubWidgets_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005fsubWidgets_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_widget_005fwidget_005f0);
      int[] _jspx_push_body_count_render_005fsubWidgets_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005fsubWidgets_005f0 = _jspx_th_render_005fsubWidgets_005f0.doStartTag();
        if (_jspx_th_render_005fsubWidgets_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005fsubWidgets_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005fsubWidgets_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005fsubWidgets_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005fsubWidgets_005fnobody.reuse(_jspx_th_render_005fsubWidgets_005f0);
      _jspx_th_render_005fsubWidgets_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005fsubWidgets_005f0, _jsp_getInstanceManager(), _jspx_th_render_005fsubWidgets_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderScript_005f2(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderScript
    com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag _jspx_th_render_005frenderScript_005f2 = (com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag) _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.get(com.exalead.cv360.searchui.view.jspapi.render.RenderScriptTag.class);
    boolean _jspx_th_render_005frenderScript_005f2_reused = false;
    try {
      _jspx_th_render_005frenderScript_005f2.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderScript_005f2.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(251,0) name = position type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderScript_005f2.setPosition("READY");
      int[] _jspx_push_body_count_render_005frenderScript_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderScript_005f2 = _jspx_th_render_005frenderScript_005f2.doStartTag();
        if (_jspx_eval_render_005frenderScript_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderScript_005f2 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderScript_005f2[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderScript_005f2);
          }
          do {
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_render_005frenderOnce_005f0(_jspx_th_render_005frenderScript_005f2, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f2))
              return true;
            out.write("\r\n");
            out.write("    refinesWidget.initToggle($('#");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("'));\r\n");
            out.write("    refinesWidget.initMenu($('#");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("'), '");
            out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
            out.write("');\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fif_005f10(_jspx_th_render_005frenderScript_005f2, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f2))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fif_005f11(_jspx_th_render_005frenderScript_005f2, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f2))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fif_005f12(_jspx_th_render_005frenderScript_005f2, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f2))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_render_005frenderScript_005f2.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderScript_005f2 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderScript_005f2[0]--;
          }
        }
        if (_jspx_th_render_005frenderScript_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderScript_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderScript_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderScript_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderScript_0026_005fposition.reuse(_jspx_th_render_005frenderScript_005f2);
      _jspx_th_render_005frenderScript_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderScript_005f2, _jsp_getInstanceManager(), _jspx_th_render_005frenderScript_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005frenderOnce_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:renderOnce
    com.exalead.cv360.searchui.view.jspapi.render.RenderOnceTag _jspx_th_render_005frenderOnce_005f0 = (com.exalead.cv360.searchui.view.jspapi.render.RenderOnceTag) _005fjspx_005ftagPool_005frender_005frenderOnce_0026_005fid.get(com.exalead.cv360.searchui.view.jspapi.render.RenderOnceTag.class);
    boolean _jspx_th_render_005frenderOnce_005f0_reused = false;
    try {
      _jspx_th_render_005frenderOnce_005f0.setPageContext(_jspx_page_context);
      _jspx_th_render_005frenderOnce_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(252,4) name = id type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005frenderOnce_005f0.setId("initRefinesWidget");
      int[] _jspx_push_body_count_render_005frenderOnce_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_render_005frenderOnce_005f0 = _jspx_th_render_005frenderOnce_005f0.doStartTag();
        if (_jspx_eval_render_005frenderOnce_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_render_005frenderOnce_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_render_005frenderOnce_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_render_005frenderOnce_005f0);
          }
          do {
            out.write("\r\n");
            out.write("        refinesWidget.initI18N({\r\n");
            out.write("        singular: \"");
            if (_jspx_meth_i18n_005fmessage_005f6(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\",\r\n");
            out.write("        plural: \"");
            if (_jspx_meth_i18n_005fmessage_005f7(_jspx_th_render_005frenderOnce_005f0, _jspx_page_context, _jspx_push_body_count_render_005frenderOnce_005f0))
              return true;
            out.write("\"\r\n");
            out.write("        });\r\n");
            out.write("    ");
            int evalDoAfterBody = _jspx_th_render_005frenderOnce_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_render_005frenderOnce_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_render_005frenderOnce_005f0[0]--;
          }
        }
        if (_jspx_th_render_005frenderOnce_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_render_005frenderOnce_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_render_005frenderOnce_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_render_005frenderOnce_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frender_005frenderOnce_0026_005fid.reuse(_jspx_th_render_005frenderOnce_005f0);
      _jspx_th_render_005frenderOnce_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_render_005frenderOnce_005f0, _jsp_getInstanceManager(), _jspx_th_render_005frenderOnce_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f6 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f6_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f6.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(254,19) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f6.setCode("refines.singular");
      int[] _jspx_push_body_count_i18n_005fmessage_005f6 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f6 = _jspx_th_i18n_005fmessage_005f6.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f6[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f6.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f6.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f6);
      _jspx_th_i18n_005fmessage_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f6, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderOnce_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderOnce_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f7 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f7_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f7.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderOnce_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(255,17) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f7.setCode("refines.plural");
      int[] _jspx_push_body_count_i18n_005fmessage_005f7 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f7 = _jspx_th_i18n_005fmessage_005f7.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f7[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f7.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f7.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f7);
      _jspx_th_i18n_005fmessage_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f7, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f10 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f10_reused = false;
    try {
      _jspx_th_c_005fif_005f10.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f10.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(260,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f10.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.displayCloseBtn}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f10 = _jspx_th_c_005fif_005f10.doStartTag();
      if (_jspx_eval_c_005fif_005f10 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        refinesWidget.initCloseButtonBehavior($('#");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("'));\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f10.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f10);
      _jspx_th_c_005fif_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f10, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f11(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f11 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f11_reused = false;
    try {
      _jspx_th_c_005fif_005f11.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f11.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(263,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f11.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.disjunctive}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f11 = _jspx_th_c_005fif_005f11.doStartTag();
      if (_jspx_eval_c_005fif_005f11 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        var options = {};\r\n");
          out.write("        options.paramNameReload = '");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.paramNameReload}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("';\r\n");
          out.write("        options.loadDateFacetParameter = '';\r\n");
          out.write("        options.loadFacetParameter = '");
          if (_jspx_meth_plma_005fgetConstantValueFromNameTag_005f2(_jspx_th_c_005fif_005f11, _jspx_page_context, _jspx_push_body_count_render_005frenderScript_005f2))
            return true;
          out.write("';\r\n");
          out.write("        refinesWidget.initCheckboxes($('#");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${cssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("'),options);\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f11.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f11);
      _jspx_th_c_005fif_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f11, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetConstantValueFromNameTag_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f11, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getConstantValueFromNameTag
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag _jspx_th_plma_005fgetConstantValueFromNameTag_005f2 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag) _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetConstantValueFromNameTag.class);
    boolean _jspx_th_plma_005fgetConstantValueFromNameTag_005f2_reused = false;
    try {
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f2.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f11);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(267,38) name = constantName type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f2.setConstantName("CLEAN_FEED_PARAMETER");
      int[] _jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetConstantValueFromNameTag_005f2 = _jspx_th_plma_005fgetConstantValueFromNameTag_005f2.doStartTag();
        if (_jspx_th_plma_005fgetConstantValueFromNameTag_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetConstantValueFromNameTag_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetConstantValueFromNameTag_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetConstantValueFromNameTag_0026_005fconstantName_005fnobody.reuse(_jspx_th_plma_005fgetConstantValueFromNameTag_005f2);
      _jspx_th_plma_005fgetConstantValueFromNameTag_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetConstantValueFromNameTag_005f2, _jsp_getInstanceManager(), _jspx_th_plma_005fgetConstantValueFromNameTag_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_th_render_005frenderScript_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_render_005frenderScript_005f2)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f12 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f12_reused = false;
    try {
      _jspx_th_c_005fif_005f12.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f12.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_render_005frenderScript_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/widget.jsp(270,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f12.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.enableDragging}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f12 = _jspx_th_c_005fif_005f12.doStartTag();
      if (_jspx_eval_c_005fif_005f12 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        refinesWidget.enableDragging();\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f12.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f12.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f12);
      _jspx_th_c_005fif_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f12, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f12_reused);
    }
    return false;
  }

  private class Helper
      extends org.apache.jasper.runtime.JspFragmentHelper
  {
    private jakarta.servlet.jsp.tagext.JspTag _jspx_parent;
    private int[] _jspx_push_body_count;

    public Helper( int discriminator, jakarta.servlet.jsp.JspContext jspContext, jakarta.servlet.jsp.tagext.JspTag _jspx_parent, int[] _jspx_push_body_count ) {
      super( discriminator, jspContext, _jspx_parent );
      this._jspx_parent = _jspx_parent;
      this._jspx_push_body_count = _jspx_push_body_count;
    }
    public boolean invoke0( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f0(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f1(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f2(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f3(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f4(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f5(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f6(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f7(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f8(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f9(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f10(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f11(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                            ");
      return false;
    }
    public boolean invoke1( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f12(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f13(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f14(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                                ");
      if (_jspx_meth_render_005fparameter_005f15(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                            ");
      return false;
    }
    public boolean invoke2( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f16(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f17(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f18(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f19(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f20(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f21(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f22(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                                ");
      if (_jspx_meth_render_005fparameter_005f23(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                            ");
      return false;
    }
    public void invoke( java.io.Writer writer )
      throws jakarta.servlet.jsp.JspException
    {
      jakarta.servlet.jsp.JspWriter out = null;
      if( writer != null ) {
        out = this.jspContext.pushBody(writer);
      } else {
        out = this.jspContext.getOut();
      }
      try {
        Object _jspx_saved_JspContext = this.jspContext.getELContext().getContext(jakarta.servlet.jsp.JspContext.class);
        this.jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,this.jspContext);
        switch( this.discriminator ) {
          case 0:
            invoke0( out );
            break;
          case 1:
            invoke1( out );
            break;
          case 2:
            invoke2( out );
            break;
        }
        jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,_jspx_saved_JspContext);
      }
      catch( java.lang.Throwable e ) {
        if (e instanceof jakarta.servlet.jsp.SkipPageException)
            throw (jakarta.servlet.jsp.SkipPageException) e;
        throw new jakarta.servlet.jsp.JspException( e );
      }
      finally {
        if( writer != null ) {
          this.jspContext.popBody();
        }
      }
    }
  }
}
