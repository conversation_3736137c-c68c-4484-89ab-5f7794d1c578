<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA See Details" group="PLM Analytics/plma" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <Description>A generic button to redirect to the details page with the specific BO type.</Description>

    <Platforms>
        <Platform type="web" supported="true" />
        <Platform type="mobile" supported="true" />
    </Platforms>

    <Includes>
        <Include type="css" path="css/style.less" />
    </Includes>


    <SupportWidgetsId arity="ZERO" />
    <SupportFeedsId arity="ZERO_OR_MANY" />

    <OptionsGroup name="General">
        <Option id="iconCss" name="Icon CSS" >
            <Description>Specifies the CSS class name of the icon added to the button. If empty, no icon is displayed.</Description>
        </Option>
        <Option id="label" name="Label" isEvaluated="true" arity="ONE">
            <Description>Specifies the label of the button. <PERSON><PERSON>ly explains the purpose of the button (3 words max).</Description>
        </Option>
        <Option id="showLabel" name="Show label" arity="ONE">
            <Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>
        <Option id="pageName" name="Target page name">
            <Description>The name of the page to get redirected to. (Defaults to search)</Description>
        </Option>
        <Option id="blacklistParams" name="Remove URL Parameters" arity="ZERO_OR_MANY">
            <Description>Removed parameters from URL.</Description>
        </Option>
    </OptionsGroup>


    <DefaultValues>
        <DefaultValue name="iconCss">fonticon fonticon-list</DefaultValue>
        <DefaultValue name="label">${i18n['plma.link.showDocuments']}</DefaultValue>
        <DefaultValue name="showLabel">false</DefaultValue>
        <DefaultValue name="pageName">search</DefaultValue>
    </DefaultValues>

</Widget>
