.mashup .searchWidget{
	&.plmaTable.plmaTableMatrix {
		.headerChartContainer{
			justify-content: space-between;
			position: relative;
			top: 1px;
		}
		.widgetContent{
			overflow-x: auto;
			border: 1px solid #e2e4e3;
			position: inherit;
			.data-table{
				position: inherit;
				table-layout: fixed;
				.linkTitle{
					cursor: pointer;
					&:hover{
						color: @clink;
					}
				}
			}

			.data_table_matrix th,
			.data_table_matrix td {
				border: none !important;
				padding: 1px !important;
				border-collapse: unset !important;
			}
			
			.data_table_matrix div.matrix_cell {
			  border: 1px solid #e2e4e3;
			  padding: 2px 3px;
			  text-align: center;
			  z-index: auto;
			  height: 50px;
			  display: flex;
			  align-items: center;
			  justify-content: center;
			  color: black;
			  font-weight: bold;
			  font-size: 1.5em;
			}
			&.doc-container {
				position: absolute;
				top: 40px;
			}
		  }
		.table-pagination{
			top: -2px;
			position: relative;
			margin: 0;
			-webkit-border-bottom-left-radius: 3px;
			-webkit-border-bottom-right-radius: 3px;
			-moz-border-radius-bottomleft: 3px;
			-moz-border-radius-bottomright: 3px;
			border-bottom-left-radius: 3px;
			border-bottom-right-radius: 3px;
			border: 1px #e2e4e3 solid;
			background: #ffffff;
			color: #77797c;
			text-align: left;
			font-family: Arial;
			font-size: 13px;
			font-style: normal;
			font-weight: bold;
			line-height: 42px;
			padding: 1px 27px 0 27px;
			border-top: 1px solid #e2e4e3;
			.fonticon{
				cursor: pointer;
				margin: 0;
				font-size: 18px;
				vertical-align: bottom;
				&:hover{
					color: @clink;
				}
			}
		}
		&.plmatable1d{
			.widgetHeader{
				padding-right: 0;
				border-bottom: none;
			}
			.data-table{
				tbody{
					tr{
						th{
							background: inherit;
							&.description{
								font-weight: bold;
								font-size: 11px;
								a{
									color: @ctext;
									&:hover{
										color: @clink;
									}
								}
							}
							&.count{
								font-size: 11px;
							}
						}
						td{
							background: inherit;
							&.description{
								font-weight: bold;
								font-size: 11px;
								a{
									color: @ctext;
									&:hover{
										color: @clink;
									}
								}
							}
							&.count{
								font-size: 11px;
							}
						}
					}
				}
			}
		}
	}
	
	.transpose-transposed {
		display: none;
	}
	
	&.transposed {
		.transpose-normal {
			display: none;
		}
		.transpose-transposed {
			display: block;
		}
		
			
	}
}