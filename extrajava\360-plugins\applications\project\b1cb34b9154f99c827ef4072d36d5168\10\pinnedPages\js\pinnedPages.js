var PinnedPages = function(uCssId, options) {
	this.$widget = $('.' + uCssId);
	this.$emptyPages = this.$widget.find('.empty-pages');
	this.userStorage = new StorageClient('user');
	this.sharedStorage = new StorageClient('shared');
	this.queryStringFields = options.queryStringFields;
	this.elemPerPage = options.elemPerPage;
	this.url = PinnedPages.utils.getCleanUrl(
		window.location.pathname + window.location.search,
		this.queryStringFields
	);
	this.displayLabel = options.displayLabel;
};

PinnedPages.STORAGE_KEY_PAGES = 'pinned_pages.pages';
PinnedPages.STORAGE_KEY_FIELDS = 'pinned_pages.fields'; // TODO: do not use anymore
PinnedPages.WIDGET_NAME = 'pinnedPages';
PinnedPages.MESSAGE_CODE_PIN = 'pinnedPages.pin';
PinnedPages.MESSAGE_CODE_UNPIN = 'pinnedPages.unpin';
PinnedPages.ICON_PINNED = 'fonticon-favorite-on';
PinnedPages.ICON_NOT_PINNED = 'fonticon-favorite-off';
PinnedPages.EVENT = 'plma:pinnedPages';

/**
 * Applies the right icon and title.
 */
PinnedPages.prototype.init = function() {
	var that = this;
	this.userStorage.get(PinnedPages.STORAGE_KEY_PAGES, function(res) {
		var pages = res.length > 0 ? JSON.parse(res[0].value) : [];
		var contains = PinnedPages.utils.containsPage(pages, {url: that.url}); 
		if (contains) {
			PinnedPages.utils.setIconAndTitle(
				that.$widget,
				PinnedPages.ICON_PINNED,
				mashupI18N.get(PinnedPages.WIDGET_NAME, PinnedPages.MESSAGE_CODE_UNPIN),
				that.displayLabel
			);
		} else {
			PinnedPages.utils.setIconAndTitle(
				that.$widget,
				PinnedPages.ICON_NOT_PINNED,
				mashupI18N.get(PinnedPages.WIDGET_NAME, PinnedPages.MESSAGE_CODE_PIN),
				that.displayLabel
			);
		}
		$(window).on(ChartboardStorageManager.PAGE_DELETED_EVENT, function(e, data) {
			that.userStorage.set(PinnedPages.STORAGE_KEY_PAGES, PinnedPages.utils.deletePageById(pages, data.pageId));
		});
	});
	this.$widget.click(function() {
		that.onClick();
	});
	this.userStorage.set(PinnedPages.STORAGE_KEY_FIELDS, this.queryStringFields);
};

/**
 * Toggles the page pinning.
 */
PinnedPages.prototype.onClick = function() {
	var that = this;
	this.userStorage.get(PinnedPages.STORAGE_KEY_PAGES, function(res) {
		var pages = res.length > 0 ? JSON.parse(res[0].value) : [];
		var contains = PinnedPages.utils.containsPage(pages, {url: that.url}); 
		var page = {
			url: that.url,
			queryStringFields: that.queryStringFields
		};
		if (contains) {
			PinnedPages.utils.deletePage(pages, page);
		} else {
			PinnedPages.utils.addPage(pages, page);
		}
		that.userStorage.set(PinnedPages.STORAGE_KEY_PAGES, pages, function() {
			if (contains) {
				PinnedPages.utils.setIconAndTitle(
					that.$widget,
					PinnedPages.ICON_NOT_PINNED,
					mashupI18N.get(PinnedPages.WIDGET_NAME, PinnedPages.MESSAGE_CODE_PIN),
					that.displayLabel
				);
			} else {
				PinnedPages.utils.setIconAndTitle(
					that.$widget,
					PinnedPages.ICON_PINNED,
					mashupI18N.get(PinnedPages.WIDGET_NAME, PinnedPages.MESSAGE_CODE_UNPIN),
					that.displayLabel
				);
			}
			PinnedPages.utils.triggerEvent(pages, that.queryStringFields);
		});
	});
};

/**
 * Finds and displays all the pinned pages.
 */
PinnedPages.prototype.getPinnedPages = function(menuConfig) {
	var that = this;
	this.sharedStorage.get(ChartboardStorageManager.PAGES_STORAGE_KEY, function(resp) {
		var chartboardConfig = resp.length > 0 ? JSON.parse(resp[0].value) : {};
		/* finds translation keys */
		var i18nKeys = {};
		$.each(chartboardConfig, function(pageId, page) {
			var description = page.description;
			if (description && description.includes('i18n')) {
				i18nKeys[description.match(/\['(.*?)'\]/)[1]] = '';
			}
		});
		/* translates description */
		if (!$.isEmptyObject(i18nKeys)) {
			var client = new I18nClient();
			client.getI18nList(i18nKeys, function(translations) {
				that.displayPinnedPages(chartboardConfig, menuConfig, translations);
			});
		} else {
			that.displayPinnedPages(chartboardConfig, menuConfig);
		}
	});
};

/**
 * Displays a list of pinned pages.
 */
PinnedPages.prototype.displayPinnedPages = function(chartboardConfig, menuConfig, translations) {
	var that = this;
	this.userStorage.get(PinnedPages.STORAGE_KEY_PAGES, $.proxy(function(res) {
		var pages = res.length > 0 ? JSON.parse(res[0].value) : [];
		if (pages.length === 0) {
			that.$emptyPages.removeClass('hidden');
			that.$widget.find('.pages').addClass('hidden');
		} else {
			that.$emptyPages.addClass('hidden');
			that.$widget.find('.pages').removeClass('hidden');
		}
		for (var i = 0; i < pages.length; i++) {
			var pageId = PinnedPages.utils.getPageId(pages[i].url);
			var pageTarget = PinnedPages.utils.getPageTarget(pages[i].url);
			var page = chartboardConfig[pageId];
			var config = PinnedPages.utils.findMenuConfigByTarget(menuConfig, pageTarget);
			var label = page ? page.label : config ? config.label : pageId;
			var icon = 'page-icon ';
			icon += page ? page.icon : config ? config.iconCss : '';
			var description = page ? page.description : config ? config.description : '';
			if (description.includes('i18n')) {
				description = translations[description.match(/\['(.*?)'\]/)[1]];
			}
			that.$widget.find('.pages')
				.append($('<a>', {href: pages[i].url, title: description, 'class': this.elemPerPage > 0 && i < this.elemPerPage ? '' : 'hidden'})
					.append(
						$('<div>', {'class': 'page'}).append($('<div>', {'class': icon})).append($('<div>', {'class': 'info-container'})
							.append($('<div>', {text: label, 'class': 'label'})).append($('<div>', {text: description, 'class': 'description'}))
						).append($('<span>', {'class': 'favorite-icon fonticon fonticon-favorite-on'}))
					)
				);
		}
		if (this.elemPerPage > 0 && pages.length > this.elemPerPage) {
			var nbPage = Math.ceil(pages.length / this.elemPerPage);
			var pagination = that.$widget.find('.pagination');
			pagination.append($('<span data-page-nb="1" class="page-button selected">'));
			for (var i = 1; i < nbPage; i++){
				pagination.append($('<span data-page-nb="' + (i+1) +'" class="page-button">'));
			}
			pagination.find('.page-button').on('click', $.proxy(function (e) {
				pagination.find('.page-button.selected').removeClass('selected');
				var $e = $(e.target);
				$e.addClass('selected');
				var pageNumber = $e.data('pageNb');
				that.$widget.find('.pages a').each($.proxy(function (index,elem) {
					if (index + 1 <= pageNumber * this.elemPerPage && index + 1 > (pageNumber - 1) * this.elemPerPage) {
						$(elem).removeClass('hidden');
					} else {
						$(elem).addClass('hidden');
					}
				}, this));
			}, this));
		} else {
			that.$widget.find('.pages a').removeClass('hidden');
		}
	}, this));
};

/**
 * Binds event handlers.
 */
PinnedPages.prototype.bind = function() {
	var that = this;
	/* unpin all pages */
	this.$widget.find('.delete-button').click(function() {
		var emptyArray = [];
		that.userStorage.set(PinnedPages.STORAGE_KEY_PAGES, emptyArray, function() {
			that.$widget.find('.pages').empty().addClass('hidden');
			that.$widget.find('.pagination').empty();
			that.$emptyPages.removeClass('hidden');
			PinnedPages.utils.triggerEvent(emptyArray, that.queryStringFields);
		});
	});
};

PinnedPages.utils = {
	
	/**
	 * Adds a page.
	 */
	addPage: function(pages, page) {
		pages.push(page);
	},
	
	/**
	 * Deletes a page (by url).
	 */
	deletePage: function(pages, page) {
		var idx = -1;
		for (var i = 0; i < pages.length; i++) {
			if (pages[i].url === page.url) {
				idx = i;
			}
		}
		if (idx !== -1) {
			pages.splice(idx, 1);
		}
	},

	/**
	 * Deletes a page (by pageId)
	 */
	deletePageById: function(pages, pageId) {
		return pages.filter(function(page) {
			return PinnedPages.utils.getPageId(page.url) !== pageId;
		});
	},
	
	/**
	 * Returns whether a page is included (by url).
	 */
	containsPage: function(pages, page) {
		for (var i = 0; i < pages.length; i++) {
			if (pages[i].url === page.url) {
				return true;
			}
		}
		return false;
	},
	
	/**
	 * Fires an event: pinned pages will be displayed.
	 */
	triggerEvent: function(pages, fields) {
		$(document).trigger(PinnedPages.EVENT, {
			pages: pages,
			queryStringFields: fields // TODO: do not use anymore
		});
	},
	
	/**
	 * Changes the icon and the title of the button.
	 */
	setIconAndTitle: function($button, icon, title, displayLabel) {
		$button.find('i')
			.removeClass(PinnedPages.ICON_PINNED + ' ' + PinnedPages.ICON_NOT_PINNED)
			.addClass(icon)
		;
		if (displayLabel) {
			$button.find('.button-label').text(title);
		} else {
			$button.attr('title', title);
		}
	},
	
	/**
	 * Removes all the fields in the query string except the ones in 'fields'.
	 */
	getCleanUrl: function(url, fields) {
		var urlBuilder = new BuildUrl(url);
		var params = urlBuilder.getParameters();
		for (var param in params) {
			if (!fields.includes(param)) {
				urlBuilder.removeParameter(param);
			}
		}

		/* 
			only for 'pageId' param
			/page/foo?pageId=foo => pageId is useless here
			this happens when the user edits a standard page
			see #9849
		*/
		var pageId = urlBuilder.getParameter('pageId');
		var pageName = PinnedPages.utils.getPageName(url);
		if (pageId && pageName && pageId[0] === pageName) {
			urlBuilder.removeParameter('pageId');
		}

		return urlBuilder.toString();
	},

	/**
	 * Returns the page id from an URL
	 */
	getPageId: function(url) {
		// simple case: /page/foo?pageId=xxx....
		var urlBuilder = new BuildUrl(url);
		var pageId = urlBuilder.getParameter('pageId');
		if (pageId) {
			return pageId[0] || PinnedPages.utils.getPageName(url);
		}
		
		// otherwise: page name
		return PinnedPages.utils.getPageName(url);
	},

	/**
	 * Returns the page target, as defined in the menu configuration
	 *
	 * @example
	 * getPageTarget('/page/foo?pageId=xxx') // foo?pageId=xxx
	 */
	getPageTarget: function (url) {
		return url.substring(url.lastIndexOf('/') + 1) || PinnedPages.utils.getPageId(url);
	},

	/**
	 * Returns the page name from an URL
	 * 
	 * example: /page/xxx?.... returns 'xxx'
	 */
	getPageName: function(url) {
		var begin = url.lastIndexOf('/') + 1;
		var end = url.lastIndexOf('?');
	
		if (end !== -1) {
			return url.substring(begin, end);
		} else {
			return url.substring(begin)
		}
	},

	/**
	 * Returns the menu config for a specific page target.
	 */
	findMenuConfigByTarget: function(menuConfig, target) {
		for (var config in menuConfig) {
			if (menuConfig[config].target && menuConfig[config].target === target.split('?')[0]) {
				return menuConfig[config];
			}
		}
		return undefined;
	}
};


