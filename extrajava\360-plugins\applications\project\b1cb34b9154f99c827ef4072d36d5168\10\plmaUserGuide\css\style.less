@import "../../plmaResources/css/styles/variables.less";

.mashup .plmaUserGuide {
    height: 100%;
    width: 100%;
    
    .widgetContent {
        padding: 5px 15px 0px;
        //max-height: 80vh;
        //overflow: auto;
        .tabs {
            margin-top:8px;
            &.ui-tabs-nav {
                border: none;
                background: @cblock-bg;
                .ui-state-default {
                    margin-right: 10px;
                    background: @cblock-bg;
                    border: none;
                    border-radius: 4px;
                    &.ui-state-hover {
                        background: @cblock-bg-lite;
                        color: @cblock;
                    }
                    &.ui-state-active {
                        background: @clink;
                        color: @cblock-bg;
                        .ui-tabs-anchor {
                            color: @cblock-bg;
                        }
                    }
                    .ui-tabs-anchor {
                        padding: 8px 14px;
                        color: @ctext;
                    }
                }
            }
        }
        .contents {
            height: 70vh;
            overflow:auto;
            border: 1px solid #d1d4d4;
            margin-bottom: 5px;
            margin-top: 5px;
            a {
                color: @clink;
            }
            h1 {
                font-size: large;
                line-height: normal;
                margin-top: 10px;
            }
            li {
                list-style: inherit;
                margin-left: 20px;
            }
            .box {
                background-color: @cblock-bg-lite;
                border: 1px solid @cblock-border;
                padding: 4px 10px;
            }
        }
    }
}

.mashup.ie .wuid.plmaUserGuide {
    display: table;
    table-layout: fixed;
}