@import "../../plmaResources/css/styles/variables.less";

.dropdownMenu {
	.appspopup {
		border-radius: 0;
		background-color: white;
		top: 5px !important;
		z-index: 100;
		border: none;
		-moz-box-shadow: 0 1px 6px 0px @ctext-weak;
		-webkit-box-shadow: 0 1px 6px 0px @ctext-weak;
		box-shadow: 0 1px 6px 0px @ctext-weak;
		min-width: 170px;
		overflow: hidden;
	}
	&.appspopup-button {
		cursor: pointer;
	}
	.appspopup-arrow {
		display: none;
	}
	.button-container {
		width: 100%;
		height: 42px;
		font-size: 15px;
		font-family: Arial;
		color: @ctext;
		font-weight: normal;
		border-bottom: 1px solid @cblock-border;
		white-space: nowrap;
		display: flex;
		.button, .plmaButton, a {
			padding-left: 7px;
			padding-right: 7px;
			line-height: 42px;
			color: @ctext-weak;
			.fonticon {
				font-size: 1.35em;
			}
		}
		.label, .button-label {
			color: @ctext;
		}
		&:hover {
			background-color: @cblock-bg-alt;
			.button, .plmaButton, a, .label, .button-label {
				color: @clink-active;
			}
		}
		&.separator {
			border-bottom: medium solid @cblock-border;
		}
		&.disabled {
			.label, .button-label {
				color: @ctext-weak;
			}
			&:hover {
				.button, .plmaButton, a, .label, .button-label {
					color: @ctext-weak;
				}
			}
			.button, .plmaButton, a, .label, .button-label {
				cursor: not-allowed;
			}
		}
	}
}