<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>

<render:import varWidget="widget"/>
<widget:getUcssId var="uCssId"/>
<plma:getUsedFeeds var="usedFeeds"/>
<render:import parameters="collection,buttonPrefix,actionName,collectionMode,displayDeleteButton,displayView" ignore="true"/>

<plma:getCollection varItems="collectionItems" varCount="collectionCount" collection="${collection}"/>

<i18n:message code="plma.resultlist.header.${actionName}.show" var="i18nShow" htmlEscape="true"/>
<i18n:message code="plma.resultlist.header.${actionName}.delete" var="i18nDelete" htmlEscape="true"/>

<div class="collection-header ">
    <span class="action-details label-section">
        <span title="${i18nShow}" data-min-size="${plma:getIntegerParam(widget, 'minCompareSize', 2)}" class="collection-icon fonticon ${collection}-header-status ${buttonPrefix}-${collectionCount > 0 ? 'on' : 'off'} ${collectionCount < plma:getIntegerParam(widget, 'minCompareSize', 2) ? 'hidden' : ''} ">
            <span class="counter count-aware ${collection}-count-aware ${collection}-counter" data-count="${collectionCount}" title="${i18nShow}">${collectionCount}</span>
        </span>
        <span class="title">Open items</span>
    </span>
    <c:if test="${displayDeleteButton}">
        <span class="action-clear count-aware ${collection}-count-aware fonticon ${buttonPrefix}-delete" data-count="${collectionCount}"
              title="${i18nDelete}"></span>
    </c:if>
</div>

<%-- Always execute this code (event handlers for new tiles, also tiles generated by infinite scroll) --%>
<render:renderScript position="READY">
    var collectionOptions = {};
    collectionOptions.feeds = ${plma:toJSArray(usedFeeds)};
    collectionOptions.actionName = '${actionName}';
    <c:if test="${not empty displayView}">
        collectionOptions.displayView = '${displayView}';
    </c:if>
    collectionOptions.url = '<c:url value="/collections"/>';
    collectionOptions.collection = '${collection}';
    collectionOptions.mode = '${collectionMode}';
    collectionOptions.buttonPrefix = '${buttonPrefix}';
    new CollectionEventHandlers('${uCssId}', collectionOptions);
</render:renderScript>

