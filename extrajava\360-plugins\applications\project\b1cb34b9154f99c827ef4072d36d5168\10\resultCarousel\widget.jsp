<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>

<render:import varWidget="widget" varFeeds="feeds"/>

<config:getOption name="widgetTitle" var="widgetTitle" defaultValue="" doEval="true"/>
<config:getOption name="displayMode" var="displayMode"/>

<request:isAjax var="isAjax"/>
<request:getParameterValue var="isInfiniteScrollRequest" name="isInfiniteScrollRequest" defaultValue="false"/>

<c:choose>
	<%-- Infinite scroll request : only render hits --%>
	<c:when test="${isAjax && isInfiniteScrollRequest == 'true'}">
	   <search:forEachFeed feeds="${feeds}" var="feed">
		   <search:forEachEntry var="entry" feed="${feed}">
			   <config:getOption var="hitTemplate" name="hitTemplate" doEval="true" entry="${entry}"/>
			   <render:template template="${hitTemplate}" defaultTemplate="templates/tile.jsp">
					 <render:parameter name="entry" value="${entry}"/>
					 <render:parameter name="feed" value="${feed}"/>
					 <render:parameter name="widget" value="${widget}"/>
			   </render:template>
		   </search:forEachEntry>
		</search:forEachFeed>
	</c:when>

	<%-- Not Infinite scroll : render full widget --%>
	<c:otherwise>
		
		<widget:widget extraCss="resultCarousel" varUcssId="uCssId">
			<c:if test="${not empty widgetTitle}">
				<widget:header>${widgetTitle}</widget:header>
			</c:if>
			
			
			<c:choose>
				<%-- If widget has no Feed --%>
				<c:when test="${search:hasFeeds(feeds) == false}">
					<widget:content>
						<render:definition name="noFeeds">
							<render:parameter name="widget" value="${widget}" />
							<render:parameter name="showSuggestion" value="true" />
						</render:definition>
					</widget:content>
				</c:when>
		
				<%-- If all feeds have no results --%>
				<c:when test="${search:hasEntries(feeds) == false}">
					<widget:content>
						<config:getOption var="customHTMLNoResultMessage" name="customHTMLNoResultMessage" />
						<c:choose>
							<c:when test="${not empty customHTMLNoResultMessage}">
								<div class="noresult">
									${customHTMLNoResultMessage}
								</div>
							</c:when>
							<c:otherwise>
								<config:getOption var="noResultsJspPathHit" name="noResultsJspPathHit" defaultValue="templates/noresult.jsp" />
								<render:template template="${noResultsJspPathHit}">
									<render:parameter name="accessFeeds" value="${feeds}" />
									<render:parameter name="showSuggestion" value="true" />
								</render:template>
							</c:otherwise>
						</c:choose>
					</widget:content>
					<%-- /If all feeds have no results --%>
				</c:when>
				
				<c:otherwise>
					<widget:content extraCss="display-${fn:toLowerCase(displayMode)}" htmlTag="ul">
						<search:forEachFeed feeds="${feeds}" var="feed" varStatus="feedStatus">
							<search:forEachEntry var="entry" feed="${feed}" varStatus="entryStatus">
								<config:getOption var="hitTemplate" name="hitTemplate" doEval="true" entry="${entry}"/>
								<render:template template="${hitTemplate}" defaultTemplate="templates/tile.jsp">
									  <render:parameter name="entry" value="${entry}"/>
									  <render:parameter name="feed" value="${feed}"/>
									  <render:parameter name="widget" value="${widget}"/>
									  <render:parameter name="lastEntry" value="${feedStatus.last && entryStatus.last}"/>
								</render:template>
							</search:forEachEntry>
						</search:forEachFeed>
						
						<search:getPaginationInfos varCurrentPage="currentPage" feeds="${feeds}" varLastPage="lastPage"/>
						<c:if test="${currentPage != lastPage}">
							<config:getOption name="collapse" var="collapse" defaultValue="false" />
							<i18n:message code="widget.resultCarousel.see-more" var="seeMoreTooltip" text="See more"/>
							<li class="hit hit-more ${collapse ? "collapse" : ""}" title="${seeMoreTooltip}">
								<span class="fonticon fonticon-right-open"></span>
							</li>
						</c:if>
					</widget:content>

				</c:otherwise>
				
			</c:choose>
		</widget:widget>

		<render:renderScript position="READY">
			<search:getPaginationInfos varCurrentPage="currentPage" feeds="${feeds}" varLastPage="lastPage"/>
			
			(function(){
			
				var ajaxClient = new MashupAjaxClient();
				var currentPage = ${currentPage};
				var lastPage = ${lastPage};
				
				function updatePage(ajaxClient){
					currentPage += 1;
					ajaxClient
					<search:forEachFeed feeds="${feeds}" var="feed">
						.addParameter('${feed.id}.page', currentPage + 1)</search:forEachFeed>;
				}
				
				ajaxClient.addWidget('${uCssId}', false)
					<search:forEachFeed feeds="${feeds}" var="feed">
						
					.addParameter('${feed.id}.page', currentPage + 1)
					</search:forEachFeed>
					.addParameter('isInfiniteScrollRequest', 'true');
					
					<request:forEachParameter var="parameterName">
						<request:getParameterValues name="${parameterName}" var="parameterValues" xmlEscape="false"/>
						<c:forEach items="${parameterValues}" var="parameterValue">
							ajaxClient.addParameter('${parameterName}','${parameterValue}');
						</c:forEach>
					</request:forEachParameter>
					
				var button = $('.${uCssId}').find('.hit-more');
				button.off('click');
				button.on('click', function(){
					$.ajax({
						type: 'GET',
						url: ajaxClient.getAjaxUrl().toString(),
						dataType: 'json',
						cache: false,
						success: function(data, textStatus){
							if (data != null && data.widgets.length > 0){
								button.before(data.widgets[0].html);
								//append script
								$('#mainWrapper').append(data.appendScript);
								updatePage(ajaxClient);
								
								if (currentPage === lastPage){
									button.hide();
								}
								$(".${uCssId}").trigger("resultCarousel:more-items");
							}
						}
						
					});
					
				});

			})();
			
		</render:renderScript>

		<%--render init & hit click event--%>
		<config:getOption name="onHitClick" var="onHitClick" defaultValue="function(e) {}"  />
		<config:getOption name="onInit" var="onInit" defaultValue="function() {}"  />
		<render:renderOnce id="${uCssId}">
			<render:renderScript>
				(function() {
					var widget = $('.${uCssId}');
					(${onInit}).call(widget, "${uCssId}");

					$('.${uCssId}').on('click', "li.hit", function(e) {
						(${onHitClick}).call(widget, e);
					});
				})();
			</render:renderScript>
		</render:renderOnce>
			
	</c:otherwise>
</c:choose>
