<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import varWidget="widget" varFeeds="feeds"/>
<%--Only one feed supported so get first feed--%>
<search:getFeed var="feed" feeds="${feeds}"/>

<config:getOption name="iconCss" var="iconCss"/>
<config:getOption name="label" var="label"/>
<config:getOption name="showLabel" var="showLabel"/>

<widget:widget varUcssId="uCssId" varCssId="cssId" disableStyles="true">
    <%--	Configure widget ID, does not seem to work if not set... --%>
    <render:template template="templates/exportButton.jsp" widget="plmaExport">
        <render:parameter name="uid" value="${uCssId}"/>
        <render:parameter name="feed" value="${feed}"/>
        <render:parameter name="numHits" value="${plma:getIntegerParam(widget, 'numHits', -1 )}"/>
        <render:parameter name="columnsConfig" value="${plma:getStringParam(widget, 'columnsConfig', '' )}"/>
        <render:parameter name="exportMode" value="${plma:getStringParam(widget, 'exportMode', 'Search API' )}"/>
        <render:parameter name="exportPerPage" value="${plma:getIntegerParam(widget, 'exportPerPage', -1)}"/>
        <render:parameter name="fileName" value="${plma:getStringParam(widget, 'fileName', 'export' )}"/>
        <render:parameter name="exportEncoding" value="${plma:getStringParam(widget, 'exportEncoding', 'UTF-8' )}"/>
        <render:parameter name="exportSeparator" value="${plma:getStringParam(widget, 'exportSeparator', ';' )}"/>
        <render:parameter name="recordDelimiter" value="${plma:getStringParam(widget, 'recordDelimiter', 'Default' )}"/>
        <render:parameter name="addBOM" value="${plma:getBooleanParam(widget, 'addBOM', true )}"/>
        <render:parameter name="exportRawValues" value="${plma:getBooleanParam(widget, 'exportRawValues', false )}"/>
        <render:parameter name="iconCss" value="${iconCss}"/>
        <render:parameter name="label" value="${label}"/>
        <render:parameter name="showLabel" value="${showLabel}"/>
    </render:template>
</widget:widget>