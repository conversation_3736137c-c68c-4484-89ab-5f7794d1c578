<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA hit details" group="PLM Analytics/Results Rendering" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>This widget displays the details of a hit that is opened from a result list.</Description>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/plmaHitDetails.js" />

		<Include type="js" path="js/plmaHitDetailsPref.js" />
		<Include type="js" path="js/plmaHitDetailsButtons.js" />
		<Include type="css" path="css/hitdetails-pref.less" />

	</Includes>

	<Preview>
		<![CDATA[
			<img src="img/preview.png" />
		]]>
	</Preview>
	
	<Dependencies>
		<Widget name="plmaResources" />
		<Widget name="compareHit" />
		<!-- Widget dependencies will be include if showThumbnail is equals to one of these values -->
		<Widget name="thumbnail" includeCondition="showThumbnail=on the left,showThumbnail=on the right" />
		<Widget name="preview" includeCondition="showPreview=true" />
		<Widget name="download" includeCondition="showDownload=true" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO_OR_MANY" label="For each hit" />
	<SupportFeedsId arity="MANY"  consumeFeed="false"/>
	<SupportI18N supported="true" />
	
	<Interactions>
		<Interaction type="in" id="exa.io.HitDecorationInterface">
			<Description>Decorate your hit.</Description>
			<Option id="hitDecorationCssSelector" name="Css path" arity="ONE">
				<Description>
				<![CDATA[
					CSS path where the decoration will be appended in the hit display.<br />
					Generally, the default value does not need to be changed for the standard hit display widget.
				]]>
				</Description>
				<Functions>
					<ContextMenu>COMMON_add('Default template', [{display:'Header left',value:'li.${entry:cleanId(entry)} .rounded-top'}, ])</ContextMenu>
					<ContextMenu>Eval()</ContextMenu>
				</Functions>
			</Option>
			<Option id="hitDecorationPosition" name="Decoration position" arity="ONE">
				<Description>
				<![CDATA[
					The decoration will be appended or prepended.
				]]>
				</Description>
				<Values>
					<Value></Value>
					<Value>append</Value>
					<Value>prepend</Value>
				</Values>
			</Option>
		</Interaction>
	</Interactions>

	<OptionsGroup name="Hit config">
		<Option id="hitTitle" name="Hit title" isEvaluated="true">
			<Description>Specifies the title displayed for each hit. Leave blank to use the &lt;i&gt;Title&lt;/i&gt; that is defined in the source feed. </Description>
		</Option>
		<Option id="hitSubTitleDescription" name="Hit subtitle description" isEvaluated="true">
			<Description>Specifies a subtitle description (small description) displayed under the hit title.</Description>
		</Option>
		<Option id="hitContent" name="Hit content" isEvaluated="true">
			<Description>Specifies the text to display as main content of the hit. Defaults to the text field.</Description>
		</Option>
		<Option id="hitUrl" name="Hit URL" isEvaluated="true">
			<Description>Specifies the URL that opens when clicking the "Open in source" button which is displayed at the top of the hit when this option is specified.</Description>
		</Option>
		<Option id="typeFacetId" name="Type facet" arity="ZERO_OR_ONE">
			<Description>The facet describing the type of the displayed hit. Used to display the type icon, if configured.</Description>
		</Option>
		<Option id="facetIcon" name="Default icon for facet">
			<Description>You can specify a default icon associated with the hit's type (default is fonticon-legend).</Description>
			<Functions>
				<ContextMenu>Facets()</ContextMenu>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Hit metas">
		<Option id="showAnalysisDate" name="Show analysis date" arity="ONE">
			<Description>Displays the analysis date.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['analysisDateMetaName', 'analysisDateFormat']})</Display>
			</Functions>
		</Option>
		<Option id="analysisDateMetaName" name="Meta name of the analysis date" arity="ZERO_OR_ONE">
			<Description>Meta name used for the analysis date. Defaults to 'analysisdate'.</Description>
			<Functions>
				<ContextMenu>Metas()</ContextMenu>
			</Functions>
		</Option>
		<OptionComposite id="analysisDateFormat" name="Analysis Date Format" isEvaluated="true" arity="ONE">
			<Option id="inputFormat" name="Input Format" isEvaluated="true" arity="ZERO_OR_ONE">
				<Description>Default Input format is 'yyyy/MM/dd H:m:s' as defined in Search Logic. "Insert Current Date" data processor uses the GMT timezone. "pattern" has to match the one in the search logic.</Description>
			</Option>
			<Option id="outputFormat" name="Output Format" isEvaluated="true" arity="ZERO_OR_ONE">
				<Description>Default Output format is 'EEE MMM dd HH:mm:ss zzz yyyy'. To check ref JSTL 'fmt:formatDate' for changing the format.</Description>
			</Option>
		</OptionComposite>
		<Option id="useExternalisedConfig" name="Use external config" arity="ONE">
			<Description>Use fields configurations from config xml file.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({ valueToMatch:'true', showOptions:['externalConfig'], hideOptions:['showEmptyMetas','metaGroups','sortModeMetas']})</Display>
			</Functions>
		</Option>
		<OptionComposite id="externalConfig" name="External Config" isEvaluated="false" arity="ONE">
			<Option id="hitDetailsId" name="HitDetails Id" isEvaluated="true" arity="ONE">
				<Description>HitDetails Configurations ID to use. You can provide MEL expression as well if you need conditional config selection.</Description>
			</Option>
			<Option id="configName" name="Configuration Name" isEvaluated="true" arity="ONE">
				<Description>Configuration name.</Description>
			</Option>
		</OptionComposite>
		<Option id="showEmptyMetas" name="Display empty metas" arity="ONE">
			<Description>Displays the names of metas with no (or empty) values.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<OptionComposite id="metaGroups" name="Meta groups" arity="ZERO_OR_MANY">
			<Description>You can display metas in groups for a more readable output.</Description>
			<Option id="groupTitle" name="Title" isEvaluated="true">
				<Description>A title of this group.</Description>
			</Option>
			<Option id="groupMetaList" name="Metas" arity="ZERO_OR_ONE">
				<Description>The metas to display ('Include') or not to display ('Exclude') in this group.</Description>
				<Functions>
					<ContextMenu>Metas()</ContextMenu>
					<ContextMenu>COMMON_appendOnChange(', ')</ContextMenu>
				</Functions>
			</Option>
			<Option id="groupFacetList" name="Facets" arity="ZERO_OR_ONE">
				<Description>The facets to display ('Include') or not to display ('Exclude') in this group.</Description>
				<Functions>
					<ContextMenu>Facets()</ContextMenu>
					<ContextMenu>COMMON_appendOnChange(', ')</ContextMenu>
				</Functions>
			</Option>
			<Option id="groupMetaFilter" name="Filtering mode" arity="ONE">
				<Description>Specifies the filtering method to apply to the metas. For the 'Exclude' and 'Include' methods, the 'Meta list' field allows to specify the metas to include or exclude.</Description>
				<Values>
					<Value>Include</Value>
					<Value>Exclude</Value>
				</Values>
			</Option>
		</OptionComposite>
		<Option id="sortModeMetas" name="Meta sort method">
			<Description>Specifies how metas will be sorted.</Description>
			<Values>
				<Value>default</Value>
				<Value>description-asc</Value>
				<Value>description-desc</Value>
				<Value>shuffle</Value>
			</Values>
		</Option>		
	</OptionsGroup>

	<OptionsGroup name="General">
		<Option id="title" name="Widget Title" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
		</Option>

		<Option id="showOpenButton" name="Show open document button" arity="ONE">
			<Description>Displays open button in order to open document in other page or widget.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="openButtonTooltip" name="Open Button Tooltip" isEvaluated="true" arity="ZERO_OR_ONE">
			<Description>Specifies the tooltip label of the open button.</Description>
		</Option>
		<!-- Close button Config start-->
		<Option id="showCloseButton" name="Show close details button" arity="ONE">
			<Description>Displays close button.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({ valueToMatch:'true', showOptions:['closeButtonLabel', 'showCloseButtonLabel']})</Display>
			</Functions>
		</Option>
		<Option id="closeButtonLabel" name="Close Button Label" isEvaluated="true" arity="ZERO_OR_ONE">
			<Description>Specifies the label of the close button.</Description>
		</Option>
		<Option id="showCloseButtonLabel" name="Show label" arity="ONE">
			<Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="scrollTop" name="Scroll Top" isEvaluated="false" arity="ONE">
			<Description>Keep hitDetails ScrollBar to top on click of hit, default to false.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="disableHitDetailsPrefTab" name="Disable HitDetails Display" arity="ONE">
			<Description>Disable HitDetails Display preference tab, default to False.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		<!-- Close button Config end-->
		<Option id="iconsSize" name="Icons size" arity="ZERO_OR_ONE"
				isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
				isXmlEscaped="false">
			<Description>Specifies icons font size (in pixels). You	must enter an integer. Default 24.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Compare">
		<Description>Compare option, de-activated by default. It allows to display compare view (only usable if compare view is present in result list layout) </Description>
		<Option id="enableCompare" name="Enable" arity="ONE">
			<Description>
				Enables "compare" button that allows user to compare multiple hits.&lt;br/&gt;
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['compareCollection','compareLabel','showCompareLabel','hitID','compareCollection','compareCounterSelector', compareIdMetaName]})</Display>
			</Functions>
		</Option>

		<Option id="compareCollection" name="Compare collection name">
			<Description>Collection name.</Description>
		</Option>

		<Option id="compareIdMetaName" name="ID meta name" isEvaluated="true" arity="ZERO_OR_ONE">
			<Description>Specifies metadata used in compare collection, by default, raw URL is used if empty.</Description>
		</Option>

		<Option id="showCompareLabel" name="Show label" arity="ONE">
			<Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Favorites">
		<Description>Favorites options, de-activated by default. It allows to display hit favorite button (only usable if compare view is present in result list layout) </Description>
		<Option id="enableFavorite" name="Enable" arity="ONE">
			<Description>
				Enables "favorite" button that allows user to add/remove hit from favorites.&lt;br/&gt;
			</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['favoriteCollection', 'favoriteCounterSelector', 'favoriteLabel', 'showFavoriteLabel', 'favoriteIdMetaName']})</Display>
			</Functions>
		</Option>

		<Option id="favoriteCollection" name="Favorites collection name">
			<Description>Collection name.</Description>
		</Option>

		<Option id="showFavoriteLabel" name="Show label" arity="ONE">
			<Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>

		<Option id="favoriteIdMetaName" name="ID meta name" isEvaluated="true" arity="ZERO_OR_ONE">
			<Description>Specifies metadata used in favorites collection, by default, raw URL is used if empty.</Description>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Advanced">
		<Option id="customJspPathHit" name="Hit JSP template" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>
				<![CDATA[
					Specifies the JSP template to use.<br />
					Using variables for finding a valid template is useful if you want to customize hit display based for example, on the TYPE.<br />
					Hit templates are in /WEB-INF/jsp/widgets/displayHits/templates/.
				]]>
			</Description>
			<Functions>
				<Check>isJspPath</Check>
				<ContextMenu>JAVA_listFiles('templates/', '*/default.jsp', 'JSP base templates')</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="noResultsJspPath" name="JSP path to use if no results" arity="ZERO_OR_ONE">
			<Description>If there are no results, the widget is either hidden using a trigger, or a message is displayed using the content of a JSP file.</Description>
			<Functions>
				<Check>isJspPath</Check>
			</Functions>
		</Option>
		<Option id="noResultJspPathWidget" name="Widget where to find the JSP if no results" arity="ZERO_OR_ONE" >
			<Description>The path in the previous option must be relative to a widget deployed as plugin. Specify this widget here. Defaults to 'plmaResources'.</Description>
		</Option>
		<Option id="forceRefineOnFeeds" name="Force refinement for feeds" arity="ZERO_OR_MANY">
			<Description>Forces refinements on the specified feeds.</Description>
			<Functions>
				<ContextMenu>Feeds()</ContextMenu>
				<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
			</Functions>
		</Option>
		<Option id="resetPageParameters" isEvaluated="true" isUrlEncoded="true" name="Remove page parameters" arity="ZERO_OR_MANY">
			<Description>Parameters to be removed from the url while building the refinement URL of facets.</Description>
			<Functions>
				<ContextMenu>PageParameters()</ContextMenu>
				<ContextMenu>COMMON_appendOnChange(', ')</ContextMenu>
			</Functions>
		</Option>
		<Option id="onInit" name="On init" isEvaluated="true">
			<Description>A JS function called on widget initialisation. e.g. function($widget) {}</Description>
			<Placeholder>function($widget) {}</Placeholder>
			<Functions>
				<Display>SetType('code', { mode: 'javascript', fullscreen: false })</Display>
			</Functions>
		</Option>
	</OptionsGroup>

	<DefaultValues>
		<DefaultValue name="showHitMetas">true</DefaultValue>
		<DefaultValue name="hitUrlTarget">Current Page</DefaultValue>
		<DefaultValue name="filterMetas">No filtering</DefaultValue>
		<DefaultValue name="customDisplay">false</DefaultValue>
		<DefaultValue name="sortModeMetas">default</DefaultValue>
		<DefaultValue name="metaUrlTarget">Current Page</DefaultValue>
		<DefaultValue name="showHitIcon">true</DefaultValue>
		<DefaultValue name="showHitId">true</DefaultValue>
		<DefaultValue name="showTextOnTop">true</DefaultValue>
		<DefaultValue name="showTextOnTopTruncate">500</DefaultValue>
		<DefaultValue name="showHitFacets">true</DefaultValue>
		<DefaultValue name="showEmptyFacets">false</DefaultValue>
		<DefaultValue name="hitFilterFacetsType">No filtering</DefaultValue>
		<DefaultValue name="groupMetaFilter">Include</DefaultValue>
		<DefaultValue name="sortModeFacets">default</DefaultValue>
		<DefaultValue name="hitFacetSortStrategy">default</DefaultValue>
		<DefaultValue name="showThumbnail">on the left</DefaultValue>
		<DefaultValue name="useThumbnailPreview">true</DefaultValue>
		<DefaultValue name="showPreview">true</DefaultValue>
		<DefaultValue name="showDownload">true</DefaultValue>
		<DefaultValue name="noResultsJspPath">template/noHitSelected.jsp</DefaultValue>
		<DefaultValue name="noResultJspPathWidget">plmaResources</DefaultValue>
		<DefaultValue name="showAnalysisDate">false</DefaultValue>
		<DefaultValue name="analysisDateMetaName">analysisdate</DefaultValue>
		<DefaultValue name="onInit">function($widget) { }</DefaultValue>
		<DefaultValue name="analysisDateFormat">yyyy/MM/dd H:m:s##EEE MMM dd HH:mm:ss zzz yyyy</DefaultValue>
		<!-- Close button Config start-->
		<DefaultValue name="showCloseButton">true</DefaultValue>
		<DefaultValue name="closeButtonLabel">Close Detail Panel</DefaultValue>
		<DefaultValue name="showCloseButtonLabel">false</DefaultValue>
		<!-- Close button Config end-->
		<DefaultValue name="scrollTop">false</DefaultValue>
		<DefaultValue name="disableHitDetailsPrefTab">false</DefaultValue>
		<DefaultValue name="showOpenButton">true</DefaultValue>
		<DefaultValue name="openButtonTooltip">Open with Enovia 3DSpace</DefaultValue>
		<!-- Default Compare Values -->
		<DefaultValue name="compareCollection">compare</DefaultValue>
		<DefaultValue name="compareLabel">Add to Compare</DefaultValue>
		<DefaultValue name="showCompareLabel">true</DefaultValue>
		<!-- Default favorites collection configuration -->
		<DefaultValue name="favoriteCollection">favoriteHits</DefaultValue>
		<DefaultValue name="favoriteLabel">Add to Favorites</DefaultValue>
		<DefaultValue name="showFavoriteLabel">false</DefaultValue>
	</DefaultValues>

</Widget>
