<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="highcharts" uri="http://www.exalead.com/jspapi/highcharts" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import parameters="widget,feeds,facet,min,max,step,uCssId,refinePanelCfg"/>

<plma:getConstantValueFromNameTag var="loadFacetParameter" constantName="CLEAN_FEED_PARAMETER" />

<list:new var="feedList"/>
<search:forEachFeed feeds="${feeds}" var="feed">
	<list:add list="${feedList}" value="'${feed.id}'"/>
</search:forEachFeed>

<search:getFacet var="mergedFacet" facetId="${facet}" feeds="${feeds}"/>

<c:choose>
    <c:when test = "${refinePanelCfg.collapseFacet}">
        <c:set var="collapseValue" value="hidden"/>
    </c:when>
    <c:otherwise>
        <c:set var="collapseValue" value=""/>
    </c:otherwise>
</c:choose>

<c:if test="${not empty mergedFacet}">
	<div class="filter-list-facet-elem filter-list-facet-elem-numerical filter-list-facet-elem-${facet}">
		<div class="facet-value-container">
			<div class="facet-container-list">
				<table class="facet table table-layout table-facets table-hover table-list-facet-${facet}">
					<!-- Title -->
					<h3 class="title-container sub-header-collapsible">
						<span class="facet-name"  id="${facet}">
							<search:getFacetLabel facet="${mergedFacet}"/>
						</span>
						<span class="icon-collapse-button icon-collapsible-button fonticon fonticon-chevron-up"></span>
						<span class="icon-collapse-button icon-collapsed-button fonticon fonticon-chevron-down hidden"></span>
					</h3>
					<div class="facet-container-list facet-container-list-numerical-slider-${facet} ${collapseValue}">
						<div class="facet-value">
							<c:if test="${empty min}">
								<c:set var="min" value="0"/>
							</c:if>
							<c:if test="${empty max}">
								<c:set var="max" value="100"/>
							</c:if>
							<c:if test="${empty step}">
								<c:set var="step" value="1"/>
							</c:if>
							<div class="refine-panel-numerical-slider refine-panel-numerical-slider-${facet}"></div>
							<div class="slider-extreme-container">
								<span class="slider-min-value">${min}</span>
								<span class="slider-max-value">${max}</span>
							</div>

							<div class="slider-action-container">
								<div class="slider-inputs">
									<input type="number" class="value min-value" max="${max}" min="${min}"/>
									<span class="value"> - </span>
									<input type="number" class="value max-value" max="${max}" min="${min}"/>
								</div>
								<i18n:message code="widgets.refinePanel.numerical.filter" var="filterButton"/>
								<i18n:message code="widgets.refinePanel.numerical.exclude" var="excludeButton"/>
								<span class="slider-button slider-exclude-button fonticon fonticon-block"
									  title="${excludeButton}"></span>
								<span class="slider-button slider-refine-button fonticon fonticon-filter"
									  title="${filterButton}"></span>
							</div>

							<div class="slider-refine-container hidden">

							</div>

						</div>
					</div>
				</table>
			</div>
		</div>
	</div>

	<render:renderScript position="READY">
		var options = {};
		options.uCssId = '${uCssId}';
		options.facet = '${facet}';
		options.min = '${min}';
		options.max = '${max}';
		options.step = '${step}';
		options.feedList = ${feedList};
		options.url = '<c:url value="/data/range/facet"/>';
		options.loadFacetParameter = '${loadFacetParameter}';
		var plmaNumericalRefine = new PLMANumericalRefine(options);
		plmaNumericalRefine.init();
	</render:renderScript>
</c:if>
