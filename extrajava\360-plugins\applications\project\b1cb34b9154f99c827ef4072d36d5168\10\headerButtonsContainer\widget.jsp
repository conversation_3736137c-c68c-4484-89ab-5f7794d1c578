<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget"%>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render"%>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>

<config:getOption name="addContainerDiv" var="addContainerDiv" defaultValue="false" />

<c:choose>
	<c:when test="${addContainerDiv == 'true'}">
		<widget:widget disableStyles="true" extraCss="headerButtonsContainer">
			<render:subWidgets />
		</widget:widget>
	</c:when>
	<c:otherwise>
		<render:subWidgets />
	</c:otherwise>
</c:choose>



