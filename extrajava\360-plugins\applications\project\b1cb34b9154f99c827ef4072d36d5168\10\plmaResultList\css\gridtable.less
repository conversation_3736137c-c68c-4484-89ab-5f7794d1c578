@header-height: 45px;
@row-height: 50px;
@fixed-col-width: min-content; /*Including toolbar width*/
@toolbar-col-width: 100px;
@grid-col-width: 160px;

.mashup .result-panel .plmaResultList .results-panel{
	.scroll-container.gridtable{
		overflow:auto;
		padding: 0px;
		&::-webkit-scrollbar-track {
			--total-width: ~"calc(var(--title-container-size, 335px) + 5px)";
			margin-left: var(--total-width, 340px);
			margin-top: @row-height;
		}
		
		/*Fix thead which can have th or td(for fixed rows)*/
		.fix-header{
			position: sticky;
			top: 0;
			height: auto;
			width: min-content;
			z-index: 10;
			box-shadow: -6px 6px 10px -2px #b4b6ba;
			margin-bottom: 3px;		
			.table-header .th {
				background: #F1F1F1;
				font-size: 12px;
				color: #3D3D3D;
				height: @header-height;

				&.table-column{
					position: relative;
					.label{
                    	position: inherit;
                    	display: inline-block;
                    	overflow: hidden;
                    	white-space: nowrap;
                    	text-overflow: ellipsis;
                    	width: 115px;
                    }
                    .icon{
                    	 display: inline-block;
                    	position: relative;
                    	bottom: 18px;
                    }
				}
				&.table-column:has(.icon-similar) {
					.label{
						width: 105px;
					}
				}
				
				&> span{
					line-height: @header-height;
					
					&.fonticon.fonticon-expand-down,
					&.fonticon.fonticon-expand-up {
						line-height: 10px;
						position: absolute;
						right: 5px;
						top: 10px;
					}
					&.fonticon.fonticon-expand-down{
						bottom: 10px;
						top: auto;
					}
				}
				&.fixed-column{
					padding-left: 0px;
					padding-right: 5px;
				}
			}
			
			.grid-row.table-header{
				height: @header-height;
			}			
		}
		.grid-row{
			display: grid;
			grid-template-columns: @fixed-col-width;	/*Fixed Column*/
			grid-auto-flow: column;
			grid-auto-columns: @grid-col-width;		
			grid-template-rows: @row-height;
			border: 1px solid #E2E4E3;
			align-items: center;
			background: white;
			
			.fixed-column{
				line-height: 3px;
				position: sticky;
				left: 0;
				box-shadow: 7px 0px 7px -3px #b4b6ba;
				background: #ffffff;
				z-index: 9;
				width: var(--title-container-size, 335px);
			}
		}
		li.hit .hit-container.grid-row:hover{
			border-bottom:1px solid #77797c;
			border-top:1px solid #77797c;
			.mainColumn{
				border-right-color: #77797c;
			}
		}
		
		.hits.gridtable-layout{
			margin-top: 0px;
			padding: 0px;
			width: min-content;
		}
		/*Can presnet in both fix-header or hits*/
		li.hit{
			margin: 0px;
			border-width: 0px;
			.hit-container{
				overflow: inherit;
				.mainColumn{
					border-right: 5px solid transparent;
					height: @row-height;
					.hitContent { 
						display: none; 
					}
				}
			}	
			.subwidgets{
				height: 0px;
				div { padding: 0px; }
			}
			&.selected{
				border-width: 0px;
				.mainColumn{
					border-right-color: #42a2da;
				}
			}
		}	
				
		.grid-row{
			.fixed-column .title-container {
				display: grid;
				grid-auto-flow: row;
				grid-template-columns: min-content;
				height: 50px;
				align-items: center;
				
				.toolbarContainer{
					grid-row: 1;
					grid-column: 1;
					width: var(--icon-container-size, @toolbar-col-width);
					position:relative;
					border-right: 1px solid;
					height: 100%;
					.freezeButtonClass:hover{
                        color:#42a2da;
                    }
				}
				.hitThumbnail{
					grid-row: 1;
					grid-column: 2;					
				}
				.icon-container {
					grid-row: 1;
					grid-column: 2;
					padding: 0px 5px 0px 5px;
					width: 45px;
				}
				.hitTitle{
					grid-row: 1;
					grid-column: 3;
					width: 180px;
					line-height: 100%;
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
					display: inline-block;
					color: #368ec4;
					font-size: 16px;					
				}
				
				&.with-thumbnail{
					.hitThumbnail{
						height: @row-height;
						width: @row-height;
						padding: 0px 5px;
					}
					.hitCursor {
					    cursor : move;
					}
					.hitTitle .icon{
						width: 22px;
					}
				}
			}
			.th.fixed-column .title-container{
				height: 45px;
				.hitThumbnail, .toolbarContainer {
					height: 45px;
				}
				.hitTitle{
					width: 180px + 55px - 1px;	/* hitTitleWidth + (iconContainerWidth + (padding*2)) - border */
				}
				&.with-thumbnail .hitTitle{
					width: 180px - 1px;
				}
			}
		}
		
		.th, .td {
			//width: 145px;
			 min-width: 132px;
			border-right: 1px solid gray;
			word-break: break-word;
			overflow: hidden;
			.resize-handle {
                content: "";
                position: absolute;
                right: 0;
                top: 0;
                width: 5px;
                height: 100%;
                cursor: col-resize;
                background-color: transparent;
            }
		}
		.td.hitFacet, .td.hitMeta, .td.hitType {
			padding: 3px;
			padding-left: 12px;
			height: @row-height - 6px;
			line-height: 40px;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
		.td .cell-content{
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
		.td.hitFacet{
			.category-link.refined:hover .categoryName{
				color:white !important;
			}
			.categoryLinksDecoration{
				text-decoration: underline 0.5px;
			}
		}
		&.grid-resize{
			.grid-row{
				grid-auto-columns: unset;
				// width: 200px;
			}
			.th {
				width: 160px;
				min-width: 146px;
				&.fixed-column{
					width: var(--title-container-size, 335px);
				}
			}
			.td{
				width: 145px;
				min-width: 131px;
				&.fixed-column{
					width: var(--title-container-size, 335px);
				}
			}
		}
	}
}