var CollectionEventHandlers = function (uCssId, options) {
    if (uCssId) {
        this.$widget = $('.' + uCssId);
        // this.widget.data('widget', this);
    } else {
        this.$widget = $();
    }
    this.options = options;
    this.$elements = {
        DETAIL_WRAPPER: this.$widget,
        COUNTER: this.$widget.find('.label-section .counter'),
        COLLECTION_ICON: this.$widget.find('collection-icon'),
        BTN_DISPLAY: this.$widget.find('.action-details'),
        BTN_CLEAR: this.$widget.find('.action-clear'),
        COUNT_AWARE: this.$widget.find('.count-aware')
    };
    var searchParams = new URLSearchParams(window.location.search);
    this.urlBuilder = new PlmaUrlBuilder(null, {
        cleanRefines: true,
        feeds: this.options.feeds,
        removeAllParams: true,
        keepParams: [],
        removeParams: ['viewCollection', 'displayView']
    });
    if (options.collection){
        this.urlBuilder.addParameter('viewCollection', options.collection);
    };
    if (options.displayView){
        this.urlBuilder.addParameter('displayView', options.displayView);
    };
    this.init();
    return this;
};

CollectionEventHandlers.prototype.init = function () {
    this.$elements.BTN_CLEAR = this.$elements.BTN_CLEAR.not('#clear-preferred-hits');
    this.$elements.BTN_DISPLAY.on('click', $.proxy(function (e) {
        // Disable parent div event
        e.stopPropagation();
        if (this.options.mode == 'js') {
            $.ajax({
                method: 'GET',
                url: this.options.url + '/getQuery/' + this.options.collection,
                dataType: 'JSON',
                async: false,
                context: this,
                success: function (data) {
                    if (this.options.mode == 'js') {
                        /*
                            In JS mode, we pass in URL hits IDs query to display, in this mode, it does not prereq collection trigger
                            /!\ it is not a recommended behavior because it passes a huge query parameter in URL in GET, it is highly recommended
                            to use collections UI pre request trigger
                         */
                        this.urlBuilder.addParameter('hits', data.answer, true);
                    }
                    window.location.href = this.urlBuilder.toString();
                },
                error: function (data) {
                    $.notify('Error getting collection items query', "error");
                }
            });
        } else {
            window.location.href = this.urlBuilder.toString();
        }
    }, this));

    this.$elements.BTN_CLEAR.on('click', $.proxy(function (e) {
        // Disable parent div event
        e.stopPropagation();
        $.ajax({
            method: 'DELETE',
            url: this.options.url + '/delete/' + this.options.collection,
            dataType: 'JSON',
            async: false,
            context: this,
            success: function (data) {
                //$.notify('Collection items removed', "success");
                // Reload page to be sure displayed selected hits are not selected anymore
                window.location.href = window.location.pathname;
            },
            error: function (data) {
                $.notify('Error getting collection items query', "error");
            }
        });
    }, this));
}