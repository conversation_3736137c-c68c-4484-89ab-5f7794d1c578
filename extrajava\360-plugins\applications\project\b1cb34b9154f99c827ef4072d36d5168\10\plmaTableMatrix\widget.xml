<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA Table (Matrix)" group="Tables" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/plmaTable.js" />
		<Include type="js" path="../plmaResources/js/popupLib.js" />
		<Include type="js" path="../plmaResources/js/plmaAjaxClient.js"/>
	</Includes>
	
	<Preview>
		<![CDATA[
			<img src="images/preview_tablematrix.png" alt="Matrix Table" />
		]]>
	</Preview>
	
	<Dependencies>
		<Trigger position="self" className="com.exalead.cv360.searchui.view.widgets.triggers.TableFacetBuilderTrigger" />
		<Widget name="plmaResources" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="MANY" />
	<SupportI18N supported="true" />

	<OptionsGroup name="General">
		<Option id="title" name="Widget Title" isEvaluated="true">
			<Description>Widget title. If empty, no name is displayed.</Description>
		</Option>
		
		<Option id="facetName" name="Facet Name" isEvaluated="true" arity="ONE">
			<Description>Widget facet name.</Description>
		</Option>
        
        <Option id="createFacet" name="Create facet" arity="ONE">
            <Description>Check this box to let the trigger attached to this widget create the needed multidimension facet. If unchecked, the "Facet name" specified above must refer to a multidimension facet defined at the feed level.</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
        </Option>
		
		<Option id="facetPageName" name="Destination page on click" isUrlEncoded="true" isEvaluated="true">
			<Description>Indicates the URL that must be accessed for a refinement option. If blank, you stay on the same page.</Description>
			<Functions>
				<ContextMenu>Pages()</ContextMenu>
			</Functions>
		</Option>

		<Option id="withAjax" name="With AJAX">
			<Description>Enables Ajax (on 2D facets).</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['widgetsId']})</Display>
			</Functions>
		</Option>
		
		<OptionComposite id="widgetsId" name="Widget wuids to update them at each refine" arity="ZERO_OR_MANY" glue="##">
			<Description>WUID list of widgets that will be updated using Ajax at each table refine.</Description>
			<Option id="idWidget" name="Widget Id" arity="ONE">
				
			</Option>
		</OptionComposite>
	</OptionsGroup>

	<OptionsGroup name="Axis">
		<Option id="nbDim" name="Number of dimension">
			<Description>Specifies the number of dimensions.</Description>
			<Values>
				<Value>1</Value>
				<Value>2</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['2'], showOptions:['axisX','axisXMaxItems','axisY','axisYMaxItems']})</Display>
				<Display>ToggleDisplay({valueToMatch:['1'], showOptions:['facet1DName','sortMode','iterMode']})</Display>
			</Functions>
		</Option>
		<Option id="facet1DName" name="Facet Name" arity="ZERO_OR_ONE">
			<Description>Specifies the facet name.</Description>
		</Option>
		<Option id="sortMode" name="Sort categories by" arity="ONE">
			<Description>
				<![CDATA[
					Sorts the categories using the selected method. Selecting anything but 'default' impacts performance.<br />
					<ul>
						<li>'default' requires no processing.</li>
						<li>'shuffle', 'desc*', 'score*', 'count*' and 'range*' impact performance on the server side.</li>
					</ul>
				]]>
			</Description>
			<Values>
				<Value>default</Value>
				<Value>description-asc</Value>
				<Value>description-desc</Value>
				<Value>score-asc</Value>
				<Value>score-desc</Value>
				<Value>count-asc</Value>
				<Value>count-desc</Value>
				<Value>range-asc</Value>
				<Value>range-desc</Value>
				<Value>shuffle</Value>
			</Values>
		</Option>
		<Option id="iterMode" name="Iteration mode" arity="ONE">
			<Description>
				<![CDATA[
					Determines what data is displayed:
					<ul>
						<li>'all' visits the first child of each node before processing its siblings,</li>
						<li>'leaves' iterates recursively, returning the end nodes,</li>
						<li>'flat' returns only the first level.</li>
					</ul>
				]]>
			</Description>
			<Values>
				<Value>flat</Value>
				<Value>leaves</Value>
				<Value>all</Value>
			</Values>
		</Option>
		<OptionComposite id="axisX" name="Axis X" arity="ONE" glue="##">
			<Description>The list of facets to use as x-axis. The first one will be the default one.</Description>
			<Option id="id" name="Facet" arity="ONE">
				<Functions>
					<ContextMenu>FacetsId()</ContextMenu>
				</Functions>
			</Option>
			<Option id="sort" name="Sort" arity="ONE">
				<Values>
					<Value>default</Value>
					<Value>alphanum</Value>
					<Value>count</Value>
					<Value>date</Value>
					<Value>num</Value>
					<Value>range</Value>
					<Value>lat</Value>
					<Value>lng</Value>
				</Values>
			</Option>
		</OptionComposite>
				
		<Option id="axisXMaxItems" name="Max items on axis X" arity="ZERO_OR_ONE">
			<Description>Specifies the maximum number of categories to display on the x-axis.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>

		<OptionComposite id="axisY" name="Axis Y" arity="ONE" glue="##">
			<Description>The list of facets to use as y-axis. The first one will be the default one.</Description>
			<Option id="id" name="Facet" arity="ONE">
				<Functions>
					<ContextMenu>FacetsId()</ContextMenu>
				</Functions>
			</Option>
			<Option id="sort" name="Sort" arity="ONE">
				<Values>
					<Value>default</Value>
					<Value>alphanum</Value>
					<Value>count</Value>
					<Value>date</Value>
					<Value>num</Value>
					<Value>range</Value>
					<Value>lat</Value>
					<Value>lng</Value>
				</Values>
			</Option>
		</OptionComposite>
		
		<Option id="axisYMaxItems" name="Max items on axis Y" arity="ZERO_OR_ONE">
			<Description>Specifies the maximum number of categories to display on the y-axis.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		
		<Option id="showSummaries" name="Show summaries">
			<Description>Displays a total column and row.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
		
		<Option id="pagination" name="Activate pagination">
			<Description>Activates pagination on the table.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['maxElemByPage']})</Display>
			</Functions>
		</Option>
		
		<Option id="maxElemByPage" name="Number of elements per page" arity="ONE">
			<Description>Specifies the number of columns to display per page.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Values">
		<OptionComposite id="aggregationList" name="Aggregation function" arity="ZERO_OR_MANY" glue="##">
			<Description>If you use the 'count' aggregation, you can ignore the 'Type' and 'Expression' fields.</Description>
			<Option id="id" name="Id" arity="ONE">
				<Functions>
					<ContextMenu>COMMON_add('Samples', ['count'])</ContextMenu>
				</Functions>
			</Option>
			<Option id="type" name="Type" arity="ONE">
				<Values>
					<Value>AVG</Value>
					<Value>MAX</Value>
					<Value>MIN</Value>
					<Value>SUM</Value>
					<Value>COUNT</Value>
					<Value>STDDEV</Value>
				</Values>
			</Option>
			<Option id="expression" name="Expression">
			</Option>
		</OptionComposite>
		
		<Option id="aggregationFormat" name="Aggregation format">
			<Description>
				<![CDATA[
					The pattern to use to format the aggregation value. 
					The syntax is the same as the <a href="http://docs.oracle.com/javase/1.4.2/docs/api/java/text/DecimalFormat.html" target="_blank">java.text.DecimalFormat</a> class.
					Leave the field empty to ignore it.
				]]>
			</Description>
		</Option>
	</OptionsGroup>
	
	<OptionsGroup name="Buttons">
		<OptionComposite name="Buttons" id="buttons" arity="ZERO_OR_MANY">
			<Option name="Icon CSS" id="css">
				<Description>CSS class(es) to define the button icon.</Description>
			</Option>
			<Option name="Tooltip" id="tooltip" isEvaluated="true">
				<Description>A short text displayed when the user hovers over the button.</Description>
			</Option>
			<Option name="On click" id="onClick" isEvaluated="true">
				<Description>Javascript code executed when the button is clicked.</Description>
				<Placeholder>function(){ }</Placeholder>
				<Values>
					<Value>function(button, widget, data){ console.log(data.myProperty); }</Value>
				</Values>
				<Functions>
					<Display>SetType('code', 'js')</Display>
				</Functions>
			</Option>
					
			<Functions>
				<Display>SetDisplayMode('list')</Display>
			</Functions>
		</OptionComposite>
	</OptionsGroup>

	<OptionsGroup name="Documentation">
        <Option id="displayDoc" name="Enable Documentation">
            <Description>If true, adds a button to display a HTML documentation to give the user information about the chart. (Default value is false)</Description>
            <Values>
                <Value>true</Value>
                <Value>false</Value>
            </Values>
            <Functions>
                <Display>ToggleDisplay({valueToMatch:['true'], showOptions:['doc']})</Display>
            </Functions>
        </Option>
        <Option id="doc" name="User Reference" isEvaluated="true">
            <Description>This is an HTML code to describe the chart.</Description>
            <Functions>
                <Display>SetType('code', 'html')</Display>
            </Functions>
        </Option>
        <Option id="techDoc" name="Technical Reference" isEvaluated="true">
            <Description>This is the chart's technical reference (it won't be displayed in the application).</Description>
            <Functions>
                <Display>SetType('code', 'html')</Display>
            </Functions>
        </Option>
    </OptionsGroup>

	<DefaultValues>
		<DefaultValue name="withAjax">false</DefaultValue>
		<DefaultValue name="aggregationList">count##COUNT##not used for count</DefaultValue>
		<DefaultValue name="showSummaries">true</DefaultValue>
	</DefaultValues>
	
</Widget>
