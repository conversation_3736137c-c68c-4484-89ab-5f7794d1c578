var PlmaGaugeChart = function (uCssId, value, maxValue, minValue, unit, title, ranges, height) {
	this.uCssId = uCssId;
	this.value = parseInt(value);
	this.maxValue = parseInt(maxValue);
	this.minValue = parseInt(minValue);
	this.unit = unit;
	this.title = title;
	this.ranges = ranges;
	this.height = height;
};

PlmaGaugeChart.prototype.init = function () {
	if (isNaN(this.value)) {
		this.value = 0;
	}
	var mainValue = ((this.value - this.minValue) / (this.maxValue - this.minValue)) * 100;
	mainValue = parseFloat(mainValue.toFixed(2));
	var otherValue = 100 - mainValue;

	var color = '#368EC4';
	for (var i = 0; i < this.ranges.length; i++) {
		if (mainValue > this.ranges[i].min && mainValue <= this.ranges[i].max) {
			color = this.ranges[i].color;
		}
	}

	var data = {
		chart: {
			renderTo: this.uCssId + '_plma_gauge',
			type: 'pie',
			plotBackgroundColor: color,
			plotBorderWidth: 0,
			plotShadow: false,
			margin: 0,
			height: this.height
		},
		credits: {
			enabled: false
		},
		title: {
			text: '<span style="font-weight: bold; font-size: 22px;">' + mainValue + ' ' + this.unit + '</span><br><span style="font-size: 16px;">' + this.title + '</span>',
			align: 'center',
			verticalAlign: 'middle',
			y: 40,
			style: {
				color: 'white'
			}
		},
		plotOptions: {
			pie: {
				slicedOffset: 0,
				size: '140%',
				dataLabels: {
					enabled: false
				},
				startAngle: -90,
				endAngle: 90,
				center: ['50%', '85%']
			}
		},
		tooltip: {
			enabled: false
		},
		series: [{

			innerSize: '80%',
			data: [
				{
					name: 'main',
					y: mainValue,
					color: 'white'
				},
				{
					name: 'other',
					y: otherValue,
					color: color
				}
			]
		}]
	};
	new Highcharts.Chart(data);


};

PlmaGaugeChart.prototype.initDoc = function () {
	var $widget = $('.' + this.uCssId);
	var $docButton = $widget.find('.doc-button');
	var $docContainer = $widget.find('.doc-container');
	var $closeButton = $docContainer.find('.close-doc');
	$docButton.add($closeButton).click(function () {
		$docContainer.toggleClass('hidden');
	});
};
