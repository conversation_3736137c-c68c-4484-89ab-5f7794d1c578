<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>

<render:import parameters="category,aggregation,categoryStyles,colors,total,index"/>

<search:getCategoryLabel var="label" category="${category}"/>
<search:getCategoryValue var="value" name="${aggregation}" category="${category}"/>

<c:choose>
	<c:when test="${not empty categoryStyles[category.description]}">
		<c:set var="color" value="${categoryStyles[category.description]}"/>
	</c:when>
	<c:otherwise>
		<c:set var="color" value="${colors[index % fn:length(colors)]}"/>
	</c:otherwise>
</c:choose>

<i18n:message var="tooltip" code="widget.HorizontalRepartition.segment.tooltip" arguments="${label};${value};${total}" argumentSeparator=";" text="${label} (${value}/${total})"/>
<string:escape var="tooltip" value="${tooltip}" escapeType="HTML"/>
<div class="category-segment" style="width: ${100*value/total}%; background-color: ${color};" title="${tooltip}">
</div>
