<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds"/>

<%-- Check if it is an infiniteScroll request --%>
<request:isAjax var="isAjax"/>
<request:getParameterValues var="infiniteScrollValues" name="infiniteScroll"/>
<request:getParameterValues var="paginationLoad" name="paginationLoad"/>
<request:getParameterValues var="isInfiniteScrollRequest" name="isInfiniteScrollRequest"/>
<c:set var="isAnInfiniteScrollRequest" value="false"/>
<c:forEach var="value" items="${infiniteScrollValues}">
	<c:if test="${value!=''}"><c:set var="isAnInfiniteScrollRequest" value="true"/></c:if>
</c:forEach>

<config:getOption name="resultListId" var="resultListId"/>
<config:getOption name="enableInfiniteScroll" var="enableInfiniteScroll"/>
<config:getOption name="containerHeight" var="containerHeight"/>
<config:getOption var="showThumbnail" name="showThumbnail" defaultValue="false"/>
<config:getOption var="displayHeader" name="displayHeader" defaultValue="false"/>
<config:getOption var="enableExport" name="enableExport" defaultValue="false"/>
<config:getOption var="customExportJSP" name="customExportJSP" defaultValue="templates/exportDisplay.jsp" />

<config:getOption var="enableSelect" name="enableSelect" defaultValue="false"/>
<config:getOption var="enableCompare" name="enableCompare" defaultValue="false"/>
<config:getOption var="compareCollection" name="compareCollection" defaultValue="compare"/>
<config:getOption var="selectCollection" name="selectCollection" defaultValue="hits-selection"/>

<config:getOption var="enablePublication" name="enablePublication" defaultValue="false" />
<config:getOption var="enableSubscription" name="enableSubscription" defaultValue="false" />
<config:getOption name="ajaxSort" var="ajaxSort" defaultValue="false"/>
<config:getOption name="customAbsoluteBlock" var="customAbsoluteBlock" defaultValue="" />
<config:getOptions var="channelNames" name="channels" />

<config:getOption var="buttonsIconSize" name="buttonsIconSize" defaultValue=""/>

<widget:getUcssId var="uCssId"/>
<c:set var="scrollContainerId" value="${uCssId}_scroll-container" />
<c:if test="${showThumbnail}">
	<config:setOption name="mainContainerId" value="${scrollContainerId}" component="${widget}"/>
</c:if>

<%--TODO reactivate option in widget.xml ?--%>
<config:getOption name="disableResultListPrefTab" var="disableResultListPrefTab" defaultValue="false" />

<plma:resultListConfig var="columnsConfig" resultListId="${resultListId}" toMap="false"/>

<c:choose>
<%--	Simply render hits in infinite scroll context--%>
	<c:when test="${isAjax && (isAnInfiniteScrollRequest || fn:length(paginationLoad) > 0)}">
		<render:template template="templates/hits.jsp" widget="plmaResultListCommons">
			<render:parameter name="feeds" value="${feeds}"/>
			<render:parameter name="widget" value="${widget}"/>
			<render:parameter name="uCssId" value="${uCssId}"/>
			<render:parameter name="columnsConfig" value="${columnsConfig}"/>
		</render:template>
		<c:if test="${search:hasEntries(feeds) && !enableInfiniteScroll}">
			<div class="pagination">
				<render:template template="templates/pagination.jsp" widget="plmaPagination">
					<render:parameter name="accessFeeds" value="${feeds}"/>
					<%--<render:parameter name="cssId" value="${cssId}"/>--%>
					<render:parameter name="wuid" value="${uCssId}"/>
				</render:template>
			</div>
		</c:if>
	</c:when>

	<c:otherwise>
		<widget:widget extraCss="plmaResultList plmaTilesResultList" varCssId="cssId" varUcssId="uCssId"
					   extraStyles="height: ${not empty containerHeight ? containerHeight.concat('px') : '100%'}">
			<c:choose>
				<%-- If widget has no Feed --%>
				<c:when test="${!search:hasFeeds(feeds)}">
					<widget:header>
						<config:getOption name="title" defaultValue=""/>
					</widget:header>
					<widget:content>
						<render:definition name="noFeeds">
							<render:parameter name="widget" value="${widget}"/>
							<render:parameter name="showSuggestion" value="true"/>
						</render:definition>
						<render:template template="templates/select-api.jsp" widget="plmaResultListCommons" />
					</widget:content>
				</c:when>

				<%-- If all feeds have no results --%>
				<c:when test="${!search:hasEntries(feeds)}">
					<widget:header extraCss="empty-result">
						<config:getOption name="title" defaultValue=""/>
					</widget:header>
					<widget:content>
						<config:getOption var="customHTMLNoResultMessage" name="customHTMLNoResultMessage"/>
						<c:choose>
							<c:when test="${not empty customHTMLNoResultMessage}">
								<div class="noresult">
									${customHTMLNoResultMessage}
								</div>
							</c:when>
							<c:otherwise>
								<config:getOption var="noResultsJspPathHit" name="noResultsJspPathHit"
												  defaultValue="/WEB-INF/jsp/commons/noResults.jsp"/>
								<render:template template="${noResultsJspPathHit}">
									<render:parameter name="accessFeeds" value="${feeds}"/>
									<render:parameter name="showSuggestion" value="true"/>
								</render:template>
							</c:otherwise>
						</c:choose>
						<render:template template="templates/select-api.jsp" widget="plmaResultListCommons" />
					</widget:content>
					<%-- /If all feeds have no results --%>
				</c:when>

				<c:otherwise>
					<div class="panels-container">
						<div class="results-panel">
							<c:if test="${displayHeader == 'true'}">
								<%-- Results --%>
								<render:template template="templates/resultListHeader.jsp" widget="plmaResultListCommons">
									<render:parameter name="feeds" value="${feeds}"/>
									<render:parameter name="columnsConfig" value="${columnsConfig}"/>
									<render:parameter name="wuid" value="${uCssId}"/>
									<render:parameter name="layoutName" value="tile-layout"/>
									<render:parameter name="disableResultListPrefTab" value="${disableResultListPrefTab}"/>
									<render:parameter name="recursiveButtonsWidgets" value=""/>
									<%-- displayButtonsContainer and plmaButton widget are displayed in hit header, not result list  --%>
									<render:parameter name="directButtonsWidgets" value="headerButtonsContainer"/>
									<render:parameter name="buttonsIconSize" value="${buttonsIconSize}"/>
									<%-- Parameters for JS creation  --%>
									<render:parameter name="ajaxSort" value="${ajaxSort}"/>
									<render:parameter name="enableExport" value="${enableExport}"/>
									<render:parameter name="channelNames" value="${channelNames}"/>
									<render:parameter name="enablePublication" value="${enablePublication}"/>
									<render:parameter name="enableSubscription" value="${enableSubscription}"/>
								</render:template>
							</c:if>

							<div id="${scrollContainerId}" class="scroll-container" style="height: 800px">
								<ul class="hits hitCustomList tile-layout" ucssId="${uCssId}">
									<render:template template="templates/hits.jsp" widget="plmaResultListCommons">
										<render:parameter name="feeds" value="${feeds}"/>
										<render:parameter name="widget" value="${widget}"/>
										<render:parameter name="uCssId" value="${uCssId}"/>
										<render:parameter name="columnsConfig" value="${columnsConfig}"/>
									</render:template>
								</ul>
								<c:if test="${search:hasEntries(feeds) == true && enableInfiniteScroll}">
                                    <div class="loading-indicator-block">
                                        <span class="label fonticon"><i18n:message code="plma.loading.results.label" text="Loading...."/></span>
                                        <span class="loading-spinner small"></span>
                                    </div>
                                </c:if>
							</div>
						</div>

						<c:if test="${search:hasEntries(feeds) == true && !enableInfiniteScroll}">
							<div class="pagination">
								<render:template template="templates/pagination.jsp" widget="plmaPagination">
									<render:parameter name="accessFeeds" value="${feeds}"/>
									<%--<render:parameter name="cssId" value="${cssId}"/>--%>
									<render:parameter name="wuid" value="${uCssId}"/>
								</render:template>
							</div>
						</c:if>
					</div>
				</c:otherwise>
			</c:choose>

			<render:renderScript position="READY">
				<string:escape var="entryUri" value="${entryUri}" escapeType="JAVASCRIPT"/>
				<search:getPaginationInfos feeds="${feeds}" varCurrentPage="currentPage" varLastPage="maxPages"/>
				<config:getOptionsComposite var="infiniteScrollExtraParameters" name="infiniteScrollExtraParameters" mapIndex="true"/>
				var extraParameters = {};
				<c:forEach items="${infiniteScrollExtraParameters}" var="infiniteScrollExtraParameter">
					extraParameters["${infiniteScrollExtraParameter.parameterName}"] = ["<string:escape value="${infiniteScrollExtraParameter.parameterValue}" escapeType="JAVASCRIPT"/>"];
				</c:forEach>
				<c:if test="${search:hasEntries(feeds) && enableInfiniteScroll}">
					new PlmaInfiniteScroll('${uCssId}', ${maxPages}, {
						feedsName:  ${plma:getKeys(feeds, '[]')},
						scrollOrientation: '${doTrasnspose? 'x' : 'y' }',
						pageInWidgetSelector: '.results-panel > .scroll-container .hits',
						getRelativeBlock: function(){
							var relativeBlock = $('.${uCssId} .results-panel > .scroll-container .hits');
							return relativeBlock;
						},
						getAbsoluteBlock: function(){
							var absoluteBlock = $('.${uCssId} .results-panel > .scroll-container');
							var customAbsoluteBlock = $("${customAbsoluteBlock}");
							absoluteBlock = (customAbsoluteBlock.length>0?customAbsoluteBlock:absoluteBlock);
							return absoluteBlock;
						},
						pxBeforeEndToTrigger: '100',
						getNewPageParams: function(){
							var hasTableView = !$($('.${uCssId} .hit .hit-table')[0]).hasClass('hidden');
							var params = {};
							params.isTableView = hasTableView;
							params = $.extend(params, PlmaResultList._PARAMS, extraParameters);
							return params;
						},
						onLoadPageSuccessEnd: function() {
							$(window).trigger(PlmaInfiniteScroll.PAGE_LOADED);
						},
                        loadingBlockSelector: '.loading-indicator-block'
					});
				</c:if>
			</render:renderScript>
		</widget:widget>
	</c:otherwise>
</c:choose>

<%-- Always execute this code (event handlers for new tiles, also tiles generated by infinite scroll) --%>
<render:renderScript position="READY">
	new HitDetailsEventsHandler('${uCssId}');
</render:renderScript>

