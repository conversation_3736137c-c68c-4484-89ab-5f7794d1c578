/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: jetty/11.0.19
 * Generated at: 2025-08-04 11:06:36 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.jsp.widgets_005fplugin_005ftmp.project.pluginWidgets.refinePanel.templates.default_;

import jakarta.servlet.*;
import jakarta.servlet.http.*;
import jakarta.servlet.jsp.*;

public final class subCategory_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_0;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_1;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_2;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_3;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_4;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_5;
private static org.apache.jasper.runtime.ProtectedFunctionMapper _jspx_fnmap_6;

static {
  _jspx_fnmap_0= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:split", org.apache.taglibs.standard.functions.Functions.class, "split", new Class[] {java.lang.String.class, java.lang.String.class});
  _jspx_fnmap_1= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:length", org.apache.taglibs.standard.functions.Functions.class, "length", new Class[] {java.lang.Object.class});
  _jspx_fnmap_2= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:getPage", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "getPage", new Class[] {java.lang.Long.class, java.lang.Long.class});
  _jspx_fnmap_3= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:suggestApplicable", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "suggestApplicable", new Class[] {com.exalead.access.feedapi.AbstractCategory.class, java.lang.reflect.Array.newInstance(java.lang.String.class,0).getClass()});
  _jspx_fnmap_4= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:toList", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "toList", new Class[] {java.lang.String.class});
  _jspx_fnmap_5= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("fn:toLowerCase", org.apache.taglibs.standard.functions.Functions.class, "toLowerCase", new Class[] {java.lang.String.class});
  _jspx_fnmap_6= org.apache.jasper.runtime.ProtectedFunctionMapper.getMapForFunction("plma:toRGB", com.exalead.cv360.searchui.view.jspapi.custom.functions.PLMAFunctions.class, "toRGB", new Class[] {java.lang.String.class, java.lang.String.class});
}

  private static final jakarta.servlet.jsp.JspFactory _jspxFactory =
          jakarta.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(13);
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/c.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/i18n.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar", Long.valueOf(1748332448319L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/list.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/string.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/url.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/search.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar", Long.valueOf(1748332448583L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar!/META-INF/fn.tld", Long.valueOf(1602854172000L));
    _jspx_dependants.put("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/plmaTags/plma.tld", Long.valueOf(1754294312028L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/request.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/map.tld", Long.valueOf(1738257872000L));
    _jspx_dependants.put("jar:file:/D:/WS/15000_PIU/webapps/360-mashup-ui/WEB-INF/lib/360-mashup-ui-core.jar!/META-INF/tags/render.tld", Long.valueOf(1738257872000L));
  }

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("jakarta.servlet");
    _jspx_imports_packages.add("jakarta.servlet.http");
    _jspx_imports_packages.add("jakarta.servlet.jsp");
    _jspx_imports_classes = null;
  }

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetMaxCount_0026_005fvar_005ffacet_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvarStatus_005fvarLevel_005fvar_005fsortMode_005fshowEmptyCategories_005froot_005fiterationMode_005fiteration_005fdrillDown;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fvar_005ffeeds_005fcategory_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fchoose;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fotherwise;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005furl_005furl_0026_005fvar_005fvalue_005fkeepQueryString_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetCategoryUrl_0026_005fvarClassName_005fvar_005fforceRefineOn_005ffeeds_005fcategory_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fplma_005fcorrectUrl_0026_005fvar_005furl_005fskipProfiles_005fpageUrl_005fkeepExtraRefinements_005fkeep_005fforceRefineOn_005ffeeds_005fdelete2DFacet_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fvar_005fname_005fcategory_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetCategoryValueType_0026_005fvar_005fname_005fcategory_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fname_005fcategory_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fstring_005fformatNumber;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fname_005fdefaultValue_005fcategory_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fsearch_005fgetCategoryUrl_0026_005fvar_005fforceState_005fforceRefineOn_005ffeeds_005fcategory_005fbaseUrl_005fnobody;
  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody;

  private volatile jakarta.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public jakarta.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetMaxCount_0026_005fvar_005ffacet_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvarStatus_005fvarLevel_005fvar_005fsortMode_005fshowEmptyCategories_005froot_005fiterationMode_005fiteration_005fdrillDown = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fvar_005ffeeds_005fcategory_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fchoose = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fc_005fotherwise = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005furl_005furl_0026_005fvar_005fvalue_005fkeepQueryString_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryUrl_0026_005fvarClassName_005fvar_005fforceRefineOn_005ffeeds_005fcategory_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fplma_005fcorrectUrl_0026_005fvar_005furl_005fskipProfiles_005fpageUrl_005fkeepExtraRefinements_005fkeep_005fforceRefineOn_005ffeeds_005fdelete2DFacet_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fvar_005fname_005fcategory_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryValueType_0026_005fvar_005fname_005fcategory_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fname_005fcategory_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fstring_005fformatNumber = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fname_005fdefaultValue_005fcategory_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryUrl_0026_005fvar_005fforceState_005fforceRefineOn_005ffeeds_005fcategory_005fbaseUrl_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
    _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.release();
    _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fgetMaxCount_0026_005fvar_005ffacet_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvarStatus_005fvarLevel_005fvar_005fsortMode_005fshowEmptyCategories_005froot_005fiterationMode_005fiteration_005fdrillDown.release();
    _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fvar_005ffeeds_005fcategory_005fnobody.release();
    _005fjspx_005ftagPool_005fc_005fchoose.release();
    _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.release();
    _005fjspx_005ftagPool_005fc_005fotherwise.release();
    _005fjspx_005ftagPool_005furl_005furl_0026_005fvar_005fvalue_005fkeepQueryString_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryUrl_0026_005fvarClassName_005fvar_005fforceRefineOn_005ffeeds_005fcategory_005fnobody.release();
    _005fjspx_005ftagPool_005fplma_005fcorrectUrl_0026_005fvar_005furl_005fskipProfiles_005fpageUrl_005fkeepExtraRefinements_005fkeep_005fforceRefineOn_005ffeeds_005fdelete2DFacet_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fvar_005fname_005fcategory_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryValueType_0026_005fvar_005fname_005fcategory_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fname_005fcategory_005fnobody.release();
    _005fjspx_005ftagPool_005fstring_005fformatNumber.release();
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fname_005fdefaultValue_005fcategory_005fnobody.release();
    _005fjspx_005ftagPool_005fsearch_005fgetCategoryUrl_0026_005fvar_005fforceState_005fforceRefineOn_005ffeeds_005fcategory_005fbaseUrl_005fnobody.release();
    _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.release();
  }

  public void _jspService(final jakarta.servlet.http.HttpServletRequest request, final jakarta.servlet.http.HttpServletResponse response)
      throws java.io.IOException, jakarta.servlet.ServletException {

    if (!jakarta.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final jakarta.servlet.jsp.PageContext pageContext;
    jakarta.servlet.http.HttpSession session = null;
    final jakarta.servlet.ServletContext application;
    final jakarta.servlet.ServletConfig config;
    jakarta.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    jakarta.servlet.jsp.JspWriter _jspx_out = null;
    jakarta.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_render_005fimport_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_render_005fimport_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fif_005f0(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fif_005f1(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_c_005fset_005f9(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f10(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_request_005fgetParameterValue_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_c_005fset_005f11(_jspx_page_context))
        return;
      out.write("\r\n");
      out.write("\r\n");
      if (_jspx_meth_plma_005fgetMaxCount_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_search_005fgetFacetLabel_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
      if (_jspx_meth_search_005fforEachCategory_005f0(_jspx_page_context))
        return;
      out.write('\r');
      out.write('\n');
    } catch (java.lang.Throwable t) {
      if (!(t instanceof jakarta.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_render_005fimport_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f0);
    try {
      _jspx_th_render_005fimport_005f0.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(13,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f0.setParameters("widget,accessFeeds,facet,categoryTemplatePath,facetConfig,uCssId,refinePanelCfg,suggestCfg");
      _jspx_th_render_005fimport_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fimport_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:import
    com.exalead.cv360.searchui.view.jspapi.render.ImportTag _jspx_th_render_005fimport_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ImportTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fimport_005f1);
    try {
      _jspx_th_render_005fimport_005f1.setJspContext(_jspx_page_context);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(14,0) name = parameters type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f1.setParameters("paginationInfo");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(14,0) name = ignore type = boolean reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fimport_005f1.setIgnore(true);
      _jspx_th_render_005fimport_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fimport_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f0_reused = false;
    try {
      _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(16,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty facetConfig}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
      if (_jspx_eval_c_005fif_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fset_005f0(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fset_005f1(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fset_005f2(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fset_005f3(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fset_005f4(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fset_005f5(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fset_005f6(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fset_005f7(_jspx_th_c_005fif_005f0, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      _jspx_th_c_005fif_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f0 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f0_reused = false;
    try {
      _jspx_th_c_005fset_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(17,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setVar("aggr");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(17,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f0.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(17,4) '${facetConfig.aggr}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${facetConfig.aggr}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f0 = _jspx_th_c_005fset_005f0.doStartTag();
      if (_jspx_th_c_005fset_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f0);
      _jspx_th_c_005fset_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f1 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f1_reused = false;
    try {
      _jspx_th_c_005fset_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(18,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setVar("isDisjunctive");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(18,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f1.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(18,4) '${facetConfig.isDisjunctive}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${facetConfig.isDisjunctive}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f1 = _jspx_th_c_005fset_005f1.doStartTag();
      if (_jspx_th_c_005fset_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f1);
      _jspx_th_c_005fset_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f2 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f2_reused = false;
    try {
      _jspx_th_c_005fset_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(19,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setVar("sortMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(19,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f2.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(19,4) '${facetConfig.sortMode}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${facetConfig.sortMode}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f2 = _jspx_th_c_005fset_005f2.doStartTag();
      if (_jspx_th_c_005fset_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f2);
      _jspx_th_c_005fset_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f3 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f3_reused = false;
    try {
      _jspx_th_c_005fset_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(20,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setVar("nbSubFacets");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(20,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f3.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(20,4) '${facetConfig.nbSubFacets}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${facetConfig.nbSubFacets}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f3 = _jspx_th_c_005fset_005f3.doStartTag();
      if (_jspx_th_c_005fset_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f3);
      _jspx_th_c_005fset_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f4 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f4_reused = false;
    try {
      _jspx_th_c_005fset_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(21,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setVar("iterMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(21,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f4.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(21,4) '${facetConfig.iterMode}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${facetConfig.iterMode}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f4 = _jspx_th_c_005fset_005f4.doStartTag();
      if (_jspx_th_c_005fset_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f4);
      _jspx_th_c_005fset_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f5 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f5_reused = false;
    try {
      _jspx_th_c_005fset_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(22,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setVar("drillDown");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(22,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f5.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(22,4) '${facetConfig.drillDown}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${facetConfig.drillDown}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f5 = _jspx_th_c_005fset_005f5.doStartTag();
      if (_jspx_th_c_005fset_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f5);
      _jspx_th_c_005fset_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f6 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f6_reused = false;
    try {
      _jspx_th_c_005fset_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(23,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setVar("displayExclude");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(23,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f6.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(23,4) '${facetConfig.displayExclude}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${facetConfig.displayExclude}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f6 = _jspx_th_c_005fset_005f6.doStartTag();
      if (_jspx_th_c_005fset_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f6);
      _jspx_th_c_005fset_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f7 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f7_reused = false;
    try {
      _jspx_th_c_005fset_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(24,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setVar("defaultView");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(24,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f7.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(24,4) '${facetConfig.defaultView}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${facetConfig.defaultView}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f7 = _jspx_th_c_005fset_005f7.doStartTag();
      if (_jspx_th_c_005fset_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f7);
      _jspx_th_c_005fset_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f1_reused = false;
    try {
      _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f1.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(27,0) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${iterMode == 'flat' && drillDown == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
      if (_jspx_eval_c_005fif_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("    ");
          if (_jspx_meth_c_005fset_005f8(_jspx_th_c_005fif_005f1, _jspx_page_context))
            return true;
          out.write('\r');
          out.write('\n');
          int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      _jspx_th_c_005fif_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f8 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f8_reused = false;
    try {
      _jspx_th_c_005fset_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(28,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f8.setVar("iterMode");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(28,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f8.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(28,4) 'drilldown'",_jsp_getExpressionFactory().createValueExpression("drilldown",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f8 = _jspx_th_c_005fset_005f8.doStartTag();
      if (_jspx_th_c_005fset_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f8);
      _jspx_th_c_005fset_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f9(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f9 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f9_reused = false;
    try {
      _jspx_th_c_005fset_005f9.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f9.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(31,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f9.setVar("categoriesDisplayed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(31,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f9.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(31,0) '${1}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${1}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f9 = _jspx_th_c_005fset_005f9.doStartTag();
      if (_jspx_th_c_005fset_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f9);
      _jspx_th_c_005fset_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f10(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f10 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f10_reused = false;
    try {
      _jspx_th_c_005fset_005f10.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f10.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(32,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f10.setVar("maxCatDepth");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(32,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f10.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(32,0) '0'",_jsp_getExpressionFactory().createValueExpression("0",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f10 = _jspx_th_c_005fset_005f10.doStartTag();
      if (_jspx_th_c_005fset_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f10);
      _jspx_th_c_005fset_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f10, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_request_005fgetParameterValue_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  request:getParameterValue
    com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag _jspx_th_request_005fgetParameterValue_005f0 = (com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag) _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.request.GetParameterValueTag.class);
    boolean _jspx_th_request_005fgetParameterValue_005f0_reused = false;
    try {
      _jspx_th_request_005fgetParameterValue_005f0.setPageContext(_jspx_page_context);
      _jspx_th_request_005fgetParameterValue_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(34,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setVar("catsearchrefine");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(34,0) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setName("catsearchrefine");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(34,0) name = defaultValue type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_request_005fgetParameterValue_005f0.setDefaultValue("");
      int[] _jspx_push_body_count_request_005fgetParameterValue_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_request_005fgetParameterValue_005f0 = _jspx_th_request_005fgetParameterValue_005f0.doStartTag();
        if (_jspx_th_request_005fgetParameterValue_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_request_005fgetParameterValue_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_request_005fgetParameterValue_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_request_005fgetParameterValue_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005frequest_005fgetParameterValue_0026_005fvar_005fname_005fdefaultValue_005fnobody.reuse(_jspx_th_request_005fgetParameterValue_005f0);
      _jspx_th_request_005fgetParameterValue_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_request_005fgetParameterValue_005f0, _jsp_getInstanceManager(), _jspx_th_request_005fgetParameterValue_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f11(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f11 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f11_reused = false;
    try {
      _jspx_th_c_005fset_005f11.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f11.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(35,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f11.setVar("catSuggests");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(35,0) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f11.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(35,0) '${fn:split(catsearchrefine, ',')}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_0),"${fn:split(catsearchrefine, ',')}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f11 = _jspx_th_c_005fset_005f11.doStartTag();
      if (_jspx_th_c_005fset_005f11.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f11);
      _jspx_th_c_005fset_005f11_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f11, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f11_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetMaxCount_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getMaxCount
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetMaxCount _jspx_th_plma_005fgetMaxCount_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetMaxCount) _005fjspx_005ftagPool_005fplma_005fgetMaxCount_0026_005fvar_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetMaxCount.class);
    boolean _jspx_th_plma_005fgetMaxCount_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetMaxCount_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetMaxCount_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(37,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetMaxCount_005f0.setVar("maxAggregationValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(37,0) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetMaxCount_005f0.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetMaxCount_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetMaxCount_005f0 = _jspx_th_plma_005fgetMaxCount_005f0.doStartTag();
        if (_jspx_th_plma_005fgetMaxCount_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetMaxCount_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetMaxCount_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetMaxCount_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetMaxCount_0026_005fvar_005ffacet_005fnobody.reuse(_jspx_th_plma_005fgetMaxCount_005f0);
      _jspx_th_plma_005fgetMaxCount_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetMaxCount_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetMaxCount_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetFacetLabel_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getFacetLabel
    com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag _jspx_th_search_005fgetFacetLabel_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag) _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetFacetLabelTag.class);
    boolean _jspx_th_search_005fgetFacetLabel_005f0_reused = false;
    try {
      _jspx_th_search_005fgetFacetLabel_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetFacetLabel_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(38,0) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetLabel_005f0.setVar("facetLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(38,0) name = facet type = com.exalead.access.feedapi.MergedFacet reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetFacetLabel_005f0.setFacet((com.exalead.access.feedapi.MergedFacet) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.MergedFacet.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetFacetLabel_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetFacetLabel_005f0 = _jspx_th_search_005fgetFacetLabel_005f0.doStartTag();
        if (_jspx_th_search_005fgetFacetLabel_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetFacetLabel_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetFacetLabel_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetFacetLabel_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetFacetLabel_0026_005fvar_005ffacet_005fnobody.reuse(_jspx_th_search_005fgetFacetLabel_005f0);
      _jspx_th_search_005fgetFacetLabel_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetFacetLabel_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetFacetLabel_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fforEachCategory_005f0(jakarta.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:forEachCategory
    com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag _jspx_th_search_005fforEachCategory_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag) _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvarStatus_005fvarLevel_005fvar_005fsortMode_005fshowEmptyCategories_005froot_005fiterationMode_005fiteration_005fdrillDown.get(com.exalead.cv360.searchui.view.jspapi.search.ForEachCategoryTag.class);
    boolean _jspx_th_search_005fforEachCategory_005f0_reused = false;
    try {
      _jspx_th_search_005fforEachCategory_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fforEachCategory_005f0.setParent(null);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(39,0) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setVar("category");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(39,0) name = varLevel type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setVarLevel("depthLevel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(39,0) name = varStatus type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setVarStatus("status");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(39,0) name = root type = com.exalead.access.feedapi.CategoryTreeContainer reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setRoot((com.exalead.access.feedapi.CategoryTreeContainer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facet}", com.exalead.access.feedapi.CategoryTreeContainer.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(39,0) name = sortMode type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setSortMode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${sortMode}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(39,0) name = iteration type = int reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setIteration(((java.lang.Integer) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${nbSubFacets}", int.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).intValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(39,0) name = iterationMode type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setIterationMode((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${iterMode}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(39,0) name = drillDown type = boolean reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setDrillDown(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${drillDown}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(39,0) name = showEmptyCategories type = boolean reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fforEachCategory_005f0.setShowEmptyCategories(false);
      int[] _jspx_push_body_count_search_005fforEachCategory_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fforEachCategory_005f0 = _jspx_th_search_005fforEachCategory_005f0.doStartTag();
        if (_jspx_eval_search_005fforEachCategory_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          do {
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_plma_005fgetCategoryLabel_005f0(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fset_005f12(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fset_005f13(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fset_005f14(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fset_005f15(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fif_005f2(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fchoose_005f0(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fchoose_005f1(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write("\r\n");
            out.write("\r\n");
            out.write("    ");
            if (_jspx_meth_c_005fif_005f3(_jspx_th_search_005fforEachCategory_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
              return true;
            out.write('\r');
            out.write('\n');
            int evalDoAfterBody = _jspx_th_search_005fforEachCategory_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
        }
        if (_jspx_th_search_005fforEachCategory_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fforEachCategory_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fforEachCategory_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fforEachCategory_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fforEachCategory_0026_005fvarStatus_005fvarLevel_005fvar_005fsortMode_005fshowEmptyCategories_005froot_005fiterationMode_005fiteration_005fdrillDown.reuse(_jspx_th_search_005fforEachCategory_005f0);
      _jspx_th_search_005fforEachCategory_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fforEachCategory_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fforEachCategory_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fgetCategoryLabel_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:getCategoryLabel
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryLabelTag _jspx_th_plma_005fgetCategoryLabel_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryLabelTag) _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fvar_005ffeeds_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.GetCategoryLabelTag.class);
    boolean _jspx_th_plma_005fgetCategoryLabel_005f0_reused = false;
    try {
      _jspx_th_plma_005fgetCategoryLabel_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fgetCategoryLabel_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(49,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryLabel_005f0.setVar("catLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(49,4) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryLabel_005f0.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(49,4) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fgetCategoryLabel_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${accessFeeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fgetCategoryLabel_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fgetCategoryLabel_005f0 = _jspx_th_plma_005fgetCategoryLabel_005f0.doStartTag();
        if (_jspx_th_plma_005fgetCategoryLabel_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fgetCategoryLabel_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fgetCategoryLabel_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fgetCategoryLabel_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fgetCategoryLabel_0026_005fvar_005ffeeds_005fcategory_005fnobody.reuse(_jspx_th_plma_005fgetCategoryLabel_005f0);
      _jspx_th_plma_005fgetCategoryLabel_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fgetCategoryLabel_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fgetCategoryLabel_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f12(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f12 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f12_reused = false;
    try {
      _jspx_th_c_005fset_005f12.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f12.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(50,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f12.setVar("catPath");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(50,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f12.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(50,4) '${fn:split(category.id, '/')}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_0),"${fn:split(category.id, '/')}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f12 = _jspx_th_c_005fset_005f12.doStartTag();
      if (_jspx_th_c_005fset_005f12.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f12);
      _jspx_th_c_005fset_005f12_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f12, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f12_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f13(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f13 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f13_reused = false;
    try {
      _jspx_th_c_005fset_005f13.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f13.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(51,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f13.setVar("catPathLength");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(51,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f13.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(51,4) '${fn:length(catPath)}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_1),"${fn:length(catPath)}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f13 = _jspx_th_c_005fset_005f13.doStartTag();
      if (_jspx_th_c_005fset_005f13.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f13);
      _jspx_th_c_005fset_005f13_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f13, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f13_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f14(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f14 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f14_reused = false;
    try {
      _jspx_th_c_005fset_005f14.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f14.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(52,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f14.setVar("catId");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(52,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f14.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(52,4) '${catPath[catPathLength - 1]}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${catPath[catPathLength - 1]}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f14 = _jspx_th_c_005fset_005f14.doStartTag();
      if (_jspx_th_c_005fset_005f14.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f14);
      _jspx_th_c_005fset_005f14_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f14, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f14_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f15(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f15 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f15_reused = false;
    try {
      _jspx_th_c_005fset_005f15.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f15.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(53,4) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f15.setVar("displayCat");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(53,4) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f15.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(53,4) '${false}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${false}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f15 = _jspx_th_c_005fset_005f15.doStartTag();
      if (_jspx_th_c_005fset_005f15.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f15);
      _jspx_th_c_005fset_005f15_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f15, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f15_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f2_reused = false;
    try {
      _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(54,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${depthLevel > maxCatDepth}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
      if (_jspx_eval_c_005fif_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fset_005f16(_jspx_th_c_005fif_005f2, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      _jspx_th_c_005fif_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f16(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f16 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f16_reused = false;
    try {
      _jspx_th_c_005fset_005f16.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f16.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(55,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f16.setVar("maxCatDepth");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(55,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f16.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(55,8) '${depthLevel}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${depthLevel}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f16 = _jspx_th_c_005fset_005f16.doStartTag();
      if (_jspx_th_c_005fset_005f16.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f16);
      _jspx_th_c_005fset_005f16_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f16, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f16_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f0 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f0_reused = false;
    try {
      _jspx_th_c_005fchoose_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      int _jspx_eval_c_005fchoose_005f0 = _jspx_th_c_005fchoose_005f0.doStartTag();
      if (_jspx_eval_c_005fchoose_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fwhen_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fotherwise_005f0(_jspx_th_c_005fchoose_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f0);
      _jspx_th_c_005fchoose_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f0 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f0_reused = false;
    try {
      _jspx_th_c_005fwhen_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(59,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${paginationInfo.uiPagination}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f0 = _jspx_th_c_005fwhen_005f0.doStartTag();
      if (_jspx_eval_c_005fwhen_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fset_005f17(_jspx_th_c_005fwhen_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f0);
      _jspx_th_c_005fwhen_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f17(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f17 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f17_reused = false;
    try {
      _jspx_th_c_005fset_005f17.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f17.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(60,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f17.setVar("facetPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(60,12) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f17.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(60,12) '${plma:getPage(categoriesDisplayed, paginationInfo.perPage)}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_2),"${plma:getPage(categoriesDisplayed, paginationInfo.perPage)}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f17 = _jspx_th_c_005fset_005f17.doStartTag();
      if (_jspx_th_c_005fset_005f17.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f17);
      _jspx_th_c_005fset_005f17_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f17, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f17_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f0 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f0_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f0.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f0);
      int _jspx_eval_c_005fotherwise_005f0 = _jspx_th_c_005fotherwise_005f0.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fset_005f18(_jspx_th_c_005fotherwise_005f0, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f0.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f0);
      _jspx_th_c_005fotherwise_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f0, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f18(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f18 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f18_reused = false;
    try {
      _jspx_th_c_005fset_005f18.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f18.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(63,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f18.setVar("facetPage");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(63,12) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f18.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(63,12) '${paginationInfo.page}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${paginationInfo.page}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f18 = _jspx_th_c_005fset_005f18.doStartTag();
      if (_jspx_th_c_005fset_005f18.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f18);
      _jspx_th_c_005fset_005f18_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f18, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f18_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f1 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f1_reused = false;
    try {
      _jspx_th_c_005fchoose_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      int _jspx_eval_c_005fchoose_005f1 = _jspx_th_c_005fchoose_005f1.doStartTag();
      if (_jspx_eval_c_005fchoose_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fwhen_005f1(_jspx_th_c_005fchoose_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fotherwise_005f1(_jspx_th_c_005fchoose_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f1);
      _jspx_th_c_005fchoose_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f1 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f1_reused = false;
    try {
      _jspx_th_c_005fwhen_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(68,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${catsearchrefine == ''}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f1 = _jspx_th_c_005fwhen_005f1.doStartTag();
      if (_jspx_eval_c_005fwhen_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fset_005f19(_jspx_th_c_005fwhen_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f1);
      _jspx_th_c_005fwhen_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f19(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f19 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f19_reused = false;
    try {
      _jspx_th_c_005fset_005f19.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f19.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(69,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f19.setVar("displayCat");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(69,12) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f19.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(69,12) '${true}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${true}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f19 = _jspx_th_c_005fset_005f19.doStartTag();
      if (_jspx_th_c_005fset_005f19.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f19);
      _jspx_th_c_005fset_005f19_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f19, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f19_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f1 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f1_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f1.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f1);
      int _jspx_eval_c_005fotherwise_005f1 = _jspx_th_c_005fotherwise_005f1.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f1 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fset_005f20(_jspx_th_c_005fotherwise_005f1, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f1.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f1);
      _jspx_th_c_005fotherwise_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f1, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f20(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f1, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f20 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f20_reused = false;
    try {
      _jspx_th_c_005fset_005f20.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f20.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f1);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(72,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f20.setVar("displayCat");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(72,12) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f20.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(72,12) '${plma:suggestApplicable(category, catSuggests)}'",_jsp_getExpressionFactory().createValueExpression(new org.apache.jasper.el.ELContextWrapper(_jspx_page_context.getELContext(),_jspx_fnmap_3),"${plma:suggestApplicable(category, catSuggests)}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f20 = _jspx_th_c_005fset_005f20.doStartTag();
      if (_jspx_th_c_005fset_005f20.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f20);
      _jspx_th_c_005fset_005f20_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f20, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f20_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_search_005fforEachCategory_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f3 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f3_reused = false;
    try {
      _jspx_th_c_005fif_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_search_005fforEachCategory_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(76,4) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${displayCat}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f3 = _jspx_th_c_005fif_005f3.doStartTag();
      if (_jspx_eval_c_005fif_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fset_005f21(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_url_005furl_005f0(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_search_005fgetCategoryUrl_005f0(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_plma_005fcorrectUrl_005f0(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("        <tr class=\"category depthLevel_");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${depthLevel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(' ');
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${status.index % 2 == 0 ? 'odd' : 'even'}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(' ');
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${className}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(' ');
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetPageClass}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(' ');
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetPage>1 && paginationInfo.uiPagination ? 'hidden' : ''}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(" in-search\"\r\n");
          out.write("            data-categorypath=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.path}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fif_005f4(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("            <td class=\"refineName\" style=\"position: relative;\">\r\n");
          out.write("                ");
          if (_jspx_meth_c_005fif_005f6(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                <div class=\"refinecontainer refinecontainer-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${catLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\">\r\n");
          out.write("                    ");
          if (_jspx_meth_render_005ftemplate_005f0(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                </div>\r\n");
          out.write("            </td>\r\n");
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fif_005f7(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fif_005f9(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("        </tr>\r\n");
          out.write("\r\n");
          out.write("        ");
          if (_jspx_meth_c_005fif_005f10(_jspx_th_c_005fif_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f3);
      _jspx_th_c_005fif_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f21(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f21 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f21_reused = false;
    try {
      _jspx_th_c_005fset_005f21.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f21.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(77,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f21.setVar("facetPageClass");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(77,8) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f21.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(77,8) 'facet-page-${facetPage}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"facet-page-${facetPage}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f21 = _jspx_th_c_005fset_005f21.doStartTag();
      if (_jspx_th_c_005fset_005f21.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f21);
      _jspx_th_c_005fset_005f21_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f21, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f21_reused);
    }
    return false;
  }

  private boolean _jspx_meth_url_005furl_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  url:url
    com.exalead.cv360.searchui.view.jspapi.url.UrlTag _jspx_th_url_005furl_005f0 = (com.exalead.cv360.searchui.view.jspapi.url.UrlTag) _005fjspx_005ftagPool_005furl_005furl_0026_005fvar_005fvalue_005fkeepQueryString_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.url.UrlTag.class);
    boolean _jspx_th_url_005furl_005f0_reused = false;
    try {
      _jspx_th_url_005furl_005f0.setPageContext(_jspx_page_context);
      _jspx_th_url_005furl_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(79,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005furl_005f0.setVar("pageUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(79,8) name = value type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005furl_005f0.setValue("");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(79,8) name = keepQueryString type = boolean reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_url_005furl_005f0.setKeepQueryString(java.lang.Boolean.valueOf("true"));
      int[] _jspx_push_body_count_url_005furl_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_url_005furl_005f0 = _jspx_th_url_005furl_005f0.doStartTag();
        if (_jspx_th_url_005furl_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_url_005furl_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_url_005furl_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_url_005furl_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005furl_005furl_0026_005fvar_005fvalue_005fkeepQueryString_005fnobody.reuse(_jspx_th_url_005furl_005f0);
      _jspx_th_url_005furl_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_url_005furl_005f0, _jsp_getInstanceManager(), _jspx_th_url_005furl_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetCategoryUrl_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getCategoryUrl
    com.exalead.cv360.searchui.view.jspapi.search.GetCategoryUrlTag _jspx_th_search_005fgetCategoryUrl_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetCategoryUrlTag) _005fjspx_005ftagPool_005fsearch_005fgetCategoryUrl_0026_005fvarClassName_005fvar_005fforceRefineOn_005ffeeds_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetCategoryUrlTag.class);
    boolean _jspx_th_search_005fgetCategoryUrl_005f0_reused = false;
    try {
      _jspx_th_search_005fgetCategoryUrl_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetCategoryUrl_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(80,8) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryUrl_005f0.setVar("categoryUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(80,8) name = varClassName type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryUrl_005f0.setVarClassName("className");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(80,8) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryUrl_005f0.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(80,8) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryUrl_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${accessFeeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(80,8) name = forceRefineOn type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryUrl_005f0.setForceRefineOn((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.forceRefineOnFeeds}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetCategoryUrl_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetCategoryUrl_005f0 = _jspx_th_search_005fgetCategoryUrl_005f0.doStartTag();
        if (_jspx_th_search_005fgetCategoryUrl_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetCategoryUrl_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetCategoryUrl_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetCategoryUrl_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetCategoryUrl_0026_005fvarClassName_005fvar_005fforceRefineOn_005ffeeds_005fcategory_005fnobody.reuse(_jspx_th_search_005fgetCategoryUrl_005f0);
      _jspx_th_search_005fgetCategoryUrl_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetCategoryUrl_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetCategoryUrl_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fcorrectUrl_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:correctUrl
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.CorrectUrlTag _jspx_th_plma_005fcorrectUrl_005f0 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.CorrectUrlTag) _005fjspx_005ftagPool_005fplma_005fcorrectUrl_0026_005fvar_005furl_005fskipProfiles_005fpageUrl_005fkeepExtraRefinements_005fkeep_005fforceRefineOn_005ffeeds_005fdelete2DFacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.CorrectUrlTag.class);
    boolean _jspx_th_plma_005fcorrectUrl_005f0_reused = false;
    try {
      _jspx_th_plma_005fcorrectUrl_005f0.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fcorrectUrl_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(82,8) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f0.setVar("categoryUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(82,8) name = url type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f0.setUrl((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(82,8) name = forceRefineOn type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f0.setForceRefineOn((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.forceRefineOnFeeds}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(82,8) name = pageUrl type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f0.setPageUrl((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(82,8) name = keep type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f0.setKeep((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.keepParameters}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(82,8) name = skipProfiles type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f0.setSkipProfiles((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:toList('refinePanel')}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_4));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(82,8) name = delete2DFacet type = java.lang.Boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f0.setDelete2DFacet(java.lang.Boolean.valueOf("true"));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(82,8) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f0.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${accessFeeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(82,8) name = keepExtraRefinements type = java.lang.Boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f0.setKeepExtraRefinements((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.keepExtraRefinements}", java.lang.Boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fcorrectUrl_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fcorrectUrl_005f0 = _jspx_th_plma_005fcorrectUrl_005f0.doStartTag();
        if (_jspx_th_plma_005fcorrectUrl_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fcorrectUrl_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fcorrectUrl_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fcorrectUrl_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fcorrectUrl_0026_005fvar_005furl_005fskipProfiles_005fpageUrl_005fkeepExtraRefinements_005fkeep_005fforceRefineOn_005ffeeds_005fdelete2DFacet_005fnobody.reuse(_jspx_th_plma_005fcorrectUrl_005f0);
      _jspx_th_plma_005fcorrectUrl_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fcorrectUrl_005f0, _jsp_getInstanceManager(), _jspx_th_plma_005fcorrectUrl_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f4 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f4_reused = false;
    try {
      _jspx_th_c_005fif_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(88,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${isDisjunctive == true}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f4 = _jspx_th_c_005fif_005f4.doStartTag();
      if (_jspx_eval_c_005fif_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                <td class=\"disjunctive\">\r\n");
          out.write("                    <input type=\"checkbox\" data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write('"');
          out.write(' ');
          if (_jspx_meth_c_005fif_005f5(_jspx_th_c_005fif_005f4, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("/>\r\n");
          out.write("                </td>\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f4);
      _jspx_th_c_005fif_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f5 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f5_reused = false;
    try {
      _jspx_th_c_005fif_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(90,69) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.state == 'REFINED'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f5 = _jspx_th_c_005fif_005f5.doStartTag();
      if (_jspx_eval_c_005fif_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("checked=\"checked\"");
          int evalDoAfterBody = _jspx_th_c_005fif_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f5);
      _jspx_th_c_005fif_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f6 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f6_reused = false;
    try {
      _jspx_th_c_005fif_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(95,16) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f6.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.enableFacetCountBars}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f6 = _jspx_th_c_005fif_005f6.doStartTag();
      if (_jspx_eval_c_005fif_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                    ");
          if (_jspx_meth_search_005fgetCategoryValue_005f0(_jspx_th_c_005fif_005f6, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                    <div class=\"aggregation-bar ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(" position-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fn:toLowerCase(refinePanelCfg.countBarPosition)}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_5));
          out.write("\"\r\n");
          out.write("                            ");
          if (_jspx_meth_c_005fchoose_005f2(_jspx_th_c_005fif_005f6, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                    ></div>\r\n");
          out.write("                ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f6);
      _jspx_th_c_005fif_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetCategoryValue_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getCategoryValue
    com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag _jspx_th_search_005fgetCategoryValue_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag) _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fvar_005fname_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag.class);
    boolean _jspx_th_search_005fgetCategoryValue_005f0_reused = false;
    try {
      _jspx_th_search_005fgetCategoryValue_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetCategoryValue_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(96,20) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValue_005f0.setVar("aggregationValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(96,20) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValue_005f0.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(96,20) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValue_005f0.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${aggr}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetCategoryValue_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetCategoryValue_005f0 = _jspx_th_search_005fgetCategoryValue_005f0.doStartTag();
        if (_jspx_th_search_005fgetCategoryValue_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetCategoryValue_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetCategoryValue_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetCategoryValue_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fvar_005fname_005fcategory_005fnobody.reuse(_jspx_th_search_005fgetCategoryValue_005f0);
      _jspx_th_search_005fgetCategoryValue_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetCategoryValue_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetCategoryValue_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f2 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f2_reused = false;
    try {
      _jspx_th_c_005fchoose_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f6);
      int _jspx_eval_c_005fchoose_005f2 = _jspx_th_c_005fchoose_005f2.doStartTag();
      if (_jspx_eval_c_005fchoose_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                ");
          if (_jspx_meth_c_005fwhen_005f2(_jspx_th_c_005fchoose_005f2, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                                ");
          if (_jspx_meth_c_005fwhen_005f3(_jspx_th_c_005fchoose_005f2, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                            ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f2);
      _jspx_th_c_005fchoose_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f2 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f2_reused = false;
    try {
      _jspx_th_c_005fwhen_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(99,32) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.countBarPosition == 'Behind' || refinePanelCfg.countBarPosition == 'Left'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f2 = _jspx_th_c_005fwhen_005f2.doStartTag();
      if (_jspx_eval_c_005fwhen_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                    style=\"width: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${aggregationValue * 100 / maxAggregationValue}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("%; background-color: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:toRGB(refinePanelCfg.countBarColor, '.5')}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_6));
          out.write("\"\r\n");
          out.write("                                ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f2);
      _jspx_th_c_005fwhen_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f3 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f3_reused = false;
    try {
      _jspx_th_c_005fwhen_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f2);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(102,32) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f3.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.countBarPosition == 'Under'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f3 = _jspx_th_c_005fwhen_005f3.doStartTag();
      if (_jspx_eval_c_005fwhen_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                    style=\"width: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${aggregationValue * 100 / maxAggregationValue}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("%; border-color: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:toRGB(refinePanelCfg.countBarColor, '.5')}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_6));
          out.write("\"\r\n");
          out.write("                                ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f3);
      _jspx_th_c_005fwhen_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_render_005ftemplate_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  render:template
    com.exalead.cv360.searchui.view.jspapi.render.TemplateTag _jspx_th_render_005ftemplate_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.TemplateTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005ftemplate_005f0);
    try {
      _jspx_th_render_005ftemplate_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005ftemplate_005f0.setParent(_jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(109,20) name = template type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setTemplate((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryTemplatePath}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(109,20) name = widget type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005ftemplate_005f0.setWidget("refinePanel");
      _jspx_th_render_005ftemplate_005f0.setJspBody(new Helper( 0, _jspx_page_context, _jspx_th_render_005ftemplate_005f0, _jspx_push_body_count_search_005fforEachCategory_005f0));
      _jspx_th_render_005ftemplate_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005ftemplate_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f0 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f0);
    try {
      _jspx_th_render_005fparameter_005f0.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f0.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(110,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setName("categoryUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(110,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f0.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryUrl}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f0.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f0);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f1 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f1);
    try {
      _jspx_th_render_005fparameter_005f1.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f1.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(111,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setName("widget");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(111,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f1.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${widget}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f1.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f1);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f2 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f2);
    try {
      _jspx_th_render_005fparameter_005f2.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f2.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(112,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setName("category");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(112,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f2.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f2.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f2);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f3 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f3);
    try {
      _jspx_th_render_005fparameter_005f3.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f3.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(113,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setName("feeds");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(113,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f3.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${accessFeeds}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f3.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f3);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f4 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f4);
    try {
      _jspx_th_render_005fparameter_005f4.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f4.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(114,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setName("facetLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(114,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f4.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetLabel}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f4.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f4);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f5 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f5);
    try {
      _jspx_th_render_005fparameter_005f5.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f5.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(115,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setName("catLabel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(115,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f5.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${catLabel}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f5.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f5);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f6 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f6);
    try {
      _jspx_th_render_005fparameter_005f6.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f6.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(116,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setName("depthLevel");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(116,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f6.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${depthLevel}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f6.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f6);
    }
    return false;
  }

  private boolean _jspx_meth_render_005fparameter_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_parent, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  render:parameter
    com.exalead.cv360.searchui.view.jspapi.render.ParameterTag _jspx_th_render_005fparameter_005f7 = new com.exalead.cv360.searchui.view.jspapi.render.ParameterTag();
    _jsp_getInstanceManager().newInstance(_jspx_th_render_005fparameter_005f7);
    try {
      _jspx_th_render_005fparameter_005f7.setJspContext(_jspx_page_context);
      _jspx_th_render_005fparameter_005f7.setParent(_jspx_parent);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(117,24) name = name type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setName("refinePanelCfg");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(117,24) name = value type = null reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_render_005fparameter_005f7.setValue((java.lang.Object) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg}", java.lang.Object.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      _jspx_th_render_005fparameter_005f7.doTag();
    } finally {
      _jsp_getInstanceManager().destroyInstance(_jspx_th_render_005fparameter_005f7);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f7(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f7 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f7_reused = false;
    try {
      _jspx_th_c_005fif_005f7.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f7.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(122,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f7.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${aggr != ''}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f7 = _jspx_th_c_005fif_005f7.doStartTag();
      if (_jspx_eval_c_005fif_005f7 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                <td class=\"count\" style=\"position: relative;\">\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fif_005f8(_jspx_th_c_005fif_005f7, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                    <div class=\"countcontainer\">\r\n");
          out.write("                        ");
          if (_jspx_meth_search_005fgetCategoryValueType_005f0(_jspx_th_c_005fif_005f7, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_c_005fchoose_005f3(_jspx_th_c_005fif_005f7, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                    </div>\r\n");
          out.write("                </td>\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f7.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f7.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f7);
      _jspx_th_c_005fif_005f7_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f7, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f7_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f8(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f8 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f8_reused = false;
    try {
      _jspx_th_c_005fif_005f8.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f8.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f7);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(124,20) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f8.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.enableFacetCountBars && refinePanelCfg.countBarPosition == 'Right'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f8 = _jspx_th_c_005fif_005f8.doStartTag();
      if (_jspx_eval_c_005fif_005f8 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_search_005fgetCategoryValue_005f1(_jspx_th_c_005fif_005f8, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                        <div class=\"aggregation-bar ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${uCssId}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write(" position-");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${fn:toLowerCase(refinePanelCfg.countBarPosition)}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_5));
          out.write("\"\r\n");
          out.write("                             style=\"width: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${aggregationValue * 100 / maxAggregationValue}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("%; background-color: ");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:toRGB(refinePanelCfg.countBarColor, '.5')}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_6));
          out.write("\">\r\n");
          out.write("                        </div>\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f8.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f8.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f8);
      _jspx_th_c_005fif_005f8_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f8, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f8_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetCategoryValue_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f8, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getCategoryValue
    com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag _jspx_th_search_005fgetCategoryValue_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag) _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fvar_005fname_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag.class);
    boolean _jspx_th_search_005fgetCategoryValue_005f1_reused = false;
    try {
      _jspx_th_search_005fgetCategoryValue_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetCategoryValue_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f8);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(125,24) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValue_005f1.setVar("aggregationValue");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(125,24) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValue_005f1.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(125,24) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValue_005f1.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${aggr}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetCategoryValue_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetCategoryValue_005f1 = _jspx_th_search_005fgetCategoryValue_005f1.doStartTag();
        if (_jspx_th_search_005fgetCategoryValue_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetCategoryValue_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetCategoryValue_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetCategoryValue_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fvar_005fname_005fcategory_005fnobody.reuse(_jspx_th_search_005fgetCategoryValue_005f1);
      _jspx_th_search_005fgetCategoryValue_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetCategoryValue_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fgetCategoryValue_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetCategoryValueType_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getCategoryValueType
    com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTypeTag _jspx_th_search_005fgetCategoryValueType_005f0 = (com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTypeTag) _005fjspx_005ftagPool_005fsearch_005fgetCategoryValueType_0026_005fvar_005fname_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTypeTag.class);
    boolean _jspx_th_search_005fgetCategoryValueType_005f0_reused = false;
    try {
      _jspx_th_search_005fgetCategoryValueType_005f0.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetCategoryValueType_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f7);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(131,24) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValueType_005f0.setVar("type");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(131,24) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValueType_005f0.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(131,24) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValueType_005f0.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${aggr}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetCategoryValueType_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetCategoryValueType_005f0 = _jspx_th_search_005fgetCategoryValueType_005f0.doStartTag();
        if (_jspx_th_search_005fgetCategoryValueType_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetCategoryValueType_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetCategoryValueType_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetCategoryValueType_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetCategoryValueType_0026_005fvar_005fname_005fcategory_005fnobody.reuse(_jspx_th_search_005fgetCategoryValueType_005f0);
      _jspx_th_search_005fgetCategoryValueType_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetCategoryValueType_005f0, _jsp_getInstanceManager(), _jspx_th_search_005fgetCategoryValueType_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f7, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f3 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f3_reused = false;
    try {
      _jspx_th_c_005fchoose_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f7);
      int _jspx_eval_c_005fchoose_005f3 = _jspx_th_c_005fchoose_005f3.doStartTag();
      if (_jspx_eval_c_005fchoose_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_c_005fwhen_005f4(_jspx_th_c_005fchoose_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_c_005fotherwise_005f2(_jspx_th_c_005fchoose_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f3);
      _jspx_th_c_005fchoose_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f4 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f4_reused = false;
    try {
      _jspx_th_c_005fwhen_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(133,28) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f4.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${type == 'STRING'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f4 = _jspx_th_c_005fwhen_005f4.doStartTag();
      if (_jspx_eval_c_005fwhen_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                ");
          if (_jspx_meth_search_005fgetCategoryValue_005f2(_jspx_th_c_005fwhen_005f4, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                            ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f4);
      _jspx_th_c_005fwhen_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetCategoryValue_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getCategoryValue
    com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag _jspx_th_search_005fgetCategoryValue_005f2 = (com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag) _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fname_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag.class);
    boolean _jspx_th_search_005fgetCategoryValue_005f2_reused = false;
    try {
      _jspx_th_search_005fgetCategoryValue_005f2.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetCategoryValue_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(134,32) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValue_005f2.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(134,32) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValue_005f2.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${aggr}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetCategoryValue_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetCategoryValue_005f2 = _jspx_th_search_005fgetCategoryValue_005f2.doStartTag();
        if (_jspx_th_search_005fgetCategoryValue_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetCategoryValue_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetCategoryValue_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetCategoryValue_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fname_005fcategory_005fnobody.reuse(_jspx_th_search_005fgetCategoryValue_005f2);
      _jspx_th_search_005fgetCategoryValue_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetCategoryValue_005f2, _jsp_getInstanceManager(), _jspx_th_search_005fgetCategoryValue_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f2 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f2_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f2.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f3);
      int _jspx_eval_c_005fotherwise_005f2 = _jspx_th_c_005fotherwise_005f2.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f2 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                                ");
          if (_jspx_meth_string_005fformatNumber_005f0(_jspx_th_c_005fotherwise_005f2, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                            ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f2.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f2);
      _jspx_th_c_005fotherwise_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f2, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_string_005fformatNumber_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f2, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  string:formatNumber
    com.exalead.cv360.searchui.view.jspapi.string.FormatNumberTag _jspx_th_string_005fformatNumber_005f0 = (com.exalead.cv360.searchui.view.jspapi.string.FormatNumberTag) _005fjspx_005ftagPool_005fstring_005fformatNumber.get(com.exalead.cv360.searchui.view.jspapi.string.FormatNumberTag.class);
    boolean _jspx_th_string_005fformatNumber_005f0_reused = false;
    try {
      _jspx_th_string_005fformatNumber_005f0.setPageContext(_jspx_page_context);
      _jspx_th_string_005fformatNumber_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f2);
      int[] _jspx_push_body_count_string_005fformatNumber_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_string_005fformatNumber_005f0 = _jspx_th_string_005fformatNumber_005f0.doStartTag();
        if (_jspx_eval_string_005fformatNumber_005f0 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
          if (_jspx_eval_string_005fformatNumber_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            _jspx_push_body_count_string_005fformatNumber_005f0[0]++;
            out = org.apache.jasper.runtime.JspRuntimeLibrary.startBufferedBody(_jspx_page_context, _jspx_th_string_005fformatNumber_005f0);
          }
          do {
            out.write("\r\n");
            out.write("                                    ");
            if (_jspx_meth_search_005fgetCategoryValue_005f3(_jspx_th_string_005fformatNumber_005f0, _jspx_page_context, _jspx_push_body_count_string_005fformatNumber_005f0))
              return true;
            out.write("\r\n");
            out.write("                                ");
            int evalDoAfterBody = _jspx_th_string_005fformatNumber_005f0.doAfterBody();
            if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
              break;
          } while (true);
          if (_jspx_eval_string_005fformatNumber_005f0 != jakarta.servlet.jsp.tagext.Tag.EVAL_BODY_INCLUDE) {
            out = _jspx_page_context.popBody();
            _jspx_push_body_count_string_005fformatNumber_005f0[0]--;
          }
        }
        if (_jspx_th_string_005fformatNumber_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_string_005fformatNumber_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_string_005fformatNumber_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_string_005fformatNumber_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fstring_005fformatNumber.reuse(_jspx_th_string_005fformatNumber_005f0);
      _jspx_th_string_005fformatNumber_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_string_005fformatNumber_005f0, _jsp_getInstanceManager(), _jspx_th_string_005fformatNumber_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetCategoryValue_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_string_005fformatNumber_005f0, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_string_005fformatNumber_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getCategoryValue
    com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag _jspx_th_search_005fgetCategoryValue_005f3 = (com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag) _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fname_005fdefaultValue_005fcategory_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetCategoryValueTag.class);
    boolean _jspx_th_search_005fgetCategoryValue_005f3_reused = false;
    try {
      _jspx_th_search_005fgetCategoryValue_005f3.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetCategoryValue_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_string_005fformatNumber_005f0);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(138,36) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValue_005f3.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(138,36) name = name type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValue_005f3.setName((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${aggr}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(138,36) name = defaultValue type = java.lang.Object reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryValue_005f3.setDefaultValue("0");
      int[] _jspx_push_body_count_search_005fgetCategoryValue_005f3 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetCategoryValue_005f3 = _jspx_th_search_005fgetCategoryValue_005f3.doStartTag();
        if (_jspx_th_search_005fgetCategoryValue_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetCategoryValue_005f3[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetCategoryValue_005f3.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetCategoryValue_005f3.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetCategoryValue_0026_005fname_005fdefaultValue_005fcategory_005fnobody.reuse(_jspx_th_search_005fgetCategoryValue_005f3);
      _jspx_th_search_005fgetCategoryValue_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetCategoryValue_005f3, _jsp_getInstanceManager(), _jspx_th_search_005fgetCategoryValue_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f9(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f9 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f9_reused = false;
    try {
      _jspx_th_c_005fif_005f9.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f9.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(147,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f9.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${displayExclude == 'true'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f9 = _jspx_th_c_005fif_005f9.doStartTag();
      if (_jspx_eval_c_005fif_005f9 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                <td class=\"exclude\">\r\n");
          out.write("                    ");
          if (_jspx_meth_c_005fchoose_005f4(_jspx_th_c_005fif_005f9, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                </td>\r\n");
          out.write("            ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f9.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f9.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f9);
      _jspx_th_c_005fif_005f9_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f9, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f9_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fchoose_005f4(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f9, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:choose
    org.apache.taglibs.standard.tag.common.core.ChooseTag _jspx_th_c_005fchoose_005f4 = (org.apache.taglibs.standard.tag.common.core.ChooseTag) _005fjspx_005ftagPool_005fc_005fchoose.get(org.apache.taglibs.standard.tag.common.core.ChooseTag.class);
    boolean _jspx_th_c_005fchoose_005f4_reused = false;
    try {
      _jspx_th_c_005fchoose_005f4.setPageContext(_jspx_page_context);
      _jspx_th_c_005fchoose_005f4.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f9);
      int _jspx_eval_c_005fchoose_005f4 = _jspx_th_c_005fchoose_005f4.doStartTag();
      if (_jspx_eval_c_005fchoose_005f4 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_c_005fwhen_005f5(_jspx_th_c_005fchoose_005f4, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_c_005fwhen_005f6(_jspx_th_c_005fchoose_005f4, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                        ");
          if (_jspx_meth_c_005fotherwise_005f3(_jspx_th_c_005fchoose_005f4, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                    ");
          int evalDoAfterBody = _jspx_th_c_005fchoose_005f4.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fchoose_005f4.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fchoose.reuse(_jspx_th_c_005fchoose_005f4);
      _jspx_th_c_005fchoose_005f4_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fchoose_005f4, _jsp_getInstanceManager(), _jspx_th_c_005fchoose_005f4_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f5(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f5 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f5_reused = false;
    try {
      _jspx_th_c_005fwhen_005f5.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f5.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(150,24) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f5.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${className == 'displayed'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f5 = _jspx_th_c_005fwhen_005f5.doStartTag();
      if (_jspx_eval_c_005fwhen_005f5 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                            ");
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_search_005fgetCategoryUrl_005f1(_jspx_th_c_005fwhen_005f5, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                            ");
          if (_jspx_meth_plma_005fcorrectUrl_005f1(_jspx_th_c_005fwhen_005f5, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("                            <a data-label=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${catLabel}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" data-categoryid=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category.id}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" class=\"refine-link exclude\"\r\n");
          out.write("                               title=\"");
          if (_jspx_meth_i18n_005fmessage_005f0(_jspx_th_c_005fwhen_005f5, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\"><span class=\"fonticon fonticon-block\"></span></a>\r\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f5.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f5.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f5);
      _jspx_th_c_005fwhen_005f5_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f5, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f5_reused);
    }
    return false;
  }

  private boolean _jspx_meth_search_005fgetCategoryUrl_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  search:getCategoryUrl
    com.exalead.cv360.searchui.view.jspapi.search.GetCategoryUrlTag _jspx_th_search_005fgetCategoryUrl_005f1 = (com.exalead.cv360.searchui.view.jspapi.search.GetCategoryUrlTag) _005fjspx_005ftagPool_005fsearch_005fgetCategoryUrl_0026_005fvar_005fforceState_005fforceRefineOn_005ffeeds_005fcategory_005fbaseUrl_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.search.GetCategoryUrlTag.class);
    boolean _jspx_th_search_005fgetCategoryUrl_005f1_reused = false;
    try {
      _jspx_th_search_005fgetCategoryUrl_005f1.setPageContext(_jspx_page_context);
      _jspx_th_search_005fgetCategoryUrl_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(152,28) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryUrl_005f1.setVar("categoryUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(152,28) name = baseUrl type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryUrl_005f1.setBaseUrl((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${facetPageName}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(152,28) name = category type = com.exalead.access.feedapi.AbstractCategory reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryUrl_005f1.setCategory((com.exalead.access.feedapi.AbstractCategory) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${category}", com.exalead.access.feedapi.AbstractCategory.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(152,28) name = forceState type = java.lang.String reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryUrl_005f1.setForceState("EXCLUDED");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(152,28) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryUrl_005f1.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${accessFeeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(152,28) name = forceRefineOn type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_search_005fgetCategoryUrl_005f1.setForceRefineOn((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.forceRefineOnFeeds}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_search_005fgetCategoryUrl_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_search_005fgetCategoryUrl_005f1 = _jspx_th_search_005fgetCategoryUrl_005f1.doStartTag();
        if (_jspx_th_search_005fgetCategoryUrl_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_search_005fgetCategoryUrl_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_search_005fgetCategoryUrl_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_search_005fgetCategoryUrl_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fsearch_005fgetCategoryUrl_0026_005fvar_005fforceState_005fforceRefineOn_005ffeeds_005fcategory_005fbaseUrl_005fnobody.reuse(_jspx_th_search_005fgetCategoryUrl_005f1);
      _jspx_th_search_005fgetCategoryUrl_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_search_005fgetCategoryUrl_005f1, _jsp_getInstanceManager(), _jspx_th_search_005fgetCategoryUrl_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_plma_005fcorrectUrl_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  plma:correctUrl
    com.exalead.cv360.searchui.view.jspapi.custom.taglib.CorrectUrlTag _jspx_th_plma_005fcorrectUrl_005f1 = (com.exalead.cv360.searchui.view.jspapi.custom.taglib.CorrectUrlTag) _005fjspx_005ftagPool_005fplma_005fcorrectUrl_0026_005fvar_005furl_005fskipProfiles_005fpageUrl_005fkeepExtraRefinements_005fkeep_005fforceRefineOn_005ffeeds_005fdelete2DFacet_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.custom.taglib.CorrectUrlTag.class);
    boolean _jspx_th_plma_005fcorrectUrl_005f1_reused = false;
    try {
      _jspx_th_plma_005fcorrectUrl_005f1.setPageContext(_jspx_page_context);
      _jspx_th_plma_005fcorrectUrl_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(154,28) name = var type = java.lang.String reqTime = false required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f1.setVar("categoryUrl");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(154,28) name = url type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f1.setUrl((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(154,28) name = forceRefineOn type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f1.setForceRefineOn((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.forceRefineOnFeeds}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(154,28) name = pageUrl type = java.lang.String reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f1.setPageUrl((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(154,28) name = keep type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f1.setKeep((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.keepParameters}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(154,28) name = skipProfiles type = java.util.List reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f1.setSkipProfiles((java.util.List) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${plma:toList('refinePanel')}", java.util.List.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, _jspx_fnmap_4));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(154,28) name = delete2DFacet type = java.lang.Boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f1.setDelete2DFacet(java.lang.Boolean.valueOf("true"));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(154,28) name = feeds type = java.util.Map reqTime = true required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f1.setFeeds((java.util.Map) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${accessFeeds}", java.util.Map.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(154,28) name = keepExtraRefinements type = java.lang.Boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_plma_005fcorrectUrl_005f1.setKeepExtraRefinements((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${refinePanelCfg.keepExtraRefinements}", java.lang.Boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
      int[] _jspx_push_body_count_plma_005fcorrectUrl_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_plma_005fcorrectUrl_005f1 = _jspx_th_plma_005fcorrectUrl_005f1.doStartTag();
        if (_jspx_th_plma_005fcorrectUrl_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_plma_005fcorrectUrl_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_plma_005fcorrectUrl_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_plma_005fcorrectUrl_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fplma_005fcorrectUrl_0026_005fvar_005furl_005fskipProfiles_005fpageUrl_005fkeepExtraRefinements_005fkeep_005fforceRefineOn_005ffeeds_005fdelete2DFacet_005fnobody.reuse(_jspx_th_plma_005fcorrectUrl_005f1);
      _jspx_th_plma_005fcorrectUrl_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_plma_005fcorrectUrl_005f1, _jsp_getInstanceManager(), _jspx_th_plma_005fcorrectUrl_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f0(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f5, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f0 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f0_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f0.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f0.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f5);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(158,38) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f0.setCode("widgets.refinePanel.exclude");
      int[] _jspx_push_body_count_i18n_005fmessage_005f0 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f0 = _jspx_th_i18n_005fmessage_005f0.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f0.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f0[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f0.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f0.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f0);
      _jspx_th_i18n_005fmessage_005f0_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f0, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f0_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fwhen_005f6(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:when
    org.apache.taglibs.standard.tag.rt.core.WhenTag _jspx_th_c_005fwhen_005f6 = (org.apache.taglibs.standard.tag.rt.core.WhenTag) _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.WhenTag.class);
    boolean _jspx_th_c_005fwhen_005f6_reused = false;
    try {
      _jspx_th_c_005fwhen_005f6.setPageContext(_jspx_page_context);
      _jspx_th_c_005fwhen_005f6.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f4);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(160,24) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fwhen_005f6.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${className == 'refined'}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fwhen_005f6 = _jspx_th_c_005fwhen_005f6.doStartTag();
      if (_jspx_eval_c_005fwhen_005f6 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                            ");
          out.write("\r\n");
          out.write("                            <a data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" class=\"refine-link cancel\" title=\"");
          if (_jspx_meth_i18n_005fmessage_005f1(_jspx_th_c_005fwhen_005f6, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\"><span\r\n");
          out.write("                                    class=\"fonticon fonticon-cancel\"></span></a>\r\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fwhen_005f6.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fwhen_005f6.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fwhen_0026_005ftest.reuse(_jspx_th_c_005fwhen_005f6);
      _jspx_th_c_005fwhen_005f6_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fwhen_005f6, _jsp_getInstanceManager(), _jspx_th_c_005fwhen_005f6_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f1(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fwhen_005f6, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f1 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f1_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f1.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f1.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fwhen_005f6);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(162,91) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f1.setCode("rangeselector.cancelFilter");
      int[] _jspx_push_body_count_i18n_005fmessage_005f1 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f1 = _jspx_th_i18n_005fmessage_005f1.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f1.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f1[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f1.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f1.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f1);
      _jspx_th_i18n_005fmessage_005f1_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f1, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f1_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fotherwise_005f3(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fchoose_005f4, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:otherwise
    org.apache.taglibs.standard.tag.common.core.OtherwiseTag _jspx_th_c_005fotherwise_005f3 = (org.apache.taglibs.standard.tag.common.core.OtherwiseTag) _005fjspx_005ftagPool_005fc_005fotherwise.get(org.apache.taglibs.standard.tag.common.core.OtherwiseTag.class);
    boolean _jspx_th_c_005fotherwise_005f3_reused = false;
    try {
      _jspx_th_c_005fotherwise_005f3.setPageContext(_jspx_page_context);
      _jspx_th_c_005fotherwise_005f3.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fchoose_005f4);
      int _jspx_eval_c_005fotherwise_005f3 = _jspx_th_c_005fotherwise_005f3.doStartTag();
      if (_jspx_eval_c_005fotherwise_005f3 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("                            ");
          out.write("\r\n");
          out.write("                            <a data-url=\"");
          out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${categoryUrl}", java.lang.String.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null));
          out.write("\" class=\"refine-link cancel\" title=\"");
          if (_jspx_meth_i18n_005fmessage_005f2(_jspx_th_c_005fotherwise_005f3, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\"><span\r\n");
          out.write("                                    class=\"fonticon fonticon-cancel\"></span></a>\r\n");
          out.write("                        ");
          int evalDoAfterBody = _jspx_th_c_005fotherwise_005f3.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fotherwise_005f3.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fotherwise.reuse(_jspx_th_c_005fotherwise_005f3);
      _jspx_th_c_005fotherwise_005f3_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fotherwise_005f3, _jsp_getInstanceManager(), _jspx_th_c_005fotherwise_005f3_reused);
    }
    return false;
  }

  private boolean _jspx_meth_i18n_005fmessage_005f2(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fotherwise_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  i18n:message
    com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag _jspx_th_i18n_005fmessage_005f2 = (com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag) _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.get(com.exalead.cv360.searchui.view.jspapi.i18n.MessageTag.class);
    boolean _jspx_th_i18n_005fmessage_005f2_reused = false;
    try {
      _jspx_th_i18n_005fmessage_005f2.setPageContext(_jspx_page_context);
      _jspx_th_i18n_005fmessage_005f2.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fotherwise_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(167,91) name = code type = null reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_i18n_005fmessage_005f2.setCode("rangeselector.cancelFilter");
      int[] _jspx_push_body_count_i18n_005fmessage_005f2 = new int[] { 0 };
      try {
        int _jspx_eval_i18n_005fmessage_005f2 = _jspx_th_i18n_005fmessage_005f2.doStartTag();
        if (_jspx_th_i18n_005fmessage_005f2.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
          return true;
        }
      } catch (java.lang.Throwable _jspx_exception) {
        while (_jspx_push_body_count_i18n_005fmessage_005f2[0]-- > 0)
          out = _jspx_page_context.popBody();
        _jspx_th_i18n_005fmessage_005f2.doCatch(_jspx_exception);
      } finally {
        _jspx_th_i18n_005fmessage_005f2.doFinally();
      }
      _005fjspx_005ftagPool_005fi18n_005fmessage_0026_005fcode_005fnobody.reuse(_jspx_th_i18n_005fmessage_005f2);
      _jspx_th_i18n_005fmessage_005f2_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_i18n_005fmessage_005f2, _jsp_getInstanceManager(), _jspx_th_i18n_005fmessage_005f2_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f10(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f3, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    jakarta.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f10 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    boolean _jspx_th_c_005fif_005f10_reused = false;
    try {
      _jspx_th_c_005fif_005f10.setPageContext(_jspx_page_context);
      _jspx_th_c_005fif_005f10.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f3);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(175,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fif_005f10.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${paginationInfo.uiPagination}", boolean.class, (jakarta.servlet.jsp.PageContext)_jspx_page_context, null)).booleanValue());
      int _jspx_eval_c_005fif_005f10 = _jspx_th_c_005fif_005f10.doStartTag();
      if (_jspx_eval_c_005fif_005f10 != jakarta.servlet.jsp.tagext.Tag.SKIP_BODY) {
        do {
          out.write("\r\n");
          out.write("            ");
          if (_jspx_meth_c_005fset_005f22(_jspx_th_c_005fif_005f10, _jspx_page_context, _jspx_push_body_count_search_005fforEachCategory_005f0))
            return true;
          out.write("\r\n");
          out.write("        ");
          int evalDoAfterBody = _jspx_th_c_005fif_005f10.doAfterBody();
          if (evalDoAfterBody != jakarta.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
            break;
        } while (true);
      }
      if (_jspx_th_c_005fif_005f10.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f10);
      _jspx_th_c_005fif_005f10_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fif_005f10, _jsp_getInstanceManager(), _jspx_th_c_005fif_005f10_reused);
    }
    return false;
  }

  private boolean _jspx_meth_c_005fset_005f22(jakarta.servlet.jsp.tagext.JspTag _jspx_th_c_005fif_005f10, jakarta.servlet.jsp.PageContext _jspx_page_context, int[] _jspx_push_body_count_search_005fforEachCategory_005f0)
          throws java.lang.Throwable {
    jakarta.servlet.jsp.PageContext pageContext = _jspx_page_context;
    //  c:set
    org.apache.taglibs.standard.tag.rt.core.SetTag _jspx_th_c_005fset_005f22 = (org.apache.taglibs.standard.tag.rt.core.SetTag) _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.get(org.apache.taglibs.standard.tag.rt.core.SetTag.class);
    boolean _jspx_th_c_005fset_005f22_reused = false;
    try {
      _jspx_th_c_005fset_005f22.setPageContext(_jspx_page_context);
      _jspx_th_c_005fset_005f22.setParent((jakarta.servlet.jsp.tagext.Tag) _jspx_th_c_005fif_005f10);
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(176,12) name = var type = java.lang.String reqTime = false required = false fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f22.setVar("categoriesDisplayed");
      // /WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(176,12) name = value type = jakarta.el.ValueExpression reqTime = true required = false fragment = false deferredValue = true expectedTypeName = java.lang.Object deferredMethod = false methodSignature = null
      _jspx_th_c_005fset_005f22.setValue(new org.apache.jasper.el.JspValueExpression("/WEB-INF/jsp/widgets_plugin_tmp/project/pluginWidgets/refinePanel/templates/default/subCategory.jsp(176,12) '${categoriesDisplayed +1}'",_jsp_getExpressionFactory().createValueExpression(_jspx_page_context.getELContext(),"${categoriesDisplayed +1}",java.lang.Object.class)).getValue(_jspx_page_context.getELContext()));
      int _jspx_eval_c_005fset_005f22 = _jspx_th_c_005fset_005f22.doStartTag();
      if (_jspx_th_c_005fset_005f22.doEndTag() == jakarta.servlet.jsp.tagext.Tag.SKIP_PAGE) {
        return true;
      }
      _005fjspx_005ftagPool_005fc_005fset_0026_005fvar_005fvalue_005fnobody.reuse(_jspx_th_c_005fset_005f22);
      _jspx_th_c_005fset_005f22_reused = true;
    } finally {
      org.apache.jasper.runtime.JspRuntimeLibrary.releaseTag(_jspx_th_c_005fset_005f22, _jsp_getInstanceManager(), _jspx_th_c_005fset_005f22_reused);
    }
    return false;
  }

  private class Helper
      extends org.apache.jasper.runtime.JspFragmentHelper
  {
    private jakarta.servlet.jsp.tagext.JspTag _jspx_parent;
    private int[] _jspx_push_body_count;

    public Helper( int discriminator, jakarta.servlet.jsp.JspContext jspContext, jakarta.servlet.jsp.tagext.JspTag _jspx_parent, int[] _jspx_push_body_count ) {
      super( discriminator, jspContext, _jspx_parent );
      this._jspx_parent = _jspx_parent;
      this._jspx_push_body_count = _jspx_push_body_count;
    }
    public boolean invoke0( jakarta.servlet.jsp.JspWriter out ) 
      throws java.lang.Throwable
    {
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f0(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f1(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f2(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f3(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f4(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f5(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f6(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                        ");
      if (_jspx_meth_render_005fparameter_005f7(_jspx_parent, _jspx_page_context, _jspx_push_body_count))
        return true;
      out.write("\r\n");
      out.write("                    ");
      return false;
    }
    public void invoke( java.io.Writer writer )
      throws jakarta.servlet.jsp.JspException
    {
      jakarta.servlet.jsp.JspWriter out = null;
      if( writer != null ) {
        out = this.jspContext.pushBody(writer);
      } else {
        out = this.jspContext.getOut();
      }
      try {
        Object _jspx_saved_JspContext = this.jspContext.getELContext().getContext(jakarta.servlet.jsp.JspContext.class);
        this.jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,this.jspContext);
        switch( this.discriminator ) {
          case 0:
            invoke0( out );
            break;
        }
        jspContext.getELContext().putContext(jakarta.servlet.jsp.JspContext.class,_jspx_saved_JspContext);
      }
      catch( java.lang.Throwable e ) {
        if (e instanceof jakarta.servlet.jsp.SkipPageException)
            throw (jakarta.servlet.jsp.SkipPageException) e;
        throw new jakarta.servlet.jsp.JspException( e );
      }
      finally {
        if( writer != null ) {
          this.jspContext.popBody();
        }
      }
    }
  }
}
