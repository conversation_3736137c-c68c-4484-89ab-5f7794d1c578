<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>

<render:import ignore="true" parameters="feed,feeds,filters"/>

<%-- Check if there is an active filter before rendering the section  --%>
<c:set var="nbFilters" value="${fn:length(filters)}" />
<%-- This updates the list of filters --%>
<c:set var="filterSection">
    <render:template template="templates/filterSection.jsp">
        <render:parameter name="feed" value="${feed}"/>
        <render:parameter name="feeds" value="${feeds}"/>
        <render:parameter name="filterType" value="SEARCH"/>
        <render:parameter name="filters" value="${filters}"/>
    </render:template>
</c:set>
<%-- If the number of filters has changed, render the section --%>
<c:if test="${fn:length(filters) > nbFilters}">
    <div class="filterType"><i18n:message code="widgets.activeFilters.section.search" text="search"/></div>
    <div class="activeFiltersContainer">
        ${filterSection}
    </div>
</c:if>

<%-- Check if there is an active filter before rendering the section  --%>
<c:set var="nbFilters" value="${fn:length(filters)}" />
<%-- This updates the list of filters --%>
<c:set var="filterSection">
    <render:template template="templates/filterSection.jsp">
        <render:parameter name="feed" value="${feed}"/>
        <render:parameter name="feeds" value="${feeds}"/>
        <render:parameter name="filterType" value="REFINED"/>
        <render:parameter name="filters" value="${filters}"/>
    </render:template>
</c:set>
<%-- If the number of filters has changed, render the section --%>
<c:if test="${fn:length(filters) > nbFilters}">
    <div class="filterType"><i18n:message code="widgets.activeFilters.section.selected" text="selected"/></div>
    <div class="activeFiltersContainer">
        ${filterSection}
    </div>
</c:if>

<%-- Check if there is an active filter before rendering the section  --%>
<c:set var="nbFilters" value="${fn:length(filters)}" />
<%-- This updates the list of filters --%>
<c:set var="filterSection">
    <render:template template="templates/filterSection.jsp">
        <render:parameter name="feed" value="${feed}"/>
        <render:parameter name="feeds" value="${feeds}"/>
        <render:parameter name="filterType" value="EXCLUDED"/>
        <render:parameter name="filters" value="${filters}"/>
    </render:template>
</c:set>

<%-- Check if there is an active filter before rendering the section  --%>
<c:set var="nbFilters" value="${fn:length(filters)}" />
<%-- This updates the list of filters --%>
<c:set var="filterSection">
    <render:template template="templates/filterSection.jsp">
        <render:parameter name="feed" value="${feed}"/>
        <render:parameter name="feeds" value="${feeds}"/>
        <render:parameter name="filterType" value="FROM_URL"/>
        <render:parameter name="filters" value="${filters}"/>
    </render:template>
</c:set>
<%-- If the number of filters has changed, render the section --%>
<c:if test="${fn:length(filters) > nbFilters}">
    <div class="filterType"><i18n:message code="widgets.activeFilters.section.applied" text="applied"/></div>
    <div class="activeFiltersContainer">
        ${filterSection}
    </div>
</c:if>

<render:template template="templates/filterSection.jsp">
    <render:parameter name="feed" value="${feed}"/>
    <render:parameter name="feeds" value="${feeds}"/>
    <render:parameter name="filterType" value="DATE"/>
    <render:parameter name="filters" value="${filters}"/>
</render:template>


