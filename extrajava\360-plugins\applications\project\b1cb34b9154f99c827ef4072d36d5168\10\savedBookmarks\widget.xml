<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Saved Bookmarks - Deprecated" group="PLM Analytics/Bookmarks" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>A widget to display saved bookmarks.</Description>
		
	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO" />
	<SupportI18N supported="true" >
		<JsKeys>
			<JsKey>widgets.savedBookmarks.noBookmark</JsKey>
			<JsKey>widgets.savedBookmarks.openBookmark</JsKey>
			<JsKey>widgets.savedBookmarks.editParam</JsKey>
			<JsKey>widgets.savedBookmarks.showDetails</JsKey>
			<JsKey>widgets.savedBookmarks.hideDetails</JsKey>
		</JsKeys>
	</SupportI18N> 


	<Dependencies>
		<Trigger position="self" className="com.exalead.cv360.searchui.triggers.impl.RemoveIfNotLoggedIn" />
		<Widget name="plmaResources" />
	</Dependencies>

	<Includes>
		<Include type="js" path="/resources/javascript/storage/storage-client-0.2.js" />
		<Include type="js" path="../plmaResources/js/lodash.min.js"/>
		<Include type="js" path="../plmaResources/js/lightbox.js"/>
		<Include type="js" path="js/savedBookmarks.js" />
		<Include type="js" path="../plmaResources/js/i18nClient.js" />
		<Include type="css" path="css/style.less" />
		<Include type="css" path="../plmaResources/css/lightbox.less"/>
		<Include type="css" path="../activeFilters/css/style.less"/>
	</Includes>
	
	<OptionsGroup name="General">
		<Option id="title" name="Widget title" arity="ZERO_OR_ONE" isEvaluated="true">
			<Description>Widget title. If blank, 'Bookmarks' is displayed.</Description>
		</Option>
		<Option id="dbKey" name="Storage database key" arity="ONE">
			<Description>Defines the key used in the storage service to store bookmarks</Description>
		</Option>
	</OptionsGroup>

	<OptionsGroup name="Advanced">
		<Option id="lightboxContainer" name="Lightbox container" arity="ZERO_OR_ONE">
			<Description>Lightbox container. If blank, 'mainWrapper' is selected. Use example : "#mainWrapper"</Description>
			<Placeholder>#mainWrapper</Placeholder>
		</Option>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="title">${i18n['widgets.savedBookmarks.title']}</DefaultValue>
		<DefaultValue name="dbKey">saved_views[]</DefaultValue>
		<DefaultValue name="lightboxContainer">#mainWrapper</DefaultValue>
	</DefaultValues>

</Widget>
