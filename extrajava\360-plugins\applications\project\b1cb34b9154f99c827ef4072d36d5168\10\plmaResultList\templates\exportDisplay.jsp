<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>

<render:import parameters="feeds"/>
<render:import parameters="columnsConfig, uCssId" ignore="true"/>

<config:getOption var="numHits" name="numHits" />
<config:getOption var="exportMode" name="exportMode" />
<config:getOption var="exportPerPage" name="exportPerPage" />
<config:getOption var="exportEncoding" name="exportEncoding" isUrlEncoded="true" isHtmlEscaped="true"/>
<config:getOption var="exportSeparator" name="exportSeparator" isUrlEncoded="true" isHtmlEscaped="true"/>
<config:getOption var="recordDelimiter" name="recordDelimiter" defaultValue="Default"/>
<config:getOption var="fileName" name="fileName" defaultValue="export" isHtmlEscaped="true"/>
<config:getOption var="addBOM" name="addBOM" defaultValue="false" />
<config:getOption var="exportIcon" name="exportIcon" defaultValue="fonticon-download" />
<config:getOption var="excludeMetas" name="excludeMetas" defaultValue="None"/>
<c:set var="parameterSeparator" value="~~~~~" />

<span class="exportContainer">
    <url:url var="exportUrl" value="../c/exportdata/batch" keepQueryString="true"/>
    <search:getPageFeed var="pageFeed"/>
    <search:getFeed var="feed" feeds="${feeds}"/>
    <search:getFeedInfo var="sapiquery" name="searchAPIUrl" feed="${feed}"/>
    <string:escape var="sapiquery" value="${sapiquery}" escapeType="HTML"/>

    <form id="${uCssId}-export-data" class="export-data-form" action="${exportUrl}" autocomplete="off" method="post" >
        <input type="hidden" name="filename" value="${fileName}">
        <input type="hidden" name="page" value="${pageFeed.id}">
        <input type="hidden" name="feedname" value="${feed.id}">
        <input type="hidden" name="hf" value="${numHits}">
        <input type="hidden" name="exportFormatParams" value="${exportSeparator}${parameterSeparator}${exportEncoding}${parameterSeparator}${recordDelimiter}">
        <c:forEach var="colConfig" items="${columnsConfig}">
            <c:if test="${colConfig.isShown || colConfig.isShown == 'ifNotEmpty' || colConfig.isShown == 'titleConfig'}">
                <string:eval var="columnLabel" string="${colConfig.label}" feeds="${feeds}"/>
                <c:choose>
                    <c:when test="${not empty colConfig.exportValue}">
                        <c:set var="paramValue" >${columnLabel}${parameterSeparator}${colConfig.exportValue}</c:set>
                    </c:when>
                    <c:otherwise>
                        <c:set var="paramValue" >${columnLabel}${parameterSeparator}<c:out value="$"/>{entry.metas['${colConfig.id}'].values}</c:set>
                    </c:otherwise>
                </c:choose>
                <string:escape var="paramValue" value="${paramValue}" escapeType="HTML"/>
                <input type="hidden" name="${not fn:contains(excludeMetas, colConfig.meta) ? 'columns' : 'no-export'}" class="column-data" value="${paramValue}">
            </c:if>
        </c:forEach>
        <input type="hidden" name="sapiquery" value="${fn:replace(sapiquery, 'of=flea', '')}">
        <input type="hidden" name="addBOM" value="${addBOM}" />
        <plma:signature var="sapiSign" value="${fn:replace(sapiquery, 'of=flea', '')}"/>
        <input type="hidden" name="sapiSign" value="${sapiSign}" />
        <render:csrf />
    </form>

    <i18n:message var="exportTooltip" code="plma.resultList.export" text="Export"/>

    <div class="plmaResultListExport" title="${exportTooltip}">
        <i class="fonticon ${exportIcon}"></i>
    </div>
</span>

