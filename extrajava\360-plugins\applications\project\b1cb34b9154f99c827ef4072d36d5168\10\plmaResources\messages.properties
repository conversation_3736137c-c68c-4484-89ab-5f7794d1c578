noHitSelected=No hit selected.
noData=This list does not contain any result.
widget.action.close=Close
widget.action.fullScreen=Full screen
widget.action.exitFullScreen=Exit full screen
statusCode.description.403=You have been logged out. Please refresh the page.
plma.selectapi.selsearch.history.label=Before Selection Search
plma.exportto3dspace.title=Share to platform
plma.exportto3dspace.adddocchkbox.label=Add New Document
plma.exportto3dspace.exportbutton.label=Export
plma.exportto3dspace.drop.label=Drop 3DSpace object
plma.exportto3dspace.publish.label= Publish
plma.exportto3dspace.docinfo.label=Document Information
plma.exportto3dspace.fileinfo.label=File Information
plma.exportto3dspace.addfilechkbox.label=Add New File
plma.exportto3dspace.nameinput.label=Name
plma.exportto3dspace.nameinput.placeHolder=Auto Name
plma.exportto3dspace.titleinput.label=Title
plma.exportto3dspace.descriptioninput.label=Description
plma.exportto3dspace.commentinput.label=Comments
plma.exportto3dspace.fileselect.placeholder=Select File
plma.exportto3dspace.error.configurations=Internal Error Invalid Options provided
plma.exportto3dspace.error.apiexecution=Internal Error in Execution of platform API's
plma.exportto3dspace.error.outside.3dexp=Export Only Allowed Inside 3DExperience
plma.exportto3dspace.error.unsupported.type=Object type not supported
plma.exportto3dspace.error.insufficient.access=User has no Modify access on this document. Export may not work
plma.exportto3dspace.commentinput.defaultcomments=Exported from Exalead
plma.exportto3dspace.controlvalidation.doc=Missing Document Context
plma.exportto3dspace.controlvalidation.doc.title=Document Title can not be empty
plma.exportto3dspace.controlvalidation.file.name=File Name can not be empty
plma.exportto3dspace.controlvalidation.file.ext=File Extension should be same as content
plma.exportto3dspace.controlvalidation.file.duplicate=File with same name already exists
plma.exportto3dspace.controlvalidation.file.notselected=File to update must be selected
plma.exportto3dspace.controlvalidation.bookmark.doc=Missing Bookmark Or Document Context
plma.exportto3dspace.controlvalidation.bookmark=Missing Bookmark Context
plma.exportto3dspace.error.nobookmark=No Bookmark Exist Create and Try Again
plma.exportto3dspace.success.done=Export Done Successfully
plma.exportto3dspace.success.share.object=Drag object
plma.swympublishmanager.success.publish=Published To Swym
plma.swympublishmanager.warn.wrong.contenttype=Please select Proper Content Type
plma.swympublishmanager.error.outside.3dexp=Publish Only Allowed Inside 3DExperience
plma.swympublishmanager.error.apiexecution=Internal Error in Execution of platform API's
plma.swympublishmanager.error.not.publish=Unable to Publish Please try with proper Information
plma.publishto3ddashboard.id=ShareContext
plma.publishto3ddashboard.container.title=Publish to 3DDashboard
plma.publishto3ddashboard.container.title.context = Dynamic Context
plma.publishto3ddashboard.container.title.snapshot = Snapshot
plma.publishto3ddashboard.bookmarkform.button.label=Publish
plma.publishto3ddashboard.swym.tab.label=Swym
plma.publishto3ddashboard.bookmark.tab.label=Bookmark
plma.publishto3ddashboard.preview.label=Preview
plma.publishto3ddashboard.preview.input.label=Link Label
plma.publishto3ddashboard.error.outside.3dexp=Publish Only Allowed Inside 3DExperience
plma.swympublishmanager.error.noswym=No Swym App Found
plma.publishto3ddashboard.info.copiedtoclipboard=Link Copied To Clipboard
plma.publishto3ddashboard.copytoclipboard=Copy To Clipboard
plma.error.outside.3dexperience=Functionality is available inside 3DExperience only
plma.error.no.3dcontent.incontext=No 3D Content Loaded in the context
plma.entrich3d.popup.header=Enrich3D API Layers Preview
plma.enrich3d.colorize.button.title=Reveal in 3D
plma.entrich3d.popup.layerquery.add=Add Layer Query
plma.entrich3d.popup.layerquery.show=Show Layer Query
plma.entrich3d.popup.changes.save=Save Changes
plma.entrich3d.popup.changes.undo=Undo Changes
plma.entrich3d.popup.restore.default=Restore Default
plma.entrich3d.popup.publish.event=Publish Event
plma.entrich3d.popup.api.poi=Pinpoint
plma.entrich3d.popup.api.colorize=Colorize
plma.entrich3d.popup.select.all=Select/Deselect All
plma.entrich3d.popup.column.series=Series
plma.entrich3d.popup.column.categories=Categories
plma.entrich3d.popup.column.aggvalue=Value
plma.entrich3d.popup.column.layerlabel=Layer Label
plma.entrich3d.popup.column.color=Color
plma.entrich3d.popup.series.notsupported=Series Not Supported
plma.enrich3d.error.no.data.from.chart=No Serie/Catgory Data found for the Serie to Colorize
plma.enrich3d.error.controller.failed=Failed to build data for enrich3d event
plma.enrich3d.error.dyndate.notsupported=Can't build data for enrich3d using dyndate facet.
plma.entrich3d.error.no.entries=Found 0 entries check access request from logs.
plma.entrich3d.error.no.matches=ZERO Matching entries found to build layer data.
plma.entrich3d.error.unsaved.changes=Unsaved changes detected. Please save or undo the changes before publishing.
plma.entrich3d.error.no.layer.selected=Please select at least one layer to publish
plma.entrich3d.error.no.layers.found=No Layers found. All actions are disabled
plma.entrich3d.warn.unmatched.entries=Found unmatched entries [{0} out of {1} entries]
plma.entrich3d.warn.index.has.paths=IndexHasPath option may be incorrectly Set.


