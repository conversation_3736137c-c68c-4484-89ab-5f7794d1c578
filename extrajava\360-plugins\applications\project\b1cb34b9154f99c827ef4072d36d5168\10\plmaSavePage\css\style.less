@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/variables.less";

.save-page {
  position: relative;
  display: inline-flex;
  flex-direction: row-reverse;
  cursor: pointer;

  .popup {
    .display-flex();
    font-size: 14px;
    position: absolute;
    top: 30px;
    //right: 0%;
    width: 400px;
    flex-direction: column;
    z-index: 100;

    border: 1px solid #e2e4e3;
    border-radius: 5px;
    background-color: #FFFFFF;
    padding: 5px 10px;
    margin-right: 5px;

    &::before {
      content: "";
      width: 13px;
      height: 13px;
      transform: rotate(45deg);
      box-shadow: inset 1px 1px 0px 0px #e2e4e3;
      background: #fff;
      position: absolute;
      z-index: -1;
      right: 2px;
      top: -7px;
    }

    > div {
      margin-top: 5px;
      display: flex;
      align-items: center;

      > :nth-child(1) {
        flex: 1;
      }

      > :nth-child(2) {
        flex: 2;
      }
    }

    .label-input, .description-input {
      border: 1px solid #e2e4e3;
      border-radius: 3px;
      padding: 0 0 0 5px;
      line-height: 26px;
      height: 26px;
      text-align: left;
      width: 100%;
    }

    .icon-input {
      margin: 0;
      font-size: 17px;
    }

    .description-label {
      align-self: flex-start;
      margin-top: 5px;
    }

    .description-input {
      min-height: 40px;
      max-height: 200px;

      &:focus-visible {
        outline: none;
      }
    }

    .button-container {
      justify-content: flex-end;

      > button {
        flex: none;
        margin-left: 10px;
        padding: 7px;
        width: 60px;
        color: #77797c;
        border: 1px solid #b4b6ba;
        border-radius: 4px;
      }

      > .save {
        color: #FFFFFF;
        background-color: #42a2da;
      }
    }

    .icon-tooltip {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      background-color: #FFFFFF;
      border: 1px solid #42a2da;
      margin-top: 10px;
      padding: 5px;
      border-radius: 5px;

      .icon-tooltip-input {
        border: 1px solid #e2e4e3;
        border-radius: 3px;
        padding: 0 0 0 5px;
        line-height: 24px;
      }

      .fonticon-container {
        margin-top: 10px;
        max-height: 300px;
        width: 100%;
        overflow-y: scroll;
        font-size: 15px;

        display: flex;
        flex-wrap: wrap;

        > span {
          margin: 5px;
        }
      }
    }
  }

  .fonticon:hover {
    color: @clink;
    cursor: pointer;
  }
}