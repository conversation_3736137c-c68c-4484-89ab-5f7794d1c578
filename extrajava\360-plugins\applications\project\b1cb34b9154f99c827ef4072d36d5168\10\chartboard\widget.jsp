<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="security" uri="http://www.exalead.com/jspapi/security" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds" varParentEntry="parentEntry" varParentFeed="parentFeed"/>

<c:set var="nbDivisions" value="12"/>
<c:set var="baseX" value="'auto'"/>
<c:set var="baseY" value="60"/>
<c:set var="marginX" value="10"/>
<c:set var="marginY" value="10"/>

<config:getOption var="widgetTitle" name="widgetTitle" defaultValue=""/>
<config:getOption var="cellHeight" name="cellHeight" defaultValue="240"/>
<config:getOption var="editButtonLocationSelector" name="editButtonLocationSelector" defaultValue=""/>
<config:getOption var="editToolbarLocationSelector" name="editToolbarLocationSelector" defaultValue=""/>
<config:getOption var="hideAllCells" name="hideAllCells" defaultValue=""/>
<config:getOption var="allowEditPages" name="allowEditPages" defaultValue="true"/>
<config:getOption var="allowEditFilters" name="allowEditFilters" defaultValue="true"/>
<config:getOption var="allowShare" name="allowShare" defaultValue="true"/>
<config:getOption var="lockMashupPages" name="lockMashupPages" defaultValue="false"/>

<string:eval var="user" string="\${security.username}" isJsEscape="true" feeds="${feeds}"/>

<request:getParameterValue name="chartboardId" var="chartboardId" defaultValue=""/>
<request:getParameterValue name="pageId" var="pageId" defaultValue=""/>
<search:getPageName var="pageName"/>
<c:set var="isMashupPage" value="false"/>

<c:choose>
    <c:when test="${pageId == undefined || pageId == ''}">
        <c:set var="pageId" value="${pageName}"/>
        <c:set var="isMashupPage" value="true"/>
    </c:when>
    <c:otherwise>
        <c:if test="${pageName == pageId}">
            <c:set var="isMashupPage" value="true"/>
        </c:if>
    </c:otherwise>
</c:choose>

<widget:widget extraCss="chartboard" varUcssId="uCssId" varCssId="cssId">

    <c:if test="${not empty widgetTitle || empty editButtonLocationSelector}">
        <widget:header extraCss="chartboard-header">
            <span class="widgetTitle">${widgetTitle}</span>
        </widget:header>
    </c:if>

    <widget:content>
        <plma:getChartboardConfig var="chartboardConfig"
                                  varCells="cellsMap"
                                  layout="${widget.layout}"
                                  chartBoardId="${chartboardId}"
                                  pageId="${pageId}"
                                  mashupPage="${isMashupPage}"
                                  nbColumns="${nbDivisions}"
                                  cellHeight="${cellHeight}"
                                  hideAllCells="${hideAllCells}"/>

        <div class="chartboard-popup-tinymce">
            <div class="chartboard-popup-tinymce-popup">
                <div class="chartboard-tinymce-template">
                    <div class="chartboard-tinymce-title"><i18n:message code="chartboard.template.name"/></div>
                    <div class="chartboard-tinymce-template-elem chartboard-tinymce-template-title active">
                        <span class="title"><i18n:message code="chartboard.template.title"/></span>
                        <span class="text"><i18n:message code="chartboard.template.text"/></span>
                    </div>
                    <div class="chartboard-tinymce-template-elem chartboard-tinymce-template-note">
                        <span class="title"><i18n:message code="chartboard.template.title"/></span>
                        <span class="text"><i18n:message code="chartboard.template.text"/></span>
                    </div>
                    <div class="chartboard-tinymce-template-elem chartboard-tinymce-template-widget">
                        <span class="title"><i18n:message code="chartboard.template.title"/></span>
                        <span class="text"><i18n:message code="chartboard.template.text"/></span>
                    </div>
                    <div class="chartboard-tinymce-template-elem chartboard-tinymce-template-arrow">
                        <span class="title"><i18n:message code="chartboard.template.title"/></span>
                        <span class="text"><i18n:message code="chartboard.template.text"/></span>
                        <span class="arrow"></span>
                    </div>
                    <div class="chartboard-tinymce-template-elem chartboard-tinymce-template-material">
                        <span class="title"><i18n:message code="chartboard.template.title"/></span>
                        <span class="text"><i18n:message code="chartboard.template.text"/></span>
                    </div>
                </div>
                <div class="chartboard-tinymce-container">
                    <div class="chartboard-tinymce-title"><i18n:message code="chartboard.note"/></div>
                    <div class="chartboard-tinymce-title-container">
                        <span><i18n:message code="chartboard.note.title"/>: </span>
                        <input type="text"/>
                    </div>
                        <%-- <div class="chartboard-tinymce-subtitle"><i18n:message code="chartboard.content"/>:</div> --%>
                    <div class="chartboard-tinymce-editor-container">
                        <div class="chartboard-tinymce-editor"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="chartboard-icon-list" style="display: none;">
            <input class="chartboard-icon-list-input"/>
            <plma:iconList var="iconList" filePath=""/>
            <c:forEach var="icon" items="${iconList}">
                <span class="fonticon-elem fonticon ${icon}"></span>
            </c:forEach>
        </div>

        <div class="chartboard-layout-container">
            <div class="chartboard-layout grid-stack">
                <c:set var="isAllowed" value="true"/>
                <c:if test="${writer == 'true' || reader == 'true'}">
                    <c:set var="isAllowed" value="true"/>
                </c:if>
                <c:choose>
                    <%-- If the user is not in writer or reader rights and the page has rights (if it has not, it means that the page is a mashup page with no modification --%>
                    <c:when test="${!isAllowed.equals('true') && isMashupPage != 'true'}">
                        <render:template template="template/unauthorizedPage.jsp"/>
                    </c:when>
                    <c:otherwise>
                        <div id="${uCssId}-notification-template" class="notification-template">
                            <render:template template="../plmaResources/template/message/message.jsp">
                                <render:parameter name="result" value="alert"/>
                                <render:parameter name="message" value=""/>
                                <render:parameter name="container" value="${uCssId}-notification-template"/>
                            </render:template>
                        </div>

                        <c:set var="customWidgetCount" value="0"/>

                        <c:forEach var="tileConfig" items="${chartboardConfig.tiles}">
                            <%-- If there is a cellId, it is a normal widget --%>
                            <c:set var="isCustomWidget" value="${empty tileConfig.cellId}"/>
                            <c:if test="${tileConfig.displayed}">
                                <div class="grid-stack-item ${not empty tileConfig.cssClass ? tileConfig.cssClass : ''} ${isCustomWidget ? 'custom-widget' : ''}"
                                     <c:if test="${not empty tileConfig.cssId}">id="${tileConfig.cssId}" </c:if>
                                        <c:choose>
                                            <c:when test="${!isCustomWidget}">
                                                data-cell-id="${tileConfig.cellId}"
                                            </c:when>
                                            <c:otherwise>
                                                data-custom-cell-id="chartboard-custom-widget-${customWidgetCount}"
                                                <c:set var="customWidgetCount" value="${customWidgetCount + 1}"/>
                                            </c:otherwise>
                                        </c:choose>
                                     data-cell-cssclass="${tileConfig.cssClass}"
                                     data-gs-x="${tileConfig.x}"
                                     data-gs-y="${tileConfig.y}"
                                     data-y="${tileConfig.y}"
                                     data-gs-width="${tileConfig.width}"
                                     data-gs-height="${tileConfig.height}"
                                     data-displayed="true">
                                    <div class="tile grid-stack-item-content">
                                        <c:choose>
                                            <c:when test="${!isCustomWidget}">
                                                <widget:forEachSubWidget
                                                        widgetContainer="${cellsMap[tileConfig.cellId]}"
                                                        feed="${parentFeed}" entry="${parentEntry}">
                                                    <render:widget/>
                                                </widget:forEachSubWidget>
                                                <div class="grid-stack-hide-item hidden"></div>
                                            </c:when>
                                            <%-- else, it is a custom widget created by the user in the chartboard --%>
                                            <c:otherwise>
                                                <div class="content ${not empty tileConfig.cssClass ? tileConfig.cssClass : ''} ${not empty tileConfig.backgroundColor ? '' : 'no-color'} ${not empty tileConfig.arrowSide ? tileConfig.arrowSide : ''}"
                                                     style="background-color:${not empty tileConfig.backgroundColor ? tileConfig.backgroundColor : ''};"
                                                     data-background-color="${not empty tileConfig.backgroundColor ? tileConfig.backgroundColor : ''}"
                                                     data-arrow-side="${not empty tileConfig.arrowSide ? tileConfig.arrowSide : ''}">
                                                        <%-- The text of our custom widget is in the preview prop --%>
                                                        ${tileConfig.preview}
                                                    <c:if test="${tileConfig.cssClass == 'chartboard-tinymce-template-arrow'}">
                                                        <div class="arrow-container ${not empty tileConfig.arrowSide ? tileConfig.arrowSide : 'left'}">
                                                            <div class="arrow-relative-container"
                                                                 style="border-${tileConfig.arrowSide == 'left' ? 'right' : tileConfig.arrowSide == 'top' ? 'bottom' : tileConfig.arrowSide == 'right' ? 'left' : tileConfig.arrowSide == 'bottom' ? 'top' : ''}-color:${not empty tileConfig.backgroundColor ? tileConfig.backgroundColor : ''};"></div>
                                                        </div>
                                                    </c:if>
                                                </div>
                                                <div class="editable-custom-widget-icon fonticon fonticon-pencil"
                                                     title="<i18n:message code="chartboard.focus.tile" />"></div>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>
                            </c:if>
                        </c:forEach>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>

        <div class="chartboard-sidebar">
            <div class="sidebar-block-title"><i18n:message code="chartboard.avalaible.widgets"/></div>
            <div class="search-bar-container">
                <div class="search-input">
                    <input type="text" title="<i18n:message code="chartboard.search.available"/>"/>
                    <span class="fonticon fonticon-search"></span>
                </div>
            </div>
            <div class="available-widgets">
                <c:forEach var="tileConfig" items="${chartboardConfig.tiles}">
                    <c:if test="${not tileConfig.displayed}">
                        <string:escape var="tilePreview" value="${tileConfig.preview}" escapeType="HTML"/>
                        <div class="available-widget preview ${tilePreview == undefined || tilePreview == '' ? '' : 'with-preview' }"
                             <c:if test="${not empty tileConfig.cssId}">id="${tileConfig.cssId}" </c:if>
                             data-cell-id="${tileConfig.cellId}"
                             data-cell-cssclass="${tileConfig.cssClass}"
                             data-displayed="false"
                             data-preview="${tilePreview}"
                        >
							<span class="gridstack-elem-title-label"><string:eval string="${tileConfig.title}"
                                                                                  feeds="${feeds}"/></span>
                                <%-- Do not render subwidget, only a preview --%>
                            <div class="tile grid-stack-item-content">
                            </div>
                        </div>
                    </c:if>
                </c:forEach>
            </div>
            <div class="trash-area">
                <div class="trash-icon fonticon fonticon-trash">
                </div>
            </div>
            <div class="sidebar-block-title"><i18n:message code="chartboard.custom.widgets"/></div>
            <div class="add-custom-widgets grid-stack">
                <div class="grid-stack-item custom-widget"
                     data-is-not-saved="true"
                     data-cell-cssclass="chartboard-tinymce-template-note"
                     data-gs-x="0"
                     data-gs-y="0"
                     data-gs-width="2"
                     data-gs-height="1">
                    <div class="tile grid-stack-item-content">
                        <div class="content chartboard-tinymce-template-note"><span
                                class="custom-widget-title"><i18n:message code="chartboard.note"/></span><span
                                class="custom-widget-icon fonticon fonticon-comment"></span></div>
                        <div class="editable-custom-widget-icon fonticon fonticon-pencil"
                             title="<i18n:message code="chartboard.focus.tile" />"></div>
                    </div>
                </div>
            </div>
        </div>
    </widget:content>

    <c:if test="${not empty chartboardConfig}">
        <c:choose>
            <c:when test="${plma:getBooleanParam(widget, 'savePreview', false)}">
                <plma:toJSON object="${chartboardConfig.tiles}" defaultValue="[]" var="jsonTiles"/>
                <plma:toJSON object="${chartboardConfig}" defaultValue="{}" var="jsonChartboard"/>
            </c:when>
            <c:otherwise>
                <plma:toJSON object="${chartboardConfig.tiles}" defaultValue="[]" var="jsonTiles" ignoreFields="${plma:toList('preview')}"/>
                <plma:toJSON object="${chartboardConfig}" defaultValue="{}" var="jsonChartboard" ignoreFields="${plma:toList('preview')}"/>
            </c:otherwise>
        </c:choose>
    </c:if>

    <c:choose>
        <c:when test="${not empty chartboardConfig && not empty chartboardConfig.label}">
            <c:set var="chartboardLabel" value="${chartboardConfig.label}"/>
        </c:when>
        <c:otherwise>
            <search:getPageName var="chartboardLabel"/>
        </c:otherwise>
    </c:choose>

    <render:renderScript position="READY">
        var options = {};
        <c:if test="${not empty editButtonLocationSelector}">
            options.editButtonLocationSelector = '${editButtonLocationSelector}';
        </c:if>
        <c:if test="${not empty editToolbarLocationSelector}">
            options.editToolbarLocationSelector = '${editToolbarLocationSelector}';
        </c:if>
        options.label = '<string:escape value="${chartboardLabel}" escapeType="JAVASCRIPT"/>';
        options.icon = <c:choose><c:when
            test="${not empty chartboardConfig && not empty chartboardConfig.icon}">'${chartboardConfig.icon}'</c:when><c:otherwise>undefined</c:otherwise></c:choose>;
        options.description = <c:choose><c:when
            test="${not empty chartboardConfig && not empty chartboardConfig.description}">'<string:escape
            value="${chartboardConfig.description}"
            escapeType="JAVASCRIPT"/>'</c:when><c:otherwise>''</c:otherwise></c:choose>;
        options.layoutLastEdit = <c:choose><c:when
            test="not empty chartboardConfig && not empty chartboardConfig.lastEdit">${chartboardConfig.lastEdit}</c:when><c:otherwise>undefined</c:otherwise></c:choose>;
        options.templatePage = '<search:getPageName/>';
        options.pageId = '${not empty pageId ? pageId : undefined}';
        options.chartboardConfig = ${jsonChartboard};
        options.baseY = ${baseY};
        options.marginY = ${marginY};
        options.originalConfig = ${jsonTiles};
        options.url = '<c:url value="/"/>';
        options.tinymceUrl = '<c:url value="/resources/widgets/plmaResources/lib/tinymce/tinymce.min.js"/>';
        options.suggestUrl = '<c:url value="/utils/suggest"/>';
        options.cssId = '${cssId}';
        options.wuid = '${widget.wuid}';
        options.user = '${user}';
        options.allowEditPages = '${allowEditPages}';
        options.hasUser = '<security:isUserConnected/>';
        options.dateParamName = '<plma:getConstantValueFromNameTag var="simpleDateRefine" constantName="SIMPLE_DATE_PARAM"/>';
        options.lockMashupPages = '${lockMashupPages}';
        options.savePreview = ${plma:getBooleanParam(widget, "savePreview", false)};
        options.pageOwner = ${plma:isPageOwner(pageContext.request, pageId )};
        options.pageEditable = ${plma:isEditable(pageContext.request, pageId )};

        new Chartboard('${uCssId}',options);
    </render:renderScript>

</widget:widget>