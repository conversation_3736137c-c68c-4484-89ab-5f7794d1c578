<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds"/>

<config:getOption name="selector" var="selector" defaultValue=".hit"/>
<config:getOption name="selectHitContainerSelector" var="selectHitContainerSelector" defaultValue=""/>
<config:getOption name="triggerEvent" var="triggerEvent" defaultValue="true"/>
<config:getOption name="limit" var="limit" defaultValue="20" />
<config:getOption name="selectAll" var="selectAll" defaultValue="true" />
<config:getOptions name="metaList" var="metaList" />
<search:getEntry var="entry" feeds="${feeds}" />
<search:getEntryInfo var="rawEntryUri" name="url" entry="${entry}"/>
<string:escape var="entryUri" value="${rawEntryUri}" escapeType="HTML"/>
<config:getOption var="enablePublication" name="enablePublication" defaultValue="false" />
<config:getOption var="enableSubscription" name="enableSubscription" defaultValue="false" />
<config:getOptions var="channelNames" name="channels" />
<config:getOption var="useApplicationConfig" name="useApplicationConfig" />
<config:getOption name="renderAllways" var="renderAllways" defaultValue="false"/>

<c:choose>
	<c:when test="${renderAllways == false}">
		<render:renderOnce id="${widget.wuid}">
			<widget:widget extraCss="multi-selection">
				<render:renderScript position="READY">
					/* Only one instance per widget */
					if (!window.MultiSelection) {
						window.MultiSelection = {};
					}
					var multiSelection = window.MultiSelection['${widget.wuid}'];
					if (!multiSelection) {
						var options = {
							renderAllways: ${renderAllways},
							<c:if test="${selectHitContainerSelector != ''}">
								selectHitContainerSelector: '${selectHitContainerSelector}',
							</c:if>
							selectAll: ${selectAll},
							<c:if test="${selectAll == 'true'}">
								containerSelector: '<config:getOption name="selectAllContainerSelector" defaultValue=".resultsTitle" />',
							</c:if>
							selectionConfig: {
								topicNames: [<c:forEach items="${channelNames}" var="channelName">'${channelName}',</c:forEach>],
								enablePublication: ${enablePublication},
								enableSubscription: ${enableSubscription}
							}
						};
						multiSelection = window.MultiSelection['${widget.wuid}'] = new MultiSelection('${selector}', '${widget.wuid}', ${triggerEvent}, ${limit}, options);
						multiSelection.init();
					} else {
						multiSelection.update();
					}
				</render:renderScript>
			</widget:widget>
		</render:renderOnce>
	</c:when>
	<c:otherwise>
		<widget:widget varUcssId="uCssId" extraCss="multi-selection" varCssId="cssId">
			<label class="hit-checkbox-container " title="select">
				<input type="checkbox" class="hit-checkbox">
				<span class="hit-checkbox-label"></span>
			</label>
			<render:renderScript position="READY">
					if (!window.MultiSelection) {
						window.MultiSelection = {};
					}
					var multiSelection = window.MultiSelection['${widget.wuid}'];
					if (!multiSelection) {
						var options = {
						    renderAllways: ${renderAllways},
							selectAll: ${selectAll},
							<c:if test="${selectAll == 'true'}">
								containerSelector: '<config:getOption name="selectAllContainerSelector" defaultValue=".resultsTitle" />',
							</c:if>
							selectionConfig: {
								topicNames: [<c:forEach items="${channelNames}" var="channelName">'${channelName}',</c:forEach>],
								enablePublication: ${enablePublication},
								enableSubscription: ${enableSubscription}
							}
						};
						multiSelection = window.MultiSelection['${widget.wuid}'] = new MultiSelection('${selector}', '${widget.wuid}', ${triggerEvent}, ${limit}, options);
						multiSelection.init();
					} else {
						multiSelection.update();
					}
			</render:renderScript>
		</widget:widget>
	</c:otherwise>
</c:choose>


<c:if test="${enablePublication || enableSubscription}">
	<render:renderScript>
		<c:choose>
			<c:when test="${useApplicationConfig}">
				<plma:getChannelsConfig var="channels" entry="${entry}" channelNames="${channelNames}" />
				var channelsConfig = ${channels};
				channelsConfig.forEach(function (channelConfig) {
					SelectAPI.addTopic(channelConfig.topic, channelConfig.data, eval('(' + channelConfig.normalizer + ')'), eval('(' + channelConfig.denormalizer + ')'));
				});
			</c:when>
			<c:otherwise>
				<config:getOptionsComposite var="channels" name="channelsConfig" mapIndex="true" entry="${entry}" />
				<c:forEach items="${channels}" var="channel">
					SelectAPI.addTopic('${channel.topic}', ${channel.data}, ${channel.normalizer}, ${channel.denormalizer});
				</c:forEach>
			</c:otherwise>
		</c:choose>
	</render:renderScript>
</c:if>

<c:if test="${not empty metaList}">
	<render:renderScript position="READY">
		window.MultiSelection['${widget.wuid}'].registerHit(
			'${entryUri}',
			{
				<c:forEach items="${metaList}" var="meta" varStatus="loop">
					'${meta}': '<search:getMetaValue entry="${entry}" metaName="${meta}" />'<c:if test="${!loop.last}">,</c:if>
				</c:forEach>
			}
		);
	</render:renderScript>
</c:if>