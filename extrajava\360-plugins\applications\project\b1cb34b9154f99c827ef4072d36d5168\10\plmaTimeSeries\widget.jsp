<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds" />

<config:getOption var="width" name="width" />
<c:if test="${width != null}"><c:set var="width" value="width:${width}px;" /></c:if>

<config:getOption var="minwidth" name="minwidth" />
<c:if test="${minwidth != null}"><c:set var="minwidth" value="min-width:${minwidth}px;" /></c:if>

<config:getOption var="enableRefines" name="enableRefines" defaultValue="false"/>
<config:getOption var="activateReloadChart" name="activateReloadChart" defaultValue="false"/>
<config:getOptionsComposite var="series" name="basedOn" mapIndex="true" />
<widget:widget varCssId="cssId" varUcssId="uCssId" extraCss="highcharts plmaCharts" extraStyles="${width} ${minwidth}">

	
	<widget:header extraCss="headerChartContainer ${enableRefines == 'true' ? 'refineEnabled':'' }">
		<span class="widgetHeaderIcon fonticon"></span>
		<div class="widgetChartHeader widgetTitle"><config:getOption name="title" defaultValue="" /></div>
		<config:getOption var="activateFilter" name="activateFilter" defaultValue="false" />
		
		<%-- fullScreen option --%>
		<config:getOption var="fullScreen" name="enableFullScreen" />
		<config:getOptionsComposite var="buttons" name="buttons" mapIndex="true"/>
		<%--<c:if test="${fullScreen == 'true'}">--%>
			<%--<span class="fonticon fonticon-resize-full widgetHeaderButton"></span>--%>
		<%--</c:if>--%>
		<%--<config:getOptionsComposite var="buttons" name="buttons" mapIndex="true"/>--%>
		<%--<c:forEach items="${buttons}" var="button">--%>
			<%--<string:escape var="buttonCss" value="${button.css}" escapeType="JAVASCRIPT"/>--%>
			<%--<string:escape var="buttonTooltip" value="${button.tooltip}" escapeType="JAVASCRIPT"/>--%>
			<%--<c:if test="${not empty buttonCss}">--%>
				<%--<span class="widgetHeaderButton ${buttonCss}" title="${buttonTooltip}" onclick="${button.onClick}"></span>--%>
			<%--</c:if>--%>
		<%--</c:forEach>--%>
	</widget:header>
	
	<widget:content>
		<plma:hasSeriesData var="hasData" seriesConfig="${series}" feeds="${feeds}"/>
		<c:choose>

			<%-- IF there are no feeds at all --%>
			<c:when test="${search:hasFeeds(feeds) == false}">
				<render:definition name="noFeeds">
					<render:parameter name="widget" value="${widget}" />
					<render:parameter name="showSuggestion" value="false" />
				</render:definition>
			</c:when>

			<c:otherwise>
				<config:getOption var="height" name="height" defaultValue=""/>
				<config:getOption var="facet" name="facetsList" />
				
				<c:set var="styleHeight" value="${height}" />
				<c:choose>
					<c:when test="${not empty height}">
						<c:set var="styleHeight" value="${height}px" />
					</c:when>
					<c:otherwise>
						<c:set var="styleHeight" value="100%" />
					</c:otherwise>
				</c:choose>

				<div class="chart-wrapper" style="height: ${styleHeight};width:100%;">
					<c:if test="${activateReloadChart == 'true'}">
						<config:getOption var="reloadCondition" name="reloadCondition" defaultValue="true"/>
						<c:if test="${reloadCondition}">
							<config:getOption var="labelReload" name="labelReload" defaultValue=""/>
							<config:getOption var="iconReload" name="iconReload" defaultValue=""/>
							<i18n:message var="seeMoreLabel" code="widget.plmacharts.seemore" />
							<div class="chart-paginate">
								<div class="see-more ${iconReload}" title="${seeMoreLabel}"> ${labelReload}</div>
							</div>
						</c:if>
					</c:if>
					<div class="chart-inner">
						<c:choose>
							<c:when test="${hasData}">
								<div id="chart_${cssId}" class="highChartsSVGWrapper" style="height: ${styleHeight};width:100%;">
								</div>
							</c:when>
							<c:otherwise>
								<div class="no-data"><div class="fonticon fonticon-chart-line"></div><i18n:message code="charts.nodata" widget="${widget}"/></div>
							</c:otherwise>
						</c:choose>
					</div>
				</div>

				<config:getOption var="doc" name="doc" defaultValue="" isJsEscaped=""/>
				<div class="doc-container hidden" style="height: ${styleHeight};">
					<div class="container">${doc}</div>
					<span class="close-doc fonticon fonticon-cancel" title="<i18n:message code='generic.close' />"></span>
				</div>
					<c:if test="${hasData}">
						<render:renderScript position="READY">
							(function(){
							<render:template template="javascript.jsp">
								<render:parameter name="chartContainerId" value="chart_${cssId}" />
								<render:parameter name="cssId" value="${cssId}" />
								<render:parameter name="uCssId" value="${uCssId}" />
								<render:parameter name="fullScreen" value="${fullScreen}" />
								<render:parameter name="buttons" value="${buttons}" />
								<render:parameter name="series" value="${series}" />
							</render:template>
							})();
						</render:renderScript>
					</c:if>
				</c:otherwise>
		</c:choose>
	</widget:content>
</widget:widget>
