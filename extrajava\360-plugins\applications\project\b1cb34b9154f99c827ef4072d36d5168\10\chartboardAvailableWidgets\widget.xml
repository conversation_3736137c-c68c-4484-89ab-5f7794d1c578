<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Chart board Cell Widget Container" group="PLM Analytics/Layout" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>Container for chartboard cells. It allows to hide specific cell content by default or add a specific cell title. Must be added as a subwidget of the Chart board widget.</Description>

	<Preview>
		<![CDATA[
            <img src="/resources/widgets/chartboardAvailableWidgets/images/chartboardAvailableWidgets.PNG" alt="Chartboard Available widget" />
        ]]>
	</Preview>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Includes>
		<Include type="css" path="css/style.less" />
	</Includes>

	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>
	
	<SupportWidgetsId arity="ZERO_OR_MANY"/>
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportI18N supported="true" />
	
	<OptionsGroup name="General">
		<Option id="title" name="Title" isEvaluated="true">
			<Description>Widget title that is displayed in the Available widget panel when subwidgets are hidden by default. If empty, the title of the first subwidget is displayed.</Description>
		</Option>
		<Option id="hide" name="Hide">
			<Description>Hides by default the subwidgets from the page. If hidden, subwidgets appear in the Available widget panel.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>
	
</Widget>
