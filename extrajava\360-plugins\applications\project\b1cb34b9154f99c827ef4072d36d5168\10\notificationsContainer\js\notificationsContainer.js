(function (window) {
    var countCallbacks = $.Callbacks('memory');

    NotificationsContainer = function (uCssId, userOptions) {
        var defaults = {
            scope : 'user',
            storageKey : 'alertsNotifications[]'
        };
        this.options = {};
        $.extend(this.options, defaults, userOptions);
        this.uCssId = uCssId;
        this.widget = $('.' + uCssId);
        this.db = null;
        this.storageKey = this.options.storageKey;
        this.init();
    };

    NotificationsContainer.getMessage = function (code) {
        return mashupI18N.get('notificationsContainer', code);
    };

    NotificationsContainer.prototype.init = function () {
        this.db = new StorageClient(this.options.scope);
        this.widget
            .on('click', '.delete-notification', function (e) {
                e.stopPropagation();
                if (confirm(NotificationsContainer.getMessage('notification.confirmation.delete'))) {
                    this.deleteNotification($(e.currentTarget).closest('.notification-wrapper'));
                }
            }.bind(this))
            .on('click', '.notification-wrapper', this.handleClickEvent.bind(this));
        this.retrieveNotifications();
    };

    NotificationsContainer.prototype.retrieveNotifications = function () {
        var self = this;
        this.widget.find(".list-container").html("");
        this.db.get(this.options.storageKey, function(notifications) {
            countCallbacks.fire(notifications.filter(function (notification) {
                // retrieve unseen notifications
                return !JSON.parse(notification.value).seen;
            }).length);
            if(notifications.length === 0){
                self.widget.find('.widget-container .no-notification').removeClass('hidden');
            }
            else{
                notifications.forEach(function(notification){
                    self.addNotification(notification);
                });
            }
        }, self.callBackError);
    };

    NotificationsContainer.prototype.onUserSeeNotification = function (alertId) {
        var currentDateTimestamp = moment().unix();
        return AlertingClient.updateNotificationInfo({
            alert_id: alertId,
            user_id: this.options.user,
            is_seen: true,
            last_seen: currentDateTimestamp
        });
    };

    NotificationsContainer.prototype.handleClickEvent = function (e) {
        var $notification = $(e.currentTarget);
        var notificationValue = $notification.data("value");
        var targetUrl = NotificationsContainer.utils.getURL() + "/" + notificationValue.pageName;
        if (notificationValue.pageId) {
            targetUrl += '?pageId=' + notificationValue.pageId;
        }
        if(notificationValue.seen === false) {
            notificationValue.seen = true;
            // set notification as seen in database
            this.onUserSeeNotification(notificationValue.alertID).then(function () {
                // set notification as seen in storage service
                this.db.set($notification.data("key"), JSON.stringify(notificationValue), function () {
                    // redirect to the related chart
                    window.location.replace(targetUrl);
                }, this.callBackError);
            }.bind(this), function(error){
                console.error(error);
            });
        }
        else{
            // redirect to the related chart
            window.location.replace(targetUrl);
        }
    };

    NotificationsContainer.prototype.addNotification = function(notification){
        var $listContainer =  this.widget.find(".list-container");
        var notificationJSON = JSON.parse(notification.value);
        var $notification_wrapper = $(NotificationsContainer.utils.notificationWrapperTemplate({
            title: notificationJSON.alertTitle,
            seen: notificationJSON.seen
        }));

        // Storing notification data in dom element
        $notification_wrapper.data("key", notification.key);
        $notification_wrapper.data("value", notificationJSON);

        $listContainer.prepend($notification_wrapper);
    };

    NotificationsContainer.prototype.deleteNotification = function ($notification) {
        this.onUserSeeNotification($notification.data('value').alertID).then(function () {
            this.db.del($notification.data('key'), function () {
                $notification.remove();
                countCallbacks.fire(this.widget.find('.list-container .notification-wrapper.unseen').length);
                if (this.widget.find('.list-container').is(':empty')) {
                    this.widget.find('.no-notification').removeClass('hidden');
                }
            }.bind(this), this.callBackError);
        }.bind(this), this.callBackError);
    };

    NotificationsContainer.prototype.callBackError = function(evt, args) {
        if(typeof console !== 'undefined'){
            console.error(args.responseText);
        }
    };

    NotificationsContainer.utils = {
        templateSettings: {
            escape: /{{([\s\S]+?)}}/g,
        },
        /**
         * Get url without page name
         * @returns {string}
         */
        getURL: function getURL(){
            var href = window.location.href;
            return href.substring(0, href.lastIndexOf("/"));
        },
        addBadgeTo: function ($element) {
            countCallbacks.add(function (count) {
                $element.find('.badge').remove();
                if (count > 0) {
                    $('<span />', {
                        'class': 'badge badge-rounded badge-primary badge-count',
                        text: count
                    }).appendTo($element);
                }
            });
        }
    };
    NotificationsContainer.utils.notificationWrapperTemplate = _.template($('#notification-wrapper-template').html(), NotificationsContainer.utils.templateSettings);

    window.NotificationsContainer = NotificationsContainer;
})(window);