/**
 * This class is used by Result list layout to select hit referenced in URL (opens hit details panel)
 *
 * @param uCssId Widget CSS ID
 * @param options Options
 * @constructor Constructor
 */
var UrlHitStelector = function (uCssId, options) {
    this.options = options;
    this.uCssId = uCssId;
    this.widget = $('.' + uCssId);

    if (this.widget.length === 0) {
        throw new Error('Unable to initialize widget UrlHitStelector : widget not found (uCssId: "' + uCssId + '").');
    } else {
        this.init();
    }
};

UrlHitStelector.prototype.init = function () {
    // Click on current selection
    if (window.location.href.split('#').length > 1) {
        let selectedUri = window.location.hash;
        if (selectedUri != ""){
            let anchor = selectedUri.substring(1);
            let $selectedItem = this.widget.find('li.hit[data-uri="' + anchor + '"]');
            if ($selectedItem.length > 0) {
                let helper = new DetailHitHelper(true);
                helper.selectHit($selectedItem);
                let entryUri = $selectedItem.data('uri');
                let paramName = $selectedItem.data('parameter');
                this.open(paramName, entryUri);
            } else {
                let $hits = this.widget.find('li.hit');
                if ($hits.length > 0) {
                    // Get param name from first hit in list (supposed to be the same for all)
                    let paramName = $($hits[0]).data('parameter');
                    this.open(paramName, anchor);
                }
            }
        }
    }
}

UrlHitStelector.prototype.open = function (paramName, entryUri) {
    var helper = new DetailHitHelper(true);
    $('.selection-panel').addClass('hidden');
    helper.loadDetail(paramName, entryUri);
    helper.pushState(entryUri);
}

