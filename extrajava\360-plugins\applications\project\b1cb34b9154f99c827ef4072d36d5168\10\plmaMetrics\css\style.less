.mashup {
	.searchWidget {
		&.plmaMetrics{
			flex: 1 0 0;
			flex-grow: 1;
		}
		.metrics {
			position: relative;
			height:125px;
			font-variant: small-caps;
			.wordbreak();
			
			.metric-wrapper {
				float:left;
				height:100%;
			}
			
			.metric-subwrapper {
				border-left:1px solid @cblock-border;
				height:100%;
			}
			
			.metric-wrapper:first-child .metric-subwrapper {
				border-left-width:0;
			}
			
			.main {
				font-size: 3vw;
				text-align: center;
				font-weight: bold;
				line-height: 70px;
				text-shadow: 0 1px 2px #ccc;
				min-height: 80px;
				padding-top:5px;
			}
			
			.main span {
				vertical-align:middle;
				font-size: 60%;
			}
			
			a {
				cursor: pointer;
			}
			
			.positive {
				color: #58b017;
			}
					
			.negative { 
				color: #B23600;
			}
			
			.description {
				font-weight: bold;
				font-size: 16px;
				text-align: center;
			}

			.docButton {
				position: absolute;
				top: 5px;
				right: 0px;
				font-size: 16px;
				cursor: pointer;
			}
		}
		.widgetHeader {
			justify-content: space-between;
		}
		.doc-container {
			top: 40px;
			font-variant: normal;
		}
	}
}