var PLMAChart2 = function(chartCssId,uCssId, options){
	var defaults = {
		data: [],
		dataProcessor: function(data){return data;},
		userOptions: {},
		defaultOptions: {},
			
			
		baseUrl: '',
		enableRefine: false
	};
	this.options = _.merge({}, defaults, options);
	this.uCssId = uCssId;
	this.chartCssId = 'chart_' + chartCssId;
	this.chartSelector = '#chart_' + chartCssId;
	this.chartWidget = $('#' + this.chartCssId);
	this.chart = null;
	this.widget = this.chartWidget.closest('.plmaCharts');
	this.widget.data('widget', this);

	this.labelLinks = {};
	this.currentData = {};
	/* Pagination starts from 1 */
	this.page = 0;
	this.maxPage = this.options.data.length;
	
	this.isInit = false;
	this.init();
};

PLMAChart2.PAGINATION_EVENT = 'plma:charts2.pagination';

PLMAChart2.prototype.init = function() {
	this.currentData = this.getNextPageData(); 
	if(this.options.hideEmpty == 'true'){
		this.hideEmptySeries();
	}
	if(this.options.isWaterFall){
		this.prepareWaterFallData(this.options.waterFallOptions.finalSumWaterfall,this.options.waterFallOptions.finalSumDisplayName,this.options.waterFallOptions.finalSumDisplayColor,this.options.waterFallOptions.intermediateSumWaterfall,this.options.waterFallOptions.intermediateSum);
	}
	this.updatePageNumber();
	
	if (this.options.enableRefine){
		this.initRefines();
	} else {
		this.initSerieHiding();
	}

	this.setWidgetIcon();
	
	this.labelLinks = this.currentData.labelLinks;

	this.widget.find('.chart-paginate .previous').on('click', $.proxy(function(){
		this.getPreviousPageData();
		this.update();
	}, this));
	this.widget.find('.chart-paginate .next').on('click', $.proxy(function(){
		this.getNextPageData();
		this.update();
	}, this));
	this.widget.find('.chart-paginate .see-more').on('click', $.proxy(function(e){
		this.reloadChartWithAllData(e);
	}, this));

	this.isInit = true;
	
	this.chart = highCharts.create({
		$widget: this.chartWidget.parent(),
		$chartContainer: this.chartWidget,
		opts: this.currentData
	});

};

PLMAChart2.prototype.update = function() {
	if(this.options.hideEmpty == 'true'){
        this.hideEmptySeries();
    }
	this.updateLabelLinks();
	if (this.options.enableRefine){
		this.initRefines();
	}
	this.updateChart();
	this.updatePageNumber();
	$(document).trigger(PLMAChart2.PAGINATION_EVENT, [this.uCssId]);
};

PLMAChart2.prototype.setWidgetIcon = function () {
	var iconToAdd = '';
	switch (this.options.userOptions.chart.defaultSeriesType) {
		case 'column':
			iconToAdd = 'fonticon-chart-bar';
			break;
		case 'area':
			iconToAdd = 'fonticon-chart-area-stacked';
			break;
		case 'areaspline':
			iconToAdd = 'fonticon-chart-area-stacked';
			break;
		case 'bar':
			iconToAdd = 'fonticon-chart-bar';
			break;
		case 'line':
			iconToAdd = 'fonticon-chart-line';
			break;
		case 'spline':
			iconToAdd = 'fonticon-chart-spline';
			break;
		case 'pie':
			iconToAdd = 'fonticon-chart-pie';
			break;
		case 'waterfall':
			iconToAdd = 'fonticon-chart-column-range';
			break;
	}
	this.widget.find('.widgetHeaderIcon').addClass(iconToAdd);
};

/**
 * Sets the PLMAChart2 object to the next page of data and returns this data.
 */
PLMAChart2.prototype.getNextPageData = function() {
	if (this.page < this.maxPage) {
		this.currentData = this.getPageData(++this.page);	
	} else {
		this.page = 1;
		this.currentData = this.getPageData(this.page);
	}
	return this.currentData;
	
};
/**
 * Sets the PLMAChart2 object to the previous page of data and returns this data.
 */
PLMAChart2.prototype.getPreviousPageData = function() {
	if (this.page > 1) {
		this.currentData = this.getPageData(--this.page);
	} else {
		this.page = this.maxPage;
		this.currentData = this.getPageData(this.page);
	}
	return this.currentData;
};
/**
 * Reload the plma chart in ajax with all data (add parameter to set max_per_level param to 0)
 */
PLMAChart2.prototype.reloadChartWithAllData = function(e) {
	var isFullSize = this.widget.hasClass('full-size-active');
	var widgetHeader = this.widget.find('.widgetHeader').detach();

	var url = new BuildUrl(window.location.href);
	var options = {};
	options.success = $.proxy(function(){
		//Get the old header back
		if(isFullSize){
			$('.' + this.uCssId).addClass('full-size-active');
		}
		$('.' + this.uCssId + ' .widgetHeader').replaceWith(widgetHeader);
		$(document).trigger(FullScreenWidget.WIDGET_RELOAD, [this.uCssId]);
		$(window).trigger('plma:resize');
	},this);
	
	var client = new PlmaAjaxClient($('.'+this.uCssId),options);
	var parameters = url.getParameters();
	
	//Delete the base params
	client.getAjaxUrl().params = {};

	$.each(parameters, function(index, value) {
		if(index != "" && index != "_"){
			for(var i=0; i<value.length; i++){
				client.getAjaxUrl().addParameter(index, value[i], false);
			}
		}
	});

	//Add custom parameters
	for(var k=0; k<this.options.reload.params.length; k++){
		client.getAjaxUrl().addParameter(this.options.reload.params[k].split('__')[0], this.options.reload.params[k].split('__')[1],false);
	}
	
	// set wuids to update
	client.addWidget(this.uCssId);

	client.update();
};
/**
 * Returns the data from the specified page index (starting from 1). Does not change the object's current page.
 */
PLMAChart2.prototype.getPageData = function(i) {
	if (this.options.data.length > 0 ) {
		var data = this.options.data[(i - 1) % this.options.data.length]; 
		return this.prepareData(data);
	} else {
		return {series: []};
	}
};
/**
 * Hide empty data from in the chart 
 */
PLMAChart2.prototype.hideEmptySeries = function(){
	var data = this.currentData;
	var seriesToKeep = [];
	for(var i=0; i<data.series.length; i++){
		var isEmpty = true;
		var dataToKeep = [];
		if(data.series[i].data){
            for(var j=0; j<data.series[i].data.length; j++){
                if(data.series[i].data[j].y > 0){
                    isEmpty = false;
                    dataToKeep.push(data.series[i].data[j])
                }
            }
        }
		data.series[i].data = dataToKeep;
		if(!isEmpty){
			seriesToKeep.push(data.series[i]);
		}
	}
	data.series = seriesToKeep;
};/**
 * Prepare data for waterfall chart
 */
PLMAChart2.prototype.prepareWaterFallData = function(withEndSum,endSumName,endSumColor,withIntermediateSum,intermediateSums){
	var data = this.currentData;
	
	//Add lineWidth to 0
	for(var i=0; i<data.series.length; i++){
		if(i!=0){
			data.series[i].lineWidth = 0;
		}
	}
	
	//Compute the positions to put intermediate sums
	var sumsPosition = [];
	if(withIntermediateSum == "true"){
		if(intermediateSums.number>data.xAxis[0].categories.length){
			intermediateSums.number = 1;
			intermediateSums.type = "Sum to fixed number of values";
		}
		if(intermediateSums.type == "Fixed number of sums"){
			var nbElemToJump = Math.round(data.xAxis[0].categories.length / (parseInt(intermediateSums.number)+1));
			for(var i=0 ; i<intermediateSums.number ; i++){
				sumsPosition.push((i+1)*nbElemToJump);
			}
		}else if(intermediateSums.type == "Sum to fixed number of values"){
			for(var i=0 ; i<data.xAxis[0].categories.length/intermediateSums.number - 1 ; i++){
				sumsPosition.push((i+1)*intermediateSums.number);
			}
		}else if(intermediateSums.type == "List of positions (2,4,6 etc...)"){
			var pos = intermediateSums.number.split(',');
			for(var i=0 ; i<pos.length ; i++){
				sumsPosition.push(parseInt(pos[i]));
			}
		}
	}
	
	//Modify the data to be iterative
	for(var j=0 ; j<data.series.length ; j++){
		
		if(withEndSum == "true"){
			var finalSum = {};
			finalSum.isSum = true;
			if(endSumColor != ""){
				finalSum.color = endSumColor;
			}
			finalSum.name = endSumName;
			data.series[j].data.push(finalSum);
			
		}
		
		for(var pos=0 ; pos<sumsPosition.length ; pos++){
			var inter = {};
			if(intermediateSums.total == "Total"){
				inter.isSum = true;
			}else if(intermediateSums.total == "Intermediate"){
				inter.isIntermediateSum = true;
			}
			if(intermediateSums.color != ""){
				inter.color = intermediateSums.color;
			}
			inter.name = intermediateSums.name + ' ' +pos;
			data.series[j].data.splice(sumsPosition[pos]+pos,0,inter);
		}
	}
	
	//Add end and intermediate sum to xAxis
	if(withEndSum === "true"){
		data.xAxis[0].categories.push(endSumName);
		data.seriesCategories.push(endSumName);
	}
	
	for(var pos=0 ; pos<sumsPosition.length ; pos++){
		data.xAxis[0].categories.splice(sumsPosition[pos]+pos,0,intermediateSums.name + ' ' + pos);
	}
	
};
/**
 * Modify raw data with user-defined operations.
 */
PLMAChart2.prototype.prepareData = function(data) {
	var d = data; /* We don't bother for a deep copy here*/
	if ($.isFunction(this.options.dataProcessor)) {
		d = this.options.dataProcessor.call(null, d);
	}
	
	d = _.merge({}, this.options.defaultOptions, d, this.options.userOptions);

	if(typeof d.chart !== "undefined"){
		d.chart.renderTo = this.chartCssId;	
	}

	if (!d.chart.events) {
		d.chart.events = {};
	}
	d.chart.events.redraw = $.proxy(function () {
		this.widget.find('.highcharts-container').trigger("plma:chart-redraw");
	}, this);

	return d;
};
/**
 * Tells whether there are several pages of data.
 */
PLMAChart2.prototype.isPaginated = function(){
	return this.options.data.length > 1
};
/**
 * Update the chart with data from the current page and redraws it.
 */
PLMAChart2.prototype.updateChart = function() {
	for (var i=0; i < this.getChart().series.length; i++) {
		var serie = this.getChart().series[i];
		if (this.currentData.series.length > i) {
			serie.setData(this.currentData.series[i].data, false, false, false);
		}
	}
	for (var i=0; i < this.getChart().xAxis.length; i++) {
		var xAxis = this.getChart().xAxis[i];
		var xAxisIndex = Math.max(i, this.currentData.xAxis.length -1);
		xAxis.setCategories(this.currentData.xAxis[xAxisIndex].categories, false);
	}
	this.getChart().redraw();
};
/**
 * Updates the display of the current page.
 */
PLMAChart2.prototype.updatePageNumber = function() {
	if (this.maxPage > 1) {
		this.widget.find('.chart-paginate .current-page').text(this.page);
		this.widget.find('.chart-paginate .max-page').text(this.maxPage);	
	} else {
		this.widget.find('.chart-paginate.chart-page-numbers').remove();
	}
};

/**
 * Updates the map of refine links for xAxis labels.
 */
PLMAChart2.prototype.updateLabelLinks = function() {
	this.labelLinks = this.currentData.labelLinks;
}

PLMAChart2.prototype.getChart = function() {
	return this.chart;
};

PLMAChart2.prototype.initSerieHiding = function() {
	/* For each page of data */
	$.each(this.options.data, $.proxy(function(i, data){
		/* For each serie in the page of data */
		$.each(data.series, $.proxy(function(i, serie){
			/* For each point in the serie */
			$.each(serie.data, $.proxy(function(i, point){
				var refineUrl = this.getRefineUrl(point.refineParam).toString();
				if (!point.events) {
					point.events = {};
				}
				point.events.click = function(){
					PLMAChart2.utils.hideOtherSeries(this);
				}
			}, this));
		}, this));
		_.merge(data, {plotOptions: {series: {cursor: "pointer"}}});
	}, this));
};

PLMAChart2.prototype.initRefines = function() {
	/* For each serie */
	$.each(this.currentData.series, $.proxy(function(i, serie){
		/* For each point in the serie */
		$.each(serie.data, $.proxy(function(i, point){
			var refineUrl = this.getRefineUrl(point.refineParam).toString();
			if (!point.events) {
				point.events = {};
			}
			point.events.click = function(){
				exa.redirect(refineUrl);
			}
		}, this));
	}, this));
	$.each(this.currentData.xAxis, $.proxy(function(i, xAxis){
		_.merge(xAxis, {labels: {style: {cursor: "pointer"}}});
	}, this));
	_.merge(this.currentData, {plotOptions: {series: {cursor: "pointer"}}});

	this.widget.off('click.plma-refine');
	this.widget.on('click.plma-refine', '.highcharts-axis-labels.highcharts-xaxis-labels', $.proxy(function(e){
		var textContent = e.target.textContent,
			title = $(e.target).find('+ title').text();
		if (this.labelLinks.hasOwnProperty(textContent) || this.labelLinks.hasOwnProperty(title)){
			var refine = this.labelLinks[textContent];
			if (typeof refine === "undefined") {
				refine = this.labelLinks[title];
			}
			if (typeof refine !== "undefined") {
				exa.redirect(this.getRefineUrl(refine).toString());	
			}
		}
	}, this));
};

/**
 * Returns the base URL for refine links : either the current page (without the query string), 
 * either the one specified (keeping the query string).
 */
PLMAChart2.prototype.getBaseUrl = function() {
	var url;
	if (this.options.baseUrl && this.options.baseUrl.length > 0) {
		url = new BuildUrl(this.options.baseUrl);
	} else {
		url = new BuildUrl(window.location.href.split('?')[0]);	
	}
	return url;
};

PLMAChart2.prototype.getRefineUrl = function(refineParam) {
	var refineUrl = this.getBaseUrl();
	var parseRefineParam = new BuildUrl(refineParam);
	for (var paramName in parseRefineParam.getParameters()) {
		if (parseRefineParam.getParameters().hasOwnProperty(paramName)){
			refineUrl.addParameters(paramName, parseRefineParam.getParameters()[paramName]);	
		}
	}
	//Add custom parameters
	for(var k=0; k<this.options.reload.params.length; k++){
		refineUrl.removeParameterWithValue_(this.options.reload.params[k].split('__')[0], this.options.reload.params[k].split('__')[1]);
//		client.getAjaxUrl().addParameter(this.options.reload.params[k].split('__')[0], this.options.reload.params[k].split('__')[1],false);
	}
	
	return refineUrl;
};

/**
 * If the series can be stacked, switch the stacking mode ('normal' <-> 'percent').
 * Updates both the tooltip and Y axis label for better readability.
 */
PLMAChart2.prototype.switchStacking = function () {
	if (this.chart) {
		var newStacking;

		var currentFormatter = this.chart.tooltip.options.formatter;
		var newFormatter;

		$.each(this.chart.series, function(index, serie) {
			var currentStacking = serie.options.stacking;
			var currentTooltipFormat = serie.tooltipOptions.pointFormat;
			var currentYaxisLabel = serie.yAxis.axisTitle.textStr;
			if (typeof currentStacking !== 'undefined') {
				newStacking = currentStacking === 'normal' ? 'percent' : 'normal';
				serie.update({
					stacking: newStacking,
					tooltip: {
						pointFormat: getNewTooltipFormat(currentStacking, currentTooltipFormat)
					}
				});
				serie.yAxis.setTitle({
					text: getNewYaxisLabel(currentStacking, currentYaxisLabel)
				});
			}
		});

		// change global formatter
		if (typeof currentFormatter !== 'undefined' && newStacking) {
			var newFormatterString = getNewTooltipFormatter(
				newStacking,
				currentFormatter.toString(),
				this.chart.tooltip.options.shared
			);
			eval('newFormatter = ' + newFormatterString);
			this.chart.update({
				tooltip: {
					formatter: newFormatter
				}
			});
		}
	}
};

PLMAChart2.prototype.getMessage = function (code) {
    return mashupI18N.get('plmaCharts2', code);
}
/**
 * Export chart as SVG/PNG
 */
PLMAChart2.prototype.exportChart = function (exportOpts) {
    exportOpts.container.showPLMASpinner({overlay: true});
    let imageName = null;
    if(exportOpts.imageType == 'svg'){
        imageName = "chart_" + this.uCssId + ".svg";
    }else if(exportOpts.imageType == 'png'){
        imageName = "chart_" + this.uCssId + ".png";
    }else{
        throw "ImageType (" + exportOpts.imageType + ") Not Supported";
    }
    this.getImageDataUrl(exportOpts.imageType, true).then(function(imageData){
        exportOpts.container.find('.preview-header').append('<span class="title">' + imageName + '</span>');
        let $preview = $('<a>',{
            download: imageName,
            onclick: "this.setAttribute('href', this.children[0].getAttribute('src'))" // TO enable download.
        });
        $preview.append($('<img>', {
            src: imageData.dataUrl,
            alt: imageName,
            style: "display: block; margin-left: auto; margin-right: auto;"
        }));
        exportOpts.container.find('.preview-content').append($preview);
        exportOpts.container.hidePLMASpinner();
        exportOpts.doneCallback(function(){
            return $preview.find('img').attr('src');
        }, imageName);
    }.bind(this))
    .catch((function(error) {
        $.notify(this.getMessage('widget.plmacharts.exportchart.err.convert'), 'error');
    }).bind(this));
};

PLMAChart2.getImage = function($chartElement, imageType){
    // Get SVG Add border to SVG
    let $svg = $chartElement.find('svg');
	var borderColor = $svg.find('.highcharts-background').attr('stroke');
	$svg.find('.highcharts-background').attr('stroke', '#77797c');
    let svg = $svg.get(0);
    // Serialize SVG.
    var xmlSerializer = new XMLSerializer();
    var stringSvg = (typeof svg === "undefined")? undefined : xmlSerializer.serializeToString(svg);
	if(imageType == 'svg'){
		$svg.find('.highcharts-background').attr('stroke', borderColor?borderColor:null);
		return "data:image/svg+xml;charset=utf-8,"+encodeURIComponent(stringSvg);
	}
	// Else build PNG
    var canvas = $('<canvas>').attr('width', $svg.width())
        .attr('height', $svg.height()).get(0);
    try{
    //sometimes canvg throws error and stops further execution
        canvg(canvas, stringSvg);
    }catch(err){ /*No error is thrown from canvg*/}

    $svg.find('.highcharts-background').attr('stroke', borderColor?borderColor:null);
    return canvas.toDataURL('image/'+imageType, 1.0);
};
PLMAChart2.getOptionsForExport = function(newChartOptions, origChartOptions){
    if(origChartOptions){
		// Return options resetted to origChartOptions : after export
		if(origChartOptions.credits){
			newChartOptions.credits.enabled = origChartOptions.credits.enabled;
			newChartOptions.credits.text = origChartOptions.credits.text;			
		}else{
			newChartOptions.credits.enabled = false;
			newChartOptions.credits.text = '';
		}
		if(origChartOptions.plotOptions && origChartOptions.plotOptions.series && origChartOptions.plotOptions.series.dataLabels){
			newChartOptions.plotOptions.series.dataLabels.enabled = origChartOptions.plotOptions.series.dataLabels.enabled;
			newChartOptions.plotOptions.series.dataLabels.shadow = origChartOptions.plotOptions.series.dataLabels.shadow;
		}else{
			newChartOptions.plotOptions.series.dataLabels.enabled = false;
		}
		return newChartOptions;
	}
	
    // Return newOptions : before export.
	// Add credits...Always on and override any existing text...
	var getCreditsText = function(){
		let imgCredits = '[ ' + mashupI18N.get('plmaResources', 'plma.exportto3dspace.commentinput.defaultcomments') +
		' (' + moment().format('DD-MMM-YYYY, hh:mm:ss A') + ') ]';
		if(newChartOptions.credits && newChartOptions.credits.enabled && newChartOptions.credits.text){
			return newChartOptions.credits.text + ' ' + imgCredits;
		}
		return imgCredits;
	}
	
	newChartOptions.credits = $.extend(true, { }, newChartOptions.credits, {
		enabled: true,
		text: getCreditsText()
	});
	
	// Enable Data Labels. If user do not want the labels in export, they can turn off
	// from chart [plotOptions.series.dataLabels]
	newChartOptions.plotOptions = $.extend(true, {}, {
		series: {
			dataLabels: {
				enabled: true,
				shadow: false
			}
		}
	}, newChartOptions.plotOptions);
	return newChartOptions;
};

PLMAChart2.prototype.getImageDataUrl = function(imageType) {
    let dfd = $.Deferred();
    //Incase chart is empty dont create the page
    if(this.chart === undefined) {
        dfd.reject("Chart has no Data");
        return dfd.promise();
    }
    let oldStyle = this.widget.attr('style');
    this.widget.attr('style', (oldStyle?oldStyle:'')+';height:600px;');
    var $fullScreenButton = this.widget.find('.fonticon.fonticon-resize-full');
    if($fullScreenButton.length == 0){
        $fullScreenButton = $('<i/>', {
            'class': 'widgetHeaderButton fonticon fonticon-resize-full'
        });
        this.widget.fullScreenWidget({ button: $fullScreenButton });
    }
    $fullScreenButton.trigger('click', [0]);
    let $chartElement = this.widget.find('.widgetContent');
    $chartElement.css('pointer-events','none');

    let origChartOptions = JSON.parse(JSON.stringify(this.getChart().options));//Deep Copy options
	let newChartOptions = JSON.parse(JSON.stringify(origChartOptions)); 
	this.getChart().update(PLMAChart2.getOptionsForExport(newChartOptions));
    // Hide resetZoom button if present.
	!this.getChart().resetZoomButton || this.getChart().resetZoomButton.hide();
	
    let dataUrl = PLMAChart2.getImage($chartElement, imageType);
    this.widget.attr('style', (oldStyle?oldStyle:''));
    $fullScreenButton.trigger('click', [0]);
    $chartElement.css('pointer-events','auto');
	// Show resetZoom button if present.
	!this.getChart().resetZoomButton || this.getChart().resetZoomButton.show();
	
	this.getChart().update(PLMAChart2.getOptionsForExport(newChartOptions, origChartOptions));
	$(window).trigger('plma:resize');
	
    dfd.resolve({
        dataUrl: dataUrl,
        type: imageType.toUpperCase()
    });

    return dfd.promise();
};

/**
 * Returns the next tooltip format based on both the current stacking mode and the tooltip format.
 */
var getNewTooltipFormat = function(currentStackingMode, tooltipFormat) {
	var PERCENTAGE_STRING = ' ({point.percentage:.0f}%)';
	var newTooltipFormat = tooltipFormat;
	if (currentStackingMode === 'normal') {
		// Put percentage
		var index = tooltipFormat.lastIndexOf('<br />');
		if (index === -1) {
			index = tooltipFormat.lastIndexOf('<br/>');
		}
		if (index !== -1) {
			newTooltipFormat = tooltipFormat.slice(0, index) + PERCENTAGE_STRING + tooltipFormat.slice(index);
		}
	} else {
		// Remove percentage
		newTooltipFormat = tooltipFormat.replace(PERCENTAGE_STRING, '');
	}
	return newTooltipFormat;
};

/**
 * Returns a formatter (function as string) based on the stacking mode and the shared option.
 */
var getNewTooltipFormatter = function(stackingMode, formatterString, shared) {
	var PERCENTAGE_STRING = shared
		? " (this.points[i].percentage ? ' (' + this.points[i].percentage + '%)' : '') + "
		: " (this.percentage ? ' (' + this.percentage + '%)' : '') + ";
	var newFormatterString = formatterString;
	var index = formatterString.lastIndexOf("'<br />'");
	if (index === -1) {
		index = formatterString.lastIndexOf("'<br/>'");
	}
	if (index !== 1) {
		if (stackingMode === 'percent') {
			// put percentage
			newFormatterString = formatterString.slice(0, index) + PERCENTAGE_STRING + formatterString.slice(index);
		} else {
			// remove percentage
			newFormatterString = formatterString.replace(PERCENTAGE_STRING, '');
		}
	}
	return newFormatterString;
};

/**
 * Returns the next Y axis label based on both the current stacking mode and the tooltip format.
 */
var getNewYaxisLabel = function(stackingMode, yAxisLabel) {
	var percentageString = ' (% of total)';
	if (stackingMode === 'normal') {
		if (yAxisLabel.includes(percentageString)) {
			return yAxisLabel;
		} else {
			// Put percentage
			return yAxisLabel.concat(percentageString);
		}
	} else {
		// Remove percentage
		return yAxisLabel.replace(percentageString, '');
	}
};

PLMAChart2.utils = {
	setXTickInterval : function(event){
		//Set the tick interval to have all names when we have a few data on the x axis
		var nbCatTotal = event.target.categories.length;
		var nbCat = 1000;
		if(event.min != undefined && event.max != undefined){
			nbCat = event.max - event.min;
		}
		if(nbCat > 300){
			event.target.options.tickInterval = Math.floor(nbCatTotal / 15, 0) + 1;
		}else if(nbCat > 200){
			event.target.options.tickInterval = Math.floor(nbCatTotal / 20, 0) + 1;
		} else if(nbCat > 100){
			event.target.options.tickInterval = Math.floor(nbCatTotal / 50, 0) + 1;
		} else if(nbCat > 50){
			event.target.options.tickInterval = Math.floor(nbCatTotal / 100, 0) + 1;
		} else if(nbCat > 10){
			event.target.options.tickInterval = Math.floor(nbCatTotal / 200, 0) + 1;
		} else {
			event.target.options.tickInterval = 1;
		}
	},
	
	setYTickInterval: function(event, intervals){
		var maxRange;
		var _intervals = intervals || 5;
		if (typeof event === "undefined" || typeof event.max === "undefined" || typeof event.min === "undefined"){
			maxRange = this.dataMax - this.dataMin;
		} else {
			maxRange = event.max - event.min;
		}
		if (maxRange) {
			var order = Math.floor(Math.log10(maxRange));
			var interval = Math.max(1, Math.floor((maxRange / _intervals ) / Math.pow(10, order))) * Math.pow(10, order);
			
			this.options.tickInterval = interval;	
		}
		
	},

	hideOtherSeries : function(point){
		//We hide/show all other series
		var index = point.series._i;
		
		var isHidden = true;
		for(var i=0 ; i<point.series.xAxis.series.length ; i++){
			if(i!=index && point.series.xAxis.series[i].visible){
				isHidden = false;
				point.series.xAxis.series[i].hide();
			}
		}
		
		if(isHidden){
			for(var i=0 ; i<point.series.xAxis.series.length ; i++){
				if(i!=index){
					point.series.xAxis.series[i].show();
				}
			}
		}
		
	},
	
	getNowValue : function(data){
		moment.locale('en');
		var now = moment().format("MMM YYYY");
		
		for(var i=0 ; i<data[0].xAxis[0].categories.length ; i++){
			if(data[0].xAxis[0].categories[i] == now){
				return i;
			}
		}
		return -1;
	}

};

var displayFilter = function(refinements, cssId){
	if (refinements != null) {
		//display filter icon if some refinement
		document.getElementsByClassName('icon-filter-chart-'+cssId)[0].style.display = 'inline-block';
	}
};

var buildRefinements = function(refinements,cssId){
	//Build the popup with the right refinements (1D) values
	if (refinements != null) {
		var context = '';
		
		for (var i = 0; i < refinements.length; i++) {
			var div = document.createElement('div');
			div.className = 'refine';
			div.innerHTML = refinements[i].description;
			context = context + '<div class="refine">' + refinements[i].description + '</div>';
			document.getElementsByClassName('icon-filter-chart-'+cssId)[0].style.display = 'inline-block';
		}
		
		//init popup
		var options = {};
		options.button = document.getElementsByClassName('icon-filter-chart-'+cssId)[0];
		options.content = context;
		options.direction = 'left';
		options.extraCss = 'filter-popup';
		var popup = new AppsPopup(options);
	}
};

var refineDataByGroups = function(data,groups){
	var resultGroup = [];
	if(data.series.length>0){
		//We build the table with the groups
		var groupList = groups.split('##');
		for(var i=0 ; i<groupList.length ; i++){
			if(groupList[i] != undefined && groupList[i] != ""){
				var groupInfo = groupList[i].split('__');
				
				var group = {};
				group.name = groupInfo[2];
				group.beginInt = parseInt(groupInfo[0]);
				group.endInt = parseInt(groupInfo[1]);
				group.stack = "total";
				group.type = "column";
				group.yAxis = 0;
				
				var dataList = [];
				for(var j=0 ; j<data.series[0].data.length ; j++){
					var dataObj = {};
					dataObj.r = data.series[0].data[j].r;
					dataObj.y = 0;
					dataList.push(dataObj);
				}
				
				group.data = dataList;
				
				resultGroup.push(group);
			}
		}
		
		//We sum the results
		for(var i=0 ; i<data.series.length ; i++){
			for(var j=0 ; j<resultGroup.length ; j++){
				if(isNaN(resultGroup[j].endInt)){
					if(parseInt(data.series[i].name) >= resultGroup[j].beginInt){
						for(var k=0 ; k<resultGroup[j].data.length ; k++){
							resultGroup[j].data[k].y = resultGroup[j].data[k].y + data.series[i].data[k].y;
						}
					}
				}else{
					if(parseInt(data.series[i].name) <= resultGroup[j].endInt && parseInt(data.series[i].name) >= resultGroup[j].beginInt){
						for(var k=0 ; k<resultGroup[j].data.length ; k++){
							resultGroup[j].data[k].y = resultGroup[j].data[k].y + data.series[i].data[k].y;
						}
					}
				}
			}
		}
	}
	
	return resultGroup;
};

var mergeData = function(newData,data){
	if(data!=undefined && data.isMerged){
		//merge xAxis
		for(var i=0 ; i<newData.xAxis.categories.length ; i++){
			var exists = false;
			for(var j=0 ; j<data.xAxis.categories.length ; j++){
				if(data.xAxis.categories[j]==newData.xAxis.categories[i]){
					exists = true;
				}
			}
			if(!exists){
				data.xAxis.categories.push(newData.xAxis.categories[i]);
			}
		}
		
		//mergeSeries
		//Complete the series of the old facet
		for(var i=0 ; i<data.series.length ; i++){
			if(data.series[i].data.length != data.xAxis.categories.length){
				var nbNewXAxis = data.xAxis.categories.length - data.series[i].data.length;
				for(var j=0 ; j<nbNewXAxis ; j++){
					var dat = {};
					dat.y = 0;
					dat.r = newData.series[0].data[j].r;
					data.series[i].data.push(dat);
				}
			}
		}
		//Complete the series of the new facet
		for(var i=0 ; i<newData.series.length ; i++){
			if(newData.series[i].data.length != data.xAxis.categories.length){
				var nbNewXAxis = data.series[i].data.length - newData.xAxis.categories.length;
				for(var j=0 ; j<nbNewXAxis ; j++){
					var dat = {};
					dat.y = 0;
					dat.r = data.series[0].data[j].r;
					newData.series[i].data.splice(0,0,dat);
				}
			}
		}
		
		//Push the new series
		for(var i=0 ; i<newData.series.length ; i++){
			data.series.push(newData.series[i]);
		}
		
		return data;
	}else{
		newData.isMerged = true;
		return newData;
	}
};

var refineChartWithInput = function(uCssId,facetName,updateIds,searchWithId,urlPage,searchServerName){
	//We get the search
	var searchText = document.getElementById('input-filter-chart-'+uCssId).value;
	
	if(searchWithId){
		if(searchText != undefined && searchText != ''){
			$.ajax({
				type:'GET',
				url:urlPage+'/'+facetName+'/cat/'+ encodeURIComponent(searchText),
				data: {searchServerName: searchServerName},
				success:function(data) {
					updateWidgetWithSuggest(uCssId,facetName,updateIds,searchText,data);
				}
			})
		}else{
			updateWidgetWithSuggest(uCssId,facetName,undefined,searchText,'');
		}
	}else{
		updateWidgetWithSuggest(uCssId,facetName,updateIds,searchText,searchText);
	}
};

var updateWidgetWithSuggest = function(uCssId,facetName,updateIds,searchText,catId){
	var client = new PlmaAjaxClient($('.'+uCssId));
	client.addWidget(uCssId);
	if(updateIds != undefined && updateIds != "[]"){
		updateIds = updateIds.replace('[','');
		updateIds = updateIds.replace(']','');
		updateIds = updateIds.split(', ');
		for(var i=0 ; i<updateIds.length ; i++){
			client.addWidget(updateIds[i]);
		}
	}
	if(searchText != undefined && searchText != ""){
		client.addParameter('q',facetName+': '+catId);
	}
	client.addParameter('currentSearch',searchText);
	client.update();
};

var toggleInputSearch = function(cssId){
	var formFilterChartClassList = document.getElementById(cssId).getElementsByClassName('form-filter-chart')[0].getClassList(); 
	formFilterChartClassList.toggle('hidden');
	if(formFilterChartClassList.contains('hidden')){
		$('#'+cssId+' .form-filter-chart').fadeOut();
	}else{
		$('#'+cssId+' .form-filter-chart').fadeIn();
	}
};

$(function() {
	
	if(window != undefined && window.location.hash != undefined && window.location.hash != ""){
		var anchor = window.location.hash;
		try {
			if($(anchor).length>0){
				$(anchor)[0].getClassList().add('selectedChartFromHome');
			}
		} catch (error) {
			/* The anchor passed is not a valid jQuery selector */
		}
	}
});

