<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<render:import parameters="widget,uCssId,feed,collection,urlParameter,compactHeader,buttonPrefix,actionName,collectionMode,displayDeleteButton,compareCollection" ignore="true"/>

<request:getParameterValue name="${urlParameter}" var="phDisplayed"/>

<c:choose>
    <c:when test="${collection == phDisplayed}">
        <c:set var="colStatus" value="collection-displayed"/>
    </c:when>
    <c:otherwise>
        <c:set var="colStatus" value="collection-hidden"/>
    </c:otherwise>
</c:choose>

<c:choose>
    <c:when test="${compactHeader}">
        <c:set var="headerClass" value="compact-header"/>
    </c:when>
    <c:otherwise>
        <c:set var="headerClass" value=""/>
    </c:otherwise>
</c:choose>

<plma:countPreferredHits collection="${collection}" var="nbFavorites"/>

<div class="preferred-hits ${colStatus} ${headerClass}">
    <div class="collection-header">
        <span class="action-detail label-section">
            <span class="favorites-actions fonticon fonticon-favorite-on ${nbFavorites < plma:getIntegerParam(widget, 'minFavoriteSize', 1) ? 'hidden' : ''}" data-min-size="1">
                <span class="counter count-aware" data-count="${nbFavorites}">${nbFavorites}</span>
            </span>
            <span class="title"><config:getOption name="title" defaultValue=""/></span>
        </span>
        <i18n:message var="defaultText" code="preferredHits.delete"/>
        <c:if test="${displayDeleteButton}">
            <span class="action-clear count-aware fonticon fonticon-favorite-delete" id="clear-preferred-hits" data-count="${nbFavorites}"
                title="<i18n:message code='plma.hits.collection.preferred.labels.trashTitle' text='Remove'/>"></span>
        </c:if>
    </div>
</div>

<render:renderScript position="READY">
    var options = {};
    options.url = '<c:url value="/preferredhit"/>';
    options.page = '<search:getPageName/>';
    options.collection = '${collection}';
    options.urlParameter = '${urlParameter}';
    options.compareCollection = '${compareCollection}'

    new PreferredHits('${uCssId}',options);
</render:renderScript>