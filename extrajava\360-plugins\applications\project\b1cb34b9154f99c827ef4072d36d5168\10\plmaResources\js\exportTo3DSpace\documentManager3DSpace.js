var DocumentManager3DSpace = function($uiContainer, options) {
    this.$uiContainer = $uiContainer
    this.options = $.extend({}, DocumentManager3DSpace.DEFAULT_OPTIONS, options);
    this.$elements = {};
    this.isPreviewCreated = $.Deferred();
	this.init();
	return this;
}

DocumentManager3DSpace.REGEX_EXT = /(?:\.([^.]+))?$/
DocumentManager3DSpace.DEFAULT_OPTIONS = {
    spaceUrl: undefined,
	enableAddNewDocument: false,
	fileExtension: undefined,
	fileMode: true,
	isBookmarkEnable: false
}
DocumentManager3DSpace.prototype.getMessage = function (code) {
    return mashupI18N.get('plmaResources', code);
}
DocumentManager3DSpace.prototype.previewLoaded = function(previewInfo){
    if(this.options.fileMode){
        if(previewInfo.getDataUrl == undefined || typeof previewInfo.getDataUrl != 'function'){
            $.notify(this.getMessage('plma.exportto3dspace.error.configurations'), 'error');
            throw Error("Need a function 'getDataUrl' to get the DataUrl of the Content to be Exported");
        }
        if(previewInfo.fileName == undefined){
            $.notify(this.getMessage('plma.exportto3dspace.error.configurations'), 'error');
            throw Error("Need a string for 'fileName' to be used for fileName");
        }
    }

    this.previewInfo = previewInfo;
    this.isPreviewCreated.resolve(true);
    if(this.$elements.EXPORT_BUTTON){ // Not created when loaded in standalone mode.
        this.$elements.EXPORT_BUTTON.attr('disabled', null);
    }
    if(this.$elements.FILE_INFO){
        let $input = this.$elements.FILE_INFO.find('.select-editable input');
        $input.val(this.previewInfo.fileName);
        $input.attr('disabled', null);
    }
}
//=================================================================================
DocumentManager3DSpace.prototype.init = function(){
    if (!window.dashboardController) {
        $.notify(this.getMessage('plma.exportto3dspace.error.outside.3dexp'), 'warn');
        return;
    }

	new Promise(function(resolve, reject){
		if(this.options.isBookmarkEnable){
			window.dashboardController.getUserDetails('current', function(userInfo){
				if(userInfo.getPreferredSecurityContext()){
					resolve(userInfo.getPreferredSecurityContext());
				}else if(userInfo.getAllSecurityContexts().length > 0){
					// When pref sec ctx is unset, use any of the security context from the any collabspace.
					resolve(userInfo.getAllSecurityContexts()[0]);
				}else{
					reject("Unable to get security context of Current user");
				}
			});
		}else{
			resolve(undefined);
		}
	}.bind(this)).then(function(securityContext){
		this.options.securityContext = securityContext;
		this.initUI();
	}.bind(this));
}
DocumentManager3DSpace.prototype.initUI = function(){
    let $controls = $('<div class="controls"></div>');
    this.$uiContainer.append($controls);

    // Add new button Checkbox/Switch
    if(this.options.enableAddNewDocument){
        this.$elements.ADD_NEW_DOC_CHKBOX =
            $('<div class="add-new-document toggle toggle-switch">' +
                '<input id="addnewdoc" type="checkbox">' +
                '<label class="control-label label" for="addnewdoc">'+
                    this.getMessage('plma.exportto3dspace.adddocchkbox.label') +
                '</label>' +
            '</div>');
        $controls.append(this.$elements.ADD_NEW_DOC_CHKBOX);
        this.$elements.ADD_NEW_DOC_CHKBOX.find('#addnewdoc').on('click', this._toggleNewDocument.bind(this));
    }
	
    //If Publish to Bookmark is Enabled Add DropDown Menus to display available Bookmarks and its Documents
    if(this.options.isBookmarkEnable){
        this.$elements.SELECTION_AREA = $('<div class="selectors-list"></div>');
		this.$elements.SELECTION_AREA.append($('<div class="bookmark-selector">' + 
			'<select class="selector">' +
				'<option class="placeholder" value="-1" disabled selected>' +
					'Select Bookmark' +
				'</option>' +
			'</select>' +
		'</div>'));
		this.$elements.SELECTION_AREA.append($('<div class="document-selector">' + 
			'<select class="selector">' +
				'<option class="placeholder" value="-1" disabled selected>' +
					'Select Document' +
				'</option>' +
			'</select>' +
		'</div>'));
		$controls.append(this.$elements.SELECTION_AREA);
		this.$elements.BOOKMARK_SELECTOR = this.$elements.SELECTION_AREA.find('.bookmark-selector .selector');
		this.$elements.DOCUMENT_SELECTOR = this.$elements.SELECTION_AREA.find('.document-selector .selector');
		this.fillBookmarkList(this.$elements.BOOKMARK_SELECTOR);
		
		this.$elements.BOOKMARK_SELECTOR.on('change', function(){
			let bookmarkId = this.$elements.BOOKMARK_SELECTOR.find(":selected").val();
			this.fillDocumentList(bookmarkId, this.$elements.DOCUMENT_SELECTOR);
		}.bind(this));
		
		this.$elements.DOCUMENT_SELECTOR.on('change', function(){
			if(this.$elements.DOCUMENT_SELECTOR.find(":selected").val()!= '-1'){
				this.renderDocumentInfo(this.$elements.DOCUMENT_SELECTOR.find(":selected").data('documentInfo'));
			}
		}.bind(this));
    }else{
		// Drop Area
		this.$elements.DROP_AREA =
			$('<div class="drop-area">' +
				'<span class="fonticon fonticon-download"></span>' +
				'<span class="label">' + this.getMessage('plma.exportto3dspace.drop.label') +'</span>' +
				'<span class="disabled-overlay"></span>' +
			'</div>');
		$controls.append(this.$elements.DROP_AREA);
		this._enableReceive();
	}
	
	// Export Button
	this.$elements.EXPORT_BUTTON  =
		$('<button type="button" class="btn btn-primary btn-sm export-button" disabled>'+
			this.getMessage('plma.exportto3dspace.publish.label') +
		'</button>');
	this.isPreviewCreated.then(function(created){
        this.$elements.EXPORT_BUTTON.attr('disabled', null);
    }.bind(this));
	$controls.append(this.$elements.EXPORT_BUTTON);
	this.$elements.EXPORT_BUTTON.on('click', this.performExport.bind(this));

}
DocumentManager3DSpace.prototype._toggleNewDocument = function(e){
    if(e.target.checked == true){
        if(this.$elements.DROP_AREA){ 
			this.$elements.DROP_AREA.addClass('disabled'); 
		}
		if(this.$elements.DOCUMENT_SELECTOR){
			this.$elements.DOCUMENT_SELECTOR.attr('disabled','');
		}
        this.options.isNewDocument = true;
        this.renderDocumentInfo(); // With empty data as new document needs to be created.
    }else{
		if(this.$elements.DOCUMENT_INFO_CONTAINER !== undefined){
            this.$elements.DOCUMENT_INFO_CONTAINER.remove();
        }
        if(this.$elements.DROP_AREA){ 
			this.$elements.DROP_AREA.removeClass('disabled'); 
		}
		if(this.$elements.DOCUMENT_SELECTOR){
			this.$elements.DOCUMENT_SELECTOR.attr('disabled',null);
			this.$elements.DOCUMENT_SELECTOR.trigger('change');
		}
        this.options.isNewDocument = false;        
    }
}
DocumentManager3DSpace.prototype._enableReceive = function(){
    if (!window.dashboardController) {
        $.notify(this.getMessage('plma.exportto3dspace.error.outside.3dexp'), 'warn');
        return;
    }
    let receiveOptions = {
        acceptedTypes: ['text/plain','Text'],
        enableSubscribe: true,
        subscribeChannel: 'com.ds.compass',
        subscribeMethod: 'PubSub'
    }
    let that = this;
    function trapEvent(event) {
        event.preventDefault();
        if(event.type === "dragenter" ){
            that.$elements.DROP_AREA.css('background-color', '#e2e4e3');
        }
        if(event.type === "dragleave" ){
            that.$elements.DROP_AREA.css('background-color', '');
        }
    }
    function acceptedContextTypes(candidates){
        return  receiveOptions.acceptedTypes.filter(function(type) {
            return candidates.contains ? candidates.contains(type) : candidates.indexOf(type) > -1;
        });
    }

    if (receiveOptions.acceptedTypes.length > 0) {
        var domElement = this.$elements.DROP_AREA.get(0);
        domElement.addEventListener("dragover", trapEvent);
        domElement.addEventListener("dragenter", trapEvent);
        domElement.addEventListener("dragleave", trapEvent);
        domElement.addEventListener("drop", (function(event) {
			if (event.preventDefault) {
				event.preventDefault();
			}
			if (event.stopPropagation) {
				event.stopPropagation();
			}
			if(that.$elements.DROP_AREA.hasClass('disabled')){
				that.$elements.DROP_AREA.css("background-color", "#EA4F37")
					.animate({ backgroundColor: "#FFFFFF"}, 2000);
				return;
			}
            that.$elements.DROP_AREA.css('background-color', '');
            var accepted = acceptedContextTypes(event.dataTransfer.types);
            if (accepted.length > 0) {
                this.getDocumentInfo(event.dataTransfer.getData(accepted[0]));
            }
        }).bind(this));
    }

    if (receiveOptions.enableSubscribe === true) {
        var usePubSub = receiveOptions.subscribeMethod === "PubSub";
        window.dashboardController.subscribe(receiveOptions.subscribeChannel, usePubSub, this.getDocumentInfo.bind(this));
    }
}
//=================================================================================
DocumentManager3DSpace.prototype.isSupported = function(itemData){
    // Define Support the Export only for objects which are received by Drop feature.
    // Case1 : type == "Document" fully supported.
    // Case2 : isDocumentType = TRUE fully supported. >>
    // Case3 : Objects having atleast one file attached(meaning the object supports the fill attachments).
    // Rest all Reject and throw error.
    var supported = itemData.type === "Document" ||
            itemData.dataelements.isDocumentType === "TRUE" ||
            itemData.relateddata.files.length > 0;
    if(supported == false){
        // Need to check if the Document API's work for Non Document types. Till then notify error.
        $.notify('[' + itemData.type + '] ' + this.getMessage('plma.exportto3dspace.error.unsupported.type'), 'error');
    }
    if(itemData.dataelements.hasModifyAccess == 'FALSE'){
        $.notify(this.getMessage('plma.exportto3dspace.error.insufficient.access'), 'error');
    }
    return supported;
}
DocumentManager3DSpace.prototype.buildDocumentInfo = function(itemData){
    if(itemData == undefined || !this.isSupported(itemData)){
        return null;
    }

    // Create documentInfo in The format that is useful for Updating/creating
    // Document in platform when the Export Happens.
    let documentInfo = { dataelements: {}, relateddata: { files: [] } };
    documentInfo.id = itemData.id;
    documentInfo.type = itemData.type;
    documentInfo.relativePath = itemData.relativePath;
    documentInfo.source = itemData.source;
    documentInfo.updateAction = "NONE";
    documentInfo.dataelements.name = itemData.dataelements.name;
    documentInfo.dataelements.title = itemData.dataelements.title;
    documentInfo.dataelements.description = itemData.dataelements.description;
    documentInfo.dataelements.typeicon = itemData.dataelements.typeicon;

    let files = itemData.relateddata.files;
    for(let fileIndex = 0; fileIndex < files.length; fileIndex++){
        let fileData = {
            id: files[fileIndex].id,
            updateAction: 'NONE',
            dataelements: {
                title: files[fileIndex].dataelements.title,
                comments: files[fileIndex].dataelements.comments,
                locker: files[fileIndex].dataelements.locker
            }
        }
        documentInfo.relateddata.files.push(fileData);
    }
    return documentInfo;
}
DocumentManager3DSpace.prototype.renderDocumentInfo = function(documentInfo){
    // Recreate the whole UI based on the data provided.
    // Tricky to change values of every element, and keep track of state of each control.
    // hence remove old UI and recreate.
    if(this.$elements.DOCUMENT_INFO_CONTAINER !== undefined){
        this.$elements.DOCUMENT_INFO_CONTAINER.remove();
    }
    this.$elements.DOCUMENT_INFO_CONTAINER = $('<div class="document-info-container"></div>');
    this.$uiContainer.append(this.$elements.DOCUMENT_INFO_CONTAINER);

    let $el;
    let documentExists = (documentInfo != undefined);
    if(documentExists){
		// Keep the documentInfo object for referencing in Export.
		this.$elements.DOCUMENT_INFO_CONTAINER.data('documentInfo', documentInfo);
    }

    //-------------------------------------------------------------------------
    // Document Information Section
    this.$elements.DOC_INFO =
        $('<fieldset class="info-container document-info">' +
            '<legend class="section-title">' +
                (documentExists ?  ('<img src="'+ documentInfo.dataelements.typeicon + '"/>') : '') +
                this.getMessage('plma.exportto3dspace.docinfo.label') +
            '</legend>'+
        '</fieldset>');
    this.$elements.DOCUMENT_INFO_CONTAINER.append(this.$elements.DOC_INFO);

    // Document Name
    $el = $('<div class="label-conatiner name">' +
        '<span class="label">' + this.getMessage('plma.exportto3dspace.nameinput.label') + '</span>' +
        '<input type="text" class="value" placeholder="' +
            this.getMessage('plma.exportto3dspace.nameinput.placeHolder') + '"/>' +
    '</div>');
    if(documentExists){
        $el.find('input').val(documentInfo.dataelements.name);
        $el.find('input').attr('disabled','');
    }
    this.$elements.DOC_INFO.append($el);

    // Document Title
    $el = $('<div class="label-conatiner title">' +
        '<span class="label">'+ this.getMessage('plma.exportto3dspace.titleinput.label') +
            '<span class="required"> *</span>' +
        '</span>' +
        '<input type="text" class="value"/>' +
    '</div>')
    if(documentExists){
        $el.find('input').val(documentInfo.dataelements.title);
        $el.find('input').attr('disabled','');
    }
    this.$elements.DOC_INFO.append($el);

    // Document Description
    $el = $('<div class="label-conatiner description">' +
        '<span class="label">'+ this.getMessage('plma.exportto3dspace.descriptioninput.label') + '</span>' +
        '<textarea rows="1" class="value"/>' +
    '</div>')
    if(documentExists){
        $el.find('textarea').val(documentInfo.dataelements.description);
        $el.find('textarea').attr('disabled','');
    }
    this.$elements.DOC_INFO.append($el);

    if(this.options.fileMode == false){
		this.$elements.FILE_INFO = $();
		$el.find('textarea').attr('disabled',null);// when the fileMode is false, enable document description update.
        return;
    }
    //-------------------------------------------------------------------------
    // File Information Section
    this.$elements.FILE_INFO =
        $('<fieldset class="info-container file-info">' +
            '<legend class="section-title">' +
                this.getMessage('plma.exportto3dspace.fileinfo.label') +
            '</legend>' +
        '</fieldset>');
    this.$elements.DOCUMENT_INFO_CONTAINER.append(this.$elements.FILE_INFO);

    // Toggle New File Creation : Enable only when document Exists in Platform.
    if(documentExists){
        $el = $('<div class="label-conatiner add-new-file toggle toggle-primary">' +
            '<input id="addnewfile" type="checkbox">' +
            '<label class="control-label" for="addnewfile">'+
                this.getMessage('plma.exportto3dspace.addfilechkbox.label') +
            '</label>' +
        '</div>')
        this.$elements.FILE_INFO.append($el);
    }

    // File Name Input/Selection
    let fileName = this.previewInfo ? this.previewInfo.fileName : '';
    $el = $('<div class="label-conatiner name">' +
        '<span class="label">' + this.getMessage('plma.exportto3dspace.nameinput.label') +
            '<span class="required"> *</span>'+
        '</span>' +
        '<span class="value select-editable">' +
            '<input type="text" class="input-file-name" name="filename" value="' + fileName + '" ' +
                (fileName === ''? '' : 'disabled') +
            '/>' +
        '</span>' +
    '</div>')
    if(documentExists){
        let files = documentInfo.relateddata.files;
        if(files.length == 0){
            // If there are no files for the Document, Enable only New File Mode.
            this.$elements.FILE_INFO.find('#addnewfile').prop('checked', true);
            this.$elements.FILE_INFO.find('#addnewfile').attr('disabled', '');
        }else{
            $el.find('input').addClass('hidden');
            let $fileSelect = $('<select class="file-selector"></select>');
            if(files.length > 1){
                // Enable default option only when we have multiple files to choose from.
                $fileSelect.append('<option value="-1" disabled selected>' +
                    this.getMessage('plma.exportto3dspace.fileselect.placeholder') +
                '</option>');
            }
            for(let it = 0; it < files.length; it++){
                // If only one file and not disabled, then mark it selected

				// Disable the file selection when the ext is different or the file is locked/checkedout already
                let ext = DocumentManager3DSpace.REGEX_EXT.exec( files[it].dataelements.title)[1];
                let disabled = (ext !== this.options.fileExtension );
                let locked = files[it].dataelements.locker && files[it].dataelements.locker.length > 0;

                $fileSelect.append(
                    $('<option value="' + it + '"' +
                        (files.length == 1 && !disabled? ' selected' : '') +
                        (disabled || locked? ' disabled' : '') + '>' +
                        (locked? '&#9919;' : (disabled? '&#9746;' : '&#9745;')) +
                        '&nbsp;' + files[it].dataelements.title +
                    '</option>'));
            }
            $el.find('.select-editable').append($fileSelect);
        }
    }
    this.$elements.FILE_INFO.append($el);

    //File Comments
    let defaultComments = '' + moment().format('DD MMM, YYYY (hh:mm:ss A)') + ': ' +
        this.getMessage('plma.exportto3dspace.commentinput.defaultcomments');
    $el = $('<div class="label-conatiner comments">' +
        '<span class="label">' +
			this.getMessage('plma.exportto3dspace.commentinput.label') +
		'</span>' +
        '<textarea rows="1" class="value"/>' +
    '</div>');
    $el.find('textarea').val(defaultComments);
    this.$elements.FILE_INFO.append($el);

    if(documentExists){
        this._toggleNewFile();
    }

}

DocumentManager3DSpace.prototype._toggleNewFile = function(){
    let $addNewFile = this.$elements.FILE_INFO.find('#addnewfile');
    let $select = this.$elements.FILE_INFO.find('.label-conatiner.name select');
    let $input = this.$elements.FILE_INFO.find('.label-conatiner.name input');
    $addNewFile.on('click', (function(e){
		if($input.attr('disabled') != undefined && this.previewInfo){
			$input.attr('disabled', null);
		}
        $input.toggleClass('hidden');
        $select.toggleClass('hidden');
    }).bind(this));
}
//=================================================================================
DocumentManager3DSpace.prototype.getDocumentInfo = function(data){
    // Get the Dropped Item Data. Only used full info is objectId & contextId
    let itemData = JSON.parse(data).data.items[0];
    let that = this;
    function onFailure(data){
        that.$elements.DROP_AREA.hidePLMASpinner();
        $.notify(that.getMessage('plma.exportto3dspace.error.apiexecution'), 'error');
        throw '[' + data.stage + ']' + data.error;
    }

    EXPUtils.getWafData().then(function(API){
        that.$elements.DROP_AREA.showPLMASpinner({overlay: true});
        // URL -> CSRF -> DocInfo -> RenderData
        let data = {
            API: API,
            platformUrl: that.options.spaceUrl,
            securityContext: itemData.contextId,
            documentInfo: {
                id: itemData.objectId
            },
            failureCallback: onFailure,
            successCallback: function(data){
                // Received URL
                data.successCallback = function(data){
                    // Received CSRF
                    data.successCallback = function(data){
                        // Received DocInfo
                        let documentInfo = that.buildDocumentInfo(data.result);
                        if(documentInfo != undefined){
							that.renderDocumentInfo(documentInfo);
                        }
                        that.$elements.DROP_AREA.hidePLMASpinner();
                    }
                    EXPUtils.fetchDocumentInfo(data);
                }
                EXPUtils.fetchCSRFToken(data);
            }
        };
        EXPUtils.fetch3DSpaceUrl(data)
    })
    ['catch'](function (err) {
        onFailure({
            stage: 'GetWafData',
            error: 'Unable to get WAFData [' + err + ']'
        });
    }.bind(this));
}

//=================================================================================
DocumentManager3DSpace.prototype.fillBookmarkList = function($bookmarkSelector){
    let that = this;
    function onFailure(data){
        that.$elements.SELECTION_AREA.hidePLMASpinner();
        $.notify(that.getMessage('plma.exportto3dspace.error.apiexecution'), 'error');
        throw '[' + data.stage + ']' + data.error;
    }
	var renderBookmarkList = function(bookmarkList){
		bookmarkList.member.forEach(function(bookmarkObj, index){
			$bookmarkSelector.append($('<option value="' + bookmarkObj.id + '" >' +
					bookmarkObj.title +
				'</option>'));
			if(bookmarkList.member.length == 1)
			   $bookmarkSelector.find('option:last-child').trigger('click');
		});
	}

    EXPUtils.getWafData().then(function(API){
        that.$elements.SELECTION_AREA.showPLMASpinner({overlay: true});
        // URL -> CSRF -> Available Bookmarks -> RenderData
        let data = {
            API: API,
            platformUrl: that.options.spaceUrl,
            securityContext: that.options.securityContext,
            failureCallback: onFailure,
            successCallback: function(data){
                // Received URL
                data.successCallback = function(data){
                    // Received CSRF
                    data.successCallback = function(data){
                        // Received BookmarkList
						that.$elements.SELECTION_AREA.hidePLMASpinner();
                        if(data.result.member.length == 0) {
                            $.notify(that.getMessage('plma.exportto3dspace.error.nobookmark'),'error');                            
                        }else{
							renderBookmarkList(data.result);
						}
                    }
					EXPUtils.fetchBookMarksList(data);
                }
                EXPUtils.fetchCSRFToken(data);
            }
        };
        EXPUtils.fetch3DSpaceUrl(data)
    })
    ['catch'](function (err) {
        onFailure({
            stage: 'GetWafData',
            error: 'Unable to get WAFData [' + err + ']'
        });
    }.bind(this));
}
DocumentManager3DSpace.prototype.fillDocumentList = function(bookmarkId, $documentSelector){
	$documentSelector.find(':not(.placeholder)').remove();
	$documentSelector.find('.placeholder').attr('selected','selected');
	
	let that = this;
	function onFailure(data){
		that.$elements.SELECTION_AREA.hidePLMASpinner();
		$.notify(that.getMessage('plma.exportto3dspace.error.apiexecution'), 'error');
		throw '[' + data.stage + ']' + data.error;
	}
	var addToDocumentsList = function(documentInfo){
		$documentSelector.append($('<option value="' + documentInfo.id + '" >' +
			documentInfo.dataelements.name +
		'</option>').data('documentInfo', documentInfo));			
	}
	
	var getBookmarksDocInfo = function(data, markSelected){
		let that = this;
		that.$elements.SELECTION_AREA.showPLMASpinner({overlay: true});
		data.successCallback = function(data){
			// Received DocInfo
			let documentInfo = that.buildDocumentInfo(data.result);
			if(documentInfo != undefined){
				addToDocumentsList(documentInfo);
				if(markSelected){
					$documentSelector.find('option[selected]').attr('selected', null);
					$documentSelector.find('option:last-child')
						.val(documentInfo.id).attr('selected','selected')
						.trigger('change');
				}
			}
			that.$elements.SELECTION_AREA.hidePLMASpinner();
		}
		EXPUtils.fetchDocumentInfo(data);
	}.bind(this);
	
	EXPUtils.getWafData().then(function(API){
		that.$elements.SELECTION_AREA.showPLMASpinner({overlay: true});
		// URL -> CSRF -> BookmarkItems -> RenderData
		let data = {
			API: API,
			platformUrl: that.options.spaceUrl,
			securityContext: that.options.securityContext,
			bookmarkId: bookmarkId,
			failureCallback: onFailure,
			successCallback: function(data){
				// Received URL
				data.successCallback = function(data){
					// Received CSRF
					data.successCallback = function(data){
						// Received the Items connected to Bookmark. 
						// We only get the id here, hecen need to bring the details.
						if(data.result){
							data.result.forEach(function(obj,index){
								if(obj.referencedObject.type == 'Document'){
									let newData = Object.create(data);
									newData.documentInfo = {
										id: obj.referencedObject.identifier
									};
									getBookmarksDocInfo(newData, !that.options.isNewDocument && data.result.length == 1);
								}
							})
						}
						that.$elements.SELECTION_AREA.hidePLMASpinner();
					}
					EXPUtils.fetchBookMarkItems(data);
				}
				EXPUtils.fetchCSRFToken(data);
			}
		};
		EXPUtils.fetch3DSpaceUrl(data)
	})
	['catch'](function (err) {
		onFailure({
			stage: 'GetWafData',
			error: 'Unable to get WAFData [' + err + ']'
		});
	}.bind(this));
}
//=================================================================================
DocumentManager3DSpace.prototype.prepareData = function(){
    if(this.$elements.DOCUMENT_INFO_CONTAINER == undefined){
        $.notify(this.getMessage('plma.exportto3dspace.controlvalidation.doc'), 'error');
        return;
    }

    let documentInfo = this.$elements.DOCUMENT_INFO_CONTAINER.data('documentInfo');
    let $docInfo = this.$elements.DOC_INFO;
    let $fileInfo = this.$elements.FILE_INFO;

    let newDocumentMode = (documentInfo == undefined);
    let newFileMode = this.options.fileMode && (newDocumentMode == true ||
        $fileInfo.find('.add-new-file #addnewfile').is(':checked'));

    // Update the information as per the User.
    if(newDocumentMode){
        // Validate
        if(this.options.isBookmarkEnable && this.$elements.BOOKMARK_SELECTOR.find(":selected").val() == -1){
            $.notify(this.getMessage('plma.exportto3dspace.controlvalidation.bookmark'), 'error');
            return;
        }

        let docTitle = $docInfo.find('.label-conatiner.title input').val().trim();
        if(docTitle == ""){
            $docInfo.find('.label-conatiner.title').addClass('has-error');
            $.notify(this.getMessage('plma.exportto3dspace.controlvalidation.doc.title'), 'error');
            return;
        }
        $docInfo.find('.label-conatiner.title').removeClass('has-error');

        // 1. New Document : Creation
        documentInfo = { dataelements: {}, relateddata: { files: [] } };
        documentInfo.tempId = EXPUtils.generateTempId();
        documentInfo.updateAction = "CREATE";
        documentInfo.dataelements.name = $docInfo.find('.label-conatiner.name input').val().trim();
        documentInfo.dataelements.title = docTitle;
        documentInfo.dataelements.description = $docInfo.find('.label-conatiner.description textarea').val().trim();

        //In case the Name is not provided enable auto name Auto Name, by removing name property.
        if(documentInfo.dataelements.name == ""){
            delete documentInfo.dataelements.name;
        }
    }else{
        // Validate
        if(this.options.isBookmarkEnable && (this.$elements.BOOKMARK_SELECTOR.find(":selected").val() == -1 ||
			this.$elements.DOCUMENT_SELECTOR.find(":selected").val() == -1)){
            $.notify(this.getMessage('plma.exportto3dspace.controlvalidation.bookmark.doc'), 'error');
            return;
        }
        // need to deepcopy the object to avoid any changes in original data retrived.
        // This can happen because of any failures during the actual API calls.
        documentInfo = JSON.parse(JSON.stringify(documentInfo));
    }
	documentInfo.dataelements.description = $docInfo.find('.label-conatiner.description textarea').val().trim();
    if(this.options.fileMode == false){        
        if(this.previewInfo.updateDocInfo){
			this.previewInfo.updateDocInfo(documentInfo);
		}
        return { 
			documentInfo: documentInfo 
		};
    }

    let fileRefIndex = 0;
    if(newFileMode){
        // Validate
        let fileName = $fileInfo.find('.label-conatiner.name input').val().trim();
        if(fileName == ""){
            $.notify(this.getMessage('plma.exportto3dspace.controlvalidation.file.name'), 'error');
            $fileInfo.find('.label-conatiner.name').addClass('has-error');
            return;
        }
        let ext = DocumentManager3DSpace.REGEX_EXT.exec(fileName.toLowerCase())[1];
        if( ext !== this.options.fileExtension ){
            $.notify(this.getMessage('plma.exportto3dspace.controlvalidation.file.ext'), 'error');
            $fileInfo.find('.label-conatiner.name').addClass('has-error');
            return;
        }

        let existingFiles = $fileInfo.find('.label-conatiner.name select option').text();
        if(existingFiles != undefined && existingFiles != "" && existingFiles.search(fileName) != -1){
            $.notify(this.getMessage('plma.exportto3dspace.controlvalidation.file.duplicate'), 'error');
            $fileInfo.find('.label-conatiner.name').addClass('has-error');
            return;
        }
        $fileInfo.find('.label-conatiner.name').removeClass('has-error');

        // 2. Existing Document but New File : Creation
        documentInfo.relateddata.files.push({
            updateAction: "CREATE",
            dataelements: {
                title: fileName,
                comments: $fileInfo.find('.label-conatiner.comments textarea').val().trim()
            }
        });
        fileRefIndex = documentInfo.relateddata.files.length - 1;
    }else{
        // Validate
        let fileIndex = $fileInfo.find('.label-conatiner.name select').get(0).value;
         // No option Selected and has only one file(fileIndex == '') or 'Select File'=-1 option is selected
         // Incase of multiple files.
        if( fileIndex == "" || fileIndex == -1 || fileIndex >= documentInfo.relateddata.files.length) {
            $.notify(this.getMessage('plma.exportto3dspace.controlvalidation.file.notselected'), 'error');
            $fileInfo.find('.label-conatiner.name').addClass('has-error');
            return;
        }
        $fileInfo.find('.label-conatiner.name').removeClass('has-error');

        // 3. Existing Document and Existing File : Update.
        let file = documentInfo.relateddata.files[fileIndex];
        file.updateAction = "REVISE";
        file.dataelements.comments = $fileInfo.find('.label-conatiner.comments textarea').val().trim();

        fileRefIndex = fileIndex;
    }
	if(this.previewInfo.updateDocInfo){
		this.previewInfo.updateDocInfo(documentInfo, fileRefIndex);
	}
    return {
        documentInfo: documentInfo,
        fileRef: documentInfo.relateddata.files[fileRefIndex]
    }
}
DocumentManager3DSpace.prototype.performExport = function(){
    let documentData = this.prepareData();
    if(documentData == undefined){
        return;
    }

    // Start Actual API Calls.
    let that = this;
    function onFailure(data){
        that.$uiContainer.hidePLMASpinner();
        $.notify(that.getMessage('plma.exportto3dspace.error.apiexecution'), 'error');
        throw '[' + data.stage + ']' + data.error;
    }

    EXPUtils.getWafData().then(function(API){
        that.$uiContainer.showPLMASpinner({overlay: true});
        // URL -> CSRF -> CheckOut -> CheckIn -> Upload -> Update Document.
        let data = {
            API: API,
            platformUrl: that.options.spaceUrl,
            securityContext: that.options.securityContext,
            documentInfo: documentData.documentInfo,
            fileRef: documentData.fileRef,
            getDataUrl: that.previewInfo.getDataUrl,
            failureCallback: onFailure,
            successCallback: function(data){
                // Received URL : data.platformUrl
                data.successCallback = function(data){
                    // Received CSRF : data.csrf
					data.successCallback = function(data){
						// Received CheckOutTicket : data.checkOutTicket
						data.successCallback = function(data){
							// Received CheckInTicket : data.checkInTicket
							data.successCallback = function(data){
								// Received FCS Receipt.
								data.successCallback = function(data){
									if(!that.options.isBookmarkEnable || !that.options.isNewDocument){
										that.$uiContainer.hidePLMASpinner();
										that.handleExportEnd(data.documentInfo);
										return;
									}
									// Get Document Info
									data.successCallback = function(data){
										// Attach New document to bookmark
										data.securityContext = that.options.securityContext;
										data.bookmark = {
											id: that.$elements.BOOKMARK_SELECTOR.find(":selected").val(),
											documentData: [{
											   identifier: data.result.id,
											   type: data.result.type,
											   relativePath: data.result.relativePath ? data.result.relativePath :
													'/resources/v1/modeler/documents/' + data.result.id,
											   source: data.result.source ? data.result.source : data.platformUrl
											}]
										};
										let docResults = Object.create(data.result);
										data.successCallback = function(data){
											that.$uiContainer.hidePLMASpinner();
											that.handleExportEnd(docResults);
										}
										EXPUtils.attachToBookmark(data);
									}
									data.documentInfo.id = data.result.id;
									EXPUtils.fetchDocumentInfo(data);
								}
								EXPUtils.modifyCreateDocument(data);
							}
							EXPUtils.uploadFile(data);
						}
						EXPUtils.fetchCheckInTicket(data);
					}
					EXPUtils.fetchCheckoutTicket(data);
                }
                EXPUtils.fetchCSRFToken(data);
            }
        };
        EXPUtils.fetch3DSpaceUrl(data)
    })
    ['catch'](function (err) {
        onFailure({
            stage: 'GetWafData',
            error: 'Unable to get WAFData [' + err + ']'
        });
    }.bind(this));
}
DocumentManager3DSpace.prototype.handleExportEnd = function(documentInfo){
    this.$elements.EXPORT_BUTTON.parent().remove();
    let $draggableOverlay = $('<div class="plmalightbox-overlay export-done-overlay visible">' +
            '<span class="label">' + this.getMessage('plma.exportto3dspace.success.done') + '</span>' +
        '</div>')
    this.$uiContainer.append($draggableOverlay);
    this.$uiContainer.css('padding-top', '15px');
    this.renderDocumentInfo(documentInfo);
    $.notify(this.getMessage('plma.exportto3dspace.success.done'), 'info');
    this._enableShare($draggableOverlay, documentInfo);
}
DocumentManager3DSpace.prototype._enableShare = function($container, documentInfo){
    if (!window.dashboardController) {
        $.notify(this.getMessage('plma.exportto3dspace.error.outside.3dexp'), 'error');
        throw 'Not expected that _enableShre function will be called'
    }
    if(!documentInfo.id || !documentInfo.dataelements || (!documentInfo.dataelements.title && !documentInfo.dataelements.name)){
        throw 'Can not enable Drag with empty document information(id,title||name are must)' + documentInfo;
    }

    $container.html($('<span class="fonticon fonticon-drag-drop"></span>' +
        '<span class="label">' +
            this.getMessage('plma.exportto3dspace.success.share.object') +
            ' [' + documentInfo.dataelements.name + ': ' + documentInfo.dataelements.title + ']' +
        '</span>'));

    let widgetInfos = window.dashboardController.widgetInfos;
    let shareOptions = {
        data: { content: {}, context: { appId: widgetInfos.appId, serviceId: "3DSpace" } },
        drag: { enabled: true },
        publish: { enabled: true, method: 'PubSub', channel: 'com.ds.compass' }
    }
    shareOptions.data.content.objectId = documentInfo.id;
    shareOptions.data.content.objectType = documentInfo.type || "Document";
    shareOptions.data.content.displayType = documentInfo.type || "Document";
    shareOptions.data.content.displayName = documentInfo.dataelements.title || documentInfo.dataelements.name;

    let domElement = $container.get(0);
    if (shareOptions.drag && shareOptions.drag.enabled === true) {
        domElement.draggable = true;
        domElement.addEventListener('dragstart', function (event) {
            var content = [shareOptions.data.content];
            window.dashboardController.setDraggedContent(event, content, shareOptions.data.context);
        }, false);
    }
    if (shareOptions.publish && shareOptions.publish.enabled === true) {
        var usePubSub = shareOptions.publish.method === 'PubSub';
        domElement.addEventListener('click', function (/*event*/) {
            var content = [shareOptions.data.content];
            window.dashboardController.publish(content, shareOptions.data.context, shareOptions.publish.channel, usePubSub);
        }, false);
    }
}

