@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/styles/responsive.less";
@import "../../plmaResources/css/styles/variables.less";

.hide() {
	.flex-grow(0);
	.transition(@transition-normal, ~"flex-grow");
	border: none;
	display: block; /* To avoid harsh transitions because of display:none */
}

//FIX infinite scroll trigger of the result list widget when inside a hidden popup
.plmalightbox.hidden {
	.plmaLightboxWidgetWrapper {
		.plmaResultList {
			.hits {
				height: 1600px;
			}
		}
	}
}

//no line clam option
.mashup .plmaResultList&.no-line-clamp .results-panel li.hit .hit-container.hit-default .hitContent{
    -webkit-line-clamp: inherit;
}

//no padding option
.mashup .plmaResultList&.no-padding .results-panel {

    .scroll-container{
        padding: 0;
    }

    .hits {
        padding: 0;

        .hitContent {
            padding: 0;
        }
    }
}

.mashup.mashup-style.ie .plmaResultList {
	.select-all-container {
		.hit-checkbox-container {
			right: 24px;
		}
	}
	.results-panel {
		.resultsTitle {
			.table-header {
				padding-right: 55px;
			}
			&.tile-layout {
				padding-bottom: 45px;
			}
		}
		li.hit .hit-table.hit-container :not(.mainColumn).table-column .content {
			padding-left: 40px;
		}
		li.hit .hit-table.hit-container .mainColumn.table-column .hitTitle {
			padding-left: 5px;
		}
	}
}

.mashup.mashup-style .plmaResultList {
	
	&.no-border {
		.panels-container {
			border: none;
		}
	}

	&.no-title {
		.resultsTitle {
			display: none;
		}
	}

	&.flat-design {

		.widgetHeader {
			&.empty-result {
				top: inherit;
				margin-top: inherit;
			}
		}

		.results-panel {

			.resultsTitle {
				.flex-basis(50px);

				.title {
					order: 1;
					color: @clink;
					float: right;
					font-size: @m-font;
				}

				.display-description-button {
					visibility: hidden;
				}
			}

			.hits {

				&.layout-lines {
					.hit {
						background: white;
						border-top: none;
						border-right: none;
						border-left: none;
						margin-bottom: 0;
						.flex-wrap(wrap);

						.hitHeader {
							white-space: inherit;
						}

						.hitMetas {
							.flex-direction(row);
							.display-flex();
						}

						.hitMetas .hitMeta:not(:last-child) {
							flex-grow: 1;
						}

						.hitMetas .hitMeta:last-child {
							flex-basis: 60px;
						}

						.hitContent {
							.flex(1 1 auto);
							margin-bottom: 0px;
							display: block;

							&.empty {
								display: none;
							}
						}

						&:hover, &.selected {
							background-color: @clink;
							color: white;
							.hitMetas {
								color: white;
							}

							.hitTitle {
								color: white;
								a {
									color: white;
								}
							}
						}
					}
				}
			}
		}
	}
	
	.config-error{
		width: 100%;
		text-align: center;
		font-size: 18px;
		line-height: 24px;
	}
		
	.panels-container {
		position: inherit;
		top: inherit;
		bottom: inherit;
		width: inherit;
		.flex-direction(column);

		.results-panel {
			border-right: none;
		}
	}
}

.article-view-title() {
	.title{
		margin-right: 10px;
		text-align: right;
		font-size: 12px;
		color: @ctext;
		line-height: inherit;
	}

	.display-description-button{
		display:none;
	}
}

.article-view-tile-content(){
	li.hit{
		border:none;
		background-color:white;
        border-bottom: 1px solid @cblock-bg-lite;
        margin-bottom: 0;

		.hit-table {
			display: none;
		}

		.hit-container{
			.flex-grow(1);
		}

		.hitTitle{
			display:none;
		}

		.hit-container.hit-default .hitMainContainer {
			font-size: @xs-font;

			.metaContainer{
				.hitType{
					margin-right: 5px;
					color: @ctext-bold;
					float: left;
					display: inline-block;
				}

				.hitMetas{
					float: inherit;
					display: block;

					.metaValue{
						color: @ctext-bold;
					}
				}
			}
		}

		.icon-container{
			padding: 5px 0px 0px 0px;
			.hitIcon{
				background-color: transparent !important;
				.icon{
					font-size:14px;
				}
			}
		}
	}
}

.mashup .plmaResultList {
	text-align: left;
	margin: 0;
	position: relative;
	overflow: auto;
	.display-flex();
	.flex-direction(column);
	flex-basis: 0;
	padding: 0;
	&:hover {
		.panels-container {
			border-color: #d1d4d4;
		}
	}

	h2.widgetHeader.rounded {
		margin: 0;
		border-radius: 0;
		border-bottom: none;
	}

	.results-panel {
		.display-flex();
		.flex-flow(column nowrap);

		&.hidden {
			overflow: hidden;
		}

		.scroll-container {
			.flex(1);
			overflow-y: auto;
		}

		.resultsTitle {
			.flex-basis(70px);
			margin-bottom: 0;
			border-bottom: 1px solid @cblock-border;
			padding: 7px 13px 8px 13px;
			box-shadow: -3px 6px 10px -6px @ctext-weak;
			z-index: 50;
			&.tile-layout {
				flex-basis: 25px;
				.table-header {
					display: none;
				}
				&.article-view{
					.article-view-title()
				}
			}

			&.article-layout{
                padding: 0 30px 0 30px;
				.table-header {
					display: none;
				}

				.article-view-title();
			}

			&.table-layout {
				&.article-view{
					padding: 0;

					.article-view-title();

                    .title{
                        position: absolute;
                        left: 18px;
                    }

					.table-header{
						padding-right: 0;
						.main-column{
							flex: 1 0 40%;
							flex-basis:40%;
                            font-size: 14px;
                            color: #77797c;

							.icon{
								display:none;
							}
						}

						.type-column{
							order:-1;

                            span.label{
                                padding-left: 20px;
                            }

                            span.icon{
                                display:none
                            }
						}

                        .column{
                            padding: 20px 0px;
                            margin-right: 10px;
                            margin-left: 10px;

                            &.sortable-column:hover{
                                .label{
                                    text-decoration: underline;
                                }
                                cursor: pointer;
                            }
                        }
					}

					.table-column{
						border-left: none;
                        font-size: 14px;
                        color: #77797c;

                        .fonticon-expand-down, .fonticon-expand-up {
                            margin-right: 10px;
                            margin-left: 10px;
                            float: none;
                        }

                        &.no-icon{
                            .icon.fonticon{
                                display: none;
                            }
                        }
					}
				}
			}
			.icon-button-template {
				float: right;
				cursor: pointer;
				font-size: 25px;
				&.selected {
					color: @clink;
					cursor: initial;
				}
				&.fonticon-list {
					margin-left: 25px;
				}
			}
			.title {
				line-height: 25px;
				font-size: 18px;
				color: @clink;
			}
			.display-description-button {
				cursor: pointer;
				span.icon.fonticon:after {
					content: '\e238';
					font-size: 12px;
				}
				.label{
					display: none;
				}
				.hidden {
					display: none;
				}
			}
			.sortContainer{
				position: relative;
				display: inline-flex;
				flex-direction: row-reverse;
				.sort-dropdown{
					display: none;
					position: absolute;
					width: 230px;
					background: white;
					border: 1px solid #e2e4e3;
					top: 28px;
					
					.sortLinkOptions{
						display: flex;
						flex-direction: column;
						.sortOption{
							display: flex;
							padding: 8px 2px;
							font-size: 16px;
							border-bottom: 1px solid #e2e4e3;
							border-left: 5px solid #ffffff;
							&:hover{
								background:#f4f5f6;
								border-left: 5px solid #3d3d3d;
								&:not(.activeSort){
									.sortLinkOrder{
										.fonticon:first-child{
											 color:#3d3d3d;
										}
									}
								}
							}

							&.activeSort{
								border-left : 5px solid #368EC4;
								.label{
									 color: #368ec4;
									 font-style:unset;
								}
								&:hover{
									.fonticon{
										&:not(.selected){
										 color:#3d3d3d;
										}
									}
								}
							}
							.label{
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
								display: flow;
								line-height: 16px;
								flex-grow: 1;
								cursor: pointer;
								margin-right: 10px;
							}
							.sortText{
								flex-grow: 1;
								margin-left: 5px;


							}
							.sortLinkOrder{
								display: inline-flex;
								.fonticon{
									font-size: 14px;
									margin-right: 10px;
								}
								.fonticon:hover{
									cursor:pointer;
								}
								.selected{
									color: @clink;
								}
							}
						}
						.sortOption:last-child{
							border-bottom: none;
						}
					}
				}
				.action-sorting.active{
					color: @clink;
				}
				.action-sorting.active + .sort-dropdown{
					display: block;
				}
				.action-sorting:not(.active) + .sort-dropdown.hasSort{
					display: block;
					position: initial;
					padding: 2px 0px 0px 0px;
					width: max-content;
					border:unset;
					margin-right: 1px;
					margin-top: -5px;
					.sortLinkOptions{						
						.sortOption{
							display:none;
						}
						.sortOption:not(.activeSort){
							display:none;
						}
					}
				}
			}
			.exportContainer{
                position: relative;
                display: inline-flex;
                flex-direction: row-reverse;
                .plmaResultListExport{
                    cursor: pointer;
                }
            }
			.select-all-container {
				position: relative;
			}
			.table-header {
				padding-top: 7px;
				padding-left: 0;
				padding-right: 45px;
				padding-bottom: 0;
				display: flex;
				&.hidden {
					display: none;
				}
				.column {
					padding: 13px 0;
				}
				.main-column {
					flex: 1 0 200px;
					flex-grow: 1;
					flex-basis: 200px;
					.icon {
						padding-left: 16px;
						padding-right: 18px;
					}
				}
				.table-column {
					flex: 1 0 0px;
					flex-grow: 1;
					flex-basis: 0;
					border-left: 1px solid @cblock-border;

					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					.icon {
						padding-left: 7px;
					}
				}				
			}
		}
		
		.table-header {
			.fonticon-expand-down, .fonticon-expand-up {
				float: right;
				font-size: 12px;
				margin-right: 5px;
				cursor: pointer;
				&:hover {
					color: @clink-active;
				}
				&.selected {
					color: @clink-active;
				}
			}
		}
		
		.scroll-container {
			padding: 5px;
		}
		.hits {
			margin-top: 10px;
			padding: 0 (@line-height /2);
			&.table-layout {
				.hit {
					.hit-default {
						display: none;
					}
				}

				&.article-view {
					li.hit {
						border: none;
						background-color: white;
					}

					li.hit .hit-container.hit-table{
						padding-right: 0;
						flex-grow: 1;
					}

					li.hit .hit-container.hit-table .table-column {
                        .flex-direction(column);
                        margin-right: 10px;
                        margin-left: 10px;

						&.mainColumn {
							flex: 1 0 40%;
							flex-basis: 40%;
                            align-items: stretch;

							.title-container {

                                .icon-container{
                                    display: none;
                                }
                                .hitTitle{
                                    a{
                                        color: #3d3d3d;
                                        font-weight: 700;
                                        font-size: 14px;
                                    }
                                }
							}

							.hitContent {
								flex-basis: auto;
                                padding: 5px 0px 10px 0px;
							}
						}

                        :not(.mainColumn){
                            min-height: inherit;

                            .categoryName, .metaValue, .content {
                                padding-left: 0px;
                            }
                        }


                        .hitTitle{
                            line-height: inherit;
                        }

						&.hitType{
                            .flex-direction(row);
							order:-1;
							align-self: baseline;
							.icon-container{
								padding-bottom: 0;
							}
							.content{
                                padding-right: 20px;
                                padding-left: 10px;
							}
						}

                        &.hitFacet{
                            .categoryName{
                                padding: 0;
                                width: 100%;
                            }
                        }

                        &.hitMeta{
                            .metaValue{
                                padding:0;
                                width: 100%;
                            }
                        }
					}

					li.hit .hit-container.hit-table .table-column:not(.mainColumn){
						border-left: none;
					}

					li.hit{
						.icon-container{
							padding: 0;
							.hitIcon{
								background-color: transparent !important;
								.icon{
									font-size:14px;
                                    margin-left: 20px;
                                    margin-top: 4px;
								}
							}
						}
					}
				}
			}
			&.tile-layout {
				.hit {
					.hit-table {
						display: none;
					}
				}

				&.article-view {
					.article-view-tile-content();
				}
			}

			&.article-layout{
				.article-view-tile-content();
			}
		}

		.closeDetailSmallArrow {
			display: none;
		}

		li.hit {
			margin-bottom: 10px;
			border: 1px solid @cblock-border;
			background-color: @cblock-bg-alt;
			padding: 0;
			font-size: @m-font;
			position: relative;
			.hit-container {
				position: relative;
				overflow: hidden;
				.display-flex();
				.flex-flow(column nowrap);
			}
			&.selected {
				border: 2px solid @clink-active;
				scroll-margin: 10px;
			}
			&:hover {
				background-color: @cblock-bg-alt;
			}
			.hidden {
				display: none;
			}

			.icon-container {
				padding: 10px;
				padding-bottom: 0;
				height: 35px;
				.hitIcon {
					border-radius: 35px;
					width: 35px;
					height: 35px;
					display: flex;
					align-items: center;
					justify-content: center;
					background-color: #d5e8f2;
					.icon {
						font-size: 17px;
						color: @clink;
					}

				}
			}

			.multi-selection {
				padding: 0;
			}
			
			.hitThumbnail {
				padding: 10px;
			}

			.hitCursor {
                cursor : move;
            }
		}
		li.hit .hit-container.hit-default {
			.hitHeader {
				.flex(0 0 auto);
				display: flex;
				&.no-icon {
					padding-left: 10px;
				}
			}
			.hitMainContainer {
				display: inline-block;
				margin-top: 10px;
				flex-grow: 1;
				.metaContainer {
					margin-right: 10px;
					.hitType {
						font-size: 12px;
						color: @ctext-weak;
						margin: 0;
						line-height: 24px;
						display: inline-block;
					}
					.hitMetas {
						display: inline-block;
						float: right;
						.hitMeta {
							margin-right: 0px;
							line-height: 24px;
						}
					}
					.hitMeta:not(:last-child) .metaValue::after{
						content: '\00a0 |';
					}
				}

				.hitTitle {
					word-wrap: break-word;
					color: @clink;
					line-height: 18px;
					font-size: 18px;
					margin: 0;
					text-overflow:ellipsis;
					overflow:hidden;
					padding-right: 72px;
					line-height: normal;
					// Addition lines for 2 line or multiline ellipsis
					display: -webkit-box !important;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					white-space: normal;
				}
			}
			.hitMetas {
				.flex(0 0 auto);
				margin: 0 0 (@line-height /2) 0;
				color: @ctext-weak;

			}
			.hitContent {
				.flex(0 0 auto);
				margin-bottom: @line-height;
				overflow: hidden;
				max-height: 150px;
				display: -webkit-box;
				-webkit-line-clamp: 5;
				-webkit-box-orient: vertical;
				padding-left: 10px;
				line-height: 15px;
				&.hidden {
					display: none;
				}
			}

			.hitFacets {
				.flex(1 0 auto);
				border-top: 1px solid @cblock-border;
				margin-left: 10px;
				margin-right: 10px;
				display: flex;
				padding-top: 7px;
				.hitFacet {
					margin-bottom: 10px;
					flex: 1 0 0px;
					flex-grow: 1;
					display: flex;
					justify-content: center;
					margin-right: 0px;
					gap: 10px;
					.categoryName {
						text-transform: capitalize;
					}
					a.category-link.refined{
						margin: auto;
						display: inline-block;
						background-position-y: top;
					}
					&:last-child {
						justify-content: right;
					}
					&:first-child {
						justify-content: left;
					}
				}
			}
			.hitMeta, .hitFacet {
				margin-right: @line-height;
				.flex-basis(auto);

				&.first {
					.flex-grow(1);
				}
			}
// 			.metaName, .facetLabel {
// 				display: none;
// 			}
			.multi-selection {
				padding: 0;
			}

			span.arrowTitleExpandCustomHit {
				float: right;
				cursor: pointer;
				font-size: 25px;

				&:hover {
					color: #368ec4;
				}

				a {
					font-weight: normal;
					color: @ctext;
				}
			}

		}
		li.hit .hit-container.hit-table {
			flex-direction: row;
			min-height: 70px;
			align-items: center;
			padding-right: 39px;
			.title-container {
				&.no-icon {
					padding-left: 10px;
				}
			}
			.table-column {
				flex: 1 0 0;
				flex-grow: 1;
				flex-basis: 0;
				display: flex;
				align-items: center;
				&.mainColumn {
					flex: 1 0 200px;
					flex-basis: 200px;
					overflow: hidden;
					.title-container {
						display: flex;
						align-items: center;
						width: 100%;
					}
				}
				.icon-container {
					display: inline-block;
					padding-bottom: 10px;
				}
				.hitTitle {
					display: inline-block;
					color: @clink;
					line-height: 19px;
					font-size: 18px;
					margin: 0;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
				.hitContent {
					padding: 10px;
					flex-basis: 200px;
					&.empty {
						padding: 0;
					}
					&.hidden {
						display: none;
					}
				}
				.hitType {
					.content {
						padding-left: 31px;
					}
				}
				&:not(.mainColumn) {
					border-left: 1px solid @cblock-border;
					min-height: 45px;
					display: flex;
					align-items: center;
					line-height: 15px;
					&.hitFacet{
						justify-content:center;
					}
					.categoryName, .metaValue, .content {
						margin: auto;
						display: inline-block;
						word-break: break-all;
					}
					&:hover .refined .categoryName {
						color: white !important;
					}
					a.category-link{
						margin: auto;
						display: inline-block;
						word-break: break-all;
						&.refined{
							background-position-y: center;
							.categoryName{
								padding: 5px;
							}
						}
					}
					&.no-icon {
						.categoryName, .metaValue, .content {
							padding: 0px 8px;
							overflow: hidden;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-line-clamp: 3;
							-webkit-box-orient: vertical;
						}
					}
				}
			}
		}
		li.hit .subwidgets{
			position: absolute;
			top: 1px;
			right: 0px;
			display: flex;
		}
	}

	/* Panels layout */
	.panels-container {
		.display-flex();
		.flex-flow(row nowrap);
		.align-items(stretch);
		overflow: hidden;
		height: 100%;
		border: 1px solid #e2e4e3;
		background: white;

		.results-panel, .details-panel {
			.flex-shrink(1);
			.flex-basis(0);
			overflow-y: auto;
			.transition(@transition-normal, ~"flex-grow");

			&.hidden {
				.hide();
			}

		}
		.results-panel {
			.flex-grow(2);
			border-right: 1px solid @cblock-border;
		}
		.details-panel {
			.flex-grow(3);
			&.hidden {
				overflow: hidden;
			}
		}
	}

}

@media (max-width: @screen-xs-max) {
	.mashup .plmaResultList {
		.panels-container .results-panel.responsive-hide {
			.hide();
		}

		.details-panel {
			.closeHitsArrow {
				display: none;
			}
			.hitNavigation.closeDetailSmallArrow {
				display: inline;
			}
		}

	}
}


/*GridTable-Layout*/

.main-flex-container-home.main-container .result-detail-container .result-panel{
	&.flex-40{
		overflow: hidden;
		.flex-grow(40);
	}
	&.flex-30{
		overflow: hidden;
		.flex-grow(30);
	}
}

.mashup .result-panel .plmaResultList .results-panel{
	.resultsTitle .global-toolbar{
		font-size: 1.3em;
		position: absolute;
		right: 10px;
		top: 13px;

		span.insert-before, span.insert-after{
			border-left: 1px solid;
			border-color: #e2e4e3;
		}
		.hit-checkbox-container.select-all-button {
			bottom: inherit;
			right: inherit;
			top: 2px;
		    position: relative;
			margin: 0px 15px;
			padding-right: 1px;
		}
	}
}

.mashup .result-panel .plmaResultList .results-panel{
	.resultsTitle.gridtable-layout{
		.flex-basis(25px);
		.widgetTitle{
			margin-right: 7px;
			font-weight: bold;
			font-size: 1.1em;
		}
		.display-description-button{ 
			display: none; 
		}
	}
	
	.scroll-container.gridtable-layout{
		span.toolbarContainer {
			display: grid;
			grid-auto-flow: column;
			grid-auto-columns: 25px;
			align-items: center;
			justify-items: center;
			justify-self: center;
			
			.plmaButton, .plmaButton .collection-modifier{		
				position: initial;
				float: right;
				line-height: 100%;
				padding: 0px;
			}
			.hit-checkbox-container{
				position:relative;
				top: -1px;
				right: auto;
				grid-column: 1;
			}
		}
		&.gridtable-transpose span.toolbarContainer .hit-checkbox-container.select-all-button{
			grid-column: 5;
		}
		
		.th.table-column.no-content-hidden,
		.td.table-column.no-content-hidden{
			display: none;
		}

		.td.table-column.hitFacet .category-link:not(:last-of-type):not(.refined) .categoryName::after{
			content: ',';
		}

        .td.table-column .category-link:hover:not(:has(.compare-result)){
            text-decoration: underline;
        }

		.td.table-column.large-content{
			position: relative;
			overflow: initial;
            .fonticon-open-down{
                display: inline-block;
                position: absolute;
                // bottom: 3px;
                right: 0px;
                font-weight: bold;
                // color: #005686;
                letter-spacing: 3px;
                background-color: #FFF;
                z-index: 1;
            }
            .cell-content{
                max-width: 93%;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .large-content-tooltip{
                &.hidden{
                    display: none;
                }
                display: flex;
                flex-direction: column;
                background-color: #fff;
                box-shadow: 0 1px 6px 0px #b4b6ba;
                min-width: 100px;
                padding:0;
                position: absolute;
                z-index: 5;
                a{
                    padding: 3px 12px;
                    background-position-y: center;
                    overflow: initial;
                    border-bottom: 1px solid #e2e4e3;
                    height: inherit;;
                    &:hover{
                        text-decoration: underline;
                        background-color: #f4f5f6;
                    }
                }
                span{
                    &:after{
                        display: none;
                    }
                }
            }
		}
	}
}

@import "gridtable.less";
@import "gridtable-transpose.less";

.mashup .result-panel .plmaResultList.compare{
    i.icon-similar.fonticon.fonticon-math-approximation {
		color: white;
		background-color: #477738;
		padding: 1px;
		border-radius: 10px;
		width: 15px;
		height: 15px;
		position: relative;
		bottom: 22px;
		left: 6px;

	}
	
	.hits li.hit.compare-reference .grid-row .td {
		background: #F2F5F7 !important;
		.ref-compare-button i{
			color: #368EC4;
			cursor: not-allowed;
		}
	}

	.base-element{
		display:none !important;
		overflow-y: hidden !important;
	}

	.compare-diff{
		color: #5dcff6;		
		&.empty{
			background-color: white;
			color: white;/*#B03A28;*/
		}
	}

	.compare-almost-diff{
        color: #2e2c9b;
        font-weight: 600;
        font-size: 14px;
        font-family: arial black;
    }
	
	.no-diff-hidden{
		display: none;
	}
}

.mashup.mashup-style .loading-indicator-block{
	display:none;
	justify-content: center;
    align-content: center;
    line-height: 40px;
	border-top: 1px solid #d1d4d4;
	.loading-spinner{
		display: inline-block;
		align-self: center;
		margin:0px 10px;
		animation: none;
	}
	.label{
		font-size: 16px;
	}

	&.active{
		display: flex;
		.loading-spinner{
		    animation: spin 2s linear infinite;
		}
	}
}
.mashup.mashup-style .gridtable-transpose .loading-indicator-block{
	flex-direction: column-reverse;
	row-gap: 18px;
    border-left: 1px solid #d1d4d4;
	.label{
		transform: rotate(-90deg);
	}
}
