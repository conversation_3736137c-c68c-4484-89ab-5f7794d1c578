/**
 * This class designed to handel the configurator for Timeline.
 * Configurator is provide by visjs. We are handling its layout,
 * filtes, and persiting the user overrides in this class.
 */
var TimelineConfigurator = function(uCssId, uniqueKey, userOptions){
	this.uCssId = uCssId;
	this.userOptions = userOptions;
	this.uniqueKey = uniqueKey;
	
	this.widget = $('.' + this.uCssId);
	this.$saveButton = this.widget.find('.timeline-configurator .save-button');
	this.$resetButton = this.widget.find('.timeline-configurator .reset-button');
	this.$container = this.widget.find('.timeline-configurator');
	this.$customConfig = this.$container.find('.custom-config');
	
	this.modOptions = { timeline: {} };
	this.customConfigList = [];
	this.storage = new StorageClient('user');
	this.init();
}

TimelineConfigurator.prototype.init = function () {
	this.$resetButton.on('click', function () {
		this.resetConfigs();
	}.bind(this));
}

TimelineConfigurator.prototype.empty = function() {
	this.$container.find('.vis-configuration-wrapper').remove();
}

TimelineConfigurator.prototype.getTimelineUserOptions = function() {
	return this.userOptions.timeline;
}

TimelineConfigurator.prototype.enableSave = function () {
	if(this.$saveButton.hasClass('modified') == false){
		this.$saveButton.addClass('modified');
		this.$saveButton.on('click', function () {
			this.save();
		}.bind(this));
	}
}

TimelineConfigurator.prototype.setup = function (timeline) {
	this.timeline = timeline;
	this.configurator = timeline.configurator;
	timeline.on('configChange', function(option){
		this.modOptions.timeline = $.extend(true, {}, this.modOptions.timeline, option);
		this.enableSave();
	}.bind(this));
}

TimelineConfigurator.prototype.getSetupOptions = function(){
	return {
		enabled: true,
		container: this.$container.get(0),
		filter: TimelineConfigurator.optionFilter,
	}
}

// Define bag to store the options with ukey=uniqueKey.
TimelineConfigurator.STORAGE_KEY = 'plmaPlanningConf[]';
TimelineConfigurator.SUPPORTED_OPTIONS = {
	'global': 						[ 'stack', 'stackSubgroups', /*'clickToUse', 'cluster', 'zoomKey', 'locale',*/ ],
	'global/orientation': 			[ 'item', 'axis' ],
	'global/margin': 				[ 'item', 'axis' ],
	'global/margin/item': 			[ 'vertical', /*'horizontal', */ ],
	/*
	'global/format':				[ 'minorLabels', 'majorLabels' ],
	'global/format/minorLabels':	[ 'day', 'week', 'month', 'year' ],
	'global/format/majorLabels':	[ 'day', 'week', 'month', 'year' ],
	*/
};
	
TimelineConfigurator.optionFilter= function (option, path) {
	let pathId = path.join('/')
	if( TimelineConfigurator.SUPPORTED_OPTIONS[pathId] && 
		TimelineConfigurator.SUPPORTED_OPTIONS[pathId].indexOf(option) !== -1){
		return true;
	}
	//console.log('Filter: ' + pathId + '=' + option);
	return false;
}

TimelineConfigurator.prototype.save = function () {
	this.widget.showPLMASpinner({overlay: true});
	let optionsToSave = $.extend(true, {}, this.userOptions, this.modOptions);
	this.storage.set(
		TimelineConfigurator.STORAGE_KEY + '|' + this.uniqueKey,
		JSON.stringify(optionsToSave), 
		function(res){
			this.$saveButton.removeClass('modified');
			window.location.reload();	
			this.widget.hidePLMASpinner();
		}.bind(this));
	
}
TimelineConfigurator.prototype.resetConfigs = function () {
	this.widget.showPLMASpinner({overlay: true});
	this.storage.del(
		TimelineConfigurator.STORAGE_KEY + '|' + this.uniqueKey, 
		function(res){
			this.$saveButton.removeClass('modified');
			window.location.reload();
			this.widget.hidePLMASpinner();			
		}.bind(this));
	
}

TimelineConfigurator.prototype.getCustomConfigHandler = function(uniqueCssClass) {
	if(this.customConfigList.includes(uniqueCssClass)){
		throw Error('CustomConfig "' + uniqueCssClass + '" already created. Use another Key.');
		return;
	}
	
	this.customConfigList.push(uniqueCssClass);
	let $container = $('<div class="' + uniqueCssClass + '"></div>');
	this.$customConfig.append($container);
	return {
		$container: $container,
		updateModification: function(key, config){
			this.modOptions[key] = config;
			this.enableSave();
		}.bind(this),
		getUserOptions: function(key){
			return this.userOptions[key]?this.userOptions[key]:{};
		}.bind(this),
	};
}