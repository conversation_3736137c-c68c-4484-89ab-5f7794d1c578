.mashup .plmaButton.publishButton{
	cursor: pointer;
	margin: 0px;
    padding: 0px 7px;
	> .button-content{
		display: flex;
		.button-label{
			margin-left: 5px;
		}
		.fonticon:after{
			content: '\e242';
			font-size: 10px;
			color: #005686;
		}
	}
	&.disabled{
		cursor: not-allowed;
		color: #f4c8c2;
		
		> .button-content > .button-label{
			color: #f4c8c2;		
		}
	}
}
.mashup.mashup-style .icons-div > div.plmaButton.publishButton{
    margin: 0px;
    padding-top: 24px;
}
.mashup .appspopup .plmaButton.publishButton{
	width: 100%;
}

.export-to-3dspace.SnapshotExport,
.export-to-3dspace.ContextShare{
	.drop-area {
		text-align: left;
		border: 2px dashed;
		font-size: 24px;
		line-height: 20px;
		padding: 5px 3px;
		float: left;
		display: flex;
		width: 150px;
		
		&> .fonticon{
			float: left;
		}
		.label{
			font-size: 12px;
			vertical-align: top;
			line-height: 12px;
		}
		.disabled-overlay{
			display: none;
		}
		&.disabled{
			position: relative;
			color: #EA4F37;
			.disabled-overlay{
				display: block;
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: #f1f1f1;
				opacity: 0.4;
			}
		}
	}
}
