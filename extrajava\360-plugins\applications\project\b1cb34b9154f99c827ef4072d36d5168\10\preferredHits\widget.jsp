<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<render:import varWidget="widget" varFeeds="feeds"/>
<search:getFeed var="feed" feeds="${feeds}"/>

<widget:widget varUcssId="uCssId" disableStyles="true">
    <render:template template="templates/preferredHits.jsp" widget="preferredHits">
        <render:parameter name="widget" value="${widget}"/>
        <render:parameter name="uCssId" value="${uCssId}"/>
        <render:parameter name="feed" value="${feed}"/>
        <render:parameter name="collection" value="${plma:getStringParam(widget, 'collection', 'collection')}"/>
        <render:parameter name="urlParameter" value="${plma:getStringParam(widget, 'urlParameter', 'preferredHits')}"/>
        <render:parameter name="compactHeader" value="${plma:getBooleanParam(widget, 'compactHeader', true)}"/>
    </render:template>
</widget:widget>