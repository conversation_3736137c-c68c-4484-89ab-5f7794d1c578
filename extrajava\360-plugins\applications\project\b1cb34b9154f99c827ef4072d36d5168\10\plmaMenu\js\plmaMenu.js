var PLMAMenu = function (uCssId, options) {
    'use strict';

    var defaults = {
        animationDuration: 200,
        showUserMenu: false
    };

    this.options = $.extend({}, defaults, options);
    this.uCssId = uCssId;

    if (uCssId) {
        this.widget = $('.' + uCssId);
    } else {
        this.widget = $();
    }

    if (this.widget.length === 0) {
        throw new Error('Unable to initialize widget PLMAMenu : widget not found (uCssId: "' + uCssId + '").');
    } else {
        this.init();
    }
};

PLMAMenu.COLLAPSIBLE_COOKIE_NAME = "SIDE_BAR_MENU_COLLAPSIBLE_SECTION";

PLMAMenu.prototype.init = function () {
    'use strict';

    this.displayModeCollapse = this.widget.find('.display-mode-collapse');
    this.displayModeExpand = this.widget.find('.display-mode-expand');
    this.displayModeShow = this.widget.find('.display-mode-show');
    this.displayModeHide = this.widget.find('.display-mode-hide');

    this.displayModeCollapse.on('click', $.proxy(function () {
        this.collapse();
    }, this));
    this.displayModeExpand.on('click', $.proxy(function () {
        this.expand();
    }, this));

    /* Init responsive */
    if (this.options.display === 'Responsive') {
        if (this.options.displayState === 'responsive') {
            this.expand();
        }

        $(window).on('resize', $.proxy(function () {
            if ($(window).width() >= 1024) {
                this.expand();
            } else {
                this.collapse();
            }
        }, this));
    }

    this.initSearch();
    this.items = this.widget.find('.menu-item');
    this.initDescription();
	this.initEditMenu();
	this.initPanelToggle();
};

PLMAMenu.prototype.initDescription = function () {
    $('.menu-item').on('mouseenter', $.proxy(function (e) {
        $('.item-description').addClass('hidden');
        var id = $(e.currentTarget).data('descid');
        $('#' + id).removeClass('hidden');
    }, this));

    $('.menu-item').on('mouseleave', $.proxy(function (e) {
        var id = $(e.currentTarget).data('descid');
        $('#' + id).addClass('hidden');
    }, this));
};

PLMAMenu.prototype.initSearch = function () {
    this.widget.find('.search-item-input').on('keyup', $.proxy(function (e) {
        var inputValue = $(e.currentTarget).val();
        var items = this.widget.find('.item-menu-container .menu-item .item-wrapper .item-label');
        /* Display menu items according to search */
        for (var i = 0; i < items.length; i++) {
            var item = $(items[i]);
            if (item.text().toLowerCase().indexOf(inputValue.toLowerCase()) !== -1) {
                item.closest('.menu-item').removeClass('hidden');
            } else {
                item.closest('.menu-item').addClass('hidden');
            }
        }

        /* Hide unused sections */
        var sections = this.widget.find('.item-menu-container .items');
        for (var j = 0; j < sections.length; j++) {
            if (!$(sections[j]).hasClass('main-items')) {
                var sectionContainer = $(sections[j]).find('.user-section-container');
                for (var k = 0; k < sectionContainer.length; k++) {
                    if ($(sectionContainer[k]).find('.menu-item:not(.hidden)').length === 0) {
                        $(sectionContainer[k]).addClass('hidden');
                    } else {
                        $(sectionContainer[k]).removeClass('hidden');
                    }
                }
            } else {
                var children = $(sections[j]).children();
                var lastSectionIndex = 0;
                var isDisplayedItem = false;
                for (var k = 0; k < children.length; k++) {
                    var child = $(children[k]);
                    if (child.hasClass('section-separation') && k > 0) {
                        if (!isDisplayedItem) {
                            $(children[lastSectionIndex]).addClass('hidden');
                        } else {
                            $(children[lastSectionIndex]).removeClass('hidden');
                        }
                        lastSectionIndex = k;
                        isDisplayedItem = false;
                    } else if (child.hasClass('menu-item') && !child.hasClass('hidden')) {
                        isDisplayedItem = true;
                    }
                }
                if (!isDisplayedItem) {
                    $(children[lastSectionIndex]).addClass('hidden');
                } else {
                    $(children[lastSectionIndex]).removeClass('hidden');
                }
            }
        }
    }, this));
};

PLMAMenu.prototype.collapse = function () {
    this.widget.addClass('collapsed').removeClass('expanded');
    $.cookie('sideBarMenu_displayState', 'collapsed');
    $(window).trigger('plma:resize', [300]);
    /* To resize highcharts */
};

PLMAMenu.prototype.expand = function () {
    this.widget.addClass('expanded').removeClass('collapsed');
    $.cookie('sideBarMenu_displayState', 'expanded');
    $(window).trigger('plma:resize', [350]);
    /* To resize highcharts */
};


PLMAMenu.prototype.initPanelToggle = function () {
    this.items.filter('.isPanelToggle').each($.proxy(function (i, e) {
        var $item = $(e);
        var panelClass = $item.data('togglepanel');
        if (panelClass) {
            $item.on('click', $.proxy(function () {
                this.togglePanel(panelClass);
            }, this));
        }
    }, this));
    var showPanel = $.cookie('sideBarMenu_showPanel');
    if (showPanel) {
        $('.collapsiblePanel.' + showPanel).removeClass('hidden');
    }
    var panelState = $.cookie('sideBarMenu_displayState');
    if (panelState === 'expanded') {
        this.widget.removeClass('collapsed');
    }
};

PLMAMenu.prototype.toggleSection = function (section) {
    section.toggleClass('section-hidden');
    var page = section.next();
    while (page.hasClass('menu-item')) {
        page.toggleClass('hidden');
        page = page.next();
    }
};

PLMAMenu.prototype.togglePanel = function (panelClass) {
    this.items.filter('.isPanelToggle').each(function (i, e) {
        var item = $(e);
        if (item.data('togglepanel') === panelClass) {
            item.toggleClass('active');
        } else {
            item.removeClass('active');
        }
    });
    $('.collapsiblePanel:not(.manualCollapse)').each(function (i, e) {
        var panel = $(e);
        if (panel.hasClass(panelClass)) {
            if (panel.hasClass('hidden')) {
                panel.removeClass('hidden');
                panel.trigger('plma:panel-open');
            } else {
                panel.addClass('hidden');
                panel.trigger('plma:panel-close');
            }
        } else {
            panel.addClass('hidden');
        }
    });
    if (this.options.sidebarCookie) {
        $.cookie('sideBarMenu_showPanel', $.cookie('sideBarMenu_showPanel') === panelClass ? '' : panelClass, {path: '/'});
    }
    $(window).trigger('plma:resize', [300]);
    /* To resize highcharts */
};

PLMAMenu.prototype.reload = function () {
    var client = new PlmaAjaxClient(this.widget, {baseUrl: this.options.baseAjaxReload});
    client.addWidget(this.uCssId, true, false);
    client.update();
};

PLMAMenu.prototype.initEditMenu = function () {
	this.editMenu = new EditMenu(this.uCssId, this.options.url, this.options.pageBaseUrl, this.options.currentPage, this.options.baseAjaxReload);
};