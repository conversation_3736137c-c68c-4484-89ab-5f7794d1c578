<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" />

<config:getOption name="inputSize" var="inputSize" defaultValue="300px" />
<config:getOption name="inputName" var="inputParamName" defaultValue="q" />
<config:getOption name="placeHolder" var="placeHolder" defaultValue="" />
<config:getOption name="buttonName" var="buttonName" defaultValue="" />
<config:getOptions var="wuids" name="ajaxWUIDs" />
<config:getOptionsComposite var="actions" name="action" doEval="true" />
<config:getOption var="hasSuggest" name="enableSuggest" defaultValue="false"/>

<config:getOption name="keepParameters" var="keepParameters" defaultValue="" />
<config:getOption name="exceptParams" var="exceptParams" defaultValue="" />
<c:set var="expandedForm" value="${plma:getBooleanParam(widget, 'expandedForm', false)}"/>

<request:getParameterValue var="inputParamValue" name="${inputParamName}" defaultValue="" />

<widget:widget extraCss="searchForm" varCssId="cssId" varUcssId="uCssId" disableStyles="true">

	<c:set var="formAction" value="${actions[0][1]}" />
	<c:choose>
		<c:when test="${keepParameters == 'true'}">
			<c:set var="exceptparamsTab" value="${fn:split(exceptParams,',')}"></c:set>
			<url:url var="formUrl" value="${formAction}" keepQueryString="true">
				<url:parameter name="${inputParamName}" value="" override="true"/>
				<c:forEach var = "i" begin = "0" end = "${fn:length(exceptParams)}">
					<url:parameter name="${exceptparamsTab[i]}" value="" override="true"/>
			    </c:forEach>
			</url:url>
		</c:when>
		<c:otherwise>
			<url:url var="formUrl" value="${formAction}" keepQueryString="false">
			</url:url>
		</c:otherwise>
	</c:choose>
	
	<form method="get" action="${formUrl}">
		<div class="searchWidget">
			<%-- Search actions --%>
			<c:if test="${fn:length(actions) > 1}">
				<div class="actions">
					<c:forEach var="action" items="${actions}">
						<span data-action="${action[1]}">${action[0] == '' ? (action[1] == '' ? feedName : action[1]) : action[0]}</span>
					</c:forEach>
				</div>
			</c:if>

			<%-- Search form & Advanced search --%>
			<div class="searchContainer">
				<c:if test="${!expandedForm}">
					<div class="searchButton fonticon fonticon-search" title="${buttonName}"></div>
				</c:if>
				<div class="searchFormContent ${expandedForm ? '':'hidden'}" style="width: ${inputSize};">
					<input type="text" 
						name="${inputParamName}" 
						value="${inputParamValue}" 
						class="searchInput"
						placeholder="${placeHolder}" />
					<c:if test="${expandedForm}">
						<div class="searchButton fonticon fonticon-search" title="${buttonName}"></div>
					</c:if>
				</div>
			</div>
		</div>
	</form>
</widget:widget>


<render:renderScript position="READY">
	<%-- Init Search Form --%>
	$('#${cssId} form').searchForm({
		uid: '${widget.wuid}',
		focus: <config:getOption name="focus" defaultValue="true" />,
		keepRefinements: true
	});
	
	<%-- Suggest --%>
	<c:if test="${hasSuggest == 'true'}">
		$('#${cssId} input[name=${inputParamName}]').enableSearchSuggest('<c:url value="/plma/suggest/widget/${feedName}" />', '${widget.wuid}', {autoSubmit: true,queryParameter:'q'});
	</c:if>
	
	
	<%-- Init search button --%>
	<c:if test="${expandedForm}">
		$("#${cssId} .searchButton").click(function () {
			$("#${cssId} .selected").submit();
		});
	</c:if>
	<c:if test="${!expandedForm}">
		$('#${cssId} .searchButton').on('click', function(e){
			$(e.target).closest('.searchContainer').find('.searchFormContent').toggleClass('hidden');
		});
	</c:if>
	

</render:renderScript>