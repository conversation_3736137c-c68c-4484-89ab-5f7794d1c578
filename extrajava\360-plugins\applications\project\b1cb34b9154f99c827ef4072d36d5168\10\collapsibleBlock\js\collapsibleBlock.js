var CollapsibleBlock = (function(){
    var defaults = {
        isCollapsedByDefault: false,
        duration: 400,
        isContentLoaded: false,
        ajaxParams: {}
    };
    
    
    return function(uCssId, options){
        var opts = $.extend({}, defaults, options);
        
        this.init(uCssId, opts);
        
        this.trigger.on('click', $.proxy(this.toggleCollapse, this));
    }
})();

CollapsibleBlock.prototype.init = function(uCssId, opts){
    this.widget = $('.wuid.' + uCssId);
    this.uCssId = uCssId;
    
    if(this.widget.length === 0){
        throw new Error ("CollapsibleBlock : could not find widget with uCssId " + uCssId);
    }
    
    this.options = opts;
    this.trigger = this.widget.find('> .collapse-trigger');
    this.content = this.widget.find('> .collapse-content');
    this.spinner = this.widget.find('> .collapse-trigger > .spinner-container');
	
	this.handleState(opts.isCollapsedByDefault);
    this.isContentLoaded = opts.isContentLoaded;
	
    /* If the block is collapsed by default but has its content loaded,
     * then it means that uncollapsing has been requested and the content
     * fetched with ajax. */
    if (this.isCollapsed && this.isContentLoaded){
        this.uncollapse();
    }
};

CollapsibleBlock.prototype.handleState = function(collapseState){
	if(this.options.stateCookieKey){
		$.cookie(this.options.stateCookieKey, collapseState);
	}
	this.isCollapsed = collapseState;
	this.trigger.attr('title', this.isCollapsed? this.options.tooltip.collapsed: this.options.tooltip.expanded);
};

CollapsibleBlock.prototype.toggleCollapse = function(){
	if (this.isCollapsed){
		this.uncollapse();
    }else{
		this.collapse();
    }
};

CollapsibleBlock.prototype.collapse = function(){
    this.content.slideUp(this.options.duration, $.proxy(function(){
        this.widget.addClass('collapsed').removeClass('uncollapsed');
        this.handleState(true);
        $(window).trigger('resize'); //To trigger infinite scroll
    }, this));
};

CollapsibleBlock.prototype.uncollapse = function(){
    if (!this.isContentLoaded){
        this.displaySpinner();
        this.loadContent();
        this.widget.addClass('uncollapsed').removeClass('collapsed');
    }else{
        this.content.slideDown(this.options.duration, $.proxy(function(){
            this.widget.addClass('uncollapsed').removeClass('collapsed');
            this.handleState(false);
            $(window).trigger('resize'); //To trigger infinite scroll
        }, this));    
    }
};

CollapsibleBlock.prototype.loadContent = function(){
    var ajaxClient = new MashupAjaxClient(this.widget, {
        spinner: false,
        success: $.proxy(function(){
            this.isContentLoaded = true;
            this.hideSpinner();
        }, this)
    });
    ajaxClient.addParameter('collapsibleBlock_uncollapse', this.uCssId);
    for (var param in this.options.ajaxParams){
        ajaxClient.addParameter(param, this.options.ajaxParams[param]);
    }
    if (this.options.addFeedsParam){
        // Add 'use_page_feeds' parameter to URL to filter executed feed(s)
        ajaxClient.addParameter('use_page_feeds', this.options.usedFeeds.join(','));
    }
    ajaxClient.addWidget(this.uCssId);
    ajaxClient.update();
};

CollapsibleBlock.prototype.displaySpinner = function () {
	this.spinner.removeClass('hidden');
};

CollapsibleBlock.prototype.hideSpinner = function () {
	this.spinner.addClass('hidden');
};
