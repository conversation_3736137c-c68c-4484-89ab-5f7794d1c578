var FacetRefine = function (uCssId, options) {
    this.uCssId = uCssId;
    this.widget = $('.' + uCssId);
    this.facetName = options.facetId;
    this.facetConfig = options.facetConfig;
    this.paginationInfo = options.pagination;
    this.paginatedSerie = options.paginatedSerie;
    this.facetDisplayConfig = options.facetDisplayConfig;
    this.$input = $('.' + this.uCssId + ' .searchInput');
    this.facetObject = $('.' + this.uCssId + '.refinePanel .filter-list-facet-elem.filter-list-facet-elem-facet-' + this.facetName)[0];
    this.paramNameReload = options.paramNameReload;
    this.paramNameReloadValue = options.paramNameReloadValue;
    this.loadDateFacetParameter = options.loadDateFacetParameter;
    this.refineEventMode = options.refineEventMode;
    this.loadFacetParameter = options.loadFacetParameter;
    this.defaultView = options.facetConfig.defaultView;
    this.applicationId = options.applicationId;
    this.customDelimiter = options.customDelimiter;
    this.pageName = options.pageName;
    this.feeds = options.feeds;
    this.baseAjaxUrl = options.baseAjaxUrl;
    this.suggestApiAction = options.suggestApiAction;
    this.refinesPanelId = options.refinesPanelId;
    this.baseAjaxReload = options.baseAjaxReload;
};

FacetRefine.COLLAPSIBLE_COOKIE_NAME = 'refine-panel-collapsible';
FacetRefine.REFINE_PANEL_FACET_OPTION = 'refine-panel-facet-option';
FacetRefine.DEFAULT_SORT = 'DEFAULT_SORT';
FacetRefine.RELOAD_FACET_PAGE = 'reloadFacetPage';
FacetRefine.LOAD_ALL_CATEGORIES = 'load-all-categories';
FacetRefine.PIE_VIEW = 'pie';
FacetRefine.COLUMN_VIEW = 'column';
FacetRefine.LIST_VIEW = 'list';
FacetRefine.SORT = 'plma_facet_sort';
FacetRefine.VIEWS = [FacetRefine.PIE_VIEW, FacetRefine.LIST_VIEW, FacetRefine.COLUMN_VIEW];
FacetRefine.HIGHCHARTS_FACET_CONTAINER = '.highcharts-facet-container';
FacetRefine.FACET_CONTAINER_LIST = '.facet-container-list';
FacetRefine.FONTICON_COLUMN = '.fonticon-chart-bar';
FacetRefine.FONTICON_PIE = '.fonticon-chart-pie';
FacetRefine.FONTICON_LIST = '.fonticon-list';
FacetRefine.PAGINATION = '.pagination';
FacetRefine.COLUMN_HIGHCHART_CONTAINER = '.facet-container-column div.highcharts-container';
FacetRefine.COLLAPSIBLE_TOOLBOX = 'toolbox-expanded';
FacetRefine.SELECTED_SORT;
FacetRefine.SELECTED_VIEW;
FacetRefine.WIDGET_NAME = 'refinePanel';
FacetRefine.PLMA_FACET_SORT_RELOAD = 'plma_facet_sort_reload';

FacetRefine.prototype.init = function () {
    // Check if the initialization should be performed.
    if (this.shouldInitialize()) {
        // Initialize the default view for the facet.
        this.initializeDefaultView();

        // Initialize the collapsible feature for the facet.
        this.initializeCollapsible();

        // Initialize the sorting functionality for the facet.
        this.initializeSort();

        // Initialize the collapsible menu for the facet.
        this.initializeCollapsibleMenu();

        // Initialize the view-related events for the facet.
        this.initializeViewEvents();

        // Initialize the pagination feature for the facet.
        this.initializePagination();

        // Add a click event handler for the refine link in the facet.
        $(this.facetObject).find('.refine-link').click($.proxy(this.refine, this));
    }
};


FacetRefine.prototype.shouldInitialize = function () {
    return this.paramNameReloadValue.length === 0 || this.isReloadedFacet();
};

FacetRefine.prototype.initializeDefaultView = function () {
    if (typeof this.defaultView !== 'undefined' && this.getFacetCategory().length > 0) {
        this.defaultView = FacetRefine.VIEWS.includes(this.defaultView) ? this.defaultView : FacetRefine.LIST_VIEW;
        var storedDefaultView = this.getSelectedViewCookie();
        this.currentView = storedDefaultView == false ? this.defaultView : storedDefaultView.toLowerCase();
        this.renderDefaultView();
    } else {
        this.currentView = FacetRefine.LIST_VIEW;
        this.renderDefaultView(FacetRefine.LIST_VIEW);
    }
};

FacetRefine.prototype.initializeCollapsible = function () {
    this.getCollapsibleCookie();
    this.addCollapsibleEvent();
};

FacetRefine.prototype.initializeSort = function () {
    if (this.facetConfig.enableSort) {
        this.addSortEvent();
    }
};

FacetRefine.prototype.initializeCollapsibleMenu = function () {
    this.addCollapsibleMenuEvent();
};

FacetRefine.prototype.initializeViewEvents = function () {
    this.facetConfig.enableColumnView && this.addColumnToggleEvent();
    this.facetConfig.enablePieView && this.addPieToggleEvent();
    this.facetConfig.enableListView && this.addListToggleEvent();
};

FacetRefine.prototype.initializePagination = function () {
    if (this.paginationInfo.paginated) {
        this.initPagination();
    }
};

FacetRefine.prototype.renderDefaultView = function () {
    var facet = $(this.facetObject);
    switch (this.currentView) {
        case FacetRefine.PIE_VIEW:
            this.buildFacetChart(FacetRefine.PIE_VIEW);
            facet.find(FacetRefine.HIGHCHARTS_FACET_CONTAINER + ',' + FacetRefine.FONTICON_PIE).addClass('selected').removeClass('hidden');
            facet.find(FacetRefine.FACET_CONTAINER_LIST + ',' + FacetRefine.PAGINATION).addClass('hidden');
            break;
        case FacetRefine.COLUMN_VIEW:
            this.buildFacetChart(FacetRefine.COLUMN_VIEW);
            facet.find(FacetRefine.HIGHCHARTS_FACET_CONTAINER + ',' + FacetRefine.FONTICON_COLUMN + ',' + FacetRefine.PAGINATION).addClass('selected').removeClass('hidden');
            facet.find(FacetRefine.FACET_CONTAINER_LIST).addClass('hidden');
            break;
        case FacetRefine.LIST_VIEW:
            facet.find(FacetRefine.FACET_CONTAINER_LIST + ',' + FacetRefine.FONTICON_LIST + ',' + FacetRefine.PAGINATION).addClass('selected');
            facet.find(FacetRefine.HIGHCHARTS_FACET_CONTAINER + ',' + FacetRefine.HIGHCHARTS_FACET_CONTAINER).addClass('hidden');
            break;
    }
};

FacetRefine.prototype.isReloadedFacet = function () {
    if (this.paramNameReloadValue.includes(this.facetName)) {
        this.updateCookies();
        return true;
    }
    return false;
};

// Define a method on the FacetRefine prototype for removing specific parameters from a URL.
FacetRefine.prototype.cleanURLParameters = function (url) {
    var tempUrl = new BuildUrl(url);
    tempUrl.removeParameter(RefinePanelSearch.PARAMETER_IS_SUGGEST_SEARCH);
    tempUrl.removeParameter(RefinePanelSearch.PARAMETER_IS_SUGGEST_SEARCH_EMPTY);
    tempUrl.removeParameter(FacetRefine.RELOAD_FACET_PAGE);
    tempUrl.removeParameter(FacetRefine.SORT);
    tempUrl.removeParameter(FacetRefine.PLMA_FACET_SORT_RELOAD);
    tempUrl.removeParameter(this.loadDateFacetParameter);
    tempUrl.removeParameter(this.loadFacetParameter);
    tempUrl.removeParameter(this.paramNameReload);
    return tempUrl.toString();
};

FacetRefine.prototype.refine = function (e) {
    var $refineLink = $(e.target).closest('.refine-link');
    var url = $refineLink.data('url');

    // Clean the URL parameters.
    url = this.cleanURLParameters(url);

    if (this.refineEventMode) {
        this.fireRefineEvent(url);
    } else {
        /* Else, then go to url */
        var url = new BuildUrl(url);
        url.removeParameter("_");

        /* Increment counter for most used filters in the quick filters */
        if (window.refineCounter) {
            var categoryId = $refineLink.data('categoryid');
            /* Increment only if a refine is performed (no zap) */
            if (categoryId && $refineLink.closest('tr.category').hasClass('displayed')) {
                var newState = window.refineCounter.incrementCategory(categoryId);
                if (!newState[categoryId].label) {
					const categoryText = $refineLink.text().trim();
                    window.refineCounter.setCategoryLabel(categoryId, categoryText !== '' ? categoryText : $refineLink.data('label'));
                }
            }
        }
        window.location.assign(url.toString());
    }
};

/**
 * refresh refinePanel widget and fire a 'refinePanel:refine' event with the @param url as value
 * @param url of the refine
 */
FacetRefine.prototype.fireRefineEvent = function (url) {
    var $refineWidget = this.$widget;

    // Create a new PlmaAjaxClient with a success callback.
    var client = new PlmaAjaxClient($("body"), {
        success: function () {
            // After a successful refresh, trigger the refine event.
            $(document).trigger("refinePanel:refine", url);
        }
    });
    client.addWidget(this.uCssId);
    var currentUrl = new BuildUrl(url);
    for (var paramName in currentUrl.params) {
        if (currentUrl.getParameters().hasOwnProperty(paramName)) {
            client.addParameters(paramName, currentUrl.getParameters()[paramName]);
        }
    }
    client.update();
};

FacetRefine.prototype.getSelectedSortOption = function () {
    return $(this.facetObject).find('.sort-menu .selected').attr('id');
};

FacetRefine.prototype.getSelectedViewOption = function () {
    return $(this.facetObject).find('.view-menu .selected').attr('id');
};

FacetRefine.prototype.reloadFacetWithAllCategories = function (isSort) {
    /* Get user default sort & view */
    if (this.facetConfig.enableSort) {
        FacetRefine.SELECTED_SORT = this.getSelectedSortOption();
    }
    FacetRefine.SELECTED_VIEW = this.getSelectedViewOption();

    /* Display spinner */
    $(this.facetObject).find('.facet-spinner-container').addClass('active');

    var categoryListToHighlight = "";

    var suggests = $('.refinepanel-suggest li');
    suggests.each(function (iSuggest, eSuggest) {
        var facetName = $(eSuggest).data('value').replace('facet__', '').split(': ')[0];
        var catName = $(eSuggest).data('value').split(': ')[1].replace('\"', '').replace('\"', '');
        /* watch if facet already int the list */
        if (this.facetName === facetName) {
            categoryListToHighlight += this.customDelimiter + catName;
        }
    });

    var url = new BuildUrl(window.location.href);
    var options = {baseUrl: this.baseAjaxReload};

    var client = new PlmaAjaxClient($('.' + this.uCssId), options);
    var parameters = url.getParameters();

    /* Delete the base params */
    client.getAjaxUrl().params = {};

    $.each(parameters, function (index, value) {
        if (index !== "" && index !== "_") {
            for (var i = 0; i < value.length; i++) {
                client.getAjaxUrl().addParameter(index, value[i], false);
            }
        }
    });

    client.getAjaxUrl().addParameter('panelId', this.refinesPanelId);

    /* Add refines on loadFacetParameter */
    for (var paramKey in parameters) {
        if (paramKey.indexOf('.r') !== -1 || paramKey.indexOf('.zr') !== -1) {
            for (var i = 0; i < parameters[paramKey].length; i++) {
                client.getAjaxUrl().addParameter(this.loadDateFacetParameter, parameters[paramKey][i].split('/')[1], false);
            }
        }
    }

    /* Add parameter */
    if (FacetRefine.SELECTED_VIEW == FacetRefine.PIE_VIEW && isSort == FacetRefine.LOAD_ALL_CATEGORIES) {
        if (this.facetConfig.enableSort && typeof FacetRefine.SELECTED_SORT != 'undefined') {
            client.getAjaxUrl().addParameter(FacetRefine.SORT, JSON.stringify({"facet": this.facetName, "sortMode": FacetRefine.SELECTED_SORT}));
            client.getAjaxUrl().addParameter(FacetRefine.PLMA_FACET_SORT_RELOAD, this.facetName);
        }
        client.getAjaxUrl().addParameter(this.paramNameReload, this.facetName);
    } else if (isSort != FacetRefine.LOAD_ALL_CATEGORIES && this.facetConfig.enableSort) {
        client.getAjaxUrl().addParameter(FacetRefine.SORT, JSON.stringify({"facet": this.facetName, "sortMode": isSort}));
        client.getAjaxUrl().addParameter(FacetRefine.PLMA_FACET_SORT_RELOAD, this.facetName);
    } else {
        if (this.facetConfig.enableSort && typeof FacetRefine.SELECTED_SORT != 'undefined') {
            client.getAjaxUrl().addParameter(FacetRefine.SORT, JSON.stringify({"facet": this.facetName, "sortMode": FacetRefine.SELECTED_SORT}));
            client.getAjaxUrl().addParameter(FacetRefine.PLMA_FACET_SORT_RELOAD, this.facetName);
        }
        client.getAjaxUrl().addParameter(this.paramNameReload, this.facetName);
        client.getAjaxUrl().addParameter(FacetRefine.RELOAD_FACET_PAGE, this.paginationInfo.page);
    }

    if (this.loadDateFacetParameter != ''){
        client.getAjaxUrl().addParameter(this.loadDateFacetParameter, this.facetName);
    }

    if (categoryListToHighlight !== "") {
        client.getAjaxUrl().addParameter("catsearchrefine", categoryListToHighlight.replace(this.customDelimiter, ''), true);
    } else {
        delete client.getAjaxUrl().params.catsearchrefine;
    }

    if (this.suggestApiAction != 'access') {
        client.getAjaxUrl().addParameter(RefinePanelSearch.PARAMETER_IS_SUGGEST_SEARCH, true, true);
        if (this.$input.val() === '') {
            client.getAjaxUrl().addParameter(RefinePanelSearch.PARAMETER_IS_SUGGEST_SEARCH_EMPTY, true, true);
        }
    }

    this.feeds.forEach(feedName => {
        client.getAjaxUrl().addParameter(feedName + '-use_synthesis_facets', this.facetName);
    })

    client.getAjaxUrl().addParameter('use_page_feeds', this.feeds.join());

    /*  set wuids to update */
    client.addWidget(this.uCssId, true, false);
    /* update children widgets
    $('.wuid.' + this.uCssId + ' .wuid').each(function (eWidget, iWidget) {
        client.addWidget(iWidget.getClassList()[1]);
    });*/

    client.getWidget($.proxy(function (widgets, appendScript) {
        /* Get the right block */
        var facetBlock = $(widgets[0].html).find('.filter-list-facet-elem-facet-' + this.facetName);
        $('.' + this.uCssId + ' .filter-list-facet-elem-facet-' + this.facetName).replaceWith(facetBlock);
        $('#mainWrapper').append(appendScript);
    }, this), function (e) {
    });
};

FacetRefine.prototype.updateFacetCategoriesList = function (pFacetName, pPage) {
    var url = new BuildUrl(window.location.href);
    var options = {baseUrl: this.baseAjaxReload};

    var client = new PlmaAjaxClient($('.' + this.uCssId), options);
    var parameters = url.getParameters();

    /* Delete the base params */
    client.getAjaxUrl().params = {};

    client.getAjaxUrl().addParameter('panelId', this.refinesPanelId);

    $.each(parameters, function (index, value) {
        if (index !== "" && index !== "_") {
            for (var i = 0; i < value.length; i++) {
                client.getAjaxUrl().addParameter(index, value[i], false);
            }
        }
    });

    this.feeds.forEach(feedName => {
        client.getAjaxUrl().addParameter(feedName + '-use_synthesis_facets', pFacetName);
        client.getAjaxUrl().addParameter(feedName + '-facets-' + pFacetName + '-page', pPage);
    })

    client.getAjaxUrl().addParameter('use_page_feeds', this.feeds.join());

    /*  set wuids to update */
    client.addWidget(this.uCssId, true, false);

    client.getWidget($.proxy(function (widgets, appendScript) {
        /* Get the right block */
        var facetBlock = $(widgets[0].html).find('.filter-list-facet-elem-facet-' + this.facetName);
        $('.' + this.uCssId + ' .filter-list-facet-elem-facet-' + this.facetName).replaceWith(facetBlock);
        $('#mainWrapper').append(appendScript);
    }, this), function (e) {
        console.error("Error getting paginated categories for facet [" + pFacetName + "] (page [" + pPage + "])");
        console.error(e);
    });
}

FacetRefine.prototype.getCollapsibleCookie = function () {
    var cookieVal = this.openCookie(FacetRefine.COLLAPSIBLE_COOKIE_NAME + '-' + this.pageName);
    var facet = $(this.facetObject);
    if (cookieVal[this.facetName] === true) {
        facet.find('.icon-collapse-button, .toolbox, .view-container .selected').toggleClass('hidden');
    }
    if (this.openCookie(FacetRefine.COLLAPSIBLE_TOOLBOX)[this.facetName]) {
        this.toggleMenuEvent();
    }
};

FacetRefine.prototype.getSelectedViewCookie = function () {
    const cookieVal = this.openCookie(FacetRefine.REFINE_PANEL_FACET_OPTION, []);
    const facetView = cookieVal.find(el => el.facet === this.facetName && el.page === this.pageName);

    return facetView ? facetView.displayMode : false;
};

FacetRefine.prototype.getSelectedSortCookie = function () {
    const cookieVal = this.openCookie(FacetRefine.REFINE_PANEL_FACET_OPTION, []);
    const facetView = cookieVal.find(el => el.facet === this.facetName && el.page === this.pageName);

    return facetView ? facetView.sortMode : false;
};

FacetRefine.prototype.getSortModeEnum = function (sortEnumValue) {
    const sortModes = {
        'alphanum': 'ALPHANUM_ASC',
        '-alphanum': 'ALPHANUM_DESC',
        '-count': 'COUNT_ASC',
        'count': 'COUNT_DESC',
        'aggr': 'AGGR_ASC',
        '-aggr': 'AGGR_DESC',
        'range': 'RANGE_ASC',
        '-range': 'RANGE_DESC',
        'shuffle': 'SHUFFLE',
        'default': 'DEFAULT'
    };

    return sortModes[sortEnumValue] || sortModes['default'];
};

FacetRefine.prototype.getURLParams = function () {
    if (location.href.indexOf('?') != -1) {
        return location.href.substring(location.href.indexOf('?') + 1);
    }
    return "";
}

FacetRefine.prototype.addSortEvent = function () {
    const handleSortClick = (sortType, sortOrder, e) => {
        const sortIcon = $(this.facetObject).find(`.fonticon-sort-${sortType}`);
        this.reloadFacetWithAllCategories(sortIcon.hasClass('selected') ? sortType.includes('asc') ? `${sortOrder}_DESC`:`${sortOrder}_ASC` : `${sortOrder}_ASC`);
        e.stopPropagation();
    };

    $(this.facetObject).find('.fonticon-sort-alpha-asc').click((e) => handleSortClick('alpha-asc', 'ALPHANUM', e));
    $(this.facetObject).find('.fonticon-sort-alpha-desc').click((e) => handleSortClick('alpha-desc', 'ALPHANUM', e));
    $(this.facetObject).find('.fonticon-sort-num-asc').click((e) => handleSortClick('num-asc', 'COUNT', e));
    $(this.facetObject).find('.fonticon-sort-num-desc').click((e) => handleSortClick('num-desc', 'COUNT', e));

    $(this.facetObject).find('.fonticon-reset').click((e) => {
        const defaultSortIcon = $(this.facetObject).find('.fonticon-sort-alpha-asc');
        this.reloadFacetWithAllCategories(FacetRefine.DEFAULT_SORT);
        defaultSortIcon.addClass('selected');
        e.stopPropagation();
    });
};

FacetRefine.prototype.openCookie = function (cookieName, storageType = {}) {
    /* get cookies */
    const cookie = JSON.parse($.cookie(cookieName));
    /* update cookies */
    return cookie || storageType;
};

FacetRefine.prototype.storeCookie = function (cookieName, cookieObject) {
    $.cookie(cookieName, JSON.stringify(cookieObject), {path: '/' + this.applicationId});
};

FacetRefine.prototype.addCollapsibleEvent = function () {
    $(this.facetObject).find('span.facet-name, span.collapsible-buttons').click($.proxy(function () {
        var facet = $(this.facetObject);
        facet.find('.icon-collapse-button, .toolbox, .view-container .selected').toggleClass('hidden');
        var cookie = this.openCookie(FacetRefine.COLLAPSIBLE_COOKIE_NAME + '-' + this.pageName);
        cookie[this.facetName] = facet.find('.icon-collapsible-button').hasClass('hidden');
        this.storeCookie(FacetRefine.COLLAPSIBLE_COOKIE_NAME + '-' + this.pageName, cookie);
        if (this.getFacetCategory().length <= 0) {
            console.warn(`Facet '${$(this.facetObject).find('span.facet-name').text()}' is Empty`);
        }
        this.toggleToolboxEvent();
    }, this));
};

FacetRefine.prototype.addCollapsibleMenuEvent = function () {
    $(this.facetObject).find('.toolbox').click($.proxy(function () {
        var cookie = this.openCookie(FacetRefine.COLLAPSIBLE_TOOLBOX);
        cookie[this.facetName] = !$(this.facetObject).find('.toolbox').hasClass('expanded');
        this.storeCookie(FacetRefine.COLLAPSIBLE_TOOLBOX, cookie);
        this.toggleMenuEvent();
    }, this));
};

FacetRefine.prototype.toggleMenuEvent = function () {
    var facet = $(this.facetObject);
    facet.find('.toolbox').toggleClass('expanded');
    facet.find('.toolbox .menu-icon').children().toggleClass('hidden')
};

FacetRefine.prototype.toggleToolboxEvent = function () {
    if (!$(this.facetObject).find('.toolbox .menu-icon').children().hasClass('hidden')) {
        $(this.facetObject).find('.toolbox .menu-icon').children().addClass('hidden');
        var cookie = this.openCookie(FacetRefine.COLLAPSIBLE_TOOLBOX);
        cookie[this.facetName] = false;
        this.storeCookie(FacetRefine.COLLAPSIBLE_TOOLBOX, cookie);
    }
};

FacetRefine.prototype.addColumnToggleEvent = function () {
    $(this.facetObject).find(FacetRefine.FONTICON_COLUMN).click($.proxy(function (e) {
        if (this.currentView != FacetRefine.COLUMN_VIEW) {
            this.currentView = FacetRefine.COLUMN_VIEW;
            var facet = this.toggleSelectedView($(this.facetObject));
            facet.find(".view-menu .selected").removeClass('selected');
            facet.find(FacetRefine.FONTICON_COLUMN).addClass('selected');
            this.buildFacetChart(FacetRefine.COLUMN_VIEW);
            facet.find(FacetRefine.HIGHCHARTS_FACET_CONTAINER).highcharts().reflow();
            facet.find(FacetRefine.HIGHCHARTS_FACET_CONTAINER + ',' + FacetRefine.PAGINATION).removeClass('hidden').addClass('selected');
            this.updateCookies();
        }
        e.stopPropagation();
    }, this));
};

FacetRefine.prototype.addPieToggleEvent = function () {
    $(this.facetObject).find(FacetRefine.FONTICON_PIE).click($.proxy(function (e) {
        if (this.currentView != FacetRefine.PIE_VIEW) {
            this.currentView = FacetRefine.PIE_VIEW;
            var facet = this.toggleSelectedView($(this.facetObject));
            facet.find(".view-menu .selected").removeClass('selected');
            facet.find(FacetRefine.FONTICON_PIE).addClass('selected');
            facet.find(FacetRefine.HIGHCHARTS_FACET_CONTAINER).removeClass('hidden').addClass('selected');
            facet.find(FacetRefine.PAGINATION).addClass('hidden');
            this.buildFacetChart(FacetRefine.PIE_VIEW);
            facet.find(FacetRefine.HIGHCHARTS_FACET_CONTAINER).highcharts().reflow();
            this.updateCookies();
        }
        e.stopPropagation();
    }, this));
};

FacetRefine.prototype.addListToggleEvent = function () {
    $(this.facetObject).find(FacetRefine.FONTICON_LIST).click($.proxy(function (e) {
        if (this.currentView != FacetRefine.LIST_VIEW) {
            this.currentView = FacetRefine.LIST_VIEW;
            var facet = this.toggleSelectedView($(this.facetObject));
            facet.find(".view-menu .selected").removeClass('selected');
            facet.find(FacetRefine.FONTICON_LIST).addClass('selected');
            facet.find(FacetRefine.FACET_CONTAINER_LIST + "," + FacetRefine.PAGINATION).removeClass('hidden').addClass('selected');
            facet.find(FacetRefine.HIGHCHARTS_FACET_CONTAINER).removeClass('selected').addClass('hidden');
            this.updateCookies();
            this.gotoPage();
        }
        e.stopPropagation();
    }, this));
};

FacetRefine.prototype.toggleSelectedView = function (facet) {
    var viewSelcted = facet.find(".view-menu .selected").attr('id');
    for (let view in FacetRefine.VIEWS) {
        if (viewSelcted == FacetRefine.VIEWS[view]) {
            facet.find('.facet-container-' + FacetRefine.VIEWS[view]).toggleClass('hidden').removeClass('selected');
            return facet;
        }
    }
};

FacetRefine.prototype.updateCookies = function () {
    /* Get user default view */
    var storedDefaultView = this.getSelectedViewCookie();
    var currentSelectedView = typeof this.getSelectedViewOption() == 'undefined' ? (storedDefaultView == false) ? this.defaultView : storedDefaultView.toLowerCase() : this.getSelectedViewOption();

    var storedSortOption = this.getSelectedSortCookie();
    var currentSortOption = typeof this.getSelectedSortOption() == 'undefined' ? (storedSortOption == false) ? this.getSortModeEnum(this.facetConfig.sortMode) : storedSortOption : this.getSelectedSortOption();

    var cookie = this.openCookie(FacetRefine.REFINE_PANEL_FACET_OPTION, []);
    if (typeof cookie == 'undefined') {
        cookie.push({"facet": this.facetName, "displayMode": currentSelectedView.toUpperCase(), "sortMode": currentSortOption, "page": this.pageName});
    } else {
        var modified = false;
        cookie.forEach((item) => {
            if (item["facet"] == this.facetName && item["page"] == this.pageName) {
                item["displayMode"] = currentSelectedView.toUpperCase();
                item["sortMode"] = currentSortOption;
                modified = true;
                return;
            }
        });
        if (!modified) {
            cookie.push({"facet": this.facetName, "displayMode": currentSelectedView.toUpperCase(), "sortMode": currentSortOption, "page": this.pageName});
        }
    }
    $.cookie(FacetRefine.REFINE_PANEL_FACET_OPTION, JSON.stringify(cookie), {path: '/' + this.applicationId});
};

FacetRefine.prototype.getFacetCategory = function () {
    return $(this.facetObject).find('.category');
};


FacetRefine.prototype.updateFacetPageDisplay = function (facet) {
    facet.find('.category').addClass('hidden');
    if (this.currentView == FacetRefine.LIST_VIEW) {
        facet.find('.facet-page-' + this.paginationInfo.page).removeClass('hidden');
    }
    facet.find('.pagination .current-page').text(this.paginationInfo.page);
};

FacetRefine.prototype.initPagination = function () {
    var pagination = $(this.facetObject).find(FacetRefine.PAGINATION);
    pagination.find('.previous-page').on('click', $.proxy(function (e) {
        this.previousPage();
        e.stopImmediatePropagation();
    }, this));
    pagination.find('.next-page').on('click', $.proxy(function (e) {
        this.nextPage();
        e.stopImmediatePropagation();
    }, this));
};

FacetRefine.prototype.previousPage = function () {
    if (this.paginationInfo.page > 1) {
        this.paginationInfo.page--;
    } else {
        this.paginationInfo.page = this.paginationInfo.pages;
    }
    this.gotoPage();
};

FacetRefine.prototype.nextPage = function () {
    if (this.paginationInfo.page < this.paginationInfo.pages) {
        this.paginationInfo.page++;
    } else {
        this.paginationInfo.page = 1;
    }
    this.gotoPage();
};

FacetRefine.prototype.gotoPage = function () {
    /* Display spinner and hide pagination */
    $(this.facetObject).find('.facet-spinner-container').addClass('active');
    $(this.facetObject).find('.pagination').addClass('hidden');
    var facet = $(this.facetObject);
    if (this.currentView == FacetRefine.LIST_VIEW && !this.paginationInfo.uiPagination) {
        // Facet is already paginated by access trigger --> add categories in DOM element calling an ajax request
        this.updateFacetCategoriesList(this.facetName, this.paginationInfo.page);
    } else {
        // Chart mode --> get JSON data series if not in cache and refresh chart (pie or column)
        // Load facet page in ajax only if not present in context (this allows to not recall ajax URL if already called)
        var currentPageCategories = this.paginatedSerie.pages.find(el => el.page == this.paginationInfo.page);
        if (!currentPageCategories) {
            var ajaxPoints = this.getRefineSerie();
            if (ajaxPoints) {
                this.paginatedSerie.pages.push({"page": this.paginationInfo.page, "points": ajaxPoints});
            }
        }
        if (this.currentView == FacetRefine.COLUMN_VIEW) {
            // In case of paginated chart (columns) --> reload chart
            this.buildFacetChart()
        }
    }
    this.updateFacetPageDisplay(facet);
    /* Hide spinner and display pagination if present */
    $(this.facetObject).find('.facet-spinner-container').removeClass('active');
    $(this.facetObject).find('.pagination').removeClass('hidden');
}

FacetRefine.prototype.getRefineSerie = function () {
    var ajaxParams = {};
    var result = [];

    this.feeds.forEach(feedName => {
        ajaxParams[this.feedName + '-use_synthesis_facets'] = this.facetName;
        if (this.currentView == FacetRefine.PIE_VIEW) {
            ajaxParams[feedName + '-facets-' + this.facetName + '-exhaustive'] = 'true';
        } else {
            ajaxParams[feedName + '-facets-' + this.facetName + '-page'] = this.paginationInfo.page;
        }
    })

    //let keepParams = this.keepAllParameters ? '&keepAllParameters=' + this.keepAllParameters :  "&keepParameters=" + this.keepParameterList;

    $.ajax({
        url: this.baseAjaxUrl + '/RefineSerie?facet=' + this.facetName + '&' + this.getURLParams(),
        //url: this.baseAjaxUrl + '/RefineSerie?facet=' + this.facetName + '&' + this.getURLParams() + keepParams,
        type: 'GET',
        data: ajaxParams,
        context: this,
        async: false,
        success: function (data) {
            result = data.points;
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.error("Error getting facet categories page...");
        }
    });

    return result;
}

FacetRefine.prototype.buildFacetChart = function () {
    /* Build data */
    var chartData = [];
    if (this.currentView == FacetRefine.PIE_VIEW) {
        if (!this.pieChartPoints) {
            this.pieChartPoints = this.getRefineSerie();
        }
        chartData = this.pieChartPoints;
    } else {
        // Load facet page in ajax only if not present in context (this allows to not recall ajax URL if already called)
        var currentPageCategories = this.paginatedSerie.pages.find(el => el.page == this.paginationInfo.page);
        if (!currentPageCategories || currentPageCategories.points.length == 0) {
            var ajaxPoints = this.getRefineSerie();
            if (ajaxPoints) {
                if (currentPageCategories) {
                    currentPageCategories.points = ajaxPoints;
                } else {
                    this.paginatedSerie.pages.push({"page": this.paginationInfo.page, "points": ajaxPoints});
                }
            }
        }
        chartData = this.paginatedSerie.pages.find(el => el.page == this.paginationInfo.page).points;
    }

    if (chartData.length < 2) {
        this.facetObject.getClassList().add('unique-cat');
    } else {
        this.facetObject.getClassList().remove('unique-cat');
    }

    /* Build chart */
    var _this = this;
    this.refineChart = new Highcharts.Chart({
        chart: {
            height: 220,
            renderTo: $(this.facetObject).find(FacetRefine.HIGHCHARTS_FACET_CONTAINER)[0],
            type: _this.currentView,
            animation: false
        },
        tooltip: {
            pointFormat: _this.currentView == FacetRefine.PIE_VIEW ? '<br>{point.y} ({point.percentage:.0f}% of {point.total})' : '<b>{point.y}</b>'
        },
        title: {
            text: null
        },
        xAxis: {
            labels: {
                enabled: false
            }
        },
        yAxis: {
            min: 0,
            title: {
                text: null
            }
        },
        credits: {
            enabled: false
        },
        series: [{
            animation: false,
            borderColor: '#f0f0f0',
            showInLegend: false,
            data: chartData,
            maxPointWidth: 10,
            dataLabels: {
                enabled: false
            },
            cursor: 'pointer',
            point: {
                events: {
                    mouseOver: $.proxy(function (e) {
                        $(this.facetObject).find('.refinecontainer').each(function (iDiv, eDiv) {
                            eDiv.getClassList().remove('active');
                        });
                    }, this),
                    click: $.proxy(function (e) {
                        url = (_this.pageName + e.point.refineParam);
                        location.href = url;
                    }, this)
                }
            }
        }]
    });

    $(window).on('plma:refineResize', function () {
        console.info("Reload refine panel chart...")
        _this.refineChart.reflow();
    });
};