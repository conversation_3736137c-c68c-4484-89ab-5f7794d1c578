@import "variables.less";
@import "utils.less";

.message-plma-container{
	max-height: 80px;
	overflow: hidden;
	.transition(@transition-normal, ~"max-height");
	&.hidden{
		max-height: 0;
		.transition(@transition-normal, ~"max-height");
	}
	.message-plma-padding{
		padding: 10px;
		.message-plma{
			padding: 5px;
		    border: 1px solid @cblock-border;
		    border-radius: 4px;
		    box-shadow: 0 1px 1px rgba(0,0,0,0.3);
		    margin: 0;
		    min-height: 30px;
		    display: flex;
		    align-items: center;
		    &.message-alert{
		    	border-color: @orange-border;
		    	background-color: @orange-background;
		    }
		    &.message-success{
		    	border-color: @green-border;
		    	background-color: @green-background;
		    }
		    &.message-info{
		    	border-color: @cyan-border;
		    	background-color: @cyan-background;
		    }
		    .message-text{
		    	font-size: 12px;
		    	margin-left: 15px;
		    	flex: 1
		    }
		    .fonticon-message{
		    	font-size: 18px;
		    }
		    .message-cancel{
		    	float: right;
		    	font-size: 18px;
		    	cursor: pointer;
		    }
		}
	}
}