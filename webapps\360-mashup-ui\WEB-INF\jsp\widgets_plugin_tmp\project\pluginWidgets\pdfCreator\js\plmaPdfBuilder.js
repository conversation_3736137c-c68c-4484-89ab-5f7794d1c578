var PLMAPDFBuilder = function(name, options, pageTemplate){
	this.name;
	this.options = options;
	this.pageTemplate = pageTemplate;
	this.pageBuilders = [];
}

PLMAPDFBuilder.TOTAL_PAGES_EXPR = '{total_pages_count_string}';

/*  buildPage method is used to build a page one by one i.e once a single page is rendered 
* including header,footer and widget then only start building other page.
*   @params
*	doc : jspdf doc object to set pages
*	headerFooterData: has all data regarding header,footer,activefilters
*	widgetData: widgets data that is used to render widget header and widget.
*	@ returns {object} - promise object to indicate whether the page is build.
*/
PLMAPDFBuilder.prototype.buildPage = function(doc, headerFooterData, widgetData){
	let prevPagePromise = this.pageBuilders.length == 0? undefined : this.pageBuilders[this.pageBuilders.length - 1];
	let builderPromise = new this.pageTemplate(this.options, prevPagePromise);
	this.pageBuilders.push(builderPromise.finished);

	builderPromise.started.then(function(pageBuilder){
		this.renderPageTemplate(pageBuilder, doc, headerFooterData)
			.then(function(pageBuilder){
				pageBuilder.renderWidget(doc, widgetData, headerFooterData);
			});
	}.bind(this));
	return builderPromise.finished;
}


/*  renderPageTemplate method is used to render Complete Page that includes rendering header and footer 
* then adding active filter on the first page
*   @params
*	doc : jspdf doc object to set pages
*	headerFooterData: has all data regarding header,footer,activefilters
*	@ returns {object} - promise object to indicate whether the page template is rendered.
*/
PLMAPDFBuilder.prototype.renderPageTemplate = function(pageBuilder, doc, headerFooterData){
	let dfd = $.Deferred();
	// Initialize with safe font setting
	pageBuilder.setFontSafe(doc, 'normal');
	pageBuilder.renderHeader(doc, headerFooterData.header)
		.then(function(pageBuilder){
			pageBuilder.renderFooter(doc, headerFooterData.footer);
			if(doc.getCurrentPageInfo().pageNumber === 1) {
			    pageBuilder.renderActiveFilters(doc, headerFooterData.activeFilters).then(function(){
			        dfd.resolve(pageBuilder);
			    })
			}else{
			    dfd.resolve(pageBuilder);
			}
		});

	return dfd.promise();
}

PLMAPDFBuilder.prototype.complete = function(doc){
	doc.putTotalPages(PLMAPDFBuilder.TOTAL_PAGES_EXPR);
}

/*------------------------------------------------------------------------*/
/* We need to build pages one by one.
*/
PDFDefaultTemplate = function(options, prevPagePromise){
	this.options = options;
	this.prevPagePromise = prevPagePromise;
	this.bodyDFD = $.Deferred();
	this.currentY = this.options.margin.top;
	this.customFontAvailable = false;

	let dfd = $.Deferred();
	// Once previous page is built return current page builder object.
	if(this.prevPagePromise){
		this.prevPagePromise.then(function(pageBuilder){
			dfd.resolve(this);
		}.bind(this))
	}else{
		dfd.resolve(this);
	}

	return {
		started: dfd.promise(),
		finished: this.bodyDFD.promise()
	};
}

/*  registerCustomFont method is used to register the custom font with jsPDF
*   @params
*	doc : jspdf doc object
*	@ returns {boolean} - true if font was registered successfully
*/
PDFDefaultTemplate.prototype.registerCustomFont = function(doc){
	if (typeof font !== 'undefined' && !this.customFontAvailable) {
		try {
			doc.addFileToVFS("NotoSansSC-Bold.ttf", font);
			doc.addFont("NotoSansSC-Bold.ttf", "NotoSansSC", "bold");
			this.customFontAvailable = true;
			return true;
		} catch (e) {
			console.warn("Failed to register custom font:", e);
			this.customFontAvailable = false;
			return false;
		}
	}
	return this.customFontAvailable;
}

/*  setFontSafe method is used to safely set font with fallback
*   @params
*	doc : jspdf doc object
*	style : font style ('normal', 'bold', etc.)
*/
PDFDefaultTemplate.prototype.setFontSafe = function(doc, style){
	if (this.registerCustomFont(doc)) {
		try {
			doc.setFont("NotoSansSC", style);
		} catch (e) {
			doc.setFont(this.options.fontFamily, style);
		}
	} else {
		doc.setFont(this.options.fontFamily, style);
	}
}

PDFDefaultTemplate.prototype.renderHeader = function(doc, headerData){
	let dfd = $.Deferred();
	let pageWidth = doc.internal.pageSize.getWidth();

	headerData.logoPromise
		.then(function(logoData){
			// Header Backgorund
			doc.setDrawColor('#ffffff');
			doc.setFillColor('#d5e8f2');
			doc.rect(0,
				0,
				pageWidth,
				this.options.header.height + this.currentY,
				'F');
			// Logo
			doc.addImage(logoData.dataUrl, logoData.type,
				this.options.margin.left,
				this.currentY,
				this.options.header.height - 5,
				this.options.header.height - 5,
				'logo');

			// PDF Title
			doc.setFontSize(this.options.header.titleFontSize);
			doc.text(headerData.title,
				this.options.margin.left*2 + this.options.header.height,
				this.currentY, {
					align: 'left',
					baseline: 'top'
			});

			// PDF : Date
			doc.setFontSize(this.options.header.fontSize);
			doc.text(headerData.date,
				pageWidth - this.options.margin.right,
				this.options.header.fontSize + this.currentY, {
					align: 'right'
			});

			// PDF Desciption
			let description = headerData.description?headerData.description : '';
			let maxLineWidth = pageWidth - this.options.margin.left*2 - this.options.header.height - this.options.margin.right ;
			let descriptionLines = doc.splitTextToSize(description, maxLineWidth);
			doc.text(descriptionLines,
				this.options.margin.left*2 + this.options.header.height,
				this.currentY + this.options.header.titleFontSize + 5, {
					align: 'left',
					baseline: 'top'
			});

			// Header Bottom Border
			doc.setDrawColor('#005686');
			doc.setLineWidth(1);
			doc.line(this.options.margin.left,
				this.options.header.height + this.currentY,
				pageWidth - this.options.margin.right,
				this.options.header.height + this.currentY);

			// Set the final Y position in page where next rendering can start. and resolve.
			this.currentY += this.options.header.height;
			dfd.resolve(this);
		}.bind(this));
	return dfd.promise();
}

PDFDefaultTemplate.prototype.renderFooter = function(doc, footerData){
	let pageHeight = doc.internal.pageSize.getHeight();
	let pageWidth = doc.internal.pageSize.getWidth();

	// Footer Backgorund
	doc.setDrawColor('#ffffff');
	doc.setFillColor('#d5e8f2');
	doc.rect(0,
		pageHeight - this.options.footer.height - this.options.margin.bottom,
		pageWidth,
		pageHeight - this.options.footer.height - this.options.margin.bottom,
		'F');

	// Footer top border
	doc.setDrawColor('#005686');
	doc.setLineWidth(1);
	doc.line(this.options.margin.left,
		pageHeight - this.options.footer.height - this.options.margin.bottom,
		pageWidth - this.options.margin.right,
		pageHeight - this.options.footer.height - this.options.margin.bottom);

	// Page Number
	doc.setFontSize(this.options.footer.fontSize);
	doc.text(doc.internal.getCurrentPageInfo().pageNumber + footerData.delimeter + PLMAPDFBuilder.TOTAL_PAGES_EXPR,
		this.options.margin.left,
		pageHeight - this.options.footer.fontSize - this.options.margin.bottom, {
			align: 'left'
	});

	// Copyright info
	doc.text(footerData.copyright? footerData.copyright:'\xa9 Dassault Systemes',
		pageWidth - this.options.margin.right,
		pageHeight - this.options.footer.fontSize - this.options.margin.bottom, {
			align: 'right'
	});
}

/*  renderActiveFilters method is used to render Active Filter that are currently available on the page who's snapshot is been taken.Active Filters are available on First Page only.
*   @params
*	doc : jspdf doc object to set pages
*	activeFilters: array having all current active filter
*	@ returns {object} - promise object to indicate whether the active filter is rendered.
*/
PDFDefaultTemplate.prototype.renderActiveFilters = function(doc, activeFilters){
	let pageWidth = doc.internal.pageSize.getWidth();
	let initialY = this.currentY;
    let dfd = $.Deferred();
	// Active Filters Label
	doc.setFontSize(this.options.widgetHeader.fontSize);
	this.setFontSafe(doc, 'bold');
	doc.text(activeFilters.label + ':',
		this.options.margin.left,
		this.currentY + 5, {
				align: 'left',
				baseline: 'top'
	});
	this.currentY += 5;

	// Render Active filters(facet & category names) as HTML
	this.setFontSafe(doc, 'normal');
	doc.setFontSize(this.options.widgetContent.fontSize);
	let maxLineWidth = pageWidth - this.options.margin.left - this.options.margin.right + 10;
	let htmlStr = '<div style="width:'+maxLineWidth+'px;color:black"><span style="font-size:12px;">';

	let currentLines = 1;
	activeFilters.filters.each(function(i, el){
		let currHtml = '<span style="font-weight:bold"><b>'+$(el).find('.facetName').text().trim() + '</b></span>: <span style="font-style:italic"><i>' +
			($(el).hasClass('excludedFilter')? '(-)' : '(+)') + ' ' +
			$(el).find('.categoryName').text().trim() + '</i></span>';
		let lines = doc.splitTextToSize($(htmlStr + currHtml).text(), maxLineWidth * 0.9);
		if(lines.length > currentLines){
			htmlStr = htmlStr.trim();
			htmlStr += '<br/>' + currHtml + ',';
			currentLines++;
		}else{
			htmlStr += currHtml + ', ';
		}
	});
	htmlStr = htmlStr.substr(0, htmlStr.length -2);
	htmlStr += '</span></div>';
    let currentPage = doc.getCurrentPageInfo().pageNumber;
    domPurify = new DOMPurify();
    //TodO: See why some HTML tags dont work {RETURN_DOM: true,ALLOWED_TAGS: ['b', 'i']}
	doc.html(domPurify.sanitize(htmlStr), {   
		x:this.options.margin.left + 10,
	    y:this.currentY + 7
	}).then(function(){
		//  page number always set to one so need to set back to currentPage
	    doc.setPage(currentPage);
		// Active Filter bottom Border.
        this.currentY += this.options.widgetHeader.fontSize + (currentLines * this.options.widgetContent.fontSize * doc.getLineHeightFactor());
        doc.setDrawColor('#d1d4d4');
        doc.setLineWidth(1);
        doc.line(this.options.margin.left,
            this.currentY,
            pageWidth - this.options.margin.right,
            this.currentY);
        this.currentY += 5;
        doc.setTextColor(this.options.textColor);
        dfd.resolve();
	}.bind(this));
	return dfd.promise();
}

/*  renderWidget method is used to render widget based on the types i.e. Chart/Data Table/Custom
*   @params
*	doc : jspdf doc object to set pages
*   widgetData: Widget Data to render widget and widget Header
*	headerFooterData:headerFooterData Data to render header and Footer
*	@ returns {object} - promise object to indicate whether the widget is rendered.
*/
PDFDefaultTemplate.prototype.renderWidget = function(doc, widgetData, headerFooterData){
	// Render Content : Based on handler provided in config(If the handler exist the OOTB render methods should not be executed)
	if(widgetData.renderWidgetHandler) {
        widgetData.renderWidgetHandler(this,doc,widgetData, headerFooterData)
			.then(function(pageBuilder){
				pageBuilder.bodyDFD.resolve(pageBuilder);
			}).catch(function(pageBuilder) {
				doc.deletePage(doc.getCurrentPageInfo().pageNumber);
				pageBuilder.bodyDFD.resolve(pageBuilder);
			});
    }else {
        // Render Content : Based on widgetType
		// Widget Header
		this.renderWidgetHeader(doc, widgetData);
		switch(widgetData.type.toLocaleLowerCase().replace(' ', '')){
        case 'chart':
            this.renderPLMAChart2(doc, widgetData)
                .then(function(pageBuilder){
                    pageBuilder.bodyDFD.resolve(pageBuilder);
                }).catch(function(pageBuilder) {
                    doc.deletePage(doc.getCurrentPageInfo().pageNumber);
                    pageBuilder.bodyDFD.resolve(pageBuilder);
                });;
            break;
        case 'datatable':
            this.renderPLMADataTable(doc,widgetData,headerFooterData)
                .then(function(pageBuilder){
                    pageBuilder.bodyDFD.resolve(pageBuilder);
                }).catch(function(pageBuilder) {
                  doc.deletePage(doc.getCurrentPageInfo().pageNumber);
                  pageBuilder.bodyDFD.resolve(pageBuilder);
              });
            break;
        default:
            console.error('Widget type['+ widgetData.type + '] Not supported. Use custom type with customer render function');
			this.bodyDFD.reject(this);
            break;
        }
	}
	return this.bodyDFD.promise();
}
/*  renderWidgetHeader method is used to render widget Header i.e.  to add a title before every widget inside the pages
*   @params
*	doc : jspdf doc object to set pages
*   widgetData: Widget Data to render widget Header
*	@ returns {object} - promise object to indicate whether the widget header is rendered.
*/
PDFDefaultTemplate.prototype.renderWidgetHeader = function(doc, widgetData){
	let pageWidth = doc.internal.pageSize.getWidth();

	// Header Backgorund
	doc.setDrawColor('#e2e4e3');
	doc.setFillColor('#f4f5f6');
	doc.rect(this.options.margin.left,
		this.currentY + 5,
		pageWidth - this.options.margin.left - this.options.margin.right,
		this.options.widgetHeader.fontSize *  doc.getLineHeightFactor() + 10,
		'DF');

	// Widget Header
	doc.setFontSize(this.options.widgetHeader.fontSize);
	this.setFontSafe(doc, 'bold');
	doc.text(widgetData.$widget.find(widgetData.headerSelector).text(),
		this.options.margin.left + 5,
		this.currentY + 10, {
			align: 'left',
			baseline: 'top'
	});

	// Reset font back to normal
	this.setFontSafe(doc, 'normal');

	this.currentY += this.options.widgetHeader.fontSize *  doc.getLineHeightFactor() + 10;
	return this.bodyDFD.promise();
}
/*  renderPLMAChart2 method is used to render a chart and add it in the jspdf file
*   @params
*	doc : jspdf doc object to set pages
*   widgetData: Widget Data to render widget
*	@ returns {object} - promise object to indicate whether the chart is rendered.
*/
PDFDefaultTemplate.prototype.renderPLMAChart2 = function(doc, widgetData){
	let dfd = $.Deferred();

	let pageWidth = doc.internal.pageSize.getWidth();
	widgetData.$widget.data('widget').getImageDataUrl('jpeg') // for png format pdf preview does not work.
		.then(function(imageData){
			let imageProps = doc.getImageProperties(imageData.dataUrl);
			let aspectRatio = imageProps.width / imageProps.height;
			let imageWidth = pageWidth - this.options.margin.left - this.options.margin.right;
			let imageHeight = imageWidth / aspectRatio;
			doc.addImage(imageData.dataUrl,imageProps.fileType,
				this.options.margin.left,
				this.currentY + 10,
				imageWidth,
				imageHeight);

			// Chart image border
			doc.setDrawColor('#e2e4e3');
			doc.rect(this.options.margin.left,
				this.currentY + 10,
				imageWidth,
				imageHeight);
			this.currentY += imageHeight;
			dfd.resolve(this);
		}.bind(this)).catch(function(err){
		    console.error(err);
		    dfd.reject(this);
		}.bind(this));
	return dfd.promise();
}

/*  renderPLMADataTable method is used to render a data table and add it in the jspdf file
*   @params
*	doc : jspdf doc object to set pages
*   headerFooterData : Headear Footer Data to render page Template
*   widgetData: Widget Data to render widget
*	@ returns {object} - promise object to indicate whether the table is rendered.
*/
PDFDefaultTemplate.prototype.renderPLMADataTable = function(doc,widgetData,headerFooterData) {
    let dfd = $.Deferred();
    let newPages = [];
    let tableContent = CreatePDFUtils.getTableContent(widgetData.$widget);
    if(tableContent.header.length == 0){
        dfd.reject(this);
        return dfd.promise();
    }
    doc.autoTable({
        margin: { top: this.currentY + 10, left: this.options.margin.left, right: this.options.margin.right },
        head: tableContent.header,
        body: tableContent.rows,
        styles: {fontSize: 9, fontStyle: "bold", halign: 'center', valign: 'middle', cellPadding: 1, lineWidth: 1, lineColor: [44, 62, 80]},
        columnStyles: {},
        horizontalPageBreak: true,
        theme: "striped",
        pageBreak: "auto",
        rowPageBreak: "auto",
        horizontalPageBreakRepeat: 0,
        didDrawPage: function (data) {
            // Called after the plugin has finished drawing everything on a page.
			// As the render header is promise, we cant resolve the dfd here, hence
			// collect the pages and at once render the header/footer and widget header on page.
            if(data.pageNumber != 1){
				// Skip first page of the table as it is already rendered with header/footer and widget header.
				newPages.push(doc.getCurrentPageInfo().pageNumber);
			}
        }.bind(this)
    });

	let params = {
		newPages:newPages,
		headerFooterData: headerFooterData,
        widgetData: widgetData,
        doc: doc,
        dfd:dfd
	};
	this.renderPageBreak(params);

    return dfd.promise();
}

/*  renderPageBreak is recurring method to Renders page template header,footer for the pages created for huge table i.e. A single table is divided into multiple pages and for every page	*	header,footer,widget header should be added.
*/
PDFDefaultTemplate.prototype.renderPageBreak = function(params){
	if(params.newPages.length == 0) {
		return params.dfd.resolve(this);
	}else {
		let pageNum = params.newPages.pop();
		this.currentY = this.options.margin.top;
		params.doc.setPage(pageNum);
		this.renderHeader(params.doc, params.headerFooterData.header)
			.then(function(pageBuilder){
				this.renderFooter(params.doc, params.headerFooterData.footer);
				this.renderWidgetHeader(params.doc, params.widgetData);
				this.renderPageBreak(params);
			}.bind(this));
	}
}

