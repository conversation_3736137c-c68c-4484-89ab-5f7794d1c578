<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>

<render:import parameters="item,type,id" ignore="true" />

<string:eval var="label" string="${item.label}"/>
<string:eval var="description" string="${item.description}"/>

<string:escape var="label" value="${label}" escapeType="HTML"/>
<string:escape var="description" value="${description}" escapeType="HTML"/>

<div class="item-description hidden" id="${type}-${id}">
    <div class="description-title">${label}</div>
    <div class="description-text">${description}</div>
</div>

