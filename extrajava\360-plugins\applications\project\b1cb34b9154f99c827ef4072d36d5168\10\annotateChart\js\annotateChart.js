var annotateChart = function (options) {


    //this key is suffixed with the page Name with params (e.j field-issue/page/fi_claim?claimid=2012-27915)
    // each chart configuration from the page is stored in a unique storage entry
    this.CHART_CONFIGURATION_STORAGE_KEY = "chart_annotation_";

    this.options = options;

    /* default annotation size */
    this.options.ANNOTATION_WITH = 200;
    this.options.ANNOTATION_HEIGHT = 120;

    /**
     * @chartsConfiguration : this object is used to save charts configuration inside storage. (array of object)
     *   @chartId : the uCssId of the plma chart
     *     @annotation : annotation of the chart
     *     @id : the annotation id
     *     <AUTHOR> the last editor
     *     @date : last edit date
     *     @x : position X
     *     @y : position Y
     *     @width : annotation width,
     *     @height : annotation height,
     * {chartsConfiguration : [
     *      {chartId : the_uCssId_of_plmaChart ,
     *       annotations : [{id : THE_ANNOTATION_ID, author : THE_AUTHOR, x : POSITION_X, y : POSITION_Y,
     *                          width : THE_WIDTH, height : THE_HEIGHT, date : THE_DATE, annotation: THE_ANNOTATION}]
     *      }]
     * }
     *
     */
    this.configurationSaver = {chartsConfiguration: []};
    this.loadAnnotation();
    this.init();

};

annotateChart.prototype.loadAnnotation = function () {
    if (this.options.waitForTrigger === "true") {
        this.setConfiguration();
        $(document).off(".load-annotation");
        $(document).on("plma:chart-is-loaded.load-annotation", $.proxy(function (e) {
            var $chart = [];
            if ($(e.target).hasClass("plmaCharts")) {
                $chart = $(e.target);
            } else {
                if ($(e.target).hasClass("highcharts-container")) {
                    $chart = $(e.target).closest(".plmaCharts");
                } else {
                    //target is the flipContainer -> find plmaCharts inside
                    $chart = $(e.target).find(".plmaCharts");
                }
            }
            if ($chart.length > 0) {
                var chartConfiguration = this.configurationSaver.chartsConfiguration.find(function (chartConfiguration) {
                    return chartConfiguration.chartId === $chart.attr("class").split(" ")[1];
                });
                if (chartConfiguration !== undefined) {
                    var uCssId = chartConfiguration.chartId;
                    var chart = Highcharts.charts.find(function (chart) {
                        return annotateChart.widgetUtils.getChartUcssIdFromContainerId($(chart.container).attr("id")) === uCssId
                    });
                    if (chart !== undefined) {
                        this.initChartConfiguration(chartConfiguration, chart);
                    }
                }
            }
            if ($chart.find(".widget-menu-container div.annotation-btn").length === 0) {
                this.createWidgetBtn($chart);
            }
        }, this));
    } else {
        $(document).ready($.proxy(function () {
            this.initChartsConfiguration();
            this.createWidgetsBtn();
        }, this));
    }
};

annotateChart.prototype.init = function () {
    this.chartConfiguration = {};
    this.$chart = undefined;
    //if widget is refreshed. detach event
    $(document).off("plma:lightbox-chart-annotation");
    $(document).on("plma:lightbox-chart-annotation", $.proxy(function (e, uCssId, reloadParametersOption) {
        if (reloadParametersOption !== undefined) {
            this.options.reload = reloadParametersOption;
        }
        if (uCssId !== undefined) {
            var charts = Highcharts.charts;
            var $plmaChart = $("." + uCssId);
            if ($plmaChart.length === 0) {
                throw new Error('Chart could not be found... (ucssId: "' + uCssId + '").');
            }
            var chartContainerId = $plmaChart.find(".chart-inner .highChartsSVGWrapper .highcharts-container").attr("id");
            this.uCssId = uCssId;
            //unset annotation mode if previously on
            if (this.chartConfiguration !== {}) {
                this.unsetAnnotationMode();
            }
            for (var i = 0; i < charts.length; i++) {
                if (charts[i].container.getAttribute("id") === chartContainerId) {
                    this.chartConfiguration = charts[i];
                    this.$chart = $(charts[i].container);
                    // set annotation mode
                    this.setAnnotationMode();
                    //register event on annotation;
                    this.registerAnnotationEvent();
                }
            }
        }
    }, this));

};

/**
 * Create an annotation widget button for each chart
 */
annotateChart.prototype.createWidgetsBtn = function () {
    if ($(this.options.btnSelector).length > 0) {
        var self = this;
        $(this.options.btnSelector).each(function (i, item) {
            if ($(item).attr("class")) {
                self.addAnnotationBtn($(item), self.options.btnIcon);
            }
        });
    }
};

/**
 * Create an annotation widget button for chart
 * @param $chart
 */
annotateChart.prototype.createWidgetBtn = function ($chart) {
    if ($(this.options.btnSelector).length > 0) {
        if ($(this.options.btnSelector).index($chart) >= 0) {
            if ($chart.attr("class") !== "") {
                this.addAnnotationBtn($chart, this.options.btnIcon);
            }
        }
    }
};

annotateChart.prototype.addAnnotationBtn = function ($item, btnIcon) {
    var annotateTitle = '';
    if (this.options.annotationScope === "shared") {
        annotateTitle = mashupI18N.get('annotateChart', 'chart.annotation.btn.title_shared');
    } else {
        annotateTitle = mashupI18N.get('annotateChart', 'chart.annotation.btn.title_user');
    }
    var cssClasses = $item.attr("class").split(" ");
    if (cssClasses.length > 2) {
        var uCssId = $item.attr("class").split(" ")[1];
        if (!this.verifyIfButtonExists(uCssId)) {
            var buttonManager = new WidgetButtonManager(uCssId, 'menu', '.headerChartContainer');
            buttonManager.addButton(btnIcon, mashupI18N.get('annotateChart', 'chart.annotation.btn.label'),
                function () {
                    $(document).trigger('plma:lightbox-chart-annotation', uCssId)
                }, undefined, "annotation-btn", annotateTitle);
        }
    }
};

annotateChart.prototype.verifyIfButtonExists = function (uCssId) {
    /* Verify if the button already exists or not */
    var menuItems = $('.' + uCssId + ' .headerChartContainer .widget-menu-container .widget-menu-item');
    for (var i = 0; i < menuItems.length; i++) {
        var $item = $(menuItems[i]);
        if ($item.hasClass('annotation-btn')) {
            return true;
        }
    }

    return false;
};

annotateChart.prototype.setAnnotationMode = function () {
    this.$chart.addClass("annotation-mode");
    //add annotate mode btn
    this.addBtn();
    // remove refine event on chart click
    this.disableRefineEvent();
    // add popup
    this.initLightbox();
    //add actions
    this.initPopupActions();

    // SHOW/HIDE popup to annotate
    this.showPopupEvent();
};

annotateChart.prototype.showPopupEvent = function () {
    this.isSelectionEvent = false;
    Highcharts.addEvent(this.chartConfiguration.container, 'click', $.proxy(function (e) {
        this.isSelectionEvent = false;
        if (this.lightbox !== undefined) {
            //prevent firing click on btn with text
            //prevent firing on selection (like zoom)
            if (!($(e.target)[0].textContent) && this.isSelectionEvent === false) {
                if (!$(e.target).hasClass("remove-annotation")) {
                    this.lightbox.show();
                    this.pointX = e.chartX;
                    this.pointY = e.chartY;
                }
            }
        }
    }, this));

    Highcharts.addEvent(this.chartConfiguration, 'selection', function (e) {
        this.isSelectionEvent = true;
    });
};

annotateChart.prototype.removeShowPopupEvent = function () {
    Highcharts.removeEvent(this.chartConfiguration.container, 'click');
    Highcharts.removeEvent(this.chartConfiguration, 'selection');
};

annotateChart.prototype.addBtn = function () {
    $(this.chartConfiguration.container).closest(".chart-wrapper").prepend("<button class='btn btn-default annotating' title='" + mashupI18N.get('annotateChart', 'chart.annotation.close.tooltip') + "'><i class='fonticon fonticon-highlighter-off'></i>" + mashupI18N.get('annotateChart', 'chart.annotation.close') + "</button>");

    this.$chart.closest(".chart-wrapper").find("button.annotating").on("click", $.proxy(function () {
        this.unsetAnnotationMode();
    }, this));
};

annotateChart.prototype.removeBtn = function () {
    $(this.chartConfiguration.container).closest(".chart-wrapper").find(".btn.btn-default.annotating").addClass("hidden");
};

annotateChart.prototype.disableRefineEvent = function () {
    for (var i = 0; i < this.chartConfiguration.series.length; i++) {
        var serie = this.chartConfiguration.series[i];
        for (var j = 0; j < serie.points.length; j++) {
            var point = serie.points[j];
            if (!point.events) {
                point.events = {};
            } else {
                this.oldPointEvent = point.events.click;
                //override refine event
                this.annotationEvent(point.events);
            }
        }
    }
};

annotateChart.prototype.annotationEvent = function (pointEvents) {
    pointEvents.click = $.proxy(function (e) {
        this.pointX = Math.round(e.point.x);
        this.pointY = Math.round(e.point.y);
    }, this);
};

annotateChart.prototype.initLightbox = function () {
    var $content = $(".annotation-popup-template").clone();
    var options = {
        showOverlay: false,
        triggerButton: $("#" + this.$chart.attr("id")),
        triggerEvent: "plma:lightbox-chart-annotation",
        content: $content.removeClass("hidden"),
        extraCss: "annotation-popup"
    };
    this.lightbox = new PLMALightbox(options);
};


annotateChart.prototype.initPopupActions = function () {
    this.lightbox.content.on("click", ".annotation-action button.add", $.proxy(function () {
        var text = this.lightbox.content.find("#annotation-input").val();
        if (text !== "") {
            var annotation = this.createAnnotation(this.pointX, this.pointY, text, this.options.author, this.options.ANNOTATION_WITH, this.options.ANNOTATION_HEIGHT, undefined, this.chartConfiguration.container.clientWidth, this.chartConfiguration.container.clientHeight);
            this.addAnnotation(annotation, this.chartConfiguration);
            this.updateChartConfiguration(this.chartConfiguration);
            this.lightbox.hide();
        }
    }, this));

    this.lightbox.content.on("click", ".annotation-action button.close", $.proxy(function () {
        this.lightbox.hide();
    }, this));
};

annotateChart.prototype.addAnnotation = function (annotation, chartConfiguration) {
    chartConfiguration.addAnnotation(annotation);
    var length = chartConfiguration.annotations.length;
    var $annotation = $(chartConfiguration.annotations[length - 1].labelsGroup.div).find('.highcharts-annotation-label');

    /* unfortunately we need to way before set annotation position if not it's not well done */
    setTimeout($.proxy(function () {
        this.setAnnotationPosition($annotation, annotation, chartConfiguration);
        this.setAnnotationSize(annotation, $annotation, annotation.width, annotation.height, chartConfiguration);
    }, this), 1000);

    this.setAnnotationId($annotation, annotation.id);
    this.setAnnotationAuthor($annotation, annotation.author);
    this.addAnnotationRemoveBtn($annotation);

};

annotateChart.prototype.onRedrawEvent = function(chartConfiguration){
    $(document).on('plma:chart-redraw', "#"+$(chartConfiguration.container).attr('id'), $.proxy(function () {
        setTimeout($.proxy(function () {
            var annotations = chartConfiguration.annotations;
            annotations.forEach(function(annotation){
                var $annotation = $(annotation.labelsGroup.div).find('.highcharts-annotation-label');
                this.setAnnotationPosition($annotation, annotation, chartConfiguration);
                this.setAnnotationSize(annotation, $annotation, annotation.width, annotation.height, chartConfiguration);
            }, this);
        }, this), 1000);
    }, this));
};

annotateChart.prototype.setAnnotationPosition = function ($annotation, annotation, chartConfiguration) {
    var x = annotation.labels[0].point.x !== undefined ?annotation.labels[0].point.x: annotation.options.labels[0].point.x;
    var containerWidth = annotation.containerWidth !== undefined ? annotation.containerWidth: annotation.options.containerWidth;
    var containerHeight = annotation.containerHeight !== undefined ? annotation.containerHeight: annotation.options.containerHeight;

    if (containerWidth) {
        x = x * chartConfiguration.container.clientWidth / containerWidth;
    }
    var y = annotation.labels[0].point.y !== undefined ?annotation.labels[0].point.y: annotation.options.labels[0].point.y;
    if (containerHeight) {
        y = y * chartConfiguration.container.clientHeight / containerHeight;
    }
    $annotation.css('left', x);
    $annotation.css('top', y);
};

annotateChart.prototype.unsetAnnotationMode = function () {
    if (this.lightbox !== undefined) {
        this.$chart.removeClass("annotation-mode");
        this.lightbox.remove();
        this.removeBtn();
        this.removeShowPopupEvent();
        this.unregisterAnnotationEvent();

        //add refine event on point click
        for (var i = 0; i < this.chartConfiguration.series.length; i++) {
            var serie = this.chartConfiguration.series[i];
            $.each(serie.data, $.proxy(function (i, point) {
                var refineUrl = annotateChart.widgetUtils.getRefineUrl(point.refineParam, this.options.reload).toString();
                if (!point.events) {
                    point.events = {};
                }
                point.events.click = function () {
                    exa.redirect(refineUrl);
                }
            }, this));
        }
    }
};

annotateChart.prototype.createAnnotation = function (x, y, text, author, width, height, id, containerWidth, containerHeight) {
    if (id === undefined) {
        id = annotateChart.widgetUtils.uuidv4();
    }
    var annotation = {
        id: id,
        author: author,
        labelOptions: {
            borderWidth: 0
        },
        labels: [{
            point: {
                x: x,
                y: y /*,
                yAxis: 0,
                xAxis: 0*/
            },
            shape: 'rect',
			/* Unescaping first to avoid multiple escapings of the same character */
            text: _.escape(_.unescape(text)),
            backgroundColor: 'white',
            useHTML: true
        }],
        shapeOptions: {strokeWidth: 0}
    };
    if (width !== undefined) {
        annotation.width = width;
    }
    if (height !== undefined) {
        annotation.height = height;
    }
    if (containerWidth !== undefined) {
        annotation.containerWidth = containerWidth;
    }
    if (containerHeight !== undefined) {
        annotation.containerHeight = containerHeight;
    }
    return annotation;
};

annotateChart.prototype.addAnnotationRemoveBtn = function ($annotation) {
    $annotation.append("<i class='fonticon fonticon-wrong remove-annotation hidden'></i>");
};

annotateChart.prototype.setAnnotationId = function ($annotation, id) {
    $annotation.attr("id", id);
};

annotateChart.prototype.setAnnotationAuthor = function ($annotation, author) {
    $annotation.data("author", author);
};

annotateChart.prototype.addAnnotationResizeBtn = function ($annotation) {
    var $resize = $("<i class='resize'></i>");
    $annotation.find("span").append($resize);
};

annotateChart.prototype.removeAnnotationResizeBtn = function ($annotation) {
    $annotation.find(".resize").remove();
};

/**
 * all event handler of annotation feature are attached with the .annotation namespace
 * This namespace allows to detach all events at once.
 * If adding new event is required use this namespace , the method annotateChart.prototype.unregisterAnnotationEvent
 * will detach added event.
 */
annotateChart.prototype.registerAnnotationEvent = function () {
    this.$chart.on("click.annotation", ".highcharts-annotation-label", $.proxy(function (e) {
        if (!$(e.target).hasClass("remove-annotation")) {
            var text = $(e.currentTarget).find("span").text();
            this.lightbox.content.find("#annotation-input").val(text);
        }
    }, this));

    this.$chart.on("mouseenter.annotation", ".highcharts-annotation-label", $.proxy(function (e) {
        $e = $(e.currentTarget);
        this.addAnnotationResizeBtn($e);
        this.disableZoomEvent();
        $e.find(".remove-annotation").removeClass("hidden");
    }, this));

    this.$chart.on("mouseleave.annotation", ".highcharts-annotation-label", $.proxy(function (e) {
        $e = $(e.currentTarget);
        this.removeAnnotationResizeBtn($e);
        this.enableZoomEvent();
        $e.find(".remove-annotation").addClass("hidden");
    }, this));

    this.$chart.on('click.annotation', '.remove-annotation', $.proxy(function (e) {
        $e = $(e.currentTarget);
        var $annotation = $e.closest(".highcharts-annotation-label");
        $e.remove();
        var id = $annotation.attr("id");
        this.chartConfiguration.removeAnnotation(id);
        this.updateChartConfiguration(this.chartConfiguration);
    }, this));

    this.$chart.on('mousedown.annotation', '.resize', $.proxy(function (e) {
        e.stopPropagation();
        $annotation = $(e.currentTarget).closest("span");
        var xOffset = e.clientX - $(e.currentTarget).closest("div.highcharts-label.highcharts-annotation-label").offset().left;
        var yOffset = e.clientY - $(e.currentTarget).closest("div.highcharts-label.highcharts-annotation-label").offset().top;
        var annotation = this.chartConfiguration.getAnnotation($annotation.closest("div.highcharts-label.highcharts-annotation-label").attr("id"));
        resizeEvent.initialiseResize($annotation, annotation, this.chartConfiguration, this, xOffset, yOffset);
    }, this));

    this.$chart.on("mousedown.annotation", ".highcharts-annotation-label", $.proxy(function (e) {
        if (!$(e.target).hasClass('remove-annotation')) { /* Disable dragging on the .remove-annotation element */
            var $annotation = $(e.currentTarget);
            var annotation = this.chartConfiguration.getAnnotation($annotation.attr("id"));
            var xOffset = e.clientX - $(e.currentTarget).offset().left;
            var yOffset = e.clientY - $(e.currentTarget).offset().top;
            dragEvent.initializeDrag($(e.currentTarget), annotation, this.chartConfiguration, this, xOffset, yOffset);
        }
    }, this));
};

annotateChart.prototype.unregisterAnnotationEvent = function () {
    this.$chart.off('.annotation');
};

annotateChart.prototype.disableZoomEvent = function () {
    this.zoomConfiguration = this.chartConfiguration.options.chart.zoomType;
    this.chartConfiguration.options.chart.zoomType = "";
};

annotateChart.prototype.enableZoomEvent = function () {
    if (this.zoomConfiguration !== "") {
        this.chartConfiguration.options.chart.zoomType = this.zoomConfiguration;
    }
};

annotateChart.prototype.setAnnotationContainer = function (annotation, chartConfiguration) {
    annotation.containerWidth = chartConfiguration.container.clientWidth;
    annotation.containerHeight = chartConfiguration.container.clientHeight;
};

var dragEvent = {
    initializeDrag: function ($draggedItem, annotation, chartConfiguration, self, initXOffset, initYOffset) {
        $(chartConfiguration.container).on("mouseup", $.proxy(function (e) {
            $(chartConfiguration.container).off("mousemove");
            $(chartConfiguration.container).off("mouseup");
            //save new X & Y coordonate
            if (chartConfiguration.axes.length > 0) {
                annotation.options.labels[0].point.x = parseInt($draggedItem.css("left").replace("px", ""));
                annotation.options.labels[0].point.y = parseInt($draggedItem.css("top").replace("px", ""));
            }
            /* set annotation container for annotation */
            self.setAnnotationContainer(chartConfiguration.getAnnotation($draggedItem.attr("id")), chartConfiguration);

            /* update annotations and save */
            self.updateChartConfiguration(chartConfiguration);
        }, this));

        $(chartConfiguration.container).on("mousemove", $.proxy(function (e) {
            var positionLeft = (e.clientX - chartConfiguration.container.getBoundingClientRect().left - initXOffset);
            var positionTop  = (e.clientY - chartConfiguration.container.getBoundingClientRect().top - initYOffset);
            var MARGIN = 20;
            var height = $draggedItem.find("span").height() + MARGIN;
            var width = $draggedItem.find("span").width() + MARGIN;
            if ($draggedItem !== undefined && positionLeft > 0 && positionTop>0
                && positionLeft + width < chartConfiguration.container.getBoundingClientRect().width
                && positionTop + height < chartConfiguration.container.getBoundingClientRect().height) {
                $draggedItem.css("left", positionLeft + 'px');
                $draggedItem.css("top", positionTop + 'px');
                /* set annotation container for annotation */
                self.setAnnotationContainer(chartConfiguration.getAnnotation($draggedItem.attr("id")), chartConfiguration);
            }
        }, this));
    },
    getChartXLimit: function(chartConfiguration){
        return chartConfiguration.container.getBoundingClientRect().right;
    },
    getChartYLimit: function(chartConfiguration){
        return chartConfiguration.container.getBoundingClientRect().bottom;
    },
    /**
     * Maximun width an annotation can be resized
     * @param chartConfiguration
     * @param $element
     * @return {number}
     */
    getMaxWidth: function (chartConfiguration, $element){
        var MARGIN = 5;
        return this.getChartXLimit(chartConfiguration) - $element.offset().left - MARGIN;
    },
    isValidWidth: function(chartConfiguration, $element, width){
        var MARGIN = 5;
        return this.getMaxWidth(chartConfiguration, $element) - width - MARGIN > 0;
    },
    getMaxHeight: function(chartConfiguration, $element){
        return this.getChartYLimit(chartConfiguration) - $element.offset().top;
    },
    isValidHeight: function(chartConfiguration, $element, height){
        return this.getMaxHeight(chartConfiguration, $element) - height > 0;
    }
};

/**
 * Set annotation size
 * update max width/height of annotation span
 * @param annotation
 * @param $annotation
 * @param width
 * @param height
 * @param chartConfiguration
 */
annotateChart.prototype.setAnnotationSize = function (annotation, $annotation, width, height, chartConfiguration) {
    width = width !== undefined?width:annotation.options.width;
    height = height !== undefined ?height:annotation.options.height;
    width = typeof width === "number"?width:parseInt(width.replace("px", ""));
    height = typeof height === "number"?height:parseInt(height.replace("px", ""));
    /* change width if chart is not in full screen mode and annotation has a width in full screen mode
        or annotation is cropped. */
    var MARGIN = 20;
    if(dragEvent.isValidWidth(chartConfiguration, $annotation, width)){
        $annotation.find("span").css("width", width - MARGIN + 'px');
    } else {
        $annotation.find("span").css("width", dragEvent.getMaxWidth(chartConfiguration, $annotation) - MARGIN + 'px');
    }
    /* change height if needed */
    if(dragEvent.isValidHeight(chartConfiguration, $annotation, height)){
        $annotation.find("span").css("height", height + 'px');
    } else {
        $annotation.find("span").css("height", (dragEvent.getMaxHeight(chartConfiguration, $annotation) - MARGIN) + 'px');
    }
};

var resizeEvent = {
    initialiseResize: function ($element, annotation, chartConfiguration, self, initXOffset, initYOffset) {
        $(document).on("mousemove", $.proxy(function (e) {
            this.start(e, $element, chartConfiguration);
        }, this));
        $(document).on("mouseup", $.proxy(function (e) {
            this.stop(e, $element, annotation, chartConfiguration, self, initXOffset, initYOffset);
        }, this));
    },
    start: function (event, $element, chartConfiguration) {
        var width = event.clientX - $element.offset().left;
        var height = event.clientY - $element.offset().top;
        if(width < dragEvent.getMaxWidth(chartConfiguration, $element.closest("div.highcharts-label.highcharts-annotation-label"))){
            $element.css("width", width + 'px');
        } else{
            //set max width
            $element.css("width", dragEvent.getMaxWidth(chartConfiguration, $element.closest("div.highcharts-label.highcharts-annotation-label")) + 'px');
        }
        $element.css("height", height + 'px');
    },
    stop: function (event, $element, annotation, chartConfiguration, self) {
        $(document).off("mousemove");
        $(document).off("mouseup");

        //set width/height
        annotation.width = parseInt($element.css("width").replace("px", ""));
        annotation.height = parseInt($element.css("height").replace("px", ""));
        self.updateChartConfiguration(chartConfiguration);
    }
};


annotateChart.widgetUtils = {
    // return a uniq css Id foreach item
    uuidv4: function () {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },
    getBaseUrl: function () {
        return new BuildUrl(window.location.href.split('?')[0]);
    },
    // @return the refine Url of a point
    getRefineUrl: function (refineParam, reloadParameters) {
        var refineUrl = this.getBaseUrl();
        var parseRefineParam = new BuildUrl(refineParam);
        for (var paramName in parseRefineParam.getParameters()) {
            if (parseRefineParam.getParameters().hasOwnProperty(paramName)) {
                refineUrl.addParameters(paramName, parseRefineParam.getParameters()[paramName]);
            }
        }
        //Add custom parameters
        if (reloadParameters !== undefined) {
            for (var k = 0; k < reloadParameters.params.length; k++) {
                refineUrl.removeParameterWithValue_(reloadParameters.params[k].split('__')[0], reloadParameters.params[k].split('__')[1]);
            }
        }
        return refineUrl;
    },
    getChartUcssIdFromContainerId: function (containerId) {
        if ($("#" + containerId).length > 0) {
            if ($("#" + containerId).closest(".plmaCharts").length > 0) {
                return $("#" + containerId).closest(".plmaCharts").attr("class").split(" ")[1];
            }
        }
        return false;
    }
};

annotateChart.prototype.saveConfiguration = function () {
    var storage = new StorageClient(this.options.annotationScope);
    storage.set(this.CHART_CONFIGURATION_STORAGE_KEY + this.options.storageKey, JSON.stringify(this.configurationSaver),
        $.proxy(function () {
            //success callback
        }, this),
        function () {
            //error callback
        });
};

annotateChart.prototype.updateChartConfiguration = function (chart) {
    var annotations = chart.annotations;
    var uCssId = annotateChart.widgetUtils.getChartUcssIdFromContainerId($(chart.container).attr("id"));
    var index = this.configurationSaver.chartsConfiguration.findIndex(function (element) {
        return element.chartId === uCssId
    });
    if (index === -1) {
        this.configurationSaver.chartsConfiguration.push({chartId: uCssId, annotations: []});
        index = this.configurationSaver.chartsConfiguration.length - 1;
    }
    //clear annotations before reedit
    this.configurationSaver.chartsConfiguration[index].annotations = [];
    for (var j = 0; j < annotations.length; j++) {
        var id = annotations[j].options.id;
        var author = annotations[j].options.author;
        var x = annotations[j].options.labels[0].point.x;
        var y = annotations[j].options.labels[0].point.y;
        var text = annotations[j].options.labels[0].text;
        var width = annotations[j].width !== undefined ? annotations[j].width:annotations[j].options.labels[0].width;
        var height = annotations[j].height !== undefined ? annotations[j].height:annotations[j].options.labels[0].height;
        var annotation = {id: id, author: author, x: x, y: y, annotation: text};
        if (width !== undefined) {
            annotation.width = width;
        } else {
            annotation.width = this.options.ANNOTATION_WITH;
        }
        if (height !== undefined) {
            annotation.height = height;
        } else {
            annotation.height = this.options.ANNOTATION_HEIGHT;
        }
        annotation.containerWidth = annotations[j].containerWidth !== undefined ?annotations[j].containerWidth:annotations[j].options.containerWidth;
        annotation.containerHeight = annotations[j].containerHeight !== undefined ?annotations[j].containerHeight:annotations[j].options.containerHeight;
        this.configurationSaver.chartsConfiguration[index].annotations.push(annotation);
    }
    this.saveConfiguration();
};

annotateChart.prototype.initChartsConfiguration = function () {
    var storage = new StorageClient(this.options.annotationScope);
    storage.get(this.CHART_CONFIGURATION_STORAGE_KEY + this.options.storageKey, $.proxy(function (items) {
            if (items.length > 0) {
                this.configurationSaver = JSON.parse(items[0].value);
                //init charts config
                for (var i = 0; i < this.configurationSaver.chartsConfiguration.length; i++) {
                    var chartConfiguration = this.configurationSaver.chartsConfiguration[i];
                    var uCssId = chartConfiguration.chartId;
                    var chart = Highcharts.charts.find(function (chart) {
                        return annotateChart.widgetUtils.getChartUcssIdFromContainerId($(chart.container).attr("id")) == uCssId
                    });
                    if (chart !== undefined) {
                        this.initChartConfiguration(chartConfiguration, chart);
                    }
                }
            }
        }, this),
        function () {
            //error callback
        });
};

annotateChart.prototype.setConfiguration = function () {
    var storage = new StorageClient(this.options.annotationScope);
    storage.get(this.CHART_CONFIGURATION_STORAGE_KEY + this.options.storageKey, $.proxy(function (items) {
            if (items.length > 0) {
                this.configurationSaver = JSON.parse(items[0].value);
            }
        }, this),
        function () {
            //error callback
        });
};

annotateChart.prototype.initChartConfiguration = function (chartConfiguration, chart) {
    for (var i = 0; i < chartConfiguration.annotations.length; i++) {
        var annotationJSON = chartConfiguration.annotations[i];
        var annotation = this.createAnnotation(annotationJSON.x, annotationJSON.y, annotationJSON.annotation,
            annotationJSON.author, annotationJSON.width, annotationJSON.height, annotationJSON.id, annotationJSON.containerWidth, annotationJSON.containerHeight);
        this.addAnnotation(annotation, chart);
    }

    this.onRedrawEvent(chart);
};
