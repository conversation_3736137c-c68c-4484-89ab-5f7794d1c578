@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/styles/variables.less";
@import "../../plmaResources/css/polyfills.less";

.mashup.mashup-style {
  .plmaDataTable {
    width: 100%;

    tr:nth-child(even) {
      background-color: @ctext-weaker;
    }
  }

  .buttons-columnVisibility[data-displayable="false"] {
    display: none;
  }

  div.dt-buttons {
    margin-right: 100px;
  }

  span.refine-link{
    cursor: pointer;
  }

  .datatableFilter {
    border-radius: 3px;
    -webkit-flex: 0 0 auto;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    margin: 3.5px;
    cursor: pointer;
    max-width: 500px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .datatableFilter.selectedFilter {
    background-color: #d5e8f2;
  }
}
