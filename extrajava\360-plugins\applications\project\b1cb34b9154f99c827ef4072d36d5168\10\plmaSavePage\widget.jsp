<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import varWidget="widget"/>

<config:getOption name="label" var="label" defaultValue="Save Page"/>
<config:getOption name="showLabel" var="showLabel"/>
<config:getOption name="paramsListMode" var="paramsListMode"/>
<config:getOptions name="paramsList" var="paramsList"/>

<search:getPageName var="pageName"/>


<widget:widget varUcssId="uCssId" extraCss="save-page" varCssId="cssId">
	<span class="plmaButton fonticon fonticon-floppy" title="${label}"></span>

    <c:if test="${showLabel}">
        <span class="button-label">${label}</span>
    </c:if>

	<div class="popup hidden">
		<div class="label-container value-container">
			<span class="page-label">Page Label</span><input class="label-input" type="text" maxlength="100" value=""/>
		</div>
		<div class="description-container value-container">
			<span class="description-label">Page Description</span><textarea class="description-input" cols="20" row="6" maxlength="300"></textarea>
		</div>
		<div class="icon-container">
			<span class="icon-label">Page Icon</span><div><span class="icon-input fonticon fonticon-doc"></span></div>
		</div>
		<div class="icon-tooltip">
			<input class="icon-tooltip-input" placeholder="Search Icon" />
			<plma:iconList var="iconList" filePath="" appName="${appName}" />
			<div class="fonticon-container">
				<c:forEach var="icon" items="${iconList}">
					<span class="fonticon-elem fonticon ${icon}"></span>
				</c:forEach>
			</div>
		</div>
		<div class="button-container">
			<button class="plmaButton cancel">Cancel</button>
			<button class="plmaButton save">Save</button>
		</div>
	</div>

    <render:renderScript position="READY">
        var options = {};
        options.url = '<c:url value="/savedPages"/>';
		options.baseAjaxReload = '<c:url value="/plma/ajax/menu"/>/<search:getPageName/>/layout';
        options.page = '${pageName}';
		options.paramsListMode = '${paramsListMode}';
		options.paramsList = ${plma:toJSArray(paramsList)};
        new SavePage('${uCssId}',options);
    </render:renderScript>
</widget:widget>