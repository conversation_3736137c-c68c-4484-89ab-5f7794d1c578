var DateRange = function (dateRanges, uCssId, options) {
	this.dateRanges = dateRanges;
	this.uCssId = uCssId;
	var defaultOptions = {};
	this.options = $.extend({}, defaultOptions, options);
	this.widget = $('.' + this.uCssId);
	this.container = $('.' + this.uCssId + ' .preference-date-range-container');
};

DateRange.prototype.init = function () {
	this.initTitle();
	this.initTree();
	this.initButtons();
};

DateRange.prototype.initTitle = function () {
	var titleBlock = $('<div class="preference-title"></div>');
	titleBlock.append($('<div class="main">' + Preferences.getMessage('plma.preference.date.range.title') + '</div>'));
	titleBlock.append($('<div class="description">' + Preferences.getMessage('plma.preference.date.range.description') + '</div>'));

	this.container.append(titleBlock);
};

DateRange.prototype.initTree = function () {
	var blockContainer = $('<div class="preference-block-container"></div>');
	var dateDisplayContainer = $('<div class="preference-block"></div>');
	var datesContainer = $('<div class="preference-elem-block preference-dates-block"></div>');
	for (var i = 0; i < this.dateRanges.length; i++) {
		var dateRange = this.dateRanges[i];
		var dateBlock = $('<div class="elem-block date-block collapsed"></div>');
		var dateDescription = $('<div class="elem-description date-description" data-id="' + _.escape(dateRange.id) + '"></div>');
		dateDescription.append($('<span class="elem-icon date-icon fonticon fonticon-calendar"></span>'));
		dateDescription.append($('<span class="elem-label date-id">' + _.escape(dateRange.id) + '</span>'));
		dateDescription.on('click', $.proxy(function (e) {
			$e = $(e.target);
			//Open details
			this.container.find('.date-block').addClass('collapsed');
			$e.closest('.date-block').removeClass('collapsed');
			this.container.find('.selected').removeClass('selected');
			$e.closest('.date-block').addClass('selected');
			this.displayDetail();
		}, this));
		var trashIcon = $('<span class="trash-icon fonticon fonticon-trash"></span>');
		dateDescription.append(trashIcon);
		trashIcon.on('click', $.proxy(function (e) {
			$e = $(e.target).closest('.date-block');
			this.deleteElem($e);
		}, this));
		dateBlock.append(dateDescription);
		var dateCategories = $('<div class="elem-categories date-categories"></div>');
		dateCategories.append($('<span class="date-label">' + _.escape(dateRange.label) + '</span>'));
		if (parseInt(dateRange.fromNb) > 0) {
			dateCategories.append($('<span class="start-date">' + Preferences.getMessage('plma.preference.date.start') + '</span>'));
			dateCategories.append($('<span class="from-date">' + _.escape(dateRange.fromNb) + ' ' + _.escape(dateRange.fromUnit) + '</span>'));
			if (parseInt(dateRange.fromNb) > 0) {
				dateCategories.append($('<span class="to-date">' + _.escape(dateRange.toNb) + ' ' + _.escape(dateRange.toUnit) + '</span>'));
				dateCategories.append($('<span class="end-date">' + Preferences.getMessage('plma.preference.date.end') + '</span>'));
				dateCategories.append($('<span class="current-date">' + Preferences.getMessage('plma.preference.date.current') + '</span>'));
			} else {
				dateCategories.append($('<span class="current-date">' + Preferences.getMessage('plma.preference.date.current') + '</span>'));
				dateCategories.append($('<span class="to-date">' + Math.abs(parseInt(dateRange.toNb)) + ' ' + _.escape(dateRange.toUnit) + '</span>'));
				dateCategories.append($('<span class="end-date">' + Preferences.getMessage('plma.preference.date.end') + '</span>'));
			}
		} else {
			dateCategories.append($('<span class="current-date">' + Preferences.getMessage('plma.preference.date.current') + '</span>'));
			dateCategories.append($('<span class="start-date">' + Preferences.getMessage('plma.preference.date.start') + '</span>'));
			dateCategories.append($('<span class="from-date">' + Math.abs(parseInt(dateRange.fromNb)) + ' ' + _.escape(dateRange.fromUnit) + '</span>'));
			dateCategories.append($('<span class="to-date">' + Math.abs(parseInt(dateRange.toNb)) + ' ' + _.escape(dateRange.toUnit) + '</span>'));
			dateCategories.append($('<span class="end-date">' + Preferences.getMessage('plma.preference.date.end') + '</span>'));
		}
		dateBlock.append(dateCategories);
		datesContainer.append(dateBlock);
	}
	var plusContainer = $('<div class="elem-block elem-block-plus facet-block collapsed"></div>');
	plusContainer.append($('<div class="elem-description facet-description"><span class="elem-icon facet-icon icon-plus fonticon fonticon-plus"></span></div>'));
	plusContainer.on('click', $.proxy(function () {
		this.addNewElem();
	}, this));
	datesContainer.append(plusContainer);
	dateDisplayContainer.append(datesContainer);

	blockContainer.append(dateDisplayContainer);
	this.container.append(blockContainer);
};

DateRange.prototype.initButtons = function () {
	/* Add Save / cancel buttons */
	var buttonsContainer = $('<div class="button-container"></div>');
	var closeButton = $('<span class="close-button">' + Preferences.getMessage('plma.preference.close') + '</span>');
	var resetButton = $('<span class="reset-button">' + Preferences.getMessage('plma.preference.reset') + '</span>');
	buttonsContainer.append(closeButton);
	buttonsContainer.append(resetButton);
	this.container.append(buttonsContainer);

	resetButton.on('click', $.proxy(function () {
		$.ajax({
			type: 'DELETE',
			url: this.options.url + 'config/user/date/range/reset',
			success: $.proxy(function () {
				$.notify(Preferences.getMessage('plma.preference.save.success'), 'success');
				setTimeout($.proxy(function() {
					this.reload();
				},this),1000);
			}, this)
		});
	}, this));
	
	closeButton.on('click', $.proxy(function () {
		window.location.reload();
	}, this));
};

DateRange.prototype.displayDetail = function () {
	var range = this.findRange();
	if (!this.detail) {
		this.initDetail(range);
	} else {
		this.updateDetail(range);
	}
	this.detail.removeClass('hidden');
};

DateRange.prototype.hideDetail = function () {
	if (this.detail) {
		this.detail.addClass('hidden');
	}
};

DateRange.prototype.initDetail = function (range) {
	if (!range) {
		range = {};
		range.id = 'newRange';
	}
	this.detail = $('<div class="preference-detail preference-date-detail hidden"></div>');
	this.detail.append($('<div class="title-container">' + Preferences.getMessage('plma.preference.detail.date.title') + '</div>'));
	this.detail.append($('<div class="id-container value-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.id') + ': </span><input class="id-input" type="text" value="' + range.id + '"/></div>'));
	this.detail.append($('<div class="label-container value-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.label') + ': </span><input class="label-input" type="text" value="' + range.label + '"/></div>'));
	this.detail.append($('<div class="from-nb-container value-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.fromNb') + ': </span><input class="from-nb-input" type="text" value="' + range.fromNb + '"/></div>'));
	this.detail.append($('<div class="from-unit-container value-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.fromUnit') + ': </span><input class="from-unit-input" type="text" value="' + range.fromUnit + '"/></div>'));
	this.detail.append($('<div class="to-nb-container value-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.toNb') + ': </span><input class="to-nb-input" type="text" value="' + range.toNb + '"/></div>'));
	this.detail.append($('<div class="to-unit-container value-container"><span class="label">' + Preferences.getMessage('plma.preference.detail.toUnit') + ': </span><input class="to-unit-input" type="text" value="' + range.toUnit + '"/></div>'));

	this.container.find('.preference-block-container').append(this.detail);

	/* Listen to modifications */
	var timerId = 0;
	this.detail.find('.id-input').on('keyup', $.proxy(function (e) {
		this.successTemp();
		var inputValue = e.target.value;
		clearTimeout(timerId);
		timerId = setTimeout($.proxy(function () {
			this.editDateRangeTemp();
			this.container.find('.date-block.selected .date-description .elem-label').text(inputValue);
		}, this), 2000);
	}, this));
	var timerLabel = 0;
	this.detail.find('.label-input').on('keyup', $.proxy(function (e) {
		this.successTemp();
		var inputValue = e.target.value;
		clearTimeout(timerLabel);
		timerLabel = setTimeout($.proxy(function () {
			this.editDateRangeTemp();
			this.container.find('.date-block.selected .date-label').text(inputValue);
		}, this), 2000);
	}, this));
	var timerFromNb = 0;
	this.detail.find('.from-nb-input').on('keyup', $.proxy(function (e) {
		this.successTemp();
		var inputValue = e.target.value + ' ' + this.detail.find('.from-unit-input')[0].value;
		clearTimeout(timerFromNb);
		timerFromNb = setTimeout($.proxy(function () {
			this.editDateRangeTemp();
			this.container.find('.date-block.selected .from-date').text(inputValue);
		}, this), 2000);
	}, this));
	var timerFromUnit = 0;
	this.detail.find('.from-unit-input').on('keyup', $.proxy(function (e) {
		this.successTemp();
		var inputValue = this.detail.find('.from-nb-input')[0].value + ' ' + e.target.value;
		clearTimeout(timerFromUnit);
		timerFromUnit = setTimeout($.proxy(function () {
			this.editDateRangeTemp();
			this.container.find('.date-block.selected .from-date').text(inputValue);
		}, this), 2000);
	}, this));
	var timerToNb = 0;
	this.detail.find('.to-nb-input').on('keyup', $.proxy(function (e) {
		this.successTemp();
		var inputValue = e.target.value + ' ' + this.detail.find('.to-unit-input')[0].value;
		clearTimeout(timerToNb);
		timerToNb = setTimeout($.proxy(function () {
			this.editDateRangeTemp();
			this.container.find('.date-block.selected .to-date').text(inputValue);
		}, this), 2000);
	}, this));
	var timerToUnit = 0;
	this.detail.find('.to-unit-input').on('keyup', $.proxy(function (e) {
		this.successTemp();
		var inputValue = this.detail.find('.to-nb-input')[0].value + ' ' + e.target.value;
		clearTimeout(timerToUnit);
		timerToUnit = setTimeout($.proxy(function () {
			this.editDateRangeTemp();
			this.container.find('.date-block.selected .to-date').text(inputValue);
		}, this), 2000);
	}, this));
};

DateRange.prototype.updateDetail = function (range) {
	if (!range) {
		range = {};
		range.id = 'newRange';
	}
	this.detail.find('.id-input')[0].value = range.id;
	this.detail.find('.label-input')[0].value = range.label;
	this.detail.find('.from-nb-input')[0].value = range.fromNb;
	this.detail.find('.from-unit-input')[0].value = range.fromUnit;
	this.detail.find('.to-nb-input')[0].value = range.toNb;
	this.detail.find('.to-unit-input')[0].value = range.toUnit;
};

DateRange.prototype.findRange = function () {
	var range = {};
	if (this.container.find('.date-block.selected').length > 0) {
		range.id = this.container.find('.date-block.selected .date-description .date-id').text();
		range.label = this.container.find('.date-block.selected .date-categories .date-label').text();
		range.fromNb = this.container.find('.date-block.selected .date-categories .from-date').text().split(' ')[0];
		range.fromUnit = this.container.find('.date-block.selected .date-categories .from-date').text().split(' ')[1];
		range.toNb = this.container.find('.date-block.selected .date-categories .to-date').text().split(' ')[0];
		range.toUnit = this.container.find('.date-block.selected .date-categories .to-date').text().split(' ')[1];

		return range;
	}
	return undefined;
};

DateRange.prototype.addNewElem = function () {
	var newRange = $('<div class="elem-block date-block"></div>');
	var newRangeDescription = $('<div class="elem-description date-description"></div>');
	newRangeDescription.append($('<span class="elem-icon date-icon fonticon fonticon-calendar"></span>'));
	newRangeDescription.append($('<span class="elem-label date-id">' + (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) + '</span>'));
	var trashIcon = $('<span class="trash-icon fonticon fonticon-trash"></span>');
	newRangeDescription.append(trashIcon);
	trashIcon.on('click', $.proxy(function (e) {
		$e = $(e.target).closest('.date-block');
		this.deleteElem($e);
	}, this));
	newRange.append(newRangeDescription);
	var newRangeCats = $('<div class="elem-categories date-categories"></div>');
	newRangeCats.append($('<span class="date-label"></span>'));
	newRangeCats.append($('<span class="start-date">' + Preferences.getMessage('plma.preference.date.start') + '</span>'));
	newRangeCats.append($('<span class="from-date"> </span>'));
	newRangeCats.append($('<span class="to-date"> </span>'));
	newRangeCats.append($('<span class="end-date">' + Preferences.getMessage('plma.preference.date.end') + '</span>'));
	newRangeCats.append($('<span class="current-date">' + Preferences.getMessage('plma.preference.date.current') + '</span>'));
	newRange.append(newRangeCats);

	var dateContainer = this.container.find('.preference-dates-block');
	dateContainer.find('.elem-block:last').before(newRange);
	this.hideDetail();
	this.container.find('.elem-block').addClass('collapsed');
	this.container.find('.selected').removeClass('selected');
	newRange.addClass('selected').removeClass('collapsed');
	this.displayDetail();

	this.addDateRangeTemp();

	newRangeDescription.on('click', $.proxy(function (e) {
		$e = $(e.target);
		//Open details
		this.container.find('.date-block').addClass('collapsed');
		$e.closest('.date-block').removeClass('collapsed');
		this.container.find('.selected').removeClass('selected');
		$e.closest('.date-description').addClass('selected');
		this.displayDetail();
	}, this));
};

DateRange.prototype.deleteElem = function (elem) {
	this.hideDetail();
	elem.remove();
	this.removeDateRangeTemp(elem.find('.date-id').text());
};

DateRange.prototype.editDateRangeTemp = function () {
	var oldRangeId = this.container.find('.date-block.selected .date-id').text();
	var newRangeId = this.detail.find('.id-container .id-input')[0].value;
	var fromNb = this.detail.find('.from-nb-container .from-nb-input')[0].value;
	var fromUnit = this.detail.find('.from-unit-container .from-unit-input')[0].value;
	var toNb = this.detail.find('.to-nb-container .to-nb-input')[0].value;
	var toUnit = this.detail.find('.to-unit-container .to-unit-input')[0].value;
	var label = this.detail.find('.label-container .label-input')[0].value;
	$.ajax({
		type: 'POST',
		url: this.options.url + 'config/user/date/range/edit',
		data: {
			confName: this.options.configName,
			oldId: oldRangeId,
			newId: newRangeId,
			fromNb: fromNb,
			fromUnit: fromUnit,
			toNb: toNb,
			toUnit: toUnit,
			label: label
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

DateRange.prototype.addDateRangeTemp = function () {
	var id = this.container.find('.date-block.selected .date-id').text();
	$.ajax({
		type: 'POST',
		url: this.options.url + 'config/user/date/range/add',
		data: {
			confName: this.options.configName,
			id: id
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

DateRange.prototype.removeDateRangeTemp = function (rangeId) {
	$.ajax({
		type: 'DELETE',
		url: this.options.url + 'config/user/date/range/remove',
		data: {
			confName: this.options.configName,
			id: rangeId
		},
		success: $.proxy(function (data) {
			if (data === 500) {
				this.failureTemp();
			} else {
				/* Activate save and cancel button */
				this.successTemp();
			}
		}, this)
	})
};

DateRange.prototype.successTemp = function () {
	this.widget.find('.button-container .save-button').addClass('active');
	this.widget.find('.button-container .cancel-button').addClass('active');
};

DateRange.prototype.failureTemp = function () {
	$.notify(Preferences.getMessage('plma.preference.save.error'), 'error');
};

DateRange.prototype.reload = function () {
	// var client = new PlmaAjaxClient(this.widget);
	// client.addWidget(this.uCssId);
	// client.update();
	location.reload();
};
