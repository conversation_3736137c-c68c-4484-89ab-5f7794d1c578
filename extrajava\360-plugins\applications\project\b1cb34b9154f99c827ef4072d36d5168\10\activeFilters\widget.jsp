<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds"/>

<config:getOption name="title" var="title" defaultValue="" doEval="true"/>
<config:getOption name="displaySavedFilter" var="displaySavedFilter" defaultValue="false"/>
<config:getOption name="showReset" var="showReset" defaultValue="true"/>
<config:getOption name="hideWidget" var="hideWidget" defaultValue="true"/>
<config:getOption name="displayMode" var="displayMode" defaultValue="Compact"/>
<config:getOption var="searchParameter" name="searchParameter" defaultValue=""/>
<config:getOption var="feedsList" name="feedsList" defaultValue=""/>
<config:getOption name="enableSuggest" var="enableSuggest" defaultValue="false" />
<config:getOptionsComposite name="customLinks" var="customLinks" mapIndex="true"/>
<config:getOptionsComposite name="customButtons" var="customButtons" mapIndex="true"/>
<config:getOptionsComposite var="customDateRefine" name="customDateRefine"/>
<config:getOptionsComposite var="customParams" name="customParams" mapIndex="true"/>

<config:getOption name="separateFeeds" var="separateFeeds" defaultValue="false"/>


<plma:getConstantValueFromNameTag var="simpleDateRefine" constantName="SIMPLE_DATE_PARAM" />

<%-- If filters are displayed by feed, let's make a map fo feed names --%>
<c:if test="${separateFeeds == 'true'}">
	<config:getOptionsComposite name="feedDisplayNames" var="feedDisplayNames" mapIndex="true"/>
	<map:new var="feedNames"/>
	<c:forEach items="${feedDisplayNames}" var="feedDisplayName">
		<map:put key="${feedDisplayName.feedId}" value="${feedDisplayName.feedDisplayName}" map="${feedNames}"/>
	</c:forEach>
</c:if>

<%-- The display mode will be used to clal the corresponding jsp template --%>
<c:set var="display" value="${fn:toLowerCase(displayMode)}"/>

<c:set var="IsDateFilters" value="false"/>
<c:forEach items="${customDateRefine}" var="refine">
	<c:if test="${param[refine[1]] != undefined}">
		<c:set var="IsDateFilters" value="true"/>
	</c:if>
</c:forEach>
<c:set var="isCustomFilters" value="false"/>
<c:forEach items="${customParams}" var="customParam">
	<request:getParameterValue var="paramValue" name="${customParam.paramName}"/>
	<c:if test="${not empty paramValue}">
		<c:set var="isCustomFilters" value="true"/>
	</c:if>
</c:forEach>
<c:if test="${param[simpleDateRefine] != undefined}">
	<c:set var="IsDateFilters" value="true"/>
</c:if>

<%-- Rendering all the filters first, and meanwhile counting the number of refined categories --%>
<list:new var="filters"/>
<c:set var="widgetContent">
	<c:choose>
		<%-- If filters are grouped by feed, iterate over the feeds, otherwise render all at once  --%>
		<c:when test="${separateFeeds == 'true'}">
			<search:forEachFeed feeds="${feeds}" var="feed">
				<%-- Let's check if the feed contains refines before rendering it --%>
				<c:set var="nbFilters" value="${fn:length(filters)}"/>
				<c:set var="feedFilters">
					<div class="feedFilters">
						<div class="secondaryTitle feedName">${not empty feedNames[feed.id] ? feedNames[feed.id] : feed.id}</div>
						<div class="filtersWrapper">
							<render:template template="templates/${display}Mode.jsp">
								<render:parameter name="feed" value="${feed}"/>
								<render:parameter name="filters" value="${filters}"/>
							</render:template>
						</div>
					</div>
				</c:set>
				<%-- If the number of filters has increased, render this feed's filters --%>
				<c:if test="${fn:length(filters) > nbFilters}">
					${feedFilters}
				</c:if>
			</search:forEachFeed>
		</c:when>
		<c:otherwise>
			<c:if test="${not empty feeds}">
				<render:template template="templates/${display}Mode.jsp">
					<render:parameter name="feeds" value="${feeds}"/>
					<render:parameter name="filters" value="${filters}"/>
				</render:template>
			</c:if>
		</c:otherwise>
	</c:choose>
	<c:if test="${empty filters && IsDateFilters=='false' && isCustomFilters=='false'}">
		<div class="no-filters"><i18n:message code="widgets.activeFilters.label.noFilter"/></div>
	</c:if>
</c:set>

<%-- Not displaying the widget if the option is selected and no refinements are active --%>
<c:if test="${hideWidget != 'true' || fn:length(filters) > 0 || IsDateFilters == 'true' }">

	<%-- Url to cancel all filters --%>
	<c:if test="${showReset == 'true'}">
		<i18n:message var="cancelAllTooltip" code="widgets.activeFilters.tooltip.cancelAll" text="Remove all filters"/>
		<list:new var="feedIds"/>
		<search:forEachFeed feeds="${feeds}" var="feed">
			<list:add value="${feed.id}" list="${feedIds}"/>
		</search:forEachFeed>
		<c:forEach var="feedName" items="${feedsList}">
			<list:add value="${feedName}" list="${feedIds}"/>
		</c:forEach>
		<url:url keepQueryString="true" var="cancelAllUrl" forceRefineOn="${feedIds}">
			<url:parameter name="${searchParameter}" value=""/>
			<c:forEach items="${customDateRefine}" var="refine">
				<url:parameter name="${refine[1]}" value=""/>
			</c:forEach>
			<c:forEach items="${customParams}" var="customParam">
				<url:parameter name="${customParam.paramName}" value=""/>
			</c:forEach>
			<url:parameter name="${simpleDateRefine}" value=""/>
			<c:forEach var="feedId" items="${feedIds}">
				<url:parameter name="${feedId}.r" value=""/>
			</c:forEach>
		</url:url>
	</c:if>


	<c:choose>
		<c:when test="${displayMode == 'Compact' }">
			<widget:widget extraCss="activeFilters compactMode" varUcssId="uCssId">

				<render:template template="templates/customButtons.jsp">
					<render:parameter name="customButtons" value="${customButtons}"/>
					<render:parameter name="uCssId" value="${uCssId}"/>
					<render:parameter name="positioning" value="left"/>
                    <render:parameter name="extraCss" value="align-start"/>
				</render:template>

				<div class="widgetTitle">${title}</div>
				<div class="activeFiltersContainer">
						${widgetContent}
				</div>

				<c:if test="${enableSuggest}">
					<div class="quickFilters">
						<div class="quickFiltersContainer"></div>
						<div class="quickFiltersContainer-droppable"></div>
						<div class="quickFiltersButtons">
							<input class="quickFilters-suggest" name="${uCssId}" placeholder="<i18n:message code='widgets.activeFilters.quickFilters.placeholder' text='Add a filter...' />" />
							<button class="quickFilters-cancel" style="display: none;" title="<i18n:message code='generic.cancel' text='Cancel' />"><i class="fonticon fonticon-wrong"></i></button>
							<button class="quickFilters-apply" style="display: none;" title="<i18n:message code='generic.apply' text='Apply' />"><i class="fonticon fonticon-check"></i></button>
						</div>
					</div>
				</c:if>
				
				<c:if test="${showReset == 'true' && (not empty filters || IsDateFilters == 'true' || isCustomFilters == 'true')}">
					<a href="${cancelAllUrl}" class="fonticon fonticon-trash reset-btn" title="${cancelAllTooltip}"></a>
				</c:if>

				<render:template template="templates/customLinks.jsp">
					<render:parameter name="customLinks" value="${customLinks}"/>
					<render:parameter name="uCssId" value="${uCssId}"/>
				</render:template>
			<render:template template="templates/customButtons.jsp">
					<render:parameter name="customButtons" value="${customButtons}"/>
					<render:parameter name="uCssId" value="${uCssId}"/>
                    <render:parameter name="positioning" value="right"/>
				</render:template>
			</widget:widget>
		</c:when>
		<c:otherwise>
			<widget:widget extraCss="activeFilters" varUcssId="uCssId">
				<c:if test="${not empty title}">
					<widget:header extraCss="sectionTitle">
						${title}
						<c:if test="${showReset == 'true' && (not empty filters || IsDateFilters == 'true' || isCustomFilters == 'true')}">
							<i18n:message var="cancelAllTooltip" code="widgets.activeFilters.tooltip.cancelAll"
										  text="Remove all filters"/>
							<list:new var="feedIds"/>
							<search:forEachFeed feeds="${feeds}" var="feed">
								<list:add value="${feed.id}" list="${feedIds}"/>
							</search:forEachFeed>
							<a href="${cancelAllUrl}" class="fonticon fonticon-trash reset-btn"
							   title="${cancelAllTooltip}"></a>
						</c:if>
					</widget:header>
					<widget:content>
						${widgetContent}
						<c:if test="${not empty customLinks && not empty customLinks[0] && not empty customLinks[0][0]}">
							<div class="secondaryTitle"><i18n:message code="widgets.activeFilters.section.links"
																	  text="links"/></div>
							<render:template template="templates/customLinks.jsp">
								<render:parameter name="customLinks" value="${customLinks}"/>
								<render:parameter name="uCssId" value="${uCssId}"/>
							</render:template>
						</c:if>
						<c:if test="${not empty customButtons && not empty customButtons[0] && not empty customButtons[0][0]}">
							<div class="secondaryTitle"><i18n:message code="widgets.activeFilters.section.links"
																	  text="links"/></div>
							<render:template template="templates/customButtons.jsp">
								<render:parameter name="customButtons" value="${customButtons}"/>
								<render:parameter name="uCssId" value="${uCssId}"/>
                                <render:parameter name="positioning" value="right"/>
							</render:template>
						</c:if>
					</widget:content>
				</c:if>
			</widget:widget>
		</c:otherwise>
	</c:choose>

	<render:renderScript position="READY">
		<request:getParameterValue var="pageId" name="pageId" defaultValue=""/>
		<search:getFeed var="feed" feeds="${feeds}" />
		<c:set var="nameOfFeed" value="${feed.id}" />

		<plma:GetChartboardPageSecurity pageId="${pageId}"
										readRights="readers" writeRights="writers"/>
		<string:eval var="user" string="\${security.username}" isJsEscape="true" feeds="${feeds}"/>
		var options = {};
		options.displaySavedFilter = '${displaySavedFilter}';
		options.pageName = '<url:getPageName />';
		options.pageId = '${pageId}';
		options.dateParam = '${simpleDateRefine}';
		options.feedName = '${nameOfFeed}';
		options.readers = '${readers}';
		options.writers = '${writers}';
		options.user = '${user}';
		new SavedFilters('${uCssId}', options);

		<c:if test="${enableSuggest}">

			var currentFilters = {
				refined: [],
				excluded: []
			};
			<search:forEachFacet var="facet" feeds="${feeds}">
				<search:forEachCategory var="category" root="${facet}" showState="REFINED" iterationMode="ALL">
					<string:escape var="catId" value="${category.id}"/>
					currentFilters.refined.push('${catId}');
				</search:forEachCategory>
				<search:forEachCategory var="category" root="${facet}" showState="EXCLUDED" iterationMode="ALL">
					<string:escape var="catId" value="${category.id}"/>
					currentFilters.excluded.push('${catId}');
				</search:forEachCategory>
			</search:forEachFacet>
			var facetsList = {};
			<search:forEachFacet var="facet" varStatus="status" feeds="${feeds}">
				<search:getFacetLabel var="facetLabel" facet="${facet}" />
				facetsList['${facet.id}'] = '<string:escape value="${facetLabel}" escapeType="JAVASCRIPT" />';
			</search:forEachFacet>

			$('.quickFilters').quickFilters({
				suggestUrl: '<c:url value="/plma/suggest/widget/${feedName}" />',
				pageId: '${pageId}',
				pageName: '${feedName}',
				wuid: '${widget.wuid}',
				feedName: '${nameOfFeed}',
				facetsList: facetsList,
				droppableItemsSelector: '<config:getOption name="draggableFiltersSelector" />',
				currentFilters: currentFilters
			});
		</c:if>
	</render:renderScript>

</c:if>

