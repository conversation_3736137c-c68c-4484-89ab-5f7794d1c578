<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import varWidget="widget" varFeeds="feeds" />

<config:getOption name="iconCss" var="iconCss"/>
<config:getOption name="label" var="label"/>
<config:getOption name="showLabel" var="showLabel"/>
<config:getOption name="onClick" var="onClick" defaultValue="function(){}"/>
<config:getOption name="onInit" var="onInit" defaultValue="function(){}"/>

<config:getOption name="renderOnce" var="renderOnce" />

<widget:widget varUcssId="uCssId" extraCss="plmaButton" varCssId="cssId" disableStyles="true">
	<c:if test="${not empty iconCss}">
		<i class="${iconCss}" title="${label}"></i>
	</c:if>
	<c:if test="${showLabel}">
		<span class="button-label">${label}</span>
	</c:if>
	
</widget:widget>

<c:choose>
	<c:when test="${renderOnce}">
		<render:renderOnce id="${uCssId}">
			<render:renderScript position="READY">
				(function() {
					var widget = $('#${cssId}');
					(${onInit}).call(widget);

					$('#${cssId}').on('click', function(e) {
						(${onClick}).call(widget, e);
					});
				})();
			</render:renderScript>
		</render:renderOnce>
	</c:when>
	<c:otherwise>
		<render:renderScript position="READY">
			(function() {
				var widget = $('#${cssId}');
				(${onInit}).call(widget);

				$('#${cssId}').on('click', function(e) {
					(${onClick}).call(widget, e);
				});
			})();
		</render:renderScript>
	</c:otherwise>
</c:choose>