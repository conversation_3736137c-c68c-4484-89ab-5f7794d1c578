<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import parameters="accessFeeds,feed,entry,widget,uCssId"/>
<%--columnsConfig object is of type com.exalead.apps.plma_tools.commons.config.ResultList --%>
<render:import parameters="hitUrl,hitTitle,hitTitleTooltip,columnsConfig" ignore="true"/>

<%-- retrieve the widget options --%>
<config:getOption var="hitContent" name="hitContent" defaultValue="${entry.content}" entry="${entry}" feed="${feed}"/>
<config:getOption name="hitContentDisplay" var="hitContentDisplay" defaultValue="false"/>
<request:getCookieValue name="description_result_list${uCssId}" var="displayDescription" defaultValue="${hitContentDisplay}"/>
<config:getOption var="maxContentLength" name="maxContentLength" defaultValue="0"/>
<config:getOption var="showThumbnail" name="showThumbnail" defaultValue="false"/>
<config:getOption var="typeFacetId" name="typeFacet"/>
<config:getOption var="typeIconSize" name="typeIconSize"/>
<config:getOption var="typeFacetIcon" name="typeFacetIcon" defaultValue="fonticon-legend"/>
<config:getOption var="renderCategoryLinks" name="renderCategoryLinks" defaultValue="false"/>
<config:getOption var="underlineCategoryLinks" name="underlineCategoryLinks" defaultValue="false"/>
<config:getOption var="ignoreCategoryColor" name="ignoreCategoryColor" defaultValue="false"/>
<config:getOption var="pinRow" name="pinRow" defaultValue="false"/>
<c:if test="${renderCategoryLinks == false}">
	<c:set var="underlineCategoryLinks" value="false"/>
</c:if>
<search:getFacet var="typeFacet" facetId="${typeFacetId}" entry="${entry}"/>
<c:set var="hitUrlTarget" value="${hitUrlTarget == 'New Page' ? '_blank' : ''}"/>

<%-- class="${wh:cleanEntryId(entry.id)}" is used by GMap widgets to append markers --%>
<c:set var="entryIdCssClass" value="${search:cleanEntryId(entry)}"/>

<%--
'entryHitId' must be url value, as used by selection code.
'entryUri' will be same as value of 'hitParamValue' if not empty else equals to 'entryHitId'
--%>
<search:getEntryInfo var="rawEntryUri" name="url" entry="${entry}"/>
<string:escape var="entryHitId" value="${rawEntryUri}" escapeType="HTML"/>
<config:getOption name="hitParamValue" var="hitParamValue" defaultValue=""/>
<config:getOption name="hitParamName" var="hitParamName" defaultValue="hit"/>
<config:getOption var="enableMetaClass" name="enableMetaClass" defaultValue="false"/>
<config:getOption var="enableMetai18n" name="enableMetai18n" defaultValue="false"/>
<config:getOption var="buttonsIconSize" name="buttonsIconSize"/>

<c:set var="entryID" value="${entryHitId}"/>
<c:if test="${hitParamValue != null && hitParamValue != ''}">
    <search:getMetaValue var="metaValue" entry="${entry}" metaName="${hitParamValue}"/>
    <c:if test="${metaValue != null && metaValue != ''}">
        <c:set var="entryID" value="${metaValue}"/>
    </c:if>
</c:if>

<li class="hit hit-list ${entryIdCssClass}"
	data-hit-id="${entryID}"
	data-uri="${entryHitId}"
	data-parameter="${hitParamName}"
	data-id-css-class="${entryIdCssClass}">
	<i18n:message var="titleLabel" code="hitcustom.column.label"/>
	<c:if test="${!isGridTable}">
		<div class="hit-container hit-default">
			<c:if test="${not empty hitTitle}">
				<div class="hitHeader widgetHeaderWithToolbar ${empty typeFacetId ? 'no-icon' : ''}" style="${plma:format('--type-icon-size:%spx;', typeIconSize, '')}">
					<div class="hitTitleContainer">
						<c:choose>
							<c:when test="${showThumbnail == 'true'}">
								<div class="hitThumbnail">
									<render:template template="thumbnail.jsp" widget="thumbnail">
										<render:parameter name="feed" value="${feed}"/>
										<render:parameter name="entry" value="${entry}"/>
										<render:parameter name="widget" value="${widget}"/>
										<render:parameter name="hitUrl" value="${hitUrl}"/>
									</render:template>
								</div>
							</c:when>
							<c:when test="${not empty typeFacetId}">
								<search:forEachCategory var="typeCategory" root="${typeFacet}">
									<c:set var="categoryIcon" value=""/>
									<plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
									<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
									<plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
									<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
									<div class="icon-container">
										<div class="hitIcon" style="background-color:${plma:toRGB(categoryColor, '.3')};">
											<i class="icon fonticon ${not empty categoryIcon? categoryIcon : typeFacetIcon}"
											   style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"></i>
										</div>
									</div>
								</search:forEachCategory>
							</c:when>
						</c:choose>

						<div class="hitTitle" title="${hitTitleTooltip}">
							<render:link href="${hitUrl}" target="${hitUrlTarget}">
								<c:if test="${showThumbnail == 'true' && not empty typeFacetId}">
									<search:forEachCategory var="typeCategory" root="${typeFacet}">
										<plma:getIconName var="categoryIcon" category="${typeCategory}" entry="${entry}"/>
										<string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
										<plma:getCategoryColor var="categoryColor" category="${typeCategory}"/>
										<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
										<i class="icon fonticon ${not empty categoryIcon? categoryIcon : typeFacetIcon}"
										   style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"></i>
									</search:forEachCategory>
								</c:if>
								${hitTitle}
							</render:link>
						</div>
					</div>
					<%-- Show toolbar  --%>
					<div class="hitActionsContainer" style="${plma:format('--icons-size:%spx;', buttonsIconSize, '')}">
						<c:if test="${plma:hasSubWidget(widget, 'plmaButtonsContainer,plmaButton')}">
							<plma:forEachSubWidget includes="plmaButtonsContainer,plmaButton" widgetContainer="${widget}" feed="${feed}" entry="${entry}">
								<render:widget/>
							</plma:forEachSubWidget>
						</c:if>
						<c:if test="${plma:getBooleanParam(widget, 'enableCompare', false)}">
							<c:set var="compareIdMetaName" value="${plma:getStringParam(widget, 'compareIdMetaName', '')}"/>
							<i18n:message  var="compareLabelI18N" code="widgets.plma.resultlist.tiles.compare.add" widget="${widget}"/>
							<render:template template="templates/compareHit.jsp" widget="compareHit">
                                <render:parameter name="compareCollection"
                                                    value="${plma:getStringParam(widget, 'compareCollection', 'compare' )}"/>
                                <render:parameter name="hitID" value="${plma:getEntryID(entry, compareIdMetaName)}"/>
                                <render:parameter name="compareLabel" value="${compareLabelI18N}"/>
                                <render:parameter name="showCompareLabel" value="${plma:getBooleanParam(widget, 'showCompareLabel', 'true' )}"/>
                            </render:template>
						</c:if>
						<c:if test="${plma:getBooleanParam(widget, 'enableFavorite', false)}">
							<c:set var="favoriteIdMetaName" value="${plma:getStringParam(widget, 'favoriteIdMetaName', '')}"/>
							<i18n:message  var="favoriteLabelI18N" code="widgets.plma.resultlist.tiles.favorite.add" widget="${widget}"/>
                            <render:template template="templates/preferredHit.jsp" widget="preferredHit">
                                <render:parameter name="feeds" value="${accessFeeds}"/>
                                <render:parameter name="entry" value="${entry}"/>
                                <render:parameter name="label" value="${favoriteLabelI18N}"/>
                                <render:parameter name="showLabel" value="false"/>
                                <render:parameter name="collection" value="${plma:getStringParam(widget, 'favoriteCollection', 'favoriteHits' )}"/>
                                <render:parameter name="counterSelector" value=".preferred-hits"/>
                                <render:parameter name="hitID" value="${plma:getEntryID(entry, favoriteIdMetaName)}"/>
                                <render:parameter name="buttonPrefix" value="favorite"/>
                            </render:template>
                        </c:if>
						<c:if test="${plma:getBooleanParam(widget, 'enableSelect', false)}">
							<c:set var="selectIdMetaName" value="${plma:getStringParam(widget, 'selectIdMetaName', '')}"/>
							<render:template template="templates/collectionButton.jsp" widget="plmaResultListCommons">
								<render:parameter name="collection" value="${plma:getStringParam(widget, 'selectCollection', 'hits-selection' )}"/>
								<render:parameter name="hitId" value="${plma:getEntryID(entry, selectIdMetaName)}"/>
								<render:parameter name="buttonPrefix" value="checkbox"/>
								<render:parameter name="actionName" value="select"/>
							</render:template>
						</c:if>
					</div>
				</div>
			</c:if>

			<div class="hitMainContainer">
				<div class="metaContainer">
					<div class="hitMetas">
						<c:forEach var="colGroup" items="${columnsConfig.columns}">
							<c:set var="showColGroup" value="true"/>
							<c:if test="${not empty colGroup.displayCondition}">
								<string:eval var="showColGroup" string="${colGroup.displayCondition}" feeds="${accessFeeds}" feed="${feed}"/>
							</c:if>

							<c:if test="${showColGroup == 'true'}">
								<c:forEach var="colConfig" items="${colGroup.column}">
								<string:eval var="showColumn" string="${not empty colConfig.displayCondition ? colConfig.displayCondition.expr : 'true'}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
									<c:if test="${!colConfig.isFacet && colConfig.isShown != 'false' && colConfig.isShown != 'titleConfig'}">
										<search:getMeta var="meta" metaName="${colConfig.meta}" entry="${entry}"/>
										<search:getMetaLabel var="metaLabel" meta="${meta}"/>
										<c:set var="metaValue" value="${plma:getFormattedValues(meta, true, colConfig, ', ')}"/>
										<c:set var="rawMetaValue" value="${plma:getRawValues(meta, true, ', ')}"/>
										<c:if test="${metaValue != ''}">
											<c:choose>
												<c:when test="${not empty plma:getJSPTemplatePath(colConfig.displayTemplate)}">
													<render:template widget="${plma:geTemplateWidget(colConfig.displayTemplate)}" template="${plma:getJSPTemplatePath(colConfig.displayTemplate)}">
														<render:parameter name="accessFeeds" value="${feeds}" />
														<render:parameter name="feed" value="${feed}" />
														<render:parameter name="entry" value="${entry}" />
														<render:parameter name="group" value="${group}" />
														<render:parameter name="colConfig" value="${colConfig}" />
													</render:template>
												</c:when>
												<c:when test="${not empty colConfig.displayTemplate && empty colConfig.displayValue}">
													<string:eval var="displayValue" string="${colConfig.displayTemplate}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
													<span class="hitMeta" title="${metaLabel}: ${rawMetaValue}">
																<span class="metaName ${columnsConfig.showMetasName ? '' : 'hidden'}">${metaLabel}: </span>
																<span class="metaValue">${displayValue}</span>
															</span>
												</c:when>
												<c:when test="${not empty colConfig.displayValue}">
													<string:eval var="displayValue" string="${colConfig.displayValue}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
													<span class="hitMeta" title="${metaLabel}: ${rawMetaValue}">
																<span class="metaName ${columnsConfig.showMetasName ? '' : 'hidden'}">${metaLabel}: </span>
																<span class="metaValue">${displayValue}</span>
															</span>
												</c:when>
												<c:otherwise>
															<span class="hitMeta" title="${metaLabel}: ${rawMetaValue}">
																<span class="metaName ${columnsConfig.showMetasName ? '' : 'hidden'}">${metaLabel}: </span>
																<span class="metaValue">${metaValue}</span>
															</span>
												</c:otherwise>
											</c:choose>
										</c:if>
									</c:if>
								</c:forEach>
							</c:if>
						</c:forEach>
					</div>
				</div>
			</div>
			<div class="hitContent ${empty hitContent ? 'empty' : ''} ${displayDescription == 'true' ? '' : 'hidden'}">
				<c:choose>
					<c:when test="${maxContentLength > 0}">
						${fn:substring(hitContent, 0, maxContentLength)}${fn:length(hitContent) > maxContentLength ? '...' : ''}
					</c:when>
					<c:otherwise>${hitContent}</c:otherwise>
				</c:choose>
			</div>
			<div class="hitFacets">
				<c:forEach var="colGroup" items="${columnsConfig.columns}">
					<c:set var="showColGroup" value="true"/>
					<c:if test="${not empty colGroup.displayCondition}">
						<string:eval var="showColGroup" string="${colGroup.displayCondition}" feeds="${accessFeeds}" feed="${feed}"/>
					</c:if>
					<c:if test="${showColGroup == 'true'}">
						<c:forEach var="colConfig" items="${colGroup.column}">
						<string:eval var="showColumn" string="${not empty colConfig.displayCondition ? colConfig.displayCondition.expr : 'true'}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
							<c:if test="${colConfig.isFacet && colConfig.isShown != 'false' && colConfig.isShown != 'titleConfig'}">
								<search:getFacet var="facet" facetId="${colConfig.meta}" entry="${entry}"/>
								<plma:getFacetLabel var="facetLabel" feed="${feed}" facetId="${facet.id}"/>
								<plma:getIconName var="facetIcon" facetId="${facet.id}"/>
								<string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML"/>
								<span class="hitFacet">
									<c:if test="${not empty facet}"><span class="facetLabel">${facetLabel}:</span></c:if>
									<search:forEachCategory root="${facet}" var="category" iterationMode="LEAVES">
									    <c:if test="${showColumn == 'true'}">
                                            <plma:getIconName var="categoryIcon" category="${category}" entry="${entry}"/>
                                            <string:escape var="categoryIcon" value="${categoryIcon}" escapeType="HTML"/>
                                            <plma:getCategoryColor var="categoryColor" category="${category}"/>
                                            <string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML"/>
                                            <search:getCategoryLabel var="categoryLabel" category="${category}"/>

                                            <c:if test="${renderCategoryLinks}">
                                                <search:getCategoryUrl var="categoryUrl" category="${category}" baseUrl="" feeds="${accessFeeds}"/>
                                                <search:getCategoryState var="catState" varClassName="catStateCss" category="${category}"/>
                                                <a class="category-link ${fn:toLowerCase(catState)}" href="${categoryUrl}">
                                            </c:if>
                                            <span class="categoryIcon fonticon ${not empty categoryIcon ? categoryIcon : facetIcon}"
                                                  style="${not empty categoryColor ? 'color:' : ''}${categoryColor}"
                                                  title="${facetLabel} : ${plma:escapeHTML(categoryLabel)}"></span>

                                            <c:choose>
                                                <c:when test="${not empty plma:getJSPTemplatePath(colConfig.displayTemplate)}">
                                                    <render:template widget="${plma:geTemplateWidget(colConfig.displayTemplate)}" template="${plma:getJSPTemplatePath(colConfig.displayTemplate)}">
                                                        <render:parameter name="accessFeeds" value="${feeds}" />
                                                        <render:parameter name="feed" value="${feed}" />
                                                        <render:parameter name="entry" value="${entry}" />
                                                        <render:parameter name="group" value="${group}" />
                                                        <render:parameter name="colConfig" value="${colConfig}" />
                                                    </render:template>
                                                </c:when>
                                                <c:when test="${not empty colConfig.displayTemplate && empty colConfig.displayValue}">
                                                    <string:eval var="displayValue" string="${colConfig.displayTemplate}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
                                                    <span class="categoryName" title="${facetLabel}: ${plma:escapeHTML(categoryLabel)}">${displayValue}</span>
                                                </c:when>
                                                <c:when test="${not empty colConfig.displayValue}">
                                                    <string:eval var="displayValue" string="${colConfig.displayValue}" feeds="${accessFeeds}" feed="${feed}" entry="${entry}"/>
                                                    <span class="categoryName" title="${facetLabel}: ${plma:escapeHTML(categoryLabel)}">${displayValue}</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span class="categoryName" title="${facetLabel}: ${plma:escapeHTML(categoryLabel)}">${categoryLabel}</span>
                                                </c:otherwise>
                                            </c:choose>
                                            <c:if test="${renderCategoryLinks}">
                                                </a>
                                            </c:if>
                                        </c:if>
									</search:forEachCategory>
								</span>
							</c:if>
						</c:forEach>
					</c:if>
				</c:forEach>
			</div>
		</div>
	</c:if>

	<div class="subwidgets">
		<plma:forEachSubWidget excludes="headerButtonsContainer,plmaButtonsContainer,plmaButton" widgetContainer="${widget}" feed="${feed}" entry="${entry}">
			<%-- Render actions subwidgets in header --%>
			<render:widget/>
		</plma:forEachSubWidget>
	</div>
</li>
