@import "../../plmaResources/css/styles/variables.less";

.pinnedPages {
	background-color: white;
	.favorite-header {
		margin-bottom: 15px;
		margin-left: 20px;
		font-family: "3DS Light", "Lucida Grande", Helvetica, Arial, Verdana, sans-serif;
		margin-top: 20px;
		.title {
			font-size: 18px;
			color: @clink-active;
		}
		.delete-button {
			cursor: pointer;
			float: right;
			font-size: 18px;
			&:hover {
				color: @clink;
			}
		}
	}
	&.button {
		cursor: pointer;
		background-color: transparent;
	}

	.pages {
		&:not(.button) {
			display: flex;
			margin-left: 20px;
			flex-wrap: wrap;
			justify-content: center;
			min-height: 144px;
			&.hidden {
				display: none;
			}
			a {
				text-decoration: none;
				flex: 0 0 300px;
				margin-bottom: 10px;
				&.hidden {
					display: none;
				}
				.page {
					padding: 10px;
					color: @ctext;
					margin-right: 10px;
					white-space: nowrap;
					border: 1px solid @cblock-border;
					display: flex;
					position: relative;
					.page-icon {
						align-self: center;
						font-size: 20px;
						color: @clink;
						margin-left: 10px;
					}
					.info-container {
						padding-left: 15px;
						line-height: 20px;
						.label {
							font-size: 16px;
						}
						.description {
							color: @ctext-weak;
							max-width: 200px;
							white-space: nowrap;
							text-overflow: ellipsis;
							overflow: hidden;
						}
					}
					.favorite-icon {
						position: absolute;
						top: 5px;
						right: 0;
						font-size: 16px;
						color: #f1b71a;
					}
				}
			}
		}
	}

	.pagination {
		display: flex;
		justify-content: center;
		margin-top: 10px;
		.page-button {
			background-color: @cblock-border;
			height: 10px;
			width: 10px;
			border-radius: 10px;
			margin: 5px;
			display: inline-block;
			cursor: pointer;
			&.selected {
				background-color: @ctext-weak;
			}
		}
	}

	.empty-pages {
		margin: 20px;
		font-size: 15px;
		.fonticon {
			font-size: 25px;
		}
		&.hidden {
			display: none;
		}
	}
}
