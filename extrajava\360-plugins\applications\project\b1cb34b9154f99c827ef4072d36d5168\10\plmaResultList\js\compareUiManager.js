var CompareUiManager = function ($widget, options) {
	this.$widget = $widget;
	
	this.options = $.extend({
		compare: {
			numericTolerance: options.compare?.numericTolerance ?? 0.05
		},
		refHandler: CompareUiManager.RefChangeReloadHandler,
		hitSelector : '.scroll-container li.hit',
		columnSelector : '.td:not(.fixed-column)',
		headerColumnSelector : '.th:not(.fixed-column)',
		radioBtnSelector : '.toolbarContainer .ref-compare-button',
		globalToolbarSelector: '.resultsTitle.gridtable-layout .global-toolbar',
		toggleClasses: 'fonticon-radiobutton-off fonticon-radiobutton-on'
	}, options);

	this.init();
	return this;
};

CompareUiManager.prototype.init = function (basketId) {
	var $header_cols = this.$widget.find(this.options.headerColumnSelector);
	$header_cols.prepend('<i class="icon-similar fonticon" title="No Difference"></i>');
	const urlParams = new URLSearchParams(window.location.search);
    this.options.compareRefId = urlParams.get(this.options.refParam);
	
	var $hits = this.$widget.find(this.options.hitSelector);
	$hits.each(function(hi, hit){
		$(hit).find(this.options.columnSelector).each(function(ci, col){
			$(col).children().each(function(){
				$(this).addClass('base-element');
			});
			$(col).append('<span class="compare-result"></span>');
		});
		if($(hit).data('hit-id') == this.options.compareRefId && hit.parentElement.dataset.pinRef == "true"){
            this.pinCompareRef($(hit))
        }
	}.bind(this));
	
	if($hits.length > 0){
        this.options.refHandler.init({
            $widget: this.$widget,
            $hits: $hits,
            refParam: this.options.refParam,
            radioBtnSelector : this.options.radioBtnSelector,
            handleRadioButton: this.handleRadioButton.bind(this)
        });
	}
}

CompareUiManager.prototype.pinCompareRef = function(hit) {
	var $fixedHeader = $(".fix-header.hitCustomList");
	hit.hide(500).addClass("pinned");
	$fixedHeader.append(hit);
	hit.show(500);
	hit.find('.freezeButtonClass').find('.freezeIcon').toggleClass('fonticon-pin-off fonticon-pin');
}
 
CompareUiManager.prototype.handleRadioButton = function($radioButton, performCompare){
	var $button = $radioButton.children('i');
	if($button.hasClass('fonticon-radiobutton-off')){
		var $lastActiveButton = this.$widget.find(this.options.radioBtnSelector + ' .fonticon-radiobutton-on');
		$lastActiveButton.toggleClass(this.options.toggleClasses);
		$lastActiveButton.parents('li.hit').removeClass('compare-reference');
		
		$button.toggleClass(this.options.toggleClasses);
		var $hitRow = $button.parents('li.hit');
		$hitRow.addClass('compare-reference');
		if(performCompare){
			this.doCompare($hitRow);
			this.initHideSimilarBtn();
		}else{
			this.$widget.showPLMASpinner({overlay: true});
		}
		return true;
	}
	return false;
}

CompareUiManager.getHideSimilarBtnData = function(toggle){
	if($.cookie('compare_hidesimilarcols') == undefined){
		$.cookie('compare_hidesimilarcols', 'false');
	}else if(toggle){
		var val = $.cookie('compare_hidesimilarcols') == 'false';
		$.cookie('compare_hidesimilarcols', val)
	}
	
	let result = {
		similarColsHidden: false,
		icon: 'fonticon-math-approximation-eye-off',
		title: mashupI18N.get('plmaResultList', 'plma.resultlist.compare.hidesimilarcols.hide')
	};
	
	if($.cookie('compare_hidesimilarcols') == 'true'){			
		result.similarColsHidden = true;
		result.icon = 'fonticon-math-approximation-eye';
		result.title = mashupI18N.get('plmaResultList', 'plma.resultlist.compare.hidesimilarcols.show');
	}

	return result;
}

CompareUiManager.prototype.initHideSimilarBtn = function(){
	let btnData = CompareUiManager.getHideSimilarBtnData(); 
	if(btnData.similarColsHidden){
		this.$widget.find(this.options.hitSelector + ' ' + this.options.columnSelector + '.no-diff').addClass('no-diff-hidden');
		this.$widget.find(this.options.headerColumnSelector + '.no-diff').addClass('no-diff-hidden');
	}

	let $toolbarContainer = this.$widget.find(this.options.globalToolbarSelector);
	let $hideSimilarBtn = $toolbarContainer.find('.action-hidesimilar-columns');
	if($hideSimilarBtn.length == 0){
		// Initialize button.
		$hideSimilarBtn = $('<span class="action-hidesimilar-columns fonticon ' + btnData.icon + 
			'" title="' + btnData.title + '"></span>');
		
		$toolbarContainer.find('.insert-after').after( $hideSimilarBtn );
		
		$hideSimilarBtn.on('click', function(){
			let btnData = CompareUiManager.getHideSimilarBtnData(true);
			this.$widget.find(this.options.hitSelector + ' ' + this.options.columnSelector + '.no-diff').toggleClass('no-diff-hidden');
			this.$widget.find(this.options.headerColumnSelector + '.no-diff').toggleClass('no-diff-hidden');
			$hideSimilarBtn.toggleClass('fonticon-math-approximation-eye fonticon-math-approximation-eye-off');
			$hideSimilarBtn.attr('title', btnData.title);
		}.bind(this));
	}
}

CompareUiManager.prototype.doCompare = function($base){
	var $base_cols = $base.find(this.options.columnSelector);
	var $others_hits = this.$widget.find(this.options.hitSelector).not($base);
	var $header_cols = this.$widget.find(this.options.headerColumnSelector);
	$header_cols.find('i.icon-similar').removeClass("fonticon-math-approximation");
	
	$base_cols.each(function(base_col_index, base_col){
		var dataType = $(base_col).attr('data-column-type');
		if(dataType != undefined && dataType.length > 0 ){
			var baseText = $(base_col).find('span.base-element').map(function() {
				return $(this).text();
			  }).get().join(', ');
			var columnName = $header_cols.eq(base_col_index).attr('title');
			$(base_col).find('.compare-result').empty().text(baseText).attr('title', columnName +": "+ baseText);
			var matchedCols = [];
			$others_hits.each(function(other_hit_index, other_hit){
				var $other_col = $(other_hit).find(this.options.columnSelector).eq(base_col_index);
				var otherText = $other_col.find('span.base-element').map(function() {
					return $(this).text();
				  }).get().join(', ');
				var $compareResult = $other_col.find('.compare-result').empty();
				const compareText = this.compareText(baseText, otherText, dataType);
				compareText.includes("compare-diff") || compareText.includes("compare-almost-diff") ? $compareResult.html(compareText) : $compareResult.text(compareText);
				if(otherText != ''){
                    $compareResult.attr('title', columnName +": "+ otherText);
                }
				if(baseText == otherText){
					matchedCols.push($other_col);
				}
			}.bind(this));
			
			if(matchedCols.length == $others_hits.length){
				$header_cols
					.eq(base_col_index).addClass('no-diff')
					.find('i.icon-similar').addClass("fonticon-math-approximation");
				
				$(base_col).addClass('no-diff');
				matchedCols.forEach(function(el){
					el.addClass('no-diff');
				});					
			}
		}
	}.bind(this));
}

CompareUiManager.prototype.compareText = function(baseText, otherText, dataType){
	baseText = baseText.trim();
	otherText = otherText.trim();
	
	if(baseText.length == 0){
		return otherText.length > 0 ? "<span class='compare-diff' >" + otherText + "</span>" : ''; 
	}
	if(otherText.length == 0 ){
		/*return '';*/
		return "<span class='compare-diff empty'>-Empty-</span>"; 
	}
	
	var returnHtml = '';
	switch (dataType.toLocaleLowerCase()){ 
		case 'string':
		case 'boolean':
		    // Check if either baseText or otherText contains a comma
            if (baseText.includes(',') || otherText.includes(',')) {
                const baseValues = new Set(baseText.split(',').map(value => value.trim()));

                // Split otherText into values and create an array with modified values for comparison
				const returnHtmlParts = otherText.split(',').map(value => {
										  const trimmedValue = value.trim();
										  return baseValues.has(trimmedValue) ? trimmedValue : `<span class='compare-diff'>${trimmedValue}</span>`;
										});

				returnHtml = returnHtmlParts.join(', ');
            }
			else {
				let diffText = '';
				otherText.split('').forEach(function(ch, id){
					if(ch != baseText.charAt(id)){
						diffText += ch;
					}else{
						if(diffText.length > 0){
							returnHtml += `<span class='compare-diff' >${diffText}</span>`;
							diffText = '';
						}
						returnHtml += ch;
					}
				});
				if(diffText.length > 0){
					returnHtml += `<span class='compare-diff' >${diffText}</span>`;
				}
			}
			break;
		case 'date':
			if(otherText!=baseText){
				returnHtml += "<span class='compare-diff' >" + otherText + "</span>";
			}			
			break;
        case 'number':
        case 'real':
        case 'integer':
        case 'decimal':
            const baseTextParts = baseText.split(' ');
            const otherTextParts = otherText.split(' ');
            const baseTextUnit = baseTextParts[1] || '';
            const otherTextUnit = otherTextParts[1] || '';
			let baseNum ;
			let otherNum ;
			if(baseText.includes(".") && otherText.includes(".")) {
				baseNum = parseFloat(baseText.replace(/,/g, '')).toFixed(2);
			    otherNum = parseFloat(otherText.replace(/,/g, '')).toFixed(2);
			} else {
				baseNum = isNaN(parseInt(baseText)) ? '' : parseInt(baseText);
                otherNum = isNaN(parseInt(otherText)) ? '' : parseInt(otherText);
			}

			if(baseTextUnit !== '' && otherTextUnit !== '') {
				if(baseTextUnit === otherTextUnit) {
					const tolerance = this.options.compare.numericTolerance;
					const toleranceRatio = tolerance * baseNum;
					const diff = Math.abs(baseNum - otherNum);
					if (diff !== 0) {
						returnHtml += `<span class='${diff < toleranceRatio ? 'compare-almost-diff' : 'compare-diff'}'>${otherTextParts[0]} </span>${otherTextUnit}`;
					}
				} else {
					returnHtml += this.createCompareDiffSpan(otherNum, otherTextUnit);
				}
			}
			else if(baseTextParts[0] !== otherTextParts[0]) {
				returnHtml += this.createCompareDiffSpan(otherNum, otherTextUnit);
			}

            break;
		default:
			throw new Error("Unknown compare data type");
	}
	return returnHtml == '' ? otherText : returnHtml;
}

CompareUiManager.prototype.createCompareDiffSpan = function(otherNum, otherTextUnit) {
    return `<span class='compare-diff'>${otherNum} ${otherTextUnit}</span>`;
}

/**
 * RefChangeReloadHandler : Handles the refChange or init useing page reload by adding
 * refparameter in url. If you want to handle the reference on server side, or want user to
 * be able to bookmark url with the selected reference, then use this Handler.
 */
CompareUiManager.RefChangeReloadHandler = (function(){
	let options = {
	    /*
         * Function to extract the ref-id from the provided hit element.
         */
		getRefIdFromHit: function($hit){
			return $hit.attr('data-hit-id');
		},
		/* When url param is absent,
		 * true: add ref param to url and reload the page.
		 * false: Choose the first hit as ref and do compare(default),
		 */
		urlParamMust: false
	};
	let _intUrlParamName = function($widget, refParam){
		if(refParam == undefined){
		    var ucssId = $widget.find('ul.hits').attr('ucssid');
		    this.URLPARAM_NAME = ucssId.split("_")[0] + '_ref';
        }else{
            this.URLPARAM_NAME = refParam;
        }
	};
	let _getRefId = function(){
		var currUrl = new BuildUrl(window.location.href);
		return currUrl.getParameter(this.URLPARAM_NAME)?
			currUrl.getParameter(this.URLPARAM_NAME)[0] : undefined;
	};
	let _setRefId = function($selectedRefHit, useReplace){
		var refId = options.getRefIdFromHit($selectedRefHit);
		var newUrl = new BuildUrl(window.location.href);
		newUrl.addParameter(this.URLPARAM_NAME,refId,true);
		if(useReplace){
			window.location.replace(newUrl.toString());
		}else{
			window.location.href = newUrl.toString();
		}
	};

	return {
		setup: function(opts){
			options = $.extend(options, opts);
			return this;
		},
		init: function(data){
			if(data.$hits.length == 0){
				return;
			}
			_intUrlParamName(data.$widget, data.refParam);

			var $selectedRefHit = $();
			var refId = _getRefId();
			var foundRefHit = refId == undefined ? true : false;

			data.$hits.each(function(hi, hit){
				if(hi==0){
					$selectedRefHit = $(hit);
				}
				if(refId != undefined){
					var hitRefId = options.getRefIdFromHit($(hit));
					if(hitRefId == refId){
						$selectedRefHit = $(hit);
						foundRefHit = true;
					}
				}else{
					// reload page without history, as the ref is not yet selected.
					if(options.urlParamMust){
						_setRefId($selectedRefHit, true);
					}else{
						foundRefHit = true;
					}
				}

				return !foundRefHit; // return true = continue loop, and return false = break loop
			}.bind(this));

			if(foundRefHit == false){
				// refId exists in url but hit may be removed from the current resultset.
				// Hence reload the page without history.
				_setRefId($selectedRefHit, true);
				return;
			}

			var $selectedRadioBtn = $selectedRefHit.find(data.radioBtnSelector);
			data.handleRadioButton($selectedRadioBtn, true);

			data.$hits
				.find(data.radioBtnSelector)
				.not($selectedRadioBtn)
				.each(function(i, radioButton){
					$(radioButton).on('click', function(){
						if(data.handleRadioButton($(radioButton), false)){
							_setRefId($(radioButton).closest('li.hit')); // if chnaged update url and reload.
						}
					}.bind(this));
				}.bind(this));
		}
	}
})();
