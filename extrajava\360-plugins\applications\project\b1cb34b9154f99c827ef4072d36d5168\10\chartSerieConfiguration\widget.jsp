<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="security" uri="http://www.exalead.com/jspapi/security" %>
<%@ taglib prefix="storage" uri="http://www.exalead.com/jspapi/storage" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>


<widget:widget extraCss="plmaChartSerieConfigurator" varCssId="cssId" varUcssId="uCssId">

	<render:import varWidget="widget"/>

	<url:getPageName var="pageName"/>
	<storage:getUserValues key="chart_state_${pageName}" var="chartsConfigurationJSON"/>

	<widget:header></widget:header>

	<widget:content>

		<div class="title">
			<div class="title-text"></div>
		</div>

		<div class="widgetContent-container">

			<div class="container">
				<div class="series-container">
					<div class="label-container">
						<div class="column-label column-label-series"><i18n:message code="Series"/></div>
						<div class="column-label column-label-representation"><i18n:message code="representation"/></div>
					</div>
					<ul class="series-list">
						<li class="serie hidden">
							<div class="plma-checkbox-container">
								<input class="decorate plma-checkbox display-series" type="checkbox"
									   name="serie-checkbox" id="serie-checkbox" value="serie" checked>
								<label class="pull-left truncate" for="serie-checkbox"></label>
							</div>
							<div class="representation-container">
									<%--<div class="label"><i18n:message code="representation" /></div>--%>
								<select class="representation-select">
									<option value="column"><i18n:message code="representation.column"/></option>
									<option value="area"><i18n:message code="representation.area"/></option>
									<option value="areaspline"><i18n:message code="representation.areaspline"/></option>
									<option value="bar"><i18n:message code="representation.bar"/></option>
									<option value="line"><i18n:message code="representation.line"/></option>
									<option value="spline"><i18n:message code="representation.spline"/></option>
								</select>
							</div>
						</li>
					</ul>
				</div>

				<div class="operator-container">
					<div class="label-container">
						<div class="column-label column-label-average"><i18n:message code="average"/></div>
						<div class="column-label column-label-median"><i18n:message code="median"/></div>
					</div>
					<div class="operator-content">
						<ul class="operator-series-list">
							<li class="serie hidden">
								<div class="plma-checkbox-container">
									<input class="decorate plma-checkbox display-operators average-operator"
										   type="checkbox" name="operator-serie-checkbox-average"
										   id="operator-serie-checkbox-average" value="serie" data-operator="AVERAGE">
									<label class="pull-left truncate" for="operator-serie-checkbox-average"></label>
								</div>
								<div class="plma-checkbox-container">
									<input class="decorate plma-checkbox display-operators median-operator"
										   type="checkbox" name="operator-serie-checkbox-median"
										   id="operator-serie-checkbox-median" value="serie" data-operator="MEDIAN">
									<label class="pull-left truncate" for="operator-serie-checkbox-median"></label>
								</div>
								<div class="checkbox-color-legend"></div>
							</li>
						</ul>
					</div>
				</div>
			</div>

			<div class="option-container">
				<div class="legend-container">
					<div><i18n:message code="displayLegend"/></div>
					<div class="plma-checkbox-container">
						<input class="decorate plma-checkbox display-legend" type="checkbox" name="legend-checkbox"
							   id="legend-checkbox" value="legend">
						<label class="pull-left" for="legend-checkbox"></label>
					</div>
				</div>
				<div class="stacking-container">
					<div class="label"><i18n:message code="stacking"/></div>
					<select class="stacking-select">
						<option value="undefined"><i18n:message code="stacking.disabled"/></option>
						<option value="normal"><i18n:message code="stacking.normal"/></option>
						<option value="percent"><i18n:message code="stacking.percent"/></option>
					</select>
				</div>
			</div>

			<div class="annotation-container">
				<div><i18n:message code="comment"/></div>
				<div class="annotation-zone">
					<textarea name="annotation " id="annotation-text-bloc" cols="30" rows="10"></textarea>
				</div>
			</div>


			<div class="action-container">
				<button class="btn btn-default restore-btn"><i18n:message code="Restore"/></button>
				<button class="btn btn-default apply-btn"><i18n:message code="Save"/></button>
			</div>

		</div>
	</widget:content>

	<config:getOption name="btnSelector" var="btnSelector"/>
	<config:getOption name="btnIcon" var="btnIcon"/>
	<config:getOption name="storageKey" var="storageKey" />
	<config:getOption name="waitForTrigger" var="waitForTrigger"/>
	<config:getOption name="popupButtonSelector" var="popupButtonSelector"/>
	<render:renderScript>
		var options = {};
		options.btnSelector = "${btnSelector}";
		options.btnIcon = "${btnIcon}";
		options.storageKey = "${storageKey}";
		options.waitForTrigger = "${waitForTrigger}";
		options.popupButtonSelector = "${popupButtonSelector}";
		options.uCssId = "${uCssId}";
		new ChartSerieConfiguration("${cssId}", options);
	</render:renderScript>

</widget:widget>