<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>

<render:import parameters="widget,accessFeeds,facet,categoryTemplatePath,facetConfig,uCssId,refinePanelCfg,suggestCfg"/>
<render:import parameters="paginationInfo" ignore="true"/>

<c:if test="${not empty facetConfig}">
    <c:set var="aggr" value="${facetConfig.aggr}"/>
    <c:set var="isDisjunctive" value="${facetConfig.isDisjunctive}"/>
    <c:set var="sortMode" value="${facetConfig.sortMode}"/>
    <c:set var="nbSubFacets" value="${facetConfig.nbSubFacets}"/>
    <c:set var="iterMode" value="${facetConfig.iterMode}"/>
    <c:set var="drillDown" value="${facetConfig.drillDown}"/>
    <c:set var="displayExclude" value="${facetConfig.displayExclude}"/>
    <c:set var="defaultView" value="${facetConfig.defaultView}"/>
</c:if>

<c:if test="${iterMode == 'flat' && drillDown == 'true'}">
    <c:set var="iterMode" value="drilldown"/>
</c:if>

<c:set var="categoriesDisplayed" value="${1}"/>
<c:set var="maxCatDepth" value="0"/>

<request:getParameterValue var="catsearchrefine" name="catsearchrefine" defaultValue=""/>
<c:set var="catSuggests" value="${fn:split(catsearchrefine, ',')}"/>

<plma:getMaxCount var="maxAggregationValue" facet="${facet}"/>
<search:getFacetLabel var="facetLabel" facet="${facet}"/>
<search:forEachCategory
        var="category"
        varLevel="depthLevel"
        varStatus="status"
        root="${facet}"
        sortMode="${sortMode}"
        iteration="${nbSubFacets}"
        iterationMode="${iterMode}"
        drillDown="${drillDown}"
        showEmptyCategories="false">
    <plma:getCategoryLabel var="catLabel" category="${category}" feeds="${accessFeeds}"/>
    <c:set var="catPath" value="${fn:split(category.id, '/')}"/>
    <c:set var="catPathLength" value="${fn:length(catPath)}"/>
    <c:set var="catId" value="${catPath[catPathLength - 1]}"/>
    <c:set var="displayCat" value="${false}"/>
    <c:if test="${depthLevel > maxCatDepth}">
        <c:set var="maxCatDepth" value="${depthLevel}"/>
    </c:if>

    <c:choose>
        <c:when test="${paginationInfo.uiPagination}">
            <c:set var="facetPage" value="${plma:getPage(categoriesDisplayed, paginationInfo.perPage)}"/>
        </c:when>
        <c:otherwise>
            <c:set var="facetPage" value="${paginationInfo.page}"/>
        </c:otherwise>
    </c:choose>

    <c:choose>
        <c:when test="${catsearchrefine == ''}">
            <c:set var="displayCat" value="${true}"/>
        </c:when>
        <c:otherwise>
            <c:set var="displayCat" value="${plma:suggestApplicable(category, catSuggests)}"/>
        </c:otherwise>
    </c:choose>

    <c:if test="${displayCat}">
        <c:set var="facetPageClass" value="facet-page-${facetPage}"/>

        <url:url var="pageUrl" value="" keepQueryString="true"/>
        <search:getCategoryUrl var="categoryUrl" varClassName="className" category="${category}" feeds="${accessFeeds}"
                               forceRefineOn="${refinePanelCfg.forceRefineOnFeeds}"/>
        <plma:correctUrl var="categoryUrl" url="${categoryUrl}" forceRefineOn="${refinePanelCfg.forceRefineOnFeeds}" pageUrl="${pageUrl}"
                         keep="${refinePanelCfg.keepParameters}" skipProfiles="${plma:toList('refinePanel')}" delete2DFacet="true" feeds="${accessFeeds}"
                         keepExtraRefinements="${refinePanelCfg.keepExtraRefinements}"/>

        <tr class="category depthLevel_${depthLevel} ${status.index % 2 == 0 ? 'odd' : 'even'} ${className} ${facetPageClass} ${facetPage>1 && paginationInfo.uiPagination ? 'hidden' : ''} in-search"
            data-categorypath="${category.path}">
            <c:if test="${isDisjunctive == true}">
                <td class="disjunctive">
                    <input type="checkbox" data-url="${categoryUrl}" <c:if test="${category.state == 'REFINED'}">checked="checked"</c:if>/>
                </td>
            </c:if>

            <td class="refineName" style="position: relative;">
                <c:if test="${refinePanelCfg.enableFacetCountBars}">
                    <search:getCategoryValue var="aggregationValue" category="${category}" name="${aggr}"/>
                    <div class="aggregation-bar ${uCssId} position-${fn:toLowerCase(refinePanelCfg.countBarPosition)}"
                            <c:choose>
                                <c:when test="${refinePanelCfg.countBarPosition == 'Behind' || refinePanelCfg.countBarPosition == 'Left'}">
                                    style="width: ${aggregationValue * 100 / maxAggregationValue}%; background-color: ${plma:toRGB(refinePanelCfg.countBarColor, '.5')}"
                                </c:when>
                                <c:when test="${refinePanelCfg.countBarPosition == 'Under'}">
                                    style="width: ${aggregationValue * 100 / maxAggregationValue}%; border-color: ${plma:toRGB(refinePanelCfg.countBarColor, '.5')}"
                                </c:when>
                            </c:choose>
                    ></div>
                </c:if>
                <div class="refinecontainer refinecontainer-${catLabel}">
                    <render:template template="${categoryTemplatePath}" widget="refinePanel">
                        <render:parameter name="categoryUrl" value="${categoryUrl}"/>
                        <render:parameter name="widget" value="${widget}"/>
                        <render:parameter name="category" value="${category}"/>
                        <render:parameter name="feeds" value="${accessFeeds}"/>
                        <render:parameter name="facetLabel" value="${facetLabel}"/>
                        <render:parameter name="catLabel" value="${catLabel}"/>
                        <render:parameter name="depthLevel" value="${depthLevel}"/>
                        <render:parameter name="refinePanelCfg" value="${refinePanelCfg}"/>
                    </render:template>
                </div>
            </td>

            <c:if test="${aggr != ''}">
                <td class="count" style="position: relative;">
                    <c:if test="${refinePanelCfg.enableFacetCountBars && refinePanelCfg.countBarPosition == 'Right'}">
                        <search:getCategoryValue var="aggregationValue" category="${category}" name="${aggr}"/>
                        <div class="aggregation-bar ${uCssId} position-${fn:toLowerCase(refinePanelCfg.countBarPosition)}"
                             style="width: ${aggregationValue * 100 / maxAggregationValue}%; background-color: ${plma:toRGB(refinePanelCfg.countBarColor, '.5')}">
                        </div>
                    </c:if>
                    <div class="countcontainer">
                        <search:getCategoryValueType var="type" category="${category}" name="${aggr}"/>
                        <c:choose>
                            <c:when test="${type == 'STRING'}">
                                <search:getCategoryValue category="${category}" name="${aggr}"/>
                            </c:when>
                            <c:otherwise>
                                <string:formatNumber>
                                    <search:getCategoryValue category="${category}" name="${aggr}"
                                                             defaultValue="0"/>
                                </string:formatNumber>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </td>
            </c:if>

            <c:if test="${displayExclude == 'true'}">
                <td class="exclude">
                    <c:choose>
                        <c:when test="${className == 'displayed'}">
                            <%-- Show exclude link if needed --%>
                            <search:getCategoryUrl var="categoryUrl" baseUrl="${facetPageName}" category="${category}" forceState="EXCLUDED"
                                                   feeds="${accessFeeds}" forceRefineOn="${refinePanelCfg.forceRefineOnFeeds}"/>
                            <plma:correctUrl var="categoryUrl" url="${categoryUrl}" forceRefineOn="${refinePanelCfg.forceRefineOnFeeds}" pageUrl="${pageUrl}"
                                             keep="${refinePanelCfg.keepParameters}" skipProfiles="${plma:toList('refinePanel')}" delete2DFacet="true" feeds="${accessFeeds}"
                                             keepExtraRefinements="${refinePanelCfg.keepExtraRefinements}"/>
                            <a data-label="${catLabel}" data-url="${categoryUrl}" data-categoryid="${category.id}" class="refine-link exclude"
                               title="<i18n:message code="widgets.refinePanel.exclude" />"><span class="fonticon fonticon-block"></span></a>
                        </c:when>
                        <c:when test="${className == 'refined'}">
                            <%-- Show cancel link if needed --%>
                            <a data-url="${categoryUrl}" class="refine-link cancel" title="<i18n:message code="rangeselector.cancelFilter" />"><span
                                    class="fonticon fonticon-cancel"></span></a>
                        </c:when>
                        <c:otherwise>
                            <%-- Show cancel link if needed --%>
                            <a data-url="${categoryUrl}" class="refine-link cancel" title="<i18n:message code="rangeselector.cancelFilter" />"><span
                                    class="fonticon fonticon-cancel"></span></a>
                        </c:otherwise>
                    </c:choose>
                </td>
            </c:if>
        </tr>

        <c:if test="${paginationInfo.uiPagination}">
            <c:set var="categoriesDisplayed" value="${categoriesDisplayed +1}"/>
        </c:if>
    </c:if>
</search:forEachCategory>
