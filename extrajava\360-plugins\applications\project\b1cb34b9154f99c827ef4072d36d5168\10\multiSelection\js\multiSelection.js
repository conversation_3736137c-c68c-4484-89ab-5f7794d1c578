MultiSelection.prototype = new SelectionWidget();
MultiSelection.prototype.constructor = MultiSelection;
function MultiSelection(selector, wuid, triggerEvent, limit, options) {
	this.selector = selector;
	this.wuid = wuid;
	this.triggerEvent = triggerEvent;
	this.limit = limit;
	this.hitList = []; /* Selected hits */
	/**
	 * All hits metas
	 * @example
	 * {
	 * 		hitId1: {
	 * 			meta1: value1,
	 * 			meta2: value2
	 * 		},
	 * 		// and so on
	 * }
	 */
	this.hitMetas = {}; 
	this.options = options;
	this.$hits = this.getHits();
	this.setSelectionConfig(this.options.selectionConfig);
};

MultiSelection.SELECT_ALL_BUTTON_CLASS = 'select-all-button';
MultiSelection.STATE_SELECT = 'select';
MultiSelection.STATE_DESELECT = 'deselect';

MultiSelection.prototype.init = function () {
	if (!this.options.renderAllways) {
		this.addCheckbox();
	}
	this.$parentWidget.on('change', 'input.hit-checkbox', $.proxy(function (e, data) {
		var $selectedHits = this.$hits.has('input.hit-checkbox:checked');
		if ($selectedHits.length < this.limit) {
			this.handleEvent(e, data);
			this.$hits.not($selectedHits).find('input.hit-checkbox').prop('disabled', false);
		} else {
			if ($selectedHits.length === this.limit) {
				this.handleEvent(e, data);
			}
			e.preventDefault();
			this.$hits.not($selectedHits).find('input.hit-checkbox').prop('disabled', true);
		}
	}, this));

	if (this.options.selectAll) {
		this.addSelectAllButton();
	}

	if (this.options.renderAllways) {
		$(".hit-checkbox-container").on('click', $.proxy(function (e) {
			e.stopImmediatePropagation();
		}, this));
	}
};

MultiSelection.prototype.getHits = function() {
	/* Need to refresh this.$widget and this.$parentWidget in case of ajax call */
	this.$widget = $('.' + this.wuid);
	this.$parentWidget = this.$widget.parent().closest('.wuid');
	return this.$parentWidget.find(this.selector);
};

MultiSelection.prototype.addCheckbox = function() {
	/* For each hit, we add a checkbox */
	var $container = $('<label class="hit-checkbox-container"></label>');
	$container.append($('<input type="checkbox" class="hit-checkbox" />'));
	$container.append($('<span class="hit-checkbox-label"></span>'));
	$container.on('click', $.proxy(function (e) {
		e.stopImmediatePropagation();
	}, this));
	
	if(this.options.selectHitContainerSelector){
		this.$hits.not(this.$hits.has('.hit-checkbox')).find(this.options.selectHitContainerSelector).append($container);
	}else{
		this.$hits.not(this.$hits.has('.hit-checkbox')).append($container);
	}
};

MultiSelection.prototype.addSelectAllButton = function() {
	var $buttonContainer = $(this.options.containerSelector);
	/* Add select all button if needed */
	if ($buttonContainer.find('.' + MultiSelection.SELECT_ALL_BUTTON_CLASS).length === 0) {
		var MESSAGE_SELECT_ALL = this.getMessage('widget.multiSelection.selectAll').replace('%d', this.limit);
		var $button = $('<label class="hit-checkbox-container"></label>')
			.addClass(MultiSelection.SELECT_ALL_BUTTON_CLASS)
			.prop('title', MESSAGE_SELECT_ALL)
			.data('state', MultiSelection.STATE_SELECT)
			.append($('<input type="checkbox" class="hit-checkbox" />'))
			.append($('<span class="hit-checkbox-label"></span>'));
		$buttonContainer.append($button);

		$button.click($.proxy(function (e) {
			var currentState = $button.data('state');
			e.preventDefault();

			this.deselectAll();
			if (currentState === MultiSelection.STATE_SELECT) {
				/* Select the first this.limit hits */
				this.getHits().find('input.hit-checkbox').slice(0, this.limit)

						.prop('checked', true)
						.trigger('change', [{programmatically: true}]); /* change from the select all button */
			}

			/* Update button */
            this._updateSelectAllButton(currentState, $button);

			/* Trigger event to update UI */
			if (this.triggerEvent) {
				this.triggerHitSelection(this.$hits.first());
			}

			this.publish();
		}, this));
	}
};

MultiSelection.prototype._updateSelectAllButton = function(currentState, $button) {
    if(!$button){
        var $buttonContainer = $(this.options.containerSelector);
        $button = $buttonContainer.find('.' + MultiSelection.SELECT_ALL_BUTTON_CLASS);
    }
	var MESSAGE_SELECT_ALL = this.getMessage('widget.multiSelection.selectAll').replace('%d', this.limit);
	var MESSAGE_DESELECT_ALL = this.getMessage('widget.multiSelection.deselectAll');
    $button
        .prop('title',
            currentState === MultiSelection.STATE_SELECT
                ? MESSAGE_DESELECT_ALL
                : MESSAGE_SELECT_ALL
        )
        .data('state', currentState === MultiSelection.STATE_SELECT ? MultiSelection.STATE_DESELECT : MultiSelection.STATE_SELECT);
    $button.find('input').prop('checked', currentState === MultiSelection.STATE_SELECT);
};

MultiSelection.prototype.deselectAll = function() {
	this.hitList = [];
	this.$hits
		.removeClass('checked')
		.find('input.hit-checkbox')
			.prop('checked', false)
			.prop('disabled', false);
};

MultiSelection.prototype.handleEvent = function (e, data) {
	var $e = $(e.currentTarget).closest(this.$hits);
	var hitId = $e.data('hitId'); /* Universal hit identifier */
	var hitUri = $e.data('uri'); /* Only used for this.triggerHitSelection */
	var hit = $.extend({}, {id: hitId, uri: hitUri}, this.hitMetas[hitId]);

	if ($e.hasClass('checked')) {
		$e.removeClass('checked');
		this.hitList.splice(this.hitList.findIndex(function(hit) {
			return hit.id === hitId;
		}), 1);
	} else {
		$e.addClass('checked');
		if (hitId) {
			this.hitList.push(hit);
		}
	}

	/* Publish here only if the hit was manually clicked */
	if (!(data && data.programmatically)) {
		this.publish();
	}

	/* Trigger 'plma:hitSelection' here only if the hit was manually clicked */
	if (this.triggerEvent && !(data && data.programmatically)) {
		this.triggerHitSelection($e);
	}

	if(this.hitList.length == 0){
		this._updateSelectAllButton(MultiSelection.STATE_DESELECT);
	}
};

MultiSelection.prototype.publish = function () {
	this.setSelectedHits(this.hitList.map(function (hit) {
		return hit.id;
	}));
};

MultiSelection.prototype.triggerHitSelection = function($target) {
	$target.trigger('plma:hitSelection', [
		this.hitList.map(function(hit) {
			return hit.uri;
		})
	]);
};

MultiSelection.prototype.update = function() {
	/* Need to add select all button in case of ajax call */
	if (this.options.selectAll) {
		this.addSelectAllButton();
	}
	this.$hits = this.getHits();

	if (this.options.renderAllways) {
		$(".hit-checkbox-container").on('click', $.proxy(function (e) {
			e.stopImmediatePropagation();
		}, this));
	} else {
		this.addCheckbox();
	}
	var $selectedHits = this.$hits.filter('.checked');
	if ($selectedHits.length === this.limit) {
		this.$hits.not($selectedHits).find('input.hit-checkbox').prop('disabled', true);
	}
};

/**
 * Adds hit metas
 */
MultiSelection.prototype.registerHit = function(id, metas) {
	this.hitMetas[id] = metas;
};

MultiSelection.prototype.getMessage = function (code) {
	return mashupI18N.get('multiSelection', code);
};

MultiSelection.prototype.onHitsSelect = function (hitList) {
	if (hitList) {
		this.deselectAll();
		this.$hits
			.filter(function () {
				return hitList.indexOf($(this).data('hitId')) !== -1;
			})
			.slice(0, this.limit)
			.find('input.hit-checkbox')
				.prop('checked', true)
				.trigger('change', [{programmatically: true}]);

		/* Trigger event to update UI */
		// Need to close the Selection Panel when the Parts are deselected from the
		// 3DNavigate widget. So Always trigger the event.
		if (this.triggerEvent) {
			this.triggerHitSelection(this.$hits.first());
		}
		if(hitList.length == 0){
		    this._updateSelectAllButton(MultiSelection.STATE_DESELECT);
		}
	}
};