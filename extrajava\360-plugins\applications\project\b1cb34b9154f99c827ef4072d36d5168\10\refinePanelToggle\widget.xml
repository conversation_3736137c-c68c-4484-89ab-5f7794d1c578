<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Refine panel toggle button" group="PLM Analytics/Facets" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>Refine panel toggle button, can be used in title widget for example to display/hide refine panel</Description>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Includes>
		<Include type="css" path="css/style.less" />
		<Include type="js" path="js/refinesToggle.js"/>
 	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO" />
	<SupportI18N supported="true">
		<JsKeys>
		</JsKeys> 
	</SupportI18N>

</Widget>
