var WidgetHeaderButton = function(uCssId, options){
	this.defaults = {
			buttonHtml: "",
			onInit: $.noop,
			onClick: $.noop
	};
	this.options = $.extend(this.defaults, options);
	this.decoratorWidget = $("." + uCssId);
	if (this.decoratorWidget.length > 0){
		this.init();	
	}else{
		throw new Error("WidgetHeaderButton : no widget found with uCssId : " + uCssId);
	}
	
};

WidgetHeaderButton.prototype.init = function(){
	this.button = $(this.options.buttonHtml);
	this.widget = this.decoratorWidget.children(".searchWidget");
	this.data = {};
	
	this.widget.children(".widgetHeader").append(this.button);
	
	if ($.isFunction(this.options.onInit)){
		this.options.onInit.call(this, this.button, this.widget, this.data);
	}
	if ($.isFunction(this.options.onClick)){
		this.button.on("click", $.proxy(function(){
			this.options.onClick.call(this, this.button, this.widget, this.data);
		}, this))
	}
};