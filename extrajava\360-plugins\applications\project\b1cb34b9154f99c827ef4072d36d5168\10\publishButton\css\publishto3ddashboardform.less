.plmalightbox .plmalightbox-box.export-to-3dspace{
	.preview-header{
		.context-link-container{
			display: flex;
			padding: 5px;
			.link-label{
				margin-right: 10px;
				display: flex;
				.label{
					margin: auto;
					margin-right: 10px;
				}
				.label-input{
					font-size: 14px;
					width: 200px;
				}
			}
			.link.copy-clipboard{
				margin: auto;
				.fonticon-copy{
					font-size: 24px;
					line-height: 20px;
					margin-top: -8px;
					padding: 5px;
					&:hover{
						color: #005686;
						background-color: #d5e8f2;
						border-radius: 5px;
					}
				}
			}
		}
		.fullscreen-preview{
			margin: auto;
			cursor: pointer;
			padding-left: 10px;
			border-left: 1px solid;
			line-height: 25px;
			&:hover{
				color: #78befa;
			}
		}
	}
}



.plmalightbox .plmalightbox-box.export-to-3dspace .swym-publish-form-container{
	.noswym{
		width: 100%;
		vertical-align: middle;
		line-height: 40px;
		font-size: 20px;
	}
	
	.publish-form .publish-form-body .publish-btn{
		float:right;
	}

	.publish-form .publish-form-body {
		text-align: left;
	}
	.publish-form :not(table){
		box-sizing: border-box;
	}
	.publish-form .platform-select-cnt,.publish-form .types-button-group-cnt,.publish-form .community-select-cnt,.publish-form .title-input-cnt,.publish-form .description-input-cnt,.publish-form .dm-select-cnt {
		margin-bottom: 10px;
	}

	.publish-form .dm-select-cnt .dm-combo {
		display: none
	}

	.publish-form .description-input-cnt.preview .scroller-root {
		min-height: 150px;
		max-height: 150px
	}

	.publish-form .description-input-cnt.preview .scroller-root .content-body {
		padding: 0 20px 0 10px;
		font-size: 14px
	}

	.publish-form .note-cnt {
		font-style: italic;
		margin-bottom: 10px;
		margin-top: 10px;
		font-size: 11px
	}

	.publish-form .alert-message {
		margin-top: 20px;
		margin-bottom: 20px;
		display: none;
		position: relative;
		padding:5px;
	}

	.publish-form .btn-default.active {
		color: white;
		background-color: #368ec4;
		border-color: #77797c
	}

	.publish-form .btn-grp>.btn {
		margin-right: 0
	}

	.publish-form textarea {
		max-width: 100%;
		min-width: 100%;
		min-height: 35px
	}

	.publish-form-view-container .loading-mask {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(255,255,255,0.8);
		z-index: 100
	}

	.publish-form-view-container .loading-mask .msg-wrap {
		width: 100%;
		height: 100%;
		text-align: center;
		display: table
	}

	.publish-form-view-container .loading-mask .msg-wrap .msg-ctn {
		display: table-cell;
		margin: auto;
		vertical-align: middle
	}

	.publish-form .btn-grp>.btn {
		margin-right: 0
	}

	.publish-form .footer-cnt {
		height: 68px;
		background-color: #f1f1f1;
		padding: 14px;
		border-top: 1px solid #e2e4e3;
		box-shadow: inset 0 1px #f9f9f9
	}

	.publish-form .footer-cnt .button-cnt {
		margin-left: 10px;
		float: right
	}

	.thread-selector-cnt {
		display: flex;
		justify-content: space-around
	}

	.thread-selector-cnt .toggle {
		text-transform: uppercase
	}

	.thread-selector-cnt .toggle.checked {
		color: #368ec4
	}

	.thread-selector-cnt .toggle .control-label {
		font-weight: bold
	}

	.thread-selector-cnt .toggle+.toggle {
		margin-top: 10px
	}

	.select-dropdown {
		display: none;
		position: absolute;
		z-index: 30;
		margin-top: 4px;
		width: 100%;
		background: #FFF;
		-webkit-box-shadow: 0 1px 2px #b4b6ba;
		box-shadow: 0 1px 2px #b4b6ba;
		background-clip: padding-box
	}

	.select-dropdown-scroll {
		height: 100%
	}

	.dropdown-visible .select-dropdown {
		display: block;
	}

	.dropdown-visible .select-choices {
		border-radius: 4px
	}

	.dropup .select-dropdown {
		bottom: 100%;
		margin-bottom: 4px;
		margin-top: 0;
		-webkit-box-shadow: 0 1px 2px #b4b6ba;
		box-shadow: 0 1px 2px #b4b6ba
	}


	select,.select,.select .close,.result-option-selected.result-option-disabled:hover,.result-option-disabled .result-option-selected:hover {
		cursor: pointer
	}

	.select {
		position: relative
	}

	.select ul {
		list-style: none;
		margin: 0
	}

	.select.select-native:before {
		content: none
	}

	.select-placeholder select,.select-placeholder optgroup,.select-placeholder option {
		color: initial
	}

	.select-not-chosen .select-choices,.select-not-chosen select,.select option[disabled],.select optgroup[disabled],.select optgroup[disabled] option,.result-option-disabled,.result-option-disabled:hover {
		color: #b4b6ba
	}

	.select.select-native.select-focus>.form-control:hover:not([disabled]):not([readonly]) {
		border-color: #368ec4
	}

	.select-hidden {
		position: absolute;
		opacity: 0;
		filter: alpha(opacity=0);
		z-index: -1
	}

	.select-dropdown {
		display: none;
		position: absolute;
		z-index: 30;
		margin-top: 4px;
		width: 100%;
		background: #FFF;
		-webkit-box-shadow: 0 1px 2px #b4b6ba;
		box-shadow: 0 1px 2px #b4b6ba;
		background-clip: padding-box
	}

	.select-dropdown-scroll {
		height: 100%
	}

	.select .result-group-label,.select .result-option-disabled,.select-multiple .select-choice {
		cursor: default
	}

	.select-results {
		padding: 0;
		margin: 6px 6px 6px
	}

	.result-option,.result-group-label {
		font-size: 14px;
		list-style: none;
		padding: 8px 14px;
		border-bottom: 1px solid #d1d4d4;
		height: 38px;
		line-height: 1.42857
	}

	.result-option {
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap
	}

	.select-placeholder .select-results>.result-option:first-child {
		background-color: #f1f1f1
	}

	.select-results>.result-option-selected,.select-placeholder .select-results>.result-option-selected:first-child {
		background-color: #368ec4;
		color: white
	}

	.result-option:hover,.select-placeholder .select-results>.result-option:first-child:hover {
		background-color: #368ec4;
		color: white
	}

	.select-choices {
		 text-rendering: optimizeSpeed;
			-webkit-text-size-adjust: none;
			-webkit-tap-highlight-color: transparent;
			text-align: left;
			font: inherit;
			cursor: pointer;
			display: block;
			padding: 6px 12px;
			width: 100%;
			height: 24px;
			border: 1px solid #b4b6ba;
			border-radius: 4px;
			background-image: none;
			font-size: 14px;
			transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
			overflow: hidden;
			border-color: #b4b6ba;
			background-color: white;
			color: #3d3d3d;
			padding-right: 14px;
			padding-left: 14px;
			list-style: none;
			margin: 0;
	}

	.drop-list {
		width:70%;
		font-size: 1.2em;
		line-height: 100%;
		padding: 10px;
		padding-top: 0px;
		position: relative;
		.bookmarks {
			margin-bottom: 10px;
		}
		.select.select-placeholder {
		margin-bottom: 5px;
		}
		.select-choices.form-control {
			width: inherit;
			height: 12px;
		}
	}

	.plmalightbox .plmalightbox-box.export-to-3dspace #export-container-global .container .exp-context-container .controls .export-button.bookmark_container{
		margin-left: 20px;
		height: 40px;
	}

	.select:not(.select-multiple) .select-choices.select-tooltips {
		padding-right: 36px
	}

	.select:not(.select-multiple) .select-choices.select-tooltips .select-choice {
		white-space: nowrap;
		height: 100%
	}

	.select-choice,.select-choice-default {
		overflow: hidden;
		text-overflow: ellipsis
	}

	.select-handle {
		display: inline-block;
		font-family: entypo;
		font-style: normal;
		font-weight: normal;
		font-variant: normal;
		text-transform: none;
		text-decoration: inherit;
		text-align: center;
		margin: 0 .2em;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		margin: 0;
		position: absolute;
		width: 38px;
		right: 0;
		top: 0;
		bottom: 0;
		font-size: 12px;
		line-height: 25px;
		border: 1px solid #b4b6ba;
		border-radius: 0 4px 4px 0;
		background-color: #f1f1f1;
		color: #3d3d3d;
		transition: border-color ease-in-out .15s,background-color linear .1s .05s
	}

	.select-handle:before {
		content: '\e147'
	}

	.select-focus .select-choices,.select:hover:not(.dropdown-visible):not(.select-disabled) .select-choices {
		border-color: #77797c
	}

	.select-focus .select-handle,.select:hover:not(.dropdown-visible):not(.select-disabled) .select-handle {
		background-color: #e2e4e3;
		border-color: #77797c
	}

	.select-focus:not(.dropdown-visible):active .select-choices,.select:hover:not(.select-disabled):not(.dropdown-visible):active .select-choices {
		border-color: #368ec4
	}

	.select-focus:not(.dropdown-visible):active .select-handle:before,.select:hover:not(.select-disabled):not(.dropdown-visible):active .select-handle:before {
		color: #3d3d3d
	}

	.select-focus .select-handle:active,.select-focus.dropdown-visible .select-handle:active,.select:hover:not(.select-disabled) .select-handle:active,.select:hover:not(.select-disabled).dropdown-visible .select-handle:active {
		background-color: #d1d4d4;
		border-color: #3d3d3d
	}

	.publish-form .community-select-cnt,.publish-form .description-input-cnt,.publish-form .platform-select-cnt,.publish-form .title-input-cnt,.publish-form .types-button-group-cnt {
		margin-bottom: 10px;
	}

	.scroller-content {
		position: absolute;
		overflow: auto;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		-webkit-overflow-scrolling: touch;
	}

	.alert-warning {
		border-left: 3px solid #e87b00;
		background-color: #fff3e9;
	}
}