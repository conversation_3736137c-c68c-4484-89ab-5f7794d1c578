<?xml version="1.0" encoding='UTF-8'?>
<Widget name="Dropdown menu" group="PLM Analytics/Layout/Hide and show" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>
		&lt;p&gt;This widget creates a dropdown menu. It is best suited when using several PLMA buttons and links.&lt;/p&gt;
		&lt;p&gt;It is possible to add a separator by adding the "dropdownMenu-separator" class to a subwidget.&lt;/p&gt;
	</Description>

	<Preview>
		<![CDATA[
            <img src="/resources/widgets/dropdownMenu/images/preview.PNG" alt="Dropdown menu" />
        ]]>
	</Preview>

	<Includes>
		<Include type="css" path="css/style.less"></Include>

        <Include type="js" path="../plmaResources/js/popupLib.js" />
	</Includes>
	<SupportWidgetsId arity="ZERO_OR_MANY"/>
	<SupportI18N supported="true" />
	<OptionsGroup name="General">
		<Option id="iconCss" name="Icon CSS">
			<Description>Specifies the CSS class of the icon.</Description>
		</Option>
		<Option id="title" name="Title" isEvaluated="true">
			<Description>Description displayed when the user hovers over the button.</Description>
		</Option>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="cssDisableStyle">true</DefaultValue>
	</DefaultValues>
</Widget>
