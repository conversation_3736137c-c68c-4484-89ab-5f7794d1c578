var LandingPageManager = function(options) {
	this.options = options;
	this.$menu = $();
	this.$fixedPageRadio = $();
	this.$saveStateRadio = $();
	this.$pageSelect = $();
	this.saveState = new SaveState(options);
	this.init();
};

LandingPageManager.METHOD = 'method';
LandingPageManager.PAGE = 'page';
LandingPageManager.FIXED_PAGE = 'fixed_page';
LandingPageManager.SAVE_STATE = 'save_state';

LandingPageManager.prototype.init = function() {
	$(window).on('setLandingPage.plma', function(e) {
		this._onSetLandingPage(e);
	}.bind(this));
};

LandingPageManager.prototype.createMenu = function(pages) {
	var FIXED_PAGE_RADIO_ID = this.options.uCssId + '_' + LandingPageManager.FIXED_PAGE;
	var SAVE_STATE_RADIO_ID = this.options.uCssId + '_' + LandingPageManager.SAVE_STATE;
	this.$fixedPageRadio = $('<input/>', {
		id: FIXED_PAGE_RADIO_ID,
		type: 'radio',
		name: LandingPageManager.METHOD,
		value: LandingPageManager.FIXED_PAGE,
		checked: !this.saveState.activated
	});
	this.$saveStateRadio = $('<input/>', {
		id: SAVE_STATE_RADIO_ID,
		type: 'radio',
		name: LandingPageManager.METHOD,
		value: LandingPageManager.SAVE_STATE,
		checked: this.saveState.activated
	});
	this.$pageSelect = $('<select/>', {
		name: LandingPageManager.PAGE,
		disabled: this.saveState.activated,
		'class': 'form-control'
	});
	if (pages.sharedPages.length) {
		LandingPageManager.utils.createOptGroup(pages.sharedPages, Preferences.getMessage('plma.preference.pages.shared')).appendTo(this.$pageSelect);
	}
	if (pages.userPages.length) {
		LandingPageManager.utils.createOptGroup(pages.userPages, Preferences.getMessage('plma.preference.pages.user')).appendTo(this.$pageSelect);
	}
	if (pages.mashupPages.length) {
		LandingPageManager.utils.createOptGroup(pages.mashupPages, Preferences.getMessage('plma.preference.pages.mashup')).appendTo(this.$pageSelect);
	}
	this.$fixedPageRadio.add(this.$saveStateRadio)
		.change(function(e) {
			this.$pageSelect.prop('disabled', !($(e.target).val() === LandingPageManager.FIXED_PAGE));
		}.bind(this));
	var $collapseButton = $('<span/>', {'class': 'collapse-button fonticon fonticon-expand-up'});
	var $title = $('<div/>', {'class': 'preference-title'}).append(
		$('<div/>', {'class': 'main', text: Preferences.getMessage('plma.preference.landingPage.title')}),
		$('<div/>', {'class': 'description', text: Preferences.getMessage('plma.preference.landingPage.description')})
	);
	var $block = $('<div/>', {'class': 'preference-block-container'}).append(
		$('<div/>', {'class': 'preference-block'}).append(
			$('<div/>', {'class': 'preference-elem-block landing-page-block'}).append(
				$('<form/>').append(
					$('<div/>', {'class': 'toggle toggle-primary'}).append(
						this.$fixedPageRadio,
						$('<label/>', {'class': 'control-label', for: FIXED_PAGE_RADIO_ID, text: Preferences.getMessage('plma.preference.landingPage.fixedPage')}),
						this.$pageSelect
					),
					$('<div/>', {'class': 'toggle toggle-primary'}).append(
						this.$saveStateRadio,
						$('<label/>', {'class': 'control-label', for: SAVE_STATE_RADIO_ID , text: Preferences.getMessage('plma.preference.landingPage.lastVisitedPage')})
					)
				)
			)
		)
	);
	$collapseButton.click(function() {
		$(this).toggleClass('fonticon-expand-up fonticon-expand-down');
		$title.find('.description').toggleClass('hidden');
		$block.toggleClass('hidden');
	});
	$title.after($block);
	this.$menu = $title.add($block);
	return this.$menu;
};

LandingPageManager.prototype.checkFixedPageRadio = function() {
	this.$fixedPageRadio.prop('checked', true).trigger('change');
};

LandingPageManager.prototype._onSetLandingPage = function(e) {
	this.checkFixedPageRadio();
	this.selectPageByValue(e.url);
	/* Disable state saving */
	if (this.saveState.activated) {
		this.saveState.activate(false);
	}
};

LandingPageManager.prototype.selectPageByValue = function(val) {
	this.$pageSelect.find('option[value="' + val + '"]').prop('selected', true);
};

LandingPageManager.prototype.selectHomePage = function() {
	this.$pageSelect.find('option[value$="index"]').prop('selected', true);
};

LandingPageManager.prototype.getPageById = function(pageId) {
	return this.$pageSelect.find('option[data-id="' + pageId + '"]');
};

LandingPageManager.prototype.removePageById = function(pageId) {
	var $pageToRemove = this.getPageById(pageId);
	if ($pageToRemove.is(':selected')) {
		this.selectHomePage();
	}
	$pageToRemove.remove();
};

LandingPageManager.prototype.editPageLabelById = function(pageId, label) {
	this.getPageById(pageId).text(label);
};

LandingPageManager.prototype._getFormData = function() {
	return this.$menu.find('form').serializeArray().reduce(function(data, item) {
		data[item.name] = item.value;
		return data;
	}, {});
};

LandingPageManager.prototype.save = function() {
	var data = this._getFormData();
	switch (data[LandingPageManager.METHOD]) {
		case LandingPageManager.FIXED_PAGE:
			var url = data[LandingPageManager.PAGE];
			if (window.dashboardController) {
				window.dashboardController.setPreference('url', url);
			}
			this.saveState.activate(false);
			break;
		case LandingPageManager.SAVE_STATE:
			this.saveState.activate(true);
			break;
		default:
			break;
	}
};

LandingPageManager.prototype.reset = function() {
	this.checkFixedPageRadio();
	this.selectHomePage();
	this.save();
};

LandingPageManager.utils = {
	createOptGroup: function(pages, label) {
		var $group = $('<optgroup/>', {
			label: label
		});
		pages.forEach(function(page) {
			var $option = $('<option/>', {
				value: page.url,
				text: page.label,
				selected: window.dashboardController && window.dashboardController.getPreference('url') && page.url === window.dashboardController.getPreference('url').value,
				'data-id': page.id
			});
			/* optgroup can't be nested: create a look-alike optgroup */
			if (page.section) {
				var $section = $group.find('option[value="' + page.section + '"]');
				var addSection = $section.length === 0;
				if (addSection) {
					$section = $('<option/>', {
						value: page.section,
						text: page.section,
						'class': 'section',
						disabled: true
					});
					$group.append($section);
				}
				/* Add padding by hand because styling an option is very limited */
				$option.html('&nbsp;&nbsp;&nbsp;&nbsp;' + $option.text());
				/* Add option at the end of the section */
				if ($section.nextAll().length > 0) {
					$section.nextAll().last().after($option);
				} else {
					$section.after($option);
				}
			} else {
				$group.append($option);
			}
		});
		return $group;
	}
};