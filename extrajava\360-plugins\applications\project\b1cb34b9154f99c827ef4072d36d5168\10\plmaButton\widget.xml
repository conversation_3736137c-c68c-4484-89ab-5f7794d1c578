<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA button" group="PLM Analytics/Layout" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<Description>A generic button to perform custom JavaScript.</Description>
	<Preview>
		<![CDATA[
            <img src="/resources/widgets/plmaButton/images/preview.png" alt="PLMA Button" />
        ]]>
	</Preview>

	<Platforms>
		<Platform type="web" supported="true" />
		<Platform type="mobile" supported="true" />
	</Platforms>

	<Includes>
		<Include type="css" path="css/style.less" />
	</Includes>
	
	<Dependencies>
		<Widget name="plmaResources" />
	</Dependencies>

	<SupportWidgetsId arity="ZERO" />
	<SupportFeedsId arity="ZERO_OR_MANY" />
	<SupportI18N supported="true">
		<JsKeys>
		</JsKeys> 
	</SupportI18N>
	
	<OptionsGroup name="General">
		<Option id="iconCss" name="Icon CSS" >
			<Description>Specifies the CSS class name of the icon added to the button. If empty, no icon is displayed.</Description>
		</Option>
		<Option id="label" name="Label" isEvaluated="true" arity="ONE">
			<Description>Specifies the label of the button. Briefly explains the purpose of the button (3 words max).</Description>
		</Option>
		<Option id="showLabel" name="Show label" arity="ONE">
			<Description>Displays the label next to the icon. If not checked, the label is displayed as a tooltip when hovering over the icon.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="onInit" name="On init" isEvaluated="true">
			<Description>The JavaScript code that is executed once when the button is loaded on the page. The "this" keyword points to the button as a jQuery object.</Description>
			<Placeholder>function() {}</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="onClick" name="On click" isEvaluated="true">
			<Description>The JavaScript code that is executed when the button is clicked. The jQuery click event is passed as a parameter to the function. The "this" keyword points to the button as a jQuery object.</Description>
			<Placeholder>function(e) {}</Placeholder>
			<Functions>
				<Display>SetType('code', 'js')</Display>
			</Functions>
		</Option>
		<Option id="renderOnce" name="Render script execution once" arity="ONE">
			<Description>Renders Javascript code in a renderOnce tag element.</Description>
			<Values>
				<Value>false</Value>
				<Value>true</Value>
			</Values>
		</Option>
	</OptionsGroup>

	
	<DefaultValues displayName="Normal" previewUrl="">
		<DefaultValue name="renderOnce">false</DefaultValue>
		<DefaultValue name="showLabel">false</DefaultValue>
		<DefaultValue name="onInit">function() {
			/* Use 'this' to handle the button as a jQuery object */
		}</DefaultValue>
		<DefaultValue name="onClick">function(e) {
			this.toggleClass('active');
		}</DefaultValue>
	</DefaultValues>
	
</Widget>
