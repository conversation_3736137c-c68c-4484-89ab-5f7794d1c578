<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>

<render:import parameters="feeds,
    widget,
    baseUrl,
    table2D,
    currentAxeX,
    axisXMaxItems,
    axisXConfigFacetList,
    axisXCategories,
    axisXRefinedCategories,
    currentAxeY,
    axisYMaxItems,
    axisYConfigFacetList,
    axisYCategories,
    currentAggregation,
    currentAggregationId,
    withAjax" />
<render:import parameters="transpose" ignore="true"/>

<config:getOption var="showSummaries" name="showSummaries" defaultValue="true" />

<config:getOption var="aggregationFormat" name="aggregationFormat" defaultValue="" />

<config:getOption var="pagination" name="pagination" defaultValue="false" />
<config:getOption var="maxElemByPage" name="maxElemByPage" />
<request:getParameterValue var="pageNb" name="pageNb" defaultValue="1" />

<config:getOptionsComposite var="widgetsId" name="widgetsId" defaultValue="" />
<list:new var="updateIds" />
<c:forEach var="widgetId" items="${widgetsId}">
	<list:add list="${updateIds}" value="${widgetId[0]}" />
</c:forEach>

<%-- QMV : Create Base Url { --%>
<c:if test="${fn:contains(baseUrl, '?') != 'true'}">
	<c:set var="baseUrl" value="${baseUrl}?"/>
</c:if>
<%-- } QMV : Calculate Cell width --%>
<c:set var="col_count" value="${axisYCategories.size() + 1 + (showSummaries == 'true'? 1 : 0 ) }"/>
<c:set var="row_count" value="${axisXCategories.size() + 1 + (showSummaries == 'true'? 1 : 0 ) }"/>
<style>
	.data_table_matrix th,
	.data_table_matrix td {
		width: ${ 100/(transpose ? row_count : col_count) }%;		
	}
</style>
<%-- } QMV --%>

<%-- If transposed, interchange X and Y values --%>
<c:if test="${transpose}">
    <c:set var="temp_currentAxeX" value="${currentAxeX}" />
    <c:set var="temp_axisXMaxItems" value="${axisXMaxItems}" />
    <c:set var="temp_axisXConfigFacetList" value="${axisXConfigFacetList}" />
    <c:set var="temp_axisXCategories" value="${axisXCategories}" />
    <c:set var="temp_axisXRefinedCategories" value="${axisXRefinedCategories}" />
    
    <c:set var="currentAxeX" value="${currentAxeY}" />
    <c:set var="axisXMaxItems" value="${axisYMaxItems}" />
    <c:set var="axisXConfigFacetList" value="${axisYConfigFacetList}" />
    <c:set var="axisXCategories" value="${axisYCategories}" />
    <c:set var="axisXRefinedCategories" value="${axisYRefinedCategories}" />
    
    <c:set var="currentAxeY" value="${temp_currentAxeX}" />
    <c:set var="axisYMaxItems" value="${temp_axisXMaxItems}" />
    <c:set var="axisYConfigFacetList" value="${temp_axisXConfigFacetList}" />
    <c:set var="axisYCategories" value="${temp_axisXCategories}" />
    <c:set var="axisYRefinedCategories" value="${temp_axisXRefinedCategories}" />
</c:if>

<widget:getUcssId var="uCssId" />

<c:set var="maxAggregationValue" value="0" />
<c:forEach items="${axisYCategories}" var="yCategory">
	<c:forEach items="${axisXCategories}" var="xCategory">
		<c:if test="${table2D[yCategory.key][xCategory.key] != null || (transpose && table2D[xCategory.key][yCategory.key] != null)}">
			<fmt:parseNumber var="value" parseLocale="en_US">
				<search:getCategoryValue category="${transpose ? table2D[xCategory.key][yCategory.key] : table2D[yCategory.key][xCategory.key]}" name="${currentAggregationId}" defaultValue="0" />
			</fmt:parseNumber>
			<c:if test="${value < 0}"><c:set var="value" value="${value * -1}"/></c:if>
			<c:if test="${value > maxAggregationValue}">
				<c:set var="maxAggregationValue" value="${value}" />
			</c:if>
		</c:if>
	</c:forEach>
</c:forEach>

<span id="param_ajax_${uCssId}" style="display: none;"><request:getParameterValue name="paramAjax" defaultValue="" /></span>

<%-- QMV : Added class data_table_matrix --%>
<table class="data-table data_table_matrix shape_${shapeType}">
	<tr>
		<td></td>
		<c:forEach items="${axisYCategories}" var="yCategory" varStatus="varStatus">
			<c:if test="${(axisXMaxItems <=0 || varStatus.index < axisXMaxItems) && (yCategory.value.description!='' || fn:length(axisYCategories)==1) && (pagination == 'false' || (varStatus.index < maxElemByPage*pageNb && varStatus.index >= maxElemByPage*(pageNb-1)))}">
				<th>
					<%-- QMV : Moved buildUrl outside div, added wrapper a { --%>
					<plma:buildUrl url="urlRefines" catLabel="categoryLabel" category="${yCategory.value}" feeds="${feeds}" labelAxis="${axisYConfigFacetList}" refineAxis="${axisXConfigFacetList}" />
					<a class="linkTitle" onclick="PLMATable.utils.refineOnFacet('${urlRefines}','<request:getParameterValue name="paramAjax" defaultValue="" />','${uCssId}','${widgetsId}')">
						<div class="withAxeTitleMenu">
							<i18n:message code="facet_${fn:replace(yCategory.value.path, ' ', '_')}" text="${yCategory.key}" />													
						</div>
					</a>
					<%-- } QMV --%>
				</th>
			</c:if>
		</c:forEach>

		<c:if test="${showSummaries == 'true'}">
			<th class="totalTitle"><i18n:message code="total_x" text="Total" /></th>
		</c:if>
	</tr>

	<c:forEach items="${axisXCategories}" var="xCategory" varStatus="varStatus">
		<c:if test="${axisYMaxItems <=0 || varStatus.index < axisYMaxItems}">
			<c:set var="isCatRefined" value="false" />
			<c:forEach var="refined" items="${axisXRefinedCategories}">
				<search:getCategoryLabel var="refinedLabel" category="${refined}" />
				<c:if test="${refinedLabel==xCategory.key}">
					<c:set var="isCatRefined" value="true" />
				</c:if>
			</c:forEach>
			<c:if test="${(isCatRefined == 'false' || fn:length(axisXCategories) == 1) && (xCategory.value.description!='' || fn:length(axisXCategories)==1)}">
				<%-- QMV : Generate label and url for X{ --%>
				<plma:buildUrl url="urlRefinesX" catLabel="categoryLabelX" category="${xCategory.value}" feeds="${feeds}" labelAxis="${axisXConfigFacetList}" refineAxis="${axisYConfigFacetList}" />
				<c:if test="${categoryLabelX==''}">
					<c:set var="categoryLabelX" value="${xCategory.value.description}"/>
				</c:if>
				<%-- } QMV --%>
				
				<tr class="${varStatus.index % 2 == 0 ? 'even' : 'odd'}">
					<th>
						<%-- QMV : added wrapper a { --%>
						<a class="linkTitle" onclick="PLMATable.utils.refineOnFacet('${urlRefinesX}','<request:getParameterValue name="paramAjax" defaultValue="" />','${uCssId}','${widgetsId}')">
						<div class="withAxeTitleMenu">
							<i18n:message code="facet_${fn:replace(xCategory.value.path, ' ', '_')}" text="${xCategory.key}" />
						</div>
						<%-- } QMV --%>
					</th>

					<c:forEach items="${axisYCategories}" var="yCategory" varStatus="varStatus">
						<c:if test="${(axisXMaxItems <=0 || varStatus.index < axisXMaxItems) && (yCategory.value.description!='' || fn:length(axisYCategories)==1) && (pagination == 'false' || (varStatus.index < maxElemByPage*pageNb && varStatus.index >= maxElemByPage*(pageNb-1)))}">
							<%-- QMV : Generate label and url for Y{ --%>
							<plma:buildUrl url="urlRefinesY" catLabel="categoryLabelY" category="${yCategory.value}" feeds="${feeds}" labelAxis="${axisYConfigFacetList}" refineAxis="${axisXConfigFacetList}" />
							<c:if test="${categoryLabelY==''}">
								<c:set var="categoryLabelY" value="${yCategory.value.description}"/>
							</c:if>
							<%-- } QMV --%>
							<td>
								<c:set var="categoryAggregationValue" value="0" /> 
								<c:if test="${table2D[yCategory.key][xCategory.key] != null || (transpose && table2D[xCategory.key][yCategory.key] != null)}">
									<search:getCategoryValue var="categoryAggregationValue" category="${transpose ? table2D[xCategory.key][yCategory.key] : table2D[yCategory.key][xCategory.key]}" name="${currentAggregationId}" defaultValue="0" />
								</c:if> 
								<%-- QMV : Add wrapper a > div{ --%>
								<a class="linkTitle"  onclick="PLMATable.utils.refineOnFacet('${urlRefinesX}${urlRefinesY}','<request:getParameterValue name="paramAjax" defaultValue="" />','${uCssId}','${widgetsId}')">
								<config:getOption var="facetName" name="facetName" />
								<%-- Categories' description doesn't exist if there is a refine: we need to retrieve the facet --%>
								<search:getFacet var="xFacet" feeds="${feeds}" facetId="${axisXConfigFacetList[0].id}" />
								<search:getCategory var="xCat" facet="${xFacet}" categoryPath="${yCategory.value.path}" />
								<search:getFacet var="yFacet" feeds="${feeds}" facetId="${axisYConfigFacetList[0].id}" />
								<search:getCategory var="yCat" facet="${yFacet}" categoryPath="${xCategory.value.path}" />
								<plma:getCategoryColor var="categoryColor" categoryName="${yCat.description}_${xCat.description}" facetId="${facetName}" />
								<string:escape var="categoryColor" value="${categoryColor}" escapeType="HTML" />
								<plma:getCategoryColor var="categoryColorTransposed" categoryName="${xCat.description}_${yCat.description}" facetId="${facetName}" />
								<string:escape var="categoryColorTransposed" value="${categoryColorTransposed}" escapeType="HTML" />
								<c:choose>
									<c:when test="${not empty categoryColor}">
										<div id="matrix_cell_${categoryLabelY}_${categoryLabelX}" class="matrix_cell" style="background-color:${categoryColor }">
											<fmt:formatNumber value="${categoryAggregationValue}" pattern="${aggregationFormat}" />
										</div>
									</c:when>
									<c:otherwise>
										<div id="matrix_cell_${categoryLabelY}_${categoryLabelX}" class="matrix_cell" style="background-color:${categoryColorTransposed }">
											<fmt:formatNumber value="${categoryAggregationValue}" pattern="${aggregationFormat}" />
										</div>
									</c:otherwise>
								</c:choose>
								</a>
								<%-- } QMV --%>
							</td>
						</c:if>
					</c:forEach>

					<c:if test="${showSummaries == 'true'}">
						<td class="summary">
							<%-- QMV : Add Wrapper div{ --%>
							<div class="matrix_cell">					
								<fmt:formatNumber pattern="${aggregationFormat}">
									<search:getCategoryValue category="${xCategory.value}" name="${currentAggregationId}" defaultValue="0" />
								</fmt:formatNumber>
							</div>
							<%-- } QMV --%>
						</td>
					</c:if>
				</tr>
			</c:if>
		</c:if>
	</c:forEach>

	<c:if test="${showSummaries == 'true'}">
		<tr>
			<th class="totalTitle"><i18n:message code="total_y" text="Total" /></th>

			<c:forEach items="${axisYCategories}" var="yCategory" varStatus="varStatus">
				<c:if test="${(axisXMaxItems <=0 || varStatus.index < axisXMaxItems) && (yCategory.value.description!='' || fn:length(axisYCategories)==1) && (pagination == 'false' || (varStatus.index < maxElemByPage*pageNb && varStatus.index >= maxElemByPage*(pageNb-1)))}">
					<td class="summary"><c:set var="sum" value="0" /> 
						<c:forEach items="${axisXCategories}" var="xCategory">
							<c:if test="${table2D[yCategory.key][xCategory.key] != null || (transpose && table2D[xCategory.key][yCategory.key] != null)}">
								<fmt:parseNumber var="value" parseLocale="en_US">
									<search:getCategoryValue category="${transpose ? table2D[xCategory.key][yCategory.key] : table2D[yCategory.key][xCategory.key]}" name="${currentAggregationId}" defaultValue="0" />
								</fmt:parseNumber>
								<c:set var="sum" value="${sum+value}" />
							</c:if>
						</c:forEach> 
						<%-- QMV : Add Wrapper div{ --%>
						<div class="matrix_cell">
							<fmt:formatNumber value="${sum}" pattern="${aggregationFormat}" />
						</div>
						<%-- } QMV --%>
					</td>
				</c:if>
			</c:forEach>

			<td class="summary">
				<config:getOption var="facetName" name="facetName" /> 
				<search:getFacet var="facet" facetId="${facetName}" feeds="${feeds}" /> 
				<search:getFacetValue var="facetValue" name="${currentAggregationId}" facet="${facet}" defaultValue="0" /> 
				<%-- QMV : Add Wrapper div{ --%>
				<div class="matrix_cell">
					<fmt:formatNumber value="${facetValue}" pattern="${aggregationFormat}" />
				</div>
				<%-- } QMV --%>
			</td>
		</tr>
	</c:if>

</table>

