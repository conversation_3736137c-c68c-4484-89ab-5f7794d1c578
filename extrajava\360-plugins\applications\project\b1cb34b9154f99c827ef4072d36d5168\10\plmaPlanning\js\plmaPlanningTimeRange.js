var PLMAPlanningTimeRange = function(start,end, boundResult){
    this.start = start;
    this.end = end;
	this.boundResult = boundResult;
	return this;
}

PLMAPlanningTimeRange.prototype.format = function(dateFormat){
    dateFormat = dateFormat | PLMAPlanningNew.utils.DATE_FORMAT;
    return {
        start: moment(this.start).format(PLMAPlanningNew.utils.DATE_FORMAT),
        end: moment(this.end).format(PLMAPlanningNew.utils.DATE_FORMAT),
    }
}
PLMAPlanningTimeRange.prototype.toString = function(){
    if(this.start < this.end){
		return '[' + moment(this.start).format(PLMAPlanningNew.utils.DATE_FORMAT) + ', '
			+ moment(this.end).format(PLMAPlanningNew.utils.DATE_FORMAT) + ']' + this.boundResult;
	}
	return undefined;
}
/*Unit, FullName, min width, */
PLMAPlanningTimeRange.UNITS = [
	['y',  'years', 		45 ],
	['Q',  'quarters', 		45 ],
	['M',  'months', 		45 ],
	['w',  'weeks', 		45 ],
	['d',  'days', 			45 ],
	['h',  'hours', 		45 ],
	['m',  'minutes', 		45 ],
	['s',  'seconds', 		45 ],
	['ms', 'milliseconds',  45 ]
];
PLMAPlanningTimeRange.UNITS_STR = ',' + PLMAPlanningTimeRange.UNITS.toString() +',';


PLMAPlanningTimeRange.isValid = function(range){
    return PLMAPlanningTimeRange.prototype.isPrototypeOf(range);
}

PLMAPlanningTimeRange.isBoundValid = function(bound){
    return bound != undefined && bound.value != undefined && bound.unit != undefined && 
		isNaN(parseInt(bound.value)) == false && 
		PLMAPlanningTimeRange.UNITS_STR.search(','+bound.unit+',') != -1;
}

PLMAPlanningTimeRange.getNowAsBound = function(){
    return { value: 0, unit: 'ms' };
}

/* Creates the range by using current time as reference. You can supply following inputs.
    { useNow: true, start: { value: int, unit: string }, end: { value: int, unit: string } }
	@useNow Uses now when one of the bound is missing. Default true.
    e.g. Get range with 7Months before now till 7days from now { start: { value: -7, unit: 'M' }, end: { value: 7, unit: 'd' } }
    Provide -ve value when you want to go back in time.
    e.g 7days before now, can be achieved by value=-7, unit='d'/unit='days'
    For 'unit', refer to moment.js documentation : https://momentjs.com/docs/#/manipulating/add/
*/
PLMAPlanningTimeRange.make = function(optionsInput){
	let options = $.extend(true,{ useNow: true }, optionsInput);
	let startValid = PLMAPlanningTimeRange.isBoundValid(options.start);
	let endValid = PLMAPlanningTimeRange.isBoundValid(options.end);
	let boundResult = '';
	
    if(startValid == false && endValid == false){
        return new Error('Wrong Input');
    }else if(options.useNow){
		if(startValid == false){
			options.start = PLMAPlanningTimeRange.getNowAsBound();
			boundResult = '(Start=Now)';
		}
		if(endValid == false){
			options.end = PLMAPlanningTimeRange.getNowAsBound();
			boundResult = '(End=Now)';
		}		
	}else if(startValid == false || endValid == false){
		return new Error('Missing One of(start/end) Info');
	}
	
	let start =	moment().add(options.start.value, options.start.unit).valueOf();
	let end = moment().add(options.end.value, options.end.unit).valueOf();
	if(start >= end){ // 0 diff range is not allowed, hence checking equlity
		return new Error('Start >= End ' + boundResult);
	}
    return new PLMAPlanningTimeRange(start, end, boundResult);
}

/*Provide Vis Timeline object.
 When isCurrentFrame!=true : It calculates the range of all the items in timeline
 When isCurrentFrame==true : It calculates the range of current visible time span.
*/
PLMAPlanningTimeRange.makeFromTimeline = function(timeline, isCurrentFrame){
    let range = timeline.getItemRange();
	if(isCurrentFrame == true){
		range = timeline.getWindow();
		return new PLMAPlanningTimeRange(
			moment(range.start).valueOf(),
			moment(range.end).valueOf()
		);
	}
	return new PLMAPlanningTimeRange(
		moment(range.min).valueOf(),
		moment(range.max).valueOf()
	);
}