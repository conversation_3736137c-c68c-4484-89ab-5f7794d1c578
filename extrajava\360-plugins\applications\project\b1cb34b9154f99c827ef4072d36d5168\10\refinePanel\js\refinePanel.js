
var PLMARefineObject = function (uCssId, facetList, currentUrl) {
	this.uCssId = uCssId;
	this.inputValue = '';
	this.facetList = facetList.replace(/ /g, '');
	this.currentUrl = currentUrl;
};

var refinesWidget = {
	state: [],
    WIDGET_NAME:'refinePanel',
	initI18N: function (i18n) {
		refinesWidget.i18n = i18n;
	},
	initCheckboxes: function ($widget,options) {
		this.paramNameReload = options.paramNameReload;
		this.loadDateFacetParameter = options.loadDateFacetParameter;
		this.loadFacetParameter = options.loadFacetParameter;
		let cleanURLParameters = FacetRefine.prototype.cleanURLParameters.bind(this);

		$widget.bind('click', {_this: this}, function (e) {
			var target = e.data._this.getEventTarget(e);
			if (target != null) {
				if (target.attr('data-url') != null) {
					exa.redirect( cleanURLParameters(target.attr('data-url')));
					return false;
				}
			}
		});
	},
	setCookie: function (name, collapsed) {
		var cookie = $.cookie('refineWidget'),
			cookieValues,
			i;

		var cookiePath = this.getCookiePath();

		if (!cookie) {
			if (collapsed) {
				$.cookie('refineWidget', name, {path: cookiePath});
			}
			return;
		}
		cookieValues = cookie.split(';');
		for (i = 0; i < cookieValues.length; i++) {
			if (cookieValues[i] == name) {
				if (collapsed) {
					return;
				} else {
					cookieValues.splice(i, 1);
					$.cookie('refineWidget', cookieValues.join(';'), {path: cookiePath});
					return;
				}
			}
		}
		cookieValues.push(name);
		$.cookie('refineWidget', cookieValues.join(';'), {path: cookiePath});
	},
	getEventTarget: function (e) {
		var target = null;
		try {
			if (!e) e = window.event;
			if (e.target) target = e.target;
			else if (e.srcElement) target = e.srcElement;
			if (target.nodeType == 3) /*  defeat Safari bug */
				target = targ.parentNode;
			/*  target = $(e.originalTarget || e.target) */
			return $(target);
		} catch (e) {
		}
		return null;
	},
	initToggle: function ($widget) {
		refinesWidget.loadState($widget);

		$widget.on('click', 'h3', function (e) {
			var $this = $(this);
			$this.find('>.icon-collapsed,>.icon-collapsable').toggleClass('icon-collapsable icon-collapsed');

			var $input = $this.next('div.inputFilterCategory');
			$input.toggleClass('table-collapsable table-collapsed');

			var $table = $input.next('table.facet');
			$table.toggleClass('table-collapsable table-collapsed');

			refinesWidget.setCookie($this.attr('name'), $table.hasClass('table-collapsed'));
			refinesWidget.showRefinementsInfo($table);
		});
	},
	initMenu: function ($widget, uCssId, options) {
	    refinesWidget._initializeButtonOptions($widget, options);
    },
    _initializeButtonOptions: function($widget, options) {
		$widget.find('.collapseButton').on('click', $.proxy(function (e) {
			refinesWidget._handleCollapseExpandAll($widget,true);
		}, this));

		$widget.find('.expandButton').on('click', $.proxy(function (e) {
			refinesWidget._handleCollapseExpandAll($widget,false);
		}, this));
    },
    _handleCollapseExpandAll: function ($widget,doCollapse) {
        $widget.find('.filter-list-facet-elem-facet .facet-value-container').each(function(i, el){
            if($(el).find("div.selected.hidden").hasClass(['hidden'],['selected']) != doCollapse){
                $(el).parent().find('span.collapsible-buttons').trigger('click');
            }
        });

        $widget.find('.filter-list-facet-elem-numerical .facet-container-list').each(function(i, el){
            if($(el).hasClass('hidden') != doCollapse){
                $(el).parent().find('h3').trigger('click');
            }
        });
    },
    initCloseButtonBehavior: function($widget) {
        $widget.find('.closeButton').on('click', $.proxy(function () {
            $(document).trigger('plma:close-refine-panel', ["refinesPanel"]);
            $(window).resize();
        }, this));
    },
	loadState: function ($widget) {
		var cookie = $.cookie('refineWidget'),
			cookieValues,
			i;
		if (!cookie) {
			return;
		}
		cookieValues = cookie.split(';');

		$widget.find('h3').each(function () {
			var $this = $(this);
			var value = $this.attr('name');
			for (i = 0; i < cookieValues.length; i++) {
				if (cookieValues[i] == value) {
					refinesWidget.showRefinementsInfo($this.find('+table.facet'));
					return;
				}
			}
		});
	},
	showRefinementsInfo: function ($table) {
		if ($table.hasClass('table-collapsed')) {
			var nbRefinements = $table.find('.refined,.excluded').length,
				$h3 = $table.prev('h3'),
				$infos = $h3.find('> span.infos');
			$h3.addClass('sub-header-collapsed');
			if (nbRefinements == 0) {
				$infos.hide();
			} else if (nbRefinements == 1) {
				$infos.show();
				$infos.html(nbRefinements + ' ' + refinesWidget.i18n.singular);
			} else {
				$infos.show();
				$infos.html(nbRefinements + ' ' + refinesWidget.i18n.plural);
			}
		} else {
			var $infos = $table.prev('h3').find('> span.infos');
			$infos.hide();
		}
	},
	getCookiePath: function () {
		var metas = document.getElementsByTagName('meta');
		for (i = 0; i < metas.length; i++) {
			if (metas[i].getAttribute('name') == 'baseurl') {
				return metas[i].getAttribute('content');
			}
		}
		return '/';
	},
		
    getMessage:function (code) {
        return mashupI18N.get(refinesWidget.WIDGET_NAME, code);
    },
	enableDragging: function() {
		$('.refine-link:not(.cancel)').draggable({
			appendTo: 'body',
			cursorAt: { left: 0 },
			helper: function(e) {
				var $target = $(e.currentTarget);
				var isExclude = $target.hasClass('exclude');
				var categoryName = isExclude
					? $target.closest('.category').find('.refineName .refine-link').text().trim()
					: $target.text().trim();
				return $('<span/>', {
					class: 'refine-link-draggable' + (isExclude ? ' exclude' : ''),
					text: categoryName
				});
			},
			revert: 'invalid',
			scroll: false,
			zIndex: 1
		});
	}

};


function renderAjax(url, widgetId, additionalParams) {
	var decoded = decodeURIComponent(url);
	var client = new PlmaAjaxClient($('.' + widgetId));
	var params = decoded.split('?');
	var parameters = getUrlParameters(params[1]);

	/* Delete the base params */
	client.getAjaxUrl().params = {};

	for (var i = 0; i < parameters.length; i++) {
		var key = parameters[i].key;
		var value = parameters[i].value;
		if (key != "" && key != "_") {
			client.getAjaxUrl().addParameter(key, value, false);
		}
	}

	if (additionalParams) {
		for (var key in additionalParams) {
			if (key && key !== "" && additionalParams[key] && additionalParams[key] !== "") {
				client.getAjaxUrl().addParameter(key, additionalParams[key]);
			}
		}
	}


	/*  set wuids to update */
	client.addWidget(widgetId);
	/*update children widgets*/
	$('.wuid.' + widgetId + ' .wuid').each(function (eWidget, iWidget) {
		client.addWidget(iWidget.getClassList()[1]);
	});

	client.update();
}

function getUrlParameters(url) {
	var f = [];
	if (url == undefined) {
		return f;
	}

	var t = url.split('&');
	var j = 0;
	for (var i = 0; i < t.length; i++) {
		var x = t[i].split('=');
		if (x.length == 1 && t[i] != "" && j > 0) {
			f[j - 1].value = f[j - 1].value + '&' + t[i];
		} else if (x.length == 2) {
			f[j] = {
				key: x[0],
				value: x[1]
			};
			j++;
		} else if (x.length == 3) { /*  CASE refine=test+tokenizer=<EMAIL> => t=['refine', 'test+tokenizer', '<EMAIL>'] */
			f[j] = {
				key: x[0],
				value: x[1] + '=' + x[2]
			};
			j++;
		}

	}
	return f;
}

$(function () {

});

