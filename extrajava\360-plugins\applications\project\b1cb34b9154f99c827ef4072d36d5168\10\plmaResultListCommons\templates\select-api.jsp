<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<config:getOption var="enablePublication" name="enablePublication" />
<config:getOption var="enableSubscription" name="enableSubscription" />
<config:getOptions var="channelNames" name="channels" />
<config:getOption var="useApplicationConfig" name="useApplicationConfig" />
<c:if test="${enablePublication || enableSubscription}">
    <render:renderScript>
        <c:choose>
            <c:when test="${useApplicationConfig}">
                <plma:getChannelsConfig var="channels" channelNames="${channelNames}" />
                var channelsConfig = ${channels};
                channelsConfig.forEach(function (channelConfig) {
                    SelectAPI.addTopic(channelConfig.topic, channelConfig.data, eval('(' + channelConfig.normalizer + ')'), eval('(' + channelConfig.denormalizer + ')'));
                });
            </c:when>
            <c:otherwise>
                <config:getOptionsComposite var="channels" name="channelsConfig" mapIndex="true"/>
                <c:forEach items="${channels}" var="channel">
                    SelectAPI.addTopic('${channel.topic}', ${channel.data}, ${channel.normalizer}, ${channel.denormalizer});
                </c:forEach>
            </c:otherwise>
        </c:choose>
    </render:renderScript>
</c:if>