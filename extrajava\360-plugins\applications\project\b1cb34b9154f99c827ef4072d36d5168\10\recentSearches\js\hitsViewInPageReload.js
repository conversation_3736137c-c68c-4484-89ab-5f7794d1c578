/**
 * HitsViewInPageReload : This class provides a way to manage custom hits display in page relaod mode.
 * E.g You want to relaod the whole page to show the hits collection. 
 * Note: This code is provided as sample. You you want to modify any behaviour you can override particular method 
 * or write your own class.
 * @param {options.id} 'collectionId'
 * @param {options.widgetSelector} 'Widget CSS Id' to attache this object and mange its display.
 * @param {options.updateDisplay} 'true|false' false by default. If the collection needs to be updated for every DBChange.
 * @param {options.queryParameter} If you want to enable query through URL then provide this param. else keep empty. Large URL can;t work with GET request. 
 * @param {options.cookieParameter} If you want to manage as cookie, and then use the pre-request trigger CookieToParam to pass query to Feed Request. 
 * 		if queryParameter and cookieParameter both are empty caller needs to manage supplying the query to access feed.
 */
var HitsViewInPageReload = function (options) {
	this.options = $.extend({ 
			updateDisplay: false, 
			paramsToKeep:[]
		}, options);
	this.$widget = $(this.options.widgetSelector);	
	this.$widget.data('hitsViewInPageReload', this);
	return this;
}

HitsViewInPageReload.COOKIE_BACKURL = 'rs_backurl';
HitsViewInPageReload.URL_PARAM = 'viewcollection';
HitsViewInPageReload.CLASS_DISPLAYED = 'collection-displayed';

HitsViewInPageReload.getInstance = function(widgetSelector){
	let obj = $(widgetSelector).data('hitsViewInPageReload');
	if(obj == undefined){
		let proxyFn = function(){ console.warn('HitsViewInPageReload not Instantiated'); }
		obj = { init: proxyFn, dbUpdate: proxyFn, show: proxyFn, hide: proxyFn };
	}
	return obj;
}

HitsViewInPageReload.getEntryUrl = function(){
	var newUrl = new BuildUrl(window.location.pathname + window.location.search);
	newUrl.removeParameter(HitsViewInPageReload.URL_PARAM);
	newUrl.removeParameter('_');
	return newUrl.toString();
}

HitsViewInPageReload.prototype.handleQueryParam = function(data){
	return this.options.queryParameter ? 
		'&' + this.options.queryParameter + '=' + encodeURIComponent(data.getQuery()) : 
		'';
}

HitsViewInPageReload.prototype.handleParamQuery = function(data){
    var dfd = $.Deferred();
	if(this.options.cookieParameter){
		// Set the path where the cookie will be applied. Hence for all ajax request during
		// display the
		var queryVal = data.getQuery();
		if(queryVal){
			$.cookie(this.options.cookieParameter, queryVal, {path: mashup.baseUrl});
			if(!$.cookie(this.options.cookieParameter) || $.cookie(this.options.cookieParameter).length !== queryVal.length){
                $.notify("Query Length is too big to set in Cookie. Result can be unexpected", "error");
                console.error("Use Stroage instead of cookie to send the query. Check comments in JS file for more details.");
                /* Use Stroage instead of cookie to send the query. Use 'storageParam' instead of 'cookieParam' in options.
                Use feed trigger 'Hits Collection Query', to read the query from Storage and append to query restriction automatically
                Remove all the cookie based implementation(page triggers cookie to param, MEL expression from feeds query restriction */
            }
			dfd.resolve(true);
		}else{
			$.cookie(this.options.cookieParameter, "empty-collection", {path: mashup.baseUrl});
			dfd.resolve(false);
		}
	}else if(this.options.storageParam){
	    var queryVal = data.getQuery();
        var value = queryVal? queryVal : "-";

        var count = data.getEntryList().length;
        var queryStorage = new StorageClient(this.options.storageType?this.options.storageType : 'user');
        queryStorage.set(this.options.storageParam, value, function(){
            dfd.resolve(count> 0);
        }, function(){
            dfd.reject();
        });
    }else{
        dfd.resolve(true);
    }
	return dfd;
}

HitsViewInPageReload.prototype.handleParamsToKeep = function(newUrl, currUrl){
	this.options.paramsToKeep.forEach(function(param){
		var paramValues = currUrl.getParameter(param);
		if(paramValues && paramValues.length > 0){
			newUrl.addParameters(param, paramValues, true);
		}
	}.bind(this));
}

HitsViewInPageReload.prototype.init = function(data){
	var hasBackUrl = $.cookie(HitsViewInPageReload.COOKIE_BACKURL) != undefined;
	var currUrl = new BuildUrl(window.location.href);
	var paramValues = currUrl.getParameter(HitsViewInPageReload.URL_PARAM);
	var hasCollectionId = paramValues != undefined? paramValues.indexOf(this.options.id) != -1 : false;

	this.initialised = this.handleParamQuery(data);
	this.initialised.then(function(cookieValid){
		if(hasBackUrl && hasCollectionId){
			if(cookieValid){
				this.$widget.addClass(HitsViewInPageReload.CLASS_DISPLAYED);
				// Add timestamp(modify url history State so that on history back the modified collection is loaded
				// instead of browser history cache.
				currUrl.addParameter('_', ''+(new Date()).getTime(), true);
				window.history.replaceState({},'', currUrl.toString() );
				
			}else{
				this.hide(data);
			}
		}else{
			if(hasBackUrl == true){ // Meaning URL_PARAM is emptry or points to some other collection.
				if(paramValues == undefined){
					// URL_PARAM is emptry mewan display exited from external trigger hence
					// So Clean up cookies.
					$.cookie(HitsViewInPageReload.COOKIE_BACKURL, null);
				}
			}else if(hasCollectionId == true){
				// Meaning url requesting to view the collection, but the back url is not present.
				// Hence Add Back url(by removing URL_PARAM).
				currUrl.removeParameter(HitsViewInPageReload.URL_PARAM);
				currUrl.removeParameter('_');
				$.cookie(HitsViewInPageReload.COOKIE_BACKURL, currUrl.toString());

				if(cookieValid){
					// Also we need to reload with the actual query value(either cookieParameter or as queryParameter.
					var newHref = window.location.pathname + '?' + HitsViewInPageReload.URL_PARAM + '=' + this.options.id;
					var newUrl = new BuildUrl(newHref);
					this.handleParamsToKeep(newUrl, currUrl);
					newHref = newUrl.toString();

					if(this.options.queryParameter){
						// QUery parameter is required. if the queryParameter is empty add one.
						// else the user knows what he wants to load.
						if(currUrl.getParameter(this.options.queryParameter) == undefined){
							newHref = newHref + this.handleQueryParam(data)
							window.location.replace(newHref);
						}
					}else{
						// Check if the cookie exists and is consistent with current entries
                        if (this.isCookieConsistent()) {
                            // If consistent, no need to reload
                            this.$widget.addClass(HitsViewInPageReload.CLASS_DISPLAYED);
                        } else {
                            // If not consistent, reload the page
                            window.location.replace(newHref);
                        }
					}
				}else{
					this.hide(data);
				}
			}
		}
	}.bind(this));
}

HitsViewInPageReload.prototype.isCookieConsistent = function() {
    var cookieParams = $.cookie(HitsViewInPageReload.COOKIE_BACKURL);
    if (!cookieParams) {
        // If the cookie doesn't exist, return false indicating inconsistency
        return false;
    }

    return true;
};

HitsViewInPageReload.prototype.dbUpdate = function(data){
	this.initialised.then(function(){
		this.handleParamQuery(data);
	}.bind(this));
}

HitsViewInPageReload.prototype.show = function(data, urlCallback){
	this.initialised.then(function(){
		if(this.options.updateDisplay || data.what == 'toggle-display'){
			if(data.btnData){
				data.btnData.$element.off('click');
			}
			if($.cookie(HitsViewInPageReload.COOKIE_BACKURL) == undefined){
				$.cookie(HitsViewInPageReload.COOKIE_BACKURL, window.location.pathname + window.location.search + window.location.hash);
			}

			var newUrl = new BuildUrl(window.location.pathname);
			var currUrl = new BuildUrl(window.location.pathname + window.location.search);
			this.handleParamsToKeep(newUrl, currUrl);

			var fn = $.isFunction(urlCallback)? urlCallback : $.noop;
			fn(newUrl, currUrl);

			newUrl.addParameter(HitsViewInPageReload.URL_PARAM, this.options.id, true);
			var newHref = newUrl.toString() + this.handleQueryParam(data);
			window.location.href = newHref;
		}
	}.bind(this));
}

HitsViewInPageReload.prototype.hide = function(data){
	this.initialised.then(function(){
		if(data.what != 'switch-display'){
			var backLoc = $.cookie(HitsViewInPageReload.COOKIE_BACKURL);
			if (backLoc) {
				$.cookie(HitsViewInPageReload.COOKIE_BACKURL, null);
				window.location.href = backLoc;
			} else {
					window.location.href = constructDynamicFallbackUrl(data);
			}
		}
		this.$widget.addClass(HitsViewInPageReload.CLASS_DISPLAYED);
	}.bind(this));

	function constructDynamicFallbackUrl(data) {
		var currentUrl = new BuildUrl(window.location.href);
		return currentUrl.toString();
	}
}