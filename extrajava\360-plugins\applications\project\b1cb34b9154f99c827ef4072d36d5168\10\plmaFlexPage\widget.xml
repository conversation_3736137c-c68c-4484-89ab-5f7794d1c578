<?xml version="1.0" encoding='UTF-8'?>
<Widget name="PLMA flex page container" group="PLM Analytics/Layout/Flex display" premium="true" xsi:noNamespaceSchemaLocation="urn:360:com.exalead.cv360.searchui.definition.v10.widget" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<Description>This widget is a flex page container with menu, content and refine panel (flex row). Main content is flex column with header and footer (3 blocks also). </Description>
	
	<Includes>
    	<Include type="css" path="css/style.less" />
		<Include type="js" path="js/plmaCollapsibleContainer.js"/>
    </Includes>
    
	<SupportFeedsId arity="ZERO_OR_MANY" consumeFeed="false"/>
	<SupportWidgetsId arity="ZERO_OR_MANY" displayType="LAYOUT" />
	<SupportI18N supported="true"/>

	<Dependencies>
		<Widget name="preferences" />
		<Widget name="plmaResources" />
	</Dependencies>
	
	<OptionsGroup name="General">
		<Option id="width" name="Width" arity="ZERO_OR_ONE">
			<Description>Specifies the table width. Defaults to '100'.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="widthFormat" name="Width format" arity="ZERO_OR_ONE">
			<Description>Specifies the width format of the table. Defaults to '%'.</Description>
			<Values>
				<Value></Value>
				<Value>%</Value>
				<Value>px</Value>
			</Values>
		</Option>
		<Option name="Wrap" id="wrap" arity="ONE">
			<Description>Allows panels to be placed on several rows if the available width is too small.</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="hasRefinePanel" name="Has refine panel" arity="ONE">
			<Description>This layout has collapsible refine panel. (If yes, last columns considered as refine panel and related expand/collapse buttons are generated automatically)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
			<Functions>
				<Display>ToggleDisplay({valueToMatch:['true'], showOptions:['refinesContainerMaxWith', 'displaytHideButton']})</Display>
			</Functions>
		</Option>
		<Option id="displayOpenButton" name="Display open refine panel button" arity="ONE">
			<Description>Display refine panel open button (not mandatory if dedicated refine panel toggle button widget is used in page)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="displayHideButton" name="Display hide refine panel button" arity="ONE">
			<Description>Display refine panel hide button (not mandatory if refine panel close button is activated)</Description>
			<Values>
				<Value>true</Value>
				<Value>false</Value>
			</Values>
		</Option>
		<Option id="refinesContainerMaxWith" name="Refines container max with" arity="ZERO_OR_ONE">
			<Description>Specifies the maximum width of refines container (in pixels), it will consider min value between flex div ratio and max width.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<Option id="buttonsIconSize" name="Icons font size" arity="ZERO_OR_ONE"
				isHighlighted="false" isUrlEncoded="false" isEvaluated="true"
				isXmlEscaped="false">
			<Description>Specifies buttons font size (in pixels). You must enter an integer. Default inherited.</Description>
			<Functions>
				<Check>isInteger</Check>
			</Functions>
		</Option>
		<OptionComposite id="variables" name="Additional CSS variables" arity="ZERO_OR_MANY" glue="##" isEvaluated="true">
			<Description>Add variables to widget div, it can be useful to configure CSS using variables for example.</Description>
			<Option id="name" name="Name" arity="ONE" isEvaluated="true">
				<Description>Variable name to add.</Description>
				<Functions>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
			<Option id="value" name="Value" arity="ONE" isEvaluated="true">
				<Description>Aggregation function expression.</Description>
				<Functions>
					<ContextMenu>COMMON_emptyOnChange()</ContextMenu>
				</Functions>
			</Option>
		</OptionComposite>
	</OptionsGroup>
	
	<DefaultValues>
		<DefaultValue name="wrap">false</DefaultValue>
		<DefaultValue name="hasRefinePanel">true</DefaultValue>
		<DefaultValue name="displayOpenButton">true</DefaultValue>
		<DefaultValue name="displayHideButton">true</DefaultValue>
		<DefaultValue name="refinesContainerMaxWith">450</DefaultValue>
	</DefaultValues>
</Widget>
