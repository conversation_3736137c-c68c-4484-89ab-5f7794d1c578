/**
 * Canvas Test Helper for debugging Chinese text rendering
 */

class CanvasTestHelper {
    constructor() {
        this.testCanvas = null;
    }

    /**
     * Test canvas rendering with Chinese text
     */
    testChineseRendering(text = "基于项目的评估") {
        console.log("=== CANVAS TEST START ===");
        console.log("Testing text:", text);

        // Create test canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Configuration
        const fontSize = 14;
        const fontWeight = 'bold';
        const fontFamily = '"Microsoft YaHei", "SimHei", "SimSun", "PingFang SC", "Hiragino Sans GB", sans-serif';
        
        // Set font and measure
        ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`;
        const metrics = ctx.measureText(text);
        
        console.log("Text metrics:", {
            width: metrics.width,
            actualBoundingBoxLeft: metrics.actualBoundingBoxLeft,
            actualBoundingBoxRight: metrics.actualBoundingBoxRight,
            actualBoundingBoxAscent: metrics.actualBoundingBoxAscent,
            actualBoundingBoxDescent: metrics.actualBoundingBoxDescent
        });
        
        // Set canvas size
        const padding = 6;
        const canvasWidth = Math.ceil(metrics.width) + padding * 2;
        const canvasHeight = Math.ceil(fontSize * 1.4) + padding * 2;
        
        canvas.width = canvasWidth;
        canvas.height = canvasHeight;
        canvas.style.border = '1px solid red';
        canvas.style.margin = '10px';
        
        // Re-apply styles
        ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`;
        ctx.fillStyle = '#000000';
        ctx.textBaseline = 'alphabetic';
        ctx.textAlign = 'left';
        
        // Draw text
        const textX = padding;
        const textY = canvasHeight - padding - (fontSize * 0.2);
        
        ctx.fillText(text, textX, textY);
        
        // Add to page for visual inspection
        const container = document.createElement('div');
        container.innerHTML = `
            <h3>Canvas Test: ${text}</h3>
            <p>Canvas size: ${canvasWidth} x ${canvasHeight}</p>
            <p>Text position: ${textX}, ${textY}</p>
        `;
        container.appendChild(canvas);
        
        // Add to body temporarily
        document.body.appendChild(container);
        
        // Convert to data URL
        const dataURL = canvas.toDataURL('image/png', 1.0);
        console.log("Data URL length:", dataURL.length);
        console.log("Canvas dimensions:", canvasWidth, "x", canvasHeight);
        
        // Clean up after 5 seconds
        setTimeout(() => {
            document.body.removeChild(container);
        }, 5000);
        
        console.log("=== CANVAS TEST END ===");
        
        return {
            canvas: canvas,
            dataURL: dataURL,
            width: canvasWidth,
            height: canvasHeight,
            textPosition: { x: textX, y: textY }
        };
    }

    /**
     * Test PDF positioning calculation
     */
    testPDFPositioning(canvasWidth, canvasHeight, pdfX, pdfY) {
        console.log("=== PDF POSITIONING TEST ===");
        
        // Convert canvas pixels to PDF points (72 DPI)
        const pdfWidth = canvasWidth * 0.75;
        const pdfHeight = canvasHeight * 0.75;
        
        // Calculate adjusted Y position
        const adjustedY = pdfY - (pdfHeight * 0.7);
        
        console.log("Canvas size:", canvasWidth, "x", canvasHeight, "px");
        console.log("PDF size:", pdfWidth, "x", pdfHeight, "pt");
        console.log("Original PDF position:", pdfX, pdfY);
        console.log("Adjusted PDF position:", pdfX, adjustedY);
        
        console.log("=== PDF POSITIONING TEST END ===");
        
        return {
            pdfWidth: pdfWidth,
            pdfHeight: pdfHeight,
            adjustedX: pdfX,
            adjustedY: adjustedY
        };
    }

    /**
     * Compare different font families
     */
    testFontFamilies(text = "基于项目的评估") {
        const fontFamilies = [
            '"Microsoft YaHei"',
            '"SimHei"',
            '"SimSun"',
            '"PingFang SC"',
            '"Hiragino Sans GB"',
            'sans-serif',
            '"Microsoft YaHei", "SimHei", "SimSun", sans-serif'
        ];

        console.log("=== FONT FAMILY TEST ===");
        
        fontFamilies.forEach((fontFamily, index) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            ctx.font = `bold 14px ${fontFamily}`;
            const metrics = ctx.measureText(text);
            
            canvas.width = Math.ceil(metrics.width) + 12;
            canvas.height = 30;
            
            ctx.font = `bold 14px ${fontFamily}`;
            ctx.fillStyle = '#000000';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, 6, 15);
            
            const container = document.createElement('div');
            container.innerHTML = `<p>Font ${index + 1}: ${fontFamily}</p>`;
            container.appendChild(canvas);
            container.style.margin = '5px';
            container.style.border = '1px solid #ccc';
            container.style.padding = '5px';
            
            document.body.appendChild(container);
            
            console.log(`Font ${index + 1} (${fontFamily}):`, {
                width: metrics.width,
                canvasSize: `${canvas.width}x${canvas.height}`
            });
            
            // Clean up after 10 seconds
            setTimeout(() => {
                if (document.body.contains(container)) {
                    document.body.removeChild(container);
                }
            }, 10000);
        });
        
        console.log("=== FONT FAMILY TEST END ===");
    }
}

// Make available globally
window.CanvasTestHelper = CanvasTestHelper;

// Auto-test when loaded
document.addEventListener('DOMContentLoaded', function() {
    // Uncomment to run automatic tests
    // const tester = new CanvasTestHelper();
    // tester.testChineseRendering();
    // setTimeout(() => tester.testFontFamilies(), 1000);
});
