var Note = function (uCssId, options) {
	this.uCssId = uCssId;
	this.widget = $('.' + uCssId);
	this.gridstackContainer = this.widget.find('.note-layout.grid-stack');
	this.noteStorageKey = 'note_' + uCssId;
	this.chartboardStorageManager = new ChartboardStorageManager();
	this.options = options;

	this.init();
};

Note.prototype.init = function () {
	/* Init gridStack Main container */
	this.gridstack = this.gridstackContainer.gridstack({
		cellHeight: this.options.baseY,
		verticalMargin: this.options.marginY,
		float: true,
		acceptWidgets: '.custom-note'
	}).data('gridstack');


	this.widget.find('.action-container').on('click', $.proxy(function (e) {
		this.gridstack.addWidget($(e.currentTarget).find('.custom-note').addClass('isNew')[0], 0, 0, 4, 2, true);
	}, this));

	this.initDragDrop();

	/* Bind the click action on editable widgets pencil to focus them */
	this.widget.find('.editable-custom-widget-icon').on('click', $.proxy(function (e) {
		this.focusEditableContent(e);
	}, this));

	this.initListenNotes();

	this.initRichTextEditor();
};

Note.prototype.initRichTextEditor = function () {
	this.richTextEditor = new NoteEditor(this.widget, {
		user: this.options.user,
		tinymceUrl: this.options.tinymceUrl,
		noteWidget: this
	});

	//upload tinymce if it isn't
	this.richTextEditor.loadScript();
};

Note.prototype.initDragDrop = function () {
	/* Moving widgets */
	this.gridstackContainer.on('dragstart', $.proxy(this.onDragStart, this));
	this.gridstackContainer.on('dragstop', $.proxy(this.onDragStop, this));

	/* Delete */
	this.widget.find('.delete-note').droppable({
		accept: '.grid-stack-item',
		tolerance: 'pointer',
		drop: $.proxy(function (event, ui) {
			this.onTrashDrop(event, ui);
		}, this)
	});

	this.gridstack.container.on('added', $.proxy(function (e, i) {
		this.onAdded(e, i);
	}, this));
};

Note.prototype.onAdded = function (event, items) {
	for (var i = items ? items.length : 0; i > 0; i--) {
		var $e = $(items[i - 1].el);

		if ($e.hasClass('custom-note')) {
			this.transformNote($e);
		}
		this.onDragStop();
	}

	this.saveNotes();
	this.listenNoteChangeHtml($e.find('.tile .content')[0], $.proxy(function () {
		this.saveNotes();
	}, this));
};

Note.prototype.onTrashDrop = function (event, ui) {
	var $tile = $(ui.draggable).closest('.grid-stack-item');
	this.gridstack.removeWidget($tile, true);
	this.widget.removeClass('dragging');
};

Note.prototype.onDragStart = function (event, ui) {
	this.widget.addClass('dragging');
};

Note.prototype.onDragStop = function (event, ui) {
	this.widget.removeClass('dragging');
};

Note.prototype.transformNote = function ($element) {
	/* Clean the $element from useless resize handles: only keep the first handle element,
	   The other ones don't work */
	$element.find('.ui-resizable-handle').detach().first().appendTo($element);

	/* Clone the widget and add it inside the custom gridstack */
	var clone = $element.clone();
	var x = clone.data('cell-cssclass').indexOf('title-widget') > -1 ? 1 : 0;
	this.widget.find('.add-note').append(clone);
	clone.find('.ui-resizable-handle').remove();

	$element.find('.editable-custom-widget-icon').on('click', $.proxy(function (e) {
		this.focusEditableContent(e);
	}, this));
	this.focusEditableContent($element[0]);
};

Note.prototype.focusEditableContent = function (e) {
	var elt = $(e.target).siblings('.content');
	if (!e.target) {
		elt = $(e).find('.content');
	}
	this.richTextEditor.openRichtextPopup(elt);
};

Note.prototype.saveNotes = function () {
	var config = this.getConfig();

	this.chartboardStorageManager.saveNote(config, this.noteStorageKey, $.proxy(function () {

	}, this), $.proxy(function () {

	}, this));
};

Note.prototype.getConfig = function () {
	this.widget.find('.note-layout .grid-stack-item.grid-stack-placeholder').remove();
	var serializedCells = _.map(this.widget.find('.note-layout .grid-stack-item'), function (el) {
		el = $(el);
		var node = el.data('_gridstack_node') || {x: 0, y: 0, width: 1, height: 1};
		var preview = el.find('.content')[0].innerHTML;

		return {
			cellId: el.data('cell-id') || "",
			x: node.x,
			y: node.y,
			width: node.width,
			height: node.height,
			cssClass: el.data('cell-cssclass') || "",
			cssId: el.attr('id') || "",
			displayed: true,
			preview: preview
		};
	}, this);

	return {tiles: serializedCells};
};

Note.prototype.initListenNotes = function () {
	this.widget.find('.custom-note .tile .content').each($.proxy(function (i,e) {
		this.listenNoteChangeHtml(e, $.proxy(function () {
			this.saveNotes();
		}, this));
	}, this));

	this.gridstack.container.on('change', $.proxy(function () {
		this.saveNotes();
	}, this));
};

Note.prototype.listenNoteChangeHtml = function (note, changeCallback) {
	/* Options for the observer (which mutations to observe) */
	var config = {attributes: false, childList: true, subtree: false};

	/* Callback function to execute when mutations are observed */
	var callback = $.proxy(function (mutationsList, observer) {
		for (var i = 0; i < mutationsList.length; i++) {
			var mutation = mutationsList[i];
			if (mutation.type === 'childList') {
				/* callback */
				changeCallback.call();

				break;
			}
		}
	}, this);

	/* Create an observer instance linked to the callback function */
	var observer = new MutationObserver(callback);

	/* Start observing the target node for configured mutations */
	if (note) {
		observer.observe(note, config);
	}
};
