<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget" varFeeds="feeds" varParentEntry="parentEntry" varParentFeed="parentFeed"/>

<config:getOption name="title" var="title" defaultValue="" doEval="true"/>
<config:getOption name="collapseByDefault" var="collapseByDefault" defaultValue="false"/>
<config:getOption name="saveState" var="saveState" defaultValue="false"/>
<config:getOption name="iconCss" var="iconCss"/>
<config:getOption name="iconCssCollapsed" var="iconCssCollapsed" />
<config:getOption name="duration" var="duration"/>
<config:getOptionsComposite name="additionalParams" var="additionalParams" mapIndex="true" isJsEscaped="true"/>

<widget:getUcssId var="uCssId"/>
<c:set var="collapseState" value="${collapseByDefault}"/>
<c:if test="${saveState == true}">
	<request:getCookieValue var="collapseState" name="collapse_state_${uCssId}" defaultValue="${collapseByDefault}"/>
</c:if>

<widget:widget extraCss="collapsibleBlock ${collapseState == 'true'? 'collapsed':'uncollapsed'}">
	<widget:header extraCss="collapse-trigger">
	    ${title}
		<div class="spinner-container hidden"><div class="spinner"></div></div>
        <span class="collapse-icon ${iconCss}"></span>
        <span class="uncollapse-icon ${iconCssCollapsed}"></span>
	</widget:header>
    <widget:content extraCss="collapse-content">
        
        <%-- Should the subwidget be rendered ? --%>
        <c:set var="renderSubwidgets" value="${collapseState != 'true'}"/>
        <c:if test="${not renderSubwidgets}">
	        <%-- Check if uncollapsing has been requested in ajax --%>
	        <request:isAjax var="isAjax" />
	        <c:if test="${isAjax}">
	            <request:getParameterValues var="uncollapseValues" name="collapsibleBlock_uncollapse"/>
	            <c:forEach var="value" items="${uncollapseValues}">
	                <c:if test="${value==uCssId}"><c:set var="renderSubwidgets" value="${true}"/></c:if>
	            </c:forEach>
	        </c:if>
        </c:if>
		
		<c:if test="${renderSubwidgets}">
            <render:subWidgets></render:subWidgets>
        </c:if>
    </widget:content>
</widget:widget>

<render:renderScript position="READY">
new CollapsibleBlock('${uCssId}', {
    isCollapsedByDefault: ${collapseState},
	<c:if test="${saveState}">
		stateCookieKey: 'collapse_state_${uCssId}',
	</c:if>
    duration: ${duration},
    isContentLoaded: ${renderSubwidgets},
    ajaxParams:{
        <c:forEach items="${additionalParams}" var="additionalParam">
        '${additionalParam.paramName}':'${additionalParam.paramValue}',
        </c:forEach>
    },
	tooltip:{
		expanded: '<i18n:message code="widget.action.collapse"/>',
		collapsed: '<i18n:message code="widget.action.expand"/>'
	},
	usedFeeds: ${plma:toJSArray(plma:getUsedFeeds(parentFeed, widget))},
	addFeedsParam : ${plma:getBooleanParam(widget, 'addUsedFeeds', true )}
});
</render:renderScript>