@import "../../plmaResources/css/polyfills.less";
@import "../../plmaResources/css/styles/variables.less";
@import "../../plmaResources/css/styles/colorPreferences.less";

.bookmarks-widget{
    position: relative;
    display: inline-flex;
    flex-direction: row-reverse;
    cursor: pointer;

    .bookmarkSearchContainer{
        .display-flex();
        .align-items(center);

        .query-bookmark {
            cursor: pointer;
            font-size: @m-font;
        }

        .bookmark-add-input {
            //margin-left: -45px;
            //display: none;
        }

        #bookmarks-hide-list {
            //margin-left: -25px;
            //display: none;
            color: @error;
        }

        #bmkInput {
            width: 300px;
        }

        .bookmarks-list{
            .display-flex();
            font-size: @m-font;
            position: absolute;
            top: 35px;
            flex-direction: column;
            z-index: 100;
            border: 1px solid @cblock-border;
            background-color: @cblock-bg;
            margin-left: 25px;
            border-top: none;
            width: 300px;

            &.hidden{
                display: none;
            }

            .bookmark-delete:hover{
                color: @error;
            }

            .bookmark-name {
                cursor: pointer;
            }

            .bookmark-item {
                padding: 8px 2px;
                font-size: 16px;
                border-bottom: 1px solid #e2e4e3;
                border-left: 5px solid #ffffff;
                //padding: 5px;

                &.bookmark-selected {
                    border-left: 5px solid @clink;
                    //color: white;
                    //background-color: @ctext-weak;
                }

                &:hover {
                    background: #f4f5f6;
                    border-left: 5px solid @ctext-bold;
                }

                &:last-child {
                    border-bottom: none;
                }
                /*
                &:hover{
                    background-color: @ctext-weaker;
                    color: @ctext-bold;
                }
                 */
            }
        }
    }
}