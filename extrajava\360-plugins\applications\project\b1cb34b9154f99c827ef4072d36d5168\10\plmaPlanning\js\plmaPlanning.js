(function (window) {
	'use strict';

	const DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss';
	
	var PLMAPlanningNew = function (uCssId, options) {
		this.uCssId = uCssId;
		this.options = options;
		this.plmaConfigOptions = $.extend({
			timespanDateFormat: 'YYYY-MMM-DD',
		}, this.options.timelineConfigOpts.plma);
		delete this.options.timelineConfigOpts.plma; // Delete as visjs can;t understand this option.
		
		this.widget = $('.' + this.uCssId);
		this.timelineData = {};
		this.timelineData.bounds = {};
		this.timelineData.groups = new vis.DataSet();
		this.timelineData.items = new vis.DataSet();
		
		this.widget.data('widget',this);
		this.init();
	}
	
	PLMAPlanningNew.getMessage = function(code) {
		return mashupI18N.get('plmaPlanning', code);
	}
	
	PLMAPlanningNew.prototype.init = function () {
		this.configurator = new TimelineConfigurator(this.uCssId, this.options.uniqueKey, this.options.userOptions);		
		this.activateTimeline();
		this.initButtons();		
	}
	
	PLMAPlanningNew.prototype.enableExport = function(options) {
		this.exporter = new PLMAPlanningExporter(this, options);
	}
	
	PLMAPlanningNew.prototype.initButtons = function () {
		/* Init Buttons */
		this.buttonManager = new WidgetButtonManager(this.uCssId, 'menu', '> .widgetHeader');
		if (this.options.buttons.buttons.length > 0) {
			for (var i = 0 ; i< this.options.buttons.buttons.length ; i++) {
				var button = this.options.buttons.buttons[i];
				if (!this.buttonManager.hasButton(button.icon, button.label)) {
					var $button = this.buttonManager.addButton(button.icon, button.label, button.action);
					if (button.icon.includes(FullScreenWidget.FULL_SCREEN_ICON_CSS_CLASS)) {
						this.widget.fullScreenWidget({
							button: $button
						});
					}
				}
			}
		}
		this.activatePanels();
		
		var $loadData = this.widget.find('.loadData');
		var curpage = $loadData.attr('data-currentpage'); 
		// Set Default Label. The actual values will be updaed in updatePaginationInfo
		this.widget.find('.loadData .label').text((function(){
			return this.options.loadLabel
				.replace('{currentpage}',curpage)
				.replace('{lastpage}', curpage);
		}.bind(this))());
		this.widget.find('.loadData .loadNextDataButton').on('click', function () {
			this.loadData('next', $loadData);
		}.bind(this));
		this.widget.find('.loadData .loadAllDataButton').on('click', function () {
			this.loadData('all', $loadData);
		}.bind(this));
		
		// Load Inital data if no data(curpage==0 and forceFirstLoad is true.
		if(this.options.forceFirstLoad && curpage == 0){
			this.loadData('init', $loadData);
		}
		
		this.widget.find('.moveLeftButton').on('click', function () {
			this.moveLeft();
		}.bind(this));
		this.widget.find('.moveRightButton').on('click', function () {
			this.moveRight();
		}.bind(this));
		this.widget.find('.zoomInButton').on('click', function () {
			this.zoomIn();
		}.bind(this));
		this.widget.find('.fitButton').on('click', function () {
			this.fit();
		}.bind(this));
		this.widget.find('.zoomOutButton').on('click', function () {
			this.zoomOut();
		}.bind(this));
	}
	
	PLMAPlanningNew.prototype.activatePanels = function () {
		this.widget.find('.left-panel-button .configurator-button').on('click', function () {
			this.widget.find('.timeline-configurator').removeClass('hidden');
			this.widget.find('.timeline-doc').addClass('hidden');
			this.widget.find('.left-panel').removeClass('hidden');
		}.bind(this));
	
		this.widget.find('.left-panel-button .timelinedoc-button').on('click', function () {
			this.widget.find('.timeline-doc').removeClass('hidden');
			this.widget.find('.timeline-configurator').addClass('hidden');
			this.widget.find('.left-panel').removeClass('hidden');
		}.bind(this));
		
		this.widget.find('.left-panel .close-button').on('click', function () {
			this.widget.find('.left-panel').addClass('hidden');
		}.bind(this));
		
		this.widget.find('.right-panel .close-button').on('click', function () {
			this.widget.find('.right-panel').addClass('hidden');
			this.widget.find('.selected').removeClass('selected');
		}.bind(this));
	}
	PLMAPlanningNew.prototype.closeAllPanels = function () {
		this.widget.find('.left-panel').addClass('hidden');
		this.widget.find('.right-panel').addClass('hidden');
	}
	
	PLMAPlanningNew.prototype.getTimelineOptions = function () {
		let minorLabels = {
			millisecond:'SSS',
			second:     's',
			minute:     'HH:mm',
			hour:       'HH:mm',
			weekday:    'ddd D',
			day:        'D',
			week:       '[WK-]ww',
			month:      'MMM',
			year:       'YYYY'
		  };
		  
		// Override in order DEFAULT -> CONFIG -> USER options.
		return $.extend({
			editable: false,				// Allows to drag items on the timeline
			selectable: false,
			zoomKey: 'ctrlKey',
			stack: false,
			stackSubgroups: false,
			height: '100%',					// height of timeline
			width: '100%',					// width of timeline
			verticalScroll: this.options.hasGroups,
			configure: this.configurator.getSetupOptions(),
			orientation: {
				axis: 'top',
				item: 'top'
			},
			margin:{
				axis: 5,
				item:{
					vertical: 10,
				},
			},
			showWeekScale: true,
			format:{
				minorLabels:function(date,scale, step){
					if(scale == 'month' && step == 3){
						return moment(date).format('[Q-]Q');
					}
					return moment(date).format(minorLabels[scale]);
				}
			}
		}, this.options.timelineConfigOpts, this.configurator.getTimelineUserOptions())
	}
	
	PLMAPlanningNew.prototype.activateTimeline = function () {
		// DOM element where the Timeline will be attached
		var container = document.getElementById('timeline-vis-' + this.uCssId);

		// Configuration for the Timeline
		var options = this.getTimelineOptions();
		
		// Create a Timeline
		if(this.options.hasGroups == false) {
			this.timeline = new vis.Timeline(container, this.timelineData.items, options);
		}else {
			this.timeline = new vis.Timeline(container, this.timelineData.items, options, this.timelineData.groups);
		}
		
		this.configurator.setup(this.timeline);		
		
		// Attach custom events supplied
		this.options.actions.forEach(function(action){
			this.timeline.on(action.name, action.handler);
		}.bind(this));
		
		this.timeline.on('changed', function (e) {
			if(this.options.fitNeeded == true){
				this.timeline.fit({animation:false});
				this.options.fitNeeded = false;
			}
		}.bind(this));
		
		this.timeline.on('rangechanged', function (e) {
			this.onChangeRange(e)
		}.bind(this));
		
		if(this.options.detailsWidgetId){
            this.timeline.on('click', function (e) {
				let hitId = undefined;
				let idClass = undefined;
				switch(e.what){
					case 'item':
						let item = this.timelineData.items.get(e.item);
						idClass = item.idClass;
						hitId = item.hitId;
						break;
					/*case 'group-label':
						let group = this.timelineData.groups.get(e.group);
						hitId = group.hitId;
						break;*/
					default:
						return;
				}
                if(!hitId){
                    throw Error('Please configure item.hitId in itemDef');
                }
                this.widget.find('.right-panel').removeClass('hidden');
                this.widget.find('.selected').removeClass('selected');
				if(idClass){
					this.widget.find('.' + idClass.replace('-est','').replace('-act','')).addClass('selected');
				}
				var option = {baseUrl: this.options.baseAjaxReload};
                var ajaxClient = new PlmaAjaxClient($("body"), {option});
                ajaxClient.addParameter("hit", hitId, true);
                ajaxClient.addWidget(this.options.detailsWidgetId, true, false);
                ajaxClient.update().then(function(){

                }.bind(this));
            }.bind(this));
        }
		
		if(options.zoomKey && options.moveable != false){
			this.widget.find('.timeline-vis .vis-panel.vis-center .vis-content').on('mousewheel', function(e){
				let isZoomKeyPressed = false;				
				switch(this.timeline.options.zoomKey){
				case 'altKey':
					isZoomKeyPressed = e.altKey;
					break;
				case 'ctrlKey':
					isZoomKeyPressed = e.ctrlKey;
					break;
				case 'shiftKey':
					isZoomKeyPressed = e.shiftKey;
					break;
				case 'metaKey':
					isZoomKeyPressed = e.metaKey;
					break;
				default:
					isZoomKeyPressed = true;
				}
				if(!isZoomKeyPressed && !this.widget.data('isZoomHelp')){
					$.notify(PLMAPlanningNew.getMessage('widget.plma.planning.zoom.help')
						.replace('{zoomKey}', this.timeline.options.zoomKey), 'warn');
					this.widget.data('isZoomHelp', true);	// Display the messge once per page refresh.			
				}			
			}.bind(this));
		}
	};
	
	PLMAPlanningNew.prototype.onChangeRange = function (e) {
		this.startDate = moment(e.start).format(this.plmaConfigOptions.timespanDateFormat);
		this.endDate = moment(e.end).format(this.plmaConfigOptions.timespanDateFormat);
		
		this.widget.find('.timespan-info.frame .timespan').text('[' + this.startDate + ' to ' + this.endDate + ']');
	};
		
	PLMAPlanningNew.prototype.zoomIn = function () {
		var range = this.timeline.getWindow();
		var interval = range.end - range.start;

		this.startDate = moment(range.start.valueOf() + interval * 0.25).format(DATE_FORMAT);
		this.endDate = moment(range.end.valueOf() - interval * 0.25).format(DATE_FORMAT);
		this.timeline.setWindow({
			start: this.startDate,
			end: this.endDate
		});
	};
	
	PLMAPlanningNew.prototype.fit = function () {
		this.timeline.fit({});
	};

	PLMAPlanningNew.prototype.zoomOut = function () {
		var range = this.timeline.getWindow();
		var interval = range.end - range.start;
		this.extendMove = "";

		this.startDate = moment(range.start.valueOf() - interval * 0.5).format(DATE_FORMAT);
		this.endDate = moment(range.end.valueOf() + interval * 0.5).format(DATE_FORMAT);

		if (this.startDate < this.minDate) {
			//Get new objects from access api and add them to the timeline
			this.extendMove = "left";
			this.minDate = this.startDate
		}
		if (this.endDate > this.maxDate) {
			//Get new objects from access api and add them to the timeline
			this.extendMove = "right";
			this.maxDate = this.endDate
		}

		this.timeline.setWindow({
			start: this.startDate,
			end: this.endDate
		});
	};
	
	PLMAPlanningNew.prototype.moveRight = function () {
		var range = this.timeline.getWindow();
		var interval = range.end - range.start;

		this.startDate = moment(range.start.valueOf() + interval * 0.5).format(DATE_FORMAT);
		this.endDate = moment(range.end.valueOf() + interval * 0.5).format(DATE_FORMAT);
		if (this.endDate > this.maxDate) {
			//Get new objects from access api and add them to the timeline
			this.maxDate = this.endDate
		}
		this.timeline.setWindow({
			start: this.startDate,
			end: this.endDate
		});
	};

	PLMAPlanningNew.prototype.moveLeft = function () {
		var range = this.timeline.getWindow();
		var interval = range.end - range.start;

		this.startDate = moment(range.start.valueOf() - interval * 0.5).format(DATE_FORMAT);
		this.endDate = moment(range.end.valueOf() - interval * 0.5).format(DATE_FORMAT);
		if (this.startDate < this.minDate) {
			//Get new objects from access api and add them to the timeline
			this.minDate = this.startDate
		}

		this.timeline.setWindow({
			start: this.startDate,
			end: this.endDate
		});
	};
	
	PLMAPlanningNew.prototype.updatePaginationInfo = function(paginationInfo) {
		let $button = this.widget.find('.loadData');
		if(paginationInfo.perpage == -1){
			paginationInfo.currentpage = paginationInfo.lastpage = 1;
		}
		
		$button.attr('data-currentpage', paginationInfo.currentpage);
		if(paginationInfo.currentpage >= paginationInfo.lastpage ){
			$button.find('span.label').text(
				this.options.loadLabel
					.replace('{currentpage}', paginationInfo.total)
					.replace('{lastpage}',paginationInfo.total));
			$button.find('span.fonticon:not(.label)').addClass('hidden');
			$button.off('click');
		}else{
			$button.find('span.label').text(
			this.options.loadLabel
				.replace('{currentpage}',paginationInfo.currentpage * paginationInfo.perpage)
				.replace('{lastpage}',paginationInfo.total));
		}
	};
	
	PLMAPlanningNew.prototype.loadData = function(what, $button) {
		this.widget.find('.widgetContent').showPLMASpinner({overlay: true});
		
		// Construct the final URL & Apply the loadUrlFilter to the BuildUrl object
		let finalUrl = new BuildUrl(this.options.loadUrl).setUrl(this.options.baseAjaxReload);
		let updatedUrl = this.options.loadUrlFilter(finalUrl) || finalUrl;
		let option = { baseUrl: updatedUrl.toString() };
		let ajaxClient = new PlmaAjaxClient($("body"), {option});

		var curpage = parseInt($button.attr('data-currentpage'));
		if(what === 'all'){
			ajaxClient.addParameter(this.options.paginationFeed + ".page", 1, true); // Page value starts from 1
			ajaxClient.addParameter(this.options.paginationFeed + ".per_page", -1, true);
		}
		else{
			ajaxClient.addParameter(this.options.paginationFeed + ".page", curpage+1, true);
			ajaxClient.addParameter(this.options.paginationFeed + ".per_page", this.options.hitsPerPage, true);
		}
		
		var additionalParams = new BuildUrl('?'+this.options.additionalParams).getParameters();
		Object.keys(additionalParams).forEach(function(param){
			ajaxClient.addParameters(param, additionalParams[param], true);
		});
		
		ajaxClient.addParameter("plmaPlanningDataReq", "true", true);
		ajaxClient.addWidget(this.options.wuid, false, false);
		
		ajaxClient.getWidget(function(widgets, pageAppendScript) {
			if(what === 'all'){
				this.timelineData.bounds = {};
				this.timelineData.groups.clear('load-all-data');
				this.timelineData.items.clear('load-all-data');
				this.options.fitNeeded = true;
			}else if(what === 'init'){
				this.options.fitNeeded = true;
			}			
			$('#mainWrapper').append(pageAppendScript);
			this.widget.find('.widgetContent').hidePLMASpinner();
		}.bind(this));
	}

	PLMAPlanningNew.prototype.update = function(items, groups, bounds){
		if(this.options.hasGroups){
			var updatedGroupIds = this.timelineData.groups.update(groups);
		}
		var updatedItemIds = this.timelineData.items.update(items);
		
		if(!this.timelineData.bounds.min || this.timelineData.bounds.min > bounds.min){
			this.timelineData.bounds.min = bounds.min;
		}
		if(!this.timelineData.bounds.max || this.timelineData.bounds.max < bounds.max){
			this.timelineData.bounds.max = bounds.max;
		}
	}
	
	PLMAPlanningNew.utils = {
		DATE_FORMAT: DATE_FORMAT,
		getVisDateStr: function(dateStr){
			return dateStr.replaceAll('/', '-')
		},
		/* This is required for Health(issue, risk, assessment) pages.
		e.g. if user apply refines on related_program and current, then these 
		refines are for the issue/risk/assessment object. Refine on 'current' should 
		not be applied to project feed with bo_type=project_space, when quering the timeline
		data.
		Hence we transfer all the refines to 'related_data' feed(which is bringing issue/risk/assessment)
		and only keep related_program refine for main 'project'(bo_type=project_space) feed.
		*/
		filterRefines: function (pageLoadUrl, mainFeed, subFeed, mainFeedRefines) {
			let filteredUrl = new BuildUrl();
			Object.keys(pageLoadUrl.getParameters()).forEach(function (param) {
				if (param === (mainFeed + '.r') || param === (mainFeed + '.zr')) {
					let paramValues = pageLoadUrl.getParameter(param);
					
					// transfer(issue specific refines) to related_data feed.
					let relatedParam = subFeed + (param === (mainFeed + '.r')?'.r':'.zr');
					filteredUrl.addParameters(relatedParam, paramValues, true);

					// Filter only refines to transfer to main Feed.
					paramValues = paramValues.filter(function(v){ 
						return mainFeedRefines.includes(v.split('/')[1]); 
					});
					filteredUrl.addParameters(param, paramValues, true);
				}
			});
			return filteredUrl;
		},
	}
	
	window.PLMAPlanningNew = PLMAPlanningNew;
})(window);

