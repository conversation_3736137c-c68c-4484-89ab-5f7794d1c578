var ChartSerieConfiguration = function (cssId, options) {
	this.$widget = $('#' + cssId);
	this.options = options;

	/* This key is suffixed with the page Name */
	/* Each chart configuration from the page is stored in a unique storage entry */
	this.CHART_CONFIGURATION_STORAGE_KEY = "chart_state_";

	/**
	 * @chartsConfiguration : this object is used to save charts configuration inside storage. (array of object)
	 *     @serie : array of shown serie (serie name).
	 *     @operator : an object containing the operator name (e.j 'MEDIAN', 'AVERAGE') and array of series (serie name)
	 * which display this operator. (e.j {'MEDIANE':["serie1", "serie2"], 'AVERAGE':["serie3"]})
	 *     @annotation : general annotation of the chart.
	 *     @legend : display legend "true"/"false"
	 *     @representations : an object with the serie names and their representation
	 * {chartsConfiguration :
     *      [{chartId : the_uCssId_of_plmaChart , configuration :
     *          {"serie" : [], "operator" : {}, "annotation" : "XXXXXXX", "legend" : "true",
     *          representations : {'serie1': 'bar', 'serie2' : 'line'}
     *          }
     *       }]
     */
	this.configurationSaver = {chartsConfiguration: []};


	this.OPERATORS = ["AVERAGE", "MEDIAN"];

	if (this.$widget.length === 0) {
		throw new Error('Unable to initialize widget : widget not found (ucssId: "' + cssId + '").');
	} else {
		this.init();
	}
};

ChartSerieConfiguration.prototype.init = function () {

	if (this.options.waitForTrigger === "true") {
		this.setConfiguration();
		$(document).off(".load-configuration");
		$(document).on("plma:chart-is-loaded.load-configuration", $.proxy(function (e) {
			this.detachWidgetEvent();
			var $chart = [];
			if ($(e.target).hasClass("plmaCharts")) {
				$chart = $(e.target);
			} else {
				if ($(e.target).hasClass("highcharts-container")) {
					$chart = $(e.target).closest(".plmaCharts");
				} else {
					//target is the flipContainer -> find plmaCharts inside
					$chart = $(e.target).find(".plmaCharts");
				}
			}
			if ($chart.length > 0) {
				var chartConfiguration = this.configurationSaver.chartsConfiguration.find(function (chartConfiguration) {
					return chartConfiguration.chartId === $chart.attr("class").split(" ")[1];
				}) || this.chartConfiguration;
				if (chartConfiguration !== undefined) {
					var uCssId = chartConfiguration.chartId;
					var chart = Highcharts.charts.find(function (chart) {
						return ChartSerieConfiguration.widgetUtils.getChartUcssIdFromContainerId($(chart.container).attr("id")) === uCssId
					});
					if (chart !== undefined) {
						this.initChartConfiguration(chart, chartConfiguration);
					}
				}
			}
			if ($chart.find(".widget-menu-container div.configuration-btn").length === 0) {
				this.createWidgetBtn($chart);
			}
		}, this));
	} else {
		$(document).ready($.proxy(function () {
			this.createWidgetsBtn();
			this.getChartConfiguration();
		}, this));
	}

	$(document).off("plma:lightbox-chart-configuration");
	$(document).on("plma:lightbox-chart-configuration", $.proxy(function (e, uCssId) {
		if (uCssId) {
			var $plmaChart = $("." + uCssId);
			if ($plmaChart.length === 0) {
				throw new Error('Chart could not be found... (ucssId: "' + uCssId + '").');
			}
			var chartContainerId = $plmaChart.find(".chart-inner .highChartsSVGWrapper .highcharts-container").attr("id");
			/* Redraw popup only if it is an other chart */
			if (uCssId !== this.uCssId || chartContainerId !== this.chartContainerId) {
				this.chartContainerId = chartContainerId;
				this.uCssId = uCssId;
				this.detachWidgetEvent();
				var charts = Highcharts.charts;
				for (var i = 0; i < charts.length; i++) {
					if (charts[i].container.getAttribute("id") === chartContainerId) {
						this.currentChart = charts[i];
						this.clearSeries();
						this.updateTitle($(charts[i].container));
						this.displaySeries("series-list", false);
						this.displayOperators("operator-series-list", true);
						this.setPopupConfiguration(this.uCssId, charts[i]);
					}
				}
				/* popup event handler */
				this.onSave();
				this.onRestore();
				this.disableEnableSeries();
				this.actionOnClickStacking();

				/* Set popup property from chart */
				this.addOperator();

				this.initLegendFromChart();
				this.initStackingFromChart();
				this.initRepresentationFromChart();
				this.initHideSerieFromChart();
				this.initOperatorsFromChart();
			}

			/* Reset popup scrolling */
			this.scrollToTop();
		}
	}, this));

	/* This event occurs when another page is displayed. In that case, operators have to be updated. */
	$(document).off(PLMAChart2.PAGINATION_EVENT);
	$(document).on(PLMAChart2.PAGINATION_EVENT, $.proxy(function (e, uCssId) {
		this.updateOperators(uCssId);
	}, this));

	$(document).off('plma:series-is-hidden');
	$(document).on('plma:series-is-hidden', $.proxy(function (e, container) {
		var containerId = $(container).attr('id');
		this.updateOperators(ChartSerieConfiguration.widgetUtils.getChartUcssIdFromContainerId(containerId));
	}, this));

	$(document).off('plma:series-is-shown');
	$(document).on('plma:series-is-shown', $.proxy(function (e, container) {
		var containerId = $(container).attr('id');
		this.updateOperators(ChartSerieConfiguration.widgetUtils.getChartUcssIdFromContainerId(containerId));
	}, this));
};

/**
 * detach all event (with namespace 'chart-configuration') on widget before attaching another time.
 */
ChartSerieConfiguration.prototype.detachWidgetEvent = function () {
	this.$widget.off(".chart-configuration");
};

/**
 * Create a configuration widget button for each chart
 */
ChartSerieConfiguration.prototype.createWidgetsBtn = function () {
	if ($(this.options.btnSelector).length > 0) {
		var self = this;
		$(this.options.btnSelector).each(function (i, item) {
			if ($(item).attr("class") && this.isClassicalChart($(item))) {
				self.addConfigurationBtn($(item), self.options.btnIcon);
			}
		}.bind(this));
	}
};

/**
 * Create an annotation widget button for chart
 * @param $chart
 */
ChartSerieConfiguration.prototype.createWidgetBtn = function ($chart) {
	if ($(this.options.btnSelector).length > 0) {
		if ($(this.options.btnSelector).index($chart) >= 0) {
			if ($chart.attr("class") !== "" && this.isClassicalChart($chart.find('.highcharts-container'))) {
				this.addConfigurationBtn($chart, this.options.btnIcon);
			}
		}
	}
};

/**
 * Verify if chart is not a pie or a waterfall chart
 */
ChartSerieConfiguration.prototype.isClassicalChart = function ($chart) {
	if ($chart.length > 0) {
		var charts = Highcharts.charts;
		for (var i = 0; i < charts.length; i++) {
			if (charts[i].container.getAttribute("id") === $chart.attr('id')) {
				var series = charts[i].series;
				for (var j = 0; j < series.length; j++) {
					var representation = series[j].type;
					if (representation === 'pie' || representation === 'waterfall') {
						return false;
					}
				}
			}
		}
	}

	return true;
};

ChartSerieConfiguration.prototype.addConfigurationBtn = function ($item, btnIcon) {
	var cssClasses = $item.attr("class").split(" ");
	if (cssClasses.length > 2) {
		var uCssId = $item.attr("class").split(" ")[1];
		var elementBtnSelector = this.options.popupButtonSelector;
		if (!this.verifyIfButtonExists(uCssId)) {
			var buttonManager = new WidgetButtonManager(uCssId, 'menu', '.headerChartContainer');
			buttonManager.addButton(btnIcon, mashupI18N.get('chartSerieConfiguration', 'chart.configuration.btn.label'),
				function () {
					$(elementBtnSelector).trigger('plma:lightbox-chart-configuration', uCssId);
				}, undefined, "configuration-btn");
		}
	}
};

ChartSerieConfiguration.prototype.verifyIfButtonExists = function (uCssId) {
	/* Verify if the button already exists or not */
	var menuItems = $('.' + uCssId + ' .headerChartContainer .widget-menu-container .widget-menu-item');
	for (var i = 0; i < menuItems.length; i++) {
		var $item = $(menuItems[i]);
		if ($item.hasClass('configuration-btn')) {
			return true;
		}
	}

	return false;
};

/**
 * Update lightbox title with chart title
 * @param $chartContainer
 */
ChartSerieConfiguration.prototype.updateTitle = function ($chartContainer) {
	var $plmaChart = $chartContainer.closest(".plmaCharts");
	var chartTitle = $plmaChart.find(".widgetChartHeader").text();
	if (chartTitle !== undefined) {
		this.$widget.find(".title").find(".title-text").text(mashupI18N.get("chartSerieConfiguration", "Edit") + " " + chartTitle);
	}
};

ChartSerieConfiguration.prototype.clearSeries = function () {
	this.$widget.find("ul.series-list li:not(:first-child)").remove();
	this.$widget.find("ul.operator-series-list li:not(:first-child)").remove();
};

/**
 *
 * @param cssClassSelector
 * @param addLegend
 */
ChartSerieConfiguration.prototype.displaySeries = function (cssClassSelector, addLegend) {
	var chartSeries = this.currentChart.series;
	for (var i = 0; i < chartSeries.length; i++) {
		var $serieTemplate = this.$widget.find("ul." + cssClassSelector).find("li.serie:first-child").clone();
		$serieTemplate.find("label").text(chartSeries[i].name);
		$serieTemplate.find("label").attr("for", $serieTemplate.find("label").attr("for") + "_" + i);
		$serieTemplate.find("input.plma-checkbox").attr("id", $serieTemplate.find("input.plma-checkbox").attr("id") + "_" + i);
		$serieTemplate.find("input.plma-checkbox").attr("value", chartSeries[i].options.id);
		$serieTemplate.find("input.plma-checkbox").attr("data-serie", chartSeries[i].options.id);
		$serieTemplate.removeClass("hidden");
		if (addLegend) {
			$serieTemplate.find(".checkbox-color-legend").css('background', chartSeries[i].color);
		}
		this.$widget.find("." + cssClassSelector).append($serieTemplate);
	}
};

ChartSerieConfiguration.prototype.displayOperators = function (cssClassSelector, addLegend) {
	var chartSeries = this.currentChart.series;
	for (var i = 0; i < chartSeries.length; i++) {
		var $serieTemplate = this.$widget.find("ul." + cssClassSelector).find("li.serie:first-child").clone();
		$($serieTemplate.find("label")[0]).text('Ave');
		$($serieTemplate.find("label")[0]).attr("for", $($serieTemplate.find("label")[0]).attr("for") + "_" + i);
		$($serieTemplate.find("input.plma-checkbox")[0]).attr("id", $($serieTemplate.find("input.plma-checkbox")[0]).attr("id") + "_" + i);
		$($serieTemplate.find("label")[1]).text('Med');
		$($serieTemplate.find("label")[1]).attr("for", $($serieTemplate.find("label")[1]).attr("for") + "_" + i);
		$($serieTemplate.find("input.plma-checkbox")[1]).attr("id", $($serieTemplate.find("input.plma-checkbox")[1]).attr("id") + "_" + i);
		$serieTemplate.find("input.plma-checkbox").attr("value", chartSeries[i].options.id);
		$serieTemplate.find("input.plma-checkbox").attr("data-serie", chartSeries[i].options.id);
		$serieTemplate.removeClass("hidden");
		if (addLegend) {
			$serieTemplate.find(".checkbox-color-legend").css('background', chartSeries[i].color);
		}
		this.$widget.find("." + cssClassSelector).append($serieTemplate);
	}
};

/**
 * Event handler that display or hide serie on change
 */
ChartSerieConfiguration.prototype.disableEnableSeries = function () {
	this.$widget.on("change.chart-configuration", "input.plma-checkbox.display-series", $.proxy(function (e) {
		var serieName = $(e.currentTarget).val();
        var serieIndex = ChartSerieConfiguration.widgetUtils.getSeriesIndex(this.currentChart, serieName);
        var serieId = ChartSerieConfiguration.widgetUtils.getSeriesId(this.currentChart, serieIndex);
		if ($(e.currentTarget).prop("checked")) {
			this.currentChart.series[serieIndex].show();
			this.saveConfiguration(serieId, undefined);
		} else {
			this.currentChart.series[serieIndex].hide();
			this.removeFromConfiguration(serieId, undefined);
		}
	}, this));
};

/**
 * Event handler that display or hide an operage ('median', 'avg')
 */
ChartSerieConfiguration.prototype.addOperator = function () {
	this.$widget.on("change.chart-configuration", "input.plma-checkbox.display-operators", $.proxy(function (e) {
		var serieName = $(e.currentTarget).val();
        var serieIndex = ChartSerieConfiguration.widgetUtils.getSeriesIndex(this.currentChart, serieName);
        var serieId = ChartSerieConfiguration.widgetUtils.getSeriesId(this.currentChart, serieIndex);
		var operator = $(e.currentTarget).data('operator');
		var currentSerie = this.currentChart.series[serieIndex];
		if ($(e.currentTarget).prop("checked")) {
			if (operator === "AVERAGE") {
				this.setAverage(currentSerie, this.currentChart);
			} else if (operator === "MEDIAN") {
				this.setMedian(currentSerie, this.currentChart);
			}
			this.saveConfiguration(serieId, operator);
		} else {
			this.removeOperator(currentSerie, operator);
			this.removeFromConfiguration(serieId, operator);
		}
	}, this));
};

/**
 * Update 'MEDIAN' and 'AVERAGE' operators.
 */
ChartSerieConfiguration.prototype.updateOperators = function (uCssId) {
	var cssId = uCssId.split('_')[0];
	var chartConfiguration = this.chartConfiguration || this.configurationSaver.chartsConfiguration.find(function (chartConfiguration) {
		return chartConfiguration.chartId === cssId;
	});
	if (chartConfiguration) {
		var chart = Highcharts.charts.find(function (chart) {
			return ChartSerieConfiguration.widgetUtils.getChartUcssIdFromContainerId($(chart.container).attr("id")) === cssId
		});
		if (chart) {
			this.drawOperators(chart, chartConfiguration);
		}
	}
};

/**
 * Event handler that display or hide legend
 */
ChartSerieConfiguration.prototype.displayLegend = function () {
	this.initLegend();

	this.actionOnClickLegend();
};

ChartSerieConfiguration.prototype.actionOnClickLegend = function () {
	this.$widget.on("change.chart-configuration", "input.plma-checkbox.display-legend", $.proxy(function (e) {
		$e = $(e.currentTarget);
		if ($e.is(':checked')) {
			this.showHideLegend(true, this.currentChart);
			this.chartConfiguration.configuration.legend = true;
		} else {
			this.showHideLegend(false, this.currentChart);
			this.chartConfiguration.configuration.legend = false;
		}
	}, this));
};

ChartSerieConfiguration.prototype.initHideSerieFromChart = function () {
	var series = this.currentChart.series;
	for (var i = 0; i < series.length; i++) {
		var visible = series[i].options.visible;
		$(this.$widget.find(".series-list .plma-checkbox-container")[i + 1]).find(".plma-checkbox.display-series").prop("checked", visible);
	}
};

ChartSerieConfiguration.prototype.initOperatorsFromChart = function () {
	var series = this.currentChart.series;

	for (var i = 0; i < series.length; i++) {
		var displayAverage = false;
		var displayMedian = false;

		for (var j = 0; j < this.currentChart.yAxis.length; j++) {
			var xAxis = this.currentChart.yAxis[j];
			for (var k = 0; k < xAxis.plotLinesAndBands.length; k++) {
				var plotLine = xAxis.plotLinesAndBands[k];
				if (plotLine.options.serieName === series[i].options.id && plotLine.options.operator === 'AVERAGE') {
					displayAverage = true;
				} else if (plotLine.options.serieName === series[i].options.id && plotLine.options.operator === 'MEDIAN') {
					displayMedian = true;
				}
			}

			$(this.$widget.find(".operator-series-list .serie")[i + 1]).find(".display-operators.average-operator").prop("checked", displayAverage);
			$(this.$widget.find(".operator-series-list .serie")[i + 1]).find(".display-operators.median-operator").prop("checked", displayMedian);
		}

	}
};

ChartSerieConfiguration.prototype.initLegend = function () {
	if (this.chartConfiguration.configuration.legend === true) {
		this.$widget.find("input.plma-checkbox.display-legend").attr("checked", true);
		this.showHideLegend(true, this.currentChart);
	} else if (this.chartConfiguration.configuration.legend === false) {
		this.showHideLegend(false, this.currentChart);
	}
};

ChartSerieConfiguration.prototype.initLegendFromChart = function () {
    this.$widget.find("input.plma-checkbox.display-legend").prop("checked", this.currentChart.options.legend.enabled?true:false);

	this.actionOnClickLegend();
};

ChartSerieConfiguration.prototype.actionOnClickStacking = function () {
	this.$widget.on("change.chart-configuration", ".stacking-container .stacking-select", $.proxy(function (e) {
		var $e = $(e.currentTarget);
		var stacking = $e.val();
		var containerId = $(this.currentChart.container).attr('id');
		if (stacking === "undefined") {
			stacking = undefined;
		}
		this.setStacking(stacking, this.currentChart);
		this.chartConfiguration.configuration.stacking = stacking;
		this.updateOperators(ChartSerieConfiguration.widgetUtils.getChartUcssIdFromContainerId(containerId));
	}, this));
};

ChartSerieConfiguration.prototype.initStacking = function () {
	if (!this.chartConfiguration.configuration.stacking) {
		this.$widget.find(".stacking-container .stacking-select").val("undefined");
		//this.setStacking(this.chartConfiguration.configuration.stacking, this.currentChart);
	} else if (this.chartConfiguration.configuration.stacking === "normal") {
		this.$widget.find(".stacking-container .stacking-select").val("normal");
		this.setStacking(this.chartConfiguration.configuration.stacking, this.currentChart);
	} else if (this.chartConfiguration.configuration.stacking === "percent") {
		this.$widget.find(".stacking-container .stacking-select").val("percent");
		this.setStacking(this.chartConfiguration.configuration.stacking, this.currentChart);
	}
};

ChartSerieConfiguration.prototype.initStackingFromChart = function () {
	var stacking = this.getStackingFromChart(this.currentChart);
	this.$widget.find('.stacking-container .stacking-select').val(stacking);
};

ChartSerieConfiguration.prototype.getStackingFromChart = function (chart) {
	var series = chart.series;
	var normalStackingCount = series.filter(function(series) {
		return series.options.stacking === 'normal';
	}).length;
	var percentStackingCount = series.filter(function(series) {
		return series.options.stacking === 'percent';
	}).length;
	var stacking = 'undefined';
	if (normalStackingCount && normalStackingCount >= percentStackingCount) {
		stacking = 'normal';
	} else if (percentStackingCount && percentStackingCount >= normalStackingCount) {
		stacking = 'percent';
	}
	return stacking;
};

ChartSerieConfiguration.prototype.setStacking = function (stacking, currentChart) {
	var currentFormatter = currentChart.tooltip.options.formatter;
	var newFormatter;
	$.each(currentChart.series, $.proxy(function (index, serie) {
		var currentTooltipFormat = serie.tooltipOptions.pointFormat;
		var currentYaxisLabel = serie.yAxis.axisTitle.textStr;
		serie.update({
			stacking: stacking,
			tooltip: {
				pointFormat: this.getNewTooltipFormat(stacking, currentTooltipFormat)
			}
		});
		serie.yAxis.setTitle({
			text: this.getNewYaxisLabel(stacking, currentYaxisLabel)
		});
	}, this));
	// change global formatter
	if (typeof currentFormatter !== 'undefined' && stacking) {
		var shared = false;
		if (currentChart && currentChart.tooltip && currentChart.tooltip.options && currentChart.tooltip.options.shared) {
			shared = currentChart.tooltip.options.shared;
		}
		var newFormatterString = this.getNewTooltipFormatter(
			stacking,
			currentFormatter.toString(),
			shared
		);
		eval('newFormatter = ' + newFormatterString);
		currentChart.update({
			tooltip: {
				formatter: newFormatter
			}
		});
	}
};

/**
 * Returns the next tooltip format based on both the current stacking mode and the tooltip format.
 */
ChartSerieConfiguration.prototype.getNewTooltipFormat = function (stackingMode, tooltipFormat) {
	var PERCENTAGE_STRING = ' ({point.percentage:.0f}%)';
	var newTooltipFormat = tooltipFormat;
	if (stackingMode === 'percent') {
		// Put percentage
		var index = tooltipFormat.lastIndexOf('<br />');
		if (index === -1) {
			index = tooltipFormat.lastIndexOf('<br/>');
		}
		if (index !== -1) {
			newTooltipFormat = tooltipFormat.slice(0, index) + PERCENTAGE_STRING + tooltipFormat.slice(index);
		}
	} else {
		// Remove percentage
		newTooltipFormat = tooltipFormat.replace(PERCENTAGE_STRING, '');
	}
	return newTooltipFormat;
};

/**
 * Returns a formatter (function as string) based on the stacking mode and the shared option.
 */
ChartSerieConfiguration.prototype.getNewTooltipFormatter = function (stackingMode, formatterString, shared) {
	var PERCENTAGE_STRING = shared
		? " (this.points[i].percentage ? ' (' + this.points[i].percentage + '%)' : '') + "
		: " (this.percentage ? ' (' + this.percentage + '%)' : '') + ";
	var newFormatterString = formatterString;
	var index = formatterString.lastIndexOf("'<br />'");
	if (index === -1) {
		index = formatterString.lastIndexOf("'<br/>'");
	}
	if (index !== 1) {
		if (stackingMode === 'percent') {
			// put percentage
			newFormatterString = formatterString.slice(0, index) + PERCENTAGE_STRING + formatterString.slice(index);
		} else {
			// remove percentage
			newFormatterString = formatterString.replace(PERCENTAGE_STRING, '');
		}
	}
	return newFormatterString;
};

/**
 * Returns the next Y axis label based on both the current stacking mode and the tooltip format.
 */
ChartSerieConfiguration.prototype.getNewYaxisLabel = function (stackingMode, yAxisLabel) {
	var percentageString = ' (% of total)';
	if (stackingMode === 'percent') {
		if (yAxisLabel.includes(percentageString)) {
			return yAxisLabel;
		} else {
			// Put percentage
			return yAxisLabel.concat(percentageString);
		}
	} else {
		// Remove percentage
		return yAxisLabel.replace(percentageString, '');
	}
};

/**
 * Event handler that change representation of all series to
 *  COLUMN / AREA / AREASPLINE / BAR / LINE / SPLINE
 */
ChartSerieConfiguration.prototype.actionChangeRepresentation = function () {
	this.$widget.on("change.chart-configuration", ".representation-container .representation-select", $.proxy(function (e) {
		var $e = $(e.currentTarget);
		var representation = $e.val();
		var serieName = $e.closest("li.serie").find(".plma-checkbox-container input.plma-checkbox").data("serie");
		this.setRepresentation(this.chartConfiguration, representation, serieName, this.currentChart);
		this.chartConfiguration.configuration.representations[serieName] = representation;
	}, this));
};

ChartSerieConfiguration.prototype.initRepresentations = function () {
	if (Object.keys(this.chartConfiguration.configuration.representations).length === 0) {
		var chart = this.currentChart;
		$.each(this.$widget.find(".representation-container .representation-select"), function (index, representationSelect) {
			var $representationSelect = $(representationSelect);
			var serieName = $representationSelect.closest(".serie").find(".plma-checkbox-container input.plma-checkbox").data("serie");
			$.each(chart.series, function (index, serie) {
				if (serie.options.id === serieName) {
					$representationSelect.val(serie.type);
				}
			});
		});
	}
};

ChartSerieConfiguration.prototype.setRepresentation = function (chartConfiguration, representation, seriesId, currentChart) {
	var series = ChartSerieConfiguration.widgetUtils.getSeriesById(currentChart, seriesId);
	if (series) {
		switch (representation) {
			case 'bar':
				if (!currentChart.inverted) {
					this.forceInvertAxis(currentChart, true);
				}
				series.update({ type: representation });
				this.afterAxesInversion(currentChart, true);
				break;
			case 'column':
				if (currentChart.inverted) {
					this.forceInvertAxis(currentChart, false);
				}
				series.update({ type: representation });
				this.afterAxesInversion(currentChart, false);
				break;
			default:
				series.update({ type: representation });
				break;
		}
	}
};

ChartSerieConfiguration.prototype.initRepresentationFromChart = function () {
	var series = this.currentChart.series;
	for (var i = 0; i < series.length; i++) {
		var representation = series[i].type;
		$(this.$widget.find(".series-list .representation-container")[i + 1]).find(".representation-select").val(representation);
	}

	this.actionChangeRepresentation();
};

ChartSerieConfiguration.prototype.invertAxis = function (currentChart, isInverted) {
	currentChart.update({
		chart: {
			inverted: isInverted
		}
	}, false);
};

ChartSerieConfiguration.prototype.forceInvertAxis = function (currentChart, isInverted) {
	currentChart.inverted = isInverted;
	for (var i = 0; i < currentChart.xAxis.length; i++) {
		currentChart.xAxis[i].update({}, false);
	}
	for (var i = 0; i < currentChart.yAxis.length; i++) {
		currentChart.yAxis[i].update({}, false);
	}
	currentChart.series.forEach(function(series) {
		series.update({}, false);
	});
	currentChart.update({}, false);
};

ChartSerieConfiguration.prototype.afterAxesInversion = function (currentChart, isInverted) {
	if (isInverted) {
		// columns to bars
		this.switchRepresentation('column', 'bar', mashupI18N.get('chartSerieConfiguration', 'representation.columnsToBars'));
	} else {
		// bars to columns
		this.switchRepresentation('bar', 'column', mashupI18N.get('chartSerieConfiguration', 'representation.barsToColumns'));
	}
};

/**
 * When representation "column" is selected, all the "bar" have to be converted into "column" as well
 * (same for the other way around).
 */
ChartSerieConfiguration.prototype.switchRepresentation = function (oldRepresentation, newRepresentation, message) {
	var $selects = this.$widget.find('select.representation-select:has(option[value="' + oldRepresentation + '"]:selected)');
	$selects.val(newRepresentation).change();
	if ($selects.length > 1) {
		$.notify(message, 'info');
	}
};

ChartSerieConfiguration.prototype.saveConfiguration = function (serieName, operatorName) {
	if (operatorName !== undefined) {
		if (this.chartConfiguration.configuration.operator[operatorName] === undefined) {
			this.chartConfiguration.configuration.operator[operatorName] = [serieName];
		} else {
			this.chartConfiguration.configuration.operator[operatorName].push(serieName);
		}
	} else {
		this.chartConfiguration.configuration.serie.push(serieName);
	}
};

ChartSerieConfiguration.prototype.removeFromConfiguration = function (serieName, operatorName) {
	if (operatorName !== undefined) {
		if (this.chartConfiguration.configuration.operator[operatorName] !== undefined) {
			this.chartConfiguration.configuration.operator[operatorName] =
				this.chartConfiguration.configuration.operator[operatorName].filter(function (item) {
					return item !== serieName
				});
		}
	} else {
		this.chartConfiguration.configuration.serie = this.chartConfiguration.configuration.serie.filter(function (item) {
			return item !== serieName
		});
	}
};

ChartSerieConfiguration.prototype.getChartConfiguration = function () {
	var storage = new StorageClient("user");
	var page = mashup.pageName;
	storage.get(this.CHART_CONFIGURATION_STORAGE_KEY + this.options.storageKey, $.proxy(function (items) {
		if (items.length > 0) {
			this.configurationSaver = JSON.parse(items[0].value);
			//init charts config
			for (var i = 0; i < this.configurationSaver.chartsConfiguration.length; i++) {
				var chartConfiguration = this.configurationSaver.chartsConfiguration[i];
				var uCssId = chartConfiguration.chartId;
				var chart = Highcharts.charts.find(function (chart) {
					return ChartSerieConfiguration.widgetUtils.getChartUcssIdFromContainerId($(chart.container).attr("id")) === uCssId
				});
				if (chart !== undefined) {
					this.initChartConfiguration(chart, chartConfiguration);
				}
			}
		}
	}, this));
};

ChartSerieConfiguration.prototype.setConfiguration = function () {
	var storage = new StorageClient("user");
	var page = mashup.pageName;
	storage.get(this.CHART_CONFIGURATION_STORAGE_KEY + this.options.storageKey, $.proxy(function (items) {
			if (items.length > 0) {
				this.configurationSaver = JSON.parse(items[0].value);
			}
		}, this),
		function () {
			//error callback
		});
};

/**
 * init chart with saved configuration
 * @param {Highcharts#Chart} chart - Highcharts chart object
 * @param chartConfiguration
 */
ChartSerieConfiguration.prototype.initChartConfiguration = function (chart, chartConfiguration) {
	/* Draw series */
	for (var i = 0; i < chart.series.length; i++) {
		var serie = chart.series[i];
		if (chartConfiguration.configuration.serie.indexOf(serie.options.id) === -1) {
			chart.series[i].hide();
		}
	}

	/* Legend */
	if (chartConfiguration.configuration.legend === true) {
		this.showHideLegend(true, chart);
	} else {
		this.showHideLegend(false, chart);
	}

	/* stacking */
	if (chartConfiguration.configuration.stacking === "undefined") {
		chartConfiguration.configuration.stacking = undefined;
	}
	this.setStacking(chartConfiguration.configuration.stacking, chart);

	/* Representation */
	if (chartConfiguration && chartConfiguration.configuration && chartConfiguration.configuration.representations && Object.keys(chartConfiguration.configuration.representations).length > 0) {
		var serieIds = Object.keys(chartConfiguration.configuration.representations);
		for (var i = 0; i < serieIds.length; i++) {
			this.setRepresentation(chartConfiguration, chartConfiguration.configuration.representations[serieIds[i]], serieIds[i], chart);
		}
	}

	/* Draw operators */
	this.drawOperators(chart, chartConfiguration);
};

/**
 *
 * @param {Highcharts#Chart} chart - Highcharts chart object
 * @param chartConfiguration
 */
ChartSerieConfiguration.prototype.drawOperators = function (chart, chartConfiguration) {
	for (var i = 0; i < chart.series.length; i++) {
		var serie = chart.series[i];
		for (var j = 0; j < this.OPERATORS.length; j++) {
			if (chartConfiguration.configuration.operator[this.OPERATORS[j]] !== undefined) {
				if (chartConfiguration.configuration.operator[this.OPERATORS[j]].indexOf(serie.options.id) > -1) {
					if (this.OPERATORS[j] === "MEDIAN") {
						this.setMedian(serie, chart);
					} else if (this.OPERATORS[j] === "AVERAGE") {
						this.setAverage(serie, chart);
					}
				}
			}
		}
	}
};

ChartSerieConfiguration.prototype.setPopupConfiguration = function (uCssId, chart) {
	var emptyConfiguration = true;
	for (var i = 0; i < this.configurationSaver.chartsConfiguration.length; i++) {
		var chartConfiguration = this.configurationSaver.chartsConfiguration[i];
		if (chartConfiguration.chartId === uCssId) {
			this.chartConfiguration = chartConfiguration;
			this.initPopupConfiguration(chartConfiguration, chart);
			emptyConfiguration = false;
			break;
		}
	}
	if (emptyConfiguration) {
		this.chartConfiguration = {
			chartId: uCssId,
			configuration: {
				"serie": [],
				"operator": {},
				"annotation": "",
				"legend": "",
				"stacking": undefined,
				"representations": {}
			}
		};
		for (var i = 0; i < chart.series.length; i++) {
			if (chart.series[i].visible) {
				this.$widget.find(".series-list").find("input.display-series.plma-checkbox[data-serie='" + chart.series[i].options.id + "']").prop("checked", true);
				this.chartConfiguration.configuration.serie.push(chart.series[i].options.id);
			}
		}
        this.chartConfiguration.configuration.legend = chart.legend.options.enabled === true ? true : false;
		this.chartConfiguration.configuration.stacking = this.getStackingFromChart(chart);

		/* Update annotation */
		this.$widget.find("textarea#annotation-text-bloc").val('');
	}
};

/**
 * init popup configuration with save configuration.
 * @param chart
 * @param chartConfiguration
 */
ChartSerieConfiguration.prototype.initPopupConfiguration = function (chartConfiguration, chart) {
	/* Update serie configuration */
	for (var i = 0; i < chartConfiguration.configuration.serie.length; i++) {
		var serieName = chartConfiguration.configuration.serie[i];
		var $checkbox = this.$widget.find(".series-list").find("input.display-series.plma-checkbox[data-serie='" + serieName + "']");
		if ($checkbox.length > 0) {
			$checkbox.prop("checked", true);
			/* Update Representation */
			if (chartConfiguration.configuration.representations.hasOwnProperty(serieName)) {
				$checkbox.closest("li.serie").find(".representation-container select.representation-select").val(chartConfiguration.configuration.representations[serieName]);
			}
		}
	}

	/* For custom query */
	for (var i = 0; i < chart.series.length; i++) {
		var serieName = chart.series[i].options.id;
		if (chart.series[i].visible) {
			var $checkbox = this.$widget.find(".series-list").find("input.display-series.plma-checkbox[data-serie='" + serieName + "']");
			if ($checkbox.length > 0) {
				$checkbox.prop("checked", true);
			}
		} else {
			var $checkbox = this.$widget.find(".series-list").find("input.display-series.plma-checkbox[data-serie='" + serieName + "']");
			if ($checkbox.length > 0) {
				$checkbox.prop("checked", false);
			}
		}
	}

	/* Update legend */
	if (chartConfiguration.configuration.legend === true) {
		var $checkbox = this.$widget.find(".legend-container").find(".plma-checkbox.display-legend");
		if ($checkbox.length > 0) {
			$checkbox.prop("checked", true);
		}
	}

	/* Update Stacking */
	if (chartConfiguration.configuration.stacking) {
		var $stackingSelect = this.$widget.find(".stacking-container .stacking-select");
		$stackingSelect.val(chartConfiguration.configuration.stacking);
	}

	/* Update annotation */
	this.$widget.find("textarea#annotation-text-bloc").val(chartConfiguration.configuration.annotation);
};


ChartSerieConfiguration.prototype.updateAnnotation = function () {
	this.chartConfiguration.configuration.annotation = this.$widget.find("textarea#annotation-text-bloc").val();
};


ChartSerieConfiguration.prototype.setAverage = function (serie, chart) {
	var average = this.average(this.getDataValues(serie));
	this.addPlotLine(chart, serie, 'AVERAGE', average);
};


ChartSerieConfiguration.prototype.setMedian = function (serie, chart) {
	var median = this.median(this.getDataValues(serie));
	this.addPlotLine(chart, serie, 'MEDIAN', median);
};

ChartSerieConfiguration.prototype.getDataValues = function (series) {
	return series.data.map(function (point) {
		return series.options.stacking === 'percent'
			? point.percentage
			: point.y;
	});
};

ChartSerieConfiguration.prototype.average = function (values) {
	var sum = values.reduce(function (a, b) {
		return a + b;
	})
	return sum / values.length;
};

ChartSerieConfiguration.prototype.median = function (values) {
	values.sort(function (a, b) {
		return a - b;
	});
	var length = values.length;
	var median = 0;
	if (length === 1) {
		median = values[0];
	} else if (length % 2 === 0) {
		var half = length / 2;
		var value1 = values[half];
		var value2 = values[half - 1];
		median = (value1 + value2) / 2;
	} else {
		median = values[Math.round(length / 2) - 1];
	}
	return median;
};

ChartSerieConfiguration.prototype.addPlotLine = function (chart, series, operator, value) {
	var label = mashupI18N.get('chartSerieConfiguration', 'operator.' + operator.toLowerCase());
	var plotLineId = series.options.id + '_' + operator;
	/* Deleting existing plot line */
	chart.yAxis[series.options.yAxis].removePlotLine(plotLineId);
	if (series.visible) {
		chart.yAxis[series.options.yAxis].addPlotLine({
			value: value,
			operator: operator,
			color: series.color,
			width: 2,
			serieName: series.options.id,
			id: plotLineId,
			zIndex: 5,
			label: {
				text: label
			}
		});
	}
};

ChartSerieConfiguration.prototype.showHideLegend = function (showHide, chart) {
	chart.legend.update({
		enabled: showHide
	});
};


ChartSerieConfiguration.prototype.removeOperator = function (serie, operator) {
	this.currentChart.yAxis[serie.options.yAxis].removePlotLine(serie.options.id + '_' + operator);
};

ChartSerieConfiguration.prototype.onChangeOperator = function () {
	this.$widget.on("change.chart-configuration", "select.operator-list", $.proxy(function (e) {
		var $option = $(e.currentTarget);
		var operator = $option.val();
		var seriesName = this.chartConfiguration.configuration.operator[operator];
		this.$widget.find(".operator-series-list").find("li:not(:first-child) input.plma-checkbox").prop("checked", false);
		if (seriesName !== undefined) {
			for (var i = 0; i < seriesName.length; i++) {
				var $checkbox = this.$widget.find(".operator-series-list").find("input.display-operators.plma-checkbox[value='" + seriesName[i] + "']");
				$checkbox.prop("checked", true);
			}
		}
	}, this));
};

ChartSerieConfiguration.prototype.onRestore = function () {
	this.$widget.on("click.chart-configuration", ".restore-btn", $.proxy(function () {
		this.updateAnnotation();
		var storage = new StorageClient("user");
		var newConfig = [];
		for (var i = 0; i < this.configurationSaver.chartsConfiguration.length; i++) {
			if (this.configurationSaver.chartsConfiguration[i].chartId !== this.chartConfiguration.chartId) {
				newConfig.push(this.configurationSaver.chartsConfiguration[i]);
			}
		}
		this.configurationSaver.chartsConfiguration = newConfig;
		storage.set(this.CHART_CONFIGURATION_STORAGE_KEY + this.options.storageKey, JSON.stringify(this.configurationSaver),
			$.proxy(function () {
				var $chart = $('.' + this.chartConfiguration.chartId);
				var $flipContainer = $chart.closest('.flipContainer');
				if ($flipContainer.length > 0) {
					/* Reload flip container */
					var client = new PlmaAjaxClient($flipContainer, {});
					var classes = $flipContainer.prop('classList');
					/* Add uCssId of the flip container, cssId otherwise */
					client.addWidget(classes[2] || classes[1]);
				} else {
					/* Reload chart */
					var client = new PlmaAjaxClient($chart);
					client.addWidget(this.chartConfiguration.chartId);
				}

				/* We need to reload this widget to display the button in the menu */
				if (this.options.waitForTrigger !== "true") {
					/* We have to verify if the annotation widget exists, if it does, we need to reload it */
					if (typeof annotateChart !== undefined) {
						client.addWidget($('.plmaChartAnnotation .annotation-popup-template').data('cssId'));
					}

					if (this.$widget.closest('.plmalightbox-wrapper').data('uCssId')) {
						client.addWidget(this.$widget.closest('.plmalightbox-wrapper').data('uCssId'));
						this.$widget.closest('.plmalightbox-wrapper').remove();
					} else {
						client.addWidget(this.options.uCssId);
					}
				}

				client.update();
				/* Close popup */
				$(".plmalightbox-overlay.visible").click();

				/* Allows popup to be refreshed */
				this.uCssId = false;

				/* Reset internal configuration */
				this.chartConfiguration = undefined;
			}, this),
			function () {
				//error callback
			});
	}, this));
};

ChartSerieConfiguration.prototype.onSave = function () {
	this.$widget.on("click.chart-configuration", ".apply-btn", $.proxy(function () {
		this.applyConfiguration();
	}, this));
};

ChartSerieConfiguration.prototype.applyConfiguration = function () {
	this.updateAnnotation();
	var storage = new StorageClient("user");
	this.configurationSaver.chartsConfiguration = this.configurationSaver.chartsConfiguration.filter($.proxy(function (item) {
		return item.chartId !== this.chartConfiguration.chartId
	}, this));
	this.configurationSaver.chartsConfiguration.push(this.chartConfiguration);
	storage.set(this.CHART_CONFIGURATION_STORAGE_KEY + this.options.storageKey, JSON.stringify(this.configurationSaver),
		$.proxy(function () {
			//close popup
			$(".plmalightbox-overlay.visible").click();
		}, this),
		function () {
			//error callback
		});
};

ChartSerieConfiguration.prototype.scrollToTop = function () {
	this.$widget.find('.widgetContent-container').scrollTop(0);
};

ChartSerieConfiguration.widgetUtils = {
	getChartUcssIdFromContainerId: function (containerId) {
		if ($("#" + containerId).length > 0) {
			if ($("#" + containerId).closest(".plmaCharts").length > 0) {
				return $("#" + containerId).closest(".plmaCharts").attr("class").split(" ")[1];
			}
		}
		return false;
	},
    /**
     *
     * @param {Highcharts#Chart} currentChart - current Highcharts chart object
     * @param {String} serieName - the series option id
     * @return {number} - the index
     */
    getSeriesIndex: function(currentChart, serieName){
        return currentChart.series.findIndex(function (serie) {
            return serie.options.id === serieName
        });
    },
    /**
     * Get Highchart Series by id
     * @param {Highcharts#Chart} currentChart - current Highcharts chart object
     * @param index
     * @return {String} series id - the series option id
     */
    getSeriesId: function(currentChart, index){
        return currentChart.series[index].options.id;
    },
    /**
     * Get Highchart Series by id
     * @param {Highcharts#Chart} currentChart - current Highcharts chart object
     * @param {String} seriesId - the series option id
     * @return {Highcharts#Series}
     */
    getSeriesById: function(currentChart, seriesId){
        return currentChart.series.find(function (series) {
            return series.options.id === seriesId;
        });
    }

};
