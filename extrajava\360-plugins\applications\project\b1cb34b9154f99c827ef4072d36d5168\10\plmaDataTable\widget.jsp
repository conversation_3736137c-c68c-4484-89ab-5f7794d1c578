<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>
<%@ taglib prefix="map" uri="http://www.exalead.com/jspapi/map" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="list" uri="http://www.exalead.com/jspapi/list" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="security" uri="http://www.exalead.com/jspapi/security" %>

<render:import varWidget="widget" varFeeds="feeds" />

<config:getOption name="title" var="title" defaultValue=""/>
<config:getOption name="height" var="height" defaultValue="0"/>
<config:getOption name="scrollContainerSelector" var="scrollContainerSelector" defaultValue=""/>
<config:getOption name="enableScrolling" var="enableScrolling" defaultValue="false"/>
<config:getOption name="scrollHeight" var="scrollHeight" defaultValue="300"/>
<c:if test="${empty scrollContainerSelector}">
	<c:choose>
		<c:when test="${height == 0}">
			<c:set var="scrollContainerSelector" value="window"/>
		</c:when>
		<c:otherwise>
			<c:set var="scrollContainerSelector" value=""/>
		</c:otherwise>
	</c:choose>
</c:if>
<config:getOption name="fullScreen" var="fullScreen" defaultValue="false" />
<config:getOption name="fixHeader" var="fixHeader" defaultValue="false" />
<config:getOption name="activateFixedRow" var="activateFixedRow" defaultValue="false" />
<config:getOption name="mode" var="mode" defaultValue="Hits"/>
<config:getOptionsComposite name="hitSeries" var="hitSeries" mapIndex="true" doEval="false"/>
<config:getOptionsComposite name="technicalData" var="technicalData" mapIndex="true" doEval="true"/>
<%-- Meaning the configuration is not updated to accomodate the cssClass. --%>
<c:forEach items="${hitSeries}" var="column" varStatus="loop">
	<c:if test="${empty column.displayColumn}">
		<map:put map="${column}" key="displayColumn" value="${column.columnCssClass}"/>
		<map:put map="${column}" key="columnCssClass" value=""/>
	</c:if>
	<c:if test="${mode == 'Hits'}">
		<map:put map="${column}" key="columnId" value="column_${loop.index}"/>
		<map:put map="${column}" key="columnCssClass" value="column_${loop.index} ${column.columnCssClass}"/>
	</c:if>
</c:forEach>
<config:getOptionsComposite name="facetSeries" var="facetSeries" mapIndex="true" />
<config:getOption name="pagination" var="pagination" defaultValue="false" />
<config:getOption name="pageSize" var="pageSize" defaultValue="${pagination == 'true' ? 10 : 0}" />
<config:getOption name="defaultValue" var="defaultValue" defaultValue="" />
<config:getOption name="enableFilters" var="enableFilters" />
<config:getOption name="hitUrl" var="hitUrl" />
<config:getOption name="hitTargetFrame" var="hitTargetFrame" defaultValue="_self"/>
<config:getOption var="target" name="target" defaultValue=""/>
<config:getOptions var="forceRefineOnFeeds" name="forceRefineOnFeeds" />
<config:getOptions var="forceRefineOnFacets" name="forceRefineOnFacets" />
<config:getOption var="forceRefineOnMulti" name="forceRefineOnMulti" defaultValue="false" />
<config:getOptions var="removeParameters" name="removeParameters" />
<config:getOption var="dataTablesDom" name="dataTablesDom" defaultValue="ftlip"/>
<config:getOption var="fixPaginationToolbar" name="fixPaginationToolbar" defaultValue="false"/>
<config:getOption var="transposedHeader" name="transposedHeader" defaultValue=""/>
<config:getOption var="saveState" name="saveState" defaultValue="${false}" />
<config:getOption var="enableExport" name="enableExport" />
<config:getOption var="numHits" name="numHits" />
<config:getOption var="exportMode" name="exportMode" />
<config:getOption var="exportPerPage" name="exportPerPage" />
<config:getOption var="exportEncoding" name="exportEncoding" isUrlEncoded="true" isHtmlEscaped="true"/>
<config:getOption var="exportSeparator" name="exportSeparator" isUrlEncoded="true" isHtmlEscaped="true"/>
<config:getOption var="recordDelimiter" name="recordDelimiter" defaultValue="Default"/>
<config:getOption var="fileName" name="fileName" defaultValue="export" isHtmlEscaped="true"/>
<config:getOption var="displayDoc" name="displayDoc" defaultValue="false"/>
<config:getOption var="doc" name="doc" defaultValue=""/>
<config:getOption var="addBOM" name="addBOM" defaultValue="false" />
<config:getOption var="enablePublication" name="enablePublication" defaultValue="false" />
<config:getOption var="nullSort" name="nullSort" defaultValue="false" />
<config:getOption var="enableSubscription" name="enableSubscription" defaultValue="false" />
<config:getOptions var="channelNames" name="channels" />
<config:getOption var="useApplicationConfig" name="useApplicationConfig" />
<config:getOption var="rowIds" name="rowIds" defaultValue="url"/>
<config:getOption name="onRowClickAction" var="onRowClickAction" defaultValue="function(){}"/>
<config:getOption name="onInitAction" var="onInitAction" defaultValue="function(){}"/>
<config:getOption name="onHoverAction" var="onHoverAction" defaultValue="function(){}"/>
<config:getOptionsComposite name="elements" var="elements" mapIndex="true" />
<config:getOption name="enableDrag" var="enableDrag" defaultValue="false" />
<config:getOption name="messageBuilder" var="messageBuilder" defaultValue="$.noop" />
<config:getOptions var="sapiURLs" name="sapiURLs"/>
<config:getOption name="searchTarget" var="searchTarget"/>
<config:getOption name="customSapiURLs" var="customSapiURLs"/>
<config:getOption name="customSapiNames" var="customSapiNames"/>
<config:getOption name="sapiNames" var="sapiNames"/>
<config:getOption name="sapiCommand" var="sapiCommand"/>
<config:getOption name="saveScrollPositions" var="saveScrollPositions" defaultValue="false"/>

<request:isAjax var="isAjax"/>
<request:getParameterValue var="onlyData" name="plmaDataTable_onlyData" defaultValue=""/>
<request:getParameterValue var="draw" name="plmaDataTable_draw" defaultValue="-1"/>
<url:url var="url" keepQueryString="true" xmlEscape="false"/>

<c:set var="paginated" value="${mode == 'Hits' || pagination == 'true'}" />
<c:set var="withLinks" value="${enableFilters == 'true' || not empty hitUrl}" />

<%-- Getting the JSON data --%>
<plma:tableJSON var="tableJson" draw="${draw}" varHasRecords="hasRecords">
	<c:choose>
		<c:when test="${mode == 'Hits'}">
			<plma:tableHitData feeds="${feeds}" hitConfig="${hitSeries}" technicalData="${technicalData}" hitUrl="${hitUrl}" rowId="${rowIds}"/>
			<search:getPaginationInfos feeds="${feeds}" varPerPage="pageSize"/>
		</c:when>
		<c:otherwise>
			<c:forEach items="${facetSeries}" var="facetSerieConfig">
				<plma:tableSynthesisData
					feeds="${feeds}" 
					facetSerieConfig="${facetSerieConfig}"
                    defaultValue="${defaultValue}"
					baseUrl="${target}" 
					forceRefineOnFeeds="${forceRefineOnFeeds}" 
					forceRefineOnFacets="${forceRefineOnFacets}"
					forceRefineOnMulti="${forceRefineOnMulti == 'true'}"/>
			</c:forEach>
		</c:otherwise>
	</c:choose>
</plma:tableJSON>

<c:set var="errorsExist" value="${false}"/>
<search:forEachFeed feeds="${feeds}" var="feed">
	<c:if test="${feed.error != null}">
		<c:set var="errorsExist" value="${true}"/>	
	</c:if>
</search:forEachFeed>

<c:choose>
	<%-- If the request only asks for data, return the JSON --%>
	<c:when test="${isAjax && not empty onlyData && onlyData != 'false'}">
		
		<c:choose>
			<%-- If no feeds --%>
			<c:when test="${search:hasFeeds(feeds) == false}">

			<widget:header>
						<config:getOption name="title" defaultValue=""/>
					</widget:header>
					<widget:content>
						<render:definition name="noFeeds">
							<render:parameter name="widget" value="${widget}"/>
							<render:parameter name="showSuggestion" value="true"/>
						</render:definition>
					</widget:content>
			</c:when>
			
			<%-- If no results --%>
			<c:when test="${not hasRecords && errorsExist}">
                <widget:content>
                    <config:getOption var="customHTMLNoResultMessage" name="customHTMLNoResultMessage"/>
                    <c:choose>
                        <c:when test="${not empty customHTMLNoResultMessage}">
                            <div class="noresult">
                                ${customHTMLNoResultMessage}
                            </div>
                        </c:when>
                        <c:otherwise>
                            <config:getOption var="noResultsJspPathHit" name="noResultsJspPathHit"
                                              defaultValue="/WEB-INF/jsp/commons/noResults.jsp"/>
                            <render:template template="${noResultsJspPathHit}">
                                <render:parameter name="accessFeeds" value="${feeds}"/>
                                <render:parameter name="showSuggestion" value="false"/>
                            </render:template>
                        </c:otherwise>
                    </c:choose>
                </widget:content>
			</c:when>
			
			<%-- --%>
			<c:otherwise>
				${tableJson}
			</c:otherwise>
		
		</c:choose>
		
	</c:when>
	
	<%-- Otherwise render the full widget --%>
	<c:otherwise>
		<widget:widget varUcssId="uCssId" extraCss="plmaDataTable mode${mode} ${paginated ? '' : 'not-paginated'} ${withLinks ? 'withLinks' : ''} ${enableScrolling ? 'infinite-scroll' : ''}">
			<%-- Title and menu button --%>
			<widget:header>
				<span class="widgetHeaderIcon fonticon fonticon-view-list"></span>
				<div class="widgetTitle">${title}</div>
				<i18n:message code="widget.plmaDataTable.menu" var="openMenuTooltip"/>
				<div class="plmadatatable-menu-btn widgetHeaderButton fonticon fonticon-menu-dot" title="${openMenuTooltip}"></div>
			</widget:header>

			<%-- Actual menu --%>
			<widget:content extraCss="plmadatatable-menu-container toolbar-mode hidden">
			<c:if test="${hasRecords && not errorsExist}">
				<%-- Full screen --%>
				<c:if test="${fullScreen == 'true'}">
					<i18n:message var="fullScreenTooltip" code="widget.plmaDataTable.fullScreen" text="Full screen"/>
					<div class="plmadatatable-menu-item plmadatatable-fullscreen" title="${fullScreenTooltip}">
						<i class="fonticon fonticon-resize-full"></i>
						<span class="label">${fullScreenTooltip}</span>
					</div>
				</c:if>
				<%-- Search --%>
				<c:if test="${fn:contains(dataTablesDom, 'f')}">
					<i18n:message var="searchTooltip" code="widget.plmaDataTable.search" text="Search"/>
					<div class="plmadatatable-menu-item plmadatatable-search" title="${searchTooltip}">
						<i class="fonticon fonticon-search"></i>
						<span class="label">${searchTooltip}</span>
					</div>
				</c:if>
				<%-- Transpose --%>
				<c:if test="${mode == 'Synthesis'}">
					<i18n:message var="transposeTooltip" code="widget.plmaDataTable.transpose" text="Transpose"/>
					<div class="plmadatatable-menu-item plmadatatable-transpose" title="${transposeTooltip}">
						<i class="fonticon fonticon-table-column-row"></i>
						<span class="label">${transposeTooltip}</span>
					</div>
				</c:if>
				<%-- Export --%>
				<c:if test="${enableExport}">
					<c:if test="${mode == 'Hits'}">
						<search:getPageFeed var="pageFeed"/>
						<search:getFeed var="feed" feeds="${feeds}"/>
						<search:getFeedInfo var="sapiquery" name="searchAPIUrl" feed="${feed}"/>
						<string:escape var="sapiquery" value="${sapiquery}" escapeType="HTML"/>
						<url:url var="exportUrl" value="../c/exportdata/${exportMode == 'Search API' ? 'stream' : 'batch'}" keepQueryString="true">
							<c:if test="${not empty exportPerPage}">
								<url:parameter name="${feed.id}.per_page" value="${exportPerPage}"/>
							</c:if>
						</url:url>
						<c:set var="parameterSeparator" value="~~~~~" />
						<form id="${uCssId}-export-data" class="export-data-form" action="${exportUrl}" autocomplete="off" method="post" >
							<input type="hidden" name="filename" value="${fileName}">
							<input type="hidden" name="page" value="${pageFeed.id}">
							<input type="hidden" name="feedname" value="${feed.id}">
							<input type="hidden" name="hf" value="${numHits}">
							<input type="hidden" name="exportFormatParams" value="${exportSeparator}${parameterSeparator}${exportEncoding}${parameterSeparator}${recordDelimiter}">
							<c:forEach items="${hitSeries}" var="column">
								<string:eval var="columnLabel" string="${column.columnLabel}" feeds="${feeds}" feed="${feed}"/>
								<c:set var="paramValue" >${columnLabel}${parameterSeparator}${column.columnValue}${parameterSeparator}${column.exportedValue}</c:set>
								<string:escape var="paramValue" value="${paramValue}" escapeType="HTML"/>
								<input type="hidden" name="columns" class="column-data ${column.columnId}" value="${paramValue}">
							</c:forEach>
							<%-- Removing the output format parameter --%>
							<input type="hidden" name="sapiquery" value="${fn:replace(sapiquery, 'of=flea', '')}">
							<input type="hidden" name="addBOM" value="${addBOM}" />
							<plma:signature var="sapiSign" value="${fn:replace(sapiquery, 'of=flea', '')}"/>
							<input type="hidden" name="sapiSign" value="${sapiSign}" />
							<render:csrf />
						</form>
					</c:if>
					<c:if test="${mode == 'Synthesis' && !fn:contains(dataTablesDom, 'B')}">
						<c:set var="dataTablesDom">B${dataTablesDom}</c:set>
					</c:if>
					<i18n:message var="exportTooltip" code="widget.plmaDataTable.export" text="Export"/>

					<div class="plmadatatable-menu-item plmadatatable-export" title="${exportTooltip}">
						<i class="fonticon fonticon-download "></i>
						<span class="label">${exportTooltip}</span>
					</div>
				</c:if>
				<%-- Settings--%>
				<i18n:message var="settingsTooltip" code="widget.plmaDataTable.config" text="Settings"/>
				<div class="plmadatatable-menu-item plmadatatable-config" title="${settingsTooltip}">
						<i class="fonticon fonticon-cog"></i>
						<span class="label">${settingsTooltip}</span>
				</div>
				<%-- Reset --%>
				<i18n:message var="resetTooltip" code="widget.plmaDataTable.reset" text="Reset"/>
				<div class="plmadatatable-menu-item plmadatatable-reset" title="${resetTooltip}">
						<i class="fonticon fonticon-reset"></i>
						<span class="label">${resetTooltip}</span>
				</div>
				<%-- Documentation --%>
				<c:if test="${displayDoc}">
					<i18n:message var="docTooltip" code="widget.plmaDataTable.doc" text="Documentation"/>
					<div class="plmadatatable-menu-item plmadatatable-doc" title="${docTooltip}">
						<i class="fonticon fonticon-info"></i>
						<span class="label">${docTooltip}</span>
					</div>
				</c:if>
				</c:if>
			</widget:content>

			<%-- Table --%>
			<c:if test="${height > 0 && enableScrolling != 'true'}">
				<c:set var="extraStyles" value="height: ${height}px;" />
			</c:if>
			<widget:content extraStyles="${extraStyles}" extraCss="table-wrapper">

				<c:choose>
					<%-- If no feeds --%>
					<c:when test="${search:hasFeeds(feeds) == false}">
						<render:definition name="noFeeds">
							<render:parameter name="widget" value="${widget}" />
							<render:parameter name="showSuggestion" value="false" />
						</render:definition>
					</c:when>
					
					<%-- If no results --%>
					<c:when test="${not hasRecords}">
						<widget:content>
                            <config:getOption var="customHTMLNoResultMessage" name="customHTMLNoResultMessage"/>
                            <c:choose>
                                <c:when test="${not empty customHTMLNoResultMessage}">
                                    <div class="noresult">
                                        ${customHTMLNoResultMessage}
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <config:getOption var="noResultsJspPathHit" name="noResultsJspPathHit"
                                                      defaultValue="/WEB-INF/jsp/commons/noResults.jsp"/>
                                    <render:template template="${noResultsJspPathHit}">
                                        <render:parameter name="accessFeeds" value="${feeds}"/>
                                        <render:parameter name="showSuggestion" value="true"/>
                                    </render:template>
                                </c:otherwise>
                            </c:choose>
                		</widget:content>
					</c:when>
					
					<%-- --%>
					<c:otherwise>

					</c:otherwise>
				
				</c:choose>
				
				<table>
					<thead></thead>
					<tbody></tbody>
				</table>

				<div class="doc-container hidden">
					<div class="container">${doc}</div>
					<span class="close-doc fonticon fonticon-cancel" title="<i18n:message code='generic.close' />"></span>
				</div>
			</widget:content>
			
			<%-- Technical stuff --%>
			<div class="hidden">
				<c:if test="${activateReloadChart == 'true'}">
					<config:getOption var="reloadCondition" name="reloadCondition" defaultValue="true"/>
					<c:if test="${reloadCondition}">
						<config:getOption var="labelReload" name="labelReload" defaultValue=""/>
						<config:getOption var="iconReload" name="iconReload" defaultValue=""/>
						<config:getOption var="paramNameReload" name="paramNameReload" defaultValue=""/>
						<config:getOption var="paramValueReload" name="paramValueReload" defaultValue=""/>
						
						<div class="see-more ${iconReload}" data-name="${paramNameReload}" data-value="${paramValueReload}"> ${labelReload}</div>
						
					</c:if>
				</c:if>
			</div>
			
		</widget:widget>
		
		<render:renderScript position="READY">
			var tableJson = ${tableJson};
			
			<c:choose>
			<%-- In hits mode, if a state is saved, we cannot rely on the provided data.
				The table must get its data again in JSON with the correct sort and per_page parameters. --%>
				<c:when test="${mode == 'Hits' && saveState == 'true'}">
					tableJson.data = [];
				</c:when>
				<c:otherwise>
					
				</c:otherwise>
			</c:choose>
			
			<c:if test="${mode == 'Hits' && (enablePublication || enableSubscription)}">
				<search:forEachFeed var="feed" feeds="${feeds}">
					<search:forEachEntry var="entry" feed="${feed}">
						<c:choose>
							<c:when test="${useApplicationConfig}">
								<plma:getChannelsConfig var="channels" entry="${entry}" channelNames="${channelNames}" />
								var channelsConfig = ${channels};
									channelsConfig.forEach(function (channelConfig) {
									SelectAPI.addTopic(channelConfig.topic, channelConfig.data, eval('(' + channelConfig.normalizer + ')'), eval('(' + channelConfig.denormalizer + ')'));
								});
							</c:when>
							<c:otherwise>
								<config:getOptionsComposite var="channels" name="channelsConfig" mapIndex="true" entry="${entry}" />
								<c:forEach items="${channels}" var="channel">
									SelectAPI.addTopic('${channel.topic}', ${channel.data}, ${channel.normalizer}, ${channel.denormalizer});
								</c:forEach>
							</c:otherwise>
						</c:choose>
					</search:forEachEntry>
				</search:forEachFeed>
			</c:if>
		
			var plmaDataTable = new PlmaDataTable('${uCssId}', {
				datatableConfig: $.extend({}, tableJson, {
					serverSide: ${mode == 'Hits'},
					<c:if test="${mode == 'Hits' && enableScrolling == 'true'}">
						scroller: true,
						scrollY: ${height > 0 ? height : 300},
					</c:if>
					rowId: '_row_id',
					select: {
						selector: 'td:not(.' + PlmaDataTable.EXTRA_COLUMN_CLASS + ')'
					},
					paging: ${paginated},
					displayLength: ${pageSize},
					lengthMenu: [10, 25, 50, 100],
					dom: '${dataTablesDom}',
					bom: ${addBOM},
					colReorder: {realtime: false},
					stateSave: ${saveState},
					stateDuration: 0,
					isNullSort: ${nullSort},
				}),
				<i18n:getLang var="lang"/>
				<url:resource var="i18nFile" file="/resources/widgets/plmaResources/lib/datatables/I18N/${lang}.json" testIfExists="false"/>
				i18nFile: '${not empty i18nFile ? i18nFile : "" }',
				displayDoc: '${displayDoc}',
				doc: '${doc}',
				enableFilters: ${withLinks},
				removeParameters: [<c:forEach var="removeParameter" varStatus="loop" items="${removeParameters}">${loop.index > 0 ? ', ' : '' }'${removeParameter}'</c:forEach>],
				columnRefines: tableJson.columnRefines,
				columnSortFields: tableJson.columnSortFields,
				columnExplicitSorts: tableJson.columnExplicitSorts,
				fixedHeader: ${fixHeader},
				fixPaginationToolbar: ${fixPaginationToolbar},
				activateFixedRow: ${activateFixedRow},
				hideEmpty: '<config:getOption name="hideEmpty" defaultValue="false" />',
				scrollContainerSelector: '<string:escape value="${scrollContainerSelector}" escapeType="JAVASCRIPT"/>',
				transposedHeader: '<string:escape value="${transposedHeader}" escapeType="JAVASCRIPT"/>',
				saveStateInStorageService: <security:isUserConnected />,
				<security:getUser var="userSecurityModel"/>
				userName: '${not empty userSecurityModel ? userSecurityModel.login : ''}',
				onRowClickAction: ${onRowClickAction},
				onInitAction: ${onInitAction},
				onHoverAction: ${onHoverAction},
				hitTargetFrame: '${hitTargetFrame}',
				<c:if test="${mode == 'Hits'}">
					selectionConfig: {
						topicNames: [<c:forEach items="${channelNames}" var="channelName">'${channelName}',</c:forEach>],
						enablePublication: ${enablePublication},
						enableSubscription: ${enableSubscription},
					},
					saveScrollPositions: ${saveScrollPositions},
				</c:if>
				<c:if test="${enableExport && mode == 'Synthesis'}">
					enableDataTablesExport: true,
					exportFileName: '<string:escape value="${fileName}" escapeType="JAVASCRIPT"/>',
                    exportSeparator: '<string:escape value="${exportSeparator}" escapeType="JAVASCRIPT"/>',
				</c:if>
				<c:if test="${enableExport && mode == 'Hits'}">
					enableControllerExport: true,
				</c:if>
				enableExportAll: '<config:getOption name="enableExportAll"/>',
				paramNames: {
					perPage: [<search:forEachFeed feeds="${feeds}" var="feed">'${feed.id}.per_page',</search:forEachFeed>],
					page: [<search:forEachFeed feeds="${feeds}" var="feed">'${feed.id}.page',</search:forEachFeed>],
					sort: [<search:forEachFeed feeds="${feeds}" var="feed">'${feed.id}.s',</search:forEachFeed>]
				},
				perPageParam: '${perPageParam}',
				extraColumnOptions: <config:getOption name="extraColumnOptions" defaultValue="{}" />,
				elements: [
					<c:forEach items="${elements}" var="element">
						{
							renderer: ${not empty element.renderer ? element.renderer : '$.noop'},
							onClick: ${not empty element.onClick ? element.onClick : '$.noop'}
						},
					</c:forEach>
				],
				enableDrag: ${enableDrag},
				isEmpty:${ not hasRecords || errorsExist},
				messageBuilder: ${messageBuilder}
				<%-- If this is an AJAX request, we need to forward its parameters to datatables' AJAX calls --%>
				<c:if test="${isAjax}">
				,
				ajaxUrl: '<string:escape value="${url}" escapeType="JAVASCRIPT"/>'
				</c:if>
			});
		</render:renderScript>
	</c:otherwise>
</c:choose>
