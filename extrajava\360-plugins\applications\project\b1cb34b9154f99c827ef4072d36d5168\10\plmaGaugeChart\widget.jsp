<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>

<render:import varFeeds="feeds" varWidget="widget"/>

<config:getOption var="displayDoc" name="displayDoc" />
<config:getOption var="width" name="width" />
<c:if test="${width != null}"><c:set var="width" value="width:${width}px;" /></c:if>
<config:getOption var="heightNum" name="height" />
<c:if test="${height != null}"><c:set var="height" value="height:${heightNum}px;" /></c:if>

<widget:widget varCssId="cssId" varUcssId="uCssId" extraCss="highcharts lineChart plmaGauge" extraStyles="${width}">
	<widget:content extraStyles="${height}">
		<c:if test="${displayDoc}">
			<span class="doc-button fonticon fonticon-info" title="<i18n:message code='plma.navigation.doc' />"></span>
			<div class="doc-container hidden">
				<div class="container"><config:getOption name="doc" /></div>
				<span class="close-doc fonticon fonticon-cancel" title="<i18n:message code='generic.close' />"></span>
			</div>
		</c:if>
		<div id="${uCssId}_plma_gauge" class="chart-container"></div>
	</widget:content>
	
	<config:getOption name="title" var="title" defaultValue="" />
	<config:getOption name="value" var="value"/>
	<config:getOption name="unit" var="unit"/>
	<config:getOption name="minValue" var="minValue"/>
	<config:getOption name="maxValue" var="maxValue"/>
	<config:getOptionsComposite name="ranges" var="ranges" doEval="true" mapIndex="true" feeds="${feeds}"/>
	<render:renderScript position="READY">
		var ranges = [];
		<c:forEach var="range" items="${ranges}">
		 	var range = {};
		 	range.color = '${range.colorRange}';
		 	range.min = ${range.minRange};
		 	range.max = ${range.maxRange};
			ranges.push(range);
		</c:forEach>
		var plmaGaugeChart = new PlmaGaugeChart('${uCssId}','${value}','${maxValue}','${minValue}','${unit}','${title}',ranges, '${heightNum}');
		plmaGaugeChart.init();
		<c:if test="${displayDoc}">
			plmaGaugeChart.initDoc();
		</c:if>
	</render:renderScript>
		
</widget:widget>
