<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="search" uri="http://www.exalead.com/jspapi/search" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="url" uri="http://www.exalead.com/jspapi/url" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import parameters="widget,feeds,uCssId"/>
<render:import parameters="columnsConfig,isGridTable,doTranspose" ignore="true"/>

<config:getOption name="defaultLayout" var="defaultLayout" defaultValue="Responsive"/>
<config:getOption var="showThumbnail" name="showThumbnail" defaultValue="false"/>

<%-- Title Column --%>
<c:choose>
	<c:when test="${doTranspose == 'transpose'}">
		<div class="${isGridTable? 'th fixed-column' : ''} column main-column">
	</c:when>
	<c:otherwise>
		<div class="${isGridTable? 'th fixed-column' : ''} column main-column" data-column-name="title" data-column-show="true" data-column-type="String">
	</c:otherwise>
</c:choose>
			<span class="title-container ${showThumbnail? 'with-thumbnail' : 'no-thumbnail'}">
				<span class="toolbarContainer"></span>
				<c:if test="${showThumbnail == 'true'}">
					<div class="hitThumbnail"></div>
				</c:if>
				<c:if test="${doTranspose != 'transpose'}">
				<span class="hitTitle">
					<span class="icon fonticon fonticon-text"></span>
					<c:choose>
						<c:when test="${defaultLayout == 'Article Responsive'}">
							<span class="label"><i18n:message code="hitcustom.column.content"/></span>
						</c:when>
						<c:otherwise>
							<span class="label"><i18n:message code="hitcustom.column.label"/></span>
						</c:otherwise>
					</c:choose>
				</span>
				</c:if>
			</span>
		</div>

<%-- Facet & Meta Columns --%>
<c:choose>
	<c:when test="${empty columnsConfig}">
	    <%-- Type Column --%>
        <config:getOption var="typeFacetId" name="typeFacet"/>
        <c:if test="${!isGridTable && not empty typeFacetId}">
        	<div class="${isGridTable? 'th' : ''} column table-column type-column" data-column-name="${typeFacetId}" data-column-show="true" data-column-type="String" title="<i18n:message code="hitcustom.column.type"/>">
        		<span class="icon fonticon fonticon-doc"></span>
        		<span class="label"><i18n:message code="hitcustom.column.type"/></span>
        	</div>
        </c:if>
		<config:getOption var="showHitFacets" name="showHitFacets" defaultValue="false"/>
		<c:if test="${showHitFacets == 'true'}">
			<config:getOption var="hitFilterFacetsType" name="hitFilterFacetsType" defaultValue="No filtering"/>
			<config:getOption var="hitFacetsList" name="hitFacetsList" defaultValue=""/>
			<config:getOption var="sortModeFacets" name="sortModeFacets" defaultValue="default"/>
			<search:forEachFacet var="facet" feeds="${feeds}"
								 filterMode="${hitFilterFacetsType}"
								 facetsList="${hitFacetsList}"
								 showEmptyFacets="true"
								 sortMode="${sortModeFacets}" varStatus="loop">
				<plma:getFacetLabel var="facetLabel" feeds="${feeds}" facetId="${facet.id}"/>
				<plma:getIconName var="facetIcon" facetId="${facet.id}"/>
				<string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML" />
				<div class="${isGridTable? 'th' : ''} column table-column ${empty facetIcon ? 'no-icon' : ''}" data-column-name="${facet.id}" title="${facetLabel}">
					<span class="icon fonticon ${facetIcon}"></span>
					<span class="label">${facetLabel}</span>
				</div>
			</search:forEachFacet>
		</c:if>

		<config:getOption var="showHitMetas" name="showHitMetas" defaultValue="false"/>
		<c:if test="${showHitMetas == 'true'}">
			<config:getOption var="filterMetas" name="filterMetas" defaultValue="No filtering"/>
			<config:getOption var="metasList" name="metas"/>
			<config:getOption var="sortModeMetas" name="sortModeMetas" defaultValue="default"/>
			<search:forEachFeed feeds="${feeds}" var="feed" varStatus="accessFeedsStatus">
				<search:forEachEntry var="entry" feed="${feed}" varStatus="entryStatus" iteration="1">
					<search:forEachMeta var="meta" entry="${entry}"
										filterMode="${filterMetas}"
										metasList="${metasList}"
										showEmptyMetas="true"
										sortMode="${sortModeMetas}">
						<search:getMetaLabel var="metaLabel" meta="${meta}"/>
						<plma:getIconName var="facetIcon" facetId="${meta.name}"/>
						<string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML" />
						<div class="${isGridTable? 'th' : ''} column table-column ${empty facetIcon ? 'no-icon' : ''}" data-column-name="${meta.name}" title="${metaLabel}">
							<span class="icon fonticon ${facetIcon}"></span>
							<span class="label">${metaLabel}</span>
						</div>
					</search:forEachMeta>
				</search:forEachEntry>
			</search:forEachFeed>
		</c:if>
	</c:when>
	<c:otherwise>
		<c:forEach var="colGroup" items="${columnsConfig.columns}">
		    <c:set var="showColGroup" value="true"/>
            <c:if test="${not empty colGroup.displayCondition}">
                <string:eval var="showColGroup" string="${colGroup.displayCondition}" feeds="${accessFeeds}" feed="${feed}"/>
            </c:if>

			<c:if test="${showColGroup == 'true'}">
                <c:forEach var="colConfig" items="${colGroup.column}">
                    <c:if test="${colConfig.isShown != 'false' && (colConfig.isShown != 'titleConfig' || doTranspose == 'transpose')}">
                        <c:set var="label" value="${not empty colConfig.label ? colConfig.label : colConfig.meta}" />
                        <c:choose>
                            <c:when test="${colConfig.isFacet == true}">
                                <plma:getIconName var="facetIcon" facetId="${colConfig.meta}"/>
                                <string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML" />
                                <div class="${isGridTable? 'th' : ''} column table-column ${empty facetIcon ? 'no-icon' : ''}" data-column-name="${colConfig.meta}"
                                    data-colgroup-id="" data-column-show="${colConfig.isShown}" data-column-type="${colConfig.type}" title="${label}">
                                    <span class="icon fonticon ${facetIcon}"></span>
                                    <span class="label">${label}</span>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <plma:getIconName var="facetIcon" facetId="${colConfig.meta}"/>
                                <string:escape var="facetIcon" value="${facetIcon}" escapeType="HTML" />
                                <div class="${isGridTable? 'th' : ''} column table-column ${empty facetIcon ? 'no-icon' : ''}" data-column-name="${colConfig.meta}"
                                    data-colgroup-id="" data-column-show="${colConfig.isShown}" data-column-type="${colConfig.type}" title="${label}">
                                    <span class="icon fonticon ${facetIcon}"></span>
                                    <span class="label">${label}</span>
                                </div>
                            </c:otherwise>
                        </c:choose>
                    </c:if>
                </c:forEach>
            </c:if>
		</c:forEach>
        <%-- Custom Columns --%>
        <config:getOption var="customMetaJSP" name="customMetaJSP" defaultValue=""/>
        <c:if test="${not empty customMetaJSP}">
            <render:template template="${customMetaJSP}" defaultTemplate="">
                <render:parameter name="accessFeeds" value="${feeds}" />
                <render:parameter name="uCssId" value="${uCssId}"/>
                <render:parameter name="widget" value="${widget}"/>
                <render:parameter name="isGridTable" value="${empty isGridTable? false : isGridTable}"/>
            </render:template>
        </c:if>
	</c:otherwise>
</c:choose>
