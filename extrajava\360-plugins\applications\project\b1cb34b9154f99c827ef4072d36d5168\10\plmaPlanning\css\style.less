@import "../../plmaResources/css/styles/utils.less";
@import "../../plmaResources/css/styles/colorPreferences.less";
@import "../../plmaResources/css/polyfills.less";

.plmaPlanning {
	.display-flex();
	overflow: hidden;
	flex-direction: column;
	height: 100%;
	
	&.full-size-active {
		position: fixed;
	    width: 100%;
	    height: 100% !important;
	    top: 0;
	    left: 0;
	    z-index: 20000;
	    .display-flex();
	    flex-direction: column;
	    padding: 20px;
	    background-color: rgba(0, 0, 0, 0.3);
	    .widgetHeader {
	    	flex-basis: 20px;
	    }
	    .widgetContent {
	    	flex: 1;
	    	padding: 0;
	    	.vis-timeline {
	    		height: 100% !important;
	    	}
	    }
	}
	
	&> .widgetContent{
		.flex(1 0 0);
		flex-grow: 1;
		.display-flex();
		flex-direction: column;
		position: relative;
		.main-widget{
			display: flex;
			flex-grow: 1;
			flex-basis: 0;
			overflow: auto;
			.timeline-vis {
				.flex(3 0 0);
				flex-grow: 3;
				flex-basis: 0;
			}
			.left-panel {
				overflow: auto;
				border-bottom: 1px solid @cblock-border;
				border-right: 1px solid @cblock-border;
				position: relative;
				font-family: 'entypo';
				width: 302px;
				.close-button{
					position:absolute;
					top: 8px;
					right: 0px;
					font-size: 20px;
					cursor: pointer;
					.fonticon:hover{
						color: #FEE000;
					}
				}
				.timeline-doc{
					flex-basis: 130px;
					&> .container{
						padding: 10px 5px;
						.doc-section{
							margin-bottom: 7px;
							.legendHeader{
								font-size: 16px;
								margin-bottom: 10px;
								display: inline-block;
								border-bottom: 1px solid;
								padding: 5px 0px;
							}
							.legendContent{
								font-size: 14px;
							}
						}
					}
				}
			}
			.right-panel{
				overflow: auto;
				.flex(1 0 100px);
				.timeline-info {
				.plmaHitDetails > .hitDetailsToolbar{
					position: absolute;
					width: -webkit-fill-available;
					background-color: white;
					padding: 15px 13px 5px;
					box-shadow: -3px 6px 10px -6px #b4b6ba;
					height: 8px;
					.hitDetailsButtons > .openHitBtn {
						position:absolute;
						margin-right: 60px;
						margin-top: -10px;
					}
				}
					.transition(0.5s);
					display: flex;
					flex-flow: column nowrap;
					&.visible {
						.flex(1 0 0);
						flex-basis: 170px;
						flex-grow: 1;
						border-bottom: 1px solid @grey-3;
					}
				}
				.close-button{
					position:absolute;
					top: 12px;
					right: 4px;
					font-size: 20px;
					z-index: 100;
					cursor: pointer;
					.fonticon:hover{
						color: #FEE000;
					}
				}
			}
		}
	}
	.timelineFooter {
		height: 35px;
		border-top: 1px solid @cblock-border;
		.buttonsContainer {
			display: inline-block;
			float: right;
			margin: 5px;
			.actionButton {
				font-size: 15px;
				border: 1px solid @cblock-border;
				padding: 3px;
				cursor: pointer;
			}
		}
		.left-panel-button {
			display: inline-block;
			height: 35px;
			line-height: 24px;
			.fonticon {
				font-size: 20px;
				position: relative;
				top: 6px;
				cursor: pointer;
			}
		}
		.loadData {
			display: inline-block;
			border: 1px solid @cblock-border;
			border-radius: 3px;
			margin: 5px;
			position: absolute;
			line-height: 20px;

			.label{
				font-size: 12px;
				font-weight: 600;
			}
			.loadNextDataButton,
			.loadAllDataButton{
				font-size: 18px;
				cursor: pointer;
			}
		}
		.timespan-info{
			margin-left: 10px;
			border: 1px solid transparent;
			padding: 3px 5px;
			background-color: #F2F5F7;
			line-height: 100%;
			color: black;
		}
	}

	/*Timeline elements default stlyes*/
	.timeline-vis {
		.flex(1 0 0);
		flex-grow: 1;
		overflow: hidden;
		
		.vis-ltr .vis-label.vis-nested-group{
			&.vis-group-level-unknown-but-gte1{
				border: none;
				border-bottom: 1px solid #bfbfbf;
			}
			
			&.vis-group-level-0,
			&.vis-group-level-unknown-but-gte1{
				padding-left: 10px;
				background-color: #fff;
				.vis-inner{
					padding-left: 0px;
				}
			}
		}
		
		.vis-panel.vis-left{
			width: 150px;
			.vis-labelset .vis-label.group-column-view{
				.vis-inner{
					display: inline-flex;
					width: inherit;
					height: 20px;
					padding-left: 0px;
					padding-right: 0px;
					&> span{
						padding: 0px 3px;
						border-left: 1px solid;
						flex-basis: 33%;
						text-align: center;
						white-space: normal;
						&:first-child{
							border-left: none;
						}
					}
				}
			}
		}
		
		.vis-item {
			.vis-item-content{
				margin-top: 0px;
			}					
		}
		
		.vis-item.vis-range{
			background-color: #78befa;
			border-color: #003c5a;			
		}
		
		.vis-item.selected{
			box-shadow: -1px -1px 7px 3px darkgrey;
		}
		
		/*Enable use of any fonticon as the shape. vis-dot will act as class fonticon*/
		.vis-item.vis-point,
		.vis-group.group-project{
			&:before{
				/* when fonticon-xxx is added to item, it is added to vis-point 
				and vis-dot both. Hence hide icon in vis-point */
				content: none; 
			}
			&> .vis-item.vis-dot{
				border: none;
				display: inline-block;
				font-family: entypo;
				font-style: normal;
				font-weight: normal;
				font-variant: normal;
				text-transform: none;
				text-decoration: inherit;
				text-align: center;
				margin: 0 0.2em;
				-webkit-font-smoothing: antialiased;
				-moz-osx-font-smoothing: grayscale;

				font-size: 14px;
				color: #77797c;
				background: transparent;
				top: 5px !important;
			}
			/* default icon when fonticon-xxx is not added */
			div:not([class *="fonticon-"]){
				&.vis-item.vis-dot:before{					
					content: '\e165'; 
				}	
			}
			&> .vis-item.vis-dot{
				&.custom-fonticon-diamond{
                    width: 0px;
                    height: 0px;
                    border: 8px solid transparent;
                    border-left-color: black;
                    left: 4px;
                    margin-top: -3px;
                    &:before{
                        content: '';
                        width: 0px;
                        height: 0px;
                        line-height: 0px;
                        border: 7px solid transparent;
                        display: inline;
                        position: absolute;
                        left: -21px;
                        border-right-color: black;
                        top: -7px;
                    }
                }
                &.rotate-45{
                    // Rotate transform creates bad positioning issues during ptinting.
                    // Hence do not use rotations......
                    //transform: translateX(-9px) rotate(45deg) !important;

                    // Having separate rotate and origin fixes the bad offset issue.
                    // but printed shape  will be without rotation.
                    -webkit-transform: rotate(-45deg) !important;
                    -moz-transform: rotate(-45deg) !important;
                    -ms-transform: rotate(-45deg) !important;
                    -o-transform: rotate(-45deg) !important;
                    transform: rotate(-45deg) !important;
                    -webkit-transform-origin: 0 100%;
                    -moz-transform-origin: 0 100%;
                    -ms-transform-origin: 0 100%;
                    -o-transform-origin: 0 100%;
                    transform-origin: 0 100%;
                }
			}
			&.vertical-connector{
				&:before{
					content: '';
					height: 14px;
					width: 1px;
					background-color: #005686;
					position: absolute;
					top: -10px;
					left: 2px;
				}				
			}
			&.inherit-color .vis-item.vis-dot{
				color: inherit;
			}
		}
		.vis-group.group-project{
			&> .vis-item.vis-dot{
				margin-left: 0px;
			}
		}
		.estact.estact-wrapper{
			box-sizing: border-box;
			background-color: #f9f9f9;
			z-index: 0;
			min-width: 26px;
			border-radius: 2px;
			border: 1px solid #e2e4e3;
		}
		.estact.estact-est{
			background-color: unset;
			z-index: 2;
			border-width: 2px;
			border-style: dashed;
			border-color: #005686;
			border-image: initial;
		}
		.estact.estact-act{
			background: transparent;
			border: none;
			border-bottom: 3px solid;
			height: 22px;
			border-bottom-width: 4px;
			border-bottom-color: #368ec4;
			.vis-item-overflow{
				position: relative;
				overflow: visible;
				padding-bottom: 2px;
				.status-info{
					font-size: 12px;
					.status-start, .status-finish{
						position: absolute;
						bottom: -8px;
					}
					.status-start{
						left: -10px;
					}
					.status-finish{
						right: -10px;
					}
				}
			}
			.vis-item-content{
				color: transparent;			
			}
		}
	}
	.status-start, .status-finish{
		color: #3d3d3d;
		&.pastdue{
			color:#D80E0E;
		}
		&.early, &.ontime{
			color: green;
		}
		&.late{
			color:#E87B00;
		}
		&.upcoming{
			color: #3d3d3d;
		}
	}
}

/*Timeline element custom styles*/
.plmaPlanning.styles-piu{
	.timeline-vis{
		.vis-panel.vis-left{
			.vis-labelset{
				.vis-label.group-program{
					background: #77797c;
					color: white;
				}
				.vis-label.group-prj{
					padding-left: 15px;
					background: #EDF6EB;
				}
				.vis-label.group-issue{
					padding-left: 35px;
				}
			}
		}
		
		.vis-item.vis-point{
			&.milestone, &.gate{
				.vis-item-content{
					padding-top: 0px;
					padding-bottom: 10px;
					padding-left: 7px;
					&> div:first-child{
						//position:absolute;
						left: 0px;
						bottom: -15px;
					}
				}
			}
			.vis-item.vis-dot{
				&.milestone{
					color: #477738;
				}			
				&.gate{
					color: #FF8A2E;
				}
				&.gate.rotate-45{
					color: #6D2815;
					font-size: 16px;
				}
				&.issue.state-inprogress{
					color: #FEE000
				}
			}
		}
	}
}

/*Details panel Styles.*/
.mashup .plmaPlanning {
	.plmaHitDetails > .hits {
		.hitDetailsHeader{
			margin-top:30px;
		}
		.hit{
				.hitContent{
				display: none;
				}
			&> .hitHeader > .hitTitle-container .hitTitle{
				font-size:18px;
			}
			.metaWrapper{
				.metaGroupTitle{
					font-size: 14px;
				}
				.metaDetailWrapper{
					line-height: 17px;
					font-size: 12px;
					.metaLabel{
						text-align: right;
						padding-right: 5px;
					}
				}
			}
			.select-all-container{
				position: relative;
			}
			.resultCarousel .hit.hit-card{  
				position: relative;
				width: 200px;
				.hitTitle .hitTitleText .title{
				font-size: 14px;
					line-height: 14px;
				}
				.hitMetas{
					flex-direction: column;
					.hitMeta:last-child{
						justify-content: flex-start;
					}
				}
			}
		}
	}
}

/*Documentation Panel Styles */
.doc-section{
	.legendContent {
		li{
			padding: 5px;
			.status-start:before{
				margin-right: 5px;
			}
		}
	}
}

/*Timeline configurator styles */
.plmaPlanning .widgetContent .main-widget .left-panel .timeline-configurator{
	.header{
		width: inherit;
		height: 30px;
		line-height: 25px;
		border-bottom: 1px solid @cblock-border;
		padding: 0px 7px;
		background-color: #e2e4e3;
		.title{
			font-size: 16px;
		}
		.buttons{
			position: absolute;
			right: 25px;
			font-size: 20px;
			padding-top: 3px;
			.fonticon{
				cursor: pointer;
				&:hover{
					color: #3d3d3d;
				}
				&.save-button{
					color: #b4b6ba;
					cursor: not-allowed;
					&.modified{
						color: #E87B00;
						cursor: pointer;
					}
				}
			}
		}
	}
	
	div.vis-configuration-wrapper{
		text-transform: capitalize;
		width: 295px;
		div.vis-configuration{
			font-size: 14px;
		}
		.vis-configuration.vis-config-option-container,
		.vis-configuration.vis-config-button{
			display: none;
		}
		div.vis-configuration.vis-config-item{
			width: 100%;
			border-bottom: 1px solid #e2e4e3;
			padding-top: 2px;
			padding-bottom: 2px;
			padding-left: 7px;
			&.vis-config-s0{
				display: none;
			}
		}
		div.vis-configuration.vis-config-item.vis-config-s2{
			left: 0px;
			width: ~'calc(100% - 5px)';
			border-radius: 0px;
			background: none;
		}
		div.vis-configuration.vis-config-item.vis-config-s3{
			left: 0px;
			padding-left: 15px;
			width: ~'calc(100% - 15px)';
			border-radius: 0px;
			background: none;
		}
		div.vis-configuration.vis-config-item.vis-config-s4{
			left: 0px;
			padding-left: 25px;
			width: ~'calc(100% - 25px)';
			border-radius: 0px;
			background: none;
		}
		div.vis-configuration.vis-config-item.vis-config-s5{
			left: 0px;
			padding-left: 35px;
			width: ~'calc(100% - 35px)';
			border-radius: 0px;
			background: none;
		}
		
		input.vis-configuration.vis-config-range{
			width: 120px;
		}
		input.vis-configuration.vis-config-rangeinput{
			width: 25px;
		}
		div.vis-configuration.vis-config-select{
			font-size: 14px;
			line-height: 18px;
		}
	}
	
	.custom-config{
		display: flex;
		flex-direction: column;
		row-gap: 10px;
		margin: 5px;
		i.fonticon{
			float: right;
			cursor: pointer;
			&:hover{
				color: @clink;
			}
			&.range-preview-button{
				font-size: 18px;
			}
		}
		.config-section{
			display: flex;
			flex-direction: column;
			row-gap: 2px;
			margin: 1px;
			font-family: 'entypo';
			font-size: 12px;
			
			.config-section-header {
				font-size: 14px;
				font-weight: bold;
				font-style: italic;
				margin-bottom: 5px;
				border-bottom: 1px solid #e2e4e3;
				padding-bottom: 5px;
			}
			&.row{
				flex-direction: row;
				display: flex;
				column-gap: 5px;
				line-height: 22px;
				.label{
					width: 105px;
					font-size: 14px;
				}
				&.level-1{
					padding-left: 7px;
				}
			}
		}
		.export-config{
			select{
				text-transform: capitalize;
			}
			.user-input.time-scale{
				width: 145px;
			}
			.range-value{
				width: 50px;
			}
			.range-unit{
				width: 80px;
			}
			.range-calculated{
				background-color: #EDF6EB;
				padding: 1px 3px;
				flex-direction: row;
				.calculations{
					flex-grow: 1;
				}
				.error{
					color: #EA4F37;
					background-color: #FFF0EE;
				}
			}
		}
	}
}

/*Export styles*/
.mashup{
	.plmalightbox .plmalightbox-box.export-to-3dspace.timeline-export{
		max-width: 90%;
		height: 90%;
		#export-container-global .container .preview-container .preview-content{
			padding-bottom: 10px;
			overflow-x: auto;
			img{
				height: 60vh;
				width: auto;
			}
		}
	}
}