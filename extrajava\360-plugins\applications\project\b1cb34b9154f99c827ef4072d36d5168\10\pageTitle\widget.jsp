<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="render" uri="http://www.exalead.com/jspapi/render" %>
<%@ taglib prefix="widget" uri="http://www.exalead.com/jspapi/widget" %>
<%@ taglib prefix="config" uri="http://www.exalead.com/jspapi/config" %>
<%@ taglib prefix="string" uri="http://www.exalead.com/jspapi/string" %>
<%@ taglib prefix="i18n" uri="http://www.exalead.com/jspapi/i18n" %>
<%@ taglib prefix="request" uri="http://www.exalead.com/jspapi/request" %>
<%@ taglib prefix="plma" uri="http://www.exalead.com/jspapi/plma" %>

<render:import varWidget="widget"/>

<config:getOption name="userConfig" var="userConfig" defaultValue="false"/>
<config:getOption name="title" var="title" defaultValue="" doEval="true"/>
<config:getOption var="heightOption" name="height"/>
<config:getOption var="fontSizeOption" name="iconsSize"/>
<c:set var="height" value=""/>
<c:set var="fontSize" value=""/>
<c:if test="${heightOption != null}"><c:set var="height" value="height:${heightOption}px;"/></c:if>
<c:if test="${fontSizeOption != null}"><c:set var="fontSize" value="--icons-size:${fontSizeOption}px;"/></c:if>

<widget:widget disableStyles="true" extraCss="pageTitleWidget" varUcssId="uCssId" extraStyles="${height} ${fontSize}">
    <c:if test="${userConfig == 'true'}">
        <plma:getPageTitle var="title" defaultValue="${title}"/>
    </c:if>
    <div class="plmaFlexTitle">
        <div class="pageTitleWrapper">
            <h1 class="pageTitle">${title}</h1>
        </div>
        <div class="iconsPlaceholder plmaTitleIconsDiv icons-div">
            <widget:forEachSubWidget>
                <div class="iconDiv">
                    <render:widget/>
                </div>
            </widget:forEachSubWidget>
        </div>
    </div>
</widget:widget>
